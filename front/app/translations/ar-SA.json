{"EmailSettingsPage.emailSettings": "إعدادات البريد الإلكتروني", "EmailSettingsPage.initialUnsubscribeError": "حد<PERSON> خطأ أثناء إلغاء الاشتراك في هذه الحملة، يُرجى المحاولة مجددًا.", "EmailSettingsPage.initialUnsubscribeLoading": "تتم معالجة طلبك، يُرجى الانتظار...", "EmailSettingsPage.initialUnsubscribeSuccess": "تم إلغاء اشتراكك في {campaignTitle} بنجاح", "UI.FormComponents.optional": "اختياري", "app.closeIconButton.a11y_buttonActionMessage": "إغلاق", "app.components.Areas.areaUpdateError": "حد<PERSON> خطأ أثناء حفظ منطقتك. حاول مرة اخرى.", "app.components.Areas.followedArea": "المنطقة المتابعة: {areaTitle}", "app.components.Areas.followedTopic": "الموضوع المتبع : {topicTitle}", "app.components.Areas.topicUpdateError": "حد<PERSON> خطأ أثناء حفظ موضوعك. حاول مرة اخرى.", "app.components.Areas.unfollowedArea": "المنطقة غير المُتابعة: {areaTitle}", "app.components.Areas.unfollowedTopic": "موضوع غير مُتابع: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "السعر:", "app.components.AssignBudgetControl.add": "إضافة", "app.components.AssignBudgetControl.added": "مضاف", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON> التصويت", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "لقد قمت بتوزيع كافة الاعتمادات الخاصة بك.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "لقد قمت بتوزيع الحد الأقصى لعدد الاعتمادات لهذا الخيار.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "لقد قمت بتوزيع جميع نقاطك.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "لقد قمت بتوزيع الحد الأقصى من النقاط لهذا الخيار.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "لقد قمت بتوزيع جميع رموزك.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "لقد قمت بتوزيع الحد الأقصى لعدد الرموز لهذا الخيار.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "لقد قمت بتوزيع جميع أصواتك.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "لقد قمت بتوزيع الحد الأقصى لعدد الأصوات لهذا الخيار.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(بما في ذلك 1 غير متصل بالإنترنت)} other {(بما في ذلك # غير متصل بالإنترنت)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "التصويت غير متاح، حيث أن هذه المرحلة غير نشطة.", "app.components.AssignMultipleVotesControl.removeVote": "إزالة التصويت", "app.components.AssignMultipleVotesControl.select": "يختار", "app.components.AssignMultipleVotesControl.votesSubmitted1": "لقد أرسلتَ تصويتك بالفعل. لتعديله، انقر على \"تعديل تصويتك\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "لقد أرسلتَ تصويتك بالفعل. لتعديله، ارجع إلى صفحة المشروع وانقر على \"تعديل تصويتك\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {رصيد} other {رصيد}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {نقطة} other {نقطة}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {رمز} other {رمز}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {التصويت} other {التصويت}}", "app.components.AssignVoteControl.maxVotesReached1": "لقد قمت بتوزيع جميع أصواتك.", "app.components.AssignVoteControl.phaseNotActive": "التصويت غير متاح، حيث أن هذه المرحلة غير نشطة.", "app.components.AssignVoteControl.select": "يختار", "app.components.AssignVoteControl.selected2": "المحدد", "app.components.AssignVoteControl.voteForAtLeastOne": "صوت لخيار وا<PERSON><PERSON> على الأقل", "app.components.AssignVoteControl.votesSubmitted1": "لقد أرسلتَ تصويتك بالفعل. لتعديله، انقر على \"تعديل تصويتك\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "لقد أرسلتَ تصويتك بالفعل. لتعديله، ارجع إلى صفحة المشروع وانقر على \"تعديل تصويتك\".", "app.components.AuthProviders.continue": "متابعة", "app.components.AuthProviders.continueWithAzure": "تابع باستخدام {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "تابع باستخدام الفيسبوك", "app.components.AuthProviders.continueWithFakeSSO": "تابع مع تسجيل الدخول الموحّد (SSO) المزيف", "app.components.AuthProviders.continueWithGoogle": "تابع باستخدام جوجل", "app.components.AuthProviders.continueWithHoplr": "تواصل مع Hoplr", "app.components.AuthProviders.continueWithIdAustria": "متابعة مع ID النمسا", "app.components.AuthProviders.continueWithLoginMechanism": "تواصل مع {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "متابعة مع MitID", "app.components.AuthProviders.franceConnectMergingFailed": "يوجد بالفعل حساب يحمل عنوان البريد الإلكتروني هذا.{br}{br}لا يمكنك الوصول إلى المنصة باستخدام FranceConnect بسبب عدم تطابق البيانات الشخصية. لتسجيل الدخول باستخدام FranceConnect, سيتعين عليك تغيير اسمك الأول أو اسمك الأخير في هذه المنصة ليطابق بياناتك الرسمية.{br}{br}يمكنك تسجيل الدخول كالمعتاد أدناه.", "app.components.AuthProviders.goToLogIn": "ألديك حساب بالفعل؟ {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "أليس لديك حساب؟ {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "تسجيل الدخول", "app.components.AuthProviders.logInWithEmail": "سجّل الدخول باستخدام البريد الإلكتروني", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "يجب أن تكون الحد الأدنى للعمر المحدد أو أعلى ليتم التحقق منك.", "app.components.AuthProviders.signUp2": "التسجيل", "app.components.AuthProviders.signUpButtonAltText": "سجّل باستخدام {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "سجّل باستخدام البريد الإلكتروني", "app.components.AuthProviders.verificationRequired": "مطلو<PERSON> التحقق", "app.components.Author.a11yPostedBy": "منشور من طرف", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 مشارك} other {{numberOfParticipants} مشاركين}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} المستخدمين", "app.components.AvatarBubbles.participant": "مشارك", "app.components.AvatarBubbles.participants1": "مشاركون", "app.components.Comments.cancel": "إلغاء", "app.components.Comments.commentingDisabledInCurrentPhase": "التعليق غير ممكن في المرحلة الحالية.", "app.components.Comments.commentingDisabledInactiveProject": "التعليق غير ممكن لأن هذا المشروع غير نشط حالياً. ", "app.components.Comments.commentingDisabledProject": "التعليق في هذا المشروع مُعطّل حالياً. ", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} للتعليق. ", "app.components.Comments.commentingMaybeNotPermitted": "لا يُسمح للجميع بالتعليق. يُرجى {signInLink} لتعرف إن كنت تمتثل للشروط أم لا.", "app.components.Comments.inputsAssociatedWithProfile": "بشكل افتراضي ، سيتم ربط عمليات الإرسال الخاصة بك بملف التعريف الخاص بك ، ما لم تحدد هذا الخيار.", "app.components.Comments.invisibleTitleComments": "التعليقات", "app.components.Comments.leastRecent": "الأقل حداثة", "app.components.Comments.likeComment": "مثل هذا التعليق", "app.components.Comments.mostLiked": "معظم ردود الفعل", "app.components.Comments.mostRecent": "الأحدث", "app.components.Comments.official": "رسمي", "app.components.Comments.postAnonymously": "انشر بشكل مجهول", "app.components.Comments.replyToComment": "ر<PERSON> <PERSON><PERSON>ى التعليق", "app.components.Comments.reportAsSpam": "الإبلاغ كإزعاج", "app.components.Comments.seeOriginal": "رؤية الأصل", "app.components.Comments.seeTranslation": "رؤية الترجمة", "app.components.Comments.yourComment": "تعليقك", "app.components.CommonGroundResults.divisiveDescription": "عبارات يتفق فيها الناس ويختلفون على نحو متساوٍ:", "app.components.CommonGroundResults.divisiveTitle": "مثير للانقسام", "app.components.CommonGroundResults.majorityDescription": "صوت أكثر من 60% في اتجاه أو آخر على ما يلي:", "app.components.CommonGroundResults.majorityTitle": "غالبية", "app.components.CommonGroundResults.participantLabel": "مشارك", "app.components.CommonGroundResults.participantsLabel1": "مشاركون", "app.components.CommonGroundResults.statementLabel": "إفادة", "app.components.CommonGroundResults.statementsLabel1": "تصريحات", "app.components.CommonGroundResults.votesLabe": "تصويت", "app.components.CommonGroundResults.votesLabel1": "الأصوات", "app.components.CommonGroundStatements.agreeLabel": "يوافق", "app.components.CommonGroundStatements.disagreeLabel": "لا أوافق", "app.components.CommonGroundStatements.noMoreStatements": "لا توجد بيانات للرد عليها الآن", "app.components.CommonGroundStatements.noResults": "لا توجد نتائج لعرضها بعد. يُرجى التأكد من مشاركتك في مرحلة \"الأرضية المشتركة\" والتحقق هنا لاحقًا.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON>ير متأكد", "app.components.CommonGroundTabs.resultsTabLabel": "نتائج", "app.components.CommonGroundTabs.statementsTabLabel": "البيانات", "app.components.CommunityMonitorModal.formError": "واجه خطأ.", "app.components.CommunityMonitorModal.surveyDescription2": "يتتبع هذا الاستطلاع المستمر مشاعرك تجاه الحوكمة والخدمات العامة.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {يستغرق أقل من دقيقة واحدة} one {يستغرق دقيقة واحدة} other {يستغرق # دقائق}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "تم إرسال رسالة إلكترونية بها رمز تأكيد إلى {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "غيِّر عنوان بريدك الإلكتروني.", "app.components.ConfirmationModal.codeInput": "الر<PERSON>ز", "app.components.ConfirmationModal.confirmationCodeSent": "تم إرسال رمز جديد", "app.components.ConfirmationModal.didntGetAnEmail": "ألم تتلقَ رسالة إلكترونية؟", "app.components.ConfirmationModal.foundYourCode": "هل عثرت على رمزك؟", "app.components.ConfirmationModal.goBack": "العودة.", "app.components.ConfirmationModal.sendEmailWithCode": "إرسال رسالة إلكترونية بها الرمز", "app.components.ConfirmationModal.sendNewCode": "أرسل رمزًا جديدًا.", "app.components.ConfirmationModal.verifyAndContinue": "تحقق ومتابعة", "app.components.ConfirmationModal.wrongEmail": "هل عنوان البريد الإلكتروني غير صحيح؟", "app.components.ConsentManager.Banner.accept": "موافقة", "app.components.ConsentManager.Banner.ariaButtonClose2": "رفض السياسة وإغلاق البانر", "app.components.ConsentManager.Banner.close": "إغلاق", "app.components.ConsentManager.Banner.mainText": "بالتصفح، فأنت توافق على {policyLink} الخاصة بنا", "app.components.ConsentManager.Banner.manage": "إدارة", "app.components.ConsentManager.Banner.policyLink": "سياسة ملفات تعريف الارتباط", "app.components.ConsentManager.Banner.reject": "ير<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "الإعلانات", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "نستخدم هذا لتخصيص وقياس فعالية الحملات الإعلانية على موقعنا الإلكتروني. لن نقوم بإظهار أي إعلانات على هذه المنصة، ولكن الخدمات التالية قد تقوم بإظهار إعلانات مخصصة لك بناءً على الصفحات التي قمت بزيارتها على موقعنا الإلكتروني.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "سماح", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "التحليلات", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "نحن نستخدم هذا التتبع لفهم كيفية استخدامك للمنصة بشكل أفضل؛ من أجل التعلم وتحسين التصفح. تُستخدم هذه المعلومات فقط في التحليلات الجماعية، ولا تُستخدم بأي شكل من الأشكال لتتبع الأفراد.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "رجوع", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "إلغاء", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "إلغاء السماح", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "الوظائف", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "هذا مطلوب لتمكين ومراقبة الوظائف الأساسية للموقع. قد لا تنطبق بعض الأدوات المُدرجة هنا عليك. للمزيد من المعلومات يُرجى قراءة سياسة ملفات تعريف الارتباط الخاصة بنا.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "مدير علامات جوجل ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "مطلوب", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "سنقوم بحفظ ملف تعريف ارتباط المصادقة إذا قمت بالتسجيل، بالإضافة إلى اللغة التي تستخدم بها هذه المنصة وذلك من أجل للحصول على منصة فعالة.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "تفضيلاتك لملفات تعريف الارتباط", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "الأدوات", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "إخلاء المسؤولية عن تحميل المحتوى", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "من خلال تحميل المحتوى، فإنك تعلن أن هذا المحتوى لا ينتهك أي لوائح أو حقوق لأطراف ثالثة، مثل حقوق الملكية الفكرية، وحقوق الخصوصية، وحقوق الأسرار التجارية، وما إلى ذلك. وبالتالي، من خلال تحميل هذا المحتوى، فإنك تتعهد بتحمل المسؤولية الكاملة والحصرية عن جميع الأضرار المباشرة وغير المباشرة الناتجة عن المحتوى الذي تم تحميله. علاوة على ذلك، فإنك تتعهد بتعويض مالك المنصة وGo Vocal ضد أي مطالبات أو التزامات من طرف ثالث ضد أطراف ثالثة، وأي تكاليف مرتبطة بها، والتي قد تنشأ أو تنتج عن المحتوى الذي قمت بتحميله.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "يلغي", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "أخبرنا لماذا", "app.components.CustomFieldsForm.addressInputAriaLabel": "إد<PERSON>ال العنوان", "app.components.CustomFieldsForm.addressInputPlaceholder6": "أدخل عنوانًا...", "app.components.CustomFieldsForm.adminFieldTooltip": "الحقل مرئي فقط للمسؤولين", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "جميع الردود على هذا الاستطلاع مجهولة المصدر.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "يجب أن يكون هناك ثلاث نقاط على الأقل للمضلع.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "يجب أن يكون هناك نقطتين على الأقل للخط.", "app.components.CustomFieldsForm.attachmentRequired": "يجب أن يكون هناك مرفق واحد على الأقل", "app.components.CustomFieldsForm.authorFieldLabel": "مؤلف", "app.components.CustomFieldsForm.authorFieldPlaceholder": "ابدأ الكتابة للبحث حسب بريد المستخدم الإلكتروني أو اسمه...", "app.components.CustomFieldsForm.back": "خلف", "app.components.CustomFieldsForm.budgetFieldLabel": "ميزانية", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "انقر على الخريطة لرسمها. ثم اسحب النقاط لتحريكها.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "انقر على الخريطة أو اكتب عنوانًا أدناه لإضافة إجابتك.", "app.components.CustomFieldsForm.confirm": "يتأكد", "app.components.CustomFieldsForm.descriptionMinLength": "يجب أن يكون الوصف على الأقل {min} حرفًا", "app.components.CustomFieldsForm.descriptionRequired": "الوصف مطلوب", "app.components.CustomFieldsForm.fieldMaximumItems": "على الأكثر {maxSelections, plural, one {# خيار} other {# خيارات}} يمكن تحديدها للحقل \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "على الأقل {minSelections, plural, one {# خيار} other {# خيارات}} يمكن تحديدها للحقل \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "الحقل \"{fieldName}\" مطلوب", "app.components.CustomFieldsForm.fileSizeLimit": "حد حجم الملف هو {maxFileSize} ميجا بايت.", "app.components.CustomFieldsForm.imageRequired": "الصورة مطلوبة", "app.components.CustomFieldsForm.minimumCoordinates2": "يجب أن يكون هناك حد أدنى من {numPoints} نقاط الخريطة.", "app.components.CustomFieldsForm.notPublic1": "*سيتم مشاركة هذه الإجابة فقط مع مديري المشاريع، وليس مع الجمهور.", "app.components.CustomFieldsForm.otherArea": "مك<PERSON> آخر", "app.components.CustomFieldsForm.progressBarLabel": "تقدم", "app.components.CustomFieldsForm.removeAnswer": "إزالة الإجابة", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*اختر العدد الذي تريده", "app.components.CustomFieldsForm.selectBetween": "*اختر بين الخيارين {minItems} و {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*اختر بالضبط {selectExactly, plural, one {# خيار} other {# خيارات}}", "app.components.CustomFieldsForm.selectMany": "*اختر العدد الذي تريده", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "انقر على الخريطة للرسم. ثم اسحب النقاط لتحريكها.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "اضغط على الخريطة للرسم.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "اضغط على الخريطة لإضافة إجابتك.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "اضغط على الخريطة أو اكتب عنوانًا أدناه لإضافة إجابتك.", "app.components.CustomFieldsForm.tapToAddALine": "انقر لإضافة خط", "app.components.CustomFieldsForm.tapToAddAPoint": "انقر لإضافة نقطة", "app.components.CustomFieldsForm.tapToAddAnArea": "انقر لإضافة منطقة", "app.components.CustomFieldsForm.titleMaxLength": "يجب أن يكون العنوان على الأكثر {max} حرفًا", "app.components.CustomFieldsForm.titleMinLength": "يجب أن يكون العنوان بطول {min} حرفًا على الأقل", "app.components.CustomFieldsForm.titleRequired": "العنوان مطلوب", "app.components.CustomFieldsForm.topicRequired": "يجب أن يكون هناك علامة واحدة على الأقل", "app.components.CustomFieldsForm.typeYourAnswer": "اكتب إجابتك", "app.components.CustomFieldsForm.typeYourAnswerRequired": "يجب عليك كتابة إجابتك", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* قم بتحميل ملف مضغوط يحتوي على ملف شكل واحد أو أكثر.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "إذا لم يتم عرض الموقع بين الخيارات أثناء الكتابة، فيمكنك إضافة إحداثيات صالحة بتنسيق \"خط العرض، خط الطول\" لتحديد موقع دقيق (على سبيل المثال: -33.019808، -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "بعض الحقول كانت غير صالحة. يُرجى تصحيح الأخطاء والمحاولة مجددًا.", "app.components.ErrorBoundary.errorFormErrorGeneric": "حدث خطأ غير معروف أثناء إرسال تقريرك. يُرجى المحاولة مجددًا.", "app.components.ErrorBoundary.errorFormLabelClose": "إغلاق", "app.components.ErrorBoundary.errorFormLabelComments": "ما الذي حصل؟", "app.components.ErrorBoundary.errorFormLabelEmail": "الب<PERSON>يد الإلكتروني", "app.components.ErrorBoundary.errorFormLabelName": "الاسم", "app.components.ErrorBoundary.errorFormLabelSubmit": "إرسال", "app.components.ErrorBoundary.errorFormSubtitle": "تم إخطار فريقنا.", "app.components.ErrorBoundary.errorFormSubtitle2": "إذا كنت بحاجة إلى المساعدة، أخبرنا بما حصل أدناه.", "app.components.ErrorBoundary.errorFormSuccessMessage": "تم إرسال ملاحظاتك. شكرًا لك!", "app.components.ErrorBoundary.errorFormTitle": "يبدو أنّ هناك مشكلة ما.", "app.components.ErrorBoundary.genericErrorWithForm": "حدث خطأ ما، ولا يمكننا عرض هذا المحتوى. يُرجى المحاولة مجددًا، أو {openForm}!", "app.components.ErrorBoundary.openFormText": "ساعدنا في تحديد الخلل", "app.components.ErrorToast.budgetExceededError": "ليس لديك ميزانية كافية", "app.components.ErrorToast.votesExceededError": "ليس لديك ما يكفي من الأصوات المتبقية", "app.components.EventAttendanceButton.forwardToFriend": "إرسال إلى صديق", "app.components.EventAttendanceButton.maxRegistrationsReached": "تم الوصول إلى الحد الأقصى لعدد التسجيلات للفعالية. لم يتبقَّ أي مكان.", "app.components.EventAttendanceButton.register": "يسجل", "app.components.EventAttendanceButton.registered": "مسجل", "app.components.EventAttendanceButton.seeYouThere": "اراك هناك!", "app.components.EventAttendanceButton.seeYouThereName": "أراك هناك ، {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "يمكن الآن رؤية معلومات أقل عن الحدث.", "app.components.EventCard.a11y_moreContentVisible": "يمكن الآن رؤية معلومات أكثر عن الحدث.", "app.components.EventCard.a11y_readMore": "اقرأ المزيد عن الحدث \"{eventTitle}\".", "app.components.EventCard.endsAt": "ينتهي بـ", "app.components.EventCard.readMore": "اقر<PERSON> أكثر", "app.components.EventCard.showLess": "<PERSON>ر<PERSON> أقل", "app.components.EventCard.showMore": "اعرض المزيد", "app.components.EventCard.startsAt": "يبدأ بـ", "app.components.EventPreviews.eventPreviewContinuousTitle2": "الأحداث القادمة والجارية في هذا المشروع", "app.components.EventPreviews.eventPreviewTimelineTitle3": "الأحداث القادمة والمستمرة في هذه المرحلة", "app.components.FileUploader.a11y_file": "الملف:", "app.components.FileUploader.a11y_filesToBeUploaded": "الملفات التي سيتم تحميلها: {fileNames}", "app.components.FileUploader.a11y_noFiles": "لم تتم إضافة ملفات.", "app.components.FileUploader.a11y_removeFile": "إزالة هذا الملف", "app.components.FileUploader.fileInputDescription": "انقر لتحديد ملف ما", "app.components.FileUploader.fileUploadLabel": "المُرفقات (بحجم أقصى 50 ميغابايت)", "app.components.FileUploader.file_too_large2": "لا يُسمح بالملفات التي يزيد حجمها عن {maxSizeMb}ميجابايت.", "app.components.FileUploader.incorrect_extension": "{fileName} غير مدعوم من قبل نظامنا، لذا لن يتم تحميله.", "app.components.FilterBoxes.a11y_allFilterSelected": "عامل تصفية الحالة المحدّد: الكل", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, no {# inputs} واحد {# input} آخر {# inputs}}", "app.components.FilterBoxes.a11y_removeFilter": "إزالة عامل التصفية", "app.components.FilterBoxes.a11y_selectedFilter": "عامل تصفية الحالة المحدّد: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "تم تحديد {numberOfSelectedTopics, plural, =0 {0 من عوامل التصفية} one {عامل تصفية واحد} other {# من عوامل التصفية}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "الكل", "app.components.FilterBoxes.areas": "التصفية بحسب المنطقة", "app.components.FilterBoxes.inputs": "المدخلات", "app.components.FilterBoxes.noValuesFound": "لا توجد قيم متاحة.", "app.components.FilterBoxes.showLess": "إظهار أقل", "app.components.FilterBoxes.showTagsWithNumber": "إظهار الكل ({numberTags})", "app.components.FilterBoxes.statusTitle": "الحالة", "app.components.FilterBoxes.topicsTitle": "الموضوعات", "app.components.FiltersModal.filters": "عوامل التصفية", "app.components.FolderFolderCard.a11y_folderDescription": "وصف المجلد:", "app.components.FolderFolderCard.a11y_folderTitle": "عنوان المجلد:", "app.components.FolderFolderCard.archived": "مؤرشف", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# من المشاريع} one {# مشروع} other {# من المشاريع}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "لا يمكن تغيير نوع الحقل بمجرد وجود عمليات إرسال.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "يكتب", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "الحفظ التلقائي", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "يتم تمكين الحفظ التلقائي افتراضيًا عند فتح محرر النماذج. في كل مرة تغلق فيها لوحة إعدادات الحقل باستخدام الزر \"X\"، سيتم تشغيل الحفظ تلقائيًا.", "app.components.GanttChart.timeRange.month": "شهر", "app.components.GanttChart.timeRange.quarter": "ربع", "app.components.GanttChart.timeRange.timeRangeMultiyear": "متعد<PERSON> السنوات", "app.components.GanttChart.timeRange.year": "سنة", "app.components.GanttChart.today": "اليوم", "app.components.GoBackButton.group.edit.goBack": "العودة", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "ع<PERSON> <PERSON><PERSON><PERSON> الصفحة السابقة", "app.components.HookForm.Feedback.errorTitle": "توجد مشكلة", "app.components.HookForm.Feedback.submissionError": "أعد المحاولة. إذا استمرت المشكلة، فتواصل معنا", "app.components.HookForm.Feedback.submissionErrorTitle": "عذرًا، حدثت مشكلة من جانبنا.", "app.components.HookForm.Feedback.successMessage": "تم إرسال النموذج بنجاح", "app.components.HookForm.PasswordInput.passwordLabel": "كلمة المرور", "app.components.HorizontalScroll.scrollLeftLabel": "قم بالتمرير إلى اليسار.", "app.components.HorizontalScroll.scrollRightLabel": "قم بالتمرير إلى اليمين.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} تم تحميل الأفكار.", "app.components.IdeaCards.filters": "المرشحات", "app.components.IdeaCards.filters.mostDiscussed": "الأكثر مناقشة", "app.components.IdeaCards.filters.newest": "جديد", "app.components.IdeaCards.filters.oldest": "قديم", "app.components.IdeaCards.filters.popular": "الأكثر اعجابا", "app.components.IdeaCards.filters.random": "عشوائي", "app.components.IdeaCards.filters.sortBy": "فرز حسب", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "تم تغيير الترتيب إلى: {currentSortType}", "app.components.IdeaCards.filters.trending": "الأكثر رواجًا", "app.components.IdeaCards.showMore": "اعرض المزيد", "app.components.IdeasMap.a11y_hideIdeaCard": "قم بإخفاء بطاقة الأفكار.", "app.components.IdeasMap.a11y_mapTitle": "عرض عام للخريطة", "app.components.IdeasMap.clickOnMapToAdd": "انقر على الخريطة لإضافة مدخلاتك", "app.components.IdeasMap.clickOnMapToAddAdmin2": "كمسؤول، يمكنك النقر على الخريطة لإضافة مدخلاتك، حتى لو لم تكن هذه المرحلة نشطة.", "app.components.IdeasMap.filters": "المرشحات", "app.components.IdeasMap.multipleInputsAtLocation": "مدخلات متعددة في هذا الموقع", "app.components.IdeasMap.noFilteredResults": "لم تحصد المُرشحات التي اخترتها أي نتائج", "app.components.IdeasMap.noResults": "لا يوجد نتائج", "app.components.IdeasMap.or": "أو", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, لا يوجد إبداءات عدم الإعجاب.} one {1 عدم إعجاب.} other {، # عدم الإعجاب.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, لا يوجد إعجابات.} one {, 1 إعجاب.} other {, # إعجاب .}}", "app.components.IdeasMap.signInLinkText": "تسجيل الدخول", "app.components.IdeasMap.signUpLinkText": "التسجيل", "app.components.IdeasMap.submitIdea2": "إرسال المدخلات", "app.components.IdeasMap.tapOnMapToAdd": "اضغط على الخريطة لإضافة مدخلاتك", "app.components.IdeasMap.tapOnMapToAddAdmin2": "كمسؤول، يمكنك النقر على الخريطة لإضافة مدخلاتك، حتى لو لم تكن هذه المرحلة نشطة.", "app.components.IdeasMap.userInputs2": "مدخلات من المشاركين", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, لا يوجد تعليقات} one {, تعليق واحد} other {, #تعليقات}}", "app.components.IdeasShow.bodyTitle": "الوصف", "app.components.IdeasShow.deletePost": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.editPost": "تعديل", "app.components.IdeasShow.goBack": "العودة", "app.components.IdeasShow.moreOptions": "المزيد من الخيارات", "app.components.IdeasShow.or": "أو", "app.components.IdeasShow.proposedBudgetTitle": "الميزانية المُقترحة", "app.components.IdeasShow.reportAsSpam": "الإبلاغ كإزعاج", "app.components.IdeasShow.send": "إرسال", "app.components.IdeasShow.skipSharing": "تخطي، سأقوم بهذا لاحقًا", "app.components.IdeasShowPage.signIn2": "تسجيل الدخول", "app.components.IdeasShowPage.sorryNoAccess": "عذرًا، لا يمكنك الوصول إلى هذه الصفحة. قد تحتاج إلى تسجيل الدخول أو تسجيل الاشتراك للوصول إليها.", "app.components.LocationInput.noOptions": "لا يوجد خيارات", "app.components.Modal.closeWindow": "أغلق النافذة", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "أدخل عنوان بريد إلكتروني جديد", "app.components.PageNotFound.goBackToHomePage": "عودة إلى الصفحة الرئيسية", "app.components.PageNotFound.notFoundTitle": "لم يتم العثور على الصفحة", "app.components.PageNotFound.pageNotFoundDescription": "تعذر العثور على الصفحة المطلوبة.", "app.components.PagesForm.descriptionMissingOneLanguageError": "أد<PERSON><PERSON> المحتوى بلغة واحدة على الأقل", "app.components.PagesForm.editContent": "المحتوى", "app.components.PagesForm.fileUploadLabel": "المُرفقات (بحجم أقصى 50 ميغابايت)", "app.components.PagesForm.fileUploadLabelTooltip": "ستظهر الملفات المُرفقة أسفل هذه الصفحة.", "app.components.PagesForm.navbarItemTitle": "الاسم في شريط التنقل", "app.components.PagesForm.pageTitle": "العنوان", "app.components.PagesForm.savePage": "حف<PERSON> الصفحة", "app.components.PagesForm.saveSuccess": "تم حفظ الصفحة بنجاح.", "app.components.PagesForm.titleMissingOneLanguageError": "أد<PERSON>ل العنوان بلغة واحدة على الأقل", "app.components.Pagination.back": "الصفحة السابقة", "app.components.Pagination.next": "الصفحة التالية", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "لقد أنفقت {votesCast}، وهو ما يتجاوز حد {votesLimit}. الرجاء إزالة بعض العناصر من سلتك وحاول مرة أخرى.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} اليسار", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "يجب عليك إنفاق ما لا يقل عن {votesMinimum} قبل أن تتمكن من إرسال سلة التسوق الخاصة بك.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "يجب عليك تحديد خيار وا<PERSON>د على الأقل قبل أن تتمكن من الإرسال.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "تحتاج إلى إضافة شيء ما إلى سلتك قبل أن تتمكن من إرسالها.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {لم يتبق أي رصيد} other {# من {totalNumberOfVotes, plural, one {رصيد واحد} other {# رصيد}} متبقي}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {لم يتبقَّ أي نقاط} other {# من {totalNumberOfVotes, plural, one {نقطة واحدة} other {# نقاط}} متبقية}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {لم يتبق أي رموز} other {# من {totalNumberOfVotes, plural, one {رمز واحد} other {# رموز}} متبقية}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {لم يتبقَّ أيُّ تصويت} other {# من {totalNumberOfVotes, plural, one {1 صوت} other {# تصويت}} متبقي}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# أصوات} one {# تصويت} other {# أصوات}} يقذف", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "لقد أدليت بـ {votesCast} تصويتات ، وهو ما يتجاوز الحد المسموح به وهو {votesLimit}. الرجاء إزالة بعض الأصوات وحاول مرة أخرى.", "app.components.ParticipationCTABars.addInput": "إضافة المدخلات", "app.components.ParticipationCTABars.allocateBudget": "خصّص ميزانيتك", "app.components.ParticipationCTABars.budgetSubmitSuccess": "لقد تم تقديم ميزانيتك بنجاح.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "مفتوح للمشاركة", "app.components.ParticipationCTABars.poll": "<PERSON>ذ الاستطلاع", "app.components.ParticipationCTABars.reviewDocument": "راجع المستند", "app.components.ParticipationCTABars.seeContributions": "انظر المساهمات", "app.components.ParticipationCTABars.seeEvents3": "مشاهدة الأحداث", "app.components.ParticipationCTABars.seeIdeas": "راجع الأفكار", "app.components.ParticipationCTABars.seeInitiatives": "انظر المبادرات", "app.components.ParticipationCTABars.seeIssues": "انظر القضايا", "app.components.ParticipationCTABars.seeOptions": "انظر الخيارات", "app.components.ParticipationCTABars.seePetitions": "انظر الالتماسات", "app.components.ParticipationCTABars.seeProjects": "شا<PERSON><PERSON> المشاريع", "app.components.ParticipationCTABars.seeProposals": "انظر المقترحات", "app.components.ParticipationCTABars.seeQuestions": "انظر الأسئلة", "app.components.ParticipationCTABars.submit": "يُقدِّم", "app.components.ParticipationCTABars.takeTheSurvey": "شارك في الاستبيان", "app.components.ParticipationCTABars.userHasParticipated": "لقد شاركت في هذا المشروع.", "app.components.ParticipationCTABars.viewInputs": "عرض المدخلات", "app.components.ParticipationCTABars.volunteer": "متطوع", "app.components.ParticipationCTABars.votesCounter.vote": "تصويت", "app.components.ParticipationCTABars.votesCounter.votes": "أصوات", "app.components.PasswordInput.a11y_passwordHidden": "كلمة المرور مخفية", "app.components.PasswordInput.a11y_passwordVisible": "كلمة المرور مرئية", "app.components.PasswordInput.a11y_strength1Password": "قوة كلمة المرور سيئة", "app.components.PasswordInput.a11y_strength2Password": "قوة كلمة المرور ضعيفة", "app.components.PasswordInput.a11y_strength3Password": "قوة كلمة المرور متوسطة", "app.components.PasswordInput.a11y_strength4Password": "قوة كلمة المرور قوية", "app.components.PasswordInput.a11y_strength5Password": "قوة كلمة المرور قوية جداً", "app.components.PasswordInput.hidePassword": "اخفاء كلمة المرور", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "قصير جداً (الحد الأدنى {minimumPasswordLength} أحرف)", "app.components.PasswordInput.minimumPasswordLengthError": "أدخل كلمة مرور لا يقل طولها عن {minimumPasswordLength} حروف", "app.components.PasswordInput.passwordEmptyError": "أدخل كلمة مرورك", "app.components.PasswordInput.passwordStrengthTooltip1": "لجعل كلمة مرورك أقوى:", "app.components.PasswordInput.passwordStrengthTooltip2": "استخدم خليط من الأحرف الصغيرة غير المتتالية والأحرف الكبيرة والأرقام والأحرف الخاصة وعلامات الترقيم", "app.components.PasswordInput.passwordStrengthTooltip3": "تجنب الكلمات الشائعة أو سهلة التخمين", "app.components.PasswordInput.passwordStrengthTooltip4": "قم بزيادة الطول", "app.components.PasswordInput.showPassword": "إظهار كلمة المرور", "app.components.PasswordInput.strength1Password": "سيئ", "app.components.PasswordInput.strength2Password": "ضعيف", "app.components.PasswordInput.strength3Password": "متوسط", "app.components.PasswordInput.strength4Password": "قوي", "app.components.PasswordInput.strength5Password": "قوي جداً", "app.components.PostCardsComponents.list": "القائمة", "app.components.PostCardsComponents.map": "الخريطة", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "أضِف تحديثًا رسميًا", "app.components.PostComponents.OfficialFeedback.cancel": "إلغاء", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "هل تريد حذف هذا التحديث الرسمي بالتأكيد؟", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "تعديل", "app.components.PostComponents.OfficialFeedback.lastEdition": "آخر تعديل في {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "آخر تحديث: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "رسمي", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "اختر كيف يمكن للآخرين رؤية اسمك", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "اسم مؤلف التحديث الرسمي", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "النص الأساسي للتحديث الرسمي", "app.components.PostComponents.OfficialFeedback.officialUpdates": "التحديثات الرسمية", "app.components.PostComponents.OfficialFeedback.postedOn": "تم النشر في {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "نشر", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "اعرض التحديثات السابقة", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "أضِف تحديثًا...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "نعتذر، حدثت مشكلة ما", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "تحديث الرسالة", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "تم نشر تحديثك بنجاح!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "ادعم مساهمتي '{postTitle}' على {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "ادعم مساهمتي: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "ادعم مساهمتي: {postTitle}. ", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "ما رأيك بهذه الفكرة؟ صوّت وشارك النقاش عبر {postUrl} لجعل صوتك مسموعًا!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "ادعم فكرتي: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "ادعم فكرتي: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "ما رأيك بهذا المُقترح؟ صوّت وشارك النقاش عبر {postUrl} لجعل صوتك مسموعًا!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "ادعم مُقترحي: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "ادعم مبادرتي : {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "لقد نشرت مشكلة '{postTitle}' على {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "لقد نشرت مشكلة للتو: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "لقد نشرت مشكلة للتو: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "ادعم خياري المُقترح '{postTitle}' على {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "ادعم خياري المُقترح:  {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "ادعم خياري: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "ادعم عريضتي '{postTitle}' على {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "ادعم عريضتي: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "ادعم عريضتي: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "ادعم مشروعي '{postTitle}' على {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "ادعم مشروعي: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "ادعم مشروعي: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "ادعم اقتراحي '{postTitle}' في {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "ادعم اقتراحي: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "لقد نشرت للتو اقتراحًا لـ {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "انضم إلى مناقشة هذا السؤال '{postTitle}' على {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "انضم إلى المناقشة: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "انضم إلى المناقشة: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "صوّت لـ {postTitle} على", "app.components.PostComponents.linkToHomePage": "اربط بالصفحة الرئيسية", "app.components.PostComponents.readMore": "اقرأ المزيد...", "app.components.PostComponents.topics": "المواضيع", "app.components.ProjectArchivedIndicator.archivedProject": "للأسف، لا يمكنك المشاركة في هذا المشروع بعد الآن لأنه قد تمت أرشفته", "app.components.ProjectArchivedIndicator.previewProject": "مشروع المشروع:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "مرئية فقط للمشرفين وأولئك الذين لديهم رابط المعاينة.", "app.components.ProjectCard.a11y_projectDescription": "وصف المشروع:", "app.components.ProjectCard.a11y_projectTitle": "عنوان المشروع:", "app.components.ProjectCard.addYourOption": "<PERSON><PERSON><PERSON> ", "app.components.ProjectCard.allocateYourBudget": "خصّص ميزانيتك", "app.components.ProjectCard.archived": "مؤرشف", "app.components.ProjectCard.comment": "تعليق", "app.components.ProjectCard.contributeYourInput": "ساهم بمُدخلك ", "app.components.ProjectCard.finished": "مكتمل", "app.components.ProjectCard.joinDiscussion": "انضم إلى المناقشة ", "app.components.ProjectCard.learnMore": "حمّل المزيد", "app.components.ProjectCard.reaction": "رد فعل", "app.components.ProjectCard.readTheReport": "اقرأ التقرير", "app.components.ProjectCard.reviewDocument": "راجع المستند", "app.components.ProjectCard.submitAnIssue": "قُم بتقديم مُشكلة ", "app.components.ProjectCard.submitYourIdea": "قُم بتقديم فكرة ", "app.components.ProjectCard.submitYourInitiative": "أرسل مبادرتك", "app.components.ProjectCard.submitYourPetition": "أرسل عريضتك", "app.components.ProjectCard.submitYourProject": "قُم بتقديم مشروعك ", "app.components.ProjectCard.submitYourProposal": "أرسل اقتراحك", "app.components.ProjectCard.takeThePoll": "شارك في الاستطلاع", "app.components.ProjectCard.takeTheSurvey": "شارك في الاستبيان", "app.components.ProjectCard.viewTheContributions": "اعرض المُساهمات", "app.components.ProjectCard.viewTheIdeas": "اعرض الأفكار", "app.components.ProjectCard.viewTheInitiatives": "عرض المبادرات", "app.components.ProjectCard.viewTheIssues": "اعرض المشكلة ", "app.components.ProjectCard.viewTheOptions": "اعرض الخيارات", "app.components.ProjectCard.viewThePetitions": "عرض الالتماسات", "app.components.ProjectCard.viewTheProjects": "اعرض المشروعات ", "app.components.ProjectCard.viewTheProposals": "عرض المقترحات", "app.components.ProjectCard.viewTheQuestions": "اعرض الأسئلة", "app.components.ProjectCard.vote": "تصويت", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# تعليق} other {# تعليقات}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, no {# contributions} واحدة {# contribution} أخرى {# contributions}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {لا توجد أفكار بعد} one {# فكرة} other {# أفكار}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# مبادرات} one {# مبادرة} other {# مبادرات}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, no {# issues} واحدة {# issue}  أخرى {# issues}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, no {# options} واحدة {# option} أخرى {# options}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# عرائض} one {# عرائض} other {# عرائض}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, no {# projects} واحد {# project} آخر {# projects}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# مقترحات} one {# مقترحات} other {# مقترحات}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, no {# questions} واحد {# question} آخر {# questions}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# تعليقات} one {# تعليقات} other {# تعليقات}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# مدخلات} one {# مدخل} other {# مدخلات}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# مشروع} one {# مشروع} other {# مشروع/مشاريع}}", "app.components.ProjectFolderCards.components.Topbar.all": "الكل", "app.components.ProjectFolderCards.components.Topbar.archived": "مؤرشف", "app.components.ProjectFolderCards.components.Topbar.draft": "مسوّدة", "app.components.ProjectFolderCards.components.Topbar.filterBy": "تصفية بحسب", "app.components.ProjectFolderCards.components.Topbar.published2": "نُشرت", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "الوسم", "app.components.ProjectFolderCards.noProjectYet": "لا توجد مشاريع بعد", "app.components.ProjectFolderCards.noProjectsAvailable": "لا تتوفر أي مشاريع", "app.components.ProjectFolderCards.showMore": "اعرض المزيد", "app.components.ProjectFolderCards.stayTuned": "لا تتوقف عن المتابعة، سيظهر مشروع عمّا قريب.", "app.components.ProjectFolderCards.tryChangingFilters": "جرِّب تغيير عوامل التصفية المحددة.", "app.components.ProjectTemplatePreview.alsoUsedIn": "تم استخدامه أيضًا في هذه المُدن", "app.components.ProjectTemplatePreview.copied": "تم النسخ", "app.components.ProjectTemplatePreview.copyLink": "ان<PERSON><PERSON> الرابط", "app.components.QuillEditor.alignCenter": "توسيط النص", "app.components.QuillEditor.alignLeft": "محاذاة إلى اليسار", "app.components.QuillEditor.alignRight": "محاذاة إلى اليمين", "app.components.QuillEditor.bold": "عريض", "app.components.QuillEditor.clean": "إزالة التنسيق", "app.components.QuillEditor.customLink": "أضِف زرًا", "app.components.QuillEditor.customLinkPrompt": "أد<PERSON>ل الرابط:", "app.components.QuillEditor.edit": "تعديل", "app.components.QuillEditor.image": "تحميل صورة", "app.components.QuillEditor.imageAltPlaceholder": "وصف موجز للصورة", "app.components.QuillEditor.italic": "مائل", "app.components.QuillEditor.link": "أضِف رابطًا", "app.components.QuillEditor.linkPrompt": "أد<PERSON>ل الرابط:", "app.components.QuillEditor.normalText": "عادي", "app.components.QuillEditor.orderedList": "قائمة مرتبة", "app.components.QuillEditor.remove": "إزالة", "app.components.QuillEditor.save": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.subtitle": "العنوان الفرعي", "app.components.QuillEditor.title": "العنوان", "app.components.QuillEditor.unorderedList": "قائمة غير مرتبة", "app.components.QuillEditor.video": "أضِف فيديو", "app.components.QuillEditor.videoPrompt": "أدخل فيديو:", "app.components.QuillEditor.visitPrompt": "زُر الرابط:", "app.components.ReactionControl.completeProfileToReact": "أكمل ملف التعريف الخاص بك للرد", "app.components.ReactionControl.dislike": "لم يعجبنى", "app.components.ReactionControl.dislikingDisabledMaxReached": "لقد وصلت إلى الحد الأقصى لعدد إبداءات عدم الإعجاب في {projectName}", "app.components.ReactionControl.like": "<PERSON><PERSON><PERSON>", "app.components.ReactionControl.likingDisabledMaxReached": "لقد وصلت إلى الحد الأقصى من الإعجابات في {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "سيتم تفعيل الرد بمجرد بدء هذه المرحلة", "app.components.ReactionControl.reactingDisabledPhaseOver": "لم يعد من الممكن الرد في هذه المرحلة", "app.components.ReactionControl.reactingDisabledProjectInactive": "لم يعد بإمكانك الرد على الأفكار في {projectName}", "app.components.ReactionControl.reactingNotEnabled": "رد الفعل غير ممكن حاليا لهذا المشروع", "app.components.ReactionControl.reactingNotPermitted": "يتم تفعيل الرد لمجموعات معينة فقط", "app.components.ReactionControl.reactingNotSignedIn": "تسجيل الدخول للرد.", "app.components.ReactionControl.reactingPossibleLater": "سيبد<PERSON> الرد على {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "تحقق من هويتك من أجل الرد.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "تاريخ الحدث: {startDate} في {startTime} إلى {endDate} في {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "تاريخ الحدث: {eventDate} من {startTime} إلى {endTime}.", "app.components.Sharing.linkCopied": "تم نسخ الرابط", "app.components.Sharing.or": "أو", "app.components.Sharing.share": "مشاركة", "app.components.Sharing.shareByEmail": "مشاركة عبر البريد الإلكتروني", "app.components.Sharing.shareByLink": "ان<PERSON><PERSON> الرابط", "app.components.Sharing.shareOnFacebook": "مشاركة على الفيسبوك", "app.components.Sharing.shareOnTwitter": "مشاركة على تويتر", "app.components.Sharing.shareThisEvent": "شارك هذا الحدث", "app.components.Sharing.shareThisFolder": "مشاركة", "app.components.Sharing.shareThisProject": "مشاركة هذا المشروع", "app.components.Sharing.shareViaMessenger": "شارك عبر الماسنجر", "app.components.Sharing.shareViaWhatsApp": "شارك عبر الواتس أب", "app.components.SideModal.closeButtonAria": "إغلاق", "app.components.StatusModule.futurePhase": "أنت تشاهد مرحلة لم تبدأ بعد. ستتمكن من المشاركة عندما تبدأ المرحلة.", "app.components.StatusModule.modifyYourSubmission1": "تعديل إرساليتك", "app.components.StatusModule.submittedUntil3": "قد يتم تقديم صوتك حتى", "app.components.TopicsPicker.numberOfSelectedTopics": "تم تحديد {numberOfSelectedTopics, plural, =0 {لا يوجد موضوعات} one {موضوع واحد} other {# موضوعات}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "توسيع الصورة", "app.components.UI.MoreActionsMenu.moreOptions": "المزيد من الخيارات", "app.components.UI.MoreActionsMenu.showMoreActions": "إظهار مزيد من الإجراءات", "app.components.UI.PhaseFilter.noAppropriatePhases": "لم يتم العثور على المراحل المناسبة لهذا المشروع", "app.components.UI.RemoveImageButton.a11y_removeImage": "إزالة", "app.components.UI.TranslateButton.original": "الأصلي", "app.components.UI.TranslateButton.translate": "ترجمة", "app.components.Unauthorized.additionalInformationRequired": "مطلوب معلومات إضافية بالنسبة لك للمشاركة.", "app.components.Unauthorized.completeProfile": "الملف الشخصي الكامل", "app.components.Unauthorized.completeProfileTitle": "أكمل ملفك الشخصي للمشاركة", "app.components.Unauthorized.noPermission": "ليس لديك إذن لعرض هذه الصفحة", "app.components.Unauthorized.notAuthorized": "عذرًا، لست مخوَّلاً للوصول إلى هذه الصفحة.", "app.components.Upload.errorImageMaxSizeExceeded": "حجم الصورة التي حدّدتها أكبر من {maxFileSize} ميغابايت", "app.components.Upload.errorImagesMaxSizeExceeded": "صورة أو أكثر من الصور التي حدّدتها حجمها أكبر من {maxFileSize} ميغابايت", "app.components.Upload.onlyOneImage": "يمكنك تحميل صورة واحدة فقط", "app.components.Upload.onlyXImages": "يمكنك تحميل {maxItemsCount} من الصور فقط", "app.components.Upload.remaining": "تبقى", "app.components.Upload.uploadImageLabel": "حدّد صورة (بحجم أقصى {maxImageSizeInMb} ميغابايت)", "app.components.Upload.uploadMultipleImagesLabel": "حدّد صورة واحدة أو أكثر", "app.components.UpsellTooltip.tooltipContent": "لا يتم تضمين هذه الميزة في خطتك الحالية. تحدث إلى مدير نجاح الحكومة أو المسؤول لإلغاء قفلها.", "app.components.UserName.anonymous": "مجهول", "app.components.UserName.anonymousTooltip2": "قرر هذا المستخدم إخفاء مساهمته", "app.components.UserName.authorWithNoNameTooltip": "لقد تم إنشاء اسمك تلقائيًا لأنك لم تدخل اسمك. يرجى تحديث ملفك الشخصي إذا كنت ترغب في تغييره.", "app.components.UserName.deletedUser": "مؤلف غير معروف", "app.components.UserName.verified": "تم التحقق منه", "app.components.VerificationModal.verifyAuth0": "التحقق باستخدام NemID", "app.components.VerificationModal.verifyBOSA": "تحقق باستخدام itsme أو eID", "app.components.VerificationModal.verifyBosaFas": "التحقق باستخدام itsme أو eID", "app.components.VerificationModal.verifyClaveUnica": "تحقق باستخدام Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "التحقق باستخدام SSO المزيف", "app.components.VerificationModal.verifyIdAustria": "التحقق من خلال هوية النمسا", "app.components.VerificationModal.verifyKeycloak": "التحقق باستخدام ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "تحقق مع MitID", "app.components.VerificationModal.verifyTwoday2": "التحقق باستخدام BankID أو Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "تحقق من هويتك", "app.components.VoteControl.budgetingFutureEnabled": "يُمكنك تخصيص ميزانيتك بداية من {enabledFromDate}. ", "app.components.VoteControl.budgetingNotPermitted": "يُمكن تفعيل إعداد الميزانية القائمة على المشاركة لمجموعات مُعينة فقط. ", "app.components.VoteControl.budgetingNotPossible": "لا يُمكن إجراء تغييرات في ميزانيتك في الوقت الحالي. ", "app.components.VoteControl.budgetingNotVerified": "يُرجى {verifyAccountLink} للاستمرار. ", "app.components.VoteInputs._shared.currencyLeft1": "لقد تبقى لديك {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "لديك {votesLeft, plural, =0 {لا يوجد رصيد متبقي} other {# من {totalNumberOfVotes, plural, one {رصيد واحد} other {# رصيد}} متبقي}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "لم يتبق لديك أي نقاط {votesLeft, plural, =0 {} other {# من {totalNumberOfVotes, plural, one {1 نقطة} other {# نقاط}} متبقية}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "لديك {votesLeft, plural, =0 {لا يوجد أي رموز متبقية} other {# من {totalNumberOfVotes, plural, one {1 رمز} other {# رموز}} متبقية}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "لديك {votesLeft, plural, =0 {لا يوجد أي تصويت متبقي} other {# من {totalNumberOfVotes, plural, one {1 صوت} other {# تصويت}} متبقي}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "لقد أرسلت ميزانيتك بالفعل. لتعديلها، انقر على \"تعديل إرسالك\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "لقد أرسلت ميزانيتك بالفعل. لتعديلها، ارجع إلى صفحة المشروع وانقر على \"تعديل ميزانيتك\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "الميزانية غير متاحة، لأن هذه المرحلة غير نشطة.", "app.components.VoteInputs.single.youHaveVotedForX2": "لقد صوتت ل {votes, plural, =0 {# خيارات} one {# خيار} other {# خيارات}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "وهذا يعني أنك ستفقد جميع البيانات المرتبطة بهذا الإدخال، مثل التعليقات وردود الفعل والتصويتات. لا يمكن التراجع عن هذا الإجراء.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "هل أنت متأكد أنك تريد حذف هذا الإدخال؟", "app.components.admin.PostManager.components.PostTable.Row.cancel": "يلغي", "app.components.admin.PostManager.components.PostTable.Row.confirm": "يتأكد", "app.components.admin.SlugInput.resultingURL": "عنوان URL الناتج", "app.components.admin.SlugInput.slugTooltip": "معرِّف الصفحة هو مجموعة فريدة من الكلمات في نهاية عنوان ويب الصفحة، أو باختصار URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "إذا غيَّرت عنوان URL، فلن تعمل الروابط المؤدية إلى الصفحة باستخدام عنوان URL القديمة.", "app.components.admin.SlugInput.urlSlugLabel": "معرِّف الصفحة", "app.components.admin.UserFilterConditions.addCondition": "أضِف شرطًا", "app.components.admin.UserFilterConditions.field_email": "الب<PERSON>يد الإلكتروني", "app.components.admin.UserFilterConditions.field_event_attendance": "تسجيلات الحدث", "app.components.admin.UserFilterConditions.field_follow": "يتبع", "app.components.admin.UserFilterConditions.field_lives_in": "يعيش في", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "استطلاع رأي المجتمع", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "تم التفاعل مع مُدخل بالحالة ", "app.components.admin.UserFilterConditions.field_participated_in_project": "ساهم في المشروع", "app.components.admin.UserFilterConditions.field_participated_in_topic": "ساهم في الموضوع", "app.components.admin.UserFilterConditions.field_registration_completed_at": "تاريخ التسجيل", "app.components.admin.UserFilterConditions.field_role": "الدور", "app.components.admin.UserFilterConditions.field_verified": "التحقق", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "الفكرة", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "المقترحات", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "لم يتم تسجيله لأي من هذه الأحداث", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "لم يتم تسجيله في أي حدث", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "مسجل لأحد هذه الأحداث", "app.components.admin.UserFilterConditions.predicate_attends_something": "تم تسجيله لحدث واحد على الأقل", "app.components.admin.UserFilterConditions.predicate_begins_with": "يبدأ بـ", "app.components.admin.UserFilterConditions.predicate_commented_in": "علّق", "app.components.admin.UserFilterConditions.predicate_contains": "يحتوى على", "app.components.admin.UserFilterConditions.predicate_ends_on": "ينتهي في", "app.components.admin.UserFilterConditions.predicate_has_value": "لديه قيمة", "app.components.admin.UserFilterConditions.predicate_in": "قام بأي إجراء", "app.components.admin.UserFilterConditions.predicate_is": "هو", "app.components.admin.UserFilterConditions.predicate_is_admin": "هو مسؤول", "app.components.admin.UserFilterConditions.predicate_is_after": "بعد", "app.components.admin.UserFilterConditions.predicate_is_before": "قبل", "app.components.admin.UserFilterConditions.predicate_is_checked": "تم التحقق منه", "app.components.admin.UserFilterConditions.predicate_is_empty": "فارغ", "app.components.admin.UserFilterConditions.predicate_is_equal": "هو", "app.components.admin.UserFilterConditions.predicate_is_exactly": "هو بالضبط", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "أك<PERSON>ر من", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "أكبر من أو يساوي", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "هو مستخدم عادي", "app.components.admin.UserFilterConditions.predicate_is_not_area": "يستثني المنطقة", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "يستبعد المجلد", "app.components.admin.UserFilterConditions.predicate_is_not_input": "يستبعد الإدخال", "app.components.admin.UserFilterConditions.predicate_is_not_project": "يستثني المشروع", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "يستبعد الموضوع", "app.components.admin.UserFilterConditions.predicate_is_one_of": "هو وا<PERSON>د من", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "<PERSON><PERSON><PERSON> المجالات", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "<PERSON><PERSON><PERSON> المجلدات", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "<PERSON><PERSON><PERSON> المدخلات", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "<PERSON><PERSON><PERSON> الم<PERSON>ا<PERSON>يع", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "<PERSON><PERSON><PERSON> الموضوعات", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "هو مدير المشروع", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "أقل من", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "أقل من أو يساوي", "app.components.admin.UserFilterConditions.predicate_is_verified": "تم التحقق منه", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "لا يبدأ بـ", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "لم يُعلق", "app.components.admin.UserFilterConditions.predicate_not_contains": "لا يحتوي على", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "لا ينتهي في", "app.components.admin.UserFilterConditions.predicate_not_has_value": "ليس لديه قيمة", "app.components.admin.UserFilterConditions.predicate_not_in": "لم يساهم", "app.components.admin.UserFilterConditions.predicate_not_is": "ليس", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "ليس مسؤولًا", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "لم يتم التحقق منه", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "ليس فارغًا", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "ليس", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "ليس مستخدمًا عاديًا", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "ليس واحدًا من", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "ليس مدير المشروع", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "لم يتم التحقق منه", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "لم ينشُر مُدخلاً ", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "ل<PERSON> <PERSON><PERSON><PERSON> على التعليق", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "لم يتفاعل مع المدخلات", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "لم يسجل في هذا الحدث", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "لم يتم إجراء الاستطلاع", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "لم يتطوع", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "لم يشارك في التصويت", "app.components.admin.UserFilterConditions.predicate_nothing": "لا شئ", "app.components.admin.UserFilterConditions.predicate_posted_input": "نَشَر مُدخلاً", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "ر<PERSON> <PERSON><PERSON>ى التعليق", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "يتفاعل مع المدخلات", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "مسجل في حدث ما", "app.components.admin.UserFilterConditions.predicate_something": "شئ ما", "app.components.admin.UserFilterConditions.predicate_taken_survey": "أجرى استطلاعًا", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "تطوع", "app.components.admin.UserFilterConditions.predicate_voted_in3": "شارك في التصويت", "app.components.admin.UserFilterConditions.rulesFormLabelField": "A", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "B", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "C", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "لن تتلقى إشعارات بشأن مساهمتك", "app.components.anonymousParticipationModal.cancel": "يلغي", "app.components.anonymousParticipationModal.continue": "يكمل", "app.components.anonymousParticipationModal.participateAnonymously": "شارك بشكل مجهول", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "سيؤدي هذا <b>إلى إخفاء ملفك الشخصي</b> بأمان عن المسؤولين ومديري المشاريع والمقيمين الآخرين لهذه المساهمة المحددة حتى لا يتمكن أي شخص من ربط هذه المساهمة بك. لا يمكن تعديل المساهمات المجهولة، وتعتبر نهائية.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "يعد جعل منصتنا آمنة لكل مستخدم أولوية قصوى بالنسبة لنا. الكلمات مهمة ، لذا يرجى أن تكون لطيفًا مع بعضكما البعض.", "app.components.avatar.titleForAccessibility": "الملف الشخصي لـ {fullName}", "app.components.customFields.mapInput.removeAnswer": "إزالة الإجابة", "app.components.customFields.mapInput.undo": "التراجع", "app.components.customFields.mapInput.undoLastPoint": "التراجع عن النقطة الأخيرة", "app.components.followUnfollow.follow": "يتبع", "app.components.followUnfollow.followADiscussion": "تابع المناقشة", "app.components.followUnfollow.followTooltipInputPage2": "تؤدي المتابعة إلى تشغيل تحديثات البريد الإلكتروني حول تغييرات الحالة والتحديثات الرسمية والتعليقات. يمكنك {unsubscribeLink} في أي وقت.", "app.components.followUnfollow.followTooltipProjects2": "المتابعة تؤدي إلى تحديثات البريد الإلكتروني حول تغييرات المشروع. يمكنك {unsubscribeLink} في أي وقت.", "app.components.followUnfollow.unFollow": "الغاء المتابعة", "app.components.followUnfollow.unsubscribe": "إلغاء الاشتراك", "app.components.followUnfollow.unsubscribeUrl": "/الملف الشخصي/تحرير", "app.components.form.ErrorDisplay.guidelinesLinkText": "إرشاداتنا", "app.components.form.ErrorDisplay.next": "التالي", "app.components.form.ErrorDisplay.previous": "السابق", "app.components.form.ErrorDisplay.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "جارٍ بدء الكتابة للبحث بحسب البريد الإلكتروني للمستخدم أو اسمه...", "app.components.form.anonymousSurveyMessage2": "جميع الردود على هذا الاستطلاع مجهولة المصدر.", "app.components.form.backToInputManager": "العودة إلى مدير الإدخال", "app.components.form.backToProject": "العودة إلى المشروع", "app.components.form.components.controls.mapInput.removeAnswer": "إزالة الإجابة", "app.components.form.components.controls.mapInput.undo": "الغاء التحميل", "app.components.form.components.controls.mapInput.undoLastPoint": "التراجع عن النقطة الأخيرة", "app.components.form.controls.addressInputAriaLabel": "إد<PERSON>ال العنوان", "app.components.form.controls.addressInputPlaceholder6": "أدخل عنوانا...", "app.components.form.controls.adminFieldTooltip": "لا يظهر الحقل إلا للمسؤولين فقط", "app.components.form.controls.allStatementsError": "يجب تحديد إجابة لجميع العبارات.", "app.components.form.controls.back": "خلف", "app.components.form.controls.clearAll": "<PERSON><PERSON><PERSON> ال<PERSON>", "app.components.form.controls.clearAllScreenreader": "مسح جميع الإجابات من سؤال المصفوفة أعلاه", "app.components.form.controls.clickOnMapMultipleToAdd3": "انقر على الخريطة للرسم. ثم اسحب على النقاط لتحريكها.", "app.components.form.controls.clickOnMapToAddOrType": "انقر على الخريطة أو اكتب عنوانًا أدناه لإضافة إجابتك.", "app.components.form.controls.confirm": "يتأكد", "app.components.form.controls.cosponsorsPlaceholder": "ابد<PERSON> بكتابة اسم للبحث", "app.components.form.controls.currentRank": "الرتبة الحالية:", "app.components.form.controls.minimumCoordinates2": "مطلوب ما لا يقل عن {numPoints} نقاط الخريطة.", "app.components.form.controls.noRankSelected": "لم يتم تحديد رتبة", "app.components.form.controls.notPublic1": "*لن تتم مشاركة هذه الإجابة إلا مع مديري المشاريع، وليس للجمهور.", "app.components.form.controls.optionalParentheses": "(خياري)", "app.components.form.controls.rankingInstructions": "اسحب وأفلِت لترتيب الخيارات.", "app.components.form.controls.selectAsManyAsYouLike": "* اختر العدد الذي تريده", "app.components.form.controls.selectBetween": "* اختر بين {minItems} و {maxItems} الخيارات", "app.components.form.controls.selectExactly2": "*اختر بالضبط {selectExactly, plural, one {# خيار} other {# خيارات}}", "app.components.form.controls.selectMany": "*اختر أكبر عدد تريده", "app.components.form.controls.tapOnFullscreenMapToAdd4": "اضغط على الخريطة للرسم. ثم اسحب على النقاط لتحريكها.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "اضغط على الخريطة للرسم.", "app.components.form.controls.tapOnMapMultipleToAdd3": "اضغط على الخريطة لإضافة إجابتك.", "app.components.form.controls.tapOnMapToAddOrType": "اضغط على الخريطة أو اكتب عنوانًا أدناه لإضافة إجابتك.", "app.components.form.controls.tapToAddALine": "انقر لإضافة خط", "app.components.form.controls.tapToAddAPoint": "انقر لإضافة نقطة", "app.components.form.controls.tapToAddAnArea": "انقر لإضافة منطقة", "app.components.form.controls.uploadShapefileInstructions": "* قم بتحميل ملف مضغوط يحتوي على ملف شكل واحد أو أكثر.", "app.components.form.controls.validCordinatesTooltip2": "إذا لم يتم عرض الموقع بين الخيارات أثناء الكتابة، فيمكنك إضافة إحداثيات صالحة بتنسيق \"خط العرض، خط الطول\" لتحديد موقع دقيق (على سبيل المثال: -33.019808، -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} من {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} من {total}، {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} من {total}، حيث {maxValue} يساوي {maxLabel}", "app.components.form.error": "خطأ", "app.components.form.locationGoogleUnavailable": "تعذر تعبئة حقل الموقع المتوفر من خرائط google.", "app.components.form.progressBarLabel": "تقدم المسح", "app.components.form.submit": "إرسال", "app.components.form.submitApiError": "حدثت مشكلة أثناء إرسال النموذج. يرجى التحقق بحثًا عن أي أخطاء وإعادة المحاولة.", "app.components.form.verifiedBlocked": "لا يمكنك تعديل هذا الحقل لأنه يحتوي على معلومات تم التحقق منها", "app.components.formBuilder.Page": "الصفحة", "app.components.formBuilder.accessibilityStatement": "بيان إمكانية الوصول", "app.components.formBuilder.addAnswer": "إضافة إجابة", "app.components.formBuilder.addStatement": "إضافة بيان", "app.components.formBuilder.agree": "يوافق", "app.components.formBuilder.ai1": "منظمة العفو الدولية", "app.components.formBuilder.aiUpsellText1": "إذا كان لديك حق الوصول إلى حزمة الذكاء الاصطناعي الخاصة بنا، فستتمكن من تلخيص وتصنيف الردود النصية باستخدام الذكاء الاصطناعي", "app.components.formBuilder.askFollowUpToggleLabel": "اطلب المتابعة", "app.components.formBuilder.bad": "سيء", "app.components.formBuilder.buttonLabel": "ملص<PERSON> الزر", "app.components.formBuilder.buttonLink": "رابط الزر", "app.components.formBuilder.cancelLeaveBuilderButtonText": "يلغي", "app.components.formBuilder.category": "فئة", "app.components.formBuilder.chooseMany": "اختيار متعدد", "app.components.formBuilder.chooseOne": "اختيار واحد", "app.components.formBuilder.close": "إغلاق", "app.components.formBuilder.closed": "مغلق", "app.components.formBuilder.configureMap": "تكوين الخريطة", "app.components.formBuilder.confirmLeaveBuilderButtonText": "نعم أريد الرحيل", "app.components.formBuilder.content": "مح<PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "يستمر إلى", "app.components.formBuilder.cosponsors": "الرعاة المشاركون", "app.components.formBuilder.default": "افتراضي", "app.components.formBuilder.defaultContent": "محتوى افتراضي", "app.components.formBuilder.delete": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.description": "الوصف", "app.components.formBuilder.disabledBuiltInFieldTooltip": "تمت إضافة هذا المحتوى في النموذج من قبل. لا يجوز استخدام محتوى افتراضي أكثر من مرة واحدة.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "إن إضافة محتوى مخصص ليس جزءًا من ترخيصك الحالي. تواصل مع مدير GovSuccess الخاص بك لمعرفة المزيد حول هذا الأمر.", "app.components.formBuilder.disagree": "لا أوافق", "app.components.formBuilder.displayAsDropdown": "عرض كقائمة منسدلة", "app.components.formBuilder.displayAsDropdownTooltip": "عرض الخيارات في قائمة منسدلة. إذا كان لديك العديد من الخيارات، فمن المستحسن القيام بذلك.", "app.components.formBuilder.done": "تم", "app.components.formBuilder.drawArea": "منطقة الرسم", "app.components.formBuilder.drawRoute": "ارسم الطريق", "app.components.formBuilder.dropPin": "إسقاط دبوس", "app.components.formBuilder.editButtonLabel": "تعديل", "app.components.formBuilder.emptyImageOptionError": "قم بتقديم إجابة واحدة على الأقل. يرجى ملاحظة أن كل إجابة يجب أن يكون لها عنوان.", "app.components.formBuilder.emptyOptionError": "أدخل إجابة واحدة على الأقل", "app.components.formBuilder.emptyStatementError": "تقديم بيان واحد على الأقل", "app.components.formBuilder.emptyTitleError": "أدخل عنوان سؤال", "app.components.formBuilder.emptyTitleMessage": "توفير عنوان لجميع الإجابات", "app.components.formBuilder.emptyTitleStatementMessage": "توفير عنوان لجميع البيانات", "app.components.formBuilder.enable": "تمكين", "app.components.formBuilder.errorMessage": "توجد مشكلة، يرجى حل المشكلة لتتمكن من حفظ التغييرات التي أجريتها", "app.components.formBuilder.fieldGroup.description": "الوصف (اختياري)", "app.components.formBuilder.fieldGroup.title": "العنوان (اختياري)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "لا تتوفر إجابات هذه الأسئلة حاليًا إلا في ملف excel الذي تم تصديره في مدير المدخلات، ولا يمكن للمستخدمين رؤيتها.", "app.components.formBuilder.fieldLabel": "خيارات الإجابة", "app.components.formBuilder.fieldLabelStatement": "تصريحات", "app.components.formBuilder.fileUpload": "تحميل ملف", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "صفحة تعتمد على الخريطة", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "قم بتضمين الخريطة كسياق أو اطرح أسئلة تعتمد على الموقع على المشاركين.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "للحصول على تجربة مستخدم مثالية، لا نوصي بإضافة أسئلة حول النقاط أو المسارات أو المناطق إلى الصفحات المستندة إلى الخرائط.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "صفحة عادية", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "لا تتضمن رخصتك الحالية ميزات رسم الخرائط الاستقصائية. تواصل مع مدير GovSuccess الخاص بك لمعرفة المزيد.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "نوع الصفحة", "app.components.formBuilder.formEnd": "نهاية النموذج", "app.components.formBuilder.formField.cancelDeleteButtonText": "يلغي", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "نعم، احذف الصفحة", "app.components.formBuilder.formField.copyNoun": "ين<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "ين<PERSON><PERSON>", "app.components.formBuilder.formField.delete": "يم<PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "سيؤدي حذف هذه الصفحة أيضًا إلى حذف المنطق المرتبط بها. هل أنت متأكد أنك تريد حذف ذلك؟", "app.components.formBuilder.formField.deleteResultsInfo": "هذا لا يمكن التراجع عنها", "app.components.formBuilder.goToPageInputLabel": "الصفحة التالية هي:", "app.components.formBuilder.good": "<PERSON>ي<PERSON>", "app.components.formBuilder.helmetTitle": "مُنشئ النماذج", "app.components.formBuilder.imageFileUpload": "تحميل صورة", "app.components.formBuilder.invalidLogicBadgeMessage": "أساس منطقي غير صالح", "app.components.formBuilder.labels2": "العلامات (اختياري)", "app.components.formBuilder.labelsTooltipContent2": "اختر تسميات اختيارية لأي من قيم المقياس الخطي.", "app.components.formBuilder.lastPage": "النهاية", "app.components.formBuilder.layout": "مخطط", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "هل انت متاكد انك تريد ان تغادر؟", "app.components.formBuilder.leaveBuilderText": "لم تحفظ التغييرات. يرجى الحفظ قبل المغادرة. إذا غادرت، فسوف تفقد التغييرات.", "app.components.formBuilder.limitAnswersTooltip": "عند تشغيله، يحتاج المستجيبون إلى تحديد العدد المحدد من الإجابات للمتابعة.", "app.components.formBuilder.limitNumberAnswers": "الحد من عدد الإجابات", "app.components.formBuilder.linePolygonMapWarning2": "قد لا يتوافق الرسم الخطي والمضلع مع معايير إمكانية الوصول. يمكن العثور على مزيد من المعلومات في {accessibilityStatement}.", "app.components.formBuilder.linearScale": "مق<PERSON><PERSON><PERSON> خطي", "app.components.formBuilder.locationDescription": "موقع", "app.components.formBuilder.logic": "الأساس المنطقي", "app.components.formBuilder.logicAnyOtherAnswer": "أي إجابة أخرى", "app.components.formBuilder.logicConflicts.conflictingLogic": "المنطق المتضارب", "app.components.formBuilder.logicConflicts.interQuestionConflict": "تحتوي هذه الصفحة على أسئلة تؤدي إلى صفحات مختلفة. إذا أجاب المشاركون على أسئلة متعددة، فسيتم عرض الصفحة الأبعد. تأكد من أن هذا السلوك يتماشى مع التدفق المقصود.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "تطبق على هذه الصفحة قواعد منطقية متعددة: منطق الأسئلة المتعددة الاختيارات، ومنطق مستوى الصفحة، ومنطق الأسئلة المتبادلة. وعندما تتداخل هذه الشروط، فإن منطق الأسئلة سيكون له الأولوية على منطق الصفحة، وسيتم عرض الصفحة الأبعد. راجع المنطق للتأكد من أنه يتماشى مع التدفق المقصود.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "تحتوي هذه الصفحة على سؤال متعدد الاختيارات حيث تؤدي الخيارات إلى صفحات مختلفة. إذا اختار المشاركون خيارات متعددة، فسيتم عرض الصفحة الأبعد. تأكد من أن هذا السلوك يتماشى مع التدفق المقصود.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "تحتوي هذه الصفحة على سؤال متعدد الاختيارات حيث تؤدي الخيارات إلى صفحات مختلفة وتحتوي على أسئلة تؤدي إلى صفحات أخرى. سيتم عرض الصفحة الأبعد إذا تداخلت هذه الشروط. تأكد من أن هذا السلوك يتوافق مع التدفق المقصود.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "تحتوي هذه الصفحة على سؤال متعدد الاختيارات حيث تؤدي الخيارات إلى صفحات مختلفة ولديها منطق محدد على مستوى الصفحة والسؤال. سيكون لمنطق السؤال الأولوية، وسيتم عرض الصفحة الأبعد. تأكد من أن هذا السلوك يتماشى مع التدفق المقصود.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "تم ضبط منطق هذه الصفحة على مستوى الصفحة ومستوى السؤال. وسوف يكون لمنطق السؤال الأولوية على منطق مستوى الصفحة. تأكد من أن هذا السلوك يتماشى مع التدفق المقصود.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "تحتوي هذه الصفحة على منطق محدد على مستوى الصفحة والسؤال، ويتم توجيه أسئلة متعددة إلى صفحات مختلفة. وسوف تكون الأولوية لمنطق السؤال، وسيتم عرض الصفحة الأبعد. تأكد من أن هذا السلوك يتماشى مع التدفق المقصود.", "app.components.formBuilder.logicNoAnswer2": "لم يتم الرد", "app.components.formBuilder.logicPanelAnyOtherAnswer": "إذا كان هناك أي إجابة أخرى", "app.components.formBuilder.logicPanelNoAnswer": "إذا لم يتم الرد", "app.components.formBuilder.logicValidationError": "قد لا يرتبط الأساس المنطقي بالصفحات السابقة", "app.components.formBuilder.longAnswer": "إجابة مطولة", "app.components.formBuilder.mapConfiguration": "تكوين الخريطة", "app.components.formBuilder.mapping": "رسم الخرائط", "app.components.formBuilder.mappingNotInCurrentLicense": "لا تتضمن رخصتك الحالية ميزات رسم الخرائط الاستقصائية. تواصل مع مدير GovSuccess الخاص بك لمعرفة المزيد.", "app.components.formBuilder.matrix": "المصفوفة", "app.components.formBuilder.matrixSettings.columns": "الأعمدة", "app.components.formBuilder.matrixSettings.rows": "الصفوف", "app.components.formBuilder.multipleChoice": "اختيار متعدد", "app.components.formBuilder.multipleChoiceHelperText": "إذا أدت خيارات متعددة إلى صفحات مختلفة وقام المشاركون باختيار أكثر من خيار، فسيتم عرض الصفحة الأبعد. تأكد من أن هذا السلوك يتوافق مع التدفق المقصود.", "app.components.formBuilder.multipleChoiceImage": "اختيار الصورة", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "ال<PERSON><PERSON> الأدنى", "app.components.formBuilder.neutral": "حيادي", "app.components.formBuilder.newField": "<PERSON><PERSON><PERSON> جديد", "app.components.formBuilder.number": "الرقم", "app.components.formBuilder.ok": "نعم", "app.components.formBuilder.open": "مفتوح", "app.components.formBuilder.optional": "اختياري", "app.components.formBuilder.other": "آخر", "app.components.formBuilder.otherOption": "\"خ<PERSON>ا<PERSON> آخر", "app.components.formBuilder.otherOptionTooltip": "اسمح للمشاركين بإدخال استجابة مخصصة إذا كانت الإجابات المقدمة لا تتطابق مع تفضيلاتهم", "app.components.formBuilder.page": "الصفحة", "app.components.formBuilder.pageCannotBeDeleted": "لا يمكن حذف هذه الصفحة.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "لا يمكن حذف هذه الصفحة ولا تسمح بإضافة أي حقول إضافية.", "app.components.formBuilder.pageRuleLabel": "الصفحة التالية هي:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "إذا لم تتم إضافة أي منطق، فسيتبع النموذج تدفقه الطبيعي. إذا كانت كل من الصفحة والأسئلة تحتوي على منطق، فسيكون لمنطق السؤال الأولوية. تأكد من توافق ذلك مع التدفق المقصود. لمزيد من المعلومات، تفضل بزيارة {supportPageLink}", "app.components.formBuilder.preview": "معاينة:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "لا يتم عرض الرعاة المشاركين في ملف PDF الذي تم تنزيله ولا يتم دعم استيرادهم عبر FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "تظهر أسئلة تحميل الملف على أنها غير مدعومة في ملف PDF الذي تم تنزيله ولا يتم دعمها للاستيراد عبر FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "تظهر أسئلة الربط في ملف PDF الذي تم تنزيله، ولكن لن تكون الطبقات مرئية. لا يُدعم استيراد أسئلة الربط عبر FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "تظهر أسئلة المصفوفة على ملف PDF الذي تم تنزيله ولكن لا يتم دعم استيرادها عبر FormSync حاليًا.", "app.components.formBuilder.printSupportTooltip.page": "تظهر عناوين الصفحات والأوصاف كرأس قسم في ملف PDF الذي تم تنزيله.", "app.components.formBuilder.printSupportTooltip.ranking": "تظهر أسئلة التصنيف على ملف PDF الذي تم تنزيله ولكن لا يتم دعم استيرادها عبر FormSync حاليًا.", "app.components.formBuilder.printSupportTooltip.topics2": "تظهر العلامات على أنها غير مدعومة في ملف PDF الذي تم تنزيله ولا يتم دعمها للاستيراد عبر FormSync.", "app.components.formBuilder.proposedBudget": "ميزانية مقترحة", "app.components.formBuilder.question": "سؤال", "app.components.formBuilder.questionCannotBeDeleted": "لا يمكن حذف هذا السؤال.", "app.components.formBuilder.questionDescriptionOptional": "وصف السؤال (اختياري)", "app.components.formBuilder.questionTitle": "عنوان السؤال", "app.components.formBuilder.randomize": "عشوائية", "app.components.formBuilder.randomizeToolTip": "سيتم ترتيب الإجابات بشكل عشوائي لكل مستخدم", "app.components.formBuilder.range": "النطاق", "app.components.formBuilder.ranking": "تصنيف", "app.components.formBuilder.rating": "تصنيف", "app.components.formBuilder.removeAnswer": "إزالة إجابة", "app.components.formBuilder.required": "مطلوب", "app.components.formBuilder.requiredToggleLabel": "جعل الإجابة عن هذا السؤال إلزامية", "app.components.formBuilder.ruleForAnswerLabel": "إذا كانت الإجابة هي:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "إذا كانت الإجابات تتضمن:", "app.components.formBuilder.save": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "حد<PERSON> القيمة القصوى للمقياس.", "app.components.formBuilder.sentiment": "مقياس المشاعر", "app.components.formBuilder.shapefileUpload": "تحميل ملف شكل Esri", "app.components.formBuilder.shortAnswer": "إجابة مختصرة", "app.components.formBuilder.showResponseToUsersToggleLabel": "إظهار الاستجابة للمستخدمين", "app.components.formBuilder.singleChoice": "اختيار أحادي", "app.components.formBuilder.staleDataErrorMessage2": "حدثت مشكلة. تم حفظ نموذج الإدخال هذا مؤخرًا في مكان آخر. قد يكون السبب في ذلك هو أنك أو مستخدم آخر قام بفتحه للتحرير في نافذة متصفح أخرى. يرجى تحديث الصفحة للحصول على أحدث نموذج ثم إجراء التغييرات مرة أخرى.", "app.components.formBuilder.stronglyAgree": "أوافق بشدة", "app.components.formBuilder.stronglyDisagree": "أختلف بشدة", "app.components.formBuilder.supportArticleLinkText": "هذه الصفحة", "app.components.formBuilder.tags": "وسوم", "app.components.formBuilder.title": "العنوان", "app.components.formBuilder.toLabel": "إ<PERSON><PERSON>", "app.components.formBuilder.unsavedChanges": "لم تحفظ التغييرات", "app.components.formBuilder.useCustomButton2": "استخدم زر الصفحة المخصصة", "app.components.formBuilder.veryBad": "سيء للغاية", "app.components.formBuilder.veryGood": "جيد جدًا", "app.components.ideas.similarIdeas.engageHere": "شارك هنا", "app.components.ideas.similarIdeas.noSimilarSubmissions": "لم يتم العثور على أي إرساليات مماثلة.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "لقد وجدنا إرساليات مماثلة - والتفاعل معها يمكن أن يساعد في جعلها أقوى!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "تم نشر مشاركات مماثلة بالفعل:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "أبحث عن مقترحات مماثلة ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {أقل من يوم} one {# يوم} other {# أيام}} متبقي", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  أسابيع متبقية", "app.components.screenReaderCurrency.AED": "درهم الإمارات العربية المتحدة", "app.components.screenReaderCurrency.AFN": "أفغاني أفغاني", "app.components.screenReaderCurrency.ALL": "ليك الألباني", "app.components.screenReaderCurrency.AMD": "الدراما الأرمنية", "app.components.screenReaderCurrency.ANG": "هولندا جزر الأنتيل الغيلدر", "app.components.screenReaderCurrency.AOA": "الأنغولية كوانزا", "app.components.screenReaderCurrency.ARS": "البيزو الأرجنتيني", "app.components.screenReaderCurrency.AUD": "دولار استرالي", "app.components.screenReaderCurrency.AWG": "أروبي فلورين", "app.components.screenReaderCurrency.AZN": "مانات الأذربيجانية", "app.components.screenReaderCurrency.BAM": "البوسنة والهرسك مارك القابل للتحويل", "app.components.screenReaderCurrency.BBD": "الدولار البربادوسي", "app.components.screenReaderCurrency.BDT": "تاكا بنجلاديشية", "app.components.screenReaderCurrency.BGN": "لي<PERSON> الب<PERSON>غاري", "app.components.screenReaderCurrency.BHD": "دينار بحريني", "app.components.screenReaderCurrency.BIF": "الفرنك البوروندي", "app.components.screenReaderCurrency.BMD": "الدولار البرمودي", "app.components.screenReaderCurrency.BND": "دولار بروناي", "app.components.screenReaderCurrency.BOB": "البوليفيانو البوليفي", "app.components.screenReaderCurrency.BOV": "مفدول البوليفي", "app.components.screenReaderCurrency.BRL": "ريال برازيلي", "app.components.screenReaderCurrency.BSD": "دولار بهامي", "app.components.screenReaderCurrency.BTN": "نغولتروم بوتان", "app.components.screenReaderCurrency.BWP": "بولا بوتسوانا", "app.components.screenReaderCurrency.BYR": "الروبل البيلاروسي", "app.components.screenReaderCurrency.BZD": "دولار بليز", "app.components.screenReaderCurrency.CAD": "الدولار الكندي", "app.components.screenReaderCurrency.CDF": "الفرنك الكونغولي", "app.components.screenReaderCurrency.CHE": "وير يورو", "app.components.screenReaderCurrency.CHF": "الفرنك السويسري", "app.components.screenReaderCurrency.CHW": "فرنك وير", "app.components.screenReaderCurrency.CLF": "الوحدة الحسابية التشيلية (UF)", "app.components.screenReaderCurrency.CLP": "البيزو التشيلي", "app.components.screenReaderCurrency.CNY": "اليوان الصيني", "app.components.screenReaderCurrency.COP": "البيزو الكولومبي", "app.components.screenReaderCurrency.COU": "وحدة الشجاعة الحقيقية", "app.components.screenReaderCurrency.CRC": "كولون كوستاريكي", "app.components.screenReaderCurrency.CRE": "ائتمان", "app.components.screenReaderCurrency.CUC": "البيزو الكوبي القابل للتحويل", "app.components.screenReaderCurrency.CUP": "البيزو الكوبي", "app.components.screenReaderCurrency.CVE": "إسكودو الرأس الأخضر", "app.components.screenReaderCurrency.CZK": "كورونا التشيكية", "app.components.screenReaderCurrency.DJF": "الفرنك الجيبوتي", "app.components.screenReaderCurrency.DKK": "كرونة دنماركية", "app.components.screenReaderCurrency.DOP": "البيزو الدومينيكي", "app.components.screenReaderCurrency.DZD": "الدينار الجزائري", "app.components.screenReaderCurrency.EGP": "الجنيه المصري", "app.components.screenReaderCurrency.ERN": "الناكفا الإريترية", "app.components.screenReaderCurrency.ETB": "بير إثيوبي", "app.components.screenReaderCurrency.EUR": "اليورو", "app.components.screenReaderCurrency.FJD": "الدولار الفيجي", "app.components.screenReaderCurrency.FKP": "جنيه جزر فوكلاند", "app.components.screenReaderCurrency.GBP": "الجنيه البريطاني", "app.components.screenReaderCurrency.GEL": "لاري جورجي", "app.components.screenReaderCurrency.GHS": "سيدي الغاني", "app.components.screenReaderCurrency.GIP": "جنيه جبل طارق", "app.components.screenReaderCurrency.GMD": "دالاسي الغامبية", "app.components.screenReaderCurrency.GNF": "الفرنك الغيني", "app.components.screenReaderCurrency.GTQ": "الكوازال الغواتيمالي", "app.components.screenReaderCurrency.GYD": "دولار جوياني", "app.components.screenReaderCurrency.HKD": "الدولار هونج كونج", "app.components.screenReaderCurrency.HNL": "لمبيرا هندوراس", "app.components.screenReaderCurrency.HRK": "الكونا الكرواتية", "app.components.screenReaderCurrency.HTG": "الغورد الهايتي", "app.components.screenReaderCurrency.HUF": "الفورنت المجري", "app.components.screenReaderCurrency.IDR": "الروبية الاندونيسية", "app.components.screenReaderCurrency.ILS": "الشيكل الإسرائيلي الجديد", "app.components.screenReaderCurrency.INR": "الروبية الهندية", "app.components.screenReaderCurrency.IQD": "الدينار العراقي", "app.components.screenReaderCurrency.IRR": "الريال الإيراني", "app.components.screenReaderCurrency.ISK": "الكرونا الأيسلندية", "app.components.screenReaderCurrency.JMD": "الدولار الجامايكي", "app.components.screenReaderCurrency.JOD": "الدينار الأردني", "app.components.screenReaderCurrency.JPY": "الين الياباني", "app.components.screenReaderCurrency.KES": "الشلن الكيني", "app.components.screenReaderCurrency.KGS": "السوم القرغيزستاني", "app.components.screenReaderCurrency.KHR": "الريال الكمبودي", "app.components.screenReaderCurrency.KMF": "فرنك جزر القمر", "app.components.screenReaderCurrency.KPW": "وون كوريا الشمالية", "app.components.screenReaderCurrency.KRW": "وون كوريا الجنوبية", "app.components.screenReaderCurrency.KWD": "دينار كويتي", "app.components.screenReaderCurrency.KYD": "دولار جزر كايمان", "app.components.screenReaderCurrency.KZT": "تنغي الكازاخستاني", "app.components.screenReaderCurrency.LAK": "لاو كيب", "app.components.screenReaderCurrency.LBP": "الليرة اللبنانية", "app.components.screenReaderCurrency.LKR": "الروبية السريلانكية", "app.components.screenReaderCurrency.LRD": "الدولار الليبيري", "app.components.screenReaderCurrency.LSL": "ليسوتو لوتي", "app.components.screenReaderCurrency.LTL": "ليتاس الليتوانية", "app.components.screenReaderCurrency.LVL": "لاتفيا لاتفيا", "app.components.screenReaderCurrency.LYD": "الدينار الليبي", "app.components.screenReaderCurrency.MAD": "درهم مغربي", "app.components.screenReaderCurrency.MDL": "ليو المولدوفي", "app.components.screenReaderCurrency.MGA": "أرياري مدغشقر", "app.components.screenReaderCurrency.MKD": "دينار مقدوني", "app.components.screenReaderCurrency.MMK": "ميانمار كيات", "app.components.screenReaderCurrency.MNT": "توغروغ المنغولية", "app.components.screenReaderCurrency.MOP": "باتاكا ماكاوية", "app.components.screenReaderCurrency.MRO": "الأوقية الموريتانية", "app.components.screenReaderCurrency.MUR": "روبية موريشيوسية", "app.components.screenReaderCurrency.MVR": "روفية جزر المالديف", "app.components.screenReaderCurrency.MWK": "كواشا ملاوية", "app.components.screenReaderCurrency.MXN": "البيزو المكسيكي", "app.components.screenReaderCurrency.MXV": "وحدة الانعكاس المكسيكية (UDI)", "app.components.screenReaderCurrency.MYR": "رينغيت ماليزي", "app.components.screenReaderCurrency.MZN": "ميتيكال موزمبيق", "app.components.screenReaderCurrency.NAD": "الدولار الناميبي", "app.components.screenReaderCurrency.NGN": "نيرة نيجيرية", "app.components.screenReaderCurrency.NIO": "قرطبة نيكاراغوا", "app.components.screenReaderCurrency.NOK": "كرونة نرويجية", "app.components.screenReaderCurrency.NPR": "الروبية النيبالية", "app.components.screenReaderCurrency.NZD": "الدولار النيوزيلندي", "app.components.screenReaderCurrency.OMR": "الريال العماني", "app.components.screenReaderCurrency.PAB": "بالبوا البنمي", "app.components.screenReaderCurrency.PEN": "سول بيرو", "app.components.screenReaderCurrency.PGK": "بابوا غينيا الجديدة كينا", "app.components.screenReaderCurrency.PHP": "البيزو الفلبيني", "app.components.screenReaderCurrency.PKR": "روبية باكستانية", "app.components.screenReaderCurrency.PLN": "الزلوتي البولندي", "app.components.screenReaderCurrency.PYG": "الغواراني الباراجواياني", "app.components.screenReaderCurrency.QAR": "الريال القطري", "app.components.screenReaderCurrency.RON": "ليو الروماني", "app.components.screenReaderCurrency.RSD": "الدينار الصربي", "app.components.screenReaderCurrency.RUB": "الروبل الروسي", "app.components.screenReaderCurrency.RWF": "الفرنك الرواندي", "app.components.screenReaderCurrency.SAR": "الريال السعودي", "app.components.screenReaderCurrency.SBD": "دولار جزر سليمان", "app.components.screenReaderCurrency.SCR": "الروبية السيشيلية", "app.components.screenReaderCurrency.SDG": "جنيه سوداني", "app.components.screenReaderCurrency.SEK": "كرونة سويدية", "app.components.screenReaderCurrency.SGD": "دولار سينغافوري", "app.components.screenReaderCurrency.SHP": "جنيه سانت هيلينا", "app.components.screenReaderCurrency.SLL": "سيراليون سيراليون", "app.components.screenReaderCurrency.SOS": "شلن صومالي", "app.components.screenReaderCurrency.SRD": "الدولار السورينامي", "app.components.screenReaderCurrency.SSP": "جنيه جنوب السودان", "app.components.screenReaderCurrency.STD": "ساو تومي وبرينسيبي دوبرا", "app.components.screenReaderCurrency.SYP": "الليرة السورية", "app.components.screenReaderCurrency.SZL": "ليلانجيني السوازي", "app.components.screenReaderCurrency.THB": "البات التايلندي", "app.components.screenReaderCurrency.TJS": "السوموني الطاجيكستاني", "app.components.screenReaderCurrency.TMT": "مانات تركمانستان", "app.components.screenReaderCurrency.TND": "الدينار التونسي", "app.components.screenReaderCurrency.TOK": "<PERSON><PERSON><PERSON> مميز", "app.components.screenReaderCurrency.TOP": "تونجا بانجا", "app.components.screenReaderCurrency.TRY": "الليرة التركية", "app.components.screenReaderCurrency.TTD": "دولار ترينيداد وتوباغو", "app.components.screenReaderCurrency.TWD": "دولار تايواني جديد", "app.components.screenReaderCurrency.TZS": "الشلن التنزاني", "app.components.screenReaderCurrency.UAH": "الهريفنيا الأوكرانية", "app.components.screenReaderCurrency.UGX": "الشلن الأوغندي", "app.components.screenReaderCurrency.USD": "دولار الولايات المتحدة", "app.components.screenReaderCurrency.USN": "دولار الولايات المتحدة (اليوم التالي)", "app.components.screenReaderCurrency.USS": "دولار أمريكي (نفس اليوم)", "app.components.screenReaderCurrency.UYI": "بيزو أوروغواي في Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "بيزو أوروغواي", "app.components.screenReaderCurrency.UZS": "سوم أوزبكستاني", "app.components.screenReaderCurrency.VEF": "البوليفار الفنزويلي", "app.components.screenReaderCurrency.VND": "الفيتنامية Đồng", "app.components.screenReaderCurrency.VUV": "فانواتو فاتو", "app.components.screenReaderCurrency.WST": "ساموا تالا", "app.components.screenReaderCurrency.XAF": "فرنك وسط أفريقيا", "app.components.screenReaderCurrency.XAG": "الفضة (أونصة تروي واحدة)", "app.components.screenReaderCurrency.XAU": "الذهب (أونصة تروي واحدة)", "app.components.screenReaderCurrency.XBA": "الوحدة الأوروبية المركبة (EURCO)", "app.components.screenReaderCurrency.XBB": "الوحدة النقدية الأوروبية (EMU-6)", "app.components.screenReaderCurrency.XBC": "الوحدة الأوروبية للحساب 9 (EUA-9)", "app.components.screenReaderCurrency.XBD": "الوحدة الأوروبية للحساب 17 (EUA-17)", "app.components.screenReaderCurrency.XCD": "دولار شرق الكاريبي", "app.components.screenReaderCurrency.XDR": "حقوق السحب الخاصة", "app.components.screenReaderCurrency.XFU": "UIC فرنك", "app.components.screenReaderCurrency.XOF": "فرنك غرب أفريقيا", "app.components.screenReaderCurrency.XPD": "البلاديوم (أونصة تروي واحدة)", "app.components.screenReaderCurrency.XPF": "فرنك سي اف بي", "app.components.screenReaderCurrency.XPT": "البلاتين (أونصة تروي واحدة)", "app.components.screenReaderCurrency.XTS": "الرموز محفوظة خصيصًا لأغراض الاختبار", "app.components.screenReaderCurrency.XXX": "لا توجد عملة", "app.components.screenReaderCurrency.YER": "الريال اليمني", "app.components.screenReaderCurrency.ZAR": "<PERSON><PERSON><PERSON> جنوب أفريقيا", "app.components.screenReaderCurrency.ZMW": "كواشا الزامبية", "app.components.screenReaderCurrency.amount": "كمية", "app.components.screenReaderCurrency.currency": "عملة", "app.components.trendIndicator.lastQuarter2": "الربع الأخير", "app.containers.AccessibilityStatement.applicability": "يسري بيان إمكانية الوصول هذا على {demoPlatformLink} الممثلة لهذا الموقع الإلكتروني؛ فهي تستخدم التعليمات البرمجية المصدر نفسها وتؤدي الوظيفة نفسها.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "طريقة التقييم", "app.containers.AccessibilityStatement.assesmentText2022": "تم تقييم إمكانية وصول هذا الموقع من قِبل كيان خارجي غير مشترك في عملية التصميم والتطوير. يمكن تحديد توافق {demoPlatformLink} المتقدم ذكرها في {statusPageLink} هذه.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "يمكنك تغيير تفضيلاتك", "app.containers.AccessibilityStatement.changePreferencesText": "في أي وقت، {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "استثناءات التوافق", "app.containers.AccessibilityStatement.conformanceStatus": "حالة التوافق", "app.containers.AccessibilityStatement.contentConformanceExceptions": "نسعى جاهدين إلى جعل المحتوى الخاص بنا شاملًا للجميع. مع ذلك، وفي بعض الحالات قد يكون هناك محتوىً لا يمكن الوصول إليه على المنصة كما هو موصح أدناه:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "عرض توضيحي لموقع الويب", "app.containers.AccessibilityStatement.email": "البريد الإلكتروني:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "أدوات المسح المضمنة", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "أدوات المسح المضمنة المتاحة للاستخدام على هذه المنصة عبارة عن برامج تابعة لجهات خارجية وقد لا تكون قابلة للوصول.", "app.containers.AccessibilityStatement.exception_1": "تُسهل منصات المشاركة الرقمية لدينا نشر المحتويات التي يولدها المُستخدمون بأنفسهم بواسطة أفراد ومؤسسات. من المحتمل أن يتم تحميل ملفات PDF والصور والأنواع الأخرى من الملفات التي تشمل وسائل الإعلام المتعددة إلى المنصة كمُلحقات أو إضافتها إلى خانات نصية بواسطة مُستخدمي المنصة. قد لا تكون هذه الوثائق مُتاحة كلياً. ", "app.containers.AccessibilityStatement.feedbackProcessIntro": "يُسعدنا سماع رأيك وملاحظاتك حول إمكانية الوصول لهذا الموقع. يُرجى التواصل معنا باستخدام إحدى الطرق التالية:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "عملية تقديم الملاحظات", "app.containers.AccessibilityStatement.govocalAddress2022": "بوليفارد باتشيكو 34، 1000 بروكسل، بلجيكا", "app.containers.AccessibilityStatement.headTitle": "بيان إمكانية الوصول | {orgName}", "app.containers.AccessibilityStatement.intro2022": "تلتزم {goVocalLink} بتوفير منصة يمكن لجميع المستخدمين الوصول إليها بصرف النظر عن التقنية أو الإمكانية المستخدمة. يتم الالتزام بمعايير إمكانية الوصول الحالية ذات الصلة في جهودها الراهنة الرامية إلى تعزيز إمكانية الوصول إلى منصاتنا وإمكانية استخدامها من قِبل جميع المستخدمين.", "app.containers.AccessibilityStatement.mapping": "رسم الخرائط", "app.containers.AccessibilityStatement.mapping_1": "تتوافق الخرائط على المنصة جزئيًا مع معايير إمكانية الوصول. يمكن التحكم في مدى الخريطة والتكبير وعناصر واجهة المستخدم باستخدام لوحة المفاتيح عند عرض الخرائط. يمكن للمسؤولين أيضًا تكوين نمط طبقات الخريطة في المكتب الخلفي، أو باستخدام تكامل Esri، لإنشاء لوحات ألوان ورموز أكثر سهولة في الوصول. سيساعد استخدام أنماط خطوط أو مضلعات مختلفة (مثل الخطوط المتقطعة) أيضًا في التمييز بين طبقات الخريطة أينما أمكن، وعلى الرغم من أنه لا يمكن تكوين مثل هذا النمط داخل منصتنا في هذا الوقت، إلا أنه يمكن تكوينه إذا كنت تستخدم خرائط مع تكامل Esri.", "app.containers.AccessibilityStatement.mapping_2": "لا يمكن الوصول إلى الخرائط الموجودة على المنصة بشكل كامل لأنها لا تعرض الخرائط الأساسية أو طبقات الخرائط أو الاتجاهات في البيانات بشكل مسموع للمستخدمين الذين يستخدمون برامج قراءة الشاشة. يجب أن تعرض الخرائط التي يمكن الوصول إليها بشكل كامل طبقات الخرائط بشكل مسموع وتصف أي اتجاهات ذات صلة في البيانات. علاوة على ذلك، لا يمكن الوصول إلى رسم الخرائط الخطية والمضلعة في المسوحات حيث لا يمكن رسم الأشكال باستخدام لوحة المفاتيح. لا تتوفر طرق إدخال بديلة في هذا الوقت بسبب التعقيد الفني.", "app.containers.AccessibilityStatement.mapping_3": "لتسهيل الوصول إلى رسم الخرائط الخطية والمضلعية، نوصي بتضمين مقدمة أو شرح في سؤال الاستطلاع أو وصف الصفحة لما تظهره الخريطة وأي اتجاهات ذات صلة. علاوة على ذلك، يمكن توفير سؤال نصي قصير أو طويل للإجابة حتى يتمكن المستجيبون من وصف إجابتهم بعبارات بسيطة إذا لزم الأمر (بدلاً من النقر فوق الخريطة). نوصي أيضًا بتضمين معلومات الاتصال بمدير المشروع حتى يتمكن المستجيبون الذين لا يستطيعون ملء سؤال الخريطة من طلب طريقة بديلة للإجابة على السؤال (على سبيل المثال اجتماع الفيديو).", "app.containers.AccessibilityStatement.mapping_4": "بالنسبة لمشاريع ومقترحات توليد الأفكار، هناك خيار لعرض المدخلات في عرض خريطة، وهو غير متاح. ومع ذلك، بالنسبة لهذه الطرق، هناك عرض قائمة بديل للمدخلات متاح، وهو متاح.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "تتضمن ورش العمل عبر الإنترنت مكون بث فيديو مباشر لا يدعم الترجمات المصاحبة حاليًا.", "app.containers.AccessibilityStatement.pageDescription": "بيان حول إمكانية الوصول لهذا الموقع الإلكتروني", "app.containers.AccessibilityStatement.postalAddress": "العنوان البريدي:", "app.containers.AccessibilityStatement.publicationDate": "تاريخ النشر", "app.containers.AccessibilityStatement.publicationDate2024": "تم نشر بيان إمكانية الوصول هذا في 21 أغسطس 2024.", "app.containers.AccessibilityStatement.responsiveness": "نسعى للرد على ملاحظاتك خلال 1-2 يوم عمل.", "app.containers.AccessibilityStatement.statusPageText": "صفحة الحالة", "app.containers.AccessibilityStatement.technologiesIntro": "تعتمد إمكانية الوصول إلى هذا الموقع على التقنيات التالية للعمل:", "app.containers.AccessibilityStatement.technologiesTitle": "التقنيات", "app.containers.AccessibilityStatement.title": "بيان إمكانية الوصول", "app.containers.AccessibilityStatement.userGeneratedContent": "محتوى من إنشاء المستخدم", "app.containers.AccessibilityStatement.workshops": "ورشات العمل", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "<PERSON><PERSON><PERSON> المشروع", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "سيسمح لك استخدام Content Builder باستخدام خيارات تخطيط أكثر تقدمًا. بالنسبة للغات التي لا يتوفر فيها محتوى في Content Builder، سيتم عرض محتوى وصف المشروع العادي بدلاً من ذلك.", "app.containers.AdminPage.ProjectDescription.linkText": "تعديل الوصف في Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "حد<PERSON> خطأ ما أثناء حفظ وصف المشروع.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "استخدم Content Builder للوصف", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "سيسمح لك استخدام \"منشئ المحتوى\" باستخدام خيارات تخطيط أكثر تقدمًا.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "عرض المشروع", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "نهاية الاستبيان", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "أنشئ مجموعة ذكية", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "ستتم إضافة المستخدمين المستوفين لجميع الشروط التالية إلى المجموعة تلقائيًا:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "إدخال قاعدة واحدة على الأقل", "app.containers.AdminPage.Users.UsersGroup.rulesError": "بعض الشروط غير مكتملة", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "حفظ المجموعة", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "لا يشكل تكوين المجموعات الذكية جزءًا من ترخيصك الحالي. تواصل مع مدير GovSuccess الخاص بك لمعرفة المزيد حول هذا الأمر.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "إدخال اسم مجموعة", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "تم تعطيل التحقق في منصتك، قم بإزالة قاعدة التحقق أو اتصل بالدعم.", "app.containers.App.appMetaDescription": "مرحباً في منصة {orgName} للمُشاركة عبر الإنترنت. \nاستكشف مشروعات محلية وشارك في نقاشات! ", "app.containers.App.loading": "التحميل جارٍ...", "app.containers.App.metaTitle1": "منصة مشاركة المواطنين | {orgName}", "app.containers.App.skipLinkText": "انتقل إلى المحتوى الرئيسي", "app.containers.AreaTerms.areaTerm": "منطقة", "app.containers.AreaTerms.areasTerm": "المناطق", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "حساب بهذا البريد الإلكتروني موجود بالفعل. يمكنك تسجيل الخروج وتسجيل الدخول باستخدام عنوان البريد الإلكتروني هذا والتحقق من حسابك في صفحة الإعدادات.", "app.containers.Authentication.steps.AccessDenied.close": "يغلق", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "لا تتوفر لديك المتطلبات للمشاركة في هذه العملية.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "العودة إلى التحقق من تسجيل الدخول مرة واحدة", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "الرجاء إدخال رمز مميز", "app.containers.Authentication.steps.Invitation.token": "<PERSON><PERSON><PERSON>", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "هل لديك حساب بالفعل؟ {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "تسجيل الدخول", "app.containers.CampaignsConsentForm.ally_categoryLabel": "الرسائل الإلكترونية في هذه الفئة", "app.containers.CampaignsConsentForm.messageError": "حد<PERSON> خطأ أثناء حفظ تفضيلات البريد الإلكتروني الخاصة بك.", "app.containers.CampaignsConsentForm.messageSuccess": "تم حفظ تفضيلاتك للبريد الإلكتروني.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "ما هي إشعارات البريد الإلكتروني التي ترغب في استلامها؟ ", "app.containers.CampaignsConsentForm.notificationsTitle": "الإشعارات", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "رجوع إلى إعدادات الملف الشخصي", "app.containers.ChangeEmail.confirmationModalTitle": "قم بتأكيد بريدك الإلكتروني", "app.containers.ChangeEmail.emailEmptyError": "أدخل عنوان بريد إلكتروني", "app.containers.ChangeEmail.emailInvalidError": "أدخل عنوان بريد إلكتروني بالتنسيق الصحيح ، على سبيل المثال <EMAIL>", "app.containers.ChangeEmail.emailRequired": "الرجاء إدخال عنوان البريد الإلكتروني.", "app.containers.ChangeEmail.emailTaken": "هذا البريد استخدم من قبل.", "app.containers.ChangeEmail.emailUpdateCancelled": "تم إلغاء تحديث البريد الإلكتروني.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "لتحديث بريدك الإلكتروني ، يرجى إعادة العملية.", "app.containers.ChangeEmail.helmetDescription": "تغيير صفحة البريد الإلكتروني الخاصة بك", "app.containers.ChangeEmail.helmetTitle": "قم بتغيير بريدك الإلكتروني", "app.containers.ChangeEmail.newEmailLabel": "بريد إلكتروني جديد", "app.containers.ChangeEmail.submitButton": "يُقدِّم", "app.containers.ChangeEmail.titleAddEmail": "أضف بريدك الإلكتروني", "app.containers.ChangeEmail.titleChangeEmail": "قم بتغيير بريدك الإلكتروني", "app.containers.ChangeEmail.updateSuccessful": "تم تحديث بريدك الإلكتروني بنجاح.", "app.containers.ChangePassword.currentPasswordLabel": "كلمة المرور الحالية", "app.containers.ChangePassword.currentPasswordRequired": "أدخل كلمة مرورك الحالية", "app.containers.ChangePassword.goHome": "انتقال إلى الصفحة الرئيسية", "app.containers.ChangePassword.helmetDescription": "صفحة تغيير كلمة مرورك", "app.containers.ChangePassword.helmetTitle": "تغيير كلمة مرورك", "app.containers.ChangePassword.newPasswordLabel": "كلمة مرور جديدة", "app.containers.ChangePassword.newPasswordRequired": "أدخل كلمة مرورك الجديدة", "app.containers.ChangePassword.password.minimumPasswordLengthError": "أدخل كلمة مرور لا يقل طولها عن {minimumPasswordLength} حروف", "app.containers.ChangePassword.passwordChangeSuccessMessage": "تم تحديث كلمة مرورك بنجاح.", "app.containers.ChangePassword.passwordEmptyError": "أدخل كلمة مرورك", "app.containers.ChangePassword.passwordsDontMatch": "تأكيد كلمة المرور الجديدة", "app.containers.ChangePassword.titleAddPassword": "<PERSON><PERSON><PERSON>ل<PERSON> مرور", "app.containers.ChangePassword.titleChangePassword": "غير كلمة المرور الخاصة بك", "app.containers.Comments.a11y_commentDeleted": "تم حذف التعليق", "app.containers.Comments.a11y_commentPosted": "تم نشر التعليق", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {لا يحب} one {1 إعجاب} other {# الإعجابات}}", "app.containers.Comments.a11y_undoLike": "التراجع عن الإعجاب", "app.containers.Comments.addCommentError": "حدث خطأ ما. يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.Comments.adminCommentDeletionCancelButton": "إلغاء", "app.containers.Comments.adminCommentDeletionConfirmButton": "احذف هذا التعليق", "app.containers.Comments.cancelCommentEdit": "إلغاء", "app.containers.Comments.childCommentBodyPlaceholder": "اكتب ردًا...", "app.containers.Comments.commentCancelUpvote": "تراجع", "app.containers.Comments.commentDeletedPlaceholder": "تم حذف هذا التعليق.", "app.containers.Comments.commentDeletionCancelButton": "احتفظ بتعليقي", "app.containers.Comments.commentDeletionConfirmButton": "ا<PERSON><PERSON><PERSON> تعليقي", "app.containers.Comments.commentLike": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentReplyButton": "ر<PERSON>", "app.containers.Comments.commentsSortTitle": "فرز التعليقات بحسب", "app.containers.Comments.completeProfileLinkText": "أكمل ملفك الشخصي", "app.containers.Comments.completeProfileToComment": "الرجاء {completeRegistrationLink} للتعليق.", "app.containers.Comments.confirmCommentDeletion": "هل تريد حذف هذا التعليق بالتأكيد؟ لا يمكن التراجع عن هذا الإجراء!", "app.containers.Comments.deleteComment": "<PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "إد<PERSON>ال مزيد من المعلومات حول السبب الذي أبديته", "app.containers.Comments.deleteReasonError": "إب<PERSON><PERSON>ء سبب", "app.containers.Comments.deleteReason_inappropriate": "إنه غير لائق أو مسيء", "app.containers.Comments.deleteReason_irrelevant": "هذا لا ينتمي إلى هنا", "app.containers.Comments.deleteReason_other": "سب<PERSON>ر", "app.containers.Comments.editComment": "تعديل", "app.containers.Comments.guidelinesLinkText": "إرشادات المجتمع", "app.containers.Comments.ideaCommentBodyPlaceholder": "اكتب تعليقك هنا", "app.containers.Comments.internalCommentingNudgeMessage": "لا يتضمن ترخيصك الحالي تقديم تعليقات داخلية. تواصل مع مدير GovSuccess الخاص بك لمعرفة المزيد حول هذا الأمر.", "app.containers.Comments.internalConversation": "محادثة داخلية", "app.containers.Comments.loadMoreComments": "تحميل المزيد من التعليقات", "app.containers.Comments.loadingComments": "تحميل التعليقات جارٍ...", "app.containers.Comments.loadingMoreComments": "تحميل المزيد من التعليقات...", "app.containers.Comments.notVisibleToUsersPlaceholder": "هذا التعليق غير مرئي للمستخدمين العاديين", "app.containers.Comments.postInternalComment": "انشر تعليقًا داخليًا", "app.containers.Comments.postPublicComment": "انشر التعليق العام", "app.containers.Comments.profanityError": "عذرًا، يبدو أن منشورك يحتوي على بعض الألفاظ التي تتنافى مع {guidelinesLink}. إننا نسعى إلى إبقاء هذه المكان آمنًا للجميع. يرجى تعديل مدخلاتك وإعادة المحاولة.", "app.containers.Comments.publicDiscussion": "مناقشة عامة", "app.containers.Comments.publishComment": "انشر تعليقك", "app.containers.Comments.reportAsSpamModalTitle": "لماذا تريد الإبلاغ عن هذا على أنه مزعج؟", "app.containers.Comments.saveComment": "<PERSON><PERSON><PERSON>", "app.containers.Comments.signInLinkText": "تسجيل الدخول", "app.containers.Comments.signInToComment": "يُرجى {signInLink} للتعليق.", "app.containers.Comments.signUpLinkText": "التسجيل", "app.containers.Comments.verifyIdentityLinkText": "تحقق من هويتك", "app.containers.Comments.visibleToUsersPlaceholder": "هذا التعليق مرئي للمستخدمين العاديين", "app.containers.Comments.visibleToUsersWarning": "التعليقات المنشورة هنا ستكون مرئية للمستخدمين العاديين.", "app.containers.ContentBuilder.PageTitle": "وصف المشروع", "app.containers.CookiePolicy.advertisingContent": "بصفتنا {orgName}، فنحن لا نستخدم أدوات الإعلان على منصات المشاركة.", "app.containers.CookiePolicy.advertisingTitle": "الإعلانات", "app.containers.CookiePolicy.analyticsContents": "تتبع ملفات تعريف ارتباط التحليلات سلوك الزائر، مثل: تحديد الصفحات التي تمت زيارتها والمدة الزمنية للزيارة. ويجوز أن تجمع أيضًا بعض البيانات الفنية، بما فيها معلومات المتصفح والموقع التقريبي وعناوين IP. نستخدم هذه البيانات داخليًا فقط لمواصلة تحسين تجربة المستخدم الشاملة والأداء الوظيفي للمنصة. يجوز أيضًا إتاحة تلك البيانات للمشاركة بين Go Vocal و{orgName} لتقييم مستوى التفاعل مع المشاريع وتحسينه في المنصة. تجدر الإشارة إلى أن البيانات تُقدَّم مجهولة الهوية وتُستخدم على مستوى جماعي ولا تكشف هويتك الشخصية. ومع ذلك، من الممكن أن يتم كشف تلك الهوية الشخصية إذا تم دمج هذه البيانات مع مصادر بيانات أخرى.", "app.containers.CookiePolicy.analyticsTitle": "التحليلات", "app.containers.CookiePolicy.cookiePolicyDescription": "شرح مفصل حول كيفية استخدامنا لملفات تعريف الارتباط على هذه المنصة", "app.containers.CookiePolicy.cookiePolicyTitle": "سياسة ملفات تعريف الارتباط", "app.containers.CookiePolicy.essentialContent": "هناك بعض ملفات تعريف الارتباط الأساسية اللازمة لضمان الأداء الوظيفي السليم لهذه المنصة. تُستخدم ملفات تعريف الارتباط الأساسية هذه في المقام الأول لمصادقة حسابك عندما تزور المنصة ولحفظ لغتك المفضلة.", "app.containers.CookiePolicy.essentialTitle": "ملفات تعريف الارتباط الأساسية", "app.containers.CookiePolicy.externalContent": "يجوز أن تعرض بعض صفحاتنا محتوى من موفرين خارجيين، مثل: YouTube أو Typeform. لا نمتلك أي سلطة لمراقبة ملفات تعريف الارتباط هذه المتوفرة من أطراف ثالثة، ويجوز أن يؤدي عرض المحتوى المقدَّم من هؤلاء الموفرين الخارجيين إلى تثبيتها في جهازك أيضًا.", "app.containers.CookiePolicy.externalTitle": "ملفات تعريف الارتباط الخارجية", "app.containers.CookiePolicy.functionalContents": "يجوز تمكين ملفات تعريف ارتباط وظيفية من قِبل زائرين لتلقي إشعارات حول تحديثات وللوصول إلى قنوات الدعم من المنصة مباشرة.", "app.containers.CookiePolicy.functionalTitle": "الوظائف", "app.containers.CookiePolicy.headCookiePolicyTitle": "سياسة ملفات تعريف الارتباط | {orgName}", "app.containers.CookiePolicy.intro": "مثل معظم المواقع الإلكترونية، فإننا نستخدم ملفات تعريف الارتباط لتحسين التجربة التي نقدمها لك وللزوار الآخرين على هذه المنصة. ولأننا نريد أن نتمتع بشفافية كاملة بشأن سبب وكيفية استخدام ملفات تعريف الارتباط هذه، فستجد كل التفاصيل أدناه، بأكبر قدر ممكن من الصياغة الواضحة. لا يتم استخدام ملفات تعريف الارتباط المستخدمة على منصتنا لتحديد وتتبع مستخدمين معينين، فهي \"لا تعرف هويتك\". ولكن من المهم التأكيد، على أنه وبالرغم من أنها تقوم بتتبع البيانات الفنية فقط، مثل معلومات المتصفح والموقع التقريبي وعنوان الـ IP، وبالرغم من عدم استخدامها لغرض تحديد الهوية، إلا أن اقترانها بمصادر البيانات الأخرى قد يؤدي إلى تحديد هويتك.", "app.containers.CookiePolicy.manageCookiesDescription": "يمكنك تمكين ملفات تعريف الارتباط التحليلية والتسويقية والوظيفية أو تعطيلها في أي وقت من تفضيلات ملفات تعريف الارتباط. يمكنك أيضًا حذف أي ملفات تعريف ارتباط حالية يدويًا أو تلقائيًا عبر متصفح الإنترنت الذي تستخدمه. ومع ذلك، يجوز إعادة ملفات تعريف الارتباط بعد موافقتك عند القيام بأي زيارات لاحقة إلى هذه المنصة. إذا لم تحذف ملفات تعريف الارتباط، يتم تخزين تفضيلات ملفات تعريف الارتباط لمدة 60 يومًا ويُطلب منك بعدها الموافقة مجددًا.", "app.containers.CookiePolicy.manageCookiesPreferences": "انتقل إلى {manageCookiesPreferencesButtonText} للاطلاع على قائمة كاملة بعمليات تكامل الأطراف الثالثة المستخدمة في هذه المنصة ولإدارة تفضيلاتك.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "إعدادات ملفات تعريف الارتباط", "app.containers.CookiePolicy.manageCookiesTitle": "إدارة ملفات تعريف الارتباط", "app.containers.CookiePolicy.viewPreferencesButtonText": "إعدادات ملفات تعريف الارتباط", "app.containers.CookiePolicy.viewPreferencesText": "يجوز ألا تُطبَّق فئات ملفات تعريف الارتباط التالية على جميع الزائرين أو المنصات، راجع {viewPreferencesButton} للاطلاع على قائمة كاملة بعمليات تكامل الأطراف الثالثة القابلة للتطبيق عليك.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "فيمَ تُستخدم ملفات تعريف الارتباط؟", "app.containers.CustomPageShow.editPage": "تعديل الصفحة", "app.containers.CustomPageShow.goBack": "رجوع", "app.containers.CustomPageShow.notFound": "لم يتم العثور على الصفحة", "app.containers.DisabledAccount.bottomText": "يمكنك تسجيل الدخول مرة أخرى من {date}.", "app.containers.DisabledAccount.termsAndConditions": "البنود و الظروف", "app.containers.DisabledAccount.text2": "تم تعطيل حسابك على منصة المشاركة {orgName} مؤقتًا لانتهاك إرشادات المجتمع. لمزيد من المعلومات حول هذا الموضوع ، يمكنك الرجوع إلى {TermsAndConditions}.", "app.containers.DisabledAccount.title": "تم تعطيل حسابك مؤقتا", "app.containers.EventsShow.addToCalendar": "إضافة إلى التقويم", "app.containers.EventsShow.editEvent": "تحرير الحدث", "app.containers.EventsShow.emailSharingBody2": "حضور هذا الحدث: {eventTitle}. اقرأ المزيد على {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "تاريخ ووقت الحدث", "app.containers.EventsShow.eventFrom2": "من \"{projectTitle}\"", "app.containers.EventsShow.goBack": "عُد", "app.containers.EventsShow.goToProject": "اذه<PERSON> إلى المشروع", "app.containers.EventsShow.haveRegistered": "لقد قمت بالتسجيل", "app.containers.EventsShow.icsError": "حد<PERSON> خطأ أثناء تنزيل ملف ICS", "app.containers.EventsShow.linkToOnlineEvent": "را<PERSON><PERSON> إلى الحدث عبر الإنترنت", "app.containers.EventsShow.locationIconAltText": "موقع", "app.containers.EventsShow.metaTitle": "الحدث: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "اجتماع عبر الإنترنت", "app.containers.EventsShow.onlineLinkIconAltText": "رابط اللقاء عبر الإنترنت", "app.containers.EventsShow.registered": "مسجل", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 مسجلين} one {1 مسجل} other {عدد المسجلين}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} المسجلين", "app.containers.EventsShow.registrantsIconAltText": "المسجلين", "app.containers.EventsShow.socialMediaSharingMessage": "حضور هذا الحدث: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# مشارك} other {# مشاركون}}", "app.containers.EventsViewer.allTime": "كل الوقت", "app.containers.EventsViewer.date": "تاريخ", "app.containers.EventsViewer.thisMonth2": "الشهر القادم", "app.containers.EventsViewer.thisWeek2": "الأسبوع القادم", "app.containers.EventsViewer.today": "اليوم", "app.containers.IdeaButton.addAContribution": "أضف مُساهمة ", "app.containers.IdeaButton.addAPetition": "أض<PERSON> عريضة", "app.containers.IdeaButton.addAProject": "أضف مشروعاً ", "app.containers.IdeaButton.addAProposal": "أضف اقتراحًا", "app.containers.IdeaButton.addAQuestion": "أضِف سؤالًا", "app.containers.IdeaButton.addAnInitiative": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnOption": "أضِف خيارًا", "app.containers.IdeaButton.postingDisabled": "لا يتم قبول الطلبات الجديدة حالياً", "app.containers.IdeaButton.postingInNonActivePhases": "يُمكن إضافة الطلبات الجديدة في المراحل النشطة فقط. ", "app.containers.IdeaButton.postingInactive": "لا يتم قبول الطلبات الجديدة حالياً", "app.containers.IdeaButton.postingLimitedMaxReached": "لقد أكملت هذا الاستبيان فعلاً. نشكرك على استجابتك!", "app.containers.IdeaButton.postingNoPermission": "عمليات الإرسال الجديدة غير ممكنة حاليًا", "app.containers.IdeaButton.postingNotYetPossible": "لا تُقبل الطلبات الجديدة هنا بعد. ", "app.containers.IdeaButton.signInLinkText": "تسجيل الدخول", "app.containers.IdeaButton.signUpLinkText": "التسجيل", "app.containers.IdeaButton.submitAnIssue": "قُم بتقديم مشكلة", "app.containers.IdeaButton.submitYourIdea": "قُم بتقديم فكرتك ", "app.containers.IdeaButton.takeTheSurvey": "شارك في الاستبيان", "app.containers.IdeaButton.verificationLinkText": "تحقق من هويتك الآن.", "app.containers.IdeaCard.readMore": "اقر<PERSON> أكثر", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {لا يوجد تعليقات} one {تعليق واحد} other {# تعليقات}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {لا توجد أصوات} one {صوت واحد} other {# صوت}} من {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "إغلاق لوحة المرشحات", "app.containers.IdeaCards.a11y_totalItems": "جميع العناصر: {ideasCount}", "app.containers.IdeaCards.all": "الكل", "app.containers.IdeaCards.allStatuses": "جميع الحالات", "app.containers.IdeaCards.contributions": "المساهمات", "app.containers.IdeaCards.ideaTerm": "الأفكار", "app.containers.IdeaCards.initiatives": "المبادرات", "app.containers.IdeaCards.issueTerm": "مشاكل", "app.containers.IdeaCards.list": "القائمة", "app.containers.IdeaCards.map": "الخريطة", "app.containers.IdeaCards.mostDiscussed": "الأكثر مناقشة", "app.containers.IdeaCards.newest": "الأحدث", "app.containers.IdeaCards.noFilteredResults": "لا يوجد نتائج. يُرجى تجربة مُرشحاً أو مُصطلحاً مُختلفاً للبحث. ", "app.containers.IdeaCards.numberResults": "النتائج ({postCount})", "app.containers.IdeaCards.oldest": "الأقدم", "app.containers.IdeaCards.optionTerm": "خيارات", "app.containers.IdeaCards.petitions": "الالتماسات", "app.containers.IdeaCards.popular": "الأكثر تصويتًا", "app.containers.IdeaCards.projectFilterTitle": "المشاريع", "app.containers.IdeaCards.projectTerm": "المشاريع", "app.containers.IdeaCards.proposals": "المقترحات", "app.containers.IdeaCards.questionTerm": "أسئلة", "app.containers.IdeaCards.random": "عشوائي", "app.containers.IdeaCards.resetFilters": "إعادة تعيين عوامل التصفية", "app.containers.IdeaCards.showXResults": "اعرض {ideasCount, plural, no {# results} واحدة # result} أخرى {# results}}", "app.containers.IdeaCards.sortTitle": "فرز", "app.containers.IdeaCards.statusTitle": "الحالة", "app.containers.IdeaCards.statusesTitle": "حالة", "app.containers.IdeaCards.topics": "الموضوعات", "app.containers.IdeaCards.topicsTitle": "الموضوعات", "app.containers.IdeaCards.trending": "الرائجة", "app.containers.IdeaCards.tryDifferentFilters": "لا يوجد نتائج. يُرجى تجربة مُرشحاً أو مُصطلحاً مُختلفاً للبحث. ", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} تعليقات} one {{ideasCount} تعليق} other {{ideasCount} تعليقات}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} مساهمات} one {{ideasCount} مساهمة} other {{ideasCount} مساهمات}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} أفكار} one {{ideasCount} فكرة} other {{ideasCount} أفكار}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} مبادرات} one {{ideasCount} مبادرات} other {{ideasCount} مبادرات}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} خيارات} one {{ideasCount} خيار} other {{ideasCount} خيارات}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} عرائض} one {{ideasCount} عرائض} other {{ideasCount} عرائض}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} مشاريع} one {{ideasCount} مشروع} other {{ideasCount} مشاريع}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} مقترحات} one {{ideasCount} مقترحات} other {{ideasCount} مقترحات}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} أسئلة} one {{ideasCount} سؤال} other {{ideasCount} أسئلة}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, no {# results} واحدة # result} أخرى {# results}}", "app.containers.IdeasEditPage.contributionFormTitle": "تعديل المُساهمة", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "فشلت عملية تحميل واحداً أو أكثر من الملفات. يُرجى تفقُّد حجم الملف وتنسيقه والمحاولة مرة أخرى. ", "app.containers.IdeasEditPage.formTitle": "تعديل الفكرة", "app.containers.IdeasEditPage.ideasEditMetaDescription": "تعديل منشورك. إضافة معلومات جديدة وتغيير القديمة. ", "app.containers.IdeasEditPage.ideasEditMetaTitle": "تعديل {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "تحرير المبادرة", "app.containers.IdeasEditPage.issueFormTitle": "تعديل المشكلة", "app.containers.IdeasEditPage.optionFormTitle": "تعديل الخيار ", "app.containers.IdeasEditPage.petitionFormTitle": "تعديل العريضة", "app.containers.IdeasEditPage.projectFormTitle": "تعديل المشروع", "app.containers.IdeasEditPage.proposalFormTitle": "تعديل الاقتراح", "app.containers.IdeasEditPage.questionFormTitle": "تعديل السؤال", "app.containers.IdeasEditPage.save": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "حدثت مشكلة أثناء إرسال النموذج. يرجى التحقق بحثًا عن أي أخطاء وإعادة المحاولة.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "تم نشر كافة المدخلات", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "استكشف جميع المُدخلات التي نُشرت على منصة {orgName} للمُشاركة. ", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "المشاركات | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "المُدخلات", "app.containers.IdeasIndexPage.loadMore": "تحميل المزيد...", "app.containers.IdeasIndexPage.loading": "التحميل جارٍ...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "بشكل افتراضي ، سيتم ربط عمليات الإرسال الخاصة بك بملف التعريف الخاص بك ، ما لم تحدد هذا الخيار.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "انشر بشكل مجهول", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "رؤية الملف الشخصي", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "هذا الاستطلاع غير مفتوح حاليا للردود. يرجى العودة إلى المشروع لمزيد من المعلومات.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "هذا الاستطلاع غير نشط حاليا.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "العودة إلى المشروع", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "لقد أنجزت بالفعل هذا الاستطلاع.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "تم إرسال الاستطلاع", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "شكرا لأستجابتك!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "يج<PERSON> أن يكون طول وصف المساهمة أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "يجب أن يكون طول النص الأساسي للفكرة أكبر من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "يج<PERSON> أن يكون طول عنوان المساهمة أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "يجب أن يكون عنوان المساهمة أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "الرجاء اختيار راعي مشارك واحد على الأقل", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "يجب أن يكون طول وصف الفكرة أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "يجب أن يكون طول وصف الفكرة أكبر من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "يُرجى إضافة وصف", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "يجب أن يكون عنوان الفكرة أقل من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "يجب أن يكون طول عنوان الفكرة أكبر من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "يجب أن يكون وصف المبادرة أقل من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "يجب أن يكون وصف المبادرة أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "يجب أن يكون عنوان المبادرة أقل من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "يجب أن يكون عنوان المبادرة أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "يج<PERSON> أن يكون طول وصف المشكلة أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "يجب أن يكون طول وصف المشكلة أكبر من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "يج<PERSON> أن يكون طول عنوان المشكلة أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "يجب أن يتجاوز طول عنوان المشكلة {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_number_required": "هذا الحقل مطلوب، الرجاء إدخال رقم صحيح", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "يج<PERSON> أن يكون طول وصف الخيار أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "يجب أن يزيد طول وصف الخيار عن {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "يج<PERSON> أن يكون طول عنوان الخيار أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "يجب أن يكون عنوان الخيار أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "يرجى تحديد وسم واحد على الأقل", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "يجب أن يكون وصف العريضة أقل من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "يجب أن يكون وصف العريضة أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "يجب أن يكون عنوان العريضة أقل من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "يجب أن يكون عنوان العريضة أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "يج<PERSON> أن يكون طول وصف المشروع أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "يجب أن يكون طول وصف المشروع أكبر من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "يج<PERSON> أن يكون طول عنوان المشروع أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "يجب أن يكون عنوان المشروع أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "يجب أن يكون وصف الاقتراح أقل من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "يجب أن يكون وصف الاقتراح أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "يجب أن يكون عنوان الاقتراح أقل من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "يجب أن يكون عنوان الاقتراح أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "ي<PERSON><PERSON>ى إدخال رقم", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "ي<PERSON><PERSON>ى إدخال رقم", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "يجب أن يكون طول وصف السؤال أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "يجب أن يكون طول وصف السؤال أكبر من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "يجب أن يكون طول عنوان السؤال أقل من {limit} حرف/حروف", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "يجب أن يكون عنوان السؤال أكثر من {limit} حرفًا", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "يُرجى إضافة عنوان", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "يجب أن يكون طول وصف المساهمة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "يج<PERSON> ألا يقل طول وصف المساهمة عن 30 حرفًا", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "يجب أن يكون طول عنوان المساهمة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "يج<PERSON> أ<PERSON>ا يقل طول عنوان المساهمة عن 10 أحرف", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "يجب أن يكون طول وصف الفكرة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "يجب ألّا يقل طول وصف الفكرة عن 30 حرفًا", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "يُرجى إضافة عنوان", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "يجب أن يكون طول عنوان الفكرة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "يجب ألّا يقل طول عنوان الفكرة عن 10 حروف", "app.containers.IdeasNewPage.api_error_includes_banned_words": "ربما استخدمت كلمة واحدة أو أكثر تُعد نابية في {guidelinesLink}. يرجى تعديل النص لإزالة أي ألفاظ نابية قد يحتوي عليها.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "يجب أن يكون وصف المبادرة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "يجب أن يكون وصف المبادرة 30 حرفًا على الأقل", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "يجب أن يكون عنوان المبادرة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "يجب أن يكون عنوان المبادرة 10 أحرف على الأقل", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "يجب أن يكون طول وصف المشكلة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "يج<PERSON> ألا يقل طول وصف المشكلة عن 30 حرفًا", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "يجب أن يكون طول عنوان المشكلة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "يج<PERSON> أ<PERSON>ا يقل طول عنوان المشكلة عن 10 أحرف", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "يجب أن يكون طول وصف الخيار أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "يج<PERSON> ألا يقل طول وصف الخيار عن 30 حرفًا", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "يجب أن يكون طول عنوان الخيار أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "يج<PERSON> أ<PERSON>ا يقل طول عنوان الخيار عن 10 أحرف", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "يجب أن يكون وصف العريضة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "يجب أن يكون وصف العريضة 30 حرفًا على الأقل", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "يجب أن يكون عنوان العريضة أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "يج<PERSON> أن يكون عنوان العريضة 10 أحرف على الأقل", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "يجب أن يكون طول وصف المشروع أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "يج<PERSON> ألا يقل طول وصف المشروع عن 30 حرفًا", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "يجب أن يكون طول عنوان المشروع أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "يج<PERSON> أ<PERSON>ا يقل طول عنوان المشروع عن 10 أحرف", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "يجب أن يكون وصف الاقتراح أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "يجب أن يكون وصف الاقتراح 30 حرفًا على الأقل", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "يجب أن يكون عنوان الاقتراح أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "يج<PERSON> أن يكون عنوان الاقتراح 10 أحرف على الأقل", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "يُرجى إضافة وصف", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "يجب أن يكون طول وصف السؤال أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "يجب ألا يقل طول وصف السؤال عن 30 حرفًا", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "يجب أن يكون طول عنوان السؤال أقل من 80 حرفًا", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "يج<PERSON> ألا يقل طول عنوان السؤال عن 10 أحرف", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "إلغاء", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "نعم أريد الرحيل", "app.containers.IdeasNewPage.contributionMetaTitle1": "إضافة مساهمة جديدة للمشروع | {orgName}", "app.containers.IdeasNewPage.editSurvey": "تعديل الاستبيان", "app.containers.IdeasNewPage.ideaNewMetaDescription": "انشر طلباً وانضم إلى المناقشة على منصة {orgName} للمُشاركة. ", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "إضافة فكرة جديدة للمشروع | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "إضافة مبادرة جديدة للمشروع | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "إضافة عدد جديد للمشروع | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "هل انت متاكد انك تريد ان تغادر؟", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "تم حفظ مسودة إجاباتك بشكل خاص ويمكنك العودة لإكمالها لاحقًا.", "app.containers.IdeasNewPage.leaveSurvey": "ترك الاستطلاع", "app.containers.IdeasNewPage.leaveSurveyText": "لن يتم حفظ إجاباتك.", "app.containers.IdeasNewPage.optionMetaTitle1": "إضافة خيار جديد للمشروع | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "إضافة عريضة جديدة إلى المشروع | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "إضافة مشروع جديد للمشروع | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "إضافة اقتراح جديد للمشروع | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "أضف سؤال جديد للمشروع | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "قبول الدعوة", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "دعوة للمشاركة في الرعاية", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "الرعاة المشاركون", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "لقد تمت دعوتك لتصبح أحد الرعاة المشاركين.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "تمت قبول الدعوة", "app.containers.IdeasShow.Cosponsorship.pending": "قيد الانتظار", "app.containers.IdeasShow.MetaInformation.attachments": "المُرفقات", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} في {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "الحالة الحالية", "app.containers.IdeasShow.MetaInformation.location": "الموقع", "app.containers.IdeasShow.MetaInformation.postedBy": "تم النشر بواسطة", "app.containers.IdeasShow.MetaInformation.similar": "مدخلات مماثلة", "app.containers.IdeasShow.MetaInformation.topics": "الموضوعات", "app.containers.IdeasShow.commentCTA": "أضِف تعليقًا", "app.containers.IdeasShow.contributionEmailSharingBody": "ادعم هذه المُساهمة '{postTitle}' على {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "ادعم هذه المُساهمة: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "شكراً لتقديم مُساهمتك! ", "app.containers.IdeasShow.contributionTwitterMessage": "ادعم هذه المُساهمة: {postTitle} ", "app.containers.IdeasShow.contributionWhatsAppMessage": "ادعم هذه المُساهمة: {postTitle}", "app.containers.IdeasShow.currentStatus": "الحالة الحالية", "app.containers.IdeasShow.deletedUser": "مؤلف غير معروف", "app.containers.IdeasShow.ideaEmailSharingBody": "ادعم فكرتي '{ideaTitle}' على {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "ادعم فكرتي: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "ادعم هذه الفكرة: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "ادعم هذه الفكرة: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "ادعم هذه المشكلة: {postTitle}", "app.containers.IdeasShow.imported": "مستورد", "app.containers.IdeasShow.importedTooltip": "تم جمع هذا {inputTerm} دون اتصال بالإنترنت وتم تحميله تلقائيًا على النظام الأساسي.", "app.containers.IdeasShow.initiativeEmailSharingBody": "ادعم هذه المبادرة '{ideaTitle}' على {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "ادعم هذه المبادرة: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "شكرا لتقديم مبادرتك!", "app.containers.IdeasShow.initiativeTwitterMessage": "ادعم هذه المبادرة: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "ادعم هذه المبادرة: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "ادعم هذه المشكلة '{postTitle}' على {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "ادعم هذه المشكلة: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "شكراً لتقديمك هذه المشكلة! ", "app.containers.IdeasShow.issueTwitterMessage": "ادعم هذه المشكلة: {postTitle}", "app.containers.IdeasShow.metaTitle": "الإدخال: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "ادعم هذه الخيار {postTitle} على {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "ادعم هذا الخيار: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "تم نشر خيارك بنجاح! ", "app.containers.IdeasShow.optionTwitterMessage": "ادعم هذا الخيار: {postTitle} ", "app.containers.IdeasShow.optionWhatsAppMessage": "ادعم هذا الخيار: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "ادعم هذه العريضة '{ideaTitle}' على {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "ادعم هذه العريضة: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "شكرا لتقديمك عريضتك!", "app.containers.IdeasShow.petitionTwitterMessage": "ادعم هذه العريضة: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "ادعم هذه العريضة: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "ادعم هذا المشروع '{postTitle}' على {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "ادعم هذا المشروع: {postTitle} ", "app.containers.IdeasShow.projectSharingModalTitle": "شكراً لتقديمك هذا المشروع! ", "app.containers.IdeasShow.projectTwitterMessage": "ادعم هذا المشروع: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "ادعم هذا المشروع: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "ادعم هذا الاقتراح '{ideaTitle}' في {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "ادعم هذا الاقتراح: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "شكرا لتقديمك اقتراحك!", "app.containers.IdeasShow.proposalTwitterMessage": "ادعم هذا الاقتراح: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "ادعم هذا الاقتراح: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "الوقت المتبقي للتصويت:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} من {votingThreshold} الأصوات المطلوبة", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "إلغاء التصويت", "app.containers.IdeasShow.proposals.VoteControl.days": "أيام", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "المبادئ التوجيهية لدينا", "app.containers.IdeasShow.proposals.VoteControl.hours": "ساعات", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "الحالة والأصوات", "app.containers.IdeasShow.proposals.VoteControl.minutes": "دقيقة", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "مزيد من المعلومات", "app.containers.IdeasShow.proposals.VoteControl.vote": "تصويت", "app.containers.IdeasShow.proposals.VoteControl.voted": "تم التصويت", "app.containers.IdeasShow.proposals.VoteControl.votedText": "سيتم إعلامك عندما تنتقل هذه المبادرة إلى الخطوة التالية. {x, plural, =0 {هناك {xDays} متبقية.} one {هناك {xDays} متبقية.} other {هناك {xDays} متبقية.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "لقد تم تقديم تصويتك!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "لسوء الحظ، لا يمكنك التصويت على هذا الاقتراح. اقرأ السبب في {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {أقل من يوم} one {يوم واحد} other {# يوم}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {لا توجد أصوات} one {صوت واحد} other {# أصوات}}", "app.containers.IdeasShow.questionEmailSharingBody": "انضم إلى مناقشة هذا السؤال '{postTitle}' على {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "انضم إلى المناقشة: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "تم نشر سؤالك بنجاح!", "app.containers.IdeasShow.questionTwitterMessage": "انضم إلى المناقشة: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "انضم إلى المناقشة: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "لماذا تريد الإبلاغ عن هذا على أنه مزعج؟", "app.containers.IdeasShow.share": "مشاركة", "app.containers.IdeasShow.sharingModalSubtitle": "تواصل مع المزيد من الأشخاص واجعل صوتك مسموعًا.", "app.containers.IdeasShow.sharingModalTitle": "شكراً لتقديم فكرتك! ", "app.containers.Navbar.completeOnboarding": "Complete onboarding", "app.containers.Navbar.completeProfile": "الملف الشخصي الكامل", "app.containers.Navbar.confirmEmail2": "تأكيد عنوان البريد الإلكتروني", "app.containers.Navbar.unverified": "لم يتم التحقق منه", "app.containers.Navbar.verified": "تم التحقق منه", "app.containers.NewAuthModal.beforeYouFollow": "قبل أن تتبع", "app.containers.NewAuthModal.beforeYouParticipate": "قبل أن تشارك", "app.containers.NewAuthModal.completeYourProfile": "أكمل ملفك الشخصي", "app.containers.NewAuthModal.confirmYourEmail": "أكِّد عنوان بريدك الإلكتروني", "app.containers.NewAuthModal.logIn": "تسجيل الدخول", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "راجع الشروط أدناه للمتابعة.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "يرجى استكمال ملف التعريف الخاص بك.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "ارجع إلى خيارات تسجيل الدخول", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "ليس لديك حساب؟ {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "اشتراك", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "يجب أن يتكون الرمز من 4 أرقام.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "تواصل مع FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "لم يتم تمكين طرق المصادقة على هذا النظام الأساسي.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "بالمتابعة ، فإنك توافق على تلقي رسائل البريد الإلكتروني من هذا النظام الأساسي. يمكنك تحديد رسائل البريد الإلكتروني التي ترغب في تلقيها في صفحة \"إعداداتي\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "الب<PERSON>يد الإلكتروني", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "إدخال عنوان بريد إلكتروني بالصيغة الصحيحة، مثل: <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "إدخال عنوان بريد إلكتروني", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "أدخل عنوان بريدك الإلكتروني للمتابعة.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "نسيت كلمة المرور?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "سجِّل الدخول إلى حسابك: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "ير<PERSON>ى إدخال كلمة مرورك", "app.containers.NewAuthModal.steps.Password.password": "كلمة المرور", "app.containers.NewAuthModal.steps.Password.rememberMe": "تذكَّرني", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "عدم التحديد في حالة استخدام كمبيوتر عام", "app.containers.NewAuthModal.steps.Success.allDone": "تم بالكامل", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "تابع الآن مشاركتك.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "تم التحقق من هويتك. أنت الآن عضو كامل في المجتمع على هذه المنصة.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "لقد تم التحقق منك الآن!", "app.containers.NewAuthModal.steps.close": "يغلق", "app.containers.NewAuthModal.steps.continue": "متابعة", "app.containers.NewAuthModal.whatAreYouInterestedIn": "ماالذي تهتم به؟", "app.containers.NewAuthModal.youCantParticipate": "لا يمكنك المشاركة", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {لا توجد إشعارات تم عرضها} one {إشعار واحد تم عرضه} other {# إشعار/إشعارات تم عرضها}}", "app.containers.NotificationMenu.adminRightsReceived": "أنت الآن مسؤول عن المنصة", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "تم حذف تعليقك على \"{postTitle}\" من قبل مسؤول لأن\n      {reasonCode, select, irrelevant {لا صلة له بالموضوع} inappropriate {محتواه غير مناسب} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} تم قبول دعوة الرعاية المشتركة الخاصة بك", "app.containers.NotificationMenu.deletedUser": "مؤلف غير معروف", "app.containers.NotificationMenu.error": "تعذّر تحميل الإشعارات", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} علق داخليًا على إدخال مخصص لك", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} علق داخليًا على إدخال قمت بالتعليق عليه داخليًا", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} علق داخليًا على إدخال في مشروع تديره", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} علق داخليًا على إدخال غير محدد في مشروع غير مُدار", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} علق على تعليقك الداخلي", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} تمت دعوتك للمشاركة في رعاية مساهمة", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} تمت دعوتك للمشاركة في رعاية فكرة", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "دعاك {name} للمشاركة في رعاية اقتراح", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} تمت دعوتك للمشاركة في رعاية قضية", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} تمت دعوتك للمشاركة في رعاية أحد الخيارات", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} تمت دعوتك للمشاركة في رعاية العريضة", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} تمت دعوتك للمشاركة في رعاية مشروع", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} تمت دعوتك للمشاركة في رعاية اقتراح", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} تمت دعوتك للمشاركة في رعاية سؤال", "app.containers.NotificationMenu.loadMore": "تحميل المزيد...", "app.containers.NotificationMenu.loading": "تحميل الإشعارات...", "app.containers.NotificationMenu.mentionInComment": "ذكرك {name} في تعليق", "app.containers.NotificationMenu.mentionInInternalComment": "{name} ذكرك في تعليق داخلي", "app.containers.NotificationMenu.mentionInOfficialFeedback": "ذكرك {name} في تحديث رسمي", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "لم تقم بإرسال الاستبيان الخاص بك", "app.containers.NotificationMenu.noNotifications": "ليس لديك أي إشعارات بعد", "app.containers.NotificationMenu.notificationsLabel": "الإشعارات", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} قدم تحديثًا رسميًا بشأن المساهمة التي تتابعها", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} قدم تحديثًا رسميًا لفكرة تتبعها", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} قدم تحديثًا رسميًا حول المبادرة التي تتبعها", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} قدم تحديثًا رسميًا بشأن مشكلة تتابعها", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} قدم تحديثًا رسميًا على الخيار الذي تتبعه", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} قدم تحديثًا رسميًا بشأن العريضة التي تتابعها", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} قدم تحديثًا رسميًا لمشروع تتابعه", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} قدم تحديثًا رسميًا بشأن الاقتراح الذي تتبعه", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} قدم تحديثًا رسميًا على سؤال تتابعه", "app.containers.NotificationMenu.postAssignedToYou": "تم تعيين {postTitle} لك", "app.containers.NotificationMenu.projectModerationRightsReceived": "أنت الآن مدير المشروع لـ {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "دخل {projectTitle} مرحلة جديدة", "app.containers.NotificationMenu.projectPhaseUpcoming": "سيدخل {projectTitle} مرحلة جديدة في {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "تم نشر مشروع جديد", "app.containers.NotificationMenu.projectReviewRequest": "{name} طل<PERSON> الموافقة على نشر المشروع \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} تمت الموافقة على \"{projectTitle}\" للنشر", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "لقد تغيرت الحالة {ideaTitle} إلى {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "بلغ {post} حد التصويت المطلوب", "app.containers.NotificationMenu.userAcceptedYourInvitation": "قبِل {name} دعوتك", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} عل<PERSON> على المساهمة التي تتابعها", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} علق على فكرة تتابعها", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} عل<PERSON> على المبادرة التي تتابعها", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} علق على القضية التي تتابعها", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} عل<PERSON> على الخيار الذي تتبعه", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} عل<PERSON> على العريضة التي تتابعها", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} عل<PERSON> على المشروع الذي تتابعه", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} عل<PERSON> على اقتراح تتابعه", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} علق على السؤال الذي تتابعه", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} تم الإبلاغ عن الرقم \"{postTitle}\" كرسائل غير مرغوب فيها", "app.containers.NotificationMenu.userReactedToYourComment": "تفاعل {name} مع تعليقك", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "أبلغ {name} عن تعليق على الرقم \"{postTitle}\" كرسائل غير مرغوب فيها", "app.containers.NotificationMenu.votingBasketNotSubmitted": "لم ترسل أصواتك", "app.containers.NotificationMenu.votingBasketSubmitted": "لقد قمت بالتصويت بنجاح", "app.containers.NotificationMenu.votingLastChance": "آخر فرصة للتصويت لـ {phaseTitle}", "app.containers.NotificationMenu.votingResults": "تم الكشف عن {phaseTitle} نتيجة تصويت", "app.containers.NotificationMenu.xAssignedPostToYou": "قام {name} بتعيين {postTitle} إليك", "app.containers.PasswordRecovery.emailError": "لا يبدو هذا كبريد إلكتروني صالح", "app.containers.PasswordRecovery.emailLabel": "الب<PERSON>يد الإلكتروني", "app.containers.PasswordRecovery.emailPlaceholder": "عنوان بريدي الإلكتروني", "app.containers.PasswordRecovery.helmetDescription": "صفحة إعادة تعيين كلمة المرور", "app.containers.PasswordRecovery.helmetTitle": "إعادة تعيين كلمة المرور", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "إذا كان عنوان البريد الإلكتروني هذا مسجَّلاً في المنصة، فقد تم إرسال رابط إعادة تعيين كلمة المرور إليه.", "app.containers.PasswordRecovery.resetPassword": "أرسل رابط إعادة تعيين كلمة المرور", "app.containers.PasswordRecovery.submitError": "لم نتمكن من العثور على حساب مرتبط بالبريد الإلكتروني هذا. يمكنك محاولة التسجيل بدلًا من ذلك.", "app.containers.PasswordRecovery.subtitle": "أين يمكننا إرسال رابط لاختيار كلمة مرور جديدة؟", "app.containers.PasswordRecovery.title": "تمت إعادة تعيين كلمة المرور", "app.containers.PasswordReset.helmetDescription": "صفحة إعادة تعيين كلمة المرور", "app.containers.PasswordReset.helmetTitle": "إعادة تعيين كلمة المرور", "app.containers.PasswordReset.login": "تسجيل الدخول", "app.containers.PasswordReset.passwordError": "يجب ألّا يقل طول كلمة المرور عن 8 حروف", "app.containers.PasswordReset.passwordLabel": "كلمة المرور", "app.containers.PasswordReset.passwordPlaceholder": "كلمة المرور الجديدة", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "تم تحديث كلمة مرورك بنجاح.", "app.containers.PasswordReset.pleaseLogInMessage": "يرجى تسجيل الدخول باستخدام كلمة مرورك الجديدة.", "app.containers.PasswordReset.requestNewPasswordReset": "اطلب إعادة تعيين كلمة مرور جديدة", "app.containers.PasswordReset.submitError": "حدث خطأ ما. يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.PasswordReset.title": "إعادة تعيين كلمة المرور", "app.containers.PasswordReset.updatePassword": "تأكيد كلمة المرور الجديدة", "app.containers.ProjectFolderCards.allProjects": "جميع المشاريع", "app.containers.ProjectFolderCards.currentlyWorkingOn": "تعمل {orgName} حاليًا على", "app.containers.ProjectFolderShowPage.editFolder": "تعديل", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "معلومات حول هذا المشروع", "app.containers.ProjectFolderShowPage.metaTitle1": "المجلد: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "تحميل المزيد", "app.containers.ProjectFolderShowPage.seeLess": "شا<PERSON><PERSON> أقل", "app.containers.ProjectFolderShowPage.share": "مشاركة", "app.containers.Projects.PollForm.document": "وثيقة", "app.containers.Projects.PollForm.formCompleted": "شكرًا لمشاركتك في الاستطلاع!", "app.containers.Projects.PollForm.maxOptions": "الح<PERSON> الأقصى {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "لقد أجريت هذا الاستطلاع بالفعل.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "يمكن إجراء هذا الاستطلاع فقط عندما تكون هذه المرحلة نشطة.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "للأسف، ليس لديك الحق بالمشاركة في هذا الاستطلاع.", "app.containers.Projects.PollForm.pollDisabledNotPossible": "من غير الممكن حاليًا المشاركة في هذا الاستطلاع.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "لم يعُد هذا الاستطلاع متاحًا لأن هذا المشروع لم يعد نشطًا بعد الآن.", "app.containers.Projects.PollForm.sendAnswer": "إرسال", "app.containers.Projects.a11y_phase": "المرحلة {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "نظرة عامة على المراحل", "app.containers.Projects.a11y_titleInputs": "جميع المُدخلات التي تم تقديمها إلى هذا المشروع ", "app.containers.Projects.a11y_titleInputsPhase": "جميع المُدخلات التي تم تقديمها إلى هذه المرحلة", "app.containers.Projects.accessRights": "حقوق الوصول", "app.containers.Projects.addedToBasket": "تمت الإضافة إلى سلَّتك", "app.containers.Projects.allocateBudget": "خصّص ميزانيتك", "app.containers.Projects.archived": "مؤرشف", "app.containers.Projects.basketSubmitted": "تم إرسال سلَّتك!", "app.containers.Projects.contributions": "المُساهمات", "app.containers.Projects.createANewPhase": "إنشاء مرحلة جديدة", "app.containers.Projects.currentPhase": "المرحلة الحالية", "app.containers.Projects.document": "وثيقة", "app.containers.Projects.editProject": "تعديل المشروع", "app.containers.Projects.emailSharingBody": "ما رأيك في هذه المبادرة؟ أدلِ بصوتك وانضم إلى المناقشة عبر {initiativeUrl} ليصبح صوتك مسموعًا!", "app.containers.Projects.emailSharingSubject": "ادعم مبادرتي: {initiativeTitle}.", "app.containers.Projects.endedOn": "انتهى في {date}", "app.containers.Projects.events": "الأحداث", "app.containers.Projects.header": "المشاريع", "app.containers.Projects.ideas": "الأفكار", "app.containers.Projects.information": "معلومات", "app.containers.Projects.initiatives": "المبادرات", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "راجع المستند", "app.containers.Projects.invisibleTitlePhaseAbout": "نبذة عن هذه المرحلة", "app.containers.Projects.invisibleTitlePoll": "شارك في الاستطلاع", "app.containers.Projects.invisibleTitleSurvey": "شارك في الاستبيان", "app.containers.Projects.issues": "المشكلات", "app.containers.Projects.liveDataMessage": "أنت تشاهد بيانات في الوقت الفعلي. يتم تحديث أعداد المشاركين باستمرار للمسؤولين. يرجى ملاحظة أن المستخدمين العاديين يرون البيانات المخزنة مؤقتًا، مما قد يؤدي إلى اختلافات طفيفة في الأعداد.", "app.containers.Projects.location": "الموقع:", "app.containers.Projects.manageBasket": "إدارة السلة", "app.containers.Projects.meetMinBudgetRequirement": "يجب الوصول إلى الحد الأدنى للميزانية لإرسال سلَّتك.", "app.containers.Projects.meetMinSelectionRequirement": "يجب الوصول إلى عدد العناصر المحددة المطلوب لإرسال سلَّتك.", "app.containers.Projects.metaTitle1": "المشروع: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "يج<PERSON> <PERSON><PERSON><PERSON>ا<PERSON> الحد الأدنى للميزانية", "app.containers.Projects.myBasket": "Basket", "app.containers.Projects.navPoll": "الاستطلاع", "app.containers.Projects.navSurvey": "الاستبيان", "app.containers.Projects.newPhase": "مرحلة جديدة", "app.containers.Projects.nextPhase": "المرحلة التالية", "app.containers.Projects.noEndDate": "لا يوجد تاريخ انتهاء", "app.containers.Projects.noItems": "لم تحدد أي عناصر حتى الآن", "app.containers.Projects.noPastEvents": "لا توجد أي أحداث سابقة لعرضها", "app.containers.Projects.noPhaseSelected": "لم يتم تحديد مرحلة", "app.containers.Projects.noUpcomingOrOngoingEvents": "لم تتم جدولة أي أحداث قادمة أو جارية حاليًا.", "app.containers.Projects.offlineVotersTooltip": "لا يعكس هذا الرقم أي أعداد للناخبين غير المتصلين بالإنترنت.", "app.containers.Projects.options": "الخيارات", "app.containers.Projects.participants": "مشاركون", "app.containers.Projects.participantsTooltip4": "يعكس هذا الرقم أيضًا عمليات إرسال الاستطلاع المجهولة. من الممكن إرسال استطلاعات مجهولة المصدر إذا كانت الاستطلاعات مفتوحة للجميع (راجع علامة التبويب {accessRightsLink} لهذا المشروع).", "app.containers.Projects.pastEvents": "أحداث سابقة", "app.containers.Projects.petitions": "الالتماسات", "app.containers.Projects.phases": "مرا<PERSON>ل", "app.containers.Projects.previousPhase": "المرحلة السابقة", "app.containers.Projects.project": "المشروع", "app.containers.Projects.projectTwitterMessage": "أدلِ بصوتك! اشترك في {projectName} | {orgName}", "app.containers.Projects.projects": "المشاريع", "app.containers.Projects.proposals": "المقترحات", "app.containers.Projects.questions": "الأسئلة", "app.containers.Projects.readLess": "قراءة أقل", "app.containers.Projects.readMore": "اقر<PERSON> المزيد", "app.containers.Projects.removeItem": "إزالة العنصر", "app.containers.Projects.requiredSelection": "عد<PERSON> العناصر المحددة المطلوب", "app.containers.Projects.reviewDocument": "راجع المستند", "app.containers.Projects.seeTheContributions": "اعرض المُساهمات", "app.containers.Projects.seeTheIdeas": "<PERSON>ا<PERSON><PERSON> الأفكار", "app.containers.Projects.seeTheInitiatives": "شا<PERSON><PERSON> المبادرات", "app.containers.Projects.seeTheIssues": "اعرض المشكلات", "app.containers.Projects.seeTheOptions": "اعرض الخيارات", "app.containers.Projects.seeThePetitions": "انظر الالتماسات", "app.containers.Projects.seeTheProjects": "اعرض المشروعات", "app.containers.Projects.seeTheProposals": "انظر المقترحات", "app.containers.Projects.seeTheQuestions": "اعرض الأسئلة", "app.containers.Projects.seeUpcomingEvents": "انظر الأحداث القادمة", "app.containers.Projects.share": "مشاركة", "app.containers.Projects.shareThisProject": "مشاركة هذا المشروع", "app.containers.Projects.submitMyBasket": "Submit basket", "app.containers.Projects.survey": "الاستبيان", "app.containers.Projects.takeThePoll": "شارك في الاستطلاع", "app.containers.Projects.takeTheSurvey": "شارك في الاستبيان", "app.containers.Projects.timeline": "الجدول الزمني", "app.containers.Projects.upcomingAndOngoingEvents": "أحدا<PERSON> قادمة وجارية", "app.containers.Projects.upcomingEvents": "الأحداث القادمة", "app.containers.Projects.whatsAppMessage": "{اسم المشروع} | من منصة المشاركة لـ {اسم المنظمة}", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "استكشف جميع مشاريع {orgName} الجارية لفهم الكيفية التي يمكنك من خلالها المشاركة.\n تعال وناقش المشاريع المحلية الأكثر أهمية بالنسبة لك.", "app.containers.ProjectsIndexPage.metaTitle1": "المشاريع | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "المشاريع", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "أنا أرغب في التطوع", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "يُرجى {signInLink} أو {signUpLink} أولًا للتطوع في هذا النشاط", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "المشاركة غير مفتوحة حاليا لهذا النشاط.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "تسجيل الدخول", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "التسجيل", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "أنا أسحب عرضي للتطوع", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {لا يوجد متطوعين} one {# متطوع} other {# من المتطوعين}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "تحذير: قد يتضمن الاستطلاع المضمن مشكلات في إمكانية الوصول لمستخدمي قارئ الشاشة. إذا واجهت أي تحديات، يرجى التواصل مع مسؤول المنصة للحصول على رابط للاستبيان من المنصة الأصلية. وبدلاً من ذلك، يمكنك طلب طرق أخرى لملء الاستبيان.", "app.containers.ProjectsShowPage.process.survey.survey": "الاستبيان", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "لتعرف إن كان بإمكانك المشاركة في هذا الاستبيان، يُرجى {logInLink} إلى المنصة أولًا.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "يمكن المشاركة في هذا الاستبيان فقط عندما تكون هذه المرحلة في الجدول الزمني نشطة.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "الرجاء {completeRegistrationLink} لإجراء الاستطلاع.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "للأسف، ليس لديك الحق بالمشاركة في هذا الاستبيان.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "تتطلب المشاركة في الاستبيان التحقق من هويتك. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "الاستبيان لم يعد متاحا، لأن هذا المشروع لم يعد نشطا.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "اكمل التسجيل", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "تسجيل الدخول", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "اشتراك", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "تحقق من حسابك الآن.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "يمكن لمستخدمين معينين فقط مراجعة هذا المستند. من فضلك {signUpLink} أو {logInLink} أولا.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "لا يمكن مراجعة هذا المستند إلا عندما تكون هذه المرحلة نشطة.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "الرجاء {completeRegistrationLink} لمراجعة الوثيقة.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "للأسف ، ليس لديك حقوق مراجعة هذا المستند.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "مراجعة هذا المستند يتطلب التحقق من حسابك. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "لم يعد المستند متاحًا ، نظرًا لأن هذا المشروع لم يعد نشطًا.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(بما في ذلك 1 غير متصل بالإنترنت)} other {(بما في ذلك # غير متصل بالإنترنت)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {اختيار واحد} other {# اختيارات}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "النسبة المئوية للمشاركين الذين اختاروا هذا الخيار.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "النسبة المئوية لإجمالي الأصوات التي حصل عليها هذا الخيار.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "يكلف:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "أ<PERSON>ه<PERSON> المزيد", "app.containers.ReactionControl.a11y_likesDislikes": "إجمالي المعجبين: {likesCount}، إجمالي غير المعجبين: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "لقد قمت بإلغاء عدم إعجابك بهذا الإدخال بنجاح.", "app.containers.ReactionControl.cancelLikeSuccess": "لقد قمت بإلغاء إعجابك بهذا الإدخال بنجاح.", "app.containers.ReactionControl.dislikeSuccess": "لقد لم يعجبك هذا الإدخال بنجاح.", "app.containers.ReactionControl.likeSuccess": "لقد أعجبك هذا الإدخال بنجاح.", "app.containers.ReactionControl.reactionErrorSubTitle": "بسبب خطأ لا يمكن تسجيل رد فعلك. يرجى المحاولة مرة أخرى في بضع دقائق.", "app.containers.ReactionControl.reactionSuccessTitle": "تم تسجيل رد فعلك بنجاح!", "app.containers.ReactionControl.vote": "تصويت", "app.containers.ReactionControl.voted": "تم التصويت", "app.containers.SearchInput.a11y_cancelledPostingComment": "تم إلغاء نشر التعليق.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} تم تحميل التعليقات.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {تم تحميل # حدث} one {تم تحميل # حدث} other {تم تحميل # حدث}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {تم تحميل # نتيجة} one {تم تحميل # نتيجة} other {تم تحميل # نتيجة}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {تم تحميل # نتيجة بحث} one {تم تحميل # نتيجة بحث} other {تم تحميل # نتيجة بحث}}.", "app.containers.SearchInput.removeSearchTerm": "إزالة مصطلح البحث", "app.containers.SearchInput.searchAriaLabel": "ب<PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "ب<PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "ب<PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "مصطلح البحث: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "حل FranceConnect هو المقترح من الدولة الفرنسية لتأمين وتيسير عملية تسجيل الاشتراك في أكثر من 700 خدمة عبر الإنترنت.", "app.containers.SignIn.or": "أو", "app.containers.SignIn.signInError": "المعلومات المقدمة غير صحيحة. انقر على \"هل نسيت كلمة المرور؟\" لإعادة تعيين كلمة المرور الخاصة بك.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "استخدم FranceConnect لتسجيل الدخول أو تسجيل الاشتراك أو التحقق من حسابك.", "app.containers.SignIn.whatIsFranceConnect": "ما هي خدمة France Connect؟", "app.containers.SignUp.adminOptions2": "للمسؤولين ومديري المشاريع", "app.containers.SignUp.backToSignUpOptions": "العودة إلى خيارات التسجيل", "app.containers.SignUp.continue": "يكمل", "app.containers.SignUp.emailConsent": "بالتسجيل، فإنك توافق على استلام رسائل إلكترونية من هذه المنصة. يمكنك اختيار الرسائل التي ترغب في استلامها من إعدادات المستخدم لديك.", "app.containers.SignUp.emptyFirstNameError": "أد<PERSON>ل اسمك الأول", "app.containers.SignUp.emptyLastNameError": "أدخل اسمك الأخير", "app.containers.SignUp.firstNamesLabel": "الاسم الأول", "app.containers.SignUp.goToLogIn": "ألديك حساب بالفعل؟ {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "لقد قرأت ووافقت على {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "لقد قرأت ووافقت على {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "قبل استخدام البيانات في mitgestalten.wien.gv.at. يمكن العثور على مزيد من المعلومات {link}.", "app.containers.SignUp.invitationErrorText": "لقد انتهت صلاحية دعوتك أو تم استخدامها بالفعل. إذا كنت قد استخدمت رابط الدعوة بالفعل لإنشاء حساب، فحاول تسجيل الدخول. وإلا، فقم بالتسجيل لإنشاء حساب جديد.", "app.containers.SignUp.lastNameLabel": "اسم العائلة", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "اتبع مجالات التركيز الخاصة بك ليتم إعلامك بها:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "تابع مواضيعك المفضلة ليتم إعلامك بها:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "حفظ التفضيلات", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "تخطي في الوقت الراهن", "app.containers.SignUp.privacyPolicyNotAcceptedError": "اقبل سياسة الخصوصية للمتابعة", "app.containers.SignUp.signUp2": "التسجيل", "app.containers.SignUp.skip": "تخطي هذه الخطوة", "app.containers.SignUp.tacError": "الموافقة على الشروط والأحكام الخاصة بنا مطلوبة للمتابعة", "app.containers.SignUp.thePrivacyPolicy": "سياسة الخصوصية", "app.containers.SignUp.theTermsAndConditions": "الشروط والأحكام", "app.containers.SignUp.unknownError": "{tenantName, select, LiberalDemocrats {يبدو أنك قد حاولت التسجيل من قبل دون إكمال عملية التسجيل. انقر على تسجيل الدخول بدلًا من ذلك، باستخدام بيانات الاعتماد التي اخترتها خلال المحاولة السابقة.} other {حدث خطأ ما، يُرجى المحاولة مجددًا في وقت لاحق.}}", "app.containers.SignUp.viennaConsentEmail": "عنوان البريد الإلكتروني", "app.containers.SignUp.viennaConsentFirstName": "الاسم الأول", "app.containers.SignUp.viennaConsentFooter": "يمكنك تغيير معلومات ملفك التعريفي بعد تسجيل الدخول. إذا كنت تملك بالفعل حسابًا يحمل عنوان البريد الإلكتروني نفسه في mitgestalten.wien.gv.at، فسيتم ربطه بحسابك الحالي.", "app.containers.SignUp.viennaConsentHeader": "سيتم إرسال البيانات التالية:", "app.containers.SignUp.viennaConsentLastName": "اسم العائلة", "app.containers.SignUp.viennaConsentUserName": "اسم المستخدم", "app.containers.SignUp.viennaDataProtection": "سياسة خصوصية Vienna", "app.containers.SiteMap.contributions": "المُساهمات", "app.containers.SiteMap.cookiePolicyLinkTitle": "ملفات تعريف الارتباط", "app.containers.SiteMap.issues": "المشكلات", "app.containers.SiteMap.options": "الخيارات", "app.containers.SiteMap.projects": "المشاريع", "app.containers.SiteMap.questions": "الأسئلة", "app.containers.SpamReport.buttonSave": "إب<PERSON><PERSON>غ", "app.containers.SpamReport.buttonSuccess": "نجاح", "app.containers.SpamReport.inappropriate": "إنه غير لائق أو مسيء", "app.containers.SpamReport.messageError": "حدث خطأ أثناء إرسال النموذج، يُرجى المحاولة مجددًا.", "app.containers.SpamReport.messageSuccess": "تم إرسال إبلاغك", "app.containers.SpamReport.other": "سب<PERSON>ر", "app.containers.SpamReport.otherReasonPlaceholder": "الوصف", "app.containers.SpamReport.wrong_content": "هذا لا ينتمي إلى هنا", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "إزالة صورة ملف التعريف", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "سيتم حذف أصواتك على المقترحات التي لا تزال مفتوحة للتصويت. لن يتم حذف الأصوات على المقترحات التي أغلقت فيها فترة التصويت.", "app.containers.UsersEditPage.addPassword": "أ<PERSON><PERSON> المرور", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "للمشاركة في المشاريع الخاصة بالمواطنين الذين تم التحقق من هوياتهم.", "app.containers.UsersEditPage.becomeVerifiedTitle": "تحقق من هويتك", "app.containers.UsersEditPage.bio": "نبذة عنك", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "لا يمكنك تعديل هذا الحقل لأنه يحتوي على معلومات تم التحقق منها.", "app.containers.UsersEditPage.buttonSuccessLabel": "نجاح", "app.containers.UsersEditPage.cancel": "إلغاء", "app.containers.UsersEditPage.changeEmail": "تغيير الايميل", "app.containers.UsersEditPage.changePassword2": "تغيير كلمة المرور", "app.containers.UsersEditPage.clickHereToUpdateVerification": "يُرجى النقر هنا لتحديث التحقق الخاص بك.", "app.containers.UsersEditPage.conditionsLinkText": "شروطنا", "app.containers.UsersEditPage.contactUs": "ألديك سبب آخر للمغادرة؟ قد نتمكن من مساعدتك {feedbackLink}.", "app.containers.UsersEditPage.deleteAccountSubtext": "نأسف لرحيلك.", "app.containers.UsersEditPage.deleteMyAccount": "ا<PERSON><PERSON><PERSON> حسا<PERSON>ي", "app.containers.UsersEditPage.deleteYourAccount": "ا<PERSON><PERSON><PERSON> حساب<PERSON>", "app.containers.UsersEditPage.deletionSection": "ا<PERSON><PERSON><PERSON> حساب<PERSON>", "app.containers.UsersEditPage.deletionSubtitle": "لا يمكن التراجع عن هذا الإجراء. سيتم إخفاء هويتك للمحتوى الذي نشرته على المنصة. إذا كنت ترغب في حذف كل المحتوى الخاص بك، يمكنك الاتصال بنا على <EMAIL>.", "app.containers.UsersEditPage.email": "الب<PERSON>يد الإلكتروني", "app.containers.UsersEditPage.emailEmptyError": "إدخال عنوان بريد إلكتروني", "app.containers.UsersEditPage.emailInvalidError": "إدخال عنوان بريد إلكتروني بالصيغة الصحيحة، مثل: <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "أخبرنا", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "الاسم الأول", "app.containers.UsersEditPage.firstNamesEmptyError": "إدخال الأسماء الأولى", "app.containers.UsersEditPage.h1": "معلومات حسابك", "app.containers.UsersEditPage.h1sub": "عدّل معلومات حسابك", "app.containers.UsersEditPage.image": "الصورة الرمزية", "app.containers.UsersEditPage.imageDropzonePlaceholder": "انقر لتحديد صورة ملفك التعريفي (بحجم أقصى 5 ميغابايت)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "جميع إعدادات ملفك التعريفي", "app.containers.UsersEditPage.language": "اللغة", "app.containers.UsersEditPage.lastName": "اسم العائلة", "app.containers.UsersEditPage.lastNameEmptyError": "إدخال الأسماء الأخيرة", "app.containers.UsersEditPage.loading": "التحميل جارٍ...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "يمكنك تغيير بريدك الإلكتروني أو كلمة المرور هنا.", "app.containers.UsersEditPage.loginCredentialsTitle": "بيانات اعتماد تسجيل الدخول", "app.containers.UsersEditPage.messageError": "لم نتمكن من حفظ ملفك التعريفي. حاول مجددًا في وقت لاحق أو تواصل مع <EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "تم حفظ ملفك التعريفي.", "app.containers.UsersEditPage.metaDescription": "هذه هي صفحة إعدادات الملف التعريفي الخاصة بـ {firstName} {lastName} على منصة الإشراك الإلكترونية لـ {tenantName}. هنا يمكنك التحقق من هويتك، وتعديل معلومات حسابك، وحذف حسابك، وتعديل تفضيلات البريد الإلكتروني الخاصة بك.", "app.containers.UsersEditPage.metaTitle1": "صفحة إعدادات الملف الشخصي لـ {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "بمجرد نقرك على هذا الزر، فلن نتمكن أبدًا من استعادة حسابك.", "app.containers.UsersEditPage.noNameWarning2": "يظهر اسمك حاليًا على المنصة على النحو التالي: \"{displayName}\" لأنك لم تدخل اسمك. هذا اسم تم إنشاؤه تلقائيًا. إذا كنت ترغب في تغييره، فيرجى إدخال اسمك أدناه.", "app.containers.UsersEditPage.notificationsSubTitle": "ما هي إشعارات البريد الإلكتروني التي ترغب في استلامها؟ ", "app.containers.UsersEditPage.notificationsTitle": "إشعارات البريد الإلكتروني", "app.containers.UsersEditPage.password": "اختر كلمة مرور جديدة", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "أدخل كلمة مرور لا يقل طولها عن {minimumPasswordLength} حروف", "app.containers.UsersEditPage.passwordAddSection": "<PERSON><PERSON><PERSON>ل<PERSON> مرور", "app.containers.UsersEditPage.passwordAddSubtitle2": "قم بتعيين كلمة مرور وقم بتسجيل الدخول بسهولة إلى النظام الأساسي ، دون الحاجة إلى تأكيد بريدك الإلكتروني في كل مرة.", "app.containers.UsersEditPage.passwordChangeSection": "تغيير كلمة مرورك", "app.containers.UsersEditPage.passwordChangeSubtitle": "أكِّد كلمة مرورك الحالية وغيِّرها إلى كلمة مرور جديدة.", "app.containers.UsersEditPage.privacyReasons": "إذا كنت قلقًا بشأن خصوصيتك، فيمكنك قراءة {conditionsLink}.", "app.containers.UsersEditPage.processing": "الإرسال جارٍ...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "الاسم الأول مطلوب عند تقديم الاسم الأخير", "app.containers.UsersEditPage.reasonsToStayListTitle": "قبل أن تغادر...", "app.containers.UsersEditPage.submit": "حفظ التغييرات", "app.containers.UsersEditPage.tooManyEmails": "هل تستلم الكثير من الرسائل الإلكترونية؟ يمكنك إدارة تفضيلات البريد الإلكتروني في إعدادات ملفك التعريفي.", "app.containers.UsersEditPage.updateverification": "هل طرأ تغيير على معلوماتك الرسمية؟ {reverifyButton}", "app.containers.UsersEditPage.user": "متى ترغب في استلام رسالة إلكترونية لإخطارك؟", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "يمكنك المشاركة في المشاريع التي يمكن الوصول إليها فقط من قبل المواطنين الذين تم التحقق من هوياتهم.", "app.containers.UsersEditPage.verifiedIdentityTitle": "أنت مواطن تم التحقق من هويته", "app.containers.UsersEditPage.verifyNow": "تحقق الآن", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "تنزيل ردودكم (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {لا يحب} one {1 إعجاب} other {# الإعجابات}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "المنشور الذي نُشر به هذا التعليق: ", "app.containers.UsersShowPage.areas": "المناطق", "app.containers.UsersShowPage.commentsWithCount": "التعليقات ({commentsCount})", "app.containers.UsersShowPage.editProfile": "تعديل ملفي الشخصي", "app.containers.UsersShowPage.emptyInfoText": "أنت لا تتابع أي عناصر من الفلتر المحدد أعلاه.", "app.containers.UsersShowPage.eventsWithCount": "أحداث ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "متابعة ({followingCount})", "app.containers.UsersShowPage.inputs": "المدخلات", "app.containers.UsersShowPage.invisibleTitlePostsList": "جميع المنشورات التي قدّمها هذا المُشارك ", "app.containers.UsersShowPage.invisibleTitleUserComments": "كل التعليقات التي نُشرت بواسطة هذا المستخدم", "app.containers.UsersShowPage.loadMore": "تحميل المزيد", "app.containers.UsersShowPage.loadMoreComments": "تحميل المزيد من التعليقات", "app.containers.UsersShowPage.loadingComments": "تحميل التعليقات جارٍ...", "app.containers.UsersShowPage.loadingEvents": "جارٍ تحميل الأحداث ...", "app.containers.UsersShowPage.memberSince": "عضو منذ {date}", "app.containers.UsersShowPage.metaTitle1": "الصفحة الشخصية لـ {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "لم يقم هذا الشخص بنشر أي تعليقات بعد.", "app.containers.UsersShowPage.noCommentsForYou": "لا توجد تعليقات هنا بعد.", "app.containers.UsersShowPage.noEventsForUser": "لم تحضر أي أحداث حتى الآن.", "app.containers.UsersShowPage.postsWithCount": "المنشورات ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "مجلدات المشروع", "app.containers.UsersShowPage.projects": "المشاريع", "app.containers.UsersShowPage.proposals": "اقتراحات", "app.containers.UsersShowPage.seePost": "اعرض المنشور", "app.containers.UsersShowPage.surveyResponses": "الردود ({responses})", "app.containers.UsersShowPage.topics": "المواضيع", "app.containers.UsersShowPage.tryAgain": "حدث خطأ ما، يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.UsersShowPage.userShowPageMetaDescription": "هذه هي صفحة ملف المُستخدم {firstName} {lastName}  على منصة {orgName} للمُشاركة عبر الإنترنت. إليك لمحة عامة عن كل منشوراتهم. ", "app.containers.VoteControl.close": "إغلاق", "app.containers.VoteControl.voteErrorTitle": "حد<PERSON> خطأ ما", "app.containers.admin.ContentBuilder.default": "تقصير", "app.containers.admin.ContentBuilder.imageTextCards": "بطاقات الصور والنصوص", "app.containers.admin.ContentBuilder.infoWithAccordions": "معلومات و أكورديون", "app.containers.admin.ContentBuilder.oneColumnLayout": "عمو<PERSON> واحد", "app.containers.admin.ContentBuilder.projectDescription": "وصف المشروع", "app.containers.app.navbar.admin": "إدارة المنصة", "app.containers.app.navbar.allProjects": "جميع المشاريع", "app.containers.app.navbar.ariaLabel": "أساسي", "app.containers.app.navbar.closeMobileNavMenu": "إغلاق قائمة التنقل عبر الجوال", "app.containers.app.navbar.editProfile": "إعداداتي", "app.containers.app.navbar.fullMobileNavigation": "جوال بالحجم الكامل", "app.containers.app.navbar.logIn": "تسجيل الدخول", "app.containers.app.navbar.logoImgAltText": "صفحة {orgName} الرئيسية", "app.containers.app.navbar.myProfile": "نشاطي", "app.containers.app.navbar.search": "ب<PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "عرض القائمة الكاملة", "app.containers.app.navbar.signOut": "تسجيل الخروج", "app.containers.eventspage.errorWhenFetchingEvents": "حدث خطأ أثناء تحميل الأحداث. يرجى إعادة تحميل الصفحة.", "app.containers.eventspage.events": "الأحداث", "app.containers.eventspage.eventsPageDescription": "اعرض كل الأحداث المنشورة في منصة {orgName}.", "app.containers.eventspage.eventsPageTitle1": "أحداث | {orgName}", "app.containers.eventspage.filterDropdownTitle": "المشاريع", "app.containers.eventspage.noPastEvents": "لا توجد أي أحداث سابقة لعرضها", "app.containers.eventspage.noUpcomingOrOngoingEvents": "لم تتم جدولة أي أحداث قادمة أو جارية حاليًا.", "app.containers.eventspage.pastEvents": "أحداث سابقة", "app.containers.eventspage.upcomingAndOngoingEvents": "أحدا<PERSON> قادمة وجارية", "app.containers.footer.accessibility-statement": "بيان إمكانية الوصول", "app.containers.footer.ariaLabel": "احتياطي", "app.containers.footer.cookie-policy": "سياسة ملفات تعريف الارتباط", "app.containers.footer.cookieSettings": "إعدادات ملفات تعريف الارتباط", "app.containers.footer.feedbackEmptyError": "لا يمكن أن يكون حقل الملاحظات فارغًا.", "app.containers.footer.poweredBy": "مشغّل بواسطة", "app.containers.footer.privacy-policy": "سياسة الخصوصية", "app.containers.footer.siteMap": "خريطة الموقع", "app.containers.footer.terms-and-conditions": "الشروط والأحكام", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "يلغي", "app.containers.ideaHeading.confirmLeaveFormButtonText": "نعم أريد الرحيل", "app.containers.ideaHeading.editForm": "تعديل النموذج", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "هل أنت متأكد أنك تريد المغادرة؟", "app.containers.ideaHeading.leaveIdeaForm": "نموذج ترك الفكرة", "app.containers.ideaHeading.leaveIdeaText": "لن يتم حفظ ردودك.", "app.containers.landing.cityProjects": "المشاريع", "app.containers.landing.completeProfile": "أكمل ملفك التعريفي", "app.containers.landing.completeYourProfile": "مرحبًا {firstName}، حان الوقت لإكمال ملفك التعريفي.", "app.containers.landing.createAccount": "التسجيل", "app.containers.landing.defaultSignedInMessage": "{orgName} تستمع إليك. حان دورك لجعل صوتك مسموعًا!", "app.containers.landing.doItLater": "سأقوم بالأمر لاحقًا", "app.containers.landing.new": "جديد", "app.containers.landing.subtitleCity": "أهلًا بك في منصة مشاركة {orgName}", "app.containers.landing.titleCity": "دعنا نشكّل مستقبل {orgName} معًا", "app.containers.landing.twitterMessage": "صوّت لـ {ideaTitle} على", "app.containers.landing.upcomingEventsWidgetTitle": "أحدا<PERSON> قادمة وجارية", "app.containers.landing.userDeletedSubtitle": "يمكنك إنشاء حساب جديد في أي وقت، أو {contactLink} لإعلامنا بما يمكننا تحسينه.", "app.containers.landing.userDeletedSubtitleLinkText": "أرسل رسالة", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "تم حذف حسابك.", "app.containers.landing.userDeletionFailed": "حد<PERSON> خطأ أثناء حذف حسابك، لقد أُبلغنا بالمشكلة ونعمل جاهدين على حلها. يُرجى المحاولة مجددًا في وقت لاحق.", "app.containers.landing.verifyNow": "تحقق الآن", "app.containers.landing.verifyYourIdentity": "فلتصبح مواطنًا تم التحقق من هويته", "app.containers.landing.viewAllEventsText": "عرض كل الأحداث", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "العودة إلى المجلد", "app.errors.after_end_at": "تاريخ البدء يتلو تاريخ الانتهاء", "app.errors.avatar_carrierwave_download_error": "لا يمكن تحميل ملف الصورة الرمزية.", "app.errors.avatar_carrierwave_integrity_error": "ملف الصورة الرمزية ليس من النوع المسموح به.", "app.errors.avatar_carrierwave_processing_error": "تعذرت معالجة الصورة الرمزية.", "app.errors.avatar_extension_blacklist_error": "امتداد ملف الصورة الرمزية غير مسموح به. الامتدادات المسموح بها هي: jpg و jpeg و gif و png.", "app.errors.avatar_extension_whitelist_error": "امتداد ملف الصورة الرمزية غير مسموح به. الامتدادات المسموح بها هي: jpg و jpeg و gif و png.", "app.errors.banner_cta_button_multiloc_blank": "أدخل نصًا للزر.", "app.errors.banner_cta_button_url_blank": "أدخل رابطًا.", "app.errors.banner_cta_button_url_url": "أدخل رابطًا صالحًا. تأكد من بدء الرابط بالمقطع 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "أدخل نصًا للزر.", "app.errors.banner_cta_signed_in_url_blank": "أدخل رابطًا.", "app.errors.banner_cta_signed_in_url_url": "أدخل رابطًا صالحًا. تأكد من بدء الرابط بالمقطع 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "أدخل نصًا للزر.", "app.errors.banner_cta_signed_out_url_blank": "أدخل رابطًا.", "app.errors.banner_cta_signed_out_url_url": "أدخل رابطًا صالحًا. تأكد من بدء الرابط بالمقطع 'https://'.", "app.errors.base_includes_banned_words": "ربما استخدمتَ كلمةً أو أكثر تُعتبر بذيئة. يُرجى تعديل نصّك لإزالة أيّ بذيئة قد تكون موجودة.", "app.errors.body_multiloc_includes_banned_words": "يحتوي الوصف على كلمات تعتبر غير مناسبة.", "app.errors.bulk_import_idea_not_valid": "الفكرة الناتجة غير صالحة: {value}.", "app.errors.bulk_import_image_url_not_valid": "لا يمكن تنزيل أي صورة من {value}. تأكد من أن عنوان URL صالح وينتهي بامتداد ملف مثل .png أو .jpg. تحدث هذه المشكلة في الصف ذو المعرف {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "موقع الفكرة مع الإحداثيات المفقودة في {value}. تحدث هذه المشكلة في الصف ذو المعرف {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "موقع الفكرة بإحداثيات غير رقمية في {value}. تحدث هذه المشكلة في الصف ذو المعرف {row}.", "app.errors.bulk_import_malformed_pdf": "يبدو أن ملف PDF الذي تم تحميله مشوه. حاول تصدير ملف PDF مرة أخرى من المصدر الخاص بك ثم قم بتحميله مرة أخرى.", "app.errors.bulk_import_maximum_ideas_exceeded": "تم تجاوز الحد الأقصى {value} من الأفكار.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "تم تجاوز الحد الأقصى {value} من الصفحات في ملف PDF.", "app.errors.bulk_import_not_enough_pdf_pages": "لا يحتوي ملف PDF الذي تم تحميله على صفحات كافية - يجب أن يحتوي على الأقل على نفس عدد الصفحات الموجودة في القالب الذي تم تنزيله.", "app.errors.bulk_import_publication_date_invalid_format": "الفكرة ذات تنسيق تاريخ النشر غير صالح \"{value}\". الرجاء استخدام التنسيق \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "تحتوي هذه المرحلة على {ideasCount, plural, one {فكرة واحدة} other {{ideasCount} أفكار}} وطريقة المشاركة التي تحاول تغييرها لا تدعم الأفكار. يُرجى إزالة {ideasCount, plural, one {الفكرة} other {الأفكار}} من المرحلة والمحاولة مجددًا.", "app.errors.cant_change_after_first_response": "لم يعُد بإمكانك تغيير هذا، لأن بعض المستخدمين قد استجابوا بالفعل", "app.errors.category_name_taken": "توجد فئة بهذا الاسم فعلاً", "app.errors.confirmation_code_expired": "Code expired. Please request a new code.", "app.errors.confirmation_code_invalid": "رمز التأكيد غير صالح. يرجى التحقق من بريدك الإلكتروني بحثًا عن الرمز الصحيح أو تجربة ميزة \"إرسال رمز جديد\"", "app.errors.confirmation_code_too_many_resets": "لقد أعدت إرسال رمز التأكيد مرات أكثر مما ينبغي. يرجى التواصل معنا لتلقي رمز دعوة بدلاً من ذلك.", "app.errors.confirmation_code_too_many_retries": "لقد أجريت محاولات أكثر مما ينبغي. يرجى طلب رمز جديد أو محاولة تغيير عنوان بريدك الإلكتروني.", "app.errors.email_already_active": "عنوان البريد الإلكتروني {value} والموجود في الصف {row} ينتمي بالفعل إلى مستخدم مسجّل", "app.errors.email_already_invited": "عنوان البريد الإلكتروني {value} والموجود في الصف {row} تمت دعوته بالفعل", "app.errors.email_blank": "لا يمكن أن يكون هذا فارغًا", "app.errors.email_domain_blacklisted": "يُرجى استخدام نطاق بريد إلكتروني مختلف للتسجيل.", "app.errors.email_invalid": "يُرجى استخدام عنوان بريد إلكتروني صالح.", "app.errors.email_taken": "يوجد حساب بهذا البريد الإلكتروني بالفعل. يمكنك تسجيل الدخول بدلًا من ذلك.", "app.errors.email_taken_by_invite": "{value} مأخوذ بالفعل بواسطة دعوة قيد الانتظار. تحقق من البريد المزعج لديك أو تواصل مع {supportEmail} للمساعدة.", "app.errors.emails_duplicate": "تم العثور على قيمة مكررة واحدة أو أكثر لعنوان البريد الإلكتروني {value} في الصف/ الصفوف التالية: {rows}", "app.errors.extension_whitelist_error": "تنسيق الملف الذي حاولت تحميله غير مدعوم.", "app.errors.file_extension_whitelist_error": "تنسيق الملف الذي حاولت تحميله غير مدعوم.", "app.errors.first_name_blank": "لا يمكن أن يكون هذا فارغًا", "app.errors.generics.blank": "لا يمكن أن يكون هذا فارغًا.", "app.errors.generics.invalid": "لا تبدو هذه كقيمة صالحة", "app.errors.generics.taken": "هذا البريد الإلكتروني موجود بالفعل. هناك حساب مرتبط به.", "app.errors.generics.unsupported_locales": "هذا الحقل لا يدعم الإعدادات المحلية الحالية.", "app.errors.group_ids_unauthorized_choice_moderator": "بصفتك مديرًا للمشروع، يمكنك فقط إرسال رسائل إلكترونية للأشخاص القادرين على الوصول إلى مشروعك/ مشاريعك", "app.errors.has_other_overlapping_phases": "لا يمكن للمشاريع امتلاك مراحل متداخلة.", "app.errors.invalid_email": "البريد الإلكتروني {value} والموجود في الصف {row} ليس عنوان بريد إلكتروني صالح", "app.errors.invalid_row": "حد<PERSON> خطأ غير معروف أثناء محاولة معالجة الصف {row}", "app.errors.is_not_timeline_project": "المشروع الحالي لا يدعم المراحل.", "app.errors.key_invalid": "يمكن للمفتاح أن يحتوي فقط على أحرف وأرقام وشَرطة سفلية (_)", "app.errors.last_name_blank": "لا يمكن أن يكون هذا فارغًا", "app.errors.locale_blank": "يُرجى اختيار لغة", "app.errors.locale_inclusion": "يُرجى اختيار لغة مدعومة", "app.errors.malformed_admin_value": "قيمة المسؤول {value} والموجودة في الصف {row} غير صالحة", "app.errors.malformed_groups_value": "المجموعة {value} التي تم العثور عليها في الصف {row} ليست مجموعة صالحة", "app.errors.max_invites_limit_exceeded1": "يتجا<PERSON>ز عدد الدعوات حد 1000.", "app.errors.maximum_attendees_greater_than1": "يجب أن يكون الحد الأقصى لعدد المسجلين أكبر من 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "يجب أن يكون الحد الأقصى لعدد المسجلين أكبر من أو يساوي العدد الحالي للمسجلين.", "app.errors.no_invites_specified": "تعذّر العثور على أي عنوان بريد إلكتروني.", "app.errors.no_recipients": "لا يمكن إرسال الحملة لأنه لا يوجد مستلمون. المجموعة التي ترسل إليها إما فارغة، أو لم يوافق أحد على تلقي رسائل البريد الإلكتروني.", "app.errors.number_invalid": "الرجاء إدخال رقم صالح.", "app.errors.password_blank": "لا يمكن أن يكون هذا فارغًا", "app.errors.password_invalid": "يرجى التحقق من كلمة مرورك الحالية مجددًا.", "app.errors.password_too_short": "يجب ألّا يقل طول كلمة المرور عن 8 حروف", "app.errors.resending_code_failed": "حد<PERSON> خطأ ما أثناء إرسال رمز التأكيد.", "app.errors.slug_taken": "عنوان URL للمشروع هذا موجود فعلاً. يرجى تغيير معرِّف صفحة المشروع إلى شيء آخر.", "app.errors.tag_name_taken": "توجد بالفعل علامة بهذا الاسم", "app.errors.title_multiloc_blank": "لا يمكن أن يكون العنوان فارغًا.", "app.errors.title_multiloc_includes_banned_words": "يحتوي العنوان على كلمات تعتبر غير مناسبة.", "app.errors.token_invalid": "يمكن استخدام روابط إعادة تعيين كلمة المرور مرة واحدة فقط وتكون صالحة لمدة ساعة واحدة بعد إرسالها. {passwordResetLink}.", "app.errors.too_common": "يمكن تخمين كلمة المرور هذه بسهولة. اختر رجاءً كلمة مرور أقوى.", "app.errors.too_long": "يُرجى اختيار كلمة مرور أقصر (لا تتجاوز الـ 72 حرفًا)", "app.errors.too_short": "يُرجى اختيار كلمة مرور بطول لا يقل عن 8 أحرف", "app.errors.uncaught_error": "حد<PERSON> خطأ غير معروف.", "app.errors.unknown_group": "المجموعة {value} التي تم العثور عليها في الصف {row} ليست مجموعة معروفة", "app.errors.unknown_locale": "اللغة {value} التي تم العثور عليها في الصف {row} هي لغة لم تتم تهيئتها", "app.errors.unparseable_excel": "تعذرت معالجة ملف إكسل المحدد.", "app.errors.url": "أدخل رابطًا صالحًا. تأكد من أن الرابط يبدأ بـ https://", "app.errors.verification_taken": "لا يمكن إكمال عملية التحقق لأنه تم التحقق من حساب آخر باستخدام نفس التفاصيل.", "app.errors.view_name_taken": "توجد طريقة عرض بهذا الاسم فعلاً", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "يو<PERSON><PERSON> محتوى غير لائق تم اكتشافه تلقائيًا في منشور أو تعليق", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "تسجيل الدخول باستخدام StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "تسجيل الاشتراك باستخدام StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "أنشئ حساب Stadt Wien الآن واستخدم حسابًا واحدًا لتسجيل دخول الكثير من خدمات Vienna الرقمية.", "app.modules.id_cow.cancel": "إلغاء", "app.modules.id_cow.emptyFieldError": "لا يمكن أن يكون هذا الحقل فارغًا.", "app.modules.id_cow.helpAltText": "يعرض مكان العثور على الرقم التسلسلي الفريد في بطاقة هوية", "app.modules.id_cow.invalidIdSerialError": "الرقم التسلسلي غير صالح", "app.modules.id_cow.invalidRunError": "إدخال غير صالح", "app.modules.id_cow.noMatchFormError": "لم يتم العثور على توافق", "app.modules.id_cow.notEntitledFormError": "غير مخوّل.", "app.modules.id_cow.showCOWHelp": "أين يمكنني العثور على الرقم التسلسلي الخاص بي؟", "app.modules.id_cow.somethingWentWrongError": "لم نتمكن من التحقق من هويتك نظرًا لحدوث خطأ ما", "app.modules.id_cow.submit": "إرسال", "app.modules.id_cow.takenFormError": "مأخوذ بالفعل.", "app.modules.id_cow.verifyCow": "تحقق باستخدام COW", "app.modules.id_franceconnect.verificationButtonAltText": "تحقق باستخدام FranceConnect", "app.modules.id_gent_rrn.cancel": "إلغاء", "app.modules.id_gent_rrn.emptyFieldError": "لا يمكن أن يكون هذا الحقل فارغًا.", "app.modules.id_gent_rrn.gentRrnHelp": "يظهر رقم تأمينك الاجتماعي في الوجه الخلفي لبطاقة هويتك الرقمية", "app.modules.id_gent_rrn.invalidRrnError": "رقم التأمين الاجتماعي غير صالح", "app.modules.id_gent_rrn.noMatchFormError": "تعذر علينا العثور على معلومات داعمة بشأن رقم تأمينك الاجتماعي", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "يتعذر علينا التحقق من هويتك؛ لأنك تقيم خارج جنت", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "يتعذر علينا التحقق من هويتك؛ لأن سنك أصغر من 14 عامًا", "app.modules.id_gent_rrn.rrnLabel": "رقم التأمين الاجتماعي", "app.modules.id_gent_rrn.rrnTooltip": "نطلب رقم تأمينك الاجتماعي للتحقق من أنك أحد مواطني جنت وسنك أكبر من 14 عامًا.", "app.modules.id_gent_rrn.showGentRrnHelp": "أين يمكنني العثور على الرقم التسلسلي الخاص بي؟", "app.modules.id_gent_rrn.somethingWentWrongError": "لم نتمكن من التحقق من هويتك نظرًا لحدوث خطأ ما", "app.modules.id_gent_rrn.submit": "إرسال", "app.modules.id_gent_rrn.takenFormError": "تم استخدام رقم تأمينك الاجتماعي فعلاً للتحقق من حساب آخر", "app.modules.id_gent_rrn.verifyGentRrn": "التحقق باستخدام GentRrn", "app.modules.id_id_card_lookup.cancel": "إلغاء", "app.modules.id_id_card_lookup.emptyFieldError": "لا يمكن أن يكون هذا الحقل فارغًا.", "app.modules.id_id_card_lookup.helpAltText": "شرح بطاقة الهوية", "app.modules.id_id_card_lookup.invalidCardIdError": "هذا المُعرف غير صالح.", "app.modules.id_id_card_lookup.noMatchFormError": "لم يتم العثور على توافق", "app.modules.id_id_card_lookup.showHelp": "أين يمكنني العثور على الرقم التسلسلي الخاص بي؟", "app.modules.id_id_card_lookup.somethingWentWrongError": "لم نتمكن من التحقق من هويتك نظرًا لحدوث خطأ ما", "app.modules.id_id_card_lookup.submit": "إرسال", "app.modules.id_id_card_lookup.takenFormError": "مأخوذ بالفعل.", "app.modules.id_oostende_rrn.cancel": "إلغاء", "app.modules.id_oostende_rrn.emptyFieldError": "لا يمكن أن يكون هذا الحقل فارغًا.", "app.modules.id_oostende_rrn.invalidRrnError": "رقم التأمين الاجتماعي غير صالح", "app.modules.id_oostende_rrn.noMatchFormError": "تعذر علينا العثور على معلومات داعمة بشأن رقم تأمينك الاجتماعي", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "يتعذر علينا التحقق من هويتك؛ لأنك تقيم خارج أوستند", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "يتعذر علينا التحقق من هويتك؛ لأن سنك أصغر من 14 عامًا", "app.modules.id_oostende_rrn.oostendeRrnHelp": "يظهر رقم تأمينك الاجتماعي في الوجه الخلفي لبطاقة هويتك الرقمية", "app.modules.id_oostende_rrn.rrnLabel": "رقم التأمين الاجتماعي", "app.modules.id_oostende_rrn.rrnTooltip": "نطلب رقم تأمينك الاجتماعي للتحقق من أنك أحد مواطني أوستند وسنك أكبر من 14 عامًا.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "أين يمكنني العثور على رقم تأميني الاجتماعي؟", "app.modules.id_oostende_rrn.somethingWentWrongError": "لم نتمكن من التحقق من هويتك نظرًا لحدوث خطأ ما", "app.modules.id_oostende_rrn.submit": "إرسال", "app.modules.id_oostende_rrn.takenFormError": "تم استخدام رقم تأمينك الاجتماعي فعلاً للتحقق من حساب آخر", "app.modules.id_oostende_rrn.verifyOostendeRrn": "التحقق باستخدام رقم التأمين الاجتماعي", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "لقد تلقيت حقوق مسؤول للوصول إلى المجلد \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "مشاركة", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "اعرض المشاريع عبر {folderUrl} ليصبح صوتك مسموعًا!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{اسم المشروع} | من منصة المشاركة لـ {اسم المنظمة}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{اسم المشروع} | من منصة المشاركة لـ {اسم المنظمة}", "app.sessionRecording.accept": "نعم أقبل", "app.sessionRecording.modalDescription1": "من أجل فهم مستخدمينا بشكل أفضل، نطلب بشكل عشوائي من نسبة صغيرة من الزوار تتبع جلسة التصفح الخاصة بهم بالتفصيل.", "app.sessionRecording.modalDescription2": "الغرض الوحيد من البيانات المسجلة هو تحسين الموقع. لن تتم مشاركة أي من بياناتك مع طرف ثالث. سيتم تصفية أي معلومات حساسة تقوم بإدخالها.", "app.sessionRecording.modalDescription3": "هل تقبل؟", "app.sessionRecording.modalDescriptionFaq": "الأسئلة الشائعة هنا.", "app.sessionRecording.modalTitle": "ساعدونا في تحسين هذا الموقع", "app.sessionRecording.reject": "لا، أنا أرفض", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "إجراء تمرين على تخصيص ميزانية", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "جمع الملاحظات على وثيقة", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "إنشاء استبيان داخل المنصة", "app.utils.AdminPage.ProjectEdit.createPoll": "إنشاء استطلاع", "app.utils.AdminPage.ProjectEdit.createSurveyText": "تضمين استبيان خارجي", "app.utils.AdminPage.ProjectEdit.findVolunteers": "بح<PERSON> عن متطوعين", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "تجميع المدخلات والملاحظات", "app.utils.AdminPage.ProjectEdit.shareInformation": "إتاحة المعلومات للمشاركة", "app.utils.FormattedCurrency.credits": "رمز/رموز ائتمان", "app.utils.FormattedCurrency.tokens": "رمز/رموز مميزة", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# رصيد} one {# رصيد} other {# رصيد}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# رموز} one {# رمز} other {# رموز}}", "app.utils.IdeaCards.mostDiscussed": "الأكثر مناقشة", "app.utils.IdeaCards.mostReacted": "معظم ردود الفعل", "app.utils.IdeaCards.newest": "الأحدث", "app.utils.IdeaCards.oldest": "الأقدم", "app.utils.IdeaCards.random": "عشوائي", "app.utils.IdeaCards.trending": "الشائع", "app.utils.IdeasNewPage.contributionFormTitle": "أضف مُساهمة جديدة", "app.utils.IdeasNewPage.ideaFormTitle": "أضف فكرة جديدة", "app.utils.IdeasNewPage.initiativeFormTitle": "إضافة مبادرة جديدة", "app.utils.IdeasNewPage.issueFormTitle1": "أض<PERSON> تعليق جديد", "app.utils.IdeasNewPage.optionFormTitle": "أضف خياراً جديداً", "app.utils.IdeasNewPage.petitionFormTitle": "إضافة عريضة جديدة", "app.utils.IdeasNewPage.projectFormTitle": "أضف مشروعاً جديداً ", "app.utils.IdeasNewPage.proposalFormTitle": "إضافة اقتراح جديد", "app.utils.IdeasNewPage.questionFormTitle": "أضف سؤالاً جديداً ", "app.utils.IdeasNewPage.surveyTitle": "الاستبيان", "app.utils.IdeasNewPage.viewYourComment": "عرض تعليقك", "app.utils.IdeasNewPage.viewYourContribution": "عرض مساهمتك", "app.utils.IdeasNewPage.viewYourIdea": "عرض فكرتك", "app.utils.IdeasNewPage.viewYourInitiative": "عرض مبادرتك", "app.utils.IdeasNewPage.viewYourInput": "عرض مدخلاتك", "app.utils.IdeasNewPage.viewYourIssue": "عرض مشكلتك", "app.utils.IdeasNewPage.viewYourOption": "عرض خيارك", "app.utils.IdeasNewPage.viewYourPetition": "عرض عريضتك", "app.utils.IdeasNewPage.viewYourProject": "عرض مشروعك", "app.utils.IdeasNewPage.viewYourProposal": "عرض اقتراحك", "app.utils.IdeasNewPage.viewYourQuestion": "عرض سؤالك", "app.utils.Projects.sendSubmission": "إرسال معرف الإرسال إلى بريدي الإلكتروني", "app.utils.Projects.sendSurveySubmission": "إرسال معرف إرسال الاستطلاع إلى بريدي الإلكتروني", "app.utils.Projects.surveySubmission": "إرسال الاستطلاع", "app.utils.Projects.yourResponseHasTheFollowingId": "إجابتك تحتوي على المعرف التالي: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "إذا قررت لاحقًا أنك تريد إزالة ردك، فيرجى الاتصال بنا باستخدام المعرف الفريد التالي:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "يجب عليك إكمال ملفك الشخصي لحضور هذا الحدث.", "app.utils.actionDescriptors.attendingEventNotInGroup": "أنت لا تستوفي المتطلبات اللازمة لحضور هذا الحدث.", "app.utils.actionDescriptors.attendingEventNotPermitted": "لا يسمح لك بحضور هذا الحدث.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "يجب عليك تسجيل الدخول أو التسجيل لحضور هذا الحدث.", "app.utils.actionDescriptors.attendingEventNotVerified": "يجب عليك التحقق من حسابك قبل أن تتمكن من حضور هذا الحدث.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "يجب عليك إكمال ملفك الشخصي للتطوع.", "app.utils.actionDescriptors.volunteeringNotInGroup": "أنت لا تستوفي متطلبات التطوع.", "app.utils.actionDescriptors.volunteeringNotPermitted": "لا يسمح لك بالتطوع.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "يجب عليك تسجيل الدخول أو التسجيل للتطوع.", "app.utils.actionDescriptors.volunteeringNotVerified": "يجب عليك التحقق من حسابك قبل أن تتمكن من التطوع.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "من فضلك {completeRegistrationLink} للتطوع.", "app.utils.errors.api_error_default.in": "ليس صحيحًا", "app.utils.errors.default.ajv_error_birthyear_required": "ي<PERSON><PERSON>ى إدخال عام الميلاد", "app.utils.errors.default.ajv_error_date_any": "ير<PERSON>ى إدخال تاريخ صالح", "app.utils.errors.default.ajv_error_domicile_required": "ير<PERSON>ى إدخال محل الإقامة", "app.utils.errors.default.ajv_error_gender_required": "ير<PERSON>ى إدخال نوع الجنس", "app.utils.errors.default.ajv_error_invalid": "غير صالح", "app.utils.errors.default.ajv_error_maxItems": "لا يجوز تضمين أكثر من {limit, plural, one {# عنصر} other {# عنصر/عناصر}}", "app.utils.errors.default.ajv_error_minItems": "يجب تضمين {limit, plural, one {# عنصر} other {# عنصر/عناصر}} على الأقل", "app.utils.errors.default.ajv_error_number_any": "ير<PERSON>ى إدخال رقم صالح", "app.utils.errors.default.ajv_error_politician_required": "يرجى تحديد إذا كنت سياسيًا", "app.utils.errors.default.ajv_error_required3": "الحقل مطلوب: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "لا يمكن تركه فارغًا", "app.utils.errors.default.api_error_accepted": "يجب القبول", "app.utils.errors.default.api_error_blank": "لا يمكن تركه فارغًا", "app.utils.errors.default.api_error_confirmation": "غير مطابق", "app.utils.errors.default.api_error_empty": "لا يمكن تركه خاليًا", "app.utils.errors.default.api_error_equal_to": "ليس صحيحًا", "app.utils.errors.default.api_error_even": "يجب أن يكون زوجيًا", "app.utils.errors.default.api_error_exclusion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_greater_than": "أصغر مما ينبغي", "app.utils.errors.default.api_error_greater_than_or_equal_to": "أصغر مما ينبغي", "app.utils.errors.default.api_error_inclusion": "غير مدرج في القائمة", "app.utils.errors.default.api_error_invalid": "غير صالح", "app.utils.errors.default.api_error_less_than": "أكبر مما ينبغي", "app.utils.errors.default.api_error_less_than_or_equal_to": "أكبر مما ينبغي", "app.utils.errors.default.api_error_not_a_number": "ليس رقمًا", "app.utils.errors.default.api_error_not_an_integer": "يجب أن يكون عددًا صحيحًا", "app.utils.errors.default.api_error_other_than": "ليس صحيحًا", "app.utils.errors.default.api_error_present": "يجب تركه فارغًا", "app.utils.errors.default.api_error_too_long": "أطول مما ينبغي", "app.utils.errors.default.api_error_too_short": "أقصر مما ينبغي", "app.utils.errors.default.api_error_wrong_length": "طول غير صحيح", "app.utils.errors.defaultapi_error_.odd": "يجب أن يكون فرديًا", "app.utils.notInGroup": "أنت لا تفي بمتطلبات المشاركة.", "app.utils.participationMethod.onSurveySubmission": "شكرًا لك. تم تسلم استجابتك.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "التصويت لم يعد متاحًا، حيث أن هذه المرحلة لم تعد نشطة.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "أنت لا تستوفي متطلبات التصويت.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "لا يسمح لك بالتصويت.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "يجب عليك تسجيل الدخول أو التسجيل للتصويت.", "app.utils.participationMethodConfig.voting.votingNotVerified": "يجب عليك التحقق من حسابك قبل أن تتمكن من التصويت.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>تم إغلاق باب تقديم الميزانيات في {endDate}.</b> كان لدى المشاركين إجمالي يبلغ <b>{maxBudget} لكل منهم لتوزيعه بين {optionCount} خيارات.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "تم تقديم الميزانية", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "الميزانية المقدمة 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "أنت لا تستوفي متطلبات تعيين الميزانيات.", "app.utils.votingMethodUtils.budgetingNotPermitted": "لا يسمح لك بتعيين الميزانيات.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "يجب عليك تسجيل الدخول أو التسجيل لتعيين الميزانيات.", "app.utils.votingMethodUtils.budgetingNotVerified": "يجب عليك التحقق من حسابك قبل أن تتمكن من تعيين الميزانيات.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>لن يتم احتساب ميزانيتك</b> حتى تنقر على \"إرسال\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "الح<PERSON> الأدنى للميزانية المطلوبة هو {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "بمجرد الانتهاء ، انقر فوق \"إرسال\" لإرسال ميزانيتك.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "حد<PERSON> الخيارات المفضلة لديك من خلال النقر على \"إضافة\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "لديك إجمالي <b>{maxBudget} لتوزيعها بين {optionCount} خيارات</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>تهانينا ، لقد تم تقديم ميزانيتك!</b> يمكنك التحقق من الخيارات أدناه في أي وقت أو تعديلها قبل <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>تهانينا، لقد تم تقديم ميزانيتك!</b> يمكنك التحقق من خياراتك أدناه في أي وقت.", "app.utils.votingMethodUtils.castYourVote": "أدلي بصوتك", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "يمكنك إضافة ما يصل إلى {maxVotes, plural, one {# رصيد} other {# رصيد}} لكل خيار.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "يمكنك إضافة ما يصل إلى {maxVotes, plural, one {# نقطة} other {# نقطة}} لكل خيار.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "يمكنك إضافة ما يصل إلى {maxVotes, plural, one {# رمز} other {# رموز}} لكل خيار.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "يمكنك إضافة ما يصل إلى {maxVotes, plural, one {# تصويت} other {# تصويت}} لكل خيار.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "بمجرد الانتهاء ، انقر فوق \"إرسال\" للإدلاء بصوتك.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "حد<PERSON> خياراتك المفضلة من خلال النقر على \"تحديد\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "لديك إجمالي <b>{totalVotes, plural, one {# رصيد} other {# رصيد}} لتوزيعه بين {optionCount, plural, one {# خيار} other {# خيارات}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "لديك إجمالي <b>{totalVotes, plural, one {# نقطة} other {# نقاط}} لتوزيعها بين {optionCount, plural, one {# خيار} other {# خيارات}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "لديك إجمالي <b>{totalVotes, plural, one {# رمز} other {# رموز}} لتوزيعها بين {optionCount, plural, one {# خيار} other {# خيارات}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "لديك إجمالي <b>{totalVotes, plural, one {# تصويت} other {# تصويتات}} لتوزيعها بين {optionCount, plural, one {# خيار} other {# خيارات}}</b>.", "app.utils.votingMethodUtils.finalResults": "النتائج النهائية", "app.utils.votingMethodUtils.finalTally": "الحصيلة النهائية", "app.utils.votingMethodUtils.howToParticipate": "كيفية المشاركة", "app.utils.votingMethodUtils.howToVote": "كيفية التصويت", "app.utils.votingMethodUtils.multipleVotingEnded1": "تم إغلاق التصويت في <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 رصيد} one {1 رصيد} other {# رصيد}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 نقطة} one {1 نقطة} other {# نقاط}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 رموز} one {1 رمز} other {# رموز}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 أصوات} one {1 صوت} other {# أصوات}}", "app.utils.votingMethodUtils.results": "نتائج", "app.utils.votingMethodUtils.singleVotingEnded": "أغلق التصويت يوم <b>{endDate}.</b> يمكن للمشاركين <b>لـ {maxVotes} خيارات.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "ح<PERSON><PERSON> خياراتك المفضلة من خلال النقر على \"تصويت\"", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "لديك <b>{totalVotes} أصوات</b> يمكنك تخصيصها للخيارات.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "بمجرد الانتهاء ، انقر فوق \"إرسال\" للإدلاء بصوتك.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "أغلق التصويت يوم <b>{endDate}.</b> يمكن للمشاركين التصويت لـ <b>خيار واحد.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "ح<PERSON><PERSON> خيارك المفضل من خلال النقر على \"تصويت\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "لديك <b>1 صوت</b> يمكنك تخصيصه لأحد الخيارات.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "أغلق التصويت يوم <b>{endDate}.</b> يمكن للمشاركين التصويت <b>من الخيارات كما يرغبون.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "يمكنك التصويت لأكبر عدد تريده من الخيارات.", "app.utils.votingMethodUtils.submitYourBudget": "أرسل ميزانيتك", "app.utils.votingMethodUtils.submittedBudgetCountText2": "قام الشخص بتقديم ميزانيته عبر الإنترنت", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "قدم الناس ميزانياتهم عبر الإنترنت", "app.utils.votingMethodUtils.submittedVoteCountText2": "قام الشخص بتقديم تصويته عبر الإنترنت", "app.utils.votingMethodUtils.submittedVotesCountText2": "أرسل الناس أصواتهم عبر الإنترنت", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "تم إرسال التصويت 🎉", "app.utils.votingMethodUtils.votesCast": "الأصوات المدلى بها", "app.utils.votingMethodUtils.votingClosed": "التصويت مغلق", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>لن يتم احتساب صوتك</b> حتى تنقر على \"إرسال\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>مبروك، تم إرسال تصويتك!</b> يمكنك التحقق من إرسالك أو تعديله قبل <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>تهانينا، تم إرسال تصويتك!</b> يمكنك مراجعة أو تعديل تصويتك أدناه في أي وقت.", "components.UI.IdeaSelect.noIdeaAvailable": "لا توجد أفكار متاحة.", "components.UI.IdeaSelect.selectIdea": "اختر الفكرة", "containers.SiteMap.allProjects": "جميع المشاريع", "containers.SiteMap.customPageSection": "صفحات مخصصة", "containers.SiteMap.folderInfo": "المزيد من المعلومات", "containers.SiteMap.headSiteMapTitle": "خريطة الموقع | {orgName}", "containers.SiteMap.homeSection": "عام", "containers.SiteMap.pageContents": "مح<PERSON>وى الصفحة", "containers.SiteMap.profilePage": "صفحة ملفك التعريفي", "containers.SiteMap.profileSettings": "إعداداتك", "containers.SiteMap.projectEvents": "الأحداث", "containers.SiteMap.projectIdeas": "الأفكار", "containers.SiteMap.projectInfo": "معلومات", "containers.SiteMap.projectPoll": "الاستطلاع", "containers.SiteMap.projectSurvey": "الاستبيان", "containers.SiteMap.projectsArchived": "المشاريع المؤرشفة", "containers.SiteMap.projectsCurrent": "المشاريع الحالية", "containers.SiteMap.projectsDraft": "مسوّدات المشاريع", "containers.SiteMap.projectsSection": "مشاريع {orgName}", "containers.SiteMap.signInPage": "تسجيل الدخول", "containers.SiteMap.signUpPage": "التسجيل", "containers.SiteMap.siteMapDescription": "يمكنك من هذه الصفحة الانتقال إلى أي محتوى على المنصة.", "containers.SiteMap.siteMapTitle": "خريطة الموقع لمنصة مشاركة {orgName}", "containers.SiteMap.successStories": "قصص نجاح", "containers.SiteMap.timeline": "مراحل المشروع", "containers.SiteMap.userSpaceSection": "حسابك", "containers.SubscriptionEndedPage.accessDenied": "لم يعُد لديك حق الوصول", "containers.SubscriptionEndedPage.subscriptionEnded": "يبدو أنه لم يعُد بإمكانك الوصول إلى هذه الصفحة، منذ انتهاء اشتراكك في Go Vocal."}