{"EmailSettingsPage.emailSettings": "Ρυθμίσεις email", "EmailSettingsPage.initialUnsubscribeError": "Υπήρξε ένα πρόβλημα με τη διαγραφή από αυτή την εκστρατεία, παρακαλούμε προσπαθήστε ξανά.", "EmailSettingsPage.initialUnsubscribeLoading": "Το αίτημά σας επεξεργ<PERSON>ζεται, παρακαλούμε περιμένετε...", "EmailSettingsPage.initialUnsubscribeSuccess": "Διαγράψατε επιτυχώς την εγγραφή σας από την εκστρατεία {campaignTitle}.", "UI.FormComponents.optional": "προαιρετικό", "app.closeIconButton.a11y_buttonActionMessage": "Κλείσιμο", "app.components.Areas.areaUpdateError": "Προέκυψε σφάλμα κατά την αποθήκευση της περιοχής σας. Προσπαθήστε ξανά.", "app.components.Areas.followedArea": "Ακολούθησε περιοχή: {areaTitle}", "app.components.Areas.followedTopic": "Ακολούθησε το θέμα: {topicTitle}", "app.components.Areas.topicUpdateError": "Προέκυψε σφάλμα κατά την αποθήκευση του θέματός σας. Προσπαθήστε ξανά.", "app.components.Areas.unfollowedArea": "Μη ακολουθούμενη περιοχή: {areaTitle}", "app.components.Areas.unfollowedTopic": "Μη ακολουθούμενο θέμα: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Τιμή:", "app.components.AssignBudgetControl.add": "Προσθήκη", "app.components.AssignBudgetControl.added": "Προστέθηκε", "app.components.AssignMultipleVotesControl.addVote": "Προσθέστε ψήφο", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Έχετε διανείμει όλες τις πιστώσεις σας.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Έχετε διανείμει τον μέγιστο αριθμό πιστωτικών μονάδων για αυτή την επιλογή.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Έχετε διανείμει όλα τα σημεία σας.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Έχετε διανείμει τον μέγιστο αριθμό βαθμών για την επιλογή αυτή.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Έχετε διανείμει όλα τα κουπόνια σας.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Έχετε διανείμει τον μέγιστο αριθμό μαρκών για αυτή την επιλογή.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Έχετε διανείμει όλες τις ψήφους σας.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Έχετε διανείμει τον μέγιστο αριθμό ψήφων για αυτή την επιλογή.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(συμπεριλαμβανομένου 1 offline)} other {(συμπεριλαμβανομένου # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Η ψηφοφορία δεν είναι διαθέσιμη, δεδομένου ότι η φάση αυτή δεν είναι ενεργή.", "app.components.AssignMultipleVotesControl.removeVote": "Αφαίρεση ψήφου", "app.components.AssignMultipleVotesControl.select": "Επιλέξτε", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Έχετε ήδη υποβάλει την ψήφο σας. Για να την τροποποιήσετε, κάντε κλικ στο \"Τροποποίηση της υποβολής σας\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Έχετε ήδη υποβάλει την ψήφο σας. Για να την τροποποιήσετε, επιστρέψτε στη σελίδα του έργου και κάντε κλικ στο \"Τροποποίηση της υποβολής σας\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credit} other {credits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {σημείο} other {σημεία}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "Έχετε διανείμει όλες τις ψήφους σας.", "app.components.AssignVoteControl.phaseNotActive": "Η ψηφοφορία δεν είναι διαθέσιμη, δεδομένου ότι η φάση αυτή δεν είναι ενεργή.", "app.components.AssignVoteControl.select": "Επιλέξτε", "app.components.AssignVoteControl.selected2": "Επιλεγμένα", "app.components.AssignVoteControl.voteForAtLeastOne": "Ψηφίστε τουλάχιστον 1 επιλογή", "app.components.AssignVoteControl.votesSubmitted1": "Έχετε ήδη υποβάλει την ψήφο σας. Για να την τροποποιήσετε, κάντε κλικ στο \"Τροποποίηση της υποβολής σας\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Έχετε ήδη υποβάλει την ψήφο σας. Για να την τροποποιήσετε, επιστρέψτε στη σελίδα του έργου και κάντε κλικ στο \"Τροποποίηση της υποβολής σας\".", "app.components.AuthProviders.continue": "Συνέχεια", "app.components.AuthProviders.continueWithAzure": "Συνεχίστε με το {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Συνεχίστε με το Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Συνεχίστε με το Fake SSO", "app.components.AuthProviders.continueWithGoogle": "Συνεχίστε με το Google", "app.components.AuthProviders.continueWithHoplr": "Συνεχίστε με το Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Συνέχεια με ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Συνεχίστε με το {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Συνεχίστε με το MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Υπάρχει ήδη λογαριασμός με αυτή τη διεύθυνση email.{br}{br}Δεν μπορείτε να αποκτήσετε πρόσβαση στην πλατφόρμα μέσω του FranceConnect, καθώς τα προσωπικά στοιχεία δεν ταιριάζουν. Για να συνδεθείτε χρησιμοποιώντας το FranceConnect, θα πρέπει πρώτα να αλλάξετε το μικρό σας όνομα ή το επώνυμό σας σε αυτή την πλατφόρμα ώστε να ταιριάζει με τα επίσημα στοιχεία σας.{br}{br}Μπορείτε να συνδεθείτε όπως κάνετε συνήθως παρακάτω.", "app.components.AuthProviders.goToLogIn": "Έχετε ήδη λογαριασμό; {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Δεν έχετε λογαριασμό; {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Σύνδεση", "app.components.AuthProviders.logInWithEmail": "Σύνδεση με Email", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Πρέπει να έχετε το καθορισμένο ελάχιστο όριο ηλικίας ή μεγαλύτερο για να επαληθευτείτε.", "app.components.AuthProviders.signUp2": "Εγγραφείτε", "app.components.AuthProviders.signUpButtonAltText": "Εγγραφείτε με {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Εγγραφείτε με Email", "app.components.AuthProviders.verificationRequired": "Απαιτείτ<PERSON>ι επαλήθευση", "app.components.Author.a11yPostedBy": "Αναρτήθηκε από", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 συμμετέχων} other {{numberOfParticipants} συμμετέχοντες}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} χρήστες", "app.components.AvatarBubbles.participant": "συμμετέχων", "app.components.AvatarBubbles.participants1": "συμμετέχοντες", "app.components.Comments.cancel": "Ακύρωση", "app.components.Comments.commentingDisabledInCurrentPhase": "Ο σχολιασμός δεν είναι δυνατός στην τρέχουσα φάση.", "app.components.Comments.commentingDisabledInactiveProject": "Ο σχολιασμός δεν είναι δυνατός επειδή αυτό το έργο δεν είναι επί του παρόντος ενεργό.", "app.components.Comments.commentingDisabledProject": "Ο σχολιασμός σε αυτό το έργο είναι προς το παρόν απενεργοποιημένος.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} για να σχολιάσετε.", "app.components.Comments.commentingMaybeNotPermitted": "Παρακαλούμε {signInLink} για να δείτε τι ενέργειες μπορούν να γίνουν.", "app.components.Comments.inputsAssociatedWithProfile": "Από προεπιλογή, οι υποβολές σας θα συσχετίζονται με το προφίλ σας, εκ<PERSON><PERSON><PERSON> αν επιλέξετε αυτή την επιλογή.", "app.components.Comments.invisibleTitleComments": "Σχόλια", "app.components.Comments.leastRecent": "Λιγότερο πρόσφατη", "app.components.Comments.likeComment": "Όπως αυτό το σχόλιο", "app.components.Comments.mostLiked": "Οι περισσότερες αντιδράσεις", "app.components.Comments.mostRecent": "Το πιο πρόσφατο", "app.components.Comments.official": "Επίσημα", "app.components.Comments.postAnonymously": "Δημοσιεύστε ανώνυμα", "app.components.Comments.replyToComment": "Απάντηση στο σχόλιο", "app.components.Comments.reportAsSpam": "Αναφορά ως spam", "app.components.Comments.seeOriginal": "Δείτε το πρωτότυπο", "app.components.Comments.seeTranslation": "Δείτε τη μετάφραση", "app.components.Comments.yourComment": "Το σχόλιό σας", "app.components.CommonGroundResults.divisiveDescription": "Δηλώσεις στις οποίες οι άνθρωποι συμφωνούν και διαφωνούν εξίσου:", "app.components.CommonGroundResults.divisiveTitle": "Διχαστικό", "app.components.CommonGroundResults.majorityDescription": "Πάνω από το 60% ψήφισε με τον ένα ή τον άλλο τρόπο για τα ακόλουθα:", "app.components.CommonGroundResults.majorityTitle": "Πλειοψηφία", "app.components.CommonGroundResults.participantLabel": "συμμετέχων", "app.components.CommonGroundResults.participantsLabel1": "συμμετέχοντες", "app.components.CommonGroundResults.statementLabel": "δήλωση", "app.components.CommonGroundResults.statementsLabel1": "δηλώσεις", "app.components.CommonGroundResults.votesLabe": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabel1": "ψήφοι", "app.components.CommonGroundStatements.agreeLabel": "Συμφωνώ", "app.components.CommonGroundStatements.disagreeLabel": "Διαφωνώ", "app.components.CommonGroundStatements.noMoreStatements": "Δεν υπάρχουν δηλώσεις για να απαντήσετε αυτή τη στιγμή", "app.components.CommonGroundStatements.noResults": "Δεν υπάρχουν ακόμη αποτελέσματα. Παρακαλούμε βεβαιωθείτε ότι έχετε συμμετάσχει στη φάση Common Ground και ελέγξτε ξανά εδώ μετά.", "app.components.CommonGroundStatements.unsureLabel": "Αβέβαιο", "app.components.CommonGroundTabs.resultsTabLabel": "Αποτελέσματα", "app.components.CommonGroundTabs.statementsTabLabel": "Δηλ<PERSON><PERSON><PERSON><PERSON>ς", "app.components.CommunityMonitorModal.formError": "Αντιμετώπισε ένα σφάλμα.", "app.components.CommunityMonitorModal.surveyDescription2": "Αυτή η συνεχής έρευνα καταγρά<PERSON>ει πώς αισθάνεστε για τη διακυβέρνηση και τις δημόσιες υπηρεσίες.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Διαρκεί <1 λεπτό} one {Διαρκεί 1 λεπτό} other {Διαρκεί # λεπτά}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Ένα μήνυμα ηλεκτρονικού ταχυδρομείου με κωδικό επιβεβαίωσης έχει σταλεί στο {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Αλλάξτε το email σας.", "app.components.ConfirmationModal.codeInput": "Κω<PERSON>ικ<PERSON>ς", "app.components.ConfirmationModal.confirmationCodeSent": "Αποστολή νέου κωδικού", "app.components.ConfirmationModal.didntGetAnEmail": "Δεν λάβατε email;", "app.components.ConfirmationModal.foundYourCode": "Βρήκατε τον κωδικό σας;", "app.components.ConfirmationModal.goBack": "Επιστροφή.", "app.components.ConfirmationModal.sendEmailWithCode": "Αποστολή email με κωδικό", "app.components.ConfirmationModal.sendNewCode": "Στείλτε νέο κωδικό.", "app.components.ConfirmationModal.verifyAndContinue": "Επαλήθευση και συνέχιση", "app.components.ConfirmationModal.wrongEmail": "Λάθος email;", "app.components.ConsentManager.Banner.accept": "Αποδοχή", "app.components.ConsentManager.Banner.ariaButtonClose2": "Απορρίψτε την πολιτική και κλείστε το πανό", "app.components.ConsentManager.Banner.close": "Κλείσιμο", "app.components.ConsentManager.Banner.mainText": "Αυτή η πλατφόρμα χρησιμοποιεί cookies σύμφωνα με την {policyLink}.", "app.components.ConsentManager.Banner.manage": "Διαχείριση", "app.components.ConsentManager.Banner.policyLink": "Πολιτική cookie", "app.components.ConsentManager.Banner.reject": "Απόρριψη", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Διαφήμιση", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Το χρησιμοποιούμε για την εξατομίκευση και τη μέτρηση της αποτελεσματικότητας των διαφημιστικών εκστρατειών του ιστότοπού μας. Δεν θα προβάλλουμε καμία διαφήμιση σε αυτή την πλατφόρμα, αλλά οι ακόλουθες υπηρεσίες ενδέχεται να σας προσφέρουν μια εξατομικευμένη διαφήμιση με βάση τις σελίδες που επισκέπτεστε στον ιστότοπό μας.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Να επιτρέπεται", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Ανάλυση", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Χρησιμοποιούμε αυτή την παρακολούθηση για να κατανοήσουμε καλύτερα τον τρόπο με τον οποίο χρησιμοποιείτε την πλατφόρμα, προκειμένου να μάθουμε και να βελτιώσουμε την πλοήγησή σας. Αυτές οι πληροφορίες χρησιμοποιούνται μόνο σε μαζικές αναλύσεις και σε καμία περίπτωση για την παρακολούθηση μεμονωμένων ατόμων.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Επιστροφή", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Ακύρωση", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Να μην επιτρέπεται", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Λειτουργικό", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Αυτό είναι απαραίτητο για την ενεργοποίηση και την παρακολούθηση των βασικών λειτουργιών του ιστότοπου. Ορισμένα εργαλεία που αναφέρονται εδώ ενδέχεται να μην ισχύουν για εσάς. Παρακαλούμε διαβάστε την πολιτική μας για τα cookies για περισσότερες πληροφορίες.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Απαιτείται", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Για να έχουμε μια λειτουργική πλατφόρμα, αποθηκεύουμε ένα cookie πιστοποίησης ταυτότητας εάν εγγραφείτε, κα<PERSON><PERSON><PERSON> και τη γλώσσα στην οποία χρησιμοποιείτε αυτή την πλατφόρμα.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Αποθήκευση", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Οι προτιμήσεις σας για τα cookies", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Εργαλεία", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Αποποίηση ευθυνών ανάρτησης περιεχομένου", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Ανεβάζοντας περιεχόμενο, δηλώνετε ότι αυτό το περιεχόμενο δεν παραβιάζει οποιουσδήποτε κανονισμούς ή δικαιώματα τρίτων, όπω<PERSON> δικαιώματα πνευματικής ιδιοκτησίας, δικαιώματα προστασίας προσωπικών δεδομένων, δικαιώματα εμπορικών μυστικών κ.ο.κ. Κατά συνέπεια, αν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αυτό το περιεχόμενο, αναλαμβάνετε την πλήρη και αποκλειστική ευθύνη για όλες τις άμεσες και έμμεσες ζημίες που προκύπτουν από το περιεχόμενο που ανεβάζετε. <PERSON><PERSON>ιπλέον, αναλαμβάνετε την υποχρέωση να αποζημιώσετε τον ιδιοκτήτη της πλατφόρμας και το Go Vocal από τυχόν αξιώσεις ή ευθύνες τρίτων έναντι τρίτων, καθώς και από κάθε σχετικό κόστος που θα προκύψει ή θα προκύψει από το περιεχόμενο που ανεβάσατε.", "app.components.ContentUploadDisclaimer.onAccept": "Καταλαβαίνω", "app.components.ContentUploadDisclaimer.onCancel": "Ακύρωση", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Πείτε μας γιατί", "app.components.CustomFieldsForm.addressInputAriaLabel": "Εισαγωγή διεύθυνσης", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Εισάγετε μια διεύθυνση...", "app.components.CustomFieldsForm.adminFieldTooltip": "Πεδίο ορατό μόνο στους διαχειριστές", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Όλες οι απαντήσεις σε αυτή την έρευνα είναι ανώνυμες.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Για ένα πολύγωνο απαιτούνται τουλάχιστον τρία σημεία.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Για μια γραμμή απαιτούνται τουλάχιστον δύο σημεία.", "app.components.CustomFieldsForm.attachmentRequired": "Απαιτείτ<PERSON>ι τουλάχιστον ένα συνημμένο", "app.components.CustomFieldsForm.authorFieldLabel": "Συγγραφέας", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Ξεκινήστε να πληκτρολογείτε για αναζήτηση με το email ή το όνομα του χρήστη...", "app.components.CustomFieldsForm.back": "Πίσω", "app.components.CustomFieldsForm.budgetFieldLabel": "Προϋπολογισμός", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Κάντε κλικ στο χάρτη για να σχεδιάσετε. Στη συνέχεια, σύρετε τα σημεία για να τα μετακινήσετε.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Κάντε κλικ στο χάρτη ή πληκτρολογήστε μια διεύθυνση παρακάτω για να προσθέσετε την απάντησή σας.", "app.components.CustomFieldsForm.confirm": "Επιβεβαίωση", "app.components.CustomFieldsForm.descriptionMinLength": "Η περιγραφή πρέπει να έχει μήκος τουλάχιστον {min} χαρακτήρες.", "app.components.CustomFieldsForm.descriptionRequired": "Η περιγραφή είναι υποχρεωτική", "app.components.CustomFieldsForm.fieldMaximumItems": "Το πολύ {maxSelections, plural, one {# επιλογή} other {# επιλογές}} μπορούν να επιλεγούν για το πεδίο \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "Τουλάχιστον {minSelections, plural, one {# επιλογή} other {# επιλογές}} μπορούν να επιλεγούν για το πεδίο \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "Το πεδίο \"{fieldName}\" είναι υποχρεωτικό", "app.components.CustomFieldsForm.fileSizeLimit": "Το όριο μεγέθους αρχείου είναι {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "Η εικόνα απαιτείται", "app.components.CustomFieldsForm.minimumCoordinates2": "Απαιτείται τουλάχιστον {numPoints} σημεία χάρτη.", "app.components.CustomFieldsForm.notPublic1": "*Η απάντηση αυτή θα κοινοποιηθεί μόνο στους διαχειριστές του έργου και όχι στο κοινό.", "app.components.CustomFieldsForm.otherArea": "Κάπου αλλού", "app.components.CustomFieldsForm.progressBarLabel": "Πρ<PERSON><PERSON><PERSON>ος", "app.components.CustomFieldsForm.removeAnswer": "Αφαίρεση απάντησης", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Επιλέξτε όσα θέλετε", "app.components.CustomFieldsForm.selectBetween": "*Επιλογή μεταξύ των επιλογών {minItems} και {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*Επιλογή ακριβώς {selectExactly, plural, one {# επιλογή} other {# επιλογές}}", "app.components.CustomFieldsForm.selectMany": "*Επιλέξτε όσα θέλετε", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Πατήστε στο χάρτη για να σχεδιάσετε. Στη συνέχεια, σύρετε τα σημεία για να τα μετακινήσετε.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Πατήστε στο χάρτη για να σχεδιάσετε.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Πατήστε στο χάρτη για να προσθέσετε την απάντησή σας.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Πατήστε στο χάρτη ή πληκτρολογήστε μια διεύθυνση παρακάτω για να προσθέσετε την απάντησή σας.", "app.components.CustomFieldsForm.tapToAddALine": "Πατήστε για να προσθέσετε μια γραμμή", "app.components.CustomFieldsForm.tapToAddAPoint": "Πατήστε για να προσθέσετε ένα σημείο", "app.components.CustomFieldsForm.tapToAddAnArea": "Πατήστε για να προσθέσετε μια περιοχή", "app.components.CustomFieldsForm.titleMaxLength": "Ο τίτλος πρέπει να είναι το πολύ {max} χαρακτήρες", "app.components.CustomFieldsForm.titleMinLength": "Ο τίτλος πρέπει να είναι τουλάχιστον {min} χαρακτήρες", "app.components.CustomFieldsForm.titleRequired": "Απαιτείται ο τίτλος", "app.components.CustomFieldsForm.topicRequired": "Απαιτείται τουλάχιστον μία ετικέτα", "app.components.CustomFieldsForm.typeYourAnswer": "Πληκτρολογήστε την απάντησή σας", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Απαιτ<PERSON><PERSON><PERSON><PERSON><PERSON> να πληκτρολογήσετε την απάντησή σας", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Ανεβάστε ένα αρχείο zip που περιέχει ένα ή περισσότερα shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Εάν η τοποθεσία δεν εμφανίζεται μεταξύ των επιλογών καθώς πληκτρολογείτε, μπορείτε να προσθέσετε έγκυρες συντεταγμένες με τη μορφή \"γεωγραφικό πλάτος, γεωγραφικό μήκος\" για να καθορίσετε μια ακριβή τοποθεσία (π.χ.: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Ορισμένα πεδία ήταν μη έγκυρα. Παρακαλούμε διορθώστε τα σφάλματα και προσπαθήστε ξανά.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Προέκυ<PERSON><PERSON> άγνωστο σφάλμα κατά την υποβολή της αναφοράς σας. Παρακαλούμε προσπαθήστε ξανά.", "app.components.ErrorBoundary.errorFormLabelClose": "Κλείσιμο", "app.components.ErrorBoundary.errorFormLabelComments": "Τι συνέβη;", "app.components.ErrorBoundary.errorFormLabelEmail": "Email", "app.components.ErrorBoundary.errorFormLabelName": "Όνομα", "app.components.ErrorBoundary.errorFormLabelSubmit": "Υποβολή", "app.components.ErrorBoundary.errorFormSubtitle": "Η ομάδα μας έχει ειδοποιηθεί.", "app.components.ErrorBoundary.errorFormSubtitle2": "Αν θέλετε να σας βοηθήσουμε, πείτε μας τι συνέβη παρακάτω.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Η ανατροφοδότησή σας έχουν σταλεί. Σας ευχαριστούμε!", "app.components.ErrorBoundary.errorFormTitle": "Φαίνε<PERSON><PERSON><PERSON> ότι υπάρχει κάποιο πρόβλημα.", "app.components.ErrorBoundary.genericErrorWithForm": "Εμφανίστηκε σφάλμα και δεν μπορούμε να εμφανίσουμε αυτό το περιεχόμενο. Παρακαλούμε προσπαθήστε ξανά, ή {openForm}", "app.components.ErrorBoundary.openFormText": "βοηθήστε μας να το λύσουμε", "app.components.ErrorToast.budgetExceededError": "Δεν έχετε αρκετό προϋπολογισμό", "app.components.ErrorToast.votesExceededError": "Δεν σας έχουν απομείνει αρκετές ψήφοι", "app.components.EventAttendanceButton.forwardToFriend": "Προώθηση σε ένα φίλο", "app.components.EventAttendanceButton.maxRegistrationsReached": "Ο μέγιστος αριθμός εγγραφών για την εκδήλωση έχει επιτευχθεί. Δεν έχουν απομείνει θέσεις.", "app.components.EventAttendanceButton.register": "Εγγραφή", "app.components.EventAttendanceButton.registered": "Εγγεγραμμένο", "app.components.EventAttendanceButton.seeYouThere": "Τα λέμε εκεί!", "app.components.EventAttendanceButton.seeYouThereName": "Τα λέμε εκεί, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Λιγότερες πληροφορίες για την εκδήλωση έγιναν ορατές.", "app.components.EventCard.a11y_moreContentVisible": "Περισσότερες πληροφορίες για την εκδήλωση έγιναν ορατές.", "app.components.EventCard.a11y_readMore": "Διαβάστε περισσότερα για την εκδήλωση \"{eventTitle}\".", "app.components.EventCard.endsAt": "Τελειώνει στις", "app.components.EventCard.readMore": "Διαβάστε περισσότερα", "app.components.EventCard.showLess": "Εμφάνιση λιγότερων", "app.components.EventCard.showMore": "Εμφάνιση περισσότερων", "app.components.EventCard.startsAt": "Αρχίζει στις", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Επόμενες και τρέχουσες εκδηλώσεις στο πλαίσιο αυτού του έργου", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Προσεχε<PERSON>ς και τρέχουσες εκδηλώσεις σε αυτή τη φάση", "app.components.FileUploader.a11y_file": "Αρχείο:", "app.components.FileUploader.a11y_filesToBeUploaded": "Αρχεία προς μεταφόρτωση: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Δεν έχουν προστεθεί αρχεία.", "app.components.FileUploader.a11y_removeFile": "Κατάργηση αυτού του αρχείου", "app.components.FileUploader.fileInputDescription": "Κάντε κλικ για να επιλέξετε ένα αρχείο", "app.components.FileUploader.fileUploadLabel": "Συνημμένα αρχεία (μέγιστο 50MB)", "app.components.FileUploader.file_too_large2": "Αρχεία μεγαλύτερα από {maxSizeMb}MB δεν επιτρέπονται.", "app.components.FileUploader.incorrect_extension": "Το {fileName} δεν υποστηρίζεται από το σύστημά μας, δεν θα μεταφορτωθεί.", "app.components.FilterBoxes.a11y_allFilterSelected": "Επιλεγμέν<PERSON> φίλτρο κατάστασης: όλα", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# υποβολή} other {# υποβολές}}", "app.components.FilterBoxes.a11y_removeFilter": "Κατάργηση φίλτρου", "app.components.FilterBoxes.a11y_selectedFilter": "Επιλεγμέν<PERSON> φίλτρο κατάστασης: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Επιλεγμένα {numberOfSelectedTopics, plural, =0 {φίλτρα μηδενικής ετικέτας} one {φίλτρο μιας ετικέτας} other {# φίλτρα ετικετών}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Όλα", "app.components.FilterBoxes.areas": "Φίλτρο ανά περιοχή", "app.components.FilterBoxes.inputs": "είσοδοι", "app.components.FilterBoxes.noValuesFound": "Δεν υπάρχουν διαθέσιμες τιμές.", "app.components.FilterBoxes.showLess": "Εμφάνιση λιγότερων", "app.components.FilterBoxes.showTagsWithNumber": "Εμφάνιση όλων ({numberTags})", "app.components.FilterBoxes.statusTitle": "Κατάσταση", "app.components.FilterBoxes.topicsTitle": "Ετικέτες", "app.components.FiltersModal.filters": "Φίλτρα", "app.components.FolderFolderCard.a11y_folderDescription": "Περιγραφ<PERSON> φακέλου:", "app.components.FolderFolderCard.a11y_folderTitle": "Τίτλος φακέλου:", "app.components.FolderFolderCard.archived": "Αρχειοθετημένος", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# έργα} one {# έργο} other {# έργα}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Ο τύπος πεδίου δεν μπορεί να αλλάξει όταν υπάρχουν υποβολές.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Τύπος", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Αυτόματη αποθήκευση", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Η αυτόματη αποθήκευση είναι ενεργοποιημένη από προεπιλογή όταν ανοίγετε τον επεξεργαστή φόρμας. Κάθ<PERSON> φορά που κλείνετε τον πίνακα ρυθμίσεων πεδίου χρησιμοποιώντας το κουμπί \"X\", θα ενεργοποιείται αυτόματα η αποθήκευση.", "app.components.GanttChart.timeRange.month": "Μήνας", "app.components.GanttChart.timeRange.quarter": "Τρίμηνο", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Πολυετές", "app.components.GanttChart.timeRange.year": "Έτος", "app.components.GanttChart.today": "Σήμερα", "app.components.GoBackButton.group.edit.goBack": "Επιστροφή", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Επιστροφή στην προηγούμενη σελίδα", "app.components.HookForm.Feedback.errorTitle": "Υπάρχει ένα πρόβλημα", "app.components.HookForm.Feedback.submissionError": "Δο<PERSON>ιμά<PERSON>τ<PERSON> ξανά. Εάν το πρόβλημα παραμένει, επικοινωνήστε μαζί μας", "app.components.HookForm.Feedback.submissionErrorTitle": "Υπήρξε ένα πρόβλημα από την πλευρά μας, συγγνώμη", "app.components.HookForm.Feedback.successMessage": "Η φόρμα υποβλήθηκε επιτυχώς", "app.components.HookForm.PasswordInput.passwordLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "app.components.HorizontalScroll.scrollLeftLabel": "Μετακινηθείτε προς τα αριστερά.", "app.components.HorizontalScroll.scrollRightLabel": "Μετακινηθείτε προς τα δεξιά.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} οι ιδέες έχουν φορτωθεί.", "app.components.IdeaCards.filters": "Φίλτρα", "app.components.IdeaCards.filters.mostDiscussed": "Τα πιο συζητημένα", "app.components.IdeaCards.filters.newest": "Νέο", "app.components.IdeaCards.filters.oldest": "Παλιό", "app.components.IdeaCards.filters.popular": "Περισσότερο άρεσε", "app.components.IdeaCards.filters.random": "Τυχαία", "app.components.IdeaCards.filters.sortBy": "Ταξινόμηση κατά", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Η ταξινόμηση άλλαξε σε: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trending", "app.components.IdeaCards.showMore": "Εμφάνιση περισσότερων", "app.components.IdeasMap.a11y_hideIdeaCard": "Απόκρυψη κάρτας ιδέας.", "app.components.IdeasMap.a11y_mapTitle": "Επισκόπηση χάρτη", "app.components.IdeasMap.clickOnMapToAdd": "Κάντε κλικ στο χάρτη για να προσθέσετε την εισήγησή σας", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Ως διαχειριστής, μπορείτε να κάνετε κλικ στο χάρτη για να προσθέσετε τη συμβολή σας, ακόμη και αν αυτή η φάση δεν είναι ενεργή.", "app.components.IdeasMap.filters": "Φίλτρα", "app.components.IdeasMap.multipleInputsAtLocation": "Πολλα<PERSON><PERSON><PERSON><PERSON> είσοδοι σε αυτή τη θέση", "app.components.IdeasMap.noFilteredResults": "Τα φίλτρα που επιλέξατε δεν επέστρεψαν αποτελέσματα", "app.components.IdeasMap.noResults": "Δεν βρέθηκαν αποτελέσματα", "app.components.IdeasMap.or": "ή", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, χωρίς αντιπάθειες.} one {1 dislike.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, χωρίς συμπάθειες.} one {, 1 like.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "σύνδεση", "app.components.IdeasMap.signUpLinkText": "εγγραφείτε", "app.components.IdeasMap.submitIdea2": "Υποβολή εισόδου", "app.components.IdeasMap.tapOnMapToAdd": "Πατήστε στο χάρτη για να προσθέσετε την εισήγησή σας", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Ως διαχειριστής, μπορείτε να πατήσετε στο χάρτη για να προσθέσετε τη γνώμη σας, ακόμη και αν αυτή η φάση δεν είναι ενεργή.", "app.components.IdeasMap.userInputs2": "Εισροές από τους συμμετέχοντες", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, no comments} one {, 1 comment} other {, # comments}}", "app.components.IdeasShow.bodyTitle": "Περιγραφή", "app.components.IdeasShow.deletePost": "Διαγραφή", "app.components.IdeasShow.editPost": "Επεξεργασία", "app.components.IdeasShow.goBack": "Επιστροφή", "app.components.IdeasShow.moreOptions": "Περισσότερες επιλογές", "app.components.IdeasShow.or": "ή", "app.components.IdeasShow.proposedBudgetTitle": "Προτεινόμενος προϋπολογισμός", "app.components.IdeasShow.reportAsSpam": "Αναφορά ως spam", "app.components.IdeasShow.send": "Αποστολή", "app.components.IdeasShow.skipSharing": "Παραλε<PERSON>ψτε το, θα το κάνω αργότερα", "app.components.IdeasShowPage.signIn2": "Συνδεθείτε", "app.components.IdeasShowPage.sorryNoAccess": "Λυπ<PERSON><PERSON>α<PERSON>, δεν μπορείτε να έχετε πρόσβαση σε αυτή τη σελίδα. Ίσως χρειαστεί να συνδεθείτε ή να εγγραφείτε για να αποκτήσετε πρόσβαση.", "app.components.LocationInput.noOptions": "Δεν υπάρχουν επιλογές", "app.components.Modal.closeWindow": "Κλείσιμο παραθύρου", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Εισάγετε μια νέα διεύθυνση email", "app.components.PageNotFound.goBackToHomePage": "Επιστροφή στην αρχική σελίδα", "app.components.PageNotFound.notFoundTitle": "Η σελίδα δεν βρέθηκε", "app.components.PageNotFound.pageNotFoundDescription": "Η ζητούμενη σελίδα δεν βρέθηκε.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Παροχή περιεχομένου για μία τουλάχιστον γλώσσα", "app.components.PagesForm.editContent": "Περιεχόμενο", "app.components.PagesForm.fileUploadLabel": "Συνημμένα αρχεία (μέγιστο 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Τα αρχεία δεν πρέπει να είναι μεγαλύτερα από 50Mb. Τα προστιθέμενα αρχεία θα εμφανίζονται στο κάτω μέρος αυτής της σελίδας.", "app.components.PagesForm.navbarItemTitle": "Όνομα στη γραμμή πλοήγησης", "app.components.PagesForm.pageTitle": "Τίτλος", "app.components.PagesForm.savePage": "Αποθήκευση σελίδας", "app.components.PagesForm.saveSuccess": "Η σελίδα αποθηκεύτηκε με επιτυχία.", "app.components.PagesForm.titleMissingOneLanguageError": "Παροχή τίτλου για μία τουλάχιστον γλώσσα", "app.components.Pagination.back": "Προηγούμενη σελίδα", "app.components.Pagination.next": "Επόμενη σελίδα", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Ξοδέψατε το {votesCast}, το οποίο υπερβαίνει το όριο του {votesLimit}. Παρακαλούμε αφαιρέστε κάποια προϊόντα από το καλάθι σας και προσπαθήστε ξανά.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} αριστερά", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Πρέπει να ξοδέψετε τουλάχιστον {votesMinimum} για να μπορέσετε να στείλετε το καλάθι σας.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Πρέπει να επιλέξετε τουλάχιστον μία επιλογή για να μπορέσετε να την υποβάλετε.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Πρέπει να προσθέσετε κάτι στο καλάθι σας πριν το στείλετε.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Δεν έχουν απομείνει μονάδες} other {# out of {totalNumberOfVotes, plural, one {1 μονάδα} other {# μονάδες}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Δεν έχουν απομείνει πόντοι} other {# out of {totalNumberOfVotes, plural, one {1 πόντος} other {# πόντοι}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Δεν έχουν απομείνει μάρκες} other {# out of {totalNumberOfVotes, plural, one {1 μάρκα} other {# μάρκες}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Δεν έχουν απομείνει ψήφοι} other {# από {totalNumberOfVotes, plural, one {1 ψήφος} other {# ψήφοι}} αριστερά}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# votes} one {# vote} other {# votes}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Έχετε δώσει {votesCast} ψήφους, που υπερβαίνουν το όριο του {votesLimit}. Παρακαλούμε αφαιρέστε μερικές ψήφους και προσπαθήστε ξανά.", "app.components.ParticipationCTABars.addInput": "Προσθήκη εισόδου", "app.components.ParticipationCTABars.allocateBudget": "Διαθέστε τον προϋπολογισμό σας", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Ο προϋπολογισμός σας υποβλήθηκε με επιτυχία.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Ανοιχτό για συμμετοχή", "app.components.ParticipationCTABars.poll": "Πάρτε μέρος στη δημοσκόπηση", "app.components.ParticipationCTABars.reviewDocument": "Επανεξέταση του εγγράφου", "app.components.ParticipationCTABars.seeContributions": "Βλέπε συνεισφορές", "app.components.ParticipationCTABars.seeEvents3": "Δείτε εκδηλώσεις", "app.components.ParticipationCTABars.seeIdeas": "Δείτε ιδέες", "app.components.ParticipationCTABars.seeInitiatives": "Δείτε πρωτοβουλίες", "app.components.ParticipationCTABars.seeIssues": "Δείτε θέματα", "app.components.ParticipationCTABars.seeOptions": "Δείτε τις επιλογές", "app.components.ParticipationCTABars.seePetitions": "Βλέ<PERSON>ε αναφορές", "app.components.ParticipationCTABars.seeProjects": "Δείτε έργα", "app.components.ParticipationCTABars.seeProposals": "Δείτε προτάσεις", "app.components.ParticipationCTABars.seeQuestions": "Δείτε ερωτήσεις", "app.components.ParticipationCTABars.submit": "Υποβολή", "app.components.ParticipationCTABars.takeTheSurvey": "Πάρτε μέρος στην έρευνα", "app.components.ParticipationCTABars.userHasParticipated": "Συμμετείχατε σε αυτό το έργο.", "app.components.ParticipationCTABars.viewInputs": "Προβολή εισροών", "app.components.ParticipationCTABars.volunteer": "Εθελοντής", "app.components.ParticipationCTABars.votesCounter.vote": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.votes": "ψήφοι", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON>ω<PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης κρυφός", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON>ω<PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης ορατός", "app.components.PasswordInput.a11y_strength1Password": "Κακ<PERSON> ισχύς κωδικού πρόσβασης", "app.components.PasswordInput.a11y_strength2Password": "Αδύναμη ισχύς κωδικού πρόσβασης", "app.components.PasswordInput.a11y_strength3Password": "Μέτρια ισχύς κωδικού πρόσβασης", "app.components.PasswordInput.a11y_strength4Password": "Ισχυρή ισχύς κωδικού πρόσβασης", "app.components.PasswordInput.a11y_strength5Password": "Πολύ ισχυρή ισχύς κωδικού πρόσβασης", "app.components.PasswordInput.hidePassword": "Απόκρυψη κωδικού πρόσβασης", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Πολύ μικρός (ελάχ. {minimumPasswordLength} χαρακτήρες)", "app.components.PasswordInput.minimumPasswordLengthError": "Δώστε έναν κωδικ<PERSON> πρόσβασης που να έχει μήκος τουλάχιστον {minimumPasswordLength} χαρακτήρες", "app.components.PasswordInput.passwordEmptyError": "Εισάγετε τον κωδικό πρόσβασής σας", "app.components.PasswordInput.passwordStrengthTooltip1": "Για να κάνετε τον κωδικό πρόσβασής σας ισχυρότερο:", "app.components.PasswordInput.passwordStrengthTooltip2": "Χρησιμοπο<PERSON>ήστε έναν συνδυασμό μη διαδοχικών πεζών γραμμάτων, κε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> γραμμάτων, ψ<PERSON><PERSON><PERSON><PERSON><PERSON>, ειδ<PERSON><PERSON><PERSON><PERSON> χαρακτήρων και σημείων στίξης", "app.components.PasswordInput.passwordStrengthTooltip3": "Αποφύγετε κοινές ή εύκολα μαντεύσιμες λέξεις", "app.components.PasswordInput.passwordStrengthTooltip4": "Αύξηση του μήκους", "app.components.PasswordInput.showPassword": "Εμφάνιση κωδικού πρόσβασης", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "Αδύνα<PERSON>ος", "app.components.PasswordInput.strength3Password": "Μέτριος", "app.components.PasswordInput.strength4Password": "Ισχυρ<PERSON>ς", "app.components.PasswordInput.strength5Password": "Πολ<PERSON> ισχυρός", "app.components.PostCardsComponents.list": "Λίστα", "app.components.PostCardsComponents.map": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Προσθέστε μια επίσημη ενημέρωση", "app.components.PostComponents.OfficialFeedback.cancel": "Ακύρωση", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Διαγραφή", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την επίσημη ενημέρωση;", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Επεξεργασία", "app.components.PostComponents.OfficialFeedback.lastEdition": "Τελευτα<PERSON>α επεξεργασία στις {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Τελευταία ενημέρωση: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Επίσημα", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Επιλέξτε πώς θα βλέπουν οι άλλοι το όνομά σας", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Όνομα συντάκτης επίσημης ενημέρωσης", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Επίσημο κείμενο της ενημέρωσης", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Επίσημες ενημερώσεις", "app.components.PostComponents.OfficialFeedback.postedOn": "Δημοσιεύτηκε στις {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Δημοσίευση", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Εμφάνιση προηγούμενων ενημερώσεων", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Δώστε μια ενημέρωση...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "Συγγνώμη, υπήρξε πρόβλημα", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Μήνυμα ενημέρωσης", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Η ενημέρωσή σας δημοσιεύθηκε με επιτυχία!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Υποστηρίξτε τη συνεισφορά μου '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Υποστηρίξτε τη συνεισφορά μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Υποστηρίξτε τη συνεισφορά μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Υποστηρίξτε την ιδέα μου '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Υποστηρίξτε την ιδέα μου: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Υποστηρίξτε την ιδέα μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Τι γνώμη έχετε για αυτή την πρόταση; Ψηφίστε την και κοινοποιείστε τη συζήτηση στο {postUrl} για να ακουστεί η φωνή σας!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Υποστηρίξτε την πρότασή μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Υποστηρίξτε την πρωτοβουλία μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Έχω δημοσιεύσει ένα σχόλιο '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Μόλις δημοσίευσα ένα σχόλιο: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Μόλις δημοσίευσα ένα σχόλιο: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Υποστηρίξτε την προτεινόμενη επιλογή μου '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Υποστηρίξτε την προτεινόμενη επιλογή μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Υποστηρίξτε την επιλογή μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Υποστηρίξτε το αίτημα μου '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Υποστηρίξτε το αίτημά μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Υποστηρίξτε το αίτημά μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Υποστηρίξτε το έργο μου '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Υποστηρίξτε το έργο μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Υποστηρίξτε το έργο μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Υποστηρίξτε την πρότασή μου '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Υποστηρίξτε την πρότασή μου: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Μόλις δημοσίευσα μια πρόταση για το {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Συμμετέχετε στη συζήτηση σχετικά με την ερώτηση '{postTitle}' στο {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Συμμετοχή στη συζήτηση: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Συμμετοχή στη συζήτηση: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Ψηφίστε για το {postTitle} στο", "app.components.PostComponents.linkToHomePage": "Σύνδεση με την αρχική σελίδα", "app.components.PostComponents.readMore": "Διαβάστε περισσότερα...", "app.components.PostComponents.topics": "Θέματα", "app.components.ProjectArchivedIndicator.archivedProject": "Δυστ<PERSON><PERSON><PERSON><PERSON>, δεν μπορείτε πλέον να συμμετέχετε σε αυτό το έργο, επειδή έχει αρχειοθετηθεί", "app.components.ProjectArchivedIndicator.previewProject": "Σχέδιο έργου:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Ορατό μόνο στους συντονιστές και σε όσους έχουν σύνδεσμο προεπισκόπησης.", "app.components.ProjectCard.a11y_projectDescription": "Περιγρα<PERSON>ή έργου:", "app.components.ProjectCard.a11y_projectTitle": "Τ<PERSON><PERSON><PERSON><PERSON> έργου:", "app.components.ProjectCard.addYourOption": "Προσθέστε την επιλογή σας", "app.components.ProjectCard.allocateYourBudget": "Διαθέστε τον προϋπολογισμό σας", "app.components.ProjectCard.archived": "Αρχειοθετημένος", "app.components.ProjectCard.comment": "Σχόλιο", "app.components.ProjectCard.contributeYourInput": "Συνεισφέρετε την εισήγησή σας", "app.components.ProjectCard.finished": "Έχει τελειώσει", "app.components.ProjectCard.joinDiscussion": "Συμμετοχή στη συζήτηση", "app.components.ProjectCard.learnMore": "Μάθετε περισσότερα", "app.components.ProjectCard.reaction": "Αντίδραση", "app.components.ProjectCard.readTheReport": "Διαβάστε την έκθεση", "app.components.ProjectCard.reviewDocument": "Επανεξέταση του εγγράφου", "app.components.ProjectCard.submitAnIssue": "Υποβάλετε ένα σχόλιο", "app.components.ProjectCard.submitYourIdea": "Υποβάλετε την ιδέα σας", "app.components.ProjectCard.submitYourInitiative": "Υποβάλετε την πρωτοβουλία σας", "app.components.ProjectCard.submitYourPetition": "Υποβάλετε την αίτησή σας", "app.components.ProjectCard.submitYourProject": "Υποβάλετε το έργο σας", "app.components.ProjectCard.submitYourProposal": "Υποβάλετε την πρότασή σας", "app.components.ProjectCard.takeThePoll": "Πάρτε μέρος στη δημοσκόπηση", "app.components.ProjectCard.takeTheSurvey": "Πάρτε μέρος στην έρευνα", "app.components.ProjectCard.viewTheContributions": "Δείτε τις συνεισφορές", "app.components.ProjectCard.viewTheIdeas": "Δείτε τις ιδέες", "app.components.ProjectCard.viewTheInitiatives": "Δείτε τις πρωτοβουλίες", "app.components.ProjectCard.viewTheIssues": "Δείτε τα σχόλια", "app.components.ProjectCard.viewTheOptions": "Δείτε τις επιλογές", "app.components.ProjectCard.viewThePetitions": "Δείτε τις αναφορές", "app.components.ProjectCard.viewTheProjects": "Δείτε τα έργα", "app.components.ProjectCard.viewTheProposals": "Δείτε τις προτάσεις", "app.components.ProjectCard.viewTheQuestions": "Δείτε τις ερωτήσεις", "app.components.ProjectCard.vote": "Ψηφίστε", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# σχόλια} other {# σχόλια}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# συνεισφορά} other {# συνεισφορές}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {δεν υπάρχουν ιδέες ακόμα} one {# ιδέα} other {# ιδέες}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# πρωτοβουλίες} one {# πρωτοβουλία} other {# πρωτοβουλίες}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# σχόλιο} other {# σχόλια}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# επιλογή} other {# επιλογές}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petitions} one {# petition} other {# petitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# έργο} other {# έργα}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# προτάσεις} one {# πρόταση} other {# προτάσεις}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# ερώτηση} other {# ερωτήσεις}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# comments} one {# comments} other {# comments}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# έργα} one {# έργο} other {# έργα}}", "app.components.ProjectFolderCards.components.Topbar.all": "Όλα", "app.components.ProjectFolderCards.components.Topbar.archived": "Αρχειοθετημένος", "app.components.ProjectFolderCards.components.Topbar.draft": "Προσχέδιο", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Φίλτρο ανά", "app.components.ProjectFolderCards.components.Topbar.published2": "Δημοσιευμένο", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Ετικέτα", "app.components.ProjectFolderCards.noProjectYet": "Προς το παρόν δεν υπάρχουν ανοικτά έργα", "app.components.ProjectFolderCards.noProjectsAvailable": "Δεν υπάρχουν διαθέσιμα έργα", "app.components.ProjectFolderCards.showMore": "Εμφάνιση περισσότερων", "app.components.ProjectFolderCards.stayTuned": "Ελέγξτε ξανά για νέες ευκαιρίες δέσμευσης", "app.components.ProjectFolderCards.tryChangingFilters": "Δοκιμάστε να αλλάξετε τα επιλεγμένα φίλτρα.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Χρησιμοποιείται επίσης σε αυτές τις πόλεις:", "app.components.ProjectTemplatePreview.copied": "Αντιγραμμένο", "app.components.ProjectTemplatePreview.copyLink": "Αντιγραφ<PERSON> συνδέσμου", "app.components.QuillEditor.alignCenter": "Κείμενο στο κέντρο", "app.components.QuillEditor.alignLeft": "Ευθυγράμμιση αριστερά", "app.components.QuillEditor.alignRight": "Ευθυγράμμιση δεξιά", "app.components.QuillEditor.bold": "Έντονη γραφή", "app.components.QuillEditor.clean": "Κατάργηση μορφοποίησης", "app.components.QuillEditor.customLink": "Προσθήκη κουμπιού", "app.components.QuillEditor.customLinkPrompt": "Εισαγωγή συνδέσμου:", "app.components.QuillEditor.edit": "Επεξεργασία", "app.components.QuillEditor.image": "Αποστολή εικόνας", "app.components.QuillEditor.imageAltPlaceholder": "Σύντομη περιγραφή της εικόνας", "app.components.QuillEditor.italic": "Πλάγια γραφή", "app.components.QuillEditor.link": "Προσθήκη συνδέσμου", "app.components.QuillEditor.linkPrompt": "Εισαγωγή συνδέσμου:", "app.components.QuillEditor.normalText": "Κανονική γραφή", "app.components.QuillEditor.orderedList": "Διατεταγμένη λίστα", "app.components.QuillEditor.remove": "Κατάργηση", "app.components.QuillEditor.save": "Αποθήκευση", "app.components.QuillEditor.subtitle": "Υπότιτλος", "app.components.QuillEditor.title": "Τίτλος", "app.components.QuillEditor.unorderedList": "Μη ταξινομημένη λίστα", "app.components.QuillEditor.video": "Προσθήκη βίντεο", "app.components.QuillEditor.videoPrompt": "Εισαγωγή βίντεο:", "app.components.QuillEditor.visitPrompt": "Επισκεφθείτε το σύνδεσμο:", "app.components.ReactionControl.completeProfileToReact": "Συμπληρώστε το προφίλ σας για να αντιδράσετε", "app.components.ReactionControl.dislike": "Dislike", "app.components.ReactionControl.dislikingDisabledMaxReached": "Έχετε φτάσει στο μέγιστο αριθμό αντιπαθειών στο {projectName}.", "app.components.ReactionControl.like": "Όπως", "app.components.ReactionControl.likingDisabledMaxReached": "Έχετε φτάσει στο μέγιστο αριθμό likes στο {projectName}.", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Η αντίδραση θα ενεργοποιηθεί μόλις ξεκινήσει αυτή η φάση", "app.components.ReactionControl.reactingDisabledPhaseOver": "Δεν είναι πλέον δυνατό να αντιδράσετε σε αυτή τη φάση", "app.components.ReactionControl.reactingDisabledProjectInactive": "Δεν μπορείτε πλέον να αντιδράσετε σε ιδέες στο {projectName}.", "app.components.ReactionControl.reactingNotEnabled": "Το Reacting δεν είναι ενεργοποιημένο για αυτό το έργο", "app.components.ReactionControl.reactingNotPermitted": "Η αντίδραση είναι ενεργοποιημένη μόνο για ορισμένες ομάδες", "app.components.ReactionControl.reactingNotSignedIn": "Συνδεθείτε για να αντιδράσετε.", "app.components.ReactionControl.reactingPossibleLater": "Το Reacting θα ξεκινήσει στο {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Επαληθεύστε την ταυτότητά σας για να αντιδράσετε.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Ημερομηνία εκδήλωσης: {startDate} στο {startTime} έως {endDate} στο {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Ημερομηνία εκδήλωσης: {eventDate} από {startTime} έως {endTime}.", "app.components.Sharing.linkCopied": "Σύνδεσμος που αντιγράφηκε", "app.components.Sharing.or": "ή", "app.components.Sharing.share": "Κοινοποίηση", "app.components.Sharing.shareByEmail": "Κοινοποίηση με email", "app.components.Sharing.shareByLink": "Αντιγραφ<PERSON> συνδέσμου", "app.components.Sharing.shareOnFacebook": "Κοινοποίηση στο Facebook", "app.components.Sharing.shareOnTwitter": "Κοινοποίηση στο Twitter", "app.components.Sharing.shareThisEvent": "Μοιραστείτε αυτή την εκδήλωση", "app.components.Sharing.shareThisFolder": "Κοινοποίηση", "app.components.Sharing.shareThisProject": "Κοινοποίηση αυτού του έργου", "app.components.Sharing.shareViaMessenger": "Κοινοποίηση μέσω Messenger", "app.components.Sharing.shareViaWhatsApp": "Κοινοποίηση μέσω WhatsApp", "app.components.SideModal.closeButtonAria": "Κλείσιμο", "app.components.StatusModule.futurePhase": "Βλέπετε μια φάση που δεν έχει ξεκινήσει ακόμα. Θα μπορέσετε να συμμετάσχετε όταν ξεκινήσει η φάση.", "app.components.StatusModule.modifyYourSubmission1": "Τροποποιήστε την υποβολή σας", "app.components.StatusModule.submittedUntil3": "Η ψήφος σας μπορεί να υποβληθεί έως", "app.components.TopicsPicker.numberOfSelectedTopics": "Επιλεγμένα {numberOfSelectedTopics, plural, =0 {μηδενικές ετικέτες} one {μια ετικέτα} other {# ετικέτες}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Αναπτύξτε την εικόνα", "app.components.UI.MoreActionsMenu.moreOptions": "Περισσότερες επιλογές", "app.components.UI.MoreActionsMenu.showMoreActions": "Εμφάνιση περισσότερων ενεργειών", "app.components.UI.PhaseFilter.noAppropriatePhases": "Δεν βρέθηκαν κατάλληλες φάσεις για αυτό το έργο", "app.components.UI.RemoveImageButton.a11y_removeImage": "Κατάργηση", "app.components.UI.TranslateButton.original": "Αρχικό", "app.components.UI.TranslateButton.translate": "Μετάφραση", "app.components.Unauthorized.additionalInformationRequired": "Για τη συμμετοχή σας απαιτούνται πρόσθετες πληροφορίες.", "app.components.Unauthorized.completeProfile": "Πλήρες προφίλ", "app.components.Unauthorized.completeProfileTitle": "Συμπληρώστε το προφίλ σας για να συμμετάσχετε", "app.components.Unauthorized.noPermission": "Δεν έχετε δικαίωμα να δείτε αυτή τη σελίδα", "app.components.Unauthorized.notAuthorized": "Λυ<PERSON><PERSON><PERSON><PERSON><PERSON>, δεν έχετε δικαίωμα πρόσβασης σε αυτή τη σελίδα.", "app.components.Upload.errorImageMaxSizeExceeded": "Η εικόνα που επιλέξατε είναι μεγαλύτερη από {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Μία ή περισσότερες εικόνες που επιλέξατε είναι μεγαλύτερες από {maxFileSize}MB", "app.components.Upload.onlyOneImage": "Μπορείτε να ανεβάσετε μόνο 1 εικόνα", "app.components.Upload.onlyXImages": "Μπορείτε να ανεβάσετε μόνο {maxItemsCount} εικόνες", "app.components.Upload.remaining": "υπόλοιπο", "app.components.Upload.uploadImageLabel": "Επιλέξτε μια εικόνα (μέγ. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Επιλέξτε μία ή περισσότερες εικόνες", "app.components.UpsellTooltip.tooltipContent": "Αυτή η λειτουργία δεν περιλαμβάνεται στο τρέχον πρόγραμμά σας. Μιλήστε με τον υπεύθυνο επιτυχίας της κυβέρνησής σας ή τον διαχειριστή για να το ξεκλειδώσετε.", "app.components.UserName.anonymous": "Ανώνυμος", "app.components.UserName.anonymousTooltip2": "Αυτ<PERSON><PERSON> ο χρήστης αποφάσισε να ανωνυμοποιήσει τη συνεισφορά του", "app.components.UserName.authorWithNoNameTooltip": "Το όνομά σας έχει δημιουργηθεί αυτόματα επειδή δεν έχετε εισάγει το όνομά σας. Παρακαλούμε ενημερώστε το προφίλ σας αν θέλετε να το αλλάξετε.", "app.components.UserName.deletedUser": "άγνω<PERSON>τος συντάκτης", "app.components.UserName.verified": "Επαληθευμένος", "app.components.VerificationModal.verifyAuth0": "Επαλήθευση με το NemID", "app.components.VerificationModal.verifyBOSA": "Επαλήθευση με itsme ή eID", "app.components.VerificationModal.verifyBosaFas": "Επαλήθευση με itsme ή eID", "app.components.VerificationModal.verifyClaveUnica": "Επαλήθευση με Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Επαλήθευση με Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Επαλήθευση με ID Austria", "app.components.VerificationModal.verifyKeycloak": "Επαλήθευση με ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Επαλήθευση με το MitID", "app.components.VerificationModal.verifyTwoday2": "Επαλήθευση με BankID ή Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Επαληθεύστε την ταυτότητά σας", "app.components.VoteControl.budgetingFutureEnabled": "Μπορείτε να διαθέσετε τον προϋπολογισμό σας ξεκινώντας από τις {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Ο συμμετο<PERSON>ικός προϋπολογισμός δεν είναι προς το παρόν ενεργοποιημένος.", "app.components.VoteControl.budgetingNotPossible": "Η πραγματοποίηση αλλαγών στον προϋπολογισμό σας δεν είναι δυνατή αυτή τη στιγμή.", "app.components.VoteControl.budgetingNotVerified": "Παρακαλούμε {verifyAccountLink} για να συνεχίσετε.", "app.components.VoteInputs._shared.currencyLeft1": "Σας έχει απομείνει {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Έχετε {votesLeft, plural, =0 {δεν σας έχουν απομείνει πιστώσεις} other {# από {totalNumberOfVotes, plural, one {1 πίστωση} other {# πιστώσεις}} που σας έχουν απομείνει}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Έχετε {votesLeft, plural, =0 {δεν σας έχουν απομείνει πόντοι} other {# από {totalNumberOfVotes, plural, one {1 πόντος} other {# πόντοι}} που σας έχουν απομείνει}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Έχετε {votesLeft, plural, =0 {δεν σας έχουν απομείνει μάρκες} other {# από {totalNumberOfVotes, plural, one {1 μάρκα} other {# μάρκες}} που σας έχουν απομείνει}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Έχετε {votesLeft, plural, =0 {δεν έχουν απομείνει ψήφοι} other {# από {totalNumberOfVotes, plural, one {1 ψήφος} other {# ψήφοι}} αριστερά}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Έχετε ήδη υποβάλει τον προϋπολογισμό σας. Για να τον τροποποιήσετε, κάντε κλικ στο \"Τροποποίηση της υποβολής σας\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Έχετε ήδη υποβάλει τον προϋπολογισμό σας. Για να τον τροποποιήσετε, επιστρέψτε στη σελίδα του έργου και κάντε κλικ στην επιλογή \"Τροποποίηση της υποβολής σας\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Ο προϋπολογισμός δεν είναι διαθέσιμος, δεδομένου ότι η φάση αυτή δεν είναι ενεργή.", "app.components.VoteInputs.single.youHaveVotedForX2": "Ψηφίσατε {votes, plural, =0 {# options} one {# option} other {# options}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "Αυτό σημαίνει ότι θα χάσετε όλα τα δεδομένα που σχετίζονται με αυτή την εισαγωγή, όπω<PERSON> σχόλια, αντιδ<PERSON><PERSON><PERSON><PERSON><PERSON>ς και ψήφους. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την είσοδο;", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Ακύρωση", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Επιβεβαίωση", "app.components.admin.SlugInput.resultingURL": "Παραγόμενο URL", "app.components.admin.SlugInput.slugTooltip": "Το slug (μον<PERSON><PERSON><PERSON><PERSON><PERSON> αναγνωριστικ<PERSON> πηγής) είναι το μοναδικό σύνολο λέξεων στο τέλος της διεύθυνσης web της σελίδας ή του URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Εάν αλλάξετε τη διεύθυνση URL, οι σύνδεσμοι προς τη σελίδα που χρησιμοποιούν την παλιά διεύθυνση URL δεν θα λειτουργούν πλέον.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Προσθέστε μια συνθήκη", "app.components.admin.UserFilterConditions.field_email": "Email", "app.components.admin.UserFilterConditions.field_event_attendance": "Εγγρα<PERSON><PERSON><PERSON> εκδηλώσεων", "app.components.admin.UserFilterConditions.field_follow": "Ακολουθήστε το", "app.components.admin.UserFilterConditions.field_lives_in": "Ζει σε", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Έρευνα κοινοτικής παρακολούθησης", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Αλληλεπίδραση με μια εισήγηση με κατάσταση", "app.components.admin.UserFilterConditions.field_participated_in_project": "Συνεισέφερε στο έργο", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Δημοσίευσε κάτι με ετικέτα", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Ημερομηνία εγγραφής", "app.components.admin.UserFilterConditions.field_role": "Ρόλος", "app.components.admin.UserFilterConditions.field_verified": "Επαλήθευση", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ιδεοληψία", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Προτάσεις", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "δεν έχει εγγραφεί σε καμία από αυτές τις εκδηλώσεις", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "δεν έχει εγγραφεί σε καμία εκδήλωση", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "είναι εγγεγραμμένος σε μία από αυτές τις εκδηλώσεις", "app.components.admin.UserFilterConditions.predicate_attends_something": "είναι εγγεγραμμένος σε τουλάχιστον μία εκδήλωση", "app.components.admin.UserFilterConditions.predicate_begins_with": "αρχίζει με", "app.components.admin.UserFilterConditions.predicate_commented_in": "σχολίασε το", "app.components.admin.UserFilterConditions.predicate_contains": "περιέχει", "app.components.admin.UserFilterConditions.predicate_ends_on": "λήγει στις", "app.components.admin.UserFilterConditions.predicate_has_value": "έχει τιμή", "app.components.admin.UserFilterConditions.predicate_in": "πραγματοποίησε οποιαδήποτε ενέργεια", "app.components.admin.UserFilterConditions.predicate_is": "είναι", "app.components.admin.UserFilterConditions.predicate_is_admin": "είναι διαχειριστής", "app.components.admin.UserFilterConditions.predicate_is_after": "είναι μετά", "app.components.admin.UserFilterConditions.predicate_is_before": "είναι πριν", "app.components.admin.UserFilterConditions.predicate_is_checked": "ελέγχεται", "app.components.admin.UserFilterConditions.predicate_is_empty": "είναι άδειο", "app.components.admin.UserFilterConditions.predicate_is_equal": "είναι", "app.components.admin.UserFilterConditions.predicate_is_exactly": "είναι ακριβώς", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "είναι μεγαλύτερο από", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "είναι μεγαλύτερο ή ίσο με", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "είναι κανον<PERSON><PERSON><PERSON>ς χρήστης", "app.components.admin.UserFilterConditions.predicate_is_not_area": "εξαιρεί την περιοχή", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "αποκλείει το φάκελο", "app.components.admin.UserFilterConditions.predicate_is_not_input": "αποκλείει την είσοδο", "app.components.admin.UserFilterConditions.predicate_is_not_project": "εξαιρεί το έργο", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "αποκλείει το θέμα", "app.components.admin.UserFilterConditions.predicate_is_one_of": "είναι ένα από τα", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "μία από τις περιοχές", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "ένας από τους φακέλους", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "μία από τις εισόδους", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "ένα από τα έργα", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "ένα από τα θέματα", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "είναι διαχειριστής έργου", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "είναι μικρ<PERSON>τερος από", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "είναι μικρ<PERSON>τερος ή ίσος με", "app.components.admin.UserFilterConditions.predicate_is_verified": "επαληθεύεται", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "δεν αρχίζει με", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "δεν σχολίασε", "app.components.admin.UserFilterConditions.predicate_not_contains": "δεν περιέχει", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "δεν τελειώνει σε", "app.components.admin.UserFilterConditions.predicate_not_has_value": "δεν έχει τιμή", "app.components.admin.UserFilterConditions.predicate_not_in": "δεν συνεισέφερε", "app.components.admin.UserFilterConditions.predicate_not_is": "δεν είναι", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "δεν είναι διαχειριστής", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "δεν έχει ελεγχθεί", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "δεν είναι άδειο", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "δεν είναι", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "δεν είναι κανονικ<PERSON>ς χρήστης", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "δεν είναι ένας από τους", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "δεν είναι διαχειριστής έργου", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "δεν επαληθεύεται", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "δεν δημοσίευσε εισήγηση", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "δεν αντέδρασε στο σχόλιο", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "δεν αντέδρασε στην είσοδο", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "δεν δήλωσε συμμετοχή σε εκδήλωση", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "δεν έχει λάβει έρευνα", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "δεν συμμετείχε εθελοντικά", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "δεν συμμετείχε στην ψηφοφορία", "app.components.admin.UserFilterConditions.predicate_nothing": "τίποτα", "app.components.admin.UserFilterConditions.predicate_posted_input": "δημοσίευσε εισήγηση", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "αντέδρασε στο σχόλιο", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "αντέδρασε στην είσοδο", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "εγγεγραμμένος σε μια εκδήλωση", "app.components.admin.UserFilterConditions.predicate_something": "κάτι", "app.components.admin.UserFilterConditions.predicate_taken_survey": "έχει λάβει έρευνα", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "προσφέρθηκε εθελοντικά", "app.components.admin.UserFilterConditions.predicate_voted_in3": "συμμετείχε στην ψηφοφορία", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Χ<PERSON>ρ<PERSON><PERSON>τηριστικό", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Κατάσταση", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Τιμή", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Δεν θα λαμβάνετε ειδοποιήσεις για τη συνεισφορά σας", "app.components.anonymousParticipationModal.cancel": "Ακύρωση", "app.components.anonymousParticipationModal.continue": "Συνεχίστε", "app.components.anonymousParticipationModal.participateAnonymously": "Συμμετέχετε ανώνυμα", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "Αυτό θα <b>απ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b> με ασφάλεια <b>το προφίλ σας</b> από τους διαχειριστές, τους διαχειριστές έργων και άλλους κατοίκους για τη συγκεκριμένη συνεισφορά, ώστε κανείς να μην είναι σε θέση να συνδέσει τη συνεισφορά αυτή με εσάς. Οι ανώνυμες συνεισφορές δεν μπορούν να επεξεργαστούν και θεωρούνται οριστικές.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Το να κάνουμε την πλατφόρμα μας ασφαλή για κάθε χρήστη αποτελεί ύψιστη προτεραιότητα για εμάς. Οι λέξεις έχουν σημασία, γι' αυτό παρακαλούμε να είστε ευγενικοί μεταξύ σας.", "app.components.avatar.titleForAccessibility": "Προφίλ του {fullName}", "app.components.customFields.mapInput.removeAnswer": "Αφαίρεση απάντησης", "app.components.customFields.mapInput.undo": "Αναίρεση", "app.components.customFields.mapInput.undoLastPoint": "Αναίρεση του τελευταίου σημείου", "app.components.followUnfollow.follow": "Ακολουθήστε το", "app.components.followUnfollow.followADiscussion": "Ακολουθήστε τη συζήτηση", "app.components.followUnfollow.followTooltipInputPage2": "Ακολουθώντας ενεργοποιεί ενημερώσεις ηλεκτρονικού ταχυδρομείου σχετικά με αλλαγές κατάστασης, επ<PERSON><PERSON>ημες ενημερώσεις και σχόλια. Μπορείτε να {unsubscribeLink} ανά πάσα στιγμή.", "app.components.followUnfollow.followTooltipProjects2": "Ακολουθε<PERSON> ενημέρωση μέσω email για αλλαγές στο έργο. Μπορείτε να {unsubscribeLink} ανά πάσα στιγμή.", "app.components.followUnfollow.unFollow": "Ακολουθήστε το", "app.components.followUnfollow.unsubscribe": "διαγραφή", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "τις κατευθυντήριες γραμμές μας", "app.components.form.ErrorDisplay.next": "Επόμενο", "app.components.form.ErrorDisplay.previous": "Προηγούμενο", "app.components.form.ErrorDisplay.save": "Αποθήκευση", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Ξεκινήστε να πληκτρολογείτε για αναζήτηση με βάση το email ή το όνομα του χρήστη...", "app.components.form.anonymousSurveyMessage2": "Όλες οι απαντήσεις σε αυτή την έρευνα είναι ανώνυμες.", "app.components.form.backToInputManager": "Επιστροφή στον διαχειριστή εισόδου", "app.components.form.backToProject": "Πίσω στο έργο", "app.components.form.components.controls.mapInput.removeAnswer": "Αφαίρεση απάντησης", "app.components.form.components.controls.mapInput.undo": "Αναίρεση", "app.components.form.components.controls.mapInput.undoLastPoint": "Αναίρεση του τελευταίου σημείου", "app.components.form.controls.addressInputAriaLabel": "Εισαγωγή διεύθυνσης", "app.components.form.controls.addressInputPlaceholder6": "Εισάγετε μια διεύθυνση...", "app.components.form.controls.adminFieldTooltip": "Πεδίο ορατό μόνο στους διαχειριστές", "app.components.form.controls.allStatementsError": "Πρέπει να επιλεγεί μια απάντηση για όλες τις δηλώσεις.", "app.components.form.controls.back": "Πίσω", "app.components.form.controls.clearAll": "Εκκαθάριση όλων", "app.components.form.controls.clearAllScreenreader": "Καθα<PERSON><PERSON><PERSON><PERSON><PERSON> όλες τις απαντήσεις από την παραπάνω ερώτηση μήτρας", "app.components.form.controls.clickOnMapMultipleToAdd3": "Κάντε κλικ στο χάρτη για να σχεδιάσετε. Στη συνέχεια, σύρετε τα σημεία για να τα μετακινήσετε.", "app.components.form.controls.clickOnMapToAddOrType": "Κάντε κλικ στο χάρτη ή πληκτρολογήστε μια διεύθυνση παρακάτω για να προσθέσετε την απάντησή σας.", "app.components.form.controls.confirm": "Επιβεβαίωση", "app.components.form.controls.cosponsorsPlaceholder": "Αρχίστε να πληκτρολογείτε ένα όνομα για αναζήτηση", "app.components.form.controls.currentRank": "Τρέχουσα κατάταξη:", "app.components.form.controls.minimumCoordinates2": "Απαιτείται τουλάχιστον {numPoints} σημεία χάρτη.", "app.components.form.controls.noRankSelected": "Δεν έχει επιλεγεί κατάταξη", "app.components.form.controls.notPublic1": "*Η απάντηση αυτή θα κοινοποιηθεί μόνο στους διαχειριστές του έργου και όχι στο κοινό.", "app.components.form.controls.optionalParentheses": "(προαιρετικά)", "app.components.form.controls.rankingInstructions": "Σύρετε και αφήστε τις επιλογές κατάταξης.", "app.components.form.controls.selectAsManyAsYouLike": "*Επιλέξτε όσα θέλετε", "app.components.form.controls.selectBetween": "*Επιλογή μεταξύ των επιλογών {minItems} και {maxItems}", "app.components.form.controls.selectExactly2": "*Επιλέξτε ακριβώς {selectExactly, plural, one {# option} other {# options}}", "app.components.form.controls.selectMany": "*Επιλέξτε όσα θέλετε", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Πατήστε στο χάρτη για να σχεδιάσετε. Στη συνέχεια, σύρετε τα σημεία για να τα μετακινήσετε.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Πατήστε στο χάρτη για να σχεδιάσετε.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Πατήστε στο χάρτη για να προσθέσετε την απάντησή σας.", "app.components.form.controls.tapOnMapToAddOrType": "Πατήστε στο χάρτη ή πληκτρολογήστε μια διεύθυνση παρακάτω για να προσθέσετε την απάντησή σας.", "app.components.form.controls.tapToAddALine": "Πατήστε για να προσθέσετε μια γραμμή", "app.components.form.controls.tapToAddAPoint": "Πατήστε για να προσθέσετε ένα σημείο", "app.components.form.controls.tapToAddAnArea": "Πατήστε για να προσθέσετε μια περιοχή", "app.components.form.controls.uploadShapefileInstructions": "* Ανεβάστε ένα αρχείο zip που περιέχει ένα ή περισσότερα shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "Εάν η τοποθεσία δεν εμφανίζεται μεταξύ των επιλογών καθώς πληκτρολογείτε, μπορείτε να προσθέσετε έγκυρες συντεταγμένες με τη μορφή \"γεωγραφικό πλάτος, γεωγραφικό μήκος\" για να καθορίσετε μια ακριβή τοποθεσία (π.χ.: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} από το {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} εκτός {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} από το {total}, όπου {maxValue} είναι {maxLabel}", "app.components.form.error": "Σφάλμα", "app.components.form.locationGoogleUnavailable": "Δεν ήταν δυνα<PERSON><PERSON> η φόρτωση του πεδίου τοποθεσίας που παρέχεται από τους χάρτες Google.", "app.components.form.progressBarLabel": "Πρ<PERSON><PERSON><PERSON>ος της έρευνας", "app.components.form.submit": "Υποβολή", "app.components.form.submitApiError": "Υπήρξε πρόβλημα με την υποβολή της φόρμας. Ελέγξτε για τυχόν σφάλματα και προσπαθήστε ξανά.", "app.components.form.verifiedBlocked": "Δεν μπορείτε να επεξεργαστείτε αυτό το πεδίο επειδή περιέχει επαληθευμένες πληροφορίες.", "app.components.formBuilder.Page": "Σελίδα", "app.components.formBuilder.accessibilityStatement": "δήλωση προσβασιμότητας", "app.components.formBuilder.addAnswer": "Προσθέστε απάντηση", "app.components.formBuilder.addStatement": "Προσθήκη δήλωσης", "app.components.formBuilder.agree": "Συμφωνώ", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "Εάν έχετε πρόσβαση στο πακέτο τεχνητής νοημοσύνης, θα μπορείτε να συνοψίζετε και να κατηγοριοποιείτε απαντήσεις κειμένου με τεχνητή νοημοσύνη.", "app.components.formBuilder.askFollowUpToggleLabel": "Ζητήστε συνέχεια", "app.components.formBuilder.bad": "Κακό", "app.components.formBuilder.buttonLabel": "Ετικέτα κουμπιού", "app.components.formBuilder.buttonLink": "Σύνδεσ<PERSON>ος κουμπιών", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Ακύρωση", "app.components.formBuilder.category": "Κατηγορία", "app.components.formBuilder.chooseMany": "Επιλέξτε πολλά", "app.components.formBuilder.chooseOne": "Επιλέξτε ένα", "app.components.formBuilder.close": "Κλείστε το", "app.components.formBuilder.closed": "Κλειστό", "app.components.formBuilder.configureMap": "Διαμόρφωση χάρτη", "app.components.formBuilder.confirmLeaveBuilderButtonText": "Ναι, θέλω να φύγω", "app.components.formBuilder.content": "Περιεχόμενο", "app.components.formBuilder.continuePageLabel": "Συνεχίζει να", "app.components.formBuilder.cosponsors": "Συν-χορηγοί", "app.components.formBuilder.default": "Προεπιλογή", "app.components.formBuilder.defaultContent": "Προεπιλεγμένο περιεχόμενο", "app.components.formBuilder.delete": "Διαγραφή", "app.components.formBuilder.deleteButtonLabel": "Διαγραφή", "app.components.formBuilder.description": "Περιγραφή", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Αυτό έχει ήδη προστεθεί στη φόρμα. Το προεπιλεγμένο περιεχόμενο μπορεί να χρησιμοποιηθεί μόνο μία φορά.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Η προσθήκη προσαρμοσμένου περιεχομένου δεν αποτελεί μέρος της τρέχουσας άδειας χρήσης. Επικοινωνήστε με τον διαχειριστή του GovSuccess για να μάθετε περισσότερα σχετικά.", "app.components.formBuilder.disagree": "Διαφωνώ", "app.components.formBuilder.displayAsDropdown": "Εμφάνιση ως dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "Εμφανίστε τις επιλογές σε ένα αναπτυσσόμενο μενού. Εάν έχετε πολλές επιλογές, συνιστάται αυτή η επιλογή.", "app.components.formBuilder.done": "Έγινε", "app.components.formBuilder.drawArea": "Περιοχή κλήρωσης", "app.components.formBuilder.drawRoute": "Διαδρομή κλήρωσης", "app.components.formBuilder.dropPin": "Καρφίτ<PERSON>α πτώσης", "app.components.formBuilder.editButtonLabel": "Επεξεργασία", "app.components.formBuilder.emptyImageOptionError": "Δώστε τουλάχιστον 1 απάντηση. Σημειώστε ότι κάθε απάντηση πρέπει να έχει τίτλο.", "app.components.formBuilder.emptyOptionError": "Δώστε τουλάχιστον 1 απάντηση", "app.components.formBuilder.emptyStatementError": "Δώστε τουλάχιστον 1 δήλωση", "app.components.formBuilder.emptyTitleError": "Δώστε έναν τίτλο ερώτησης", "app.components.formBuilder.emptyTitleMessage": "Δώστε έναν τίτλο για όλες τις απαντήσεις", "app.components.formBuilder.emptyTitleStatementMessage": "Δώστε έναν τίτλο για όλες τις δηλώσεις", "app.components.formBuilder.enable": "Ενεργοποίηση", "app.components.formBuilder.errorMessage": "Υπάρχει πρόβλημα, παρακαλούμε διορθώστε το πρόβλημα για να μπορέσετε να αποθηκεύσετε τις αλλαγές σας.", "app.components.formBuilder.fieldGroup.description": "Περιγραφή (προαιρετική)", "app.components.formBuilder.fieldGroup.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (προαιρετικ<PERSON>)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Επί του παρόντος, οι απαντήσεις σε αυτές τις ερωτήσεις είναι διαθέσιμες μόνο στο εξαγόμενο αρχείο excel στο Input Manager και δεν είναι ορατές στους χρήστες.", "app.components.formBuilder.fieldLabel": "Επιλο<PERSON><PERSON><PERSON> απαντήσεων", "app.components.formBuilder.fieldLabelStatement": "Δηλ<PERSON><PERSON><PERSON><PERSON>ς", "app.components.formBuilder.fileUpload": "Ανέβασμα αρχείου", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Σελίδα με βάση το χάρτη", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Ενσωματώστε χάρτη ως πλαίσιο ή κάντε ερωτήσεις στους συμμετέχοντες με βάση την τοποθεσία.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Για βέλτιστη εμπειρία χρήστη, δεν συνιστούμε την προσθήκη ερωτήσεων για σημεία, διαδρομές ή περιοχές σε σελίδες που βασίζονται σε χάρτες.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Κανονική σελίδα", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Οι λειτουργίες χαρτογράφησης έρευνας δεν περιλαμβάνονται στην τρέχουσα άδειά σας. Επικοινωνήστε με τον GovSuccess Manager σας για να μάθετε περισσότερα.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Τύ<PERSON>ος σελίδας", "app.components.formBuilder.formEnd": "Τέλος φόρμας", "app.components.formBuilder.formField.cancelDeleteButtonText": "Ακύρωση", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Ναι, διαγράψτε τη σελίδα", "app.components.formBuilder.formField.copyNoun": "Αντιγραφή", "app.components.formBuilder.formField.copyVerb": "Αντιγραφή", "app.components.formBuilder.formField.delete": "Διαγραφή", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Η διαγραφή αυτής της σελίδας θα διαγράψει επίσης τη λογική που σχετίζεται με αυτήν. Είστε σίγουροι ότι θέλετε να τη διαγράψετε;", "app.components.formBuilder.formField.deleteResultsInfo": "Αυτό δεν μπορεί να αναιρεθεί", "app.components.formBuilder.goToPageInputLabel": "Στη συνέχεια, η επόμενη σελίδα είναι:", "app.components.formBuilder.good": "Καλή", "app.components.formBuilder.helmetTitle": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τ<PERSON><PERSON> φορμών", "app.components.formBuilder.imageFileUpload": "Ανέβασμα εικόνας", "app.components.formBuilder.invalidLogicBadgeMessage": "Άκυρη λογική", "app.components.formBuilder.labels2": "Ετικ<PERSON>τες (προαιρετικ<PERSON>)", "app.components.formBuilder.labelsTooltipContent2": "Επιλέξτε προαιρετικές ετικέτες για οποιαδήποτε από τις τιμές της γραμμικής κλίμακας.", "app.components.formBuilder.lastPage": "Τερματισμός", "app.components.formBuilder.layout": "Διάταξη", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Είσαι σίγουρος ότι θέλεις να φύγεις;", "app.components.formBuilder.leaveBuilderText": "Έχετε μη αποθηκευμένες αλλαγές. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αποθηκεύστε τις πριν φύγετε. Αν φύγετε, θα χάσετε τις αλλαγές σας.", "app.components.formBuilder.limitAnswersTooltip": "Όταν είναι ενεργοποιημένη, οι ερωτώμενοι πρέπει να επιλέξουν τον καθορισμένο αριθμό απαντήσεων για να προχωρήσουν.", "app.components.formBuilder.limitNumberAnswers": "Περιορισμός του αριθμού των απαντήσεων", "app.components.formBuilder.linePolygonMapWarning2": "Η σχεδίαση γραμμών και πολυγώνων ενδέχεται να μην πληροί τα πρότυπα προσβασιμότητας. Περισσότερες πληροφορίες μπορείτε να βρείτε στο {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Γραμμική κλίμακα", "app.components.formBuilder.locationDescription": "Τοποθεσία", "app.components.formBuilder.logic": "Λογική", "app.components.formBuilder.logicAnyOtherAnswer": "Οποιαδήπ<PERSON>τε άλλη απάντηση", "app.components.formBuilder.logicConflicts.conflictingLogic": "Αντιφατική λογική", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Αυτή η σελίδα περιέχει ερωτήσεις που οδηγούν σε διαφορετικές σελίδες. Εάν οι συμμετέχοντες απαντήσουν σε πολλαπλές ερωτήσεις, θα εμφανιστεί η πιο απομακρυσμένη σελίδα. Βεβαιωθείτε ότι αυτή η συμπεριφορά ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Σε αυτή τη σελίδα εφαρμόζονται πολλαπλοί κανόνες λογικής: λογική πολλαπλών επιλογών, λογική σε επίπεδο σελίδας και λογική μεταξύ ερωτήσεων. Όταν αυτές οι συνθήκες επικαλύπτονται, η λογική των ερωτήσεων θα υπερισχύει της λογικής των σελίδων και θα εμφανίζεται η πιο απομακρυσμένη σελίδα. Ελέγξτε τη λογική για να βεβαιωθείτε ότι ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Αυτή η σελίδα περιέχει μια ερώτηση πολλαπλών επιλογών όπου οι επιλογές οδηγούν σε διαφορετικές σελίδες. Εάν οι συμμετέχοντες επιλέξουν πολλαπλές επιλογές, θα εμφανιστεί η πιο απομακρυσμένη σελίδα. Βεβαιωθείτε ότι αυτή η συμπεριφορά ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Αυτή η σελίδα περιέχει μια ερώτηση πολλαπλών επιλογών όπου οι επιλογές οδηγούν σε διαφορετικές σελίδες και έχει ερωτήσεις που οδηγούν σε άλλες σελίδες. Η πιο απομακρυσμένη σελίδα θα εμφανιστεί εάν οι συνθήκες αυτές επικαλύπτονται. Βεβαιωθείτε ότι αυτή η συμπεριφορά ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Αυτή η σελίδα περιέχει μια ερώτηση πολλαπλών επιλογών όπου οι επιλογές οδηγούν σε διαφορετικές σελίδες και έχει οριστεί λογική τόσο σε επίπεδο σελίδας όσο και σε επίπεδο ερώτησης. Η λογική της ερώτησης θα υπερισχύει και θα εμφανίζεται η πιο απομακρυσμένη σελίδα. Βεβαιωθείτε ότι αυτή η συμπεριφορά ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Αυτή η σελίδα έχει ρυθμίσει τη λογική τόσο σε επίπεδο σελίδας όσο και σε επίπεδο ερώτησης. Η λογική της ερώτησης θα υπερισχύει της λογικής σε επίπεδο σελίδας. Βεβαιωθείτε ότι αυτή η συμπεριφορά ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Αυτή η σελίδα έχει ρυθμίσει τη λογική τόσο σε επίπεδο σελίδας όσο και σε επίπεδο ερώτησης, και πολλαπλές ερωτήσεις οδηγούν σε διαφορετικές σελίδες. Η λογική της ερώτησης θα υπερισχύει και θα εμφανίζεται η πιο απομακρυσμένη σελίδα. Βεβαιωθείτε ότι αυτή η συμπεριφορά ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.logicNoAnswer2": "Δεν απαντήθηκε", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Εάν οποιαδήποτε άλλη απάντηση", "app.components.formBuilder.logicPanelNoAnswer": "Εάν δεν απαντηθεί", "app.components.formBuilder.logicValidationError": "Η λογική δεν μπορεί να παραπέμπει σε προηγούμενες σελίδες", "app.components.formBuilder.longAnswer": "Μεγάλη απάντηση", "app.components.formBuilder.mapConfiguration": "Διαμόρφωση χάρτη", "app.components.formBuilder.mapping": "Χ<PERSON>ρτ<PERSON><PERSON>ρ<PERSON>φηση", "app.components.formBuilder.mappingNotInCurrentLicense": "Οι λειτουργίες χαρτογράφησης έρευνας δεν περιλαμβάνονται στην τρέχουσα άδειά σας. Επικοινωνήστε με τον GovSuccess Manager σας για να μάθετε περισσότερα.", "app.components.formBuilder.matrix": "Μήτρα", "app.components.formBuilder.matrixSettings.columns": "Στήλες", "app.components.formBuilder.matrixSettings.rows": "Σειρές", "app.components.formBuilder.multipleChoice": "Πολλαπλή επιλογή", "app.components.formBuilder.multipleChoiceHelperText": "Εάν πολλαπλές επιλογές οδηγούν σε διαφορετικές σελίδες και οι συμμετέχοντες επιλέξουν περισσότερες από μία, θα εμφανιστεί η πιο απομακρυσμένη σελίδα. Βεβαιωθείτε ότι αυτή η συμπεριφορά ευθυγραμμίζεται με την προβλεπόμενη ροή σας.", "app.components.formBuilder.multipleChoiceImage": "Επιλογή εικόνας", "app.components.formBuilder.multiselect.maximum": "Μέγιστο", "app.components.formBuilder.multiselect.minimum": "Ελάχιστο", "app.components.formBuilder.neutral": "Ουδέτερη", "app.components.formBuilder.newField": "Νέο πεδίο", "app.components.formBuilder.number": "Αριθμός", "app.components.formBuilder.ok": "Εντάξει", "app.components.formBuilder.open": "Ανοίξτε το", "app.components.formBuilder.optional": "Προαιρετικό", "app.components.formBuilder.other": "Άλλα", "app.components.formBuilder.otherOption": "Επιλογή \"Άλλο\"", "app.components.formBuilder.otherOptionTooltip": "Επιτρέψτε στους συμμετέχοντες να εισάγουν μια προσαρμοσμένη απάντηση εάν οι απαντήσεις που παρέχονται δεν ταιριάζουν με την προτίμησή τους.", "app.components.formBuilder.page": "Σελίδα", "app.components.formBuilder.pageCannotBeDeleted": "Αυτή η σελίδα δεν μπορεί να διαγραφεί.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Αυτή η σελίδα δεν μπορεί να διαγραφεί και δεν επιτρέπει την προσθήκη πρόσθετων πεδίων.", "app.components.formBuilder.pageRuleLabel": "Η επόμενη σελίδα είναι:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Εάν δεν προστεθεί καμία λογική, η φόρμα θα ακολουθήσει την κανονική της ροή. Εάν τόσο η σελίδα όσο και οι ερωτήσεις της έχουν λογική, η λογική της ερώτησης θα έχει προτεραιότητα. Βεβαιωθείτε ότι αυτό ευθυγραμμίζεται με την προβλεπόμενη ροή σας Για περισσότερες πληροφορίες, επισκεφθείτε τη διεύθυνση {supportPageLink}.", "app.components.formBuilder.preview": "Προεπισκόπηση:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Οι συν-χ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δεν εμφανίζονται στο PDF που κατεβάζετε και δεν υποστηρίζονται για εισαγωγή μέσω του FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Οι ερωτήσεις μεταφόρτωσης αρχείων εμφανίζονται ως μη υποστηριζόμενες στο κατεβασμένο PDF και δεν υποστηρίζονται για εισαγωγή μέσω του FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Οι ερωτήσεις χαρτογράφησης εμφανίζονται στο κατεβασμένο PDF, αλλά τα επίπεδα δεν θα είναι ορατά. Οι ερωτήσεις χαρτογράφησης δεν υποστηρίζονται για εισαγωγή μέσω του FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Οι ερωτήσεις μήτρας εμφανίζ<PERSON>ντα<PERSON> στο κατεβασμένο PDF, αλλά προς το παρόν δεν υποστηρίζονται για εισαγωγή μέσω του FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Οι τίτλοι και οι περιγραφές των σελίδων εμφανίζονται ως επικεφαλίδα ενότητας στο PDF που μεταφορτώνεται.", "app.components.formBuilder.printSupportTooltip.ranking": "Οι ερωτήσεις κατάταξης εμφανίζονται στο κατεβασμένο PDF, αλλά δεν υποστηρίζονται επί του παρόντος για εισαγωγή μέσω του FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Οι ετικέτες εμφανίζονται ως μη υποστηριζόμενες στο κατεβασμένο PDF και δεν υποστηρίζονται για εισαγωγή μέσω του FormSync.", "app.components.formBuilder.proposedBudget": "Προτεινόμενος προϋπολογισμός", "app.components.formBuilder.question": "Ερώτηση", "app.components.formBuilder.questionCannotBeDeleted": "Αυτή η ερώτηση δεν μπορεί να διαγραφεί.", "app.components.formBuilder.questionDescriptionOptional": "Περιγραφή της ερώτησης (προαιρετική)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ερώτησης", "app.components.formBuilder.randomize": "Τυχαία επιλογή", "app.components.formBuilder.randomizeToolTip": "Η σειρά των απαντήσεων θα είναι τυχαία ανά χρήστη", "app.components.formBuilder.range": "Εύρ<PERSON>", "app.components.formBuilder.ranking": "Κατάταξη", "app.components.formBuilder.rating": "Αξιολόγηση", "app.components.formBuilder.removeAnswer": "Αφαίρεση απάντησης", "app.components.formBuilder.required": "Απαιτούμενο", "app.components.formBuilder.requiredToggleLabel": "Να απαιτείται η απάντηση στην ερώτηση αυτή", "app.components.formBuilder.ruleForAnswerLabel": "Αν η απάντηση είναι:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Εάν οι απαντήσεις περιλαμβάνουν:", "app.components.formBuilder.save": "Αποθήκευση", "app.components.formBuilder.selectRangeTooltip": "Επιλέξτε τη μέγιστη τιμή για την κλίμακά σας.", "app.components.formBuilder.sentiment": "Κλίμακα συναισθήματος", "app.components.formBuilder.shapefileUpload": "Ανέβασμα shapefile Esri", "app.components.formBuilder.shortAnswer": "Σύντομη απάντηση", "app.components.formBuilder.showResponseToUsersToggleLabel": "Εμφάνιση απάντησης στους χρήστες", "app.components.formBuilder.singleChoice": "Ενιαία επιλογή", "app.components.formBuilder.staleDataErrorMessage2": "Υπήρξε ένα πρόβλημα. Αυτή η φόρμα εισαγωγής έχει αποθηκευτεί πιο πρόσφατα κάπου αλλού. Αυτό μπορεί να οφείλεται στο γεγονός ότι εσείς ή κάποιος άλλος χρήστης την έχει ανοίξει για επεξεργασία σε άλλο παράθυρο του προγράμματος περιήγησης. Παρακαλούμε ανανεώστε τη σελίδα για να λάβετε την πιο πρόσφατη φόρμα και στη συνέχεια κάντε ξανά τις αλλαγές σας.", "app.components.formBuilder.stronglyAgree": "Συμφωνώ απόλυτα", "app.components.formBuilder.stronglyDisagree": "Διαφωνώ απόλυτα", "app.components.formBuilder.supportArticleLinkText": "αυτή τη σελίδα", "app.components.formBuilder.tags": "Ετικέτες", "app.components.formBuilder.title": "Τίτλος", "app.components.formBuilder.toLabel": "στο", "app.components.formBuilder.unsavedChanges": "Έχετε μη αποθηκευμένες αλλαγές", "app.components.formBuilder.useCustomButton2": "Χρήση προσαρμοσμένου κουμπιού σελίδας", "app.components.formBuilder.veryBad": "Πολύ κακό", "app.components.formBuilder.veryGood": "Πολύ καλά", "app.components.ideas.similarIdeas.engageHere": "Συμμετέχετε εδώ", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Δεν βρέθηκαν παρόμοιες υποβολές.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Βρήκαμε παρόμοιες υποταγές - η ενασχόληση μαζί τους μπορεί να τις βοηθήσει να γίνουν ισχυρότερες!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Παρόμοιες υποβολές έχουν ήδη αναρτηθεί:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Ψάχνετε για παρόμοιες υποβολές ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Λιγότερο από μία ημέρα} one {# ημέρα} other {# ημέρες}} για να ολοκληρωθεί", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  Εβδομάδες απομένουν έως την ολοκλήρωση", "app.components.screenReaderCurrency.AED": "Ντιρ<PERSON><PERSON><PERSON> Ηνωμένων Αραβικών Εμιράτων", "app.components.screenReaderCurrency.AFN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν<PERSON>ς", "app.components.screenReaderCurrency.ALL": "Αλβανικό λεκ", "app.components.screenReaderCurrency.AMD": "Αρμενικό δράμα", "app.components.screenReaderCurrency.ANG": "Γ<PERSON><PERSON>ούλντερ των Ολλαν<PERSON>ικ<PERSON>ν Αντιλλών", "app.components.screenReaderCurrency.AOA": "Αγκ<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ARS": "Πέσο Αργεντινής", "app.components.screenReaderCurrency.AUD": "Δολ<PERSON><PERSON>ι<PERSON> Αυστραλίας", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Αζερμπαϊτζάν Manat", "app.components.screenReaderCurrency.BAM": "Βοσνία-Ερζεγοβίνη Μετατρέψιμο σήμα", "app.components.screenReaderCurrency.BBD": "Δολ<PERSON><PERSON><PERSON><PERSON>ντος", "app.components.screenReaderCurrency.BDT": "Μπαγ<PERSON><PERSON>αν<PERSON><PERSON>ς Τάκα", "app.components.screenReaderCurrency.BGN": "Βουλγαρικό Lev", "app.components.screenReaderCurrency.BHD": "Δην<PERSON><PERSON><PERSON><PERSON>ν", "app.components.screenReaderCurrency.BIF": "Φράγκ<PERSON>π<PERSON>υρούντι", "app.components.screenReaderCurrency.BMD": "Δολ<PERSON><PERSON><PERSON><PERSON>δων", "app.components.screenReaderCurrency.BND": "Μπρουνέι Δολάριο", "app.components.screenReaderCurrency.BOB": "Βολιβιανό Boliviano", "app.components.screenReaderCurrency.BOV": "Βολιβιανή Mvdol", "app.components.screenReaderCurrency.BRL": "Ρεάλ Βραζιλίας", "app.components.screenReaderCurrency.BSD": "Δολ<PERSON><PERSON><PERSON><PERSON>ς", "app.components.screenReaderCurrency.BTN": "Ngultrum του Μπουτάν", "app.components.screenReaderCurrency.BWP": "<PERSON>ula Μποτσουάνα", "app.components.screenReaderCurrency.BYR": "Λευκορωσικό ρούβλι", "app.components.screenReaderCurrency.BZD": "Δολ<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CAD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CDF": "<PERSON>ρά<PERSON><PERSON><PERSON>νγκό", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Ελβετικό φράγκο", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Χιλιανή λογιστική μονάδα (UF)", "app.components.screenReaderCurrency.CLP": "Χιλιαν<PERSON> πέσο", "app.components.screenReaderCurrency.CNY": "Κινέζι<PERSON>α γιουάν", "app.components.screenReaderCurrency.COP": "Κολομβιανό πέσο", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Κόστα Ρίκα Colón", "app.components.screenReaderCurrency.CRE": "Πίστωση", "app.components.screenReaderCurrency.CUC": "Κουβ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μετατρέψιμο πέσο", "app.components.screenReaderCurrency.CUP": "Κουβα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πέσο", "app.components.screenReaderCurrency.CVE": "Escudo του Πράσινου Ακρωτηρίου", "app.components.screenReaderCurrency.CZK": "Κορόνα Τσεχίας", "app.components.screenReaderCurrency.DJF": "Φράγκο Τζιμπουτί", "app.components.screenReaderCurrency.DKK": "Δανική κορώνη", "app.components.screenReaderCurrency.DOP": "Δομινικαν<PERSON> πέσο", "app.components.screenReaderCurrency.DZD": "Δηνάριο Αλγερίας", "app.components.screenReaderCurrency.EGP": "Αιγυπτιακή λίρα", "app.components.screenReaderCurrency.ERN": "Ερυθραία Nakfa", "app.components.screenReaderCurrency.ETB": "Αιθιοπ<PERSON><PERSON><PERSON> Birr", "app.components.screenReaderCurrency.EUR": "Ευρ<PERSON>", "app.components.screenReaderCurrency.FJD": "Δολ<PERSON><PERSON><PERSON><PERSON>ζ<PERSON>", "app.components.screenReaderCurrency.FKP": "Νήσοι Φόκλαντ Λίρα", "app.components.screenReaderCurrency.GBP": "Βρετανική λίρα", "app.components.screenReaderCurrency.GEL": "Γεωργιανό Λάρι", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON> της <PERSON>ς", "app.components.screenReaderCurrency.GIP": "Λίρα Γιβ<PERSON>λτάρ", "app.components.screenReaderCurrency.GMD": "Γκάμπια Νταλάσι", "app.components.screenReaderCurrency.GNF": "Φράγκ<PERSON>ς", "app.components.screenReaderCurrency.GTQ": "Guatemalan <PERSON>", "app.components.screenReaderCurrency.GYD": "Δο<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "app.components.screenReaderCurrency.HKD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ονγκ", "app.components.screenReaderCurrency.HNL": "Lempira Ονδούρας", "app.components.screenReaderCurrency.HRK": "Κροατι<PERSON><PERSON> Κούνα", "app.components.screenReaderCurrency.HTG": "Αϊτινή Go<PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "Ουγγρικό φιορίνι", "app.components.screenReaderCurrency.IDR": "Ινδονησιακή ρουπία", "app.components.screenReaderCurrency.ILS": "Ισραηλινό νέο σέκελ", "app.components.screenReaderCurrency.INR": "Ινδική ρουπία", "app.components.screenReaderCurrency.IQD": "Ιρακινό δηνάριο", "app.components.screenReaderCurrency.IRR": "Ιρανική Ριάλ", "app.components.screenReaderCurrency.ISK": "Ισλανδική Króna", "app.components.screenReaderCurrency.JMD": "Δολ<PERSON><PERSON><PERSON><PERSON>ζαμάικα", "app.components.screenReaderCurrency.JOD": "Δην<PERSON><PERSON><PERSON><PERSON>ς", "app.components.screenReaderCurrency.JPY": "Ιαπωνικό γεν", "app.components.screenReaderCurrency.KES": "Σιλίνγ<PERSON>ένυ<PERSON>ς", "app.components.screenReaderCurrency.KGS": "Κιρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Som", "app.components.screenReaderCurrency.KHR": "Ριέλ Καμπότζης", "app.components.screenReaderCurrency.KMF": "Φράγκο των Κομορών", "app.components.screenReaderCurrency.KPW": "Γ<PERSON>υ<PERSON>ν Βόρειας Κορέ<PERSON>ς", "app.components.screenReaderCurrency.KRW": "<PERSON><PERSON>υ<PERSON><PERSON>τ<PERSON>ας <PERSON>ο<PERSON>ς", "app.components.screenReaderCurrency.KWD": "Δην<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Δολάριο Νήσων Καϊμάν", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Λίρα <PERSON>ου", "app.components.screenReaderCurrency.LKR": "Ρουπία Σρι Λάνκα", "app.components.screenReaderCurrency.LRD": "Δολάρι<PERSON>ιβ<PERSON>ρ<PERSON>ς", "app.components.screenReaderCurrency.LSL": "Λεσότ<PERSON> Lot<PERSON>", "app.components.screenReaderCurrency.LTL": "Λιθουανικά Λίτα", "app.components.screenReaderCurrency.LVL": "Λετονικ<PERSON> Λατς", "app.components.screenReaderCurrency.LYD": "Λιβυκό δηνάριο", "app.components.screenReaderCurrency.MAD": "Μαροκινό Ντιρχ<PERSON>", "app.components.screenReaderCurrency.MDL": "Μολδαβικό λεύ", "app.components.screenReaderCurrency.MGA": "Μαδαγασκάρη Ariary", "app.components.screenReaderCurrency.MKD": "Μακεδονικ<PERSON> δηνάριο", "app.components.screenReaderCurrency.MMK": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MNT": "Μογγολικ<PERSON>", "app.components.screenReaderCurrency.MOP": "Pataca <PERSON>", "app.components.screenReaderCurrency.MRO": "Μαυριταν<PERSON><PERSON><PERSON> Ouguiya", "app.components.screenReaderCurrency.MUR": "Ρουπία Μαυρικίου", "app.components.screenReaderCurrency.MVR": "Μαλδ<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MWK": "Κουάτσα Μαλάουι", "app.components.screenReaderCurrency.MXN": "Μεξικά<PERSON><PERSON><PERSON><PERSON> πέσο", "app.components.screenReaderCurrency.MXV": "Μεξικανική Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Ρινγκίτ Μαλαισίας", "app.components.screenReaderCurrency.MZN": "Με<PERSON>ι<PERSON><PERSON><PERSON>ζαμβ<PERSON>ης", "app.components.screenReaderCurrency.NAD": "Δολ<PERSON><PERSON><PERSON><PERSON> Ναμίμπια", "app.components.screenReaderCurrency.NGN": "Νιγηριανή Νάιρα", "app.components.screenReaderCurrency.NIO": "Νικαράγ<PERSON>υα Córdoba", "app.components.screenReaderCurrency.NOK": "Νορβηγική κορώνη", "app.components.screenReaderCurrency.NPR": "Ρουπία Νεπάλ", "app.components.screenReaderCurrency.NZD": "Δολάριο Νέας Ζηλανδίας", "app.components.screenReaderCurrency.OMR": "Ριάλ Ομάν", "app.components.screenReaderCurrency.PAB": "Μπαλμπόα του Παναμά", "app.components.screenReaderCurrency.PEN": "Περουβιανό Sol", "app.components.screenReaderCurrency.PGK": "Κίνα Παπούα Νέας Γουινέας", "app.components.screenReaderCurrency.PHP": "Φιλιππιν<PERSON><PERSON>ι<PERSON><PERSON> πέσο", "app.components.screenReaderCurrency.PKR": "Πακιστανική ρουπία", "app.components.screenReaderCurrency.PLN": "Πολωνικά Złoty", "app.components.screenReaderCurrency.PYG": "Παραγουάη Γκουαρανί", "app.components.screenReaderCurrency.QAR": "Ριάλ Κατάρ", "app.components.screenReaderCurrency.RON": "Ρουμάνικο λεύ", "app.components.screenReaderCurrency.RSD": "Δην<PERSON><PERSON>ι<PERSON>ρβ<PERSON>ς", "app.components.screenReaderCurrency.RUB": "Ρωσικό ρούβλι", "app.components.screenReaderCurrency.RWF": "Φράγκο Ρουάντα", "app.components.screenReaderCurrency.SAR": "Σαουδαραβικό Ριάλ", "app.components.screenReaderCurrency.SBD": "Δολαρί<PERSON><PERSON> Νήσων Σολομώντος", "app.components.screenReaderCurrency.SCR": "Ρουπία Σεϋχελλών", "app.components.screenReaderCurrency.SDG": "Λίρ<PERSON>ν", "app.components.screenReaderCurrency.SEK": "Σουηδική κορώνα", "app.components.screenReaderCurrency.SGD": "Σιγκαπούρη Δολάριο", "app.components.screenReaderCurrency.SHP": "Αγία Ελένη Λίρα", "app.components.screenReaderCurrency.SLL": "Σιέρα Λεόνε Λεόνε", "app.components.screenReaderCurrency.SOS": "Σομαλικό σιλίνγκ", "app.components.screenReaderCurrency.SRD": "Σουριν<PERSON><PERSON> δολάριο", "app.components.screenReaderCurrency.SSP": "Λίρα Νοτίου Σουδάν", "app.components.screenReaderCurrency.STD": "Σάο Τομέ και Πρίνσιπε Dobra", "app.components.screenReaderCurrency.SYP": "Συριακή λίρα", "app.components.screenReaderCurrency.SZL": "Σουάζι Lilangeni", "app.components.screenReaderCurrency.THB": "Μπατ Ταϊλάνδης", "app.components.screenReaderCurrency.TJS": "Τατ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Σομόνι", "app.components.screenReaderCurrency.TMT": "Τουρκμενιστ<PERSON>ν Manat", "app.components.screenReaderCurrency.TND": "Δην<PERSON><PERSON><PERSON><PERSON>η<PERSON>ας", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "Τουρκική λίρα", "app.components.screenReaderCurrency.TTD": "Δολ<PERSON><PERSON>ι<PERSON> Τρινιντάντ και Τομπάγκο", "app.components.screenReaderCurrency.TWD": "Νέο δολάριο Ταϊβάν", "app.components.screenReaderCurrency.TZS": "Σιλίνγκ Τανζανίας", "app.components.screenReaderCurrency.UAH": "Ουκρανική Γρίβνα", "app.components.screenReaderCurrency.UGX": "Ουγκάντα σιλίνγκ", "app.components.screenReaderCurrency.USD": "Δολά<PERSON>ιο των Ηνωμένων Πολιτειών", "app.components.screenReaderCurrency.USN": "Δολ<PERSON><PERSON>ι<PERSON> των Ηνωμένων Πολιτειων (Επόμενη ημέρα)", "app.components.screenReaderCurrency.USS": "Δολ<PERSON><PERSON>ι<PERSON> των Ηνωμένων Πολιτειων (αυθημερόν)", "app.components.screenReaderCurrency.UYI": "Ουρουγουάη Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Πέσο Ουρουγουάης", "app.components.screenReaderCurrency.UZS": "Ουζμπε<PERSON>ιστάν Som", "app.components.screenReaderCurrency.VEF": "Βενεζουέλα Bolívar", "app.components.screenReaderCurrency.VND": "Βιετναμέζικ<PERSON>", "app.components.screenReaderCurrency.VUV": "Βανουάτου Vatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Κεντρικό αφρικανικό φράγκο CFA", "app.components.screenReaderCurrency.XAG": "Άργυρος (μία ουγγιά troy)", "app.components.screenReaderCurrency.XAU": "<PERSON>ρυ<PERSON><PERSON><PERSON> (μία ουγγιά)", "app.components.screenReaderCurrency.XBA": "Ευρωπαϊκή σύνθετη μονάδα (EURCO)", "app.components.screenReaderCurrency.XBB": "Ευρωπαϊκή Νομισματική Μονάδα (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Ευρωπαϊκή Μονάδα Λογαριασμού 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Ευρωπαϊκή Μονάδα Λογαριασμού 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Δολάριο Ανατολικής Καραϊβικής", "app.components.screenReaderCurrency.XDR": "Ειδικά τραβηκτικά δικαιώματα", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Φράγκο CFA Δυτικής Αφρικής", "app.components.screenReaderCurrency.XPD": "Παλλάδιο (μία ουγγιά τροίας)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Πλατίνα (μία ουγγιά troy)", "app.components.screenReaderCurrency.XTS": "Κωδικο<PERSON> που προορίζονται ειδικά για σκοπούς δοκιμών", "app.components.screenReaderCurrency.XXX": "Δεν υπάρχει νόμισμα", "app.components.screenReaderCurrency.YER": "Ριάλ Υεμένης", "app.components.screenReaderCurrency.ZAR": "Ραντ Νότιας Αφρικής", "app.components.screenReaderCurrency.ZMW": "Ζάμπια Κουάτσα", "app.components.screenReaderCurrency.amount": "Ποσό", "app.components.screenReaderCurrency.currency": "Νόμισμα", "app.components.trendIndicator.lastQuarter2": "τελευταίο τρίμηνο", "app.containers.AccessibilityStatement.applicability": "Αυτή η δήλωση προσβασιμότητας ισχύει για ένα {demoPlatformLink} που είναι αντιπροσωπευτικό αυτού του ιστότοπου, χρησιμοποιεί τον ίδιο πηγαίο κώδικα και έχει την ίδια λειτουργικότητα.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αξιολόγησης", "app.containers.AccessibilityStatement.assesmentText2022": "Η προσβασιμότητα αυτού του ιστότοπου αξιολογήθηκε από εξωτερικό φορέα που δεν συμμετείχε στη διαδικασία σχεδιασμού και ανάπτυξης. Η συμμόρφωση του προαναφερθέντος {demoPlatformLink} μπορεί να διαπιστωθεί σε αυτό το {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "μπορείτε να αλλάξετε τις προτιμήσεις σας", "app.containers.AccessibilityStatement.changePreferencesText": "Ανά πάσα στιγμή, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Εξαιρέσεις συμμόρφωσης", "app.containers.AccessibilityStatement.conformanceStatus": "Κατάσταση συμμόρφωσης", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Προσπαθούμε να καταστήσουμε το περιεχόμενό μας χωρίς αποκλεισμούς για όλους. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, σε ορισμένες περιπτώσεις μπορεί να υπάρχει μη προσβάσιμο περιεχόμενο στην πλατφόρμα, όπως περιγράφεται παρακάτω:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "δικτυ<PERSON><PERSON><PERSON>ς τόπος επίδειξης", "app.containers.AccessibilityStatement.email": "Ηλεκτρονικό ταχυδρομείο:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Ενσωματωμένα εργαλεία έρευνας", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Τα ενσωματωμένα εργαλεία έρευνας που είναι διαθέσιμα για χρήση σε αυτή την πλατφόρμα είναι λογισμικό τρίτων και ενδέχεται να μην είναι προσβάσιμα.", "app.containers.AccessibilityStatement.exception_1": "Οι πλατφόρμες ψηφιακής δέσμευσής μας διευκολύνουν το περιεχόμενο που δημιουργείται από χρήστες και δημοσιεύεται από άτομα και οργανισμούς. Είναι πιθανό να μεταφορτώνονται στην πλατφόρμα ως συνημμένα αρχεία PDF, εικόνες ή άλλοι τύποι αρχείων, συμπεριλαμβανομένων των πολυμέσων, ή να προστίθενται σε πεδία κειμένου από τους χρήστες της πλατφόρμας. Τα έγγραφα αυτά ενδέχεται να μην είναι πλήρως προσβάσιμα.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Καλωσορίζουμε την ανατροφοδότησή σας σχετικά με την προσβασιμότητα αυτού του ιστότοπου. Παρακαλούμε επικοινωνήστε μαζί μας με μία από τις ακόλουθες μεθόδους:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Διαδικα<PERSON><PERSON><PERSON> ανατροφοδότησης", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Βρυξέλλες, Βέλγιο", "app.containers.AccessibilityStatement.headTitle": "Δήλωση προσβασιμότητας | {orgName}", "app.containers.AccessibilityStatement.intro2022": "Το {goVocalLink} δεσμεύεται να παρέχει μια πλατφόρμα που είναι προσβάσιμη σε όλους τους χρήστες, ανεξ<PERSON>ρτήτως τεχνολογίας ή ικανότητας. Τα τρέχοντα σχετικά πρότυπα προσβασιμότητας τηρούνται στις συνεχείς προσπάθειές μας να μεγιστοποιήσουμε την προσβασιμότητα και τη χρηστικότητα των πλατφορμών μας για όλους τους χρήστες.", "app.containers.AccessibilityStatement.mapping": "Χ<PERSON>ρτ<PERSON><PERSON>ρ<PERSON>φηση", "app.containers.AccessibilityStatement.mapping_1": "Οι χάρτες στην πλατφόρμα πληρούν εν μέρει τα πρότυπα προσβασιμότητας. Η έκταση του χάρτη, το ζουμ και τα widgets UI μπορούν να ελέγχονται με τη χρήση πληκτρολογίου κατά την προβολή χαρτών. Οι διαχειριστές μπορούν επίσης να διαμορφώσουν το στυλ των επιπέδων χαρτών στο back office ή χρησιμοποιώντας την ενσωμάτωση της <PERSON>, για να δημιουργήσουν πιο προσβάσιμες παλέτες χρωμάτων και συμβολισμούς. Η χρήση διαφορετικών στυλ γραμμών ή πολυγώνων (π.χ. διακεκομμένες γραμμές) θα βοηθήσει επίσης στη διαφοροποίηση των επιπέδων χάρτη, όπου είναι δυνατόν, και παρόλο που το εν λόγω στυλ δεν μπορεί να ρυθμιστεί στην πλατφόρμα μας αυτή τη στιγμή, μπορεί να ρυθμιστεί εάν χρησιμοποιείτε χάρτες με την ενσωμάτωση της Esri.", "app.containers.AccessibilityStatement.mapping_2": "Οι χάρτες της πλατφόρμας δεν είναι πλήρως προσβάσιμοι, καθώς δεν παρουσιάζουν ακουστικά τους βασικούς χάρτες, τα επίπεδα χαρτών ή τις τάσεις των δεδομένων στους χρήστες που χρησιμοποιούν προγράμματα ανάγνωσης οθόνης. Οι πλήρως προσβάσιμοι χάρτες θα πρέπει να παρουσιάζουν ακουστικά τα στρώματα του χάρτη και να περιγράφουν τυχόν σχετικές τάσεις στα δεδομένα. Επιπλέον, η σχεδίαση χαρτών με γραμμές και πολύγωνα στις έρευνες δεν είναι προσβάσιμη, καθώς τα σχήματα δεν μπορούν να σχεδιαστούν με τη χρήση πληκτρολογίου. Εναλλακτικές μέθοδοι εισαγωγής δεν είναι διαθέσιμες προς το παρόν λόγω τεχνικής πολυπλοκότητας.", "app.containers.AccessibilityStatement.mapping_3": "Για να καταστήσετε τη σχεδίαση χαρτών με γραμμές και πολύγωνα πιο προσιτή, συνιστούμε να συμπεριλάβετε μια εισαγωγή ή επεξήγηση στην ερώτηση της έρευνας ή στην περιγραφή της σελίδας σχετικά με το τι δείχνει ο χάρτης και τυχόν σχετικές τάσεις. Ε<PERSON><PERSON><PERSON><PERSON>έον, θα μπορούσε να προβλεφθεί μια ερώτηση κειμένου σύντομης ή μακράς απάντησης, ώστε οι ερωτηθέντες να μπορούν να περιγράψουν την απάντησή τους με απλά λόγια, αν χρειαστεί (αντί να κάνουν κλικ στο χάρτη). Συνιστούμε επίσης να συμπεριληφθούν στοιχεία επικοινωνίας για τον υπεύθυνο του έργου, ώστε οι ερωτηθέντες που δεν μπορούν να συμπληρώσουν μια ερώτηση χάρτη να μπορούν να ζητήσουν μια εναλλακτική μέθοδο για να απαντήσουν στην ερώτηση (π.χ. βιντεοσκοπημένη συνάντηση).", "app.containers.AccessibilityStatement.mapping_4": "Για τα έργα και τις προτάσεις I<PERSON>, υπάρχει μια επιλογή για την εμφάνιση των εισροών σε προβολή χάρτη, η οποία δεν είναι προσβάσιμη. Ω<PERSON><PERSON><PERSON><PERSON><PERSON>, για αυτές τις μεθόδους υπάρχει διαθέσιμη μια εναλλακτική προβολή λίστας των εισροών, η οποία είναι προσβάσιμη.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Τα διαδικτ<PERSON><PERSON><PERSON><PERSON> σεμινάριά μας διαθέτουν ένα στοιχείο ζωντανής ροής βίντεο, το οποίο επί του παρόντος δεν υποστηρίζει υπότιτλους.", "app.containers.AccessibilityStatement.pageDescription": "Δήλωση σχετικά με την προσβασιμότητα του παρόντος δικτυακού τόπου", "app.containers.AccessibilityStatement.postalAddress": "Ταχυδρομική διεύθυνση:", "app.containers.AccessibilityStatement.publicationDate": "Ημερομηνία δημοσίευσης", "app.containers.AccessibilityStatement.publicationDate2024": "Η παρούσα δήλωση προσβασιμότητας δημοσιεύθηκε στις 21 Αυγούστου 2024.", "app.containers.AccessibilityStatement.responsiveness": "Στ<PERSON><PERSON><PERSON> μας είναι να απαντάμε στην ανατροφοδότηση εντός 1-2 εργά<PERSON>ιμων ημερών.", "app.containers.AccessibilityStatement.statusPageText": "σελίδα κατάστασης", "app.containers.AccessibilityStatement.technologiesIntro": "Η προσβασιμότητα αυτού του ιστότοπου βασίζεται στη λειτουργία των ακόλουθων τεχνολογιών:", "app.containers.AccessibilityStatement.technologiesTitle": "Τεχνολογίες", "app.containers.AccessibilityStatement.title": "Δήλωση προσβασιμότητας", "app.containers.AccessibilityStatement.userGeneratedContent": "Περιεχόμενο που δημιουργείται από χρήστες", "app.containers.AccessibilityStatement.workshops": "Σεμινάρια", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Επιλέξτε έργο", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Η χρήση του Content Builder θα σας επιτρέψει να χρησιμοποιήσετε πιο προηγμένες επιλογές διάταξης. Για τις γλώσσες στις οποίες δεν υπάρχει διαθέσιμο περιεχόμενο στον κατασκευαστή περιεχομένου, θα εμφανιστεί το κανονικό περιεχόμενο της περιγραφής του έργου.", "app.containers.AdminPage.ProjectDescription.linkText": "Επεξεργασία περιγραφής στο Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "Κάτι πήγε στρα<PERSON><PERSON> κατά την αποθήκευση της περιγραφής του έργου.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Χρήση του Content Builder για περιγραφή", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Η χρήση του Content Builder θα σας επιτρέψει να χρησιμοποιήσετε πιο προηγμένες επιλογές διάταξης.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Προβολή έργου", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Τέλος της έρευνας", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Δημιουργία μιας έξυπνης ομάδας", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Οι χρήστες που πληρούν όλες τις παρακάτω προϋποθέσεις θα προστεθούν αυτόματα στην ομάδα:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Παρέχετε τουλάχιστον έναν κανόνα", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Ορισμένες συνθήκες είναι ελλιπείς", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Αποθήκευση ομάδας", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Η διαμόρφωση των έξυπνων ομάδων δεν αποτελεί μέρος της τρέχουσας άδειας χρήσης. Επικοινωνήστε με τον GovSuccess Manager σας για να μάθετε περισσότερα σχετικά.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Δώστε ένα όνομα ομάδας", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Η επαλήθευση είναι απενεργοποιημένη για την πλατφόρμα σας, καταργήστε τον κανόνα επαλήθευσης ή επικοινωνήστε με την υποστήριξη.", "app.containers.App.appMetaDescription": "Καλώς ήρθατε στην διαδικτυακή πλατφόρμα συμμετοχής του {orgName}. Εξερευνήστε τοπικά έργα και συμμετέχετε στη συζήτηση!", "app.containers.App.loading": "Φόρτωση...", "app.containers.App.metaTitle1": "Πλατφόρμα εμπλοκής των πολιτών | {orgName}", "app.containers.App.skipLinkText": "Μετάβαση στο κύριο περιεχόμενο", "app.containers.AreaTerms.areaTerm": "περιοχή", "app.containers.AreaTerms.areasTerm": "περιοχές", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Ένας λογαριασμός με αυτό το email υπάρχει ήδη. Μπορείτε να αποσυνδεθείτε, να συνδεθείτε με αυτή τη διεύθυνση ηλεκτρονικού ταχυδρομείου και να επαληθεύσετε το λογαριασμό σας στη σελίδα ρυθμίσεων.", "app.containers.Authentication.steps.AccessDenied.close": "Κλείστε το", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Δεν πληροίτε τις προϋποθέσεις για να συμμετάσχετε σε αυτή τη διαδικασία.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Επιστροφή στην επαλήθευση ενιαίας σύνδεσης", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Παρακαλούμε εισάγετε ένα διακριτικό", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Έχετε ήδη λογαριασμό; {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Συνδεθείτε", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Ηλεκτρον<PERSON><PERSON><PERSON> μηνύματα σε αυτή την κατηγορία", "app.containers.CampaignsConsentForm.messageError": "Υπήρξε σφάλμα κατά την αποθήκευση των προτιμήσεων ηλεκτρονικού ταχυδρομείου σας.", "app.containers.CampaignsConsentForm.messageSuccess": "Οι προτιμήσεις ηλεκτρονικού ταχυδρομείου σας έχουν αποθηκευτεί.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Τι είδους ειδοποιήσεις ηλεκτρονικού ταχυδρομείου θέλετε να λαμβάνετε;", "app.containers.CampaignsConsentForm.notificationsTitle": "Ειδοποιήσεις", "app.containers.CampaignsConsentForm.submit": "Αποθήκευση", "app.containers.ChangeEmail.backToProfile": "Πίσω στις ρυθμίσεις προφίλ", "app.containers.ChangeEmail.confirmationModalTitle": "Επιβεβαιώστε το email σας", "app.containers.ChangeEmail.emailEmptyError": "Δώστε μια διεύθυνση ηλεκτρονικού ταχυδρομείου", "app.containers.ChangeEmail.emailInvalidError": "Δώστε μια διεύθυνση ηλεκτρονικού ταχυδρομείου στη σωστή μορφή, για παράδειγμα <EMAIL>.", "app.containers.ChangeEmail.emailRequired": "Παρακαλούμε εισάγετε μια διεύθυνση ηλεκτρονικού ταχυδρομείου.", "app.containers.ChangeEmail.emailTaken": "Αυτό το email χρησιμοποιείται ήδη.", "app.containers.ChangeEmail.emailUpdateCancelled": "Η ενημέρωση μέσω email έχει ακυρωθεί.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Για να ενημερώσετε το email σας, επαν<PERSON>κκινήστε τη διαδικασία.", "app.containers.ChangeEmail.helmetDescription": "Αλλάξτε τη σελίδα email σας", "app.containers.ChangeEmail.helmetTitle": "Αλλάξτε το email σας", "app.containers.ChangeEmail.newEmailLabel": "Νέο email", "app.containers.ChangeEmail.submitButton": "Υποβολή", "app.containers.ChangeEmail.titleAddEmail": "Προσθέστε το email σας", "app.containers.ChangeEmail.titleChangeEmail": "Αλλάξτε το email σας", "app.containers.ChangeEmail.updateSuccessful": "Το email σας ενημερώθηκε επιτυχώς.", "app.containers.ChangePassword.currentPasswordLabel": "Τρέχων κωδικ<PERSON>ς πρόσβασης", "app.containers.ChangePassword.currentPasswordRequired": "Εισάγετε τον τρέχοντα κωδικό πρόσβασής σας", "app.containers.ChangePassword.goHome": "Go to home", "app.containers.ChangePassword.helmetDescription": "Αλλαγή του κωδικού πρόσβασής σας", "app.containers.ChangePassword.helmetTitle": "Αλλάξτε τον κωδικό πρόσβασής σας", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON><PERSON> κωδικ<PERSON>ς πρόσβασης", "app.containers.ChangePassword.newPasswordRequired": "Εισάγετε τον νέο σας κωδικό πρόσβασης", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Παρέχετε έναν κωδικό πρόσβασης που έχει μήκος τουλάχιστον {minimumPasswordLength} χαρακτήρες.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Ο κωδικός πρόσβασής σας ενημερώθηκε επιτυχώς", "app.containers.ChangePassword.passwordEmptyError": "Εισάγετε τον κωδικό πρόσβασής σας", "app.containers.ChangePassword.passwordsDontMatch": "Επιβεβαίωση νέου κωδικού πρόσβασης", "app.containers.ChangePassword.titleAddPassword": "Προσθέστε έναν κωδικό πρόσβασης", "app.containers.ChangePassword.titleChangePassword": "Αλλάξτε τον κωδικό πρόσβασής σας", "app.containers.Comments.a11y_commentDeleted": "Το σχόλιο διαγράφηκε", "app.containers.Comments.a11y_commentPosted": "Το σχόλιο δημοσιεύτηκε", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {Δεν μου αρέσει} one {1 like} other {# likes}}", "app.containers.Comments.a11y_undoLike": "Αναίρεση όπως", "app.containers.Comments.addCommentError": "Κάτι πήγε στραβά. Προσπαθήστε ξανά αργότερα.", "app.containers.Comments.adminCommentDeletionCancelButton": "Ακύρωση", "app.containers.Comments.adminCommentDeletionConfirmButton": "Διαγράψτε αυτό το σχόλιο", "app.containers.Comments.cancelCommentEdit": "Ακύρωση", "app.containers.Comments.childCommentBodyPlaceholder": "Γράψτε μια απάντηση...", "app.containers.Comments.commentCancelUpvote": "Αναίρεση", "app.containers.Comments.commentDeletedPlaceholder": "Αυτό το σχόλιο έχει διαγραφεί.", "app.containers.Comments.commentDeletionCancelButton": "Κρατήστε το σχόλιό μου", "app.containers.Comments.commentDeletionConfirmButton": "Διαγράψτε το σχόλιό μου", "app.containers.Comments.commentLike": "Όπως", "app.containers.Comments.commentReplyButton": "Απάντηση", "app.containers.Comments.commentsSortTitle": "Ταξινόμηση των σχολίων κατά", "app.containers.Comments.completeProfileLinkText": "συμπληρώστε το προφίλ σας", "app.containers.Comments.completeProfileToComment": "Παρακαλούμε {completeRegistrationLink} για να σχολιάσετε.", "app.containers.Comments.confirmCommentDeletion": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτό το σχόλιο; Δεν υπάρχει επιστροφή!", "app.containers.Comments.deleteComment": "Διαγραφή", "app.containers.Comments.deleteReasonDescriptionError": "Δώστε περισσότερες πληροφορίες σχετικά με το λόγο σας", "app.containers.Comments.deleteReasonError": "Δώστε έναν λόγο", "app.containers.Comments.deleteReason_inappropriate": "Είν<PERSON>ι ακατάλληλο ή προσβλητικό", "app.containers.Comments.deleteReason_irrelevant": "Δεν είναι σχετικό", "app.containers.Comments.deleteReason_other": "Άλλος λόγος", "app.containers.Comments.editComment": "Επεξεργασία", "app.containers.Comments.guidelinesLinkText": "τις κατευθυντήριες γραμμές της κοινότητάς μας", "app.containers.Comments.ideaCommentBodyPlaceholder": "Γράψτε το σχόλιό σας εδώ", "app.containers.Comments.internalCommentingNudgeMessage": "Η δημιουργία εσωτερικών σχολίων δεν περιλαμβάνεται στην τρέχουσα άδειά σας. Επικοινωνήστε με τον διαχειριστή σας στο GovSuccess για να μάθετε περισσότερα γι' αυτό.", "app.containers.Comments.internalConversation": "Εσωτερική συζήτηση", "app.containers.Comments.loadMoreComments": "Φορτώστε περισσότερα σχόλια", "app.containers.Comments.loadingComments": "Φόρτωση σχολίων...", "app.containers.Comments.loadingMoreComments": "Φόρτωση περισσότερων σχολίων...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Αυτό το σχόλιο δεν είναι ορατό στους κανονικούς χρήστες", "app.containers.Comments.postInternalComment": "Δημοσίευση εσωτερικού σχολίου", "app.containers.Comments.postPublicComment": "Δημοσίευση δημόσιου σχολίου", "app.containers.Comments.profanityError": "Ουπς! Φαίνεται ότι η δημοσίευσή σας περιέχει κάποια γλώσσα που δεν ανταποκρίνεται στις {guidelinesLink}. Προσπαθούμε να διατηρήσουμε αυτό το χώρο ασφαλή για όλους. Παρακαλούμε επεξεργαστείτε την εισήγησή σας και προσπαθήστε ξανά.", "app.containers.Comments.publicDiscussion": "Δημόσια συζήτηση", "app.containers.Comments.publishComment": "Δημοσιεύστε το σχόλιό σας", "app.containers.Comments.reportAsSpamModalTitle": "Για<PERSON><PERSON> θέλετε να το αναφέρετε ως spam;", "app.containers.Comments.saveComment": "Αποθήκευση", "app.containers.Comments.signInLinkText": "σύνδεση", "app.containers.Comments.signInToComment": "Παρακαλούμε {signInLink} για να σχολιάσετε.", "app.containers.Comments.signUpLinkText": "εγγραφείτε", "app.containers.Comments.verifyIdentityLinkText": "Επαληθεύστε την ταυτότητά σας", "app.containers.Comments.visibleToUsersPlaceholder": "Αυτό το σχόλιο είναι ορατό στους τακτικούς χρήστες", "app.containers.Comments.visibleToUsersWarning": "Τα σχόλια που δημοσιεύοντα<PERSON> εδώ θα είναι ορατά στους τακτικούς χρήστες.", "app.containers.ContentBuilder.PageTitle": "Περιγραφή του έργου", "app.containers.CookiePolicy.advertisingContent": "Τα cookies διαφήμισης μπορεί να χρησιμοποιηθούν για την εξατομίκευση και τη μέτρηση της αποτελεσματικότητας που έχουν οι εξωτερικές εκστρατείες μάρκετινγκ στη δέσμευση με αυτή την πλατφόρμα. Δεν θα προβάλλουμε καμία διαφήμιση σε αυτή την πλατφόρμα, αλλ<PERSON> ενδέχεται να λάβετε εξατομικευμένες διαφημίσεις με βάση τις σελίδες που επισκέπτεστε.", "app.containers.CookiePolicy.advertisingTitle": "Διαφήμιση", "app.containers.CookiePolicy.analyticsContents": "Τα cookies ανάλυσης παρακολουθούν τη συμπεριφορά των επισκεπτών, όπως ποιες σελίδες επισκέπτονται και για πόσο χρονικό διάστημα. Ενδέχεται επίσης να συλλέγουν ορισμένα τεχνικά δεδομένα, συμπεριλαμβανομένων πληροφοριών για το πρόγραμμα περιήγησης, την κατά προσέγγιση τοποθεσία και τις διευθύνσεις IP. Χρησιμοποιούμε αυτά τα δεδομένα μόνο εσωτερικά για να συνεχίσουμε να βελτιώνουμε τη συνολική εμπειρία του χρήστη και τη λειτουργία της πλατφόρμας. Τα δεδομένα αυτά ενδέχεται επίσης να κοινοποιούνται μεταξύ του Go Vocal και του {orgName} για την αξιολόγηση και τη βελτίωση της δέσμευσης σε έργα στην πλατφόρμα. Σημειώστε ότι τα δεδομένα είναι ανώνυμα και χρησιμοποιούνται σε συγκεντρωτικό επίπεδο - δεν σας ταυτοποιούν προσωπικά. Ωστόσο, είναι πιθανό ότι εάν τα δεδομένα αυτά συνδυαστούν με άλλες πηγές δεδομένων, θα μπορούσε να προκύψει τέτοια ταυτοποίηση.", "app.containers.CookiePolicy.analyticsTitle": "Cookies ανάλυσης", "app.containers.CookiePolicy.cookiePolicyDescription": "Λεπτομερής επεξήγηση του τρόπου με τον οποίο χρησιμοποιούμε τα cookies σε αυτή την πλατφόρμα", "app.containers.CookiePolicy.cookiePolicyTitle": "Πολιτική cookie", "app.containers.CookiePolicy.essentialContent": "Ορισμένα cookies είναι απαραίτητα για τη διασφάλιση της ορθής λειτουργίας αυτής της πλατφόρμας. Αυτά τα βασικά cookies χρησιμοποιούνται κυρίως για την πιστοποίηση του λογαριασμού σας όταν επισκέπτεστε την πλατφόρμα και για την αποθήκευση της προτιμώμενης γλώσσας σας.", "app.containers.CookiePolicy.essentialTitle": "Απαραίτητα cookies", "app.containers.CookiePolicy.externalContent": "Ορισμένες από τις σελίδες μας ενδέχεται να προβάλλουν περιεχόμενο από εξωτερικούς παρόχους, π.χ. YouTube ή Typeform. Δεν έχουμε τον έλεγχο αυτών των cookies τρίτων και η προβολή του περιεχομένου από αυτούς τους εξωτερικούς παρόχους μπορεί επίσης να έχει ως αποτέλεσμα την εγκατάσταση cookies στη συσκευή σας.", "app.containers.CookiePolicy.externalTitle": "Εξωτερικά cookies", "app.containers.CookiePolicy.functionalContents": "Τα λειτουργικ<PERSON> cookies ενδέχεται να είναι ενεργοποιημένα για τους επισκέπτες ώστε να λαμβάνουν ειδοποιήσεις σχετικά με ενημερώσεις και να έχουν πρόσβαση σε κανάλια υποστήριξης απευθείας από την πλατφόρμα.", "app.containers.CookiePolicy.functionalTitle": "Λειτουργικά cookies", "app.containers.CookiePolicy.headCookiePolicyTitle": "Πολιτικ<PERSON> | {orgName}", "app.containers.CookiePolicy.intro": "Τα cookies είναι αρχεία κειμένου που αποθηκεύονται στο πρόγραμμα περιήγησης ή στον σκληρό δίσκο του υπολογιστή ή της κινητής συσκευής σας όταν επισκέπτεστε έναν ιστότοπο και τα οποία μπορεί να αναφέρονται από τον ιστότοπο κατά τις επόμενες επισκέψεις. Χρησιμοποιούμε cookies για να κατανοήσουμε πώς οι επισκέπτες χρησιμοποιούν αυτή την πλατφόρμα για να βελτιώσουμε το σχεδιασμό και την εμπειρία της, για να θυμόμαστε τις προτιμήσεις σας (όπως η προτιμώμενη γλώσσα σας) και για να υποστηρίζουμε βασικές λειτουργίες για τους εγγεγραμμένους χρήστες και τους διαχειριστές της πλατφόρμας.", "app.containers.CookiePolicy.manageCookiesDescription": "Μπορείτε να ενεργοποιήσετε ή να απενεργοποιήσετε τα cookies ανάλυσης, μάρκετινγκ και τα λειτουργικά cookies ανά πάσα στιγμή στις προτιμήσεις σας για τα cookies. Μπορείτε επίσης να διαγράψετε χειροκίνητα ή αυτόματα τυχόν υπάρχοντα cookies μέσω του προγράμματος περιήγησής σας στο διαδίκτυο. Ωστόσο, τα cookies ενδέχεται να τοποθετηθούν εκ νέου μετά τη συγκατάθεσή σας κατά τις επόμενες επισκέψεις σας σε αυτή την πλατφόρμα. Εάν δεν διαγράψετε τα cookies, οι προτιμήσεις σας για τα cookies αποθηκεύονται για 60 ημέρες, μετά τις οποίες θα σας ζητηθεί εκ νέου η συγκατάθεσή σας.", "app.containers.CookiePolicy.manageCookiesPreferences": "Μεταβείτε στο {manageCookiesPreferencesButtonText} για να δείτε μια πλήρη λίστα των ενοποιήσεων τρίτων μερών που χρησιμοποιούνται σε αυτή την πλατφόρμα και να διαχειριστείτε τις προτιμήσεις σας.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "ρυθμίσεις cookie", "app.containers.CookiePolicy.manageCookiesTitle": "Διαχείριση των cookies σας", "app.containers.CookiePolicy.viewPreferencesButtonText": "Ρυθμίσεις cookie", "app.containers.CookiePolicy.viewPreferencesText": "Οι παρακάτω κατηγορίες cookies ενδέχεται να μην ισχύουν για όλους τους επισκέπτες ή τις πλατφόρμες, δείτε το {viewPreferencesButton} για μια πλήρη λίστα των ενοποιήσεων τρίτων μερών που ισχύουν για εσάς.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Για ποιο λόγο χρησιμοποιούμε τα cookies;", "app.containers.CustomPageShow.editPage": "Επεξεργασία σελίδας", "app.containers.CustomPageShow.goBack": "Επιστροφή", "app.containers.CustomPageShow.notFound": "Η σελίδα δεν βρέθηκε", "app.containers.DisabledAccount.bottomText": "Μπορείτε να συνδεθείτε ξανά από το {date}.", "app.containers.DisabledAccount.termsAndConditions": "όροι & προϋποθέσεις", "app.containers.DisabledAccount.text2": "Ο λογαριασμός σας στην πλατφόρμα συμμετοχής του {orgName} έχει προσωρινά απενεργοποιηθεί για παραβίαση των κατευθυντήριων γραμμών της κοινότητας. Για περισσότερες πληροφορίες σχετικά με αυτό, μπορείτε να συμβουλευτείτε το {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Ο λογαριασμός σας έχει απενεργοποιηθεί προσωρινά", "app.containers.EventsShow.addToCalendar": "Προσθήκη στο ημερολόγιο", "app.containers.EventsShow.editEvent": "Επεξεργασία συμβάντος", "app.containers.EventsShow.emailSharingBody2": "Παρακολουθήστε αυτή την εκδήλωση: {eventTitle}. Διαβάστε περισσότερα στο {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Ημερομηνία και ώρα εκδήλωσης", "app.containers.EventsShow.eventFrom2": "Από το \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Πηγαίνετε πίσω", "app.containers.EventsShow.goToProject": "Μεταβείτε στο έργο", "app.containers.EventsShow.haveRegistered": "έχουν εγγραφεί", "app.containers.EventsShow.icsError": "Σφάλμα λήψης του αρχείου ICS", "app.containers.EventsShow.linkToOnlineEvent": "Σύνδεσμος για την online εκδήλωση", "app.containers.EventsShow.locationIconAltText": "Τοποθεσία", "app.containers.EventsShow.metaTitle": "Εκδήλωση: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Διαδικτυα<PERSON><PERSON> συνάντηση", "app.containers.EventsShow.onlineLinkIconAltText": "Σύνδεσμος online συνάντησης", "app.containers.EventsShow.registered": "καταχωρημένο", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 εγγεγραμμένοι} one {1 εγγεγραμμένος} other {# εγγεγραμμένοι}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} εγγεγραμμένοι", "app.containers.EventsShow.registrantsIconAltText": "Καταχωρούμενοι", "app.containers.EventsShow.socialMediaSharingMessage": "Παρακολουθήστε αυτή την εκδήλωση: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# participant} other {# participants}}", "app.containers.EventsViewer.allTime": "Όλη την ώρα", "app.containers.EventsViewer.date": "Ημερομηνία", "app.containers.EventsViewer.thisMonth2": "Επόμενος μήνας", "app.containers.EventsViewer.thisWeek2": "Επόμενη εβδομάδα", "app.containers.EventsViewer.today": "Σήμερα", "app.containers.IdeaButton.addAContribution": "Προσθήκη συνεισφοράς", "app.containers.IdeaButton.addAPetition": "Προσθέστε μια αναφορά", "app.containers.IdeaButton.addAProject": "Προσθήκη έργου", "app.containers.IdeaButton.addAProposal": "Προσθέστε μια πρόταση", "app.containers.IdeaButton.addAQuestion": "Προσθήκη ερώτησης", "app.containers.IdeaButton.addAnInitiative": "Προσθέστε μια πρωτοβουλία", "app.containers.IdeaButton.addAnOption": "Προσθήκη επιλογής", "app.containers.IdeaButton.postingDisabled": "Προς το παρόν δεν γίνονται δεκτές νέες υποβολές", "app.containers.IdeaButton.postingInNonActivePhases": "Νέες υποβολές μπορούν να προστεθούν μόνο σε ενεργές φάσεις.", "app.containers.IdeaButton.postingInactive": "Προς το παρόν δεν γίνονται δεκτές νέες υποβολές.", "app.containers.IdeaButton.postingLimitedMaxReached": "Έχετε ήδη ολοκληρώσει αυτή την έρευνα. Ευχαριστούμε για την απάντησή σας!", "app.containers.IdeaButton.postingNoPermission": "Προς το παρόν δεν γίνονται δεκτές νέες υποβολές", "app.containers.IdeaButton.postingNotYetPossible": "Νέες υποβολές δεν γίνονται ακόμη δεκτές.", "app.containers.IdeaButton.signInLinkText": "σύνδεση", "app.containers.IdeaButton.signUpLinkText": "εγγραφείτε", "app.containers.IdeaButton.submitAnIssue": "Υποβάλετε ένα σχόλιο", "app.containers.IdeaButton.submitYourIdea": "Υποβάλετε την ιδέα σας", "app.containers.IdeaButton.takeTheSurvey": "Πάρτε μέρος στην έρευνα", "app.containers.IdeaButton.verificationLinkText": "Επαληθεύστε την ταυτότητά σας τώρα.", "app.containers.IdeaCard.readMore": "Διαβάστε περισσότερα", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {δεν υπάρχουν σχόλια} one {1 σχόλιο} other {# σχόλια}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {καμία ψήφος} one {1 ψήφος} other {# ψήφοι}} εκτός {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Κλείσιμο πίνακα φίλτρων", "app.containers.IdeaCards.a11y_totalItems": "Σύνολο δημοσιεύσεων: {ideasCount}", "app.containers.IdeaCards.all": "Όλα", "app.containers.IdeaCards.allStatuses": "Όλες οι καταστάσεις", "app.containers.IdeaCards.contributions": "Συνεισφορές", "app.containers.IdeaCards.ideaTerm": "Ιδέες", "app.containers.IdeaCards.initiatives": "Πρωτοβουλίες", "app.containers.IdeaCards.issueTerm": "Θέματα", "app.containers.IdeaCards.list": "Λίστα", "app.containers.IdeaCards.map": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "Τα πιο συζητημένα", "app.containers.IdeaCards.newest": "Πιο πρόσφατα", "app.containers.IdeaCards.noFilteredResults": "Δεν βρέθηκαν αποτελέσματα. Παρακαλούμε δοκιμάστε ένα διαφορετικ<PERSON> φίλτρο ή όρο αναζήτησης.", "app.containers.IdeaCards.numberResults": "Αποτελέσματα ({postCount})", "app.containers.IdeaCards.oldest": "Παλαιότερο", "app.containers.IdeaCards.optionTerm": "Επιλογές", "app.containers.IdeaCards.petitions": "Αναφορές", "app.containers.IdeaCards.popular": "Με τις περισσότερες ψήφους", "app.containers.IdeaCards.projectFilterTitle": "Έργα", "app.containers.IdeaCards.projectTerm": "Έργα", "app.containers.IdeaCards.proposals": "Προτάσεις", "app.containers.IdeaCards.questionTerm": "Ερωτήσεις", "app.containers.IdeaCards.random": "Τυχαία", "app.containers.IdeaCards.resetFilters": "Επαναφορ<PERSON> φίλτρων", "app.containers.IdeaCards.showXResults": "Εμφάνιση {ideasCount, plural, one {# αποτέλεσμα} other {# αποτελέσματα}}", "app.containers.IdeaCards.sortTitle": "Ταξινόμηση", "app.containers.IdeaCards.statusTitle": "Κατάσταση", "app.containers.IdeaCards.statusesTitle": "Κατάσταση", "app.containers.IdeaCards.topics": "Ετικέτες", "app.containers.IdeaCards.topicsTitle": "Ετικέτες", "app.containers.IdeaCards.trending": "Τάσεις", "app.containers.IdeaCards.tryDifferentFilters": "Δεν βρέθηκαν αποτελέσματα. Παρακαλούμε δοκιμάστε ένα διαφορετικ<PERSON> φίλτρο ή όρο αναζήτησης.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} σχόλια} one {{ideasCount} σχόλιο} other {{ideasCount} σχόλια}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} συνεισφορές} one {{ideasCount} συνεισφορά} other {{ideasCount} συνεισφορές}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ιδέες} one {{ideasCount} ιδέα} other {{ideasCount} ιδέες}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} πρωτοβουλίες} one {{ideasCount} πρωτοβουλία} other {{ideasCount} πρωτοβουλίες}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} επιλογές} one {{ideasCount} επιλογή} other {{ideasCount} επιλογές}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} αναφορές} one {{ideasCount} αναφορές} other {{ideasCount} αναφορές}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} έργα} one {{ideasCount} έργο} other {{ideasCount} έργα}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} προτάσεις} one {{ideasCount} πρόταση} other {{ideasCount} προτάσεις}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} ερωτήσεις} one {{ideasCount} ερώτηση} other {{ideasCount} ερωτήσεις}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# αποτέλεσμα} other {# αποτελέσματα}}", "app.containers.IdeasEditPage.contributionFormTitle": "Επεξεργασία συνεισφοράς", "app.containers.IdeasEditPage.editedPostSave": "Αποθήκευση", "app.containers.IdeasEditPage.fileUploadError": "Ένα ή περισσότερα αρχεία απέτυχαν να φορτωθούν. Ελέγξτε το μέγεθος και τη μορφή του αρχείου και προσπαθήστε ξανά.", "app.containers.IdeasEditPage.formTitle": "Επεξεργασία ιδέας", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Επεξεργαστείτε τη δημοσίευσή σας. Προσθέστε νέες και αλλάξτε τις παλιές πληροφορίες.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Επεξεργασία {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Επεξεργασία πρωτοβουλίας", "app.containers.IdeasEditPage.issueFormTitle": "Επεξεργασ<PERSON>α σχολίου", "app.containers.IdeasEditPage.optionFormTitle": "Επεξεργασία επιλογής", "app.containers.IdeasEditPage.petitionFormTitle": "Επεξεργασία αίτησης", "app.containers.IdeasEditPage.projectFormTitle": "Επεξεργασία έργου", "app.containers.IdeasEditPage.proposalFormTitle": "Επεξεργασία πρότασης", "app.containers.IdeasEditPage.questionFormTitle": "Επεξεργα<PERSON><PERSON>α ερώτησης", "app.containers.IdeasEditPage.save": "Αποθήκευση", "app.containers.IdeasEditPage.submitApiError": "Υπήρξε πρόβλημα με την υποβολή της φόρμας. Ελέγξτε για τυχόν σφάλματα και προσπαθήστε ξανά.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Όλες οι εισροές αναρτήθηκαν", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Εξερευνήστε όλες τις εισηγήσεις που έχουν δημοσιευθεί στην πλατφόρμα συμμετοχής του {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Θέσεις | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Δημοσιεύσεις", "app.containers.IdeasIndexPage.loadMore": "Φορτώστε περισσότερα...", "app.containers.IdeasIndexPage.loading": "Φόρτωση...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Από προεπιλογή, οι υποβολές σας θα συσχετίζονται με το προφίλ σας, εκ<PERSON><PERSON><PERSON> αν επιλέξετε αυτή την επιλογή.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Δημοσιεύστε ανώνυμα", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Ορατότητα προφίλ", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Αυτή η έρευνα δεν είναι επί του παρόντος ανοικτή για απαντήσεις. Παρακαλείστε να επιστρέψετε στο έργο για περισσότερες πληροφορίες.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Αυτή η έρευνα δεν είναι επί του παρόντος ενεργή.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Επιστροφή στο έργο", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Έχετε ήδη ολοκληρώσει αυτή την έρευνα.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Υποβληθείσα έρευνα", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Ευχα<PERSON><PERSON><PERSON>τ<PERSON> για την απάντησή σας!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Η περιγραφή της συνεισφοράς πρέπει να είναι μικρότερη από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Το σώμα της ιδέας πρέπει να έχει μήκος μεγαλύτερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Ο τίτλος της συνεισφοράς πρέπει να είναι μικρότερος από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Ο τίτλος της συνεισφοράς πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Παρακαλούμε επιλέξτε τουλάχιστον έναν συνυποστηρικτή", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Η περιγραφή της ιδέας πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Η περιγραφή της ιδέας πρέπει να έχει μήκος μεγαλύτερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Παρακαλούμε δώστε μια περιγραφή", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Ο τίτλος της ιδέας πρέπει να είναι μικρότερος από {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Ο τίτλος της ιδέας πρέπει να έχει μήκος μεγαλύτερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Η περιγραφή της πρωτοβουλίας πρέπει να είναι μικρότερη από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Η περιγραφή της πρωτοβουλίας πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Ο τίτλος της πρωτοβουλίας πρέπει να είναι μικρότερος από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Ο τίτλος της πρωτοβουλίας πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Η περιγραφή του θέματος πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Η περιγραφή του θέματος πρέπει να έχει μήκος μεγαλύτερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Ο τίτλος του θέματος πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Ο τίτλος του τεύχους πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_number_required": "Αυτό το πεδίο είναι υποχρεωτικ<PERSON>, παρακαλ<PERSON> εισάγετε έναν έγκυρο αριθμό", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Η περιγραφή της επιλογής πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Η περιγραφή της επιλογής πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Ο τίτλος της επιλογής πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Ο τίτλος της επιλογής πρέπει να έχει μήκος μεγαλύτερο από {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Παρακαλούμε επιλέξτε τουλάχιστον μία ετικέτα", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Η περιγραφή της αναφοράς πρέπει να είναι μικρότερη από {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Η περιγραφή της αναφοράς πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Ο τίτλος της αναφοράς πρέπει να είναι μικρότερος από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Ο τίτλος της αναφοράς πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Η περιγραφή του έργου πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Η περιγραφή του έργου πρέπει να έχει μήκος μεγαλύτερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Ο τίτλος του έργου πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Ο τίτλος του έργου πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Η περιγραφή της πρότασης πρέπει να είναι μικρότερη από {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Η περιγραφή της πρότασης πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Ο τίτλος της πρότασης πρέπει να είναι μικρότερος από {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Ο τίτλος της πρότασης πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Παρακαλούμε εισάγετε έναν αριθμό", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Παρακαλούμε εισάγετε έναν αριθμό", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Η περιγραφή της ερώτησης πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Η περιγραφή της ερώτησης πρέπει να έχει μήκος μεγαλύτερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Ο τίτλος της ερώτησης πρέπει να έχει μήκος μικρότερο από {limit} χαρακτήρες", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Ο τίτλος της ερώτησης πρέπει να υπερβαίνει τους {limit} χαρακτήρες.", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Παρακαλούμε δώστε έναν τίτλο", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Η περιγραφή της συνεισφοράς πρέπει να έχει μήκος μικρότερο από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Η περιγραφή της συνεισφοράς πρέπει να έχει μήκος τουλάχιστον 30 χαρακτήρων", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Ο τίτλος της συνεισφοράς πρέπει να έχει μήκος μικρότερο από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Ο τίτλος της συνεισφοράς πρέπει να έχει μήκος τουλάχιστον 10 χαρακτήρων", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Η περιγραφή της ιδέας πρέπει να είναι μικρότερη από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Η περιγραφή της ιδέας πρέπει να έχει μήκος τουλάχιστον 30 χαρακτήρων", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Παρακαλούμε δώστε έναν τίτλο", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Ο τίτλος της ιδέας πρέπει να είναι μικρότερος από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Ο τίτλος της ιδέας πρέπει να αποτελείται από τουλάχιστον 10 χαρακτήρες", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Μπορεί να έχετε χρησιμοποιήσει μία ή περισσότερες λέξεις που θεωρούνται αισχρολογία από το {guidelinesLink}. Παρακαλούμε τροποποιήστε το κείμενό σας για να καταργήσετε τυχόν αισχρολογίες που μπορεί να υπάρχουν.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Η περιγραφή της πρωτοβουλίας πρέπει να είναι μικρότερη από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Η περιγραφή της πρωτοβουλίας πρέπει να αποτελείται από τουλάχιστον 30 χαρακτήρες.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Ο τίτλος της πρωτοβουλίας πρέπει να είναι μικρότερος από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Ο τίτλος της πρωτοβουλίας πρέπει να αποτελείται από τουλάχιστον 10 χαρακτήρες.", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Η περιγραφή του θέματος πρέπει να έχει μήκος μικρότερο από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Η περιγραφή του θέματος πρέπει να έχει μήκος τουλάχιστον 30 χαρακτήρων", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Ο τίτλος του θέματος πρέπει να είναι μικρότερος από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Ο τίτλος του θέματος πρέπει να έχει μήκος τουλάχιστον 10 χαρακτήρων", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Η περιγραφή της επιλογής πρέπει να είναι μικρότερη από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Η περιγραφή της επιλογής πρέπει να έχει μήκος τουλάχιστον 30 χαρακτήρων", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Ο τίτλος της επιλογής πρέπει να είναι μικρότερος από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Ο τίτλος της επιλογής πρέπει να έχει μήκος τουλάχιστον 10 χαρακτήρων", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Η περιγραφή της αναφοράς πρέπει να είναι μικρότερη από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Η περιγραφή της αναφοράς πρέπει να έχει μήκος τουλάχιστον 30 χαρακτήρων.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Ο τίτλος της αναφοράς πρέπει να είναι μικρότερος από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Ο τίτλος της αναφοράς πρέπει να αποτελείται από τουλάχιστον 10 χαρακτήρες", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Η περιγραφή του έργου πρέπει να είναι μικρότερη από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Η περιγραφή του έργου πρέπει να έχει μήκος τουλάχιστον 30 χαρακτήρων", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Ο τίτλος του έργου πρέπει να είναι μικρότερος από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Ο τίτλος του έργου πρέπει να έχει μήκος τουλάχιστον 10 χαρακτήρων", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Η περιγραφή της πρότασης πρέπει να είναι μικρότερη από 80 χαρακτήρες.", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Η περιγραφή της πρότασης πρέπει να αποτελείται από τουλάχιστον 30 χαρακτήρες.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Ο τίτλος της πρότασης πρέπει να είναι μικρότερος από 80 χαρακτήρες.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Ο τίτλος της πρότασης πρέπει να αποτελείται από τουλάχιστον 10 χαρακτήρες.", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Παρακαλούμε δώστε μια περιγραφή", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Η περιγραφή της ερώτησης πρέπει να είναι μικρότερη από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Η περιγραφή της ερώτησης πρέπει να έχει μήκος τουλάχιστον 30 χαρακτήρων", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Ο τίτλος της ερώτησης πρέπει να είναι μικρότερος από 80 χαρακτήρες", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Ο τίτλος της ερώτησης πρέπει να έχει μήκος τουλάχιστον 10 χαρακτήρων", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Ακύρωση", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "Ναι, θέλω να φύγω", "app.containers.IdeasNewPage.contributionMetaTitle1": "Προσθήκη νέας συνεισφοράς στο έργο | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Επεξεργασία έρευνας", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Δημοσιεύστε μια εισήγηση και συμμετέχετε στη συζήτηση στην πλατφόρμα συμμετοχής του {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Προσθήκη νέας ιδέας στο έργο | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Προσθήκη νέας πρωτοβουλίας στο έργο | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Προσθήκη νέου θέματος στο έργο | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Είσαι σίγουρος ότι θέλεις να φύγεις;", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Το προσχέδιο των απαντή<PERSON>εών σας έχει αποθηκευτεί ιδιαιτέρως και μπορείτε να επιστρέψετε για να το συμπληρώσετε αργότερα.", "app.containers.IdeasNewPage.leaveSurvey": "Έρευνα άδειας", "app.containers.IdeasNewPage.leaveSurveyText": "Οι απαντήσεις σας δεν θα αποθηκευτούν.", "app.containers.IdeasNewPage.optionMetaTitle1": "Προσθήκη νέας επιλογής στο έργο | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Προσθήκη νέας αναφοράς στο έργο | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Προσθήκη νέου έργου στο έργο | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Προσθήκη νέας πρότασης στο έργο | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Προσθήκη νέας ερώτησης στο έργο | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Αποδοχή πρόσκλησης", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Πρόσκληση για συν-χορηγία", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Συν-χορηγοί", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Έχετε προσκληθεί να γίνετε συν-χορηγός.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Πρόσκληση αποδεκτή", "app.containers.IdeasShow.Cosponsorship.pending": "σε εκκρεμότητα", "app.containers.IdeasShow.MetaInformation.attachments": "Συνημμένα αρχεία", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} στις {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Τρέχουσα κατάσταση", "app.containers.IdeasShow.MetaInformation.location": "Τοποθεσία", "app.containers.IdeasShow.MetaInformation.postedBy": "Δημοσιεύτηκε από", "app.containers.IdeasShow.MetaInformation.similar": "Παρόμοιες εισροές", "app.containers.IdeasShow.MetaInformation.topics": "Ετικέτες", "app.containers.IdeasShow.commentCTA": "Προσθέστε ένα σχόλιο", "app.containers.IdeasShow.contributionEmailSharingBody": "Υποστηρίξτε αυτή τη συνεισφορά '{postTitle}' στο {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Υποστηρίξτε αυτή τη συνεισφορά: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Ευχαριστούμε για την υποβολή της συνεισφοράς σας!", "app.containers.IdeasShow.contributionTwitterMessage": "Υποστηρίξτε αυτή τη συνεισφορά: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Υποστηρίξτε αυτή τη συνεισφορά: {postTitle}", "app.containers.IdeasShow.currentStatus": "Τρέχουσα κατάσταση", "app.containers.IdeasShow.deletedUser": "άγνω<PERSON>τος συντάκτης", "app.containers.IdeasShow.ideaEmailSharingBody": "Υποστηρίξτε την ιδέα μου '{ideaTitle}' στο {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Υποστηρίξτε την ιδέα μου: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Υποστηρίξτε αυτή την ιδέα: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Υποστηρίξτε αυτή την ιδέα: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Υποστηρίξτε αυτό το σχόλιο: {postTitle}", "app.containers.IdeasShow.imported": "Εισαγόμενο", "app.containers.IdeasShow.importedTooltip": "Το εν λόγω {inputTerm} συλλέχθηκε εκτός σύνδεσης και μεταφορτώθηκε αυτόματα στην πλατφόρμα.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Υποστηρίξτε αυτή την πρωτοβουλία '{ideaTitle}' στο {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Υποστηρίξτε αυτή την πρωτοβουλία: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Ευχαριστούμε για την υποβολή της πρωτοβουλίας σας!", "app.containers.IdeasShow.initiativeTwitterMessage": "Υποστηρίξτε αυτή την πρωτοβουλία: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Υποστηρίξτε αυτή την πρωτοβουλία: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Υποστηρίξτε αυτό το σχόλιο '{postTitle}' στο {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Υποστηρίξτε αυτό το σχόλιο: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Ευχαριστούμε για την υποβολή του σχολίου σας!", "app.containers.IdeasShow.issueTwitterMessage": "Υποστηρίξτε αυτό το σχόλιο: {postTitle}", "app.containers.IdeasShow.metaTitle": "Εισαγωγή: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Υποστηρίξτε αυτή την επιλογή '{postTitle}' στο {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Υποστηρίξτε αυτή την επιλογή: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Η επιλογή σας έχει δημοσιευθεί με επιτυχία!", "app.containers.IdeasShow.optionTwitterMessage": "Υποστηρίξτε αυτή την επιλογή: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Υποστηρίξτε αυτή την επιλογή: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Υποστηρίξτε την αίτηση '{ideaTitle}' στο {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Υποστηρίξτε αυτό το αίτημα: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Ευχαριστούμε για την υποβολή της αναφοράς σας!", "app.containers.IdeasShow.petitionTwitterMessage": "Υποστηρίξτε αυτό το αίτημα: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Υποστηρίξτε αυτό το αίτημα: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Υποστηρίξτε αυτό το έργο '{postTitle}' στο {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Υποστηρίξτε αυτό το έργο: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Ευχαριστούμε για την υποβολή του έργου σας!", "app.containers.IdeasShow.projectTwitterMessage": "Υποστηρίξτε αυτό το έργο: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Υποστηρίξτε αυτό το έργο: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Υποστηρίξτε αυτή την πρόταση '{ideaTitle}' στο {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Υποστηρίξτε την πρόταση αυτή: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Ευχαριστούμε για την υποβολή της πρότασής σας!", "app.containers.IdeasShow.proposalTwitterMessage": "Υποστηρίξτε την πρόταση αυτή: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Υποστηρίξτε την πρόταση αυτή: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Απομένει χρόνος για την ψηφοφορία:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} από τις {votingT<PERSON>esh<PERSON>} απαιτούμενες ψήφους", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Ακύρωση ψήφου", "app.containers.IdeasShow.proposals.VoteControl.days": "ημέρες", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "οι κατευθυντήριες γραμμές μας", "app.containers.IdeasShow.proposals.VoteControl.hours": "ώρες", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Κατάσταση και ψήφοι", "app.containers.IdeasShow.proposals.VoteControl.minutes": "λεπτά", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Περισσότερες πληροφορίες", "app.containers.IdeasShow.proposals.VoteControl.vote": "Ψηφίστε", "app.containers.IdeasShow.proposals.VoteControl.voted": "Ψηφίστηκε", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Θα λάβετε ειδοποίηση όταν η πρόταση αυτή περάσει στο επόμενο βήμα. {x, plural, =0 {Απομένει {xDays}.} one {Απομένουν {xDays}.} other {Απομένουν {xDays}.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Η ψήφος σας υποβλήθηκε!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Δυστυ<PERSON><PERSON><PERSON>, δεν μπορείτε να ψηφίσετε την πρόταση αυτή. Διαβάστε γιατί στο {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {λιγότερο από μία ημέρα} one {μία ημέρα} other {# ημέρες}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {δεν υπάρχουν ψήφοι} one {1 ψήφος} other {# ψήφοι}}", "app.containers.IdeasShow.questionEmailSharingBody": "Συμμετέχετε στη συζήτηση σχετικά με την ερώτηση '{postTitle}' στο {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Λάβετε μέρος στη συζήτηση: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Η ερώτησή σας δημοσιεύτηκε με επιτυχία!", "app.containers.IdeasShow.questionTwitterMessage": "Λάβετε μέρος στη συζήτηση: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Λάβετε μέρος στη συζήτηση: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Για<PERSON><PERSON> θέλετε να το αναφέρετε ως spam;", "app.containers.IdeasShow.share": "Κοινοποίηση", "app.containers.IdeasShow.sharingModalSubtitle": "Πλησιά<PERSON>τ<PERSON> περισσότερους ανθρώπους και κάντε τη φωνή σας να ακουστεί.", "app.containers.IdeasShow.sharingModalTitle": "Ευχαριστούμε για την υποβολή της ιδέας σας!", "app.containers.Navbar.completeOnboarding": "Ολοκληρωμ<PERSON>ν<PERSON> onboarding", "app.containers.Navbar.completeProfile": "Πλήρες προφίλ", "app.containers.Navbar.confirmEmail2": "Επιβεβαίωση email", "app.containers.Navbar.unverified": "Μη επαληθευμένος", "app.containers.Navbar.verified": "Επαληθευμένος", "app.containers.NewAuthModal.beforeYouFollow": "Πριν ακολουθήσετε", "app.containers.NewAuthModal.beforeYouParticipate": "Πριν από τη συμμετοχή σας", "app.containers.NewAuthModal.completeYourProfile": "Συμπληρώστε το προφίλ σας", "app.containers.NewAuthModal.confirmYourEmail": "Επιβεβαιώστε το email σας", "app.containers.NewAuthModal.logIn": "Συνδεθείτε", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Ελέγξτε τους παρακάτω όρους για να συνεχίσετε.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Παρακαλούμε συμπληρώστε το προφίλ σας.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Επιστροφή στις επιλογές σύνδεσης", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Δεν έχετε λογαριασμό; {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Εγγραφείτε", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Ο κωδικός πρέπει να έχει 4 ψηφία.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Συνεχίστε με το FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Σε αυτή την πλατφόρμα δεν είναι ενεργοποιημένη καμία μέθοδος ελέγχου ταυτότητας.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Συνεχίζ<PERSON><PERSON><PERSON><PERSON><PERSON>, συμφωνείτε να λαμβάνετε μηνύματα ηλεκτρονικού ταχυδρομείου από αυτή την πλατφόρμα. Μπορείτε να επιλέξετε τα email που επιθυμείτε να λαμβάνετε στη σελίδα \"Οι ρυθμίσεις μου\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Ηλεκτρονικό ταχυδρομείο", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Δώστε μια διεύθυνση ηλεκτρονικού ταχυδρομείου στη σωστή μορφή, για παράδειγμα <EMAIL>.", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Δώστε μια διεύθυνση ηλεκτρονικού ταχυδρομείου", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Εισάγετε τη διεύθυνση email σας για να συνεχίσετε.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Ξεχάσατε τον κωδικό πρόσβασης;", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Συνδεθείτε στο λογαριασμό σας: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τον κωδικό πρόσβασής σας", "app.containers.NewAuthModal.steps.Password.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "app.containers.NewAuthModal.steps.Password.rememberMe": "Να με θυμάσαι", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Μην επιλέγετε εάν χρησιμοποιείτε δημόσιο υπολογιστή", "app.containers.NewAuthModal.steps.Success.allDone": "Όλα έτοιμα", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Τώρα συνεχίστε τη συμμετοχή σας.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Η ταυτότητά σας έχει επαληθευτεί. Τώρα είστε πλήρες μέλος της κοινότητας σε αυτή την πλατφόρμα.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Είστε τώρα επαληθευμένοι !", "app.containers.NewAuthModal.steps.close": "Κλείστε το", "app.containers.NewAuthModal.steps.continue": "Συνεχίστε", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Τι σας ενδιαφέρει;", "app.containers.NewAuthModal.youCantParticipate": "Δεν μπορείτε να συμμετάσχετε", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {δεν υπάρχουν προβεβλημένες ειδοποιήσεις} one {1 μη προβεβλημένη ειδοποίηση} other {# μη προβεβλημένες ειδοποιήσεις}}", "app.containers.NotificationMenu.adminRightsReceived": "Είστε τώρα διαχειριστής της πλατφόρμας", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Το σχόλιό σας στο \"{postTitle}\" έχει διαγραφεί από έναν διαχειριστή επειδή\n      {reasonCode, select, irrelevant {είναι άσχετο} inappropriate {το περιεχόμενό του είναι ακατάλληλο} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} αποδέχθηκε την πρόσκλησή σας για συν-χορηγία", "app.containers.NotificationMenu.deletedUser": "Άγνωστος συντάκτης", "app.containers.NotificationMenu.error": "Δεν ήταν δυνα<PERSON>ή η φόρτωση ειδοποιήσεων", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} σχολιά<PERSON><PERSON><PERSON>ε εσωτερικά μια είσοδο που σας έχει ανατεθεί", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} σχολιά<PERSON>ατε εσωτερικά μια είσοδο που σχολιάσατε εσωτερικά", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} σχολιά<PERSON>ατε εσωτερικά μια είσοδο σε ένα έργο που διαχειρίζεστε", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} σχολία<PERSON>ε εσωτερικά μια μη ανατεθείσα είσοδο σε ένα μη διαχειριζόμενο έργο", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} σχολίασε το εσωτερικό σας σχόλιο", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} σας κάλεσε να συν-χορηγήσετε μια συνεισφορά", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} σας κάλεσε να συν-χορηγήσετε μια ιδέα", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} σας κάλεσε να συνυπογράψετε μια πρόταση", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} σας κάλεσε να συνυπογράψετε ένα θέμα", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} σας κάλεσε να συν-χορηγήσετε μια επιλογή", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} σας κάλεσε να συνυπογράψετε μια αίτηση", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} σας κάλεσε να συν-χορηγήσετε ένα έργο", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} σας κάλεσε να συνυπογράψετε μια πρόταση", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} σας κάλεσε να συνυπογράψετε μια ερώτηση", "app.containers.NotificationMenu.loadMore": "Φορτώστε περισσότερα...", "app.containers.NotificationMenu.loading": "Φόρτωση ειδοποιήσεων...", "app.containers.NotificationMenu.mentionInComment": "Ο/η {name} σας ανέφερε σε ένα σχόλιο", "app.containers.NotificationMenu.mentionInInternalComment": "{name} σας ανέφερε σε ένα εσωτερικό σχόλιο", "app.containers.NotificationMenu.mentionInOfficialFeedback": "Ο/η {name} σας ανέφερε σε μια επίσημη ενημέρωση", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Δεν υποβάλατε την έρευνά σας", "app.containers.NotificationMenu.noNotifications": "Δεν έχετε ακόμα ειδοποιήσεις", "app.containers.NotificationMenu.notificationsLabel": "Ειδοποιήσεις", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} έδωσε μια επίσημη ενημέρωση για μια συνεισφορά που ακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} έδωσε μια επίσημη ενημέρωση για μια ιδέα που ακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} έδωσε επίσημη ενημέρωση για μια πρωτοβουλία που ακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} έδωσε επίσημη ενημέρωση για ένα θέμα που παρακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} έδωσε μια επίσημη ενημέρωση για μια επιλογή που ακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} έδωσε μια επίσημη ενημέρωση σχετικά με μια αίτηση που ακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} έδωσε μια επίσημη ενημέρωση για ένα έργο που παρακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} έδωσε επίσημη ενημέρωση σχετικά με μια πρόταση που ακολουθείτε", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} έδωσε μια επίσημη ενημέρωση σχετικά με μια ερώτηση που ακολουθείτε", "app.containers.NotificationMenu.postAssignedToYou": "Το {postTitle} σας ανατέθηκε", "app.containers.NotificationMenu.projectModerationRightsReceived": "Είστε πλέον διαχειριστής έργου του {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "Το έργο {projectTitle} εισήλθε σε νέα φάση", "app.containers.NotificationMenu.projectPhaseUpcoming": "Το έργο {projectTitle} θα εισέλθει σε νέα φάση στις {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Δημο<PERSON>ιεύθηκε ένα νέο έργο", "app.containers.NotificationMenu.projectReviewRequest": "{name} ζήτησε έγκριση για τη δημοσίευση του έργου \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} εγκεκριμένο \"{projectTitle}\" για δημοσίευση", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} Η κατάσταση έχει αλλάξει σε {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "Η δημοσίευση {post} έφθασε το όριο ψηφοφορίας", "app.containers.NotificationMenu.userAcceptedYourInvitation": "Ο/η {name} αποδέχτηκε την πρόσκλησή σας", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} σχολιάσατε μια συνεισφορά που ακολουθείτε", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} σχολιάσατε μια ιδέα που ακολουθείτε", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} σχολιάσατε μια πρωτοβουλία που ακολουθείτε", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} σχολιάσατε ένα θέμα που ακολουθείτε", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} σχολίασε μια επιλογή που ακολουθείτε", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} σχολιάσατε μια αίτηση που ακολουθείτε", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} σχολιά<PERSON>α<PERSON>ε ένα έργο που παρακολουθείτε", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} σχολιάσατε μια πρόταση που ακολουθείτε", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} σχολίασε μια ερώτηση που ακολουθείτε", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} ανέφερε το \"{postTitle}\" ως spam", "app.containers.NotificationMenu.userReactedToYourComment": "Ο/η {name} αντέδρασε στο σχόλιό σας", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} ανέφερε ένα σχόλιο στο \"{postTitle}\" ως spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Δεν υποβάλατε τις ψήφους σας", "app.containers.NotificationMenu.votingBasketSubmitted": "Ψηφίσατε με επιτυχία", "app.containers.NotificationMenu.votingLastChance": "Τελευταία ευκαιρία να ψηφίσετε για το {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} Απ<PERSON>καλύφθηκαν τα αποτελέσματα της ψηφοφορίας", "app.containers.NotificationMenu.xAssignedPostToYou": "Ο/η {name} ανέθεσε το {postTitle} σε εσάς", "app.containers.PasswordRecovery.emailError": "Αυτό δεν μοιάζει με έγκυρο email", "app.containers.PasswordRecovery.emailLabel": "Email", "app.containers.PasswordRecovery.emailPlaceholder": "Η διεύθυνση email μου", "app.containers.PasswordRecovery.helmetDescription": "Σελίδα επαναφοράς του κωδικού πρόσβασής σας", "app.containers.PasswordRecovery.helmetTitle": "Επαναφορ<PERSON> του κωδικού πρόσβασής σας", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Εάν αυτή η διεύθυνση ηλεκτρονικού ταχυδρομείου είναι εγγεγραμμένη στην πλατφόρμα, έχε<PERSON> σταλεί σύνδεσμος επαναφοράς κωδικού πρόσβασης.", "app.containers.PasswordRecovery.resetPassword": "Αποστολή συνδέσμου επανα<PERSON><PERSON><PERSON><PERSON><PERSON> κωδικού πρόσβασης", "app.containers.PasswordRecovery.submitError": "Δεν μπορέσαμε να βρούμε έναν λογαριασμό που συνδέεται με αυτό το email. Μπορείτε να προσπαθήσετε να εγγραφείτε αντ'αυτού.", "app.containers.PasswordRecovery.subtitle": "Πού μπορούμε να στείλουμε έναν σύνδεσμο για να επιλέξετε έναν νέο κωδικό πρόσβασης;", "app.containers.PasswordRecovery.title": "Επανα<PERSON><PERSON><PERSON><PERSON> κωδικού πρόσβασης", "app.containers.PasswordReset.helmetDescription": "Σελίδα επαναφοράς του κωδικού πρόσβασής σας", "app.containers.PasswordReset.helmetTitle": "Επαναφορ<PERSON> του κωδικού πρόσβασής σας", "app.containers.PasswordReset.login": "Σύνδεση", "app.containers.PasswordReset.passwordError": "Ο κωδικός πρόσβασης πρέπει να αποτελείται από τουλάχιστον 8 χαρακτήρες", "app.containers.PasswordReset.passwordLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON><PERSON> κωδικ<PERSON>ς πρόσβασης", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Ο κωδικός πρόσβασής σας ενημερώθηκε επιτυχώς.", "app.containers.PasswordReset.pleaseLogInMessage": "Παρακαλούμε συνδεθείτε με τον νέο σας κωδικό πρόσβασης.", "app.containers.PasswordReset.requestNewPasswordReset": "Αίτηση επανα<PERSON><PERSON><PERSON><PERSON><PERSON> νέου κωδικού πρόσβασης", "app.containers.PasswordReset.submitError": "Κάτι πήγε στραβά. Προσπαθήστε ξανά αργότερα.", "app.containers.PasswordReset.title": "Επαναφορ<PERSON> του κωδικού πρόσβασής σας", "app.containers.PasswordReset.updatePassword": "Επιβεβαίωση νέου κωδικού πρόσβασης", "app.containers.ProjectFolderCards.allProjects": "Όλα τα έργα", "app.containers.ProjectFolderCards.currentlyWorkingOn": "Ο οργανισμ<PERSON>ς {orgName} εργάζεται επί του παρόντος σε", "app.containers.ProjectFolderShowPage.editFolder": "Επεξεργασ<PERSON>α φακέλου", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Πληροφορίες σχετικά με αυτό το έργο", "app.containers.ProjectFolderShowPage.metaTitle1": "Φάκελος: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Διαβάστε περισσότερα", "app.containers.ProjectFolderShowPage.seeLess": "Δείτε λιγότερα", "app.containers.ProjectFolderShowPage.share": "Κοινοποίηση", "app.containers.Projects.PollForm.document": "Έγγραφο", "app.containers.Projects.PollForm.formCompleted": "Σας ευχαριστούμε! Η απάντησή σας ελήφθη.", "app.containers.Projects.PollForm.maxOptions": "μέγ. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Έχετε ήδη συμμετάσχει σε αυτή τη δημοσκόπηση.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Αυτή η δημοσκόπηση μπορεί να πραγματοποιηθεί μόνο όταν αυτή η φάση είναι ενεργή.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Αυτή η δημοσκόπηση δεν είναι προς το παρόν ενεργοποιημένη", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Προς το παρόν δεν είναι δυνατή η διεξαγωγή αυτής της δημοσκόπησης.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Η δημοσκόπηση δεν είναι πλέον διαθέσιμη, καθ<PERSON>ς αυτό το έργο δεν είναι πλέον ενεργό.", "app.containers.Projects.PollForm.sendAnswer": "Αποστολή", "app.containers.Projects.a11y_phase": "Φάση {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Επισκόπηση φάσεων", "app.containers.Projects.a11y_titleInputs": "Όλες οι εισηγήσεις που υποβλήθηκαν σε αυτό το έργο", "app.containers.Projects.a11y_titleInputsPhase": "Όλες οι εισηγήσεις που υποβλήθηκαν σε αυτή τη φάση", "app.containers.Projects.accessRights": "Δικαιώματα πρόσβασης", "app.containers.Projects.addedToBasket": "Προστέθηκε στο καλάθι σας", "app.containers.Projects.allocateBudget": "Διαθέστε τον προϋπολογισμό σας", "app.containers.Projects.archived": "Αρχειοθετημένος", "app.containers.Projects.basketSubmitted": "Το καλάθι σας έχει υποβληθεί!", "app.containers.Projects.contributions": "Συνεισφορές", "app.containers.Projects.createANewPhase": "Δημιουργήστε μια νέα φάση", "app.containers.Projects.currentPhase": "Τρέχουσα φάση", "app.containers.Projects.document": "Έγγραφο", "app.containers.Projects.editProject": "Επεξεργασία έργου", "app.containers.Projects.emailSharingBody": "Πώς σας φαίνεται αυτή η πρωτοβουλία; Ψηφίστε την και κοινοποιείστε τη συζήτηση στη διεύθυνση {initiativeUrl} για να ακουστεί η φωνή σας!", "app.containers.Projects.emailSharingSubject": "Υποστηρίξτε την πρωτοβουλία μου: {initiativeTitle}.", "app.containers.Projects.endedOn": "Έληξε στις {date}", "app.containers.Projects.events": "Εκδηλώσεις", "app.containers.Projects.header": "Έργα", "app.containers.Projects.ideas": "Ιδέες", "app.containers.Projects.information": "Πληροφορίες", "app.containers.Projects.initiatives": "Πρωτοβουλίες", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Επανεξέταση του εγγράφου", "app.containers.Projects.invisibleTitlePhaseAbout": "Σχετι<PERSON><PERSON> με αυτή τη φάση", "app.containers.Projects.invisibleTitlePoll": "Πάρτε μέρος στη δημοσκόπηση", "app.containers.Projects.invisibleTitleSurvey": "Πάρτε μέρος στην έρευνα", "app.containers.Projects.issues": "Σχόλια", "app.containers.Projects.liveDataMessage": "Βλέπετε δεδομένα σε πραγματι<PERSON><PERSON> χρόνο. Ο αριθμός των συμμετεχόντων ενημερώνεται συνεχώς για τους διαχειριστές. Λάβετε υπόψη ότι οι κανονικοί χρήστες βλέπουν δεδομένα που έχουν αποθηκευτεί στην προσωρινή μνήμη, γεγονός που μπορεί να έχει ως αποτέλεσμα μικρές διαφορές στους αριθμούς.", "app.containers.Projects.location": "Τοποθεσία:", "app.containers.Projects.manageBasket": "Διαχείριση καλαθιού", "app.containers.Projects.meetMinBudgetRequirement": "Συμπληρώστε τον ελάχιστο προϋπολογισμό για να υποβάλετε το καλάθι σας.", "app.containers.Projects.meetMinSelectionRequirement": "Συμπληρώστε την απαιτούμενη επιλογή για να υποβάλετε το καλάθι σας.", "app.containers.Projects.metaTitle1": "Έργο: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Ελάχιστος απαιτούμενος προϋπολογισμός", "app.containers.Projects.myBasket": "Καλάθι", "app.containers.Projects.navPoll": "Δημοσκόπηση", "app.containers.Projects.navSurvey": "Έρευνα", "app.containers.Projects.newPhase": "Νέα φάση", "app.containers.Projects.nextPhase": "Επόμενη φάση", "app.containers.Projects.noEndDate": "Δεν υπάρχει ημερομηνία λήξης", "app.containers.Projects.noItems": "Δεν έχετε επιλέξει κανένα στοιχείο ακόμα", "app.containers.Projects.noPastEvents": "Δεν υπάρχουν προηγούμενες εκδηλώσεις για εμφάνιση", "app.containers.Projects.noPhaseSelected": "Δεν έχει επιλεγεί φάση", "app.containers.Projects.noUpcomingOrOngoingEvents": "Προς το παρόν δεν έχουν προγραμματιστεί επερχόμενες ή τρέχουσες εκδηλώσεις.", "app.containers.Projects.offlineVotersTooltip": "Ο αριθμός αυτός δεν αντικατοπτρίζει τις μετρήσεις των ψηφοφόρων εκτός σύνδεσης.", "app.containers.Projects.options": "Επιλογές", "app.containers.Projects.participants": "Συμμετέχοντες", "app.containers.Projects.participantsTooltip4": "Ο αριθμός αυτός αντικατοπτρίζει επίσης τις ανώνυμες υποβολές της έρευνας. Οι ανώνυμες υποβολές ερωτήσεων είναι δυνατές εάν οι έρευνες είναι ανοικτές σε όλους (βλ. την καρτέλα {accessRightsLink} για αυτό το έργο).", "app.containers.Projects.pastEvents": "Προηγούμενες εκδηλώσεις", "app.containers.Projects.petitions": "Αναφορές", "app.containers.Projects.phases": "Φάσεις", "app.containers.Projects.previousPhase": "Προηγούμενη φάση", "app.containers.Projects.project": "Έργο", "app.containers.Projects.projectTwitterMessage": "Κάντε τη φωνή σας να ακουστεί! Συμμετέχετε στο {projectName} | {orgName}", "app.containers.Projects.projects": "Έργα", "app.containers.Projects.proposals": "Προτάσεις", "app.containers.Projects.questions": "Ερωτήσεις", "app.containers.Projects.readLess": "Διαβάστε λιγότερα", "app.containers.Projects.readMore": "Διαβάστε περισσότερα", "app.containers.Projects.removeItem": "Κατάργη<PERSON>η στοιχείου", "app.containers.Projects.requiredSelection": "Υποχρεωτική επιλογή", "app.containers.Projects.reviewDocument": "Επανεξέταση του εγγράφου", "app.containers.Projects.seeTheContributions": "Δείτε τις συνεισφορές", "app.containers.Projects.seeTheIdeas": "Δείτε τις ιδέες", "app.containers.Projects.seeTheInitiatives": "Δείτε τις πρωτοβουλίες", "app.containers.Projects.seeTheIssues": "Δείτε τα σχόλια", "app.containers.Projects.seeTheOptions": "Δείτε τις επιλογές", "app.containers.Projects.seeThePetitions": "Δείτε τις αναφορές", "app.containers.Projects.seeTheProjects": "Δείτε τα έργα", "app.containers.Projects.seeTheProposals": "Δείτε τις προτάσεις", "app.containers.Projects.seeTheQuestions": "Δείτε τις ερωτήσεις", "app.containers.Projects.seeUpcomingEvents": "Δείτε τις επερχόμενες εκδηλώσεις", "app.containers.Projects.share": "Κοινοποίηση", "app.containers.Projects.shareThisProject": "Κοινοποίηση αυτού του έργου", "app.containers.Projects.submitMyBasket": "Υποβάλετε το καλάθι σας", "app.containers.Projects.survey": "Έρευνα", "app.containers.Projects.takeThePoll": "Πάρτε μέρος στη δημοσκόπηση", "app.containers.Projects.takeTheSurvey": "Πάρτε μέρος στην έρευνα", "app.containers.Projects.timeline": "Χρονοδιάγραμμα", "app.containers.Projects.upcomingAndOngoingEvents": "Επικείμενες και τρέχουσες εκδηλώσεις", "app.containers.Projects.upcomingEvents": "Επερχόμενες εκδηλώσεις", "app.containers.Projects.whatsAppMessage": "{projectName} | από την πλατφόρμα συμμετοχής του {orgName}", "app.containers.Projects.yourBudget": "Συνολικός προϋπολογισμός", "app.containers.ProjectsIndexPage.metaDescription": "Εξερευνήστε όλα τα τρέχοντα έργα του {orgName} για να καταλάβετε πώς μπορείτε να συμμετάσχετε. Ελάτε να συζητήσετε τα τοπικά έργα που σας ενδιαφέρουν περισσότερο.", "app.containers.ProjectsIndexPage.metaTitle1": "Έργα | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Έργα", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Θέλω να γίνω εθελοντής", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Παρακαλούμε {signInLink} ή {signUpLink} πρώτα για να συμμετάσχετε εθελοντικά σε αυτή τη δραστηριότητα", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Η συμμετοχή δεν είναι προς το παρόν ανοικτή για αυτή τη δραστηριότητα.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "σύνδεση", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "εγγραφείτε", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Αποσύρω την προσφορά μου ως εθελοντής", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {δεν υπάρχουν εθελοντές} one {# εθελοντής} other {# εθελοντές}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Προειδοποίηση: Η ενσωματωμένη έρευνα μπορεί να έχει προβλήματα προσβασιμότητας για τους χρήστες συσκευών ανάγνωσης οθόνης. <PERSON><PERSON><PERSON> αντιμετωπίζετε προβλήματα, παρακαλούμε επικοινωνήστε με τον διαχειριστή της πλατφόρμας για να λάβετε έναν σύνδεσμο για την έρευνα από την αρχική πλατφόρμα. Ε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ικ<PERSON>, μπορείτε να ζητήσετε άλλους τρόπους συμπλήρωσης της έρευνας.", "app.containers.ProjectsShowPage.process.survey.survey": "Έρευνα", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Για να μάθετε αν μπορείτε να λάβετε μέρος σε αυτή την έρευνα, παρακαλούμε {logInLink} στην πλατφόρμα πρώτα.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Αυτή η έρευνα μπορεί να πραγματοποιηθεί μόνο όταν αυτή η φάση στο χρονοδιάγραμμα είναι ενεργή.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Παρακαλούμε {completeRegistrationLink} για να λάβετε μέρος στην έρευνα.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Αυτή η έρευνα δεν είναι προς το παρόν ενεργοποιημένη", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Η συμμετοχή σε αυτή την έρευνα απαιτεί επαλήθευση της ταυτότητάς σας. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Η έρευνα δεν είναι πλέον διαθέσιμη, δεδομένου ότι το έργο αυτό δεν είναι πλέον ενεργό.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "πλήρης εγγραφή", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "Σύνδεση", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "εγγραφείτε", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Επαληθεύστε το λογαριασμό σας τώρα.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Μόνο ορισμένοι χρήστες μπορούν να αναθεωρήσουν αυτό το έγγραφο. Παρακαλείστε να επισκεφθείτε πρώτα το {signUpLink} ή το {logInLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Αυτό το έγγραφο μπορεί να επανεξεταστεί μόνο όταν αυτή η φάση είναι ενεργή.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Παρακαλούμε {completeRegistrationLink} για να επανεξετάσετε το έγγραφο.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Δυστυ<PERSON><PERSON><PERSON>, δεν έχετε το δικαίωμα να αναθεωρήσετε αυτό το έγγραφο.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Η εξέταση αυτού του εγγράφου απαιτεί επαλήθευση του λογαριασμού σας. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Το έγγραφο δεν είναι πλέον διαθέσιμο, δεδομένου ότι το έργο αυτό δεν είναι πλέον ενεργό.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(συμπεριλαμβανομένου 1 offline)} other {(συμπεριλαμβανομένου # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 επιλογή} other {# επιλογές}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Το ποσοστό των συμμετεχόντων που επέλεξαν αυτή την επιλογή.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Το ποσοστό των συνολικών ψήφων που έλαβε αυτή η επιλογή.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Κόστος:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Εμφάνιση περισσότερων", "app.containers.ReactionControl.a11y_likesDislikes": "Συνολικά likes: {likesCount}, total dislikes: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Ακυρώσα<PERSON><PERSON> επιτυχώς την αντιπάθειά σας για αυτή την είσοδο.", "app.containers.ReactionControl.cancelLikeSuccess": "Ακυρώσατε επιτυχώς το like για αυτή την είσοδο.", "app.containers.ReactionControl.dislikeSuccess": "Δεν σας άρεσε αυτή η είσοδος με επιτυχία.", "app.containers.ReactionControl.likeSuccess": "Σας άρεσε αυτή η εισαγωγή με επιτυχία.", "app.containers.ReactionControl.reactionErrorSubTitle": "<PERSON><PERSON><PERSON><PERSON> σφάλματος η αντίδρασή σας δεν μπόρεσε να καταχωρηθεί. Παρα<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά σε λίγα λεπτά.", "app.containers.ReactionControl.reactionSuccessTitle": "Η αντίδρασή σας καταχωρήθηκε επιτυχώς!", "app.containers.ReactionControl.vote": "Ψηφίστε", "app.containers.ReactionControl.voted": "Ψηφίστηκε", "app.containers.SearchInput.a11y_cancelledPostingComment": "Ακυρώθηκε το σχόλιο ανάρτησης.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} τα σχόλια έχουν φορτωθεί.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# events have loaded} one {# event has loaded} other {# events have loaded}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# results have loaded} one {# result has loaded} other {# results have loaded}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# τα αποτελέσματα της αναζήτησης έχουν φορτωθεί} one {# το αποτέλεσμα της αναζήτησης έχει φορτωθεί} other {# τα αποτελέσματα της αναζήτησης έχουν φορτωθεί}}.", "app.containers.SearchInput.removeSearchTerm": "Κατάργηση όρου αναζήτησης", "app.containers.SearchInput.searchAriaLabel": "Αναζήτηση", "app.containers.SearchInput.searchLabel": "Αναζήτηση", "app.containers.SearchInput.searchPlaceholder": "Αναζήτηση", "app.containers.SearchInput.searchTerm": "Όρος αναζήτησης: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "Το FranceConnect είναι η λύση που προτείνει το γαλλικό κράτος για την ασφαλή και απλοποιημένη εγγραφή σε περισσότερες από 700 διαδικτυακές υπηρεσίες.", "app.containers.SignIn.or": "Ή", "app.containers.SignIn.signInError": "Οι παρεχόμενες πληροφορίες δεν είναι σωστές. Κάντε κλικ στο 'Ξεχάσατε τον κωδικό πρόσβασης;' για να επαναφέρετε τον κωδικό πρόσβασής σας.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Χρησιμοποιήστε το FranceConnect για να συνδεθείτε, να εγγραφείτε ή να επαληθεύσετε το λογαριασμό σας.", "app.containers.SignIn.whatIsFranceConnect": "Τι είναι το France Connect;", "app.containers.SignUp.adminOptions2": "Για διαχειριστές και διαχειριστές έργων", "app.containers.SignUp.backToSignUpOptions": "Επιστροφή στις επιλογές εγγραφής", "app.containers.SignUp.continue": "Συνεχίστε", "app.containers.SignUp.emailConsent": "Με την εγγραφή σας, συμφωνείτε να λαμβάνετε μηνύματα ηλεκτρονικού ταχυδρομείου από αυτή την πλατφόρμα. Μπορείτε να επιλέξετε τα email που επιθυμείτε να λαμβάνετε στη σελίδα \"Οι ρυθμίσεις μου\".", "app.containers.SignUp.emptyFirstNameError": "Εισάγετε το μικρό σας όνομα", "app.containers.SignUp.emptyLastNameError": "Εισάγετε το επώνυμό σας", "app.containers.SignUp.firstNamesLabel": "Μικρό όνομα", "app.containers.SignUp.goToLogIn": "Έχετε ήδη λογαριασμό; {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Έχω διαβάσει και συμφωνώ με το {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Έχω διαβάσει και συμφωνώ με το {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Αποδέχομαι ότι τα δεδομένα θα χρησιμοποιηθούν στο mitgestalten.wien.gv.at. Περαιτέρω πληροφορίες μπορείτε να βρείτε {link}.", "app.containers.SignUp.invitationErrorText": "Η πρόσκλησή σας έχει λήξει ή έχει ήδη χρησιμοποιηθεί. Εάν έχετε ήδη χρησιμοποιήσει τον σύνδεσμο πρόσκλησης για τη δημιουργία λογαριασμού, προσπαθήστε να συνδεθείτε. Διαφορετικ<PERSON>, εγγραφείτε για να δημιουργήσετε έναν νέο λογαριασμό.", "app.containers.SignUp.lastNameLabel": "Επώνυμο", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Ακολουθήστε τους τομείς εστίασης που επιθυμείτε για να ενημερώνεστε γι' αυτούς:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Ακολουθήστε τα αγαπημένα σας θέματα για να ενημερώνεστε γι' αυτά:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Αποθήκευση προτιμήσεων", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Παραλε<PERSON>ψτε προς το παρόν", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Αποδεχτείτε την πολιτική απορρήτου μας για να προχωρήσετε", "app.containers.SignUp.signUp2": "Εγγραφείτε", "app.containers.SignUp.skip": "Παραλείψτε αυτό το βήμα", "app.containers.SignUp.tacError": "Αποδεχτείτε τους όρους και τις προϋποθέσεις μας για να προχωρήσετε", "app.containers.SignUp.thePrivacyPolicy": "την πολιτική απορρήτου", "app.containers.SignUp.theTermsAndConditions": "τους όρους και τις προϋποθέσεις", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {Φαίνετα<PERSON> ότι προσπαθήσατε να εγγραφείτε πριν χωρίς να ολοκληρώσετε τη διαδικασία. Κάντ<PERSON> κλικ στην επιλογή Σύνδεση αντ'αυτού, χρησιμοποιώντας τα διαπιστευτήρια που επιλέχθηκαν κατά την προηγούμενη προσπάθεια.} other {Κάτι πήγε στραβά. Παρακαλούμε προσπαθήστε ξανά αργότερα.}}", "app.containers.SignUp.viennaConsentEmail": "Διεύθυνση email", "app.containers.SignUp.viennaConsentFirstName": "Μικρό όνομα", "app.containers.SignUp.viennaConsentFooter": "Μπορείτε να αλλάξετε τις πληροφορίες του προφίλ σας μετά την είσοδο. Εάν έχετε ήδη έναν λογαριασμό με την ίδια διεύθυνση email στο mitgestalten.wien.gv.at, αυτός θα συνδεθεί με τον τρέχοντα λογαριασμό σας.", "app.containers.SignUp.viennaConsentHeader": "Θα μεταδοθούν τα ακόλουθα δεδομένα:", "app.containers.SignUp.viennaConsentLastName": "Επώνυμο", "app.containers.SignUp.viennaConsentUserName": "Όνομα χρήστη", "app.containers.SignUp.viennaDataProtection": "την πολιτική απορρήτου της Β<PERSON>έ<PERSON>ης", "app.containers.SiteMap.contributions": "Συνεισφορές", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Σχόλια", "app.containers.SiteMap.options": "Επιλογές", "app.containers.SiteMap.projects": "Έργα", "app.containers.SiteMap.questions": "Ερωτήσεις", "app.containers.SpamReport.buttonSave": "Αναφορά", "app.containers.SpamReport.buttonSuccess": "Επιτυχία", "app.containers.SpamReport.inappropriate": "Είν<PERSON>ι ακατάλληλο ή προσβλητικό", "app.containers.SpamReport.messageError": "Υπήρξε σφάλμα κατά την υποβολή της φόρμας, παρακαλούμε προσπαθήστε ξανά.", "app.containers.SpamReport.messageSuccess": "Η αναφορά σας έχει αποσταλεί", "app.containers.SpamReport.other": "Άλλος λόγος", "app.containers.SpamReport.otherReasonPlaceholder": "Περιγραφή", "app.containers.SpamReport.wrong_content": "Δεν είναι σχετικό", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Κατάργηση εικόνας προφίλ", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Οι ψήφοι σας σε προτάσεις που είναι ακόμη ανοικτές για ψηφοφορία θα διαγραφούν. Οι ψήφοι για προτάσεις για τις οποίες έχει λήξει η περίοδος ψηφοφορίας δεν θα διαγραφούν.", "app.containers.UsersEditPage.addPassword": "Προσθήκη κωδικού πρόσβασης", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Για να συμμετάσχετε σε έργα που απαιτούν επαλήθευση.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Επαληθεύστε την ταυτότητά σας", "app.containers.UsersEditPage.bio": "Σχετικά με εσάς", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Δεν μπορείτε να επεξεργαστείτε αυτό το πεδίο επειδή περιέχει επαληθευμένες πληροφορίες.", "app.containers.UsersEditPage.buttonSuccessLabel": "Επιτυχία", "app.containers.UsersEditPage.cancel": "Ακύρωση", "app.containers.UsersEditPage.changeEmail": "Αλλαγή email", "app.containers.UsersEditPage.changePassword2": "Αλλαγ<PERSON> κωδικού πρόσβασης", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Παρακαλούμε κάντε κλικ εδώ για να ενημερώσετε την επαλήθευσή σας.", "app.containers.UsersEditPage.conditionsLinkText": "τις προϋποθέσεις μας", "app.containers.UsersEditPage.contactUs": "Άλλος ένας λόγος για να φύγετε; {feedbackLink} και ίσως μπορούμε να βοηθήσουμε.", "app.containers.UsersEditPage.deleteAccountSubtext": "Λυπούμαστε που σας βλέπουμε να φεύγετε.", "app.containers.UsersEditPage.deleteMyAccount": "Διαγραφή του λογαριασμού μου", "app.containers.UsersEditPage.deleteYourAccount": "Διαγραφή του λογαριασμού σας", "app.containers.UsersEditPage.deletionSection": "Διαγραφή του λογαριασμού σας", "app.containers.UsersEditPage.deletionSubtitle": "Αυτή η ενέργεια δεν μπορεί να ανακληθεί. Το περιεχόμενο που δημοσιεύσατε στην πλατφόρμα θα ανωνυμοποιηθεί. Εάν επιθυμείτε να διαγράψετε όλο το περιεχόμενό σας, μπορείτε να επικοινωνήσετε μαζί μας στο <EMAIL>.", "app.containers.UsersEditPage.email": "Email", "app.containers.UsersEditPage.emailEmptyError": "Δώστε μια διεύθυνση email", "app.containers.UsersEditPage.emailInvalidError": "Παρέχετε μια διεύθυνση email στη σωστή μορφή, για παράδειγμα <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Ενημερώστε μας", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Μικρό όνομα", "app.containers.UsersEditPage.firstNamesEmptyError": "Δώστε ένα μικρό όνομα", "app.containers.UsersEditPage.h1": "Τα στοιχεία του λογαριασμού σας", "app.containers.UsersEditPage.h1sub": "Επεξεργαστείτε τις πληροφορίες του λογαριασμού σας", "app.containers.UsersEditPage.image": "Εικόνα avatar", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Κάντε κλικ για να επιλέξετε μια εικόνα προφίλ (μέγ. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Όλες οι ρυθμίσεις για το προφίλ σας", "app.containers.UsersEditPage.language": "Γλώσσα", "app.containers.UsersEditPage.lastName": "Επώνυμο", "app.containers.UsersEditPage.lastNameEmptyError": "Δώστε ένα επώνυμο", "app.containers.UsersEditPage.loading": "Φόρτωση...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Μπορείτε να αλλάξετε το email ή τον κωδικό πρόσβασής σας εδώ.", "app.containers.UsersEditPage.loginCredentialsTitle": "Πιστοποιη<PERSON><PERSON><PERSON><PERSON> σύνδεσης", "app.containers.UsersEditPage.messageError": "Δεν μπορέσαμε να αποθηκεύσουμε το προφίλ σας. Προσπαθήστε ξανά αργότερα ή επικοινωνήστε με το <EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Το προφίλ σας έχει αποθηκευτεί.", "app.containers.UsersEditPage.metaDescription": "Αυτή είναι η σελίδα ρυθμίσεων του προφίλ του/της {firstName} {lastName} στην ηλεκτρονική πλατφόρμα συμμετοχής του {tenantName}. Εδώ μπορείτε να επαληθεύσετε την ταυτότητά σας, να επεξεργαστείτε τα στοιχεία του λογαριασμού σας, να διαγράψετε τον λογαριασμό σας και να επεξεργαστείτε τις προτιμήσεις ηλεκτρονικού ταχυδρομείου σας.", "app.containers.UsersEditPage.metaTitle1": "Σελίδα ρυθμίσεων προφίλ του {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Μόλις πατήσετε αυτό το κουμπί, δεν θα έχουμε τρόπο να επαναφέρουμε τον λογαριασμό σας.", "app.containers.UsersEditPage.noNameWarning2": "Το όνομά σας εμφανίζεται επί του παρόντος στην πλατφόρμα ως: \"{displayName}\" επειδή δεν έχετε εισάγει το όνομά σας. Αυτ<PERSON> είναι ένα όνομα που δημιουργείται αυτόματα. Αν θέλετε να το αλλάξετε, παρακαλούμε εισάγετε το όνομά σας παρακάτω.", "app.containers.UsersEditPage.notificationsSubTitle": "Τι είδους ειδοποιήσεις ηλεκτρονικού ταχυδρομείου θέλετε να λαμβάνετε;", "app.containers.UsersEditPage.notificationsTitle": "Ειδοποι<PERSON><PERSON><PERSON>ις μέσω ηλεκτρονικού ταχυδρομείου", "app.containers.UsersEditPage.password": "Επιλογή νέου κωδικού πρόσβασης", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Δώστε έναν κωδικ<PERSON> πρόσβασης που να έχει μήκος τουλάχιστον {minimumPasswordLength} χαρακτήρες", "app.containers.UsersEditPage.passwordAddSection": "Προσθέστε έναν κωδικό πρόσβασης", "app.containers.UsersEditPage.passwordAddSubtitle2": "Ορίστε έναν κωδικό πρόσβασης και συνδεθείτε εύκολα στην πλατφόρμα, χωρ<PERSON>ς να χρειάζεται να επιβεβαιώνετε κάθε φορά το email σας.", "app.containers.UsersEditPage.passwordChangeSection": "Αλλάξτε τον κωδικό πρόσβασής σας", "app.containers.UsersEditPage.passwordChangeSubtitle": "Επιβεβαιώστε τον τρέχοντα κωδικό πρόσβασης και αλλάξτε τον σε νέο κωδικό πρόσβασης.", "app.containers.UsersEditPage.privacyReasons": "<PERSON><PERSON><PERSON> ανησυχείτε για το απόρρητό σας, μπορείτε να διαβάσετε το {conditionsLink}.", "app.containers.UsersEditPage.processing": "Αποστολή...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Το μικρό όνομα απαιτε<PERSON><PERSON><PERSON><PERSON> όταν παρέχεται το επώνυμο", "app.containers.UsersEditPage.reasonsToStayListTitle": "Πριν ξεκινήσετε...", "app.containers.UsersEditPage.submit": "Αποθήκευση αλλαγών", "app.containers.UsersEditPage.tooManyEmails": "Λαμβάνετε πάρα πολλά μηνύματα ηλεκτρονικού ταχυδρομείου; Μπορείτε να διαχειριστείτε τις προτιμήσεις σας για τα email στις ρυθμίσεις του προφίλ σας.", "app.containers.UsersEditPage.updateverification": "Άλλαξαν τα επίσημα στοιχεία σας; {reverify<PERSON>utton}", "app.containers.UsersEditPage.user": "Πότε θέλετε να σας στείλουμε ένα email για να σας ειδοποιήσουμε;", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Μπορείτε να συμμετέχετε σε έργα που απαιτούν επαλήθευση.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Έχετε επαληθευτεί", "app.containers.UsersEditPage.verifyNow": "Επαλήθευση τώρα", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Κατεβάστε τις απαντήσεις σας (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {Δεν μου αρέσει} one {1 like} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Εισήγηση στην οποία δημοσιεύτηκε αυτό το σχόλιο ως απάντηση:", "app.containers.UsersShowPage.areas": "Περιοχ<PERSON>ς", "app.containers.UsersShowPage.commentsWithCount": "Σχόλια ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Επεξεργα<PERSON>ία του προφίλ μου", "app.containers.UsersShowPage.emptyInfoText": "Δεν ακολουθείτε κανένα στοιχείο του καθορισμένου φίλτρου παραπάνω.", "app.containers.UsersShowPage.eventsWithCount": "Εκδηλώσεις ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Ακολουθώντας ({followingCount})", "app.containers.UsersShowPage.inputs": "Είσοδοι", "app.containers.UsersShowPage.invisibleTitlePostsList": "Όλες οι εισηγήσεις που υποβλήθηκαν από αυτόν τον συμμετέχοντα", "app.containers.UsersShowPage.invisibleTitleUserComments": "Όλα τα σχόλια που δημοσιεύτηκαν από αυτόν τον συμμετέχοντα", "app.containers.UsersShowPage.loadMore": "Φορτώστε περισσότερα", "app.containers.UsersShowPage.loadMoreComments": "Φορτώστε περισσότερα σχόλια", "app.containers.UsersShowPage.loadingComments": "Φόρτωση σχολίων...", "app.containers.UsersShowPage.loadingEvents": "Φόρτωση συμβάντων...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON><PERSON><PERSON> απ<PERSON> {date}", "app.containers.UsersShowPage.metaTitle1": "Σελίδα προφίλ του {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Αυτό το άτομο δεν έχει δημοσιεύσει ακόμα σχόλια.", "app.containers.UsersShowPage.noCommentsForYou": "Δεν υπάρχουν ακόμη σχόλια εδώ.", "app.containers.UsersShowPage.noEventsForUser": "Δεν έχετε παρακολουθήσει καμία εκδήλωση ακόμα.", "app.containers.UsersShowPage.postsWithCount": "Υποβολές ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Φάκελοι έργου", "app.containers.UsersShowPage.projects": "Έργα", "app.containers.UsersShowPage.proposals": "Προτάσεις", "app.containers.UsersShowPage.seePost": "Δείτε την υποβολή", "app.containers.UsersShowPage.surveyResponses": "Απαντήσεις ({responses})", "app.containers.UsersShowPage.topics": "Θέματα", "app.containers.UsersShowPage.tryAgain": "Εμφανίστηκε σφάλμα, παρακαλούμε προσπαθήστε ξανά αργότερα.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Αυτή είναι η σελίδα του προφίλ του/της {firstName} {lastName} στην διαδικτυακή πλατφόρμα συμμετοχής του {orgName}. Εδώ είναι μια επισκόπηση όλων των εισηγήσεών τους.", "app.containers.VoteControl.close": "Κλείσιμο", "app.containers.VoteControl.voteErrorTitle": "Κάτι πήγε στραβά", "app.containers.admin.ContentBuilder.default": "προεπιλογή", "app.containers.admin.ContentBuilder.imageTextCards": "Κάρ<PERSON><PERSON>ς εικόνας & κειμένου", "app.containers.admin.ContentBuilder.infoWithAccordions": "Πληροφορίες & ακορντε<PERSON>ν", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 στήλη", "app.containers.admin.ContentBuilder.projectDescription": "Περιγραφή του έργου", "app.containers.app.navbar.admin": "Διαχείριση πλατφόρμας", "app.containers.app.navbar.allProjects": "Όλα τα έργα", "app.containers.app.navbar.ariaLabel": "Πρωτεύον", "app.containers.app.navbar.closeMobileNavMenu": "Κλείστε το μενού πλοήγησης για κινητά", "app.containers.app.navbar.editProfile": "Οι ρυθμίσεις μου", "app.containers.app.navbar.fullMobileNavigation": "Πλήρες για κινητά", "app.containers.app.navbar.logIn": "Σύνδεση", "app.containers.app.navbar.logoImgAltText": "{orgName} Αρχική σελίδα", "app.containers.app.navbar.myProfile": "Η δραστηριότητά μου", "app.containers.app.navbar.search": "Αναζήτηση", "app.containers.app.navbar.showFullMenu": "Εμφάνιση πλήρους μενού", "app.containers.app.navbar.signOut": "Αποσύνδεση", "app.containers.eventspage.errorWhenFetchingEvents": "Προέκυψε σφάλμα κατά τη φόρτωση των εκδηλώσεων. Προσπαθήστε να επαναφορτώσετε τη σελίδα.", "app.containers.eventspage.events": "Εκδηλώσεις", "app.containers.eventspage.eventsPageDescription": "Εμφάνιση όλων των εκδηλώσεων που έχουν δημοσιευθεί στην πλατφόρμα του {orgName}.", "app.containers.eventspage.eventsPageTitle1": "Εκδηλώσεις | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Έργα", "app.containers.eventspage.noPastEvents": "Δεν υπάρχουν προηγούμενες εκδηλώσεις για εμφάνιση", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Προς το παρόν δεν έχουν προγραμματιστεί επερχόμενες ή τρέχουσες εκδηλώσεις.", "app.containers.eventspage.pastEvents": "Προηγούμενες εκδηλώσεις", "app.containers.eventspage.upcomingAndOngoingEvents": "Επικείμενες και τρέχουσες εκδηλώσεις", "app.containers.footer.accessibility-statement": "Δήλωση προσβασιμότητας", "app.containers.footer.ariaLabel": "Δευτερεύον", "app.containers.footer.cookie-policy": "Πολιτική cookie", "app.containers.footer.cookieSettings": "Ρυθμίσεις cookie", "app.containers.footer.feedbackEmptyError": "Το πεδίο ανατροφοδότησης δεν μπορεί να είναι κενό.", "app.containers.footer.poweredBy": "Υποστηρίζεται από", "app.containers.footer.privacy-policy": "Πολιτική απορρήτου", "app.containers.footer.siteMap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ιστότοπου", "app.containers.footer.terms-and-conditions": "Όροι και προϋποθέσεις", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Ακύρωση", "app.containers.ideaHeading.confirmLeaveFormButtonText": "Ναι, θέλω να φύγω", "app.containers.ideaHeading.editForm": "Επεξεργασία φόρμας", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Είσαι σίγουρος ότι θέλεις να φύγεις;", "app.containers.ideaHeading.leaveIdeaForm": "Αφήστε τη φόρμα ιδεών", "app.containers.ideaHeading.leaveIdeaText": "Οι απαντήσεις σας δεν θα αποθηκευτούν.", "app.containers.landing.cityProjects": "Έργα", "app.containers.landing.completeProfile": "Συμπληρώστε το προφίλ σας", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήρθατε, {firstName}. Ήρθε η ώρα να συμπληρώσετε το προφίλ σας.", "app.containers.landing.createAccount": "Εγγραφείτε", "app.containers.landing.defaultSignedInMessage": "Το {orgName} σας ακούει. Είναι η σειρά σας να κάνετε τη φωνή σας να ακουστεί!", "app.containers.landing.doItLater": "Θα το κάνω αργότερα", "app.containers.landing.new": "νέα", "app.containers.landing.subtitleCity": "<PERSON><PERSON><PERSON><PERSON>ς ήρθατε στην πλατφόρμα συμμετοχής του {orgName}", "app.containers.landing.titleCity": "Ας διαμορφώσουμε μαζί το μέλλον του {orgName}", "app.containers.landing.twitterMessage": "Ψηφίστε την {ideaTitle} στο", "app.containers.landing.upcomingEventsWidgetTitle": "Επικείμενες και τρέχουσες εκδηλώσεις", "app.containers.landing.userDeletedSubtitle": "Μπορείτε να δημιουργήσετε έναν νέο λογαριασμ<PERSON> ανά πάσα στιγμή ή {contactLink} για να μας ενημερώσετε για το τι μπορούμε να βελτιώσουμε.", "app.containers.landing.userDeletedSubtitleLinkText": "επικοινωνήστε μαζί μας", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Ο λογαριασμός σας έχει διαγραφεί.", "app.containers.landing.userDeletionFailed": "Παρουσιάστηκε σφάλμα κατά τη διαγραφή του λογαριασμού σας, έχουμε ενημερωθεί για το πρόβλημα και θα κάνουμε ό,τι καλύτερο μπορούμε για να το διορθώσουμε. Παρακαλούμε δοκιμάστε ξανά αργότερα.", "app.containers.landing.verifyNow": "Επαλήθευση τώρα", "app.containers.landing.verifyYourIdentity": "Επαληθεύστε την ταυτότητά σας", "app.containers.landing.viewAllEventsText": "Προβολή όλων των εκδηλώσεων", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Π<PERSON><PERSON>ω στο φάκελο", "app.errors.after_end_at": "Η ημερομηνία έναρξης εμφανίζεται μετά την ημερομηνία λήξης", "app.errors.avatar_carrierwave_download_error": "Δεν ήταν δυνατή η λήψη του αρχείου avatar.", "app.errors.avatar_carrierwave_integrity_error": "Το αρχείο avatar δεν ανήκει σε επιτρεπόμενο τύπο.", "app.errors.avatar_carrierwave_processing_error": "Δεν ήταν δυνατή η επεξεργασία του avatar.", "app.errors.avatar_extension_blacklist_error": "Η επέκταση αρχείου της εικόνας avatar δεν επιτρέπεται. Οι επιτρεπόμενες επεκτάσεις είναι: jpg, jpeg, gif και png.", "app.errors.avatar_extension_whitelist_error": "Η επέκταση αρχείου της εικόνας avatar δεν επιτρέπεται. Οι επιτρεπόμενες επεκτάσεις είναι: jpg, jpeg, gif και png.", "app.errors.banner_cta_button_multiloc_blank": "Εισάγετε ένα κείμενο κουμπιού.", "app.errors.banner_cta_button_url_blank": "Εισάγετε έναν σύνδεσμο.", "app.errors.banner_cta_button_url_url": "Εισάγετε έναν έγκυρο σύνδεσμο. Βεβαιωθείτε ότι ο σύνδεσμος αρχίζει με 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Εισάγετε ένα κείμενο κουμπιού.", "app.errors.banner_cta_signed_in_url_blank": "Εισάγετε έναν σύνδεσμο.", "app.errors.banner_cta_signed_in_url_url": "Εισάγετε έναν έγκυρο σύνδεσμο. Βεβαιωθείτε ότι ο σύνδεσμος αρχίζει με 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Εισάγετε ένα κείμενο κουμπιού.", "app.errors.banner_cta_signed_out_url_blank": "Εισάγετε έναν σύνδεσμο.", "app.errors.banner_cta_signed_out_url_url": "Εισάγετε έναν έγκυρο σύνδεσμο. Βεβαιωθείτε ότι ο σύνδεσμος αρχίζει με 'https://'.", "app.errors.base_includes_banned_words": "Μπορεί να έχετε χρησιμοποιήσει μία ή περισσότερες λέξεις που θεωρούνται βωμολοχίες. Παρακαλούμε τροποποιήστε το κείμενό σας για να αφαιρέσετε τυχόν βωμολοχίες που μπορεί να υπάρχουν.", "app.errors.body_multiloc_includes_banned_words": "Η περιγραφή περιέχει λέξεις που θεωρούνται ακατάλληλες.", "app.errors.bulk_import_idea_not_valid": "Η ιδέα που προκύπτει δεν είναι έγκυρη: {value}.", "app.errors.bulk_import_image_url_not_valid": "Δεν ήταν δυνατή η λήψη εικόνας από το {value}. Βεβαιωθείτε ότι η διεύθυνση URL είναι έγκυρη και τελειώνει με επέκταση αρχείου όπως .png ή .jpg. Αυτό το πρόβλημα εμφανίζεται στη σειρά με αναγνωριστικό {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Θέση ιδέας με ελλείπουσα συντεταγμένη στο {value}. Αυτό το πρόβλημα εμφανίζεται στη σειρά με αναγνωριστικό {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Θέση ιδέας με μη αριθμητική συντεταγμένη στο {value}. Αυτό το πρόβλημα εμφανίζεται στη σειρά με αναγνωριστικό {row}.", "app.errors.bulk_import_malformed_pdf": "Το αρχείο PDF που μεταφορτώθηκε φαίνεται να είναι κακοσχηματισμένο. Δοκιμάστε να εξαγάγετε ξανά το PDF από την πηγή σας και, στη συνέχεια, ανεβάστε το ξανά.", "app.errors.bulk_import_maximum_ideas_exceeded": "Έχει ξεπεραστεί το μέγιστο όριο των {value} ιδεών.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Έχει ξεπεραστεί το μέγιστο όριο των {value} σελίδων σε ένα PDF.", "app.errors.bulk_import_not_enough_pdf_pages": "Το PDF που μεταφορτώθηκε δεν έχει αρκετές σελίδες - θα πρέπει να έχει τουλάχιστον τον ίδιο αριθμό σελίδων με το πρότυπο που μεταφορτώθηκε.", "app.errors.bulk_import_publication_date_invalid_format": "Ιδέα με άκυρη μορφή ημερομηνίας δημοσίευσης \"{value}\". Χρησιμοποιήστε τη μορφή \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "Η μέθοδος συμμετοχής που επιλέξατε δεν υποστηρίζει αυτόν τον τύπο δημοσίευσης. Παρακαλούμε επεξεργαστείτε την επιλογή σας και δοκιμάστε ξανά.", "app.errors.cant_change_after_first_response": "Δεν μπορείτε πλέον να το αλλάξετε αυτό, καθ<PERSON><PERSON> κάποιοι χρήστες έχουν ήδη ανταποκριθεί", "app.errors.category_name_taken": "Μια κατηγορία με αυτό το όνομα υπάρχει ήδη", "app.errors.confirmation_code_expired": "Ο κωδικός έχει λήξει. Παρακαλούμε ζητήστε έναν νέο κωδικό.", "app.errors.confirmation_code_invalid": "Μη έγκυρος κωδικ<PERSON>ς επιβεβαίωσης. Παρακαλούμε ελέγξτε το email σας για τον σωστό κωδικό ή δοκιμάστε 'Αποστολή νέου κωδικού'", "app.errors.confirmation_code_too_many_resets": "Έχετε ξαναστείλει τον κωδικό επιβεβαίωσης πάρα πολλές φορές. Παρακαλούμε επικοινωνήστε μαζί μας για να λάβετε αντί αυτού έναν κωδικό πρόσκλησης.", "app.errors.confirmation_code_too_many_retries": "Προσπαθήσατε πάρα πολλές φορές. Παρακαλούμε ζητήστε νέο κωδικό ή δοκιμάστε να αλλάξετε το email σας.", "app.errors.email_already_active": "Η διεύθυνση email {value} που βρέθηκε στη σειρά {row} ανήκει ήδη σε εγγεγραμμένο συμμετέχοντα", "app.errors.email_already_invited": "Η διεύθυνση email {value} που βρέθηκε στη σειρά {row} έχει ήδη προσκληθεί", "app.errors.email_blank": "Αυτό δεν μπορεί να είναι κενό", "app.errors.email_domain_blacklisted": "Παρακαλούμε χρησιμοποιήστε έναν διαφορετικό τομέα ηλεκτρονικού ταχυδρομείου για να εγγραφείτε.", "app.errors.email_invalid": "Παρακαλούμε χρησιμοποιήστε μια έγκυρη διεύθυνση email.", "app.errors.email_taken": "Ένας λογαριασμός με αυτό το email υπάρχει ήδη. Μπορείτε να συνδεθείτε αντί αυτού.", "app.errors.email_taken_by_invite": "Το {value} είναι ήδη κατειλημμένο από μια πρόσκληση σε εκκρεμότητα. Ελέγξτε το φάκελο ανεπιθύμητης αλληλογραφίας ή επικοινωνήστε με το {supportEmail} αν δεν μπορείτε να το βρείτε.", "app.errors.emails_duplicate": "Μία ή περισσότερες διπλές τιμές για τη διεύθυνση email {value} βρέθηκαν στην/στις ακόλουθη/ες γραμμή/ες: {rows}", "app.errors.extension_whitelist_error": "Η μορφή του αρχείου που προσπαθήσατε να μεταφορτώσετε δεν υποστηρίζεται.", "app.errors.file_extension_whitelist_error": "Η μορφή του αρχείου που προσπαθήσατε να ανεβάσετε δεν υποστηρίζεται.", "app.errors.first_name_blank": "Αυτό δεν μπορεί να είναι κενό", "app.errors.generics.blank": "Αυτό δεν μπορεί να είναι κενό.", "app.errors.generics.invalid": "Αυτό δεν μοιάζει με έγκυρη τιμή", "app.errors.generics.taken": "Αυτό το email υπάρχει ήδη. Ένας άλλος λογαριασμός είναι συνδεδεμένος με αυτό.", "app.errors.generics.unsupported_locales": "Αυτό το πεδίο δεν υποστηρίζει την τρέχουσα τοπική γλώσσα.", "app.errors.group_ids_unauthorized_choice_moderator": "Ως διαχειριστής έργου, μπορείτε να στέλνετε email μόνο σε άτομα που έχουν πρόσβαση στο/στα έργο/α σας", "app.errors.has_other_overlapping_phases": "Τα έργα δεν μπορούν να έχουν επικαλυπτόμενες φάσεις.", "app.errors.invalid_email": "Το email {value} που βρέθηκε στη γραμμή {row} δεν είναι έγκυρη διεύθυνση email", "app.errors.invalid_row": "Προέκυ<PERSON><PERSON> άγνωστο σφάλμα κατά την προσπάθεια επεξεργασίας της γραμμής {row}", "app.errors.is_not_timeline_project": "Το τρέχον έργο δεν υποστηρίζει φάσεις.", "app.errors.key_invalid": "Το κλειδί μπορεί να περιέχει μόνο γράμματα, αριθμούς και υπογράμμιση(_)", "app.errors.last_name_blank": "Αυτό δεν μπορεί να είναι κενό", "app.errors.locale_blank": "Παρακαλούμε επιλέξτε μια γλώσσα", "app.errors.locale_inclusion": "Παρακαλούμε επιλέξτε μια υποστηριζόμενη γλώσσα", "app.errors.malformed_admin_value": "Η τιμή διαχειριστή {value} που βρέθηκε στη γραμμή {row} δεν είναι έγκυρη", "app.errors.malformed_groups_value": "Η ομάδα {value} που βρέθηκε στη γραμμή {row} δεν είναι έγκυρη ομάδα", "app.errors.max_invites_limit_exceeded1": "Ο αριθμός των προσκλήσεων υπερβαίνει το όριο των 1000.", "app.errors.maximum_attendees_greater_than1": "Ο μέγιστος αριθμός εγγεγραμμένων πρέπει να είναι μεγαλύτερος από 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Ο μέγιστος αριθμός εγγεγραμμένων πρέπει να είναι μεγαλύτερος ή ίσος με τον τρέχοντα αριθμό εγγεγραμμένων.", "app.errors.no_invites_specified": "Δεν μπόρεσε να βρεθεί καμία διεύθυνση email.", "app.errors.no_recipients": "Η καμπάνια δεν μπορεί να αποσταλεί επειδή δεν υπάρχουν παραλήπτες. Η ομάδα στην οποία στέλνετε είναι κενή ή κανείς δεν έχει συναινέσει στη λήψη μηνυμάτων ηλεκτρονικού ταχυδρομείου.", "app.errors.number_invalid": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε έναν έγκυρο αριθμό.", "app.errors.password_blank": "Αυτό δεν μπορεί να είναι κενό", "app.errors.password_invalid": "Ελέγξτε ξανά τον τρέχοντα κωδικό πρόσβασής σας.", "app.errors.password_too_short": "Ο κωδικός πρόσβασης πρέπει να αποτελείται από τουλάχιστον 8 χαρακτήρες", "app.errors.resending_code_failed": "Κάτι πήγε στραβ<PERSON> κατά την αποστολή του κωδικού επιβεβαίωσης.", "app.errors.slug_taken": "Αυτή η διεύθυνση URL του έργου υπάρχει ήδη. Παρακαλούμε αλλάξτε το slug του έργου σε κάτι άλλο.", "app.errors.tag_name_taken": "Μια ετικέτα με αυτό το όνομα υπάρχει ήδη", "app.errors.title_multiloc_blank": "Ο τίτλος δεν μπορεί να είναι κενός.", "app.errors.title_multiloc_includes_banned_words": "Ο τίτλος περιέχει λέξεις που θεωρούνται ακατάλληλες.", "app.errors.token_invalid": "Οι σύνδεσμοι επαναφορ<PERSON>ς κωδικού πρόσβασης μπορούν να χρησιμοποιηθούν μόνο μία φορά και ισχύουν για μία ώρα μετά την αποστολή τους. {passwordResetLink}.", "app.errors.too_common": "<PERSON>υτ<PERSON><PERSON> ο κωδικός πρόσβασης μπορεί εύκολα να γίνει αντιληπτός. Παρακαλούμε επιλέξτε έναν ισχυρότερο κωδικό πρόσβασης.", "app.errors.too_long": "Παρακαλούμε επιλέξτε έναν μικρότερο κωδικό πρόσβασης (μέγιστο 72 χαρακτήρες)", "app.errors.too_short": "Παρακαλούμε επιλέξτε έναν κωδικό πρόσβασης με τουλάχιστον 8 χαρακτήρες", "app.errors.uncaught_error": "Εμφανίστηκε ένα άγνωστο σφάλμα.", "app.errors.unknown_group": "Η ομάδα {value} που βρέθηκε στη γραμμή {row} δεν είναι γνωστή ομάδα", "app.errors.unknown_locale": "Η γλώσσα {value} που βρέθηκε στη γραμμή {row} δεν είναι μια διαμορφωμένη γλώσσα", "app.errors.unparseable_excel": "Το επιλεγμένο αρχείο Excel δεν μπόρεσε να επεξεργαστεί.", "app.errors.url": "Εισάγετε έναν έγκυρο σύνδεσμο. Βεβαιωθείτε ότι ο σύνδεσμος αρχίζει με https://", "app.errors.verification_taken": "Η επαλήθευση δεν μπορεί να ολοκληρωθεί καθώς έχει επαληθευτεί άλλος λογαριασμός με τα ίδια στοιχεία.", "app.errors.view_name_taken": "Μια προβολή με αυτό το όνομα υπάρχει ήδη", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Το ακατάλληλο περιεχόμενο εντοπίστηκε αυτόματα σε μια δημοσίευση ή ένα σχόλιο", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Σύνδεση στο StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Εγγραφή στο StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Δημιουργήστε τώρα έναν λογαριασμό Stadt Wien και χρησιμοποιήστε μία σύνδεση για πολλές ψηφιακές υπηρεσίες της Βιέννης.", "app.modules.id_cow.cancel": "Ακύρωση", "app.modules.id_cow.emptyFieldError": "Αυτό το πεδίο δεν μπορεί να είναι κενό.", "app.modules.id_cow.helpAltText": "Δείχνει πού μπορείτε να βρείτε τον σειριακό αριθμό ταυτότητας σε ένα δελτίο ταυτότητας", "app.modules.id_cow.invalidIdSerialError": "Μη έγκυρος σειρι<PERSON><PERSON><PERSON>ς αριθμός ταυτότητας", "app.modules.id_cow.invalidRunError": "Μη έγ<PERSON>υ<PERSON><PERSON> R<PERSON>", "app.modules.id_cow.noMatchFormError": "Δεν βρέθηκε καμία αντιστοιχία.", "app.modules.id_cow.notEntitledFormError": "Δεν δικαιούται.", "app.modules.id_cow.showCOWHelp": "Πού μπορώ να βρω τον σειριακ<PERSON> αριθμό της ταυτότητάς μου;", "app.modules.id_cow.somethingWentWrongError": "Δεν μπορούμε να σας επαληθεύσουμε επειδή κάτι πήγε στραβά", "app.modules.id_cow.submit": "Υποβολή", "app.modules.id_cow.takenFormError": "Ήδη κατειλημμένο.", "app.modules.id_cow.verifyCow": "Επαλήθευση με COW", "app.modules.id_franceconnect.verificationButtonAltText": "Επαλήθευση με FranceConnect", "app.modules.id_gent_rrn.cancel": "Ακύρωση", "app.modules.id_gent_rrn.emptyFieldError": "Αυτό το πεδίο δεν μπορεί να είναι κενό.", "app.modules.id_gent_rrn.gentRrnHelp": "Ο αριθμός κοινωνικής ασφάλισής σας αναγράφεται στο πίσω μέρος του ψηφιακού δελτίου ταυτότητάς σας", "app.modules.id_gent_rrn.invalidRrnError": "Μη έγκυρος αριθμός κοινωνικής ασφάλισης", "app.modules.id_gent_rrn.noMatchFormError": "Δεν μπορέσαμε να βρούμε πίσω πληροφορίες για τον αριθμό κοινωνικής ασφάλισής σας", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Δεν μπορούμε να σας επαληθεύσουμε επειδή ζείτε εκτός Γάνδης", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Δεν μπορούμε να σας επαληθεύσουμε επειδή είστε μικρότεροι από 14 ετών", "app.modules.id_gent_rrn.rrnLabel": "Αριθμ<PERSON>ς μητρώου κοινωνικής ασφάλισης", "app.modules.id_gent_rrn.rrnTooltip": "Ζητάμε τον αριθμό κοινωνικής ασφάλισής σας για να επαληθεύσουμε αν είστε πολίτης της Γ<PERSON>δη<PERSON>, άνω των 14 ετών.", "app.modules.id_gent_rrn.showGentRrnHelp": "Πού μπορώ να βρω τον σειριακ<PERSON> αριθμό της ταυτότητάς μου;", "app.modules.id_gent_rrn.somethingWentWrongError": "Δεν μπορούμε να σας επαληθεύσουμε επειδή κάτι πήγε στραβά", "app.modules.id_gent_rrn.submit": "Υποβολή", "app.modules.id_gent_rrn.takenFormError": "Ο αριθμός κοινωνικής ασφάλισής σας έχει ήδη χρησιμοποιηθεί για την επαλήθευση άλλου λογαριασμού", "app.modules.id_gent_rrn.verifyGentRrn": "Επαλήθευση χρησιμοποιώντας το GentRrn", "app.modules.id_id_card_lookup.cancel": "Ακύρωση", "app.modules.id_id_card_lookup.emptyFieldError": "Αυτό το πεδίο δεν μπορεί να είναι κενό.", "app.modules.id_id_card_lookup.helpAltText": "Επεξήγηση δελτίου ταυτότητας", "app.modules.id_id_card_lookup.invalidCardIdError": "Αυτό το αναγνωριστικό δεν είναι έγκυρο.", "app.modules.id_id_card_lookup.noMatchFormError": "Δεν βρέθηκε καμία αντιστοιχία.", "app.modules.id_id_card_lookup.showHelp": "Πού μπορώ να βρω τον σειριακ<PERSON> αριθμό της ταυτότητάς μου;", "app.modules.id_id_card_lookup.somethingWentWrongError": "Δεν μπορούμε να σας επαληθεύσουμε επειδή κάτι πήγε στραβά", "app.modules.id_id_card_lookup.submit": "Υποβολή", "app.modules.id_id_card_lookup.takenFormError": "Ήδη κατειλημμένο.", "app.modules.id_oostende_rrn.cancel": "Ακύρωση", "app.modules.id_oostende_rrn.emptyFieldError": "Αυτό το πεδίο δεν μπορεί να είναι κενό.", "app.modules.id_oostende_rrn.invalidRrnError": "Μη έγκυρος αριθμός κοινωνικής ασφάλισης", "app.modules.id_oostende_rrn.noMatchFormError": "Δεν μπορέσαμε να βρούμε πίσω πληροφορίες για τον αριθμό κοινωνικής ασφάλισής σας", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Δεν μπορούμε να σας επαληθεύσουμε επειδή ζείτε εκτός του Oostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Δεν μπορούμε να σας επαληθεύσουμε επειδή είστε μικρότεροι από 14 ετών", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Ο αριθμός κοινωνικής ασφάλισής σας αναγράφεται στο πίσω μέρος του ψηφιακού δελτίου ταυτότητάς σας", "app.modules.id_oostende_rrn.rrnLabel": "Αριθμ<PERSON>ς μητρώου κοινωνικής ασφάλισης", "app.modules.id_oostende_rrn.rrnTooltip": "Ζητάμε τον αριθμό κοινωνικής ασφάλισής σας για να επαληθεύσουμε αν είστε πολίτης του <PERSON>, άνω των 14 ετών.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Πού μπορώ να βρω τον αριθμό κοινωνικής ασφάλισής μου;", "app.modules.id_oostende_rrn.somethingWentWrongError": "Δεν μπορούμε να σας επαληθεύσουμε επειδή κάτι πήγε στραβά", "app.modules.id_oostende_rrn.submit": "Υποβολή", "app.modules.id_oostende_rrn.takenFormError": "Ο αριθμός κοινωνικής ασφάλισής σας έχει ήδη χρησιμοποιηθεί για την επαλήθευση άλλου λογαριασμού", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Επαλήθευση με τον αριθμό κοινωνικής ασφάλισης", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Λάβατε δικαιώματα διαχειριστή του φακέλου \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Κοινοποίηση", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Δείτε τα έργα στη διεύθυνση {folderUrl} για να κάνετε τη φωνή σας να ακουστεί!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | από την πλατφόρμα συμμετοχής του {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | από την πλατφόρμα συμμετοχής του {orgName}", "app.sessionRecording.accept": "Ναι, δέχομαι", "app.sessionRecording.modalDescription1": "Προκειμένου να κατανοήσουμε καλύτερα τους χρήστες μας, ζητάμε τυχαία από ένα μικρό ποσοστό επισκεπτών να παρακολουθήσουμε λεπτομερώς τη συνεδρία περιήγησής τους.", "app.sessionRecording.modalDescription2": "Ο μοναδικ<PERSON>ς σκοπός των καταγεγραμμένων δεδομένων είναι η βελτίωση του ιστότοπου. Κανένα από τα δεδομένα σας δεν θα κοινοποιηθεί σε 3ο μέρος. Οποιαδήποτε ευαίσθητη πληροφορία εισάγετε θα φιλτράρεται.", "app.sessionRecording.modalDescription3": "Δέχεστε;", "app.sessionRecording.modalDescriptionFaq": "Συχνές ερωτήσεις εδώ.", "app.sessionRecording.modalTitle": "Βοηθήστε μας να βελτιώσουμε αυτή την ιστοσελίδα", "app.sessionRecording.reject": "Όχι, απορρίπτω", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Διεξαγω<PERSON><PERSON> άσκησης κατανομής του προϋπολογισμού", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Συλλογή ανατροφοδότησης για ένα έγγραφο", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Δημιουργ<PERSON>α έρευνας εντός της πλατφόρμας", "app.utils.AdminPage.ProjectEdit.createPoll": "Δημιουργ<PERSON>α δημοσκόπησης", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Ενσωμάτωση εξωτερικής έρευνας", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Εύρεση εθελοντών", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Συλλογή εισηγήσεων και ανατροφοδότησης", "app.utils.AdminPage.ProjectEdit.shareInformation": "Κοινοποιείστε πληροφορίες", "app.utils.FormattedCurrency.credits": "μονάδες", "app.utils.FormattedCurrency.tokens": "διακριτικά", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Τα πιο συζητημένα", "app.utils.IdeaCards.mostReacted": "Οι περισσότερες αντιδράσεις", "app.utils.IdeaCards.newest": "Νεότερο", "app.utils.IdeaCards.oldest": "Παλαιότερο", "app.utils.IdeaCards.random": "Τυχαία", "app.utils.IdeaCards.trending": "Trending", "app.utils.IdeasNewPage.contributionFormTitle": "Προσθήκη νέας συνεισφοράς", "app.utils.IdeasNewPage.ideaFormTitle": "Προσθήκη νέας ιδέας", "app.utils.IdeasNewPage.initiativeFormTitle": "Προσθήκη νέας πρωτοβουλίας", "app.utils.IdeasNewPage.issueFormTitle1": "Προσθέστε νέο σχόλιο", "app.utils.IdeasNewPage.optionFormTitle": "Προσθήκη νέας επιλογής", "app.utils.IdeasNewPage.petitionFormTitle": "Προσθήκη νέας αναφοράς", "app.utils.IdeasNewPage.projectFormTitle": "Προσθήκη νέου έργου", "app.utils.IdeasNewPage.proposalFormTitle": "Προσθήκη νέας πρότασης", "app.utils.IdeasNewPage.questionFormTitle": "Προσθήκη νέας ερώτησης", "app.utils.IdeasNewPage.surveyTitle": "Έρευνα", "app.utils.IdeasNewPage.viewYourComment": "Δείτε το σχόλιό σας", "app.utils.IdeasNewPage.viewYourContribution": "Δείτε τη συνεισφορά σας", "app.utils.IdeasNewPage.viewYourIdea": "Δείτε την ιδέα σας", "app.utils.IdeasNewPage.viewYourInitiative": "Δείτε την πρωτοβουλία σας", "app.utils.IdeasNewPage.viewYourInput": "Δείτε την εισήγησή σας", "app.utils.IdeasNewPage.viewYourIssue": "Δείτε το θέμα σας", "app.utils.IdeasNewPage.viewYourOption": "Δείτε την επιλογή σας", "app.utils.IdeasNewPage.viewYourPetition": "Δείτε την αναφορά σας", "app.utils.IdeasNewPage.viewYourProject": "Δείτε το έργο σας", "app.utils.IdeasNewPage.viewYourProposal": "Δείτε την πρότασή σας", "app.utils.IdeasNewPage.viewYourQuestion": "Δείτε την ερώτησή σας", "app.utils.Projects.sendSubmission": "Στείλτε το αναγνωριστικό υποβολής στο email μου", "app.utils.Projects.sendSurveySubmission": "Αποστολή αναγνωριστικού υποβολής έρευνας στο email μου", "app.utils.Projects.surveySubmission": "Υποβολή έρευνας", "app.utils.Projects.yourResponseHasTheFollowingId": "Η απάντησή σας έχει το ακόλουθο αναγνωριστικό: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Εάν αργότερα αποφασίσετε ότι θέλετε να αφαιρεθεί η απάντησή σας, παρακαλούμε επικοινωνήστε μαζί μας με το ακόλουθο μοναδικό αναγνωριστικό:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Πρέπει να συμπληρώσετε το προφίλ σας για να παρακολουθήσετε αυτή την εκδήλωση.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Δεν πληροίτε τις προϋποθέσεις για να παρακολουθήσετε αυτή την εκδήλωση.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Δεν επιτρέπεται να παρευρεθείτε σε αυτή την εκδήλωση.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Πρέπει να συνδεθείτε ή να εγγραφείτε για να παρακολουθήσετε αυτή την εκδήλωση.", "app.utils.actionDescriptors.attendingEventNotVerified": "Πρέπει να επαληθεύσετε το λογαριασμό σας πριν μπορέσετε να παρακολουθήσετε αυτή την εκδήλωση.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Πρέπει να συμπληρώσετε το προφίλ σας για να γίνετε εθελοντής.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Δεν πληροίτε τις προϋποθέσεις για να γίνετε εθελοντής.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Δεν επιτρέπεται η εθελοντική εργασία.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Πρέπει να συνδεθείτε ή να εγγραφείτε για να γίνετε εθελοντές.", "app.utils.actionDescriptors.volunteeringNotVerified": "Πρέπει να επαληθεύσετε το λογαριασμό σας πριν μπορέσετε να προσφέρετε εθελοντική εργασία.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Παρακαλούμε {completeRegistrationLink} για εθελοντισμό.", "app.utils.errors.api_error_default.in": "Δεν είναι σωστό", "app.utils.errors.default.ajv_error_birthyear_required": "Συμπληρώστε το έτος γέννησής σας", "app.utils.errors.default.ajv_error_date_any": "Συμπληρώστε μια έγκυρη ημερομηνία", "app.utils.errors.default.ajv_error_domicile_required": "Συμπληρώστε τον τόπο κατοικίας σας", "app.utils.errors.default.ajv_error_gender_required": "Συμπληρώστε το φύλο σας", "app.utils.errors.default.ajv_error_invalid": "δεν είναι έγκυρο", "app.utils.errors.default.ajv_error_maxItems": "Δεν μπορεί να περιλαμβάνει περισσότερα από {limit, plural, one {# στοιχείο} other {# στοιχεία}}", "app.utils.errors.default.ajv_error_minItems": "Πρέπει να περιλαμβάνει τουλάχιστον {limit, plural, one {# στοιχείο} other {# στοιχεία}}", "app.utils.errors.default.ajv_error_number_any": "Παρακαλούμε συμπληρώστε έναν έγκυρο αριθμό", "app.utils.errors.default.ajv_error_politician_required": "Παρακαλούμε συμπληρώστε αν είστε πολιτικός", "app.utils.errors.default.ajv_error_required3": "Το πεδίο είναι υποχρεωτικό: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Δεν μπορεί να είναι κενό", "app.utils.errors.default.api_error_accepted": "Πρέπει να γίνει αποδεκτό", "app.utils.errors.default.api_error_blank": "Δεν μπορεί να είναι κενό", "app.utils.errors.default.api_error_confirmation": "Δεν ταιριάζει", "app.utils.errors.default.api_error_empty": "Δεν μπορεί να είναι κενό", "app.utils.errors.default.api_error_equal_to": "Δεν είναι σωστό", "app.utils.errors.default.api_error_even": "Πρέπει να είναι ίσο", "app.utils.errors.default.api_error_exclusion": "Είναι δεσμευμένο", "app.utils.errors.default.api_error_greater_than": "Είναι πολύ μικρό", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Είναι πολύ μικρό", "app.utils.errors.default.api_error_inclusion": "Δεν περιλαμβάνεται στην λίστα", "app.utils.errors.default.api_error_invalid": "δεν είναι έγκυρο", "app.utils.errors.default.api_error_less_than": "Είναι πολύ μεγάλο", "app.utils.errors.default.api_error_less_than_or_equal_to": "Είναι πολύ μεγάλο", "app.utils.errors.default.api_error_not_a_number": "Δεν είναι αριθμός", "app.utils.errors.default.api_error_not_an_integer": "Πρέπει να είναι ακέραιος", "app.utils.errors.default.api_error_other_than": "Δεν είναι σωστό", "app.utils.errors.default.api_error_present": "Πρέπει να είναι κενό", "app.utils.errors.default.api_error_too_long": "Είναι πολύ μεγάλο", "app.utils.errors.default.api_error_too_short": "Είναι πολύ μικρός", "app.utils.errors.default.api_error_wrong_length": "Έχει λάθος μήκος", "app.utils.errors.defaultapi_error_.odd": "Πρέπει να είναι περιττό", "app.utils.notInGroup": "Δεν πληροίτε τις προϋποθέσεις συμμετοχής.", "app.utils.participationMethod.onSurveySubmission": "Σας ευχαριστούμε. Η απάντησή σας λήφθηκε.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Η ψηφοφορία δεν είναι πλέον διαθέσιμη, καθώς η φάση αυτή δεν είναι πλέον ενεργή.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Δεν πληροίτε τις προϋποθέσεις για να ψηφίσετε.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Δεν επιτρέπεται να ψηφίσετε.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Πρέπει να συνδεθείτε ή να εγγραφείτε για να ψηφίσετε.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Πρέπει να επαληθεύσετε το λογαριασμό σας πριν μπορέσετε να ψηφίσετε.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Η υποβολή προϋπολογισμών έκλεισε στη διεύθυνση {endDate}.</b> Οι συμμετέχοντες είχαν στη διάθεσή τους συνολικά <b>{maxBudget} για να τα κατανείμουν μεταξύ {optionCount} επιλογών.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Υποβληθείς προϋπολογισμός", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Υποβληθείς προϋπολογισμός 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Δεν πληροίτε τις προϋποθέσεις για την εκχώρηση προϋπολογισμών.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Δεν επιτρέπεται να εκχωρείτε προϋπολογισμούς.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Πρέπει να συνδεθείτε ή να εγγραφείτε για να εκχωρήσετε προϋπολογισμούς.", "app.utils.votingMethodUtils.budgetingNotVerified": "Πρέπει να επαληθεύσετε το λογαριασμό σας προτού μπορέσετε να εκχωρήσετε προϋπολογισμούς.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Ο προϋπολογισμός σας δεν θα υπολογιστεί</b> μέχρι να κάνετε κλικ στο κουμπί \"Υποβολή\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Ο ελάχιστος απαιτούμενος προϋπολογισμός είναι {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Μόλις τελειώσετε, κάντε κλικ στο κουμπί \"Υποβολή\" για να υποβάλετε τον προϋπολογισμό σας.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Επιλέξτε τις προτιμώμενες επιλογές σας πατώντας το κουμπί \"Προσθήκη\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Έχετε συνολικά <b>{maxBudget} για να τα κατανείμετε μεταξύ των επιλογών {optionCount} </b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>Συγχ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ο προϋπολογισμός σας υποβλήθηκε!</b> Μπορείτε να ελέγξετε τις επιλογές σας παρακάτω ανά πάσα στιγμή ή να τις τροποποιήσετε πριν από τη διεύθυνση <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON>υ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ο προϋπολογισμός σας υποβλήθηκε!</b> Μπορείτε να ελέγξετε τις επιλογές σας παρακάτω ανά πάσα στιγμή.", "app.utils.votingMethodUtils.castYourVote": "Ψηφίστε", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Μπορείτε να προσθέσετε το πολύ {maxVotes, plural, one {# πιστώσεις} other {# πιστώσεις}} ανά επιλογή.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Μπορείτε να προσθέσετε το πολύ {maxVotes, plural, one {# σημείο} other {# σημεία}} ανά επιλογή.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Μπορείτε να προσθέσετε το πολύ {maxVotes, plural, one {# token} other {# tokens}} ανά επιλογή.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Μπορείτε να προσθέσετε το πολύ {maxVotes, plural, one {# ψηφοφορία} other {# ψήφους}} ανά επιλογή.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Μόλις τελειώσετε, κάντε κλικ στο κουμπί \"Υποβολή\" για να δώσετε την ψήφο σας.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Επιλέξτε τις προτιμώμενες επιλογές σας πατώντας το κουμπί \"Select\" (Επιλογή).", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Τελικά αποτελέσματα", "app.utils.votingMethodUtils.finalTally": "Τελικ<PERSON>ς απολογισμός", "app.utils.votingMethodUtils.howToParticipate": "Πώς να συμμετάσχετε", "app.utils.votingMethodUtils.howToVote": "Πώς να ψηφίσετε", "app.utils.votingMethodUtils.multipleVotingEnded1": "Η ψηφοφορία έκλεισε στο <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 μονάδες} one {1 μονάδα} other {# μονάδες}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 βαθμοί} one {1 βαθμός} other {# βαθμοί}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 ψήφοι} one {1 ψήφος} other {# ψήφοι}}", "app.utils.votingMethodUtils.results": "Αποτελέσματα", "app.utils.votingMethodUtils.singleVotingEnded": "Η ψηφοφορία έκλεισε στο <b>{endDate}.</b> Οι συμμετέχοντες μπορούσαν <b>να ψηφίσουν για τις επιλογές {maxVotes} .</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Επιλέξτε τις προτιμώμενες επιλογές σας πατώντας \"Ψηφίστε\".", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Έχετε <b>{totalVotes} ψήφους</b> που μπορείτε να αντιστοιχίσετε στις επιλογές.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Μόλις τελειώσετε, κάντε κλικ στο κουμπί \"Υποβολή\" για να δώσετε την ψήφο σας.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Η ψηφοφορία έκλεισε στο <b>{endDate}.</b> Οι συμμετέχοντες μπορούσαν να ψηφίσουν <b>για 1 επιλογή.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Επιλέξτε την επιλογή που προτιμάτε πατώντας \"Ψηφίστε\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Έχετε στη διάθεσή σας <b>1 ψήφο</b> την οποία μπορείτε να αντιστοιχίσετε σε μία από τις επιλογές.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Η ψηφοφορία έκλεισε στο <b>{endDate}.</b> Οι συμμετέχοντες μπορούσαν να ψηφίσουν <b>για όσες επιλογές επιθυμούσαν.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Μπορείτε να ψηφίσετε όσες επιλογές θέλετε.", "app.utils.votingMethodUtils.submitYourBudget": "Υποβάλετε τον προϋπολογισμό σας", "app.utils.votingMethodUtils.submittedBudgetCountText2": "το άτομο υπέβαλε τον προϋπολογισμό του ηλεκτρονικά", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "οι πολίτες υπέβαλαν τους προϋπολογισμούς τους ηλεκτρονικά", "app.utils.votingMethodUtils.submittedVoteCountText2": "άτομο υπέβαλε την ψήφο του ηλεκτρονικά", "app.utils.votingMethodUtils.submittedVotesCountText2": "οι πολίτες υπέβαλαν τις ψήφους τους ηλεκτρονικά", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Υποβληθείσα ψήφος 🎉", "app.utils.votingMethodUtils.votesCast": "Ψήφοι", "app.utils.votingMethodUtils.votingClosed": "Η ψηφοφορία έκλεισε", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Η ψήφος σας δεν θα μετρήσει</b> μέχρι να κάνετε κλικ στο κουμπί \"Υποβολή\".", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>Συ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, η ψήφος σας υποβλήθηκε!</b> Μπορείτε να ελέγξετε ή να τροποποιήσετε την υποβολή σας πριν <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, η ψήφος σας υποβλήθηκε!</b> Μπορείτε να ελέγξετε ή να τροποποιήσετε την υποβολή σας παρακάτω ανά πάσα στιγμή.", "components.UI.IdeaSelect.noIdeaAvailable": "Δεν υπάρχουν διαθέσιμες ιδέες.", "components.UI.IdeaSelect.selectIdea": "Επιλέξτε ιδέα", "containers.SiteMap.allProjects": "Όλα τα έργα", "containers.SiteMap.customPageSection": "Προσαρμοσμένες σελίδες", "containers.SiteMap.folderInfo": "Περισσότερες πληροφορίες", "containers.SiteMap.headSiteMapTitle": "Χάρτης ιστότοπου | {orgName}", "containers.SiteMap.homeSection": "Γενικά", "containers.SiteMap.pageContents": "Περιεχόμενο σελίδας", "containers.SiteMap.profilePage": "Η σελίδα του προφίλ σας", "containers.SiteMap.profileSettings": "Οι ρυθμίσεις σας", "containers.SiteMap.projectEvents": "Εκδηλώσεις", "containers.SiteMap.projectIdeas": "Ιδέες", "containers.SiteMap.projectInfo": "Πληροφορίες", "containers.SiteMap.projectPoll": "Δημοσκόπηση", "containers.SiteMap.projectSurvey": "Έρευνα", "containers.SiteMap.projectsArchived": "Αρχειοθετημένα έργα", "containers.SiteMap.projectsCurrent": "Τρέχοντα έργα", "containers.SiteMap.projectsDraft": "Προσχέδια έργων", "containers.SiteMap.projectsSection": "Έργα του {orgName}", "containers.SiteMap.signInPage": "Είσοδος", "containers.SiteMap.signUpPage": "Εγγραφείτε", "containers.SiteMap.siteMapDescription": "Από αυτή τη σελίδα, μπορείτε να πλοηγηθείτε σε οποιοδήποτε περιεχόμενο της πλατφόρμας.", "containers.SiteMap.siteMapTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ιστότοπου της πλατφόρμας συμμετοχής του {orgName}", "containers.SiteMap.successStories": "Ιστορίες επιτυχίας", "containers.SiteMap.timeline": "Φάσ<PERSON><PERSON>ς έργων", "containers.SiteMap.userSpaceSection": "Ο λογαριασμός σας", "containers.SubscriptionEndedPage.accessDenied": "Δεν έχετε πλέον πρόσβαση", "containers.SubscriptionEndedPage.subscriptionEnded": "Αυτή η σελίδα είναι προσβάσιμη μόνο για πλατφόρμες με ενεργή συνδρομή."}