{"UI.FormComponents.required": "obligatoriu", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.info": "This image is always cropped to a certain ratio to make sure all crucial aspects are on display at all times. The {link} for this image type is {aspect}.", "app.components.Admin.ImageCropper.infoLinkText": "recommended ratio", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Header text for registered users", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Atenție: culoarea selectată nu are un efect contrastant suficient de mare. <PERSON><PERSON> lucru face ca textul să fie greu de citit. Alegeți o culoare mai închisă pentru a crește lizibilitatea textului.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Add Events to navigation bar", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "When enabled, a link to all project events will be added to the navigation bar.", "app.components.AdminPage.SettingsPage.eventsSection": "Events", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Homepage customizable section", "app.components.ProjectTemplatePreview.close": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.createProject": "Create project", "app.components.ProjectTemplatePreview.goBack": "Întoarce-te", "app.components.ProjectTemplatePreview.goBackTo": "Întoarce-te la {goBackLink}.", "app.components.ProjectTemplatePreview.infoboxLine1": "Doriți să utilizați acest șablon pentru proiectul dumneavoastră de bugetare participativă?", "app.components.ProjectTemplatePreview.infoboxLine2": "Contactează persoana responsabilă din cadrul primăriei sau accesează {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Project folder", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Data selectată nu este validă. Vă rugăm să furnizați o dată în următorul format: AAAA-LL-ZZ", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Vă rugăm să selectați o dată de început pentru proiect", "app.components.ProjectTemplatePreview.projectStartDate": "Data de începere a proiectului dvs", "app.components.ProjectTemplatePreview.projectTitle": "Titlul proiectului tău", "app.components.ProjectTemplatePreview.projectTitleError": "Introduceți un titlu de proiect", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "<PERSON><PERSON> rugăm să tastați un titlu de proiect pentru toate limbile", "app.components.ProjectTemplatePreview.projectsOverviewPage": "pagina de prezentare a proiectelor", "app.components.ProjectTemplatePreview.responseError": "Oops, something went wrong.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Vedeți mai multe șabloane", "app.components.ProjectTemplatePreview.successMessage": "Proiectul a fost creat cu succes!", "app.components.ProjectTemplatePreview.typeProjectName": "Tastați numele proiectului", "app.components.ProjectTemplatePreview.useTemplate": "Utilizați acest șablon", "app.components.UserSearch.addModerators": "Adaugă", "app.components.UserSearch.searchUsers": "Căutați utilizatori", "app.components.admin.Graphs": "No data available with the current filters.", "app.components.admin.Graphs.noDataShort": "Nu există date disponibile.", "app.components.admin.InputManager.onePost": "1 input", "app.components.admin.PostManager.PostPreview.assignee": "Responsabil", "app.components.admin.PostManager.PostPreview.cancelEdit": "Anulați editarea", "app.components.admin.PostManager.PostPreview.currentStatus": "Status-ul curent", "app.components.admin.PostManager.PostPreview.delete": "Șterge", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Sunteți sigur că doriți să ștergeți această postare? Această acțiune nu va mai putea fi anulată.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Sunteți sigur că doriți să ștergeți această postare? Postarea va fi ștearsă din toate fazele proiectului și nu va mai putea fi recuperată.", "app.components.admin.PostManager.PostPreview.edit": "Edita<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "Neatribuit", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.PostPreview.picks": "<PERSON><PERSON><PERSON>: {picksN<PERSON>ber}", "app.components.admin.PostManager.PostPreview.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "Eroare", "app.components.admin.PostManager.allPhases": "Toate fazele", "app.components.admin.PostManager.allProjects": "Toate proiectele", "app.components.admin.PostManager.allStatuses": "Toate statusurile", "app.components.admin.PostManager.allTopics": "Toate subiectele", "app.components.admin.PostManager.anyAssignment": "<PERSON><PERSON> sarc<PERSON>", "app.components.admin.PostManager.assignedTo": "Assigned to {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Assigned to me", "app.components.admin.PostManager.assignee": "Responsabil", "app.components.admin.PostManager.comments": "Comenta<PERSON><PERSON>", "app.components.admin.PostManager.currentLat": "Latitudine actuală", "app.components.admin.PostManager.currentLng": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.currentZoomLevel": "Nivelul actual de zoom", "app.components.admin.PostManager.delete": "Șterge", "app.components.admin.PostManager.deleteAllSelectedInputs": "Delete {count} posts", "app.components.admin.PostManager.deleteConfirmation": "Sunteți sigur că doriți să ștergeți acest strat?", "app.components.admin.PostManager.edit": "Edita<PERSON><PERSON>", "app.components.admin.PostManager.exportAllInputs": "Export all posts (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Export all comments (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Export comments for this project (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Export posts in this project (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Export selected posts (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Export comments for selected posts (.xslx)", "app.components.admin.PostManager.exports": "Exports", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Choose how people will see your name", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Explain this status change", "app.components.admin.PostManager.goToDefaultMapView": "Accesați centrul de hartă și măriți", "app.components.admin.PostManager.hiddenFieldsLink": "câmpuri as<PERSON>nse", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/en/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Sfat: <PERSON><PERSON><PERSON><PERSON><PERSON> {hiddenFieldsLink} atunci când vă configurați sondajul pentru a urmări cine a răspuns la acesta.", "app.components.admin.PostManager.importError": "Fișierul selectat nu a putut fi importat deoarece nu este un fișier GeoJSON valid", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputManagerHeader": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputs": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputsExportFileName": "input", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Only show posts that need feedback", "app.components.admin.PostManager.latestFeedbackMode": "Use the latest existing official update as an explanation", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Moving this input away from its current project will lose the information about its assigned phases. Do you want to proceed?", "app.components.admin.PostManager.multipleInputs": "{ideaCount} posts", "app.components.admin.PostManager.newFeedbackMode": "Write a new update to explain this change", "app.components.admin.PostManager.noFilteredResults": "The filters you selected did not return any results", "app.components.admin.PostManager.noOne": "Neatribuit", "app.components.admin.PostManager.officialUpdateAuthor": "Choose how people will see your name", "app.components.admin.PostManager.officialUpdateBody": "Explain this status change", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.projectsTab": "Proiecte", "app.components.admin.PostManager.projectsTabTooltipContent": "You can drag and drop posts to move them from one project to another. Note that for timeline projects, you will still need to add the post to a specific phase.", "app.components.admin.PostManager.publication_date": "Publicat pe", "app.components.admin.PostManager.resetFiltersButton": "Resetați filtrele", "app.components.admin.PostManager.resetInputFiltersDescription": "Reset the filters to see all input.", "app.components.admin.PostManager.saved": "Saved", "app.components.admin.PostManager.setAsDefaultMapView": "Salvați punctul central și nivelul de zoom curent ca hărți implicite", "app.components.admin.PostManager.statusChangeGenericError": "There was an error, please try again later or contact support.", "app.components.admin.PostManager.statusChangeSave": "Change status", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Change the status of a post using drag and drop. The original author and other contributors will receive a notification of the changed status.", "app.components.admin.PostManager.timelineTab": "Timeline", "app.components.admin.PostManager.timelineTabTooltipText": "Drag and drop posts to copy them to different project phases.", "app.components.admin.PostManager.title": "Titlu", "app.components.admin.PostManager.topicsTab": "Subiecte", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.votes": "Votes", "app.components.admin.ReportExportMenu.FileName.fromFilter": "from", "app.components.admin.ReportExportMenu.FileName.groupFilter": "group", "app.components.admin.ReportExportMenu.FileName.projectFilter": "proiect", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "until", "app.components.admin.ReportExportMenu.downloadPng": "Download as PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Download as SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Download Excel", "app.components.admin.SlugInput.regexError": "Slug-ul poate conține numai litere obișnuite, minuscule (a-z), numere (0-9) și liniuțe (-). Primul și ultimul caracter nu pot fi cratime. Sunt interzise cratimele consecutive (--).", "app.components.admin.TerminologyConfig.saveButton": "Save", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Contribuție", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "More information on how to embed a link for Google Forms can be found in {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "this support article", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Ideea", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "What should an input be called?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Problemă", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "A maximum budget is required", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "The minimum budget can't be larger than the maximum budget", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "A minimum budget is required", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Opțiune", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Proiect", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Campaigns.campaignFrom": "From:", "app.containers.Admin.Campaigns.campaignTo": "To:", "app.containers.Admin.Campaigns.noAccess": "We're sorry, but it seems like you don't have access to the emails section", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automated emails", "app.containers.Admin.Invitations.addToGroupLabel": "Add these people to specific manual user groups", "app.containers.Admin.Invitations.adminLabelTooltip": "When you select this option, the people you're inviting will have access to all your platform settings.", "app.containers.Admin.Invitations.configureInvitations": "3. Configure the invitations", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "There are no invites that match your search", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteTooltip": "Cancelling an invitation will allow you to resend an invitation to this person.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Download and fill out the template", "app.containers.Admin.Invitations.downloadTemplate": "Download template", "app.containers.Admin.Invitations.email": "E-mail", "app.containers.Admin.Invitations.emailListLabel": "Manually enter the email addresses of the people you want to invite. Seperate each address by a comma.", "app.containers.Admin.Invitations.exportInvites": "Export all invitations", "app.containers.Admin.Invitations.fileRequirements": "Important: In order to send the invitations correctly, no column can be removed from the import template. Leave unused columns empty.", "app.containers.Admin.Invitations.filetypeError": "Incorrect file type. Only XLSX files are supported.", "app.containers.Admin.Invitations.groupsPlaceholder": "No group selected", "app.containers.Admin.Invitations.helmetDescription": "Invite users to the platform", "app.containers.Admin.Invitations.helmetTitle": "Admin invitation dashboard", "app.containers.Admin.Invitations.importOptionsInfo": "These options will only be taken into account when they are not defined in the Excel file.\n      Please visit the {supportPageLink} for more information.", "app.containers.Admin.Invitations.importTab": "Import email addresses", "app.containers.Admin.Invitations.invitationOptions": "Invitation options", "app.containers.Admin.Invitations.invitationSubtitle": "Invite people to the platform at any point in time. They get a neutral invitation email with your logo, in which they are asked to register on the platform.", "app.containers.Admin.Invitations.invitePeople": "Invite people via email", "app.containers.Admin.Invitations.inviteStatus": "Stare", "app.containers.Admin.Invitations.inviteStatusAccepted": "Accepted", "app.containers.Admin.Invitations.inviteStatusPending": "Pending", "app.containers.Admin.Invitations.inviteTextLabel": "Optionally type a message that will be added to the invitation mail.", "app.containers.Admin.Invitations.invitedSince": "Invited", "app.containers.Admin.Invitations.invitesSupportPageURL": "http://support.govocal.com/en/articles/1771605-invite-people-to-the-platform", "app.containers.Admin.Invitations.localeLabel": "Select the language of the invitation", "app.containers.Admin.Invitations.moderatorLabel": "Give these people project management rights", "app.containers.Admin.Invitations.moderatorLabelTooltip": "When you select this option, the invitee(s) will be assigned project manager rights for the selected project(s). More information on project manager rights {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "http://support.govocal.com/articles/1948422-appoint-the-right-project-moderators", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "here", "app.containers.Admin.Invitations.name": "Nume", "app.containers.Admin.Invitations.processing": "Sending out invitations. Please wait...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "No project(s) selected", "app.containers.Admin.Invitations.save": "Send out invitations", "app.containers.Admin.Invitations.saveErrorMessage": "One or more errors occurred and the invitations were not sent out. Please correct the error(s) listed below and try again.", "app.containers.Admin.Invitations.saveSuccess": "Succes!", "app.containers.Admin.Invitations.saveSuccessMessage": "Invitation successfully sent out.", "app.containers.Admin.Invitations.supportPage": "support page", "app.containers.Admin.Invitations.supportPageLinkText": "Visit the support page", "app.containers.Admin.Invitations.tabAllInvitations": "All invitations", "app.containers.Admin.Invitations.tabInviteUsers": "Invite users", "app.containers.Admin.Invitations.textTab": "Manually enter email addresses", "app.containers.Admin.Invitations.unknownError": "A apărut o eroare. Vă rugăm să încercați din nou mai târziu.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Upload your completed template file", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} if you want more info about all supported columns in the import template.", "app.containers.Admin.Moderation.all": "Toate", "app.containers.Admin.Moderation.belongsTo": "Belongs to", "app.containers.Admin.Moderation.collapse": "<PERSON><PERSON>", "app.containers.Admin.Moderation.comment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.content": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.date": "Date", "app.containers.Admin.Moderation.goToComment": "Open this comment in a new tab", "app.containers.Admin.Moderation.goToPost": "Open this post in a new tab", "app.containers.Admin.Moderation.goToProposal": "Open this proposal in a new tab", "app.containers.Admin.Moderation.markFlagsError": "Couldn't mark item(s). Try again.", "app.containers.Admin.Moderation.markNotSeen": "Mark {selectedItemsCount, plural, one {# item} other {# items}} as not seen", "app.containers.Admin.Moderation.markSeen": "Mark {selectedItemsCount, plural, one {# item} other {# items}} as seen", "app.containers.Admin.Moderation.moderationsTooltip": "This page allows you to quickly check all new posts that have been published to your platform, including ideas and comments. You can mark posts as being 'seen' so that others know what still needs to be processed.", "app.containers.Admin.Moderation.noUnviewedItems": "There are no unseen items", "app.containers.Admin.Moderation.noViewedItems": "There are no seen items", "app.containers.Admin.Moderation.post": "Post", "app.containers.Admin.Moderation.profanityBlockerSetting": "Profanity blocker", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Block posts containing the most commonly reported offensive words.", "app.containers.Admin.Moderation.project": "Proiect", "app.containers.Admin.Moderation.read": "Seen", "app.containers.Admin.Moderation.readMore": "Citește mai mult", "app.containers.Admin.Moderation.removeFlagsError": "Couldn't remove warning(s). Try again.", "app.containers.Admin.Moderation.rowsPerPage": "Rows per page", "app.containers.Admin.Moderation.settings": "Settings", "app.containers.Admin.Moderation.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.Admin.Moderation.show": "Show", "app.containers.Admin.Moderation.status": "Stare", "app.containers.Admin.Moderation.successfulUpdateSettings": "Settings updated successfully.", "app.containers.Admin.Moderation.type": "Type", "app.containers.Admin.Moderation.unread": "Not seen", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "This page consists of the following sections. You can turn them on/off and edit them as required.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Sections", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "View page", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Nu este afișat pe pagină", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Afișat pe pagină", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Attachments", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Add files (max. 50 MB) that will be available to download from the page.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Bottom info section", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Add your own content to the customizable section at the bottom of the page.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Edit", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Events list", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Show events related to the projects.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero banner", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Customise the page banner image and text.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Lista de proiecte", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Afișați proiectele în funcție de setările paginii dvs. Puteți previzualiza proiectele care vor fi afișate.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Top info section", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Add your own content to the customizable section at the top of the page.", "app.containers.Admin.PagesAndMenu.addButton": "Adăugați la bara de navigare", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Nume în bara de navigare", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Sunteți sigur că doriți să ștergeți această pagină? Acest lucru nu poate fi anulat.", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Alte pagini disponibile", "app.containers.Admin.PagesAndMenu.components.savePage": "Save page", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Attachments (max 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Couldn't save attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the bottom of this page", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Attachments saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Attachments | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Salvați și activați atașamentele", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Save attachments", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Furniza<PERSON>i conținut pentru toate limbile", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Couldn't save bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Bottom info section saved", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Bottom info section", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Salvați și activați secțiunea de informații de jos", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Save bottom info section", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Please select at least one tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "By area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "By tag(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Edit custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Linked projects", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Select which projects and related events can be displayed on the page.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "<PERSON> successfully created", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Custom page saved", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Titlu în bara de navigare", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Create custom page | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Create custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "None", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Page settings", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Save custom page", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Please select an area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Selected area", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Selected tags", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "The slug can only contain regular, lowercase letters (a-z), numbers (0-9) and hyphens (-). The first and last characters cannot be hyphens. Consecutive hyphens (--) are forbidden.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "You must enter a slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Enter a title in every language", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Enter a title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Vizualizați pagina personalizată", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Edit custom page | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Page content", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Edit", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "No available projects based on your {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "This project has no tag or area filter, so no projects will be displayed.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Projects list | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "page settings", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projects list", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "The following projects will be shown on this page based on your {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "IMPLICIT", "app.containers.Admin.PagesAndMenu.deleteButton": "Delete", "app.containers.Admin.PagesAndMenu.editButton": "Edit", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.heroBannerError": "Couldn't save hero banner", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Hero banner saved", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Save hero banner", "app.containers.Admin.PagesAndMenu.homeTitle": "Home", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Provide content for at least one language", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Pages & menu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Eliminați din bara de navigare", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Salvați și activați bannerul eroului", "app.containers.Admin.PagesAndMenu.title": "Pages & menu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.topInfoError": "Couldn't save top info section", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Top info section saved", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Top info section | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Top info section", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Salvați și activați secțiunea de informații de sus", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Save top info section", "app.containers.Admin.PagesAndMenu.viewButton": "Vizualizați", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Delete Group", "app.containers.Admin.Users.GroupsHeader.editGroup": "Edit Group", "app.containers.Admin.Users.GroupsPanel.allUsers": "Registered users", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Groups", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Get an overview of all the people and organisations that registered on the platform. Add a selection of users to Manual groups or Smart groups.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Invitation pending", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.deleteUser": "Delete user", "app.containers.Admin.Users.email": "E-mail", "app.containers.Admin.Users.helmetDescription": "User list in admin", "app.containers.Admin.Users.helmetTitle": "Admin - users dashboard", "app.containers.Admin.Users.name": "Nume", "app.containers.Admin.Users.options": "Opțiuni", "app.containers.Admin.Users.seeProfile": "See profile", "app.containers.Admin.Users.userDeletionConfirmation": "Permanently remove this user?", "app.containers.Admin.Users.userDeletionFailed": "An error occurred while deleting this user, please try again.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.youCantDeleteYourself": "You cannot delete your own account via the user admin page", "app.containers.Admin.Users.youCantUnadminYourself": "You cannot give up your role as an admin now", "app.containers.Admin.emails.addCampaignTitle": "Create a new email", "app.containers.Admin.emails.allUsers": "Registered users", "app.containers.Admin.emails.changeRecipientsButton": "Change recipients", "app.containers.Admin.emails.confirmSendHeader": "Email to all users?", "app.containers.Admin.emails.deleteButtonLabel": "Șterge", "app.containers.Admin.emails.draft": "Draft", "app.containers.Admin.emails.editButtonLabel": "Edita<PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "Edit campaign", "app.containers.Admin.emails.failed": "Failed", "app.containers.Admin.emails.fieldBody": "Message", "app.containers.Admin.emails.fieldBodyError": "Furnizați un mesaj de e-mail pentru toate limbile", "app.containers.Admin.emails.fieldReplyTo": "Replies should go to", "app.containers.Admin.emails.fieldReplyToEmailError": "Furnizați o adresă de e-mail în formatul corect, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Furnizați o adresă de e-mail", "app.containers.Admin.emails.fieldReplyToTooltip": "You can choose where to send replies to your email.", "app.containers.Admin.emails.fieldSender": "From", "app.containers.Admin.emails.fieldSenderError": "Furnizați un expeditor al e-mailului", "app.containers.Admin.emails.fieldSenderTooltip": "You can decide who the recipients will see as the sender of the email.", "app.containers.Admin.emails.fieldSubject": "Email Subject", "app.containers.Admin.emails.fieldSubjectError": "Furnizați un subiect de e-mail pentru toate limbile", "app.containers.Admin.emails.fieldSubjectTooltip": "This will be shown in the subject line of the email and in the user’s inbox overview. Make it clear and engaging.", "app.containers.Admin.emails.fieldTo": "To", "app.containers.Admin.emails.fieldToTooltip": "You can select the user groups that will receive your email", "app.containers.Admin.emails.formSave": "Salvați ca proiect", "app.containers.Admin.emails.groups": "Groups", "app.containers.Admin.emails.helmetDescription": "Send out manual emails to user groups and activate automated campaigns", "app.containers.Admin.emails.previewSentConfirmation": "A preview email has been sent to your email address", "app.containers.Admin.emails.previewTitle": "Preview", "app.containers.Admin.emails.send": "Trimite", "app.containers.Admin.emails.sendNowButton": "Send now", "app.containers.Admin.emails.sendTestEmailButton": "Send me a test email", "app.containers.Admin.emails.sendTestEmailTooltip": "When you click this link, a test email will be sent to your email address only. This allows you to check how the email looks like in real life.", "app.containers.Admin.emails.senderRecipients": "Sender and recipients", "app.containers.Admin.emails.sending": "Sending", "app.containers.Admin.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.emails.toAllUsers": "Do you want to send this email to all registered users?", "app.containers.Admin.ideas.import": "Import", "app.containers.Admin.messaging.helmetTitle": "Messaging", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "This image is part of the folder card; the card that summarizes the folder and is shown on the homepage for example. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "This image is shown at the top of the folder page. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visit our support center", "app.containers.Admin.projects.all.components.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.draft": "Draft", "app.containers.Admin.projects.all.components.manageButtonLabel": "Administrează", "app.containers.Admin.projects.all.deleteFolderConfirm": "Are you sure you want to delete this folder? All of the projects within the folder will also be deleted. This action cannot be undone.", "app.containers.Admin.projects.all.deleteFolderError": "There was an issue removing this folder. Please try again.", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Are you sure you want to delete this project? This cannot be undone.", "app.containers.Admin.projects.all.deleteProjectError": "There was an error deleting this project, please try again later.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "This is some text. You can edit and format it by using the editor in the panel on the right.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participants", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Project results", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Report summary", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Add the goal of the project, participation methods used, and the outcome", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visitors", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "No project", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Delete", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Edit", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "An error occurred when trying to create this report. Please try again later.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Start with a blank page", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Titlul raportului", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Creați un raport", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Print to PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Start with a project template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Report template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "A report with this title already exists. Please pick a different title.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Share as PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "To share with everyone, print the report as a PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Share as web link", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "This web link is only accessible to admin users.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Share", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Create a report", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Customise your report and share it with internal stakeholders or community with a web link.", "app.containers.Admin.reporting.helmetDescription": "Pagina de raportare a administratorului", "app.containers.Admin.reporting.helmetTitle": "Reporting", "app.containers.Admin.reporting.printPrepare": "Preparing to print...", "app.containers.Admin.reporting.reportBuilder": "<PERSON><PERSON><PERSON><PERSON> <PERSON> rap<PERSON>", "app.containers.Admin.reporting.reportHeader": "Antetul raportului", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "total users on the platform", "app.containers.AdminPage.DashboardPage._blank": "unknown", "app.containers.AdminPage.DashboardPage.allGroups": "All Groups", "app.containers.AdminPage.DashboardPage.allProjects": "Toate proiectele", "app.containers.AdminPage.DashboardPage.allTime": "All Time", "app.containers.AdminPage.DashboardPage.comments": "Comenta<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Comenta<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "A base dataset is required to measure the representativeness of platform users.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "We're currently working on the {fieldName} dashboard, it will be available soon", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# item is} other {# items are}} hidden in this graph. Change to {tableViewLink} to view all data.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} for user registration", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} out of {total} users included ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Show {numberOfHiddenItems} more", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Optional", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Representativeness score:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "This score reflects how accurately platform user data reflects the total population. Learn more about {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Required", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "table view", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Total population", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Users", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Adăugați un grup de vârstă", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} și mai mult", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Grup de vârstă", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Grupul (grupurile) de vârstă {upperBound} și mai mult nu este (sunt) inclusă(e).", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Age group {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Grup<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "și mai mult", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Aplicați un exemplu de grupare", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Ștergeți tot", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "From", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Setați grupurile de vârstă pentru a le alinia cu setul de date de bază.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Gama", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "To", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Editați grupele de vârstă", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "This item will not be calculated.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "See less", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "See {numberOfHiddenItems} more...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Base month (optional)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Grupuri de vârstă (Anul nașterii)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Complete", "app.containers.AdminPage.DashboardPage.components.Field.default": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Please fill out all enabled options, or disable the options you want to omit from the graph. At least one option must be filled out.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Incomplete", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Number of total residents", "app.containers.AdminPage.DashboardPage.components.Field.options": "Options", "app.containers.AdminPage.DashboardPage.components.Field.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Saved", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Vă rugăm să setați mai întâi {setAgeGroupsLink} pentru a începe să introduceți datele de bază.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "setați grupurile de vârstă", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "<PERSON><PERSON> mediu de r<PERSON><PERSON>: {days} zile", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "<PERSON>um<PERSON><PERSON><PERSON> mediu de zile pentru a răspunde", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Feedback of<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Starea intrării", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Intr<PERSON>ri în funcție de stare", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Actualizare oficială", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Procentul de intrări", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Stare schimbată", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Edit base data", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "how we calculate representativeness scores", "app.containers.AdminPage.DashboardPage.continuousType": "Without a timeline", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Cumulative total", "app.containers.AdminPage.DashboardPage.customDateRange": "Custom", "app.containers.AdminPage.DashboardPage.day": "zi", "app.containers.AdminPage.DashboardPage.false": "false", "app.containers.AdminPage.DashboardPage.female": "femeie", "app.containers.AdminPage.DashboardPage.fromTo": "from {from} to {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard for activities on the platform", "app.containers.AdminPage.DashboardPage.helmetTitle": "Admin dashboard page", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Alegeți resursele pentru a le arăta în funcție de proiect", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Alegeți resursele pentru a le arăta în funcție de idee", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Select user group", "app.containers.AdminPage.DashboardPage.male": "masculin", "app.containers.AdminPage.DashboardPage.month": "lună", "app.containers.AdminPage.DashboardPage.noData": "There is no data to be shown.", "app.containers.AdminPage.DashboardPage.noPhase": "No phase created for this project", "app.containers.AdminPage.DashboardPage.overview.management": "Management", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Proiecte și participare", "app.containers.AdminPage.DashboardPage.overview.showLess": "Show less", "app.containers.AdminPage.DashboardPage.overview.showMore": "Show more", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participation per project", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participarea in funcție de subiect", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Previous 30 days", "app.containers.AdminPage.DashboardPage.previous90Days": "Previous 90 days", "app.containers.AdminPage.DashboardPage.previousWeek": "Previous week", "app.containers.AdminPage.DashboardPage.previousYear": "Previous year", "app.containers.AdminPage.DashboardPage.projectType": "Project type : {projectType}", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "This base dataset is required to calculate the representativeness of platform users compared to the total population.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "See how representative your platform users are compared to the total population - based on data collected during user registration. Learn more about {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "See how representative your platform users are compared to the total population - based on data collected during user registration.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Community representativeness", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Back to dashboard", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "None of the enabled registration fields are supported at the moment.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Here you can show/hide items on the dashboard and enter the base data. Only the enabled fields for {userRegistrationLink} will appear here.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Edit base data", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "user registration", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.resolutionday": "in Days", "app.containers.AdminPage.DashboardPage.resolutionmonth": "in Months", "app.containers.AdminPage.DashboardPage.resolutionweek": "in Weeks", "app.containers.AdminPage.DashboardPage.selectProject": "Select project", "app.containers.AdminPage.DashboardPage.selectedProject": "current project filter", "app.containers.AdminPage.DashboardPage.selectedTopic": "current tag filter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Discover what's happening on your platform.", "app.containers.AdminPage.DashboardPage.tabOverview": "Prezentare generală", "app.containers.AdminPage.DashboardPage.tabReports": "Proiecte", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Representativeness", "app.containers.AdminPage.DashboardPage.tabUsers": "Users", "app.containers.AdminPage.DashboardPage.timelineType": "Cronologie", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "This {period}", "app.containers.AdminPage.DashboardPage.true": "true", "app.containers.AdminPage.DashboardPage.unspecified": "nespecificat", "app.containers.AdminPage.DashboardPage.users": "Users", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Users by age", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Users by geographic area", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Users by gender", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registrations", "app.containers.AdminPage.DashboardPage.week": "săptămână", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Tips for choosing a favicon image: select a simple image, as the shown image size is very small. The image should be saved as a PNG, and should be square with a transparent background (or a white background if necessary). Your favicon should only be set once as changes will require some technical support.", "app.containers.AdminPage.FaviconPage.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Succes!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Adaugă", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Șterge", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Folder managers", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Folder managers can edit the folder description, create new projects within the folder, and have project management rights over all projects within the folder. They cannot delete projects and they do not have access to projects that are not within their folder. You can {projectManagementInfoCenterLink} to find more information on project management rights.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/en/articles/4648650-assign-the-right-project-managers", "app.containers.AdminPage.FolderPermissions.noMatch": "No match found", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Căutați utilizatori", "app.containers.AdminPage.FoldersEdit.addToFolder": "Add to folder", "app.containers.AdminPage.FoldersEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Delete this folder", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Des<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.draftStatus": "Draft", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Add files to this folder", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the folder page.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Descriptions", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "There are no projects in this folder. Go back to the main Projects tab to create and add projects.", "app.containers.AdminPage.FoldersEdit.folderName": "Folder name", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.FoldersEdit.multilocError": "All text fields must be filled in for every language.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "There are no projects that you can add to this folder.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Folder card image", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Permissions", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Folder projects", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projects added to this folder", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projects you can add to this folder", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Choose whether this folder is \"draft\", \"published\" or \"archived\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Published", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Remove from folder", "app.containers.AdminPage.FoldersEdit.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Succes!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Short description", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "shown on the homepage", "app.containers.AdminPage.FoldersEdit.statusLabel": "Publication status", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Titlu", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Create a new folder", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "View Folder", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Customise the hero banner image and text.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Hero banner", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Save hero banner", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Editați termenii și condițiile și politica de confidențialitate ale platformei dvs. Alte pagini, inclusiv paginile Despre și Întrebări frecvente, pot fi editate în fila {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Platform policies", "app.containers.AdminPage.PagesEdition.privacy-policy": "Politica de confidențialitate", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Termeni și condiții", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "List of projects on the platform", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Projects dashboard", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Create new projects or manage existing projects.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Proiecte", "app.containers.AdminPage.ProjectDashboard.published": "Published", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Close settings panel", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Column layout", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Des<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Homepage description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Shown on the project card on the home page.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Shown on the project page. Clearly describe what the project is about, what you expect from your users and what they can expect from you.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectDescription.preview": "Preview", "app.containers.AdminPage.ProjectDescription.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectDescription.saved": "Saved!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Decide on which message you want to give to your audience. Edit your project and enrich it with images, videos, file attachments,… This information helps visitors to understand what your project is about.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Descrierea proiectului", "app.containers.AdminPage.ProjectDescription.whiteSpace": "White space", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Include border", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Vertical height", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Large", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Medium", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Small", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Latitudinea implicită a punctului central al hărții. Acceptă o valoare cuprinsă între -90 și 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Longitudinea implicită a punctului central al hărții. Acceptă o valoare cuprinsă între -180 și 180.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Editați stratul", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Editați stratul", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectEdit.MapTab.here": "here", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Importați fișierul GeoJSON", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Latitudine implicită", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Culoarea stratului", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Această culoare se aplică tuturor caracteristicilor din stratul de hartă. Dimen<PERSON><PERSON><PERSON> markerului, lățimile liniei și opacitatea umplerii sunt fixate în mod implicit.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "<PERSON>ct<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Opțional, selectați o pictogramă care este afișată în markere. Faceți click pe {url} pentru a vedea lista de pictograme pe care o puteți selecta.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "<PERSON><PERSON>le stratului", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Acest nume de strat este afișat în legenda hărții", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Sfat de instrumente pentru straturi", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Acest text este afișat ca un sfat de instrumente atunci când treceți cu mouse-ul peste caracteristicile stratului de pe hartă", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Longitudine implicită", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Centrul și zoomul implicit al hărții", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Personalizați vizualizarea hăr<PERSON>ii, inclusiv încărcarea și stilizarea straturilor hărții, setarea centrului hărții și a nivelului de zoom.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Configurarea h<PERSON>ii", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Eliminați stratul", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "articol de sprijin", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Nivel de zoom implicit", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Setați cât de mărită este harta în mod implicit. Alegeți o valoare între 0 și 20, unde 0 este complet micșorat (întreagul glob este vizibil) și 20 este complet mărită (blocurile și clădirile sunt vizibile)", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Add a poll question", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Șterge", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Șterge", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Export the poll results", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "The maximum number of choices is greater than the number of options", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Multiple choice", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "<PERSON>ăr<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "All questions must have answer choices", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Only one option", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Poll respondents have only one choice", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Here you can create poll questions, set the answer choices for participants to choose from for each question, decide whether you want participants to only be able to select one answer choice (single choice) or multiple answer choices (multiple choice), and export the poll results. You can create multiple poll questions within one poll.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Single choice", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Polls settings and results", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Wrong maximum", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Give feedback, add tags or copy posts to the next project phase.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Input manager", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Export the survey results (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Here, you can download the results of the Typeform survey(s) within this project as an Excel file.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Survey Results", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Survey", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Consult the survey answers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Add cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Sunteți sigur?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Des<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Use this to explain what is required from volunteers and what they can expect.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Couldn't save because the form contains errors.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Imagine", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Titlu", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Șterge", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Edita<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Edit cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Add a description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Add a title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Export volunteers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "New cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Here, you can set up the causes users can volunteer for and download the volunteers.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Voluntariat", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {no volunteers} one {# volunteer} other {# volunteers}}", "app.containers.AdminPage.ProjectEdit.addNewInput": "Add an input", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Project tags", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonymous polling", "app.containers.AdminPage.ProjectEdit.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "This area cannot be deleted because it is being used to display projects on the following more custom page(s). You will need to unlink the area from the page, or delete the page before you can delete the area.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "All Areas", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "The project will show on every area filter.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Area filter", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projects can be filtered on the homepage using areas. Areas can be set {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "No specific area", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "The project will not show when filtering by area.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Selection", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "The project will show on selected area filter(s).", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Cards", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Create a project from a template", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Creați un sondaj extern", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Create an in-platform survey", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Set up a survey without leaving our platform.", "app.containers.AdminPage.ProjectEdit.createPoll": "Create a poll", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Set up a multiple-choice questionnaire.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "You can set the default order for posts to be displayed on the main project page.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Ordonare", "app.containers.AdminPage.ProjectEdit.departments": "Departments", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Des<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.disabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Enabled", "app.containers.AdminPage.ProjectEdit.draft": "Draft", "app.containers.AdminPage.ProjectEdit.draftStatus": "Draft", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Administrează", "app.containers.AdminPage.ProjectEdit.enabled": "Enabled", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Evenimente", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Documente atașate (max. 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the project information page.", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Find volunteers", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Ask participants to volunteer for activities and causes.", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Select a folder to add this project to.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Custom content", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Submissions to this form have started to come in. Changes to the form may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Form successfully saved", "app.containers.AdminPage.ProjectEdit.fromATemplate": "From a template", "app.containers.AdminPage.ProjectEdit.generalTab": "General", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Collect input and feedback", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Who is responsible for processing the posts?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "All new input in this project will be assigned to this person. The assignee can be changed in the {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Commenting on posts", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Input form", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "input manager", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Input manager", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Submitting posts", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Default view", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Choose the default view to show input: cards in a grid view or pins on a map. Participants can manually switch between the two views.", "app.containers.AdminPage.ProjectEdit.limited": "Limited", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Load more templates", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Map", "app.containers.AdminPage.ProjectEdit.mapTab": "Hartă", "app.containers.AdminPage.ProjectEdit.maximum": "Maximum", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Participants cannot exceed this budget when submitting their basket.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Require participants to meet a minimum budget to submit their basket (enter '0' if you would not like to set a minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON> multe de<PERSON>ii", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "http://support.govocal.com/articles/1948422-appoint-the-right-project-moderators", "app.containers.AdminPage.ProjectEdit.newProject": "New Project", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Cel mai recent", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Not a valid amount", "app.containers.AdminPage.ProjectEdit.noFolder": "No folder", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "No templates found", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Acest câmp nu poate fi gol", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Not a valid number", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Cel mai vechi", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Only visible to admin", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON>u", "app.containers.AdminPage.ProjectEdit.optionYes": "Yes (select folder)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Participation levels", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "What do you want to do?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Choose how users can participate.", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Access rights", "app.containers.AdminPage.ProjectEdit.pollTab": "Son<PERSON>j de tip poll", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Project card image", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "This image is part of the project card; the card that summarizes the project and is shown on the homepage for example.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "This image is shown at the top of the project page.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Project managers can edit projects, manage posts and email participants. You can {moderationInfoCenterLink} to find more information about the rights assigned to project managers.", "app.containers.AdminPage.ProjectEdit.projectName": "Project name", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Project type", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projects with a timeline have a clear beginning and end and can have different phases. Projects without a timeline are continuous.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "The project type can not be changed later.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Published", "app.containers.AdminPage.ProjectEdit.purposes": "Purposes", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Aleat<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "An error occurred while saving your data. Please try again.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Success!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Your form has been saved!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Search the templates", "app.containers.AdminPage.ProjectEdit.selectGroups": "Select group(s)", "app.containers.AdminPage.ProjectEdit.shareInformation": "Share information", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Set up and personalize your project.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visit our support center", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Cancel", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Are you sure you want to leave?", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Embed URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Service", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "You can find more information on how to embed a survey {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "This tag cannot be deleted because it is being used to display projects on the following more custom page(s). \nYou will need to unlink the tag from the page, or delete the page before you can delete the tag.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "General settings for the project", "app.containers.AdminPage.ProjectEdit.titleLabel": "Title", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Choose a title that is short, engaging and clear. It will be shown in the dropdown overview and on the project cards on the home page.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tags", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Select {topicsCopy} for this project. Users can use these to filter projects by.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Total budget", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "În tendințe", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Neatribuit", "app.containers.AdminPage.ProjectEdit.unlimited": "Unlimited", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Use template", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Voluntariat", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# groups can view} one {# group can view} other {# groups can view}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Add an event", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "End", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Șterge", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Are you sure you want to delete this event? There is no way to undo this!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Event description", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Edita<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Edit Event", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Documente atașate (max. 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Attachments are shown below the event description.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Locație", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Create a new event", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "We could not save your changes, please try again.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Success!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Link upcoming events to this projects and show them on the project's event page.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Title and dates", "app.containers.AdminPage.ProjectEvents.titleEvents": "Project events", "app.containers.AdminPage.ProjectEvents.titleLabel": "Event name", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Collapse all fields", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Field description", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Edit input form", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Enabled", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Include this field.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Expand all fields", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Input form", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required", "app.containers.AdminPage.ProjectIdeaForm.required": "Obligator<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Require this field to be filled in.", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Saved!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "View form", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Dates", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Are you sure you want to delete this phase?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Phase description", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "End Date", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Documente atașate (max. 50MB)", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "A apărut o eroare la trimiterea formularului, vă rugăm să încercați din nou.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Saved!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Data de începere", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Phase name", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartiție", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminology (homepage filter)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "How should tags in the front page filter be called? E.g. tags, categories, departments, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Tags can be configured {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "here", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Term for one tag (singular)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Term for multiple tags (plural)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tags", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Add field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Add a new registration field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Add option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Answer format", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Provide an answer format", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Provide an answer option for all languages", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Save answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Answer option successfully saved", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Answer choices", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Fields", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Drag and drop the fields to determine the order in which they appear in the sign-up form.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "De<PERSON>ult field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Șterge", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Optional text shown under the field name on the signup form.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Answer choices for place of residence can be set in the {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Edita<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Edit answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Des<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Field name", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Provide a field name for all languages", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Field settings", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Yes-no (checkbox)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Date", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Long answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Multiple choice (select multiple)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numeric value", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Multiple choice (select one)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Short answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Geographic areas tab", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON><PERSON> ascuns", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Make answering this field required?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Custom fields", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Add answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Șterge", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Obligator<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Save field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON> successfully saved", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON><PERSON> coloane", "app.containers.AdminPage.SettingsPage.addAreaButton": "Add a geographic area", "app.containers.AdminPage.SettingsPage.addTopicButton": "Add tag", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Are you sure you want to delete this area?", "app.containers.AdminPage.SettingsPage.areaTerm": "Term for one area (singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "area", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Șterge", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Edita<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Term for multiple areas (plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "areas", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Display avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Show profile pictures of participants and number of them to non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeader": "Header text", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Sub-header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Sub-header text", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Banner text", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Show preview for", "app.containers.AdminPage.SettingsPage.brandingDescription": "Add your logo and set the platform colors.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Platform branding", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "Aspect", "app.containers.AdminPage.SettingsPage.color_primary": "Primary color", "app.containers.AdminPage.SettingsPage.color_secondary": "Secondary color", "app.containers.AdminPage.SettingsPage.color_text": "Text color", "app.containers.AdminPage.SettingsPage.colorsTitle": "Colors", "app.containers.AdminPage.SettingsPage.confirmHeader": "Are you sure you want to delete this tag?", "app.containers.AdminPage.SettingsPage.contentModeration": "Content moderation", "app.containers.AdminPage.SettingsPage.ctaHeader": "Buttons", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Custom page header | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Custom", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Button text", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Button link", "app.containers.AdminPage.SettingsPage.defaultTopic": "Default tag", "app.containers.AdminPage.SettingsPage.delete": "Șterge", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Șterge", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "This will delete the tag from all existing posts. This change will apply to all projects.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Add and delete tags that you would like to use on your platform to categorize posts. You can add the tags to specific projects in the {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Desktop", "app.containers.AdminPage.SettingsPage.editFormTitle": "Edit area", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Edita<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Edit tag", "app.containers.AdminPage.SettingsPage.fieldDescription": "Area description", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "This description is only for internal collaboration and is not shown to users.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Area name", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Provide an area name for all languages", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "The name you choose for each area can be used as a registration field option and to filter projects on the homepage.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Save tag", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Tag name", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Provide a tag name for all languages", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "The name you choose for each tag will be visible to the platform users", "app.containers.AdminPage.SettingsPage.fixedRatio": "Banner cu raport fix", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Acest tip de banner funcționează cel mai bine cu imagini care nu ar trebui să fie tăiate, cum ar fi imagini cu text, un logo sau elemente specifice care sunt esențiale pentru cetățenii dumneavoastră. Acest banner este înlocuit cu o casetă solidă în culoarea primară atunci când utilizatorii sunt conectați. Puteți seta această culoare în setările generale. Mai multe informații despre utilizarea recomandată a imaginilor pot fi găsite pe site-ul nostru {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "baza de cunoștințe", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Banner pe toată lățimea", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Acest banner se întinde pe toată lățimea pentru un efect vizual deosebit. Imaginea va încerca să acopere cât mai mult spațiu posibil, ceea ce face ca ea să nu fie întotdeauna vizibilă în orice moment. Puteți combina acest banner cu o suprapunere de orice culoare. Mai multe informații despre utilizarea recomandată a imaginii pot fi găsite pe {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.header": "Homepage banner", "app.containers.AdminPage.SettingsPage.headerDescription": "Customise the homepage banner image and text.", "app.containers.AdminPage.SettingsPage.header_bg": "Banner image", "app.containers.AdminPage.SettingsPage.helmetDescription": "Admin settings page", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin settings page", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Add your own content to the customizable section at the bottom of the homepage.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Homepage header | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Image overlay color", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Image overlay opacity", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Detect inappropriate content", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Auto-detect inappropriate content posted on the platform.", "app.containers.AdminPage.SettingsPage.languages": "Languages", "app.containers.AdminPage.SettingsPage.languagesTooltip": "You can select multiple languages in which you want to offer your platform to users. You will need to create content for every selected language.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Activity", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Please upload a header image", "app.containers.AdminPage.SettingsPage.no_button": "No button", "app.containers.AdminPage.SettingsPage.organizationName": "Name of city or organization", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Enable overlay", "app.containers.AdminPage.SettingsPage.phone": "Phone", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Platform configuration", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Profanity blocker", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Block input, proposals and comments containing the most commonly reported offensive words", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "This text is shown on the homepage above the projects.", "app.containers.AdminPage.SettingsPage.projectsSettings": "project settings", "app.containers.AdminPage.SettingsPage.projects_header": "Projects header", "app.containers.AdminPage.SettingsPage.registrationFields": "Registration fields", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Provide a short description at the top of your registration form.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registration", "app.containers.AdminPage.SettingsPage.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "Save area", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Success!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Sign up\"", "app.containers.AdminPage.SettingsPage.signed_in": "Button for registered visitors", "app.containers.AdminPage.SettingsPage.signed_out": "Button for non-registered visitors", "app.containers.AdminPage.SettingsPage.signupFormText": "Registration helper text", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Add a short description at the top of the sign-up form.", "app.containers.AdminPage.SettingsPage.step1": "Email and password step", "app.containers.AdminPage.SettingsPage.step1Tooltip": "This is shown on the top of the first page of the sign-up form (name, email, password).", "app.containers.AdminPage.SettingsPage.step2": "Registration questions step", "app.containers.AdminPage.SettingsPage.step2Tooltip": "This is shown on the top of the second page of the sign-up form (additional registration fields).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Define the geographic areas that you would like to use for your platform, such as neighborhoods, boroughs or districts. You can associate these geographic areas with projects (filterable on the landing page) or ask participants to select their area of residence as a registration field to create Smart Groups and define access rights.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Choose how people will see your organization name, select the languages of your platform and link to your website.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "The provided subtitle exceeds the maximum allowed character limit (90 chars)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminology", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Settings updated successfully.", "app.containers.AdminPage.SettingsPage.tabPolicies": "Policies", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registration", "app.containers.AdminPage.SettingsPage.tabSettings": "General", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Define what geographic unit you would like to use for your projects (e.g., neighborhoods, districts, boroughs, etc.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geographic areas", "app.containers.AdminPage.SettingsPage.titleBasic": "General settings", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "The provided title exceeds the maximum allowed character limit (35 chars)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tag manager", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Acest banner este util în special în cazul imaginilor care nu se potrivesc bine cu textul din titlu, subtitlu sau buton. Aceste elemente vor fi împinse sub banner. Mai multe informații despre utilizarea recomandată a imaginilor pot fi găsite pe {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.urlError": "The URL is not valid", "app.containers.AdminPage.SettingsPage.urlTitle": "Website", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "You can add a link to your own website. This link will be used on the bottom of the homepage.", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "Emails", "app.containers.AdminPage.SideBar.groups": "Groups", "app.containers.AdminPage.SideBar.guide": "Guide", "app.containers.AdminPage.SideBar.inputManager": "Input manager", "app.containers.AdminPage.SideBar.insights": "Reporting", "app.containers.AdminPage.SideBar.menu": "Pages & menu", "app.containers.AdminPage.SideBar.messaging": "Messaging", "app.containers.AdminPage.SideBar.moderation": "Activity", "app.containers.AdminPage.SideBar.processing": "Processing", "app.containers.AdminPage.SideBar.projects": "Proiecte", "app.containers.AdminPage.SideBar.settings": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.users": "Users", "app.containers.AdminPage.SideBar.workshops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.addTopics": "Adaugă", "app.containers.AdminPage.Topics.browseTopics": "Browse tags", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "Are you sure you want to delete this project tag?", "app.containers.AdminPage.Topics.delete": "Șterge", "app.containers.AdminPage.Topics.deleteTopicLabel": "Șterge", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "This tag will no longer be able to be added to new posts in this project.", "app.containers.AdminPage.Topics.inputForm": "Input form", "app.containers.AdminPage.Topics.lastTopicWarning": "At least one tag is required. If you do not want to use tags, they can be disabled in the {ideaFormLink} tab.", "app.containers.AdminPage.Topics.projectTopicsDescription": "You can add and delete the tags that can be assigned to posts in this project.", "app.containers.AdminPage.Topics.remove": "Elimină", "app.containers.AdminPage.Topics.title": "Project tags", "app.containers.AdminPage.Topics.topicManager": "Tag manager", "app.containers.AdminPage.Topics.topicManagerInfo": "If you would like to add additional project tags, you can do so in the {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Add a new group", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Group name", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Furnizați un nume de grup", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "What type of group do you need?", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Salvați grupul", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Află mai multe despre grupuri", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "You can select users from the overview and add them to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "You can define conditions and users who meet the conditions are automatically added to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Manual group", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart group", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "There is no one in this group yet", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Go to {allUsersLink} to manually add some users.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "No user(s) match your search", "app.containers.AdminPage.Users.GroupsPanel.select": "Select", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Sunteți sigur?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "An error occurred while adding users to the groups, please try again.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Remove from group", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Delete selected users from this group?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "An error occurred while deleting users from the group, please try again.", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Adaugă", "app.containers.AdminPage.groups.permissions.add": "Add", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Delete", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Șterge", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Are you sure you want to remove this group from the project?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Select one or more groups", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {No members} one {1 member} other {{count} members}}", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Sunteți sigur?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Project managers not found", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Nothing is shown, because there are no actions the user can take in this project.", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Pending invitation", "app.containers.AdminPage.groups.permissions.save": "Save", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Success!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.projects.all.existingProjects": "Existing projects", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projects and folders", "app.containers.AdminPage.widgets.copied": "Copied to clipboard", "app.containers.AdminPage.widgets.copyToClipboard": "Copy this code", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Copy the HTML code", "app.containers.AdminPage.widgets.fieldAccentColor": "Accent color", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Widget background color", "app.containers.AdminPage.widgets.fieldButtonText": "Button text", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Join now", "app.containers.AdminPage.widgets.fieldFont": "Font", "app.containers.AdminPage.widgets.fieldFontDescription": "This must be an existing font name from {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Font size (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Header subtitle", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "You can have a say", "app.containers.AdminPage.widgets.fieldHeaderText": "Header title", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Our participation platform", "app.containers.AdminPage.widgets.fieldHeight": "Height (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Number of posts", "app.containers.AdminPage.widgets.fieldProjects": "Proiecte", "app.containers.AdminPage.widgets.fieldRelativeLink": "Links to", "app.containers.AdminPage.widgets.fieldShowFooter": "Show button", "app.containers.AdminPage.widgets.fieldShowHeader": "Show header", "app.containers.AdminPage.widgets.fieldShowLogo": "Show logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Site background color", "app.containers.AdminPage.widgets.fieldSort": "Sorted by", "app.containers.AdminPage.widgets.fieldTextColor": "Text color", "app.containers.AdminPage.widgets.fieldTopics": "Subiecte", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Homepage", "app.containers.AdminPage.widgets.htmlCodeExplanation": "You can copy this HTML code and paste it on that part of your website where you want to add your widget.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget HTML code", "app.containers.AdminPage.widgets.previewTitle": "Preview", "app.containers.AdminPage.widgets.settingsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortNewest": "<PERSON><PERSON> mai recente", "app.containers.AdminPage.widgets.sortPopular": "<PERSON>le mai votate", "app.containers.AdminPage.widgets.sortTrending": "În tendințe", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimensions", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Header & Footer", "app.containers.AdminPage.widgets.titleInputSelection": "Input selection", "app.containers.AdminPage.widgets.titleStyle": "Style", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Save", "app.containers.admin.ContentBuilder.delete": "Delete", "app.containers.admin.ContentBuilder.error": "error", "app.containers.admin.ContentBuilder.errorMessage": "There is an error on {locale} content, please fix the issue to be able to save your changes", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 column", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 column", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 columns with 30% and 60% width respectively", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 columns with 60% and 30% width respectively", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 even columns", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Show more", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Title", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Participants timeline", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Chart", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Date range", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Title", "app.containers.admin.ReportBuilder.charts.noData": "There is no data available for the filters you have selected.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Traffic sources", "app.containers.admin.ReportBuilder.charts.usersByAge": "Users by age", "app.containers.admin.ReportBuilder.charts.usersByGender": "Users by gender", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Visitor timeline", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Add status", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Șterge", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Edita<PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Edit status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statuses currently assigned to participant input cannot be deleted. You can remove/change the status from existing input in the {manageTab} tab.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "This status cannot be deleted or moved.", "app.containers.admin.ideaStatuses.all.manage": "Administrează", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Selected for implementation or next steps", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Approved", "app.containers.admin.ideaStatuses.form.category": "Category", "app.containers.admin.ideaStatuses.form.categoryDescription": "Please select the category that best represents your status. This selection will help our analytics tool to more accurately process and analyze posts.", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "alții", "app.containers.admin.ideaStatuses.form.fieldColor": "Color", "app.containers.admin.ideaStatuses.form.fieldDescription": "Status Description", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Provide a status description for all lanugages", "app.containers.admin.ideaStatuses.form.fieldTitle": "Status Name", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Provide a status name for all lanugages", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Successfully implemented", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implemented", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Ineligible or not selected to move forward", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Not Selected", "app.containers.admin.ideaStatuses.form.saveStatus": "Save status", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Considered for implementation or next steps", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Under Consideration", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Viewed but not yet processed", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Viewed", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Manage input and their statuses.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Input manager | Participation platform of {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Give feedback, add tags and move input from one project to another", "app.containers.admin.import.importInputs": "Import inputs", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Anyone", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Selection", "app.containers.admin.project.permissions.viewingRightsTitle": "Who can see this project?", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Rata de participare", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automated campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automated emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "From {quantity} campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Custom campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Custom emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Total emails sent", "app.modules.commercial.analytics.admin.components.Events.completed": "Completat", "app.modules.commercial.analytics.admin.components.Events.events": "Events", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Total evenimente adăugate", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Urmează", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Accepted", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitations", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Pending", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Numărul total de invitații trimise", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Accesați Managerul de intrare", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Active", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Proiecte care nu sunt arhivate și care sunt vizibile în tabelul \"Active\" de pe pagina principală", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archived", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Draft projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Finished", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Proiecte care nu sunt arhivate și care sunt vizibile în tabelul \"Active\" de pe pagina principală", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Total proiecte", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Numărul de proiecte care sunt vizibile pe platformă", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "New registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Rata de înregistrare", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Total registrations", "app.modules.commercial.analytics.admin.components.Tab": "Vizitatori", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Ultimele 30 de zile:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Ultimele 7 zile:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Vizualizări de pagină pe vizită", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Durata vizitei", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Vizitatori\" este numărul de vizitatori unici. Dacă o persoană vizitează platforma de mai multe ori, aceasta este luată în considerare o singură dată.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Vizite", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Vizite\" reprezintă numărul de sesiuni. Dacă o persoană a vizitat platforma de mai multe ori, fiecare vizită este luată în considerare.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Ieri:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Language", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Numărul de vizitatori", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Procentul de vizitatori", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Referent", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "faceți clic aici", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Refer<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Pentru a vizualiza lista completă a referenților, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Surse de trafic", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visits", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Email deliveries over time", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registrations over time", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Date", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistică", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Statistici generale", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Vizite și vizitatori de-a lungul timpului", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Total pe perioadă", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Language", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Campanii", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Intrare directă", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Procentul de vizite", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Mo<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Rețele sociale", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Surse de trafic", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Numă<PERSON>l de vizite", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Site-uri web", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "You can remove this content flag by selecting this item and clicking the remove button at the top. It will then reappear in the Seen or Not seen tabs", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Inappropriate content auto-detected.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "There are no posts reported for review by the community or flagged for inappropriate content by our Natural Language Processing system", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Remove {numberOfItems, plural, one {content warning} other {# content warnings}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Reported as inappropriate by a platform user.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Content Warnings", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Report builder", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Pages shown on your navigation bar", "app.modules.navbar.admin.containers.createCustomPageButton": "Create custom page", "app.modules.navbar.admin.containers.deletePageConfirmation": "Are you sure you want to delete this page? This cannot be undone. You can also remove the page from the navigation bar if you aren’t ready to delete it yet.", "app.modules.navbar.admin.containers.pageHeader": "Pages & menu", "app.modules.navbar.admin.containers.pageSubtitle": "Your navigation bar can display up to five pages in addition to the Home and projects pages. You can rename menu items, re-order and add new pages  with your own content.", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visit our support center", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "For more information on recommended image resolutions, {supportPageLink}."}