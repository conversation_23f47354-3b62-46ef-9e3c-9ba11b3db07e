{"UI.FormComponents.required": "gere<PERSON><PERSON>", "app.Admin.ManagementFeed.action": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.after": "Sonra", "app.Admin.ManagementFeed.before": "Önce", "app.Admin.ManagementFeed.changed": "Değiştirilmiş", "app.Admin.ManagementFeed.created": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.date": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.deleted": "Silinmiş", "app.Admin.ManagementFeed.folder": "Klasör", "app.Admin.ManagementFeed.idea": "Fikir", "app.Admin.ManagementFeed.in": "{project}projesinde", "app.Admin.ManagementFeed.item": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.key": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.managementFeedNudge": "Yönetim akışına erişim mevcut lisansınıza dahil de<PERSON>dir. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.Admin.ManagementFeed.noActivityFound": "Etkinlik bulunamadı", "app.Admin.ManagementFeed.phase": "Aşama", "app.Admin.ManagementFeed.project": "<PERSON><PERSON>", "app.Admin.ManagementFeed.projectReviewApproved": "<PERSON><PERSON>", "app.Admin.ManagementFeed.projectReviewRequested": "<PERSON>je incelemesi talep edildi", "app.Admin.ManagementFeed.title": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.user": "Kullanıcı", "app.Admin.ManagementFeed.userPlaceholder": "Bir kullanıcı seçin", "app.Admin.ManagementFeed.value": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.viewDetails": "Detayları görüntüle", "app.Admin.ManagementFeed.warning": "Deneysel özellik: Son 30 gün içinde yöneticiler veya müdürler tarafından gerçekleştirilen seçili eylemlerin minimal bir listesi. <PERSON><PERSON><PERSON> eylemler dahil edilmemiştir.", "app.Admin.Moderation.managementFeed": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.Moderation.participationFeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Emin misin?", "app.components.Admin.Campaigns.clicked": "Tıklandı", "app.components.Admin.Campaigns.deleteCampaignButton": "Kampanyayı sil", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Kabul Edildi", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Zıpladı", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Tıklandı", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip": "E-postanıza bir veya daha fazla bağlantı eklediğinizde, bir bağlantıya tıklayan kullanıcı sayısı burada gösterilecektir.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "<PERSON><PERSON><PERSON> edil<PERSON>", "app.components.Admin.Campaigns.deliveryStatus_failed": "Başarısız", "app.components.Admin.Campaigns.deliveryStatus_opened": "Açıldı", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Taslak", "app.components.Admin.Campaigns.manageButtonLabel": "Yönetmek", "app.components.Admin.Campaigns.opened": "Açıldı", "app.components.Admin.Campaigns.project": "<PERSON><PERSON>", "app.components.Admin.Campaigns.recipientsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON>", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.info": "<PERSON>u gö<PERSON><PERSON><PERSON><PERSON> her zaman belirli bir oranda kırpılarak tüm önemli unsurların her zaman görüntülenmesi sağlanır. Bu görüntü türü için {link} adresi {aspect}şeklindedir.", "app.components.Admin.ImageCropper.infoLinkText": "önerilen oran", "app.components.Admin.ImageCropper.mobileCropExplanation": "Not: Mobil cihazlarda görüntü 3:1 oranında kırpılacağından, g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n önemli alanları dikey kesikli çizgilerin içinde yer almalıdır.", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Kayıtlı kullanıcılar için başlık metni", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Uyarı: Seçtiğiniz renk yeterince yüksek kontrasta sahip değil. Bu durum metinlerin zor okummasına neden olabilir. Okunabilirliği optimize etmek için daha koyu bir renk seçin.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Gezinme çubuğuna Etkinlikler ekleyin", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gezinme çubuğunda tüm proje etkinliklerine bağlantı eklenir.", "app.components.AdminPage.SettingsPage.eventsSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "<PERSON> <PERSON><PERSON><PERSON>ril<PERSON><PERSON><PERSON>", "app.components.AnonymousPostingToggle.userAnonymity": "Kullanıcı anonimliği", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Kullanıcılar kimliklerini diğer <PERSON>, proje yöneticilerinden ve yöneticilerden gizleyebilecekler. Bu katkılar yine de denetlenebilecektir.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Kullanıcıların anonim olarak katılmasına izin verin", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Kullanıcılar yine de gerçek adlarıyla katılmayı tercih edebilirler, ancak isterlerse katkılarını anonim olarak gönderme seçeneğine sahip olacaklardır. <PERSON><PERSON><PERSON>, katk<PERSON>larının geçmesi için Erişim Hakları sekmesinde belirlenen gerekliliklere uymaları gerekecektir. Kullanıcı profili verileri, katılım verisi dışa aktarımında mevcut olmayacaktır.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Kullanıcı anonimliği hakkında daha fazla bilgiyi {supportArticle}adresimizde bulabilirsiniz.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "destek makalesi", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Ek koltuklar eklendiğinde, faturaland<PERSON><PERSON><PERSON>z artırılacaktır. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Anketi tamamladığınız için teşekkür ederiz! Önümüzdeki çeyrekte tekrar katılabilirsiniz.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Pdf olarak indir", "app.components.FormSync.downloadExcelTemplate": "Bir Excel şablonu indirin", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel şablonları Sıralama sorular<PERSON>nı, <PERSON><PERSON>, <PERSON><PERSON><PERSON> yükleme sorularını ve herhangi bir harita giriş sorusunu (Drop Pin, Draw Route, Draw Area, ESRI dosya yükleme) içermeyecektir, çünkü bunlar şu anda toplu içe aktarma için desteklenmemektedir.", "app.components.ProjectTemplatePreview.close": "Ka<PERSON><PERSON>", "app.components.ProjectTemplatePreview.createProject": "<PERSON><PERSON>", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "''{templateTitle}'' şablonuna dayalı bir proje oluşturun", "app.components.ProjectTemplatePreview.goBack": "<PERSON><PERSON> gidin", "app.components.ProjectTemplatePreview.goBackTo": "{goBackLink} hedefine geri dön.", "app.components.ProjectTemplatePreview.govocalExpert": "Go Vocal uzmanı", "app.components.ProjectTemplatePreview.infoboxLine1": "Bu şablonu katılım projeniz için kullanmak istiyor musunuz?", "app.components.ProjectTemplatePreview.infoboxLine2": "Kent yönetiminizdeki sorumlu kişiye ulaşın veya {link} ile iletişime geçin.", "app.components.ProjectTemplatePreview.projectFolder": "<PERSON><PERSON>", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Seçilen tarih geçersiz. Lütfen şu biçimde bir tarih girin: YYYY-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Lütfen proje için bir ba<PERSON><PERSON><PERSON>ç tarihi seçin", "app.components.ProjectTemplatePreview.projectStartDate": "Projenizin başlangıç tarihi", "app.components.ProjectTemplatePreview.projectTitle": "Projenizin başlığı", "app.components.ProjectTemplatePreview.projectTitleError": "Lütfen bir proje başlığı yazın", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Lütfen tüm diller için bir proje başlığı yazın", "app.components.ProjectTemplatePreview.projectsOverviewPage": "projelere genel bakış sayfası", "app.components.ProjectTemplatePreview.responseError": "Hay aksi! Bir şeyler ters gitti.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "<PERSON><PERSON> fazla şab<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.successMessage": "Proje başarıyla oluşturuldu!", "app.components.ProjectTemplatePreview.typeProjectName": "<PERSON><PERSON>nin adını yazın", "app.components.ProjectTemplatePreview.useTemplate": "<PERSON>u şab<PERSON> kullanın", "app.components.SeatInfo.additionalSeats": "<PERSON><PERSON>", "app.components.SeatInfo.additionalSeatsToolTip": "<PERSON><PERSON>, '<PERSON><PERSON>'ın üzerine satın aldığınız ek koltuk sayısını gösterir.", "app.components.SeatInfo.adminSeats": "Yönetici koltukları", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} yönetici koltukları dahil", "app.components.SeatInfo.adminSeatsTooltip1": "Yöneticiler platformdan sorumludur ve tüm klasörler ve projeler için yönetici haklarına sahiptirler. Farklı roller hakkında daha fazla bilgi edinmek için {visitHelpCenter} adresini ziyaret edebilirsiniz.", "app.components.SeatInfo.currentAdminSeatsTitle": "Mevcut yönetici koltukları", "app.components.SeatInfo.currentManagerSeatsTitle": "Mevcut yönetici koltukları", "app.components.SeatInfo.includedAdminToolTip": "<PERSON><PERSON>, y<PERSON><PERSON><PERSON><PERSON> sözleşmeye dahil olan yöneticiler için mevcut koltuk sayısını gösterir.", "app.components.SeatInfo.includedManagerToolTip": "<PERSON><PERSON>, y<PERSON><PERSON><PERSON><PERSON> sözleşmeye dahil olan yöneticiler için mevcut koltuk sayısını gösterir.", "app.components.SeatInfo.includedSeats": "<PERSON><PERSON><PERSON><PERSON> dahil", "app.components.SeatInfo.managerSeats": "Yönetici koltukları", "app.components.SeatInfo.managerSeatsTooltip": "Klasör/proje yöneticileri sınırs<PERSON>z sayıda klasörü/projeyi yönetebilir. Farklı roller hakkında daha fazla bilgi edinmek için {visitHelpCenter} adresini ziyaret edebilirsiniz.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} yönetici koltukları dahil", "app.components.SeatInfo.remainingSeats": "<PERSON><PERSON>", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Toplam koltuk sayısı", "app.components.SeatInfo.totalSeatsTooltip": "<PERSON><PERSON>, planınızdaki toplam koltuk sayısını ve satın aldığınız ek koltukları gösterir.", "app.components.SeatInfo.usedSeats": "Kullanılmış koltuklar", "app.components.SeatInfo.view": "G<PERSON>rü<PERSON><PERSON><PERSON>", "app.components.SeatInfo.visitHelpCenter": "<PERSON>ım merkezimizi ziyaret edin", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Planınızda {adminSeatsIncluded}var. <PERSON><PERSON><PERSON> kolt<PERSON> kulland<PERSON> son<PERSON>, '<PERSON><PERSON> koltuklar' altında ekstra koltuklar eklenecektir.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Planınızda {manager<PERSON><PERSON><PERSON><PERSON>ncluded}, klasör yöneticileri ve proje yöneticileri için uygundur. <PERSON>ü<PERSON> koltukları kullandıktan sonra, '<PERSON>k koltuklar' altında ekstra koltuklar eklenecektir.", "app.components.UserSearch.addModerators": "<PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Kullanıcıları aramak için yazın...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternatif hata mesajı", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Varsayılan <PERSON>, kullanıcılara aşağıdaki hata mesajı gösterilecektir:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Hata mesajını özelleştirme", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"Alternatif hata mesajı\" metin kutusunu kull<PERSON>rak her dil için bu mesajın üzerine yazabilirsiniz. Metin kutusunu boş b<PERSON><PERSON><PERSON>, var<PERSON><PERSON>lan mesaj gösterilecektir.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "<PERSON>a mesajı", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, katıl<PERSON><PERSON> gerekliliklerini karşılamadıklarında bunu göreceklerdir.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Hata mesajını kaydet", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Seçili soru yok. Lütfen önce bir soru seçin.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "Cevap yok", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} yanı<PERSON>ar", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "<PERSON><PERSON> so<PERSON>u", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} <PERSON><PERSON><PERSON><PERSON> kadar", "app.components.admin.DatePhasePicker.Input.openEnded": "Açık uçlu", "app.components.admin.DatePhasePicker.Input.selectDate": "<PERSON><PERSON><PERSON>", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Bitiş tarihini te<PERSON>n", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Net başlangıç tarihi", "app.components.admin.Graphs": "Mevcut filtrelerle ilgili veri yok.", "app.components.admin.Graphs.noDataShort": "<PERSON>eri mevcut de<PERSON>.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Zaman içindeki yorumlar", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Zaman içindeki gönderiler", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Zaman içindeki tepkiler", "app.components.admin.InputManager.onePost": "1 girdi", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Çevrimdışı seçim ayarı", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Çevrimdışı oy ayarlaması", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, y<PERSON>z yüze veya kağıt oyları gibi diğer kaynaklardan gelen katılım verilerini dahil etmenize olanak tanır:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Dijital oylardan görsel olarak farklı olacaktır.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "<PERSON><PERSON>, nihai oylama sonuçlarını etkileyecektir.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Katılım verileri gösterge tablolarına yansıtılmayacaktır.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Bir seçenek için çevrimdışı oylar bir projede yalnızca bir kez belirlenebilir ve bir projenin tüm aşamaları arasında paylaşılır.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Önce toplam çevrimdışı katılımcı sayısını girmelisiniz.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Toplam çevrimdışı katılımcılar", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "Doğru sonuçları hesaplayabilmek için <b>bu aşamadaki toplam çevrimdışı katılımcı sayısını</b> bilmemiz gerekmektedir.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Lütfen sadece çevrimdışı katılanları belirtiniz.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "app.components.admin.PostManager.PostPreview.assignee": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.cancelEdit": "Düzenlemeyi iptal et", "app.components.admin.PostManager.PostPreview.currentStatus": "<PERSON><PERSON> andaki durum", "app.components.admin.PostManager.PostPreview.delete": "Sil", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Bu girdiyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Bu girdiyi silmek istediğinizden emin misiniz? Girdi projedeki tüm aşamalardan silinir ve kurtarılamaz.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "Atanmamış", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "<PERSON><PERSON><PERSON> diğer katılımcıların katılımcı bütçelerine kaç kez dahil edildiği", "app.components.admin.PostManager.PostPreview.picks": "<PERSON><PERSON><PERSON><PERSON>: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "<PERSON><PERSON><PERSON> sayılır:", "app.components.admin.PostManager.PostPreview.save": "<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "Özellik katmanı ekle", "app.components.admin.PostManager.addFeatureLayerInstruction": "ArcGIS Online üzerinde barındırılan özellik katmanının URL'sini kopyalayın ve aşağıdaki girişe yapıştırın:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Haritaya yeni bir özellik katmanı ekleyin", "app.components.admin.PostManager.addWebMap": "Web Haritası Ekle", "app.components.admin.PostManager.addWebMapInstruction": "Web Haritanızın portal kimliğini ArcGIS Online'dan kopyalayın ve aşağıdaki girişe yapıştırın:", "app.components.admin.PostManager.allPhases": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.allProjects": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.components.admin.PostManager.allStatuses": "<PERSON><PERSON><PERSON> du<PERSON>", "app.components.admin.PostManager.allTopics": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.anyAssignment": "<PERSON><PERSON><PERSON> bir yö<PERSON>", "app.components.admin.PostManager.assignedTo": "{assigneeName} adlı kişiye atandı", "app.components.admin.PostManager.assignedToMe": "<PERSON><PERSON> atandı", "app.components.admin.PostManager.assignee": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.authenticationError": "Bu katmanı getirmeye çalışırken bir kimlik doğrulama hatası oluştu. Lütfen URL'yi ve Esri API anahtarınızın bu katmana erişimi olup olmadığını kontrol edin.", "app.components.admin.PostManager.automatedStatusTooltipText": "Koşullar karşılandığında bu durum otomatik olarak güncellenir", "app.components.admin.PostManager.bodyTitle": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.components.admin.PostManager.cancel": "İptal", "app.components.admin.PostManager.cancel2": "İptal", "app.components.admin.PostManager.co-sponsors": "Eş sponsorlar", "app.components.admin.PostManager.comments": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, tepkiler ve oylar gibi bu girdi<PERSON>le ilişkili tüm verileri kaybedeceğiniz anlamına gelir. Bu işlem geri alınamaz.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Bu girdileri silmek istediğinizden emin misiniz?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "<PERSON><PERSON><PERSON> kaldır", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "<PERSON>u fi<PERSON>ri, oy aldığı bir aşamadan çıkarmaya çalışıyorsunuz. <PERSON><PERSON><PERSON>, bu oylar kaybolacaktır. Bu fikri bu aşamadan kaldırmak istediğinizden emin misiniz?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Bu fikirle ilişkili o<PERSON>r ka<PERSON>", "app.components.admin.PostManager.components.goToInputManager": "<PERSON><PERSON><PERSON>net<PERSON>sine git", "app.components.admin.PostManager.components.goToProposalManager": "<PERSON><PERSON><PERSON><PERSON> git", "app.components.admin.PostManager.contributionFormTitle": "Katkıyı düzenle", "app.components.admin.PostManager.cost": "Maliyet", "app.components.admin.PostManager.createInput": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.createInputsDescription": "Geçmiş bir projeden yeni bir girdi seti oluşturma", "app.components.admin.PostManager.currentLat": "<PERSON><PERSON><PERSON> or<PERSON>a", "app.components.admin.PostManager.currentLng": "Boylamı ortala", "app.components.admin.PostManager.currentZoomLevel": "Yak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.defaultEsriError": "Bu katmanı getirmeye çalışırken bir hata oluştu. Lütfen ağ bağlantınızı ve URL'nin doğru olup olmadığını kontrol edin.", "app.components.admin.PostManager.delete": "Sil", "app.components.admin.PostManager.deleteAllSelectedInputs": "{count} yayını sil", "app.components.admin.PostManager.deleteConfirmation": "Bu katmanı silmek istediğinizden emin misiniz?", "app.components.admin.PostManager.dislikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "<PERSON>je<PERSON><PERSON>", "app.components.admin.PostManager.editStatuses": "Durumları düzenleme", "app.components.admin.PostManager.editTags": "Etiketleri düzenle", "app.components.admin.PostManager.editedPostSave": "<PERSON><PERSON>", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Esri ArcGIS Online'dan veri içe aktarma bir eklenti özelliğidir. Kilidini açmak için GS yöneticinizle konuşun.", "app.components.admin.PostManager.esriSideError": "ArcGIS uygulamasında bir hata oluştu. Lütfen birkaç dakika bekleyin ve daha sonra tekrar deneyin.", "app.components.admin.PostManager.esriWebMap": "Esri <PERSON>tası", "app.components.admin.PostManager.exportAllInputs": "<PERSON>üm yayınları dışa aktar (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "<PERSON><PERSON><PERSON> yo<PERSON>ları dışa aktar (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Bu proje ile ilgili yorumları dışa aktar (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "<PERSON>u projedeki yayınları dışa aktar (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Seçilen yayınları dışa aktar (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Seçilen yayınlarla ilgili yorumları dışa aktar (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Oyları girdiye göre dışa aktar (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Oyları kullanıcıya göre dışa aktar (.xslx)", "app.components.admin.PostManager.exports": "Dışa Aktarılanlar", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Harita verilerini yalnızca GeoJSON katmanları olarak veya ArcGIS Online'dan içe aktararak yükleyebilirsiniz. Bir Özellik Katmanı eklemek istiyorsanız lütfen mevcut GeoJSON katmanlarını kaldırın.", "app.components.admin.PostManager.featureLayerTooltop": "Özellik Katmanı URL'sini ArcGIS Online'daki öğe sayfasının sağ tarafında bulabilirsiniz.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "İnsanların adınızı nasıl göreceğini seçin", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Bu durum değişikliğini açıklayın", "app.components.admin.PostManager.fileUploadError": "Bir veya daha fazla dosya yüklenemedi. Lütfen dosya boyutunu ve biçimini kontrol edin ve tekrar deneyin.", "app.components.admin.PostManager.formTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.generalApiError2": "Bu öğeyi getirmeye çalışırken bir hata oluştu. Lütfen URL'nin veya Portal Kimliğinin doğru olup olmadığını ve bu öğeye erişiminiz olup olmadığını kontrol edin.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Harita verilerini yalnızca GeoJSON katmanları olarak veya ArcGIS Online'dan içe aktararak yükleyebilirsiniz. Bir GeoJSON katmanı yüklemek istiyorsanız lütfen tüm ArcGIS verilerini kaldırın.", "app.components.admin.PostManager.goToDefaultMapView": "Varsayılan harita merkezine git", "app.components.admin.PostManager.hiddenFieldsLink": "g<PERSON><PERSON> al<PERSON>", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "İpucu: Typeform kullanıyorsanız, anketinize kimlerin yanıt verdiğinin kaydını tutmak için {hiddenFieldsLink} ekleyin.", "app.components.admin.PostManager.import2": "İthalat", "app.components.admin.PostManager.importError": "Seçilen dosya geçerli bir GeoJSON dosyası olmadığı için içe aktarılamadı", "app.components.admin.PostManager.importEsriFeatureLayer": "Esri Özellik Katmanını İçe Aktar", "app.components.admin.PostManager.importEsriWebMap": "Esri Web Haritasını İçe Aktar", "app.components.admin.PostManager.importInputs": "İthalat girdileri", "app.components.admin.PostManager.imported": "İthal", "app.components.admin.PostManager.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} {totalCount} {totalCount, plural, one {girişinden} other {girişleri}} içe aktarılmıştır. İçe aktarma hala devam ediyor, lütfen daha sonra tekrar kontrol edin.", "app.components.admin.PostManager.inputManagerHeader": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputs": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.inputsExportFileName": "input", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Yalnızca geri bildirim gerektiren yayınları göster", "app.components.admin.PostManager.issueFormTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>", "app.components.admin.PostManager.latestFeedbackMode": "<PERSON><PERSON><PERSON><PERSON><PERSON> olarak mevcut en son resmi bilgilendirmeyi kullan", "app.components.admin.PostManager.layerAdded": "Katman baş<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.likes": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Bu girdiyi mevcut projesinden uzaklaştırmak, atanmış aşamaları hakkındaki bilgileri kaybedecektir. <PERSON>am etmek istiyor musunuz?", "app.components.admin.PostManager.mapData": "<PERSON><PERSON> ve<PERSON>", "app.components.admin.PostManager.multipleInputs": "{ideaCount} yayın", "app.components.admin.PostManager.newFeedbackMode": "Bu değişikliği açıklamak için yeni bir bilgilendirme yazın", "app.components.admin.PostManager.noFilteredResults": "Seçtiğiniz filtrelerle herhangi bir sonuç bulunmadı", "app.components.admin.PostManager.noInputs": "<PERSON><PERSON><PERSON><PERSON> girdi yok", "app.components.admin.PostManager.noInputsDescription": "Kendi girdilerinizi ekleyebilir veya geçmiş bir katılım projesinden başlayabilirsiniz.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 girdiler} one {1 girdi} other {# girdiler}} seçilen proje ve fazdan içe aktarılacaktır. İçe aktarma arka planda çalışacak ve tamamlandığında girdiler girdi yöneticisinde görünecektir.", "app.components.admin.PostManager.noOne": "Atanmamış", "app.components.admin.PostManager.noProject": "<PERSON><PERSON> yok", "app.components.admin.PostManager.officialFeedbackModal.author": "<PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Adınızın nasıl görüneceğini seçin", "app.components.admin.PostManager.officialFeedbackModal.description": "Re<PERSON>i geri bildirim sağlamak sürecin şeffaf kalmasına yardımcı olur ve platforma olan güveni artırır.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "<PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "<PERSON><PERSON> bild<PERSON><PERSON> g<PERSON>", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "<PERSON><PERSON><PERSON> geri bildirim", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Durum değişikliğinin nedenini açıklayın", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "<PERSON><PERSON> bi<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.skip": "Bu seferlik atla.", "app.components.admin.PostManager.officialFeedbackModal.title": "Kararınızı açıklayın", "app.components.admin.PostManager.officialUpdateAuthor": "İnsanların adınızı nasıl göreceğini seçin", "app.components.admin.PostManager.officialUpdateBody": "Bu durum değişikliğini açıklayın", "app.components.admin.PostManager.offlinePicks": "Çevrimdışı seçimler", "app.components.admin.PostManager.offlineVotes": "Çevrimdışı oylar", "app.components.admin.PostManager.onlineVotes": "Çevrimiçi oylar", "app.components.admin.PostManager.optionFormTitle": "<PERSON>üzenleme se<PERSON>ği", "app.components.admin.PostManager.participants": "Katılımcılar", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Çev<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.pbItemCountTooltip": "<PERSON><PERSON><PERSON> diğer katılımcıların katılımcı bütçelerine kaç kez dahil edildiği", "app.components.admin.PostManager.petitionFormTitle": "Dilekçeyi düzenle", "app.components.admin.PostManager.postedIn": "Posted in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "<PERSON><PERSON>", "app.components.admin.PostManager.projectsTab": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.projectsTabTooltipContent": "Bir projeden diğerine taşımak için yayınları sürükleyip bırakabilirsiniz. Zaman çizelgesi projeleri için yayını belirli bir aşamaya eklemeniz gerekeceğini unutmayın.", "app.components.admin.PostManager.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.proposedBudgetTitle": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>e", "app.components.admin.PostManager.publication_date": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "app.components.admin.PostManager.questionFormTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "Filtreleri sıfırla", "app.components.admin.PostManager.resetInputFiltersDescription": "Tüm girdileri görmek için filtreleri sıfırlayın.", "app.components.admin.PostManager.saved": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.screeningTooltip": "Tarama mevcut planınıza dahil <PERSON>. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Tarama bu aşama için ka<PERSON>. Etkinleştirmek için aşama kurulumuna gidin", "app.components.admin.PostManager.selectAPhase": "Bir faz seçin", "app.components.admin.PostManager.selectAProject": "<PERSON><PERSON> proje seçin", "app.components.admin.PostManager.setAsDefaultMapView": "Mevcut merkez noktasını ve yakınlaştırma düzeyini harita varsayılanları olarak kaydedin", "app.components.admin.PostManager.startFromPastInputs": "Geçmiş girdilerden başlayın", "app.components.admin.PostManager.statusChangeGenericError": "<PERSON><PERSON> hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin veya destek ekibiyle iletişime geçin.", "app.components.admin.PostManager.statusChangeSave": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.statusesTab": "Durum", "app.components.admin.PostManager.statusesTabTooltipContent": "S<PERSON>rükle ve bırak yöntemini kullanarak bir yayının durumunu değiştirin. Orijinal yazar ve diğer katkıda bulunanlar değişen durumla ilgili bir bildirim alacaktır.", "app.components.admin.PostManager.submitApiError": "Formun gönderilmesinde bir sorun oluştu. Lütfen hata olup olmadığını kontrol edin ve tekrar deneyin.", "app.components.admin.PostManager.timelineTab": "Zaman Çizelgesi", "app.components.admin.PostManager.timelineTabTooltipText": "Yayınları farklı proje aşamalarına kopyalamak için sürükleyip bırakın.", "app.components.admin.PostManager.title": "Başlık", "app.components.admin.PostManager.topicsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "G<PERSON>rü<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "Bir seferde yalnızca bir Web Haritası ekleyebilirsiniz. Farklı bir tane içe aktarmak için mevcut olanı kaldırın.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Harita verilerini yalnızca GeoJSON katmanları olarak veya ArcGIS Online'dan içe aktararak yükleyebilirsiniz. Bir Web Haritası bağlamak istiyorsanız lütfen mevcut GeoJSON katmanlarını kaldırın.", "app.components.admin.PostManager.webMapTooltip": "Web Haritası portal kimliğini ArcGIS Online öğe sayfanızda, sağ tarafta bulabilirsiniz.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {Bir günden az} one {Bir gün} other {# gün}} sol", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "İptal", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON>, anket sonuçlarını silin", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "<PERSON>u geri alınamaz", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Anket sonuçlarını silin", "app.components.admin.ProjectEdit.survey.downloadResults2": "Anket sonuçlarını indirin", "app.components.admin.ReportExportMenu.FileName.fromFilter": "kimden", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grup", "app.components.admin.ReportExportMenu.FileName.projectFilter": "proje", "app.components.admin.ReportExportMenu.FileName.topicFilter": "et<PERSON>t", "app.components.admin.ReportExportMenu.FileName.untilFilter": "s<PERSON><PERSON>", "app.components.admin.ReportExportMenu.downloadPng": "PNG olarak indirin", "app.components.admin.ReportExportMenu.downloadSvg": "SVG olarak indirin", "app.components.admin.ReportExportMenu.downloadXlsx": "Excel'i indirin", "app.components.admin.SlugInput.regexError": "<PERSON><PERSON> kısa adında yalnızca küçük harf (a-z), r<PERSON><PERSON> (0-9) ve tire (-) bulunabilir. İlk ve son karakterler tire olamaz. Ardışık tire kullanımına (--) izin verilmez.", "app.components.admin.TerminologyConfig.saveButton": "<PERSON><PERSON>", "app.components.admin.commonGroundInputManager.title": "Başlık", "app.components.admin.seatSetSuccess.admin": "Yönetici", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON><PERSON> tamam", "app.components.admin.seatSetSuccess.close": "Ka<PERSON><PERSON>", "app.components.admin.seatSetSuccess.manager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.orderCompleted": "Sipariş tamamlandı", "app.components.admin.seatSetSuccess.reflectedMessage": "Planınızdaki değişiklikler bir sonraki fatura döneminize yansıtılacaktır.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} hakları seçilen kullanıcı(lar)a verilmiştir.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Tüm anket sonuçlarını silmek istediğinizden emin misiniz?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Bu katılım yöntemi beta aşamasındadır. Geri bildirim toplamak ve deneyimi iyileştirmek için kademeli olarak kullanıma sunuyoruz.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Bir belge hakkında geri bildirim toplamak özel bir özelliktir ve mevcut lisansınıza dahil de<PERSON>dir. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Katkı", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "<PERSON><PERSON><PERSON> sayı<PERSON>ı gereklidir", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Minimum oy sayısına ulaşmak için gereken gün sayısı", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Google Formlar için bir bağlantının nasıl yerleştirileceği hakkında daha fazla bilgi {googleFormsTooltipLink} bulunabilir.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "bu destek makalesinde", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Fikir", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Bir girdinin adı ne olmalıdır?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Konveio belgenizin bağlantısını buraya girin. Konveio'nun kurulumu hakkında daha fazla bilgi için {supportArticleLink} adresimizi okuyun.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "destek makalesi", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "<PERSON><PERSON>, mevcut planınıza dahil <PERSON>. Kilidi açmak için Kamu Başarı Yöneticinize veya yöneticinize ulaşın.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "<PERSON><PERSON><PERSON><PERSON> bütçe gereklidir", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Seçenek başına maksimum oy sayısı toplam oy sayısına eşit veya daha az olmalıdır", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Ma<PERSON><PERSON>um oy sayısı gereklidir", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Minimum bütçe maksimum bütçeden büyük olamaz", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Minimum bütçe gereklidir", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Minimum oy sayısı maksimum oy sayısından fazla olamaz", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Asgari bir oy sayısı gereklidir", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "<PERSON><PERSON><PERSON> ta<PERSON>hi", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>ç tarihi", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Seçenek", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "<PERSON><PERSON><PERSON> se<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Bir aşama oluşturduktan sonra Girdi yöneticisi sekmesinde oylama seçeneklerini yapılandırın.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "<PERSON><PERSON><PERSON> {optionsPageLink}adresinde yapılandırın.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Katılımcılar", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Dilekçe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Yöneticiler ve müdürler", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Belgeye açıklama ekleme:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} bu <PERSON><PERSON><PERSON><PERSON> ka<PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "İptal", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Yorum:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Ortak toprak fazı", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "<PERSON><PERSON><PERSON> silme", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, bu a<PERSON><PERSON><PERSON><PERSON> silin", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Bu aşamayı silmek istediğinizden emin misiniz?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Bu aşamayla ilgili tüm veriler silinecektir. Bu işlem geri alınamaz.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Belge açıklama aşaması", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Dış anket aşaması", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Fikir oluşturma aşaması", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Platform araştırması aşamasında", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Bitiş tarihi yok", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Anket aşaması", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Teklifler aşaması", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>React:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Etkin<PERSON> için kayıt yaptırdım:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Kayıtlı kullanıcılar", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b><PERSON><PERSON><PERSON> kat<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON><PERSON> kat<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Onaylanmış e-postaları olan kullanıcılar", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Gönüllülük aşaması", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>O<PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "<PERSON><PERSON><PERSON>ı", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "<PERSON><PERSON> katı<PERSON>?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "<PERSON><PERSON><PERSON><PERSON>, bir y<PERSON>netici tarafından incelenip onaylanana kadar gö<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>yacaktır. <PERSON><PERSON><PERSON>, gir<PERSON><PERSON>i inceledikten veya tepki verdikten sonra düzenleyemezler.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Bağlantıya sahip olan herkes taslak proje ile etkileşime geçebilir", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>je <PERSON>neticilerinin projeyi yayınlamasına olanak tanır.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "{name}ta<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>tı<PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Arşivlendi", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Taslak", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Açıklama düzenle", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Gruplar", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Çevrimdışı seçmenler", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "<PERSON><PERSON><PERSON>{inFolder, select, true { veya <PERSON>ör Yöneticileri} other {}} projeyi ya<PERSON>ı<PERSON>lir", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 katılımcı} other {{participantsCount} katılı<PERSON><PERSON><PERSON><PERSON>}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Yerleşik yöntemlere <PERSON> (örn. ha<PERSON><PERSON> anketler)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Bir projenin ta<PERSON>i", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Not: Anonim veya açık katılım izinlerinin etkinleştirilmesi, kullanıcıların birden fazla kez katılmasına izin vererek yanıltıcı veya eksik kullanıcı verilerine yol açabilir.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <b><PERSON><PERSON></b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Katılımcılar arasında", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Etkinlik kayıtları", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Go Vocal yönte<PERSON>iyle et<PERSON> giren kull<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "<PERSON><PERSON> be<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Proje hakemleri bilgilendirilmiştir.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Yayınlandı - Aktif", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Yayınlandı - Bitti", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "<PERSON><PERSON> bağlantısını yenileyin", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Proje <PERSON>zleme bağlantısını yeniden oluşturun. Bu, önceki bağlantıyı geçersiz kılacaktır.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Eski bağlantılar çalışmayı durduracaktır ancak istediğiniz zaman yeni bir bağlantı oluşturabilirsiniz.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Emin misiniz? Bu, mevcut bağlantıyı devre dışı bırakacaktır", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "İptal", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, bağlantıyı yenile", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Onay talep edin", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Projeyi yayınlayabilmeniz için önce bir yönetici{inFolder, select, true { veya Klasör Yöneticilerinden biri} other {}} tarafından onaylanmalıdır. Onay istemek için aşağıdaki düğmeyi tıklayın.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Paylaş", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Bağlantıyı kopyala", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "Bağlantı kopyalandı", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Özel bağlantıların paylaşılması mevcut planınıza dahil de<PERSON>. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "<PERSON><PERSON> proje<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON> var", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "G<PERSON>rü<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Değerlendirmeye alınacak asgari oy sayısı", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Minimum oy sayısı gereklidir", "app.components.app.containers.AdminPage.ProjectEdit.report": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "G<PERSON><PERSON><PERSON>in taranmasını gerektirir", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Zaman Çizelgesi", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Trafik", "app.components.formBuilder.cancelMethodChange1": "İptal", "app.components.formBuilder.changeMethodWarning1": "<PERSON><PERSON>nte<PERSON><PERSON>, önceki yöntem kullanılırken üretilen veya alınan herhangi bir girdi verisinin gizlenmesine yol açabilir.", "app.components.formBuilder.changingMethod1": "Yöntem değiştirme", "app.components.formBuilder.confirmMethodChange1": "<PERSON><PERSON>, devam edin", "app.components.formBuilder.copySurveyModal.cancel": "İptal", "app.components.formBuilder.copySurveyModal.description": "<PERSON><PERSON>, cevaplar olmadan tüm soruları ve mantığı kopyalayacaktır.", "app.components.formBuilder.copySurveyModal.duplicate": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Bu projede uygun bir aşama bulunamamıştır", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Seçili faz yok. Lütfen önce bir faz seçin.", "app.components.formBuilder.copySurveyModal.noProject": "<PERSON><PERSON> yok", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Seçili proje yok. Lütfen önce bir proje seçin.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Bu anketteki değişiklikleri zaten kaydettiniz. Başka bir anketi çoğaltırsanız, değişiklikler kaybolacaktır.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Anket aşaması", "app.components.formBuilder.copySurveyModal.title": "Çoğaltmak için bir anket seçin", "app.components.formBuilder.editWarningModal.addOrReorder": "<PERSON><PERSON> ekleme veya yeniden sı<PERSON>ama", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Yanıt verileriniz hatalı olabilir", "app.components.formBuilder.editWarningModal.changeQuestionText2": "<PERSON><PERSON>", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Bir yazım hatasını mı düzeltiyorsunuz? Yanıt verilerinizi etkilemeyecektir", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Bu soruyla bağlantılı yanıt verilerini kaybedersiniz", "app.components.formBuilder.editWarningModal.deteleAQuestion": "<PERSON><PERSON> silme", "app.components.formBuilder.editWarningModal.exportYouResponses2": "Yanıtlarınızı dışa aktarın.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Uyarı: <PERSON><PERSON><PERSON> verilerini sonsuza kadar kaybedebilirsiniz. <PERSON><PERSON> et<PERSON>en ö<PERSON>,", "app.components.formBuilder.editWarningModal.noCancel": "Hayır, iptal et", "app.components.formBuilder.editWarningModal.title4": "Canlı anketi düzenle", "app.components.formBuilder.editWarningModal.yesContinue": "<PERSON><PERSON>, devam edin", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "Bu anket için er<PERSON><PERSON> ha<PERSON> ayarları", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Anket formundaki demografik alanlar' etkinleştirildi. Anket formu görüntülendiğinde, yapılandırılmış demografik sorular anketin bitiminden hemen önce yeni bir sayfaya eklenecektir. Bu sorular {accessRightsSettingsLink}adresinden değiştirilebilir.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "<PERSON>u aşama için er<PERSON><PERSON>im ha<PERSON>r<PERSON> ayarları.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Anket katılımcılarının anket cevaplarını göndermek için kaydolmaları veya giriş yapmaları gerekmeyecektir, bu da mükerrer gönderimlerle sonuçlanabilir", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Kaydolma/log in adımını atlayarak, anket katılımcıları hakkında demografik bilgi toplamamayı kabul etmiş olursunuz, bu da veri analizi yeteneklerinizi etkileyebilir", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>kmesi altında \"Herkes\" için erişime izin verecek şekilde ayarlanmıştır.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "<PERSON><PERSON><PERSON>ek isterseniz, bunu {accessRightsSettingsLink}adresinden yapabilirsiniz.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Anket katılımcılarına kayıt/log in adımı aracılığıyla aşağıdaki demografik soruları soruyorsunuz.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Demografik bilgilerin toplanmasını kolaylaştırmak ve kullanıcı veritabanınıza entegrasyonunu sağlamak için, demografik soruları doğrudan kayıt/log-in sürecine dahil etmenizi tavsiye ederiz. Bunu yapmak için lütfen {accessRightsSettingsLink}adresini kullanın", "app.components.onboarding.askFollowPreferences": "Kullanıcılardan alanları veya konuları takip etmelerini isteyin", "app.components.onboarding.followHelperText": "<PERSON><PERSON>, kayıt sürecinde kullanıcıların aşağıda seçtiğiniz alanları veya konuları takip edebilecekleri bir adımı etkinleştirir", "app.components.onboarding.followPreferences": "Tercihleri takip edin", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} plan dahil<PERSON>e, {noOfAdditionalSeats} ek", "app.components.seatsWithinPlan.seatsWithinPlanText": "Plan dahilindeki k<PERSON>", "app.containers.Admin.Campaigns.campaignFrom": "<PERSON><PERSON>:", "app.containers.Admin.Campaigns.campaignTo": "<PERSON><PERSON>:", "app.containers.Admin.Campaigns.customEmails": "Özel e-postalar", "app.containers.Admin.Campaigns.customEmailsDescription": "Özel e-postalar gönderin ve istatistikleri kontrol edin.", "app.containers.Admin.Campaigns.noAccess": "Üzgünüz ancak e-postalar bölümüne erişiminiz yok", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Otomatik e-postalar", "app.containers.Admin.Insights.tabReports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.a11y_removeInvite": "<PERSON><PERSON><PERSON><PERSON> kaldır", "app.containers.Admin.Invitations.addToGroupLabel": "<PERSON>u kişileri belirli manuel kullanıcı gruplarına e<PERSON>", "app.containers.Admin.Invitations.adminLabel1": "Davetlilere yönetici hakları verin", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, davet ed<PERSON><PERSON><PERSON><PERSON>z kişiler tüm platform ayarlarınıza erişebilecektir.", "app.containers.Admin.Invitations.configureInvitations": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı<PERSON>", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Aramanızla eşleşen davetiye yok", "app.containers.Admin.Invitations.deleteInvite": "Sil", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Bu daveti silmek istediğinizden emin misiniz?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Bir davetiyeyi iptal ettiğinizde o kişiye yeniden davetiye gönderebilirsiniz.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. <PERSON><PERSON><PERSON><PERSON> indirin ve doldurun", "app.containers.Admin.Invitations.downloadTemplate": "Şablonu indirin", "app.containers.Admin.Invitations.email": "E-posta", "app.containers.Admin.Invitations.emailListLabel": "<PERSON><PERSON> etmek istediğiniz kişilerin e-posta adreslerini manuel o<PERSON>ak girin. Her adresi virgülle a<PERSON>ı<PERSON>ın.", "app.containers.Admin.Invitations.exportInvites": "<PERSON><PERSON><PERSON> davetiyeleri dışa aktar", "app.containers.Admin.Invitations.fileRequirements": "Önemli: Daveti<PERSON>lerin doğru şekilde gönderilebilmesi için içe aktarma şablonundan hiçbir sütun kaldırılamaz. Kullanılmayan sütunları boş bırakın.", "app.containers.Admin.Invitations.filetypeError": "Yanlış dosya türü. Yalnızca XLSX dosyaları desteklenir.", "app.containers.Admin.Invitations.groupsPlaceholder": "Hiçbir grup seçilmedi", "app.containers.Admin.Invitations.helmetDescription": "Platforma kullanıcı davet edin", "app.containers.Admin.Invitations.helmetTitle": "Yönetici davetiye panosu", "app.containers.Admin.Invitations.importOptionsInfo": "Bu seçenekler yalnızca Excel dosyasında tanımlanmadıklarında dikkate alınacaktır.\n      Daha fazla bilgi için lütfen {supportPageLink} adresini ziyaret edin.", "app.containers.Admin.Invitations.importTab": "E-posta adreslerini içe aktar", "app.containers.Admin.Invitations.invitationExpirationWarning": "Davetiyelerin süresinin 30 gün sonra dolduğunu unutmayın. Bu süreden sonra da yeniden gönderebilirsiniz.", "app.containers.Admin.Invitations.invitationOptions": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.invitationSubtitle": "İnsanları istediğiniz zaman platforma davet edebilirsiniz. Logonuzun bulunduğu tarafsız bir davetiye e-postası alırlar ve bu e-postada platforma kaydolmaları istenir.", "app.containers.Admin.Invitations.invitePeople": "Kişileri e-posta yoluyla davet edin", "app.containers.Admin.Invitations.inviteStatus": "Durum", "app.containers.Admin.Invitations.inviteStatusAccepted": "Kabul Edildi", "app.containers.Admin.Invitations.inviteStatusPending": "Beklemede", "app.containers.Admin.Invitations.inviteTextLabel": "İsterseniz davetiye e-postasına eklenecek bir mesaj yazabilirsiniz.", "app.containers.Admin.Invitations.invitedSince": "<PERSON><PERSON> edildi", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.moderatorLabel": "Bu kişilere proje yönetimi yet<PERSON>leri verin", "app.containers.Admin.Invitations.moderatorLabelTooltip": "<PERSON><PERSON> se<PERSON><PERSON><PERSON><PERSON>, davet edilen kişilere seçilen projeler için proje yöneticisi hakları atanır. Proje yöneticisi hakları hakkında daha fazla bilgi için {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "buraya", "app.containers.Admin.Invitations.name": "Ad", "app.containers.Admin.Invitations.processing": "Davetiyeler gönderiliyor. Lütfen bekleyin...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Hiçbir proje seçilmedi", "app.containers.Admin.Invitations.save": "Davetiyeler<PERSON>", "app.containers.Admin.Invitations.saveErrorMessage": "Bir veya daha fazla hata oluştu ve davetiyeler gönderilmedi. Lütfen aşağıda listelenen hataları düzeltin ve tekrar deneyin.", "app.containers.Admin.Invitations.saveSuccess": "Başarılı!", "app.containers.Admin.Invitations.saveSuccessMessage": "Davetiye başarıyla gönderildi.", "app.containers.Admin.Invitations.supportPage": "destek sayfası", "app.containers.Admin.Invitations.supportPageLinkText": "Destek sayfasını ziyaret edin", "app.containers.Admin.Invitations.tabAllInvitations": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.tabInviteUsers": "Kullanıcıları davet edin", "app.containers.Admin.Invitations.textTab": "E-posta adreslerini manuel o<PERSON> girin", "app.containers.Admin.Invitations.unknownError": "<PERSON>ir sorun olu<PERSON>. Lütfen daha sonra tekrar deneyin.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Tamamlanmış şablon dosyanızı yükleyin", "app.containers.Admin.Invitations.visitSupportPage": "İçe aktarma şablonundaki desteklenen tüm sütunlar hakkında daha fazla bilgi istiyorsanız {supportPageLink}.", "app.containers.Admin.Moderation.all": "Tümü", "app.containers.Admin.Moderation.belongsTo": "Şuna ait:", "app.containers.Admin.Moderation.collapse": "daha azını göster", "app.containers.Admin.Moderation.comment": "<PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionCancelButton": "İptal", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.confirmCommentDeletion": "Bu yorumu silmek istediğinizden emin misiniz? Bu kalıcıdır ve geri alınamaz.", "app.containers.Admin.Moderation.content": "İçerik", "app.containers.Admin.Moderation.date": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.deleteComment": "<PERSON><PERSON><PERSON> sil", "app.containers.Admin.Moderation.goToComment": "Bu yorumu yeni sekmede aç", "app.containers.Admin.Moderation.goToPost": "Bu yayını yeni sekmede aç", "app.containers.Admin.Moderation.goToProposal": "Bu öneriyi yeni sekmede aç", "app.containers.Admin.Moderation.markFlagsError": "<PERSON><PERSON><PERSON><PERSON> işaretlenemedi. <PERSON><PERSON><PERSON>.", "app.containers.Admin.Moderation.markNotSeen": "{selectedItemsCount, plural, one {# öğeyi} other {# öğeyi}} g<PERSON>rülmedi olarak işaretle", "app.containers.Admin.Moderation.markSeen": "{selectedItemsCount, plural, one {# öğeyi} other {# öğeyi}} g<PERSON><PERSON><PERSON><PERSON><PERSON> olarak işaretle", "app.containers.Admin.Moderation.moderationsTooltip": "<PERSON><PERSON> <PERSON><PERSON>, fi<PERSON><PERSON>r ve yorumlar da dahil olmak üzere platformunuzda yayımlanan tüm yeni yayınları hızlı bir şekilde kontrol etmenizi sağlar. Diğer kişilerin nelerin hala işlenmesi gerektiğini anlayabilmesi için yayınları 'görüldü' olarak işaretleyebilirsiniz.", "app.containers.Admin.Moderation.noUnviewedItems": "Görülmeyen öğe yok", "app.containers.Admin.Moderation.noViewedItems": "Görülen öğe yok", "app.containers.Admin.Moderation.pageTitle1": "Besleme", "app.containers.Admin.Moderation.post": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.profanityBlockerSetting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "En sık bildirilen saldırgan kelimeleri içeren yayınları engelleyin.", "app.containers.Admin.Moderation.project": "<PERSON><PERSON>", "app.containers.Admin.Moderation.read": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON> fazla bilgi", "app.containers.Admin.Moderation.removeFlagsError": "Uyarılar kaldırılamadı. <PERSON><PERSON><PERSON>.", "app.containers.Admin.Moderation.rowsPerPage": "<PERSON><PERSON> ba<PERSON>ına satır sayı<PERSON>ı", "app.containers.Admin.Moderation.settings": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.settingsSavingError": "Kaydedilemedi. Ayarı değiştirmeyi tekrar deneyin.", "app.containers.Admin.Moderation.show": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.status": "Durum", "app.containers.Admin.Moderation.successfulUpdateSettings": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>.", "app.containers.Admin.Moderation.type": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.unread": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Bu sayfa aşağıdaki bölümlerden oluşur. Bunları açıp kapatabilir ve gerektiği gibi düzenleyebilirsiniz.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Sayfayı gör<PERSON>üle", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Sayfa üzerinde gösterilmemiştir", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Sayfa üzerinde gösterilmiştir", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Ekler", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Sayfadan indirilebilecek dosyaları (en fazla 50 MB) ekleyin.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Alt bilgi bölümü", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Sayfanın altındaki özelleştirilebilir bölüme kendi içeriğinizi ekleyin.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Etkin<PERSON> listesi", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Projelerle ilgili etkinlikleri gösterin.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero bandı", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Sayfa banner resmini ve metnini özelleştirin.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Sayfa ayarlarınıza göre projeleri gösterin. Gösterilecek projeleri önizleyebilirsiniz.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Üst bilgi bö<PERSON>ü<PERSON>ü", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Sayfanın üst kısmındaki özelleştirilebilir bölüme kendi içeriğinizi ekleyin.", "app.containers.Admin.PagesAndMenu.addButton": "<PERSON><PERSON>inme <PERSON>uğ<PERSON>", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "<PERSON><PERSON><PERSON>me <PERSON> ad", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Bu sayfayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "<PERSON><PERSON><PERSON> diller i<PERSON>in bir başl<PERSON>k <PERSON>ğlayın", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Kullanılabil<PERSON>", "app.containers.Admin.PagesAndMenu.components.savePage": "Sayfayı kaydet", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Ekler (maksimum 50 MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Başarılı", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "İçerik", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Dosyalar 50Mb'den büyük olmamalıdır. Eklenen dosyalar bu sayfanın altında gösterilecektir", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>r", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Ekler | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Ekler", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "<PERSON><PERSON><PERSON> ka<PERSON> ve etkinleştirin", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "<PERSON><PERSON><PERSON> diller için içerik sağlayın", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Başarılı", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "İçerik", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Alt bilgi bölümü kaydedilemedi", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Alt bilgi bölümü kaydedildi", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Alt bilgi bölümü", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Alt bilgi bölümünü kaydedin ve etkinleştirin", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Alt bilgi bölümünü kaydet", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Özel sayfalar oluşturmak mevcut lisansınıza dahil <PERSON>. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Lütfen en az bir etiket seçin", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Başarılı", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Bölgeye göre", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Etiket(ler)e göre", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Projeleri etikete veya alana göre görüntülemek mevcut lisansınızın bir parçası değildir. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "İçerik", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "<PERSON>zel sayfayı düzenleme", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Bağlantılı projeler", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Sayfada hangi projelerin ve ilgili etkinliklerin görüntülenebileceğini seçin.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Sayfa başarıyla oluşturuldu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "<PERSON>zel say<PERSON> ka<PERSON>edildi", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Gezinti çubuğundaki başlık", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Özel sayfa oluştur | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Özel sayfa oluşturma", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Hiç<PERSON>i", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "<PERSON><PERSON> a<PERSON>ları", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Özel sayfayı kaydet", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Lütfen bir alan <PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "<PERSON><PERSON><PERSON><PERSON> alan", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Seçilen etiketler", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "<PERSON><PERSON> kısa adında yalnızca küçük harf (a-z), r<PERSON><PERSON> (0-9) ve tire (-) bulunabilir. İlk ve son karakterler tire olamaz. Ardışık tire kullanımına (--) izin verilmez.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Bir slug girmelisiniz", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Başlık", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Her dilde bir ba<PERSON><PERSON><PERSON>k girin", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "<PERSON><PERSON> ba<PERSON>l<PERSON>k girin", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "<PERSON>zel sayfayı gör<PERSON><PERSON>ü<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "<PERSON>zel sayfayı düzenle | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "<PERSON><PERSON> içeriği", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.automatedEmailsLinkText": "otomatik e-postalar", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Tek aşamalı proje<PERSON>, bitiş tarihi bo<PERSON><PERSON> ve açıklama doldurulmamışsa, proje sayfasında bir zaman çizelgesi görüntülenmeyecektir.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "{pageSettingsLink}adresinize göre mevcut proje yok.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Bu projenin etiket veya alan filtresi yoktur, bu nedenle hiçbir proje görüntülenmeyecektir.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "<PERSON><PERSON><PERSON> listesi | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "<PERSON><PERSON>ı", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "A<PERSON>a<PERSON><PERSON><PERSON><PERSON> projeler {pageSettingsLink}adresinize bağlı olarak bu sayfada gösterilecektir.", "app.containers.Admin.PagesAndMenu.defaultTag": "VARSAYILAN", "app.containers.Admin.PagesAndMenu.deleteButton": "Sil", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Başarılı", "app.containers.Admin.PagesAndMenu.heroBannerError": "Hero bandı kaydedilemedi", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Hero bandı kaydedildi", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Hero bandını kaydet", "app.containers.Admin.PagesAndMenu.homeTitle": "<PERSON>", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "En az bir dil için içerik sağlayın", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Gezinme çubuğuna en fazla 5 öğe ekleyebilirsiniz", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "<PERSON><PERSON><PERSON> ve <PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "<PERSON><PERSON><PERSON>me <PERSON>n kaldır", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "<PERSON><PERSON><PERSON> kayd<PERSON>in ve etkinleştirin", "app.containers.Admin.PagesAndMenu.title": "<PERSON><PERSON><PERSON> ve <PERSON>", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Başarılı", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "İçerik", "app.containers.Admin.PagesAndMenu.topInfoError": "Üst bilgi bölü<PERSON>ü kaydedilemedi", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Üst bilgi bölümü kaydedildi", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Üst bilgi b<PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Üst bilgi bö<PERSON>ü<PERSON>ü", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Üst bilgi bölümünü kaydedin ve etkinleştirin", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Üst bilgi bölümünü kaydet", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Yaş", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Topluluk", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Yönetici özeti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Üst düzey kapsayıcılık göstergeleri", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>, daha kapsayıcı ve temsili bir katılım platformunu teşvik etmeye yönelik ilerlememizi vurgulayan kapsayıcılık göstergelerini özetlemektedir.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "Katılımcılar", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Üst düzey katılım göstergeleri", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>, seçilen zaman aralığı için temel katılım göstergelerini özetlemekte ve katılım eğilimlerine ve performans ölçütlerine genel bir bakış sağlamaktadır.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projeler", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Platform raporu", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, se<PERSON><PERSON>n zaman aralığıyla örtüşen kamuya açık projelere, bu projelerde en çok kullanılan yöntemlere ve toplam katılım miktarına ilişkin metriklere genel bir bakış sunmaktadır.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "<PERSON><PERSON><PERSON><PERSON> zaman çizelgesi", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Engellenen kullanıcılar", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Engellenen kullanıcıları yönetin.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Grubu sil", "app.containers.Admin.Users.GroupsHeader.editGroup": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.admins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.allUsers": "Kayıtlı kullanıcılar", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Gruplar", "app.containers.Admin.Users.GroupsPanel.managers": "<PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Atanmış öğeler", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Platforma kaydolan tüm kişi ve kuruluşları genel hatlarıyla görün. Seçilen kullanıcıları Manuel gruplara veya Akıllı gruplara ekleyin.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "<PERSON><PERSON> be<PERSON>", "app.containers.Admin.Users.admin": "Yönetici", "app.containers.Admin.Users.assign": "<PERSON><PERSON>", "app.containers.Admin.Users.assignedItems": "{name}i<PERSON><PERSON>", "app.containers.Admin.Users.buyOneAditionalSeat": "Bir ek koltuk satın alın", "app.containers.Admin.Users.changeUserRights": "Kullanıcı haklarını değiştirme", "app.containers.Admin.Users.confirm": "<PERSON>aylayın", "app.containers.Admin.Users.confirmAdminQuestion": "{name} platformuna yönetici hakları vermek istediğinizden emin misiniz?", "app.containers.Admin.Users.confirmNormalUserQuestion": "{name} adresini normal bir kullanıcı olarak ayarlamak istediğinizden emin misiniz?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "{name} adresini normal bir kullanıcı olarak ayarlamak istediğinizden emin misiniz? Lütfen onaylandığında atandıkları tüm proje ve klasörlerin yönetici haklarını kaybedeceklerini unutmayın.", "app.containers.Admin.Users.deleteUser": "Kullanıcıyı sil", "app.containers.Admin.Users.email": "E-posta", "app.containers.Admin.Users.folder": "Klasör", "app.containers.Admin.Users.folderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.helmetDescription": "Yöneticide kullanıcı listesi", "app.containers.Admin.Users.helmetTitle": "Yönetici - kullanıcılar panosu", "app.containers.Admin.Users.inviteUsers": "Kullanıcıları davet edin", "app.containers.Admin.Users.joined": "Katıldım", "app.containers.Admin.Users.lastActive": "Son aktif", "app.containers.Admin.Users.name": "Ad", "app.containers.Admin.Users.noAssignedItems": "Atanmış öğe yok", "app.containers.Admin.Users.options": "Seçenekler", "app.containers.Admin.Users.permissionToBuy": "{name} yönetici haklarını vermek için 1 ek koltuk satın almanız gerekir.", "app.containers.Admin.Users.platformAdmin": "Platform yöneticisi", "app.containers.Admin.Users.projectManager": "<PERSON><PERSON>", "app.containers.Admin.Users.reachedLimitMessage": "Planınızdaki koltuk sınırına ulaştınız, {name} için 1 ek koltuk eklenecektir.", "app.containers.Admin.Users.registeredUser": "Kayıtlı kullanıcı", "app.containers.Admin.Users.remove": "Kaldırmak", "app.containers.Admin.Users.removeModeratorFrom": "Kullanıcı bu projenin ait olduğu klasörü yönetiyor. Bunun yerine \"{folderTitle}\" adresinden atamayı kaldırın.", "app.containers.Admin.Users.role": "Rol", "app.containers.Admin.Users.seeProfile": "<PERSON><PERSON>", "app.containers.Admin.Users.selectPublications": "Projeleri veya klasörleri seçme", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Aramak i<PERSON> ya<PERSON>ın", "app.containers.Admin.Users.setAsAdmin": "Yönetici olar<PERSON> a<PERSON>", "app.containers.Admin.Users.setAsNormalUser": "Normal kullanıcı olarak ayarla", "app.containers.Admin.Users.setAsProjectModerator": "<PERSON><PERSON> y<PERSON> olarak ayarlayın", "app.containers.Admin.Users.setUserAsProjectModerator": "{name} adresini proje yöneticisi olarak atayın", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON><PERSON> tamam", "app.containers.Admin.Users.userBlockModal.blockAction": "Kullanıcıyı engelle", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Bu kullanıcının içeriği bu işlemle kaldırılmayacaktır. Gerekirse içeriklerini denetlemeyi unutmayın.", "app.containers.Admin.Users.userBlockModal.blocked": "<PERSON>gel<PERSON>di", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "<PERSON><PERSON> kullanıcı {from}adresinden beri engellenmiştir. Yasak {to}adresine kadar sürmektedir.", "app.containers.Admin.Users.userBlockModal.cancel": "İptal", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "{name}en<PERSON><PERSON> ka<PERSON>ı<PERSON> istediğinizden emin misiniz?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} {date}adresine kadar en<PERSON>.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 gün} other {{numberOfDays} gün}}", "app.containers.Admin.Users.userBlockModal.header": "Kullanıcıyı engelle", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Sebep", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Bu durum engellenen kullanıcıya bildirilecektir.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Seçilen kullanıcı {daysBlocked}platformuna giriş yapamayacaktır. <PERSON>unu geri <PERSON> isters<PERSON>z, engellenen kullanıcılar listesinden engellemeyi kaldırabilirsiniz.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Engellem<PERSON>i <PERSON>", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, bu kull<PERSON><PERSON><PERSON>ı<PERSON>ın engelini kaldırmak istiyorum", "app.containers.Admin.Users.userDeletionConfirmation": "Bu kullanıcı kalıcı olarak kaldırılsın mı?", "app.containers.Admin.Users.userDeletionFailed": "Bu kullanıcı silinirken bir hata oluş<PERSON>, lütfen tekrar deneyin.", "app.containers.Admin.Users.userDeletionProposalVotes": "Bu işlem aynı zamanda bu kullanıcının hala oylamaya açık olan teklifler üzerindeki tüm oylarını da silecektir.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Kullanıcı içgörüleri", "app.containers.Admin.Users.youCantDeleteYourself": "Kullanıcı yönetici sayfası üzerinden kendi hesabınızı silemezsiniz", "app.containers.Admin.Users.youCantUnadminYourself": "Yönetici rolünüzden şu anda vazgeçemezsiniz", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Topluluk gözlemcisi", "app.containers.Admin.communityMonitor.healthScore": "Sağlık Puanı", "app.containers.Admin.communityMonitor.healthScoreDescription": "<PERSON><PERSON> puan, seç<PERSON>n dönem için katılımcılar tarafından yanıtlanan tüm duygu ölçeği sorularının ortalamasıdır.", "app.containers.Admin.communityMonitor.lastQuarter": "son <PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.liveMonitor": "Canlı monitör", "app.containers.Admin.communityMonitor.noResults": "<PERSON>u dönem için sonuç yok.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Anket yanıtı yok", "app.containers.Admin.communityMonitor.participants": "Katılımcılar", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Toplum İzleme Anketi başvuruları kabul ediyor.", "app.containers.Admin.communityMonitor.settings.accessRights2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Bir kullanıcı bir etkinliğe katıldıktan, bir oy verdikten veya bir anket gönderdikten sonra bir proje sayfasına geri döndükten sonra.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Topluluk İzleyicisi Yöneticileri tüm Topluluk İzleyicisi ayarlarına ve verilerine erişebilir ve bunları yönetebilir.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Topluluk İzleme Yöneticileri", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>zleyicisi anketini ve izinlerini düzenleyebilir, yanıt verilerini görebilir ve raporlar oluşturabilir.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Varsayılan frekans değeri %100'dür.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Açılır pencere sıklığı (0 ila 100)", "app.containers.Admin.communityMonitor.settings.management2": "Y<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.popup": "Popup", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Kullanıcılara periyodik olarak Topluluk Gözlemcisi Anketini tamamlamaları için teşvik eden bir açılır pencere gösterilir. Aşağıda belirtilen koşullar karşılandığında açılır pencereyi rastgele görecek kullanıcıların yüzdesini belirleyen sıklığı ayarlayabilirsiniz.", "app.containers.Admin.communityMonitor.settings.popupSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere a<PERSON>ları", "app.containers.Admin.communityMonitor.settings.preview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Kullanıcı anketi son 3 ay içinde doldurmamıştır.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Kullanıcı açılır pencereyi önceki 3 ay içinde görmemiştir.", "app.containers.Admin.communityMonitor.settings.save": "<PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.saved": "Kurtarıldı", "app.containers.Admin.communityMonitor.settings.settings": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.survey2": "Anket", "app.containers.Admin.communityMonitor.settings.surveySettings3": "<PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Ana <PERSON> veya bir Özel <PERSON> yüklendiğinde.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "T<PERSON>m kullanıcı verilerini anonimleştirin", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Kullanıcılardan gelen tüm anket girdileri kaydedilmeden önce anonimleştirilecektir", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Kullanıcıların yine de '<PERSON><PERSON><PERSON><PERSON>' kapsamındaki katılım gerekliliklerine uymaları gerekecektir. Kullanıcı profili verileri anket veri aktarımında mevcut olmayacaktır.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere hangi koşullar altında kullanıcılara görünebilir?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "<PERSON><PERSON><PERSON><PERSON><PERSON> kimler?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Toplam anket yanıtları", "app.containers.Admin.communityMonitor.upsell.aiSummary": "Yapay zeka özeti", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Topluluk İzleyicisini Etkinleştir", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Bu özellik mevcut planınıza dahil de<PERSON>dir. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Sağlık puanı", "app.containers.Admin.communityMonitor.upsell.learnMore": "Daha fazla bilgi edinin", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Zaman içinde puan", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Community Monitor, sa<PERSON><PERSON><PERSON>, hizmetlerden memnuniyetini ve toplum yaşamını sürekli olarak takip ederek bir adım önde olmanıza yardımcı olur.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Net puanlar, gü<PERSON><PERSON>ü alıntılar ve meslektaşlarınızla veya seçilmiş yetkililerle paylaşabileceğiniz üç aylık bir rapor alın.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Zaman içinde gel<PERSON>en, okun<PERSON><PERSON> kola<PERSON> skorlar", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "AI tarafından özetlenen önemli sakin alıntıları", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Şehrinizin bağlamına göre uyarlanmış sorular", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "<PERSON>da rastgele işe alınan sa<PERSON>", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Üç aylık PDF raporları, paylaşıma hazır", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "<PERSON><PERSON>lar büyümeden önce topluluğunuzun nasıl hiss<PERSON>ğini anlayın", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Saymak", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Masaüstü veya <PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobil", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "<PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "C<PERSON>az T<PERSON>i", "app.containers.Admin.earlyAccessLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.earlyAccessLabelExplanation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> bulunan yeni yayınlanmı<PERSON> bir özelliktir.", "app.containers.Admin.emails.addCampaign": "E-posta oluşturun", "app.containers.Admin.emails.addCampaignTitle": "Yeni bir e-posta oluşturun", "app.containers.Admin.emails.allParticipantsInProject": "<PERSON><PERSON><PERSON><PERSON> tüm katılımcı<PERSON>", "app.containers.Admin.emails.allUsers": "Kayıtlı kullanıcılar", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Otomatik e-postalar otomatik olarak gönderilir ve bir kullanıcının eylemleri tarafından tetiklenir. Bunlardan bazılarını platformunuzun tüm kullanıcıları için kapatabilirsiniz. Diğer otomatik e-postalar kapatılamaz çünkü platformunuzun düzgün çalışması için gereklidirler.", "app.containers.Admin.emails.automatedEmails": "Otomatik e-postalar", "app.containers.Admin.emails.automatedEmailsDigest": "E-posta yalnızca içerik varsa gönderilecektir", "app.containers.Admin.emails.automatedEmailsRecipients": "Bu e-postayı alacak kullanıcılar", "app.containers.Admin.emails.automatedEmailsTriggers": "Bu e-postayı tetikleyen olay", "app.containers.Admin.emails.changeRecipientsButton": "Alıcıları değiştir", "app.containers.Admin.emails.clickOnButtonForExamples": "Destek sayfamızdaki bu e-posta örneklerini kontrol etmek için aşağıdaki düğmeye tıklayın.", "app.containers.Admin.emails.confirmSendHeader": "Tüm kullanıcılara e-posta gönderilsin mi?", "app.containers.Admin.emails.deleteButtonLabel": "Sil", "app.containers.Admin.emails.draft": "Taslak", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "Kampanyayı düzenleyin", "app.containers.Admin.emails.editDisabledTooltip2": "Çok yakında: Bu e-posta şu anda düzenlenemiyor.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_intro_multiloc": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "Başlık", "app.containers.Admin.emails.emptyCampaignsDescription": "Katılımcılarınıza e-posta göndererek onlarla kolayca bağlantı kurun. <PERSON>inle iletişime geçeceğinizi seçin ve etkileşiminizi takip edin.", "app.containers.Admin.emails.emptyCampaignsHeader": "İlk e-postanızı gönderin", "app.containers.Admin.emails.failed": "Başarısız", "app.containers.Admin.emails.fieldBody": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Bir e-posta mesajı girin", "app.containers.Admin.emails.fieldReplyTo": "Yanıtlar şuraya gitmelidir", "app.containers.Admin.emails.fieldReplyToEmailError": "<EMAIL> gibi doğru bir biçimle e-posta adresi girin", "app.containers.Admin.emails.fieldReplyToError": "Bir e-posta adresi girin", "app.containers.Admin.emails.fieldReplyToTooltip": "E-postalarınıza yanıtların nereye gönderileceğini seçebilirsiniz.", "app.containers.Admin.emails.fieldSender": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldSenderError": "E-posta i<PERSON>in gö<PERSON>en bilgisi girin", "app.containers.Admin.emails.fieldSenderTooltip": "Alıcıların e-postayı gönderen olarak kimi göreceğine karar verebilirsiniz.", "app.containers.Admin.emails.fieldSubject": "<PERSON>-<PERSON><PERSON>", "app.containers.Admin.emails.fieldSubjectError": "Bir e-posta konusu girin", "app.containers.Admin.emails.fieldSubjectTooltip": "<PERSON><PERSON>, e-postanın konu satırında ve kullanıcının gelen kutusu genel görünümünde gösterilecektir. <PERSON><PERSON> anlaşılır ve ilgi çekici olmalıdır.", "app.containers.Admin.emails.fieldTo": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldToTooltip": "E-postanızı alacak kullanıcı gruplarını seçebilirsiniz", "app.containers.Admin.emails.formSave": "Taslak olarak kaydet", "app.containers.Admin.emails.from": "<PERSON><PERSON>?", "app.containers.Admin.emails.groups": "Gruplar", "app.containers.Admin.emails.helmetDescription": "Kullanıcı gruplarına manuel e-postalar gönderin ve otomatik kampanyalar etkinleştirin", "app.containers.Admin.emails.nameVariablesInfo2": "{firstName} {lastName}değişkenlerini kullanarak vatandaşlarla doğrudan konuşabilirsiniz. Örneğin \"Sayın {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "E-posta adresinize bir e-posta önizlemesi gönderildi", "app.containers.Admin.emails.previewTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.regionMultilocError": "Lütfen tüm diller i<PERSON>in bir de<PERSON> girin", "app.containers.Admin.emails.seeEmailHereText": "Bu tür bir e-posta gönderilir gönderilmez buradan kontrol edebileceksiniz.", "app.containers.Admin.emails.send": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sendNowButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sendTestEmailButton": "Bana bir deneme e-postası gönder", "app.containers.Admin.emails.sendTestEmailTooltip": "Bu bağlantıya tıkladığınızda, yaln<PERSON>zca sizin e-posta adresinize bir deneme e-postası gönderilecektir. Bu, e-postanın nasıl görüneceğini kontrol etmenizi sağlar.", "app.containers.Admin.emails.senderRecipients": "Gönderici ve alıcılar", "app.containers.Admin.emails.sending": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "Bunlar kullanıcılara gönderilen e-postalardır", "app.containers.Admin.emails.subject": "<PERSON><PERSON>:", "app.containers.Admin.emails.supportButtonLabel": "Destek sayfamızdaki örneklere bakın", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "<PERSON><PERSON>?", "app.containers.Admin.emails.toAllUsers": "Bu e-postayı tüm kayıtlı kullanıcılara göndermek istiyor musunuz?", "app.containers.Admin.emails.viewExample": "G<PERSON>rü<PERSON><PERSON><PERSON>", "app.containers.Admin.ideas.import": "İçe aktar", "app.containers.Admin.inspirationHub.AllProjects": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Topluluk gözlemcisi", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Belge açıklaması", "app.containers.Admin.inspirationHub.ExternalSurvey": "Dış anket", "app.containers.Admin.inspirationHub.Filters.Country": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Method": "Yöntem", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Highlighted": "Vurgulandı", "app.containers.Admin.inspirationHub.Ideation": "Düşünce", "app.containers.Admin.inspirationHub.Information": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Sabitlenmiş projeleri görmek için lütfen bir ülke seçin", "app.containers.Admin.inspirationHub.PinnedProjects.country": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Bu ülke için sabitlenmiş proje bulunamadı. <PERSON><PERSON><PERSON> ülkeler için sabitlenmiş projeleri görmek için ülkeyi değiştirin", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Daha fazla sabitlenmiş proje görmek için ülkeyi değiştirin", "app.containers.Admin.inspirationHub.Poll": "Anket", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Açık uçlu", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Daha fazlasını okuyun...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Faz {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "Projenizin ilham merkezine dahil edil<PERSON><PERSON> is<PERSON>, GovSuccess yöneticinize ulaşın.", "app.containers.Admin.inspirationHub.Proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Katılımcılar (en düşük ilk)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Katılımcılar (en yüksek ilk)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Başlangıç tarihi (en eski ilk)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Başlangıç tarihi (en yeni ilk)", "app.containers.Admin.inspirationHub.Survey": "Anket", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Dünyanın dört bir yanındaki en iyi projelerin seçilmiş listesi.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Uygulayıcı arkadaşlarınızla konuşun ve onlardan bir şeyler öğrenin.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>üğ<PERSON>ne ve ülkeye göre filtreleyin.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "<PERSON><PERSON><PERSON> Merkezini Etkinleştirin", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Bu özellik mevcut planınıza dahil de<PERSON>dir. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Daha fazla bilgi edinin", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "<PERSON><PERSON><PERSON>, si<PERSON> dünyanın dört bir yanındaki Go Vocal platformlarındaki olağanüstü katılım projelerinden oluşan bir akışa bağlar. Diğer şehirlerin nasıl başarılı projeler yürüttüğünü öğrenin ve diğer uygulayıcılarla konuşun.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Öncü demokrasi uygulayıcılarından oluşan bir ağa katılın", "app.containers.Admin.inspirationHub.Volunteering": "Gönüllülük", "app.containers.Admin.inspirationHub.Voting": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.commonGround": "Ortak zemin", "app.containers.Admin.inspirationHub.filters": "filtreler", "app.containers.Admin.inspirationHub.resetFilters": "Filtreleri sıfırla", "app.containers.Admin.inspirationHub.seemsLike": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> göre başka proje yok. {filters}ad<PERSON><PERSON> den<PERSON>.", "app.containers.Admin.messaging.automated.editModalTitle": "Kampanya alanlarını düzenleme", "app.containers.Admin.messaging.automated.variablesToolTip": "Mesajınızda aşağıdaki değişkenleri kullanabilirsiniz:", "app.containers.Admin.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.messaging.newProjectPhaseModal.alternatively": "<PERSON><PERSON><PERSON><PERSON>, her bir a<PERSON><PERSON><PERSON>n ayarlarından belirli aşamalar için bu e-posta kampanyasını devre dışı bırakabilirsiniz.", "app.containers.Admin.messaging.newProjectPhaseModal.cancel": "İptal", "app.containers.Admin.messaging.newProjectPhaseModal.disabledMessage1": "Bu aynı zamanda mevcut tüm proje aşamaları için {emailCampaignName} e-posta kampanyasını devre dışı bırakacaktır. Bu ayar devre dışı bırakıldığı sürece bu e-posta kampanyasını herhangi bir aşama için yapılandıramazsınız.", "app.containers.Admin.messaging.newProjectPhaseModal.enabledMessage1": "Bu, mevcut proje aşamaları için {emailCampaignName} e-posta kampanyasını otomatik olarak etkinleştirmeyecektir. Bu ayarın etkinleştirilmesi, yalnı<PERSON><PERSON> her aşama için bu e-posta kampanyasını yapılandırmanıza izin verecektir.", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOff1": "{emailCampaignName} e-posta kampanyası ayarını devre dışı bırakmak istediğinizden emin misiniz?", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOn1": "{emailCampaignName} e-posta kampanyası ayarını etkinleştirin mi?", "app.containers.Admin.messaging.newProjectPhaseModal.turnOff": "Evet, kapatın", "app.containers.Admin.messaging.newProjectPhaseModal.turnOn": "Evet, açın", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "<PERSON>u widget, her kull<PERSON><PERSON><PERSON><PERSON>ya <b>takip tercihlerine göre</b> projeler gösterir. Bu, takip ettikleri projelerin yanı sıra girdileri takip ettikleri projeleri ve ilgilendikleri konular veya alanlarla ilgili projeleri içerir.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "<PERSON><PERSON> widget, kullanıcıya yalnızca katılabileceği projeler varsa gösterilecektir. Bu mesaj<PERSON><PERSON>, bu sizin (yönetici) şu anda herhangi bir projeye katılamayacağınız anlamına gelir. Bu mesaj gerçek ana sayfada görünmeyecektir.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Katılıma açık", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "<PERSON>u widget, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> şu anda <b>katılmak için bir eylemde</b> bulunabileceği projeleri sergileyecektir.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Başlık", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "<PERSON><PERSON> widget, kullanıcıya yalnızca takip tercihlerine göre kendisiyle ilgili projeler varsa gösterilecektir. Bu mesajı gö<PERSON><PERSON>rsen<PERSON>, bu sizin (yönetici) şu anda hiçbir şeyi takip etmediğiniz anlamına gelir. Bu mesaj gerçek ana sayfada görünmeyecektir.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Takip edilen <PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Arşivlendi", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Tamamlandı ve arşivlendi", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "<PERSON><PERSON> mev<PERSON> de<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "<PERSON>u widget <b><PERSON><PERSON><PERSON> ve/veya arşivlenmiş projeleri</b> <PERSON><PERSON><PERSON><PERSON>. \"<PERSON><PERSON>\" ayr<PERSON><PERSON> son aşamada olan ve son aşamanın bir rapor olduğu projeleri de içerir.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "<PERSON>, biz yaptık.", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "<PERSON><PERSON><PERSON> diller i<PERSON>in bir ad girin", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "<PERSON><PERSON> bo<PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "İsim", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Sonuçlanan URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Gezinti çubuğu yalnızca kullanıcıların erişebildiği projeleri gösterecektir.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Bu widget yalnızca Topluluk Gözlemcisi yanıtları kabul ederken Ana Sayfada görünür olacaktır.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Topluluk Gözlemcisi", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Başlık", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Önemli:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Duyarlılık anketi sorusu örneği", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Bitiş tarihi yok", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Atlıkarıncayı atlamak için escape tuşuna basın", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projeler ve klasörler (eski)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "<PERSON><PERSON><PERSON> başlı<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} şu anda üzerinde çalışıyor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Şimdi katılın!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "klasör", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Lütfen bir proje veya klasör se<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "<PERSON>je veya k<PERSON>ör se<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Avatarları göster", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Başlık", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "{days} gün i<PERSON> ba<PERSON>lı<PERSON>r", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "{weeks} haftasında başlıyor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} g<PERSON><PERSON><PERSON>nce", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} haftalar önce", "app.containers.Admin.project.Campaigns.campaignFrom": "<PERSON><PERSON>?", "app.containers.Admin.project.Campaigns.campaignTo": "<PERSON><PERSON>?", "app.containers.Admin.project.Campaigns.customEmails": "Özel e-postalar", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Özel e-postalar gönderin ve istatistikleri kontrol edin.", "app.containers.Admin.project.Campaigns.noAccess": "Üzgünüz, ancak e-postalar bölümüne erişiminiz yok gibi görünüyor", "app.containers.Admin.project.emails.addCampaign": "E-posta oluşturun", "app.containers.Admin.project.emails.addCampaignTitle": "<PERSON><PERSON>", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Tüm {participants} ve proje takipçileri", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "<PERSON><PERSON>, projede herhangi bir işlem gerçekleştiren kayıtlı kullanıcıları içerir. Kayıtsız veya anonim kullanıcılar da<PERSON>.", "app.containers.Admin.project.emails.dateSent": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarih", "app.containers.Admin.project.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.draft": "Taslak", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "Kampanyayı düzenle", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Katılımcılarınıza e-posta göndererek onlarla kolayca bağlantı kurun. <PERSON>inle iletişime geçeceğinizi seçin ve etkileşiminizi takip edin.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "İlk e-postanızı gönderin", "app.containers.Admin.project.emails.failed": "Başarısız", "app.containers.Admin.project.emails.fieldBody": "E-posta Mesajı", "app.containers.Admin.project.emails.fieldBodyError": "<PERSON><PERSON><PERSON> diller için bir e-posta mesajı sağlayın", "app.containers.Admin.project.emails.fieldReplyTo": "Yanıtlar şu adrese gitmelidir", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Doğru formatta bir e-posta adresi girin, <PERSON>rne<PERSON>in <EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Bir e-posta adresi <PERSON>ın", "app.containers.Admin.project.emails.fieldReplyToTooltip": "E-postanızdaki kullanıcılardan hangi e-posta adresinin doğrudan yanıt alacağını seçin.", "app.containers.Admin.project.emails.fieldSender": "<PERSON><PERSON>", "app.containers.Admin.project.emails.fieldSenderError": "E-postanın göndericisini sağlayın", "app.containers.Admin.project.emails.fieldSenderTooltip": "Kullanıcıların e-postayı gönderen olarak kimi göreceğini seçin.", "app.containers.Admin.project.emails.fieldSubject": "<PERSON>-<PERSON><PERSON>", "app.containers.Admin.project.emails.fieldSubjectError": "<PERSON><PERSON><PERSON> diller için bir e-posta konusu sağlayın", "app.containers.Admin.project.emails.fieldSubjectTooltip": "<PERSON>u, e-postanın konu satırında ve kullanıcının gelen kutusu genel görünümünde gösterilecektir. Açık ve ilgi çekici hale getirin.", "app.containers.Admin.project.emails.fieldTo": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.formSave": "Taslak olarak kaydet", "app.containers.Admin.project.emails.from": "<PERSON><PERSON>?", "app.containers.Admin.project.emails.helmetDescription": "<PERSON><PERSON> katı<PERSON>ımcılarına manuel e-postalar gönderin", "app.containers.Admin.project.emails.infoboxAdminText": "Proje <PERSON>ma sekmesinden yalnızca tüm proje katılımcılarına e-posta gönderebilirsiniz.  Diğer katılımcılara veya kullanıcı alt kümelerine e-posta göndermek için {link} sekmesine gidin.", "app.containers.Admin.project.emails.infoboxLinkText": "Platform Mesajlaşması", "app.containers.Admin.project.emails.infoboxModeratorText": "Proje <PERSON>şma sekmesinden yalnızca tüm proje katılımcılarına e-posta gönderebilirsiniz. Yöneticiler, Platform Mesajlaşma sekmesi aracılığıyla diğer katılımcılara veya kullanıcı alt kümelerine e-posta gönderebilir.", "app.containers.Admin.project.emails.message": "<PERSON><PERSON>", "app.containers.Admin.project.emails.nameVariablesInfo2": "{firstName} {lastName}değişkenlerini kullanarak vatandaşlarla doğrudan konuşabilirsiniz. Örneğin \"Sayın {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "Katılımcılar", "app.containers.Admin.project.emails.previewSentConfirmation": "E-posta adresinize bir önizleme e-postası gönderildi", "app.containers.Admin.project.emails.previewTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.projectParticipants": "<PERSON><PERSON>ı<PERSON>", "app.containers.Admin.project.emails.recipients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.send": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sendTestEmailButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sendTestEmailTooltip": "'Gerçek hayatta' nasıl göründüğünü kontrol etmek için bu taslak e-postayı giriş yaptığınız e-posta adresine gönderin.", "app.containers.Admin.project.emails.senderRecipients": "Gönderici ve alıcılar", "app.containers.Admin.project.emails.sending": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "Bunlar kullanıcılara gönderilen e-postalardır", "app.containers.Admin.project.emails.status": "Durum", "app.containers.Admin.project.emails.subject": "<PERSON><PERSON>:", "app.containers.Admin.project.emails.to": "<PERSON><PERSON>?", "app.containers.Admin.project.messaging.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Bu gör<PERSON><PERSON><PERSON> klasör kartının bir parçasıdır; klasörü özetleyen ve örneğin ana sayfada gösterilen kart. Önerilen görüntü çözünürlükleri hakkında daha fazla bilgi için, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Bu görüntü klasör sayfasının en üstünde gösterilir. Önerilen görüntü çözünürlükleri hakkında daha fazla bilgi için, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "destek merkezimizi ziyaret edin", "app.containers.Admin.projects.all.askPersonalData3": "Ad ve e-posta i<PERSON>in al<PERSON> e<PERSON>in", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Tüm sorular PDF üzerinde gösterilir. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iler şu anda FormSync aracılığıyla içe aktarma için desteklenmemektedir: <PERSON><PERSON><PERSON>, Etiketler ve Dosya Yükleme.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Tüm sorular PDF üzerinde gösterilir. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iler şu anda FormSync aracılığıyla içe aktarma için desteklenmemektedir: <PERSON><PERSON><PERSON><PERSON> sorular<PERSON> (pim <PERSON><PERSON><PERSON><PERSON>, rota çizme ve alan çizme), s<PERSON><PERSON><PERSON> soruları, matris soruları ve dosya yükleme soruları.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "<PERSON><PERSON> sonu", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "<PERSON>un başlangıcı", "app.containers.Admin.projects.all.components.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.containers.Admin.projects.all.components.draft": "Taslak", "app.containers.Admin.projects.all.components.manageButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "<PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectError": "<PERSON>u projeyi kopyala<PERSON>en bir hata <PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>in.", "app.containers.Admin.projects.all.customiseEnd": "<PERSON><PERSON> sonunu <PERSON>.", "app.containers.Admin.projects.all.customiseStart": "Formun başlangıcını özelleştirin.", "app.containers.Admin.projects.all.deleteFolderButton1": "Klasörü sil", "app.containers.Admin.projects.all.deleteFolderConfirm": "Bu klasörü silmek istediğinizden emin misiniz? Klasörle birlikte içindeki tüm projeler silinir. Bu işlem geri alınamaz.", "app.containers.Admin.projects.all.deleteFolderError": "Bu klasör kaldırılırken bir sorun oluştu. Lütfen tekrar deneyin.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "<PERSON><PERSON> silme", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Bu projeyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "app.containers.Admin.projects.all.deleteProjectError": "<PERSON>u proje silinirken bir hata <PERSON>, lü<PERSON><PERSON> daha sonra tekrar deneyin.", "app.containers.Admin.projects.all.exportAsPDF1": "PDF formunu indirin", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Çevrimiçi ve çevrimdışı yanıtları birleştirebilirsiniz. Çevrimdışı yanıtları yüklemek için bu projenin 'Girdi yöneticisi' sekmesine gidin ve 'İçe aktar' seçeneğine tıklayın.", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Çevrimiçi ve çevrimdışı yanıtları birleştirebilirsiniz. Çevrimdışı yanıtları yüklemek için bu projenin 'Anket' sekmesine gidin ve 'İçe Aktar'a tıklayın.", "app.containers.Admin.projects.all.logicNotInPDF": "Anket mantığı indirilen PDF'ye yansıtılmayacaktır. Basılı anket katılımcıları tüm anket sorularını görecektir.", "app.containers.Admin.projects.all.new.Folders.Filters.search": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Klasör", "app.containers.Admin.projects.all.new.Folders.Table.managers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# proje} other {# projeler}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Durum", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> tarihi", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Keşfedilebilirlik", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Klasörler", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Geçerli faz katılım yöntemine göre filtreleme", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Düşünce", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Katılım yöntemi", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Belge açıklaması", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Ortak zemin", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Anket", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Anket", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Gönüllülük", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Bilgilendirme", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Başlamadı", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "<PERSON><PERSON><PERSON><PERSON><PERSON> durumu", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Geçmiş", "app.containers.Admin.projects.all.new.Projects.Filters.Search.search": "<PERSON><PERSON> arama", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alfabetik olarak (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alfabetik olarak (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Yakında başlayacak veya bitecek aşamalar", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created": "Yakın zamanda oluşturuldu", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Durum", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Gruplar", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Görünürlük", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "Filtre ekle", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Eklenecek başka filtre yok", "app.containers.Admin.projects.all.new.Projects.Table.admins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "<PERSON><PERSON><PERSON> projeler y<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Arşivlendi", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d sol", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d ba<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Keşfedilebilirlik:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Taslak", "app.containers.Admin.projects.all.new.Projects.Table.end": "Bitiş", "app.containers.Admin.projects.all.new.Projects.Table.ended": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON><PERSON><PERSON><PERSON> sona eriyor", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Gruplar", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "<PERSON><PERSON> fazla <PERSON>…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo sol", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}ba<PERSON><PERSON><PERSON> i<PERSON> mo", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "<PERSON><PERSON><PERSON>:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Görevlendirilmedi", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Aşama", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Lans<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.project": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.public": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.published": "Yayınlandı", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Daha fazlasını yüklemek için aşağı kaydırın", "app.containers.Admin.projects.all.new.Projects.Table.start": "Başlangıç", "app.containers.Admin.projects.all.new.Projects.Table.status": "Durum", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Durum:", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Görünürlük", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Görünürlük:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} gruplar", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y sol", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y ba<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Mevcut aşama: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} g<PERSON><PERSON><PERSON> kaldı", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Klasör: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Mevcut aşama yok", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Bitiş tarihi yok", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "Aşama yok", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Aşama {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Aşamalar:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Başlangıç tarihi: {date}", "app.containers.Admin.projects.all.new.folders": "Klasörler", "app.containers.Admin.projects.all.new.ordering": "Sipariş verme", "app.containers.Admin.projects.all.new.projects": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.timeline": "Zaman Çizelgesi", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Zaman çizelge<PERSON>.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "<PERSON><PERSON><PERSON> bit<PERSON> tarihi yok", "app.containers.Admin.projects.all.new.timeline.project": "<PERSON><PERSON>", "app.containers.Admin.projects.all.notes": "Notlar", "app.containers.Admin.projects.all.personalDataExplanation5": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, dışa aktarılan PDF'ye ad, soyad ve e-posta alanlarını ekleyecektir. Kağıt formu yükledikten sonra, çevrimdışı anket katılımcısı için otomatik olarak bir hesap oluşturmak üzere bu verileri kullanacağız.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "Yapay Zeka Özeti", "app.containers.Admin.projects.project.analysis.Comments.comments": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "<PERSON><PERSON>, 5 veya daha fazla yorum olduğunda kullanılabilir.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Yorumları özetleyin", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON>} =1 {1 yeni yorum} other {# yeni yorum}}", "app.containers.Admin.projects.project.analysis.aiSummary": "Yapay zeka özeti", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Bu yapay zeka tarafından oluşturulmuş bir içeriktir. 100 doğru olmayabilir. Lütfen doğruluk için gerçek girdileri gözden geçirin ve çapraz referans alın. Seçilen girdilerin sayısı azaltılırsa doğruluğun muhtemelen artacağını unutmayın.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Yalnızca katılımcılara gönderilen e-posta bildirimleri", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Arama motorları tarafından indekslenmez", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "<PERSON> sayfada veya widget'la<PERSON> g<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Yalnızca doğrudan URL üzerinden erişilebilir", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "<PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Bu projenin ne kadar keşfedilebilir olduğunu seçin.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "<PERSON><PERSON> proje, er<PERSON><PERSON><PERSON><PERSON> olan herkes tarafından görülebilir ve ana sayfada ve widget'larda görünecektir.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Bu proje genel kamuoyundan gizlenecek ve sadece bağlantıya sahip olanlar tarafından görülebilecektir.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Bu projeyi kimler bula<PERSON>ir?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Açık yapay zeka analizi", "app.containers.Admin.projects.project.ideas.analysisText2": "Yapay zeka destekli özetleri keşfedin ve bireysel başvuruları görüntüleyin.", "app.containers.Admin.projects.project.ideas.importInputs": "İthalat", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Bir rapor o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bunu kamuoyu ile paylaşmayı seçebilirsiniz.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Bilgi <PERSON>ımı için daha karmaşık bir sayfa oluşturun", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "<PERSON><PERSON> rapor <PERSON>:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "<PERSON>ir rapor o<PERSON>", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Geçmiş bir aşama için rapor oluşturun veya sıfırdan başlayın.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "Bu rapor herkese açık değildir. Herkese açık hale getirmek için \"Görünür\" geçişini etkinleştirin.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>, ancak rapor hen<PERSON>z herkese açık değil. Herkese açık hale getirmek için \"Görün<PERSON>r\" geçişini etkinleştirin.", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Bir faz şablonu ile başlayın", "app.containers.Admin.projects.project.information.ReportTab.report": "<PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Geçmiş bir anketin veya fikir aşamasının sonuçlarını pay<PERSON>şın", "app.containers.Admin.projects.project.information.ReportTab.visible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "<PERSON><PERSON> <PERSON><PERSON>, a<PERSON><PERSON> ba<PERSON><PERSON> ba<PERSON><PERSON>az herkese açık olacaktır. Herkese açık olma<PERSON>ı i<PERSON> \"Görünür\" geçişini devre dışı bırakın.", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "<PERSON>u rapor şu anda herkese açıktır. Herkese açık olma<PERSON> i<PERSON><PERSON> \"Görünür\" geçişini devre dışı bırakın.", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Bu raporu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "<PERSON>az<PERSON> e<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "<PERSON><PERSON> edebil<PERSON> i<PERSON> buna onay vermeniz gerekir", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Formu buradan indirebilirsiniz.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Yüklenen form \"<PERSON><PERSON><PERSON><PERSON> veriler\" b<PERSON><PERSON><PERSON><PERSON><PERSON> ile oluşturuldu", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Form dili", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Bu dosyanın Google Cloud Form Parser kullanılarak işlenmesine izin veriyorum", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Excel dosyasını içe aktarma", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "<PERSON><PERSON> etmek için lü<PERSON>fen bir dosya yü<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Şablon buradan indirilebilir.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Tamamlanmış bir <b>Excel dosyası</b> (.xlsx) yükleyin. Bu proje için sağlanan şablonu kullanmalıdır. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> formlardan</b> olu<PERSON><PERSON> bir <b>PDF dosyası</b> yükleyin. Bu aşamada yazdırılmış bir form kullanmalıdır. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Yeni kullanıcı için bu e-postayı kullanın", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Yeni bir hesap oluşturmak için geçerli bir e-posta girin", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Bu bilgilerle yazar için yeni bir hesap oluşturulacaktır. Bu girdi ona eklenecektir.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "<PERSON><PERSON> isim", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Soyadı", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Bu girdiyi bir yazara atamak için lütfen bir e-posta adresi ve/veya ad ve soyad girin. Veya onay kutusundaki işareti kaldırın.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Bu e-posta ile ilişkilendirilmiş bir hesap zaten var. Bu girdi ona eklenecektir.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Kullanıcı onayı (kullanıcı hesabı oluştur)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "<PERSON><PERSON><PERSON> girdi<PERSON>i on<PERSON>n", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Yazar:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "E-posta:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "İçe aktarma sırasında hatalar oluştu ve bazı girdiler içe aktarılmadı. Lütfen hataları düzeltin ve eksik girdileri yeniden içe aktarın.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Geçersiz form verileri. Yukarıdaki formda hata olup olmadığını kontrol edin.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Excel dosyasını içe aktar (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "İthalat", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Taranmış formları içe aktarma (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Taranmış formları içe aktarma", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "İçe aktarılan girdiler", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "İçe aktarma. Bu işlem birkaç dakika sürebilir.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "<PERSON>u girdi anonim olarak aktarılmıştır.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} girdiler ithal edilmiştir ve onay gerektirmektedir.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} gir<PERSON><PERSON> onay<PERSON>. Lütfen doğrulama sorunları için her bir girdiyi kontrol edin ve ayrı ayrı onaylayın.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "<PERSON><PERSON>:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Henüz incelenecek bir şey yok. Taranmış giriş formlarını içeren bir PDF dosyasını veya girişleri içeren bir Excel dosyasını içe aktarmak için \"{importFile}\" adresini tıklayın.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Henüz incelenecek bir şey yok. Girdileri içeren bir Excel dosyasını içe aktarmak için \"{importFile}\" adresini tıklayın.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Say<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "İçe aktarılan dosya görüntülenemiyor. İçe aktarılan dosya görüntüleme yalnızca PDF içe aktarmaları için kullanılabilir.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Aşama:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Seçilen faz girişleri içeremez. Lütfen başka bir tane seçin.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Bu proje fikir içerebilecek herhangi bir aşama içermemektedir.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Lütfen bu girişleri hangi faza eklemek istediğinizi seçin.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.comments": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.participantsTimeline": "Katılımcılar zaman çizelgesi", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "Dönem seçiniz", "app.containers.Admin.projects.project.participation.usersByAge": "<PERSON><PERSON><PERSON> gö<PERSON>", "app.containers.Admin.projects.project.participation.usersByGender": "Cinsiyete göre kullanı<PERSON>ılar", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "<PERSON>ir soru e<PERSON>in", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Aşama düzeyinde kullanıcı alanları ekleme veya düzenleme yeteneği mevcut lisansınıza dahil değildir. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} seçenekler", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "<PERSON><PERSON> du<PERSON>u", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "<PERSON>u sorular anket formunun son say<PERSON><PERSON><PERSON> olarak eklenecektir, <PERSON><PERSON>nk<PERSON> faz ayarlarında \"Ankette alanları göster?\" seçilmiştir.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Ekstra soru sorulmayacaktır.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Opsiyonel", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "İsteğe bağlı - grup tarafından referans verildiği için her zaman etkindir", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "<PERSON><PERSON> kaldır", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Gerekli - grup tarafından referans verildiği i<PERSON>in her zaman etkin", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "{verificationMethod}ile kim<PERSON>ğ<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Aşağıdaki ekstra soruları tamamlayın", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "E-postanızı onaylayın", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Doğrulama yönteminden döndürülen veriler:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Adınızı, soyadınızı, e-posta adresinizi ve şifrenizi girin", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "E-posta adresinizi girin", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ne kadar süre sonra doğrulanmalıdır?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "{verificationMethod} ile kim<PERSON> (kullanıcı grubuna göre)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Katılmak i<PERSON> her<PERSON>i bir eylem gerekmiyor", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Yukarıda listelenen doğrulanmış verilere dayalı olarak katılımı kısıtlamak için akıllı grupları kullanın", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> son 30 dakika içinde doğrulanmış olmalıdır.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> son {days} gün iç<PERSON><PERSON> doğrulanmış olmalıdır.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "Son 30 gün i<PERSON><PERSON>e", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "Son 30 dakika içinde", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "Son 7 gün i<PERSON><PERSON>e", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "<PERSON><PERSON> kez yeterli", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Doğrulanmış alanlar:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} doğ<PERSON>lama", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Katılımcıların adlar<PERSON>, onaylanmış e-postaları ve şifreleri ile tam bir hesap oluşturmaları gerekmektedir.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Katılımcılar<PERSON> adlar<PERSON>, e-postaları ve şifreleriyle tam bir hesap oluşturmaları gerekir.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Kimlik Doğrulama", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "E-posta onayı", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Katılımcıların e-postalarını tek seferlik bir kodla onaylamaları gerekmektedir.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Gelişmiş spam algılama", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "<PERSON><PERSON> özellik, IP adreslerini ve cihaz verilerini analiz ederek oturumu kapatılmış kullanıcıların mükerrer anket göndermelerini önlemeye yardımcı olur. Oturum açma zorunluluğu kadar kesin olmasa da, yinelenen yanıtların sayısını azaltmaya yardımcı olabilir.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Not: Paylaş<PERSON><PERSON> a<PERSON> (ofisler veya halka açık Wi-Fi gibi), farklı kullanıcıların mükerrer olarak işaretlenmesi küçük bir ihtimaldir.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Gelişmiş spam algılamayı etkinleştirin", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Katılımcılara sorulan ekstra sorular", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Hiç<PERSON>i", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "<PERSON><PERSON> ka<PERSON>adan veya giriş yapmadan katılabilir.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Ekstra soruları ve grupları sıfırlayın", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Katılımı kullanıcı gruplarıyla sınırlandırın", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO doğrulaması", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Katılımcıların {verificationMethod}adresinden kimliklerini doğrulamaları gerekmektedir.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Açık yapay zeka analizi", "app.containers.Admin.projects.project.survey.allFiles": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.allResponses": "<PERSON><PERSON><PERSON> ya<PERSON>", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Doğruluk: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Yapay zeka özeti oluşturulurken bir hata oluştu. Lütfen aşağıda yeniden oluşturmayı deneyin.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Açık yapay zeka analizi", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Bu soru için ö<PERSON>tleri gizle", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Açık analiz eylemleri", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} yeni yan<PERSON>ar", "app.containers.Admin.projects.project.survey.analysis.regenerate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Yapay zeka içgörülerini gösterin", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Mevcut planınızda bir seferde en fazla 30 girdiyi özetleyebilirsiniz. Daha fazla bilgi edinmek için GovSuccess Yöneticinizle veya yöneticinizle konuşun.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "<PERSON><PERSON><PERSON> i<PERSON><PERSON> ilgili soruları seçin", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "{question}adresindeki analizinize ilgili başka sorular da eklemek ister misiniz?", "app.containers.Admin.projects.project.survey.cancel": "İptal", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON> et", "app.containers.Admin.projects.project.survey.consentModalCancel": "İptal", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "OpenAI'yi bu proje için bir veri işleyici olarak kullanmayı kabul ediyorum", "app.containers.Admin.projects.project.survey.consentModalText1": "<PERSON><PERSON>, OpenAI'nin bu proje için bir veri işlemcisi olarak kullanılmasını kabul etmiş olursunuz.", "app.containers.Admin.projects.project.survey.consentModalText2": "OpenAI API'leri otomatik metin özetlerine ve otomatik etiketleme deneyiminin bazı kısımlarına gü<PERSON>ğ<PERSON>.", "app.containers.Admin.projects.project.survey.consentModalText3": "Kullanıcıların anketlerinde yazdıklarını, fi<PERSON>rlerini ve yorumlarını yalnızca OpenAI API'lerine gö<PERSON>, asla profillerinden herhangi bir bilgi göndermiyoruz.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI bu verileri modellerini daha fazla eğitmek için kullanmayacaktır. OpenAI'nin veri gizliliğini nasıl ele aldığı hakkında daha fazla bilgi {link}adresinde bulunabilir.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON> et<PERSON>", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Formu düzenlemeden önce analize giremezsiniz", "app.containers.Admin.projects.project.survey.deleteAnalysis": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Bu analizi silmek istediğinizden emin misiniz? Bu eylem geri alınamaz.", "app.containers.Admin.projects.project.survey.explore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.followUpResponses": "Yanıtları takip edin", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> ortalama", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "GeoJSON olarak dışa aktar", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Bu soruya verilen yanıtları bir GeoJSON dosyası olarak dışa aktarın. Her GeoJSON Özel<PERSON>, il<PERSON><PERSON> katı<PERSON>ımcının tüm anket yanıtları o Özelliğin 'özellikler' nesnesinde listelenecektir.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Ayrıntıları gizle", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} yan<PERSON>tlayanlar} one {{respondentCount} yan<PERSON><PERSON><PERSON><PERSON><PERSON>} other {{respondentCount} yan<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "Detayları görüntüle", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} seçim<PERSON>} one {{numberChoices} seçim} other {{numberChoices} seçim<PERSON>}}", "app.containers.Admin.projects.project.survey.heatMap": "Isı haritası", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Esri Smart Mapping kullanılarak oluşturulan ısı haritaları hakkında daha fazla bilgi edinin.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Isı haritası Esri Smart Mapping kullanılarak oluşturulmuştur. Is<PERSON> haritaları, çok sayıda veri noktası olduğunda kullanışlıdır. Daha az sayıda nokta için, do<PERSON><PERSON><PERSON> yalnızca konum noktalarına bakmak daha iyi olabilir. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Isı haritası görünümü", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Mantık tarafından gizlenmiş", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Bir kullanıcı bu yanıtı seçtiğinde mantık {pageNumber} sayfasına kadar tüm sayfaları atlar ({numQuestionsSkipped} sorular atlandı). Atlanan sayfaları ve soruları gizlemek veya göstermek için tıklayın.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "<PERSON>ir kullanıcı bu cevabı seçtiğinde mantık anketin sonuna atlar ({numQuestionsSkipped} sorular atlandı). Atlanan sayfaları ve soruları gizlemek veya göstermek için tıklayın.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "<PERSON>u sayfadaki mantık {pageNumber} sayfas<PERSON>na kadar tüm sayfaları atlar ({numQuestionsSkipped} sorular atlandı). Atlanan sayfaları ve soruları gizlemek veya göstermek için tıklayın.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "<PERSON>u sayfadaki mantık anketin sonuna atlar ({numQuestionsSkipped} sorular atlandı). Atlanan sayfaları ve soruları gizlemek veya göstermek için tıklayın.", "app.containers.Admin.projects.project.survey.newAnalysis": "<PERSON><PERSON> analiz", "app.containers.Admin.projects.project.survey.nextInsight": "Sonraki içgörü", "app.containers.Admin.projects.project.survey.openAnalysis": "Açık yapay zeka analizi", "app.containers.Admin.projects.project.survey.otherResponses": "<PERSON><PERSON><PERSON> ya<PERSON>", "app.containers.Admin.projects.project.survey.page": "Say<PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "Önceki içgörü", "app.containers.Admin.projects.project.survey.responses": "Yanıtlar", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Bu sayfaya verilen yanıt sayısı toplam anket yanıt sayısından daha düşüktür çünkü bazı katılımcılar anketteki mantık nedeniyle bu sayfayı görmemiş olacaktır.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Bu soruya verilen yanıt sayısı toplam anket yanıt sayısından daha düşüktür çünkü bazı katılımcılar anketteki mantık nedeniyle bu soruyu görmemiş olacaktır.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Duyarlılık ölçeği", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Tüm yanıtlarınızı anında özetleyin.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Verilerinizle doğal dilde konuşun.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Yapay zeka tarafından oluşturulan özetlerden bireysel yanıtlara referanslar alın.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Tam bir genel bakış için {link} adresimize göz atın.", "app.containers.Admin.projects.project.survey.upsell.button": "Yapay zeka analizinin kilidini açın", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "destek makalesi", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Yapay zeka ile verileri daha hızlı analiz edin", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Bu özellik mevcut planınıza dahil de<PERSON>dir. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.containers.Admin.projects.project.survey.viewAnalysis": "G<PERSON>rü<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Yapay zeka destekli özetleri keşfedin ve bireysel başvuruları görüntüleyin.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Dönem seçiniz", "app.containers.Admin.projects.project.traffic.trafficSources": "Trafik kaynakları", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Ziyaretçi verilerini toplama ve görüntüleme şeklimizi değiştirdik. <PERSON><PERSON><PERSON>, ziyaretçi verileri daha doğrudur ve GDPR ile uyumlu olmaya devam ederken daha fazla veri türü mevcuttur. Bu yeni verileri yalnızca Kasım 2024'te toplamaya başladık, bu nedenle bundan önce hiçbir veri mevcut değil.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "<PERSON><PERSON><PERSON><PERSON>i zaman çizelgesi", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "<PERSON><PERSON><PERSON>u", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Aşama hakkında biraz metin e<PERSON>in", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Bu bir metin içeriğid<PERSON>. Sağdaki panelde bulunan düzenleyiciyi kullanarak düzenleyebilir ve biçimlendirebilirsiniz.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Katılımcılar", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "<PERSON><PERSON><PERSON>, kull<PERSON><PERSON><PERSON> katılım yöntemlerini ve sonucu e<PERSON>in", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Bu rapor kaydedilmemiş değişiklikler içermektedir. Lütfen yazdırmadan önce kaydedin.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Başlık zaten alınmış", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Önceki {days} g<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>ıldığ<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "İstatistikleri gizle", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Katılım oranı", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Son dö<PERSON><PERSON><PERSON> ka<PERSON>şılaştırmayı göster", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Önce bir tarih aralığı seçmeniz gerekir.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Katılım", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Yorumları göster", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Girdileri gö<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Oyları göster", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demografik Bilgiler", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "<PERSON><PERSON>t tarih a<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "<PERSON><PERSON><PERSON> alanı", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Bilinmiyor", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Kullanıcılar: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Esneme", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktif", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Arşivlendi", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Açık uçlu", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planlanmış", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>u", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Yayınlandı", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "<PERSON>u widget için veriler eksik. <PERSON><PERSON>u kaydedebilmek için yeniden yapılandırın veya silin.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Topluluk Monitörü Sağlık Puanı", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Bu projede uygun bir aşama bulunamamıştır", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Seçili faz yok. Lütfen önce bir faz seçin.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "<PERSON><PERSON> yok", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Seçili proje yok. Lütfen önce bir proje seçin.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Bu raporu çoğaltamazsınız çünkü erişiminiz olmayan veriler içerir.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Eriş<PERSON><PERSON>z o<PERSON> veriler içerdiği için bu raporu düzen<PERSON>emezsiniz.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "\"{reportName}\" ad<PERSON><PERSON> silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Bu raporu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Sil", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {days, plural, no {# gün} one {# gün} other {# gün}} önce", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Bu raporu oluşturmaya çalışırken bir hata oluştu. Lütfen daha sonra tekrar deneyin.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Boş bir sayfa ile ba<PERSON><PERSON>ın", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Bir Topluluk İzleyicisi şablonu ile başlayın", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "<PERSON><PERSON> b<PERSON>ı<PERSON>ı", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "<PERSON>ir rapor o<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Raporunuzu özelleştirin ve PDF dosyası olarak şirket içi paydaşlarla veya toplulukla paylaşın.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "<PERSON>ir rapor o<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "İlk raporunuzu oluşturun", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Seçilen proje yok", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Bir platform şablonu ile başlayın", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "PDF'e Yazdır", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Bir proje şablonu ile başlayın", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "<PERSON><PERSON>rek {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Bu başlığa sahip bir rapor zaten mevcut. Lütfen farklı bir başlık seçin.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "PDF olarak paylaş", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "<PERSON><PERSON>le paylaşmak için raporu PDF olarak yazdırın.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Web bağlantısı olarak paylaş", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Bu web bağlantısına yalnızca yönetici kullanıcılar erişebilir.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Paylaş", "app.containers.Admin.reporting.contactToAccess": "Özel bir rapor oluşturmak premium lisansın bir parçasıdır. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Topluluk gözlemci raporları", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Bu raporlar Topluluk İzleyicisi ile ilgilidir. Raporlar her çeyrekte otomatik olarak oluşturulur.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "<PERSON>ir rapor o<PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Raporunuzu özelleştirin ve bir web bağlantısı ile dahili paydaşlarla veya toplulukla paylaşın.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Raporlarınız burada görünecektir.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "<PERSON><PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "İlerleme <PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Bunlar Kamu Başarı Yöneticiniz tarafından oluşturulan raporlardır", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Raporlarınız", "app.containers.Admin.reporting.deprecated": "DEVRE DIŞI BIRAKILDI", "app.containers.Admin.reporting.helmetDescription": "Yönetici raporlama sayfası", "app.containers.Admin.reporting.helmetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.printPrepare": "Baskıya hazırlanıyor...", "app.containers.Admin.reporting.reportBuilder": "<PERSON><PERSON>", "app.containers.Admin.reporting.reportHeader": "<PERSON><PERSON> b<PERSON>ı<PERSON>ı", "app.containers.Admin.reporting.warningBanner3": "Bu rapordaki grafikler ve sayılar yalnızca bu sayfada otomatik olarak güncellenir. <PERSON><PERSON><PERSON> say<PERSON>larda güncellemek için raporu kaydedin.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Ortak zemin", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Düşünce", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Kullanılan Yöntemler", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Anket", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Anket", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "<PERSON><PERSON><PERSON> {days} g<PERSON><PERSON><PERSON>: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "Dış anket", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Gönüllülük", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "<PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "G<PERSON>rü<PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.downloads": "<PERSON><PERSON>rmeler", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Başka bir anketi çoğaltın", "app.containers.Admin.surveyFormTab.editSurveyForm": "<PERSON><PERSON> form<PERSON>", "app.containers.Admin.surveyFormTab.inputFormDescription": "Hangi bilgilerin sağlanması gerektiğini belirtin, katılımcı yanıtlarını yönlendirmek için kısa açıklamalar veya talimatlar ekleyin ve her alanın isteğe bağlı mı yoksa gerekli mi olduğunu belirtin.", "app.containers.Admin.surveyFormTab.surveyForm": "<PERSON><PERSON> formu", "app.containers.Admin.tools.apiTokens.createTokenButton": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCancel": "İptal", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Jetonunuz oluşturulmuştur. Lütfen aşağıdaki {secret} adresini kopyalayın ve güvenli bir şekilde saklayın.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Genel API'miz ile kullanmak için yeni bir token oluşturun.", "app.containers.Admin.tools.apiTokens.createTokenError": "Belirteciniz için bir ad girin", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Belirteç oluştur", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Önemli!</b> Bu {secret} adresini yalnızca bir kez kopyalayabilirsiniz. Bu pencereyi kapatırsanız bir daha göremezsiniz.", "app.containers.Admin.tools.apiTokens.createTokenName": "İsim", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Belirteçinize bir isim verin", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Belirteciniz oluşturuldu", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Ka<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Kopyalandı!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "<PERSON><PERSON> bir be<PERSON><PERSON> oluştur<PERSON>", "app.containers.Admin.tools.apiTokens.createdAt": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.delete": "Belirteci sil", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Bu belirteci silmek istediğinizden emin misiniz?", "app.containers.Admin.tools.apiTokens.description": "Genel API'miz için API belirteçlerinizi yönetin. Daha fazla bilgi için {link}adresimize bakın.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "<PERSON>", "app.containers.Admin.tools.apiTokens.link": "API belgeleri", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "İsim", "app.containers.Admin.tools.apiTokens.noTokens": "<PERSON><PERSON><PERSON><PERSON> hiç jetonunuz yok.", "app.containers.Admin.tools.apiTokens.title": "Genel API belirteçleri", "app.containers.Admin.tools.esriDisabled": "Esri entegrasyonu bir eklenti özelliğidir. Bu konuda daha fazla bilgi almak istiyorsanız GovSuccess Yöneticinizle iletişime geçin.", "app.containers.Admin.tools.esriIntegration2": "<PERSON><PERSON><PERSON> entegra<PERSON><PERSON>", "app.containers.Admin.tools.esriIntegrationButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Esri hesabınızı bağlayın ve ArcGIS Online'dan verileri doğrudan haritalama projelerinize aktarın.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logosu", "app.containers.Admin.tools.esriKeyInputDescription": "Projelerdeki harita sekmelerinde ArcGIS Online'dan harita katmanlarınızı içe aktarmaya izin vermek için Esri API anahtarınızı ekleyin.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API anahtarı", "app.containers.Admin.tools.esriKeyInputPlaceholder": "API anahtarını buraya yapıştırın", "app.containers.Admin.tools.esriMaps": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriSaveButtonError": "Anahtarınızı kaydederken bir hata olu<PERSON>, lütfen tekrar deneyin.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API anahtarı kaydedildi", "app.containers.Admin.tools.esriSaveButtonText": "<PERSON><PERSON> t<PERSON>", "app.containers.Admin.tools.learnMore": "Daha fazla bilgi edinin", "app.containers.Admin.tools.managePublicAPIKeys": "API Anahtarlarını Yönetme", "app.containers.Admin.tools.manageWidget": "Widget'ı yönet", "app.containers.Admin.tools.manageWorkshops": "Atölye çalışmalarını yönetin", "app.containers.Admin.tools.powerBIAPIImage": "Power BI görüntüsü", "app.containers.Admin.tools.powerBIDescription": "Microsoft Power BI Çalışma Alanınızdaki Go Vocal verilerine erişmek için tak ve çalıştır Power BI Şablonlarımızı kullanın.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI lisansınızın bir parçası değildir. Bu konuda daha fazla bilgi istiyorsanız GovSuccess Yöneticinizle iletişime geçin.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Şablonları indirin", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Go Vocal verilerinizi bir Power BI veri akışında kullanmayı düşünü<PERSON>n<PERSON>z, bu şablon Go Vocal verilerinize bağlanan yeni bir veri akışı oluşturmanızı sağlayacaktır. Bu şablonu indirdikten sonra, PowerBI'ye yüklemeden önce şablonda aşağıdaki ##CLIENT_ID## ve ##CLIENT_SECRET## dizelerini bulup genel API kimlik bilgilerinizle değiştirmeniz gerekir.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Veri akışı şablonunu indirin", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Veri akışı şablonu", "app.containers.Admin.tools.powerBITemplates.intro": "Not: Bu Power BI şablonlarından birini kullanmak için önce {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "genel API'miz için bir dizi kimlik bilgisi oluşturun", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "<PERSON><PERSON><PERSON><PERSON>, Go Vocal verilerinize dayalı bir Power BI raporu oluşturacaktır. Go Vocal platformunuza tüm veri bağlantılarını kuracak, veri modelini ve bazı varsayılan gösterge tablolarını oluşturacaktır. Şablonu Power BI'da açtığınızda genel API kimlik bilgilerinizi girmeniz istenecektir. Ayrıca platformunuz için Temel URL'yi de girmeniz gerekecektir: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "<PERSON><PERSON><PERSON><PERSON> indirin", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "<PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Go Vocal verilerinizi Power BI'da kullanma hakkında daha fazla bilgiyi {link}adresimizde bulabilirsiniz.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "destek makalesi", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI şablonları", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Genel API'mizde özel entegrasyonlar oluşturmak için kimlik bilgilerini yönetin.", "app.containers.Admin.tools.publicAPIDisabled1": "Genel API, mevcut lisansınızın bir parçası değildir. Bu konuda daha fazla bilgi istiyorsanız GovSuccess Yöneticinizle iletişime geçin.", "app.containers.Admin.tools.publicAPIImage": "Genel API görüntüsü", "app.containers.Admin.tools.publicAPITitle": "Genel API Erişimi", "app.containers.Admin.tools.toolsLabel": "Araçlar", "app.containers.Admin.tools.widgetDescription": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.Admin.tools.widgetImage": "Widget <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Canlı video toplantıları düzenleyin, eşzamanlı grup tartışmalarını ve münazaraları kolaylaştırın. Tıpkı çevrimdışı yaptığınız gibi girdi toplayın, oy verin ve fikir birliğine varın.", "app.containers.Admin.tools.workshopsImage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Çevrimiçi müzakere atölyeleri", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "platformdaki toplam kullanıcı sayısı", "app.containers.AdminPage.DashboardPage._blank": "bilinmiyor", "app.containers.AdminPage.DashboardPage.allGroups": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.allProjects": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.allTime": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.comments": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Platform kullanıcılarının temsil gücünü ölçmek için temel alınacak bir veri seti gereklidir.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Çok yakında", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "<PERSON>u anda {fieldName} panosu üzerinde çalışıyoruz, yakı<PERSON> kullan<PERSON>k", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "Bu grafikte {numberOfHiddenItems, plural, one {# öğe} other {# öğe}} gizli. Tüm verileri görmek için {tableViewLink} değişikliği yapın.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "Kullanıcı kaydı için {requiredOrOptional}", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known}/{total} kullanıcı dahil edildi ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "{numberOfHiddenItems} ö<PERSON><PERSON> da<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "İsteğe bağlı", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Lütfen temel alınacak bir veri seti sağlayın.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Temsil gücü puanı:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "<PERSON><PERSON> puan, platform kullanıcı verilerinin toplam nüfusu ne kadar isabetli temsil ettiğini gösterir. {representativenessArticleLink} hakkında daha fazla bilgi edinin.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Zorun<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Temel alınacak verileri gönder", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "tablo görünümü", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "<PERSON>lam nüfus", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "<PERSON><PERSON> ya<PERSON> grubu e<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} ve üzeri", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "<PERSON><PERSON> gru<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "{upperBound} ve üzeri yaş grupları da<PERSON>.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "<PERSON><PERSON> grubu {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Yaş grupları", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "ve üzeri", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Örnek gruplandırma uygulayın", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "<PERSON><PERSON>ını, temel alınacak veri setinizle uyumlu olacak şekilde ayarlayın.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Aralık", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Yaş gruplarını düzenleyin", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Bu öğe hesaplanmayacaktır.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON>ha azını göster", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "{numberOfHiddenItems} öğe daha g<PERSON>...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Temel ay (isteğe bağlı)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "<PERSON><PERSON> grupları (Doğum yılı)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Çok yakında", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Tam", "app.containers.AdminPage.DashboardPage.components.Field.default": "Varsayılan", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Lütfen tüm etkin seçenekleri doldurun veya grafikten çıkarmak istediğiniz seçenekleri devre dışı bırakın. En az bir seçenek doldurulmalıdır.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Toplam ikamet eden kişi sayısı", "app.containers.AdminPage.DashboardPage.components.Field.options": "Seçenekler", "app.containers.AdminPage.DashboardPage.components.Field.save": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Temel alınacak verileri girmeye başlamak için lütfen önce {setAgeGroupsLink}.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "yaş grupları belirleyin", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Ortalama yanıt süresi: {days} gün", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Yanıt vermek için ortalama gün sayısı", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "geri bildirim verildi", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "<PERSON><PERSON><PERSON> g<PERSON>re gir<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "<PERSON><PERSON><PERSON> bi<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "<PERSON><PERSON><PERSON> sü<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Durum", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "<PERSON>rum değişti", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Toplam", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Temel alınacak verileri dü<PERSON>le", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "temsi̇l gücü puanlarını hesaplama biçimimiz", "app.containers.AdminPage.DashboardPage.continuousType": "Zaman çize<PERSON> o<PERSON>", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Küm<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "g<PERSON>n", "app.containers.AdminPage.DashboardPage.false": "yanlış", "app.containers.AdminPage.DashboardPage.female": "kadın", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Tepkilere göre ilk 5 girdi", "app.containers.AdminPage.DashboardPage.fromTo": "{from} ile {to} arasında", "app.containers.AdminPage.DashboardPage.helmetDescription": "<PERSON>daki faaliyetler i<PERSON><PERSON> pano", "app.containers.AdminPage.DashboardPage.helmetTitle": "Yönetici panosu say<PERSON>ı", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Projeye göre gösterilecek kaynağı seçin", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Etikete göre gösterilecek bir kaynak seçin", "app.containers.AdminPage.DashboardPage.inputs1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Kullanıcı grubu seçin", "app.containers.AdminPage.DashboardPage.male": "erkek", "app.containers.AdminPage.DashboardPage.month": "ay", "app.containers.AdminPage.DashboardPage.noData": "Gösterilecek herhangi bir veri yok.", "app.containers.AdminPage.DashboardPage.noPhase": "Bu proje için aşama oluşturulmadı", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "<PERSON><PERSON><PERSON>, tepki veren veya yorum yapan katılımc<PERSON> sayısı.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.overview.management": "Y<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projeler & Katılım", "app.containers.AdminPage.DashboardPage.overview.showLess": "<PERSON><PERSON> a<PERSON> g<PERSON>", "app.containers.AdminPage.DashboardPage.overview.showMore": "<PERSON><PERSON> faz<PERSON>", "app.containers.AdminPage.DashboardPage.participants": "Katılımcılar", "app.containers.AdminPage.DashboardPage.participationPerProject": "<PERSON><PERSON> başına katılım", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Etiket başına katılım", "app.containers.AdminPage.DashboardPage.perPeriod": "{period} başına", "app.containers.AdminPage.DashboardPage.previous30Days": "Önceki 30 gün", "app.containers.AdminPage.DashboardPage.previous90Days": "Önceki 90 gün", "app.containers.AdminPage.DashboardPage.previousWeek": "Önceki hafta", "app.containers.AdminPage.DashboardPage.previousYear": "Önceki yıl", "app.containers.AdminPage.DashboardPage.projectType": "<PERSON><PERSON> türü: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Temel alınacak bu veri seti, platform kullanıcılarının toplam nüfusla kıyaslandığında temsil gücünü hesaplamak için gereklidir.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Lütfen temel alınacak bir veri seti sağlayın.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Kullanıcı kaydı sırasında toplanan verilere göre platform kullanıcılarınızın nüfusun toplamıyla kıyaslandığındaki temsil gücünü görün. {representativenessArticleLink} hakkında daha fazla bilgi edinin.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Kullanıcı kaydı sırasında toplanan verilere göre platform kullanıcılarınızın nüfusun toplamıyla kıyaslandığındaki temsil gücünü görün.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Topluluk temsili", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "<PERSON><PERSON> d<PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "<PERSON><PERSON> <PERSON>, etkin kayıt al<PERSON>ı<PERSON>ın hiçbiri desteklenmiyor.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "<PERSON><PERSON><PERSON>, öğeleri panoda gösterebilir veya gizleyebilir ve temel alınacak verileri girebilirsiniz. Yalnızca {userRegistrationLink} için etkinleştirilmiş olan alanlar görünür.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Temel alınacak verileri dü<PERSON>le", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "kullanıcı kaydı", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Temel alınacak verileri gönder", "app.containers.AdminPage.DashboardPage.resolutionday": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.resolutionmonth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.resolutionweek": "Haftalar", "app.containers.AdminPage.DashboardPage.selectProject": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.selectedProject": "geçerli proje filtresi", "app.containers.AdminPage.DashboardPage.selectedTopic": "geçerli etiket filtresi", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Platformunuzda neler olduğunu k<PERSON>şfedin.", "app.containers.AdminPage.DashboardPage.tabOverview": "Genel Bakış", "app.containers.AdminPage.DashboardPage.tabReports": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.timelineType": "Zaman Çizelgesi", "app.containers.AdminPage.DashboardPage.titleDashboard": "Pan<PERSON>", "app.containers.AdminPage.DashboardPage.total": "Toplam", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Bu {period}", "app.containers.AdminPage.DashboardPage.true": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.unspecified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "<PERSON><PERSON><PERSON> gö<PERSON>", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "<PERSON>ğ<PERSON><PERSON> al<PERSON> g<PERSON>", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Cinsiyete göre kullanı<PERSON>ılar", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "<PERSON><PERSON>tlar", "app.containers.AdminPage.DashboardPage.week": "hafta", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Bir favicon görseli seçmek için ipuçları: G<PERSON>r<PERSON>nd<PERSON>ğ<PERSON> haliyle görselin boyutu çok küçük olacağı için basit bir görsel seçin. Görsel PNG olarak kaydedilmeli, kare biç<PERSON>inde olmalı ve arka planı saydam (veya gerekirse beyaz) olmalıdır. Üzerinde değişiklik yapmak teknik destek gerektireceğinden favicon'unuz yalnızca bir kez ayarlanmalıdır.", "app.containers.AdminPage.FaviconPage.save": "<PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON><PERSON> so<PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Başarılı!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Sil", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Klasör yöneticileri klasör açıklamasını düzenleyebilir, klasör içinde yeni projeler oluşturabilir ve klasör içindeki tüm projeler üzerinde proje yönetimi haklarına sahip olur. Projeleri silemezler ve kendi klasörleri içinde olmayan projelere erişimleri yoktur. Proje yönetimi hakları hakkında daha fazla bilgi için {projectManagementInfoCenterLink} adresini ziyaret edebilirsiniz.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Eşleşme bulunamadı", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "Yardım Merkezimizi ziyaret edin", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Kullanıcı arama", "app.containers.AdminPage.FoldersEdit.addToFolder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "<PERSON><PERSON> klasörü sil", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.draftStatus": "Taslak", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "<PERSON>u k<PERSON>öre dosya ekle", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Dosyalar 50 MB'tan büyük olmamalıdır. Eklenen dosyalar klasör sayfasında gösterilir.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Açıklamalar", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Bu klasörde proje yok. Proje oluşturmak ve eklemek için ana Projeler sekmesine geri dö<PERSON>.", "app.containers.AdminPage.FoldersEdit.folderName": "Klasör adı", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Başlık resmi", "app.containers.AdminPage.FoldersEdit.multilocError": "Her dil için tüm metin alanları doldurulmalıdır.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Bu klasöre ekleyebileceğiniz bir proje yok.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Klasör kartı görseli", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Klasördeki projeler", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "<PERSON>u klasöre eklenen projeler", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Bu klasöre ekleyebileceğ<PERSON>z projeler", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Bu klasör i<PERSON><PERSON> \"taslak\", \"yayımlanmış\" veya \"arşivlenmiş\" seç<PERSON>i yapın.", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Yayımlandı", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Klasörden kaldır", "app.containers.AdminPage.FoldersEdit.save": "<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "<PERSON><PERSON> so<PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Başarılı!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "ana say<PERSON>da g<PERSON>", "app.containers.AdminPage.FoldersEdit.statusLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> durumu", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Proje<PERSON>in neden bir araya gelebileceğini açıklayın, g<PERSON><PERSON><PERSON> bir kimlik tanımlayın ve bilgi paylaşın.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Proje<PERSON>in neden bir araya gelebileceğini açıklayın, g<PERSON><PERSON><PERSON> bir kimlik tanımlayın ve bilgi paylaşın.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "<PERSON><PERSON><PERSON> metin alan<PERSON>ı doldurulmalıdır.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Başlık", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "<PERSON><PERSON> bir k<PERSON>ö<PERSON>", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Klasörü <PERSON>ö<PERSON>", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Hero bandı görselini ve metnini özelleştirin.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Hero bandı", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Hero bandını kaydet", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration <PERSON><PERSON>, di<PERSON><PERSON><PERSON> projelere göz atarak projeleriniz için il<PERSON> bulabileceğiniz bir yerdir.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Platformunuzun hüküm ve koşullarını ve gizlilik politikasını düzenleyin. Hakkında ve SSS sayfaları da dahil olmak üzere diğer sayfalar {navigationLink} sekmesinden düzenlenebilir.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Platform politikaları", "app.containers.AdminPage.PagesEdition.privacy-policy": "Gi̇zli̇li̇k Poli̇ti̇kası", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.description": "Bu işlem geri alınamaz.", "app.containers.AdminPage.Project.confirmation.no": "İptal", "app.containers.AdminPage.Project.confirmation.title": "Tüm katılım verilerini sıfırlamak istediğinizden emin misiniz?", "app.containers.AdminPage.Project.confirmation.yes": "<PERSON>üm katılım verilerini sıfırlayın", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, o<PERSON><PERSON><PERSON>, tep<PERSON><PERSON>i, anket yanıtlarını, anket yanıtlarını, gönüllüleri ve etkinlik kayıtlarını temizleyin. Oylama aşamaları söz konusu olduğunda, bu eylem oyları temizler ancak seçenekleri temizlemez.", "app.containers.AdminPage.Project.data.title": "Bu projedeki tüm katılım verilerini temizleyin", "app.containers.AdminPage.Project.resetParticipationData": "<PERSON>üm katılım verilerini sıfırlayın", "app.containers.AdminPage.Project.settings.accessRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.back": "<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.data": "<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.general": "<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "<PERSON><PERSON>i projelerin listesi", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "<PERSON><PERSON> projeler oluşturun veya mevcut projeleri yönetin.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDashboard.published": "Yayımlandı", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "<PERSON><PERSON><PERSON> panelini kapatın", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "<PERSON> g<PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Sol", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "Doğru.", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> metin girin", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Birincil", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "<PERSON><PERSON><PERSON><PERSON> tipi", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "İkincil", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Düğme URL'si", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "<PERSON><PERSON>ğme için bir URL girin", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "<PERSON> <PERSON><PERSON> a<PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "<PERSON> sayfadaki proje kartında gösterilir.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "<PERSON>je sayfasında gösterilir. Projenin ne hakkında olduğunu, kullanıcılarınızdan ne beklediğinizi ve onların sizden ne bekleyebileceğini açıkça tanımlayın.", "app.containers.AdminPage.ProjectDescription.errorMessage": "<PERSON><PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "app.containers.AdminPage.ProjectDescription.preview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.ProjectDescription.saved": "<PERSON><PERSON><PERSON><PERSON>!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Hedef kitlenize hangi mesajı vermek istediğinize karar verin. Projenizi düzenleyin ve g<PERSON><PERSON>, videolar, dosya ekleri gibi öğelerle zenginleştirin. <PERSON><PERSON> <PERSON>l<PERSON><PERSON>, ziyaretçilerin projenizin ne hakkında olduğunu anlamalarına yardımcı olur.", "app.containers.AdminPage.ProjectDescription.titleDescription": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Kenarlık ekle", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Büyük", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Orta", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Küçük", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Düzenlemeyi iptal et", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Harita merkez noktasının varsayılan enlemi. 90 ile 90 arasında bir değer kabul eder.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Harita merkez noktasının varsayılan boylamı. 90 ile 90 arasında bir değer kabul eder.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "<PERSON><PERSON> ka<PERSON>ü<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Katmanı düzenle", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "<PERSON><PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "app.containers.AdminPage.ProjectEdit.MapTab.here": "buraya", "app.containers.AdminPage.ProjectEdit.MapTab.import": "GeoJSON dosyasını içe aktarın", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Varsayılan enlem", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Katmandaki tüm özellikler bu renkle şekillendirilecektir. <PERSON><PERSON> renk, GeoJSON dosyanızdaki mevcut stillerin de üzerine yazılacaktır.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Fosforlu kalem simgesi", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "İsteğe bağl<PERSON> o<PERSON>ak, fosforlu kalemler için gösterilecek bir simge seçin. Seçebileceğiniz simgelerin listesini görmek için {url} adresine tıklayın.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Katman adı", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "<PERSON><PERSON> katman adı harita açıklamalarında gösterilir", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "<PERSON><PERSON> <PERSON><PERSON>, harita üzerinde katman özelliklerinin üzerine gelindiğinde bir araç ipucu olarak görüntülenir", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "<PERSON><PERSON> anda GeoJSON dosyalarını destekliyoruz. <PERSON><PERSON> ka<PERSON>ın nasıl dönüştürüleceği ve şekillendirileceği hakkında ipuçları için {supportArticle} okuyun.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Varsayılan boylam", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "<PERSON><PERSON>lan merkezi ve yakınlaştırma dü<PERSON>yi", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Haritanın varsayılan merkez noktası ve yakınlaştırma düzeyi. Aşağıdaki değerleri manuel olarak ayarlayın veya haritanın geçerli merkez noktasını ve yakınlaştırma düzeyini varsayılan değerler olarak kaydetmek için haritanın sol alt köşesindeki {button} düğmesine tıklayın.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "<PERSON><PERSON> yüklemek ve şekillendirmek, harita merkez<PERSON> ve yakınlaştırma düzeyini ayarlamak dahil olmak üzere harita görünümünü özelleştirin.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "<PERSON><PERSON>ırması", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "<PERSON><PERSON> konfi<PERSON><PERSON><PERSON> anda a<PERSON> a<PERSON>, her a<PERSON><PERSON> için farklı harita konfigürasyonları oluşturamazsınız.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Katmanı kaldırın", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Yakınlaştırmayı kaydet", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "destek makalesini", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Haritanın varsayılan yakınlaştırma düzeyidir. 1 ile 17 arasında bir değer kabul edilir; burada 1 tamamen uzaklaştırılmış (tüm dünya görünür) ve 17 tamamen yakınlaştırılmış (sokaklar ve binalar görünür) olur", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "T<PERSON>m kullanıcı verilerini anonimleştirin", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Kullanıcılardan gelen tüm anket girdileri kaydedilmeden önce anonimleştirilecektir", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Kullanıcıların yine de eri<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON>' sekmesi altındaki katılım gerekliliklerine uymaları gerekecektir. Kullanıcı profili verileri anket veri aktarımında mevcut olmayacaktır.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "<PERSON>u seçeneği etkinleştirirseniz, kullanıcı kayıt alanları kayıt sürecinin bir parçası olarak değil, anket<PERSON> son sayfası olarak gösterilecektir.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Anket formundaki demografik alanlar", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Ankette demografik alanları gösterin?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Bu makalede otomatik paylaşımın nasıl çalıştığı hakkında daha fazla bilgi edinin.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Sonuçları otomatik paylaş", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Oylama sonuçları platformda ve aşama sona erdiğinde katılımcılara e-posta yoluyla paylaşılır. Bu, var<PERSON><PERSON>lan olarak şeffaflık sağlar.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Bir yanıt seçeneği e<PERSON>in", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "<PERSON>ir oylama sorusu e<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "İptal", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "İptal", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "İptal", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Sil", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Sil", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "<PERSON><PERSON><PERSON> d<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "<PERSON><PERSON><PERSON> ka<PERSON>et", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "<PERSON>ylama sonuçlarını dışa aktar", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Maks<PERSON>um seçenek sayısı, seçenek sayısından fazladır", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Çoktan seçmeli", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Seçenek yok", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "<PERSON><PERSON><PERSON> soru<PERSON>ın cevap seçenekleri olmalıdır", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "<PERSON><PERSON><PERSON> bir <PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Oylamaya katılanların yalnızca bir seçeneği var", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "<PERSON><PERSON><PERSON>: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "<PERSON><PERSON>da oylama soru<PERSON> oluş<PERSON>bilir, her soru için katılımc<PERSON>ın yanıt seç<PERSON>rini ayarlayabilir, katıl<PERSON><PERSON><PERSON><PERSON>ların yalnızca bir yanıt se<PERSON> (tek seçenek) veya birden fazla yanıt <PERSON> (çoktan seçmeli) belirlemesine karar verebilir ve oylama sonuçlarını dışa aktarabilirsiniz. Tek bir oylama içinde birden fazla oylama sorusu oluşturabilirsiniz.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Tek seçenek", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "<PERSON><PERSON><PERSON> a<PERSON>ı ve sonuçları", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Yan<PERSON><PERSON><PERSON> maksimum", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "İthalat", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, et<PERSON><PERSON> e<PERSON>in veya yayınları bir sonraki proje aşamasına kopyalayın.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>, geri bildirim verin ve konuları atayın.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "<PERSON><PERSON><PERSON>şımı kapalı.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "<PERSON><PERSON><PERSON>, aşama kurulumunda değiştirmediğiniz sürece aşamanın sonunda paylaşılmayacaktır.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Bu sonuçlar aşama sona erdiğinde otomatik olarak paylaşılacaktır. Sonuçların paylaşılma zamanını değiştirmek için bu aşamanın bitiş tarihini değiştirin.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Anket sonuçlarını dışa aktar (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Sonuçlar", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "<PERSON><PERSON><PERSON>, bu proje kapsamındaki Typeform anketlerinin sonuçlarını Excel dosyası olarak indirebilirsiniz.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "<PERSON><PERSON> formu", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Anket <PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Anket", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Anket yanı<PERSON><PERSON>na bakın", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Emin misiniz?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Gönüllülerden neler istendiğini ve onların neler bekleyebileceklerini açıklamak için kullanın.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Form hatalar içerdiğinden kaydedilemedi.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "G<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Başlık", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Sil", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Amaç, katılımcıların gönüllü olabileceği bir eylem veya faaliyettir.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Bir açıklama ekleyin", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Bir ba<PERSON><PERSON><PERSON><PERSON> e<PERSON>in", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Gönüllüleri dışa aktar", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Amaç, katılımcıların gönüllü olabileceği bir eylem veya faaliyettir.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Burada, kullanıcıların gönüllü olabilecekleri amaçları ayarlayabilir ve gönüllüleri indirebilirsiniz.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Gönüllülük", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {gö<PERSON><PERSON><PERSON><PERSON> yok} one {# gönüll<PERSON>} other {# gön<PERSON>ll<PERSON>}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "bütçe tahsi̇si̇", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Seçeneklere bir bütçe atayın ve katılımcılardan toplam bütçeye uyan tercih ettikleri seçenekleri seçmelerini isteyin.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Bütçe tahsisi", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Kullanıcıların yorum yapmasına izin vermek oylama sürecini saptırabilir.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Seçeneklerin varsayılan görünümü", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Ku<PERSON>ı<PERSON><PERSON><PERSON> i<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Kullanıcıların hangi ek eylemleri gerçekleştirebileceğini seçin.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Sabit tutar", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu var<PERSON><PERSON><PERSON> o<PERSON> \"oy\" o<PERSON><PERSON>.", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "<b> {voteTypeDescription} </b> adresinin ne zaman kullanılacağı hakkında daha fazla bilgiyi {optionAnalysisArticleLink}adresimizde bulabilirsiniz.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Seçenek başına maksimum oy", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "<PERSON><PERSON><PERSON><PERSON> oy miktarı", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Bir kullanıcının toplamda verebileceği oy sayısını sınırlayabilirsiniz (seçenek başına en fazla bir oy olacak şekilde).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "seçenek başına birden fazla oy", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Kullanıcılara seçenekler arasında dağıtmaları için bir miktar jeton verilir", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Seçenek başına birden fazla oy", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Kullanıcı başına oy sayısı", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Seçenek analizine genel bakış", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Oylanacak seçenekler", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Nokta", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "seçenek başına tek oy", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Kullanıcılar seçeneklerden herhangi birini onaylamayı seçebilir", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Her seçenek için bir oy", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Sınırsız", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Bir oylamanın adı ne olmalıdır?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "<PERSON><PERSON><PERSON><PERSON>, puanlar, karbon kredileri...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "<PERSON><PERSON><PERSON><PERSON> jeton, puan, karbon kredisi...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Her oylama yönteminin farklı ön konfigürasyonları vardır", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "<PERSON><PERSON><PERSON>, kullanıcıların nasıl oy kullanacağına ilişkin kuralları belirler", "app.containers.AdminPage.ProjectEdit.addNewInput": "<PERSON><PERSON> giri<PERSON>", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Projenizi şimdi bir klasöre ekleyebilir veya bunu daha sonra proje ayarlarından yapabilirsiniz", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.altText": "Alt metin", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "<PERSON><PERSON><PERSON> oyla<PERSON>", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Etkinleştirildiğinde kimin neye oy verdiğini görmek mümkün olmaz. Kullanıcıların yine bir hesapları olması gerekir ve yalnızca bir kez oy kullanabilirler.", "app.containers.AdminPage.ProjectEdit.approved": "Onaylandı", "app.containers.AdminPage.ProjectEdit.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Arşivlenen projeler hala gö<PERSON>ü<PERSON>bilir, ancak daha fazla katılıma izin vermez", "app.containers.AdminPage.ProjectEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Bu alan silinemez çünkü aşağıdaki daha özel sayfa(lar)da projeleri görüntülemek için kullanılmaktadır. Alanı silebilmeniz için önce alanın sayfayla bağlantısını kaldırmanız veya sayfayı silmeniz gerekir.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Proje her alan filtres<PERSON>e gö<PERSON>ili<PERSON>.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "<PERSON>", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projeler ana sayfada alanlar kullanılarak filtrelenebilir. Alanlar {areasLabelTooltipLink} olarak ayarlanabilir.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "buraya", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "<PERSON><PERSON><PERSON> bir alan yok", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Alana göre filtreleme yapıldığında bu proje gösterilmez.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "<PERSON>je seçilen alan filtrelerinde gösterilir.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "En çok tartışılanlar", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Anket içeriği ekleme", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Bu ankete başvurular gelmeye başlamıştır. Ankette yapılan değişiklikler veri kaybına ve dışa aktarılan dosyalarda eksik verilere neden olabilir.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Anket başar<PERSON><PERSON> kaydedildi", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Anket", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Bir oylama yöntemi seçin ve kullanıcıların birkaç farklı seçenek arasında önceliklendirme yapmasını sağlayın.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Bir oylama veya önceliklendirme çalışması yapın", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Şablon kullanarak bir proje oluşturun", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Create an external survey", "app.containers.AdminPage.ProjectEdit.createInput": "<PERSON><PERSON> girdi e<PERSON>me", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Platform içi bir anket oluşturun", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Platformumuzdan ayrılmadan bir anket oluşturun.", "app.containers.AdminPage.ProjectEdit.createPoll": "Bir oylama o<PERSON>ştur<PERSON>", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Çoktan seçmeli bir anket hazırlayın.", "app.containers.AdminPage.ProjectEdit.createProject": "<PERSON><PERSON> proje", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Bir Typeform, Google Form, Enalyzer, SurveyXact, Qualtrics, SmartSurvey, Snap Survey veya Microsoft Forms anketi yer<PERSON>ş<PERSON>rin.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Yayınların ana proje sayfasında görüntülenecekleri varsayılan sırayı ayarlayabilirsiniz.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sıralama", "app.containers.AdminPage.ProjectEdit.departments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.descriptionTab": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "<PERSON><PERSON>, beğenmemeyi etkinleştirir veya devre dışı bırakır, ancak beğenme yine de etkin olacaktır. Bir seçenek analizi yapmadığınız sürece bunu devre dışı bırakmanızı öneririz.", "app.containers.AdminPage.ProjectEdit.disabled": "<PERSON><PERSON> dı<PERSON>ı", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Katılımcı başına beğenmeme sayı<PERSON>ı", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Beğenmemeyi etkinleştir", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Bir belge hakkında geri bildirim top<PERSON>a", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Konveio ile interaktif bir PDF yerleştirin ve yorum ve geri bildirim toplayın.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "<PERSON><PERSON> dı<PERSON>ı", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "<PERSON><PERSON><PERSON> proje<PERSON>, yöneticiler ve atanmış proje yöneticileri dışındaki tüm kişiler için gizlidir.", "app.containers.AdminPage.ProjectEdit.draft": "Taslak", "app.containers.AdminPage.ProjectEdit.draftStatus": "Taslak", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Kullanıcıların hangi katılımcı eylemleri gerçekleştirebileceğini seçin.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Ekler (maksimum 50 MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Dosyalar 50 MB'tan büyük olmamalıdır. Eklenen dosyalar proje bilgileri sayfasında gösterilir.", "app.containers.AdminPage.ProjectEdit.filesTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bulun", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Katılımcılardan faaliyetler ve amaçlar için gönüllü olmalarını isteyin.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Bir klasör yöneticisi olarak, projeyi oluştururken bir klasör seçebilirsiniz, ancak daha sonra bunu yalnızca bir yönetici değiştirebilir", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Klasör kartı resmi alternatif metni", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Bu projeyi eklemek için bir klasör seçin.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "<PERSON>zel içerik", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Bu forma başvurular gelmeye başlamıştır. Formda yapılacak değişiklikler veri kaybına ve dışa aktarılan dosyalarda eksik verilere neden olabilir.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Form başarıyla kaydedildi", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "<PERSON><PERSON> sonu", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Bir <PERSON>", "app.containers.AdminPage.ProjectEdit.generalTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Başlık resmi alt metni", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Başlık resmi", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "YENİ", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Kullanıcılara bilgi verin veya geçmiş aşamalara ilişkin sonuçları paylaşmak için rapor oluşturucuyu kullanın.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Bilgi veya sonuç <PERSON>ı", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Girdi ve geri bildirim top<PERSON>n", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "<PERSON><PERSON><PERSON><PERSON>, tep<PERSON><PERSON> ve/veya yorumlar oluşturun veya toplayın. Farklı girdi türleri arasından seçim yapın: <PERSON><PERSON><PERSON>, se<PERSON><PERSON><PERSON> analizi, soru<PERSON>c<PERSON><PERSON>, sorun tanımlama ve daha fazlası.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "<PERSON><PERSON>ı<PERSON>ların işlenmesinden kim sorumludur?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Bu projedeki tüm yeni girdiler bu kişiye atanacaktır. Atanan kişi {ideaManagerLink} yo<PERSON><PERSON>tirilebilir.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yorum yapma", "app.containers.AdminPage.ProjectEdit.inputFormTab": "<PERSON><PERSON><PERSON> formu", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "gir<PERSON>", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> tepki verme", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Varsayılan görünüm", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "G<PERSON><PERSON> görüntülemek için varsayılan görünümü seçin: ızgara görünümünde kartlar veya harita üzerinde iğneler. Katılımcılar iki görünüm arasında manuel olarak geçiş yapabilirler.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Konveio URL'sini gömün", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Number of likes per participant", "app.containers.AdminPage.ProjectEdit.limited": "Sınırlı", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "<PERSON><PERSON> fazla <PERSON>", "app.containers.AdminPage.ProjectEdit.mapDisplay": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxLikes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Seçenek başına maksimum oy sayısı toplam oy sayısına eşit veya daha az olmalıdır", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Katılımcılar sepetlerini gönderirken bu bütçeyi aşamazlar.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Katılımcıların sepetlerini göndermek için asgari bir bütçeyi karşılamalarını zorunlu kılın (asgari bir bütçe belirlemek istemiyorsanız '0' girin).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "Yardım Merkezimizi ziyaret edin", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Proje yöneticileri kimlerdir?", "app.containers.AdminPage.ProjectEdit.moreDetails": "<PERSON><PERSON> fazla bilgi", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "İlhama mı ihtiyacınız var? {inspirationHubLink}adresindeki diğer şehirlerden benzer projeleri keşfedin", "app.containers.AdminPage.ProjectEdit.newContribution": "Bir katkı ekleyin", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newInitiative": "<PERSON><PERSON> g<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newIssue": "<PERSON>ir sorun e<PERSON>in", "app.containers.AdminPage.ProjectEdit.newOption": "Bir seçenek ekleyin", "app.containers.AdminPage.ProjectEdit.newPetition": "Dilekçe ekleyin", "app.containers.AdminPage.ProjectEdit.newProject": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newProposal": "<PERSON><PERSON> tekli<PERSON> e<PERSON>", "app.containers.AdminPage.ProjectEdit.newQuestion": "<PERSON>ir soru e<PERSON>in", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "En yeni", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Geçerli bir tutar <PERSON>ğ<PERSON>", "app.containers.AdminPage.ProjectEdit.noFolder": "Klasör yok", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "- Klasör yok -", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Şablon bulunamadı", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Lütfen bir proje başlığı girin", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Geçerli bir sayı <PERSON>il", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "En eski", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Sadece yönetici tarafından görülebilir", "app.containers.AdminPage.ProjectEdit.optionNo": "Hay<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionYes": "<PERSON><PERSON> (klasör <PERSON>)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Katılım seviyeleri", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Ne yapmak istiyorsunuz?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Kullanıcıların katılma biçim<PERSON> seçin.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Her bir eylemi kimin gerçekleştirebileceğini belirleyebilir ve daha fazla bilgi toplamak için katılımcılara ek sorular sorabilirsiniz.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Katılımcı gereksinimleri & sorular", "app.containers.AdminPage.ProjectEdit.pendingReview": "<PERSON><PERSON> be<PERSON>", "app.containers.AdminPage.ProjectEdit.permissionsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.pollTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Proje kartı g<PERSON>", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Bu görsel proje kartının bir par<PERSON>sıdır; projeyi özetleyen ve örneğin ana sayfada gösterilen kart.\n\n    Önerilen görüntü çözünürlükleri hakkında daha fazla bilgi için, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Klasör", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Bu görüntü proje sayfasının en üstünde gösterilir.\n\n    Önerilen görüntü çözünürlükleri hakkında daha fazla bilgi için, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Proje kartı görseli alternatif metin", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "G<PERSON>rme engelli kullanıcılar için görüntünün kısa bir açıklamasını sağlayın. Bu, ekran okuyucuların görüntünün ne hakkında olduğunu iletmesine yardımcı olur.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Proje yöneticileri projeleri düzenleyebilir, ya<PERSON><PERSON>nları yönetebilir ve katılımcılara e-posta gönderebilir. Proje yöneticilerine atanan haklar hakkında daha fazla bilgi edinmek için {moderationInfoCenterLink}.", "app.containers.AdminPage.ProjectEdit.projectName": "<PERSON><PERSON> adı", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Zaman çizelgesi olan projelerin net bir başlangıcı ve sonu vardır ve bu projelerin farklı aşamaları olabilir. Zaman çizelgesi olmayan projeler süreklidir.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "<PERSON><PERSON> türü daha sonra değiştirilemez.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Projeyi belirli kullanıcılara görünmez olacak şekilde ayarlayabilirsiniz.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Proje durumunu mu arıyorsunuz? Artık istediğiniz zaman doğrudan proje sayfası başlığından değiştirebilirsiniz.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Yayınlanan projeler herkes veya seçildiyse bir grup alt kümesi tarafından görülebilir.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Yayımlandı", "app.containers.AdminPage.ProjectEdit.purposes": "Amaçlar", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Katılım verilerini sıfırlayın", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Verileriniz kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Başarılı!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Formunuz kaydedildi!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Şablonlarda a<PERSON> ya<PERSON>ın", "app.containers.AdminPage.ProjectEdit.selectGroups": "Grup seçin", "app.containers.AdminPage.ProjectEdit.setup": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Surveys", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Projenizi ayarlayın ve kişiselleştirin.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "destek merkezimizi ziyaret edin", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Anket içeriği ekleme", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "İptal", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}({choiceCount, plural, no {# seçimler} one {# seçim} other {# seçimler}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, gitmek istiyorum.", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Bu ankete başvurular gelmeye başlamıştır. Ankette yapılan değişiklikler veri kaybına ve dışa aktarılan dosyalarda eksik verilere neden olabilir.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "İthalat", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "İthalat", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "<PERSON><PERSON><PERSON> cevap, u<PERSON>n cevap ve duyarlılık ölçeği takip soruları için yapay zeka özetlerine sol kenar çubuğundaki yapay zeka sekmesinden erişilebilir.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Doğrusal ölçek", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "<PERSON><PERSON><PERSON> cevap", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Çoktan seçmeli - birçok seçim yapın", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Görüntü seçimi - çok sayıda seçin", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Henüz anket yanıtı yok", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Yanıtlara açık", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Yanıtlara açık", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Opsiyonel", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Her<PERSON><PERSON> bir mantık eklenmezse, anket normal akışını izleyecektir. He<PERSON> sayfanın hem de soruların mantığı varsa, soru mantığı öncelikli olacaktır. <PERSON>unun amaçladığınız anket akışıyla uyumlu olduğundan emin olun. Daha fazla bilgi için {supportPageLink}adresini ziyaret edin.", "app.containers.AdminPage.ProjectEdit.survey.point": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Gitmek istediğine emin misin?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Mevcut değişiklikleriniz kaydedilmeyecektir.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Sıralama", "app.containers.AdminPage.ProjectEdit.survey.rating": "Değerlendirme", "app.containers.AdminPage.ProjectEdit.survey.required2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {yanıtlar} one {yanıtlar} other {yanıtlar}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# yanıtlar} one {# yanıt} other {# yanıtlar}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Çok<PERSON> se<PERSON>meli - <PERSON><PERSON> seçin", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Duyarlılık doğrusal ölçeği", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Anket başar<PERSON><PERSON> kaydedildi", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Anket", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Anket yanıtları", "app.containers.AdminPage.ProjectEdit.survey.text2": "<PERSON><PERSON><PERSON> c<PERSON>p", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Toplam {count} yanıtlar", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "G<PERSON>rü<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL'<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyService": "Hizmet", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Bir anketin nasıl yerleştirileceği hakkında daha fazla bilgiyi {surveyServiceTooltipLink} adresinde bulabilirsiniz.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "buraya", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Bu etiket silinemez çünkü aşağıdaki daha özel sayfa(lar)da projeleri görüntülemek için kullanılmaktadır. \nEtiketi silebilmeniz için önce etiketin sayfayla bağlantısını kaldırmanız veya sayfayı silmeniz gerekir.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "<PERSON><PERSON> <PERSON><PERSON><PERSON> genel <PERSON>", "app.containers.AdminPage.ProjectEdit.titleLabel": "Başlık", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "<PERSON><PERSON><PERSON>, il<PERSON> ve net bir başlık seçin. <PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>, açılan genel görünümde ve ana sayfadaki proje kartlarında gösterilecektir.", "app.containers.AdminPage.ProjectEdit.topicLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Bu proje için {topicsCopy} seçin. Kullanıcılar bunları projeleri filtreleme ölçütü olarak kullanabilir.", "app.containers.AdminPage.ProjectEdit.totalBudget": "<PERSON><PERSON> bütçe", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Atanmamış", "app.containers.AdminPage.ProjectEdit.unlimited": "Sınırsız", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>an", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Gönüllülük", "app.containers.AdminPage.ProjectEdit.voteTermError": "<PERSON><PERSON>ma <PERSON> tüm yerel ayarlar için belirtilmelidir", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# grup görüntüleyebilir} one {# grup görüntüleyebilir} other {# grup görüntüleyebilir}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Bir et<PERSON> ekleyin", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Ek bilgi", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adres 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Etkinlik yerinin sokak adresi", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adres 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Örn. Apt, Süit, Bina", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "<PERSON>a adı, kat numarası vb. gibi konumu tanımlamaya yardımcı olabilecek ek adres bilgileri.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Destek makalesine bakın", "app.containers.AdminPage.ProjectEvents.customButtonLink": "D<PERSON>ş bağlantı", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Harici bir URL'ye bağlantı ekleyin (Örn. Etkinlik hizmeti veya biletleme web sitesi). <PERSON><PERSON><PERSON>, var<PERSON><PERSON><PERSON> katılım düğmesi davranışını geçersiz kılacaktır.", "app.containers.AdminPage.ProjectEvents.customButtonText": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Harici bir URL ayarlandığında düğ<PERSON> metnin<PERSON> \"<PERSON><PERSON><PERSON>\" dışında bir değere ayarlayın.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Başlangıç", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Bitiş", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Sil", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Bu etkinliği silmek istediğinizden emin misiniz? Bu işlem geri alınamaz!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Etkinlik açıklaması", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "<PERSON>tkinliğ<PERSON>", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "<PERSON><PERSON>t yaptıranlara doğrudan platformdan e-posta göndermek için yöneticilerin {userTabLink} sekmesinde bir kullanıcı grubu oluşturması gerekir. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Etkinlik tarihleri", "app.containers.AdminPage.ProjectEvents.eventImage": "Etkinlik görüntüsü", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Etkinlik görseli alternatif metni", "app.containers.AdminPage.ProjectEvents.eventLocation": "Etkinlik yeri", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "İhracat kayıtlıları", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Ekler (maksimum 50 MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Ekler etkinlik açıklamasının altında gösterilir.", "app.containers.AdminPage.ProjectEvents.locationLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "<PERSON><PERSON><PERSON><PERSON> kayı<PERSON>ı kişi", "app.containers.AdminPage.ProjectEvents.newEventTitle": "<PERSON><PERSON> bir et<PERSON> oluşturun", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Çevrimiçi etkinlik bağlantısı", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Etkinliğ<PERSON>z <PERSON>e, buraya bir bağlant<PERSON> ekleyin.", "app.containers.AdminPage.ProjectEvents.preview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "<PERSON><PERSON> konum<PERSON>u da<PERSON>n", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Harita ü<PERSON>inde konumu has<PERSON>ştır", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Aşağıdaki haritaya tıklayarak etkinlik konumu işaretleyicinizin gösterildiği yeri daraltabilirsiniz.", "app.containers.AdminPage.ProjectEvents.register": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.registerButton": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.registrant": "KAYITLI", "app.containers.AdminPage.ProjectEvents.registrants": "KAYITLI", "app.containers.AdminPage.ProjectEvents.registrationLimit": "<PERSON><PERSON>t sınırı", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Yaptığı<PERSON><PERSON>z değişiklikleri kaydedemedik, lütfen tekrar deneyin.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Başarılı!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Bir konum arayın", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Yaklaşan etkinlikleri bu projelere bağlayın ve projenin etkinlik sayfasında gösterin.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Başlık ve tarihler", "app.containers.AdminPage.ProjectEvents.titleEvents": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.titleLabel": "Etkinlik adı", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Düğmeyi harici bir URL'ye bağlama", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Varsayılan olarak, kullanıcıların bir etkinliğe kaydolmasına olanak tanıyan platform içi etkinlik kayıt düğmesi gösterilecektir. Bunun yerine harici bir URL'ye bağlantı vermek için bunu değiştirebilirsiniz.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Etkinliğe kayıt yaptıran<PERSON>ın sayısını sınırlayın", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Maks<PERSON>um etkinlik kayıt sayısı belirleyin. Sınıra ulaşılırsa, başka kayıt kabul edilmeyecektir.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/kullan<PERSON>lar", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Projenize dosya e<PERSON>me", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> proje<PERSON>, aşamalara ve etkinliklere ekleyin.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Sensemaking'e ba<PERSON><PERSON> olarak <PERSON>ya e<PERSON>in", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Bağlam ve içgörü sağlamak için Sensemaking projenize dosyalar ekleyin.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Çok yakında", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Anketleri senkronize edin, g<PERSON>rüşmeleri yükleyin ve yapay zekanın verilerinizdeki noktaları birleştirmesine izin verin.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "<PERSON><PERSON><PERSON> bir <PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Dosyaları analiz etmek için yapay zekayı kullanın", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Transkriptleri v<PERSON>. <PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Yapay zeka destekli içgörüler", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Önemli konuları ortaya çıkarmak için yüklenen dosyaları analiz edin.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Yapay zeka işleme kullanarak bu dosyaların gelişmiş analizine izin verin.", "app.containers.AdminPage.ProjectFiles.askButton": "Sor", "app.containers.AdminPage.ProjectFiles.categoryLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Dosyaları seçin", "app.containers.AdminPage.ProjectFiles.close": "Ka<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Onaylayın ve yükleyin", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Bu dosyayı silmek istediğinizden emin misiniz?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Markdown dosyası yüklenemedi.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "CSV önizlemesi yüklenemedi.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "CSV önizlemelerinde maksimum 50 satır gösterilir.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV dosyası önizleme için çok büyük.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "<PERSON><PERSON><PERSON> silme", "app.containers.AdminPage.ProjectFiles.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "Dosyayı indirin", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "<PERSON> indirin", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Herhangi bir dosyayı buraya sürükleyip bırakın veya", "app.containers.AdminPage.ProjectFiles.editFile": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Dosya adı nokta içermeyebilir.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "<PERSON><PERSON>a adı gereklidir.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Dosyayı indirin", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Bu dosya maksimum 50 MB sınırını aştığı için yüklenmeyecektir.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "<PERSON><PERSON><PERSON> başarıyla yüklendi", "app.containers.AdminPage.ProjectFiles.info_sheet": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "Örn. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "<PERSON><PERSON><PERSON>, Belediye Binası kayıtları", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Örneğin PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "<PERSON><PERSON><PERSON>, bilgilendirici belgeler", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Örn. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Bir seferde en fazla {maxFiles} dosya yükleyebilirsiniz.", "app.containers.AdminPage.ProjectFiles.meeting": "Toplantı", "app.containers.AdminPage.ProjectFiles.noFilesFound": "<PERSON><PERSON><PERSON> b<PERSON>.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Politika", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Önizleme bu dosya türü i<PERSON>in henüz desteklenmiyor.", "app.containers.AdminPage.ProjectFiles.report": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.retryUpload": "<PERSON><PERSON><PERSON><PERSON><PERSON> yeniden dene", "app.containers.AdminPage.ProjectFiles.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.selectFileType": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Stratejik plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Bir seferde en fazla {maxFiles} dosya yükleyebilirsiniz.", "app.containers.AdminPage.ProjectFiles.unknown": "Bilinmiyor", "app.containers.AdminPage.ProjectFiles.upload": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# dosya} other {# dosyalar}} ba<PERSON><PERSON><PERSON><PERSON>, {numberOfErrors, plural, one {# hata} other {# hatalar}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Dosyayı görü<PERSON>üle", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "<PERSON><PERSON><PERSON> al<PERSON> da<PERSON>t", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.enabled": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "<PERSON>u alanı dahil edin.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "<PERSON><PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "<PERSON><PERSON>m alanları genişlet", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "<PERSON><PERSON><PERSON> formu", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Hangi bilgilerin sağlanması gerektiğini belirtin, katılımcı yanıtlarını yönlendirmek için kısa açıklamalar veya talimatlar ekleyin ve her alanın isteğe bağlı mı yoksa gerekli mi olduğunu belirtin.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Hangi bilgilerin sağlanması gerektiğini belirtin, katılımcı yanıtlarını yönlendirmek için kısa açıklamalar veya yönergeler ekleyin ve her alanın isteğe bağlı mı yoksa zorunlu mu olduğunu belirtin", "app.containers.AdminPage.ProjectIdeaForm.required": "Zorun<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "<PERSON>u alanın doldurulmasını zorunlu kılın.", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.ProjectIdeaForm.saved": "<PERSON><PERSON><PERSON><PERSON>!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Otomatik e-postalar", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Aşama düzeyinde tetiklenen e-postaları yapılandırabilirsiniz", "app.containers.AdminPage.ProjectTimeline.datesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "An<PERSON><PERSON> katılın", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Anket", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Bu aşamayı silmek istediğinizden emin misiniz?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Aşama açıklaması", "app.containers.AdminPage.ProjectTimeline.disabledProjectPhaseEmailMessage": "Bu seçenek şu anda {automatedEmailsLink} sayfasındaki tüm projeler için kapalıdır. <PERSON><PERSON><PERSON>, bu aşama için bu ayarı tek tek değiştiremezsiniz.", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Aşamayı düzenleyin", "app.containers.AdminPage.ProjectTimeline.endDate": "Bitiş tarihi", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Bitiş Tarihi", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Ekler (maksimum 50 MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "<PERSON><PERSON> bir a<PERSON><PERSON> o<PERSON>ş<PERSON>", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Bu aşamanın önceden tanımlanmış bir bitiş tarihi yoktur.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Bazı yöntemlerin sonuç <PERSON>ı (oylama sonuçları gibi) bir bitiş tarihi seçilene kadar tetiklenmeyecektir.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "B<PERSON>n sonra bir a<PERSON>ama <PERSON>, bu aş<PERSON>ya bir bitiş tarihi ekleyecektir.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "<PERSON><PERSON><PERSON> için bir bitiş tarihi seçilmemesi şu anlama gelir:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Değişiklikleri kaydet", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Form gönderilirken bir hata <PERSON>, lütfen tekrar den<PERSON>in.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "<PERSON><PERSON><PERSON><PERSON>!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.ProjectTimeline.startDate": "Başlangıç tarihi", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Başlangıç tarihi", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Anket başlığı", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Aşama adı", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminoloji (ana sayfa filtresi)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Ön sayfa filtresindeki etiketler nasıl adlandırılmalıdır? Ö<PERSON>. et<PERSON><PERSON>, ka<PERSON><PERSON><PERSON>, departman vb.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Etiketler {topicManagerLink} üzerinden yapılandırılabilir.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "buraya", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Tek etiket için k<PERSON>ılan terim (tekil)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "et<PERSON>t", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Birden çok etiket için k<PERSON> terim (çoğul)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Yeni bir kayıt alanı e<PERSON>in", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Seçenek ekleyin", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Bir yanıt biçimi <PERSON>ğlayın", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "<PERSON><PERSON><PERSON> diller için bir yanıt seçeneği sağlayın", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "<PERSON><PERSON>t seçeneğini kaydet", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "<PERSON><PERSON><PERSON> seçeneği başarıyla kaydedildi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Kayıt formunda görünecekleri sırayı belirlemek için alanları sürükleyip bırakın.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "<PERSON>ars<PERSON><PERSON><PERSON> alan", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Sil", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Kayıt formunda alan adının altında isteğe bağlı olarak gösterilen metin.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "İkamet yeri için yanıt seçenekleri {geographicAreasTabLink} sekmesinde belirlenebilir.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON><PERSON> seç<PERSON>ini dü<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "<PERSON><PERSON><PERSON> diller i<PERSON>in bir alan adı girin", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (onay kutusu)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "<PERSON><PERSON><PERSON> yanıt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "<PERSON><PERSON><PERSON> (birden fazla se<PERSON>)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "<PERSON><PERSON><PERSON> (<PERSON><PERSON> se<PERSON>)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "<PERSON><PERSON><PERSON> yanıt", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Bu alanın yanıtlanması zorunlu hale getirilsin mi?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON><PERSON> seçeneği e<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "İptal", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Sil", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Bu kayıt sorusu cevap seçeneğini silmek istediğinizden emin misiniz? Belirli kullanıcıların bu seçenekle yanıtladığı tüm kayıtlar kalıcı olarak silinecektir. Bu işlem geri alınamaz.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Bu kayıt sorusunu silmek istediğinizden emin misiniz? Kullanıcıların bu soruya verdikleri tüm yanıtlar kalıcı olarak silinecek ve artık projelerde veya tekliflerde sorulmayacaktır. Bu işlem geri alınamaz.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Zorun<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Alanı kaydet", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.addAreaButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.addTopicButton": "Etiket ekleyin", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "<PERSON>van <PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Kullanıcı - <PERSON><PERSON><PERSON><PERSON> 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Projeleri onaylamak için hangi yöneticilerin bildirim alacağını seçin. Klasör Yöneticileri varsayılan olarak kendi klasörlerindeki tüm projeler için onay<PERSON>ı<PERSON>ıdır.", "app.containers.AdminPage.SettingsPage.approvalSave": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.approvalTitle": "<PERSON><PERSON> onay a<PERSON>ı", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Bu alanı silmek istediğinizden emin misiniz?", "app.containers.AdminPage.SettingsPage.areaTerm": "Tek alan i<PERSON>in k<PERSON> terim (tekil)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "alan", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Sil", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Birden çok alan i<PERSON> k<PERSON> terim (çoğul)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "En az bir dil seçin.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatarlar", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Avatarları görüntüle", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Kayıtlı olmayan ziyaretçilere katılımcıların profil resimlerini ve sayılarını göster", "app.containers.AdminPage.SettingsPage.bannerHeader": "Başlık metni", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Kayıtlı olmayan ziyaretçiler için ba<PERSON><PERSON><PERSON>k metni", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Kayıtlı olmayan ziyaretçiler için alt başlık metni", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Alt başlık metni", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.brandingDescription": "Logonuzu ekleyin ve platform renklerini ayarlayın.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Platform marka kimliği", "app.containers.AdminPage.SettingsPage.cancel": "İptal", "app.containers.AdminPage.SettingsPage.chooseLayout": "D<PERSON>zen", "app.containers.AdminPage.SettingsPage.color_primary": "<PERSON>nk", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON><PERSON><PERSON> renk", "app.containers.AdminPage.SettingsPage.color_text": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.colorsTitle": "Ren<PERSON>r", "app.containers.AdminPage.SettingsPage.confirmHeader": "<PERSON>u etiketi silmek istediğinizden emin misiniz?", "app.containers.AdminPage.SettingsPage.contentModeration": "İçerik moderasyonu", "app.containers.AdminPage.SettingsPage.ctaHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Özel sayfa başlığı | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı", "app.containers.AdminPage.SettingsPage.defaultTopic": "Varsayılan etiket", "app.containers.AdminPage.SettingsPage.delete": "Sil", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Sil", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, etiketi mevcut tüm yayınlardan siler. Bu değişiklik tüm projeler için geçerli olur.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Platformunuzda yayınları kategorize etmek için kullanmak istediğiniz etiketleri ekleyin ve silin. Etiketleri {adminProjectsLink} üzerindeki belirli projelere ekleyebilirsiniz.", "app.containers.AdminPage.SettingsPage.desktop": "Masaüstü", "app.containers.AdminPage.SettingsPage.editFormTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Etiketi düzenleyin", "app.containers.AdminPage.SettingsPage.fieldDescription": "<PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Bu açıklama yalnızca dahili iş birliği amacıyla kullanılır ve kullanıcılara gösterilmez.", "app.containers.AdminPage.SettingsPage.fieldTitle": "<PERSON>", "app.containers.AdminPage.SettingsPage.fieldTitleError": "<PERSON><PERSON><PERSON> diller i<PERSON>in bir alan adı girin", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Alanlar için se<PERSON>, bir kayıt alanı seçeneği olarak ve ana sayfada projeleri filtrelemek için kullanılabilir.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Etiketi ka<PERSON>et", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Etiket adı", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "<PERSON><PERSON><PERSON> diller için bir etiket adı girin", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Etiketler için seçtiğiniz adlar platform kullanıcıları tarafından görülebilir", "app.containers.AdminPage.SettingsPage.fixedRatio": "Sabit oran", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Bu banner türü, metin, logo veya vatandaşlarınız için çok önemli olan belirli öğeler içeren görüntüler gibi kırpılmaması gereken görüntülerle en iyi şekilde çalışır. Bu banner, kullanıcılar oturum açtığında ana renkte düz bir kutu ile değiştirilir. Bu rengi genel ayarlardan ayarlayabilirsiniz. Önerilen resim kullanımı hakkında daha fazla bilgiyi {link}adresimizde bulabilirsiniz.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "BİLGİ TABANI", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON> geni<PERSON> bant", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Bu banner, harika bir görsel etki için tüm genişlik boyunca uzanır. G<PERSON>rüntü mümkün olduğunca fazla alanı kaplamaya çalışacak ve bu da her zaman görünür olmamasına neden olacaktır. Bu banner'ı istediğiniz renkte bir kaplama ile birleştirebilirsiniz. Önerilen görsel kullanımı hakkında daha fazla bilgiyi {link}adresimizde bulabilirsiniz.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "BİLGİ TABANI", "app.containers.AdminPage.SettingsPage.header": "Ana sayfa bandı", "app.containers.AdminPage.SettingsPage.headerDescription": "Ana sayfa bandı görselini ve metnini özelleştirin.", "app.containers.AdminPage.SettingsPage.header_bg": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.helmetDescription": "Yönetici ayarları sayfası", "app.containers.AdminPage.SettingsPage.helmetTitle": "Yönetici ayarları sayfası", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "<PERSON> sayfanın alt kısmındaki özelleştirilebilir bölüme kendi içeriklerinizi ekleyin.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Ana sayfa b<PERSON>ğı | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Bindirme görseli rengi", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Bindirme görseli opaklığı", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Uygunsuz içeriği tespit edin", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Platformda yayınlanan uygunsuz içerikleri otomatik olarak tespit edin.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Bu özellik etkin olduğu sü<PERSON>, kat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarafından gönderilen girdi, teklif ve yorumlar otomatik olarak incelenecektir. Potansiyel olarak uygunsuz içerik içerdiği için işaretlenen gönderiler engellenmeyecek, ancak {linkToActivityPage} sayfasında incelenmek üzere vurgulanacaktır.", "app.containers.AdminPage.SettingsPage.languages": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Platformunuzu kullanıcılara sunmak istediğiniz birden fazla dil seçebilirsiniz. Seçilen her dil için içerik oluşturmanız gerekecektir.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "E<PERSON>kin<PERSON>", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Lütfen bir başlık görseli yükleyin", "app.containers.AdminPage.SettingsPage.no_button": "Düğme yok", "app.containers.AdminPage.SettingsPage.organizationName": "<PERSON><PERSON> veya kuruluşun adı", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "<PERSON><PERSON><PERSON> diller i<PERSON>in bir kuruluş adı veya şehir belirtin.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "<PERSON><PERSON>ımını etkinleştir", "app.containers.AdminPage.SettingsPage.phone": "Telefon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Platform yapılandırması", "app.containers.AdminPage.SettingsPage.population": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.populationMinError": "<PERSON><PERSON><PERSON><PERSON> pozitif bir sayı olmalıdır.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Bölgenizdeki toplam nüfus sayısı. Bu, katılım oranını hesaplamak için kullanılır. Geçerli değilse boş bırakın.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "En sık bildirilen saldırgan kelimeleri içeren girdileri, önerileri ve yorumları engelleyin", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "<PERSON>u metin ana sayfada projelerin üzerinde gösterilir.", "app.containers.AdminPage.SettingsPage.projectsSettings": "proje ayarları", "app.containers.AdminPage.SettingsPage.projects_header": "<PERSON><PERSON><PERSON> başlı<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.registrationFields": "<PERSON><PERSON><PERSON> al<PERSON>", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Kayıt formunuzun üst kısmına kısa bir açıklama ekleyin.", "app.containers.AdminPage.SettingsPage.registrationTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.save": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "Alanı kaydet", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "<PERSON><PERSON> so<PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Başarılı!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Onaylayıcıları seçin", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Kullanıcılara kayıttan sonra takip etmeleri için gösterilecek alanları seçin", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Kullanıcılara kayıttan sonra takip etmeleri için gösterilecek konuları seçin", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Kaydedilemedi. Ayarı değiştirmeyi tekrar deneyin.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Kaydolu<PERSON>\"", "app.containers.AdminPage.SettingsPage.signed_in": "Kayıtlı ziyaretçiler için d<PERSON>", "app.containers.AdminPage.SettingsPage.signed_out": "Kayıtlı olmayan z<PERSON>retçiler için dü<PERSON>", "app.containers.AdminPage.SettingsPage.signupFormText": "<PERSON><PERSON>t yardımı metni", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "<PERSON><PERSON>t formunun üst kısmına kısa bir açıklama ekleyin.", "app.containers.AdminPage.SettingsPage.statuses": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.step1": "E-posta ve parola adımı", "app.containers.AdminPage.SettingsPage.step1Tooltip": "<PERSON><PERSON>, kayıt formunun ilk sayfasının üst kısmında gösterilir (ad, e-posta, parola).", "app.containers.AdminPage.SettingsPage.step2": "<PERSON><PERSON>t soruları adımı", "app.containers.AdminPage.SettingsPage.step2Tooltip": "<PERSON><PERSON>, kayıt formunun ikinci sayfasının üst kısmında gösterilir (diğer kayıt alanları).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Platformunuz için kullanmak istedi<PERSON><PERSON><PERSON>, semtler veya ilçeler gibi coğrafi alanları tanımlayın. Bu coğrafi alanları projelerle ilişkilendirebilir (açılış sayfasında filtrelenebilir) veya katılımcılardan Akıllı Gruplar oluşturmak ve erişim haklarını tanımlamak için bir kayıt alanı olarak ikamet ettikleri bölgeyi seçmelerini isteyebilirsiniz.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Kuruluşunuzun adının insanlara nasıl görünt<PERSON>leneceğini belirleyin, platformunuzun dillerini seçin ve web sitenize bağlantı sağlayın.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Sağlanan alt yazı izin verilen maksimum karakter sınırını (90) aşıyor", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminoloji", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "<PERSON><PERSON>ş<PERSON>", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabPolicies": "Politikalar", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabRegistration": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabSettings": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Projeleriniz için hangi coğrafi birimi kullanmak istediğinizi tanımlayın (mahalleler, ilçeler, semtler vb.)", "app.containers.AdminPage.SettingsPage.titleAreas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.titleBasic": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Sağlanan başlık izin verilen maksimum karakter sınırını (35) aşıyor", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Etiket yöneticisi", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Bu banner özellikle başlık, alt başlık veya düğmedeki metinle iyi çalışmayan görüntüler için kullanışlıdır. Bu öğeler banner'ın altına itilecektir. Önerilen görsel kullanımı hakkında daha fazla bilgiyi {link}adresimizde bulabilirsiniz.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "BİLGİ TABANI", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.urlError": "URL geçerli değil", "app.containers.AdminPage.SettingsPage.urlPatternError": "Geçerli bir URL girin.", "app.containers.AdminPage.SettingsPage.urlTitle": "Web sitesi", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Kendi web sitenize bir bağlantı ekleyebilirsiniz. Bu bağlantı ana sayfanın alt kısmında kullanılacaktır.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Profillerinde adı olmayan kullanıcıların platformda nasıl görüneceğini seçin. Bu, bir aşama için erişim haklarını 'E-posta onayı' olarak ayarladığınızda gerçekleşecektir. Her durumda, kat<PERSON><PERSON><PERSON><PERSON>, kullanı<PERSON><PERSON><PERSON> kendileri için otomatik olarak oluşturduğumuz profil adını güncelleyebileceklerdir.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Kullanıcı adı gösterimi (yalnızca e-posta onaylı kullanıcılar için)", "app.containers.AdminPage.SideBar.administrator": "Yönetici", "app.containers.AdminPage.SideBar.communityPlatform": "Topluluk platformu", "app.containers.AdminPage.SideBar.community_monitor": "Topluluk gözlemcisi", "app.containers.AdminPage.SideBar.customerPortal": "Müşteri portalı", "app.containers.AdminPage.SideBar.dashboard": "Pan<PERSON>", "app.containers.AdminPage.SideBar.emails": "E-postalar", "app.containers.AdminPage.SideBar.folderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.groups": "Gruplar", "app.containers.AdminPage.SideBar.guide": "Kılavuz", "app.containers.AdminPage.SideBar.inputManager": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.insights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inspirationHub": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.knowledgeBase": "<PERSON><PERSON>gi ta<PERSON>ı", "app.containers.AdminPage.SideBar.language": "Dil", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "<PERSON><PERSON><PERSON> ve <PERSON>", "app.containers.AdminPage.SideBar.messaging": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.moderation": "E<PERSON>kin<PERSON>", "app.containers.AdminPage.SideBar.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.processing": "İşleme", "app.containers.AdminPage.SideBar.projectManager": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.projects": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.settings": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.signOut": "Çıkış yapın", "app.containers.AdminPage.SideBar.support": "Destek", "app.containers.AdminPage.SideBar.toPlatform": "Platforma", "app.containers.AdminPage.SideBar.tools": "Araçlar", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON> profilim", "app.containers.AdminPage.SideBar.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.workshops": "<PERSON><PERSON><PERSON>eler", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "Etiketlere göz atın", "app.containers.AdminPage.Topics.cancel": "İptal", "app.containers.AdminPage.Topics.confirmHeader": "Bu proje etiketini silmek istediğinizden emin misiniz?", "app.containers.AdminPage.Topics.delete": "Sil", "app.containers.AdminPage.Topics.deleteTopicLabel": "Sil", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Bu etiket artık bu projedeki yeni yayınlara eklenemeyecektir.", "app.containers.AdminPage.Topics.inputForm": "<PERSON><PERSON><PERSON> formu", "app.containers.AdminPage.Topics.lastTopicWarning": "En az bir etiket gereklidir. Etiket kullanmak istemiyorsanız {ideaFormLink} sekmesinden etiketleri devre dışı bırakabilirsiniz.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Bu projedeki yayınlara atanabilecek etiketleri ekleyebilir ve silebilirsiniz.", "app.containers.AdminPage.Topics.remove": "Kaldır", "app.containers.AdminPage.Topics.title": "<PERSON><PERSON>", "app.containers.AdminPage.Topics.topicManager": "Etiket yöneticisi", "app.containers.AdminPage.Topics.topicManagerInfo": "Ek proje etiketleri eklemek isterseniz bunu {topicManagerLink} üzerinden yapabilirsiniz.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Yeni bir grup ekleyin", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Grup adı", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Grup adı girin", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Manuel grup oluşturun", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Ne tür bir gruba ihtiyacınız var?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Manuel grup oluşturun", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Akıllı grup oluşturun", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Gruplar hakkında daha fazla bilgi edinin", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Genel bakıştan kullanıcıları seçebilir ve bu gruba ekleyebilirsiniz.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Koşullar tanımlayabilirsiniz. Bu koşulları karşılayan kullanıcılar otomatik olarak bu gruba eklenir.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "<PERSON> grup", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Akıllı grup", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Bu grupta henüz kimse yok", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Bazı kullanıcıları manuel olarak eklemek için {allUsersLink} adresine gidin.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Aramanızla eşleşen kullanıcı yok", "app.containers.AdminPage.Users.GroupsPanel.select": "Seç", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Tümünü dışa aktar", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Gruptaki kullanıcıları dışa aktarma", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Seçili dışa aktar", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "<PERSON>u grubu silmek istediğinizden emin misiniz?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Gruplara kullanıcı eklenirken bir hata oluş<PERSON>, lütfen tekrar deneyin.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Gruptan kaldır", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "<PERSON><PERSON>ilen kullanıcılar bu gruptan silinsin mi?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Kullanıcılar gruptan silinirken bir hata oluş<PERSON>, lütfen tekrar deneyin.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Gruba kullanıcı ekleme", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON> e<PERSON>", "app.containers.AdminPage.groups.permissions.addQuestion": "Demografik sorular ekleyin", "app.containers.AdminPage.groups.permissions.answerChoices": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.answerFormat": "Cevap formatı", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "En az bir seçenek sunulmalıdır", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Bu kullanıc<PERSON>, bu projeyi içeren klasörü yönetir. Bu kullanıcının bu proje için moderatörlük haklarını kaldırmak için ya klasör haklarını iptal edebilir ya da projeyi farklı bir klasöre taşıyabilirsiniz.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "<PERSON><PERSON> bir soru o<PERSON>", "app.containers.AdminPage.groups.permissions.createAQuestion": "Bir soru oluşturun", "app.containers.AdminPage.groups.permissions.defaultField": "<PERSON>ars<PERSON><PERSON><PERSON> alan", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Sil", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Sil", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Lütfen tüm seçenekler için bir ba<PERSON><PERSON><PERSON>k belirtin", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (onay kutusu)", "app.containers.AdminPage.groups.permissions.fieldType_date": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "<PERSON><PERSON><PERSON> cevap", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "<PERSON><PERSON><PERSON> (birden fazla se<PERSON>)", "app.containers.AdminPage.groups.permissions.fieldType_number": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.fieldType_select": "<PERSON><PERSON><PERSON> (<PERSON><PERSON> se<PERSON>)", "app.containers.AdminPage.groups.permissions.fieldType_text": "<PERSON><PERSON><PERSON> c<PERSON>p", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Ayrıntılı izinleri değiştirmek lisansınızın bir parçası değildir. Bu konuda daha fazla bilgi edinmek için lütfen GovSuccess Yöneticinizle iletişime geçin.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Bu grubu projeden kaldırmak istediğinizden emin misiniz?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Bir veya daha fazla grup seçin", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Üye yok} one {1 üye} other {{count} üye}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Lütfen başlığı tüm dillerde doldurun", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Emin misiniz?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Proje yöneticileri bulunamadı", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Bu projede kullanıcının gerçekleştirebileceği hiçbir eylem olmadığından herhangi bir şey gösterilmiyor.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Yalnızca yöneticiler yeni bir soru oluşturabilir.", "app.containers.AdminPage.groups.permissions.option1": "Seçenek 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Belgeye kim açıklama ekleyebilir?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Bir etkinliğe katılmak için kimler kaydolabilir?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "<PERSON><PERSON><PERSON><PERSON> hakkında kim yorum yapa<PERSON>ir?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "<PERSON><PERSON><PERSON><PERSON>r hakkında kimler yorum yapabilir?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "<PERSON><PERSON> teklif <PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "G<PERSON><PERSON><PERSON>e kim tepki verebilir?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "<PERSON><PERSON> girdi sun<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Ankete kim katı<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Ankete kimler katılabilir?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "<PERSON><PERSON> gö<PERSON>üllü ola<PERSON>ir?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> kimler oy kull<PERSON>bil<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "<PERSON><PERSON> oy kull<PERSON>ir?", "app.containers.AdminPage.groups.permissions.questionDescription": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON> b<PERSON>ş<PERSON>ığı", "app.containers.AdminPage.groups.permissions.save": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "<PERSON><PERSON> so<PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Başarılı!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Yaptığın<PERSON>z değişiklikler kaydedildi.", "app.containers.AdminPage.groups.permissions.select": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.selectValueError": "Lütfen bir cevap türü <PERSON>", "app.containers.AdminPage.new.createAProject": "Bir proje oluş<PERSON>", "app.containers.AdminPage.new.fromScratch": "S<PERSON><PERSON><PERSON>rda<PERSON>", "app.containers.AdminPage.phase.methodPicker.addOn1": "Ekleme", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Yapay zeka destekli içgörüler", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Katılımcıların her seferinde bir fikir olmak üzere anlaşma ve anlaşmazlıkları ortaya çıkarmalarına yardımcı olun.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Ortak bir zemin bulun", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Konveio ile interaktif bir PDF yerleştirin ve yorum ve geri bildirim toplayın.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Bir belge hakkında geri bildirim top<PERSON>a", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Üçüncü taraf bir anket yerleştirin", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "Dış anket", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Kullanıcılarınızın kolektif zekasından yararlanın. Onları herkese açık bir forumda fikirlerini sunmaya, tartışmaya ve/veya geri bildirimde bulunmaya davet edin.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "<PERSON><PERSON><PERSON><PERSON> girdi ve geri bildirim top<PERSON>", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Platform içi yapay zeka destekli içgörülerden yoksun", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Platform içi raporlama ve veri görselleştirme ve işlemeden yoksundur", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Platform içi rapor oluşturucu ile bağlantı", "app.containers.AdminPage.phase.methodPicker.logic1": "Mantık", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Çok çeşitli soru tipleri", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Katılımcıların bir zaman ve oy sınırı ile fikir yüklemelerine izin verin.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> veya <PERSON>", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Hızlı anket", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "<PERSON><PERSON><PERSON>, çoktan seçmeli bir anket hazırlayın.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON>, <PERSON><PERSON><PERSON> a<PERSON>an elde edilen sonuçları görselleştirin ve veri açısından zengin raporlar oluşturun.", "app.containers.AdminPage.phase.methodPicker.survey1": "Anket", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Çok çeşitli özel soru türleri aracılığıyla kullanıcılarınızın ihtiyaçlarını ve düşüncelerini anlayın.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "<PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Bir anket oluşturun", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Ku<PERSON><PERSON><PERSON><PERSON>lardan etkinlikler ve amaçlar için gönüllü olmalarını veya bir panel için katılımcı bulmalarını isteyin.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Katılımcıları veya gönüllüleri işe alın", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Bir oylama yöntemi seçin ve kullanıcıların birkaç farklı seçenek arasında önceliklendirme yapmasını sağlayın.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Bir oylama veya önceliklendirme çalışması yapın", "app.containers.AdminPage.projects.all.all": "Tümü", "app.containers.AdminPage.projects.all.createProjectFolder": "<PERSON><PERSON>", "app.containers.AdminPage.projects.all.existingProjects": "<PERSON><PERSON><PERSON> projeler", "app.containers.AdminPage.projects.all.homepageWarning": "Ana sayfa görünümünüzü özelleştirin: <PERSON><PERSON><PERSON><PERSON><PERSON>, ana say<PERSON><PERSON>i sırayı ayarlamak için projeleri ve klasörleri burada düzenleyebilir.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Proje Yöneticisi olduğunuz projeler burada görünecektir.", "app.containers.AdminPage.projects.all.noProjects": "<PERSON><PERSON> b<PERSON>.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Yalnızca yöneticiler proje klasörleri oluşturabilir.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projeler ve klasörler", "app.containers.AdminPage.projects.all.publishedTab": "Yayınlandı", "app.containers.AdminPage.projects.all.searchProjects": "<PERSON><PERSON> arama", "app.containers.AdminPage.projects.all.yourProjects": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "Yapay Zeka <PERSON>lizi", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Doğruluk: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Sor", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Özetlemek yerine, verilerinize ilgili sorular sorabilirsiniz. Bu özellik mevcut planınıza dahil de<PERSON>dir. Açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Bir soru sorun", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Bu içgörü aşağıdaki soruları içermektedir:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "<PERSON><PERSON><PERSON> sil", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Bu soruyu silmek istediğinizden emin misiniz?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Özeti sil", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Bu özetleri silmek istediğinizden emin misiniz?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "<PERSON>in özetleriniz burada görüntülenecektir, anca<PERSON>u anda hen<PERSON>z herhangi bir özetiniz yok.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Başlamak için yukarıdaki Otomatik özetleme düğmesine tıklayın.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Daha az girdi hakkında soru sormak daha yüksek doğruluk sağlar. <PERSON><PERSON><PERSON><PERSON>, arama veya demografik filtreler kullanarak mevcut girdi seçimini azaltın.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "<PERSON><PERSON><PERSON> girdi<PERSON> i<PERSON> soru", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Bu içgörünün kalitesini değerlendirin", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "<PERSON><PERSON><PERSON><PERSON> geri <PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Şunlar i<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "<PERSON><PERSON><PERSON> girdi<PERSON> i<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> te<PERSON>ekkür ederiz", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "Yapay zeka bu kadar çok girdiyi tek seferde işleyemez. Onları daha küçük gruplara ayırın.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Mevcut planınızda bir seferde en fazla 30 girdiyi özetleyebilirsiniz. Daha fazla bilgi edinmek için GovSuccess Yöneticinizle veya yöneticinizle konuşun.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "Anlıyorum.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Platformumuz ana temaları keşfetmenize, verileri özetlemenize ve çeşitli perspektifleri incelemenize olanak tanır. Belirli cevaplar veya içgörüler arıyorsanız, <PERSON>zetin ötesinde daha derinlere inmek için \"Soru Sor\" özelliğini kullanmayı düşünün.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "<PERSON><PERSON><PERSON>, YZ zaman zaman orijinal veri setinde açıkça bulunmayan bilgiler üretebilir.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hal<PERSON><PERSON>asyonlar:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "Yapay zeka belirli temaları veya fikirleri diğerlerinden daha fazla vurgulayarak genel yorumu çarpıtabilir.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Abartı:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, en doğru sonuçlar için 20-200 iyi tanımlanmış girdiyi işlemek üzere optimize edilmiştir. Veri hacmi bu aralığın ötesine geçtikçe, özet daha üst düzey ve genelleştirilmiş hale gelebilir. <PERSON><PERSON>, ya<PERSON><PERSON> \"daha az doğru\" old<PERSON><PERSON><PERSON> anlam<PERSON>na gelmez, daha ziyade daha geniş eğilimlere ve kalıplara odaklanacağı anlamına gelir. Daha incelikli içgörüler için, daha büyük veri kümelerini daha küçük, daha yönetilebilir alt kümelere ayırmak üzere (otomatik) etiketleme özelliğini kullanmanızı öneririz.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Veri Hacmi ve Doğruluğu:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Yapay zeka tarafından oluşturulan özetleri, büyük veri kümelerini anlamak için bir başlangıç noktası olarak kullanmanızı öneririz, ancak son s<PERSON><PERSON> olarak <PERSON>.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Yapay zeka ile nasıl çalışılır?", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Seçilen girdileri etikete ekle", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Etiket ekle", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Bu özellik mevcut planınıza dahil de<PERSON>dir. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "<PERSON><PERSON><PERSON> gir<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "<PERSON><PERSON><PERSON> gir<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON><PERSON>, ben <PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "G<PERSON><PERSON><PERSON>i etiketinize otomatik olarak atamak istiyor musunuz?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Girişleri etiketlere otomatik olarak atamak için <b></b> farklı yöntemler vardır.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Tercih ettiğiniz yöntemi başlatmak için <b>otomatik etiketleme düğmesini</b> kullanın.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Se<PERSON><PERSON> olan girişe atamak için bir etikete tıklayın.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "<PERSON><PERSON>, otomatik etiketleme", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Otomatik etiketleme", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Otomatik etiketler bilgisayar tarafından otomatik olarak türetilir. Bunları her zaman değiştirebilir veya kaldırabilirsiniz.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Otomatik etiketleme", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Bu etiketlerle zaten ilişkilendirilmiş olan girdiler tekrar sınıflandırılmayacaktır.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Sınıflandırma yalnızca etiketin adına göre yapılır. En iyi sonuçlar için ilgili anahtar kelimeleri seçin.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Etiketler Etikete göre", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Etiketleri oluşturursunuz ve örnek olarak birkaç girişi manuel olarak atarsınız, geri kalanını bilgisayar atar", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Etiketler Örnek olarak", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "\"Etiketler: etikete göre \"ye benzer, ancak sistemi iyi örneklerle eğittiğiniz için daha yüksek doğrulukla.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Etiketleri siz o<PERSON>ş<PERSON>unuz, bilgisayar g<PERSON>leri atar", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Etiketler Etikete göre", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "<PERSON><PERSON>, önceden tanımlanmış bir etiket setiniz olduğunda veya projeniz etiketler açısından sınırlı bir kapsama sahip olduğunda iyi çalışır.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Önemli bir beğenmeme/beğenme oranına sahip girdileri tespit edin", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Tartışmalı", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Etiketi sil", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "<PERSON>u etiketi silmek istediğinizden emin misiniz?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "<PERSON>unu bir daha g<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Etiketi düzenle", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Girişlerin aralarında dağıtılmasını istediğiniz en fazla 9 etiket seçin.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, etiketlere halihazırda atanmış olan girdilere dayanmaktadır. Bilgisayar sizin örneğinizi takip etmeye çalışacaktır.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Etiketler Örnek olarak", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "<PERSON><PERSON><PERSON>z herhangi bir özel etiketiniz yok.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Bilgisayar etiketleri otomatik olarak algılar ve bunları girişlerinize atar.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Etiketler Tam <PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Projeleriniz geniş bir etiket yelpazesini ka<PERSON>adığında iyi çalışır. Başlamak için iyi bir yer.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Nasıl etiketlemek istersiniz?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Her bir girdinin dilini tespit edin", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Dil", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Aktif filtre yok", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Daha doğru veya hedefe yönelik özetler yapmak amacıyla girdileri alt bölümlere ayırmak ve filtrelemek için etiketleri kullanın.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Etiketler Platform etiketleri", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Yazarın gönderi yaparken seçtiği mevcut platform etiketlerini atayın", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Tavsiye edilir", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Etiketi yeniden adlandır", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "İptal", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "İsim", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Etiketi yeniden adlandır", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Her bir girdiye metinden türetilen olumlu veya olumsuz bir duygu atayın", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Duyarlılık", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Etiket algılama", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Geçerli filtreleri kullanın", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Hangi girdileri etiketlemek istiyorsunuz?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Otomatik etiketleme <PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Tartışmalı", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "<PERSON>a erdi", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Başarısız", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Örnek olarak", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "<PERSON><PERSON> ediyor", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "Etiket tarafından", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Dil", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP etiketi", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Yakın zamanda gerçekleştirilen yapay zeka görevi yok", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Platform etiketi", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Duyarlılık", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Başladı", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Başarılı", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Özetleme g<PERSON>vi", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Şurada tetikle<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Tümü", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Doğum yılı", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "İkametgah", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Nişanlılık", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Cinsiyet", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Reaksiyon sayı<PERSON>ı", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "<PERSON>y sayısı", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "<PERSON><PERSON><PERSON> e<PERSON>", "app.containers.AdminPage.projects.project.analysis.anonymous": "<PERSON><PERSON><PERSON> girdi", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "İkametgahlarına gö<PERSON>", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Arka Plan İşleri", "app.containers.AdminPage.projects.project.analysis.comments": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "<PERSON><PERSON><PERSON><PERSON> yeri tablosu görüntülenemeyecek kadar büyük", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Boş yanıtları gizle", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Yanıtlar", "app.containers.AdminPage.projects.project.analysis.end": "Bitiş", "app.containers.AdminPage.projects.project.analysis.filter": "Yalnızca bu değere sahip girdileri göster", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Cevapsız yanıtları gizle", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Otomatik içgörüler", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON><PERSON> kombinasyonun {count} <PERSON><PERSON><PERSON><PERSON> bulunmaktadır.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "Yanlış", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Sonraki ısı haritası", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Sonraki içgörü", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "İstatistiksel olarak anlamlı bir içgörü değil.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Otomatik içgörüler 30'dan az katılımcısı olan projeler için mevcut değildir.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Katılımcılar", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Önceki ısı haritası", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Önceki içgörü", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "İstatistiksel olarak anlamlı içgörü.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "Do<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "T<PERSON>m içgörüleri görüntüleyin", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Otomatik içgörüleri görüntüleyin", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Geçersiz bir shapefile yüklendi ve görüntülenemiyor.", "app.containers.AdminPage.projects.project.analysis.limit": "Limit", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "<PERSON> soru", "app.containers.AdminPage.projects.project.analysis.manageInput": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Sonrak<PERSON> grafik", "app.containers.AdminPage.projects.project.analysis.noAnswer": "Cevap yok", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "<PERSON><PERSON><PERSON> ve<PERSON>.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Yüklenen şekil dosyası yok.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Mevcut filtrelerinize karşılık gelen giriş yok", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Önceki grafik", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "Kaldırmak", "app.containers.AdminPage.projects.project.analysis.removeFilter": "<PERSON><PERSON><PERSON><PERSON> kaldır", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "<PERSON><PERSON><PERSON><PERSON> kaldır", "app.containers.AdminPage.projects.project.analysis.search": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapefiles burada GeoJSON formatında görüntülenir. B<PERSON>, orijinal dosyadaki stil doğru görüntülenmeyebilir.", "app.containers.AdminPage.projects.project.analysis.start": "Başlangıç", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Destek Makalesi", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Bilinmiyor", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "<PERSON><PERSON><PERSON> sorular<PERSON> gö<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Seçilen soruları görüntüleyin", "app.containers.AdminPage.projects.project.analysis.votes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copied": "Panoya kopyalandı", "app.containers.AdminPage.widgets.copyToClipboard": "<PERSON>u kodu kopy<PERSON>ın", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "HTML kodunu kopyalayın", "app.containers.AdminPage.widgets.fieldAccentColor": "<PERSON><PERSON><PERSON> rengi", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Widget arka plan rengi", "app.containers.AdminPage.widgets.fieldButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "<PERSON><PERSON><PERSON> katılın", "app.containers.AdminPage.widgets.fieldFont": "Yazı tipi", "app.containers.AdminPage.widgets.fieldFontDescription": "<PERSON><PERSON>, {googleFontsLink} adresindeki mevcut bir yazı tipi adı olmalıdır.", "app.containers.AdminPage.widgets.fieldFontSize": "<PERSON><PERSON><PERSON> tipi boyutu (piksel)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Başlık alt yazısı", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Sesinizi <PERSON>", "app.containers.AdminPage.widgets.fieldHeaderText": "Üst bilgi başlığı", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Katılım platformumuz", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON> (piksel)", "app.containers.AdminPage.widgets.fieldInputsLimit": "<PERSON><PERSON><PERSON><PERSON> say<PERSON>ı", "app.containers.AdminPage.widgets.fieldProjects": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldRelativeLink": "Bağlantılar", "app.containers.AdminPage.widgets.fieldShowFooter": "Düğmeyi göster", "app.containers.AdminPage.widgets.fieldShowHeader": "Başlığı göster", "app.containers.AdminPage.widgets.fieldShowLogo": "Logoyu göster", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Site arka plan rengi", "app.containers.AdminPage.widgets.fieldSort": "Sıralama ölçütü", "app.containers.AdminPage.widgets.fieldTextColor": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldTopics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldWidth": "Genişlik", "app.containers.AdminPage.widgets.homepage": "<PERSON>", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Bu HTML kodunu kopyalayabilir ve web sitenizde widget'ınızı eklemek istediğiniz bölüme yapıştırabilirsiniz.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget HTML kodu", "app.containers.AdminPage.widgets.previewTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.settingsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortNewest": "En yeni", "app.containers.AdminPage.widgets.sortPopular": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortTrending": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Boyutlar", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Üst Bilgi ve Alt Bilgi", "app.containers.AdminPage.widgets.titleInputSelection": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleStyle": "Stil", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.PageTitle": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.SaveError": "<PERSON> sayfayı kaydederken bir şeyler ters gitti.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerImage": "<PERSON> g<PERSON>", "app.containers.ContentBuilder.homepage.bannerSubtext": "Banner alt metni", "app.containers.ContentBuilder.homepage.bannerText": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "D<PERSON>zen", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Ana sayfa banner'ındaki görüntü ve metin dışındaki ayarların özelleştirilmesi mevcut lisansınıza dahil değildir. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.containers.ContentBuilder.homepage.customized_button": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_text_label": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_url_label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Platformunuzda yaklaşan sonraki 3 etkinliği görüntüler.", "app.containers.ContentBuilder.homepage.eventsDescription": "Platformunuzda yaklaşan sonraki 3 etkinliği görüntüler.", "app.containers.ContentBuilder.homepage.fixedRatio": "Sabit oranlı banner", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Bu banner türü, metin, logo veya vatandaşlarınız için çok önemli olan belirli öğeler içeren görüntüler gibi kırpılmaması gereken görüntülerle en iyi şekilde çalışır. Bu banner, kullanıcılar oturum açtığında ana renkte düz bir kutu ile değiştirilir. Bu rengi genel ayarlardan ayarlayabilirsiniz. Önerilen resim kullanımı hakkında daha fazla bilgiyi {link}adresimizde bulabilirsiniz.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "BİLGİ TABANI", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Tam genişlikte banner", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Bu banner, harika bir görsel etki için tüm genişlik boyunca uzanır. G<PERSON>rüntü mümkün olduğunca fazla alanı kaplamaya çalışacak ve bu da her zaman görünür olmamasına neden olacaktır. Bu banner'ı istediğiniz renkte bir kaplama ile birleştirebilirsiniz. Önerilen görsel kullanımı hakkında daha fazla bilgiyi {link}adresimizde bulabilirsiniz.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "BİLGİ TABANI", "app.containers.ContentBuilder.homepage.imageOverlayColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>a rengi", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Görüntü kaplama opaklığı", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Geçersiz URL", "app.containers.ContentBuilder.homepage.no_button": "Düğme yok", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>yan k<PERSON>", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "<PERSON><PERSON>ımını etkinleştir", "app.containers.ContentBuilder.homepage.projectsDescription": "Projelerinizin görüntülenme sırasını yapılandırmak için {link}adresinde yeniden sıralayın.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.registeredUsersView": "Kayıtlı kullanıcılar", "app.containers.ContentBuilder.homepage.showAvatars": "Avatarları görüntüle", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Kayıtlı olmayan ziyaretçilere katılımcıların profil resimlerini ve sayılarını gösterme", "app.containers.ContentBuilder.homepage.sign_up_button": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.signedInDescription": "Kayıtlı kullanıcılar banner'ı bu şekilde görür.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Platforma kayıtlı olmayan ziyaretçiler banner'ı bu şekilde görür.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Bu banner özellikle başlık, altyazı veya düğmedeki metinle iyi çalışmayan görüntülerde kullanışlıdır. Bu öğeler banner'ın altına itilecektir. Önerilen görsel kullanımı hakkında daha fazla bilgiyi {link}adresimizde bulabilirsiniz.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "BİLGİ TABANI", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON><PERSON> sıra", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Gömme yüksekliği (piksel)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "Gömülü içeriğinizin sayfada görünmesini istediğiniz yükseklik (piksel cinsinden).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Yerleştirdiğiniz içeriğin kısa açıklaması", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Ekran okuyucu veya başka bir yardımcı teknoloji kullanan kullanıcılar için bu bilgilerin sağlanması yararlıdır.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Web sitesi adresi", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Yerleştirmek istediğiniz web sitesinin tam URL'si.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Harici bir web sitesindeki içeriği sayfanızda bir HTML iFrame içinde görüntüleyin. Her sayfanın gömülemeyeceğini unutmayın. Bir sayfayı katıştırmakta sorun yaşıyorsanız, katıştırmaya izin verecek şekilde yapılandırılıp yapılandırılmadığını sayfanın sahibine danışın.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Destek sayfamızı ziyaret edin", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu <PERSON><PERSON><PERSON><PERSON> yerleştirilemedi. {visitLinkMessage} adresinden daha fazla bilgi edinebilirsiniz.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Geçerli bir web adresi girin, <PERSON><PERSON><PERSON><PERSON> https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultiloc": "Akordeon", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Varsayılan olarak açık", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Bu genişletilebilir akordeon içeriğidir. Sağdaki panelde bulunan düzenleyiciyi kullanarak düzenleyebilir ve biçimlendirebilirsiniz.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Başlık", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Akordeon başlığı", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "Sil", "app.containers.admin.ContentBuilder.error": "hata", "app.containers.admin.ContentBuilder.errorMessage": "{locale} içeriğinde bir hata var, değişikliklerinizi kaydedebilmek için lütfen sorunu düzel<PERSON>", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Katılım avatarlarını gizle", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve kamu hizmetleri hakkında ne düşündüğünüzü takip eden, üç ayda bir yapılan ve devam eden bir ankettir.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "An<PERSON><PERSON> katılın", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Size daha iyi hizmet vermemize yardımcı olun", "app.containers.admin.ContentBuilder.homepage.default": "varsay<PERSON>lan", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "<PERSON><PERSON>m çağrısı", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "Birincil düğme URL'si", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "İkincil düğme URL'si", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "<PERSON><PERSON><PERSON><PERSON>ü<PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Başlık", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Ana sayfa banner'ı", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Ana sayfa banner'ı", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Resim ve metin kartları", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 sütun", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "<PERSON> sayfada kilidini açmak için yönetici panelindeki \"Teklifler\" bölümünde teklifleri etkinleştirin", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageMultiloc": "Resim", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Görselin kısa açıklaması", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "<PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> \"alt metin\" <PERSON><PERSON><PERSON><PERSON>, platformunuzu ekran okuyucu kullanan kullanıcılar için erişilebilir kılmak açısından önemlidir.", "app.containers.admin.ContentBuilder.participationBox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.textMultiloc": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 sütun", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 sütun", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "Sırasıyla %30 ve %60 genişliğe sahip 2 sütun", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "Sırasıyla %60 ve %30 genişliğe sahip 2 sütun", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 eşit sütun", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://ornek.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "En çok tepki gören girdiler", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Fikir oluşturma aşaması", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Bu proje veya aşama i<PERSON>in herhangi bir girdi mevcut de<PERSON>.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "<PERSON><PERSON> faz<PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Başlık", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "<PERSON>lam girdiler: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "<PERSON><PERSON><PERSON> met<PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Bu proje veya aşama i<PERSON>in herhangi bir girdi mevcut de<PERSON>.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Aşama seçiniz", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "İçerik", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Başlık", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "<PERSON><PERSON>tlar", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Katılımcılar zaman çizelgesi", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Bazı veriler bizim takip etmediğimiz harici bir ankette toplandığı için katılım sayılarının tam olarak doğru olmayabileceğini lütfen unutmayın.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Başlık", "app.containers.admin.ReportBuilder.charts.noData": "Seçtiğiniz filtreler için kullanılabilir veri yok.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Trafik kaynakları", "app.containers.admin.ReportBuilder.charts.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.usersByAge": "<PERSON><PERSON><PERSON> gö<PERSON>", "app.containers.admin.ReportBuilder.charts.usersByGender": "Cinsiyete göre kullanı<PERSON>ılar", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "<PERSON><PERSON><PERSON><PERSON>i zaman çizelgesi", "app.containers.admin.ReportBuilder.managerLabel1": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.periodLabel1": "Dönem", "app.containers.admin.ReportBuilder.projectLabel1": "<PERSON><PERSON>", "app.containers.admin.ReportBuilder.quarterReport1": "Toplum Gözlemcisi Raporu: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Başlangıç", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "1 ek koltuk satın alın", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "<PERSON>aylayın", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Bir kişiye yönetici hakları vermek istediğinizden emin misiniz?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Yönetici hakları verin", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Planınız dahilindeki koltuk sınırına ul<PERSON>ın<PERSON>, {noOfSeats} ek {noOfSeats, plural, one {koltuk} other {koltuk}} eklenecektir.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "<PERSON><PERSON> e<PERSON>", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Varsayılan statüler silinemez.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Sil", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Katılımcı girdisine atanmış olan durumlar silinemez. {manageTab} sekmesinde mevcut girdiden durumu kaldırabilir veya değiştirebilirsiniz.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Bu durum silinemez veya taşınamaz.", "app.containers.admin.ideaStatuses.all.manage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Özel giriş durumlarının yapılandırılması mevcut planınıza dahil değildir. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Bir projedeki katılımcı girdisine atanabilecek durumu yönetin. Durum herkes tarafından görülebilir ve katılımcıların bilgilendirilmesine yardımcı olur.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Bir proje içindeki tekliflere atanabilecek durumu yönetin. Durum herkes tarafından görülebilir ve katılımcıların bilgilendirilmesine yardımcı olur.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "G<PERSON>ş durumlarını düzenleme", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "<PERSON><PERSON><PERSON><PERSON>nı düzenleme", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Uygulama veya sonraki adımlar i<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Onaylandı", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "<PERSON><PERSON><PERSON> geri bild<PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Cevaplandı", "app.containers.admin.ideaStatuses.form.category": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.categoryDescription": "Lütfen durumunuzu en iyi yansıtan kategoriyi seçin. <PERSON><PERSON> seçim, analiz aracımızın yayınları daha doğru bir şekilde işlemesine ve analiz etmesine yardımcı olacaktır.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "<PERSON><PERSON><PERSON> seçeneklerden hiçbiriyle eşleşmiyor", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "Renk", "app.containers.admin.ideaStatuses.form.fieldDescription": "Durum Açıklaması", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "<PERSON><PERSON><PERSON> diller için bir durum açıklaması girin", "app.containers.admin.ideaStatuses.form.fieldTitle": "Durum Adı", "app.containers.admin.ideaStatuses.form.fieldTitleError": "<PERSON><PERSON><PERSON> diller için bir durum adı girin", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Başarıyla uygulandı", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Uygulandı", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "<PERSON><PERSON> et<PERSON><PERSON> için u<PERSON>gun değ<PERSON> veya seçilmedi", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Seçilmedi", "app.containers.admin.ideaStatuses.form.saveStatus": "<PERSON><PERSON><PERSON> ka<PERSON>et", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Uygulama veya sonraki adımlar iç<PERSON>il<PERSON>", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Değerlendiriliyor", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Görüntülendi ancak henüz işlenmedi", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Girdileri ve durumlarını yönetin.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "<PERSON><PERSON><PERSON> | {orgName} katılım platformu", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, et<PERSON><PERSON> e<PERSON>in ve girdileri bir projeden diğerine ta<PERSON>ıyın", "app.containers.admin.ideas.all.inputManagerPageTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.tabOverview": "Genel Bakış", "app.containers.admin.import.importInputs": "Girdileri içe aktar", "app.containers.admin.import.importNoLongerAvailable3": "Bu özellik artık burada mevcut değildir. Girdileri bir fikir aşamasına aktarmak için aşamaya gidin ve \"İçe Aktar \"ı seçin.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 ek yönetici koltuğu} other {# ek yönetici koltukları}} ve {managerSeats, plural, one {1 ek yönetici koltuğu} other {# ek yönetici koltukları}} limitin üzerine eklenecektir.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 ek yönetici koltuğu limitin üzerine eklenecektir} other {# limitin üzerinde ek yönetici koltuğu eklenecektir}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 ek yönetici koltuğu limitin üzerine eklenecektir} other {# limitin üzerinde ek yönetici koltuğu eklenecektir}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Davetiyeleri onaylayın ve gönderin", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Koltuk kullanımı üzerindeki etkiyi teyit edin", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Planınızdaki mevcut koltuk sınırına ulaştınız.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "<PERSON>u projenin yöneticileri ve idarecileri", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Yalnızca yöneticiler ve ortak çalışanlar", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Yalnızca platform yöneticileri, klasör yöneticileri ve proje yöneticileri işlem yapabilir", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>yan k<PERSON>ı<PERSON> da dahil olmak üzere herkes katılabilir.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Belirli kullanıcı grup(lar)ındaki kullanıcılar katılabilir. Kullanıcı gruplarını \"Kullanıcılar\" sekmesinden yönetebilirsiniz.", "app.containers.admin.project.permissions.viewingRightsTitle": "Bu projeyi kimler görebilir?", "app.containers.phaseConfig.enableSimilarInputDetection": "Benzer giriş algılamayı etkinleştir", "app.containers.phaseConfig.similarInputDetectionTitle": "<PERSON><PERSON> girdi <PERSON>", "app.containers.phaseConfig.similarInputDetectionTooltip": "Yinelemeleri önlemeye yardımcı olmak için katılımcılara yazarken benzer girdileri gösterin.", "app.containers.phaseConfig.similarityThresholdBody": "Benzerlik eşiği (gövde)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "<PERSON><PERSON>, iki açıklamanın benzer olarak işaretlenmesi için ne kadar benzer olması gerektiğini kontrol eder. 0 (katı) ile 1 (yumuşak) arasında bir değer kullanın. Düşük değerler daha az ancak daha doğru eşleşmeler döndürür.", "app.containers.phaseConfig.similarityThresholdTitle": "Benzerlik eşiği (başlık)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "<PERSON><PERSON>, iki ba<PERSON><PERSON><PERSON><PERSON><PERSON>n benzer olarak işaretlenmesi için ne kadar benzer olması gerektiğini kontrol eder. 0 (katı) ile 1 (yumuşak) arasında bir değer kullanın. Düşük değerler daha az ama daha doğru eşleşmeler döndürür.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "<PERSON><PERSON> özellik, 30 Haziran 2025 tarihine kadar erken erişim teklifinin bir parçası olarak kullanılabilir. Bu tarihten sonra da kullanmaya devam etmek isterseniz, etkinleştirme seçeneklerini görüşmek için lütfen Kamu Başarı Yöneticinize veya yöneticinize ulaşın.", "app.containers.survey.sentiment.noAnswers2": "<PERSON><PERSON> anda yanıt yok.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 yorum} one {1 yorum} other {# yorum}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir proje<PERSON> kat<PERSON>, bir tek<PERSON><PERSON> gö<PERSON> veya etkileşimde bulunmuş ya da etkinliklere katılmış kullanıcılar veya ziyaretçilerdir.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Katılımcılar", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Katılım oranı", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Katılımcı olan ziyaretçilerin yüzdesi.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Toplam katılımcılar", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Otomatik kampanyalar", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Otomatik e-postalar", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "{quantity} kampanyalarından", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Kampanyalar", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Özel e-postalar", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-postalar", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Gönderilen toplam e-posta", "app.modules.commercial.analytics.admin.components.Events.completed": "Tamamlandı", "app.modules.commercial.analytics.admin.components.Events.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Eklenen toplam etkinlik", "app.modules.commercial.analytics.admin.components.Events.upcoming": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Kabul Edildi", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Beklemede", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "G<PERSON><PERSON>ilen top<PERSON> davetiye", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "<PERSON><PERSON><PERSON> git", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Arşivlenmemiş ve ana sayfadaki 'Aktif' tablosunda görülebilen projeler", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Taslak projeler", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Tamamlandı", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "<PERSON><PERSON>m arşivlenmiş projeler ve bitmiş olan aktif zaman çizelgesi projeleri burada sayılır", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "<PERSON><PERSON> projeler", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Platformda görün<PERSON><PERSON> olan proje <PERSON>ı", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "<PERSON><PERSON> ka<PERSON>", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Kayıtlı kullanıcı haline gelen ziyaretçilerin yüzdesi.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "<PERSON><PERSON>tlar", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Toplam kayıtlar", "app.modules.commercial.analytics.admin.components.Tab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Son 30 gün:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Son 7 gün:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Ziyaretçiler\" tekil ziyaretçi sayısıdır. Platformu birden fazla kez ziyaret eden kişi yalnızca bir kez sayılır.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Ziyaretler\" oturum sayısıdır. Platformu birden fazla kez ziyaret eden kişinin her ziyareti sayılır.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Dün:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Saymak", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Dil", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Ziyaretçi sayısı", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Ziyaretçi yüzdesi", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Yönlendiren", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "buraya tıklayın", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Yönlendirenlerin tam listesini gö<PERSON>ü<PERSON><PERSON><PERSON><PERSON> i<PERSON>, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Trafik kaynakları", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.totalParticipants": "Toplam katılımcılar", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Henüz ziyaretçi verisi bulunmamaktadır.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Ziyaretçi verilerini toplama ve görüntüleme şeklimizi değiştirdik. <PERSON><PERSON><PERSON>, ziyaretçi verileri daha doğrudur ve GDPR ile uyumlu olmaya devam ederken daha fazla veri türü mevcuttur. Ziyaretçi zaman çizelgesi için kullanılan veriler daha eskiye dayansa da, \"Z<PERSON>ret süresi\", \"<PERSON><PERSON>ret başına sayfa görüntüleme\" ve diğer grafikler için verileri yalnızca Kasım 2024'te toplamaya başladık, bu nedenle bundan önce hiçbir veri mevcut değil. Bu nedenle, Kasım 2024'ten önceki verileri seçerseniz, bazı grafiklerin boş olabileceğini veya garip görünebileceğini unutmayın.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Zaman içinde e-posta teslimatları", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Zaman içinde katılımcılar", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Zaman içinde kayıtlar", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "İstatistik", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "<PERSON><PERSON>tat<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Zaman içindeki ziyaretler ve ziyaretçiler", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Dönem toplamı", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Saymak", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Dil", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Kampanyalar", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Ziyaretçi sayısı", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Yönlendiren", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Yönlendiren web siteleri", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Arama motorları", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO yönlendirmeleri", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Trafik kaynağı", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Web Siteleri", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Bu öğeyi seçip üstteki kaldır düğmesine tıklayarak bu içerik bayrağını kaldırabilirsiniz. Ardından Görüldü veya Görülmedi sekmelerinde yeniden görünecektir", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Uygunsuz içerik otomatik olarak algılandı.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Topluluk tarafından incelenmek üzere bildirilen veya Doğal Dil İşleme sistemimiz tarafından uygunsuz içerik nedeniyle işaretlenen hiçbir yayın yok", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "{numberOfItems, plural, one {içerik uyarısını} other {# içerik uyarısını}} kaldır", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Bir platform kullanıcısı tarafından uygunsuz olduğu bildirildi.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "İçerik Uyarıları", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "<PERSON><PERSON>", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "<PERSON><PERSON><PERSON>me <PERSON>ğunuzda gösterilen sayfalar", "app.modules.navbar.admin.containers.addProject": "<PERSON><PERSON><PERSON> gezinme çubuğ<PERSON> ekle", "app.modules.navbar.admin.containers.createCustomPageButton": "Özel sayfa oluşturma", "app.modules.navbar.admin.containers.deletePageConfirmation": "Bu sayfayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz. Henüz sayfayı silmeye hazır değilseniz gezinme çubuğundan da silebilirsiniz.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Gezinme çubuğuna en fazla 5 öğe ekleyebilirsiniz", "app.modules.navbar.admin.containers.pageHeader": "<PERSON><PERSON><PERSON> ve <PERSON>", "app.modules.navbar.admin.containers.pageSubtitle": "<PERSON><PERSON><PERSON><PERSON>, Ana Say<PERSON> ve projeler sayfalarına ek olarak beş adede kadar sayfa görüntüleyebilir. Menü öğelerini yeniden adlandırabilir, sıralarını değiştirebilir ve kendi içeriğinizin bulunduğu yeni sayfalar ekleyebilirsiniz.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "YAPAY ZEKA", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widget'lar", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "AI içeriğini raporunuza sürüklemek için aşağıdaki ☰ simgesini kullanın.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Kullanılabilir yapay zeka içgörüsü yoktur. Bunları projenizde oluşturabilirsiniz.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Projeye git", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Aşama seçiniz", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Yapay zeka analizinin kilidini açın", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Yapay zeka tarafından oluşturulan içgörüleri raporunuza çekin", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Yapay zeka ile daha hızlı raporlama yapın", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Yapay zeka ile raporlama mevcut planınıza dahil <PERSON>. Bu özelliğin kilidini açmak için Kamu Başarı Yöneticinizle görüşün.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "<PERSON><PERSON>, mevcut planınıza dahil <PERSON>. Kilidi açmak için Kamu Başarı Yöneticinize veya yöneticinize ulaşın.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "<PERSON><PERSON><PERSON> alanına göre gruplama", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Anket sorusuna göre gruplandırın", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Grup modu", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Anket yanıtlarını kayıt alanlarına (cinsiyet, konum, yaş, vb.) veya diğer anket sorularına göre gruplayın.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "Hiç<PERSON>i", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "<PERSON><PERSON><PERSON> alanı", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Anket aşaması", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "<PERSON><PERSON> so<PERSON>u", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "<PERSON><PERSON><PERSON> silmek istediğinizden emin misiniz?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "İptal", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "<PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "<PERSON><PERSON><PERSON><PERSON><PERSON> buraya yazın", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Takip etmek veya takibi bırakmak için aşağıdaki düğmelere tıklayın. Proje sayısı parantez içinde gösterilmiştir.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "Sizin bölgenizde", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Tercihleri takip edin", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Takip tercihlerinize göre şu anda aktif bir proje bulunmamaktadır.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Bu widget, kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> takip ettiğ<PERSON> \"alan<PERSON>\" ile ilişkili projeleri gösterir. Platformunuzun \"alanlar\" için farklı bir ad kullanabileceğini unutmayın - platform ayarlarındaki \"Alanlar\" sek<PERSON><PERSON> bakın. Kullanıcı henüz herhangi bir alanı takip etmiyorsa, widget takip edilebilecek mevcut alanları gösterecektir. Bu durumda widget maksimum 100 alan gösterecektir.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Yayınlanmış proje veya klasör mevcut değil", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Yayınlanmış projeler ve klasörler", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "<PERSON><PERSON> widget, projeler sayfasında tanımlanan sıralamaya uyarak o anda yayınlanmış olan projeleri ve klasörleri gösterir. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, \"eski\" projeler widget'ının \"aktif\" sekmesi<PERSON> a<PERSON>ıdı<PERSON>.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Seçili proje veya klasör yok", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Projeleri veya klasörleri seçme", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Seçilen projeler ve k<PERSON>örler", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "<PERSON>u widget ile, projelerin veya klasörlerin kullanıcılara gösterilmesini istediğiniz sırayı seçebilir ve belirleyebilirsiniz.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projeler", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "destek merkezimizi ziyaret edin", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Önerilen görüntü <PERSON>zünürlükleri hakkında daha fazla bilgi için, {supportPageLink}."}