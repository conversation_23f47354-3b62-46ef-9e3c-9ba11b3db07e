{"UI.FormComponents.required": "απαιτείται", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.info": "This image is always cropped to a certain ratio to make sure all crucial aspects are on display at all times. The {link} for this image type is {aspect}.", "app.components.Admin.ImageCropper.infoLinkText": "recommended ratio", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Κείμενο κεφαλίδας για εγγεγραμμένους χρήστες", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Προειδοποίηση: Το χρώμα που επιλέξατε δεν έχει αρκετά υψηλή αντίθεση. Αυτό μπορεί να έχει ως αποτέλεσμα το κείμενο να είναι δυσανάγνωστο. Επιλέξτε ένα πιο σκούρο χρώμα για να βελτιστοποιήσετε την αναγνωσιμότητα.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Προσθήκη εκδηλώσεων στη γραμμή πλοήγησης", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Όταν είναι ενεργοποιημένο, ένα<PERSON> σύνδεσμος προς όλες οι εκδηλώσεις του έργου θα προστεθεί στη γραμμή πλοήγησης.", "app.components.AdminPage.SettingsPage.eventsSection": "Events", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Προσαρμόσιμη ενότητα της αρχικής σελίδας", "app.components.AnonymousPostingToggle.userAnonymity": "Ανωνυμία χρήστη", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Να επιτρέπει στους χρήστες να συμμετέχουν ανώνυμα", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Οι χρήστες μπορούν να εξακολουθούν να επιλέγουν να συμμετέχουν με το πραγματικό τους όνομα, αλλά θα έχουν τη δυνατότητα να υποβάλλουν τις συνεισφορές τους ανώνυμα, αν το επιλέξουν. Όλοι οι χρήστες θα εξακολουθούν να πρέπει να συμμορφώνονται με τις απαιτήσεις που ορίζονται στην καρτέλα Δικαιώματα πρόσβασης για να περάσουν οι συνεισφορές τους. Τα δεδομένα του προφίλ του χρήστη δεν θα είναι διαθέσιμα στην εξαγωγή δεδομένων συμμετοχής.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Μάθετε περισσότερα για την ανωνυμία των χρηστών στο {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "άρθρο υποστήριξης", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Once additional seats are added, your billing will be increased. Reach out to your GovSuccess Manager to learn more about it.", "app.components.ProjectTemplatePreview.close": "Close", "app.components.ProjectTemplatePreview.createProject": "Δημιουργία έργου", "app.components.ProjectTemplatePreview.goBack": "Go back", "app.components.ProjectTemplatePreview.goBackTo": "Επιστρέψτε στο {goBackLink}.", "app.components.ProjectTemplatePreview.infoboxLine1": "Θέλετε να χρησιμοποιήσετε αυτό το πρότυπο για το δικό σας έργο συμμετοχής;", "app.components.ProjectTemplatePreview.infoboxLine2": "Επικοινωνήστε με τον υπεύθυνο στη διοίκηση της πόλης σας ή επικοινωνήστε με έναν {link}.", "app.components.ProjectTemplatePreview.projectFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>γου", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Η επιλεγμένη ημερομηνία δεν είναι έγκυρη. Παρακαλούμε δώστε μια ημερομηνία με την ακόλουθη μορφή: ΕΕΕΕ-ΜΜ-ΗΗ", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Παρακαλούμε επιλέξτε μια ημερομηνία έναρξης για το έργο", "app.components.ProjectTemplatePreview.projectStartDate": "Η ημερομηνία έναρξης του έργου σας", "app.components.ProjectTemplatePreview.projectTitle": "Ο τίτλος του έργου σας", "app.components.ProjectTemplatePreview.projectTitleError": "Πληκτρολογήστε τον τίτλο έργου", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Παρακαλούμε πληκτρολογήστε έναν τίτλο έργου για όλες τις γλώσσες", "app.components.ProjectTemplatePreview.projectsOverviewPage": "σελίδα επισκόπησης έργων", "app.components.ProjectTemplatePreview.responseError": "Ουπς, κάτι πήγε στραβά.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Δείτε περισσότερα πρότυπα", "app.components.ProjectTemplatePreview.successMessage": "Το έργο δημιουργήθηκε με επιτυχία!", "app.components.ProjectTemplatePreview.typeProjectName": "Πληκτρολογήστε το όνομα του έργου", "app.components.ProjectTemplatePreview.useTemplate": "Χρησιμοποιήστε αυτό το πρότυπο", "app.components.SeatInfo.additionalSeats": "Additional seats", "app.components.SeatInfo.additionalSeatsToolTip": "This shows the number of additional seats you have purchased on top of 'Included seats'.", "app.components.SeatInfo.adminSeats": "Admin seats", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} admin seats included", "app.components.SeatInfo.adminSeatsTooltip1": "Administrators are in charge of the platform and they have manager rights for all folders and projects. You can {visitHelpCenter} to learn more about the different roles.", "app.components.SeatInfo.currentAdminSeatsTitle": "Current admin seats", "app.components.SeatInfo.currentManagerSeatsTitle": "Current manager seats", "app.components.SeatInfo.includedAdminToolTip": "This shows the number of available seats for admins included in the yearly contract.", "app.components.SeatInfo.includedManagerToolTip": "This shows the number of available seats for managers included in the yearly contract.", "app.components.SeatInfo.includedSeats": "Included seats", "app.components.SeatInfo.managerSeats": "Manager seats", "app.components.SeatInfo.managerSeatsTooltip": "Folder/project managers can manage an unlimited number of folders/projects. You can {visitHelpCenter} to learn more about the different roles.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} manager seats included", "app.components.SeatInfo.remainingSeats": "Remaining seats", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Total seats", "app.components.SeatInfo.totalSeatsTooltip": "This shows the summed number of seats within your plan and additional seats you have purchased.", "app.components.SeatInfo.usedSeats": "Used seats", "app.components.SeatInfo.view": "View", "app.components.SeatInfo.visitHelpCenter": "visit our help center", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Your plan has {adminSeatsIncluded}. Once you've used all the seats, extra seats will be added under 'Additional seats'.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Your plan has {manager<PERSON><PERSON><PERSON><PERSON>ncluded}, eligible for folder managers and project managers. Once you've used all the seats, extra seats will be added under 'Additional seats'.", "app.components.UserSearch.addModerators": "Add", "app.components.UserSearch.searchUsers": "Πληκτρολογήστε για να αναζητήσετε χρήστες...", "app.components.admin.Graphs": "Δεν υπάρχουν διαθέσιμα δεδομένα με τα τρέχοντα φίλτρα.", "app.components.admin.Graphs.noDataShort": "Δεν υπάρχουν διαθέσιμα δεδομένα.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Comments over time", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Posts over time", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Αντιδρά<PERSON><PERSON>ις με την πάροδο του χρόνου", "app.components.admin.InputManager.onePost": "1 εισήγηση", "app.components.admin.PostManager.PostPreview.assignee": "Αποδέκτης", "app.components.admin.PostManager.PostPreview.cancelEdit": "Ακύρωση επεξεργασίας", "app.components.admin.PostManager.PostPreview.currentStatus": "Current status", "app.components.admin.PostManager.PostPreview.delete": "Delete", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εισήγηση; Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εισήγηση; Η εισήγηση θα διαγραφεί από όλες τις φάσεις του έργου και δεν μπορεί να ανακτηθεί.", "app.components.admin.PostManager.PostPreview.edit": "Edit", "app.components.admin.PostManager.PostPreview.noOne": "Μη εκχωρημένο", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Ο αριθμός των φορών που έχει συμπεριληφθεί στους συμμετοχικούς προϋπολογισμούς άλλων συμμετεχόντων", "app.components.admin.PostManager.PostPreview.picks": "Επιλογές: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Η αντίδραση μετράει:", "app.components.admin.PostManager.PostPreview.save": "Save", "app.components.admin.PostManager.PostPreview.submitError": "Σφάλμα", "app.components.admin.PostManager.allPhases": "Όλες οι φάσεις", "app.components.admin.PostManager.allProjects": "All projects", "app.components.admin.PostManager.allStatuses": "Όλες οι καταστάσεις", "app.components.admin.PostManager.allTopics": "Όλες οι ετικέτες", "app.components.admin.PostManager.anyAssignment": "Οποιοσδήποτε διαχειριστής", "app.components.admin.PostManager.assignedTo": "Εκχωρήθηκε στον/στην {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Εκχωρήθηκε σε μένα", "app.components.admin.PostManager.assignee": "Assignee", "app.components.admin.PostManager.bodyTitle": "Περιγραφή", "app.components.admin.PostManager.comments": "Comments", "app.components.admin.PostManager.components.goToInputManager": "Μετάβαση στον διαχειριστή εισόδου", "app.components.admin.PostManager.components.goToProposalManager": "Μετάβαση στον διαχειριστή προτάσεων", "app.components.admin.PostManager.contributionFormTitle": "Επεξεργασία συμβολής", "app.components.admin.PostManager.cost": "Κόστος", "app.components.admin.PostManager.currentLat": "Κεντρικό γεωγραφικό πλάτος", "app.components.admin.PostManager.currentLng": "Κεντρικό γεωγραφικό μήκος", "app.components.admin.PostManager.currentZoomLevel": "Ε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ζουμ", "app.components.admin.PostManager.delete": "Delete", "app.components.admin.PostManager.deleteAllSelectedInputs": "Διαγραφή {count} δημοσιεύσεων", "app.components.admin.PostManager.deleteConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτό το επίπεδο;", "app.components.admin.PostManager.dislikes": "Απεχθάνεται το", "app.components.admin.PostManager.edit": "Edit", "app.components.admin.PostManager.editProjects": "Επεξεργασία έργων", "app.components.admin.PostManager.editStatuses": "Επεξεργα<PERSON><PERSON><PERSON> καταστάσεων", "app.components.admin.PostManager.editTags": "Επεξεργασ<PERSON>α ετικετών", "app.components.admin.PostManager.editedPostSave": "Αποθήκευση", "app.components.admin.PostManager.exportAllInputs": "Εξαγωγ<PERSON> όλων των δημοσιεύσεων (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Εξαγω<PERSON><PERSON> όλων των σχολίων (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Εξαγωγή σχολίων για αυτό το έργο (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Εξαγωγή δημοσιεύσεων σε αυτό το έργο (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Εξαγωγή επιλεγμένων δημοσιεύσεων (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Εξαγωγή σχολίων για επιλεγμένες δημοσιεύσεις (.xslx)", "app.components.admin.PostManager.exports": "Εξαγωγές", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Επιλέξτε πώς θα βλέπουν οι χρήστες το όνομά σας", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Εξηγήστε αυτή την αλλαγή κατάστασης", "app.components.admin.PostManager.fileUploadError": "Ένα ή περισσότερα αρχεία απέτυχαν να μεταφορτωθούν. Ελέγξτε το μέγεθος και τη μορφή του αρχείου και προσπαθήστε ξανά.", "app.components.admin.PostManager.formTitle": "Επεξεργασία ιδέας", "app.components.admin.PostManager.goToDefaultMapView": "Μετάβαση στο προεπιλεγμένο κέντρο χάρτη", "app.components.admin.PostManager.hiddenFieldsLink": "κρυφά πεδία", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Συμβουλή: Αν χρησιμοποιείτε το Typeform, προσθέστε το {hiddenFieldsLink} για να παρακολουθείτε ποιος έχει απαντήσει στην έρευνά σας.", "app.components.admin.PostManager.importError": "Το επιλεγμένο αρχείο δεν μπόρεσε να εισαχθεί επειδή δεν είναι έγκυρο αρχείο GeoJSON", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputManagerHeader": "Εισήγηση", "app.components.admin.PostManager.inputs": "Input", "app.components.admin.PostManager.inputsExportFileName": "εισήγηση", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Εμφάνιση μόνο των δημοσιεύσεων που χρειάζονται ανατροφοδότηση", "app.components.admin.PostManager.issueFormTitle": "Επεξεργα<PERSON><PERSON><PERSON> ζητήματος", "app.components.admin.PostManager.latestFeedbackMode": "Χρησιμοποιήστε την τελευτα<PERSON>α υπάρχουσα επίσημη ενημέρωση ως επεξήγηση", "app.components.admin.PostManager.likes": "Μου αρέσει", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Moving this input away from its current project will lose the information about its assigned phases. Do you want to proceed?", "app.components.admin.PostManager.multipleInputs": "{ideaCount} δημοσιεύσεις", "app.components.admin.PostManager.newFeedbackMode": "Γράψτε μια νέα ενημέρωση για να εξηγήσετε αυτή την αλλαγή", "app.components.admin.PostManager.noFilteredResults": "The filters you selected did not return any results", "app.components.admin.PostManager.noOne": "Unassigned", "app.components.admin.PostManager.officialUpdateAuthor": "Choose how people will see your name", "app.components.admin.PostManager.officialUpdateBody": "Explain this status change", "app.components.admin.PostManager.optionFormTitle": "Επιλογή επεξεργασίας", "app.components.admin.PostManager.participants": "Συμμετέχοντες", "app.components.admin.PostManager.participatoryBudgettingPicks": "Επιλογές", "app.components.admin.PostManager.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.postedIn": "Καταχ<PERSON><PERSON><PERSON>θηκε στο {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Επεξεργασία έργου", "app.components.admin.PostManager.projectsTab": "Projects", "app.components.admin.PostManager.projectsTabTooltipContent": "Μπορείτε να σύρετε και να αποθέσετε δημοσιεύσεις για να τις μετακινήσετε από ένα έργο σε ένα άλλο. Σημειώστε ότι για έργα χρονοδιαγράμματος, θα πρέπει ακόμα να προσθέσετε τη δημοσίευση σε μια συγκεκριμένη φάση.", "app.components.admin.PostManager.proposedBudgetTitle": "Προτεινόμενος προϋπολογισμός", "app.components.admin.PostManager.publication_date": "Δημοσιεύθηκε στις", "app.components.admin.PostManager.questionFormTitle": "Επεξεργα<PERSON><PERSON>α ερώτησης", "app.components.admin.PostManager.reactions": "Αντιδρά<PERSON>εις", "app.components.admin.PostManager.resetFiltersButton": "Reset filters", "app.components.admin.PostManager.resetInputFiltersDescription": "Επαναφέρετε τα φίλτρα για να δείτε όλες τις εισηγήσεις.", "app.components.admin.PostManager.saved": "Αποθηκευμένο", "app.components.admin.PostManager.setAsDefaultMapView": "Αποθηκεύστε το τρέχον κεντρικ<PERSON> σημείο και το επίπεδο ζουμ ως προεπιλογές του χάρτη", "app.components.admin.PostManager.statusChangeGenericError": "Υπήρξε σφάλμα, παρακαλούμε προσπαθήστε ξανά αργότερα ή επικοινωνήστε με την υποστήριξη.", "app.components.admin.PostManager.statusChangeSave": "Αλλαγ<PERSON> κατάστασης", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Αλλάξτε την κατάσταση μιας δημοσίευσης χρησιμοποιώντας σύρσιμο και απόθεση. Ο αρχικός συντάκτης και οι άλλοι συνεισφέροντες θα λάβουν ειδοποίηση για την αλλαγή της κατάστασης.", "app.components.admin.PostManager.submitApiError": "Υπήρξε πρόβλημα με την υποβολή της φόρμας. Ελέγξτε για τυχόν σφάλματα και προσπαθήστε ξανά.", "app.components.admin.PostManager.timelineTab": "Timeline", "app.components.admin.PostManager.timelineTabTooltipText": "Σύρετε και αποθέστε δημοσιεύσεις για να τις αντιγράψετε σε διαφορετικές φάσεις του έργου.", "app.components.admin.PostManager.title": "Title", "app.components.admin.PostManager.topicsTab": "Tags", "app.components.admin.PostManager.topicsTabTooltipText": "Προσθέστε ετικέτες σε μια εισήγηση χρησιμοποιώντας σύρσιμο και απόθεση.", "app.components.admin.PostManager.votes": "Ψήφοι", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON>ιγ<PERSON>τερο από μία ημέρα} one {Μία ημέρα} other {# days}} left", "app.components.admin.ReportExportMenu.FileName.fromFilter": "από", "app.components.admin.ReportExportMenu.FileName.groupFilter": "ομάδα", "app.components.admin.ReportExportMenu.FileName.projectFilter": "έργο", "app.components.admin.ReportExportMenu.FileName.topicFilter": "ετικέτα", "app.components.admin.ReportExportMenu.FileName.untilFilter": "μέχρι", "app.components.admin.ReportExportMenu.downloadPng": "Λήψη ως PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Λήψη ως SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Λήψη Excel", "app.components.admin.SlugInput.regexError": "Το slug μπορεί να περιέχει μόνο κανον<PERSON>, πεζ<PERSON> γράμματα (a-z), αριθ<PERSON><PERSON><PERSON>ς (0-9) και παύλες (-). Ο πρώτος και ο τελευ<PERSON><PERSON><PERSON>ος χαρακτήρας δεν μπορούν να είναι παύλες. Οι διαδοχικές παύλες (--) απαγορεύονται.", "app.components.admin.TerminologyConfig.saveButton": "Save", "app.components.admin.seatSetSuccess.admin": "Διαχειριστής", "app.components.admin.seatSetSuccess.allDone": "Όλα έτοιμα", "app.components.admin.seatSetSuccess.close": "Κλείστε το", "app.components.admin.seatSetSuccess.manager": "Διευθυντής", "app.components.admin.seatSetSuccess.orderCompleted": "Η παραγγελία ολοκληρώθηκε", "app.components.admin.seatSetSuccess.reflectedMessage": "Οι αλλαγές στο πρόγραμμά σας θα αποτυπωθούν στον επόμενο κύκλο χρέωσης.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} τα δικαιώματα έχουν χορηγηθεί στον/στους επιλεγμένο(-ους) χρήστη(-ες).", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Η συλλογή σχολίων για ένα έγγραφο είναι μια προσαρμοσμένη λειτουργία και δεν περιλαμβάνεται στην τρέχουσα άδεια χρήσης. Επικοινωνήστε με τον διαχειριστή σας στο GovSuccess για να μάθετε περισσότερα σχετικά με αυτό.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Συνεισφορά", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Περισσότερες πληροφορίες σχετικά με τον τρόπο ενσωμάτωσης ενός συνδέσμου για τις Φόρμες Google μπορείτε να βρείτε στο {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "αυτό το άρθρο υποστήριξης", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Ιδέα", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Πώς πρέπει να ονομάζεται μια εισήγηση;", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Comment", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Δώστε τον σύνδεσμο προς το έγγραφο Konveio εδώ. Διαβάστε το {supportArticleLink} για περισσότερες πληροφορίες σχετικά με τη ρύθμιση του Konveio.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "άρθρο υποστήριξης", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Απαιτείται μέγιστος προϋπολογισμός", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Ο μέγιστος αριθμός ψήφων ανά επιλογή πρέπει να είναι μικρότερος ή ίσος με το συνολικό αριθμό ψήφων", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Απαιτεί<PERSON><PERSON><PERSON> μέγιστος αριθμός ψήφων", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Ο ελάχιστος προϋπολογισμός δεν μπορεί να είναι μεγαλύτερος από τον μέγιστο προϋπολογισμό", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Απαιτείται ελάχιστος προϋπολογισμός", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Ο ελάχιστος αριθμός ψήφων δεν μπορεί να είναι μεγαλύτερος από τον μέγιστο αριθμό.", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Απαιτεί<PERSON><PERSON><PERSON> ελάχιστος αριθμός ψήφων", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Επιλογή", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Καρτέλα Input Manager", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Διαμορφώστε τις επιλογές ψηφοφορίας στη διεύθυνση {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Project", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Ερώτηση", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} within plan, {noOfAdditionalSeats} additional", "app.components.seatsWithinPlan.seatsWithinPlanText": "Seats within plan", "app.containers.Admin.Campaigns.campaignFrom": "Από:", "app.containers.Admin.Campaigns.campaignTo": "Προς:", "app.containers.Admin.Campaigns.customEmails": "Προσαρμοσμένα emails", "app.containers.Admin.Campaigns.customEmailsDescription": "Στείλτε προσαρμοσμένα email και ελέγξτε τα στατιστικά στοιχεία.", "app.containers.Admin.Campaigns.noAccess": "Λυ<PERSON><PERSON><PERSON><PERSON>αστε, αλλ<PERSON> φαίνετ<PERSON><PERSON> ότι δεν έχετε πρόσβαση στην ενότητα emails", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Αυτοματοποιημένα μηνύματα ηλεκτρονικού ταχυδρομείου", "app.containers.Admin.Invitations.addToGroupLabel": "Προσθέστε αυτά τα άτομα σε συγκεκριμένες μη αυτόματες ομάδες χρηστών", "app.containers.Admin.Invitations.adminLabel1": "Δώστε στους προσκεκλημένους δικαιώματα διαχειριστή", "app.containers.Admin.Invitations.adminLabelTooltip": "Όταν επιλέγετε αυτή την επιλογή, τα άτομα που προσκαλείτε θα έχουν πρόσβαση σε όλες τις ρυθμίσεις της πλατφόρμας σας.", "app.containers.Admin.Invitations.configureInvitations": "3. Διαμόρφωση των προσκλήσεων", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Δεν υπάρχουν προσκλήσεις που να ταιριάζουν με την αναζήτησή σας", "app.containers.Admin.Invitations.deleteInvite": "Delete", "app.containers.Admin.Invitations.deleteInviteTooltip": "Η ακύρωση μιας πρόσκλησης θα σας επιτρέψει να στείλετε εκ νέου πρόσκληση σε αυτό το άτομο.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και συμπληρώστε το πρότυπο", "app.containers.Admin.Invitations.downloadTemplate": "Λήψη προτύπου", "app.containers.Admin.Invitations.email": "Email", "app.containers.Admin.Invitations.emailListLabel": "Εισάγετε με μη αυτόματο τρόπο τις διευθύνσεις email των ατόμων που θέλετε να προσκαλέσετε. Διαχωρίστε κάθε διεύθυνση με κόμμα.", "app.containers.Admin.Invitations.exportInvites": "Εξαγωγ<PERSON> όλων των προσκλήσεων", "app.containers.Admin.Invitations.fileRequirements": "Σημαντικό: Για να αποσταλ<PERSON><PERSON>ν σωστά οι προσκλήσεις, καμία στήλη δεν μπορεί να καταργηθεί από το πρότυπο εισαγωγής. Αφήστε τις αχρησιμοποίητες στήλες κενές.", "app.containers.Admin.Invitations.filetypeError": "Λα<PERSON><PERSON><PERSON><PERSON>μένος τύπος αρχείου. Υποστηρίζονται μόνο τα αρχεία XLSX.", "app.containers.Admin.Invitations.groupsPlaceholder": "Δεν έχει επιλεγεί ομάδα", "app.containers.Admin.Invitations.helmetDescription": "Πρόσκληση χρηστών στην πλατφόρμα", "app.containers.Admin.Invitations.helmetTitle": "Πίνα<PERSON>ας εργαλείων πρόσκλησης διαχειριστή", "app.containers.Admin.Invitations.importOptionsInfo": "Αυτές οι επιλογές θα ληφθούν υπόψη μόνο όταν δεν έχουν οριστεί στο αρχείο Excel. Παρακαλούμε επισκεφθείτε τη σελίδα {supportPageLink} για περισσότερες πληροφορίες.", "app.containers.Admin.Invitations.importTab": "Εισαγωγή διευθύνσεων email", "app.containers.Admin.Invitations.invitationOptions": "Επιλο<PERSON><PERSON><PERSON> πρόσκλησης", "app.containers.Admin.Invitations.invitationSubtitle": "Προσκα<PERSON>έστε άτομα στην πλατφόρμα σε οποιαδήποτε χρονική στιγμή. Λαμβάνουν ένα ουδέτερο email πρόσκλησης με το λογότυπό σας, στο οποίο τους ζητείται να εγγραφούν στην πλατφόρμα.", "app.containers.Admin.Invitations.invitePeople": "Πρόσκληση ατόμων μέσω email", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "Accepted", "app.containers.Admin.Invitations.inviteStatusPending": "Σε εκκρεμότητα", "app.containers.Admin.Invitations.inviteTextLabel": "Πληκτρολογήστε προαιρε<PERSON><PERSON><PERSON><PERSON> ένα μήνυμα που θα προστεθεί στο μήνυμα ηλεκτρονικού ταχυδρομείου πρόσκλησης.", "app.containers.Admin.Invitations.invitedSince": "Πρόσκληση", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Επιλέξτε τη γλώσσα της πρόσκλησης", "app.containers.Admin.Invitations.moderatorLabel": "Δώστε σε αυτά τα άτομα δικαιώματα διαχείρισης έργου", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Όταν επιλέγετε αυτήν την επιλογή, θα εκχωρηθούν στον/στους προσκεκλημένο/ους δικαιώματα διαχειριστή έργου για το/τα επιλεγμένο/α έργο/α. Περισσότερες πληροφορίες σχετικά με τα δικαιώματα διαχειριστή έργου {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "here", "app.containers.Admin.Invitations.name": "Name", "app.containers.Admin.Invitations.processing": "Αποστολή προσκλήσεων. Παρακαλούμε περιμένετε...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Δεν έχει επιλεγεί κανένα έργο", "app.containers.Admin.Invitations.save": "Αποστολή προσκλήσεων", "app.containers.Admin.Invitations.saveErrorMessage": "Εμφανίσ<PERSON>η<PERSON><PERSON><PERSON> ένα ή περισσότερα σφάλματα και οι προσκλήσεις δεν στάλθηκαν. Παρακαλούμε διορθώστε το/τα σφάλμα(τα) που αναφέρονται παρακάτω και προσπαθήστε ξανά.", "app.containers.Admin.Invitations.saveSuccess": "Επιτυχία!", "app.containers.Admin.Invitations.saveSuccessMessage": "Η πρόσκληση στάλθηκε επιτυχώς.", "app.containers.Admin.Invitations.supportPage": "σελίδα υποστήριξης", "app.containers.Admin.Invitations.supportPageLinkText": "Επισκεφθείτε τη σελίδα υποστήριξης", "app.containers.Admin.Invitations.tabAllInvitations": "Όλες οι προσκλήσεις", "app.containers.Admin.Invitations.tabInviteUsers": "Πρόσκληση χρηστών", "app.containers.Admin.Invitations.textTab": "Εισάγετε με μη αυτόματο τρόπο διευθύνσεις email", "app.containers.Admin.Invitations.unknownError": "Something went wrong. Please try again later.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε το ολοκληρωμένο αρχείο προτύπου", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} αν θέλετε περισσότερες πληροφορίες σχετικά με όλες τις υποστηριζόμενες στήλες στο πρότυπο εισαγωγής.", "app.containers.Admin.Moderation.all": "All", "app.containers.Admin.Moderation.belongsTo": "Ανήκει σε", "app.containers.Admin.Moderation.collapse": "δείτε λιγότερα", "app.containers.Admin.Moderation.comment": "Comment", "app.containers.Admin.Moderation.content": "Content", "app.containers.Admin.Moderation.date": "Ημερομηνία", "app.containers.Admin.Moderation.goToComment": "Άνοιγμα αυτού του σχόλιου σε νέα καρτέλα", "app.containers.Admin.Moderation.goToPost": "Άνοιγμα αυτής της δημοσίευσης σε νέα καρτέλα", "app.containers.Admin.Moderation.goToProposal": "Άνοιγμα αυτής της πρότασης σε νέα καρτέλα", "app.containers.Admin.Moderation.markFlagsError": "Η επισήμανση του/των στοιχείου(ων) δεν ήταν δυνατή. Δοκιμάστε ξανά.", "app.containers.Admin.Moderation.markNotSeen": "Επισημάνετε {selectedItemsCount, plural, one {# στοιχείο} other {# στοιχεία}} ως μη αναγνωσμένο", "app.containers.Admin.Moderation.markSeen": "Επισημάνετε {selectedItemsCount, plural, one {# στοιχείο} other {# στοιχεία}} ως αναγνωσμένο", "app.containers.Admin.Moderation.moderationsTooltip": "Αυτή η σελίδα σας επιτρέπει να ελέγχετε γρήγορα όλες τις νέες δημοσιεύσεις που έχουν δημοσιευτεί στην πλατφόρμα σας, συμπεριλαμβανομένων των ιδεών και των σχολίων. Μπορείτε να επισημάνετε τις δημοσιεύσεις ως \"αναγνωσμένες\", ώστε οι άλλοι να γνωρίζουν τι χρειάζεται ακόμη να επεξεργαστεί.", "app.containers.Admin.Moderation.noUnviewedItems": "Δεν υπάρχουν μη μη αναγνωσμένα στοιχεία", "app.containers.Admin.Moderation.noViewedItems": "Δεν υπάρχουν μη αναγνωσμένα στοιχεία", "app.containers.Admin.Moderation.pageTitle1": "Τροφοδοσία", "app.containers.Admin.Moderation.post": "Δημοσίευση", "app.containers.Admin.Moderation.profanityBlockerSetting": "Αποκλεισμ<PERSON>ς αισχρολογίας", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Αποκλείστε τις δημοσιεύσεις που περιέχουν τις πιο συχνά αναφερόμενες προσβλητικές λέξεις.", "app.containers.Admin.Moderation.project": "Project", "app.containers.Admin.Moderation.read": "Αναγνωσμένο", "app.containers.Admin.Moderation.readMore": "Read more", "app.containers.Admin.Moderation.removeFlagsError": "Η κατάργηση της/των προειδοποίηση(εων) δεν ήταν δυνατή. Δοκιμάστε ξανά.", "app.containers.Admin.Moderation.rowsPerPage": "Γρα<PERSON><PERSON><PERSON>ς ανά σελίδα", "app.containers.Admin.Moderation.settings": "Ρυθμίσεις", "app.containers.Admin.Moderation.settingsSavingError": "Η αποθήκευση δεν ήταν δυνατή. Προσπαθήστε να αλλάξετε τη ρύθμιση ξανά.", "app.containers.Admin.Moderation.show": "Εμφάνιση", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Οι ρυθμίσεις ενημερώθηκαν με επιτυχία.", "app.containers.Admin.Moderation.type": "Τύπος", "app.containers.Admin.Moderation.unread": "Μη αναγνωσμένο", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Αυτή η σελίδα αποτελείται από τις ακόλουθες ενότητες. Μπορείτε να τις ενεργοποιήσετε/απενεργοποιήσετε και να τις επεξεργαστείτε όπως απαιτείται.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Sections", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "View page", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Δεν εμφανίζεται στη σελίδα", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Εμφανίζεται στη σελίδα", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Attachments", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Προσθέστε αρχεία (μέγιστο 50 MB) που θα είναι διαθέσιμα για λήψη από τη σελίδα.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Bottom info section", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Προσθέστε το δικό σας περιεχόμενο στην προσαρμόσιμη ενότητα στο κάτω μέρος της σελίδας.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Edit", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Λίστα εκδηλώσεων", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Προβολή συμβάντων που σχετίζονται με τα έργα.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero banner", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Προσ<PERSON><PERSON>μόστε την εικόνα και το κείμενο του μπάνερ της σελίδας.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Λίστα έργων", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Προβολή των έργων με βάση τις ρυθμίσεις σας. Μπορείτε να κάνετε προεπισκόπηση των έργων που θα προβληθούν.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Top info section", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Προσθέστε το δικό σας περιεχόμενο στην προσαρμόσιμη ενότητα στο επάνω μέρος της σελίδας.", "app.containers.Admin.PagesAndMenu.addButton": "Προσθήκη στη γραμμή πλοήγησης", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Όνομα στη γραμμή πλοήγησης", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή τη σελίδα; Αυτό δεν μπορεί να αναιρεθεί.", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Άλλες διαθέσιμες σελίδες", "app.containers.Admin.PagesAndMenu.components.savePage": "Save page", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON> successfully saved", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Attachments (max 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Η αποθήκευση των συνημμένων αρχείων δεν ήταν δυνατή", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Τα αρχεία δεν πρέπει να είναι μεγαλύτερα από 50Mb. Τα προστιθέμενα αρχεία θα εμφανίζονται στο κάτω μέρος αυτής της σελίδας", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Τα συνημμένα αρχεία αποθηκεύτηκαν", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Συνημμένα | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Attachments", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Αποθήκευση και ενεργοποίηση συνημμένων", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Αποθήκευση συνημμένων", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Παροχή περιεχομένου για όλες τις γλώσσες", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Δεν κατέστη δυνατή η αποθήκευση του κάτω τμήματος πληροφοριών", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Το κάτω τμήμα πληροφοριών αποθηκεύτηκε", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Κάτω τμήμα πληροφοριών", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Αποθήκευση και ενεργοποίηση της ενότητας πληροφοριών κάτω μέρους", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Αποθήκευση της ενότητας πληροφοριών κάτω μέρους", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Please select at least one tag", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Success", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Με βάση την περιοχή", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "Με βάση τις ετικέτες", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Επεξεργασία προσαρμοσμένης σελίδας", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Συνδεδεμένα έργα", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Επιλέξτε ποια έργα και σχετικά συμβάντα μπορούν να προβληθούν στη σελίδα.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Η σελίδα δημιουργήθηκε επιτυχώς", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Η σελίδα αποθηκεύτηκε επιτυχώς", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Η προσαρμοσμένη σελίδα αποθηκεύτηκε", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Τ<PERSON><PERSON><PERSON>ος στη γραμμή πλοήγησης", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Δημιουργία προσαρμοσμένης σελίδας | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Δημιουργία προσαρμοσμένης σελίδας", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Κανένα", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Ρυθμίσεις σελίδας", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Αποθήκευση προσαρμοσμένης σελίδας", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Επιλέξτε μια περιοχή", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Επιλεγμένη περιοχή", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Επιλεγμένες ετικέτες", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "The slug can only contain regular, lowercase letters (a-z), numbers (0-9) and hyphens (-). The first and last characters cannot be hyphens. Consecutive hyphens (--) are forbidden.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Πρέπει να εισαγάγετε ένα slug", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Εισάγετε έναν τίτλο σε κάθε γλώσσα", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Enter a title", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Προβολή προσαρμοσμένης σελίδας", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Επεξεργασία προσαρμοσμένης σελίδας | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Page content", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.automatedEmailsLinkText": "αυτοματοποιημένα μηνύματα ηλεκτρονικού ταχυδρομείου", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Edit", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Δεν υπάρχουν διαθέσιμα έργα με βάση τις ρυθμίσεις σας {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Αυτό το έργο δεν διαθέτει φίλτρο ετικέτας ή περιοχής, επομένως δεν θα προβληθεί κανένα έργο.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Λίστα έργων | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "ρυθμίσεις σελίδας", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projects list", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Τα παρακάτω έργα θα προβληθούν σε αυτή τη σελίδα με βάση τις ρυθμίσεις σας {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "ΠΡΟΕΠΙΛΟΓΗ", "app.containers.Admin.PagesAndMenu.deleteButton": "Delete", "app.containers.Admin.PagesAndMenu.editButton": "Edit", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.heroBannerError": "Η αποθήκευση του κύριου μπάνερ δεν ήταν δυνατή", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Το κύριο μπάνερ αποθηκεύτηκε", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Αποθήκευση κύριου μπάνερ", "app.containers.Admin.PagesAndMenu.homeTitle": "Home", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Provide content for at least one language", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Σελίδες & μενού | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Κατάργηση από τη γραμμή πλοήγησης", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Αποθήκευση και ενεργοποίηση κύριου μπάνερ", "app.containers.Admin.PagesAndMenu.title": "Σελίδες & μενού", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Success", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Content", "app.containers.Admin.PagesAndMenu.topInfoError": "Η αποθήκευση της ενότητας κορυφα<PERSON>ων πληροφοριών δεν ήταν δυνατή", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Η ενότητα με τις κορυφαίες πληροφορίες αποθηκεύτηκε", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Τμήμα κορυφαίων πληροφοριών | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Ενότητα κορυφα<PERSON>ων πληροφοριών", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Αποθήκευση και ενεργοποίηση της ενότητας κορυφαίων πληροφοριών", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Αποθήκευση της ενότητας κορυφα<PERSON>ων πληροφοριών", "app.containers.Admin.PagesAndMenu.viewButton": "Προβολή", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Αποκλεισμένοι χρήστες", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Διαχείριση αποκλεισμένων χρηστών.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Διαγρα<PERSON><PERSON> ομάδας", "app.containers.Admin.Users.GroupsHeader.editGroup": "Επεξεργασία ομάδας", "app.containers.Admin.Users.GroupsPanel.allUsers": "Εγγεγραμμένοι χρήστες", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Ομάδες", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Αποκτήστε μια επισκόπηση όλων των ατόμων και των οργανισμών που έχουν εγγραφεί στην πλατφόρμα. Προσθέστε μια επιλογή χρηστών σε μη αυτόματες ομάδες ή έξυπνες ομάδες.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Εκκρεμεί πρόσκληση", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.buyOneAditionalSeat": "Αγοράστε μία επιπλέον θέση", "app.containers.Admin.Users.changeUserRights": "Change user rights", "app.containers.Admin.Users.confirm": "Επιβεβαίωση", "app.containers.Admin.Users.confirmAdminQuestion": "Είστε σίγουροι ότι θέλετε να δώσετε δικαιώματα διαχειριστή της πλατφόρμας {name} ;", "app.containers.Admin.Users.confirmNormalUserQuestion": "Είστε σίγουροι ότι θέλετε να ορίσετε το {name} ως κανονικό χρήστη;", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Είστε σίγουροι ότι θέλετε να ορίσετε το {name} ως κανονικό χρήστη; Λάβετε υπόψη ότι θα χάσει τα δικαιώματα διαχειριστή σε όλα τα έργα και τους φακέλους που του έχουν ανατεθεί κατά την επιβεβαίωση.", "app.containers.Admin.Users.deleteUser": "Διαγρα<PERSON><PERSON> χρήστη", "app.containers.Admin.Users.email": "Email", "app.containers.Admin.Users.folderManager": "Διαχειριστής φακέλων", "app.containers.Admin.Users.helmetDescription": "Λίστ<PERSON> χρηστών στο διαχειριστή", "app.containers.Admin.Users.helmetTitle": "Διαχειριστής - π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εργαλείων χρηστών", "app.containers.Admin.Users.inviteUsers": "Πρόσκληση χρηστών", "app.containers.Admin.Users.name": "Name", "app.containers.Admin.Users.options": "Options", "app.containers.Admin.Users.permissionToBuy": "Για να δώσετε στο {name} δικαιώματα διαχειριστή, πρέπει να αγοράσετε 1 επιπλέον θέση.", "app.containers.Admin.Users.platformAdmin": "Διαχειριστής πλατφόρμας", "app.containers.Admin.Users.projectManager": "Διαχειριστής έργου", "app.containers.Admin.Users.reachedLimitMessage": "Έχετε φτάσει στο όριο των θέσεων στο πρόγραμμά σας, θα προστεθεί 1 επιπλέον θέση για το {name} .", "app.containers.Admin.Users.registeredUser": "Εγγεγρα<PERSON><PERSON><PERSON>νος χρήστης", "app.containers.Admin.Users.seeProfile": "Δείτε το προφίλ", "app.containers.Admin.Users.setAsAdmin": "Ορισμός ως διαχειριστής", "app.containers.Admin.Users.setAsNormalUser": "Ορισμός ως κανονικός χρήστης", "app.containers.Admin.Users.userBlockModal.allDone": "Όλα έτοιμα", "app.containers.Admin.Users.userBlockModal.blockAction": "Αποκλ<PERSON>ισμ<PERSON>ς χρήστη", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Το περιεχόμενο αυτού του χρήστη δεν θα αφαιρεθεί μέσω αυτής της ενέργειας. Μην ξεχάσετε να μετριάσετε το περιεχόμενό του αν χρειαστεί.", "app.containers.Admin.Users.userBlockModal.blocked": "Αποκλεισμένο", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Αυτ<PERSON>ς ο χρήστης έχει μπλοκαριστεί από το {from}. Ο αποκλεισμός διαρκεί μέχρι το {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Ακύρωση", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Είστε σίγουροι ότι θέλετε να ξεμπλοκάρετε το {name};", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} μπλοκά<PERSON>εται μέχρι το {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 ημέρα} other {{numberOfDays} days}}", "app.containers.Admin.Users.userBlockModal.header": "Αποκλ<PERSON>ισμ<PERSON>ς χρήστη", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Λόγ<PERSON>", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Αυτό θα κοινοποιηθεί στον αποκλεισμένο χρήστη.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Ο επιλεγμένος χρήστης δεν θα είναι σε θέση να συνδεθεί στην πλατφόρμα {daysBlocked}. Εάν επιθυμείτε να το ανατρέψετε αυτό, μπορείτε να τον ξεμπλοκάρετε από τη λίστα των αποκλεισμένων χρηστών.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Ξεμπλοκάρετε το", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON><PERSON>, θέλω να ξεμπλοκάρω αυτόν τον χρήστη", "app.containers.Admin.Users.userDeletionConfirmation": "Μόνιμη κατάργηση αυτού του χρήστη;", "app.containers.Admin.Users.userDeletionFailed": "Προέκυψε σφάλμα κατά τη διαγραφή αυτού του χρήστη, παρακαλούμε προσπαθήστε ξανά.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Γνωριμίες χρηστών", "app.containers.Admin.Users.youCantDeleteYourself": "Δεν μπορείτε να διαγράψετε τον δικό σας λογαριασμό μέσω της σελίδας διαχείρισης χρηστών", "app.containers.Admin.Users.youCantUnadminYourself": "Δεν μπορείτε να παραιτηθείτε από το ρόλο σας ως διαχειριστής τώρα", "app.containers.Admin.emails.addCampaignTitle": "Create a new email", "app.containers.Admin.emails.allUsers": "Registered users", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Τα αυτοματοποιημένα μηνύματα ηλεκτρονικού ταχυδρομείου αποστέλλονται αυτόματα και ενεργοποιούνται από τις ενέργειες ενός χρήστη. Μπορείτε να απενεργοποιήσετε ορισμένα από αυτά για όλους τους χρήστες της πλατφόρμας σας. Τα υπόλοιπα αυτοματοποιημένα μηνύματα ηλεκτρονικού ταχυδρομείου δεν μπορούν να απενεργοποιηθούν επειδή είναι απαραίτητα για την ορθή λειτουργία της πλατφόρμας σας.", "app.containers.Admin.emails.automatedEmails": "Αυτοματοποιημένα μηνύματα ηλεκτρονικού ταχυδρομείου", "app.containers.Admin.emails.automatedEmailsDigest": "Το email θα σταλεί μόνο αν υπάρχει περιεχόμενο", "app.containers.Admin.emails.automatedEmailsRecipients": "Χρήστες που θα λάβουν αυτό το email", "app.containers.Admin.emails.automatedEmailsTriggers": "Γεγ<PERSON>ν<PERSON><PERSON> που ενεργοποιεί αυτό το email", "app.containers.Admin.emails.changeRecipientsButton": "Αλλαγ<PERSON> παραληπτών", "app.containers.Admin.emails.clickOnButtonForExamples": "Κάντε κλικ στο παρακ<PERSON>τω κουμπί για να δείτε παραδείγματα αυτού του email στη σελίδα υποστήριξής μας.", "app.containers.Admin.emails.confirmSendHeader": "Αποστολ<PERSON> email σε όλους τους χρήστες;", "app.containers.Admin.emails.deleteButtonLabel": "Delete", "app.containers.Admin.emails.draft": "Προσχέδιο", "app.containers.Admin.emails.editButtonLabel": "Edit", "app.containers.Admin.emails.editCampaignTitle": "Επεξεργασία εκστρατείας", "app.containers.Admin.emails.failed": "Απέτυχε", "app.containers.Admin.emails.fieldBody": "Μήνυμα", "app.containers.Admin.emails.fieldBodyError": "Δώστε ένα μήνυμα ηλεκτρονικού ταχυδρομείου για όλες τις γλώσσες", "app.containers.Admin.emails.fieldReplyTo": "Οι απαντήσεις θα πρέπει να πηγαίνουν στο", "app.containers.Admin.emails.fieldReplyToEmailError": "Παρέχετε μια διεύθυνση email στη σωστή μορφή, για παράδειγμα <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Παροχή διεύθυνσης email", "app.containers.Admin.emails.fieldReplyToTooltip": "Μπορείτε να επιλέξετε πού θα αποστέλλονται οι απαντήσεις στο email σας.", "app.containers.Admin.emails.fieldSender": "Από", "app.containers.Admin.emails.fieldSenderError": "Δώστε τον αποστολέα του email", "app.containers.Admin.emails.fieldSenderTooltip": "Μπορείτε να αποφασίσετε ποιον θα βλέπουν οι παραλήπτες ως αποστολέα του email.", "app.containers.Admin.emails.fieldSubject": "Θέμα ηλεκτρονικού ταχυδρομείου", "app.containers.Admin.emails.fieldSubjectError": "Παρέχετε ένα θέμα ηλεκτρονικού ταχυδρομείου για όλες τις γλώσσες", "app.containers.Admin.emails.fieldSubjectTooltip": "Αυτό θα εμφανίζεται στη γραμμή θέματος του email και στην επισκόπηση των εισερχομένων του χρήστη. Κάντε το σαφές και ελκυστικό.", "app.containers.Admin.emails.fieldTo": "<PERSON>ρ<PERSON>", "app.containers.Admin.emails.fieldToTooltip": "Μπορείτε να επιλέξετε τις ομάδες χρηστών που θα λάβουν το email σας", "app.containers.Admin.emails.formSave": "Αποθήκευση ως προσχέδιο", "app.containers.Admin.emails.from": "Από:", "app.containers.Admin.emails.groups": "Groups", "app.containers.Admin.emails.helmetDescription": "Αποστολή μη αυτόματων μηνυμάτων ηλεκτρονικού ταχυδρομείου σε ομάδες χρηστών και ενεργοποίηση αυτοματοποιημένων εκστρατειών", "app.containers.Admin.emails.previewSentConfirmation": "Ένα email προεπισκόπησης έχει σταλεί στη διεύθυνση email σας", "app.containers.Admin.emails.previewTitle": "Προεπισκόπηση", "app.containers.Admin.emails.seeEmailHereText": "Μό<PERSON>ις σταλεί ένα τέτοιο μήνυμα ηλεκτρονικού ταχυδρομείου, θα μπορείτε να το ελέγξετε εδώ.", "app.containers.Admin.emails.send": "Send", "app.containers.Admin.emails.sendNowButton": "Στείλτε τώρα", "app.containers.Admin.emails.sendTestEmailButton": "Στείλτε μου ένα δοκιμαστικό email", "app.containers.Admin.emails.sendTestEmailTooltip": "Όταν κάνετε κλικ σε αυτόν τον σύνδεσμο, ένα δοκιμαστικ<PERSON> email θα σταλεί μόνο στη δική σας διεύθυνση email. Αυτό σας επιτρέπει να ελέγξετε πώς φαίνεται το email στην πραγματικότητα.", "app.containers.Admin.emails.senderRecipients": "Αποστολέας και παραλήπτες", "app.containers.Admin.emails.sending": "Αποστολή", "app.containers.Admin.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "Πρόκειται για μηνύματα ηλεκτρονικού ταχυδρομείου που αποστέλλονται στους χρήστες", "app.containers.Admin.emails.subject": "Θέμα:", "app.containers.Admin.emails.supportButtonLabel": "Δείτε παραδείγματα στη σελίδα υποστήριξης", "app.containers.Admin.emails.to": "Προς:", "app.containers.Admin.emails.toAllUsers": "Θέλετε να στείλετε αυτό το μήνυμα ηλεκτρονικού ταχυδρομείου σε όλους τους εγγεγραμμένους χρήστες;", "app.containers.Admin.emails.viewExample": "Προβολή", "app.containers.Admin.ideas.import": "Εισαγωγή", "app.containers.Admin.messaging.helmetTitle": "Messaging", "app.containers.Admin.messaging.newProjectPhaseModal.cancel": "Ακύρωση", "app.containers.Admin.messaging.newProjectPhaseModal.turnOff": "<PERSON>α<PERSON>, απενεργοποιήστε", "app.containers.Admin.messaging.newProjectPhaseModal.turnOn": "<PERSON>α<PERSON>, ενεργοποιήστε", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "This image is part of the folder card; the card that summarizes the folder and is shown on the homepage for example. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "This image is shown at the top of the folder page. For more information on recommended image resolutions, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "visit our support center", "app.containers.Admin.projects.all.components.archived": "Archived", "app.containers.Admin.projects.all.components.draft": "Draft", "app.containers.Admin.projects.all.components.manageButtonLabel": "Edit", "app.containers.Admin.projects.all.copyProjectButton": "Copy project", "app.containers.Admin.projects.all.copyProjectError": "There was an error copying this project, please try again later.", "app.containers.Admin.projects.all.deleteFolderButton1": "Delete folder", "app.containers.Admin.projects.all.deleteFolderConfirm": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτόν τον φάκελο; Θα διαγραφούν επίσης όλα τα έργα εντός του φακέλου. Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "app.containers.Admin.projects.all.deleteFolderError": "Υπήρξε πρόβλημα με την αφαίρεση αυτού του φακέλου. Προσπαθήστε ξανά.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Delete project", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Σίγουρα θέλετε να διαγράψετε αυτό το έργο Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.", "app.containers.Admin.projects.all.deleteProjectError": "Υπήρξε σφάλμα κατά τη διαγραφή αυτού του έργου, παρακαλούμε δοκιμάστε ξανά αργότερα.", "app.containers.Admin.projects.project.survey.cancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalButton": "Continue", "app.containers.Admin.projects.project.survey.consentModalCancel": "Cancel", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "I agree to using OpenAI as a data processor for this project", "app.containers.Admin.projects.project.survey.consentModalText1": "By continuing you agree to the using OpenAI as a data processor for this project.", "app.containers.Admin.projects.project.survey.consentModalText2": "The OpenAI APIs power the automated text summaries and parts of the automated tagging experience.", "app.containers.Admin.projects.project.survey.consentModalText3": "We only send what users wrote in their surveys, ideas and comments to the OpenAI APIs, never any information from their profile.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI will not use this data for further training of its models. More information on how OpenAI handles data privacy can be found {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "here", "app.containers.Admin.projects.project.survey.consentModalTitle": "Before you continue", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Delete", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Are you sure you want to delete this analysis? This action cannot be undone.", "app.containers.Admin.projects.project.survey.viewAnalysis": "View", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "This is some text. You can edit and format it by using the editor in the panel on the right.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Participants", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Project results", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Report summary", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Add the goal of the project, participation methods used, and the outcome", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Visitors", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Ο τίτλος είναι ήδη κατειλημμένος", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "No project", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Δεν μπορείτε να αντιγράψετε αυτή την έκθεση, επειδή περιέχει δεδομένα στα οποία δεν έχετε πρόσβαση.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Δεν μπορείτε να επεξεργαστείτε αυτή την αναφορά, επειδή περιέχει δεδομένα στα οποία δεν έχετε πρόσβαση.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Σίγουρα θέλετε να διαγράψετε το \"{reportName}\"; Αυτή η ενέργεια δεν μπορεί να ανακληθεί.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Σίγουρα θέλετε να διαγράψετε αυτή την αναφορά; Αυτή η ενέργεια δεν μπορεί να ανακληθεί.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Delete", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Διπλό", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Edit", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Τροποποιημένο {days, plural, no {# ημέρες} one {# ημέρα} other {# ημέρες}} πριν", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "An error occurred when trying to create this report. Please try again later.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Start with a blank page", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Αναφορά τίτλου", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Δημιουργ<PERSON><PERSON> αναφοράς", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Print to PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Start with a project template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Report template", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "A report with this title already exists. Please pick a different title.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Share as PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "To share with everyone, print the report as a PDF.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Share as web link", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "This web link is only accessible to admin users.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Share", "app.containers.Admin.reporting.contactToAccess": "Creating a custom report is part of the premium license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Όλες οι εκθέσεις", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Create a report", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Customise your report and share it with internal stakeholders or community with a web link.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Οι αναφορές σας θα εμφανίζονται εδώ.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Ανα<PERSON><PERSON><PERSON><PERSON>ς αναζήτησης", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Εκθέσεις προόδου", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Πρόκειται για αναφορές που δημιουργούνται από τον Διαχειριστή Κυβερνητικής Επιτυχίας", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Οι αναφορές σας", "app.containers.Admin.reporting.helmetDescription": "Σελίδα δημιουρ<PERSON><PERSON><PERSON>ς εκθέσεων του Διαχειριστή", "app.containers.Admin.reporting.helmetTitle": "Reporting", "app.containers.Admin.reporting.printPrepare": "Preparing to print...", "app.containers.Admin.reporting.reportBuilder": "Εργα<PERSON><PERSON><PERSON><PERSON> κατ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αναφορών", "app.containers.Admin.reporting.reportHeader": "Κεφαλίδα αναφοράς", "app.containers.Admin.tools.apiTokens.createTokenButton": "Δημιουργία νέου token", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Ακύρωση", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Δημιουργήστε ένα νέο token για χρήση με το δημόσιο API μας.", "app.containers.Admin.tools.apiTokens.createTokenError": "Δώστε ένα όνομα για το διακριτικό σας", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Δημιουργία token", "app.containers.Admin.tools.apiTokens.createTokenName": "Όνομα", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Δώστε ένα όνομα στο διακριτικό σας", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Το διακριτικ<PERSON> σας έχει δημιουργηθεί", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Κλείστε το", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "Αντιγραφή {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Αντιγραφή!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Δημιουργήστε ένα νέο token", "app.containers.Admin.tools.apiTokens.createdAt": "Δημιουργήθηκε", "app.containers.Admin.tools.apiTokens.delete": "Διαγραφή token", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτό το διακριτικό;", "app.containers.Admin.tools.apiTokens.description": "Διαχειριστείτε τα κουπόνια API για το δημόσιο API μας. Για περισσότερες πληροφορίες, δείτε το {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Τελευταία χρήση", "app.containers.Admin.tools.apiTokens.link": "Τεκμηρίωση API", "app.containers.Admin.tools.apiTokens.name": "Όνομα", "app.containers.Admin.tools.apiTokens.noTokens": "Δεν έχετε ακόμα μάρκες.", "app.containers.Admin.tools.apiTokens.title": "Δημόσια κουπόνια API", "app.containers.Admin.tools.learnMore": "Μάθετε περισσότερα", "app.containers.Admin.tools.managePublicAPIKeys": "Διαχείριση κλειδιών API", "app.containers.Admin.tools.manageWidget": "Διαχείριση widget", "app.containers.Admin.tools.manageWorkshops": "Διαχείριση εργαστηρίων", "app.containers.Admin.tools.publicAPIDescription": "Διαχειριστείτε τα διαπιστευτήρια για τη δημιουργία προσαρμοσμένων ενοποιήσεων στο δημόσιο API μας.", "app.containers.Admin.tools.publicAPIImage": "Δημόσια εικόνα API", "app.containers.Admin.tools.publicAPITitle": "Δημόσια πρόσβαση API", "app.containers.Admin.tools.toolsLabel": "Εργαλεία", "app.containers.Admin.tools.widgetDescription": "Μπορείτε να δημιουργήσετε ένα widget, να το προσαρμόσετε και να το προσθέσετε στον δικό σας ιστότοπο για να προσελκύσετε κόσμο σε αυτή την πλατφόρμα.", "app.containers.Admin.tools.widgetImage": "Εικόνα widget", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Πραγματοποιήστε ζωντανές συνεδριάσεις μέσω βίντεο, διευκολύνετε ταυτόχρονες ομαδικές συζητήσεις και συζητήσεις. Συγκεντρώστε πληροφορίες, ψηφίστε και καταλήξτε σε συναίνεση, όπως ακριβώς θα κάνατε εκτός σύνδεσης.", "app.containers.Admin.tools.workshopsImage": "Εικόνα εργαστηρίων", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Διαδικτυα<PERSON><PERSON> εργαστήρια διαβούλευσης", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "συνολικοί χρήστες στην πλατφόρμα", "app.containers.AdminPage.DashboardPage._blank": "άγνωστο", "app.containers.AdminPage.DashboardPage.allGroups": "Όλες οι ομάδες", "app.containers.AdminPage.DashboardPage.allProjects": "Όλα τα έργα", "app.containers.AdminPage.DashboardPage.allTime": "Όλοι οι χρόνοι", "app.containers.AdminPage.DashboardPage.comments": "Comments", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Comments", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Απαιτε<PERSON><PERSON><PERSON><PERSON> ένα βασικό σύνολο δεδομένων για τη μέτρηση της αντιπροσώπευσης των χρηστών της πλατφόρμας.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Αναμένεται σύντομα", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Εργαζόμαστε επί του παρόντος στον πίνακα εργαλείων {fieldName}, θα είναι διαθέσιμος σύντομα", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# στοιχείο είναι} other {# στοιχεία είναι}} κρυμμένα σε αυτό το γράφημα. Αλλάξτε σε {tableViewLink} για να δείτε όλα τα δεδομένα.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} για την εγγραφή του χρήστη", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} από τους {total} χρήστες που περιλαμβάνονται ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Εμφάνιση {numberOfHiddenItems} ακόμη", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Προαιρετικό", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Παρακαλούμε δώστε ένα βασικό σύνολο δεδομένων.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Βαθμολογ<PERSON><PERSON> αντιπροσωπευτικότητας:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Αυτή η βαθμολογία αντικατοπτρίζει με πόση ακρίβεια τα δεδομένα των χρηστών της πλατφόρμας αντικατοπτρίζουν το συνολικό πληθυσμό. Μάθετε περισσότερα σχετικά με το {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Required", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Υποβολή δεδομένων βάσης", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "προβολή πίνακα", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Συνολικός πληθυσμός", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Users", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Προσθήκη ηλικιακής ομάδας", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} και άνω", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Ηλικιακή ομάδα", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Η ηλικιακή ομάδα (ηλικιακ<PERSON>ς ομάδες) {upperBound} και άνω δεν περιλαμβάνεται.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Ηλικιακή ομάδα {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Ηλικια<PERSON><PERSON>ς ομάδες", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "και άνω", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Εφαρμογή παραδείγ<PERSON>α<PERSON>ος ομαδοποίησης", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Εκκαθάριση όλων", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "From", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Ορίστε τις ηλικια<PERSON><PERSON>ς ομάδες ώστε να ευθυγραμμιστούν με το βασικό σας σύνολο δεδομένων.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Εύρ<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "To", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Επεξεργα<PERSON><PERSON>α ηλικιακών ομάδων", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Αυτό το στοιχείο δεν θα υπολογιστεί.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "See less", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Δείτε {numberOfHiddenItems} ακόμη...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μήνας (προαιρετικό)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Ηλικια<PERSON><PERSON><PERSON> ομάδες (Έτος γέννησης)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Coming soon", "app.containers.AdminPage.DashboardPage.components.Field.complete": "<PERSON>λ<PERSON><PERSON><PERSON>ς", "app.containers.AdminPage.DashboardPage.components.Field.default": "Προεπιλογή", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Παρακαλούμε συμπληρώστε όλες τις ενεργοποιημένες επιλογές ή απενεργοποιήστε τις επιλογές που θέλετε να παραλείψετε από το γράφημα. Πρέπει να συμπληρωθεί τουλάχιστον μία επιλογή.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Μη πλήρης", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Αριθμός συνολικών κατοίκων", "app.containers.AdminPage.DashboardPage.components.Field.options": "Options", "app.containers.AdminPage.DashboardPage.components.Field.save": "Save", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Saved", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Παρακαλούμε {setAgeGroupsLink} πρώτα για να ξεκινήσετε την εισαγωγή των βασικών δεδομένων.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "ορίστε ηλικιακ<PERSON>ς ομάδες", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "<PERSON><PERSON><PERSON><PERSON> χρόνος απόκρισης: {days} ημέρες", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "<PERSON><PERSON><PERSON><PERSON> αριθμ<PERSON>ς ημερών για απάντηση", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Ανατροφοδότηση που έχει δοθεί", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Κατάσταση εισήγησης", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Εισηγή<PERSON><PERSON><PERSON><PERSON> αν<PERSON> κατάσταση", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Αριθμός εισηγήσεων", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Επίσημη ενημέρωση", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Ποσοστ<PERSON> εισηγήσεων", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> απόκρισης", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Η κατάσταση άλλαξε", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Total", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Επεξεργα<PERSON>ία δεδομένων βάσης", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "πώς υπολογίζουμε τις βαθμολογίες αντιπροσωπευτικότητας", "app.containers.AdminPage.DashboardPage.continuousType": "Without a timeline", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Αθροιστικ<PERSON> σύνολο", "app.containers.AdminPage.DashboardPage.customDateRange": "Προσαρμοσμένο", "app.containers.AdminPage.DashboardPage.day": "day", "app.containers.AdminPage.DashboardPage.false": "λάθος", "app.containers.AdminPage.DashboardPage.female": "γυναίκα", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Top 5 εισροές αν<PERSON> αντιδράσεις", "app.containers.AdminPage.DashboardPage.fromTo": "from {from} to {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Πίνα<PERSON>ας εργαλείων για τις δραστηριότητες στην πλατφόρμα", "app.containers.AdminPage.DashboardPage.helmetTitle": "Σελίδα πίνακα εργαλείων διαχειριστή", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Επιλογή πόρου για εμφάνιση ανά έργο", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Επιλογή πόρου για εμφάνιση ανά ετικέτα", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Επιλογή ομάδας χρηστών", "app.containers.AdminPage.DashboardPage.male": "άνδρας", "app.containers.AdminPage.DashboardPage.month": "month", "app.containers.AdminPage.DashboardPage.noData": "There is no data to be shown.", "app.containers.AdminPage.DashboardPage.noPhase": "No phase created for this project", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Ο αριθμός των συμμετεχόντων που δημοσίευσαν πληροφορίες, αντ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν ή σχολίασαν.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Απεχθάνεται το", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Μου αρέσει", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Συνολικές αντιδράσεις", "app.containers.AdminPage.DashboardPage.overview.management": "Διαχείριση", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Έργα & Συμμετοχή", "app.containers.AdminPage.DashboardPage.overview.showLess": "Show less", "app.containers.AdminPage.DashboardPage.overview.showMore": "Show more", "app.containers.AdminPage.DashboardPage.participationPerProject": "Συμμετοχή ανά έργο", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participation per tag", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Προηγούμενες 30 ημέρες", "app.containers.AdminPage.DashboardPage.previous90Days": "Προηγούμενες 90 ημέρες", "app.containers.AdminPage.DashboardPage.previousWeek": "Προηγούμενη εβδομάδα", "app.containers.AdminPage.DashboardPage.previousYear": "Προηγούμενο έτος", "app.containers.AdminPage.DashboardPage.projectType": "Project type : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Αντιδρά<PERSON>εις", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Αυτό το βασικ<PERSON> σύνολο δεδομένων απαιτείται για τον υπολογισμό της αντιπροσωπευτικότητας των χρηστών της πλατφόρμας σε σύγκριση με τον συνολικό πληθυσμό.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Please provide a base dataset.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Δείτε πόσο αντιπροσωπευτικο<PERSON> είναι οι χρήστες της πλατφόρμας σας σε σύγκριση με τον συνολικό πληθυσμό - με βάση τα δεδομένα που συλλέγονται κατά την εγγραφή των χρηστών. Μάθετε περισσότερα σχετικά με την {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Δείτε πόσο αντιπροσωπευτικ<PERSON><PERSON> είναι οι χρήστες της πλατφόρμας σας σε σύγκριση με τον συνολικό πληθυσμό - με βάση τα δεδομένα που συλλέγονται κατά την εγγραφή των χρηστών.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Αντιπροσώπευση κοινότητας", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "<PERSON><PERSON><PERSON><PERSON> στον πίνακα εργαλείων", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Κανένα από τα ενεργοποιημένα πεδία εγγραφής δεν υποστηρίζεται προς το παρόν.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Εδώ μπορείτε να εμφανίσετε/αποκρύψετε στοιχεία στον πίνακα εργαλείων και να εισαγάγετε τα βασικά δεδομένα. Μόνο τα ενεργοποιημένα πεδία για την {userRegistrationLink} θα εμφανίζονται εδώ.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Edit base data", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "εγγραφ<PERSON> χρήστη", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Submit base data", "app.containers.AdminPage.DashboardPage.resolutionday": "σε ημέρες", "app.containers.AdminPage.DashboardPage.resolutionmonth": "σε μήνες", "app.containers.AdminPage.DashboardPage.resolutionweek": "σε εβδομάδες", "app.containers.AdminPage.DashboardPage.selectProject": "Select project", "app.containers.AdminPage.DashboardPage.selectedProject": "φίλτρο τρέχοντος έργου", "app.containers.AdminPage.DashboardPage.selectedTopic": "φίλτρο τρέχουσας ετικέτας", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Ανακαλύψτε τι συμβαίνει στην πλατφόρμα σας.", "app.containers.AdminPage.DashboardPage.tabOverview": "Επισκόπηση", "app.containers.AdminPage.DashboardPage.tabReports": "Reports", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Αναπαράσταση", "app.containers.AdminPage.DashboardPage.tabUsers": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.timelineType": "Timeline", "app.containers.AdminPage.DashboardPage.titleDashboard": "Πίνα<PERSON><PERSON>ς εργαλείων", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "This {period}", "app.containers.AdminPage.DashboardPage.true": "σωστό", "app.containers.AdminPage.DashboardPage.unspecified": "αδιευκρίνιστο", "app.containers.AdminPage.DashboardPage.users": "Users", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Χρήστες ανά ηλικία", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Χρήστ<PERSON><PERSON> ανά γεωγραφική περιοχή", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Χρήστες ανά φύλο", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Εγγρα<PERSON><PERSON>ς", "app.containers.AdminPage.DashboardPage.week": "week", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Συμβουλές για την επιλογή εικόνας favicon: επιλέξτε μια απλή εικόνα, καθώς το μέγεθος της εικόνας που εμφανίζεται είναι πολύ μικρό. Η εικόνα θα πρέπει να αποθηκευτεί ως PNG, και θα πρέπει να είναι τετράγωνη με διαφανές φόντο (ή λευκό φόντο, εάν είναι απαραίτητο). Το favicon σας θα πρέπει να οριστεί μόνο μία φορά, καθώς οι αλλαγές απαιτούν κάποια τεχνική υποστήριξη.", "app.containers.AdminPage.FaviconPage.save": "Save", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Κάτι πήγε στραβά, παρακαλούμε δοκιμάστε ξανά αργότερα.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Success!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Add", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Delete", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Διαχειριστές φακέλων", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Οι διαχειριστές φακέλων μπορούν να επεξεργάζονται την περιγραφή του φακέλου, να δημιουργούν νέα έργα εντός του φακέλου και να έχουν δικαιώματα διαχείρισης έργων σε όλα τα έργα εντός του φακέλου. Δεν μπορούν να διαγράψουν έργα και δεν έχουν πρόσβαση σε έργα που δεν βρίσκονται στο φάκελό τους. Μπορείτε να {projectManagementInfoCenterLink} για να βρείτε περισσότερες πληροφορίες σχετικά με τα δικαιώματα διαχείρισης έργων.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Δεν βρέθηκε αντιστοιχία", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "επισκεφθείτε το Κέντρο Βοήθειας", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Αναζήτηση χρηστών", "app.containers.AdminPage.FoldersEdit.addToFolder": "Προσθήκη σε φάκελο", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Archived", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Διαγρα<PERSON><PERSON> αυτού του φακέλου", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Description", "app.containers.AdminPage.FoldersEdit.draftStatus": "Draft", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Προσθήκη αρχείων σε αυτό το φάκελο", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Τα αρχεία δεν πρέπει να είναι μεγαλύτερα από 50Mb. Τα προστιθέμενα αρχεία θα εμφανίζονται στη σελίδα του φακέλου.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Περιγρα<PERSON><PERSON>ς", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Δεν υπάρχουν έργα σε αυτόν το φάκελο. Επιστρέψτε στην κύρια καρτέλα Έργα για να δημιουργήσετε και να προσθέσετε έργα.", "app.containers.AdminPage.FoldersEdit.folderName": "Όνομα φακέλου", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.FoldersEdit.multilocError": "Όλα τα πεδία κειμένου πρέπει να συμπληρωθούν για κάθε γλώσσα.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Δεν υπάρχουν έργα που μπορείτε να προσθέσετε σε αυτόν το φάκελο.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Εικόνα κάρτας φακέλου", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Δικαιώματα", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Έργα φακέλου", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Έργα που προστίθενται σε αυτόν το φάκελο", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Έργα που μπορείτε να προσθέσετε σε αυτόν το φάκελο", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Επιλέξτε αν αυτός ο φάκελος είναι \"προσχέδιο\", \"δημοσιευμένος\" ή \"αρχειοθετημένος\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Δημοσιευμένος", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Κατάργηση από φάκελο", "app.containers.AdminPage.FoldersEdit.save": "Save", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Success!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Σύντομη περιγραφή", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "εμφανίζ<PERSON>ται στην αρχική σελίδα", "app.containers.AdminPage.FoldersEdit.statusLabel": "Κατάσταση δημοσίευσης", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Εξηγήστε γιατί τα έργα ανήκουν μαζ<PERSON>, ορίστε μια οπτική ταυτότητα και κοινοποιείστε πληροφορίες.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Title", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Δημιουργ<PERSON>α νέου φακέλου", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.url": "ΔΙΕΎΘΥΝΣΗ URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Προβολή φακέλου", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Προσ<PERSON>ρμόστε την εικόνα και το κείμενο του κύριου μπάνερ.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> μπάνερ", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Save hero banner", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Επεξεργαστείτε τους όρους και τις προϋποθέσεις και την πολιτική απορρήτου της πλατφόρμας σας. Άλλες σελίδες, συμπεριλαμβανομένων των σελίδων Πληροφορίες και Συχνές ερωτήσεις, μπορούν να επεξεργαστούν στην καρτέλα {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Πολιτικές της πλατφόρμας", "app.containers.AdminPage.PagesEdition.privacy-policy": "Privacy Policy", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Όροι και προϋποθέσεις", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Λίστα έργων στην πλατφόρμα", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Πίνα<PERSON><PERSON>ς εργαλείων έργων", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Δημιουργήστε νέα έργα ή διαχειριστείτε υπάρχοντα έργα.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projects", "app.containers.AdminPage.ProjectDashboard.published": "Published", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Κλείσιμο του πίνακα ρυθμίσεων", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Διάταξη στήλης", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Περιγραφή αρχικής σελίδας", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Εμφανίζ<PERSON><PERSON><PERSON><PERSON> στην κάρτα έργου στην αρχική σελίδα.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Εμφανίζεται στη σελίδα του έργου. Περιγράψτε με σαφήνεια περί τίνος πρόκειται το έργο, τι περιμένετε από τους χρήστες σας και τι μπορούν να περιμένουν από εσάς.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Κάτι πήγε στραβά, παρακαλούμε δοκιμάστε ξανά αργότερα", "app.containers.AdminPage.ProjectDescription.preview": "Preview", "app.containers.AdminPage.ProjectDescription.save": "Save", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectDescription.saved": "Saved!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Αποφασίστε ποιο μήνυμα θέλετε να δώσετε στο κοινό σας. Επεξεργαστείτε το έργο σας και εμπλουτίστε το με εικόνες, β<PERSON><PERSON><PERSON><PERSON><PERSON>, συνημμένα αρχεία,... Αυτές οι πληροφορίες βοηθούν τους επισκέπτες να καταλάβουν περί τίνος πρόκειται το έργο σας.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Περιγραφή έργου", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> χώρος", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Συμπεριλάβετε πλαίσιο", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Κάθετο ύψος", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Μεγάλο", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Medium", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Μικρό", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Ακύρωση επεξεργασίας", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Το προεπιλεγμένο γεωγραφικ<PERSON> πλάτος του κεντρικού σημείου του χάρτη. Δέχεται μια τιμή μεταξύ -90 και 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Το προεπιλεγμένο γεωγραφικ<PERSON> μήκος του κεντρικού σημείου του χάρτη. Δέχεται τιμή μεταξύ -90 και 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Επεξεργα<PERSON><PERSON>α επιπέδου χάρτη", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Επεξεργασ<PERSON>α επιπέδου", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectEdit.MapTab.here": "here", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Εισαγωγή αρχείου GeoJSON", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Προεπιλεγμένο γεωγραφικό πλάτος", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Χρώμα επιπέδου", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Όλα τα χαρακτηριστικά στο επίπεδο θα διαμορφωθούν με αυτό το χρώμα. Αυτό το χρώμα θα αντικαταστήσει επίσης κάθε υπάρχον στυλ στο αρχείο GeoJSON.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Εικονίδι<PERSON> δείκτη", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Προαιρετικά επιλέξτε ένα εικονίδιο που θα εμφανίζεται στους δείκτες. Κάντε κλικ στο {url} για να δείτε τη λίστα των εικονιδίων που μπορείτε να επιλέξετε.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Όνομα επιπέδου", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Αυτό το όνομα επιπέδου εμφανίζεται στο υπόμνημα του χάρτη", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Συμβουλή εργαλείου επιπέδου", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Αυτό το κείμενο εμφανίζεται ως συμβουλή εργαλείου όταν περνάτε με το ποντίκι πάνω από τα χαρακτηριστικά του επιπέδου στο χάρτη", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Επίπεδα χάρτη", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Προεπιλεγμένο γεωγραφικό μήκος", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Προεπιλεγ<PERSON><PERSON>ν<PERSON> κέντρο και ζουμ χάρτη", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Προσαρμόστε την προβολή του χάρτη, συμπεριλαμβανομένης της μεταφόρτωσης και της διαμόρφωσης των επιπέδων του χάρτη και της ρύθμισης του κέντρου και του επιπέδου ζουμ του χάρτη.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Διαμόρφωση χάρτη", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Κατάργηση επιπέδου", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Save", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "άρθρο υποστήριξης", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Επί<PERSON><PERSON><PERSON><PERSON> ζουμ χάρτη", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Το προεπιλεγμένο επίπεδο ζουμ του χάρτη. Δέχεται μια τιμή μεταξύ 1 και 17, όπου το 1 είναι πλήρως ζουμαρισμένο (<PERSON><PERSON><PERSON> ο κόσμος είναι ορατός) και το 17 είναι πλήρως ζουμαρισμένο (τα τετράγωνα και τα κτίρια είναι ορατά)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Ανωνυμοποίηση όλων των δεδομένων χρήστη", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Όλες οι εισροές των χρηστών στην έρευνα θα είναι ανώνυμες πριν καταγραφούν.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Οι χρήστες θα εξακολουθούν να πρέπει να συμμορφώνονται με τις απαιτήσεις συμμετοχής στην καρτέλα \"Δικαιώματα πρόσβασης\". Τα δεδομένα του προφίλ χρήστη δεν θα είναι διαθέσιμα στην εξαγωγή δεδομένων της έρευνας.", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Προσθέστε μια επιλογή απάντησης", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Προσθήκη ερώτησης δημοσκόπησης", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Ακύρωση", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Επεξεργα<PERSON><PERSON>α επιλογής απάντησης", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Αποθήκευση επιλογών απάντησης", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Επεξεργα<PERSON><PERSON><PERSON> επιλογ<PERSON>ν απάντησης", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Επεξεργα<PERSON><PERSON>α ερώτησης", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Εξαγωγή των αποτελεσμάτων της δημοσκόπησης", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Ο μέγιστος αριθμός επιλογών είναι μεγαλύτερος από τον αριθμό των επιλογών", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Πολλαπλές επιλογές", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "No options", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Όλες οι ερωτήσεις πρέπει να έχουν επιλογές απαντήσεων", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Μόνο μία επιλογή", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Οι ερωτηθέντες της δημοσκόπησης έχουν μόνο μία επιλογή", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Διαχειριστείτε τις επιλογές απαντήσεων για: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Εδώ μπορείτε να δημιουργήσετε ερωτήσεις δημοσκόπησης, να ορίσετε τις επιλογές απαντήσεων από τις οποίες οι συμμετέχοντες μπορούν να επιλέξουν για κάθε ερώτηση, να αποφασίσετε αν θέλετε οι συμμετέχοντες να μπορούν να επιλέξουν μόνο μία επιλογή απάντησης (μία επιλογή) ή πολλαπλές επιλογές απάντησης (πολλαπλές επιλογές) και να εξάγετε τα αποτελέσματα της δημοσκόπησης. Μπορείτε να δημιουργήσετε πολλαπλές ερωτήσεις δημοσκόπησης στο πλαίσιο μιας δημοσκόπησης.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Αποθήκευση", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Μία επιλογή", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Ρυθμίσεις και αποτελέσματα δημοσκοπήσεων", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Μέγιστο όριο λανθασμένων ερωτήσεων", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Δώστε ανατροφοδότηση, προσθέστε ετικέτες ή αντιγράψτε τις δημοσιεύσεις στην επόμενη φάση του έργου.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Διαχείριση εισηγήσεων", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Εξαγωγή των αποτελεσμάτων της έρευνας (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "<PERSON><PERSON><PERSON>, μπορείτε να κατεβάσετε τα αποτελέσματα της(των) έρευνας(ών) Typeform στο πλαίσιο αυτού του έργου ως αρχείο Excel.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Αποτελέσματα έρευνας", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Survey", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Συμβουλευτείτε τις απαντήσεις της έρευνας", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Προσθήκη αιτίας", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Χρησιμοποιήστε το για να εξηγήσετε τι απαιτείται από τους εθελοντές και τι μπορούν να περιμένουν.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Δεν ήταν δυνατή η αποθήκευση επειδή η φόρμα περιέχει σφάλματα.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Image", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Μια αιτία είναι μια δράση ή δραστηριότητα για την οποία οι συμμετέχοντες μπορούν να προσφέρουν εθελοντικά.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Επεξεργασία αιτίας", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Add a description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Add a title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Εξαγωγή εθελοντών", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Νέα αιτία", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Save", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Εδώ μπορείτε να ρυθμίσετε τις αιτίες για τις οποίες οι χρήστες μπορούν να συμμετέχουν εθελοντικά και να κατεβάσετε τους εθελοντές.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Εθελοντισμός", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {no participants} one {# participant} other {# participants}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "κατανομή του προϋπολογισμού", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Αναθέστε έναν προϋπολογισμό στις επιλογές και ζητήστε από τους συμμετέχοντες να επιλέξουν τις προτιμώμενες επιλογές που εντάσσονται στο πλαίσιο του συνολικού προϋπολογισμού.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Κατανομή του προϋπολογισμού", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Η δυνατότητα σχολιασμού από τους χρήστες μπορεί να επηρεάσει τη διαδικασία ψηφοφορίας.", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Προεπιλεγμένη προβολή των επιλογών", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Δράσεις για τους χρήστες", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Επιλέξτε ποιες πρόσθετες ενέργειες μπορούν να κάνουν οι χρήστες.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Σταθερό ποσό", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Ε<PERSON>ν παραμείνει κενό, η επιλογή θα είναι προεπιλεγμένη σε \"vote\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Μάθετε περισσότερα για το πότε να χρησιμοποιήσετε το <b> {voteTypeDescription} </b> στο {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Μέγιστες ψήφοι ανά επιλογή", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Μέγιστος αριθμός ψήφων", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Μπορείτε να περιορίσετε τον αριθμό των ψήφων που μπορεί να δώσει ένας χρήστης συνολικά (με μέγιστο όριο μία ψήφο ανά επιλογή).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "πολλαπλ<PERSON><PERSON> ψήφοι ανά επιλογή", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Στους χρήστες δίνεται ένα ποσό μάρκων για να το διανείμουν μεταξύ των επιλογών", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Πολλα<PERSON><PERSON><PERSON><PERSON> ψήφοι ανά επιλογή", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Αριθμός ψήφων ανά χρήστη", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Επισκόπηση ανάλυσης επιλογών", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Επιλο<PERSON><PERSON><PERSON> προς ψήφιση", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "μία ψή<PERSON>ος ανά επιλογή", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Οι χρήστες μπορούν να επιλέξουν να εγκρίνουν οποιαδήποτε από τις επιλογές", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Μία <PERSON>ή<PERSON>ος ανά επιλογή", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Απεριόριστα", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Πώς πρέπει να ονομάζεται η ψηφοφορία;", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Π.χ. κου<PERSON><PERSON><PERSON><PERSON><PERSON>, πόντο<PERSON>, πιστώσεις άνθρακα...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Π.χ. κου<PERSON><PERSON><PERSON><PERSON>, π<PERSON>ντ<PERSON>, πίστωση άνθρακα...", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Κάθε μέθοδος ψηφοφορίας έχει διαφορετικές προρυθμίσεις", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Μέθοδος ψηφοφορίας", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Η μέθοδος ψηφοφορίας καθορίζει τους κανόνες του τρόπου με τον οποίο οι χρήστες ψηφίζουν", "app.containers.AdminPage.ProjectEdit.addNewInput": "Προσθήκη εισήγησης", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Ετικέτες έργου", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Ανώνυμη δημοσκόπηση", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Όταν είναι ενεργοποιημένη, εί<PERSON><PERSON><PERSON> αδύνατο να δείτε ποιος ψήφισε τι. Οι χρήστες εξακολουθούν να χρειάζονται έναν λογαριασμό και μπορούν να ψηφίσουν μόνο μία φορά.", "app.containers.AdminPage.ProjectEdit.archived": "Archived", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archived", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Αυτή η περιοχή δεν μπορεί να διαγραφεί επειδή χρησιμοποιείται για την προβολή έργων στις παρακάτω προσαρμοσμένες σελίδες. Για να μπορέσετε να διαγράψετε την περιοχή, θα πρέπει πρώτα να καταργήσετε τη σύνδεση της περιοχής από τη σελίδα ή να διαγράψετε τη σελίδα.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Όλες οι περιοχές", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Το έργο θα εμφανίζεται σε κάθε φίλτρο περιοχής.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Φίλτρο περιοχής", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Τα έργα μπορούν να φιλτραριστούν στην αρχική σελίδα χρησιμοποιώντας περιοχές. Οι περιοχές μπορούν να οριστούν στο {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Δεν υπάρχει συγκεκριμένη περιοχή", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Το έργο δεν θα εμφανίζεται κατά το φιλτράρισμα με βάση την περιοχή.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Επιλογή", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Το έργο θα εμφανιστε<PERSON> στο(α) επιλεγμένο(α) φίλτρο(α) περιοχής.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Κάρτ<PERSON>ς", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Επιλέξτε μια μέθοδο ψηφοφορίας και βάλτε τους χρήστες να δώσουν προτεραιότητα μεταξύ μερικών διαφορετικών επιλογών.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Διεξαγωγή ψηφοφορ<PERSON><PERSON>ς ή άσκησης ιεράρχησης", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Δημιουργία έργου από πρότυπο", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Δημιουρ<PERSON><PERSON><PERSON> εξωτερικής έρευνας", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Δημιουργ<PERSON>α έρευνας εντός της πλατφόρμας", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Ρύθμιση έρευνας χωρίς να φύγετε από την πλατφόρμα μας.", "app.containers.AdminPage.ProjectEdit.createPoll": "Δημιουργ<PERSON>α δημοσκόπησης", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Ρύθμιση ερωτηματολόγιου πολλαπλών επιλογών.", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Ενσωματώστε μια έρευνα Typeform, Φόρμας Google Form, Enalyzer, SurveyXact, Qualtrics, SmartSurvey, Snap Survey ή Φορμών Microsoft.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Μπορείτε να ορίσετε την προεπιλεγμένη σειρά εμφάνισης των δημοσιεύσεων στην κύρια σελίδα του έργου.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sorting", "app.containers.AdminPage.ProjectEdit.departments": "Τμήματα", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Description", "app.containers.AdminPage.ProjectEdit.disabled": "Disabled", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Αριθμός αντιπαθειών ανά συμμετέχοντα", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Ενεργοποίηση αντιπάθειας", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Συλλογή ανατροφοδότησης για ένα έγγραφο", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Ενσωματώστε ένα διαδραστικό PDF και συλλέξτε σχόλια και σχόλια με το Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Enabled", "app.containers.AdminPage.ProjectEdit.draft": "Draft", "app.containers.AdminPage.ProjectEdit.draftStatus": "Draft", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.enabled": "Enabled", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Επιλέξτε ποιες συμμετοχικές δράσεις μπορούν να αναλάβουν οι χρήστες.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Events", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Συνημμένα αρχεία (μέγιστο 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Τα αρχεία δεν πρέπει να είναι μεγαλύτερα από 50MB. Τα προστιθέμενα αρχεία θα εμφανίζονται στη σελίδα πληροφοριών του έργου.", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Εύρεση εθελοντών", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Ζητήστε από τους συμμετέχοντες να συμμετάσχουν εθελοντικά σε δραστηριότητες και αιτίες.", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Επιλέξτε έναν φάκελο για να προσθέσετε αυτό το έργο.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Custom content", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Submissions to this form have started to come in. Changes to the form may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Form successfully saved", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Survey end", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Από ένα πρότυπο", "app.containers.AdminPage.ProjectEdit.generalTab": "General", "app.containers.AdminPage.ProjectEdit.google_forms": "Φόρμες Google", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Header image", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Συλλογή εισηγήσεων και ανατροφοδότησης", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Δημιουρ<PERSON><PERSON><PERSON> ή συλλογή εισροών, αν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ων ή/και σχολίων. Επιλέξτε μεταξύ διαφορετικών τύπων εισροών: συλλ<PERSON><PERSON><PERSON> ιδεώ<PERSON>, αν<PERSON><PERSON><PERSON><PERSON><PERSON> ε<PERSON><PERSON><PERSON>ο<PERSON>ώ<PERSON>, ερώτηση και απάντηση, εντοπισμός ζητημάτων και άλλα.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Ποιος είναι υπεύθυνος για την επεξεργασία των δημοσιεύσεων;", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Όλες οι νέες εισηγήσεις σε αυτό το έργο θα ανατίθενται σε αυτό το άτομο. Ο υπεύθυνος ανάθεσης μπορεί να αλλάξει στο {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Σχολιασμ<PERSON>ς δημοσιεύσεων", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Φόρμα εισήγησης", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "διαχείριση εισηγήσεων", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Input manager", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Υποβολή δημοσιεύσεων", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Αντίδραση σε εισροές", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Προεπιλεγμένη προβολή", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Επιλέξτε την προεπιλεγμένη προβολή για την εμφάνιση της εισήγησης: κάρτες σε προβολή πλέγματος ή καρφίτσες σε χάρτη. Οι συμμετέχοντες μπορούν να εναλλάσσουν χειροκίνητα μεταξύ των δύο προβολών.", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Ενσωματώστε το URL του Konveio", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Αρ<PERSON><PERSON><PERSON><PERSON><PERSON> likes ανά συμμετέχοντα", "app.containers.AdminPage.ProjectEdit.limited": "Περιορισμένη", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Φόρτωση περισσότερων προτύπων", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Map", "app.containers.AdminPage.ProjectEdit.mapTab": "Map", "app.containers.AdminPage.ProjectEdit.maxDislikes": "<PERSON><PERSON><PERSON><PERSON>στα dislikes", "app.containers.AdminPage.ProjectEdit.maxLikes": "Μέγιστο αριθμό likes", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Ο μέγιστος αριθμός ψήφων ανά επιλογή πρέπει να είναι μικρότερος ή ίσος με το συνολικό αριθμό ψήφων", "app.containers.AdminPage.ProjectEdit.maximum": "Μέγιστο", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Οι συμμετέχοντες δεν μπορούν να υπερβούν αυτόν τον προϋπολογισμό κατά την υποβολή του καλαθιού τους.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Φόρμες Microsoft", "app.containers.AdminPage.ProjectEdit.minimum": "Ελάχιστο", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Απαιτήστε από τους συμμετέχοντες να πληρούν έναν ελάχιστο προϋπολογισμό για να υποβάλουν το καλάθι τους (πληκτρολογήστε \"0\" εάν δεν θέλετε να ορίσετε ελάχιστο).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.ProjectEdit.moreDetails": "Περισσότερες λεπτομέρειες", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.newProject": "Νέο έργο", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Most recent", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Μη έγκυρο ποσό", "app.containers.AdminPage.ProjectEdit.noFolder": "Δεν υπάρχει φάκελος", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Δεν βρέθηκαν πρότυπα", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Παρακαλούμε εισάγετε έναν τίτλο έργου", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Μη έγκυρος αριθμός", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Oldest", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Ορατό μόνο από τον διαχειριστή", "app.containers.AdminPage.ProjectEdit.optionNo": "Δεν υπάρχει", "app.containers.AdminPage.ProjectEdit.optionYes": "Ναι (επιλέξτε φάκελο)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Επίπεδα συμμετοχής", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Τι θέλετε να κάνετε;", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Επιλέξτε τον τρόπο με τον οποίο μπορούν να συμμετέχουν οι χρήστες.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Μπορείτε να καθορίσετε ποιος μπορεί να εκτελέσει κάθε ενέργεια και να κάνετε πρόσθετες ερωτήσεις στους συμμετέχοντες για να συλλέξετε περισσότερες πληροφορίες.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Απαιτήσεις & ερωτήσεις συμμετεχόντων", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Δικαιώματα πρόσβασης", "app.containers.AdminPage.ProjectEdit.pollTab": "Poll", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Οι περισσότερες αντιδράσεις", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Εικόνα κάρτας έργου", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "This image is part of the project card; the card that summarizes the project and is shown on the homepage for example.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "This image is shown at the top of the project page.\n\n    For more information on recommended image resolutions, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Διαχείριση έργου", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Οι διαχειριστές έργων μπορούν να επεξεργάζονται έργα, να διαχειρίζονται δημοσιεύσεις και να στέλνουν email στους συμμετέχοντες. Μπορείτε να {moderationInfoCenterLink} για να βρείτε περισσότερες πληροφορίες σχετικά με τα δικαιώματα που έχουν εκχωρηθεί στους διαχειριστές έργων.", "app.containers.AdminPage.ProjectEdit.projectName": "Όνομα έργου", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Τύ<PERSON>ος έργου", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Τα έργα με χρονοδιάγραμμα έχουν σαφή αρχή και τέλος και μπορούν να έχουν διαφορετικές φάσεις. Τα έργα χωρίς χρονοδιάγραμμα είναι συνεχή.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Ο τύπος έργου δεν μπορεί να αλλάξει αργότερα.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Μπορείτε να ορίσετε το έργο να είναι αόρατο σε ορισμένους χρήστες.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Ορατότητα του έργου", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Published", "app.containers.AdminPage.ProjectEdit.purposes": "Σκοποί", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Random", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Προέκυψε σφάλμα κατά την αποθήκευση των δεδομένων σας. Παρακαλούμε προσπαθήστε ξανά.", "app.containers.AdminPage.ProjectEdit.saveProject": "Save", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Success!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Η φόρμα σας έχει αποθηκευτεί!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Αναζήτηση στα πρότυπα", "app.containers.AdminPage.ProjectEdit.selectGroups": "Επιλέξτε ομάδα(ες)", "app.containers.AdminPage.ProjectEdit.shareInformation": "Κοινοποιείστε πληροφορίες", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Ρυθμίστε και εξατομικεύστε το έργο σας.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "visit our support center", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Add survey content", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Cancel", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# choices} one {# choice} other {# choices}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "Ναι, θέλω να φύγω", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Linear scale", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Multiple choice - choose many", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "No survey responses yet", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Open for responses", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Optional", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Are you sure you want to leave?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Οι τρέχουσες αλλαγές σας δεν θα αποθηκευτούν.", "app.containers.AdminPage.ProjectEdit.survey.required2": "Required", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Multiple choice - choose one", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Survey successfully saved", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Survey", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Total {count} responses", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "View survey", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL ενσωμάτωσης", "app.containers.AdminPage.ProjectEdit.surveyService": "Υπηρεσία", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Μπορείτε να βρείτε περισσότερες πληροφορίες σχετικά με τον τρόπο ενσωμάτωσης μιας έρευνας {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Αυτή η ετικέτα δεν μπορεί να διαγραφεί επειδή χρησιμοποιείται για την προβολή έργων στις παρακάτω προσαρμοσμένες σελίδες.\nΓια να μπορέσετε να διαγράψετε την ετικέτα , θα πρέπει πρώτα να καταργήσετε τη σύνδεση της ετικέτας από τη σελίδα ή να διαγράψετε τη σελίδα.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Γενικές ρυθμίσεις για το έργο", "app.containers.AdminPage.ProjectEdit.titleLabel": "Title", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Επιλέξτε έναν τίτλο που να είναι σύντομος, ελκυ<PERSON>τι<PERSON><PERSON><PERSON> και σαφής. Θα εμφανίζεται στην αναπτυσσόμενη επισκόπηση και στις κάρτες του έργου στην αρχική σελίδα.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Tags", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Επιλέξτε {topicsCopy} για αυτό το έργο. Οι χρήστες μπορούν να τα χρησιμοποιήσουν για να φιλτράρουν τα έργα με βάση αυτά.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Συνολικός προϋπολογισμός", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Trending", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Unassigned", "app.containers.AdminPage.ProjectEdit.unlimited": "Απεριόριστα", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Χρήση προτύπου", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Προβολή έργου", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Volunteering", "app.containers.AdminPage.ProjectEdit.voteTermError": "Οι όροι ψηφοφορίας πρέπει να καθορίζονται για όλες τις τοπικές γλώσσες", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# ομάδες μπορούν να προβάλλουν} one {# ομάδα μπορεί να προβάλλει} other {# ομάδες μπορούν να προβάλλουν}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Προσθήκη εκδήλωσης", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Έναρξη", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Τέλος", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εκδήλωση; Δεν υπάρχει τρόπος να το αναιρέσετε!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Περιγραφή της εκδήλωσης", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Επεξεργα<PERSON>ία εκδήλωσης", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Τα συνημμένα αρχεία εμφαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κάτω από την περιγραφή της εκδήλωσης.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Location", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Δημιουργ<PERSON>α νέας εκδήλωσης", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Save", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Δεν μπορέσαμε να αποθηκεύσουμε τις αλλαγές σας, παρακαλούμε προσπαθήστε ξανά.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Success!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Συνδέστε τις επερχόμενες εκδηλώσεις με αυτό το έργο και εμφανίστε τις στη σελίδα εκδηλώσεων του έργου.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Τί<PERSON><PERSON><PERSON> και ημερομηνίες", "app.containers.AdminPage.ProjectEvents.titleEvents": "Εκδηλώ<PERSON><PERSON><PERSON>ς έργου", "app.containers.AdminPage.ProjectEvents.titleLabel": "Όνομα εκδήλωσης", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Σύμπτυξη όλων των πεδίων", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Περιγρα<PERSON><PERSON> πεδίου", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Edit input form", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Enabled", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Συμπεριλάβετε αυτό το πεδίο.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Ανάπτυξη όλων των πεδίων", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Input form", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Καθορίστε ποιες πληροφορίες πρέπει να παρέχονται, προσθέστε σύντομες περιγραφές ή οδηγίες για να καθοδηγήσετε τις απαντήσεις των συμμετεχόντων και καθορίστε αν κάθε πεδίο είναι προαιρετικό ή υποχρεωτικό", "app.containers.AdminPage.ProjectIdeaForm.required": "Required", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Απαιτ<PERSON><PERSON><PERSON><PERSON><PERSON> η συμπλήρωση αυτού του πεδίου.", "app.containers.AdminPage.ProjectIdeaForm.save": "Save", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Saved!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "View form", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Αυτοματοποιημένα μηνύματα ηλεκτρονικού ταχυδρομείου", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Μπορείτε να ρυθμίσετε τα μηνύματα ηλεκτρονικού ταχυδρομείου που ενεργοποιούνται σε επίπεδο φάσης", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Ημερομηνίες", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή τη φάση;", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Περιγραφ<PERSON> φάσης", "app.containers.AdminPage.ProjectTimeline.disabledProjectPhaseEmailMessage": "Αυτή η επιλογή είναι προς το παρόν απενεργοποιημένη για όλα τα έργα στη σελίδα {automatedEmailsLink} . Κ<PERSON><PERSON><PERSON> συνέπεια, δεν θα μπορείτε να ενεργοποιήσετε μεμονωμένα αυτή τη ρύθμιση για αυτή τη φάση.", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "End Date", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "There was an error submitting the form, please try again.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Saved!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Ημερομηνία έναρξης", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Όνομα φάσης", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Ανέβασμα συνημμένων", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Ορολογία (φίλτρο αρχικής σελίδας)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Πώς πρέπει να ονομάζονται οι ετικέτες στο φίλτρο της αρχικής σελίδας; Π.χ. ετικέτες, κατηγορίες, τμήματα, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Οι ετικέτες μπορούν να διαμορφωθούν {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "here", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Όρος για μία ετικέτα (ενικός)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Όρος για πολλαπλές ετικέτες (πληθυντικός)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "ετικέτες", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Προσθήκη πεδίου", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Προσθήκη νέου πεδίου καταχώρισης", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Προσθήκη επιλογής", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Μορφή απάντησης", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Παροχή μορφής απάντησης", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Επιλογή απάντησης", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Παροχή επιλογής απάντησης για όλες τις γλώσσες", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Αποθήκευση επιλογής απάντησης", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Επιλογή απάντησης αποθηκεύτηκε επιτυχώς", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Answer choices", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Πεδία", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Σύρετε και αποθέστε τα πεδία για να καθορίσετε τη σειρά με την οποία εμφανίζονται στη φόρμα εγγραφής.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Προεπιλεγμένο πεδίο", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Προαι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κείμενο που εμφαν<PERSON><PERSON><PERSON><PERSON>α<PERSON> κάτω από το όνομα του πεδίου στη φόρμα εγγραφής.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Οι επιλογές απαντήσεων για τον τόπο διαμονής μπορούν να οριστούν στο {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Επεξεργα<PERSON><PERSON>α επιλογής απάντησης", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Description", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Όνομα πεδίου", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Παρέχετε ένα όνομα πεδίου για όλες τις γλώσσες", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Ρυθμίσεις πεδίου", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Ναι-όχι (πλαί<PERSON>ιο ελέγχου)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Date", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Μεγάλη απάντηση", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Πολλαπλή επιλογή (επιλέξτε πολλαπλές)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Αριθμητική τιμή", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Πολλαπλή επιλογή (επιλέξτε μία)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Σύντομη απάντηση", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Καρτέλα Γεωγρα<PERSON>ικές περιοχές", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Κρυφό πεδίο", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Να είναι υποχρεωτική η απάντηση σε αυτό το πεδίο;", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Προσαρμοσμένα πεδία", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Προσθήκη επιλογής απάντησης", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Cancel", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την επιλογή απάντησης ερώτησης εγγραφής; Όλες οι εγγραφές στις οποίες συγκεκριμένοι χρήστες απάντησαν με αυτή την επιλογή θα διαγραφούν οριστικά. Αυτή η ενέργεια δεν μπορεί να ανακληθεί.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Είστε σίγουρος ότι θέλετε να διαγράψετε αυτή την ερώτηση εγγραφής; Όλες οι απαντήσεις που έχουν δώσει οι χρήστες σε αυτή την ερώτηση θα διαγραφούν οριστικά και δεν θα τίθεται πλέον σε έργα ή προτάσεις. Αυτή η ενέργεια δεν μπορεί να ανακληθεί.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Required", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Αποθήκευση πεδίου", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Πεδ<PERSON><PERSON> αποθηκεύτηκε επιτυχώς", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Δύο στήλες", "app.containers.AdminPage.SettingsPage.addAreaButton": "Προσθήκη γεωγραφικής περιοχής", "app.containers.AdminPage.SettingsPage.addTopicButton": "Add tag", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την περιοχή;", "app.containers.AdminPage.SettingsPage.areaTerm": "Όρος για μία περιοχή (ενικός)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "περιοχή", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.areasTerm": "Όρος για πολλαπλές περιοχές (πληθυντικός)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "περιοχές", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Select at least one language.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Εμφάνιση avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Εμφάνιση εικόνων προφίλ των συμμετεχόντων και του αριθμού τους σε μη εγγεγραμμένους επισκέπτες", "app.containers.AdminPage.SettingsPage.bannerHeader": "Κείμενο κεφαλίδας", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Κείμενο κεφαλίδας για μη εγγεγραμμένους επισκέπτες", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Κείμενο υποκεφαλίδας για μη εγγεγραμμένους επισκέπτες", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Κείμενο υποκεφαλίδας", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Κείμεν<PERSON> μ<PERSON>νερ", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Προβολή προεπισκόπησης για", "app.containers.AdminPage.SettingsPage.brandingDescription": "Προσθέστε το λογότυπό σας και ορίστε τα χρώματα της πλατφόρμας.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Επωνυμία πλατφόρμας", "app.containers.AdminPage.SettingsPage.cancel": "Cancel", "app.containers.AdminPage.SettingsPage.chooseLayout": "Διάταξη", "app.containers.AdminPage.SettingsPage.color_primary": "Πρωτεύον χρώμα", "app.containers.AdminPage.SettingsPage.color_secondary": "Δευτερεύον χρώμα", "app.containers.AdminPage.SettingsPage.color_text": "Χρώμα κειμένου", "app.containers.AdminPage.SettingsPage.colorsTitle": "Χρώματα", "app.containers.AdminPage.SettingsPage.confirmHeader": "Are you sure you want to delete this tag?", "app.containers.AdminPage.SettingsPage.contentModeration": "Εποπτεία περιεχομένου", "app.containers.AdminPage.SettingsPage.ctaHeader": "Κουμπιά", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Κεφαλίδα προσαρμοσμένης σελίδας | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Custom", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Κείμενο κουμπιού", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Σύνδεσμος κουμπιού", "app.containers.AdminPage.SettingsPage.defaultTopic": "Προεπιλεγμένη ετικέτα", "app.containers.AdminPage.SettingsPage.delete": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Αυτό θα διαγράψει την ετικέτα από όλες τις υπάρχουσες δημοσιεύσεις. Αυτή η αλλαγή θα εφαρμοστεί σε όλα τα έργα.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Προσθέστε και διαγράψτε τις ετικέτες που θα θέλατε να χρησιμοποιήσετε στην πλατφόρμα σας για την κατηγοριοποίηση των δημοσιεύσεων. Μπορείτε να προσθέσετε τις ετικέτες σε συγκεκριμένα έργα στο {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Επιφάνεια εργασίας", "app.containers.AdminPage.SettingsPage.editFormTitle": "Περιοχή επεξεργασίας", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Επεξεργα<PERSON>ία ετικέτας", "app.containers.AdminPage.SettingsPage.fieldDescription": "Περιγραφή περιοχής", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Αυτή η περιγραφή προορίζεται μόνο για εσωτερική συνεργασία και δεν εμφανίζεται στους χρήστες.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Όνομα περιοχής", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Δώστε ένα όνομα περιοχής για όλες τις γλώσσες", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Το όνομα που επιλέγετε για κάθε περιοχή μπορεί να χρησιμοποιηθεί ως επιλογή πεδίου εγγραφής και για το φιλτράρισμα έργων στην αρχική σελίδα.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Αποθήκευση ετικέτας", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Tag name", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Παρέχετε ένα όνομα ετικέτας για όλες τις γλώσσες", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Το όνομα που επιλέγετε για κάθε ετικέτα θα είναι ορατό στους χρήστες της πλατφόρμας", "app.containers.AdminPage.SettingsPage.fixedRatio": "Διαφημιστικ<PERSON> πλαίσιο διορθωμένης αναλογίας", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Αυτό ο τύπος διαφημιστικού πλαισίου λειτουργεί καλύτερα με εικόνες που δεν πρέπει να έχουν περικοπεί, ό<PERSON>ω<PERSON> εικόνες με κείμενο, έν<PERSON> λογότυπο ή συγκεκριμένα στοιχεία που είναι βασικά για τους πολίτες σας. Αυτό το διαφημιστικό πλαίσιο αντικαθίσταται με ένα σταθερό πλαίσιο με το βασικό χρώμα όταν συνδέονται οι χρήστες. Μπορείτε να ρυθμίσετε αυτό το χρώμα στις γενικές ρυθμίσεις. Μπορείτε να βρείτε περισσότερες πληροφορίες για την προτεινόμενη χρήση εικόνων εδώ {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "γνωσια<PERSON><PERSON> βάση", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Μπάνερ πλήρους πλάτους", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Αυτό το διαφημιστικό πλαίσιο εκτείνεται σε όλο το πλάτος για ένα εξαιρετικό οπτικό αποτέλεσμα. Η εικόνα θα προσπαθήσει να καλύψει το μεγαλύτερο δυνατό χώρο, με αποτέλεσμα να μην εμφανίζεται πάντα. Μπορείτε να συνδυάσετε αυτό το διαφημιστικό πλαίσιο με μια χρωματική επικάλυψη. Μπορείτε να βρείτε περισσότερες πληροφορίες για την προτεινόμενη χρήση εικόνων εδώ {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.header": "Μπάνερ αρχικής σελίδας", "app.containers.AdminPage.SettingsPage.headerDescription": "Προσ<PERSON><PERSON><PERSON>όστε την εικόνα και το κείμενο του μπάνερ της αρχικής σελίδας.", "app.containers.AdminPage.SettingsPage.header_bg": "Εικόνα μπάνερ", "app.containers.AdminPage.SettingsPage.helmetDescription": "Σελίδα ρυθμίσεων διαχειριστή", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin settings page", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Προσθέστε το δικό σας περιεχόμενο στην προσαρμόσιμη ενότητα στο κάτω μέρος της αρχικής σελίδας.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Κεφαλίδα αρχικής σελίδας | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Χρώμα επικάλυψης εικόνας", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Αδιαφάνεια επικάλυψης εικόνας", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Ανίχνευση ακατάλληλου περιεχομένου", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Αυτό<PERSON><PERSON><PERSON><PERSON> εντο<PERSON>ι<PERSON>μ<PERSON>ς ακατάλληλου περιεχομένου που δημοσιεύεται στην πλατφόρμα.", "app.containers.AdminPage.SettingsPage.languages": "Γλώσ<PERSON><PERSON>ς", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Μπορείτε να επιλέξετε πολλαπλές γλώσσες στις οποίες θέλετε να προσφέρετε την πλατφόρμα σας στους χρήστες. Θα χρειαστεί να δημιουργήσετε περιεχόμενο για κάθε επιλεγμένη γλώσσα.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Activity", "app.containers.AdminPage.SettingsPage.logo": "Λογότυπο", "app.containers.AdminPage.SettingsPage.noHeader": "Παρακαλούμε ανεβάστε μια εικόνα κεφαλίδας (1440 x 480 px)", "app.containers.AdminPage.SettingsPage.no_button": "Δεν υπάρχει κουμπί", "app.containers.AdminPage.SettingsPage.organizationName": "Όνομα πόλης ή οργανισμού", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Provide an organization name or city for all languages.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Enable overlay", "app.containers.AdminPage.SettingsPage.phone": "Τηλέφωνο", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Διαμόρφωση πλατφόρμας", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Profanity blocker", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Αποκλεισμ<PERSON>ς εισηγήσεων, προ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και σχολίων που περιέχουν τις πιο συχνά αναφερόμενες προσβλητικές λέξεις", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Το κείμενο αυτό εμφανίζεται στην αρχική σελίδα πάνω από τα έργα.", "app.containers.AdminPage.SettingsPage.projectsSettings": "ρυθμίσεις έργου", "app.containers.AdminPage.SettingsPage.projects_header": "Επικεφαλίδα έργων", "app.containers.AdminPage.SettingsPage.registrationFields": "Πεδία εγγραφής", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Παρέχετε μια σύντομη περιγραφή στην κορυφή της φόρμας εγγραφής σας.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Εγγραφή", "app.containers.AdminPage.SettingsPage.save": "Save", "app.containers.AdminPage.SettingsPage.saveArea": "Περιοχή αποθήκευσης", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Success!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Εγγραφείτε\"", "app.containers.AdminPage.SettingsPage.signed_in": "Κουμπί για εγγεγραμμένους επισκέπτες", "app.containers.AdminPage.SettingsPage.signed_out": "Κουμπί για μη εγγεγραμμένους επισκέπτες", "app.containers.AdminPage.SettingsPage.signupFormText": "Βοηθητικ<PERSON> κείμενο εγγραφής", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Προσθέστε μια σύντομη περιγραφή στην κορυφή της φόρμας εγγραφής.", "app.containers.AdminPage.SettingsPage.step1": "Βήμα ηλεκτρονικού ταχυδρομείου και κωδικού πρόσβασης", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Αυτό εμφανίζε<PERSON>αι στην κορυφή της πρώτης σελίδας της φόρμας εγγραφής (όνομα, email, κωδικός πρόσβασης).", "app.containers.AdminPage.SettingsPage.step2": "Βήμα ερωτήσεων εγγραφής", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Αυτό εμφανίζεται στην κορυφή της δεύτερης σελίδας της φόρμας εγγραφής (πρόσθετα πεδία εγγραφής).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Καθορίστε τις γεωγραφικές περιοχές που θα θέλατε να χρησιμοποιήσετε για την πλατφόρμα σας, ό<PERSON>ω<PERSON> γειτονιές, δήμ<PERSON>υς ή περιφέρειες. Μπορείτε να συσχετίσετε αυτές τις γεωγραφικές περιοχές με έργα (με δυνατότητα φιλτραρίσματος στη σελίδα προορισμού) ή να ζητήσετε από τους συμμετέχοντες να επιλέξουν την περιοχή διαμονής τους ως πεδίο εγγραφής για τη δημιουργία έξυπνων ομάδων και τον καθορισμό δικαιωμάτων πρόσβασης.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Επιλέξτε πώς οι άνθρωποι θα βλέπουν το όνομα του οργανισμού σας, επιλέξτε τις γλώσσες της πλατφόρμας σας και συνδέστε τον ιστότοπό σας.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Ο παρεχόμενος υπό<PERSON>ιτλος υπερβαίνει το μέγιστο επιτρεπόμενο όριο χαρακτήρων (90 χαρακτήρες)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Καθορίστε ποιες πληροφορίες θα κληθούν να παρέχουν οι χρήστες κατά την εγγραφή τους.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Ορολογία", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Settings updated successfully.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Περιοχ<PERSON>ς", "app.containers.AdminPage.SettingsPage.tabBranding": "Branding", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Κατα<PERSON>τ<PERSON><PERSON><PERSON><PERSON>ς εισόδου", "app.containers.AdminPage.SettingsPage.tabPolicies": "Πολιτικές", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registration", "app.containers.AdminPage.SettingsPage.tabSettings": "General", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Καθορίστε ποια γεωγραφική μονάδα θα θέλατε να χρησιμοποιήσετε για τα έργα σας (π.χ. γειτονιές, συνοικίες, δήμοι κ.λπ.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geographic areas", "app.containers.AdminPage.SettingsPage.titleBasic": "Γενικές ρυθμίσεις", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Ο παρεχόμενος τίτλος υπερβαίνει το μέγιστο επιτρεπόμενο όριο χαρακτήρων (35 χαρακτήρες)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tag manager", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Αυτό το διαφημιστικό πλαίσιο είναι ιδιαίτερα χρήσιμο με εικόνες που δεν συνδυάζεται καλά με κείμενο από τον τίτλο, τον υπότιτλο ή ένα κουμπί. Μπορείτε να βρείτε περισσότερες πληροφορίες για την προτεινόμενη χρήση εικόνων εδώ {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "knowledge base", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Δύο γραμμές", "app.containers.AdminPage.SettingsPage.urlError": "Η διεύθυνση URL δεν είναι έγκυρη", "app.containers.AdminPage.SettingsPage.urlPatternError": "Enter a valid URL.", "app.containers.AdminPage.SettingsPage.urlTitle": "Ιστότοπος", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Μπορείτε να προσθέσετε έναν σύνδεσμο προς τον δικό σας ιστότοπο. Αυτός ο σύνδεσμος θα χρησιμοποιείται στο κάτω μέρος της αρχικής σελίδας.", "app.containers.AdminPage.SideBar.administrator": "Διαχειριστής", "app.containers.AdminPage.SideBar.communityPlatform": "Κοινοτική πλατφόρμα", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "Emails", "app.containers.AdminPage.SideBar.folderManager": "Διαχειριστής φακέλων", "app.containers.AdminPage.SideBar.groups": "Groups", "app.containers.AdminPage.SideBar.guide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "Input manager", "app.containers.AdminPage.SideBar.insights": "Αναφορά", "app.containers.AdminPage.SideBar.knowledgeBase": "Βάση γνώσεων", "app.containers.AdminPage.SideBar.language": "Γλώσσα", "app.containers.AdminPage.SideBar.menu": "Pages & menu", "app.containers.AdminPage.SideBar.messaging": "Messaging", "app.containers.AdminPage.SideBar.moderation": "Activity", "app.containers.AdminPage.SideBar.notifications": "Ειδοποιήσεις", "app.containers.AdminPage.SideBar.processing": "Επεξεργασία", "app.containers.AdminPage.SideBar.projectManager": "Διαχειριστής έργου", "app.containers.AdminPage.SideBar.projects": "Projects", "app.containers.AdminPage.SideBar.settings": "Settings", "app.containers.AdminPage.SideBar.signOut": "Υπογράψτε έξω", "app.containers.AdminPage.SideBar.support": "Υποστήριξη", "app.containers.AdminPage.SideBar.toPlatform": "Προς την πλατφόρμα", "app.containers.AdminPage.SideBar.tools": "Εργαλεία", "app.containers.AdminPage.SideBar.user.myProfile": "Το προφίλ μου", "app.containers.AdminPage.SideBar.users": "Users", "app.containers.AdminPage.SideBar.workshops": "Σεμινάρια", "app.containers.AdminPage.Topics.addTopics": "Add", "app.containers.AdminPage.Topics.browseTopics": "Αναζήτηση ετικετών", "app.containers.AdminPage.Topics.cancel": "Cancel", "app.containers.AdminPage.Topics.confirmHeader": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την ετικέτα έργου;", "app.containers.AdminPage.Topics.delete": "Delete", "app.containers.AdminPage.Topics.deleteTopicLabel": "Delete", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Αυτή η ετικέτα δεν θα μπορεί πλέον να προστεθεί σε νέες δημοσιεύσεις σε αυτό το έργο.", "app.containers.AdminPage.Topics.inputForm": "Input form", "app.containers.AdminPage.Topics.lastTopicWarning": "Απαιτείται τουλάχιστον μία ετικέτα. Αν δεν θέλετε να χρησιμοποιείτε ετικέτες, μπορείτε να τις απενεργοποιήσετε στην καρτέλα {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Μπορείτε να προσθέσετε και να διαγράψετε τις ετικέτες που μπορούν να αντιστοιχιστούν σε δημοσιεύσεις σε αυτό το έργο.", "app.containers.AdminPage.Topics.remove": "Remove", "app.containers.AdminPage.Topics.title": "Project tags", "app.containers.AdminPage.Topics.topicManager": "Tag manager", "app.containers.AdminPage.Topics.topicManagerInfo": "Αν θέλετε να προσθέσετε επιπλέον ετικέτες έργου, μπορείτε να το κάνετε στην καρτέλα {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Προσθήκη νέας ομάδας", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Όνομα ομάδας", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Δώστε ένα όνομα ομάδας", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Δημιουργ<PERSON>α μη αυτόματης ομάδας", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Τι είδους ομάδα χρειάζεστε;", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Αποθήκευση ομάδας", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Μάθετε περισσότερα για τις ομάδες", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Μπορείτε να επιλέξετε χρήστες από την επισκόπηση και να τους προσθέσετε σε αυτή την ομάδα.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Μπορείτε να ορίσετε προϋποθέσεις και οι χρήστες που πληρούν τις προϋποθέσεις προστίθενται αυτόματα σε αυτή την ομάδα.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Μη αυτόματη ομάδα", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Έξυπνη ομάδα", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Δεν υπάρχει ακόμη κανείς σε αυτή την ομάδα", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Μεταβείτε στο {allUsersLink} για να προσθέσετε με μη αυτόματο τρόπο κάποιους χρήστες.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Κανέν<PERSON><PERSON> χρήστης(ες) δεν ταιριάζει(-ουν) στην αναζήτησή σας", "app.containers.AdminPage.Users.GroupsPanel.select": "Επιλέξτε", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την ομάδα;", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά την προσθήκη χρηστών στις ομάδες, δοκι<PERSON>άστε ξανά.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Κατάργηση από ομάδα", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Διαγραφή επιλεγμένων χρηστών από αυτή την ομάδα;", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Προέκυψε σφάλμα κατά τη διαγραφή χρηστών από την ομάδα, δοκιμάστε ξανά.", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Add", "app.containers.AdminPage.groups.permissions.add": "Add", "app.containers.AdminPage.groups.permissions.addQuestion": "Προσθέστε δημογραφικές ερωτήσεις", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Δημιουργ<PERSON><PERSON> νέας ερώτησης", "app.containers.AdminPage.groups.permissions.createAQuestion": "Δημιουργήστε μια ερώτηση", "app.containers.AdminPage.groups.permissions.defaultField": "Προεπιλεγμένο πεδίο", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Delete", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Delete", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Ναι-όχι (πλαί<PERSON>ιο ελέγχου)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Ημερομηνία", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Μεγάλη απάντηση", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Πολλαπλή επιλογή (επιλέξτε πολλαπλές)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Αριθμητική τιμή", "app.containers.AdminPage.groups.permissions.fieldType_select": "Πολλαπλή επιλογή (επιλέξτε ένα)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Σύντομη απάντηση", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Είστε σίγουροι ότι θέλετε να καταργήσετε αυτή την ομάδα από το έργο;", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Επιλέξτε μία ή περισσότερες ομάδες", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Κανένα μέλος} one {1 μέλος} other {{count} μέλη}}", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Δεν βρέθηκαν οι διαχειριστές του έργου", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Δεν εμφαν<PERSON><PERSON><PERSON>τ<PERSON>ι τίποτα, επειδ<PERSON> δεν υπάρχουν ενέργειες που μπορεί να κάνει ο χρήστης σε αυτό το έργο.", "app.containers.AdminPage.groups.permissions.option1": "Επιλογή 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Πρόσκληση σε εκκρεμότητα", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Ποιος μπορεί να σχολιάσει το έγγραφο;", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Ποιος μπορεί να σχολιάσει τις εισροές;", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Ποιος μπορεί να σχολιάσει τις προτάσεις;", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Ποιος μπορεί να δημοσιεύσει μια πρόταση;", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Ποιος μπορεί να αντιδράσει στις εισροές;", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Ποιος μπορεί να υποβάλει εισροές;", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Ποιος μπορε<PERSON> να λάβει μέρος στη δημοσκόπηση;", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Ποιος μπορεί να συμμετάσχει στην έρευνα;", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Ποιος μπορεί να ψηφίσει επί των προτάσεων;", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Ποιος μπορεί να ψηφίσει;", "app.containers.AdminPage.groups.permissions.save": "Save", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Success!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.groups.permissions.select": "Επιλέξτε", "app.containers.AdminPage.projects.all.existingProjects": "Υφιστάμενα έργα", "app.containers.AdminPage.projects.all.projectsAndFolders": "Έργα και φάκελοι", "app.containers.AdminPage.widgets.copied": "Αντιγρα<PERSON><PERSON> στο πρόχειρο", "app.containers.AdminPage.widgets.copyToClipboard": "Αντιγράψτε αυτόν τον κώδικα", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Αντιγράψτε τον κώδικα HTML", "app.containers.AdminPage.widgets.fieldAccentColor": "Χρώμα έμφασης", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Χρώ<PERSON><PERSON> φόντου widget", "app.containers.AdminPage.widgets.fieldButtonText": "Button text", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Εγγραφείτε τώρα", "app.containers.AdminPage.widgets.fieldFont": "Γραμματοσειρά", "app.containers.AdminPage.widgets.fieldFontDescription": "Αυτό πρέπει να είναι ένα υπάρχον όνομα γραμματοσειράς από το {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Μέγεθος γραμματοσειράς (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Υπότιτλος κεφαλίδας", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Μπορείτε να έχετε λόγο", "app.containers.AdminPage.widgets.fieldHeaderText": "<PERSON><PERSON><PERSON><PERSON><PERSON> κεφαλίδας", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Η πλατφόρμα συμμετοχής μας", "app.containers.AdminPage.widgets.fieldHeight": "Ύψος (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Αριθμός δημοσιεύσεων", "app.containers.AdminPage.widgets.fieldProjects": "Projects", "app.containers.AdminPage.widgets.fieldRelativeLink": "Σύνδεσμοι σε", "app.containers.AdminPage.widgets.fieldShowFooter": "Εμφάνιση κουμπιού", "app.containers.AdminPage.widgets.fieldShowHeader": "Εμφάνιση κεφαλίδας", "app.containers.AdminPage.widgets.fieldShowLogo": "Εμφάνιση λογότυπου", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Χρώ<PERSON>α φόντου της ιστοσελίδας", "app.containers.AdminPage.widgets.fieldSort": "Ταξινόμηση ανά", "app.containers.AdminPage.widgets.fieldTextColor": "Text color", "app.containers.AdminPage.widgets.fieldTopics": "Tags", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Αρχική σελίδα", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Μπορείτε να αντιγράψετε αυτόν τον κώδικα HTML και να τον επικολλήσετε στο τμήμα του ιστότοπού σας όπου θέλετε να προσθέσετε το widget σας.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Κώδικας HTML του widget", "app.containers.AdminPage.widgets.previewTitle": "Preview", "app.containers.AdminPage.widgets.settingsTitle": "Settings", "app.containers.AdminPage.widgets.sortNewest": "Νεότερο", "app.containers.AdminPage.widgets.sortPopular": "Popular", "app.containers.AdminPage.widgets.sortTrending": "Trending", "app.containers.AdminPage.widgets.subtitleWidgets": "Μπορείτε να δημιουργήσετε ένα widget, να το προσαρμόσετε και να το προσθέσετε στον δικό σας ιστότοπο για να προσελκύσετε κόσμο σε αυτή την πλατφόρμα.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Διαστάσεις", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Κεφαλίδα και υποσέλιδο", "app.containers.AdminPage.widgets.titleInputSelection": "Επιλογή εισήγησης", "app.containers.AdminPage.widgets.titleStyle": "Στυλ", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Save", "app.containers.admin.ContentBuilder.delete": "Delete", "app.containers.admin.ContentBuilder.error": "error", "app.containers.admin.ContentBuilder.errorMessage": "Υπάρχει σφάλμα στο περιεχόμενο {locale}, παρακαλούμε διορθώστε το πρόβλημα για να μπορέσετε να αποθηκεύσετε τις αλλαγές σας", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 στήλες", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 στήλες", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 στήλες με πλάτος 30% και 60% αντίστοιχα", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 στήλες με πλάτος 60% και 30% αντίστοιχα", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 ίσες στήλες", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Show more", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Title", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Participants timeline", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Chart", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Date range", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Title", "app.containers.admin.ReportBuilder.charts.noData": "There is no data available for the filters you have selected.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Traffic sources", "app.containers.admin.ReportBuilder.charts.usersByAge": "Users by age", "app.containers.admin.ReportBuilder.charts.usersByGender": "Users by gender", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Visitor timeline", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Αγοράστε 1 επιπλέον θέση", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Επιβεβαίωση", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Είστε σίγουροι ότι θέλετε να δώσετε σε 1 άτομο δικαιώματα διαχειριστή;", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Δώστε δικαιώματα διαχειριστή", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Έχετε φτάσει στο όριο των θέσεων που περιλαμβάνονται στο πρόγραμμά σας, θα προστεθεί {noOfSeats} επιπλέον {noOfSeats, plural, one {θέση} other {θέσεις}}", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Προσθήκη κατάστασης", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Delete", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Edit", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Επεξεργα<PERSON><PERSON>α κατάστασης", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Οι καταστάσεις που έχουν εκχωρηθεί επί του παρόντος στην εισήγηση συμμετεχόντων δεν μπορούν να διαγραφούν. Μπορείτε να καταργήσετε/αλλάξετε την κατάσταση από την υπάρχουσα εισήγηση στην καρτέλα {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Αυτή η κατάσταση δεν μπορεί να διαγραφεί ή να μετακινηθεί.", "app.containers.admin.ideaStatuses.all.manage": "Edit", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Διαχειριστείτε την κατάσταση που μπορεί να εκχωρηθεί σε εισροές συμμετεχόντων σε ένα έργο. Η κατάσταση είναι δημόσια ορατή και βοηθά στην ενημέρωση των συμμετεχόντων.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Επεξερ<PERSON><PERSON><PERSON><PERSON><PERSON> καταστ<PERSON><PERSON><PERSON>ων εισόδου", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Επιλεγμένο για υλοποίηση ή επόμενα βήματα", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Εγκεκριμένο", "app.containers.admin.ideaStatuses.form.category": "Κατηγορία", "app.containers.admin.ideaStatuses.form.categoryDescription": "Παρακαλούμε επιλέξτε την κατηγορία που αντιπροσωπεύει καλύτερα την κατάστασή σας. Αυτή η επιλογή θα βοηθήσει το εργαλείο ανάλυσης να επεξεργάζεται και να αναλύει με μεγαλύτερη ακρίβεια τις δημοσιεύσεις.", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Other", "app.containers.admin.ideaStatuses.form.fieldColor": "Χρώμα", "app.containers.admin.ideaStatuses.form.fieldDescription": "Περιγρα<PERSON><PERSON> κατάστασης", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Δώστε μια περιγραφή κατάστασης για όλες τις γλώσσες", "app.containers.admin.ideaStatuses.form.fieldTitle": "Όνομα κατάστασης", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Παρέχετε ένα όνομα κατάστασης για όλες τις γλώσσες", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Εφαρμόστηκε επιτυχώς", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Υλοποιήθηκε", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Μη επιλέξιμο ή δεν επιλέχθηκε για να προωθηθεί", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Μη επιλεγμένο", "app.containers.admin.ideaStatuses.form.saveStatus": "Αποθήκευση κατάστασης", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Εξετάζ<PERSON>ται για υλοποίηση ή επόμενα βήματα", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Υπό εξέταση", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Εξετά<PERSON><PERSON><PERSON><PERSON><PERSON> αλλά δεν έχει υποβληθεί ακόμη σε επεξεργασία", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Προβλήθηκε", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Διαχειριστείτε εισηγήσεις και τις καταστάσεις τους", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Διαχείριση εισηγήσεων | Πλατφόρμα συμμετοχής του {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Δώστε ανατροφοδότηση, προσθέστε ετικέτες και μετακινήστε εισηγήσεις από ένα έργο σε άλλο", "app.containers.admin.ideas.all.inputManagerPageTitle": "Διαχειριστής εισόδου", "app.containers.admin.ideas.all.tabOverview": "Επισκόπηση", "app.containers.admin.import.importInputs": "Εισαγωγή εισηγήσεων", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 επιπλέον θέση διαχειριστή} other {# additional admin seats}} and {managerSeats, plural, one {1 πρόσθετη θέση διαχειριστή} other {# additional manager seats}} θα προστεθούν πάνω από το όριο.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 επιπλέον θέση διαχειριστή θα προστεθεί πάνω από το όριο} other {# επιπλέον θέσεις διαχειριστή θα προστεθούν πάνω από το όριο}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {Θα προστεθεί 1 επιπλέον θέση διευθυντή πάνω από το όριο} other {# επιπλέον θέσεις διευθυντών θα προστεθούν πέραν του ορίου}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Επιβεβαίωση και αποστολή προσκλήσεων", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Confirm impact on seat usage", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Έχετε φτάσει στο όριο των διαθέσιμων θέσεων στο πρόγραμμά σας.", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Μόνο διαχειριστές και συνεργάτες", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Μόνο οι διαχειριστές της πλατφόρμας, οι διαχειριστές φακέλων και οι διαχειριστές έργων μπορούν να προβούν στην ενέργεια", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Οποιοσδήποτε", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Οποιο<PERSON><PERSON>ήποτε, συμπεριλαμβανομένων των μη εγγεγραμμένων χρηστών, μπορεί να συμμετάσχει.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Selection", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Μπορούν να συμμετέχουν χρήστες σε συγκεκριμένες ομάδες χρηστών. Μπορείτε να διαχειριστείτε τις ομάδες χρηστών στην καρτέλα \"Χρήστες\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Ποιος μπορεί να δει αυτό το έργο;", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Ποσοστό συμμετοχής", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Αυτοματοποιημένες εκστρατείες", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automated emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Από {ποσότητα} εκστρατείες", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Campaigns", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Προσαρμοσμένες εκστρατείες", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Προσαρμοσμένα emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Emails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Σύνολο emails που στάλθηκαν", "app.modules.commercial.analytics.admin.components.Events.completed": "Ολοκληρώθηκε", "app.modules.commercial.analytics.admin.components.Events.events": "Events", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Σύνολ<PERSON> εκδηλώσεων που προστέθηκαν", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Επερχόμενη", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Accepted", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Invitations", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Pending", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Συνολικές προσκλήσεις που εστάλησαν", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Μετάβαση στη Διαχείριση εισηγήσεων", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Εισηγήσεις", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Active", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Έργα που δεν έχουν αρχειοθετηθεί και είναι ορατά στον πίνακα \"Ενεργό\" στην αρχική σελίδα", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archived", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Draft projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Finished", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Όλα τα αρχειοθετημένα έργα και τα ενεργά έργα χρονοδιαγράμματος που έχουν ολοκληρωθεί υπολογίζονται εδώ", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projects", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Σύνολο <PERSON>γων", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Ο αριθμός των έργων που είναι ορατά στην πλατφόρμα", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Νέες εγγραφές", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Ποσοστό εγγραφής", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registrations", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Σύνολο εγγραφών", "app.modules.commercial.analytics.admin.components.Tab": "Επισκέπτες", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Τελευταίες 30 ημέρες:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Τελευταίες 7 ημέρες:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Προβολές σελίδων ανά επίσκεψη", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Διάρκεια επίσκεψης", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Επισκέπτες\" είναι ο αριθμός των μοναδικών επισκεπτών. Εάν ένα άτομο επισκέπτεται την πλατφόρμα πολλές φορές, υπολογίζεται μόνο μία φορά.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Επισκέψεις", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Επισκέψεις\" είναι ο αριθμός των συνεδριών. Ε<PERSON><PERSON> ένα άτομο επισκέφθηκε την πλατφόρμα πολλές φορές, κάθε επίσκεψη μετράται.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Χθες:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Language", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Αριθμός επισκεπτών", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Ποσοστ<PERSON> επισκεπτών", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Σημ<PERSON><PERSON><PERSON> αναφο<PERSON><PERSON>ς", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "κάντε κλικ εδώ", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Σημεία αναφοράς", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Για να δείτε την πλήρη λίστα σημείων αναφοράς, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Visitors", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "<PERSON><PERSON><PERSON><PERSON><PERSON> κυκλοφορίας", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Visits", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Παραδ<PERSON><PERSON><PERSON>ις email διαχρονικά", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Εγγρα<PERSON><PERSON><PERSON> διαχρονικά", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Date", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Στατιστικ<PERSON> στοιχεία", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Συνολικ<PERSON> στατιστικά στοιχεία", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Επισκέψεις και επισκέπτες με την πάροδο του χρόνου", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Σύν<PERSON><PERSON><PERSON> κατά τη διάρκεια της περιόδου", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Καταμέτρηση", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Language", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Εκστρατείες", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Άμεση καταχώρηση", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Ποσοστ<PERSON> επισκέψεων", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "<PERSON>η<PERSON><PERSON><PERSON><PERSON><PERSON> αναζήτησης", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Κοινωνικ<PERSON> δίκτυα", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Πηγή επισκεψιμότητας", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Αριθμός επισκέψεων", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Ιστότοποι", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Μπορείτε να καταργήσετε αυτή τη σημαία περιεχομένου επιλέγοντας αυτό το στοιχείο και κάνοντας κλικ στο κουμπί κατάργησης στο επάνω μέρος. Στη συνέχεια θα εμφανιστεί ξανά στις καρτέλες \"Αναγνωσμένο\" ή \"Μη αναγνωσμένο\"", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Αυτό<PERSON><PERSON><PERSON><PERSON> εντοπισμ<PERSON>ς ακατάλληλου περιεχομένου.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Δεν υπάρχουν δημοσιεύσεις που αναφέρθηκαν για έλεγχο από την κοινότητα ή επισημάνθηκαν για ακατάλληλο περιεχόμενο από το σύστημα επεξεργασίας φυσικής γλώσσας μας", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Κατάργηση {numberOfItems, plural, one {προειδοποίηση περιεχομένου} other {# προειδοποιήσεις περιεχομένου}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Αναφέρθηκε ως ακατάλληλη απ<PERSON> έναν χρήστη της πλατφόρμας.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Προειδοποιήσεις περιεχομένου", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Report builder", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Σελίδες που εμφανίζονται στη γραμμή πλοήγησης", "app.modules.navbar.admin.containers.createCustomPageButton": "Create custom page", "app.modules.navbar.admin.containers.deletePageConfirmation": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή τη σελίδα; Αυτό δεν μπορεί να αναιρεθεί. Μπορείτε επίσης να καταργήσετε τη σελίδα από τη γραμμή πλοήγησης αν δεν είστε ακόμα έτοιμοι να τη διαγράψετε.", "app.modules.navbar.admin.containers.pageHeader": "Pages & menu", "app.modules.navbar.admin.containers.pageSubtitle": "Η γραμμή πλοήγησης μπορεί να εμφανίζει έως και πέντε σελίδες εκτός από τις σελίδες Αρχική σελίδα και Έργα. Μπορείτε να μετονομάσετε τα στοιχεία μενού, να αναδιατάξετε και να προσθέσετε νέες σελίδες με το δικό σας περιεχόμενο.", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "visit our support center", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "For more information on recommended image resolutions, {supportPageLink}."}