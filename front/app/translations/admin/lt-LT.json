{"UI.FormComponents.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.action": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.after": "Po", "app.Admin.ManagementFeed.before": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.changed": "Modifikuota", "app.Admin.ManagementFeed.created": "Sukurta", "app.Admin.ManagementFeed.date": "Data", "app.Admin.ManagementFeed.deleted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.folder": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.idea": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.in": "projekte {project}", "app.Admin.ManagementFeed.item": "Prekė", "app.Admin.ManagementFeed.key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.managementFeedNudge": "Prieiga prie valdymo kanalo nėra įtraukta į dabartinę licenciją. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.Admin.ManagementFeed.noActivityFound": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.phase": "Fazė", "app.Admin.ManagementFeed.project": "Projektas", "app.Admin.ManagementFeed.projectReviewApproved": "Patvirtintas projektas", "app.Admin.ManagementFeed.projectReviewRequested": "Prašoma peržiūrėti projektą", "app.Admin.ManagementFeed.title": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.user": "Vartotojas", "app.Admin.ManagementFeed.userPlaceholder": "Pasirinkite naudotoją", "app.Admin.ManagementFeed.value": "Vertė", "app.Admin.ManagementFeed.viewDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ti informaciją", "app.Admin.ManagementFeed.warning": "Eksperimentinė funkcija: Minimalus administratorių ar vadovų per paskutines 30 dienų atliktų pasirinktų veiksmų sąrašas. Įtraukti ne visi veiksmai.", "app.Admin.Moderation.managementFeed": "<PERSON><PERSON><PERSON>", "app.Admin.Moderation.participationFeed": "Dalyvavi<PERSON> kanalas", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "<PERSON>r t<PERSON>rai?", "app.components.Admin.Campaigns.clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deleteCampaignButton": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "app.components.Admin.Campaigns.deliveryStatus_accepted": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Atšokęs", "app.components.Admin.Campaigns.deliveryStatus_clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip": "Kai į el. lai<PERSON> pridėjote vieną ar daugiau nuorodų, čia bus rod<PERSON>, k<PERSON><PERSON> spustel<PERSON> nuorodą, <PERSON><PERSON><PERSON><PERSON>.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_failed": "Nepavyko", "app.components.Admin.Campaigns.deliveryStatus_opened": "Atidaryta", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Projektas", "app.components.Admin.Campaigns.manageButtonLabel": "Tvarkykite", "app.components.Admin.Campaigns.opened": "Atidaryta", "app.components.Admin.Campaigns.project": "Projektas", "app.components.Admin.Campaigns.recipientsTitle": "Gavėjai", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistika", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON>", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.info": "Šis vaizdas visada ap<PERSON>pomas tam tikru <PERSON>, kad visada būtų matomi visi svarbiausi aspektai. Šio tipo vaizdas {link} yra {aspect}.", "app.components.Admin.ImageCropper.infoLinkText": "reko<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.ImageCropper.mobileCropExplanation": "Pastaba: bet kuri svarbi vaizdo sritis turi būti vertikaliose brūkšninėse linijose, nes mobiliuosiuose įrenginiuose vaizdas bus apkarpytas iki 3:1 santykio.", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Registruotų naudotojų antraštės tekstas", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Įspėjimas: pasirinkta spalva nėra pakankamai kontrastinga. Dėl to tekstas gali būti <PERSON>i įskaitomas. Pasirinkite tamsesnę spalvą, kad optimizuotumėte įskaitomumą.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Įtraukti įvykius į nar<PERSON><PERSON><PERSON> juo<PERSON>", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Įjungus <PERSON><PERSON><PERSON>, na<PERSON><PERSON><PERSON>o juostoje bus pridėta nuoroda į visus projekto įvykius.", "app.components.AdminPage.SettingsPage.eventsSection": "Rengin<PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Pagrindin<PERSON> puslapis pritaiko<PERSON> skyrius", "app.components.AnonymousPostingToggle.userAnonymity": "Vartotojo anonimiš<PERSON>", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Naudotojai galės paslėpti savo tapatybę nuo kitų naudotojų, projektų vadovų ir administratorių. Šie įnašai vis tiek gali būti moderuoja<PERSON>.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "leisti naudotojams dalyvauti anonimiškai", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Vartotojai ir toliau gali dalyvauti naudodami savo tikrąjį vardą ir pavardę, ta<PERSON><PERSON><PERSON>, jei nor<PERSON>, galės pateikti anoniminę informaciją. Visi naudotojai vis tiek turės atitikti skirtuke \"Prieigos teis<PERSON>\" nustatytus reikalavimus, kad jų įnašai būtų priimami. Naudotojo profilio duomenų nebus galima rasti eksportuojant dalyvavimo duomenis.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Daugiau informacijos apie naudotojo anonimiškumą rasite mūsų svetainėje {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "para<PERSON> straip<PERSON>", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Pridėjus papildomų vietų, jūsų sąskaita bus padidinta. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad užpild<PERSON>te apklausą! Kitą ketvirtį kviečiame ją atlikti dar kartą.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Atsisiųsti kaip pdf", "app.components.FormSync.downloadExcelTemplate": "Atsisiųsti \"Excel\" š<PERSON><PERSON>", "app.components.FormSync.downloadExcelTemplateTooltip2": "Į \"Excel\" š<PERSON><PERSON><PERSON> nebus įtraukti re<PERSON><PERSON><PERSON>, mat<PERSON><PERSON> klaus<PERSON>, failų įkėlimo klausimai ir bet kokie žemėlapių įvesties klausimai (\"Drop Pin\", \"Draw Route\", \"Draw Area\", ESRI failų įkėlimas), nes šiuo metu jie nepalaikomi masiniam importui.", "app.components.ProjectTemplatePreview.close": "Uždaryti", "app.components.ProjectTemplatePreview.createProject": "Sukurti projektą", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Sukurkite projektą pagal šabloną ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Grįžti atgal", "app.components.ProjectTemplatePreview.goBackTo": "Grįžkite į {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Eiti Vokalo ekspertas", "app.components.ProjectTemplatePreview.infoboxLine1": "Norite naudoti šį šabloną savo dalyvavimo projekte?", "app.components.ProjectTemplatePreview.infoboxLine2": "<PERSON><PERSON><PERSON><PERSON>tės į atsakingą asmenį savo miesto administracijoje arba kreipkitės į {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Projekto aplankas", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Pasirinkta data negalioja. Nurodykite datą tokiu formatu: YYYYY-MM-DD .", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Pasirinkite projekto pradžios datą", "app.components.ProjectTemplatePreview.projectStartDate": "Projekto pradžios data", "app.components.ProjectTemplatePreview.projectTitle": "Jūsų projekto pavadinimas", "app.components.ProjectTemplatePreview.projectTitleError": "Įveskite projekto pavadinimą", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Įveskite projekto pavadinimą visomis kalbomis", "app.components.ProjectTemplatePreview.projectsOverviewPage": "projektų apžvalgos puslapis", "app.components.ProjectTemplatePreview.responseError": "<PERSON><PERSON>, ka<PERSON><PERSON> nutiko ne taip.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Žiūrėti daugiau šablonų", "app.components.ProjectTemplatePreview.successMessage": "Projektas sėkmingai sukurtas!", "app.components.ProjectTemplatePreview.typeProjectName": "Įveskite projekto pavadinimą", "app.components.ProjectTemplatePreview.useTemplate": "Naudokite šį šabloną", "app.components.SeatInfo.additionalSeats": "Papildomos vietos", "app.components.SeatInfo.additionalSeatsToolTip": "<PERSON><PERSON> rod<PERSON> p<PERSON> vietų, k<PERSON><PERSON> įsigijote prie \"Įtrauktų vietų\", <PERSON><PERSON><PERSON><PERSON>.", "app.components.SeatInfo.adminSeats": "<PERSON><PERSON><PERSON> viet<PERSON>", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} įtrauktos administrator<PERSON>us vietos", "app.components.SeatInfo.adminSeatsTooltip1": "Administratoriai yra atsakingi už platformą ir turi visų aplankų ir projektų valdytojo teises. Norėdami sužinoti daugiau apie skirtingus vaidmenis, galite {visitHelpCenter} .", "app.components.SeatInfo.currentAdminSeatsTitle": "Da<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.components.SeatInfo.currentManagerSeatsTitle": "Dabartinės vadovo vietos", "app.components.SeatInfo.includedAdminToolTip": "<PERSON><PERSON><PERSON> į metinę sutartį įtrauktų laisvų administratorių vietų skaičius.", "app.components.SeatInfo.includedManagerToolTip": "Čia nurodomas į metinę sutartį įtrauktų vadovų laisvų vietų skaič<PERSON>.", "app.components.SeatInfo.includedSeats": "Įtrauktos s<PERSON><PERSON><PERSON><PERSON>s", "app.components.SeatInfo.managerSeats": "Vadovo vietos", "app.components.SeatInfo.managerSeatsTooltip": "Aplankų / projektų tvarkytojai gali tvarkyti neribotą aplankų / projektų skaičių. Norėdami sužinoti daugiau apie skirtingus vaidmenis, galite {visitHelpCenter} .", "app.components.SeatInfo.managersIncludedText": "{managerSeats} įtrauktos v<PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.remainingSeats": "Likusios vietos", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "<PERSON><PERSON> viso viet<PERSON>", "app.components.SeatInfo.totalSeatsTooltip": "<PERSON><PERSON> rod<PERSON> jūsų plano vietų skaičius ir papildomai įsigytų vietų skaičius.", "app.components.SeatInfo.usedSeats": "<PERSON><PERSON><PERSON>", "app.components.SeatInfo.view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.visitHelpCenter": "apsilankykite mūsų pagalbos centre", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Jūsų planas yra {adminSeatsIncluded}. Kai išnaudosite visas vietas, papildomų vietų bus pridėta skiltyje \"Papildomos vietos\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Jūsų plane yra {manager<PERSON><PERSON><PERSON><PERSON>nc<PERSON>}, tinkamas aplankų tvarkytojams ir projektų vadovams. Kai išnaudosite visas vietas, papildomų vietų bus pridėta skiltyje \"Papildomos vietos\".", "app.components.UserSearch.addModerators": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UserSearch.searchUsers": "Įveskite naudotojų paiešką...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternatyvus klaidos praneš<PERSON>s", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "<PERSON><PERSON> numaty<PERSON><PERSON><PERSON> nustatymus naudotojams bus rodomas toks klaidos p<PERSON>:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Pritaikyti klaidos p<PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Šį pranešimą galite perrašyti kiekvienai kalbai naudodami toliau esantį teksto lauką \"Alternatyvus klaidos pranešimas\". Jei teksto laukelį paliksite tuščią, bus rodomas numatytasis pranešimas.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "<PERSON><PERSON><PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "<PERSON> dalyviai p<PERSON>, kai neatitiks dalyvavimo re<PERSON>.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Išsaugoti klaidos p<PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Nepasirinktas nė vienas klausimas. Pirmiausia pasirinkite klausimą.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "<PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} atsaky<PERSON>i", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Ap<PERSON><PERSON><PERSON> k<PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} iki <PERSON>", "app.components.admin.DatePhasePicker.Input.openEnded": "Atviro tipo", "app.components.admin.DatePhasePicker.Input.selectDate": "Pasirinkite datą", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Aiški pabaigos data", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Aiški pradžios data", "app.components.admin.Graphs": "Nėra duomenų apie dabartinius filtrus.", "app.components.admin.Graphs.noDataShort": "Duomen<PERSON> nė<PERSON>.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "<PERSON><PERSON><PERSON> la<PERSON> b<PERSON>", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Pranešimai laik<PERSON> b<PERSON>", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.components.admin.InputManager.onePost": "1 įvestis", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Neprisijungus parinkčių reguliavimas", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Neprisijungus balsų koregavimas", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Š<PERSON> parinktis leidžia įtraukti duomenis apie dalyvavimą iš kitų šaltinių, pavyzdžiui, asmeninių ar popierinių balsavimų:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Jis vizualiai skirsis nuo skaitmeninių balsų.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Tai turės įtakos galutiniams balsavimo rezultatams.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Tai <PERSON>pindės dalyvavimo duomenų suvestinėse.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Neprisijungus<PERSON>ji balsai už parinktį projekte gali būti nustatyti tik vieną kartą ir yra bendri visiems projekto etapams.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "Pirmiausia turite įvesti bendrą neprisijungusių dalyvių skaičių.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "<PERSON><PERSON> viso daly<PERSON> be interneto", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "<PERSON><PERSON> apskaič<PERSON>oti teisingus rezultatus, turi<PERSON>, <b>kiek iš viso šiame etape dalyvavo neprisijungusių dalyvių</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Nurodykite tik tuos, kurie dalyvavo neprisijungę.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modifikuota pagal {name}", "app.components.admin.PostManager.PostPreview.assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.cancelEdit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.currentStatus": "<PERSON><PERSON><PERSON><PERSON> bū<PERSON>", "app.components.admin.PostManager.PostPreview.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Ar tikrai norite ištrinti šią įvestį? Šio veik<PERSON> atšauk<PERSON> negalima.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Ar tikrai norite ištrinti šią įvestį? Įvestis bus ištrinta iš visų projekto etapų ir jos nebus galima atkurti.", "app.components.admin.PostManager.PostPreview.edit": "Red<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "Nepriskirta", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Kiek kartų tai buvo įtraukta į kitų dalyvių dalyvau<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.picks": "Pasirinkimai: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Reak<PERSON><PERSON><PERSON> skai<PERSON>:", "app.components.admin.PostManager.PostPreview.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "<PERSON><PERSON><PERSON><PERSON> sluoksnį", "app.components.admin.PostManager.addFeatureLayerInstruction": "Nukopijuokite \"ArcGIS Online\" esančio elementų sluoksnio URL ir įklijuokite jį į toliau pateiktą įvestį:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Į žemėlapį pridėkite naują funk<PERSON> sluoksnį", "app.components.admin.PostManager.addWebMap": "Pridėti žiniatinklio žemėlapį", "app.components.admin.PostManager.addWebMapInstruction": "Nukopijuokite savo internetinio žemėlapio portalo ID iš \"ArcGIS Online\" ir įklijuokite jį į toliau pateiktą įvestį:", "app.components.admin.PostManager.allPhases": "Visi etapai", "app.components.admin.PostManager.allProjects": "Visi projektai", "app.components.admin.PostManager.allStatuses": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.allTopics": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.anyAssignment": "Bet kuris administratorius", "app.components.admin.PostManager.assignedTo": "Priskirta {assigneeName}", "app.components.admin.PostManager.assignedToMe": "<PERSON><PERSON><PERSON><PERSON> man", "app.components.admin.PostManager.assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.authenticationError": "Bandant gauti šį sluoksnį įvyko autentifikavimo klaida. Patikrinkite URL adresą ir tai, ar j<PERSON><PERSON><PERSON> \"Esri\" API raktas turi prieigą prie šio sluoksnio.", "app.components.admin.PostManager.automatedStatusTooltipText": "<PERSON><PERSON> būsena atnaujinama automatiškai, kai įvykdomos sąlygos", "app.components.admin.PostManager.bodyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel2": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.co-sponsors": "Bend<PERSON>aut<PERSON><PERSON>", "app.components.admin.PostManager.comments": "Komentarai", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON>, kad prarasite visus su šiais įvesties duomenimis susijusius duomenis, pvz., <PERSON><PERSON><PERSON><PERSON>, re<PERSON><PERSON><PERSON> ir balsus. <PERSON><PERSON> ve<PERSON> negalima <PERSON>.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Ar tikrai norite iš<PERSON>nti š<PERSON> įvesties duomenis?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Pašalinti temą", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Bandote pašalinti šią idėją iš etapo, kuriame ji gavo balsų. <PERSON>i tai padarysite, <PERSON>ie balsai bus prarasti. Ar tikrai norite pašalinti šią idėją iš šio etapo?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Su šia idėja susiję balsai bus prarasti", "app.components.admin.PostManager.components.goToInputManager": "Eikite į įvesties tvarkytuvę", "app.components.admin.PostManager.components.goToProposalManager": "Eiti į pasiūlymų tvarkyklę", "app.components.admin.PostManager.contributionFormTitle": "Redaguoti indėlį", "app.components.admin.PostManager.cost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.createInput": "Sukurti įvestį", "app.components.admin.PostManager.createInputsDescription": "Sukurti naują įvesties duomenų rinkinį iš ankstesnio projekto", "app.components.admin.PostManager.currentLat": "Centro platuma", "app.components.admin.PostManager.currentLng": "Centro ilguma", "app.components.admin.PostManager.currentZoomLevel": "Priartinimo lygis", "app.components.admin.PostManager.defaultEsriError": "Bandant gauti šį sluoksnį įvyko k<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON>, ar pris<PERSON><PERSON> prie tinklo ir ar URL adresas yra te<PERSON>.", "app.components.admin.PostManager.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteAllSelectedInputs": "<PERSON><PERSON><PERSON><PERSON> {count} p<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.deleteConfirmation": "Ar tikrai norite ištrinti šį sluoksnį?", "app.components.admin.PostManager.dislikes": "Nemėgsta", "app.components.admin.PostManager.edit": "Red<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "Redaguoti projektus", "app.components.admin.PostManager.editStatuses": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.components.admin.PostManager.editTags": "Redaguot<PERSON>", "app.components.admin.PostManager.editedPostSave": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Duomenų importavimas i<PERSON> \"Esri ArcGIS Online\" yra papildoma funkcija. Pasitarkite su GS vadybininku, kad j<PERSON> atrakintumėte.", "app.components.admin.PostManager.esriSideError": "\"ArcGIS\" programoje įvyko klaida. Palaukite kelias minutes ir pabandykite vėliau.", "app.components.admin.PostManager.esriWebMap": "<PERSON>sri <PERSON>", "app.components.admin.PostManager.exportAllInputs": "Eksportuoti visus pranešimus (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Eksportuoti visus komentarus (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Eksportuoti šio projekto komentarus (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Eksportuoti šio projekto pranešimus (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Eksportuoti pasirinktus įrašus (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Eksportuoti pasirinktų pranešimų komentarus (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Eks<PERSON>uo<PERSON> balsus pagal įvestį (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Eks<PERSON><PERSON><PERSON> balsus pagal naudo<PERSON> (.xslx)", "app.components.admin.PostManager.exports": "Eksportas", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Žemėlapio duomenis galite įkelti tik kaip GeoJSON sluoksnius arba importuoti iš \"ArcGIS Online\". Jei norite pridėti funkcinį sluoksnį, pašalinkite visus esamus GeoJSON sluoksnius.", "app.components.admin.PostManager.featureLayerTooltop": "Funkcinio sluoksnio URL rasite \"ArcGIS Online\" elemento puslapio dešinė<PERSON> pu<PERSON>.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON> mat<PERSON> j<PERSON> vardą", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Paaiškinkite šį statuso pasikeitimą", "app.components.admin.PostManager.fileUploadError": "Nepavyko įkelti vieno ar daugiau failų. Patikrinkite failo dydį ir formatą ir bandykite dar kartą.", "app.components.admin.PostManager.formTitle": "Redaguoti idėją", "app.components.admin.PostManager.generalApiError2": "Bandant gauti šį elementą įvyko klaida. <PERSON><PERSON><PERSON><PERSON><PERSON>, ar URL arba portalo ID yra teisingi ir ar turite prieigą prie šio elemento.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Žemėlapio duomenis galite įkelti tik kaip GeoJSON sluoksnius arba importuoti iš \"ArcGIS Online\". Jei norite įkelti GeoJSON sluoksnį, pašalinkite visus ArcGIS duomenis.", "app.components.admin.PostManager.goToDefaultMapView": "Eiti į numatytąjį žemėlapių centrą", "app.components.admin.PostManager.hiddenFieldsLink": "paslė<PERSON>i laukai", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Patarimas: <PERSON><PERSON> \"Typeform\", p<PERSON><PERSON><PERSON><PERSON> {hiddenFieldsLink} , kad <PERSON>, kas atsak<PERSON> į jūsų apklausą.", "app.components.admin.PostManager.import2": "Importas", "app.components.admin.PostManager.importError": "Pasirink<PERSON> failo ne<PERSON><PERSON>, nes tai nėra galiojantis GeoJSON failas", "app.components.admin.PostManager.importEsriFeatureLayer": "\"Esri\" elementų sluoksnio importavimas", "app.components.admin.PostManager.importEsriWebMap": "Importuoti Esri žiniatinklio žemėlapį", "app.components.admin.PostManager.importInputs": "Importo įvestys", "app.components.admin.PostManager.imported": "I<PERSON>rt<PERSON><PERSON>", "app.components.admin.PostManager.initiativeFormTitle": "Redaguoti iniciatyvą", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} iš {totalCount} {totalCount, plural, one {įvestis buvo} other {įvestis buvo}} importuota. Importas vis dar v<PERSON>, patikrinkite vėliau.", "app.components.admin.PostManager.inputManagerHeader": "Įvestis", "app.components.admin.PostManager.inputs": "Įvestis", "app.components.admin.PostManager.inputsExportFileName": "įvestis", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Rod<PERSON><PERSON> tik tuos <PERSON>, apie kuriuos reikia atsiliepimų", "app.components.admin.PostManager.issueFormTitle": "Redaguoti problemą", "app.components.admin.PostManager.latestFeedbackMode": "Kaip paa<PERSON>škinimą naudokite naujausią esamą oficialų atnaujinimą", "app.components.admin.PostManager.layerAdded": "Sluoksnis sėkmingai pridėtas", "app.components.admin.PostManager.likes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Perkėlus šį įvesties elementą iš dabartinio projekto, bus prarasta informacija apie jam priskirtus etapus. Ar norite tęsti?", "app.components.admin.PostManager.mapData": "Žemėlapio duomenys", "app.components.admin.PostManager.multipleInputs": "{ideaCount} p<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.newFeedbackMode": "Parašykite naują at<PERSON>, kuriame paa<PERSON>ite šį pakeitimą", "app.components.admin.PostManager.noFilteredResults": "Pasirinkti filtrai nedavė jokių rezultatų", "app.components.admin.PostManager.noInputs": "Įvesties dar n<PERSON>ra", "app.components.admin.PostManager.noInputsDescription": "<PERSON><PERSON><PERSON> pridėti savo indėlį arba pradėti nuo ankstesnio dalyvavimo projekto.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 įvestis} one {1 įvestis} other {# įvestis}} bus importuota iš pasirinkto projekto ir fazės. Importas vyks fone, o jam pasibaigus įvestys bus rodomos įvesties tvarkyklėje.", "app.components.admin.PostManager.noOne": "Nepriskirta", "app.components.admin.PostManager.noProject": "Nėra projekto", "app.components.admin.PostManager.officialFeedbackModal.author": "Autorius", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Pasirink<PERSON>, kaip bus rod<PERSON><PERSON> j<PERSON>das", "app.components.admin.PostManager.officialFeedbackModal.description": "Oficialių atsiliepimų teikimas padeda užtikrinti proceso skaidrumą ir didina pasitikėjimą platforma.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "<PERSON>rius yra privalomas", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Reikalingas grįžtamasis ryšys", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Paaiškinkite statuso pakeitimo priežastį", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Atsiliepimai apie paštą", "app.components.admin.PostManager.officialFeedbackModal.skip": "Praleisti šį kartą", "app.components.admin.PostManager.officialFeedbackModal.title": "Paaiškinkite savo sprendimą", "app.components.admin.PostManager.officialUpdateAuthor": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON> mat<PERSON> j<PERSON> vardą", "app.components.admin.PostManager.officialUpdateBody": "Paaiškinkite šį statuso pasikeitimą", "app.components.admin.PostManager.offlinePicks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.offlineVotes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balsai", "app.components.admin.PostManager.onlineVotes": "Balsavimas internetu", "app.components.admin.PostManager.optionFormTitle": "Redaguoti parinktį", "app.components.admin.PostManager.participants": "Dalyviai", "app.components.admin.PostManager.participatoryBudgettingPicks": "Parinkimai", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Pasirinkimai internetu", "app.components.admin.PostManager.pbItemCountTooltip": "Kiek kartų tai buvo įtraukta į kitų dalyvių dalyvau<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.petitionFormTitle": "Redaguoti peticiją", "app.components.admin.PostManager.postedIn": "Posted in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Redaguoti projektą", "app.components.admin.PostManager.projectsTab": "Projektai", "app.components.admin.PostManager.projectsTabTooltipContent": "Norėdami per<PERSON> p<PERSON> iš vieno projekto į kitą, galite juos vilkti ir nuleisti. Atkreipkite dėmesį, kad laiko juostos projektuose įrašą vis tiek reikės įtraukti į konkretų etapą.", "app.components.admin.PostManager.proposalFormTitle": "Redaguoti p<PERSON>ū<PERSON>ą", "app.components.admin.PostManager.proposedBudgetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.publication_date": "Paskelbta", "app.components.admin.PostManager.questionFormTitle": "Redaguot<PERSON> k<PERSON>", "app.components.admin.PostManager.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "<PERSON><PERSON> na<PERSON>jo nustatyti filtrus", "app.components.admin.PostManager.resetInputFiltersDescription": "<PERSON><PERSON> filtrus, kad <PERSON> visus įvesties duomenis.", "app.components.admin.PostManager.saved": "Išsaugota", "app.components.admin.PostManager.screeningTooltip": "Į dabartinį planą atrankinė patikra neįtraukta. Pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi, kad jį atrakintumėte.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Šiame etape atranka išjungiama. Eikite į fazės s<PERSON>, kad ją įjungtumėte.", "app.components.admin.PostManager.selectAPhase": "Pasirinkite etapą", "app.components.admin.PostManager.selectAProject": "Pasirinkite projektą", "app.components.admin.PostManager.setAsDefaultMapView": "Išsaugoti dabartinį centrinį tašką ir priartinimo lygį kaip numaty<PERSON><PERSON> žem<PERSON>lapio parametrus", "app.components.admin.PostManager.startFromPastInputs": "Pradėkite nuo ankstesnių įvesties duomenų", "app.components.admin.PostManager.statusChangeGenericError": "Įvyko klaida, pabandykite vėliau arba kreipkitės į palaikymo komandą.", "app.components.admin.PostManager.statusChangeSave": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.statusesTab": "Statusas", "app.components.admin.PostManager.statusesTabTooltipContent": "Pakeiskite praneš<PERSON> būsen<PERSON> vilkdami ir nuleisdami. Pradinis autorius ir kiti autoriai gaus pranešimą apie pakeistą būsen<PERSON>.", "app.components.admin.PostManager.submitApiError": "<PERSON><PERSON> problema pateiki<PERSON> formą. <PERSON><PERSON><PERSON><PERSON><PERSON>, ar <PERSON><PERSON><PERSON>, ir bandykite dar kartą.", "app.components.admin.PostManager.timelineTab": "<PERSON><PERSON> j<PERSON>", "app.components.admin.PostManager.timelineTabTooltipText": "Norėdami kopijuoti pranešimus į skirtingus projekto etapus, vilkite ir nuleiskite juos.", "app.components.admin.PostManager.title": "Pavadinimas", "app.components.admin.PostManager.topicsTab": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTabTooltipText": "Pridėkite žymas prie įvesties vilkdami ir nuleisdami.", "app.components.admin.PostManager.view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votes": "Balsai", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "Vienu metu galite pridėti tik vieną žiniatinklio žemėlapį. Jei norite importuoti kitą žemėlapį, pašalinkite esamą.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Žemėlapio duomenis galite įkelti tik kaip GeoJSON sluoksnius arba importuoti iš \"ArcGIS Online\". Jei norite prijungti žiniatinklio žemėlapį, pašalinkite visus esamus GeoJSON sluoksnius.", "app.components.admin.PostManager.webMapTooltip": "Interneto žemėlapio portalo ID rasite \"ArcGIS Online\" elemento puslapyje, de<PERSON><PERSON><PERSON><PERSON> pu<PERSON>.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON><PERSON><PERSON> nei dieną} one {Vieną dieną} other {# dienų}} liko", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> a<PERSON>kla<PERSON> rezultatus", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "To negalima at<PERSON><PERSON><PERSON>", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> rezultatus", "app.components.admin.ProjectEdit.survey.downloadResults2": "Atsisiųsti apklausos rezultatus", "app.components.admin.ReportExportMenu.FileName.fromFilter": "i<PERSON>", "app.components.admin.ReportExportMenu.FileName.groupFilter": "grupė", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projektas", "app.components.admin.ReportExportMenu.FileName.topicFilter": "<PERSON><PERSON><PERSON>", "app.components.admin.ReportExportMenu.FileName.untilFilter": "iki", "app.components.admin.ReportExportMenu.downloadPng": "Atsisiųsti kaip PNG", "app.components.admin.ReportExportMenu.downloadSvg": "Atsisiųsti kaip SVG", "app.components.admin.ReportExportMenu.downloadXlsx": "Atsisiųsti \"Excel", "app.components.admin.SlugInput.regexError": "Sluoksnyje gali būti tik paprastos ma<PERSON> (a-z), <PERSON><PERSON><PERSON><PERSON><PERSON> (0-9) ir brū<PERSON> (-). Pirmasis ir paskutinis simboliai negali būti brūk<PERSON>neliai. <PERSON><PERSON> e<PERSON> brū<PERSON> (--) dra<PERSON><PERSON><PERSON><PERSON>.", "app.components.admin.TerminologyConfig.saveButton": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.components.admin.commonGroundInputManager.title": "Pavadinimas", "app.components.admin.seatSetSuccess.admin": "<PERSON><PERSON>", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.close": "Uždaryti", "app.components.admin.seatSetSuccess.manager": "Vadybininkas", "app.components.admin.seatSetSuccess.orderCompleted": "Užsakymas įvykdytas", "app.components.admin.seatSetSuccess.reflectedMessage": "Jūsų plano pakeitimai bus atspindėti kitame sąskaitų pateikimo cikle.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} pasirinktam (-iems) naudotojui (-ams) su<PERSON><PERSON><PERSON>.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Ar tikrai norite ištrinti visus apklausos rezultatus?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Šis dalyvavimo metodas yra beta versijoje. Palaipsniui jį diegiame, kad gautume atsiliepimų ir patobulintume patirtį.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Atsiliepimų apie dokumentą rinkimas yra pasirinktinė funk<PERSON>, kuri nėra įtraukta į dabartinę licenciją. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Įnašas", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Reikalingas die<PERSON> s<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "<PERSON><PERSON><PERSON>, per kurias reikia surinkti mažiausią balsų skaiči<PERSON>, s<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Daugiau informacijos apie tai, kaip įterpti \"Google\" formų nuorodą, rasite {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "šis paramos straip<PERSON>nis", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Iniciatyva", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "<PERSON><PERSON> vadinti įvestį?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Komentaras", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Čia pateikite nuorodą į \"Konveio\" dokumentą. Daugiau informacijos apie \"Konveio\" nustatymą rasite mūsų svetainėje {supportArticleLink} .", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "para<PERSON> straip<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Tai neįtraukta į dabartinį planą. Susisiekite su savo Vyriausybės sėkmės vadybininku arba administratoriumi, kad jį atrakintumėte.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Reikalingas didžiausias biudžetas", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Didž<PERSON>usias balsų skaičius vienam variantui turi būti mažes<PERSON> arba lygus bendram balsų skaičiui", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Reikalingas maksimalus balsų s<PERSON>č<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Žinučių siuntimas", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Mažiausias biudžetas negali būti didesnis už didžiausią biudžetą", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Reikalingas minimal<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Minimalus balsų skaičius negali būti didesnis už maksimalų.", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Reikalingas <PERSON> b<PERSON> s<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Trūks<PERSON>a p<PERSON>igo<PERSON> data", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Trūkstama pradžios data", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Galimybė", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Įvesties t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kortelėje Įvesties tvarkytuvas sukonfigūruokite balsavimo parink<PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Sukonfigūruokite balsavimo parinktis svetainėje {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Dalyva<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Dalyviai", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Peticija", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Administratoriai ir vadovai", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Do<PERSON><PERSON> anotacija:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} gali da<PERSON>iame etape.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Komentaras:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Bendra įžeminimo fazė", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Ištrinti etapą", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, ištrinkite šį etapą", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Ar tikrai norite ištrinti šį etapą?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Visi su šiuo etapu susiję duomenys bus ištrinti. To negalima atšaukti.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Dokumentų anotavimo etapas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Visi", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> et<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Idėjos kūrimo etapas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Platformos apklausos etape", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Informavimo etapas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Galutinė data nenurodyta", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Apklausos etapas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Pasiūlymų teikimo etapas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Reaguokite:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Užsiregistruota į renginį:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Pateikite įvesties duomenis:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Apklausa:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Apklausa:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurių el. pašto adresai patvirtinti", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Savanoryst<PERSON><PERSON> et<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Balsavimas:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Balsavimo etapas", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Kas gali da<PERSON>?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Įvestys nebus matomos, kol jų neperž<PERSON>ūr<PERSON>s ir nepatvir<PERSON>s administratorius. Autoriai negali redaguoti įvesties duomenų po to, kai jie buvo peržiūrėti arba į juos sureaguota.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Tik administratoriai", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "<PERSON><PERSON>, turi<PERSON><PERSON> nuo<PERSON>, gali bendrauti su projekto projektu.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Patvirtinimas leidžia projektų vadovams paskelbti projektą.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "<PERSON><PERSON><PERSON><PERSON> {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Archyvuota", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Projektas", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Redaguoti aprašymą", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Visi", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Grup<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Neprisijung<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Projektą gali skelbti tik administratoriai{inFolder, select, true { arba aplankų tvarkytojai} other {}} .", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 dalyvis} other {{participantsCount} dalyviai}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Įterptųjų metodų (pvz., išorinių apklausų) dalyviai", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Projekto se<PERSON>i", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Pastaba: Įjungus anoniminio arba atviro da<PERSON> le<PERSON>, naudotojai gali daly<PERSON> keli<PERSON> kart<PERSON>, tod<PERSON>l naudotojų duomenys gali būti klaidinantys arba neiš<PERSON>mūs.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Dalyviai <b>neįtraukiami</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Dalyvauja:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Renginio registrantai", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> \"Go Vocal\" metodais", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Projekto recenzentams buvo pranešta.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Paskelbti", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Paskelbta - Aktyvi", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Paskelbta - Baigta", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Atnaujinti projekto peržiūros nuorodą", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Atkurti projekto peržiūros nuorodą. Tai panaikins ankstesnę nuorodą.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Senosios nuorodos nust<PERSON> ve<PERSON>, tač<PERSON>u bet kada galite sukurti naują nuorodą.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Ar tikrai? Tai išjungs dabartinę nuorodą", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, atnaujinkite nuorodą", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "<PERSON><PERSON><PERSON> p<PERSON> projekt<PERSON>, jį turi patvirtinti administratorius{inFolder, select, true { arba vienas iš aplankų tvarkytojų} other {}} . Norėdami pap<PERSON>yti patvir<PERSON>, spustelėkite toliau esantį mygtuką.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Nustatymai", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Kopijuoti nuorodą", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "Nukopijuota nuoroda", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Dalijimasis privačiomis nuorodomis nėra įtrauktas į dabartinį planą. Pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi, kad jį atrakintumėte.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Dalytis š<PERSON>o projektu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "<PERSON><PERSON> turi p<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projektas", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimalus s<PERSON>stytinų balsų s<PERSON>č<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Reikalingas <PERSON> b<PERSON> s<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.report": "Ataskaita", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Reikalavimas atlikti sąnaudų atranką", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "<PERSON><PERSON> j<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Eismo", "app.components.formBuilder.cancelMethodChange1": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.changeMethodWarning1": "Keičiant metodus gali būti paslėpti visi įvesties duomenys, sukurti arba gauti naudojant ankstesnį metodą.", "app.components.formBuilder.changingMethod1": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.confirmMethodChange1": "Taip, tęsti", "app.components.formBuilder.copySurveyModal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.description": "Tai nukopijuos visus klausimus ir logiką be atsakymų.", "app.components.formBuilder.copySurveyModal.duplicate": "Dublikatas", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Šiame projekte tinkamų etapų nerasta", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Fazė nepasirinkta. Pirmiausia pasirinkite fazę.", "app.components.formBuilder.copySurveyModal.noProject": "Nėra projekto", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Projektas nepasirinktas. Pirmiausia pasirinkite projektą.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Jau išsaugojote šios apklausos pakeitimus. Jei dubliuosite kitą apklausą, pakeitimai bus prarasti.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Apklausos etapas", "app.components.formBuilder.copySurveyModal.title": "Pasirinkite apklausą, kurią norite dubliuoti", "app.components.formBuilder.editWarningModal.addOrReorder": "Pridėti arba keisti klausimų eiliškumą", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Jūsų atsakymo duomenys gali būti netikslūs.", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Redaguoti tekstą", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Taisote rašybos klaidą? Tai neturės įtakos jūsų atsakymo duomenims", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Prarasite su tuo klausimu susijusius atsakymo duomenis", "app.components.formBuilder.editWarningModal.deteleAQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.editWarningModal.exportYouResponses2": "eksportuokite savo atsakymus.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Įspėjimas: <PERSON><PERSON>te visam laikui prarasti atsakymo duomenis. <PERSON><PERSON><PERSON>,", "app.components.formBuilder.editWarningModal.noCancel": "Ne, atšaukti", "app.components.formBuilder.editWarningModal.title4": "Redaguoti tiesioginę apkla<PERSON>ą", "app.components.formBuilder.editWarningModal.yesContinue": "Taip, tęsti", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "šios apklausos prieigos teisių nustatymai", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "\"Demografiniai laukai apklausos formoje\" yra įjungta. Kai rodoma apklausos forma, visi sukonfigūruoti demografiniai klausimai bus įtraukti į naują puslapį prieš pat apklausos pabaigą. Šiuos klausimus galima keisti svetainėje {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "šio etapo prieigos teisių nustatymus.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "<PERSON><PERSON> pateikti ap<PERSON><PERSON><PERSON>, iš apklausos respondentų nebus reikalaujama užsiregistruoti ar prisijungti, to<PERSON><PERSON><PERSON> gali bū<PERSON>, kad atsakymai bus pateikiami du kartus.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Atmesdami registracijos ir (arba) prisijungimo žingsnį, su<PERSON><PERSON><PERSON>, kad nebūtų renkama demografinė informacija apie apklausos respondentus, o tai gali turėti įtakos jūsų duomenų analizės galimybėms.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "<PERSON><PERSON> \"Prieigos te<PERSON>\" nustatyta, kad prieiga leidžiama \"Bet kam\".", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "Jei norite tai pakeisti, galite tai padaryti {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Registracijos/prisijungimo etape apklausos respondentams užduodami šie demografiniai klausimai.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Norėdami supaprastinti demografinės informacijos <PERSON> ir u<PERSON><PERSON>, kad ji būtų integruota į naudotojų duomenų bazę, patariame visus demografinius klausimus įtraukti tiesiai į registracijos ir prisijungimo procesą. Norėdami tai padary<PERSON>, naudokite {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Paprašykite naudotojų sekti sritis arba temas", "app.components.onboarding.followHelperText": "Taip aktyvuojamas registracijos proceso etapas, kuria<PERSON> naudoto<PERSON>i gal<PERSON>s sekti toliau nurodytas sritis arba temas.", "app.components.onboarding.followPreferences": "Sekite nuostatas", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} pagal plan<PERSON>, {noOfAdditionalSeats} papildomai", "app.components.seatsWithinPlan.seatsWithinPlanText": "Vietos pagal planą", "app.containers.Admin.Campaigns.campaignFrom": "<PERSON><PERSON>:", "app.containers.Admin.Campaigns.campaignTo": "Kam:", "app.containers.Admin.Campaigns.customEmails": "Pasirinktiniai el. la<PERSON>", "app.containers.Admin.Campaigns.customEmailsDescription": "Siųskite pasirinktinius el. laiškus ir tikrinkite statistiką.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet atrodo, kad neturite prieigos prie el. laišk<PERSON> skyriaus.", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automatiniai el. laiškai", "app.containers.Admin.Insights.tabReports": "Ataskaitos", "app.containers.Admin.Invitations.a11y_removeInvite": "Pašalinti kvietimą", "app.containers.Admin.Invitations.addToGroupLabel": "Pridėti šiuos žmones prie konkrečių rankinio naudojimo naudotojų grupių", "app.containers.Admin.Invitations.adminLabel1": "Suteikite pakviestiesiems administratoriaus teises", "app.containers.Admin.Invitations.adminLabelTooltip": "Pasirinkus šią parinktį, kviečiamiems žmonėms bus suteikta prieiga prie visų jūsų platformos nustatymų.", "app.containers.Admin.Invitations.configureInvitations": "3. Kvietimų konfigūravimas", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Kvietimų, atitinkančių jūsų paiešką, nėra", "app.containers.Admin.Invitations.deleteInvite": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Ar tikrai norite ištrinti šį kvietimą?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Atšaukę kvietimą galėsite iš naujo išsiųsti kvietimą šiam as<PERSON>.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Atsisiųskite ir užpildykite šabloną", "app.containers.Admin.Invitations.downloadTemplate": "Atsisiųsti šabloną", "app.containers.Admin.Invitations.email": "El. <PERSON>", "app.containers.Admin.Invitations.emailListLabel": "Rankiniu būdu įveskite ž<PERSON>, k<PERSON><PERSON>s nor<PERSON>, el. pa<PERSON><PERSON> adres<PERSON>. Kiekvieną adresą atskirkite kableliu.", "app.containers.Admin.Invitations.exportInvites": "Eksportuoti visus kvietimus", "app.containers.Admin.Invitations.fileRequirements": "Svarbu: kad k<PERSON>ti<PERSON>i būtų siunčiami te<PERSON>, iš <PERSON>o šablono negalima pašalinti jokio stulpelio. Nenaudojamus stulpelius palikite tu<PERSON>.", "app.containers.Admin.Invitations.filetypeError": "Neteisingas failo tipas. Palaikomi tik XLSX failai.", "app.containers.Admin.Invitations.groupsPlaceholder": "Grupė <PERSON>", "app.containers.Admin.Invitations.helmetDescription": "Kvieskite naudotojus į platformą", "app.containers.Admin.Invitations.helmetTitle": "Administratoriaus k<PERSON>timų skydelis", "app.containers.Admin.Invitations.importOptionsInfo": "Į šias parink<PERSON> bus atsižvelgta tik tada, kai jos nebus apibrėž<PERSON> \"Excel\" faile.\n      Daugiau informacijos rasite svetainėje {supportPageLink} .", "app.containers.Admin.Invitations.importTab": "Importuoti el. pa<PERSON>to adresus", "app.containers.Admin.Invitations.invitationExpirationWarning": "<PERSON><PERSON><PERSON><PERSON>, kad kvietimų galiojimo laikas baigiasi po 30 dienų. Praėjus <PERSON>, juos vis tiek galite siųsti iš naujo.", "app.containers.Admin.Invitations.invitationOptions": "Kvietimo parink<PERSON>", "app.containers.Admin.Invitations.invitationSubtitle": "Kvieskite žmones į platformą bet kuriuo metu. Jie gaus neutralų kvietimo el. laišką su jūsų logotipu, kuriame bus prašoma užsiregistruoti platformoje.", "app.containers.Admin.Invitations.invitePeople": "Kvieskite žmones el. paštu", "app.containers.Admin.Invitations.inviteStatus": "Statusas", "app.containers.Admin.Invitations.inviteStatusAccepted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteTextLabel": "Pasirinktinai įveskite žinutę, kuri bus pridėta prie kvietimo <PERSON>ško.", "app.containers.Admin.Invitations.invitedSince": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Pasirinkite kvietimo kalbą", "app.containers.Admin.Invitations.moderatorLabel": "Suteikite šiems žmonėms projektų valdymo teises", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Pasirinkus šią parinktį, k<PERSON><PERSON><PERSON><PERSON> (-iems) asmeniui (-ims) bus suteiktos pasirinkto (-ų) projekto (-ų) vadovo teisės. Daugiau informacijos apie projekto vadovo teises {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "čia", "app.containers.Admin.Invitations.name": "Pavadinimas", "app.containers.Admin.Invitations.processing": "Kvietimų siuntimas. Palaukite...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Projektas (-ai) nebuvo atrinktas (-i)", "app.containers.Admin.Invitations.save": "Išsiųsti kvietimus", "app.containers.Admin.Invitations.saveErrorMessage": "Įvyko viena ar daugiau klaidų ir kvietimai nebuvo išsiųsti. Ištaisykite toliau nurodytą (-as) klaidą (-as) ir bandykite dar kartą.", "app.containers.Admin.Invitations.saveSuccess": "Sėkmė!", "app.containers.Admin.Invitations.saveSuccessMessage": "Kvietimas sėkmingai išsiųstas.", "app.containers.Admin.Invitations.supportPage": "paramos puslapis", "app.containers.Admin.Invitations.supportPageLinkText": "Apsilankykite palaikymo pu<PERSON>pyje", "app.containers.Admin.Invitations.tabAllInvitations": "Visi kvietimai", "app.containers.Admin.Invitations.tabInviteUsers": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.textTab": "<PERSON><PERSON><PERSON> būdu įveskite el. pa<PERSON>to adresus", "app.containers.Admin.Invitations.unknownError": "<PERSON><PERSON><PERSON> nutiko ne taip. Prašome pabandyti vėliau.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Įkelkite u<PERSON><PERSON><PERSON><PERSON>ą šablono failą", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} jei norite gauti daugiau informacijos apie visus palaikomus importo šablono stulpelius.", "app.containers.Admin.Moderation.all": "Visi", "app.containers.Admin.Moderation.belongsTo": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.collapse": "<PERSON><PERSON>. ma<PERSON><PERSON>", "app.containers.Admin.Moderation.comment": "Komentaras", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.confirmCommentDeletion": "Ar tikrai norite ištrinti šį komentarą? Tai nuolatini<PERSON> ve<PERSON>, kurio negal<PERSON>.", "app.containers.Admin.Moderation.content": "Turinys", "app.containers.Admin.Moderation.date": "Data", "app.containers.Admin.Moderation.deleteComment": "Ištrinti komentarą", "app.containers.Admin.Moderation.goToComment": "Atidarykite šį komentarą naujame skirtuke", "app.containers.Admin.Moderation.goToPost": "Atidarykite šį pranešimą naujame skirtuke", "app.containers.Admin.Moderation.goToProposal": "Atidaryti šį pasiūlymą naujame skirtuke", "app.containers.Admin.Moderation.markFlagsError": "Nepavyko p<PERSON>žymėti elemento (-ų). Bandykite dar kartą.", "app.containers.Admin.Moderation.markNotSeen": "Ženklas {selectedItemsCount, plural, one {# elementas} other {# elementai}} kaip nematytas", "app.containers.Admin.Moderation.markSeen": "Ženklas {selectedItemsCount, plural, one {# elementas} other {# elementai}} kaip matyti", "app.containers.Admin.Moderation.moderationsTooltip": "Šiame puslapyje galite greitai patikrinti visus naujus įrašus, pask<PERSON><PERSON>us jūsų platformoje, įskaitant idėjas ir komentarus. <PERSON><PERSON><PERSON> pa<PERSON><PERSON><PERSON><PERSON> p<PERSON> kaip \"matytus\", kad kiti ž<PERSON>, ką dar reikia apdoroti.", "app.containers.Admin.Moderation.noUnviewedItems": "Nėra nematomų elementų", "app.containers.Admin.Moderation.noViewedItems": "Nėra matytų elementų", "app.containers.Admin.Moderation.pageTitle1": "Pašarai", "app.containers.Admin.Moderation.post": "Raš<PERSON><PERSON>", "app.containers.Admin.Moderation.profanityBlockerSetting": "<PERSON><PERSON><PERSON> b<PERSON>", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "bloku<PERSON><PERSON> praneš<PERSON>, kuriuose yra dažniausiai pranešamų įžeidžiančių žodži<PERSON>.", "app.containers.Admin.Moderation.project": "Projektas", "app.containers.Admin.Moderation.read": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.removeFlagsError": "Nepavyko p<PERSON> įspėjimo (-ų). Bandykite dar kartą.", "app.containers.Admin.Moderation.rowsPerPage": "Eilut<PERSON><PERSON> vien<PERSON>", "app.containers.Admin.Moderation.settings": "Nustatymai", "app.containers.Admin.Moderation.settingsSavingError": "Nepavyko išsaugoti. Pabandykite dar kartą pakeisti nustatymą.", "app.containers.Admin.Moderation.show": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.status": "Statusas", "app.containers.Admin.Moderation.successfulUpdateSettings": "Nustatymai sėkmingai at<PERSON>ujinti.", "app.containers.Admin.Moderation.type": "Tipas", "app.containers.Admin.Moderation.unread": "Nepastebėta", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Šį puslapį sudaro š<PERSON>i. Galite juos įjungti / išjungti ir redaguoti pagal poreikį.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> puslapį", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Puslapyje <PERSON>pate<PERSON>", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Pried<PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Prid<PERSON><PERSON><PERSON> failus (ne daugiau kaip 50 MB), kuriuos bus galima atsisiųsti iš <PERSON>.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Apačioje esantis informacijos skyrius", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Į puslapio apačioje esantį pritaikomąjį skyrių įtraukite savo turinį.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Renginių sąrašas", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Rodyti su projektais susijusius įvykius.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "<PERSON> baneris", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Pritaikykite puslapio reklaminio skydelio vaizdą ir tekstą.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Projektų sąrašas", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "<PERSON><PERSON><PERSON> projektus pagal puslapio nustatymus. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rodomus projektus.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> informaci<PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Į puslapio viršuje esantį pritaikomąjį skyrių įtraukite savo turinį.", "app.containers.Admin.PagesAndMenu.addButton": "Pridėti į navbar", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Pavadinimas navbar'e", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Ar tikrai norite iš<PERSON>nti šį puslapį? To negal<PERSON><PERSON>.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Pateikite visų kalbų pavadinimą", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Kiti galimi pu<PERSON>i", "app.containers.Admin.PagesAndMenu.components.savePage": "<PERSON>šsaugoti puslapį", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "<PERSON><PERSON><PERSON> (ne daugiau kaip 50 MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Sėkmė", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Turinys", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Nepavyko išsaugoti priedų", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Failai neturėtų būti didesni nei 50 MB. Pridėti failai bus rodomi šio puslapio apačioje.", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "<PERSON>š<PERSON><PERSON><PERSON><PERSON> p<PERSON>ai", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Priedai | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Pried<PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir įjungti priedus", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> priedus", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Pateikite turinį visomis kalbomis", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Sėkmė", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Turinys", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Nepavyko išsaugoti apatinio informacijos sky<PERSON>us", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "<PERSON>š<PERSON>ug<PERSON>s apatinis informacijos skyrius", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Apačioje esantis informacijos skyrius", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Išsaugoti ir įjungti apatinį informacijos skyrių", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Išsaugoti apatinį informacijos skyrių", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Pasirinktinių puslapių kūrimas nėra įtrauktas į dabartinę licenciją. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Pasirinkite bent vieną <PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Sėkmė", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "Pagal vietovę", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "<PERSON><PERSON> (-as)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Projektų rodymas pagal žymą ar sritį nėra dabartinės licencijos dalis. Susisiekite su \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Turinys", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Redaguoti pasirinktinį puslapį", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "<PERSON><PERSON><PERSON><PERSON> projektai", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "<PERSON><PERSON><PERSON><PERSON>, kurie projektai ir susiję įvykiai gali būti rodomi pu<PERSON>.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> sukurtas", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> pu<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Pavadinimas naršymo j<PERSON>stoje", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Sukurti pasirinktinį puslapį | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Sukurti pasirinktinį puslapį", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Nėra", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Puslapio nustatymai", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Išsaugoti pasirinktinį puslapį", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Pasirinkite sritį", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "<PERSON><PERSON><PERSON><PERSON> sritis", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Sluoksnyje gali būti tik paprastos ma<PERSON> (a-z), <PERSON><PERSON><PERSON><PERSON><PERSON> (0-9) ir brū<PERSON> (-). Pirmasis ir paskutinis simboliai negali būti brūk<PERSON>neliai. <PERSON><PERSON> e<PERSON> brū<PERSON> (--) dra<PERSON><PERSON><PERSON><PERSON>.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Turite įvesti slugą", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Pavadinimas", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Įveskite pavadinimą visomis kal<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Įveskite pavadinimą", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasirinktinį puslapį", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Redaguoti pasirinktinį puslapį | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Pus<PERSON>io turinys", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.automatedEmailsLinkText": "automatiniai el. la<PERSON>škai", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Jei vieno etapo projektų atveju pabaigos data yra tuš<PERSON>, o aprašymas neužpildytas, projekto puslapyje nebus rodomas laiko grafi<PERSON>.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Nėra galimų projektų pagal jū<PERSON>ų {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Šiam projektui netaikomas nei žymų, nei sričių filtras, todė<PERSON> nebus rodomi jokie projektai.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Projektų sąrašas | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "<PERSON><PERSON><PERSON> nust<PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projektų sąrašas", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Šiame puslapyje bus rodomi šie projektai pagal jū<PERSON>ų {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "NUMATYTASIS", "app.containers.Admin.PagesAndMenu.deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.editButton": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Sėkmė", "app.containers.Admin.PagesAndMenu.heroBannerError": "Nepavyko išsaugoti herojaus vėliavos", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hero<PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hero<PERSON> banerį", "app.containers.Admin.PagesAndMenu.homeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Pateikite turinį bent viena kalba.", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Į nar<PERSON>ymo juostą galite įtraukti ne daugiau kaip 5 elementus.", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Puslap<PERSON><PERSON> ir meniu | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "<PERSON><PERSON>lint<PERSON> iš navbar", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir įjungti hero<PERSON><PERSON> banerį", "app.containers.Admin.PagesAndMenu.title": "Puslapia<PERSON> ir meniu", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Sėkmė", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Turinys", "app.containers.Admin.PagesAndMenu.topInfoError": "Nepavyko išsaugoti viršutinės informacijos <PERSON>", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Išsaugota viršutinė informacijos skiltis", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> informacijos sky<PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> informaci<PERSON>", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Išsaugoti ir įjungti viršutinį informacijos skyrių", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Išsaugoti viršutinį informacijos skyrių", "app.containers.Admin.PagesAndMenu.viewButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Bendruomenė", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Aukščiausio lygio įtraukties rodikliai", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "<PERSON><PERSON><PERSON> pat<PERSON> įtraukties rodik<PERSON>, k<PERSON><PERSON><PERSON> mūs<PERSON> p<PERSON>, pad<PERSON><PERSON> kuriant įtraukesnę ir reprezentatyvesnę dalyvavimo platformą.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Aukščiausio lygio dalyvavimo <PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Toliau šiame skirsnyje pateikiami pagrindiniai pasirinkto laikotarpio dalyvavimo rod<PERSON>, apžvelgiamos dalyvavimo tendencijos ir veik<PERSON> rodik<PERSON>.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projektai", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "paskelbti projektai", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Platformos ataskaita", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Jūsų projektai", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Toliau apžvelgiami viešai matomi projekt<PERSON>, kurie sutampa su pasirinktu laiko intervalu, da<PERSON><PERSON>usiai šiuose projektuose naudojami metodai ir bendro dalyvavi<PERSON> rod<PERSON>.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Registracijų tvarkaraštis", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Užblokuoti naudotojai", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Tvarkykite užblokuotus naudotojus.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Ištrinti grupę", "app.containers.Admin.Users.GroupsHeader.editGroup": "Redaguoti grupę", "app.containers.Admin.Users.GroupsPanel.admins": "Administratoriai", "app.containers.Admin.Users.GroupsPanel.allUsers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Grup<PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.managers": "Projektų vadovai", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Priskirti elementai", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Apžvelkite visus platformoje užsiregistravusius asmenis ir organizacijas. Įtraukite pasirinktus naudotojus į rankines grupes arba išmaniąsias grupes.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Laukiama kvietimo", "app.containers.Admin.Users.admin": "<PERSON><PERSON>", "app.containers.Admin.Users.assign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.assignedItems": "Priskirti elementai {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Įsigykite vieną papildomą vietą", "app.containers.Admin.Users.changeUserRights": "Naudotojo teisių keitimas", "app.containers.Admin.Users.confirm": "Patvirtinkite", "app.containers.Admin.Users.confirmAdminQuestion": "Ar tikrai norite suteikti {name} platformos administratoriaus teises?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Ar tikrai norite nustatyti {name} kaip įprastą naudotoją?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Ar tikrai norite nustatyti {name} kaip įprastą naudotoją? Atkreipkite dėmesį, kad jis neteks valdytojo teisių į visus projektus ir aplankus, kuri<PERSON> jis priskirtas patvirtinus.", "app.containers.Admin.Users.deleteUser": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.email": "El. <PERSON>", "app.containers.Admin.Users.folder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.folderManager": "Aplankų tvarkyklė", "app.containers.Admin.Users.helmetDescription": "Naudotojų sąrašas administrator<PERSON>us s<PERSON>ra<PERSON>e", "app.containers.Admin.Users.helmetTitle": "Administratorius - naudotojų prietaisų skydelis", "app.containers.Admin.Users.inviteUsers": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.joined": "Prisijungė prie", "app.containers.Admin.Users.lastActive": "Paskutinis aktyvus", "app.containers.Admin.Users.name": "Pavadinimas", "app.containers.Admin.Users.noAssignedItems": "Nėra priskirtų elementų", "app.containers.Admin.Users.options": "Parinktys", "app.containers.Admin.Users.permissionToBuy": "<PERSON><PERSON><PERSON><PERSON> sute<PERSON>ti {name} <PERSON><PERSON><PERSON> teises, turite įsigyti 1 papildomą vietą.", "app.containers.Admin.Users.platformAdmin": "<PERSON><PERSON>", "app.containers.Admin.Users.projectManager": "Projektų vadovas", "app.containers.Admin.Users.reachedLimitMessage": "Pasiekėte savo plano vietų limitą, bus pridėta 1 papildoma vieta {name} .", "app.containers.Admin.Users.registeredUser": "Registruotas naudotojas", "app.containers.Admin.Users.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.removeModeratorFrom": "Naudotojas moderu<PERSON> a<PERSON>, kuria<PERSON> p<PERSON>o šis projektas. Vietoj to pašalinkite užduotį iš \"{folderTitle}\".", "app.containers.Admin.Users.role": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.seeProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profilį", "app.containers.Admin.Users.selectPublications": "Pasirinkite projektus arba aplankus", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Įveskite paie<PERSON><PERSON> tip<PERSON>", "app.containers.Admin.Users.setAsAdmin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.setAsNormalUser": "Nustatyti kaip įprastą naudotoją", "app.containers.Admin.Users.setAsProjectModerator": "Nustatyti kaip projekto vadovą", "app.containers.Admin.Users.setUserAsProjectModerator": "Projekto vadovu paskirti {name}", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.blockAction": "Blokuoti naudotoją", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Atliekant šį veiksmą šio naudotojo turinys nebus pašalintas. <PERSON><PERSON>, ne<PERSON><PERSON>š<PERSON>te moderuoti jo turinio.", "app.containers.Admin.Users.userBlockModal.blocked": "Užblokuotas", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Šis naudotojas užblokuotas nuo {from}. Draudimas galioja iki {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Ar tikrai norite atblo<PERSON>oti {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} blo<PERSON><PERSON><PERSON> iki {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 diena} other {{numberOfDays} dienų}}", "app.containers.Admin.Users.userBlockModal.header": "Blokuoti naudotoją", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Priežastis", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Apie tai bus pranešta užblokuotam naudotojui.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Pasirinktas naudotojas negalės prisijungti prie platformos {daysBlocked}. Jei norite tai at<PERSON><PERSON><PERSON>, galite jį atblokuoti iš užblokuotų naudotojų sąrašo.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Atblokuoti", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, noriu at<PERSON>i šį naudotoją", "app.containers.Admin.Users.userDeletionConfirmation": "Visam laikui p<PERSON> šį naudotoją?", "app.containers.Admin.Users.userDeletionFailed": "Šalinant šį naudotoją įvyko klaida, bandykite dar kartą.", "app.containers.Admin.Users.userDeletionProposalVotes": "Taip pat bus ištrinti visi šio naudotojo balsai dėl p<PERSON>ūly<PERSON>ų, dėl kurių vis dar galima balsuoti.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Vartotojo įžvalgos", "app.containers.Admin.Users.youCantDeleteYourself": "Negalite ištrinti savo paskyros per naudotojo administratoriaus puslapį", "app.containers.Admin.Users.youCantUnadminYourself": "Dabar negalite atsisakyti administrator<PERSON>us vaidmens", "app.containers.Admin.communityMonitor.communityMonitorLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.healthScore": "<PERSON><PERSON><PERSON><PERSON> balas", "app.containers.Admin.communityMonitor.healthScoreDescription": "<PERSON><PERSON> balas yra visų jausmų skal<PERSON> k<PERSON>, į kuriuos dalyviai atsakė pasirinktu la<PERSON>piu, vid<PERSON><PERSON>.", "app.containers.Admin.communityMonitor.lastQuarter": "<PERSON><PERSON><PERSON><PERSON> ket<PERSON>", "app.containers.Admin.communityMonitor.liveMonitor": "<PERSON><PERSON><PERSON><PERSON><PERSON> monitor<PERSON>", "app.containers.Admin.communityMonitor.noResults": "Šio laikotarpio rezultatų nėra.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Atsakymų į apklausą nėra", "app.containers.Admin.communityMonitor.participants": "Dalyviai", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Ataskaitos", "app.containers.Admin.communityMonitor.settings": "Nustatymai", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Bendruomen<PERSON><PERSON> s<PERSON> apklausoje priimamos paraiškos.", "app.containers.Admin.communityMonitor.settings.accessRights2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Po to, kai naudotojas užregistruoja dalyvavimą renginyje, patei<PERSON>a balsavimą arba grįžta į projekto puslapį pateikęs apklausą.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Bendruomenės monitoriaus valdytojai gali pasiekti ir valdyti visus bendruomenės monitoriaus nustatymus ir duomenis.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Bendruomen<PERSON><PERSON> s<PERSON> vado<PERSON>i", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Vadovai gali redaguoti \"Community Monitor\" apklausą ir leidimus, matyti atsakymų duomenis ir kurti ataskaitas.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Numatytoji <PERSON> vertė yra 100 %.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Iššokančių<PERSON><PERSON> la<PERSON> (0-100)", "app.containers.Admin.communityMonitor.settings.management2": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.popup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Naudotojams periodiškai rodoma<PERSON> langa<PERSON>, kuria<PERSON> jie raginami užpildyti Bendrijos stebėtojo apklausą. <PERSON><PERSON><PERSON> regu<PERSON>, kuris nustato naudo<PERSON>j<PERSON>, atsitiktinai matysiančių iššokantį langą, procentin<PERSON> dalį, kai bus įvykdytos toliau nurodytos sąlygos.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Iššokančiųjų langų nustatymai", "app.containers.Admin.communityMonitor.settings.preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Vartotojas dar nebuvo pildęs apklausos per pastaruosius 3 mėnesius.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Vartotojas per pastaruosius 3 mėnesius dar nebuvo matę<PERSON> lango.", "app.containers.Admin.communityMonitor.settings.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.saved": "Išsaugota", "app.containers.Admin.communityMonitor.settings.settings": "Nustatymai", "app.containers.Admin.communityMonitor.settings.survey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.surveySettings3": "<PERSON><PERSON><PERSON> n<PERSON>", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Įkrovus pagrindinį puslapį arba pasirinktinį puslapį.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonimizuoti visus naudotojo duomenis", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Visi apklausos duomenys, k<PERSON>uos pateiks naudotojai, p<PERSON><PERSON> juo<PERSON> įrašant, bus anonimiški.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Naudotojai vis tiek turės laikytis dalyvavimo reikalavimų, nurodytų \"Prieigos teisėse\". Naudotojo profilio duomenys nebus prieinami apklausos duomenų eksporto metu.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ly<PERSON>mis naudotojams gali būti rod<PERSON> i<PERSON> langa<PERSON>?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Kas yra vadovai?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "<PERSON><PERSON> viso apkla<PERSON> atsakymų", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI santrauka", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Įjungti Bendrijos monitori<PERSON>", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Ši funkcija neįtraukta į dabartinį planą. Norėdami j<PERSON> atrak<PERSON>, pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi.", "app.containers.Admin.communityMonitor.upsell.healthScore": "<PERSON><PERSON><PERSON><PERSON> balas", "app.containers.Admin.communityMonitor.upsell.learnMore": "Sužinokite daugiau", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Rezultatas per tam tikrą laiką", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "\"Community Monitor\" padeda <PERSON>, nes nuolat stebi gyventojų pasitikėjimą, pasitenkinimą paslaugomis ir bendruomenė<PERSON> g<PERSON>.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Gaukite a<PERSON>škius įvertinimus, įtakingas citatas ir ketvir<PERSON><PERSON> at<PERSON>ait<PERSON>, kuria galėsite pasidalyti su kolegomis ar išrinktais pareigūnais.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "<PERSON>g<PERSON><PERSON> skaitomi balai, kurie la<PERSON> b<PERSON><PERSON> kei<PERSON>.", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Pagrindinės gyventojų citatos, apibendrintos pagal AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "<PERSON><PERSON><PERSON>, pritaikyti jūsų miesto kontekstui", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Atsitiktine tvarka platformoje įdarbinti gyventojai", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Ketvirtinės PDF ataskaitos, <PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON> j<PERSON> j<PERSON> bendru<PERSON>n<PERSON>, kol <PERSON>os dar <PERSON>o", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Skaičiuokite", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "<PERSON><PERSON> kom<PERSON> arba kita", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Įrenginių tipai", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Įrenginio tipas", "app.containers.Admin.earlyAccessLabel": "Ankstyvoji prieiga", "app.containers.Admin.earlyAccessLabelExplanation": "Tai naujai išleista funkcija, kuri<PERSON> galima naudoti ankstyvosios prieigos sistemoje.", "app.containers.Admin.emails.addCampaign": "Sukurti el. paštą", "app.containers.Admin.emails.addCampaignTitle": "Sukurti naują el. paštą", "app.containers.Admin.emails.allParticipantsInProject": "Visi projekto dalyviai", "app.containers.Admin.emails.allUsers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automatiniai el. laiškai siunčiami automatiškai ir yra paleidžiami atlikus naudotojo veiksmus. Kai kuriuos iš jų galite išjungti visiems savo platformos naudotojams. Kitų automatinių el. laiškų išjungti negalima, nes jie reikalingi tinkamam jūsų platformos veikimui.", "app.containers.Admin.emails.automatedEmails": "Automatiniai el. laiškai", "app.containers.Admin.emails.automatedEmailsDigest": "El. laiškas bus išsiųstas tik tada, jei yra turinio", "app.containers.Admin.emails.automatedEmailsRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurie gaus šį el. lai<PERSON>ą", "app.containers.Admin.emails.automatedEmailsTriggers": "Įvykis, d<PERSON><PERSON> kurio si<PERSON> šis el. la<PERSON>", "app.containers.Admin.emails.changeRecipientsButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.clickOnButtonForExamples": "Paspauskite toliau esantį mygtuką ir peržiūrėkite šio el. laiško pavyzdžius mūsų palaikymo puslapyje.", "app.containers.Admin.emails.confirmSendHeader": "Elektroniniu paštu visiems naudotojams?", "app.containers.Admin.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.draft": "Projektas", "app.containers.Admin.emails.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "Redaguoti kampaniją", "app.containers.Admin.emails.editDisabledTooltip2": "Jau netrukus: <PERSON><PERSON><PERSON> metu šio la<PERSON>š<PERSON> redaguoti negalima.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Mygtuko te<PERSON>", "app.containers.Admin.emails.editRegion_intro_multiloc": "Įvadas", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "Pavadinimas", "app.containers.Admin.emails.emptyCampaignsDescription": "Lengvai palaikykite ryšį su dalyviais siųsdami jiems el. laiš<PERSON>. Pasirinkite, su kuo susisiekti, ir stebėkite savo dalyvavimą.", "app.containers.Admin.emails.emptyCampaignsHeader": "Siųskite pirmąjį el. laišką", "app.containers.Admin.emails.failed": "Nepavyko", "app.containers.Admin.emails.fieldBody": "Žinutė", "app.containers.Admin.emails.fieldBodyError": "Pateikite el. pašto p<PERSON> visomis kal<PERSON>", "app.containers.Admin.emails.fieldReplyTo": "Atsakymai turėtų būti siunčiami adresu", "app.containers.Admin.emails.fieldReplyToEmailError": "Nurodykite el. pašto adresą tinkamu formatu, pavyzdžiui, <EMAIL>.", "app.containers.Admin.emails.fieldReplyToError": "Nurodykite el. pašto ad<PERSON>ą", "app.containers.Admin.emails.fieldReplyToTooltip": "<PERSON><PERSON><PERSON> p<PERSON>, kur siųsti atsakymus į el. lai<PERSON>.", "app.containers.Admin.emails.fieldSender": "<PERSON><PERSON>", "app.containers.Admin.emails.fieldSenderError": "Nurodykite el. laiško siuntėją", "app.containers.Admin.emails.fieldSenderTooltip": "<PERSON><PERSON><PERSON> n<PERSON>, ką gavėjai matys kaip el. lai<PERSON> si<PERSON>.", "app.containers.Admin.emails.fieldSubject": "<PERSON><PERSON> p<PERSON><PERSON> tema", "app.containers.Admin.emails.fieldSubjectError": "Pateikite el. laiško temą visomis kalbomis", "app.containers.Admin.emails.fieldSubjectTooltip": "Tai bus rodoma el. laiško temos eilutėje ir naudotojo gautų laiškų apžvalgoje. Pasistenkite, kad jis būtų aiškus ir įtraukiantis.", "app.containers.Admin.emails.fieldTo": "Į", "app.containers.Admin.emails.fieldToTooltip": "Galite pasirinkti naudotojų grupes, kurioms bus siunčiami el. laiškai", "app.containers.Admin.emails.formSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaip juodraštį", "app.containers.Admin.emails.from": "<PERSON><PERSON>:", "app.containers.Admin.emails.groups": "Grup<PERSON><PERSON>", "app.containers.Admin.emails.helmetDescription": "Siųskite rankinius el. laiškus naudotojų grupėms ir aktyvuokite automatines kampanijas", "app.containers.Admin.emails.nameVariablesInfo2": "Su piliečiais galite kalbėtis tiesiogiai naudodami kintamuosius {firstName} {lastName}. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, \"<PERSON><PERSON><PERSON><PERSON> {firstName} {lastName}, ...\".", "app.containers.Admin.emails.previewSentConfirmation": "Į jūsų el. pašto adresą buvo išsiųstas peržiūros el. lai<PERSON>", "app.containers.Admin.emails.previewTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.regionMultilocError": "Nurodykite visų kalbų vertę", "app.containers.Admin.emails.seeEmailHereText": "Kai tik bus išsiųstas tokio tipo el. laiš<PERSON>, galėsite jį patikrinti čia.", "app.containers.Admin.emails.send": "Si<PERSON>sti", "app.containers.Admin.emails.sendNowButton": "Siųsti dabar", "app.containers.Admin.emails.sendTestEmailButton": "Siųskite man bandomąjį el. lai<PERSON>ą", "app.containers.Admin.emails.sendTestEmailTooltip": "<PERSON><PERSON><PERSON><PERSON> nuo<PERSON>, tik į jūsų el. pašto ad<PERSON> bus išsiųstas bandomasis el. laiškas. Taip galėsite pat<PERSON><PERSON><PERSON>, kaip el. laiškas atrodo realybėje.", "app.containers.Admin.emails.senderRecipients": "Siuntėjas ir gavėjai", "app.containers.Admin.emails.sending": "Siuntimas", "app.containers.Admin.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "Tai naudotojams siunčiami el. la<PERSON>škai", "app.containers.Admin.emails.subject": "Tema:", "app.containers.Admin.emails.supportButtonLabel": "Žr. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mūsų pagalbos puslapyje", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "Kam:", "app.containers.Admin.emails.toAllUsers": "Ar norite siųsti šį el. laišką visiems registruotiems naudotojams?", "app.containers.Admin.emails.viewExample": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.ideas.import": "Importas", "app.containers.Admin.inspirationHub.AllProjects": "Visi projektai", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Dokumento anotacija", "app.containers.Admin.inspirationHub.ExternalSurvey": "<PERSON>š<PERSON><PERSON><PERSON> apklausa", "app.containers.Admin.inspirationHub.Filters.Country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Method": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Search": "Pa<PERSON>š<PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "G<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Highlighted": "Pab<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Information": "Informacija", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Pasirink<PERSON> šalį, kad pama<PERSON><PERSON>te prisegtus projektus", "app.containers.Admin.inspirationHub.PinnedProjects.country": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Nerastas nė vienas šios šalies prisegtas projektas. Pakeiskite šalį, kad pamatytumėte kitų šalių prisegtus projektus", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Pakeiskite šalį, kad pamatytum<PERSON>te daugiau prisegtų projektų", "app.containers.Admin.inspirationHub.Poll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Atviro tipo", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Fazė {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "<PERSON><PERSON>, kad jūs<PERSON> projektas būtų įtrauktas į įkvėpimo centrą, kre<PERSON><PERSON>t<PERSON><PERSON> į \"GovSuccess\" v<PERSON><PERSON><PERSON>.", "app.containers.Admin.inspirationHub.Proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Rūšiuoti pagal", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Dalyviai (pirmiausia mažiausia suma)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Dalyviai (pirmi pagal dydį)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Pradžios data (seniausia pirma)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Pradžios data (naujausia pirma)", "app.containers.Admin.inspirationHub.Survey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Sudarytas geriausių projektų visame pasaulyje sąrašas.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Bendraukite su kitais specialistais ir mokykitės iš j<PERSON>.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtruokite pagal metod<PERSON>, miesto dydį ir šalį.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Įgalinti įkvėpimo centrą", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Ši funkcija neįtraukta į dabartinį planą. Norėdami j<PERSON> atrak<PERSON>, pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi.", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Sužinokite daugiau", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "\"Inspiration Hub\" jungia jus prie išskirtinių dalyvavimo projektų \"Go Vocal\" platformose visame pasaulyje. <PERSON><PERSON><PERSON><PERSON><PERSON>, kaip kituose miestuose vykdomi sėkmingi projektai, ir pabendraukite su kitais praktikais.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Prisijunkite prie novatoriškų demokratijos praktikų tinklo", "app.containers.Admin.inspirationHub.Volunteering": "Savanorystė", "app.containers.Admin.inspirationHub.Voting": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.commonGround": "<PERSON><PERSON> pagrindas", "app.containers.Admin.inspirationHub.filters": "filtrai", "app.containers.Admin.inspirationHub.resetFilters": "<PERSON><PERSON> na<PERSON>jo nustatyti filtrus", "app.containers.Admin.inspirationHub.seemsLike": "<PERSON><PERSON><PERSON>, kad daugiau projektų nėra. Pabandykite pakeisti {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Redaguot<PERSON> kamp<PERSON> la<PERSON>", "app.containers.Admin.messaging.automated.variablesToolTip": "Pranešime galite naudoti š<PERSON> kintamuo<PERSON>:", "app.containers.Admin.messaging.helmetTitle": "Žinučių siuntimas", "app.containers.Admin.messaging.newProjectPhaseModal.alternatively": "Taip pat galite išjungti šią el. pašto kampaniją tam tikriems etapams kiekvieno etapo nustatymuose.", "app.containers.Admin.messaging.newProjectPhaseModal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.messaging.newProjectPhaseModal.disabledMessage1": "Taip pat bus išjungta {emailCampaignName} el. pašto kampanija visiems esamiems projekto etapams. Kol šis nustatymas bus išjungtas, negalėsite konfigūruoti šios el. pašto kampanijos jokiam etapui.", "app.containers.Admin.messaging.newProjectPhaseModal.enabledMessage1": "Tai automatiškai neįjungs el. pašto kampanijos {emailCampaignName} esamiems projekto etapams. Įjungus šį nustatymą, šią el. pašto kampaniją galėsite konfigūruoti tik kiekvienam etapui.", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOff1": "Ar tikrai norite išjungti el. pašto kampanijos {emailCampaignName} nustatymą?", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOn1": "Įjungti el. pa<PERSON>to kamp<PERSON> {emailCampaignName} nustatymą?", "app.containers.Admin.messaging.newProjectPhaseModal.turnOff": "Taip, išjunkite", "app.containers.Admin.messaging.newProjectPhaseModal.turnOn": "<PERSON><PERSON>, įjunkite", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Šiame valdiklyje rodomi k<PERSON> naudotojo projektai, <b>atsižvelgiant į j<PERSON> sekimo par<PERSON></b>. <PERSON> apima projektus, kuri<PERSON><PERSON> jie seka, taip pat projektus, kuri<PERSON> įvestis jie seka, ir projektus, susi<PERSON><PERSON> su juos dominančiomis temomis ar sritimis.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Šis valdiklis naudotojui bus rodomas tik tuo atveju, jei yra projekt<PERSON>, kuriuose jis gali dalyvauti. Jei matote šį praneš<PERSON>, tai <PERSON><PERSON><PERSON><PERSON>, kad šiuo metu jūs (administratorius) negalite dalyvauti jokiuose projektuose. Šis pranešimas nebus matomas tikrajame pagrindiniame puslapyje.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Šiame valdiklyje bus rod<PERSON> proje<PERSON>, kuriuose naudotojas š<PERSON>o metu gali <b><PERSON><PERSON><PERSON><PERSON></b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Pavadinimas", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Šis valdiklis naudotojui bus rodomas tik tuo atveju, jei pagal jo sekimo parinktis yra jam aktualių projektų. <PERSON><PERSON> matote šį prane<PERSON>im<PERSON>, tai <PERSON><PERSON><PERSON><PERSON>, kad j<PERSON><PERSON> (administratorius) <PERSON><PERSON>o metu nieko nesekate. <PERSON>is pranešimas nebus matomas tikrajame pagrindiniame puslapyje.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Archyvuota", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filt<PERSON><PERSON><PERSON> pagal", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Baigtas", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Baigta ir archy<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "Duomen<PERSON> nėra", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> <b>baigti ir (arba) archyvuoti projektai.</b>\"Baigti\" taip pat apima projekt<PERSON>, kurie yra paskutiniame etape ir kurių paskutinis etapas yra ataskaita.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Baigti projektai", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "<PERSON><PERSON><PERSON>, mes tai pad<PERSON>...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Pateikite visų kalbų pavadinimus", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Projektas negal<PERSON> b<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Pavadinimas", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projektas", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Gautas URL adresas", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Pridėti projektą", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Naršymo juostoje bus rodomi tik tie projektai, prie kurių prieigą turi naudotojai.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Šis valdiklis bus matomas pagrindin<PERSON>e puslapyje tik tada, kai <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> steb<PERSON> priima atsakymus.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Pavadinimas", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Svarbu:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Nuotaikų apklausos klausimo pavyzdys", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "Galutinė data nenurodyta", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Paspauskite escape, jei norite praleisti ka<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projektai ir a<PERSON>lank<PERSON> (paveldėti)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Projektų pavadinimas", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} š<PERSON>o metu dirba", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Mygtuko te<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Dalyvaukite dabar!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "a<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Pasirinkite projektą arba aplanką", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Pasirinkite projektą arba aplanką", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "<PERSON><PERSON><PERSON> avataru<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Dėmesio centre", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Pavadinimas", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Nuo {days} dienų", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Nuo {weeks} savaičių", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} prie<PERSON> kelet<PERSON> dienų", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} prie<PERSON> kelias savaites", "app.containers.Admin.project.Campaigns.campaignFrom": "<PERSON><PERSON>:", "app.containers.Admin.project.Campaigns.campaignTo": "Kam:", "app.containers.Admin.project.Campaigns.customEmails": "Pasirinktiniai el. la<PERSON>", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Siųskite pasirinktinius el. laiškus ir tikrinkite statistiką.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet atrodo, kad neturite prieigos prie el. laišk<PERSON> skyriaus.", "app.containers.Admin.project.emails.addCampaign": "Sukurti el. paštą", "app.containers.Admin.project.emails.addCampaignTitle": "<PERSON><PERSON><PERSON> kampanija", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Visi {participants} ir projekto <PERSON>", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Tai apima registruotus naudo<PERSON>, kurie atliko bet kokį veiksmą projekte. Neregistruoti arba anoniminiai naudotojai neįtraukiami.", "app.containers.Admin.project.emails.dateSent": "Išsiuntimo data", "app.containers.Admin.project.emails.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.draft": "Projektas", "app.containers.Admin.project.emails.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "Redaguoti kampaniją", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Lengvai palaikykite ryšį su dalyviais siųsdami jiems el. laiš<PERSON>. Pasirinkite, su kuo susisiekti, ir stebėkite savo dalyvavimą.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Siųskite pirmąjį el. laišką", "app.containers.Admin.project.emails.failed": "Nepavyko", "app.containers.Admin.project.emails.fieldBody": "<PERSON><PERSON> <PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBodyError": "Pateikite el. pašto p<PERSON> visomis kal<PERSON>", "app.containers.Admin.project.emails.fieldReplyTo": "Atsakymai turėtų būti siunčiami adresu", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Nurodykite el. pašto adresą tinkamu formatu, pavyzdžiui, <EMAIL>.", "app.containers.Admin.project.emails.fieldReplyToError": "Nurodykite el. pašto ad<PERSON>ą", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Pasirink<PERSON>, kuri<PERSON> el. pašto adresu naudotojai turėtų gauti ties<PERSON>ginius atsakymus į jūsų el. lai<PERSON>.", "app.containers.Admin.project.emails.fieldSender": "<PERSON><PERSON>", "app.containers.Admin.project.emails.fieldSenderError": "Nurodykite el. laiško siuntėją", "app.containers.Admin.project.emails.fieldSenderTooltip": "Pasirinkite, ką naudotojai matys kaip el. lai<PERSON><PERSON> si<PERSON>.", "app.containers.Admin.project.emails.fieldSubject": "<PERSON><PERSON> p<PERSON><PERSON> tema", "app.containers.Admin.project.emails.fieldSubjectError": "Pateikite el. laiško temą visomis kalbomis", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Tai bus rodoma el. laiško temos eilutėje ir naudotojo gautų laiškų apžvalgoje. Pasistenkite, kad jis būtų aiškus ir įtraukiantis.", "app.containers.Admin.project.emails.fieldTo": "Į", "app.containers.Admin.project.emails.formSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaip juodraštį", "app.containers.Admin.project.emails.from": "<PERSON><PERSON>:", "app.containers.Admin.project.emails.helmetDescription": "išsiųsti rankinius el. laiškus projekto dalyviams", "app.containers.Admin.project.emails.infoboxAdminText": "Kortelėje Projekto pranešimai galite siųsti el. laiškus tik visiems projekto dalyviams.  Norėdami siųsti el. laiškus kitiems dalyviams arba naudotojų pogrupiams, eikite į skirtuką {link} .", "app.containers.Admin.project.emails.infoboxLinkText": "Platformos pranešimų siuntimas", "app.containers.Admin.project.emails.infoboxModeratorText": "Kortelėje Projekto pranešimai galite siųsti el. laiškus tik visiems projekto dalyviams. Administratoriai gali siųsti el. laiškus kitiems dalyviams arba naudotojų pogrupiams per skirtuką Platform Messaging.", "app.containers.Admin.project.emails.message": "Žinutė", "app.containers.Admin.project.emails.nameVariablesInfo2": "Su piliečiais galite kalbėtis tiesiogiai naudodami kintamuosius {firstName} {lastName}. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, \"<PERSON><PERSON><PERSON><PERSON> {firstName} {lastName}, ...\".", "app.containers.Admin.project.emails.participants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.previewSentConfirmation": "Į jūsų el. pašto adresą buvo išsiųstas peržiūros el. lai<PERSON>", "app.containers.Admin.project.emails.previewTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.projectParticipants": "Projekto <PERSON>i", "app.containers.Admin.project.emails.recipients": "Gavėjai", "app.containers.Admin.project.emails.send": "Si<PERSON>sti", "app.containers.Admin.project.emails.sendTestEmailButton": "Siųsti <PERSON>", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Siųskite šį el. laiško projektą el. pa<PERSON><PERSON>, kuri<PERSON> esate p<PERSON>, kad pat<PERSON>, ka<PERSON> jis atrodo \"tikrovėje\".", "app.containers.Admin.project.emails.senderRecipients": "Siuntėjas ir gavėjai", "app.containers.Admin.project.emails.sending": "Siuntimas", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "Tai naudotojams siunčiami el. la<PERSON>škai", "app.containers.Admin.project.emails.status": "Statusas", "app.containers.Admin.project.emails.subject": "Tema:", "app.containers.Admin.project.emails.to": "Kam:", "app.containers.Admin.project.messaging.helmetTitle": "Žinučių siuntimas", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "<PERSON><PERSON> pave<PERSON> yra aplanko kort<PERSON> da<PERSON>; k<PERSON><PERSON><PERSON>, kuri<PERSON><PERSON> apibendr<PERSON><PERSON> aplankas ir kuri rod<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pagrindiniame puslapyje. Daugiau informacijos apie rekomenduojamą vaizdo raišką rasite {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Šis vaizdas rodomas aplanko puslapio viršuje. Daugiau informacijos apie rekomenduojamą vaizdo skiriamąją gebą rasite {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "apsilankykite mūsų pagalbos centre", "app.containers.Admin.projects.all.askPersonalData3": "Pridėkite vardo ir el. pa<PERSON><PERSON>", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Visi klausimai pateikti PDF formatu. Tačiau šiuo metu toliau išvardyti klausimai nėra importuojami per \"FormSync\": <PERSON><PERSON><PERSON><PERSON>, ž<PERSON><PERSON> ir failų įkėlimas.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Visi klausimai pateikti PDF formatu. Tačiau šiuo metu toliau išvardyti klausimai nėra importuojami per \"FormSync\": <PERSON><PERSON><PERSON>, susiję su žemėlapių sudarymu (nule<PERSON><PERSON><PERSON><PERSON><PERSON> smei<PERSON>, br<PERSON><PERSON><PERSON><PERSON><PERSON> maršruto ir brėžiamojo ploto klausimai), reiting<PERSON><PERSON> klausimai, matricos klausimai ir failų įkėlimo klausimai.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Formos pabaiga", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Formos pradžia", "app.containers.Admin.projects.all.components.archived": "Archyvuota", "app.containers.Admin.projects.all.components.draft": "Projektas", "app.containers.Admin.projects.all.components.manageButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "Kopijuoti projektą", "app.containers.Admin.projects.all.copyProjectError": "Įvyko klaida kop<PERSON>nt šį projektą, pabandykite vėliau.", "app.containers.Admin.projects.all.customiseEnd": "Pritaikykite formos pabaigą.", "app.containers.Admin.projects.all.customiseStart": "Pritaikykite formos pradžią.", "app.containers.Admin.projects.all.deleteFolderButton1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.deleteFolderConfirm": "Ar tikrai norite ištrinti šį aplanką? Visi aplanke esantys projektai taip pat bus ištrinti. Šio veiksmo atšaukti neįmanoma.", "app.containers.Admin.projects.all.deleteFolderError": "Iš<PERSON>lo problema šalinant šį aplanką. Bandykite dar kartą.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Iš<PERSON>nti projektą", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Ar tikrai norite ištrinti šį projektą? To negal<PERSON>.", "app.containers.Admin.projects.all.deleteProjectError": "Įvyko klaida <PERSON>nant šį projektą, pabandykite vėliau.", "app.containers.Admin.projects.all.exportAsPDF1": "Atsisiųsti PDF formą", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Galite derinti internetinius ir neprisijungus prie interneto teikiamus atsakymus. Norėdami įkelti ne internetu gautus atsakymus, eikite į šio projekto skirtuk<PERSON> \"Įvesties tvarkytuvas\" ir spustelėkite \"Importuoti\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Galite derinti internetinius ir neprisijungus prie interneto teikiamus atsakymus. Norėdami įkelti ne internetu gautus atsakymus, eikite į šio projekto skirtuką \"Apklausa\" ir spustelėkite \"Importuoti\".", "app.containers.Admin.projects.all.logicNotInPDF": "Apklausos logika neatsispindės atsisiųstame PDF dokumente. Respondentai, atsakę į popierinius klausimus, matys visus apklausos klausimus.", "app.containers.Admin.projects.all.new.Folders.Filters.search": "Paieškos aplankai", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Įkelti visi aplankai", "app.containers.Admin.projects.all.new.Folders.Table.folder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Vadovai", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projektas} other {# projektai}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Statusas", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Projekto pradžios data", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Aplankalai", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtravimas pagal da<PERSON> fazės dalyvavi<PERSON> met<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Informacija", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Dalyva<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Dokumento anotacija", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "<PERSON><PERSON> pagrindas", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Savanorystė", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informavimas", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Nepradėta", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Dalyvavimo valstybė", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Search.search": "Paieškos projektas", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "<PERSON><PERSON> a<PERSON> (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "<PERSON><PERSON> a<PERSON> (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Vadybininkas", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "<PERSON><PERSON><PERSON> pras<PERSON>s arba besibaigiantis etapas", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created": "Neseniai sukurtas", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "<PERSON>eseniai <PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Statusas", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Administratoriai", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Grup<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON><PERSON><PERSON> filt<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Daugiau nereikia pridėti jokių filtrų", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Administratoriai", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Įkelti visi projektai", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Bet kas", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Archyvuota", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "<PERSON><PERSON><PERSON><PERSON> etapas", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d palikite", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d <PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "<PERSON><PERSON><PERSON>:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Projektas", "app.containers.Admin.projects.all.new.Projects.Table.end": "Pabaiga", "app.containers.Admin.projects.all.new.Projects.Table.ended": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Baigiasi š<PERSON>dien", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Grup<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Įkeliama daugiau…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Vadybininkas", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo kairė", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo prad<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Kitas etapas:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Nepriskirta", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Fazė", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projektas", "app.containers.Admin.projects.all.new.Projects.Table.public": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.published": "Paskelbta", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Slink<PERSON> ž<PERSON>yn, kad įkeltumėte daugiau", "app.containers.Admin.projects.all.new.Projects.Table.start": "Pradžia", "app.containers.Admin.projects.all.new.Projects.Table.status": "Statusas", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Būklė:", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "<PERSON><PERSON><PERSON><PERSON>:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} grupės", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} vadovai", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y į kairę", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y prad<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "<PERSON><PERSON><PERSON><PERSON> fazė: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} liko dienų", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Aplankas: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "<PERSON><PERSON><PERSON> et<PERSON>o", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Galutinė data nenurodyta", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "Nėra etapų", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Fazė {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Etapai:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projektas", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Pradžios data: {date}", "app.containers.Admin.projects.all.new.folders": "Aplankalai", "app.containers.Admin.projects.all.new.ordering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.projects": "Projektai", "app.containers.Admin.projects.all.new.timeline": "<PERSON><PERSON> j<PERSON>", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Nepavyko įkelti laiko juostos.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Projekto pabaigos data nenustatyta", "app.containers.Admin.projects.all.new.timeline.project": "Projektas", "app.containers.Admin.projects.all.notes": "Pastabos", "app.containers.Admin.projects.all.personalDataExplanation5": "Naudojant šią parinktį į eksportuojamą PDF failą bus įtraukti vardo, pava<PERSON><PERSON><PERSON> ir el. pa<PERSON><PERSON> lauka<PERSON>. Įkėlus popierinę formą, šiuos duomenis panaudosime automatiniam neprisijungusio apklausos respondento paskyros sukūrimui.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI santrauka", "app.containers.Admin.projects.project.analysis.Comments.comments": "Komentarai", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Komentarų santrauka pat<PERSON>, kai yra 5 ar daugiau komentarų.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "A<PERSON><PERSON>dr<PERSON><PERSON> pastabas", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON><PERSON>} =1 {1 naujas komentaras} other {# nauji komentarai}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI santrauka", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Tai dirbtinio intelekto sukurtas turinys. Jis gali būti ne 100 % tikslus. Peržiūrėkite ir palyginkite su faktiniais įvesties duomenimis, kad įsitikintumėte tikslumu. <PERSON>r<PERSON><PERSON><PERSON> omen<PERSON>, kad tikslumas greič<PERSON>, jei suma<PERSON> pasirinkt<PERSON> įvesties duomenų skaičius.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Pranešimai el. paštu siunčiami tik dalyviams", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Paieškos varikliai neindeksuoja", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Nematomas pagrindiniame puslapyje arba valdikliuose", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "pasiekiama tik per tiesioginį URL adresą", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "<PERSON><PERSON><PERSON><PERSON>, kiek šis projektas yra atrandamas.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "<PERSON><PERSON> projektas matoma<PERSON> v<PERSON>, kurie turi p<PERSON>, ir bus rodomas pagrindiniame puslapyje bei valdikliuose.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Šis projektas bus paslėptas nuo plačiosios visuomenės ir bus matomas tik tie<PERSON>, kurie turi nuorodą.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Kas gali rasti šį projektą?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Atvira AI analizė", "app.containers.Admin.projects.project.ideas.analysisText2": "Peržiūrėkite dirbtinio intelekto valdomas sant<PERSON> ir peržiūrėkite atskiras paraiškas.", "app.containers.Admin.projects.project.ideas.importInputs": "Importas", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gal<PERSON> p<PERSON>, kad pras<PERSON> etapui ja da<PERSON> su visuomene.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Sukurti sudėtingesnį informacijos dalijimosi puslapį", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Sukurkite ataskaitą:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Su<PERSON>rti ataskaitą", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Sukurkite praėjusio etapo ataskaitą arba pradėkite nuo pradžių.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "<PERSON><PERSON> at<PERSON>a nėra vieša. <PERSON><PERSON> nor<PERSON>, kad ji būt<PERSON> vieša, įjunkite perjungiklį \"Mat<PERSON>\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "<PERSON><PERSON> etapas j<PERSON>, ta<PERSON><PERSON><PERSON> ataskaita dar n<PERSON>ra viešai paskelbta. <PERSON><PERSON> nor<PERSON>, kad ji <PERSON> vieša, įjunkite perjungiklį \"<PERSON><PERSON>\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Pradėkite nuo fazė<PERSON>", "app.containers.Admin.projects.project.information.ReportTab.report": "Ataskaita", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Pasidalykite ankstesnės apklausos ar idėjų kūrimo etapo rezultatais", "app.containers.Admin.projects.project.information.ReportTab.visible": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Ši ataskaita bus pask<PERSON>bta viešai, kai tik prasidės etapas. <PERSON><PERSON> nor<PERSON>, kad ji neb<PERSON> vieša, išjunkite perjungiklį \"Matoma\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "<PERSON><PERSON> at<PERSON>a šiuo metu yra vieša. <PERSON><PERSON> norite, kad ji nebūt<PERSON> vieša, išjunkite perjungiklį \"Mat<PERSON>\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Ar tikrai norite ištrinti šią ataskaitą? Šio ve<PERSON> at<PERSON>uk<PERSON> negalima.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Pridėti į fazę", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "<PERSON><PERSON><PERSON> t<PERSON>dam<PERSON> da<PERSON>, turite su tuo sutikti.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Formą galite atsisiųsti čia.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Įkelta forma buvo sukurta su skiltimi \"Asmens duomenys\".", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Formos kalba", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "<PERSON><PERSON><PERSON>, kad <PERSON>is <PERSON>as būt<PERSON> apdoroja<PERSON> naudojant \"Google Cloud Form Parser\".", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "I<PERSON>rt<PERSON><PERSON> \"Excel\" failą", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, įkelkite failą", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Šabloną galite atsisiųsti čia.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Įkelti", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Įkelkite užpildytą <b>\"Excel\" failą</b> (.xlsx). <PERSON>e turi būti naudojamas šiam projektui pateiktas šablonas. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Įkelkite <b>nuskaitytų formų PDF failą</b>. <PERSON>e turi būti naudojama šiame etape atspausdinta forma. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Naudokite šį el. pašto adresą naujam naudotojui", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Įveskite galiojantį el. pa<PERSON><PERSON>, kad sukurtum<PERSON>te naują paskyrą", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Autoriui bus sukurta nauja paskyra su šiais duomenimis. Į ją bus įtraukta ši įvestis.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Vardas", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Pa<PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Įveskite el. pašto ad<PERSON> ir (arba) vardą ir pavardę, kad šią įvestį priskirtumėte autoriui. Arba panaikinkite sutikimo langelio žymėjimą.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Su šiuo el. pašto adresu jau yra susieta paskyra. Ši įvestis bus pridėta prie jos.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Naudo<PERSON><PERSON> sutikim<PERSON> (sukurti naudotojo paskyrą)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "<PERSON><PERSON><PERSON><PERSON> visus įvesties duomenis", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Autorius:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "El. p<PERSON>:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Importuojant įvyko klaidų ir kai kurie įvesties duomenys nebuvo importuoti. Ištaisykite klaidas ir iš naujo importuokite trūkstamus įvesties duomenis.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Netinkami formos duomenys. <PERSON><PERSON><PERSON><PERSON><PERSON>, ar pirmiau pateiktoje formoje nėra klaidų.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "<PERSON><PERSON>rt<PERSON><PERSON> \"Excel\" failą (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Importas", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Importuokite nuskaitytas formas (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Importuoti nuskaitytas formas", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "I<PERSON>rtuo<PERSON> įėjimai", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importavimas. Šis procesas gali užtrukti kelias minutes.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "<PERSON>ie duomenys buvo import<PERSON><PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} import<PERSON><PERSON> įvesties duomenys, kuriuos reikia pat<PERSON>.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} įvestys negalėjo būti patvir<PERSON>. Patikrinkite kiekvieną įvestį, ar nėra patvirtinimo problemų, ir patvirtinkite atskirai.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Vietovė:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "<PERSON><PERSON> kas n<PERSON>ra ką peržiūrė<PERSON>. Spustelėkite \"{importFile}\", jei norite importuoti PDF failą su nuskaitytomis įvesties formomis arba \"Excel\" failą su įvesties duomenimis.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Kol kas n<PERSON>ra ką perž<PERSON>. Spustelėkite \"{importFile}\", kad importuotumėte \"Excel\" failą su įvesties duomenimis.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Įvestis", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Nepavyksta parodyti importuoto failo. Importuoto failo peržiūra galima tik importuojant PDF failus.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Etapas:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Pasirinktoje fazėje negali būti įėjimų. Pasirinkite kitą.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Šiame projekte nėra jokių etapų, kuriuose gali būti idėjų.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "<PERSON><PERSON><PERSON><PERSON>, prie kurio etapo norite pridėti <PERSON> įėjimus.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Įvesties importuotojas", "app.containers.Admin.projects.project.participation.comments": "Komentarai", "app.containers.Admin.projects.project.participation.inputs": "Įėjimai", "app.containers.Admin.projects.project.participation.participantsTimeline": "Dalyvių tvarkaraštis", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "Pasirinkite laikotarpį", "app.containers.Admin.projects.project.participation.usersByAge": "Naudotojai pagal amžių", "app.containers.Admin.projects.project.participation.usersByGender": "Naudotojai pagal lytį", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Galimy<PERSON>ė pridėti arba redaguoti naudotojo laukus etapo lygmeniu nėra įtraukta į dabartinę licenciją. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} parinktys", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Šie klausimai bus įtraukti į paskutinį apklausos formos puslapį, nes fazės nustatymuose pasirinkta \"Rodyti apklausos lauku<PERSON>?\".", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Jokių papildomų klausimų nebus užduodama.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Pasirinktinai", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Neprival<PERSON>s - visada įjungtas, nes į jį nurodo grupė", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Reikalinga - visada įjungta, nes į ją nurodo grupė", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Autentiškumo nustatymas naudojant {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Užpildykite toliau pateiktus papildomus klausimus", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Patvirtinkite savo el. pašto ad<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "<PERSON><PERSON><PERSON>, grąžinti iš tikrinimo metodo:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Įveskite vard<PERSON>, <PERSON><PERSON><PERSON><PERSON>, el. pa<PERSON><PERSON> ad<PERSON> ir slaptažodį", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Įveskite savo el. pa<PERSON><PERSON> ad<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "<PERSON><PERSON> seniai turėtų būti tikrinami naudotojai?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Ta<PERSON><PERSON><PERSON><PERSON><PERSON> pat<PERSON><PERSON> naudojan<PERSON> {verificationMethod} (pagal naudotojų grupę)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "<PERSON><PERSON>, ne<PERSON>ikia imtis jokių veiksmų", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Naudokite išmani<PERSON>, kad a<PERSON><PERSON><PERSON><PERSON>, remdamiesi pirmiau išvardytais patikrintais duomenimis.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Naudotojai turi būti pat<PERSON> per paskutines 30 minučių.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Naudotoja<PERSON> turi būti patik<PERSON>ti per pastarąsias {days} dienas.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "Per pastarąsias 30 dienų", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "Per paskutines 30 minučių", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "Per pastarą<PERSON><PERSON> 7 dienas", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Vieną kartą užtenka", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "<PERSON><PERSON><PERSON><PERSON>:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} pat<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Dalyviai turi susikurti pilną paskyrą su savo vardu, pat<PERSON><PERSON><PERSON> el. pašto adresu ir <PERSON>.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Dalyviai turi susikurti pilną paskyrą su savo vardu, el. pa<PERSON>to adresu ir <PERSON>.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Autentiškumo nustatymas", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Patvirtinimas el. paštu", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Dalyviai turi patvirtinti savo el. pašto ad<PERSON>ą vienkartin<PERSON> kodu.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Išplėstinis nepageidaujamų laiškų aptikimas", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "<PERSON><PERSON>, analizuodama IP adresus ir įrenginio duomenis, pad<PERSON> išvengti pasikartojančių apklausų pateikimų iš atsijungusių naudotojų. Nors ji nėra tokia tiksli kaip reikalav<PERSON>s prisijungti, ji gali padėti sumažinti pasikartojančių atsakymų skaičių.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Pastaba: <PERSON><PERSON> naudoja<PERSON><PERSON> (pvz., biuruose arba v<PERSON> \"Wi-Fi\") yra ne<PERSON> tiki<PERSON>, kad skirtingi naudotojai gali būti pa<PERSON><PERSON><PERSON> kaip dublik<PERSON>i.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Įgalinkite pažangų nepageidaujamų laiškų aptikimą", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>imai daly<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "Nėra", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Dalyvauti gali visi, kurie nori, ta<PERSON><PERSON><PERSON> nesireg<PERSON> ir nepris<PERSON>jungia.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "<PERSON><PERSON> naujo nustatyti papildomus klausimus ir grupes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Apriboti dalyvavimą tik naudotojų grupei (-ėms)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO tikrinimas", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Dalyviai turi patvirtinti savo tapatybę adresu {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Atvira AI analizė", "app.containers.Admin.projects.project.survey.allFiles": "V<PERSON> failai", "app.containers.Admin.projects.project.survey.allResponses": "Visi atsakymai", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Tikslumas: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Klaida generuojant AI santrauką. Pabandykite ją atkurti toliau.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Atvira AI analizė", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Paslėpti š<PERSON> klaus<PERSON> sa<PERSON>", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "pasirinkti įėjimai", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Atviros analiz<PERSON>", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} nauji <PERSON>", "app.containers.Admin.projects.project.survey.analysis.regenerate": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "<PERSON><PERSON><PERSON> dirbtinio intelekto įžvalgas", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Pagal dabartinį planą vienu metu galite apibendrinti ne daugiau kaip 30 įvesties duomenų. Pasitarkite su \"GovSuccess\" vadybininku arba administratoriumi ir sužinokite daugiau.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Pasirinkite susijusius klausimus analizei", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Ar į {question}analizę norite įtraukti kitus susijusius klausimus?", "app.containers.Admin.projects.project.survey.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Sutin<PERSON> naudoti \"OpenAI\" kaip šio projekto duomenų tvarkytoją", "app.containers.Admin.projects.project.survey.consentModalText1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad \"OpenAI\" būtų naudojama kaip šio projekto duomenų tvarkytoja.", "app.containers.Admin.projects.project.survey.consentModalText2": "Naudojant \"OpenAI\" API sukuriamos automatinės teksto santraukos ir dalis automatinio žymėjimo patirties.", "app.containers.Admin.projects.project.survey.consentModalText3": "Į \"OpenAI\" API siunčiame tik tai, ką naudotojai paraš<PERSON> savo a<PERSON>, id<PERSON><PERSON> ir komenta<PERSON>, niekada nesiunčiame jokios informacijos iš jų profilio.", "app.containers.Admin.projects.project.survey.consentModalText4": "\"OpenAI\" nenaudos šių duomenų tolesniam savo modelių mokymui. Daugiau informacijos apie tai, kaip \"OpenAI\" tvarko duomenų privatumą, rasite {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "čia", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Negalite įvesti analizės, kol nepakeitėte formos", "app.containers.Admin.projects.project.survey.deleteAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Ar tikrai norite ištrinti šią analizę? Šio veiksmo atšauk<PERSON> negalima.", "app.containers.Admin.projects.project.survey.explore": "Naršykite", "app.containers.Admin.projects.project.survey.followUpResponses": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> vidutiniškai", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Eksportuoti kaip GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Eksportuokite šio klausimo atsakymus kaip GeoJSON failą. Kiekvienos GeoJSON savybės visi susiję respondento apklausos atsakymai bus išvardyti tos savybės objekte \"savybės\".", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Paslėpti informaciją", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondentai} one {{respondentCount} respondentas} other {~{respondentCount} respondentai}}.", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ti informaciją", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} pasirinkimai} one {{numberChoices} pasirinkimas} other {~{numberChoices} pasirinkimai}}", "app.containers.Admin.projects.project.survey.heatMap": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Sužinokite daugiau apie š<PERSON>, su<PERSON><PERSON><PERSON> naudo<PERSON> \"Esri Smart Mapping\".", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Šilumos žemėlapis sukurtas naudojan<PERSON> \"Esri Smart Mapping\". Šilumos žemėlapiai naudingi, kai yra daug duomenų taškų. Jei taškų yra ma<PERSON>ia<PERSON>, gali būti geriau tiesiogiai peržiūrėti tik vietos taškus. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Šilumos žemėlapio vaizdas", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- <PERSON><PERSON><PERSON><PERSON><PERSON> pagal logik<PERSON>", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Kai naudotojas pasirenka šį atsakymą, logika pral<PERSON><PERSON><PERSON> visus puslapius iki puslapio {pageNumber} ({numQuestionsSkipped} klausimai praleisti). Spustelėkite , kad paslėptumėte arba parodytumėte praleistus puslapius ir klausimus.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Kai naudotojas pasirenka šį atsakymą, logika perkel<PERSON> į apklausos pabaigą ({numQuestionsSkipped} klausimai praleisti). <PERSON><PERSON><PERSON>ėkite , jei norite paslėpti arba parodyti praleistus puslapius ir klausimus.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logika šiame puslapyje praleidž<PERSON> visus puslapius iki puslapio {pageNumber} ({numQuestionsSkipped} klausimai praleisti). <PERSON><PERSON><PERSON>ėkite , jei norite paslėpti arba parodyti praleistus puslapius ir klausimus.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Šiame puslapyje logika praleidžia apklausos pabaigą ({numQuestionsSkipped} klausimai praleisti). <PERSON><PERSON><PERSON>ėki<PERSON> , jei norite paslėpti arba parodyti praleistus puslapius ir klausimus.", "app.containers.Admin.projects.project.survey.newAnalysis": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.nextInsight": "Kita įžvalga", "app.containers.Admin.projects.project.survey.openAnalysis": "Atvira AI analizė", "app.containers.Admin.projects.project.survey.otherResponses": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.page": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "Ankstesnė įžvalga", "app.containers.Admin.projects.project.survey.responses": "Atsakymai", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Šio puslapio atsakymų skaičius yra mažesnis už bendrą apklausos atsakymų skaičių, nes kai kurie respondentai šio puslapio nematė dėl apklausos logikos.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Atsakymų į šį klaus<PERSON><PERSON> skaičius yra mažesnis už bendrą apklausos atsakymų skaičių, nes kai kurie respondentai šio klausimo nematė dėl apklausos logikos.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Nuotaikų skalė", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Akimirksniu apibendrinkite visus savo atsakymus.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Bendraukite su duomenimis natūralia kalba.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Gaukite nuorodas į atskirus atsakymus iš dirbtinio intelekto sukurtų santraukų.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>žval<PERSON>ą rasite {link} .", "app.containers.Admin.projects.project.survey.upsell.button": "Atrakinti AI analizę", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "para<PERSON> straip<PERSON>", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Greitesnė duomenų analizė naudojant dirbtinį intelektą", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Ši funkcija neįtraukta į dabartinį planą. Norėdami j<PERSON> atrak<PERSON>, pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi.", "app.containers.Admin.projects.project.survey.viewAnalysis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Peržiūrėkite dirbtinio intelekto valdomas sant<PERSON> ir peržiūrėkite atskiras paraiškas.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Pasirinkite laikotarpį", "app.containers.Admin.projects.project.traffic.trafficSources": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Pakeitėme lankytojų duomenų rinkimo ir rodymo būd<PERSON>. <PERSON><PERSON><PERSON> to lankytojų duomenys yra tikslesni ir galima gauti daugiau duomenų tipų, tačiau jie vis dar atitinka BDAR reikalavimus. Šiuos naujus duomenis pradėjome rinkti tik 2024 m. lapkritį, todėl anksčiau jokių duomenų nebuvo.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Lankytojų tvarkaraštis", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Pridėkite tekstą apie etapą", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Tai yra tam tikras tekstas. Jį galite redaguoti ir formatuoti naudodami dešinėje esantį redaktorių.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Dalyviai", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Projekto rezultatai", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Ataskaitos santrauka", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Įrašykite projekto t<PERSON>, naudo<PERSON> dalyvavimo metodus ir rezultatus.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Lankytojai", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Šioje ataskaitoje yra neišsaugotų pakeitimų. Prieš spausdindami išsaugokite.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Pavadinimas jau u<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Palyginti su ankstesnėmis {days} dienomis", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Paslėpti statistiką", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Dalyvavimo lygis", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Rodyti palyginimą su praėjusiu laikotarpiu", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Pirmiausia reikia pasirinkti datos intervalą.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Komentarai", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Įėjimai", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "<PERSON><PERSON><PERSON> komenta<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Rod<PERSON><PERSON> įvestis", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "<PERSON><PERSON><PERSON> balsus", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Balsai", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demografiniai duomenys", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Registracijos datos intervalas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "<PERSON><PERSON>ci<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Vartotojai: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Ištempkite", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktyvus", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Archyvuota", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Baigtas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planuojama", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projektai", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Le<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Paskelbta", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Trūksta šio valdiklio duomenų. Perkonfigūruokite arba ištrinkite jį, kad gal<PERSON>te išsaugoti ataskaitą.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "<PERSON><PERSON><PERSON> s<PERSON>senos sistemos sve<PERSON> balas", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "Metai", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Šiame projekte tinkamų etapų nerasta", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Fazė nepasirinkta. Pirmiausia pasirinkite fazę.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Nėra projekto", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Projektas nepasirinktas. Pirmiausia pasirinkite projektą.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Negalite dubliuoti <PERSON>ask<PERSON>, nes joje patei<PERSON>ami duomenys, prie kurių neturite prieigos.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Negalite redaguoti šios ataskaitos, nes joje yra duomenų, prie kurių neturite prieigos.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Ar tikrai norite i<PERSON> \"{reportName}\"? <PERSON>io ve<PERSON> negalima at<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Ar tikrai norite ištrinti šią ataskaitą? Šio ve<PERSON> at<PERSON>uk<PERSON> negalima.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Dublikatas", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Red<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "<PERSON><PERSON><PERSON> {days, plural, no {# dienų} one {# dienų} other {# dienų}} prieš", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Bandant sukurti šią ataskaitą įvyko klaida. Prašome pabandyti vėliau.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Pradėkite nuo tuš<PERSON> pu<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Pradėkite nuo bendruomen<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Ataskaitos pavadinimas", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Su<PERSON>rti ataskaitą", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Pritaikykite savo ataskaitą ir bendrinkite ją su vidaus suinteresuotosiomis šalimis arba bendruomene kaip <PERSON> failą.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Su<PERSON>rti ataskaitą", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Sukurkite pirmąją ataskaitą", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Nepasirinktas nė vienas projektas", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Pradėkite nuo platformos šablono", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Spausdinti į PDF", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Pradėkite nuo projekto šablono", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Ketvir<PERSON> {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Ataskaitos šablonas", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "<PERSON><PERSON><PERSON> pavadin<PERSON> ataskaita jau parengta. Pasirinkite kitą pavadinimą.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Pasirinkite ketvirtį", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Pasirinkite metus", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Jei norite pasidalyti su visais, atsispausdinkite ataskaitą kaip PDF failą.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "<PERSON><PERSON><PERSON> ka<PERSON> nuorodą", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "<PERSON><PERSON><PERSON>klio nuorodą gali pasiekti tik administratoriaus naudotojai.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.contactToAccess": "Pasirink<PERSON><PERSON>s ataskaitos kūrimas yra \"Premium\" licencijos dalis. Norėdami <PERSON>, k<PERSON>ipkitės į savo \"GovSuccess\" v<PERSON><PERSON><PERSON>.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Visos ataskaitos", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Bend<PERSON><PERSON> s<PERSON> ataskaitos", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Šios ataskaitos yra susijusios su Bendrijos stebėtoju. Ataskaitos automatiškai generuojamos kas ketvirtį.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Su<PERSON>rti ataskaitą", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Pritaikykite savo ataskaitą ir bendrinkite ją su vidaus suinteresuotosiomis šalimis arba bendruomene naudodami interneto nuorodą.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Jūsų ataskaitos bus rodomos čia.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Pa<PERSON>škos ataskaitos", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Pažangos ataskaitos", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Š<PERSON>askaitas sukūrė jū<PERSON>ų Vyriausybės sėkmė<PERSON> v<PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Jūsų ataskaitos", "app.containers.Admin.reporting.deprecated": "NUSTATYTAS", "app.containers.Admin.reporting.helmetDescription": "Administrator<PERSON>us ataskaitų puslapis", "app.containers.Admin.reporting.helmetTitle": "Pranešimų teikimas", "app.containers.Admin.reporting.printPrepare": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>...", "app.containers.Admin.reporting.reportBuilder": "Ataskaitų rengėjas", "app.containers.Admin.reporting.reportHeader": "Ataskaitos antraštė", "app.containers.Admin.reporting.warningBanner3": "Šioje ataskaitoje pateiktos diagramos ir skaičiai automatiškai atnaujinami tik šiame puslapyje. Norėdami juos atnaujinti kituose puslapiuose, <PERSON><PERSON><PERSON>ug<PERSON><PERSON> ataskaitą.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "<PERSON><PERSON> pagrindas", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Informacija", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "<PERSON><PERSON><PERSON><PERSON> {days} dienų: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "<PERSON>š<PERSON><PERSON><PERSON> apklausa", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Savanorystė", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Diagrama", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "Lentelė", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.surveyFormTab.downloads": "Atsisiuntimai", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Dub<PERSON>uoti kit<PERSON> apkla<PERSON>", "app.containers.Admin.surveyFormTab.editSurveyForm": "Redaguoti apklausos formą", "app.containers.Admin.surveyFormTab.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON>ą informaciją reikia pateikti, prid<PERSON>ki<PERSON> trumpus aprašymus arba instrukcijas, k<PERSON><PERSON><PERSON> vado<PERSON>udamiesi dalyviai galė<PERSON>ų atsakyti, ir <PERSON>uro<PERSON>, ar k<PERSON><PERSON><PERSON> laukas yra neprival<PERSON>, ar prival<PERSON>.", "app.containers.Admin.surveyFormTab.surveyForm": "<PERSON><PERSON><PERSON><PERSON><PERSON> forma", "app.containers.Admin.tools.apiTokens.createTokenButton": "Sukurti naują simbolį", "app.containers.Admin.tools.apiTokens.createTokenCancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Jūsų simbolis sukurtas. Nukopijuokite toliau pateiktą {secret} ir saugiai jį išsaugokite.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Sukurkite naują simbolį, kuris būtų naudojamas su mūsų viešąja API.", "app.containers.Admin.tools.apiTokens.createTokenError": "Pateikite savo simbolio pavadin<PERSON>ą", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Sukurti simbolį", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b><PERSON><PERSON><PERSON>!</b> <PERSON><PERSON><PERSON> svetain<PERSON> {secret} galite kopijuoti tik vieną kartą. Jei uždarysite šį langą, daugiau jo nebegalėsite matyti.", "app.containers.Admin.tools.apiTokens.createTokenName": "Pavadinimas", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Suteikite savo simboliui pavadinimą", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Jūsų simbolis sukurtas", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Uždaryti", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Nukopijuota!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Sukurti naują simbolį", "app.containers.Admin.tools.apiTokens.createdAt": "Sukurta", "app.containers.Admin.tools.apiTokens.delete": "<PERSON><PERSON><PERSON><PERSON> simbolį", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Ar tikrai norite ištrinti šį simbolį?", "app.containers.Admin.tools.apiTokens.description": "Tvarkykite savo API žetonus, skirtus mūsų viešajai API. Daugiau informacijos rasite mūsų svetainėje {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Paskutinį kartą naudotas", "app.containers.Admin.tools.apiTokens.link": "API dokumentacija", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Pavadinimas", "app.containers.Admin.tools.apiTokens.noTokens": "Dar neturite jokių žetonų.", "app.containers.Admin.tools.apiTokens.title": "Viešieji API žetonai", "app.containers.Admin.tools.esriDisabled": "\"Esri\" integracija yra papildoma funkcija. Jei norite gauti daugiau informacijos apie tai, kreipkitės į \"GovSuccess\" v<PERSON><PERSON>ink<PERSON>.", "app.containers.Admin.tools.esriIntegration2": "\"Esri\" integracija", "app.containers.Admin.tools.esriIntegrationButton": "Įgalinti \"<PERSON><PERSON>ri", "app.containers.Admin.tools.esriIntegrationDescription3": "Prisijunkite prie \"Esri\" paskyros ir importuokite duomenis iš \"ArcGIS Online\" tiesiai į kartografavimo projektus.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logotipas", "app.containers.Admin.tools.esriKeyInputDescription": "Pridėkite savo Esri API raktą, kad būtų galima importuoti žemėlapių sluoksnius iš \"ArcGIS Online\" projektų žemėlapių skirtukuose.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API raktas", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Čia įklijuokite API raktą", "app.containers.Admin.tools.esriMaps": "Esri <PERSON>", "app.containers.Admin.tools.esriSaveButtonError": "Įrašant raktą įvyko klaida, bandykite dar kartą.", "app.containers.Admin.tools.esriSaveButtonSuccess": "Išsaugotas API raktas", "app.containers.Admin.tools.esriSaveButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.learnMore": "Sužinokite daugiau", "app.containers.Admin.tools.managePublicAPIKeys": "API raktų tvarkymas", "app.containers.Admin.tools.manageWidget": "<PERSON><PERSON><PERSON><PERSON> valdiklį", "app.containers.Admin.tools.manageWorkshops": "Tvarkykite seminarus", "app.containers.Admin.tools.powerBIAPIImage": "\"Power BI\" vaizdas", "app.containers.Admin.tools.powerBIDescription": "Naudokite mūsų \"Power BI\" <PERSON><PERSON><PERSON><PERSON>, kad <PERSON><PERSON> p<PERSON> \"Go Vocal\" duomenis \"Microsoft Power BI\" darbo srityje.", "app.containers.Admin.tools.powerBIDisabled1": "\"Power BI\" nėra jūsų licencijos dalis. Jei norite gauti daugiau informacijos apie tai, k<PERSON><PERSON><PERSON>tės į \"GovSuccess\" v<PERSON><PERSON>ink<PERSON>.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Atsisiųst<PERSON>", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "<PERSON>i ketinate naudoti \"Go Vocal\" duomenis \"Power BI\" duomenų sraute, š<PERSON><PERSON>ablone galėsite sukurti naują duomenų srautą, kuris bus prijungtas prie \"Go Vocal\" duomenų. Atsisiuntę šį šabloną, prie<PERSON> įkeldami į \"PowerBI\", pirmiausia turite rasti ir pakeisti šablone esančias eilutes ##CLIENT_ID### ir ##CLIENT_SECRET### savo viešaisiais API įgaliojimais.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Atsisiųsti duomenų s<PERSON>uto <PERSON>", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Duomenų s<PERSON>", "app.containers.Admin.tools.powerBITemplates.intro": "Pastaba: <PERSON><PERSON><PERSON><PERSON> naudoti bet kurį iš šių \"Power BI\" šablonų, pirmiausia turite {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "sukurti viešosios API įgaliojimų rinkinį.", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "<PERSON><PERSON>as sukurs \"Power BI\" ataskaitą pagal \"Go Vocal\" duomenis. Jame bus nustatyti visi duomenų ryšiai su \"Go Vocal\" platforma, sukurtas duomenų modelis ir keletas numatytųjų skydelių. Atidarius <PERSON> \"Power BI\" programoje, būsite paprašyti įvesti savo viešus API įgaliojimus. Taip pat turėsite įvesti savo platformos bazinį URL adresą, kuris yra: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Atsisiųsti ataskaitų šabloną", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Ataskaitos šablonas", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Daugiau informacijos apie \"Go Vocal\" duomenų naudojimą \"Power BI\" rasite mūsų svetainėje {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "para<PERSON> straip<PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "\"Power BI\" šablonai", "app.containers.Admin.tools.powerBITitle": "\"Power BI", "app.containers.Admin.tools.publicAPIDescription": "Tvarkykite įgaliojimus, kad gal<PERSON>tumėte kurti pasirinktines integracijas per mūsų viešąją API.", "app.containers.Admin.tools.publicAPIDisabled1": "Viešoji API nėra jūsų dabartinės licencijos dalis. Jei norite gauti daugiau informacijos apie tai, kreipkitės į \"GovSuccess\" vadybininką.", "app.containers.Admin.tools.publicAPIImage": "Viešasis API vaizdas", "app.containers.Admin.tools.publicAPITitle": "Vieša API prieiga", "app.containers.Admin.tools.toolsLabel": "Įrankiai", "app.containers.Admin.tools.widgetDescription": "<PERSON><PERSON><PERSON> sukurti valdiklį, jį pritaikyti ir pridėti prie savo svet<PERSON>, kad prit<PERSON>uk<PERSON><PERSON>te žmones į šią platformą.", "app.containers.Admin.tools.widgetImage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.widgetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.workshopsDescription": "Ren<PERSON><PERSON> vaiz<PERSON>, palengvinkite vienu metu vykstančias grupines diskusijas ir debatus. Rinkite informaciją, balsuokite ir pasiekite konsensusą, kaip ir neprisijungę prie interneto.", "app.containers.Admin.tools.workshopsImage": "Dirbtuvių vaizdas", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Svarstymų seminarai internetu", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "bendras platformos naudotojų s<PERSON>", "app.containers.AdminPage.DashboardPage._blank": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.allGroups": "Visos <PERSON>", "app.containers.AdminPage.DashboardPage.allProjects": "Visi projektai", "app.containers.AdminPage.DashboardPage.allTime": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.comments": "Komentarai", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Komentarai", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Norint įvertinti platformos naudotojų reprezentatyvumą, reikia bazinio duomenų rinkinio.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Šiuo metu dirbame su {fieldName} prietaisų skydeliu, netrukus jis bus prieinamas.", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# elementas yra} other {# elementai yra}} paslėpti šioje diagramoje. Norė<PERSON>i per<PERSON><PERSON><PERSON> visus duomenis, pakeiskite į {tableViewLink} .", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} naudotojų registracijai", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} iš {total} įtrauktų naudotojų ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Rodyti {numberOfHiddenItems} daugiau", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Pasirinktinai", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Pateikite bazinį duomenų rinkinį.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Reprezentatyvu<PERSON> balas:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "<PERSON><PERSON>, kaip tik<PERSON>i platformos naudotojų duomenys atspindi visą populiaciją. Sužinokite daugiau apie {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Pateikti bazinius duomenis", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "<PERSON><PERSON> g<PERSON>vento<PERSON> s<PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Vartotojai", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "<PERSON><PERSON><PERSON><PERSON> grup<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} ir da<PERSON><PERSON>u", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "<PERSON><PERSON><PERSON>us g<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Neįtraukiama {upperBound} ir v<PERSON><PERSON><PERSON> amžia<PERSON> grup<PERSON> (-ės).", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "<PERSON><PERSON><PERSON>us grup<PERSON> {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "ir da<PERSON><PERSON>u", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "<PERSON><PERSON><PERSON> g<PERSON> pavyzdį", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Išvalyti viską", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Nustatykite amž<PERSON>us grup<PERSON>, kad jos atitiktų jūsų bazinį duomenų rinkinį.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Diapazonas", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Į", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Redaguoti amžiaus grupes", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Šis elementas nebus skaičiuojamas.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Žr. {numberOfHiddenItems} daugiau...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "<PERSON><PERSON><PERSON> (neprivaloma)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "<PERSON><PERSON><PERSON><PERSON> (gimimo <PERSON>i)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Pilnas", "app.containers.AdminPage.DashboardPage.components.Field.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Užpildykite visas įjungtas parinktis arba išjunkite parinktis, kurių norite neįtraukti į diagramą. <PERSON><PERSON> b<PERSON><PERSON> u<PERSON><PERSON><PERSON><PERSON> bent viena parinktis.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.options": "Parinktys", "app.containers.AdminPage.DashboardPage.components.Field.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Išsaugota", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Norė<PERSON><PERSON> p<PERSON> įvesti bazinius duomenis, pirmiausia apsilankykite {setAgeGroupsLink} .", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "nustatyti amžiaus grupes.", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>ky<PERSON> laikas: {days} dienų", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Vidutinė atsakymo pateikim<PERSON> truk<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Pateiktas grįžtamasis ryšys", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Įvesties būsena", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Įvestys pagal būsen<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Įėjimų skai<PERSON>ius", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Indėlių procentin<PERSON> dalis", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Statusas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Statusas pakeistas", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "<PERSON><PERSON> viso", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Redaguoti bazinius duomenis", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> reprezentaty<PERSON><PERSON> balus", "app.containers.AdminPage.DashboardPage.continuousType": "Be laiko juostos", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Bendra suma", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "diena", "app.containers.AdminPage.DashboardPage.false": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.female": "moteriškos lyties", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "5 svarbiausi įvesties duomenys pagal re<PERSON>", "app.containers.AdminPage.DashboardPage.fromTo": "iš {from} į {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "platformoje vykdomos veiklos prietaisų skydelis", "app.containers.AdminPage.DashboardPage.helmetTitle": "Administratoriaus prietaisų skydelio puslapis", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Pasirink<PERSON>, kuriuos norite rodyti pagal projektą", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Pasirinkite išteklių, kurį norite rodyti pagal žymą", "app.containers.AdminPage.DashboardPage.inputs1": "Įėjimai", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Įvestys pagal būsen<PERSON>", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Pasirinkite naudotojų grupę", "app.containers.AdminPage.DashboardPage.male": "vyriškos lyties", "app.containers.AdminPage.DashboardPage.month": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.noData": "<PERSON><PERSON><PERSON>, kuriuos reikėtų pateikti, nėra.", "app.containers.AdminPage.DashboardPage.noPhase": "<PERSON>iam projektui nesukurtas joks etapas", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Daly<PERSON><PERSON>, paskelbusių įvestis, reagavusių ar komentavusių, s<PERSON><PERSON><PERSON>.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Nemėgsta", "app.containers.AdminPage.DashboardPage.numberOfLikes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "<PERSON><PERSON> viso reak<PERSON>", "app.containers.AdminPage.DashboardPage.overview.management": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekt<PERSON> ir <PERSON>", "app.containers.AdminPage.DashboardPage.overview.showLess": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.overview.showMore": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.participants": "Dalyviai", "app.containers.AdminPage.DashboardPage.participationPerProject": "Dalyvavimas projekte", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Dalyvavimas už žymą", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Ankstesnės 30 dienų", "app.containers.AdminPage.DashboardPage.previous90Days": "Ankstesnės 90 dienų", "app.containers.AdminPage.DashboardPage.previousWeek": "Ankstesnė savaitė", "app.containers.AdminPage.DashboardPage.previousYear": "Ankstesni metai", "app.containers.AdminPage.DashboardPage.projectType": "Projekto tipas : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Šis bazinis duomenų rinkinys reikalingas norint apskaičiuoti platformos naudotojų reprezentatyvumą, palyginti su visa populiacija.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Pateikite bazinį duomenų rinkinį.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kiek reprezentatyv<PERSON>s yra jūsų platformos naudotojai, palyginti su visa populiacija, remdamiesi naudotojų registracijos metu surinktais duomenimis. Sužinokite daugiau apie {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kiek reprezentaty<PERSON><PERSON>s yra jūsų platformos naudotojai, palyginti su visa populiacija, remdamiesi naudotojų registracijos metu surinktais duomenimis.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "<PERSON><PERSON><PERSON> reprez<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Atgal į prietaisų skydelį", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Šiuo metu nepalaikomas nė vienas iš įjungtų registracijos laukų.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Čia galite rodyti / slėpti prietaisų skydelio elementus ir įvesti bazinius duomenis. Čia bus rodomi tik įgalinti {userRegistrationLink} laukai.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Redaguoti bazinius duomenis", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "naudotojo registracija", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Pateikti bazinius duomenis", "app.containers.AdminPage.DashboardPage.resolutionday": "dieno<PERSON>", "app.containers.AdminPage.DashboardPage.resolutionmonth": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.resolutionweek": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.selectProject": "Pasirinkite projektą", "app.containers.AdminPage.DashboardPage.selectedProject": "dabartinio projekto filtras", "app.containers.AdminPage.DashboardPage.selectedTopic": "dabartinis žymi<PERSON> filtras", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas vyksta jūsų platformoje.", "app.containers.AdminPage.DashboardPage.tabOverview": "Apžvalga", "app.containers.AdminPage.DashboardPage.tabReports": "Ataskaitos", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabUsers": "Vartotojai", "app.containers.AdminPage.DashboardPage.timelineType": "<PERSON><PERSON> j<PERSON>", "app.containers.AdminPage.DashboardPage.titleDashboard": "Prietaisų skydelis", "app.containers.AdminPage.DashboardPage.total": "<PERSON><PERSON> viso", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Šis {period}", "app.containers.AdminPage.DashboardPage.true": "tiesa", "app.containers.AdminPage.DashboardPage.unspecified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.users": "Vartotojai", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Naudotojai pagal amžių", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Vartotojai pagal geografinę teritoriją", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Naudotojai pagal lytį", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registracijos", "app.containers.AdminPage.DashboardPage.week": "savaitė", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Patarimai renkantis favicon paveikslėlį: pasirinkite paprastą paveikslėlį, nes <PERSON>to paveikslėlio dydis yra labai mažas. Paveiksliukas turėtų būti išsaugota<PERSON> kaip <PERSON>, jis tur<PERSON><PERSON><PERSON> bū<PERSON>, o jo fona<PERSON> <PERSON> s<PERSON><PERSON><PERSON> (a<PERSON><PERSON>, j<PERSON> reiki<PERSON>, bal<PERSON>). Savo favikoną turėtumėte nustatyti tik vieną kartą, nes norint ją pakeisti, reik<PERSON>s tam tikros techninės pagalbos.", "app.containers.AdminPage.FaviconPage.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "<PERSON><PERSON><PERSON>, bandykite vėliau.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Sėkmė!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Aplankų tvarkyklės", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Aplankų valdytojai gali redaguoti aplanko aprašymą, aplanke kurti naujus projektus ir turėti visų aplanke esančių projektų valdymo teises. Jie negali ištrinti projektų ir neturi prieigos prie projektų, kurie nėra jų aplanke. Daugiau informacijos apie projektų valdymo teises galite rasti {projectManagementInfoCenterLink} .", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Nerasta atitikmenų", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "apsilankykite mūsų pagalbos centre", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Ieškoti naudotojų", "app.containers.AdminPage.FoldersEdit.addToFolder": "Pridėti į aplanką", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Archyvuota", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Ištrinkite šį aplanką", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.draftStatus": "Projektas", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "<PERSON><PERSON><PERSON>ti failus į šį aplanką", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Failai neturėtų būti didesni nei 50 MB. Pridėti failai bus rodomi aplanko puslapyje.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Šiame aplanke nėra projektų. Grįžkite į pagrindinį skirt<PERSON>ą \"Projektai\" ir kurkite bei pridėkite projektus.", "app.containers.AdminPage.FoldersEdit.folderName": "<PERSON><PERSON><PERSON><PERSON> pavadin<PERSON>s", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.multilocError": "Kiekviena kalba turi būti užpildyti visi teksto la<PERSON>i.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Į šį aplanką negalima įtraukti jokių projektų.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Aplankų kortelės vaizdas", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Aplankų projektai", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Nustatymai", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Į šį aplanką įtraukti projektai", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "<PERSON><PERSON><PERSON><PERSON>, kuriuos galite įtraukti į šį aplanką", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "<PERSON><PERSON><PERSON><PERSON>, ar <PERSON><PERSON> a<PERSON>as yra \"juodra<PERSON><PERSON>\", \"paskel<PERSON><PERSON>\", ar \"archy<PERSON><PERSON><PERSON>\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Paskelbta", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "<PERSON><PERSON><PERSON>, bandykite vėliau.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Sėkmė!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "<PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "<PERSON><PERSON> pagrindiniame pusla<PERSON>je", "app.containers.AdminPage.FoldersEdit.statusLabel": "Le<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kodėl projektai priklauso vienas kit<PERSON>, apibrėžkite vizualinį identitetą ir dalinkitės informacija.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kodėl projektai priklauso vienas kit<PERSON>, apibrėžkite vizualinį identitetą ir dalinkitės informacija.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "<PERSON><PERSON> b<PERSON><PERSON>i visi teksto la<PERSON>i.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Pavadinimas", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Sukurti naują aplanką", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Nustatymai", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Pritaikykite hero<PERSON>us banerio vaizdą ir tekstą.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "<PERSON> baneris", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hero<PERSON> banerį", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Įkvėpimo centras - tai vieta, kurioje galite rasti įkvėpimo savo projektams naršydami po kitų platformų projektus.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Redaguokite savo platformos sąlygas ir privatumo politiką. <PERSON><PERSON> puslapi<PERSON>, įskaitant puslapius Apie ir DUK, galite redaguoti skirtuke {navigationLink} .", "app.containers.AdminPage.PagesEdition.policiesTitle": "Platformos politika", "app.containers.AdminPage.PagesEdition.privacy-policy": "Privatumo politika", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Taisyk<PERSON><PERSON><PERSON> ir <PERSON>", "app.containers.AdminPage.Project.confirmation.description": "Šio ve<PERSON> negalima at<PERSON>ti.", "app.containers.AdminPage.Project.confirmation.no": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.confirmation.title": "Ar tikrai norite iš naujo nustatyti visus dalyvavimo duomenis?", "app.containers.AdminPage.Project.confirmation.yes": "<PERSON><PERSON> naujo nustatyti visus dalyvavimo duomenis", "app.containers.AdminPage.Project.data.descriptionText1": "Aiškios <PERSON>, komentarai, balsai, re<PERSON><PERSON><PERSON>, atsakymai į apklausas, apklaus<PERSON> atsakymai, savanoriai ir renginių dalyviai. Balsavimo etapų atveju šiuo veiksmu bus išvalyti balsai, bet ne parinktys.", "app.containers.AdminPage.Project.data.title": "Išvalyti visus šio projekto dalyvavimo duomenis", "app.containers.AdminPage.Project.resetParticipationData": "<PERSON><PERSON> naujo nustatyti visus dalyvavimo duomenis", "app.containers.AdminPage.Project.settings.accessRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.back": "Atgal", "app.containers.AdminPage.Project.settings.data": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.events": "Rengin<PERSON><PERSON>", "app.containers.AdminPage.Project.settings.general": "Bendra", "app.containers.AdminPage.Project.settings.projectTags": "<PERSON>je<PERSON><PERSON>", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Platformos projektų sąrašas", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Projektų prietaisų skydelis", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Kurkite naujus projektus arba tvarkykite esamus projektus.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projektai", "app.containers.AdminPage.ProjectDashboard.published": "Paskelbta", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Uždaryti nustatymų skydelį", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Centras", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Mygtukų derinimas", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Mygtuko te<PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Įveskite mygtuko tekstą", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Mygtuko tipas", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Mygtuko URL", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Įveskite mygtuko URL adresą", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Stulpelių išdėstymas", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Pradžia aprašymas", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Rodoma projekto kortelėje pagrindiniame puslapyje.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Parodyta projekto puslapyje. <PERSON><PERSON><PERSON><PERSON>, a<PERSON> ką kalbama projekte, ko tikitės iš naudotojų ir ko jie gali tikėtis iš jūsų.", "app.containers.AdminPage.ProjectDescription.errorMessage": "<PERSON><PERSON><PERSON>, bandykite vėliau", "app.containers.AdminPage.ProjectDescription.preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.ProjectDescription.saved": "Išgelbėtas!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "<PERSON>us<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> žinutę norite perduoti savo auditorijai. Redaguokite projektą ir praturtinkite jį paveikslėliais, vaizdo įrašais, <PERSON><PERSON> priedais,… . Ši informacija padeda lankytojams suprasti, apie ką yra jūsų projektas.", "app.containers.AdminPage.ProjectDescription.titleDescription": "<PERSON>je<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Baltoji erdvė", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Įtraukti pasienį", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Atšaukti redagavimą", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Numatytoji žemėlapio centro taško platuma. Priimama vertė nuo -90 iki 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Numatytoji žemėlapio centro taško ilguma. Priimama reikšmė nuo -90 iki 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Redaguoti žemėlapio sluoksnį", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Redaguoti sluoksnį", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "<PERSON><PERSON><PERSON>, bandykite vėliau", "app.containers.AdminPage.ProjectEdit.MapTab.here": "čia", "app.containers.AdminPage.ProjectEdit.MapTab.import": "GeoJSON failo importavimas", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> platuma", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Sluoksnio spalva", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Visos sluoksnio funkcijos bus stilizuotos šia spalva. Ši spalva taip pat perrašys bet kokį esamą stilių jūsų GeoJSON faile.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Pasirinktinai pasirinkite piktogramą, kuri bus rodoma žymekliuose. Spustelėkite {url} , kad pamatytumėte piktogramų, kurias galite pasirinkti, s<PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Sluoks<PERSON> pavadin<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Šis sluoksnio pavadinimas rodomas žemėlapio legendoje", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Sluoksnio įrankių užuomina", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Šis tekstas rodomas ka<PERSON> įrankių u<PERSON><PERSON><PERSON>, kai žemėlapyje užvedate pelės žymeklį ant sluoksnio funkcijų.", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Žemėlapio sluoksniai", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Šiuo metu palaikomi GeoJSON failai ir funkcijų sluoksnių bei žiniatinklio žemėlapių importavimas iš \"ArcGIS Online\". Perskaitykite {supportArticle} , kur rasite patarimų, ka<PERSON>, konvertuoti ir stilizuoti žemėlapių sluoksnius.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Žemėlapio numatytasis centras ir priartinimas", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Numatytasis žemėlapio centro taškas ir priartinimo lygis. Toliau nurodytas vertes reguliuokite rankiniu būdu arba spustelėkite mygtuką {button} žemėlapio apatiniame kairiajame kampe, kad iš<PERSON>ugotumėte dabartinį centrinį tašką ir žemėlapio priartinimo lygį kaip numatytąsias vertes.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "<PERSON><PERSON><PERSON> žemė<PERSON>io vaizdą, įskaitant žemėlapio sluoksnių įkėlimą ir stilizavimą, žem<PERSON>lapio centro ir priartinimo lygio nustatymą.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Žemėlapio konfigūracija", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Šiuo metu žemėlapio konfigūracija yra bendra visiems etapams, negalite sukurti skirtingų žemėlapio konfigūracijų kiekvienam etapui.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Nuimkite sluoksnį", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Išsaugoti priartinimą", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "para<PERSON> straip<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Neįvardytas sluoksnis", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Žemėlapio priartinimo lygis", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Numatytasis žemėlapio priartinimo lygis. Priimama reikšmė nuo 1 iki 17, kur 1 reiškia visišką priartinimą (matomas visas pasaulis), o 17 - visi<PERSON><PERSON><PERSON> priartinimą (matomi blokai ir pastatai).", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonimizuoti visus naudotojo duomenis", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Visi apklausos duomenys, k<PERSON>uos pateiks naudotojai, p<PERSON><PERSON> juo<PERSON> įrašant, bus anonimiški.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Naudotojai vis tiek turės laikytis dalyvavimo re<PERSON>, nurodytų prieigos skirtuke \"Prieigos teisės\". Naudotojo profilio duomenys nebus prieinami apklausos duomenų eksporte.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "Jei įjungsite šią parinktį, naudotojo registracijos laukai bus rodomi paskutiniame apklausos puslapyje, o ne registracijos proceso metu.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demografiniai laukai apklausos formoje", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> demografinius la<PERSON>?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Š<PERSON>e straipsnyje skaitykite daugiau apie tai, ka<PERSON> ve<PERSON>a automatini<PERSON> da<PERSON>.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Automatinio bendrinimo rezultatai", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, balsavimo rezultatais dalijamasi platformoje ir el. paš<PERSON> dalyvia<PERSON>. Taip pagal nutylėjimą užtik<PERSON> s<PERSON>.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Rezultatų dalijimasis", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "<PERSON><PERSON><PERSON><PERSON> parinktį", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "<PERSON><PERSON><PERSON><PERSON> apkla<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Redaguoti atsakymo parinktį", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Išsaugoti atsakymo parinktis", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Redaguoti atsaky<PERSON> par<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Redaguot<PERSON> k<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Apklausos rezultatų eksportavimas", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Didžiausias galimų pasirinkimų skaičius yra didesnis už parinkčių skaičių", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Nėra galimybių", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Visi klausimai turi turėti atsakymų variantus", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Tik viena galimybė", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Apklausos respondentai turi tik vieną pasirinkimą", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Tvarkykite atsakymų variantus: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Čia galite kurti apklausos klausimus, nustatyti atsakymų variantus, iš kurių dalyviai gali rinktis kiekvieną klausimą, nusp<PERSON><PERSON><PERSON><PERSON>, ar dalyviai gali pasirinkti tik vieną atsakymo variantą (vienas pasirinkimas), ar kelis atsaky<PERSON> variantus (daugkartinis pasirinkimas), ir eksportuoti apklausos rezultatus. Vienoje apklausoje galite sukurti kelis apklausos klausimus.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Vienas pasirinkim<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Apklausų nustatymai ir rezultatai", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Neteisingas didž<PERSON><PERSON> le<PERSON> dyd<PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Importas", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Pateikite atsiliepimus, pridėkite žymas arba kopijuokite pranešimus į kitą projekto etapą.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "<PERSON>var<PERSON><PERSON><PERSON> p<PERSON>, teikite atsiliepimus ir skirkite temas.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Įvesties tvarkyklė", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Rezultatų bendrinimas yra išjungtas.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Pasibaigus etapui balsavimo rezultatai nebus dalijami, nebent juos pakeisite etapo sąrankoje.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Pasibaigus etapui, šie rezultatai bus automatiškai pasidalyti. Pakeiskite šio etapo pabaigos datą, kad pakeistumėte dalijimosi rezultatais laiką.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Eksportuokite apklausos rezultatus (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Rezultatai", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Čia galite atsisiųsti \"Typeform\" ap<PERSON><PERSON><PERSON> (-ų), atlik<PERSON> (-ų) pagal šį projektą, rezulta<PERSON> ka<PERSON> \"Excel\" failą.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "<PERSON><PERSON><PERSON><PERSON><PERSON> forma", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Apklausos rezultatai", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Susipažinkite su apklausos atsakymais", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON><PERSON><PERSON> priežastį", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "<PERSON>r t<PERSON>rai?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ko reikalaujama iš savanorių ir ko jie gali tik<PERSON>.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Nepav<PERSON><PERSON>, nes formoje yra klaidų.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Vaizdas", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Pavadinimas", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Priežastis - tai veiksma<PERSON> ar ve<PERSON>, k<PERSON><PERSON> da<PERSON>viai gali atlikti savanoriškai.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Redaguoti priežastį", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Eksporto savanoriai", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Priežastis - tai veiksma<PERSON> ar ve<PERSON>, k<PERSON><PERSON> da<PERSON>viai gali atlikti savanoriškai.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "<PERSON><PERSON>ja priežastis", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Čia galite nustatyti priežastis, dėl kurių naudotojai gali savan<PERSON>uti, ir atsisiųst<PERSON> savan<PERSON>.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Savanorystė", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {nėra <PERSON>} one {# dalyvis} other {# dalyviai}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Pasirinkimams priskirkite biudžetą ir paprašykite dalyvių pasirinkti pageidaujamus variantus, kurie atitiktų bendrą biudžetą.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Biudžeto paskirstymas", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Leidimas naudotojams komentuoti gali iškreipti balsavimo procesą.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "K<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Numatytoji parinkčių peržiūra", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Veiksmai naudo<PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "<PERSON><PERSON><PERSON><PERSON>, kokius papildomus veiksmus gali atlikti naudotojai.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Fiksuota suma", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON><PERSON> p<PERSON> t<PERSON>, paga<PERSON> numa<PERSON><PERSON><PERSON><PERSON> nustatymus bus pasirinkta \"balsavimas\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Sužinokite daugiau apie tai, kada naudoti <b> {voteTypeDescription} </b> mūs<PERSON> svetainėje {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "<PERSON><PERSON><PERSON>lus balsų skaičius už vieną variantą", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Did<PERSON><PERSON>usias balsų s<PERSON>č<PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Galite apriboti bendrą naudotojo balsų skaičių (ne daugiau kaip vienas balsas už vieną parinktį).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "keli balsai už vieną parinktį", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Vartotojams suteikiama tam tikra žetonų suma, kurią jie gali paskirstyti tarp parinkčių.", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "<PERSON><PERSON> balsai už vieną parinktį", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "<PERSON><PERSON><PERSON> naudo<PERSON>jo bals<PERSON> s<PERSON>č<PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Galimybių analizės apžvalga", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "vienas balsas už vieną variantą", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Vartotojai gali pasirinkti patvirtinti bet kurią iš ši<PERSON> parink<PERSON>ių", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Vienas balsas už kiekvieną variantą", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Žetonas", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "<PERSON><PERSON> t<PERSON> būti vadi<PERSON>as balsavi<PERSON>?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "pvz., <PERSON><PERSON><PERSON>, taš<PERSON>, anglies dioksido kreditai...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "pvz., <PERSON><PERSON><PERSON>, ta<PERSON><PERSON>, anglies dioksido kreditas...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Kiekvienas balsavi<PERSON> metodas turi <PERSON>s konfigūrac<PERSON>s", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Balsavimo metodas nustato naudotojų balsavimo ta<PERSON>.", "app.containers.AdminPage.ProjectEdit.addNewInput": "Įtraukti įvestį", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Projektą į aplanką galite įtraukti dabar arba tai padaryti vėliau projekto nustatymuose.", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "<PERSON>je<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.altText": "Alt tekstas", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "<PERSON> š<PERSON> funkcija įjungta, neįmanoma <PERSON><PERSON><PERSON><PERSON>, kas u<PERSON> ką balsavo. Vartotojams vis tiek reikia turėti paskyrą ir jie gali balsuoti tik vieną kartą.", "app.containers.AdminPage.ProjectEdit.approved": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archived": "Archyvuota", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Archyvuoti projektai vis dar matomi, ta<PERSON>iau juose nebegalima to<PERSON>u da<PERSON>.", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archyvuota", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Šios srities negalima ištrinti, nes ji naudojama projektams rodyti kitame (-uose) pasirinktiniame (-uose) puslapyje (-iuose). <PERSON><PERSON><PERSON> sritį, turėsite panaikinti srities sąsają su puslapiu arba ištrinti puslapį.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Visos sritys", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projektas bus rodomas visų sričių filtruose.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "<PERSON><PERSON><PERSON> filt<PERSON>", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projektus pagrindiniame puslapyje galima filtruoti pagal sritis. Sritys gali būti nustatytos {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "čia", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Nėra konkrečios srities", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "<PERSON>lt<PERSON><PERSON>nt pagal sritį projektas nebus rodomas.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projektas bus rodomas p<PERSON> (-uose) srities (-ių) filtre (-uose).", "app.containers.AdminPage.ProjectEdit.cardDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Daugiausia diskusijų", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "<PERSON><PERSON><PERSON><PERSON> turinį", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Jau pradėtos teikti šios apklausos paraiškos. Dėl apklausos pakeitimų eksportuotuose failuose gali būti prarasti ir neišsamūs duomenys.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Apklausa sėkmingai išsaugota", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Pasirinkite balsavimo metodą ir paprašykite naudotojų nustatyti prioritetus tarp kelių skirtingų galimybių.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Atlikti balsavimą arba nustatyti prioritetus", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Sukurti projektą iš <PERSON>", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Įdiegti išorinę apkla<PERSON>ą", "app.containers.AdminPage.ProjectEdit.createInput": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> įvestį", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Sukurkite platformoje atliekamą apklausą", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Nustatykite apklausą neišeidami iš mūsų platformos.", "app.containers.AdminPage.ProjectEdit.createPoll": "Su<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Sukurkite klausimyną su keliais atsakymų variantais.", "app.containers.AdminPage.ProjectEdit.createProject": "Na<PERSON>jas projektas", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Įterpkite \"Typeform\", \"Google Form\", \"Enalyzer\", \"SurveyXact\", \"Qualtrics\", \"SmartSurvey\", \"Snap Survey\" arba \"Microsoft Forms\" apklausą.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Galite nustatyti numatytąją pranešimų rodymo pagrindiniame projekto puslapyje tvarką.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.departments": "Departamentai", "app.containers.AdminPage.ProjectEdit.descriptionTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "Tai įjungs arba išjungs nepatinka, bet patinka vis tiek bus įjungta. Rekomenduojame palikti šią funkcij<PERSON> išjungt<PERSON>, nebent atliekate parinkčių analizę.", "app.containers.AdminPage.ProjectEdit.disabled": "Neįgalieji", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "<PERSON><PERSON><PERSON> nemėgstamų puslapių skaičius", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Įgalinti nemėgti", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "<PERSON><PERSON><PERSON> atsiliepimus apie dokument<PERSON>", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Įterpkite interaktyvų PDF failą ir rinkite komentarus bei atsiliepimus naudo<PERSON>i \"Konveio\".", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Neįgalieji", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Įjungta", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Projektų projektai yra paslėpti visiems žmonėms, išskyrus administratorius ir priskirtus projektų vadovu<PERSON>.", "app.containers.AdminPage.ProjectEdit.draft": "Projektas", "app.containers.AdminPage.ProjectEdit.draftStatus": "Projektas", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "Įjungta", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "<PERSON><PERSON><PERSON><PERSON>, koki<PERSON><PERSON><PERSON><PERSON><PERSON> veik<PERSON> gali at<PERSON>ti naudotojai.", "app.containers.AdminPage.ProjectEdit.enalyzer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.eventsTab": "Rengin<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne daugiau kaip 50 MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Failai neturėtų būti didesni nei 50 MB. Pridėti failai bus rodomi projekto informacijos puslapyje.", "app.containers.AdminPage.ProjectEdit.filesTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Paprašykite dalyvių savanoriškai dalyvauti veikloje ir renginiuose.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Kaip aplankų tvarkytojas, kurdami projektą galite pasirinkti aplanką, tačiau vėliau jį pakeisti gali tik administratorius.", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> paveikslėlio alternaty<PERSON> te<PERSON>", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Pasirinkite aplanką, į kurį norite įtraukti šį projektą.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> turi<PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Pradėtos teikti šios formos paraiškos. Dėl formos pakeitimų gali būti prarasti duomenys ir eksportuotose bylose pateikti neišsamūs duomenys.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Forma sėkmingai išsaugota", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Apklausos pabaiga", "app.containers.AdminPage.ProjectEdit.fromATemplate": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.generalTab": "Bendra", "app.containers.AdminPage.ProjectEdit.google_forms": "\"Google\" formos", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Antraštė<PERSON> paveikslėlio alt tekstas", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NAUJIENA", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Pateikite informaciją naudotojams arba naudokite ataskaitų kūrimo įrankį, kad pasidalytumėte ankstesnių etapų rezultatais.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Dalytis informacija ar rezultatais", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "<PERSON>ink<PERSON> informaciją ir atsiliepimus", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Su<PERSON>rti arba rinkti įvestis, re<PERSON><PERSON><PERSON> ir (arba) komentarus. Galite rinktis iš įvairių įvesties tipų: id<PERSON><PERSON><PERSON> rink<PERSON>, galimybių analizės, klaus<PERSON><PERSON> ir atsakymų, problemų nustatymo ir kt.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Ka<PERSON> už pranešimų tvarkymą?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Visi nauji šio projekto duomenys bus priskirti šiam asmeniui. Asignitorių galima pakeisti {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Pranešimų komentavimas", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Įvesties forma", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "įvesties tvarkyklė", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Įvesties tvarkyklė", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Pranešimų pateikimas", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Reagavimas į įvestį", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "<PERSON><PERSON><PERSON><PERSON><PERSON> rod<PERSON>", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Pasirinkite numatytąjį rodinį, k<PERSON><PERSON> bus rodomi įvesties duomenys: k<PERSON><PERSON><PERSON><PERSON> tinklelio rodinyje arba smeigtukai žemėlapyje. Dalyviai gali rankiniu būdu perjungti abu rodinius.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Įkvėpimo centras", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Įterpti \"Konveio\" URL", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Vienam <PERSON>ly<PERSON> \"patinka\" paspa<PERSON><PERSON><PERSON> skai<PERSON>", "app.containers.AdminPage.ProjectEdit.limited": "Ribotas", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Įkelti daugiau <PERSON>", "app.containers.AdminPage.ProjectEdit.mapDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxLikes": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"pat<PERSON>a", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Didž<PERSON>usias balsų skaičius vienam variantui turi būti mažes<PERSON> arba lygus bendram balsų skaičiui", "app.containers.AdminPage.ProjectEdit.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Pateikdami savo krepšelį dalyviai negali viršyti š<PERSON> bi<PERSON>.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "\"Microsoft\" formos", "app.containers.AdminPage.ProjectEdit.minimum": "Minimal<PERSON>", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad dalyviai atitiktų minimalų biudžetą, kad galėtų pateikti savo krepšelį (įveskite \"0\", jei nenorite nustatyti minimalaus biudžeto).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "apsilankykite mūsų pagalbos centre", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Kas yra projektų vadovai?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Daugiau informacijos", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Reikia įkvėpimo? Susipažinkite su panašiais projektais kituose miestuose {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "<PERSON><PERSON><PERSON><PERSON> įnašą", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newInitiative": "Pridėti iniciatyvą", "app.containers.AdminPage.ProjectEdit.newIssue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newOption": "<PERSON><PERSON><PERSON><PERSON> parinktį", "app.containers.AdminPage.ProjectEdit.newPetition": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newProject": "Na<PERSON>jas projektas", "app.containers.AdminPage.ProjectEdit.newProposal": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON><PERSON><PERSON> suma", "app.containers.AdminPage.ProjectEdit.noFolder": "Nėra a<PERSON>", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "- <PERSON><PERSON><PERSON> -", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Šablonų nerasta", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Įveskite projekto pavadinimą", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionNo": "Ne", "app.containers.AdminPage.ProjectEdit.optionYes": "<PERSON><PERSON> (pasirinkite aplanką)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Dalyvavimo lygis", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Ką norite daryti?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "<PERSON><PERSON><PERSON><PERSON>, kaip naudo<PERSON>i gali da<PERSON>.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "<PERSON><PERSON><PERSON>, kas gali atlikti kiekvieną veiksmą, ir užduoti papildomų klausimų daly<PERSON>ms, kad surinktumėte daugiau informacijos.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Dalyvių reikalavimai ir klausimai", "app.containers.AdminPage.ProjectEdit.pendingReview": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.permissionsTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.pollTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Dauguma reakcijų", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Projekto k<PERSON> vaizda<PERSON>", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "<PERSON><PERSON> pave<PERSON> yra projekto kortel<PERSON> da<PERSON>; tai kortel<PERSON>, kurioje apibendrinamas projektas ir kuri rod<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pagrindiniame puslapyje.\n\n    Daugiau informacijos apie rekomenduojamą vaizdo raišką rasite {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Šis vaizdas rodomas projekto puslapio viršuje.\n\n    Daugiau informacijos apie rekomenduojamą vaizdo skiriamąją gebą rasite {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Projekto kortelės vaizdo alternatyvus tekstas", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Pateikite trumpą paveikslėlio aprašymą regos negalią turintiems naudotojams. Tai padeda ekrano skaitytuva<PERSON> perteikti, kas yra paveik<PERSON>ly<PERSON>.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Projektų valdymas", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projektų vadovai gali redaguoti projektus, tvar<PERSON><PERSON> p<PERSON> ir siųsti el. laiškus dalyviams. Daugiau informacijos apie projektų vadovams suteiktas teises galite rasti {moderationInfoCenterLink} .", "app.containers.AdminPage.ProjectEdit.projectName": "Projekto pavadinimas", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Projekto tipas", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "<PERSON><PERSON><PERSON><PERSON>, kuri<PERSON> nustat<PERSON>, turi a<PERSON><PERSON> pradžią ir pabaigą ir gali turėti skirtingus etapus. Projektai be laiko juostos yra tę<PERSON>i.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Vėliau projekto tipo pakeisti negalima.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "<PERSON><PERSON><PERSON>, kad projektas būtų nematomas tam tikriems naudotojams.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Ieškote projekto būsenos? Dabar ją bet kada galite pakeisti tiesiai iš projekto puslapio <PERSON>.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Paskelbti projektai yra matomi visiems arba grupės pogrup<PERSON>, jei jis pasirin<PERSON>.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Paskelbta", "app.containers.AdminPage.ProjectEdit.purposes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Atsitiktinis", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "<PERSON><PERSON> na<PERSON>jo nustatyti dalyvavimo duomenis", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> duomenis įvyko klaida. Bandykite dar kartą.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Sėkmė!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Jūsų forma iš<PERSON>ugota!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.selectGroups": "Pasirinkite grupę (-es)", "app.containers.AdminPage.ProjectEdit.setup": "Sąranka", "app.containers.AdminPage.ProjectEdit.shareInformation": "Dalytis informacija", "app.containers.AdminPage.ProjectEdit.smart_survey": "\"SmartSurvey\"", "app.containers.AdminPage.ProjectEdit.snap_survey": "\"Snap\" apklausa", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Nustatykite ir suasmeninkite projektą.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "apsilankykite mūsų pagalbos centre", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "<PERSON><PERSON><PERSON><PERSON> turinį", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# pasirinkimai} one {# pasirinkimas} other {# pasirinkimai}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, nor<PERSON>i", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Red<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Jau pradėta teikti paraiškas šiai apklausai. Dėl apklausos pakeitimų eksportuotuose failuose gali būti prarasti ir neišsamūs duomenys.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Failų įkėlimas", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Grįžti atgal", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Importas", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Importas", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI santraukas, skirt<PERSON> trumpo at<PERSON>, ilgo atsa<PERSON> ir sentimentų skalės kontroliniams klausimams, galite rasti kairėje šoninėje juostoje esančiame skirtuke AI.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Linijinė skalė", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrica", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Ilgas atsakymas", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "<PERSON><PERSON> p<PERSON> - pasirin<PERSON><PERSON> daugybę", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Vaizdų pasirinkimas - pasirinkite daugybę", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Atsakymų į apklausą dar nėra", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Pasirinktinai", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Jei logikos nepridėta, apkla<PERSON> vyks įprasta tvarka. Jei ir pusla<PERSON>, ir jo klausimuose yra logikos, pirmenybė bus teikiama klausimų logikai. Įsitikinkite, kad tai atitinka jūsų numatytą apklausos eigą. Daugiau informacijos rasite svetainėje {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.survey.point": "Vieta", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Ar tikrai norite išvykti?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Dabartiniai pakeitimai nebus iš<PERSON>ugoti.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.rating": "Įvertinimas", "app.containers.AdminPage.ProjectEdit.survey.required2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {atsakymai} one {atsakymai} other {atsakymai}}.", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# atsakymai} one {# atsakymai} other {# atsakymai}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "<PERSON><PERSON> p<PERSON> - pasirin<PERSON><PERSON> vieną", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Nuotaikų linijinė skalė", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Esri shapefile įkėlimas", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Apklausa sėkmingai išsaugota", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.text2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "<PERSON><PERSON> viso {count} atsakymų", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Įterpimo URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Paslauga", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Daugiau informacijos apie tai, kaip įterpti ap<PERSON>, rasite {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "čia", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Apkla<PERSON> \"Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "<PERSON>ios žymos negalima ištrinti, nes ji naudojama projektams rodyti kitame (-uose) pasirinktiniame (-uose) puslapyje (-iuose). \n<PERSON><PERSON><PERSON>mi žymą, turėsite panaikinti žymą nuo puslapio arba ištrinti puslapį.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Bendrieji projekto nustatymai", "app.containers.AdminPage.ProjectEdit.titleLabel": "Pavadinimas", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Pasirinkite trumpą, patrauklų ir aiškų pavadinimą. Jis bus rodomas išskleidžiamojoje apžvalgoje ir projekto kortelėse pagrindiniame puslapyje.", "app.containers.AdminPage.ProjectEdit.topicLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Šiam projektui pasirinkite {topicsCopy} . Vartotojai gali naudoti šiuos parametrus projektams filtruoti.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Bendras biudžetas", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Nepriskirta", "app.containers.AdminPage.ProjectEdit.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Naudokite <PERSON>ą", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projekt<PERSON>", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Savanorystė", "app.containers.AdminPage.ProjectEdit.voteTermError": "<PERSON>lsavimo s<PERSON>ly<PERSON> turi būti nurodytos visoms vietovėms", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# grupės gali peržiū<PERSON>} one {# grupė gali peržiūr<PERSON>ti} other {# grupės gali peržiūr<PERSON>}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "<PERSON><PERSON><PERSON><PERSON> įvykį", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Papildoma informacija", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adresas 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Renginio vietos adresas", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adresas 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Pvz., butas, apartamentai, pastatas", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Papildoma adreso informacija, kuri <PERSON> identifikuoti vietą, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pastato pava<PERSON>, aukšto numeris ir pan.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "<PERSON><PERSON><PERSON> pala<PERSON> straipsnį", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Išorinė nuoroda", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Pridėkite nuorodą į išorinį URL (pvz., Renginių tarnybos arba bilietų pardavimo svetainę). Nustačius šį parametrą, bus pakeistas numatytasis lankomumo mygtuko elgesys.", "app.containers.AdminPage.ProjectEvents.customButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> my<PERSON> te<PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad mygtuko tekstas būtų kitoks nei \"Registruotis\", kai nustatytas išorinis URL adresas.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Pradžia", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Pabaiga", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Ar tikrai norite ištrinti šį įvykį? <PERSON><PERSON>ra jokio būdo tai at<PERSON>!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Redaguoti įvykį", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Norėdami registrantams siųsti el. laiškus tiesiogiai iš platformos, administratoriai turi sukurti naudotojų grupę skirtuke {userTabLink} . {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Renginių datos", "app.containers.AdminPage.ProjectEvents.eventImage": "Įvykio vaizdas", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Įvykio vaizdo alternatyvus tekstas", "app.containers.AdminPage.ProjectEvents.eventLocation": "Renginio vieta", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Eksporto registruotojai", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne daugiau kaip 50 MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Priedai patei<PERSON>ami po įvykio aprašymu.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Vieta", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Didž<PERSON>usias registruo<PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Sukurti naują įvykį", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Internetin<PERSON> re<PERSON> nuoroda", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "<PERSON><PERSON> re<PERSON> v<PERSON>, pridėkite nuorodą į jį čia.", "app.containers.AdminPage.ProjectEvents.preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Patikslinti žemėlapio vietą", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Patikslinti vietą žemėlapyje", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Spustelė<PERSON><PERSON> to<PERSON> esantį žemėlapį galite pat<PERSON>, kur rod<PERSON><PERSON> jū<PERSON><PERSON> renginio vietos žymeklis.", "app.containers.AdminPage.ProjectEvents.register": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.registerButton": "Registracijos <PERSON>", "app.containers.AdminPage.ProjectEvents.registrant": "registruo<PERSON>jas", "app.containers.AdminPage.ProjectEvents.registrants": "registrantai", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Registracijos limitas", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Nepavyko išsaugoti jūsų pakeitimų, bandykite dar kartą.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Sėkmė!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Vietos paieška", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Susiekite būsimus renginius su šiuo projektu ir rodykite juos projekto renginių puslapyje.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Pavadinimas ir datos", "app.containers.AdminPage.ProjectEvents.titleEvents": "Projekto reng<PERSON>i", "app.containers.AdminPage.ProjectEvents.titleLabel": "Įvykio pavadinimas", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Mygtuko susiejimas su išoriniu URL adresu", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "<PERSON><PERSON> numatytu<PERSON><PERSON> nustatymus bus rodomas platformoje esantis renginio registravimo mygt<PERSON>, kuri<PERSON> naudotojai galės užsiregistruoti į renginį. Jį galite pakeisti, kad vietoj to būtų pateikta nuoroda į išorinį URL adresą.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "riboti renginio dalyvių skaičių", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Nustatykite didžiausią renginio dalyvių skaičių. Jei limitas bus pasiektas, daugiau registracijų nebus priimama.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Vartotojai", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "<PERSON><PERSON><PERSON><PERSON> failus į projektą", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Prie projekto, etapų ir įvykių prisekite failus iš <PERSON>.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "<PERSON><PERSON><PERSON><PERSON> failus kaip konte<PERSON>t<PERSON> į \"Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Pridėkite failų prie \"Sensemaking\" projekto, kad suteiktum<PERSON>te konteksto ir įžvalgų.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sinchronizuokite a<PERSON>, įkelkite interviu ir leiskite dirbtiniam intelektui sujungti visus duomenis.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Įkelkite bet kokį failą", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Naudokite AI failams analizuoti", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Apdoroti nuorašus ir kt.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Įžvalgos, paremtos dirbtiniu intelektu", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> įkeltus failus, kad atskle<PERSON>te pagrindines temas.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Sudarykite galimybę atlikti pažangią šių failų analizę naudodami dirbtinio intelekto apdorojimą.", "app.containers.AdminPage.ProjectFiles.askButton": "Klauskite", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Kategorija", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Pasirinkite failus", "app.containers.AdminPage.ProjectFiles.close": "Uždaryti", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Patvirtinkite ir įkelkite", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Ar tikrai norite ištrinti šį failą?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Nepavyko įkelti žymėjimo failo.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Nepavyko įkelti CSV peržiūros.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "CSV peržiūroje rodoma ne daugiau kaip 50 eilučių.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV failas yra per <PERSON>, kad būt<PERSON> galima perž<PERSON>.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.done": "Atlikta", "app.containers.AdminPage.ProjectFiles.downloadFile": "Atsisiųsti failą", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Atsisiųsti visą failą", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Vilkite ir numeskite bet kokius failus čia arba", "app.containers.AdminPage.ProjectFiles.editFile": "Redaguoti failą", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "<PERSON>ailo pavadinime negali būti <PERSON>.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Failo pavadin<PERSON>", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "<PERSON><PERSON><PERSON> nuro<PERSON> p<PERSON>.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Atsisiųsti failą", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "<PERSON>is failas nebus įkeltas, nes vir<PERSON><PERSON> 50 MB ribą.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Visi failai įkelti sėkmingai", "app.containers.AdminPage.ProjectFiles.info_sheet": "Informacija", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "pvz., WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Garso interviu, rotušės įrašai", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "pvz., PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Ataskaitos, informaciniai dokumentai", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "Interviu", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Vienu metu galite įkelti ne daugiau kaip {maxFiles} failų.", "app.containers.AdminPage.ProjectFiles.meeting": "Susitikimas", "app.containers.AdminPage.ProjectFiles.noFilesFound": "Failų nerasta.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Politika", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Šio tipo failų peržiūra dar nepalaikoma.", "app.containers.AdminPage.ProjectFiles.report": "Ataskaita", "app.containers.AdminPage.ProjectFiles.retryUpload": "Pakartokite įkėlimą", "app.containers.AdminPage.ProjectFiles.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "<PERSON><PERSON><PERSON> s<PERSON> atnaujintas.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Ieškoti failų", "app.containers.AdminPage.ProjectFiles.selectFileType": "Failo tipas", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategin<PERSON> planas", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Vienu metu galite įkelti ne daugiau kaip {maxFiles} failų.", "app.containers.AdminPage.ProjectFiles.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.upload": "Įkelti", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# failas} other {# failai}} sė<PERSON><PERSON><PERSON> įkelta, {numberOfErrors, plural, one {# klaida} other {# klaidos}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "<PERSON><PERSON><PERSON><PERSON> visus laukus", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Įvesties formos redagavimas", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Įjungta", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Įtraukite šį lauką.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "<PERSON><PERSON><PERSON>, bandykite vėliau", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus laukus", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Įvesties forma", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON>ą informaciją reikia pateikti, prid<PERSON>ki<PERSON> trumpus aprašymus arba instrukcijas, k<PERSON><PERSON><PERSON> vado<PERSON>udamiesi dalyviai galė<PERSON>ų atsakyti, ir <PERSON>uro<PERSON>, ar k<PERSON><PERSON><PERSON> laukas yra neprival<PERSON>, ar prival<PERSON>.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON>ą informaciją reikia patei<PERSON>i, p<PERSON><PERSON><PERSON><PERSON> trumpus apraš<PERSON>us ar instruk<PERSON>, kad da<PERSON>viai gal<PERSON> atsakyti, ir <PERSON><PERSON><PERSON><PERSON>te, ar k<PERSON><PERSON><PERSON> laukelis yra neprival<PERSON>, ar prival<PERSON><PERSON>.", "app.containers.AdminPage.ProjectIdeaForm.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON><PERSON> la<PERSON> b<PERSON> užpildytas.", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Išgelbėtas!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automatiniai el. laiškai", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Galite sukonfigūruoti el<PERSON>, paleidžiamus etapo lygmeniu", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Datos", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Atlikite apklausą", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Ar tikrai norite ištrinti šį etapą?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.disabledProjectPhaseEmailMessage": "Šiuo metu ši parinktis išjungta visiems {automatedEmailsLink} puslapyje esantiems projektams. Todėl šiame etape negalėsite atskirai perjungti šio nustatymo.", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Redagavimo etapas", "app.containers.AdminPage.ProjectTimeline.endDate": "Galutinė data", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Pabaigos data", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne daugiau kaip 50 MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Naujas etapas", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Šio etapo pabaigos data iš anksto nenustatyta.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Kai kurių metodų rezultatų da<PERSON>sis (pvz., balsavimo rezultatų) nebus aktyvuotas, kol nebus pasirinkta pabaigos data.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "Kai tik po šio etapo pridėsite kitą etapą, prie šio etapo bus pridėta pabaigos data.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "<PERSON>, kad <PERSON><PERSON><PERSON><PERSON> da<PERSON>:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Įvyko klaida patei<PERSON>ant formą, pabandykite dar kartą.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Išgelbėtas!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.ProjectTimeline.startDate": "Pradžios data", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Pradžios data", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "<PERSON>p<PERSON><PERSON><PERSON> pava<PERSON>", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Faz<PERSON><PERSON> p<PERSON>", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Įkelti priedus", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_perskirstymas", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologija (pagrindinio puslapio filtras)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "<PERSON><PERSON> t<PERSON> būti vadinamos žymos titulinio puslapio filtre? Pvz., <PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON><PERSON>, skyriai, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "<PERSON><PERSON><PERSON> būti su<PERSON> {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "čia", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "<PERSON>ien<PERSON> ž<PERSON> term<PERSON> (vienaskaita)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Ke<PERSON>ų <PERSON> (daugiskaitos) terminas", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Pridėti naują registracijos lauką", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "<PERSON><PERSON><PERSON><PERSON> parinktį", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Atsakymo formatas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Pateikite atsakymo formatą", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Atsakymo variantas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Pateikite atsakymo variantą visomis kal<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Išsaugoti atsakymo parinktį", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Atsakymo variantas sėkmingai išsaugotas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Atsakymų variantai", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Laukai", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Vilkite ir n<PERSON>, kad n<PERSON><PERSON>, kokia tvarka jie bus rodomi registracijos formoje.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "<PERSON><PERSON><PERSON><PERSON><PERSON> sritis", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, rod<PERSON><PERSON> po lauko pavadinimu registracijos formoje.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Gyvenamosios vietos atsakymų variantus galima nustatyti svetainėje {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Redaguoti atsakymo parinktį", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Pateikite visų kalbų lauko pavadinimą", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON> langelis)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Data", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Ilgas atsakymas", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "<PERSON><PERSON> (pasirinkite kelis)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Skaitmeninė vertė", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "<PERSON><PERSON> (pasirinkite vieną)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Ar b<PERSON><PERSON> į šį lauką?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Pasirinktiniai laukai", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON><PERSON><PERSON> parinktį", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Ar tikrai norite ištrinti šią registracijos klausimo atsa<PERSON> parinktį? Visi įrašai, į kuriuos konkretūs naudotojai atsakė naudodami šią parinktį, bus visam laikui ištrinti. Šio veiksmo atšaukti neįmanoma.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Ar tikrai norite ištrinti šį registracijos klausimą? Visi naudotojų atsakymai į šį klausimą bus visam laikui ištrinti, ir jis nebebus užduodamas projektuose ar pasi<PERSON>. Šio veiksmo atšaukti negalima.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Išsaugoti la<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON><PERSON> sėk<PERSON>", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Du stulpel<PERSON>i", "app.containers.AdminPage.SettingsPage.addAreaButton": "Pridėti geografinę sritį", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Gyvūnas - pvz., <PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Vartotojas - pvz., Vartotojas 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "<PERSON><PERSON><PERSON><PERSON>, kurie administratoriai gaus pranešimus apie projektų patvirtinimą. Aplankų valdytojai pagal numatytuosius nustatymus yra visų jų aplankuose esančių projektų tvirtintojai.", "app.containers.AdminPage.SettingsPage.approvalSave": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.approvalTitle": "Projekto pat<PERSON><PERSON> n<PERSON>", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Ar tikrai norite ištrinti šią sritį?", "app.containers.AdminPage.SettingsPage.areaTerm": "Vienos srities terminas (vienaskaita)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "sritis", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "<PERSON><PERSON><PERSON>, <PERSON>iš<PERSON><PERSON><PERSON> keli<PERSON> srit<PERSON> (daugiskaita)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "sritys", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Pasirinkite bent vieną kalbą.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "<PERSON><PERSON><PERSON> avataru<PERSON>", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Neregistruotiems lankytojams rodyti dalyvių profilio nuotraukas ir j<PERSON> skaičių", "app.containers.AdminPage.SettingsPage.bannerHeader": "An<PERSON>š<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Neregistruotiems lankytojams skirtas antraštės tekstas", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Neužsiregistravusiems lankytojams skirtas paantraštės tekstas", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Po<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Reklaminio skydelio tekstas", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.brandingDescription": "Pridėkite logotipą ir nustatykite platformos spalvas.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Platformos prekės ž<PERSON>k<PERSON>", "app.containers.AdminPage.SettingsPage.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.chooseLayout": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.color_primary": "Pagrindinė spalva", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON><PERSON><PERSON> spalva", "app.containers.AdminPage.SettingsPage.color_text": "Teks<PERSON> spalva", "app.containers.AdminPage.SettingsPage.colorsTitle": "Spalvos", "app.containers.AdminPage.SettingsPage.confirmHeader": "Ar tikrai norite ištrinti šią žymą?", "app.containers.AdminPage.SettingsPage.contentModeration": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.ctaHeader": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> pusla<PERSON> | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Mygtuko te<PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "<PERSON><PERSON>uk<PERSON> nuoroda", "app.containers.AdminPage.SettingsPage.defaultTopic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Taip ištrinsite žymą iš visų esamų pranešimų. Šis pakeitimas bus taikomas visiems projektams.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Pridėkite ir iš<PERSON>nk<PERSON> ž<PERSON>, kurias norite naudoti savo platformoje pranešimams kategorizuoti. Žymas galite pridėti prie konkrečių projektų {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editFormTitle": "Redagavimo sritis", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Redaguoti žymą", "app.containers.AdminPage.SettingsPage.fieldDescription": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "<PERSON><PERSON> a<PERSON> skirtas tik vidiniam bendradarbiavimui ir nerodomas naudo<PERSON>.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Vietovės pava<PERSON>s", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Pateikite visų kalbų srities pavadinimą", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Kiekvienai sričiai pasirinktą pavadinimą galima naudoti kaip registracijos lauko parinktį ir filtruoti projektus pagrindiniame puslapyje.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Išsaugoti žymą", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Pateikite visų kalbų žymų pavadinimus", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Kiekvienai žymai parinktas pavadinimas bus matomas platformos naudotojams.", "app.containers.AdminPage.SettingsPage.fixedRatio": "<PERSON><PERSON><PERSON><PERSON> sant<PERSON>", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Šis banerio tipas geriausiai tinka vaizdams, kurių nereikėtų apkarpyti, pavyzdžiui, vaizdams su tekstu, logotipu ar konkrečiais elementais, kurie yra labai svarbūs jūsų piliečiams. <PERSON> naudotojai prisijungia, šis baneris pakeičiamas vientisu pagrindinės spalvos langeliu. Šią spalvą galite nustatyti bendruosiuose nustatymuose. Daugiau informacijos apie rekomenduojamą vaizdų naudojimą rasite mūsų svetainėje {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "žinių bazė", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Ši reklaminė juosta išsitempia per visą plotį ir sukuria puikų vizualinį efektą. Vaizdas stengsis užimti kuo daugiau vietos, tod<PERSON>l ne visada bus matomas visą laiką. Šį banerį galite derinti su bet kokios spalvos uždengimu. Daugiau informacijos apie rekomenduojamą vaizdo naudojimą rasite mūsų svetainėje {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "žinių bazė", "app.containers.AdminPage.SettingsPage.header": "Pagrindinio puslapio reklaminis skydelis", "app.containers.AdminPage.SettingsPage.headerDescription": "Pritaikykite pagrindinio puslapio reklaminio skydelio vaizdą ir tekstą.", "app.containers.AdminPage.SettingsPage.header_bg": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.helmetDescription": "Administratoriaus nustatymų puslapis", "app.containers.AdminPage.SettingsPage.helmetTitle": "Administratoriaus nustatymų puslapis", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Pridėkite savo turinį į pagrindinio puslapio apačioje esantį pritaikomąjį skyrių.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Pagrindinio pu<PERSON> | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Vaizdo uždengimo spalva", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Vaizdo uždengimo neskaidrumas", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "<PERSON><PERSON><PERSON><PERSON> turinį", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "automatiškai aptikti netinkamą platformoje skelbiamą turinį.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Kai š<PERSON> funkcija įjungta, <PERSON><PERSON><PERSON><PERSON> paskelbta informacija, pasiūlymai ir komentarai bus automatiškai peržiūrimi. <PERSON><PERSON><PERSON><PERSON>i, p<PERSON><PERSON><PERSON><PERSON><PERSON> kaip galimai netinkamo turinio, nebus b<PERSON>kuojami, bet bus p<PERSON><PERSON><PERSON><PERSON><PERSON>, kad juos būtų galima peržiūrėti puslapyje {linkToActivityPage} .", "app.containers.AdminPage.SettingsPage.languages": "Kalbos", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Galite pasirinkti keli<PERSON> ka<PERSON>, kuriomis norite siūlyti savo platformą naudotojams. Turėsite sukurti turinį kiekvienai pasirinktai kalbai.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Veikla", "app.containers.AdminPage.SettingsPage.logo": "Logotipas", "app.containers.AdminPage.SettingsPage.noHeader": "Įkelkite <PERSON><PERSON><PERSON><PERSON><PERSON>s paveikslėlį", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.organizationName": "Miesto ar organizacijos pavadinimas", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Visoms kalboms nurodykite organizacijos pavadinimą arba miestą.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Įjungti perdangą", "app.containers.AdminPage.SettingsPage.phone": "Telefonas", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Platformos konfigūracija", "app.containers.AdminPage.SettingsPage.population": "G<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.populationMinError": "Populiacija turi b<PERSON><PERSON>.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Bendras jūsų teritorijos gyventojų s<PERSON>. Jis naudojamas dalyvavimo lygiui apskaičiuoti. Palikite t<PERSON>, jei <PERSON>.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "<PERSON><PERSON><PERSON> b<PERSON>", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "B<PERSON><PERSON><PERSON><PERSON> įvestį, p<PERSON><PERSON><PERSON><PERSON> ir k<PERSON>, kuri<PERSON><PERSON> yra dažniaus<PERSON>i minimų įžeidžiančių žodžių.", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Šis tekstas rodomas pagrindiniame puslapyje virš projektų.", "app.containers.AdminPage.SettingsPage.projectsSettings": "projekto nustatymai", "app.containers.AdminPage.SettingsPage.projects_header": "Projektų antraštė", "app.containers.AdminPage.SettingsPage.registrationFields": "Registracijos <PERSON>", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Registracijos formos viršuje pateikite trumpą aprašymą.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registracija", "app.containers.AdminPage.SettingsPage.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON>šsaugoti sritį", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "<PERSON><PERSON><PERSON>, bandykite vėliau.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Sėkmė!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Pasirinkite tvirtintojus", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Pasirinkite sritis, kurios bus rodomos naudotojams po registracijos", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Pasirinkite temas, kurios bus rodomos naudotojams po registracijos", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Nepavyko išsaugoti. Pabandykite dar kartą pakeisti nustatymą.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Užsiregistruoti\"", "app.containers.AdminPage.SettingsPage.signed_in": "Mygtukas registruotiems lankytojams", "app.containers.AdminPage.SettingsPage.signed_out": "<PERSON><PERSON><PERSON>s neregistruotiems lankytojams", "app.containers.AdminPage.SettingsPage.signupFormText": "Registracijos pagalbinis tekstas", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Registracijos formos viršuje pridėkite trumpą aprašymą.", "app.containers.AdminPage.SettingsPage.statuses": "Statutai", "app.containers.AdminPage.SettingsPage.step1": "El. pa<PERSON><PERSON> ir <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Tai rodoma pirmojo puslapio viršuje esančioje registracijos formoje (vardas, el. pa<PERSON><PERSON> ad<PERSON>, slap<PERSON>žodis).", "app.containers.AdminPage.SettingsPage.step2": "Registracijos klausimų žingsnis", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Tai rodoma registracijos formos antrojo puslapio virš<PERSON> (papildomi registracijos laukai).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Apibrėžkite geografines sritis, kurias norite naudoti savo platformai, pavyzd<PERSON><PERSON>, r<PERSON><PERSON>us, apylinkes ar apygardas. Šias geografines sritis galite susieti su projektais (filtruojama pradiniame puslapyje) arba paprašyti dalyvių pasirinkti savo gyvenamąją vietovę kaip registracijos lauką, kad būtų galima sukurti išmaniąsias grupes ir nustatyti prieigos teises.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON> mat<PERSON> jū<PERSON>ų organizacijos pavadinimą, pasirinkite platformos kalbas ir nuorodą į savo svetainę.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Pateikta antraštė virš<PERSON> did<PERSON> leistiną simbolių limitą (90 simbolių).", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Nurodykite, kokią informaciją žmonių prašoma pateikti registruojantis.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologija", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Nustatymai sėkmingai at<PERSON>ujinti.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "Prek<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Įvesties būsenos", "app.containers.AdminPage.SettingsPage.tabPolicies": "Taisykl<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Projekto pat<PERSON>", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON> būsen<PERSON>", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registracija", "app.containers.AdminPage.SettingsPage.tabSettings": "Bendra", "app.containers.AdminPage.SettingsPage.tabTopics2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabWidgets": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tablet": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON>", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kokį geografinį vienetą norėtumėte naudoti savo projektams (pvz., rajona<PERSON>, a<PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON>, rajonai ir pan.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geografinė<PERSON> s<PERSON>s", "app.containers.AdminPage.SettingsPage.titleBasic": "<PERSON><PERSON><PERSON> n<PERSON>", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Pateiktas pavadinimas virš<PERSON> did<PERSON>ą leistiną simbolių limitą (35 simboliai).", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Žymių tvarkyklė", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "<PERSON><PERSON> baneri<PERSON> y<PERSON><PERSON> naudingas naudo<PERSON> p<PERSON><PERSON>, k<PERSON><PERSON> <PERSON> dera su antra<PERSON>ė<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar mygtuko tekstu. Šie elementai bus nustumti po baneriu. Daugiau informacijos apie rekomenduojamą vaizdų naudojimą rasite mūsų svetainėje {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "žinių bazė", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.urlError": "URL adresas negalioja", "app.containers.AdminPage.SettingsPage.urlPatternError": "Įveskite galiojantį URL adresą.", "app.containers.AdminPage.SettingsPage.urlTitle": "<PERSON>o s<PERSON>", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Galite pridėti nuorodą į savo svetainę. Ši nuoroda bus naudojama pagrindinio puslapio a<PERSON>čioje.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Pasirinkite, kaip platformoje bus rodomi vardo savo profilyje neturintys naudotojai. Tai įvyks, kai nustatysite fazės prieigos teises į \"El. pašto patvirtinimas\". Visais atvejais dalyvavę platformoje naudotojai galės atnaujinti profilio vard<PERSON>, kurį jiems sugeneravome automatiškai.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "<PERSON>udo<PERSON><PERSON> vardo rod<PERSON> (tik na<PERSON><PERSON><PERSON><PERSON>, turintiems patvirtintą el. pašto ad<PERSON>)", "app.containers.AdminPage.SideBar.administrator": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.communityPlatform": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.community_monitor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.customerPortal": "Klientų portalas", "app.containers.AdminPage.SideBar.dashboard": "Prietaisų skydelis", "app.containers.AdminPage.SideBar.emails": "Elektroniniai laiškai", "app.containers.AdminPage.SideBar.folderManager": "Aplankų tvarkyklė", "app.containers.AdminPage.SideBar.groups": "Grup<PERSON><PERSON>", "app.containers.AdminPage.SideBar.guide": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "Įvesties tvarkyklė", "app.containers.AdminPage.SideBar.insights": "Pranešimų teikimas", "app.containers.AdminPage.SideBar.inspirationHub": "Įkvėpimo centras", "app.containers.AdminPage.SideBar.knowledgeBase": "Žinių bazė", "app.containers.AdminPage.SideBar.language": "Kalba", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Puslapia<PERSON> ir meniu", "app.containers.AdminPage.SideBar.messaging": "Žinučių siuntimas", "app.containers.AdminPage.SideBar.moderation": "Veikla", "app.containers.AdminPage.SideBar.notifications": "Pranešimai", "app.containers.AdminPage.SideBar.processing": "Apdor<PERSON>jima<PERSON>", "app.containers.AdminPage.SideBar.projectManager": "Projektų vadovas", "app.containers.AdminPage.SideBar.projects": "Projektai", "app.containers.AdminPage.SideBar.settings": "Nustatymai", "app.containers.AdminPage.SideBar.signOut": "<PERSON>si<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.support": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.toPlatform": "Į platformą", "app.containers.AdminPage.SideBar.tools": "Įrankiai", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON> profilis", "app.containers.AdminPage.SideBar.users": "Vartotojai", "app.containers.AdminPage.SideBar.workshops": "Seminarai", "app.containers.AdminPage.Topics.addTopics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.browseTopics": "Naršyti žymas", "app.containers.AdminPage.Topics.cancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.confirmHeader": "Ar tikrai norite ištrinti šią projekto žymą?", "app.containers.AdminPage.Topics.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.deleteTopicLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Šios žymos nebebus galima pridėti prie naujų šio projekto pranešimų.", "app.containers.AdminPage.Topics.inputForm": "Įvesties forma", "app.containers.AdminPage.Topics.lastTopicWarning": "<PERSON><PERSON><PERSON> bent viena žyma. <PERSON><PERSON> nenorite naudoti <PERSON>, jas galima išjungti skirtuke {ideaFormLink} .", "app.containers.AdminPage.Topics.projectTopicsDescription": "<PERSON><PERSON><PERSON> p<PERSON> ir <PERSON><PERSON>, kurias galima priskirti šio projekto pranešimams.", "app.containers.AdminPage.Topics.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Topics.title": "<PERSON>je<PERSON><PERSON>", "app.containers.AdminPage.Topics.topicManager": "Žymių tvarkyklė", "app.containers.AdminPage.Topics.topicManagerInfo": "Jei norite pridėti papildomų projekto žymų, galite tai padaryti {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Pridėti naują grupę", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Grup<PERSON><PERSON> p<PERSON>", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Pateikite grupės p<PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Sukurti rankinę grupę", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Kokio tipo grupės jums reikia?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Išsaugoti grupę", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Sukurti rankinę grupę", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Sukurti išmaniąją grupę", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Sužinokite daugiau apie grupes", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Galite pasirinkti naudotojus iš a<PERSON> ir įtraukti juos į šią grupę.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "<PERSON><PERSON><PERSON><PERSON>, o jas atitinkantys naudotojai automatiškai įtraukiami į šią grupę.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Rankinio valdymo grupė", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Išmanioji grupė", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Šioje grupėje dar nėra nė vieno nario", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Eikite į {allUsersLink} ir rankiniu būdu pridėkite keletą naudotojų.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Jūsų paiešką atitinkančių naudotojų nėra", "app.containers.AdminPage.Users.GroupsPanel.select": "Pasirinkite", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Eksportuoti visus", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Grupės naudotojų eksportavimas", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Pasirinktas eksportas", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Ar tikrai norite ištrinti šią grupę?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Pridedant naudotojus į grupes įvyko klaida, bandykite dar kartą.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> g<PERSON>", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "<PERSON><PERSON><PERSON>nti pasirinktus šios grupės naudotojus?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Šalinant naudotojus i<PERSON> g<PERSON> įvyko klaida, bandykite dar kartą.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Naudotojų įtraukimas į grupę", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.add": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addAnswer": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.addQuestion": "Pridėti demografinių klausimų", "app.containers.AdminPage.groups.permissions.answerChoices": "Atsakymų variantai", "app.containers.AdminPage.groups.permissions.answerFormat": "Atsakymo formatas", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "<PERSON><PERSON> b<PERSON><PERSON> bent vienas pasirin<PERSON>as", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Šis naudotojas vadovauja aplank<PERSON>, k<PERSON><PERSON> yra šis projektas. Norėdami panaikinti šio naudotojo teises moderuoti šį projektą, galite panaikinti jo teises į aplanką arba perkelti projektą į kitą aplanką.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Sukurti naują k<PERSON>", "app.containers.AdminPage.groups.permissions.createAQuestion": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.groups.permissions.defaultField": "<PERSON><PERSON><PERSON><PERSON><PERSON> sritis", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Nurodykite visų pasirinkimų pavadinimus", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON> langelis)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Data", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Ilgas atsakymas", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "<PERSON><PERSON> (pasirinkite kelis)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Skaitmeninė vertė", "app.containers.AdminPage.groups.permissions.fieldType_select": "<PERSON><PERSON> (pasirinkite vieną)", "app.containers.AdminPage.groups.permissions.fieldType_text": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Granuliariųjų leidimų keitimas nėra licencijos dalis. Norėdami apie tai su<PERSON> da<PERSON>, kreipkitės į savo \"GovSuccess\" vadybininką.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Ar tikrai norite pašalinti šią grupę iš projekto?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Pasirinkite vieną ar daugiau grupių", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {nari<PERSON> nėra} one {1 narys} other {{count} nariai}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Užpildykite pavadinimą visomis kal<PERSON>", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "<PERSON>r t<PERSON>rai?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Projektų vadovai nerasti", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "<PERSON><PERSON><PERSON>, nes šiame projekte naudotojas negali atlikti jokių veiksmų.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Tik administratoriai gali sukurti naują klausimą.", "app.containers.AdminPage.groups.permissions.option1": "1 variantas", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Laukiama kvietimo", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Kas gali komentuoti dokumentą?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Kas gali užsiregistruoti į renginį?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Kas gali komentuoti įvesties duomenis?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Kas gali teikti pastabas d<PERSON>l p<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Kas gali pateikti p<PERSON>ūlymą?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Kas gali reaguoti į įvestis?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Kas gali pateikti duomenis?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Kas gali daly<PERSON> a<PERSON>kla<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Kas gali daly<PERSON> a<PERSON>kla<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Kas gali sa<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Kas gali balsuoti d<PERSON> p<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Kas gali balsuoti?", "app.containers.AdminPage.groups.permissions.questionDescription": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "<PERSON><PERSON><PERSON>, bandykite vėliau.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Sėkmė!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Pakeitimai buvo išsaugoti.", "app.containers.AdminPage.groups.permissions.select": "Pasirinkite", "app.containers.AdminPage.groups.permissions.selectValueError": "Pasirinkite atsakymo tipą", "app.containers.AdminPage.new.createAProject": "Sukurti projektą", "app.containers.AdminPage.new.fromScratch": "Nuo nulio", "app.containers.AdminPage.phase.methodPicker.addOn1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Įžvalgos, paremtos dirbtiniu intelektu", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Padėkite dalyviams išryškinti sutikimą ir nesutikimą po vieną idėją.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Raskite bendrą pagrindą", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Įterpkite interaktyvų PDF failą ir rinkite komentarus bei atsiliepimus naudo<PERSON>i \"Konveio\".", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "<PERSON><PERSON><PERSON> atsiliepimus apie dokument<PERSON>", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Įterpti trečiosios šalies apkla<PERSON>ą", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "<PERSON>š<PERSON><PERSON><PERSON> apklausa", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Pasinaudokite kolektyvine naudotojų informacija. Pakvieskite juos pateikti, aptarti idėjas ir (arba) pateikti atsiliepimus viešame forume.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Rinkti informaciją ir atsiliepimus v<PERSON>", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Dalytis informacija", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Trūksta platformos įžvalgų, paremtų dirbtiniu intelektu", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Trūksta platformoje teikiamų ataskaitų, duomenų vizualizavimo ir apdorojimo", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Sąsaja su platformoje es<PERSON>č<PERSON> ataskaitų kūrimo įrankiu", "app.containers.AdminPage.phase.methodPicker.logic1": "Logika", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Platus klausimų tipų pasirinkimas", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Leiskite dalyviams įkelti idė<PERSON>, nustatydami laiko ir balsavimo limit<PERSON>.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, peticijos ar iniciatyvos", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Sukurkite trumpą klausimyną su keliais atsakymų variantais.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Pateikite informaciją naudoto<PERSON>, vizualizuokite kituose etapuose gautus rezultatus ir kurkite daug duomenų turinčias ataskaitas.", "app.containers.AdminPage.phase.methodPicker.survey1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Supraskite savo naudotojų poreikius ir mąstymą naudodamiesi įvairiais privačių klausimų tipais.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "<PERSON>p<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Su<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Paprašykite naudotojų savanoriškai dalyvauti veikloje ir renginiuose arba surasti dalyvių diskusijai.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Įdarbinti dalyvius arba savanorius", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Pasirinkite balsavimo būdą ir paprašykite naudotojų nustatyti prioritetus tarp kelių skirtingų parinkčių.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Atlikti balsavimą arba nustatyti prioritetus", "app.containers.AdminPage.projects.all.all": "Visi", "app.containers.AdminPage.projects.all.createProjectFolder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.all.existingProjects": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.AdminPage.projects.all.homepageWarning": "Pritaikykite pagrindinio puslapio rodinį: administratoriai čia gali sudėlioti projektus ir aplankus ir nustatyti tvarką pagrindiniame puslapyje.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Čia bus rodomi projektai, kuriuose esate projekto vadovas.", "app.containers.AdminPage.projects.all.noProjects": "Projektų nerasta.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Projektų aplankus gali kurti tik administratoriai.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projektai ir a<PERSON>lankai", "app.containers.AdminPage.projects.all.publishedTab": "Paskelbta", "app.containers.AdminPage.projects.all.searchProjects": "Projektų paieška", "app.containers.AdminPage.projects.all.yourProjects": "Jūsų projektai", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI analizė", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Tikslumas: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Klauskite", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON> duo<PERSON>, galite už<PERSON><PERSON>i jiems aktualius klausimus. Ši funkcija neįtraukta į dabartinį planą. Norėdami j<PERSON> atrak<PERSON>, pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Užduoti k<PERSON>imą", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Ši įžvalga apima š<PERSON> klausimus:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Ar tikrai norite ištrinti šį klausimą?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Ištrinti sa<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Ar tikrai norite i<PERSON> sa<PERSON>?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Čia bus rodomos jū<PERSON>ų teksto sa<PERSON>uk<PERSON>, tačiau šiuo metu jų dar neturite.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "<PERSON><PERSON><PERSON><PERSON>, spustelėkite viršuje esantį mygtuką Automatinis apibendrinimas.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "pasirinkti įėjimai", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "<PERSON><PERSON><PERSON><PERSON>nt klausimus apie mažiau įvesties duomenų, pasi<PERSON><PERSON><PERSON> didesnis tik<PERSON>. Sumažinkite dabartinį įvesties pasirinkimą naudodam<PERSON>, pai<PERSON><PERSON><PERSON> ar demografinius filtrus.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Klausimas visiems įvesties duomenims", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Įvertinkite šios įžvalgos kokybę", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Atkurti filtrus", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Apibendrinkite", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Visų įvesties duomenų santrauka", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Dėkojame už atsiliepimus", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "Dirbtinis intelektas negali apdoroti tiek daug įvesties duomenų vienu metu. Suskirstykite juos į mažesnes grupes.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Vienu metu galite apibendrinti ne daugiau kaip 30 įvesties duomenų pagal dabartinį planą. Pasitarkite su \"GovSuccess\" vadybininku arba administratoriumi ir sužinokite daugiau.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "<PERSON><PERSON> supra<PERSON>", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Mūsų platformoje galite nagrinėti pagrindines temas, apibendrinti duomenis ir analizuoti įvairias perspektyvas. Jei ieškote konkrečių atsakymų ar įžvalgų, pasinaudokite funkcija \"Užduoti klausimą\", kad gal<PERSON> gilinti<PERSON> ne tik į santrauką.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Nors tai pasitai<PERSON> retai, kartais dirbtinis intelektas gali generuoti informaciją, kurios aiškiai nebuvo pradiniame duomenų rinkinyje.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Haliucinacijos:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "Dirbtinis intelektas gali labiau pabrėžti tam tikras temas ar idėjas nei kitas ir taip iškreipti bendrą interpretaciją.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Persistengimas:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Mūsų sistema optimizuota 20-200 gerai apibrėžtų įvesties duomenų, kad rezultatai būtų tiksliausi. Kai duomenų apimtis viršija šį intervalą, santrauka gali tapti labiau aukšto lygio ir apibendrinta. <PERSON>, kad dirbtinis intelektas taps \"mažiau tikslus\", veikiau jis sutelks dėmesį į platesnes tendencijas ir dėsningumus. Norėdami gauti daugiau niuansų turinčių įžvalgų, rekomendu<PERSON><PERSON> naudoti (automatinio) <PERSON><PERSON><PERSON>jimo funkciją, kad didesnius duomenų rinkinius suskirstytumėte į mažesnius, lengviau valdomus poaibius.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Duomenų kiekis ir tikslumas:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Rekomenduojame naudoti dirbtinio intelekto generuojamas santraukas kaip pradinį tašką norint suprasti didelius duomenų rinkini<PERSON>, bet ne kaip galutinį žodį.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "<PERSON><PERSON> dirbti su dirbtiniu intelektu", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Pasirinktų įvesties duomenų įtraukimas į žymą", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Ši funkcija neįtraukta į dabartinį planą. Norėdami j<PERSON> atrak<PERSON>, pasitarkite su savo Vyriausybės sėkmės vadybininku arba administratoriumi.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Visi įvesties duomenys", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Visi įvesties duomenys", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, aš tai padarysiu", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Ar norite automatiškai priskirti įvestis žymai?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Yra <b>įvairių būdų</b> automatiškai priskirti įvestis žymėms.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "<PERSON><PERSON><PERSON><PERSON> <b>auto<PERSON><PERSON><PERSON></b> paleiskite pageidaujamą metodą.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Spustelėkite žymę, kad ją priskirtumėte šiuo metu pasirinktai įvesties daliai.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "<PERSON><PERSON>, auto<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automat<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Automatines žymas automatiškai nustato kompiuteris. Jas bet kada galite pakeisti arba pašalinti.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automat<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Įvestys, jau susietos su šiomis žymėmis, nebus klasifikuojamos i<PERSON>ujo.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Klasifikuojama tik pagal žym<PERSON> p<PERSON>. Norėdami gauti g<PERSON> rezultatus, <PERSON><PERSON><PERSON><PERSON> tinka<PERSON> r<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Žymos: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "<PERSON><PERSON><PERSON> sukuriate žymas ir rankiniu būdu priskiriate keletą įėjimų, o kompiuteris priskiria likusius.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Žymos: <PERSON><PERSON> pavyzdį", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Panašiai kaip \"<PERSON><PERSON><PERSON>: pagal et<PERSON>\", tač<PERSON><PERSON> tik<PERSON> yra <PERSON>, nes sistema mokoma naudojant gerus pavyz<PERSON><PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "<PERSON><PERSON><PERSON>, kom<PERSON><PERSON><PERSON> p<PERSON> įvestis", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Žymos: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "<PERSON>, kai turite iš anksto nustatytą žymių rinkinį arba kai jūsų projekto žymių apimtis yra ribota.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "<PERSON><PERSON><PERSON><PERSON> įvesties duomenis, kurių nepatinka ir patinka sant<PERSON> yra dideli<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Prieštaringa<PERSON> vertina<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "<PERSON>š<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Ar tikrai norite ištrinti šią žymą?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Daugiau to nerodykite", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Redaguoti žymą", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Pasirinkite ne daugiau kaip 9 žymes, tarp kurių norite paskirstyti įvestis.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Klasifikuojama pagal šiuo metu žymėms priskirtas įvestis. Kompiuteris bandys sekti jūsų pavyzdžiu.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Žymos: <PERSON><PERSON> pavyzdį", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Dar neturite jokių pasirinktinių žymų.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Kompiuteris automatiškai aptinka žymas ir priskiria jas jūsų įvestims.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Žymos: Visiškai automatizuotas", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "G<PERSON>i ve<PERSON>, kai jūsų projektai apima daugybę žymių. Gera pradžia.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "<PERSON><PERSON> nor<PERSON>?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Įvestys be žymų", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "nustatyti kiekvienos įvesties kalbą", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "Kalba", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Nėra aktyviųjų filtrų", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "<PERSON><PERSON><PERSON><PERSON>, kad su<PERSON>te ir filtruotumėte įvesties duomenis ir galėtumėte parengti tikslesnes arba tikslingesnes santraukas.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Žymos: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Priskirkite esamas <PERSON>, kurias autorius pasirin<PERSON> s<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Pervadinti žymą", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Pavadinimas", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Pervadinti žymą", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Pasirinkite visus", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "priskirti teigiamą arba neigiamą nuotaiką kiekvienai įvesties informacijai, gaut<PERSON> iš teks<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Nuotaikos", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Naudokite dabar<PERSON>ius filtrus", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "<PERSON><PERSON><PERSON> įvestis norite pa<PERSON>?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Automatinio žymėjimo užduotis", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Prieštaringa<PERSON> vertina<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Baigėsi ties", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Nepavyko", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Pagal pavyzdį", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "Vykdoma", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "<PERSON><PERSON> et<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "Kalba", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP žyma", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Neseniai atliktų dirbtinio intelekto užduočių nėra", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Nuotaikos", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Pradėta nuo", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Perėmė", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Apibendr<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Suveikia, kai", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Visi", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autorius", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Žemia<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Nuolatinė gyvenamoji vieta", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filtrai", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Lyt<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Įvestis", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON>ment<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "<PERSON>ak<PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "<PERSON><PERSON><PERSON> skaič<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Į", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Pridėti prie analizės", "app.containers.AdminPage.projects.project.analysis.anonymous": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Autoriai pagal amžių", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Autoriai pagal gyvenamąją vietą", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Pagrindiniai darbai", "app.containers.AdminPage.projects.project.analysis.comments": "Komentarai", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Gyvenamosios vietos schema yra per <PERSON>, kad j<PERSON> būt<PERSON> galima rod<PERSON>i", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Paslėpti tu<PERSON><PERSON> atsa<PERSON>", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Atsakymai", "app.containers.AdminPage.projects.project.analysis.end": "Pabaiga", "app.containers.AdminPage.projects.project.analysis.filter": "Rodyt<PERSON> tik įvestis su šia reikšme", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Paslėpti atsakymus be atsakymo", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Automatinės įžvalgos", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON><PERSON> derinio atvejų yra {count} .", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Nemėgsta", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Naršykite", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Įėjimai", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Kitas šil<PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Kita įžvalga", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Tai nėra statistiškai reikšminga įžvalga.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "<PERSON><PERSON><PERSON><PERSON>, kuriuose dalyvauja mažiau nei 30 dalyvių, automatinės įžvalgos neteikiamos.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Dalyviai", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Ankstesnis <PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Ankstesnė įžvalga", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Eilučių reikšmės", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistiškai reikšminga įžvalga.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Apibendrinkite", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Vienetai", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visas įžvalgas", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> automatines įžvalgas", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Įvestys be žymų", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Buvo įkelta negaliojanti shapefile ir jos negalima rodyti.", "app.containers.AdminPage.projects.project.analysis.limit": "Apribojimas", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "<PERSON>g<PERSON><PERSON><PERSON> klaus<PERSON>", "app.containers.AdminPage.projects.project.analysis.manageInput": "<PERSON><PERSON><PERSON><PERSON> įvesties duomenis", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Kit<PERSON> gra<PERSON>", "app.containers.AdminPage.projects.project.analysis.noAnswer": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "<PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "Nėra įkeltos shapefile.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Dabartinių filtrų neatitinka jokie įėjimai", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Ankstesnė diagrama", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.removeFilter": "<PERSON><PERSON><PERSON><PERSON> filtrą", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "<PERSON><PERSON><PERSON><PERSON> filtrą", "app.containers.AdminPage.projects.project.analysis.search": "Pa<PERSON>š<PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shape failai čia rodomi GeoJSON formatu. Todėl originalaus failo stilius gali būti rod<PERSON> net<PERSON>.", "app.containers.AdminPage.projects.project.analysis.start": "Pradžia", "app.containers.AdminPage.projects.project.analysis.supportArticle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus klausimus", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pasirinktus klausimus", "app.containers.AdminPage.projects.project.analysis.votes": "Balsai", "app.containers.AdminPage.widgets.copied": "Nukopijuota į iškarpinę", "app.containers.AdminPage.widgets.copyToClipboard": "Nukopijuokite šį kodą", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Nukopijuokite HTML kodą", "app.containers.AdminPage.widgets.fieldAccentColor": "Akcento spalva", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Valdiklio fono spalva", "app.containers.AdminPage.widgets.fieldButtonText": "Mygtuko te<PERSON>", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Prisijunkite dabar", "app.containers.AdminPage.widgets.fieldFont": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldFontDescription": "Tai turi būti es<PERSON>s š<PERSON>o pavadinimas iš {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "<PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "G<PERSON>te pareikšti savo nuomonę", "app.containers.AdminPage.widgets.fieldHeaderText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Mūsų dalyvavimo platforma", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.containers.AdminPage.widgets.fieldProjects": "Projektai", "app.containers.AdminPage.widgets.fieldRelativeLink": "Nuorodos į", "app.containers.AdminPage.widgets.fieldShowFooter": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldShowHeader": "<PERSON><PERSON><PERSON>š<PERSON>ę", "app.containers.AdminPage.widgets.fieldShowLogo": "Rodyti logotipą", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> fono spalva", "app.containers.AdminPage.widgets.fieldSort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pagal", "app.containers.AdminPage.widgets.fieldTextColor": "Teks<PERSON> spalva", "app.containers.AdminPage.widgets.fieldTopics": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Pradžia", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Galite nukopijuoti šį HTML kodą ir įklijuoti jį toje svet<PERSON>, į kurią norite įtraukti valdiklį.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Valdiklio HTML kodas", "app.containers.AdminPage.widgets.previewTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.settingsTitle": "Nustatymai", "app.containers.AdminPage.widgets.sortNewest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortPopular": "Populiaru<PERSON>", "app.containers.AdminPage.widgets.sortTrending": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.subtitleWidgets": "<PERSON><PERSON><PERSON> sukurti valdiklį, jį pritaikyti ir pridėti prie savo svet<PERSON>, kad prit<PERSON>uk<PERSON><PERSON>te žmones į šią platformą.", "app.containers.AdminPage.widgets.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleDimensions": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir pora<PERSON>", "app.containers.AdminPage.widgets.titleInputSelection": "Įvesties pasirinkimas", "app.containers.AdminPage.widgets.titleStyle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleWidgets": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.Save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.PageTitle": "Pradžia", "app.containers.ContentBuilder.homepage.SaveError": "<PERSON><PERSON><PERSON><PERSON><PERSON> pagrindinį puslapį ka<PERSON>kas nutiko ne taip.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "Du stulpel<PERSON>i", "app.containers.ContentBuilder.homepage.bannerImage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerSubtext": "Re<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerText": "Reklaminio skydelio tekstas", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Į dabartinę licenciją neįtrauktas kitų nustatymų, iš<PERSON>rus pagrindinio puslapio reklaminio skydelio vaizdą ir tekstą, pritaikymas. Susisiekite su savo \"GovSuccess\" vadybininku ir sužinokite apie tai daugiau.", "app.containers.ContentBuilder.homepage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Mygtuko te<PERSON>", "app.containers.ContentBuilder.homepage.customized_button_url_label": "<PERSON><PERSON>uk<PERSON> nuoroda", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Rodo 3 artimiausius įvykius jūsų platformoje.", "app.containers.ContentBuilder.homepage.eventsDescription": "Rodo 3 artimiausius įvykius jūsų platformoje.", "app.containers.ContentBuilder.homepage.fixedRatio": "<PERSON><PERSON><PERSON><PERSON> sant<PERSON> reklaminis skydel<PERSON>", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Šis banerio tipas geriausiai tinka vaizdams, kurių nereikėtų apkarpyti, pavyzdžiui, vaizdams su tekstu, logotipu ar konkrečiais elementais, kurie yra labai svarbūs jūsų piliečiams. <PERSON> naudotojai prisijungia, šis baneris pakeičiamas vientisu pagrindinės spalvos langeliu. Šią spalvą galite nustatyti bendruosiuose nustatymuose. Daugiau informacijos apie rekomenduojamą vaizdų naudojimą rasite mūsų svetainėje {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "žinių bazė", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "<PERSON><PERSON>o p<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Ši reklaminė juosta išsitempia per visą plotį ir sukuria puikų vizualinį efektą. Vaizdas stengsis užimti kuo daugiau vietos, tod<PERSON>l ne visada bus matomas visą laiką. Šį banerį galite derinti su bet kokios spalvos uždengimu. Daugiau informacijos apie rekomenduojamą vaizdo naudojimą rasite mūsų svetainėje {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "žinių bazė", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Vaizdo uždengimo spalva", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Vaizdo uždengimo neskaidrumas", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Netinkamas URL adresas", "app.containers.ContentBuilder.homepage.no_button": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "<PERSON>eregis<PERSON><PERSON><PERSON> na<PERSON>", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Įjungti perdangą", "app.containers.ContentBuilder.homepage.projectsDescription": "Jei norite konfigūruoti projektų rodymo tvarką, pakeiskite jų eiliškumą svetainėje {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Projektų puslapis", "app.containers.ContentBuilder.homepage.registeredUsersView": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.showAvatars": "<PERSON><PERSON><PERSON> avataru<PERSON>", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Neregistruotiems lankytojams rodyti dalyvių profilio nuotraukas ir j<PERSON> skaičių", "app.containers.ContentBuilder.homepage.sign_up_button": "Užsiregistruokite", "app.containers.ContentBuilder.homepage.signedInDescription": "Taip reklaminį skydelį mato registruoti naudo<PERSON>i.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Taip reklaminį skydelį mato platformoje neužsiregistravę lankytojai.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "<PERSON><PERSON> baneri<PERSON> y<PERSON><PERSON> naudingas naudo<PERSON> p<PERSON><PERSON>, k<PERSON><PERSON> <PERSON> dera su antra<PERSON>ė<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar mygtuko tekstu. Šie elementai bus nustumti po baneriu. Daugiau informacijos apie rekomenduojamą vaizdų naudojimą rasite mūsų svetainėje {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "žinių bazė", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Įterpties aukštis (pikseliai)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON><PERSON>, kurį norite, kad įterptasis turinys būt<PERSON> rod<PERSON> (pikseliais).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Trumpas įterpiamo turinio <PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurie naudo<PERSON>i ekrano skaitytuvu ar kita pagalbine technologija, naudinga pateikti šią informaciją.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> norite įterpti, URL.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Rodyti išorinės svetain<PERSON> turinį savo puslapyje HTML iFrame. Atkreipkite dėmesį, kad ne kiekvieną puslapį galima įterpti. <PERSON><PERSON> kyla <PERSON> įterpiant puslapį, pasitarkite su puslapio savinink<PERSON>, ar jis sukon<PERSON><PERSON><PERSON><PERSON> ta<PERSON>, kad būtų galima įterpti.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Apsilankykite mūsų palaikymo puslapyje", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> turi<PERSON> įterpti. {visitLinkMessage} su<PERSON><PERSON><PERSON> daug<PERSON>.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Įveskite galiojantį ž<PERSON><PERSON><PERSON> ad<PERSON>, pavyzdžiui, https://example.com.", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Įterpti", "app.containers.admin.ContentBuilder.accordionMultiloc": "Akor<PERSON>ona<PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "<PERSON><PERSON><PERSON><PERSON> pagal numa<PERSON><PERSON><PERSON><PERSON> nustatymus", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Tekstas", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Tai išplečiamas akordeono turinys. Jį galite redaguoti ir formatuoti naudodamiesi dešinėje esančiame skydelyje esančiu redaktoriumi.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Pavadinimas", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Akordeono pavadinimas", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.error": "k<PERSON>a", "app.containers.admin.ContentBuilder.errorMessage": "{locale} turi<PERSON><PERSON> įvyko klaida, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>, kad <PERSON>te išsaugoti pakeiti<PERSON>.", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Paslėpti dalyvavimo avatarus", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "<PERSON> kas ketvirtį at<PERSON><PERSON><PERSON>, kuria si<PERSON><PERSON>, ką manote apie valdymą ir vieš<PERSON> pas<PERSON>.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Atlikite apklausą", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Padėkite mums geriau jus aptarnauti", "app.containers.admin.ContentBuilder.homepage.default": "numatytasis", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Rengin<PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Rengin<PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Kvietimas imtis ve<PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "Pagrindinio mygtuko URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Pagrindinio mygtuko te<PERSON>tas", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "<PERSON><PERSON><PERSON> URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Pavadinimas", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Pagrindinio puslapio reklaminis skydelis", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Pagrindinio puslapio reklaminis skydelis", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Paveikslėlių ir teksto k<PERSON>s", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 stulpelis", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projektai", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Įgalinkite pasiūlymus administratoriaus skydelio skiltyje \"Pasiūlymai\", kad jie būtų atrakinti pagrindiniame puslapyje", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageMultiloc": "Vaizdas", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Trumpas v<PERSON>", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "<PERSON><PERSON>, kad jū<PERSON><PERSON> platforma būtų prieinama naudotojams, naudojantiems ekrano s<PERSON>tytuvu<PERSON>, svarbu prie paveikslėlių pridėti \"alt tekstą\".", "app.containers.admin.ContentBuilder.participationBox": "Dalyvavimo <PERSON>", "app.containers.admin.ContentBuilder.textMultiloc": "Tekstas", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 stulpelis", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 stulpelis", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 stulpeliai, kurių plotis atitinkamai 30 % ir 60 %", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 stulpeliai, kurių plotis atitinkamai 60 % ir 30 %", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 lyginiai stulpeliai", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Labiausiai reaguota į įvestis", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Idėjos kūrimo etapas", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "Šio projekto ar etapo įvesties duomenų nėra.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Įėjimų skai<PERSON>ius", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Pavadinimas", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "<PERSON><PERSON> viso s<PERSON>: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Ilgo teksto sulenkimas", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "Šio projekto ar etapo įvesties duomenų nėra.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Pasirinkite etapą", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Autorius", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Turinys", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Balsai", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Įvestis", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Pavadinimas", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registracijos tarifas", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registracijos", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Dalyvių tvarkaraštis", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Atkreipkite dėmesį, kad <PERSON><PERSON><PERSON><PERSON> skaičius gali būti nevisiškai tikslus, nes kai kurie duomenys yra surinkti atliekant išorinę apklausą, kurios mes nese<PERSON>.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Diagrama", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Dat<PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Pavadinimas", "app.containers.admin.ReportBuilder.charts.noData": "Pasirinktų filtrų duomenų nėra.", "app.containers.admin.ReportBuilder.charts.trafficSources": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.users": "Vartotojai", "app.containers.admin.ReportBuilder.charts.usersByAge": "Naudotojai pagal amžių", "app.containers.admin.ReportBuilder.charts.usersByGender": "Naudotojai pagal lytį", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Lankytojų tvarkaraštis", "app.containers.admin.ReportBuilder.managerLabel1": "Projektų vadovas", "app.containers.admin.ReportBuilder.periodLabel1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.projectLabel1": "Projektas", "app.containers.admin.ReportBuilder.quarterReport1": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> ataskaita: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Pradžia", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Įsigykite 1 papildomą vietą", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Patvirtinkite", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Ar tikrai norite 1 asmeniui suteikti vadovo teises?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> teises", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Pasiekėte savo plano vietų limitą, bus pridėta 1 papildoma vieta.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Numatytųj<PERSON> būsenų negalima ištrinti.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Red<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Red<PERSON><PERSON><PERSON> b<PERSON>", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Šiuo metu dalyvio įvestims priskirtų būsenų negalima ištrinti. Statusą iš esamų įvesties duomenų galite pa<PERSON>lint<PERSON> / pakeisti skirtuke {manageTab} .", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Šio statuso negalima ištrinti ar <PERSON>.", "app.containers.admin.ideaStatuses.all.manage": "Red<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Pasirinktinių įvesties būsenų konfigūravimas nėra įtrauktas į dabartinį planą. Pasitarkite su savo vyriausybės sėkmės vadybininku arba administratoriumi, kad jį atrakintumėte.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Tvarkyki<PERSON> bū<PERSON>ą, kuri<PERSON> galima priskirti dalyvio įvesties duomenims projekte. Statusas yra viešai matomas ir padeda informuoti dalyvius.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Tvarkykite būseną, kuri<PERSON> galima priskirti pasiūlymams projekte. Būsena yra viešai matoma ir padeda informuoti dalyvius.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Įvesties būsenų redagavimas", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Redaguoti pasiūlymų būsenas", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Pasirinkta įgyvendinti arba toles<PERSON> ve<PERSON>i", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Pateikti ofici<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Atsakyta", "app.containers.admin.ideaStatuses.form.category": "Kategorija", "app.containers.admin.ideaStatuses.form.categoryDescription": "Pasirinkite geriausiai jūsų statusą atitinkančią kategoriją. Šis pasirinkimas padės mūsų analizės įrankiui tiksliau apdoroti ir analizuoti pranešimus.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Neatitinka nė vienos iš kitų parinkčių", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "Spalva", "app.containers.admin.ideaStatuses.form.fieldDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Pateikite visų kalbų būsenos aprašymą", "app.containers.admin.ideaStatuses.form.fieldTitle": "<PERSON>ū<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Pateikite visų kalbų būsenos pavadinimą", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Sėkmingai įgyvendinta", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Įgyvendinta", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Netinkamas", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Netinkami arba neatrinkti tolesniam darbui", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Nepasirinkta", "app.containers.admin.ideaStatuses.form.saveStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Svarstoma galimybė įgyvendinti arba imtis tolesnių veiksmų", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet dar neap<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Tvarkykite įvestis ir jų būsenas.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Įvesties tvarkytojas | Dalyvavimo platforma {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Pateikite atsiliepimus, pridėkite žymas ir perkelkite įvestį iš vieno projekto į kitą", "app.containers.admin.ideas.all.inputManagerPageTitle": "Įvesties tvarkyklė", "app.containers.admin.ideas.all.tabOverview": "Apžvalga", "app.containers.admin.import.importInputs": "Importo įvestys", "app.containers.admin.import.importNoLongerAvailable3": "Ši funkcija čia nebėra prieinama. Jei norite importuoti įvesties duomenis į idėjų kūrimo etapą, eikite į etapą ir pasirinkite \"Importuoti\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 papildoma administratoriaus vieta} other {# papildomų administratoriaus vietų}} ir {managerSeats, plural, one {1 papildoma vadybininko vieta} other {# papildomų vadybininko vietų}} bus pridėta virš limito.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 papildoma administratoriaus vieta bus pridėta virš limito} other {# papildomų administratoriaus vietų bus pridėta virš limito}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 papildoma vadybininko vieta bus pridėta virš limito} other {# papildomų vadybininko vietų bus pridėta virš limito}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Patvirtinkite ir išsiųskite kvietimus", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Patvirtinkite poveikį sėdynių naudojimui", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Pasiekėte savo plano laisvų vietų limitą.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Šio projekto administratoriai ir vadovai", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Tik administratoriai ir bendradarb<PERSON>i", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Veiksmų gali imtis tik platformos administratoriai, aplankų tvarkytojai ir projektų vadovai.", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "Bet kas", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Daly<PERSON><PERSON> gali visi, įskaitant neregistruotus naudo<PERSON>.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Dalyvauti gali tam tikros (-ų) naudotojų grupės (-ių) naudotojai. Naudotojų grupes galite tvarkyti skirtuke \"Naudotojai\".", "app.containers.admin.project.permissions.viewingRightsTitle": "Kas gali matyti šį projektą?", "app.containers.phaseConfig.enableSimilarInputDetection": "Panašių įvesties duomenų aptikimo įjungimas", "app.containers.phaseConfig.similarInputDetectionTitle": "Panašių įvesties duomenų aptikimas", "app.containers.phaseConfig.similarInputDetectionTooltip": "Parodykite dalyviams panašius įvesties duomenis, kol jie rašo te<PERSON>, kad iš<PERSON>te pasikartojimų.", "app.containers.phaseConfig.similarityThresholdBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kūnas)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Šiuo parametru nust<PERSON>, kiek pana<PERSON> turi būti du apraš<PERSON>ai, kad jie būtų pažymėti kaip panaš<PERSON>. Naudokite reikšmę nuo 0 (griežtas) iki 1 (švelnus). Naudojant mažesnes vertes gaunama mažiau, bet tikslesnių atitikmenų.", "app.containers.phaseConfig.similarityThresholdTitle": "Pana<PERSON><PERSON><PERSON> (pavadinimas)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "<PERSON><PERSON> tai nustato<PERSON>, kiek pana<PERSON> turi būti du pavadinimai, kad jie būtų pažymėti kaip panaš<PERSON>. Naudokite reikšmę nuo 0 (griežtas) iki 1 (švelnus). Naudojant mažesnes vertes gaunama mažiau, bet tikslesnių atitikmenų.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "<PERSON><PERSON><PERSON>ą kaip ankstyvosios prieigos dalį galima naudoti iki 2025 m. birželio 30 d. Jei norite ja naudotis ir po šios datos, susisiekite su savo Vyriausybės sėkmės vadybininku arba administratoriumi ir aptarkite aktyvavimo galimybes.", "app.containers.survey.sentiment.noAnswers2": "Šiuo metu atsakymų nėra.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 komentarų} one {1 komentaras} other {# komentarai}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Dalyviai - tai naudotojai arba la<PERSON>, kurie daly<PERSON>vo projekte, p<PERSON><PERSON><PERSON><PERSON> pasi<PERSON> arba su juo sąveikavo, arba dalyvavo renginiuose.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Dalyviai", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Dalyvavimo lygis", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurie <PERSON>, pro<PERSON><PERSON><PERSON> da<PERSON>.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Bendras dalyvių s<PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automatiniai el. laiškai", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "I<PERSON> {quantity} kampanijų", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Pasirinktiniai el. la<PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Elektroniniai laiškai", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "<PERSON><PERSON> viso iš<PERSON>ųstų el. laiškų", "app.modules.commercial.analytics.admin.components.Events.completed": "Užbaigta", "app.modules.commercial.analytics.admin.components.Events.events": "Rengin<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "<PERSON><PERSON> viso p<PERSON> įvykių", "app.modules.commercial.analytics.admin.components.Events.upcoming": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Kvietimai", "app.modules.commercial.analytics.admin.components.Invitations.pending": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "<PERSON><PERSON> viso išsiųsta kvietimų", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Eikite į įvesties tvarkytuvę", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Įėjimai", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktyvus", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "<PERSON><PERSON><PERSON><PERSON>, kurie n<PERSON> archyvuoti ir matomi pagrindinio pu<PERSON> \"Aktyvūs\".", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Archyvuota", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Projektų projektai", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Baigtas", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Čia skaičiuojami visi archyvuoti projektai ir baigti aktyvūs laiko juostos projektai.", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projektai", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "<PERSON><PERSON> viso projektų", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Platformoje matomų projektų skaičius", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Naujos registracijos", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Registracijos tarifas", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurie tampa registruo<PERSON><PERSON>, pro<PERSON><PERSON><PERSON>.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registracijos", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "<PERSON>š viso registracijų", "app.modules.commercial.analytics.admin.components.Tab": "Lankytojai", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Per paskutines 30 dienų:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Paskutinės 7 dienos:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Vieno apsilankymo puslapių peržiūros", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Apsilankymo trukmė", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Lankytojai", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Lankytojai\" - unikalių lankytojų skaičius. <PERSON><PERSON> asmuo platformoje lankosi kelis kartus, jis skaič<PERSON>oja<PERSON> tik vieną kartą.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Apsilankymai", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Apsilankymai\" - tai <PERSON><PERSON><PERSON><PERSON> skaič<PERSON>. <PERSON><PERSON> as<PERSON>o platform<PERSON> la<PERSON> keli<PERSON> kart<PERSON>, s<PERSON><PERSON><PERSON><PERSON><PERSON> kiekvienas apsilankymas.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "<PERSON><PERSON><PERSON>:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Skaičiuokite", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "Kalba", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "<PERSON>nky<PERSON><PERSON><PERSON> s<PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Lankytojų procentin<PERSON> da<PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "Spauskite čia", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "<PERSON><PERSON><PERSON>i", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Norėdami <PERSON>ū<PERSON>ti visą nukreip<PERSON>či<PERSON> s<PERSON>, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Lankytojai", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Apsilankymai", "app.modules.commercial.analytics.admin.components.totalParticipants": "Bendras dalyvių s<PERSON>", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Lankytojų duomenų dar nėra.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Pakeitėme lankytojų duomenų rinkimo ir rodymo būd<PERSON>. <PERSON><PERSON><PERSON> to lankytojų duomenys yra tikslesni ir galima gauti daugiau duomenų tipų, tačiau jie vis dar atitinka BDAR reikalavimus. Nors lankytojų laiko juostai naudojami duomenys yra senesni, duomenis \"Apsilankymo trukmei\", \"Apsilankymo puslapių peržiūros\" ir kitoms diagramoms pradėjome rinkti tik 2024 m. lapkritį, todėl anksčiau jokių duomenų nėra. Todėl jei pasirinksite duomenis iki 2024 m. lap<PERSON><PERSON><PERSON><PERSON>, tur<PERSON>kite omenyje, kad kai kurios diagramos gali būti tuščios arba atrodyti keistai.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Laikui bėgant pristatomi el. laiškai", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Dalyviai laikui b<PERSON>", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registracijos la<PERSON> b<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Data", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "<PERSON><PERSON> statistika", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Apsilankymai ir lankytojai laikui b<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Iš viso per laikotarpį", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Skaičiuokite", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "Kalba", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Tiesioginis įvažiavimas", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "<PERSON>nky<PERSON><PERSON><PERSON> s<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Apsilankymų procentin<PERSON> dalis", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Pa<PERSON>škos sistemos", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Socialiniai tinklai", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO nukreipimai", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Apsilankymų s<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "<PERSON><PERSON> s<PERSON>", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "<PERSON><PERSON><PERSON> turinio v<PERSON>liavėlę galite pašalinti pasirinkę šį elementą ir spustelėję viršuje esantį mygtuką pašalinti. Tuomet jis vėl atsiras skirtukuose \"Mat<PERSON>as\" arba \"Nematytas\".", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Netinkamas turinys aptinkamas automatiškai.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "<PERSON>ėra <PERSON>, apie kuriuos bendruomenė būtų prane<PERSON>, kad juos per<PERSON>, arba pranešim<PERSON>, kuriuos mūsų natūralios kalbos apdorojimo sistema būtų pažymėjusi kaip netinkamo turinio.", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Pa<PERSON>linti {numberOfItems, plural, one {turinio įspėjimą} other {# turinio įspėjimai}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Platformos naudotojas pranešė apie netinkamą.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Įspėjimai d<PERSON>l turinio", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Ataskaitų rengėjas", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "<PERSON><PERSON><PERSON><PERSON><PERSON> juo<PERSON> rod<PERSON>", "app.modules.navbar.admin.containers.addProject": "Projekto įtraukimas į nar<PERSON><PERSON>o juost<PERSON>", "app.modules.navbar.admin.containers.createCustomPageButton": "Sukurti pasirinktinį puslapį", "app.modules.navbar.admin.containers.deletePageConfirmation": "Ar tikrai norite ištrinti šį puslapį? To negal<PERSON> at<PERSON>. <PERSON>p pat galite pa<PERSON> puslapį iš nar<PERSON><PERSON>o juo<PERSON>, jei dar nesate pasirengę jo ištrinti.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Į nar<PERSON>ymo juostą galite įtraukti ne daugiau kaip 5 elementus.", "app.modules.navbar.admin.containers.pageHeader": "Puslapia<PERSON> ir meniu", "app.modules.navbar.admin.containers.pageSubtitle": "Navigacijos juostoje, be pusla<PERSON><PERSON> \"Pradž<PERSON>\" ir \"Projektai\", gali būti rodomi ne daugiau kaip penki puslapiai. Galite pervadinti meniu elementus, keisti jų eiliškumą ir pridėti naujų puslapių su savo turiniu.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Valdikliai", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Naudokite toliau esančią piktogramą ☰ ir vilkite AI turinį į ataskaitą.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Nėra jokių galimų dirbtinio intelekto įžvalgų. Galite jas sukurti savo projekte.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Eiti į projektą", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Pasirinkite etapą", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Atrakinti AI analizę", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Įtraukite dirbtinio intelekto generuojamas įžvalgas į savo ataskaitą", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Ataskaitas pateikite greičiau naudodami dirbtinį intelektą", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Ataskaitų teikimas su AI nėra įtrauktas į jūsų dabartinį planą. Norėdami atrakinti šią funk<PERSON>ją, pasitarkite su savo Vyriausybės sėkmės vadybininku.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Tai neįtraukta į dabartinį planą. Susisiekite su savo Vyriausybės sėkmės vadybininku arba administratoriumi, kad jį atrakintumėte.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Grupavimas pagal registracijos la<PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Grupuoti pagal a<PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Grupė<PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Grupuokite apklausos atsakymus pagal registracijos <PERSON> (lytis, vietovė, amžius ir kt.) arba kitus apklausos klausimus.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "Nėra", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "<PERSON><PERSON>ci<PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Apklausos etapas", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Ap<PERSON><PERSON><PERSON> k<PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Ar tikrai norite tai i<PERSON>nti?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "<PERSON><PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Red<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Paskelbkite savo komentarą", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Parašykite savo komentarą čia", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Norėdami sekti arba at<PERSON>akyti sekimo, spustelėkite toliau esan<PERSON> mygtukus. Projektų skaičius nurodytas skliausteliuose.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "Jūsų vietovėje", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "Atlikta", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Sekite nuostatas", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Šiuo metu nėra jokių aktyvių projektų, atsižvelgiant į jūsų sekimo parink<PERSON>.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Šiame valdiklyje rodomi projektai, susiję su naudotojo sekamomis sritimis. Atkreipkite dėmesį, kad jūsų platformoje gali būti naudojamas kitoks \"sričių\" pavadinimas - žr. platformos nustatymų skirtuką \"Sritys\". Jei naudotojas dar neseka jokių sričių, valdiklyje bus rodomos galimos sritys, kurias galima sekti. Šiuo atveju valdiklyje bus rodoma ne daugiau kaip 100 sričių.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Nėra paskelbtų projektų ar aplankų", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Paskelbti projektai ir aplankai", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Šiame valdiklyje bus rodomi š<PERSON>o metu paskelbti projektai ir aplankai, atsižvelgiant į projektų puslapyje nustatytą eiliškumą. <PERSON><PERSON> elgsena yra tokia pati, ka<PERSON> ir \"sen<PERSON><PERSON><PERSON>\" projektų valdiklio skirtuko \"active\" (aktyvus).", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Nėra pasirinktų projektų ar aplankų", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Pasirinkite projektus arba aplankus", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Pasirinkti projektai ir a<PERSON>lankai", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Naudodami šį valdiklį galite pasirinkti ir nustatyti tvarką, kuria norite, kad projektai ar aplankai būtų rodomi naudotojams.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projektai", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "apsilankykite mūsų pagalbos centre", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Daugiau informacijos apie rekomenduojamą vaizdo skiriamąją gebą rasite {supportPageLink}."}