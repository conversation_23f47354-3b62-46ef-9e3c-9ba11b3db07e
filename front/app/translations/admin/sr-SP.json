{"UI.FormComponents.required": "потребан", "app.Admin.ManagementFeed.action": "Action", "app.Admin.ManagementFeed.after": "After", "app.Admin.ManagementFeed.before": "Before", "app.Admin.ManagementFeed.changed": "Modified", "app.Admin.ManagementFeed.created": "Created", "app.Admin.ManagementFeed.date": "Date", "app.Admin.ManagementFeed.deleted": "Deleted", "app.Admin.ManagementFeed.folder": "Folder", "app.Admin.ManagementFeed.idea": "Idea", "app.Admin.ManagementFeed.in": "in project {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON>", "app.Admin.ManagementFeed.key": "Key", "app.Admin.ManagementFeed.managementFeedNudge": "Accessing the management feed is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.Admin.ManagementFeed.noActivityFound": "No activity found", "app.Admin.ManagementFeed.phase": "Phase", "app.Admin.ManagementFeed.project": "Project", "app.Admin.ManagementFeed.projectReviewApproved": "Project approved", "app.Admin.ManagementFeed.projectReviewRequested": "Project review requested", "app.Admin.ManagementFeed.title": "Management feed", "app.Admin.ManagementFeed.user": "User", "app.Admin.ManagementFeed.userPlaceholder": "Select a user", "app.Admin.ManagementFeed.value": "Value", "app.Admin.ManagementFeed.viewDetails": "View details", "app.Admin.ManagementFeed.warning": "Experimental feature: A minimal list of selected actions performed by admins or managers in the last 30 days. Not all actions are included.", "app.Admin.Moderation.managementFeed": "Management feed", "app.Admin.Moderation.participationFeed": "Participation feed", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Are you sure?", "app.components.Admin.Campaigns.clicked": "Clicked", "app.components.Admin.Campaigns.deleteCampaignButton": "Delete campaign", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Accepted", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Bounced", "app.components.Admin.Campaigns.deliveryStatus_clicked": "Clicked", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip": "When you added one or more links to your email, the number of users who clicked a link will be shown here.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Delivered", "app.components.Admin.Campaigns.deliveryStatus_failed": "Failed", "app.components.Admin.Campaigns.deliveryStatus_opened": "Opened", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Draft", "app.components.Admin.Campaigns.manageButtonLabel": "Manage", "app.components.Admin.Campaigns.opened": "Opened", "app.components.Admin.Campaigns.project": "Project", "app.components.Admin.Campaigns.recipientsTitle": "Recipients", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Statistics", "app.components.Admin.Campaigns.subject": "Subject", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.info": "Ова слика се увек исече у одређеном односу како би се осигурало да су сви кључни аспекти увек приказани. {link} за овај тип слике је {aspect}.", "app.components.Admin.ImageCropper.infoLinkText": "препоручени однос", "app.components.Admin.ImageCropper.mobileCropExplanation": "Note: Any important area of the image should be contained within the vertical dashed lines, as the image will be cropped to a 3:1 ratio on mobile devices.", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Текст заглавља за регистроване кориснике", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Упозорење: боја коју сте изабрали нема довољно висок контраст. То може довести до текста који је тешко читати. Изаберите тамнију боју да бисте оптимизовали читљивост.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Додајте догађаје на траку за навигацију", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "Када је омогућено, на траку за навигацију ће бити додата веза ка свим догађајима пројекта.", "app.components.AdminPage.SettingsPage.eventsSection": "Догађаји", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Прилагодљиви одељак почетне странице", "app.components.AnonymousPostingToggle.userAnonymity": "Анонимност корисника", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Users will be able to hide their identity from other users, project managers and admins. These contributions can still be moderated.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Дозволите корисницима да учествују анонимно", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Корисници и даље могу да изаберу да учествују са својим правим именом, али ће имати могућност да анонимно поднесу прилоге ако то желе. Сви корисници ће и даље морати да се придржавају захтева постављених на картици Права приступа да би могли да дају свој допринос. Подаци корисничког профила неће бити доступни при извозу података о учешћу.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Сазнајте више о анонимности корисника у нашој {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "чланак подршке", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Када се додају додатна места, ваш обрачун ће бити повећан. Обратите се свом ГовСуццесс менаџеру да сазнате више о томе.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Thank you for completing the survey! You're welcome to take it again next quarter.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Download as pdf", "app.components.FormSync.downloadExcelTemplate": "Download an Excel template", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel templates will not include Ranking questions, Matrix questions, File upload questions and any mapping input questions (Drop Pin, Draw Route, Draw Area, ESRI file upload) as these are not supported for bulk importing at this time.", "app.components.ProjectTemplatePreview.close": "Близу", "app.components.ProjectTemplatePreview.createProject": "Креирајте пројекат", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Create a project based on the template ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Повратак назад", "app.components.ProjectTemplatePreview.goBackTo": "Вратите се на {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "Стручњак за Go Vocal", "app.components.ProjectTemplatePreview.infoboxLine1": "Желите да користите овај шаблон за свој пројекат учешћа?", "app.components.ProjectTemplatePreview.infoboxLine2": "Обратите се одговорном лицу у вашој градској управи или контактирајте {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Фасцикла пројекта", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Изабрани датум је неважећи. Наведите датум у следећем формату: ГГГГ-ММ-ДД", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Молимо изаберите датум почетка пројекта", "app.components.ProjectTemplatePreview.projectStartDate": "Датум почетка вашег пројекта", "app.components.ProjectTemplatePreview.projectTitle": "Наслов вашег пројекта", "app.components.ProjectTemplatePreview.projectTitleError": "Унесите назив пројекта", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Унесите назив пројекта за све језике", "app.components.ProjectTemplatePreview.projectsOverviewPage": "страница са прегледом пројеката", "app.components.ProjectTemplatePreview.responseError": "Упс! Нешто није у реду.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "Погледајте још шаблона", "app.components.ProjectTemplatePreview.successMessage": "Пројекат је успешно креиран!", "app.components.ProjectTemplatePreview.typeProjectName": "Унесите назив пројекта", "app.components.ProjectTemplatePreview.useTemplate": "Користите овај шаблон", "app.components.SeatInfo.additionalSeats": "Додатна седишта", "app.components.SeatInfo.additionalSeatsToolTip": "Ово показује број додатних седишта која сте купили на врху „Укључена седишта“.", "app.components.SeatInfo.adminSeats": "Административна места", "app.components.SeatInfo.adminSeatsIncludedText": "Укључено је {adminSeats} административних места", "app.components.SeatInfo.adminSeatsTooltip1": "Администратори су задужени за платформу и имају менаџерска права за све фасцикле и пројекте. Можете {visitHelpCenter} да сазнате више о различитим улогама.", "app.components.SeatInfo.currentAdminSeatsTitle": "Тренутна административна места", "app.components.SeatInfo.currentManagerSeatsTitle": "Тренутна менаџерска места", "app.components.SeatInfo.includedAdminToolTip": "Ово показује број расположивих места за администраторе укључених у годишњи уговор.", "app.components.SeatInfo.includedManagerToolTip": "Ово показује број расположивих места за менаџере укључених у годишњи уговор.", "app.components.SeatInfo.includedSeats": "Укључена седишта", "app.components.SeatInfo.managerSeats": "Менаџерска места", "app.components.SeatInfo.managerSeatsTooltip": "Менаџери фасцикли/пројеката могу управљати неограниченим бројем фасцикли/пројеката. Можете {visitHelpCenter} да сазнате више о различитим улогама.", "app.components.SeatInfo.managersIncludedText": "Укључено {managerSeats} менаџерских места", "app.components.SeatInfo.remainingSeats": "Преостала места", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Укупно места", "app.components.SeatInfo.totalSeatsTooltip": "Ово показује збир седишта у оквиру вашег плана и додатних места која сте купили.", "app.components.SeatInfo.usedSeats": "Половна седишта", "app.components.SeatInfo.view": "Поглед", "app.components.SeatInfo.visitHelpCenter": "посетите наш центар за помоћ", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Ваш план има {adminSeatsIncluded}. Када искористите сва седишта, додатна седишта ће бити додата под 'Додатна седишта'.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "Ваш план има {managerSeatsIncluded}, испуњава услове за менаџере фасцикли и менаџере пројеката. Када искористите сва седишта, додатна седишта ће бити додата под 'Додатна седишта'.", "app.components.UserSearch.addModerators": "Додати", "app.components.UserSearch.searchUsers": "Унесите да бисте претражили кориснике...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternative error message", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "By default, the following error message will be shown to users:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Customize error message", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "You can overwrite this message for each language using the \"Alternative error message\" text box below. If you leave the text box empty, the default message will be shown.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Error message", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "This is what participants will see when they don't meet the participation requirements.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Save error message", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "No question selected. Please select a question first.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "No answer", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} responses", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Survey question", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} until now", "app.components.admin.DatePhasePicker.Input.openEnded": "Open ended", "app.components.admin.DatePhasePicker.Input.selectDate": "Select date", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Clear end date", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Clear start date", "app.components.admin.Graphs": "Нема доступних података са тренутним филтерима.", "app.components.admin.Graphs.noDataShort": "Нема расположивих података.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Коментари током времена", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Постови током времена", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Реакције током времена", "app.components.admin.InputManager.onePost": "1 улаз", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Offline picks adjustment", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Offline votes adjustment", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "This option allows you to include participation data from other sources, such as in-person or paper votes:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "It will be visually distinct from digital votes.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "It will affect the final vote results.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "It will not be reflected in participation data dashboards.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offline votes for an option can only be set once in a project, and are shared between all phases of a project.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "You must enter the total offline participants first.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Total offline participants", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "In order to calculate the correct results, we need to know the <b>total amount of offline participants for this phase</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Please indicate only those that participated offline.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Modified by {name}", "app.components.admin.PostManager.PostPreview.assignee": "Асигнее", "app.components.admin.PostManager.PostPreview.cancelEdit": "Откажи уређивање", "app.components.admin.PostManager.PostPreview.currentStatus": "Тренутни статус", "app.components.admin.PostManager.PostPreview.delete": "Избриши", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Да ли сте сигурни да желите да избришете овај унос? Ова радња се не може опозвати.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Да ли сте сигурни да желите да избришете овај унос? Унос ће бити избрисан из свих фаза пројекта и не може се вратити.", "app.components.admin.PostManager.PostPreview.edit": "Уредити", "app.components.admin.PostManager.PostPreview.noOne": "Унассигнед", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Колико је пута ово укључено у партиципативне буџете других учесника", "app.components.admin.PostManager.PostPreview.picks": "Избори: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Реакција се рачуна:", "app.components.admin.PostManager.PostPreview.save": "сачувати", "app.components.admin.PostManager.PostPreview.submitError": "Грешка", "app.components.admin.PostManager.addFeatureLayer": "Add feature layer", "app.components.admin.PostManager.addFeatureLayerInstruction": "Copy the URL of the feature layer hosted on ArcGIS Online and paste it in the input below:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Add a new feature layer to the map", "app.components.admin.PostManager.addWebMap": "Add Web Map", "app.components.admin.PostManager.addWebMapInstruction": "Copy the portal ID of your Web Map from ArcGIS Online and paste it in the input below:", "app.components.admin.PostManager.allPhases": "Све фазе", "app.components.admin.PostManager.allProjects": "Сви пројекти", "app.components.admin.PostManager.allStatuses": "Сви статуси", "app.components.admin.PostManager.allTopics": "Све ознаке", "app.components.admin.PostManager.anyAssignment": "Било који администратор", "app.components.admin.PostManager.assignedTo": "Додељено на {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Додељено мени", "app.components.admin.PostManager.assignee": "Асигнее", "app.components.admin.PostManager.authenticationError": "An authentication error occured while trying to fetch this layer. Please check the URL and that your Esri API key has access to this layer.", "app.components.admin.PostManager.automatedStatusTooltipText": "This status updates automatically when conditions are met", "app.components.admin.PostManager.bodyTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel": "Cancel", "app.components.admin.PostManager.cancel2": "Cancel", "app.components.admin.PostManager.co-sponsors": "Co-sponsors", "app.components.admin.PostManager.comments": "Коментари", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "То значи да ћете изгубити све податке повезане са овим уносима, као што су коментари, реакције и гласови. Ова радња се не може опозвати.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Да ли сте сигурни да желите да избришете ове уносе?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Remove topic", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Покушавате да уклоните ову идеју из фазе у којој је добила гласове. Ако то урадите, ови гласови ће бити изгубљени. Да ли сте сигурни да желите да уклоните ову идеју из ове фазе?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Гласови повезани са овом идејом биће изгубљени", "app.components.admin.PostManager.components.goToInputManager": "Идите на менаџер уноса", "app.components.admin.PostManager.components.goToProposalManager": "Идите до менаџера предлога", "app.components.admin.PostManager.contributionFormTitle": "Уреди допринос", "app.components.admin.PostManager.cost": "Цост", "app.components.admin.PostManager.createInput": "Create input", "app.components.admin.PostManager.createInputsDescription": "Create a new set of inputs from a past project", "app.components.admin.PostManager.currentLat": "Централна ширина", "app.components.admin.PostManager.currentLng": "Централна географска дужина", "app.components.admin.PostManager.currentZoomLevel": "Ниво зумирања", "app.components.admin.PostManager.defaultEsriError": "An error occured while trying to fetch this layer. Please check your network connect and that the URL is correct.", "app.components.admin.PostManager.delete": "Избриши", "app.components.admin.PostManager.deleteAllSelectedInputs": "Обри<PERSON>и {count} постова", "app.components.admin.PostManager.deleteConfirmation": "Да ли сте сигурни да желите да избришете овај слој?", "app.components.admin.PostManager.dislikes": "Не свиђа ми се", "app.components.admin.PostManager.edit": "Уредити", "app.components.admin.PostManager.editProjects": "Уредите пројекте", "app.components.admin.PostManager.editStatuses": "Уредите статусе", "app.components.admin.PostManager.editTags": "Уредите Тема", "app.components.admin.PostManager.editedPostSave": "сачувати", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Importing data from Esri ArcGIS Online is an add-on feature. Talk to your GS manager to unlock it.", "app.components.admin.PostManager.esriSideError": "An error occured on the ArcGIS application. Please wait a few minutes and try again later.", "app.components.admin.PostManager.esriWebMap": "Esri Web Map", "app.components.admin.PostManager.exportAllInputs": "Извези све постове (.кслк)", "app.components.admin.PostManager.exportIdeasComments": "Извези све коментаре (.кслк)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Извези коментаре за овај пројекат (.кслк)", "app.components.admin.PostManager.exportInputsProjects": "Извези постове у овом пројекту (.кслк)", "app.components.admin.PostManager.exportSelectedInputs": "Извези изабране постове (.кслк)", "app.components.admin.PostManager.exportSelectedInputsComments": "Извези коментаре за изабране постове (.кслк)", "app.components.admin.PostManager.exportVotesByInput": "Export votes by input (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Export votes by user (.xslx)", "app.components.admin.PostManager.exports": "Извоз", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any current GeoJSON layers if you wish to add a Feature Layer.", "app.components.admin.PostManager.featureLayerTooltop": "You can find the Feature Layer URL on the right hand side of the item page on ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Одаберите како ће људи видети ваше име", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Објасните ову промену статуса", "app.components.admin.PostManager.fileUploadError": "Отпремање једне или више датотека није успело. Проверите величину и формат датотеке и покушајте поново.", "app.components.admin.PostManager.formTitle": "Уредите идеју", "app.components.admin.PostManager.generalApiError2": "An error occured while trying to fetch this item. Please check that the URL or Portal ID is correct and you have access to this item.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any ArcGIS data if you wish to upload a GeoJSON layer.", "app.components.admin.PostManager.goToDefaultMapView": "Идите на подразумевани центар мапе", "app.components.admin.PostManager.hiddenFieldsLink": "скривена поља", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Савет: <PERSON><PERSON><PERSON> користите Типеформ, додајте {hiddenFieldsLink} да бисте пратили ко је одговорио на вашу анкету.", "app.components.admin.PostManager.import2": "Import", "app.components.admin.PostManager.importError": "Изабрана датотека није могла да се увезе јер није важећа ГеоЈСОН датотека", "app.components.admin.PostManager.importEsriFeatureLayer": "Import Esri Feature Layer", "app.components.admin.PostManager.importEsriWebMap": "Import Esri Web Map", "app.components.admin.PostManager.importInputs": "Import inputs", "app.components.admin.PostManager.imported": "Увезено", "app.components.admin.PostManager.initiativeFormTitle": "Edit initiative", "app.components.admin.PostManager.inputCommentsExportFileName": "инпут_цомментс", "app.components.admin.PostManager.inputImportProgress": "{importedCount} out of {totalCount} {totalCount, plural, one {input has} other {inputs have}} been imported. The import is still in progress, please check back later.", "app.components.admin.PostManager.inputManagerHeader": "Улазни", "app.components.admin.PostManager.inputs": "Улазни", "app.components.admin.PostManager.inputsExportFileName": "улазни", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Прикажи само постове којима су потребне повратне информације", "app.components.admin.PostManager.issueFormTitle": "Уреди проблем", "app.components.admin.PostManager.latestFeedbackMode": "Користите најновије постојеће званично ажурирање као објашњење", "app.components.admin.PostManager.layerAdded": "Layer added successfully", "app.components.admin.PostManager.likes": "Свиђа", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Померањем овог уноса даље од његовог тренутног пројекта изгубићете информације о додељеним фазама. Да ли желите да наставите?", "app.components.admin.PostManager.mapData": "Map data", "app.components.admin.PostManager.multipleInputs": "{ideaCount} постова", "app.components.admin.PostManager.newFeedbackMode": "Напишите ново ажурирање да бисте објаснили ову промену", "app.components.admin.PostManager.noFilteredResults": "Филтери које сте изабрали нису дали резултате", "app.components.admin.PostManager.noInputs": "No inputs yet", "app.components.admin.PostManager.noInputsDescription": "You add your own input or start from a past participation project.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 inputs} one {1 input} other {# inputs}} will be imported from the selected project and phase. The import will run in the background, and the inputs will appear in the input manager once it is complete.", "app.components.admin.PostManager.noOne": "Унассигнед", "app.components.admin.PostManager.noProject": "No project", "app.components.admin.PostManager.officialFeedbackModal.author": "Author", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "Choose how your name will appear", "app.components.admin.PostManager.officialFeedbackModal.description": "Providing official feedback helps keep the process transparent and builds trust in the platform.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Author is required", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Feedback is required", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Official feedback", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Explain the reason for the status change", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Post feedback", "app.components.admin.PostManager.officialFeedbackModal.skip": "Skip this time", "app.components.admin.PostManager.officialFeedbackModal.title": "Explain your decision", "app.components.admin.PostManager.officialUpdateAuthor": "Одаберите како ће људи видети ваше име", "app.components.admin.PostManager.officialUpdateBody": "Објасните ову промену статуса", "app.components.admin.PostManager.offlinePicks": "Offline picks", "app.components.admin.PostManager.offlineVotes": "Offline votes", "app.components.admin.PostManager.onlineVotes": "Online votes", "app.components.admin.PostManager.optionFormTitle": "Измени опцију", "app.components.admin.PostManager.participants": "Учесници", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Online picks", "app.components.admin.PostManager.pbItemCountTooltip": "Колико је пута ово укључено у партиципативне буџете других учесника", "app.components.admin.PostManager.petitionFormTitle": "Edit petition", "app.components.admin.PostManager.postedIn": "Објављено у {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Уреди пројекат", "app.components.admin.PostManager.projectsTab": "Пројекти", "app.components.admin.PostManager.projectsTabTooltipContent": "Можете превући и испустити постове да бисте их преместили из једног пројекта у други. Имајте на уму да ћете за пројекте временске линије и даље морати да додате објаву у одређену фазу.", "app.components.admin.PostManager.proposalFormTitle": "Edit proposal", "app.components.admin.PostManager.proposedBudgetTitle": "Предложени буџет", "app.components.admin.PostManager.publication_date": "Објављено на", "app.components.admin.PostManager.questionFormTitle": "Уреди питање", "app.components.admin.PostManager.reactions": "Реакције", "app.components.admin.PostManager.resetFiltersButton": "Ресетујте филтере", "app.components.admin.PostManager.resetInputFiltersDescription": "Ресетујте филтере да бисте видели сав унос.", "app.components.admin.PostManager.saved": "Сачувано", "app.components.admin.PostManager.screeningTooltip": "Screening is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Screening is turned off for this phase. Go to phase setup to enable it", "app.components.admin.PostManager.selectAPhase": "Select a phase", "app.components.admin.PostManager.selectAProject": "Select a project", "app.components.admin.PostManager.setAsDefaultMapView": "Сачувајте тренутну средишњу тачку и ниво зумирања као подразумеване вредности мапе", "app.components.admin.PostManager.startFromPastInputs": "Start from past inputs", "app.components.admin.PostManager.statusChangeGenericError": "Дошло је до грешке, покушајте поново касније или контактирајте подршку.", "app.components.admin.PostManager.statusChangeSave": "Промени статус", "app.components.admin.PostManager.statusesTab": "Статус", "app.components.admin.PostManager.statusesTabTooltipContent": "Промените статус објаве помоћу превлачења и отпуштања. Оригинални аутор и други сарадници ће добити обавештење о промењеном статусу.", "app.components.admin.PostManager.submitApiError": "Дошло је до проблема при слању обрасца. Проверите да ли постоје грешке и покушајте поново.", "app.components.admin.PostManager.timelineTab": "Временска линија", "app.components.admin.PostManager.timelineTabTooltipText": "Превуците и испустите постове да бисте их копирали у различите фазе пројекта.", "app.components.admin.PostManager.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "Тема", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.view": "View", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "votes_by_input", "app.components.admin.PostManager.votesByUserExportFileName": "votes_by_user", "app.components.admin.PostManager.webMapAlreadyExists": "You can only add one Web Map at a time. Remove the current one to import a different one.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "You may only upload map data as either GeoJSON layers or importing from ArcGIS Online. Please remove any current GeoJSON layers if you wish to connect a Web Map.", "app.components.admin.PostManager.webMapTooltip": "You can find the Web Map portal ID on your ArcGIS Online item page, on the right hand side.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {Мање од једног дана} one {Једног дана} other {# дана}} лево", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "Cancel", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "Yes, delete survey results", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "This cannot be undone", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Delete survey results", "app.components.admin.ProjectEdit.survey.downloadResults2": "Download survey results", "app.components.admin.ReportExportMenu.FileName.fromFilter": "из", "app.components.admin.ReportExportMenu.FileName.groupFilter": "група", "app.components.admin.ReportExportMenu.FileName.projectFilter": "пројекат", "app.components.admin.ReportExportMenu.FileName.topicFilter": "таг", "app.components.admin.ReportExportMenu.FileName.untilFilter": "све док", "app.components.admin.ReportExportMenu.downloadPng": "Преузми као ПНГ", "app.components.admin.ReportExportMenu.downloadSvg": "Преузмите као СВГ", "app.components.admin.ReportExportMenu.downloadXlsx": "Преузмите Екцел", "app.components.admin.SlugInput.regexError": "Пуж може да садржи само обична, мала слова (аз), бројеве (0-9) и цртице (-). Први и последњи знак не могу бити цртице. Узастопне цртице (--) су забрањене.", "app.components.admin.TerminologyConfig.saveButton": "Сачувајте", "app.components.admin.commonGroundInputManager.title": "Title", "app.components.admin.seatSetSuccess.admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.allDone": "Завршено", "app.components.admin.seatSetSuccess.close": "Близу", "app.components.admin.seatSetSuccess.manager": "Менаџер", "app.components.admin.seatSetSuccess.orderCompleted": "Наруџба је завршена", "app.components.admin.seatSetSuccess.reflectedMessage": "Промене у вашем плану ће се одразити на следећи обрачунски циклус.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} права је додељено изабраним корисницима.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Are you sure you want to delete all survey results?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "This participation method is in beta. We're gradually rolling it out to gather feedback and improve the experience.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Прикупљање повратних информација о документу је прилагођена функција и није укључена у вашу тренутну лиценцу. Обратите се свом ГовСуццесс менаџеру да сазнате више о томе.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Допринос", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Number of days is required", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Number of days to reach minimum number of votes", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Више информација о томе како да уградите везу за Гоогле формуларе можете пронаћи у {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "овај чланак подршке", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Идеја", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiative", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Како треба да се зове улаз?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Коментар", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "Овде наведите везу до вашег Конвеио документа. Прочитајте наш {supportArticleLink} за више информација о подешавању Конвеио-а.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "чланак подршке", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "This is not included in your current plan. Reach out to your Government Success Manager or admin to unlock it.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Потребан је максимални буџет", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Максим<PERSON><PERSON><PERSON>н број гласова по опцији мора бити мањи или једнак укупном броју гласова", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Потребан је максималан број гласова", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Messaging", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Минимални буџет не може бити већи од максималног буџета", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Потребан је минимални буџет", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Минимални број гласова не може бити већи од максималног", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Потребан је минималан број гласова", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Missing end date", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Missing start date", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Опција", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Картица Менаџер уноса", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Configure the voting options in the Input manager tab after creating a phase.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Конфигуришите опције гласања у {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Participation options", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "Participants", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Petition", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Admins & managers", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b>Annotating document:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} can participate in this phase.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "Поништити, отказати", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Comment:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "Common ground phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Обриши фазу", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "Да, избришите ову фазу", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Да ли сте сигурни да желите да избришете ову фазу?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "Сви подаци који се односе на ову фазу биће обрисани. Ово се не може поништити.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Фаза белешке на документу", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Everyone", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Фаза екстерног анкетирања", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Фаза идеје", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "У фази анкетирања платформе", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Фаза информација", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Mixed rights", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "Нема крајњег датума", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Фаза анкетирања", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Proposals phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>React:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b>Registered for event:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Registered users", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Submit inputs:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Taking poll:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Taking survey:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Users with confirmed emails", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Volunteering:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Фаза волонтирања", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Voting:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Фаза гласања", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Who can participate?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Inputs won’t be visible until an admin reviews and approves them. Authors can’t edit inputs after they are screened or reacted on.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "Само администратори", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "Anyone with the link can interact with the draft project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Approve", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Approving allows Project Managers to publish the project.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Approved by {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Нацрт", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Edit description", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "Сви", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Гру<PERSON>е", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Hidden", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline voters", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Only admins{inFolder, select, true { or the Folder Managers} other {}} can publish the project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 учесник} other {{participantsCount} учесници}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Participants in embedded methods (e.g., external surveys)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Followers of a project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Note: Enabling anonymous or open participation permissions may allow users to participate multiple times, leading to misleading or incomplete user data.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "Participants <b>do not include</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "Participants include:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Event registrants", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "Users interacting with Go Vocal methods", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Waiting for approval", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Project reviewers have been notified.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "Public", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Publish", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Објављено - активно", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Објављено - Завршено", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Refresh project preview link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Regenerate project preview link. This will invalidate the previous link.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Old links will stop working but you can generate a new one at any time.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Are you sure? This will disable the current link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "Cancel", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "Yes, refresh link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Request approval", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "The project must be approved by an admin{inFolder, select, true { or one of the Folder Managers} other {}} before you can publish it. Click the button below to request approval.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Settings", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Share", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "Copy link", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "Link copied", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Sharing private links is not included on your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Share this project", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Who has access", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Поглед", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Пројекат", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Proposal", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Пита<PERSON>е", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Minimum number of votes to be considered", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Minimum number of votes is required", "app.components.app.containers.AdminPage.ProjectEdit.report": "Report", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Require screening of inputs", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Timeline", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Traffic", "app.components.formBuilder.cancelMethodChange1": "Cancel", "app.components.formBuilder.changeMethodWarning1": "Changing methods can lead to hiding any input data generated or received while using the previous method.", "app.components.formBuilder.changingMethod1": "Changing method", "app.components.formBuilder.confirmMethodChange1": "Yes, continue", "app.components.formBuilder.copySurveyModal.cancel": "Cancel", "app.components.formBuilder.copySurveyModal.description": "This will copy all the questions and logic without the answers.", "app.components.formBuilder.copySurveyModal.duplicate": "Duplicate", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "No appropriate phases found in this project", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "No phase selected. Please select a phase first.", "app.components.formBuilder.copySurveyModal.noProject": "No project", "app.components.formBuilder.copySurveyModal.noProjectSelected": "No project selected. Please select a project first.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "You have already saved changes to this survey. If you duplicate another survey, the changes will be lost.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Survey phase", "app.components.formBuilder.copySurveyModal.title": "Select a survey to duplicate", "app.components.formBuilder.editWarningModal.addOrReorder": "Додајте или промените редослед питања", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Ваши подаци о одговору могу бити нетачни", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Уредите текст", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Исправљате грешку у куцању? То неће утицати на податке о вашим одговорима", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Изгубићете податке о одговорима који су повезани са тим питањем", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Избришите питање", "app.components.formBuilder.editWarningModal.exportYouResponses2": "извезите своје одговоре.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Упозорење: Можете заувек изгубити податке о одговорима. Пре него што наставите,", "app.components.formBuilder.editWarningModal.noCancel": "Не, откажи", "app.components.formBuilder.editWarningModal.title4": "Измените анкету уживо", "app.components.formBuilder.editWarningModal.yesContinue": "Да, настави", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "access rights settings for this survey", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Demographic fields in survey form' is enabled. When the survey form is displayed any configured demographic questions will be added on a new page immediately before the end of the survey. These questions can be changed in the {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "access rights settings for this phase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Survey respondents will not be required to sign up or log in to submit survey answers, which may result in duplicate submissions", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "By skipping the sign up/log in step, you accept not to collect demographic information on survey respondents, which may impact your data analysis capabilities", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "This survey is set to allow access for \"Anyone\" under the Access Rights tab.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "If you wish to change this, you can do so in the {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "You are asking the following demographic questions of survey respondents through the sign up/log in step.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "To streamline the collection of demographic information and ensure its integration into your user database, we advise incorporating any demographic questions directly into the sign-up/log-in process. To do so, please use the {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Замолите кориснике да прате области или теме", "app.components.onboarding.followHelperText": "Ово активира корак у процесу регистрације где ће корисници моћи да прате области или теме које изаберете испод", "app.components.onboarding.followPreferences": "Пратите подешавања", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} у оквиру плана, {noOfAdditionalSeats} додатно", "app.components.seatsWithinPlan.seatsWithinPlanText": "Седишта у оквиру плана", "app.containers.Admin.Campaigns.campaignFrom": "Од:", "app.containers.Admin.Campaigns.campaignTo": "До:", "app.containers.Admin.Campaigns.customEmails": "Прилагођене е-поруке", "app.containers.Admin.Campaigns.customEmailsDescription": "Шаљите прилагођене имејлове и проверите статистику.", "app.containers.Admin.Campaigns.noAccess": "Жа<PERSON> нам је, али изгледа да немате приступ одељку е-поште", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Аутоматизоване е-поруке", "app.containers.Admin.Insights.tabReports": "Reports", "app.containers.Admin.Invitations.a11y_removeInvite": "Remove invitation", "app.containers.Admin.Invitations.addToGroupLabel": "Додајте ове људе одређеним ручним корисничким групама", "app.containers.Admin.Invitations.adminLabel1": "Дајте позванима администраторска права", "app.containers.Admin.Invitations.adminLabelTooltip": "Када изаберете ову опцију, људи које позивате имаће приступ свим подешавањима ваше платформе.", "app.containers.Admin.Invitations.configureInvitations": "3. Конфигуришите позивнице", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "Нема позивница које одговарају вашој претрази", "app.containers.Admin.Invitations.deleteInvite": "Избриши", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Are you sure you want to delete this invitation?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Отказивање позивнице ће вам омогућити да поново пошаљете позив овој особи.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Преузмите и попуните шаблон", "app.containers.Admin.Invitations.downloadTemplate": "Преузмите шаблон", "app.containers.Admin.Invitations.email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.emailListLabel": "Ручно унесите адресе е-поште људи које желите да позовете. Одвојите сваку адресу зарезом.", "app.containers.Admin.Invitations.exportInvites": "Извези све позивнице", "app.containers.Admin.Invitations.fileRequirements": "Важно: Да бисте исправно послали позивнице, ниједна колона се не може уклонити из шаблона за увоз. Оставите неискоришћене колоне празне.", "app.containers.Admin.Invitations.filetypeError": "Нетачан тип датотеке. Подржане су само КСЛСКС датотеке.", "app.containers.Admin.Invitations.groupsPlaceholder": "Није изабрана ниједна група", "app.containers.Admin.Invitations.helmetDescription": "Позовите кориснике на платформу", "app.containers.Admin.Invitations.helmetTitle": "Контролна табла за позиве администратора", "app.containers.Admin.Invitations.importOptionsInfo": "Ове опције ће бити узете у обзир само када нису дефинисане у Екцел датотеци.\n      Молимо посетите {supportPageLink} за више информација.", "app.containers.Admin.Invitations.importTab": "Увезите адресе е-поште", "app.containers.Admin.Invitations.invitationExpirationWarning": "Имајте на уму да позивнице истичу након 30 дана. Након овог периода, још увек можете да их поново пошаљете.", "app.containers.Admin.Invitations.invitationOptions": "Опције позива", "app.containers.Admin.Invitations.invitationSubtitle": "Позовите људе на платформу у било ком тренутку. Добијају неутралну позивницу са вашим логом у којој се од њих тражи да се региструју на платформи.", "app.containers.Admin.Invitations.invitePeople": "Позовите људе путем е-поште", "app.containers.Admin.Invitations.inviteStatus": "Статус", "app.containers.Admin.Invitations.inviteStatusAccepted": "Прихваћено", "app.containers.Admin.Invitations.inviteStatusPending": "нерешен", "app.containers.Admin.Invitations.inviteTextLabel": "Опционо унесите поруку која ће бити додата у пошту са позивом.", "app.containers.Admin.Invitations.invitedSince": "Позвани", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Изаберите језик позива", "app.containers.Admin.Invitations.moderatorLabel": "Дајте овим људима права на управљање пројектима", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Када изаберете ову опцију, позваним особама ће бити додељена права менаџера пројекта за изабране пројекте. Више информација о правима менаџера пројекта {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "овде", "app.containers.Admin.Invitations.name": "Име", "app.containers.Admin.Invitations.processing": "Слање позивница. Сачекајте...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Ниједан пројекат(и) није изабран", "app.containers.Admin.Invitations.save": "Пошаљите позивнице", "app.containers.Admin.Invitations.saveErrorMessage": "Дошло је до једне или више грешака и позивнице нису послате. Исправите доле наведене грешке и покушајте поново.", "app.containers.Admin.Invitations.saveSuccess": "Успех!", "app.containers.Admin.Invitations.saveSuccessMessage": "Позивница је успешно послата.", "app.containers.Admin.Invitations.supportPage": "страница за подршку", "app.containers.Admin.Invitations.supportPageLinkText": "Посетите страницу за подршку", "app.containers.Admin.Invitations.tabAllInvitations": "Све позивнице", "app.containers.Admin.Invitations.tabInviteUsers": "Позовите кориснике", "app.containers.Admin.Invitations.textTab": "Ручно унесите адресе е-поште", "app.containers.Admin.Invitations.unknownError": "Нешто није у реду. Покушајте поново касније.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Отпремите своју завршену датотеку шаблона", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} ако желите више информација о свим подржаним колонама у шаблону за увоз.", "app.containers.Admin.Moderation.all": "Све", "app.containers.Admin.Moderation.belongsTo": "Припада", "app.containers.Admin.Moderation.collapse": "види мање", "app.containers.Admin.Moderation.comment": "Коментар", "app.containers.Admin.Moderation.commentDeletionCancelButton": "Cancel", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Delete", "app.containers.Admin.Moderation.confirmCommentDeletion": "Are you sure you want to delete this comment? This is permanent and can't be undone.", "app.containers.Admin.Moderation.content": "Садржај", "app.containers.Admin.Moderation.date": "Датум", "app.containers.Admin.Moderation.deleteComment": "Delete comment", "app.containers.Admin.Moderation.goToComment": "Отворите овај коментар у новој картици", "app.containers.Admin.Moderation.goToPost": "Отворите овај пост у новој картици", "app.containers.Admin.Moderation.goToProposal": "Отворите овај предлог у новој картици", "app.containers.Admin.Moderation.markFlagsError": "Није могуће означити ставке. Покушајте поново.", "app.containers.Admin.Moderation.markNotSeen": "Марк {selectedItemsCount, plural, one {# ставка} other {# предмета}} како се не види", "app.containers.Admin.Moderation.markSeen": "Марк {selectedItemsCount, plural, one {# ставка} other {# предмета}} као што се види", "app.containers.Admin.Moderation.moderationsTooltip": "Ова страница вам омогућава да брзо проверите све нове постове који су објављени на вашој платформи, укључујући идеје и коментаре. Можете да означите постове као да су „виђене“ тако да други знају шта још треба да се обради.", "app.containers.Admin.Moderation.noUnviewedItems": "Нема невиђених предмета", "app.containers.Admin.Moderation.noViewedItems": "Нема виђених предмета", "app.containers.Admin.Moderation.pageTitle1": "Напајање", "app.containers.Admin.Moderation.post": "пошта", "app.containers.Admin.Moderation.profanityBlockerSetting": "Блокатор вулгарности", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Блокирајте постове који садрже најчешће пријављене увредљиве речи.", "app.containers.Admin.Moderation.project": "Пројекат", "app.containers.Admin.Moderation.read": "Сеен", "app.containers.Admin.Moderation.readMore": "Опширније", "app.containers.Admin.Moderation.removeFlagsError": "Није могуће уклонити упозорење(а). Покушајте поново.", "app.containers.Admin.Moderation.rowsPerPage": "Редова по страници", "app.containers.Admin.Moderation.settings": "Подешавања", "app.containers.Admin.Moderation.settingsSavingError": "Није могуће сачувати. Покушајте поново да промените поставку.", "app.containers.Admin.Moderation.show": "Прикажи", "app.containers.Admin.Moderation.status": "Статус", "app.containers.Admin.Moderation.successfulUpdateSettings": "Подешавања су успешно ажурирана.", "app.containers.Admin.Moderation.type": "Тип", "app.containers.Admin.Moderation.unread": "Не види", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Ова страница се састоји од следећих одељака. Можете их укључити/искључити и уредити по потреби.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Секције", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Прикажи страницу", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Није приказано на страници", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Приказано на страници", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Прилози", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "Додајте датотеке (макс. 50 МБ) које ће бити доступне за преузимање са странице.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Доњи одељак са информацијама", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Додајте свој садржај у прилагодљиви одељак на дну странице.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "Уредити", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Листа догађаја", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Прикажите догађаје у вези са пројектима.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Херојски банер", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Прилагодите слику и текст банера странице.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Листа пројеката", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Прикажите пројекте на основу подешавања ваше странице. Можете прегледати пројекте који ће бити приказани.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Топ инфо одељак", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Додајте свој садржај у прилагодљиви одељак на врху странице.", "app.containers.Admin.PagesAndMenu.addButton": "Додај на навигациону траку", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Име у навигационој траци", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Да ли сте сигурни да желите да избришете ову страницу? Ово се не може поништити.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "Наведите наслов за све језике", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Друге доступне странице", "app.containers.Admin.PagesAndMenu.components.savePage": "Сачувај страницу", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Страница је успешно сачувана", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Прилози (макс. 50МБ)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Успех", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Садржај", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Није могуће сачувати прилоге", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Датотеке не би требало да буду веће од 50Мб. Додате датотеке ће бити приказане на дну ове странице", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Прилози су сачувани", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Прилози | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Прилози", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Сачувајте и омогућите прилоге", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Сачувајте прилоге", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Обезбедите садржај за све језике", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Успех", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Садржај", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Није могуће сачувати доњи одељак са информацијама", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Доњи одељак са информацијама је сачуван", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Доњи одељак са информацијама", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Сачувајте и омогућите доњи одељак са информацијама", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Сачувајте доњи одељак са информацијама", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Creating custom pages is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Изаберите најмање једну ознаку", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Успех", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "По области", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "По ознакама", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Displaying projects by tag or area is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Садржај", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Уредите прилагођену страницу", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Повезани пројекти", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "Изаберите који пројекти и повезани догађаји могу бити приказани на страници.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Страница је успешно направљена", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Страница је успешно сачувана", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Прилагођена страница је сачувана", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Наслов на траци за навигацију", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Креирајте прилагођену страницу | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Направите прилагођену страницу", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "Нијед<PERSON>н", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Подешавања странице", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Сачувајте прилагођену страницу", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Молимо изаберите област", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Изабрано подручје", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Изабране ознаке", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Пуж може да садржи само обична, мала слова (аз), бројеве (0-9) и цртице (-). Први и последњи знак не могу бити цртице. Узастопне цртице (--) су забрањене.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "Морате унети пуж", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "Унесите наслов на сваком језику", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Унесите наслов", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Прикажи прилагођену страницу", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "Дугме", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Уреди прилагођену страницу | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Сад<PERSON><PERSON><PERSON><PERSON> странице", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.automatedEmailsLinkText": "аутоматизоване е-поруке", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "Уредити", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "За једнофазне пројекте, ако је крајњи датум празан и опис није попуњен, временска линија неће бити приказана на страници пројекта.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Нема доступних пројеката на основу вашег {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Овај пројекат нема ознаку или филтер подручја, тако да ниједан пројекат неће бити приказан.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Листа пројеката | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "подешавања странице", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Листа пројеката", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Следећи пројекти ће бити приказани на овој страници на основу вашег {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.defaultTag": "УОБИЧАЈЕНО", "app.containers.Admin.PagesAndMenu.deleteButton": "Избриши", "app.containers.Admin.PagesAndMenu.editButton": "Уредити", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Успех", "app.containers.Admin.PagesAndMenu.heroBannerError": "Није могуће сачувати банер хероја", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Херојски банер је сачуван", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "Сачувај херој банер", "app.containers.Admin.PagesAndMenu.homeTitle": "Почетна", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Обезбедите садржај за најмање један језик", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "You can only add up to 5 items to the navigation bar", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Странице и мени | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Уклони са навигационе траке", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Сачувајте и омогућите банер хероја", "app.containers.Admin.PagesAndMenu.title": "Странице и мени", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Успех", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Садржај", "app.containers.Admin.PagesAndMenu.topInfoError": "Није могуће сачувати одељак са најважнијим информацијама", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Одељак са главним информацијама је сачуван", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Топ инфо секција | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Топ инфо одељак", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Сачувајте и омогућите одељак са главним информацијама", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Сачувајте топ инфо одељак", "app.containers.Admin.PagesAndMenu.viewButton": "Поглед", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Age", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Community", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Executive summary", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Top-level inclusion indicators", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "The following section outlines inclusion indicators, highlighting your our progress towards fostering a more inclusive and representative participation platform.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "participants", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Top-level participation indicators", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "The following section outlines the key participation indicators for the selected time range, providing an overview of engagement trends and performance metrics.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projects", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "projects published", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Platform report", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Your projects", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "The following section provides an overview of publicly visible projects that overlap with the selected time range, the most used methods in these projects, and metrics concerning the total amount of participation.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Registrations timeline", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Блокирани корисници", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Управљајте блокираним корисницима.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Избриши групу", "app.containers.Admin.Users.GroupsHeader.editGroup": "Уреди групу", "app.containers.Admin.Users.GroupsPanel.admins": "Admins", "app.containers.Admin.Users.GroupsPanel.allUsers": "Регистровани корисници", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Гру<PERSON>е", "app.containers.Admin.Users.GroupsPanel.managers": "Project managers", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Assigned items", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Добијте преглед свих људи и организација које су се регистровале на платформи. Додајте избор корисника у групе Ручно или Паметне групе.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Позив на чекању", "app.containers.Admin.Users.admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.assign": "Assign", "app.containers.Admin.Users.assignedItems": "Assigned items for {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Купите једно додатно седиште", "app.containers.Admin.Users.changeUserRights": "Промените корисничка права", "app.containers.Admin.Users.confirm": "Потврди", "app.containers.Admin.Users.confirmAdminQuestion": "Да ли сте сигурни да желите да дате {name} администраторских права платформе?", "app.containers.Admin.Users.confirmNormalUserQuestion": "Да ли сте сигурни да желите да поставите {name} као обичан корисник?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Да ли сте сигурни да желите да поставите {name} као обичан корисник? Имајте на уму да ће изгубити права менаџера за све пројекте и фасцикле којима су додељени након потврде.", "app.containers.Admin.Users.deleteUser": "Избриши корисника", "app.containers.Admin.Users.email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.folder": "Folder", "app.containers.Admin.Users.folderManager": "Управљач фасциклама", "app.containers.Admin.Users.helmetDescription": "Листа корисника у админ", "app.containers.Admin.Users.helmetTitle": "Администратор - контролна табла корисника", "app.containers.Admin.Users.inviteUsers": "Позовите кориснике", "app.containers.Admin.Users.joined": "Joined", "app.containers.Admin.Users.lastActive": "Last active", "app.containers.Admin.Users.name": "Име", "app.containers.Admin.Users.noAssignedItems": "No assigned items", "app.containers.Admin.Users.options": "Опције", "app.containers.Admin.Users.permissionToBuy": "Да бисте дали {name} администраторских права, морате купити 1 додатно место.", "app.containers.Admin.Users.platformAdmin": "Админ платформе", "app.containers.Admin.Users.projectManager": "Вођа пројекта", "app.containers.Admin.Users.reachedLimitMessage": "Достигли сте ограничење места у оквиру свог плана, биће додато 1 додатно место за {name}.", "app.containers.Admin.Users.registeredUser": "Регистровани корисник", "app.containers.Admin.Users.remove": "Remove", "app.containers.Admin.Users.removeModeratorFrom": "The user is moderating the folder this project belongs to. Remove assignment from \"{folderTitle}\" instead.", "app.containers.Admin.Users.role": "Role", "app.containers.Admin.Users.seeProfile": "Види профил", "app.containers.Admin.Users.selectPublications": "Select projects or folders", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Type to search", "app.containers.Admin.Users.setAsAdmin": "Постави као администратор", "app.containers.Admin.Users.setAsNormalUser": "Постави као нормалан корисник", "app.containers.Admin.Users.setAsProjectModerator": "Set as project manager", "app.containers.Admin.Users.setUserAsProjectModerator": "Assign {name} as project manager", "app.containers.Admin.Users.userBlockModal.allDone": "Завршено", "app.containers.Admin.Users.userBlockModal.blockAction": "Блокирај корисника", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Садржај овог корисника неће бити уклоњен овом радњом. Не заборавите да модерирате њихов садржај ако је потребно.", "app.containers.Admin.Users.userBlockModal.blocked": "Блокирано", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Овај корисник је блокиран од {from}. Забрана траје до {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Поништити, отказати", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "Да ли сте сигурни да желите да деблокирате {name}?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} је блокирано до {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 дан} other {{numberOfDays} дана}}", "app.containers.Admin.Users.userBlockModal.header": "Блокирај корисника", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Разлог", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Ово ће бити саопштено блокираном кориснику.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Изабрани корисник неће моћи да се пријави на платформу за {daysBlocked}. Ако желите да ово вратите, можете да их деблокирате са листе блокираних корисника.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Деблокирај", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "Да, желим да деблокирам овог корисника", "app.containers.Admin.Users.userDeletionConfirmation": "Желите ли да трајно уклоните овог корисника?", "app.containers.Admin.Users.userDeletionFailed": "Дошло је до грешке приликом брисања овог корисника, покушајте поново.", "app.containers.Admin.Users.userDeletionProposalVotes": "Ово ће такође избрисати све гласове овог корисника о предлозима који су још отворени за гласање.", "app.containers.Admin.Users.userExportFileName": "усер_екпорт", "app.containers.Admin.Users.userInsights": "Увиди корисника", "app.containers.Admin.Users.youCantDeleteYourself": "Не можете избрисати сопствени налог преко корисничке администраторске странице", "app.containers.Admin.Users.youCantUnadminYourself": "Не можете сада одустати од своје улоге администратора", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Community monitor", "app.containers.Admin.communityMonitor.healthScore": "Health Score", "app.containers.Admin.communityMonitor.healthScoreDescription": "This score is the average of all sentiment-scale questions answered by participants for the period selected.", "app.containers.Admin.communityMonitor.lastQuarter": "last quarter", "app.containers.Admin.communityMonitor.liveMonitor": "Live monitor", "app.containers.Admin.communityMonitor.noResults": "No results for this period.", "app.containers.Admin.communityMonitor.noSurveyResponses": "No survey responses", "app.containers.Admin.communityMonitor.participants": "Participants", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Reports", "app.containers.Admin.communityMonitor.settings": "Settings", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Community Monitor Survey is accepting submissions.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Access rights", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "After a user registers an event attendance, submits a vote, or returns to a project page after submitting a survey.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Community Monitor Managers can access and manage all Community Monitor settings and data.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Community Monitor Managers", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Managers can edit the Community Monitor survey & permissions, see response data and create reports.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "The default frequency value is 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Popup frequency (0 to 100)", "app.containers.Admin.communityMonitor.settings.management2": "Management", "app.containers.Admin.communityMonitor.settings.popup": "Popup", "app.containers.Admin.communityMonitor.settings.popupDescription3": "A popup is periodically displayed to users encouraging them to complete the Community Monitor Survey. You can adjust the frequency which determines the percentage of users who will randomly see the popup when the conditions outlined below are met.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Popup settings", "app.containers.Admin.communityMonitor.settings.preview": "Preview", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "User has not already filled out the survey in the previous 3 months.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "User has not already seen the popup in the previous 3 months.", "app.containers.Admin.communityMonitor.settings.save": "Save", "app.containers.Admin.communityMonitor.settings.saved": "Saved", "app.containers.Admin.communityMonitor.settings.settings": "Settings", "app.containers.Admin.communityMonitor.settings.survey2": "Survey", "app.containers.Admin.communityMonitor.settings.surveySettings3": "General settings", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Upon loading the Homepage or a Custom Page.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymize all user data", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "All of the survey's inputs from users will be anonymized before being recorded", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Users will still need to comply with participation requirements under the 'Access Rights'. User profile data will not be available in the survey data export.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Under what conditions can the popup appear for users?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Who are the managers?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Total survey responses", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI summary", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Enable Community Monitor", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Health score", "app.containers.Admin.communityMonitor.upsell.learnMore": "Learn more", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Score over time", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "The Community Monitor helps you stay ahead by tracking resident trust, satisfaction with services, and community life — continuously.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Get clear scores, powerful quotes, and a quarterly report you can share with colleagues or elected officials.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Easy-to-read scores that evolve over time", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Key resident quotes, summarised by AI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Questions tailored to your city's context", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Residents recruited randomly on the platform", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Quarterly PDF reports, ready to share", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "Understand how your community feels before issues grow", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Count", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Desktop or Other", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobile", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Device Types", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Device Type", "app.containers.Admin.earlyAccessLabel": "Early Access", "app.containers.Admin.earlyAccessLabelExplanation": "This is a newly released feature available in Early Access.", "app.containers.Admin.emails.addCampaign": "Create email", "app.containers.Admin.emails.addCampaignTitle": "Направите нову е-пошту", "app.containers.Admin.emails.allParticipantsInProject": "All participants in project", "app.containers.Admin.emails.allUsers": "Регистровани корисници", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Аутоматске е-поруке се аутоматски шаљу и покрећу се радњама корисника. Неке од њих можете искључити за све кориснике ваше платформе. Остале аутоматизоване е-поруке се не могу искључити јер су потребне за правилно функционисање ваше платформе.", "app.containers.Admin.emails.automatedEmails": "Аутоматизоване е-поруке", "app.containers.Admin.emails.automatedEmailsDigest": "Е-пошта ће бити послата само ако има садржаја", "app.containers.Admin.emails.automatedEmailsRecipients": "Корисници који ће примити ову е-пошту", "app.containers.Admin.emails.automatedEmailsTriggers": "Догађај који покреће ову е-пошту", "app.containers.Admin.emails.changeRecipientsButton": "Промените примаоце", "app.containers.Admin.emails.clickOnButtonForExamples": "Кликните на дугме испод да бисте проверили примере ове е-поште на нашој страници за подршку.", "app.containers.Admin.emails.confirmSendHeader": "Пошаљите е-пошту свим корисницима?", "app.containers.Admin.emails.deleteButtonLabel": "Избриши", "app.containers.Admin.emails.draft": "Нацрт", "app.containers.Admin.emails.editButtonLabel": "Уредити", "app.containers.Admin.emails.editCampaignTitle": "Уредите кампању", "app.containers.Admin.emails.editDisabledTooltip2": "Coming soon: This email cannot currently be edited.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Button text", "app.containers.Admin.emails.editRegion_intro_multiloc": "Introduction", "app.containers.Admin.emails.editRegion_subject_multiloc": "Subject", "app.containers.Admin.emails.editRegion_title_multiloc": "Title", "app.containers.Admin.emails.emptyCampaignsDescription": "Easily connect with your participants by sending them emails. Choose who to contact and track your engagement.", "app.containers.Admin.emails.emptyCampaignsHeader": "Send your first email", "app.containers.Admin.emails.failed": "Није успео", "app.containers.Admin.emails.fieldBody": "Порука", "app.containers.Admin.emails.fieldBodyError": "Обезбедите е-поруку за све језике", "app.containers.Admin.emails.fieldReplyTo": "Одговори треба да иду на", "app.containers.Admin.emails.fieldReplyToEmailError": "Наведите адресу е-поште у исправном формату, на пример име@провидер.цом", "app.containers.Admin.emails.fieldReplyToError": "Наведите адресу е-поште", "app.containers.Admin.emails.fieldReplyToTooltip": "Можете одабрати где да шаљете одговоре на своју е-пошту.", "app.containers.Admin.emails.fieldSender": "Од", "app.containers.Admin.emails.fieldSenderError": "Наведите пошиљаоца е-поште", "app.containers.Admin.emails.fieldSenderTooltip": "Можете одлучити кога ће примаоци видети као пошиљаоца е-поште.", "app.containers.Admin.emails.fieldSubject": "Наслов е-поште", "app.containers.Admin.emails.fieldSubjectError": "Наведите тему е-поште за све језике", "app.containers.Admin.emails.fieldSubjectTooltip": "Ово ће бити приказано у наслову е-поште и у прегледу пријемног сандучета корисника. Нека буде јасно и привлачно.", "app.containers.Admin.emails.fieldTo": "До", "app.containers.Admin.emails.fieldToTooltip": "Можете одабрати корисничке групе које ће примити вашу е-пошту", "app.containers.Admin.emails.formSave": "Сачувај као нацрт", "app.containers.Admin.emails.from": "Од:", "app.containers.Admin.emails.groups": "Гру<PERSON>е", "app.containers.Admin.emails.helmetDescription": "Шаљите ручне е-поруке групама корисника и активирајте аутоматизоване кампање", "app.containers.Admin.emails.nameVariablesInfo2": "You can speak directly to citizens using the variables {firstName} {lastName}. E.g. \"Dear {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Е-порука за преглед је послата на вашу адресу е-поште", "app.containers.Admin.emails.previewTitle": "Преглед", "app.containers.Admin.emails.regionMultilocError": "Please provide a value for all languages", "app.containers.Admin.emails.seeEmailHereText": "Чим се пошаље емаил овог типа, моћи ћете да га проверите овде.", "app.containers.Admin.emails.send": "Пошаљи", "app.containers.Admin.emails.sendNowButton": "Пошаљи одмах", "app.containers.Admin.emails.sendTestEmailButton": "Пошаљите ми тест е-поште", "app.containers.Admin.emails.sendTestEmailTooltip": "Када кликнете на ову везу, тест е-поште ће бити послат само на вашу адресу е-поште. Ово вам омогућава да проверите како емаил изгледа у стварном животу.", "app.containers.Admin.emails.senderRecipients": "Пошиљалац и примаоци", "app.containers.Admin.emails.sending": "Слање", "app.containers.Admin.emails.sent": "Послано", "app.containers.Admin.emails.sentToUsers": "Ово су е-поруке које се шаљу корисницима", "app.containers.Admin.emails.subject": "Предмет:", "app.containers.Admin.emails.supportButtonLabel": "Погледајте примере на нашој страници за подршку", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "До:", "app.containers.Admin.emails.toAllUsers": "Да ли желите да пошаљете ову е-пошту свим регистрованим корисницима?", "app.containers.Admin.emails.viewExample": "Поглед", "app.containers.Admin.ideas.import": "Увоз", "app.containers.Admin.inspirationHub.AllProjects": "All projects", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Community monitor", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Document annotation", "app.containers.Admin.inspirationHub.ExternalSurvey": "External survey", "app.containers.Admin.inspirationHub.Filters.Country": "Country", "app.containers.Admin.inspirationHub.Filters.Method": "Method", "app.containers.Admin.inspirationHub.Filters.Search": "Search", "app.containers.Admin.inspirationHub.Filters.Topic": "Topic", "app.containers.Admin.inspirationHub.Filters.population": "Population", "app.containers.Admin.inspirationHub.Highlighted": "Highlighted", "app.containers.Admin.inspirationHub.Ideation": "Ideation", "app.containers.Admin.inspirationHub.Information": "Information", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Please choose a country to see pinned projects", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Country", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "No pinned projects found for this country. Change the country to see pinned projects for other countries", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Change the country to see more pinned projects", "app.containers.Admin.inspirationHub.Poll": "Poll", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Open ended", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Read more...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Phase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "If you don't want your project to be included in the inspiration hub, reach out to your GovSuccess manager.", "app.containers.Admin.inspirationHub.Proposals": "Proposals", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Sort by", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Participants (lowest first)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Participants (highest first)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Start date (oldest first)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Start date (newest first)", "app.containers.Admin.inspirationHub.Survey": "Survey", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Curated list of the best projects around the globe.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Talk to, and learn from, fellow practitioners.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filter by method, city size & country.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Enable Inspiration Hub", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "Learn more", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "The Inspiration Hub connects you to a curated feed of exceptional participation projects on Go Vocal platforms across the world. Learn how other cities run successful projects & talk to other practitioners.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Join a network of pioneering democracy practitioners", "app.containers.Admin.inspirationHub.Volunteering": "Volunteering", "app.containers.Admin.inspirationHub.Voting": "Voting", "app.containers.Admin.inspirationHub.commonGround": "Common ground", "app.containers.Admin.inspirationHub.filters": "filters", "app.containers.Admin.inspirationHub.resetFilters": "Reset filters", "app.containers.Admin.inspirationHub.seemsLike": "Seems like there are no more projects. Try changing the {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Edit campaign fields", "app.containers.Admin.messaging.automated.variablesToolTip": "You can use the following variables in your message:", "app.containers.Admin.messaging.helmetTitle": "Месс<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.messaging.newProjectPhaseModal.alternatively": "Alternatively, you can disable this email campaign for specific phases in the settings of each phase.", "app.containers.Admin.messaging.newProjectPhaseModal.cancel": "Поништити, отказати", "app.containers.Admin.messaging.newProjectPhaseModal.disabledMessage1": "This will also disable the {emailCampaignName} email campaign for all existing project phases. You won't be able to configure this email campaign for any phase as long as this setting is disabled.", "app.containers.Admin.messaging.newProjectPhaseModal.enabledMessage1": "This will not automatically enable the {emailCampaignName} email campaign for existing project phases. Enabling this setting will only allow you to configure this email campaign for each phase.", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOff1": "Are you sure you want to disable the {emailCampaignName} email campaign setting?", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOn1": "Enable the {emailCampaignName} email campaign setting?", "app.containers.Admin.messaging.newProjectPhaseModal.turnOff": "Да, искључи", "app.containers.Admin.messaging.newProjectPhaseModal.turnOn": "Да, укључи", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "This widget shows each user projects <b>based on their follow preferences</b>. This includes projects that they follow, as well as projects where they follow inputs, and projects related to topics or areas that they are interested in.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "This widget will only be shown to the user if there are projects where they can participate. If you see this message, it means that you (the admin) cannot participate in any projects at this moment. This message will not be visible on the real homepage.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "Open to participation", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "This widget will showcase projects where the user can currently <b>take an action to participate</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Title", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "For you", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "This widget will only be shown to the user if there are projects relevant for them based on their follow preferences. If you see this message, it means that you (the admin) are not following anything at the moment. This message will not be visible on the real homepage.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "Followed items", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "Archived", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filter by", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Finished", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Finished and archived", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "No data available", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "This widget shows <b>projects that are finished and/or archived.</b>. \"Finished\" also includes projects that are in the last phase, and where the last phase is a report.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Finished projects", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "You said, we did...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "Provide a name for all languages", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "The project cannot be empty", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Name", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Project", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Resulting URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Save", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Add project", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "The navigation bar will only show projects to which users have access.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "This widget will only be visible on the Homepage when the Community Monitor is accepting responses.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Community Monitor", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Important:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Example of a sentiment survey question", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "No end date", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Press escape to skip carrousel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projects and folders (legacy)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Назив пројекта", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} тренутно ради на", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Button text", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Participate now!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Description", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Please select a project or folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Select project or folder", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Show avatars", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Title", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Starting in {days} days", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Starting in {weeks} weeks", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} days ago", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} weeks ago", "app.containers.Admin.project.Campaigns.campaignFrom": "From:", "app.containers.Admin.project.Campaigns.campaignTo": "To:", "app.containers.Admin.project.Campaigns.customEmails": "Custom emails", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Send out custom emails and check statistics.", "app.containers.Admin.project.Campaigns.noAccess": "We're sorry, but it seems like you don't have access to the emails section", "app.containers.Admin.project.emails.addCampaign": "Create email", "app.containers.Admin.project.emails.addCampaignTitle": "New campaign", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "All {participants} and followers from the project", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "This includes registered users that performed any action in the project. Unregistered or anonymized users are not included.", "app.containers.Admin.project.emails.dateSent": "Date sent", "app.containers.Admin.project.emails.deleteButtonLabel": "Delete", "app.containers.Admin.project.emails.draft": "Draft", "app.containers.Admin.project.emails.editButtonLabel": "Edit", "app.containers.Admin.project.emails.editCampaignTitle": "Edit campaign", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Easily connect with your participants by sending them emails. Choose who to contact and track your engagement.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Send your first email", "app.containers.Admin.project.emails.failed": "Failed", "app.containers.Admin.project.emails.fieldBody": "Email Message", "app.containers.Admin.project.emails.fieldBodyError": "Provide an email message for all languages", "app.containers.Admin.project.emails.fieldReplyTo": "Replies should go to", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Provide an email address", "app.containers.Admin.project.emails.fieldReplyToTooltip": "Choose what email address should receive direct replies from users on your email.", "app.containers.Admin.project.emails.fieldSender": "From", "app.containers.Admin.project.emails.fieldSenderError": "Provide a sender of the email", "app.containers.Admin.project.emails.fieldSenderTooltip": "Choose whom users will see as the sender of the email.", "app.containers.Admin.project.emails.fieldSubject": "Email Subject", "app.containers.Admin.project.emails.fieldSubjectError": "Provide an email subject for all languages", "app.containers.Admin.project.emails.fieldSubjectTooltip": "This will be shown in the subject line of the email and in the user’s inbox overview. Make it clear and engaging.", "app.containers.Admin.project.emails.fieldTo": "To", "app.containers.Admin.project.emails.formSave": "Save as draft", "app.containers.Admin.project.emails.from": "From:", "app.containers.Admin.project.emails.helmetDescription": "Send out manual emails to project participants", "app.containers.Admin.project.emails.infoboxAdminText": "From the Project Messaging tab you can only email all project participants.  To email other participants or subsets of users go to the {link} tab.", "app.containers.Admin.project.emails.infoboxLinkText": "Platform Messaging", "app.containers.Admin.project.emails.infoboxModeratorText": "From the Project Messaging tab you can only email all project participants. Admins can send emails to other participants or subsets of users via the Platform Messaging tab.", "app.containers.Admin.project.emails.message": "Message", "app.containers.Admin.project.emails.nameVariablesInfo2": "You can speak directly to citizens using the variables {firstName} {lastName}. E.g. \"Dear {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "participants", "app.containers.Admin.project.emails.previewSentConfirmation": "A preview email has been sent to your email address", "app.containers.Admin.project.emails.previewTitle": "Preview", "app.containers.Admin.project.emails.projectParticipants": "Project participants", "app.containers.Admin.project.emails.recipients": "Recipients", "app.containers.Admin.project.emails.send": "Send", "app.containers.Admin.project.emails.sendTestEmailButton": "Send a preview", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Send this draft email to the email address with which you are logged in, to check how it looks like in ‘real life’.", "app.containers.Admin.project.emails.senderRecipients": "Sender and recipients", "app.containers.Admin.project.emails.sending": "Sending", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "These are emails sent to users", "app.containers.Admin.project.emails.status": "Статус", "app.containers.Admin.project.emails.subject": "Subject:", "app.containers.Admin.project.emails.to": "To:", "app.containers.Admin.project.messaging.helmetTitle": "Messaging", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Ова слика је део картице фасцикле; картица која резимира фасциклу и приказана је на почетној страници, на пример. За више информација о препорученим резолуцијама слика, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Ова слика је приказана на врху странице фасцикле. За више информација о препорученим резолуцијама слика, {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "посетите наш центар за подршку", "app.containers.Admin.projects.all.askPersonalData3": "Add fields for name and email", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "All questions are shown on the PDF. However, the following are not currently supported for import via FormSync: Images, Tags and File Upload.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "All questions are shown on the PDF. However, the following are not currently supported for import via FormSync: Mapping questions (drop pin, draw route and draw area), ranking questions, matrix questions and file upload questions.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "End of the form", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Start of the form", "app.containers.Admin.projects.all.components.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.draft": "Нацрт", "app.containers.Admin.projects.all.components.manageButtonLabel": "Уредити", "app.containers.Admin.projects.all.copyProjectButton": "Копирај пројекат", "app.containers.Admin.projects.all.copyProjectError": "Дошло је до грешке при копирању овог пројекта, покушајте поново касније.", "app.containers.Admin.projects.all.customiseEnd": "Customise the end of the form.", "app.containers.Admin.projects.all.customiseStart": "Customise the start of the form.", "app.containers.Admin.projects.all.deleteFolderButton1": "Избриши фасциклу", "app.containers.Admin.projects.all.deleteFolderConfirm": "Да ли сте сигурни да желите да избришете овај фолдер? Сви пројекти унутар фасцикле ће такође бити избрисани. Ова радња се не може опозвати.", "app.containers.Admin.projects.all.deleteFolderError": "Дошло је до проблема при уклањању ове фасцикле. Молим вас, покушајте поново.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Избриши пројекат", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Да ли сте сигурни да желите да избришете овај пројекат? Ово се не може поништити.", "app.containers.Admin.projects.all.deleteProjectError": "Дошло је до грешке при брисању овог пројекта, покушајте поново касније.", "app.containers.Admin.projects.all.exportAsPDF1": "Download PDF form", "app.containers.Admin.projects.all.itIsAlsoPossible1": "You can combine online and offline responses. To upload offline responses, go to the 'Input manager' tab of this project, and click 'Import'.", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "You can combine online and offline responses. To upload offline responses, go to the 'Survey' tab of this project, and click 'Import'.", "app.containers.Admin.projects.all.logicNotInPDF": "Survey logic will not be reflected in the downloaded PDF. Paper respondents will see all survey questions.", "app.containers.Admin.projects.all.new.Folders.Filters.search": "Search folders", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "All folders have been loaded", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Folder", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Managers", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# project} other {# projects}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Project start date", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Discoverability", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Hidden", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "Folders", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filter by the current phase participation method", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Information", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Participation method", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Document annotation", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "Common ground", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Survey", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Poll", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Proposals", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Volunteering", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Voting", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Collecting data", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informing", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Not started", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Participation state", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Past", "app.containers.Admin.projects.all.new.Projects.Filters.Search.search": "Search project", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alphabetically (a-z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alphabetically (z-a)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Manager", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Phase starting or ending soon", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created": "Recently created", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "Recently viewed", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Groups", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Visibility", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "Public", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "Add filter", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Clear", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "No more filters to add", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "All projects have been loaded", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "Anyone", "app.containers.Admin.projects.all.new.Projects.Table.archived": "Archived", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Current phase:", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d left", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d to start", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Discoverability:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Draft", "app.containers.Admin.projects.all.new.Projects.Table.end": "End", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Ended", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Ends today", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Groups", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Hidden", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Loading more…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Manager", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}mo left", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}mo to start", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Next phase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "Not assigned", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Phase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Pre-launch", "app.containers.Admin.projects.all.new.Projects.Table.project": "Project", "app.containers.Admin.projects.all.new.Projects.Table.public": "Public", "app.containers.Admin.projects.all.new.Projects.Table.published": "Published", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Scroll down to load more", "app.containers.Admin.projects.all.new.Projects.Table.start": "Start", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Visibility", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Visibility:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} groups", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} managers", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}y left", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y to start", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Current phase: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} days left", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Folder: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "No current phase", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "No end date", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "No phases", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Phase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Phases:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Project", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Start date: {date}", "app.containers.Admin.projects.all.new.folders": "Folders", "app.containers.Admin.projects.all.new.ordering": "Ordering", "app.containers.Admin.projects.all.new.projects": "Projects", "app.containers.Admin.projects.all.new.timeline": "Timeline", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Failed to load timeline.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Project has no end date", "app.containers.Admin.projects.all.new.timeline.project": "Project", "app.containers.Admin.projects.all.notes": "Notes", "app.containers.Admin.projects.all.personalDataExplanation5": "This option will add the first name, last name, and email fields to the exported PDF. Upon uploading the paper form, we will use that data to auto-generate an account for the offline survey respondent.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI Summary", "app.containers.Admin.projects.project.analysis.Comments.comments": "Comments", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Comment summary is available when there are 5 or more comments.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Summarize comments", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {Refresh} =1 {1 new comment} other {# new comments}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI summary", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "This is AI-generated content. It may not be 100% accurate. Please review and cross-reference with the actual inputs for accuracy. Be aware that the accuracy is likely to improve if the number of selected inputs is reduced.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Email notifications only sent to participants", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Hidden", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Not indexed by search engines", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Not visible on the homepage or widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Only accessible via direct URL", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "Public", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "Select how discoverable this project is.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "This project is visible to everyone who has access, and will appear on the homepage and in the widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "This project will be hidden from the wider public, and will only be visible to those who have the link.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Who can find this project?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Open AI analysis", "app.containers.Admin.projects.project.ideas.analysisText2": "Explore AI-powered summaries and view individual submissions.", "app.containers.Admin.projects.project.ideas.importInputs": "Import", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "After creating a report, you can choose to share it with the public once the phase starts.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "Create a more complex page for information sharing", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "Create a report to:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Create a report", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "Create a report for a past phase, or start from scratch.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "This report is not public. To make it public, enable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "This phase has started, but the report is not public yet. To make it public, enable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Start with a phase template", "app.containers.Admin.projects.project.information.ReportTab.report": "Report", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Share results of a past survey or ideation phase", "app.containers.Admin.projects.project.information.ReportTab.visible": "Visible", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "This report will be public as soon as the phase starts. To make it not public, disable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "This report is currently public. To make it not public, disable the \"Visible\" toggle.", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Да ли сте сигурни да желите да избришете овај извештај? Ова радња се не може опозвати.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Додајте у фазу", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "Морате да пристанете на ово пре него што наставите", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "The form can be downloaded here.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Отпремљени образац је креиран са одељком „Лични подаци“.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Језик форме", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Овим се слажем са обрадом ове датотеке користећи Гоогле Цлоуд Форм Парсер", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Import Excel file", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Отпремите датотеку да бисте наставили", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "The template can be downloaded here.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Отпремити", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Upload a completed <b>Excel file</b> (.xlsx). It must use the template provided for this project. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Upload a <b>PDF file of scanned forms</b>. It must use a form printed from this phase. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Use this email for the new user", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Унесите важећу е-пошту да бисте креирали нови налог", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "A new account will be created for the author with these details. This input will be added to it.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "Име", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Презиме", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Please enter an email address and/or a first name and last name to assign this input to an author. Or uncheck the consent box.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Већ постоји налог повезан са овом е-поштом. Овај унос ће му бити додат.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Сагласност корисника (креирајте кориснички налог)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Одобре", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Approve all inputs", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Аутор:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Емаил:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Errors occurred during the import and some inputs have not imported. Please correct the errors and re-import any missing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Неважећи подаци обрасца. Проверите да ли постоје грешке у формулару изнад.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Import Excel file (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Увоз", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Import scanned forms (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Import scanned forms", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Увезени улази", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importing. This process may take a few minutes.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "This input was imported anonymously.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} inputs have been imported and require approval.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} inputs could not be approved. Please check each input for validation issues and confirm individually.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Локал:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Још нема шта да се прегледа. Кликните на „{importFile}“ да бисте увезли ПДФ датотеку која садржи скениране обрасце за унос или Екцел датотеку која садржи улазне податке.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Nothing to review yet. Click \"{importFile}\" to import an Excel file containing inputs.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Улазни", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Страна", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Није могуће приказати увезену датотеку. Преглед увезених датотека доступан је само за увоз ПДФ-а.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "фаза:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Изабрана фаза не може да садржи улазе. Молимо изаберите другу.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Овај пројекат не садржи ниједну фазу која може да садржи идеје.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Молимо изаберите у коју фазу желите да додате ове улазе.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Увозник уноса", "app.containers.Admin.projects.project.participation.comments": "Comments", "app.containers.Admin.projects.project.participation.inputs": "Inputs", "app.containers.Admin.projects.project.participation.participantsTimeline": "Participants timeline", "app.containers.Admin.projects.project.participation.reactions": "Reactions", "app.containers.Admin.projects.project.participation.selectPeriod": "Select period", "app.containers.Admin.projects.project.participation.usersByAge": "Users by age", "app.containers.Admin.projects.project.participation.usersByGender": "Users by gender", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "Required", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Add a question", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "The ability to add or edit user fields at phase level is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} options", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "Field status", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "These questions will be added as the last page of the survey form, because 'Show fields in survey?' has been selected in phase settings.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "No extra questions will be asked.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Optional", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Optional - always enabled because referenced by group", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Remove field", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Required - always enabled because referenced by group", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Authenticate with {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Complete the extra questions below", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Confirm your email", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Data returned from verification method:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "Enter first name, last name, email and password", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "Enter your email", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "How recently should users be verified?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identity verification with {verificationMethod} (based on user group)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "No actions are required to participate", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Use smart groups to restrict participation based on the verified data listed above", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Users must have been verified in the last 30 minutes.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Users must have been verified in the last {days} days.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "In the last 30 days", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "In the last 30 minutes", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "In the last 7 days", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Once is enough", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Verified fields:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} verification", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Account creation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Participants need to create a full account with their name, confirmed email and password.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Participants need to create a full account with their name, email and password.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Authentication", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "Edit", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Email confirmation", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Participants need to confirm their email with a one-time code.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Advanced spam detection", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "This feature helps prevent duplicate survey submissions from logged-out users by analyzing IP addresses and device data. While not as precise as requiring login, it can help to reduce the number of duplicate responses.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Note: On shared networks (like offices or public Wi-Fi), there is a small chance that different users could be flagged as duplicates.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Enable advanced spam detection", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Extra questions asked to participants", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "None", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Anyone can participate without signing up or logging in.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Reset extra questions and groups", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Restrict participation to user group(s)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO verification", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Participants need to verify their identify with {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Open AI analysis", "app.containers.Admin.projects.project.survey.allFiles": "All files", "app.containers.Admin.projects.project.survey.allResponses": "All responses", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Accuracy: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "There was an error generating the AI summary. Please try to regenerate it below.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Open AI analysis", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "<PERSON><PERSON> summaries for this question", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "inputs selected", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Open analysis actions", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} new responses", "app.containers.Admin.projects.project.survey.analysis.regenerate": "Regenerate", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Show AI insights", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "You can summarise a maximum of 30 inputs at a time on your current plan. Talk to your GovSuccess Manager or admin to unlock more.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Изаберите сродна питања за анализу", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "Да ли желите да укључите било која друга сродна питања у своју анализу {question}?", "app.containers.Admin.projects.project.survey.cancel": "Поништити, отказати", "app.containers.Admin.projects.project.survey.consentModalButton": "Настави", "app.containers.Admin.projects.project.survey.consentModalCancel": "Поништити, отказати", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Слажем се да користим ОпенАИ као процесор података за овај пројекат", "app.containers.Admin.projects.project.survey.consentModalText1": "Ако наставите, прихватате да користите ОпенАИ као процесор података за овај пројекат.", "app.containers.Admin.projects.project.survey.consentModalText2": "ОпенАИ АПИ-ји покрећу аутоматизоване текстуалне сажетке и делове искуства аутоматског означавања.", "app.containers.Admin.projects.project.survey.consentModalText3": "ОпенАИ АПИ-ју шаљемо само оно што су корисници написали у својим анкетама, идејама и коментарима, а никада никакве информације са њиховог профила.", "app.containers.Admin.projects.project.survey.consentModalText4": "ОпенАИ неће користити ове податке за даљу обуку својих модела. Више информација о томе како ОпенАИ поступа са приватношћу података можете пронаћи {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "овде", "app.containers.Admin.projects.project.survey.consentModalTitle": "Пре него што наставите", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "You can't enter the analysis before you have edited the form", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Избриши", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Да ли сте сигурни да желите да избришете ову анализу? Ова радња се не може опозвати.", "app.containers.Admin.projects.project.survey.explore": "Explore", "app.containers.Admin.projects.project.survey.followUpResponses": "Follow up responses", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> average", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Export as GeoJSON", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Export the responses to this question as a GeoJSON file. For each GeoJSON Feature, all of the related respondent's survey responses will be listed in that Feature's 'properties' object.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Hide details", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} respondents} one {{respondentCount} respondent} other {{respondentCount} respondents}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "View details", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} choices} one {{numberChoices} choice} other {{numberChoices} choices}}", "app.containers.Admin.projects.project.survey.heatMap": "Heat map", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Learn more about heat maps generated using Esri Smart Mapping.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "The heat map is generated using Esri Smart Mapping. Heat maps are useful when there is a large amount of data points. For fewer points, it may be better to look at only the location points directly. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Heat map view", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Hidden by logic", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "When a user selects this answer logic skips all pages until page {pageNumber} ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "When a user selects this answer logic skips to the survey end ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Logic on this page skips all pages until page {pageNumber} ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Logic on this page skips to the survey end ({numQuestionsSkipped} questions skipped). Click to hide or show the skipped pages and questions.", "app.containers.Admin.projects.project.survey.newAnalysis": "Нова анализа", "app.containers.Admin.projects.project.survey.nextInsight": "Следећи увид", "app.containers.Admin.projects.project.survey.openAnalysis": "Отворена АИ анализа", "app.containers.Admin.projects.project.survey.otherResponses": "Other responses", "app.containers.Admin.projects.project.survey.page": "Page", "app.containers.Admin.projects.project.survey.previousInsight": "Претходни увид", "app.containers.Admin.projects.project.survey.responses": "Responses", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "The number of responses for this page is lower than the total number of survey responses because some respondents will not have seen this page because of logic in the survey.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "The number of responses for this question is lower than the total number of survey responses because some respondents will not have seen this question because of logic in the survey.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Sentiment scale", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Instantly summarise all your responses.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Talk to your data in natural language.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Get references to individual responses from AI generated summaries.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Check our {link} for a full overview.", "app.containers.Admin.projects.project.survey.upsell.button": "Unlock AI analysis", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "support article", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Analyse data faster with AI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Поглед", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Explore AI-powered summaries and view individual submissions.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Select period", "app.containers.Admin.projects.project.traffic.trafficSources": "Traffic sources", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "We have changed the way we collect and display visitor data. As a result, visitor data is more accurate and more types of data are available, while still being GDPR compliant. We only started collecting this new data in November 2024, so before that no data is available.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Visitors timeline", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Phase report", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Add some text about the phase", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Ово је неки текст. Можете га уређивати и форматирати помоћу уређивача на табли са десне стране.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "Учесници", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Резултати пројекта", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Резиме извештаја", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Додајте циљ пројекта, коришћене методе учешћа и исход", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Посетиоци", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "This report contains unsaved changes. Please save before printing.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Title is already taken", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Compared to previous {days} days", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Hide statistics", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Participation rate", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Show comparison with last period", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "You need to select a date range first.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Comments", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Inputs", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Participation", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Show comments", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Show inputs", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Show votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Votes", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demographics", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Registration date range", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Registration field", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Unknown", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Users: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Active", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "Archived", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Finished", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Open-ended", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "Planned", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projects", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Publication status", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Објављено", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "The data for this widget is missing. Reconfigure or delete it to be able to save the report.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Community Monitor Health Score", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Quarter", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "Year", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "No appropriate phases found in this project", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "No phase selected. Please select a phase first.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Нема пројекта", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "No project selected. Please select a project first.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "You cannot duplicate this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "You cannot edit this report because it contains data that you don't have access to.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "Are you sure you want to delete \"{reportName}\"? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Are you sure you want to delete this report? This action cannot be undone.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Избриши", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Duplicate", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "Уредити", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Modified {days, plural, no {# days} one {# day} other {# days}} ago", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "Дошло је до грешке при покушају креирања овог извештаја. Покушајте поново касније.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Почните са празном страном", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Start with a Community Monitor template", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Наслов извештаја", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Create a report", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Customise your report and share it with internal stakeholders or community as a PDF file.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Направите извештај", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "Create your first report", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "No project selected", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Start with a platform template", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Одштампајте у ПДФ", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Почните са шаблоном пројекта", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Quarter {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Шаблон извештаја", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Извештај са овим насловом већ постоји. Изаберите други наслов.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Select quarter", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Select year", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Делите као ПДФ", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Да бисте поделили са свима, одштампајте извештај као ПДФ.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Делите као веб везу", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Ова веб веза је доступна само администраторима.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Објави", "app.containers.Admin.reporting.contactToAccess": "Прављење прилагођеног извештаја је део премијум лиценце. Обратите се свом ГовСуццесс менаџеру да сазнате више о томе.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "All reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Community monitor reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "These are reports are related to the Community Monitor. Reports are automatically generated every quarter.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Направите извештај", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Прилагодите свој извештај и поделите га са интерним заинтересованим странама или заједницом помоћу веб везе.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Your reports will appear here.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Search reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Progress reports", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "These are reports created by your Government Success Manager", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "Your reports", "app.containers.Admin.reporting.deprecated": "DEPRECATED", "app.containers.Admin.reporting.helmetDescription": "Страница за извештавање администратора", "app.containers.Admin.reporting.helmetTitle": "Извештавање", "app.containers.Admin.reporting.printPrepare": "Припрема за штампање...", "app.containers.Admin.reporting.reportBuilder": "Градитељ извештаја", "app.containers.Admin.reporting.reportHeader": "Заглавље извештаја", "app.containers.Admin.reporting.warningBanner3": "Graphs and numbers in this report only update automatically on this page. Save the report to update them on other pages.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "Common ground", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideation", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Information", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Methods Used", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Survey", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Poll", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Previous {days} days: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Proposals", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "External survey", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Volunteering", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Voting", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Chart", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "Table", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "View", "app.containers.Admin.surveyFormTab.downloads": "Downloads", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Duplicate another survey", "app.containers.Admin.surveyFormTab.editSurveyForm": "Edit survey form", "app.containers.Admin.surveyFormTab.inputFormDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required.", "app.containers.Admin.surveyFormTab.surveyForm": "Survey form", "app.containers.Admin.tools.apiTokens.createTokenButton": "Креирајте нови токен", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Поништити, отказати", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "Your token has been created. Please copy the {secret} below and store it safely.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Направите нови токен који ћете користити са нашим јавним АПИ-јем.", "app.containers.Admin.tools.apiTokens.createTokenError": "Наведите име за свој токен", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Креирајте токен", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Important!</b> You can only copy this {secret} once. If you close this window you will not be able to see it again.", "app.containers.Admin.tools.apiTokens.createTokenName": "Име", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Дајте свом токену име", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "Ваш токен је креиран", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Близу", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON>о<PERSON><PERSON><PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Копирано!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Креирајте нови токен", "app.containers.Admin.tools.apiTokens.createdAt": "Цреатед", "app.containers.Admin.tools.apiTokens.delete": "Избриши токен", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Да ли сте сигурни да желите да избришете овај токен?", "app.containers.Admin.tools.apiTokens.description": "Управљајте својим АПИ токенима за наш јавни АПИ. За више информација погледајте нашу {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Последње коришћено", "app.containers.Admin.tools.apiTokens.link": "АПИ документација", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Име", "app.containers.Admin.tools.apiTokens.noTokens": "Још увек немате ниједан токен.", "app.containers.Admin.tools.apiTokens.title": "Јавни АПИ токени", "app.containers.Admin.tools.esriDisabled": "The Esri integration is an add-on feature. Contact your GovSuccess Manager if you want more information on this.", "app.containers.Admin.tools.esriIntegration2": "Esri integration", "app.containers.Admin.tools.esriIntegrationButton": "Enable <PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Connect your Esri account and import data from ArcGIS Online directly into your mapping projects.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logo", "app.containers.Admin.tools.esriKeyInputDescription": "Add your Esri API key to allow importing your map layers from ArcGIS Online in the map tabs in projects.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API key", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Paste API key here", "app.containers.Admin.tools.esriMaps": "Esri Maps", "app.containers.Admin.tools.esriSaveButtonError": "There was an error saving your key, please try again.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API key saved", "app.containers.Admin.tools.esriSaveButtonText": "Save key", "app.containers.Admin.tools.learnMore": "Сазнајте више", "app.containers.Admin.tools.managePublicAPIKeys": "Управљајте АПИ кључевима", "app.containers.Admin.tools.manageWidget": "Управљај виџетом", "app.containers.Admin.tools.manageWorkshops": "Управљајте радионицама", "app.containers.Admin.tools.powerBIAPIImage": "Повер БИ слика", "app.containers.Admin.tools.powerBIDescription": "Користите наше плуг & плаи Повер БИ шаблоне да бисте приступили подацима Go Vocal-а у свом Мицрософт Повер БИ радном простору.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI is not part of your license. Contact your GovSuccess Manager if you want more info on this.", "app.containers.Admin.tools.powerBIDownloadTemplates": "Преузмите шаблоне", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Ако намеравате да користите своје Go Vocal податке у оквиру Повер БИ тока података, овај шаблон ће вам омогућити да подесите нови ток података који се повезује са вашим Go Vocal подацима. Када преузмете овај шаблон, прво морате да пронађете и замените следеће стрингове ##ЦЛИЕНТ_ИД## и ##ЦЛИЕНТ_СЕЦРЕТ## у шаблону својим јавним АПИ акредитивима пре него што их отпремите у ПоверБИ.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Преузмите шаблон тока података", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Шаблон тока података", "app.containers.Admin.tools.powerBITemplates.intro": "Напомена: Да бисте користили било који од ових Повер БИ шаблона, прво морате {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "креирајте скуп акредитива за наш јавни АПИ", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "This template will create a Power BI report based on your Go Vocal data. It will set up all the data connections to your Go Vocal platform, create the data model and some default dashboards. When you open the template in Power BI you will be prompted to enter your public API credentials. You will also need to enter the Base Url for your platform, which is: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Преузмите шаблон за извештавање", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Шаблон извештаја", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Further details about using your Go Vocal data in Power BI can be found in our {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "support article", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Повер БИ шаблони", "app.containers.Admin.tools.powerBITitle": "Повер БИ", "app.containers.Admin.tools.publicAPIDescription": "Управљајте акредитивима да бисте креирали прилагођене интеграције на нашем јавном АПИ-ју.", "app.containers.Admin.tools.publicAPIDisabled1": "The public API is not part of your current license. Contact your GovSuccess Manager if you want more info on this.", "app.containers.Admin.tools.publicAPIImage": "Слика јавног АПИ-ја", "app.containers.Admin.tools.publicAPITitle": "Јавни АПИ приступ", "app.containers.Admin.tools.toolsLabel": "<PERSON>ла<PERSON><PERSON>", "app.containers.Admin.tools.widgetDescription": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.Admin.tools.widgetImage": "Слика виџета", "app.containers.Admin.tools.widgetTitle": "Видгет", "app.containers.Admin.tools.workshopsDescription": "Одржавајте видео састанке уживо, водите симултане групне дискусије и дебате. Прикупите мишљење, гласајте и постигните консензус, баш као што бисте то урадили ван мреже.", "app.containers.Admin.tools.workshopsImage": "Слика радионица", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Радионице за разматрање на мрежи", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "укупно корисника на платформи", "app.containers.AdminPage.DashboardPage._blank": "непознат", "app.containers.AdminPage.DashboardPage.allGroups": "Све групе", "app.containers.AdminPage.DashboardPage.allProjects": "Сви пројекти", "app.containers.AdminPage.DashboardPage.allTime": "Све време", "app.containers.AdminPage.DashboardPage.comments": "Коментари", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Коментари", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Основни скуп података је потребан за мерење репрезентативности корисника платформе.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Долази ускоро", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Тренутно радимо на контролној табли {fieldName} , ускоро ће бити доступна", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# ставка је} other {# ставке су}} скривено у овом графикону. Промените на {tableViewLink} да бисте видели све податке.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} за регистрацију корисника", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} од {total} корисника укључено ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Прика<PERSON><PERSON> још {numberOfHiddenItems}", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Опционо", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Наведите основни скуп података.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Оцена репрезентативности:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Овај резултат одражава колико тачно подаци о корисницима платформе одражавају укупну популацију. Сазнајте више о {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "Потребан", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Пошаљите основне податке", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "приказ табеле", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Тотална популација", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Корисници", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Додајте старосну групу", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} и више", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Старосна група", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Старосна група(е) од {upperBound} и више није укључена.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Стар<PERSON><PERSON>на група {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Старосне групе", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "и преко", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Примените пример груписања", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Избриши све", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "Од", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Подесите старосне групе да буду у складу са вашим основним скупом података.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "Домет", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "сачувати", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "До", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Уредите старосне групе", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Ова ставка неће бити израчуната.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "Видите мање", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Погледајте још {numberOfHiddenItems}...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Основни месец (опционо)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Старосне групе (година рођења)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Долази ускоро", "app.containers.AdminPage.DashboardPage.components.Field.complete": "комплетан", "app.containers.AdminPage.DashboardPage.components.Field.default": "Уобичајено", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Попуните све омогућене опције или онемогућите опције које желите да изоставите са графикона. Најмање једна опција мора бити попуњена.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Непотпун", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Број укупног броја становника", "app.containers.AdminPage.DashboardPage.components.Field.options": "Опције", "app.containers.AdminPage.DashboardPage.components.Field.save": "сачувати", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Сачувано", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Молимо прво {setAgeGroupsLink} да бисте почели да уносите основне податке.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "поставити старосне групе", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Авг. време одговора: {days} дана", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Просечан број дана за одговор", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Дате повратне информације", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Статус уноса", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Уноси по статусу", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Број улаза", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Званично ажурирање", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Проценат инпута", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Време одзива", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Статус", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Статус промењен", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Укупно", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Уредите основне податке", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "како израчунавамо резултате репрезентативности", "app.containers.AdminPage.DashboardPage.continuousType": "Без временске линије", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Кумулативно укупно", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "дан", "app.containers.AdminPage.DashboardPage.false": "лажно", "app.containers.AdminPage.DashboardPage.female": "Женско", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Топ 5 уноса по реакцијама", "app.containers.AdminPage.DashboardPage.fromTo": "од {from} до {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Контролна табла за активности на платформи", "app.containers.AdminPage.DashboardPage.helmetTitle": "Страница администраторске контролне табле", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Изаберите ресурс за приказ по пројекту", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Изаберите ресурс за приказ према ознаци", "app.containers.AdminPage.DashboardPage.inputs1": "Inputs", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Inputs by status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Изаберите групу корисника", "app.containers.AdminPage.DashboardPage.male": "Мушки", "app.containers.AdminPage.DashboardPage.month": "месец дана", "app.containers.AdminPage.DashboardPage.noData": "Нема података за приказ.", "app.containers.AdminPage.DashboardPage.noPhase": "За овај пројекат није креирана фаза", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Број учесника који су објавили доприносе, реаговали или коментарисали.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "Не свиђа ми се", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Свиђа", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Тоталне реакције", "app.containers.AdminPage.DashboardPage.overview.management": "Менаџмент", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Пројекти и учешће", "app.containers.AdminPage.DashboardPage.overview.showLess": "Прикажи мање", "app.containers.AdminPage.DashboardPage.overview.showMore": "Прикажи више", "app.containers.AdminPage.DashboardPage.participants": "Participants", "app.containers.AdminPage.DashboardPage.participationPerProject": "Учешће по пројекту", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Учешће по ознаци", "app.containers.AdminPage.DashboardPage.perPeriod": "По {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Претходних 30 дана", "app.containers.AdminPage.DashboardPage.previous90Days": "Претходних 90 дана", "app.containers.AdminPage.DashboardPage.previousWeek": "Претходна седмица", "app.containers.AdminPage.DashboardPage.previousYear": "Претходна година", "app.containers.AdminPage.DashboardPage.projectType": "Тип пројекта: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Реакције", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Овај основни скуп података је неопходан за израчунавање репрезентативности корисника платформе у поређењу са укупном популацијом.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Наведите основни скуп података.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "Погледајте колико су репрезентативни корисници ваше платформе у поређењу са укупном популацијом - на основу података прикупљених током регистрације корисника. Сазнајте више о {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "Погледајте колико су репрезентативни корисници ваше платформе у поређењу са укупном популацијом - на основу података прикупљених током регистрације корисника.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Репрезентативност заједнице", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Назад на контролну таблу", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Тренутно није подржано ниједно од омогућених поља за регистрацију.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Овде можете приказати/сакрити ставке на контролној табли и унети основне податке. Овде ће се појавити само омогућена поља за {userRegistrationLink}.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Уредите основне податке", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "Регистрација корисника", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Пошаљите основне податке", "app.containers.AdminPage.DashboardPage.resolutionday": "у Данима", "app.containers.AdminPage.DashboardPage.resolutionmonth": "у месецима", "app.containers.AdminPage.DashboardPage.resolutionweek": "у недељама", "app.containers.AdminPage.DashboardPage.selectProject": "Изаберите пројекат", "app.containers.AdminPage.DashboardPage.selectedProject": "филтер тренутног пројекта", "app.containers.AdminPage.DashboardPage.selectedTopic": "тренутни филтер ознака", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Откријте шта се дешава на вашој платформи.", "app.containers.AdminPage.DashboardPage.tabOverview": "Преглед", "app.containers.AdminPage.DashboardPage.tabReports": "Извештаји", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Репрезентативност", "app.containers.AdminPage.DashboardPage.tabUsers": "Корисници", "app.containers.AdminPage.DashboardPage.timelineType": "Временска линија", "app.containers.AdminPage.DashboardPage.titleDashboard": "Кома<PERSON><PERSON>на табла", "app.containers.AdminPage.DashboardPage.total": "Укупно", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Ово {period}", "app.containers.AdminPage.DashboardPage.true": "истина", "app.containers.AdminPage.DashboardPage.unspecified": "неодређено", "app.containers.AdminPage.DashboardPage.users": "Корисници", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Корисници по годинама", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Корисници према географском подручју", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Корисници по полу", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Регистрације", "app.containers.AdminPage.DashboardPage.week": "Недеља", "app.containers.AdminPage.FaviconPage.favicon": "Фавицон", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Савети за избор слике икона за фавикон: изаберите једноставну слику, пошто је приказана величина слике веома мала. Слика треба да буде сачувана као ПНГ и треба да буде квадратна са провидном позадином (или белом позадином ако је потребно). Ваш фавицон треба да се подеси само једном јер ће промене захтевати техничку подршку.", "app.containers.AdminPage.FaviconPage.save": "сачувати", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Нешто није у реду. Молимо покушајте касније.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Успех!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Додати", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Избриши", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Управљачи фасциклама", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Менаџери фасцикли могу уређивати опис фасцикле, креирати нове пројекте унутар фасцикле и имати права управљања пројектима над свим пројектима унутар фасцикле. Не могу да бришу пројекте и немају приступ пројектима који се не налазе у њиховој фасцикли. Можете {projectManagementInfoCenterLink} да пронађете више информација о правима управљања пројектима.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Није пронађено подударање", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "посетите наш центар за помоћ", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Претражите кориснике", "app.containers.AdminPage.FoldersEdit.addToFolder": "Дода<PERSON> у фасциклу", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Избришите ову фасциклу", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.draftStatus": "Нацрт", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Додајте датотеке у ову фасциклу", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Датотеке не би требало да буду веће од 50Мб. Додате датотеке ће бити приказане на страници фасцикле.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "О<PERSON>и<PERSON>и", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "У овој фасцикли нема пројеката. Вратите се на главну картицу Пројекти да бисте креирали и додали пројекте.", "app.containers.AdminPage.FoldersEdit.folderName": "Име фасцикле", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Слика заглавља", "app.containers.AdminPage.FoldersEdit.multilocError": "Сва текстуална поља морају бити попуњена за сваки језик.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "Нема пројеката које можете додати у ову фасциклу.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Слика картице фолдера", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Дозволе", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Пројекти фасцикле", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Подешавања", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Пројекти додати у ову фасциклу", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Пројекти које можете додати у ову фасциклу", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Одаберите да ли је овај фолдер „нацрт“, „објављен“ или „архивиран“.", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Објављено", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Уклони из фасцикле", "app.containers.AdminPage.FoldersEdit.save": "сачувати", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Нешто није у реду. Молимо покушајте касније.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Успех!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Кратак опис", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "приказано на почетној страници", "app.containers.AdminPage.FoldersEdit.statusLabel": "Статус публикације", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Објасните зашто пројекти припадају заједно, дефинишите визуелни идентитет и поделите информације.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Објасните зашто пројекти припадају заједно, дефинишите визуелни идентитет и поделите информације.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Сва текстуална поља морају бити попуњена.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Креирате нову фасциклу", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Подешавања", "app.containers.AdminPage.FoldersEdit.url": "УРЛ", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Поглед директоријум", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Прилагодите слику и текст банера хероја.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Херојски банер", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "Сачувај херој банер", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub is a place where you can find inspiration for your projects by browsing through projects on other platforms.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Уредите услове и одредбе своје платформе и политику приватности. Друге странице, укључујући странице О и Честа питања, могу се уређивати на картици {navigationLink}.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Политике платформе", "app.containers.AdminPage.PagesEdition.privacy-policy": "Правила приватности", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Услови", "app.containers.AdminPage.Project.confirmation.description": "This action cannot be undone.", "app.containers.AdminPage.Project.confirmation.no": "Cancel", "app.containers.AdminPage.Project.confirmation.title": "Are you sure you want to reset all participation data?", "app.containers.AdminPage.Project.confirmation.yes": "Reset all participation data", "app.containers.AdminPage.Project.data.descriptionText1": "Clear ideas, comments, votes, reactions, survey responses, poll responses, volunteers and event registrants. In the case of voting phases, this action will clear the votes but not the options.", "app.containers.AdminPage.Project.data.title": "Clear all participation data from this project", "app.containers.AdminPage.Project.resetParticipationData": "Reset all participation data", "app.containers.AdminPage.Project.settings.accessRights": "Права приступа", "app.containers.AdminPage.Project.settings.back": "Назад", "app.containers.AdminPage.Project.settings.data": "Data", "app.containers.AdminPage.Project.settings.description": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.events": "Догађаји", "app.containers.AdminPage.Project.settings.general": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "Ознаке пројекта", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Списак пројеката на платформи", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Контролна табла за пројекте", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Креирајте нове пројекте или управљајте постојећим пројектима.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Пројекти", "app.containers.AdminPage.ProjectDashboard.published": "Објављено", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Затворите панел са подешавањима", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Центар", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Пуна ширина", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Лево", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Поравнање дугмади", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON>ел тако", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Текст дугмета", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Унесите текст за дугме", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "Примарна", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Тип дугмета", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Секундарни", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "УРЛ дугмета", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Унесите УРЛ за дугме", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Распоред колоне", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Опис почетне странице", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Приказано на картици пројекта на почетној страници.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Приказано на страници пројекта. Јасно опишите о чему се ради у пројекту, шта очекујете од својих корисника и шта они могу да очекују од вас.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Нешто није у реду. Молимо покушајте касније", "app.containers.AdminPage.ProjectDescription.preview": "Преглед", "app.containers.AdminPage.ProjectDescription.save": "сачувати", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.ProjectDescription.saved": "Сачувано!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Одлучите коју поруку желите да дате својој публици. Уредите свој пројекат и обогатите га сликама, видео записима, прилозима датотека,… Ове информације помажу посетиоцима да разумеју о чему се ради у вашем пројекту.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Опис пројекта", "app.containers.AdminPage.ProjectDescription.whiteSpace": "Бели простор", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Укључи ивицу", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "Вертик<PERSON><PERSON><PERSON> висина", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "Велики", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Средње", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "Мала", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Откажи уређивање", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Подразумевана географска ширина централне тачке мапе. Прихвата вредност између -90 и 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Подразумевана дужина средишње тачке мапе. Прихвата вредност између -90 и 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Уредите слој мапе", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Уреди слој", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Нешто није у реду. Молимо покушајте касније", "app.containers.AdminPage.ProjectEdit.MapTab.here": "овде", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Увезите ГеоЈСОН датотеку", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Подразумевана ширина", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Боја слоја", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Све карактеристике у слоју ће бити стилизоване овом бојом. Ова боја ће такође заменити било који постојећи стил у вашој ГеоЈСОН датотеци.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Икона маркера", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Опционо изаберите икону која се приказује у маркерима. Кликните {url} да бисте видели листу икона које можете да изаберете.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Име слоја", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Назив овог слоја је приказан на легенди карте", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "<PERSON><PERSON><PERSON><PERSON> слоја", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Овај текст се приказује као алатка када пређете мишем преко карактеристика слоја на мапи", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Слојеви карте", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Тренутно подржавамо ГеоЈСОН датотеке. Прочитајте {supportArticle} за савете о томе како да конвертујете и стилизујете слојеве карте.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Подразумевана дужина", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Подразумевани центар мапе и зумирање", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "The default center point and zoom level of the map. Manually adjust the values below, or click on the {button} button in the bottom left corner of the map to save the current center point and zoom level of the map as the default values.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Прилагодите приказ мапе, укључујући отпремање и стилизовање слојева карте и подешавање центра мапе и нивоа зумирања.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Конфигурација карте", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Конфигурација мапе се тренутно дели између фаза, не можете креирати различите конфигурације мапе по фази.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Уклоните слој", "app.containers.AdminPage.ProjectEdit.MapTab.save": "сачувати", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Save zoom", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "чланак подршке", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Unnamed layer", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Ниво зумирања мапе", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Подразумевани ниво зумирања мапе. Прихвата вредност између 1 и 17, где је 1 потпуно умањен (цео свет је видљив), а 17 је потпуно увећан (блокови и зграде су видљиви)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Анонимизирајте све корисничке податке", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Сви уноси корисника у анкету биће анонимизовани пре него што буду снимљени", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Корисници ће и даље морати да се придржавају услова за учешће на картици приступа „Права приступа“. Подаци корисничког профила неће бити доступни у извозу података анкете.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "If you enable this option, user registration fields will be shown as the last page in the survey instead of as part of the signup process.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demographic fields in survey form", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Show demographic fields in survey?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "Read more about how auto-sharing works in this article.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Auto-share results", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Voting results are shared on the platform and via email to participants when the phase ends. This ensures transparency by default.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Result sharing", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Додајте опцију одговора", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Додајте питање у анкети", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Поништити, отказати", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Поништити, отказати", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Поништити, отказати", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Избриши", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Избриши", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Измени опцију одговора", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Сачувајте опције одговора", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Измените опције одговора", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Уреди питање", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Извезите резултате анкете", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Максима<PERSON><PERSON>н број избора је већи од броја опција", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Вишеструки избор", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "Нема опција", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Сва питања морају имати избор одговора", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Само једна опција", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Испитаници у анкети имају само један избор", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Управљајте опцијама одговора за: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "полл_екпорт", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Овде можете креирати питања за анкету, поставити изборе одговора за учеснике да бирају за свако питање, одлучити да ли желите да учесници могу да изаберу само један одговор (један избор) или вишеструки избор одговора (вишеструки избор) и извезу резултати анкете. Можете креирати више питања за анкету у оквиру једне анкете.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "сачувати", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "сачувати", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "сачувати", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Један избор", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Подешавања анкете и резултати", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Погрешан максимум", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Дајте повратне информације, додајте ознаке или копирајте постове у следећу фазу пројекта.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Manage proposals, give feedback and assign topics.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Менаџер уноса", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Result sharing is turned off.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Voting results won't be shared at the end of the phase unless you modify it in the phase setup.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "These results will be automatically shared once the phase ends. Modify the end date of this phase to change when the results are shared.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Извези резултате анкете (.клск)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Results", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Овде можете преузети резултате анкете типа Типеформ у оквиру овог пројекта као Екцел датотеку.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Survey form", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Резултати анкете", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Анкета", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Консултујте одговоре на анкету", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Додајте узрок", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Јеси ли сигуран?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Користите ово да објасните шта се од волонтера тражи и шта могу да очекују.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Није могуће сачувати јер образац садржи грешке.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Слика", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Избриши", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Уредити", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Узрок је акција или активност за коју учесници могу волонтирати.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Уредите узрок", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Додај опис", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Додајте наслов", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Извоз волонтера", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Узрок је акција или активност за коју учесници могу волонтирати.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Нови узрок", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "сачувати", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Овде можете подесити разлоге за које корисници могу да волонтирају и преузимају волонтере.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Волонтирање", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {нема волонтера} one {#добровољац} other {# волонтера}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "буџетска издвајања", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Доделите буџет опцијама и замолите учеснике да изаберу своје жељене опције које се уклапају у укупни буџет.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Буџетска издвајања", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Омогућавање корисницима да коментаришу може утицати на процес гласања.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "Credit", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Подразумевани приказ опција", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Акције за кориснике", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "Изаберите које додатне радње корисници могу да предузму.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Фиксни износ", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "Ако оставите празно, подразумевано ће бити „гласање“.", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "Сазнајте више о томе када да користите <b> {voteTypeDescription} </b> у нашем {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Максима<PERSON>ан број гласова по опцији", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Максимални број гласова", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Можете ограничити укупан број гласова које корисник може дати (са максимално једним гласом по опцији).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "више гласова по опцији", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Корисницима се даје одређена количина токена за дистрибуцију између опција", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "Више гласова по опцији", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Број гласова по кориснику", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Преглед анализе опција", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Опције за гласање", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "Point", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "један глас по опцији", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Корисници могу изабрати да одобре било коју од опција", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Један глас по опцији", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Неограничено", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Како треба да се зове гласање?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "Нпр. жетони, поени, карбонски кредити...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Нпр. жето<PERSON>, тачка, кредит за угљеник...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Vote", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Сваки метод гласања има различите предконфигурације", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "На<PERSON><PERSON>н гласања", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Начин гласања одређује правила како корисници гласају", "app.containers.AdminPage.ProjectEdit.addNewInput": "Додајте унос", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "You can add your project to an folder now, or do it later in the project settings", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Ознаке пројекта", "app.containers.AdminPage.ProjectEdit.altText": "Alt text", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Анонимно гласање", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Када је омогућено, немогуће је видети ко је за шта гласао. Корисницима је и даље потребан налог и могу да гласају само једном.", "app.containers.AdminPage.ProjectEdit.approved": "Approved", "app.containers.AdminPage.ProjectEdit.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Archived projects are still visible, but do not allow further participation", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Ова област се не може избрисати јер се користи за приказивање пројеката на следећим прилагођеним страницама. Мораћете да прекинете везу између области и странице или да избришете страницу пре него што избришете област.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Све области", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Пројекат ће се приказати на сваком филтеру подручја.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Филтер подручја", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Пројекти се могу филтрирати на почетној страници користећи области. Области се могу подесити на {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "овде", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "Нема одређеног подручја", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Пројекат се неће приказати приликом филтрирања по области.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Избор", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Пројекат ће се приказати на одабраним филтерима подручја.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Картице", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Most discussed", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Add survey content", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Submissions to this survey have started to come in. Changes to the survey may result in data loss and incomplete data in the exported files.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Survey successfully saved", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "View survey", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Изаберите метод гласања и нека корисници дају приоритет између неколико различитих опција.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Спроведите гласање или вежбу одређивања приоритета", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Креирајте пројекат из шаблона", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Уградите екстерну анкету", "app.containers.AdminPage.ProjectEdit.createInput": "Add new input", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Направите анкету на платформи", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "Поставите анкету без напуштања наше платформе.", "app.containers.AdminPage.ProjectEdit.createPoll": "Направите анкету", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Поставите упитник са више одговора.", "app.containers.AdminPage.ProjectEdit.createProject": "New project", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Уградите Типеформ, Го<PERSON><PERSON><PERSON><PERSON> Форм, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Сурве<PERSON>К<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ри<PERSON><PERSON>, СмартСурвеи, Снап Сурвеи или Мицрософт Формс анкету.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Можете подесити подразумевани редослед да се објаве приказују на главној страници пројекта.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Сортирање", "app.containers.AdminPage.ProjectEdit.departments": "Одељења", "app.containers.AdminPage.ProjectEdit.descriptionTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "This will enable or disable disliking, but liking will still be enabled. We recommend leaving this disabled unless you are carrying out an option analysis.", "app.containers.AdminPage.ProjectEdit.disabled": "Онемогућено", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Број несвиђања по учеснику", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Омогући несвиђање", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Прикупите повратне информације о документу", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Уградите интерактивни ПДФ и прикупите коментаре и повратне информације уз Конвеио.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Онемогућено", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Омогућено", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Draft projects are hidden for all people except admins and assigned project managers.", "app.containers.AdminPage.ProjectEdit.draft": "Нацрт", "app.containers.AdminPage.ProjectEdit.draftStatus": "Нацрт", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Уредити", "app.containers.AdminPage.ProjectEdit.enabled": "Омогућено", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "Изаберите које партиципативне радње корисници могу да предузму.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Ана<PERSON><PERSON>з<PERSON><PERSON><PERSON>р", "app.containers.AdminPage.ProjectEdit.eventsTab": "Догађаји", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Прилози (макс. 50МБ)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Датотеке не би требало да буду веће од 50Мб. Додате датотеке ће бити приказане на страници са информацијама о пројекту.", "app.containers.AdminPage.ProjectEdit.filesTab": "Files", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Пронађите добровољце", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Замолите учеснике да волонтирају за активности и циљеве.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "As a folder manager, you can choose a folder when creating the project, but only an admin can change it afterward", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Folder card image alternative text", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Изаберите фасциклу у коју ћете додати овај пројекат.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Прилагођени садржај", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Почеле су да стижу пријаве за овај образац. Промене обрасца могу довести до губитка података и непотпуних података у извезеним датотекама.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Образац је успешно сачуван", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Крај анкете", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Из шаблона", "app.containers.AdminPage.ProjectEdit.generalTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Гоогле Формс", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Header image alt text", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Слика заглавља", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "NEW", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "Обезбедите информације корисницима или користите алатку за прављење извештаја да поделите резултате о прошлим фазама.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Делите информације или резултате", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Прикупите унос и повратне информације", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Креирајте или прикупљајте уносе, реакције и/или коментаре. Изаберите између различитих типова уноса: прикупљање идеја, анализа опција, питања и одговора, идентификација проблема и још много тога.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Ко је одговоран за обраду постова?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Сви нови доприноси у овом пројекту биће додељени овој особи. Уступник се може променити у {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Коментаришући постове", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Формулар за унос", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "менаџер уноса", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Менаџер уноса", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Слање постова", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Реаговање на инпуте", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Подразумевани приказ", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Изаберите подразумевани приказ за приказ уноса: картице у приказу мреже или игле на мапи. Учесници могу ручно да прелазе између два приказа.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Inspiration hub", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Угради Конвеио УРЛ", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Number of likes per participant", "app.containers.AdminPage.ProjectEdit.limited": "Огра<PERSON><PERSON><PERSON><PERSON>н", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Учитајте више шаблона", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Мапа", "app.containers.AdminPage.ProjectEdit.mapTab": "Мапа", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Максимално несвиђања", "app.containers.AdminPage.ProjectEdit.maxLikes": "Максимум лајкова", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Максим<PERSON><PERSON><PERSON>н број гласова по опцији мора бити мањи или једнак укупном броју гласова", "app.containers.AdminPage.ProjectEdit.maximum": "Максимум", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Учесници не могу прекорачити овај буџет приликом подношења своје корпе.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Мицрософт Формс", "app.containers.AdminPage.ProjectEdit.minimum": "Мини<PERSON>ум", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Захтевајте од учесника да испуне минимални буџет да би послали своју корпу (унесите '0' ако не желите да поставите минимум).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "посетите наш центар за помоћ", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Who are the project managers?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Детаљније", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Need inspiration? Explore similar projects from other cities in the {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Додајте допринос", "app.containers.AdminPage.ProjectEdit.newIdea": "Нова идеја", "app.containers.AdminPage.ProjectEdit.newInitiative": "Add an initiative", "app.containers.AdminPage.ProjectEdit.newIssue": "Додајте проблем", "app.containers.AdminPage.ProjectEdit.newOption": "Додајте опцију", "app.containers.AdminPage.ProjectEdit.newPetition": "Add a petition", "app.containers.AdminPage.ProjectEdit.newProject": "Нови пројекат", "app.containers.AdminPage.ProjectEdit.newProposal": "Add a proposal", "app.containers.AdminPage.ProjectEdit.newQuestion": "Додајте питање", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Најновији", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Није важећи износ", "app.containers.AdminPage.ProjectEdit.noFolder": "Нема фолдера", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— No folder —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Није пронађен ниједан шаблон", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Унесите назив пројекта", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Није важећи број", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Најстарији", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Видљиво само администратору", "app.containers.AdminPage.ProjectEdit.optionNo": "Не", "app.containers.AdminPage.ProjectEdit.optionYes": "Да (изаберите фолдер)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Нивои учешћа", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Шта желиш да радиш?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Одаберите како корисници могу да учествују.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "Можете да одредите ко може да предузме сваку радњу и да поставите додатна питања учесницима како бисте прикупили више информација.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Захтеви и питања за учеснике", "app.containers.AdminPage.ProjectEdit.pendingReview": "Pending approval", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Права приступа", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Access rights", "app.containers.AdminPage.ProjectEdit.pollTab": "Анкета", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Највише реакција", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Слика картице пројекта", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Ова слика је део картице пројекта; картица која резимира пројекат и приказана је на почетној страници на пример.\n\n    За више информација о препорученим резолуцијама слика, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Folder", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Ова слика је приказана на врху странице пројекта.\n\n    За више информација о препорученим резолуцијама слика, {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Project card image alternative text", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Provide a short description of the image for visually impaired users. This helps screen readers convey what the image is about.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Пројектни менаџмент", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Менаџери пројеката могу уређивати пројекте, управљати објавама и е-поштом учесницима. Можете {moderationInfoCenterLink} да бисте пронашли више информација о правима која су додељена менаџерима пројекта.", "app.containers.AdminPage.ProjectEdit.projectName": "Име пројекта", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Тип пројекта", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Пројекти са временском линијом имају јасан почетак и крај и могу имати различите фазе. Пројекти без временског оквира су континуирани.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Тип пројекта се не може касније променити.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Можете подесити да пројекат буде невидљив одређеним корисницима.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Видљивост пројекта", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Looking for the project status? Now you can change it at any time directly from the project page header.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Published projects are visible to everyone or a group subset if selected.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Објављено", "app.containers.AdminPage.ProjectEdit.purposes": "сврхе", "app.containers.AdminPage.ProjectEdit.qualtrics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Насумично", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Reset participation data", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Дошло је до грешке при чувању ваших података. Молим вас, покушајте поново.", "app.containers.AdminPage.ProjectEdit.saveProject": "сачувати", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Успех!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Ваш образац је сачуван!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Претражите шаблоне", "app.containers.AdminPage.ProjectEdit.selectGroups": "Изаберите групу(е)", "app.containers.AdminPage.ProjectEdit.setup": "Подесити", "app.containers.AdminPage.ProjectEdit.shareInformation": "Делите информације", "app.containers.AdminPage.ProjectEdit.smart_survey": "СмартСурвеи", "app.containers.AdminPage.ProjectEdit.snap_survey": "Снап Сурвеи", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Подесите и персонализујте свој пројекат.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "посетите наш центар за подршку", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Додајте садржај анкете", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Поништити, отказати", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# избора} one {# избора} other {# избора}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "Да, желим да одем", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "Edit", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Почели су да пристижу пријаве за ову анкету. Промене у анкети могу довести до губитка података и непотпуних података у извезеним датотекама.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "File upload", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Go back", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Import", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Import", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "AI summaries for short answer, long answer, and sentiment scale follow up questions can be accessed from the AI tab in the left sidebar.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Лин<PERSON><PERSON><PERSON>на скала", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrix", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Дуг одговор", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Вишеструки избор - изаберите много", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Image choice - choose many", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "New submission", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Још нема одговора на анкету", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Отворено за одговоре", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Open for responses", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Опционо", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "If no logic is added, the survey will follow its normal flow. If both the page and its questions have logic, the question logic will take precedence. Ensure this aligns with your intended survey flow. For more information, visit {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "Location", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Јесте ли сигурни да желите да одете?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Ваше тренутне промене неће бити сачуване.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "Ranking", "app.containers.AdminPage.ProjectEdit.survey.rating": "Rating", "app.containers.AdminPage.ProjectEdit.survey.required2": "Потребан", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {responses} one {response} other {responses}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# responses} one {# response} other {# responses}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Вишеструки избор - изаберите један", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Sentiment linear scale", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Esri shapefile upload", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Анкета је успешно сачувана", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Анкета", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Одговори на анкету", "app.containers.AdminPage.ProjectEdit.survey.text2": "Кратак одговор", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Укупно {count} одговора", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "Прикажи анкету", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "View", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Угради УРЛ", "app.containers.AdminPage.ProjectEdit.surveyService": "Услуга", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Можете пронаћи више информација о томе како да уградите анкету {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "овде", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Опитни мајмун", "app.containers.AdminPage.ProjectEdit.survey_xact": "СурвеиКсацт", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Ова ознака се не може избрисати јер се користи за приказивање пројеката на следећим прилагођеним страницама. \nМораћете да прекинете везу између ознаке и странице или да избришете страницу пре него што можете да избришете ознаку.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Општа подешавања за пројекат", "app.containers.AdminPage.ProjectEdit.titleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Изаберите наслов који је кратак, занимљив и јасан. Биће приказан у падајућем прегледу и на пројектним картицама на почетној страници.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Ознаке", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Изаберите {topicsCopy} за овај пројекат. Корисници могу да их користе за филтрирање пројеката према.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Укупан буџет", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "У тренду", "app.containers.AdminPage.ProjectEdit.typeform": "Типеформ", "app.containers.AdminPage.ProjectEdit.unassigned": "Унассигнед", "app.containers.AdminPage.ProjectEdit.unlimited": "Неограничено", "app.containers.AdminPage.ProjectEdit.url": "УРЛ", "app.containers.AdminPage.ProjectEdit.useTemplate": "Користите шаблон", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Погледај пројекат", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Волонтирање", "app.containers.AdminPage.ProjectEdit.voteTermError": "Услови гласања морају бити наведени за све локације", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# група може да види} one {# група може да види} other {# група може да види}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Додајте догађај", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Додатне Информације", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Адреса 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Адреса локације догађаја", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Адреса 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Нпр. стан, апар<PERSON><PERSON><PERSON><PERSON>, зграда", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Додатне информације о адреси које могу помоћи у идентификацији локације, као што су назив зграде, број спрата итд.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Погледајте чланак о подршци", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Екстерна веза", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Додајте везу ка спољној УРЛ адреси (нпр. услуга догађаја или веб локација за продају карата). Ово подешавање ће заменити подразумевано понашање дугмета за присуство.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Текст прилагођеног дугмета", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Set the button text to a value other than \"Register\" when an external URL is set.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Почетак", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "К<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Избриши", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Да ли сте сигурни да желите да избришете овај догађај? Не постоји начин да се ово поништи!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Опис догађаја", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Уредити", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Уреди догађај", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "To email registrants directly from the platform, admins must create a user group in the {userTabLink} tab. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Датуми догађаја", "app.containers.AdminPage.ProjectEvents.eventImage": "Слика догађаја", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Event image alternative text", "app.containers.AdminPage.ProjectEvents.eventLocation": "Локација догађаја", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Export registrants", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Прилози (макс. 50МБ)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Прилози су приказани испод описа догађаја.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Локација", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Maximum registrants", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Направите нови догађај", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Линк за онлајн догађај", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "Ако је ваш догађај онлајн, додајте линк до њега овде.", "app.containers.AdminPage.ProjectEvents.preview": "Преглед", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Прецизирајте локацију на мапи", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Прецизирајте локацију на мапи", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "You can refine where your event location marker is shown by clicking on the map below.", "app.containers.AdminPage.ProjectEvents.register": "Register", "app.containers.AdminPage.ProjectEvents.registerButton": "Register button", "app.containers.AdminPage.ProjectEvents.registrant": "registrant", "app.containers.AdminPage.ProjectEvents.registrants": "registrants", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Registration limit", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "сачувати", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Нисмо могли да сачувамо ваше промене, покушајте поново.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Успех!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Потражите локацију", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Повежите предстојеће догађаје са овим пројектима и прикажите их на страници догађаја пројекта.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Наслов и датуми", "app.containers.AdminPage.ProjectEvents.titleEvents": "Пројектни догађаји", "app.containers.AdminPage.ProjectEvents.titleLabel": "Назив догађаја", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Link the button to an external URL", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "By default, the in-platform event register button will be shown allowing users to register for an event. You can change this to link to an external URL instead.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Limit the number of event registrants", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Set a maximum number of event registrants. If the limit is reached, no further registrations will be accepted.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/админ/усерс", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Корисници", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Add files to your project", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Attach files from this list to your project, phases, and events.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Add files as context to Sensemaking", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Add files to your Sensemaking project to provide context and insights.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Coming soon", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Sync surveys, upload interviews, and let AI connect the dots across your data.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Upload any file", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Use AI to analyze files", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Process transcripts, etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "Add files", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "AI-powered insights", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analyze uploaded files to surface key topics.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Allow advanced analytics of these files using AI processing.", "app.containers.AdminPage.ProjectFiles.askButton": "Ask", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Category", "app.containers.AdminPage.ProjectFiles.chooseFiles": "Choose files", "app.containers.AdminPage.ProjectFiles.close": "Close", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Confirm and upload", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Are you sure you want to delete this file?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Could not load markdown file.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Could not load CSV preview.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "Maximum 50 rows are shown in CSV previews.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV file is too large to preview.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Delete file", "app.containers.AdminPage.ProjectFiles.description": "Description", "app.containers.AdminPage.ProjectFiles.done": "Done", "app.containers.AdminPage.ProjectFiles.downloadFile": "Download file", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Download full file", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "Drag and drop any files here or", "app.containers.AdminPage.ProjectFiles.editFile": "Edit file", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Description", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "File name may not contain a dot.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "File Name", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "File name is required.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Download file", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Preview", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "This file will not be uploaded, as it exceeds the maximum limit of 50 MB.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "All files uploaded successfully", "app.containers.AdminPage.ProjectFiles.info_sheet": "Information", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "E.g. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Audio interviews, Town Hall recordings", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "E.g. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Reports, informational documents", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "E.g. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Images", "app.containers.AdminPage.ProjectFiles.interview": "Interview", "app.containers.AdminPage.ProjectFiles.maxFilesError": "You can only upload a maximum of {maxFiles} files at a time.", "app.containers.AdminPage.ProjectFiles.meeting": "Meeting", "app.containers.AdminPage.ProjectFiles.noFilesFound": "No files found.", "app.containers.AdminPage.ProjectFiles.other": "Other", "app.containers.AdminPage.ProjectFiles.policy": "Policy", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Preview not yet supported for this file type.", "app.containers.AdminPage.ProjectFiles.report": "Report", "app.containers.AdminPage.ProjectFiles.retryUpload": "Retry upload", "app.containers.AdminPage.ProjectFiles.save": "Save", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "File updated successfully.", "app.containers.AdminPage.ProjectFiles.searchFiles": "Search files", "app.containers.AdminPage.ProjectFiles.selectFileType": "File type", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategic plan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "You can only upload a maximum of {maxFiles} files at a time.", "app.containers.AdminPage.ProjectFiles.unknown": "Unknown", "app.containers.AdminPage.ProjectFiles.upload": "Upload", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# file} other {# files}} uploaded successfully, {numberOfErrors, plural, one {# error} other {# errors}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "View file", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Скупи сва поља", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Опис поља", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Уредите образац за унос", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Омогућено", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Укључите ово поље.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Нешто није у реду. Молимо покушајте касније", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Проширите сва поља", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Формулар за унос", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Наведите које информације треба дати, додајте кратке описе или упутства за усмеравање одговора учесника и наведите да ли је свако поље опционо или обавезно.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Наведите које информације треба дати, додајте кратке описе или упутства за усмеравање одговора учесника и наведите да ли је свако поље опционо или обавезно", "app.containers.AdminPage.ProjectIdeaForm.required": "Потребан", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Захтевајте да се ово поље попуни.", "app.containers.AdminPage.ProjectIdeaForm.save": "сачувати", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Сачувано!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "Прикажи образац", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Аутоматизоване е-поруке", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Можете да конфигуришете е-поруке које се активирају на нивоу фазе", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Датуми", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Take the survey", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Survey", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Да ли сте сигурни да желите да избришете ову фазу?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Опис фазе", "app.containers.AdminPage.ProjectTimeline.disabledProjectPhaseEmailMessage": "Ова опција је тренутно искључена за све пројекте на страници {automatedEmailsLink} . Као резултат тога, нећете моћи појединачно да укључите ову поставку за ову фазу.", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Едит <PERSON>е", "app.containers.AdminPage.ProjectTimeline.endDate": "Крајњи датум", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Крајњи датум", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Прилози (макс. 50МБ)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Направите нову фазу", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Ова фаза нема унапред дефинисан датум завршетка.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Дељење резултата неких метода (као што су резултати гласања) неће бити покренуто док се не изабере крајњи датум.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "Чим додате фазу након ове, она ће овој фази додати и датум завршетка.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Ако не изаберете крајњи датум за ово, то значи да:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Preview", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Save changes", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Дошло је до грешке при слању обрасца, покушајте поново.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Сачувано!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.ProjectTimeline.startDate": "Датум почетка", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Датум почетка", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Survey title", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Назив фазе", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Отпремите прилоге", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_репартитион", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Терминологија (филтер за почетну страницу)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Како треба звати ознаке у филтеру на насловној страни? Нпр. ознаке, категорије, одељења, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Ознаке се могу конфигурисати {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "овде", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Термин за једну ознаку (једнина)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "таг", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Термин за више ознака (множина)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "ознаке", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Додај поље", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Додајте ново поље за регистрацију", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Додај опцију", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Формат одговора", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Наведите формат одговора", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Опција одговора", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Обезбедите опцију одговора за све језике", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Сачувај опцију одговора", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Опција одговора је успешно сачувана", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Избори одговора", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Поља", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Превуците и отпустите поља да бисте одредили редослед којим се појављују у обрасцу за регистрацију.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Подразумевано поље", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Избриши", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Опциони текст приказан испод назива поља на обрасцу за регистрацију.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Избори одговора за место становања могу се поставити у {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Уредити", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Измени опцију одговора", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Име поља", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Наведите назив поља за све језике", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Подешавања поља", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Да-не (поље за потврду)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Датум", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Дуг одговор", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Вишеструки избор (изаберите више)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Нумеричка вредност", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Вишеструки избор (изаберите један)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Кратак одговор", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Картица Географске области", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Скривено поље", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Да ли је одговор на ово поље обавезан?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Прилагођена област", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Додајте опцију одговора", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Поништити, отказати", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Избриши", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "Да ли сте сигурни да желите да избришете ову опцију одговора на питање за регистрацију? Сви записи на које су одређени корисници одговорили овом опцијом биће трајно избрисани. Ова радња се не може опозвати.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Да ли сте сигурни да желите да избришете ово питање за регистрацију? Сви одговори које су корисници дали на ово питање биће трајно обрисани и више се неће постављати у пројектима или предлозима. Ова радња се не може опозвати.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Потребан", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "Сачувај поље", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Поље је успешно сачувано", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Две колоне", "app.containers.AdminPage.SettingsPage.addAreaButton": "Додајте географску област", "app.containers.AdminPage.SettingsPage.addTopicButton": "Дода<PERSON> ознаку", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Animal - eg Elephant Cat", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "User - eg User 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Select which admins will receive notifications to approve projects. Folder Managers are by default approvers for all projects within their folders.", "app.containers.AdminPage.SettingsPage.approvalSave": "Save", "app.containers.AdminPage.SettingsPage.approvalTitle": "Project approval settings", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Да ли сте сигурни да желите да избришете ову област?", "app.containers.AdminPage.SettingsPage.areaTerm": "Израз за једну област (једнина)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "области", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Избриши", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Уредити", "app.containers.AdminPage.SettingsPage.areasTerm": "Израз за више области (множина)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "области", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Изаберите најмање један језик.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Ава<PERSON><PERSON><PERSON>и", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Прикажи аватаре", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Нерегистрованим посетиоцима показати профилне слике учесника и њихов број", "app.containers.AdminPage.SettingsPage.bannerHeader": "Текст заглавља", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Текст заглавља за нерегистроване посетиоце", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Текст подзаглавља за нерегистроване посетиоце", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Текст подзаглавља", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Текст банера", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Прикажи преглед за", "app.containers.AdminPage.SettingsPage.brandingDescription": "Додајте свој лого и поставите боје платформе.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Брендирање платформе", "app.containers.AdminPage.SettingsPage.cancel": "Поништити, отказати", "app.containers.AdminPage.SettingsPage.chooseLayout": "Лаи<PERSON><PERSON>т", "app.containers.AdminPage.SettingsPage.color_primary": "Примарна боја", "app.containers.AdminPage.SettingsPage.color_secondary": "Секундарна боја", "app.containers.AdminPage.SettingsPage.color_text": "Боја текста", "app.containers.AdminPage.SettingsPage.colorsTitle": "Боје", "app.containers.AdminPage.SettingsPage.confirmHeader": "Да ли сте сигурни да желите да избришете ову ознаку?", "app.containers.AdminPage.SettingsPage.contentModeration": "Модерирање садржаја", "app.containers.AdminPage.SettingsPage.ctaHeader": "Дугмад", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Прилагођено заглавље странице | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Текст дугмета", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Веза са дугметом", "app.containers.AdminPage.SettingsPage.defaultTopic": "Подразумевана ознака", "app.containers.AdminPage.SettingsPage.delete": "Избриши", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Избриши", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Ово ће избрисати ознаку из свих постојећих постова. Ова промена ће се односити на све пројекте.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Додајте и избришите ознаке које желите да користите на својој платформи за категоризацију постова. Можете додати ознаке одређеним пројектима у {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Десктоп", "app.containers.AdminPage.SettingsPage.editFormTitle": "Уреди област", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Уредити", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Уреди ознаку", "app.containers.AdminPage.SettingsPage.fieldDescription": "Опис подручја", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Овај опис је само за интерну сарадњу и не приказује се корисницима.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Назив области", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Наведите назив области за све језике", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Име које одаберете за сваку област може се користити као опција поља за регистрацију и за филтрирање пројеката на почетној страници.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "Сачувај ознаку", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Означи име", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Наведите назив ознаке за све језике", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Име које одаберете за сваку ознаку биће видљиво корисницима платформе", "app.containers.AdminPage.SettingsPage.fixedRatio": "Фиксни однос", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Овај тип банера најбоље функционише са сликама које не би требало да се изрезују, као што су слике са текстом, логотипом или специфичним елементима који су кључни за ваше грађане. Овај банер се замењује пуним оквиром у примарној боји када су корисници пријављени. Ову боју можете да подесите у општим подешавањима. Више информација о препорученој употреби слике можете пронаћи на нашем {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "база знања", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Пуна ширина", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Овај банер се протеже по целој ширини за одличан визуелни ефекат. Слика ће покушати да покрије што је могуће више простора, због чега није увек видљива у сваком тренутку. Можете комбиновати овај банер са преклопом било које боје. Више информација о препорученој употреби слике можете пронаћи на нашем {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "база знања", "app.containers.AdminPage.SettingsPage.header": "Банер за почетну страницу", "app.containers.AdminPage.SettingsPage.headerDescription": "Прилагодите слику и текст банера почетне странице.", "app.containers.AdminPage.SettingsPage.header_bg": "Слика банера", "app.containers.AdminPage.SettingsPage.helmetDescription": "Страница са подешавањима администратора", "app.containers.AdminPage.SettingsPage.helmetTitle": "Страница са подешавањима администратора", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Додајте свој садржај у прилагодљиви одељак на дну почетне странице.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Заглавље почетне странице | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Боја преклапања слике", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Прозирност преклапања слике", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Откријте неприкладан садржај", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Аутоматско откривање неприкладног садржаја објављеног на платформи.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "While this feature is enabled, input, proposals and comments posted by participants will be automatically reviewed. Posts flagged as potentially containing inappropriate content will not be blocked, but will be highlighted for review on the {linkToActivityPage} page.", "app.containers.AdminPage.SettingsPage.languages": "Језици", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Можете одабрати више језика на којима желите да понудите своју платформу корисницима. Мораћете да креирате садржај за сваки изабрани језик.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Активност", "app.containers.AdminPage.SettingsPage.logo": "Лого", "app.containers.AdminPage.SettingsPage.noHeader": "Отпремите слику заглавља", "app.containers.AdminPage.SettingsPage.no_button": "Нема дугмета", "app.containers.AdminPage.SettingsPage.organizationName": "Назив града или организације", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Наведите назив организације или град за све језике.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Омогући преклапање", "app.containers.AdminPage.SettingsPage.phone": "Телефон", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Конфигурација платформе", "app.containers.AdminPage.SettingsPage.population": "Population", "app.containers.AdminPage.SettingsPage.populationMinError": "Population must be a positive number.", "app.containers.AdminPage.SettingsPage.populationTooltip": "The total number of inhabitants on your territory. This is used to calculate the participation rate. Leave empty if not applicable.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Блокатор вулгарности", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Блокирајте унос, предлоге и коментаре који садрже најчешће пријављене увредљиве речи", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Овај текст је приказан на почетној страници изнад пројеката.", "app.containers.AdminPage.SettingsPage.projectsSettings": "подешавања пројекта", "app.containers.AdminPage.SettingsPage.projects_header": "Заглавље пројеката", "app.containers.AdminPage.SettingsPage.registrationFields": "Поља за регистрацију", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Наведите кратак опис на врху вашег обрасца за регистрацију.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Регистрација", "app.containers.AdminPage.SettingsPage.save": "сачувати", "app.containers.AdminPage.SettingsPage.saveArea": "Сачувај област", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Нешто није у реду. Молимо покушајте касније.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Успех!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Select approvers", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Изаберите области које ће бити приказане корисницима да прате након регистрације", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Изаберите теме које ће бити приказане корисницима да прате након регистрације", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Није могуће сачувати. Покушајте поново да промените поставку.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Региструјте се\"", "app.containers.AdminPage.SettingsPage.signed_in": "Дугме за регистроване посетиоце", "app.containers.AdminPage.SettingsPage.signed_out": "Дугме за нерегистроване посетиоце", "app.containers.AdminPage.SettingsPage.signupFormText": "Помоћни текст за регистрацију", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Додајте кратак опис на врх обрасца за регистрацију.", "app.containers.AdminPage.SettingsPage.statuses": "Statuses", "app.containers.AdminPage.SettingsPage.step1": "Корак е-поште и лозинке", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Ово је приказано на врху прве странице обрасца за регистрацију (име, имејл, лозинка).", "app.containers.AdminPage.SettingsPage.step2": "Корак питања регистрације", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Ово је приказано на врху друге странице обрасца за регистрацију (додатна поља за регистрацију).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Дефинишите географске области које желите да користите за своју платформу, као што су четврти, општине или окрузи. Можете да повежете ове географске области са пројектима (који се могу филтрирати на одредишној страници) или замолити учеснике да изаберу своју област становања као поље за регистрацију за креирање паметних група и дефинисање права приступа.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Одаберите како ће људи видети име ваше организације, изаберите језике ваше платформе и везу до ваше веб странице.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Наведени титл премашује максимално дозвољено ограничење знакова (90 знакова)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Терминологија", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Подешавања су успешно ажурирана.", "app.containers.AdminPage.SettingsPage.tabAreas1": "Подручја", "app.containers.AdminPage.SettingsPage.tabBranding": "Брендирање", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Статуси уноса", "app.containers.AdminPage.SettingsPage.tabPolicies": "Политике", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Project approval", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Proposal statuses", "app.containers.AdminPage.SettingsPage.tabRegistration": "Регистрација", "app.containers.AdminPage.SettingsPage.tabSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "Tags", "app.containers.AdminPage.SettingsPage.tabWidgets": "Видгет", "app.containers.AdminPage.SettingsPage.tablet": "Таблет", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Дефинишите коју географску јединицу желите да користите за своје пројекте (нпр. комшилуке, округе, општине, итд.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Географске области", "app.containers.AdminPage.SettingsPage.titleBasic": "Општа подешавања", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Наведени наслов премашује максимално дозвољено ограничење знакова (35 знакова)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Таг манагер", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Овај банер је посебно користан са сликама које не функционишу добро са текстом из наслова, поднаслова или дугмета. Ове ставке ће бити гурнуте испод банера. Више информација о препорученој употреби слике можете пронаћи на нашем {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "база знања", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Два реда", "app.containers.AdminPage.SettingsPage.urlError": "УРЛ није важећи", "app.containers.AdminPage.SettingsPage.urlPatternError": "Унесите важећи УРЛ.", "app.containers.AdminPage.SettingsPage.urlTitle": "Веб сајт", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Можете додати везу на своју веб локацију. Ова веза ће се користити на дну почетне странице.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "Choose how users without a name in their profile will appear in the platform. This will occur when you set the access rights for a phase to ‘Email confirmation’. In all cases, upon participation, users will be able to update the profile name we autogenerated for them.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "User name display (for users with email confirmed only)", "app.containers.AdminPage.SideBar.administrator": "Администратор", "app.containers.AdminPage.SideBar.communityPlatform": "Платформа заједнице", "app.containers.AdminPage.SideBar.community_monitor": "Community monitor", "app.containers.AdminPage.SideBar.customerPortal": "Customer portal", "app.containers.AdminPage.SideBar.dashboard": "Кома<PERSON><PERSON>на табла", "app.containers.AdminPage.SideBar.emails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.folderManager": "Управљач фасциклама", "app.containers.AdminPage.SideBar.groups": "Гру<PERSON>е", "app.containers.AdminPage.SideBar.guide": "<PERSON>о<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "Менаџер уноса", "app.containers.AdminPage.SideBar.insights": "Извештавање", "app.containers.AdminPage.SideBar.inspirationHub": "Inspiration hub", "app.containers.AdminPage.SideBar.knowledgeBase": "База знања", "app.containers.AdminPage.SideBar.language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Странице и мени", "app.containers.AdminPage.SideBar.messaging": "Месс<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.moderation": "Активност", "app.containers.AdminPage.SideBar.notifications": "Обавештења", "app.containers.AdminPage.SideBar.processing": "Обрада", "app.containers.AdminPage.SideBar.projectManager": "Вођа пројекта", "app.containers.AdminPage.SideBar.projects": "Пројекти", "app.containers.AdminPage.SideBar.settings": "Подешавања", "app.containers.AdminPage.SideBar.signOut": "Одјава", "app.containers.AdminPage.SideBar.support": "Подршка", "app.containers.AdminPage.SideBar.toPlatform": "На платформу", "app.containers.AdminPage.SideBar.tools": "<PERSON>ла<PERSON><PERSON>", "app.containers.AdminPage.SideBar.user.myProfile": "Мој профил", "app.containers.AdminPage.SideBar.users": "Корисници", "app.containers.AdminPage.SideBar.workshops": "Радионице", "app.containers.AdminPage.Topics.addTopics": "Додати", "app.containers.AdminPage.Topics.browseTopics": "Прегледајте ознаке", "app.containers.AdminPage.Topics.cancel": "Поништити, отказати", "app.containers.AdminPage.Topics.confirmHeader": "Да ли сте сигурни да желите да избришете ову ознаку пројекта?", "app.containers.AdminPage.Topics.delete": "Избриши", "app.containers.AdminPage.Topics.deleteTopicLabel": "Избриши", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Ова ознака више неће моћи да се додаје у нове постове у овом пројекту.", "app.containers.AdminPage.Topics.inputForm": "Формулар за унос", "app.containers.AdminPage.Topics.lastTopicWarning": "је потребно, најмање један таг. Ако не желите да користите ознаке, оне се могу онемогућити на картици {ideaFormLink}.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Можете додавати и брисати ознаке које се могу доделити објавама у овом пројекту.", "app.containers.AdminPage.Topics.remove": "Уклони", "app.containers.AdminPage.Topics.title": "Ознаке пројекта", "app.containers.AdminPage.Topics.topicManager": "Таг манагер", "app.containers.AdminPage.Topics.topicManagerInfo": "Ако желите да додате додатне ознаке пројекта, то можете учинити у {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Додајте нову групу", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Назив групе", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Наведите име групе", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Направите ручну групу", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Која врста групе вам је потребна?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Сачувај групу", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Направите ручну групу", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Направите паметну групу", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Сазнајте више о групама", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Можете изабрати кориснике из прегледа и додати их у ову групу.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Можете дефинисати услове и корисници који испуњавају услове се аутоматски додају у ову групу.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Ручна група", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Паметна група", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "У овој групи још нема никога", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Идите на {allUsersLink} да бисте ручно додали неке кориснике.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Ниједан корисник(и) не одговара вашој претрази", "app.containers.AdminPage.Users.GroupsPanel.select": "Изаберите", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Export all", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Export users in group", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Export selected", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Да ли сте сигурни да желите да избришете ову групу?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Дошло је до грешке при додавању корисника у групе, покушајте поново.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Уклони из групе", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Желите ли да избришете изабране кориснике из ове групе?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Дошло је до грешке приликом брисања корисника из групе, покушајте поново.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Add users to group", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Додати", "app.containers.AdminPage.groups.permissions.add": "Додати", "app.containers.AdminPage.groups.permissions.addAnswer": "Додајте одговор", "app.containers.AdminPage.groups.permissions.addQuestion": "Додајте демографска питања", "app.containers.AdminPage.groups.permissions.answerChoices": "Избори одговора", "app.containers.AdminPage.groups.permissions.answerFormat": "Формат одговора", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Мора се обезбедити најмање један избор", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "This user moderates the folder containing this project. To remove their moderator rights for this project, you can either revoke their folder rights or move the project to a different folder.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Направите ново питање", "app.containers.AdminPage.groups.permissions.createAQuestion": "Направите питање", "app.containers.AdminPage.groups.permissions.defaultField": "Подразумевано поље", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Избриши", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Избриши", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Please provide a title for all choices", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "Да-не (поље за потврду)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Датум", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Дуг одговор", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Вишеструки избор (изаберите више)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Нумеричка вредност", "app.containers.AdminPage.groups.permissions.fieldType_select": "Вишеструки избор (изаберите један)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Кратак одговор", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Changing granular permissions is not part of your license. Please contact your GovSuccess Manager to learn more about it.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Да ли сте сигурни да желите да уклоните ову групу из пројекта?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Изаберите једну или више група", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {Нема чланова} one {1 члан} other {{count} чланови}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Молимо попуните наслов на свим језицима", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Јеси ли сигуран?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Менаџери пројекта нису пронађени", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Ништа се не приказује, јер нема радњи које корисник може да предузме у овом пројекту.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Only admins can create a new question.", "app.containers.AdminPage.groups.permissions.option1": "Опција 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Позив на чекању", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Ко може да дода коментаре на документ?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Who can sign up to attend an event?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Ко може да коментарише уносе?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Ко може да коментарише предлоге?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Ко може да постави предлог?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Ко може да реагује на инпуте?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Ко може да поднесе инпуте?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Ко може да гласа?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Ко може да полаже анкету?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Who can volunteer?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Ко може да гласа о предлозима?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Ко може да гласа?", "app.containers.AdminPage.groups.permissions.questionDescription": "О<PERSON>ис питања", "app.containers.AdminPage.groups.permissions.questionTitle": "Наслов питања", "app.containers.AdminPage.groups.permissions.save": "сачувати", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Нешто није у реду. Молимо покушајте касније.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Успех!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Промене су сачуване.", "app.containers.AdminPage.groups.permissions.select": "Изаберите", "app.containers.AdminPage.groups.permissions.selectValueError": "Молимо изаберите тип одговора", "app.containers.AdminPage.new.createAProject": "Create a project", "app.containers.AdminPage.new.fromScratch": "From scratch", "app.containers.AdminPage.phase.methodPicker.addOn1": "Add on", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "AI-powered insights", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Help participants surface agreement and disagreement, one idea at a time.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Find common ground", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Embed an interactive PDF and collect comments and feedback with Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Collect feedback on a document", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Embed a third-party survey", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "External survey", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Tap into your users' collective intelligence. Invite them to submit, discuss ideas, and/or provide feedback in a public forum.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Collect input and feedback in public", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "Share information", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Lacks in-platform AI-powered insights", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Lacks in-platform reporting and data visualisation and processing", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Link with in-platform report builder", "app.containers.AdminPage.phase.methodPicker.logic1": "Logic", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Wide range of question types", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Allow participants to upload ideas with a time and vote limit.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "Proposals, petitions or initiatives", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Quick poll", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Set up a short, multiple-choice questionnaire.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "Provide information to users, visualise results from other phases and create data rich reports.", "app.containers.AdminPage.phase.methodPicker.survey1": "Survey", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Understand your users' needs and thinking via a wide range of private question types.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Survey options", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Create a survey", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Ask users to volunteer for activities and causes or find participants for a panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Recruit participants or volunteers", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Select a voting method, and have users prioritise between a few different options.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Conduct a voting or prioritization exercise", "app.containers.AdminPage.projects.all.all": "All", "app.containers.AdminPage.projects.all.createProjectFolder": "New folder", "app.containers.AdminPage.projects.all.existingProjects": "Постојећи пројекти", "app.containers.AdminPage.projects.all.homepageWarning": "Customize your homepage display: admins can arrange projects and folders here to set the order on the homepage.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "Projects where you are a Project Manager will appear here.", "app.containers.AdminPage.projects.all.noProjects": "No projects found.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Only admins can create project folders.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Пројекти и фасцикле", "app.containers.AdminPage.projects.all.publishedTab": "Објављено", "app.containers.AdminPage.projects.all.searchProjects": "Search projects", "app.containers.AdminPage.projects.all.yourProjects": "Your projects", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "<PERSON><PERSON> Аналисис", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Тачност: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Питати", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Instead of summarising, you can ask relevant questions to your data. This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Поставите питање", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "This insight includes the following questions:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Избриши питање", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Да ли сте сигурни да желите да избришете ово питање?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Избриши резиме", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Да ли сте сигурни да желите да избришете ове резимее?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Ваши текстуални резимеи ће бити приказани овде, али тренутно још увек немате ниједан.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "Кликните на дугме Аутоматски сумирај изнад да бисте започели.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "inputs selected", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Постављање питања о мањем броју уноса доводи до веће прецизности. Смањите тренутни избор уноса користећи ознаке, претрагу или демографске филтере.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Питања за", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Питање за све улазе", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Оцените квалитет овог увида", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "Вратите филтере", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Summarize", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Резиме за", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Резиме за све инпуте", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Хвала на повратним информацијама", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "The AI can’t process so many inputs in one go. Divide them into smaller groups.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "You can summarise a maximum of 30 inputs at a time on your current plan. Talk to your GovSuccess Manager or admin to unlock more.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "разумем", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Наша платформа вам омогућава да истражите основне теме, сумирате податке и испитате различите перспективе. Ако тражите конкретне одговоре или увиде, размислите о коришћењу функције „Поставите питање“ да бисте заронили дубље изван резимеа.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Иако ретка, АИ би повремено могао да генерише информације које нису биле експлицитно присутне у оригиналном скупу података.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "халуцинације:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "АИ би могао да нагласи одређене теме или идеје више од других, потенцијално искрививши целокупну интерпретацију.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "претеривање:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Наш систем је оптимизован за руковање 20-200 добро дефинисаних улаза за најтачније резултате. Како се обим података повећава изван овог опсега, резиме може постати виши и генерализованији. То не значи да АИ постаје „мање тачан“, већ да ће се фокусирати на шире трендове и обрасце. За нијансираније увиде, препоручујемо да користите функцију (ауто) означавања за сегментирање већих скупова података у мање подскупове којима се лакше управља.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Обим и тачност података:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Препоручујемо да користите резимее генерисане вештачком интелигенцијом као полазну тачку за разумевање великих скупова података, али не и као последњу реч.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Како радити са АИ", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Додајте изабране улазе у ознаку", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Дода<PERSON> ознаку", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Сви улази", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Сви улази", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Све ознаке", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "Не, урадићу то", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Да ли желите да аутоматски доделите уносе вашој ознаци?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "Постоји <b>различитих метода</b> за аутоматско додељивање улаза ознакама.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Користите <b>дугме за аутоматско означавање</b> да бисте покренули жељени метод.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Кликните на ознаку да бисте је доделили тренутно изабраном улазу.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Да, аутоматско означавање", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Ауто-таг", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Рачунар аутоматски изводи аутоматске ознаке. Можете их променити или уклонити у сваком тренутку.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Ауто-таг", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "Inputs already associated with these tags will not be classified again.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Класификација се заснива искључиво на називу ознаке. Изаберите релевантне кључне речи за најбоље резултате.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Тагови: По етикети", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Ви креирате ознаке и ручно доделите неколико улаза као пример, рачунар додељује остале", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Тагови: На примеру", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "Слично као „Ознаке: према етикети“, али са повећаном прецизношћу док обучавате систем добрим примерима.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Ви креирате ознаке, рачунар додељује улазе", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Тагови: По етикети", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Ово добро функционише када имате унапред дефинисан скуп ознака или када ваш пројекат има ограничен опсег у смислу ознака.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Откријте уносе са значајним односом несвиђања/свиђања", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "Контроверзно", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Обри<PERSON>и ознаку", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "Да ли сте сигурни да желите да избришете ову ознаку?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Не показуј ово поново", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Уреди ознаку", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Додајте име", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Изаберите највише 9 ознака између којих желите да се подаци расподељују.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Класификација је заснована на улазима који су тренутно додељени ознакама. Рачунар ће покушати да следи ваш пример.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Тагови: На примеру", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "You do not have any custom tags yet.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Рачунар аутоматски открива ознаке и додељује их вашим улазима.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Ознаке: Потпуно аутоматизован", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "Добро функционише када ваши пројекти покривају широк спектар ознака. Добро место за почетак.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Како желите да означите?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Уноси без ознака", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Откријте језик сваког уноса", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Лансир<PERSON>ње", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Нема активних филтера", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Користите ознаке да бисте поделили и филтрирали уносе како бисте направили тачније или циљаније сажетке.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "Остало", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Ознаке: Ознаке платформе", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Доделите постојеће ознаке платформе које је аутор изабрао приликом објављивања", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Recommended", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Преимен<PERSON>ј ознаку", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "Поништити, отказати", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Име", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "сачувати", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Преимен<PERSON>ј ознаку", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Select all", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Додели позитивно или негативно осећање сваком уносу, изведено из текста", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Сентимент", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Детекција ознака", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Користите тренутне филтере", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Које уносе желите да означите?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Задатак аутоматског означавања", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "Контроверзно", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Завршено у", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Није успео", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "На примеру", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "У току", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "По етикети", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "НЛП ознака", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Ниједан недавни АИ задатак није обављен", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Ознака платформе", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "У реду чекања", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Сентимент", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Почело у", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Успео", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Задатак сумирања", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Покренуто на", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "Изнад", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Све", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "Испод", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Година рођења", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Домициле", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Ангажовање", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Филтери", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "Од", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Пол", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Улазни", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON>рој коментара", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Број реакција", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "<PERSON>рој гласова", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "До", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Add to analysis", "app.containers.AdminPage.projects.project.analysis.anonymous": "Анонимоус инпут", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Аутори по годинама", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Аутори по пребивалишту", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Позадински послови", "app.containers.AdminPage.projects.project.analysis.comments": "Коментари", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Табела пребивалишта је превелика за приказ", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Hide empty answers", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Responses", "app.containers.AdminPage.projects.project.analysis.end": "К<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.filter": "Прикажи само уносе са овом вредношћу", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Hide responses with no answer", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Auto-insights", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Column values", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "There are {count} instances of this combination.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "Dislikes", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Explore", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "False", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Inputs", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "<PERSON>s", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Next heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Next insight", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Not a statistically significant insight.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Auto insights are not available for projects with less than 30 participants.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "Participants", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Previous heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "Previous insight", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Row values", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistically significant insight.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Summarize", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Analysis tags", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "True", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Units", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "View all insights", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "View auto-insights", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Уноси без ознака", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "An invalid shapefile was uploaded and cannot be displayed.", "app.containers.AdminPage.projects.project.analysis.limit": "Limit", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Main question", "app.containers.AdminPage.projects.project.analysis.manageInput": "Manage input", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Next graph", "app.containers.AdminPage.projects.project.analysis.noAnswer": "Нема одговора", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "No answer provided.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "No shapefile uploaded.", "app.containers.AdminPage.projects.project.analysis.noInputs": "Ниједан унос не одговара вашим тренутним филтерима", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Previous graph", "app.containers.AdminPage.projects.project.analysis.reactions": "Реакције", "app.containers.AdminPage.projects.project.analysis.remove": "Remove", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Уклоните филтер", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Уклоните филтер", "app.containers.AdminPage.projects.project.analysis.search": "Претрага", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapefiles are displayed in GeoJSON format here. As such, styling in the original file may not display correctly.", "app.containers.AdminPage.projects.project.analysis.start": "Почетак", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Чланак подршке", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Непознат", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "View all questions", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "View selected questions", "app.containers.AdminPage.projects.project.analysis.votes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copied": "Копирано у међуспремник", "app.containers.AdminPage.widgets.copyToClipboard": "Ископирај овај код", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Копирајте ХТМЛ код", "app.containers.AdminPage.widgets.fieldAccentColor": "Боја акцента", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Боја позадине виџета", "app.containers.AdminPage.widgets.fieldButtonText": "Текст дугмета", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Придружи се сад", "app.containers.AdminPage.widgets.fieldFont": "Фонт", "app.containers.AdminPage.widgets.fieldFontDescription": "Ово мора бити постојеће име фонта од {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Величина фонта (пк)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Поднаслов заглавља", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Можете рећи", "app.containers.AdminPage.widgets.fieldHeaderText": "Наслов заглавља", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Наша платформа за учешће", "app.containers.AdminPage.widgets.fieldHeight": "Ви<PERSON><PERSON><PERSON> (пк)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Број постова", "app.containers.AdminPage.widgets.fieldProjects": "Пројекти", "app.containers.AdminPage.widgets.fieldRelativeLink": "Линкови на", "app.containers.AdminPage.widgets.fieldShowFooter": "Дугме Прикажи", "app.containers.AdminPage.widgets.fieldShowHeader": "Прикажи заглавље", "app.containers.AdminPage.widgets.fieldShowLogo": "Прикажи лого", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Боја позадине сајта", "app.containers.AdminPage.widgets.fieldSort": "Сортирано по", "app.containers.AdminPage.widgets.fieldTextColor": "Боја текста", "app.containers.AdminPage.widgets.fieldTopics": "Ознаке", "app.containers.AdminPage.widgets.fieldWidth": "Ши<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Почетна страница", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Можете да копирате овај ХТМЛ код и да га налепите на онај део своје веб локације где желите да додате свој виџет.", "app.containers.AdminPage.widgets.htmlCodeTitle": "ХТМЛ код виџета", "app.containers.AdminPage.widgets.previewTitle": "Преглед", "app.containers.AdminPage.widgets.settingsTitle": "Подешавања", "app.containers.AdminPage.widgets.sortNewest": "Најновије", "app.containers.AdminPage.widgets.sortPopular": "Популарно", "app.containers.AdminPage.widgets.sortTrending": "У тренду", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Видгет", "app.containers.AdminPage.widgets.titleDimensions": "Димензије", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Заглавље и подножје", "app.containers.AdminPage.widgets.titleInputSelection": "Избор уноса", "app.containers.AdminPage.widgets.titleStyle": "Стиле", "app.containers.AdminPage.widgets.titleWidgets": "Видгет", "app.containers.ContentBuilder.Save": "сачувати", "app.containers.ContentBuilder.homepage.PageTitle": "Почетна страница", "app.containers.ContentBuilder.homepage.SaveError": "Something went wrong while saving the homepage.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "Две колоне", "app.containers.ContentBuilder.homepage.bannerImage": "Слика банера", "app.containers.ContentBuilder.homepage.bannerSubtext": "Подтекст банера", "app.containers.ContentBuilder.homepage.bannerText": "Текст банера", "app.containers.ContentBuilder.homepage.button": "Дугме", "app.containers.ContentBuilder.homepage.chooseLayout": "Лаи<PERSON><PERSON>т", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Customizing settings other than the image and text on the homepage banner is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.ContentBuilder.homepage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Текст дугмета", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Веза са дугметом", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Приказује следећа 3 предстојећа догађаја на вашој платформи.", "app.containers.ContentBuilder.homepage.eventsDescription": "Приказује следећа 3 предстојећа догађаја на вашој платформи.", "app.containers.ContentBuilder.homepage.fixedRatio": "Банер са фиксним односом", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Овај тип банера најбоље функционише са сликама које не би требало да се изрезују, као што су слике са текстом, логотипом или специфичним елементима који су кључни за ваше грађане. Овај банер се замењује пуним оквиром у примарној боји када су корисници пријављени. Ову боју можете да подесите у општим подешавањима. Више информација о препорученој употреби слике можете пронаћи на нашем {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "база знања", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Банер пуне ширине", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Овај банер се протеже по целој ширини за одличан визуелни ефекат. Слика ће покушати да покрије што је могуће више простора, због чега није увек видљива у сваком тренутку. Можете комбиновати овај банер са преклопом било које боје. Више информација о препорученој употреби слике можете пронаћи на нашем {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "база знања", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Боја преклапања слике", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Прозирност преклапања слике", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Неважећи УРЛ", "app.containers.ContentBuilder.homepage.no_button": "Нема дугмета", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Нерегистровани корисници", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Омогући преклапање", "app.containers.ContentBuilder.homepage.projectsDescription": "Да бисте конфигурисали редослед приказивања ваших пројеката, промените њихов редослед на {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Страница са пројектима", "app.containers.ContentBuilder.homepage.registeredUsersView": "Регистровани корисници", "app.containers.ContentBuilder.homepage.showAvatars": "Прикажи аватаре", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Нерегистрованим посетиоцима покажите профилне слике учесника и њихов број", "app.containers.ContentBuilder.homepage.sign_up_button": "Региструјте се", "app.containers.ContentBuilder.homepage.signedInDescription": "Овако регистровани корисници виде банер.", "app.containers.ContentBuilder.homepage.signedOutDescription": "Овако виде банер посетиоци који нису регистровани на платформи.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "This banner is in particular useful with images that don't work well with text from the title, subtitle or button. These items will be pushed below the banner. More info on the recommended image usage can be found on our {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "база знања", "app.containers.ContentBuilder.homepage.twoRowLayout": "Два реда", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Вис<PERSON>на уградње (пиксели)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "Висина на којој желите да се ваш уграђени садржај појави на страници (у пикселима).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Кратак опис садржаја који уграђујете", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "Корисно је пружити ове информације корисницима који се ослањају на читач екрана или другу помоћну технологију.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Сајт адреса", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Пун УРЛ веб локације коју желите да уградите.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Display content from an external website on your page in an HTML iFrame. Note that not every page can be embedded. If you are having trouble embedding a page, check with the owner of the page if it is configured to allow embedding.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Посетите нашу страницу за подршку", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "Жа<PERSON> нам је, овај садржај није могао да се угради. {visitLinkMessage} да бисте сазнали више.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "Унесите важећу веб адресу, на пример https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Уграђивање", "app.containers.admin.ContentBuilder.accordionMultiloc": "Хармоника", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Отвори подразумевано", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Текст", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Ово је прошириви садржај хармонике. Можете га уређивати и форматирати помоћу уређивача на табли са десне стране.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Наслов хармонике", "app.containers.admin.ContentBuilder.buttonMultiloc": "Дугме", "app.containers.admin.ContentBuilder.delete": "Избриши", "app.containers.admin.ContentBuilder.error": "грешка", "app.containers.admin.ContentBuilder.errorMessage": "Постоји грешка на {locale} садржају, решите проблем да бисте могли да сачувате промене", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Hide participation avatars", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "This is a quarterly, ongoing survey that tracks how you feel about governance & public services.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Take the survey", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Help us serve you better", "app.containers.admin.ContentBuilder.homepage.default": "Уобичајено", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Догађаји", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Догађаји", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "Call to action", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Description", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "Primary button URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Primary button text", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "Secondary button URL", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Secondary button text", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Title", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Банер за почетну страницу", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Банер за почетну страницу", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Картице са сликама и текстом", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 колона", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Пројекти", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Омогућите предлоге у одељку „Предлози“ на административној табли да бисте их откључали на почетној страници", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Предлози", "app.containers.admin.ContentBuilder.imageMultiloc": "Слика", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Кратак опис слике", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Додавање „алт текста“ за слике је важно како би ваша платформа била доступна корисницима који користе читаче екрана.", "app.containers.admin.ContentBuilder.participationBox": "Participation Box", "app.containers.admin.ContentBuilder.textMultiloc": "Текст", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 колона", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 колона", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 колоне са 30% и 60% ширине респективно", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 колоне са 60% и 30% ширине респективно", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 парне колоне", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Most reacted inputs", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Ideation phase", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "There are no inputs available for this project or phase.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Number of inputs", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Прикажи више", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Total inputs: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Collapse long text", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "There are no inputs available for this project or phase.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Select phase", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Author", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Content", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Reactions", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Votes", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Input", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Title", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registration rate", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registrations", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Временска линија учесника", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "Please note that participation numbers may not be fully accurate as some data is captured in an external survey that we do not track.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "<PERSON>р<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Период", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.noData": "Нема доступних података за филтере које сте изабрали.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Извори саобраћаја", "app.containers.admin.ReportBuilder.charts.users": "Users", "app.containers.admin.ReportBuilder.charts.usersByAge": "Корисници по годинама", "app.containers.admin.ReportBuilder.charts.usersByGender": "Корисници по полу", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Временска линија посетилаца", "app.containers.admin.ReportBuilder.managerLabel1": "Project manager", "app.containers.admin.ReportBuilder.periodLabel1": "Period", "app.containers.admin.ReportBuilder.projectLabel1": "Project", "app.containers.admin.ReportBuilder.quarterReport1": "Community Monitor Report: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Start", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Купите 1 додатно седиште", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Потврди", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Да ли сте сигурни да желите да дате права менаџера 1 особи?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Дајте менаџерска права", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Достигли сте ограничење укључених места у оквиру вашег плана, биће додато 1 додатно место.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Додај статус", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Default statuses cannot be deleted.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Избриши", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Уредити", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Уреди статус", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Статуси који су тренутно додељени уносу учесника не могу се избрисати. Можете уклонити/променити статус постојећег уноса на картици {manageTab}.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Овај статус се не може избрисати или преместити.", "app.containers.admin.ideaStatuses.all.manage": "Уредити", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Configuring custom input statuses is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Управљајте статусом који се може доделити уносу учесника у оквиру пројекта. Статус је јавно видљив и помаже у информисању учесника.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Manage the status that can be assigned to proposals within a project. The status is publicly visible and helps in keeping participants informed.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Измените статусе уноса", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Edit proposal statuses", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Изабрано за имплементацију или следеће кораке", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Одобрено", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Official feedback provided", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Answered", "app.containers.admin.ideaStatuses.form.category": "Категорија", "app.containers.admin.ideaStatuses.form.categoryDescription": "Изаберите категорију која најбоље представља ваш статус. Овај избор ће помоћи нашем алату за аналитику да прецизније обрађује и анализира постове.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Not matching any of the other options", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Остало", "app.containers.admin.ideaStatuses.form.fieldColor": "Боја", "app.containers.admin.ideaStatuses.form.fieldDescription": "О<PERSON><PERSON>с статуса", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Наведите опис статуса за све језике", "app.containers.admin.ideaStatuses.form.fieldTitle": "Статус Име", "app.containers.admin.ideaStatuses.form.fieldTitleError": "Наведите назив статуса за све језике", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Успешно спроведено", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Имплементирано", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Proposal is ineligible", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Ineligible", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Не испуњава услове или није изабрано за напредовање", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Није изабран", "app.containers.admin.ideaStatuses.form.saveStatus": "Сачувај статус", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Разматра се за имплементацију или следеће кораке", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "У разматрању", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Прегледано, али још није обрађено", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Погледано", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Управљајте уносима и њиховим статусима.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Менаџер уноса | Платформа за учешће од {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Дајте повратне информације, додајте ознаке и премештајте унос из једног пројекта у други", "app.containers.admin.ideas.all.inputManagerPageTitle": "Менаџер уноса", "app.containers.admin.ideas.all.tabOverview": "Преглед", "app.containers.admin.import.importInputs": "Увезите улазе", "app.containers.admin.import.importNoLongerAvailable3": "This feature is no longer available here. To import inputs to an ideation phase, go to the phase and select \"Import\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 додатно администраторско место} other {# додатна администраторска места}} и {managerSeats, plural, one {1 додатно менаџерско место} other {# додатна менаџерска места}} биће додато преко ограничења.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 додатно администраторско место ће бити додато преко ограничења} other {# додатна администраторска места ће бити додата преко ограничења}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 додатно менаџерско место ће бити додато преко ограничења} other {# додатна менаџерска места ће бити додата преко ограничења}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Потврдите и пошаљите позивнице", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Потврдите утицај на коришћење седишта", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Достигли сте ограничење расположивих места у оквиру свог плана.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Администратори и менаџери овог пројекта", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "Само администратори и сарадници", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Само администратори платформе, менаџери фасцикли и менаџери пројеката могу да предузму акцију", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON>ило ко", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "Свако укључујући нерегистроване кориснике може учествовати.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Избор", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Корисници у одређеним групама корисника могу учествовати. Групама корисника можете управљати на картици „Корисници“.", "app.containers.admin.project.permissions.viewingRightsTitle": "Ко може да види овај пројекат?", "app.containers.phaseConfig.enableSimilarInputDetection": "Enable similar input detection", "app.containers.phaseConfig.similarInputDetectionTitle": "Similar input detection", "app.containers.phaseConfig.similarInputDetectionTooltip": "Show participants similar input while they type to help avoid duplicates.", "app.containers.phaseConfig.similarityThresholdBody": "Similarity threshold (body)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "This controls how similar two descriptions must be to be flagged as similar. Use a value between 0 (strict) and 1 (lenient). Lower values return fewer but more accurate matches.", "app.containers.phaseConfig.similarityThresholdTitle": "Similarity threshold (title)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "This controls how similar two titles must be to be flagged as similar. Use a value between 0 (strict) and 1 (lenient). Lower values return fewer but more accurate matches.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "This feature is available as part of an early access offering until June 30th, 2025. If you'd like to continue using it beyond that date, please reach out to your Government Success Manager or admin to discuss activation options.", "app.containers.survey.sentiment.noAnswers2": "No responses at this time.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 comments} one {1 comment} other {# comments}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Participants are users or visitors that have participated in a project, posted or interacted with a proposal or attended events.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "Participants", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Стопа учешћа", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Percentage of visitors that become participants.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Укупно учесника", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Аутоматизоване кампање", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Аутоматизоване е-поруке", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "Од {quantity} кампања", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "Кампање", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Прилагођене кампање", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Прилагођене е-поруке", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Укупан број послатих е-порука", "app.modules.commercial.analytics.admin.components.Events.completed": "Завршено", "app.modules.commercial.analytics.admin.components.Events.events": "Догађаји", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Укупан број догађаја је додат", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Предстојећи", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Прихваћено", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Позивнице", "app.modules.commercial.analytics.admin.components.Invitations.pending": "нерешен", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Укупан број послатих позивница", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Идите на Менаџер уноса", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Инпутс", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Ак<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "Пројекти који нису архивирани и видљиви у табели 'Активни' на почетној страници", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "Ар<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Нацрти пројеката", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Овде се рачунају сви архивирани пројекти и активни пројекти временске линије који су завршени", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Пројекти", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Укупно пројеката", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Број пројеката који су видљиви на платформи", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Нове регистрације", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Стопа регистрације", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Percentage of visitors that become registered users.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Регистрације", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Укупно регистрација", "app.modules.commercial.analytics.admin.components.Tab": "Посетиоци", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Последњих 30 дана:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Последњих 7 дана:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Број приказа странице по посети", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Трајање посете", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Посетиоци", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "„Посетиоци“ је број јединствених посетилаца. Ако особа посети платформу више пута, она се рачуна само једном.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Посете", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "„Посете“ је број сесија. Ако је особа посетила платформу више пута, свака посета се рачуна.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "јуче:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Count", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Број посетилаца", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Проценат посетилаца", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Рефер<PERSON><PERSON>р", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "кликните овде", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Реферрери", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Да бисте видели комплетну листу упућивача, {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Посетиоци", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Извори саобраћаја", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Посете", "app.modules.commercial.analytics.admin.components.totalParticipants": "Total participants", "app.modules.commercial.analytics.admin.containers.visitors.noData": "There is no visitor data yet.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "We have changed the way we collect and display visitor data. As a result, visitor data is more accurate and more types of data are available, while still being GDPR compliant. While the data used for the visitors timeline goes back longer, we only started collecting the data for the \"Visit duration\", \"Pageviews per visit\" and the other graphs in November 2024, so before that no data is available. Therefore, if you select data before November 2024, be aware that some graphs might be empty or look odd.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Испоруке путем е-поште током времена", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Participants over time", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Регистрације током времена", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Датум", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Статистика", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Укупна статистика", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Посете и посетиоци током времена", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Укупно током периода", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "Цоунт", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "Кампање", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Ди<PERSON><PERSON><PERSON><PERSON><PERSON>н улазак", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Number of visitors", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Проценат посета", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Referrer websites", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Претраживачи", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Друштвене мреже", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO redirects", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Извор саобраћаја", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "Број посета", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Вебситес", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Можете уклонити ову заставицу садржаја тако што ћете изабрати ову ставку и кликнути на дугме за уклањање на врху. Затим ће се поново појавити на картицама Виђено или Невиђено", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Неприкладан садржај је аутоматски откривен.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Нема постова пријављених за преглед од стране заједнице или означених због неприкладног садржаја од стране нашег система за обраду природног језика", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Уклони {numberOfItems, plural, one {упозорење о садржају} other {# упозорења о садржају}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Корисник платформе пријавио је као неприкладан.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Упозорења о садржају", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Градитељ извештаја", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Странице које се приказују на траци за навигацију", "app.modules.navbar.admin.containers.addProject": "Add project to navbar", "app.modules.navbar.admin.containers.createCustomPageButton": "Направите прилагођену страницу", "app.modules.navbar.admin.containers.deletePageConfirmation": "Да ли сте сигурни да желите да избришете ову страницу? Ово се не може поништити. Такође можете да уклоните страницу са траке за навигацију ако још нисте спремни да је избришете.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "You can only add up to 5 items to the navigation bar", "app.modules.navbar.admin.containers.pageHeader": "Странице и мени", "app.modules.navbar.admin.containers.pageSubtitle": "Ваша навигациона трака може да прикаже до пет страница поред почетне странице и страница са пројектима. Можете преименовати ставке менија, преуредити и додати нове странице са сопственим садржајем.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widgets", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Use the ☰ icon below to drag AI content into your report.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "There are no available AI insights. You can create them in your project.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Go to project", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Select phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Unlock AI analysis", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Pull AI-generated insights into your report", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Report faster with AI", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Reporting with AI is not included in your current plan. Talk to your Government Success Manager to unlock this feature.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "This is not included in your current plan. Reach out to your Government Success Manager or admin to unlock it.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Group by registration field", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Group by survey question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Group mode", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Group survey responses by registration fields (gender, location, age, etc) or other survey questions.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "None", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "Question", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Registration field", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Survey phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Survey question", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Are you sure you want to delete this?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "Cancel", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Delete", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "Edit", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Post your comment", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Save", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "Write your comment here", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Click on the buttons below to follow or unfollow. The number of projects is shown in brackets.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "In your area", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "Done", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Follow preferences", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "There are currently no active projects given your follow preferences.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "This widget shows projects associated with the \"areas\" the user follows. Note that your platform might use a different name for \"areas\"- see the \"Areas\" tab in the platform settings. If the user does not follow any areas yet, the widget will show the available areas to follow. In this case the widget will show a maximum of 100 areas.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "No published projects or folders available", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Published projects and folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "This widget will case the projects and folders that are currently published, respecting the ordering defined on the projects page. This behavior is the same as the \"active\" tab of the \"legacy\" projects widget.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "No projects or folders selected", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Select projects or folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Selected projects and folders", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "With this widget, you can select and determine the order in which you want projects or folders to show to users.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projects", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "посетите наш центар за подршку", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "За више информација о препорученим резолуцијама слика, {supportPageLink}."}