{"UI.FormComponents.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.action": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.after": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.before": "<PERSON><PERSON>", "app.Admin.ManagementFeed.changed": "Muokattu", "app.Admin.ManagementFeed.created": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.date": "Päivämäärä", "app.Admin.ManagementFeed.deleted": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.folder": "Kansio", "app.Admin.ManagementFeed.idea": "Idea", "app.Admin.ManagementFeed.in": "projektissa {project}", "app.Admin.ManagementFeed.item": "<PERSON><PERSON>", "app.Admin.ManagementFeed.key": "Avain", "app.Admin.ManagementFeed.managementFeedNudge": "Hallintosyötteen käyttö ei sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.Admin.ManagementFeed.noActivityFound": "Toimintaa ei löytynyt", "app.Admin.ManagementFeed.phase": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.project": "Projekti", "app.Admin.ManagementFeed.projectReviewApproved": "Projekti hyväksytty", "app.Admin.ManagementFeed.projectReviewRequested": "Projektin tark<PERSON>a p<PERSON>y", "app.Admin.ManagementFeed.title": "Hallintosyöte", "app.Admin.ManagementFeed.user": "Käyttäjä", "app.Admin.ManagementFeed.userPlaceholder": "Valitse käyttäjä", "app.Admin.ManagementFeed.value": "Arvo", "app.Admin.ManagementFeed.viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.warning": "Kokeellinen ominaisuus: Vähimmäisluettelo valituista toiminn<PERSON>ta, jot<PERSON> järjestelmänvalvojat tai johtajat ovat suorittaneet viimeisen 30 päivän aikana. Kai<PERSON><PERSON> toiminnot eivät ole mukana.", "app.Admin.Moderation.managementFeed": "Hallintosyöte", "app.Admin.Moderation.participationFeed": "Osallistumissyöte", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "<PERSON><PERSON><PERSON>?", "app.components.Admin.Campaigns.clicked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deleteCampaignButton": "Poista kampanja", "app.components.Admin.Campaigns.deliveryStatus_accepted": "Hyväksytty", "app.components.Admin.Campaigns.deliveryStatus_bounced": "Pomppii", "app.components.Admin.Campaigns.deliveryStatus_clicked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip": "Kun lisäsit yhden tai useamman linkin sähköpostiisi, linkkiä napsauttaneiden käyttäjien määrä näkyy tässä.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Toimitettu", "app.components.Admin.Campaigns.deliveryStatus_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_opened": "Avattu", "app.components.Admin.Campaigns.deliveryStatus_sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.draft": "Luonnos", "app.components.Admin.Campaigns.manageButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.opened": "Avattu", "app.components.Admin.Campaigns.project": "Projekti", "app.components.Admin.Campaigns.recipientsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.statsButton": "Tilastot", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON>", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.components.Admin.ImageCropper.info": "Tämä kuva rajataan aina tiettyyn suhteeseen varmistaakseen, että kaikki tärkeät näkökohdat ovat aina näkyvissä. Tämän kuvatyypin {link} on {aspect}.", "app.components.Admin.ImageCropper.infoLinkText": "suositeltu suhde", "app.components.Admin.ImageCropper.mobileCropExplanation": "Huomautus: <PERSON><PERSON> ka<PERSON> tärkeiden alueiden tulee olla pystysuorien katkoviivojen sisällä, koska kuva rajataan mobiililaitteilla kuvasuhteeseen 3:1.", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Otsikkoteksti rekisteröityneille käyttäjille", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Varoitus: v<PERSON><PERSON><PERSON><PERSON> värin kontrasti ei ole tarpeek<PERSON> korkea. Tämä voi johtaa teksti<PERSON>, jota on vaikea lukea. Valitse tummempi väri paranta<PERSON> l<PERSON>a.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Lisää tapahtumia navigointipalkkiin", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON>n tämä on k<PERSON><PERSON>össä, linkki kaikkiin projektin tapahtumiin lisätään navigointipalkkiin.", "app.components.AdminPage.SettingsPage.eventsSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "<PERSON><PERSON><PERSON><PERSON> muka<PERSON> osio", "app.components.AnonymousPostingToggle.userAnonymity": "Käyttäjän anonymiteetti", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Käyttäjät voivat piilottaa henkilöllisyytensä muilta käyttäjiltä, projektipäälliköiltä ja ylläpitäjiltä. Näitä lahjoituksia voidaan edelleen valvoa.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "<PERSON><PERSON> k<PERSON>täjien osallistua anonyymisti", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Käyttäjät voivat silti halutessaan osallistua oikealla nimellä, mutta he voivat halutessaan lähettää lahjoituksia nimettömästi. Kaikkien käyttäjien on silti täytettävä Käyttöoikeudet-välilehdellä asetetut vaatimukset, jotta heidän panoksensa voidaan suorittaa. Käyttäjäprofiilitiedot eivät ole käytettävissä osallistumistietojen viennissä.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Lisätietoja käyttäjien nimettömyydestä on {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/en/articles/7946486-enabling-anonymous-participation", "app.components.BillingWarning.billingWarning": "Kun lisää paikkoja on lisät<PERSON>, laskutustasi korotetaan. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Kiitos kyselyn t<PERSON>tämisestä! Voit ottaa sen uudelleen ensi vuosineljänneksellä.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Lataa pdf-tiedostona", "app.components.FormSync.downloadExcelTemplate": "Lataa Excel-malli", "app.components.FormSync.downloadExcelTemplateTooltip2": "Excel-malleissa ei ole mukana ranking-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, matri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tied<PERSON><PERSON><PERSON><PERSON> latauskysymyksiä tai kartoituskysymyksiä (pudotusnasta, re<PERSON><PERSON> piirt<PERSON>n, al<PERSON><PERSON> pii<PERSON>, ESRI-tiedostojen lataus), koska niitä ei tällä hetkellä tueta joukkotuonnissa.", "app.components.ProjectTemplatePreview.close": "kiinni", "app.components.ProjectTemplatePreview.createProject": "<PERSON><PERSON> projekti", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "Luo projekti mallin ''{templateTitle}'' pohjalta", "app.components.ProjectTemplatePreview.goBack": "<PERSON><PERSON>", "app.components.ProjectTemplatePreview.goBackTo": "<PERSON><PERSON><PERSON> k<PERSON> {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "CitizenLabin asiantunt<PERSON>", "app.components.ProjectTemplatePreview.infoboxLine1": "Haluatko käyttää tätä mallia osallistumisprojektissasi?", "app.components.ProjectTemplatePreview.infoboxLine2": "Ota yhteyttä kaupungin hallinnon vastuuhenkilöön tai ota yhteyttä numeroon {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Projektikansio", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Valittu päivämäärä on virheellinen. Anna päivämäärä seuraavassa muodossa: VVVV-KK-PP", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Valitse projektin aloituspäivä", "app.components.ProjectTemplatePreview.projectStartDate": "Projektin alkamispäivä", "app.components.ProjectTemplatePreview.projectTitle": "Projektisi nimi", "app.components.ProjectTemplatePreview.projectTitleError": "<PERSON><PERSON><PERSON><PERSON> projektin nimi", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "<PERSON><PERSON><PERSON><PERSON> projektin nimi kaikille kiel<PERSON>", "app.components.ProjectTemplatePreview.projectsOverviewPage": "hankkeiden yleiskuvaussivu", "app.components.ProjectTemplatePreview.responseError": "Hups! <PERSON><PERSON> meni pieleen.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "<PERSON><PERSON> lis<PERSON> malleja", "app.components.ProjectTemplatePreview.successMessage": "Projekti luotiin onnistuneesti!", "app.components.ProjectTemplatePreview.typeProjectName": "<PERSON><PERSON><PERSON><PERSON> projektin nimi", "app.components.ProjectTemplatePreview.useTemplate": "Käytä tätä mallia", "app.components.SeatInfo.additionalSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.SeatInfo.additionalSeatsToolTip": "Tämä näyttää ostamiesi lisäistuimien määrän \"Sisältyvät paikat\" -k<PERSON><PERSON> lis<PERSON>.", "app.components.SeatInfo.adminSeats": "<PERSON><PERSON> paikat", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} järjestelmänvalvojan paikkoja mukana", "app.components.SeatInfo.adminSeatsTooltip1": "Järjestelmänvalvojat ovat vastuussa alustasta ja heillä on hallintaoikeudet kaikille kansioille ja projekteille. Voit {visitHelpCenter} oppia lisää eri roolei<PERSON>.", "app.components.SeatInfo.currentAdminSeatsTitle": "Nykyiset j<PERSON>rjestelmänvalvojan paikat", "app.components.SeatInfo.currentManagerSeatsTitle": "Nykyiset manageripaikat", "app.components.SeatInfo.includedAdminToolTip": "Tämä näyttää vuosisopimukseen sisältyvien ylläpitäjien käytettävissä olevien paikkojen määrän.", "app.components.SeatInfo.includedManagerToolTip": "Tämä näyttää vuotuiseen sopimukseen sisältyvien johtajien käytettävissä olevien paikkojen määrän.", "app.components.SeatInfo.includedSeats": "<PERSON><PERSON><PERSON> is<PERSON>", "app.components.SeatInfo.managerSeats": "<PERSON><PERSON> pai<PERSON>", "app.components.SeatInfo.managerSeatsTooltip": "Kansio-/projektipäälliköt voivat hallita rajoittamattoman määrän kansioita/projekteja. Voit {visitHelpCenter} oppia lisää eri rooleista.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} manager<PERSON><PERSON><PERSON><PERSON> mukana", "app.components.SeatInfo.remainingSeats": "Jäljellä olevat istuimet", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.components.SeatInfo.totalSeats": "Istumapaikkoja yhteensä", "app.components.SeatInfo.totalSeatsTooltip": "Tämä näyttää suunnitelmasi ja ostamiesi lisäpaikkojen yhteenlasketun määrän.", "app.components.SeatInfo.usedSeats": "Käytetyt istuimet", "app.components.SeatInfo.view": "Näytä", "app.components.SeatInfo.visitHelpCenter": "k<PERSON><PERSON> ohje<PERSON>ksessamme", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "Suunnitelmassasi on {adminSeatsIncluded}. Kun olet käyt<PERSON>änyt kaikki istu<PERSON>t, lisäpaikkoja lisätään kohtaan \"Lisäpaikat\".", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on {manager<PERSON><PERSON><PERSON><PERSON>ncluded}, j<PERSON> on kelvollinen kansio- ja projektipäälliköille. Kun olet käyttänyt kaikki istu<PERSON>t, lisäpaikkoja lisätään kohta<PERSON> \"Lisäpaikat\".", "app.components.UserSearch.addModerators": "Lisätä", "app.components.UserSearch.searchUsers": "<PERSON>e käyttäjiä kirjo<PERSON>...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Vaihtoehtoinen virheilmoitus", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Oletuksena se<PERSON>ava virheilmoitus näytetään käyttäjille:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "<PERSON><PERSON>uta virheilmoitusta", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "Voit korvata tämän viestin jokaiselle kielelle käyttämällä alla olevaa \"Vaihtoehtoinen virheilmoitus\" -tekstiruutua. Jos jätät tekstikentän tyhj<PERSON>ks<PERSON>, oletusviesti tulee näkyviin.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "<PERSON><PERSON><PERSON>ä<PERSON> osallistu<PERSON>t näkevät, kun he eivät täytä osallistumisvaatimuksia.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "<PERSON><PERSON><PERSON> v<PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "Yhtään kysymystä ei valittu. Valitse ensin kysymys.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "<PERSON><PERSON> vast<PERSON>ta", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} <PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "<PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} t<PERSON><PERSON><PERSON><PERSON> asti", "app.components.admin.DatePhasePicker.Input.openEnded": "Avoin loppu", "app.components.admin.DatePhasePicker.Input.selectDate": "Valitse päivämäärä", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Tyhjennä lopetuspäivä", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Tyhjennä aloituspäivä", "app.components.admin.Graphs": "Tietoja ei ole saatavilla nykyisillä suodattimilla.", "app.components.admin.Graphs.noDataShort": "Tietoja ei ole saatavilla.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "<PERSON><PERSON><PERSON> ajan mit<PERSON>", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reak<PERSON><PERSON> ajan <PERSON>", "app.components.admin.InputManager.onePost": "1 sisääntulo", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Offline-valinna<PERSON> s<PERSON>", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Offline-äänien säätö", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "Tämän vaihtoehdon avulla voit sisällyttää osallistumistietoja muista lähteistä, kuten henkilökohtaisesta tai paperiäänestyksestä:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Se eroaa visuaalisesti digitaalisista äänistä.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Se vaikuttaa lopullisiin äänestystuloksiin.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "Se ei näy osallistumistietojen hallint<PERSON>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offline-<PERSON><PERSON><PERSON> vai<PERSON><PERSON><PERSON><PERSON><PERSON>aan as<PERSON>a vain kerran projektissa, ja ne jaetaan projektin kaikkien vaiheiden kesken.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "<PERSON>un on ensin s<PERSON>ötettävä offline-osallistujien kokonaismäärä.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Offline-osallistujat y<PERSON>sä", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "<PERSON><PERSON><PERSON> tulosten laskemiseksi meidän on tiedettävä tämän vaiheen <b>offline-osallist<PERSON>jien kokonaismäärä</b>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "<PERSON><PERSON><PERSON> vain offline-til<PERSON><PERSON> o<PERSON>.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "Muoka<PERSON>u {name}", "app.components.admin.PostManager.PostPreview.assignee": "Valtuutettu", "app.components.admin.PostManager.PostPreview.cancelEdit": "<PERSON><PERSON> muokkaus", "app.components.admin.PostManager.PostPreview.currentStatus": "Nyk<PERSON>nen tila", "app.components.admin.PostManager.PostPreview.delete": "Poistaa", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän s<PERSON>tteen? Tätä toimintoa ei voi kumota.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän syötteen? Syöte poistetaan kaikista projektin vai<PERSON>a, eikä sitä voi palauttaa.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "<PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "<PERSON><PERSON><PERSON> monta kertaa tämä on sisällytetty muiden osallistujien osallistumisbudjetteihin", "app.components.admin.PostManager.PostPreview.picks": "Valinnat: {picksNumber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "Reaktiomäärät:", "app.components.admin.PostManager.PostPreview.save": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "Lisää ominaisuustaso", "app.components.admin.PostManager.addFeatureLayerInstruction": "Kopioi ArcGIS Onlinessa isännöidyn ominaisuuskerroksen URL-osoite ja liitä se alla olevaan syötteeseen:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Lisää karttaan uusi ominaisuustaso", "app.components.admin.PostManager.addWebMap": "Lisää verkkokartta", "app.components.admin.PostManager.addWebMapInstruction": "Kopioi verkkokarttasi portaalitunnus ArcGIS Onlinesta ja liitä se alla olevaan syötteeseen:", "app.components.admin.PostManager.allPhases": "<PERSON><PERSON><PERSON> vaiheet", "app.components.admin.PostManager.allProjects": "Kaikki projektit", "app.components.admin.PostManager.allStatuses": "Kaikki tilat", "app.components.admin.PostManager.allTopics": "<PERSON><PERSON><PERSON> tun<PERSON>", "app.components.admin.PostManager.anyAssignment": "<PERSON><PERSON><PERSON> ta<PERSON>", "app.components.admin.PostManager.assignedTo": "Osoitettu {assigneeName}", "app.components.admin.PostManager.assignedToMe": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.assignee": "Valtuutettu", "app.components.admin.PostManager.authenticationError": "Tätä tasoa haettaessa tapahtui todennusvirhe. Tarkista URL-osoite ja että Esri API -avaimellasi on pääsy tähän tasoon.", "app.components.admin.PostManager.automatedStatusTooltipText": "Tämä tila päivittyy automaattisesti, kun ehdot täyttyvät", "app.components.admin.PostManager.bodyTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cancel": "<PERSON>utt<PERSON>", "app.components.admin.PostManager.cancel2": "Peruuta", "app.components.admin.PostManager.co-sponsors": "Yhteissponsorit", "app.components.admin.PostManager.comments": "Ko<PERSON>ntit", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "Tämä tark<PERSON>, että menetät kaikki näihin s<PERSON>tteisiin liittyvät tiedot, kuten kommentit, reaktiot ja äänet. Tätä toimintoa ei voi kumota.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa nämä s<PERSON>ötteet?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "Poista aihe", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "Yrität poistaa tämän idean vaiheesta, jossa se on saanut ääniä. <PERSON><PERSON> teet tämän, nämä äänet menetetään. <PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän idean tästä vaiheesta?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "<PERSON><PERSON><PERSON>ä<PERSON> ideaan liittyvät äänet menetetään", "app.components.admin.PostManager.components.goToInputManager": "<PERSON><PERSON><PERSON> hallintaan", "app.components.admin.PostManager.components.goToProposalManager": "<PERSON><PERSON><PERSON> hall<PERSON>", "app.components.admin.PostManager.contributionFormTitle": "Muokkaa panosta", "app.components.admin.PostManager.cost": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.createInput": "<PERSON><PERSON>", "app.components.admin.PostManager.createInputsDescription": "Luo uusi syötejoukko aiemmasta projektista", "app.components.admin.PostManager.currentLat": "Keskileveysaste", "app.components.admin.PostManager.currentLng": "Keskipituusaste", "app.components.admin.PostManager.currentZoomLevel": "Zoomaustaso", "app.components.admin.PostManager.defaultEsriError": "<PERSON><PERSON><PERSON><PERSON><PERSON> kerroksen noutamisessa tapahtui virhe. Tarkista verkkoyhteytesi ja että URL-osoite on oikea.", "app.components.admin.PostManager.delete": "Poistaa", "app.components.admin.PostManager.deleteAllSelectedInputs": "Poista {count} viestit", "app.components.admin.PostManager.deleteConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän tason?", "app.components.admin.PostManager.dislikes": "<PERSON>i pidä", "app.components.admin.PostManager.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "Muokkaa projekteja", "app.components.admin.PostManager.editStatuses": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editTags": "<PERSON><PERSON><PERSON><PERSON> tun<PERSON>a", "app.components.admin.PostManager.editedPostSave": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Tiet<PERSON>je<PERSON> tuonti Esri ArcGIS Onlinesta on lisäominaisuus. Keskustele GS-päällikön kanssa avataksesi sen.", "app.components.admin.PostManager.esriSideError": "ArcGIS-sovelluksessa tapahtui virhe. Odota muutama minuutti ja yritä myö<PERSON> u<PERSON>.", "app.components.admin.PostManager.esriWebMap": "Esri Web Map", "app.components.admin.PostManager.exportAllInputs": "<PERSON>ie kaikki viestit (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "<PERSON>ie kaikki kommentit (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Vie tämän projektin kommentit (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Vie viestit tässä projektissa (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Vie valitut viestit (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "<PERSON>ie valittujen viestien kommentit (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "<PERSON>ie äänet s<PERSON>tteen mukaan (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "<PERSON>ie ää<PERSON> k<PERSON>n mukaan (.xslx)", "app.components.admin.PostManager.exports": "Vienti", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Voit ladata karttatietoja vain joko GeoJSON-tasoina tai tuomalla ArcGIS Onlinesta. Poista kaikki nykyiset GeoJSON-tasot, jos haluat lisät<PERSON> ominaisuuskerroksen.", "app.components.admin.PostManager.featureLayerTooltop": "Löydät ominaisuuskerroksen URL-osoitteen ArcGIS Onlinen tuotesivun oikealta puolelta.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON>, miten ihmiset näkevät nimesi", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Selitä tämä tilamuutos", "app.components.admin.PostManager.fileUploadError": "<PERSON><PERSON>den tai useamman tiedoston lataaminen epäonnistui. Tarkista tiedoston koko ja muoto ja yritä uudelleen.", "app.components.admin.PostManager.formTitle": "<PERSON><PERSON><PERSON><PERSON> ideaa", "app.components.admin.PostManager.generalApiError2": "<PERSON><PERSON><PERSON> kohdetta haettaessa tapahtui virhe. Tarkista, että URL-osoite tai portaalin tunnus on o<PERSON>a ja että sinulla on pä<PERSON><PERSON> tähän kohteeseen.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Voit ladata karttatietoja vain joko GeoJSON-tasoina tai tuomalla ArcGIS Onlinesta. Poista kaikki ArcGIS-tiedot, jos haluat ladata GeoJSON-<PERSON><PERSON><PERSON><PERSON>.", "app.components.admin.PostManager.goToDefaultMapView": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.hiddenFieldsLink": "piilotetut kentät", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Vinkki: <PERSON><PERSON>, lisä<PERSON> {hiddenFieldsLink} seura<PERSON>ks<PERSON> kyselyysi vastanneista.", "app.components.admin.PostManager.import2": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.importError": "Valittua tiedostoa ei voitu tuoda, koska se ei ole kelvollinen GeoJSON-tiedosto", "app.components.admin.PostManager.importEsriFeatureLayer": "<PERSON><PERSON> Layer", "app.components.admin.PostManager.importEsriWebMap": "Tuo Esri Web Map", "app.components.admin.PostManager.importInputs": "<PERSON><PERSON>", "app.components.admin.PostManager.imported": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} / {totalCount} {totalCount, plural, one {syötettä on} other {syötettä on}} tuotu. <PERSON><PERSON><PERSON> on viel<PERSON> kesken, tarkista tilanne my<PERSON>min uude<PERSON>.", "app.components.admin.PostManager.inputManagerHeader": "Syöte", "app.components.admin.PostManager.inputs": "Syöte", "app.components.admin.PostManager.inputsExportFileName": "syöttö", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Näyt<PERSON> vain viestit, jotka tarvi<PERSON><PERSON><PERSON> palautetta", "app.components.admin.PostManager.issueFormTitle": "<PERSON><PERSON><PERSON><PERSON> on<PERSON>", "app.components.admin.PostManager.latestFeedbackMode": "Käytä viimeisintä olemassa olevaa virallista päivitystä selityksenä", "app.components.admin.PostManager.layerAdded": "<PERSON><PERSON> l<PERSON>", "app.components.admin.PostManager.likes": "Tykkää", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Tämän syötteen siirtäminen pois nykyisestä projektista menettää tiedot sille osoitetuista vaiheista. Haluatko edetä?", "app.components.admin.PostManager.mapData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.multipleInputs": "{ideaCount} viestiä", "app.components.admin.PostManager.newFeedbackMode": "<PERSON><PERSON><PERSON><PERSON> uusi pä<PERSON>s selittääksesi tämän muutoksen", "app.components.admin.PostManager.noFilteredResults": "Valitsemasi suodattimet eivät palauttaneet tuloksia", "app.components.admin.PostManager.noInputs": "Ei vielä s<PERSON>ötteitä", "app.components.admin.PostManager.noInputsDescription": "Voit lisätä oman panoksesi tai aloittaa aiemmasta osallistumisprojektista.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 syötettä} one {1 syöte} other {# syötettä}} tuodaan valitusta projektista ja vaiheesta. <PERSON><PERSON>i suoritetaan taustalla, ja syötteet näkyvät syötteiden hallinnassa, kun se on valmis.", "app.components.admin.PostManager.noOne": "<PERSON><PERSON>", "app.components.admin.PostManager.noProject": "Ei projektia", "app.components.admin.PostManager.officialFeedbackModal.author": "Tekijä", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "<PERSON><PERSON><PERSON>, miten nimesi näkyy", "app.components.admin.PostManager.officialFeedbackModal.description": "Virallisen palautteen antaminen auttaa pitämään prosessin läpinäkyvänä ja lisää luottamusta alustaa kohta<PERSON>.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Tekijä vaaditaan", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "<PERSON><PERSON><PERSON><PERSON> tarvi<PERSON>", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Vira<PERSON><PERSON> palaute", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Selitä tilanmu<PERSON>ksen s<PERSON>y", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "Lähetä palautetta", "app.components.admin.PostManager.officialFeedbackModal.skip": "<PERSON><PERSON> tällä kertaa", "app.components.admin.PostManager.officialFeedbackModal.title": "Perustele päätöksesi", "app.components.admin.PostManager.officialUpdateAuthor": "<PERSON><PERSON><PERSON>, miten ihmiset näkevät nimesi", "app.components.admin.PostManager.officialUpdateBody": "Selitä tämä tilamuutos", "app.components.admin.PostManager.offlinePicks": "Offline-valinnat", "app.components.admin.PostManager.offlineVotes": "Offline-äänet", "app.components.admin.PostManager.onlineVotes": "Verkkoäänestykset", "app.components.admin.PostManager.optionFormTitle": "Muokkaa v<PERSON>", "app.components.admin.PostManager.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicks": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Online-vali<PERSON>t", "app.components.admin.PostManager.pbItemCountTooltip": "<PERSON><PERSON><PERSON> monta kertaa tämä on sisällytetty muiden osallistujien osallistumisbudjetteihin", "app.components.admin.PostManager.petitionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.postedIn": "Lähetetty {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Muokkaa projektia", "app.components.admin.PostManager.projectsTab": "Projektit", "app.components.admin.PostManager.projectsTabTooltipContent": "Voit siirtää viestejä projektista toiseen vetämällä ja pudotta<PERSON>. <PERSON><PERSON><PERSON>, että aikajanaprojekteissa sinun on silti lisättävä viesti tiettyyn vaiheeseen.", "app.components.admin.PostManager.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.proposedBudgetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> bud<PERSON>ti", "app.components.admin.PostManager.publication_date": "Julkaistu", "app.components.admin.PostManager.questionFormTitle": "Muokkaa kysymystä", "app.components.admin.PostManager.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetFiltersButton": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.resetInputFiltersDescription": "<PERSON><PERSON><PERSON> su<PERSON> n<PERSON>däks<PERSON> kaikki tulot.", "app.components.admin.PostManager.saved": "Tallennettu", "app.components.admin.PostManager.screeningTooltip": "Seulonta ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Se<PERSON>ta on kytketty pois päältä tässä vaiheessa. Ota se käyttöön siirtymällä vaiheasetuksiin", "app.components.admin.PostManager.selectAPhase": "Valitse vaihe", "app.components.admin.PostManager.selectAProject": "Valitse projekti", "app.components.admin.PostManager.setAsDefaultMapView": "Tallenna nykyinen keskipiste ja zoomaustaso kartan oletusasetuksiksi", "app.components.admin.PostManager.startFromPastInputs": "Aloita aiemmista syötteistä", "app.components.admin.PostManager.statusChangeGenericError": "Tapaht<PERSON> virhe. Yritä myöhemmin uudelleen tai ota yhteyttä tukeen.", "app.components.admin.PostManager.statusChangeSave": "<PERSON><PERSON><PERSON><PERSON> tilaa", "app.components.admin.PostManager.statusesTab": "Tila", "app.components.admin.PostManager.statusesTabTooltipContent": "<PERSON>uta viestin tilaa vetämällä ja pudottamalla. Alkuperäinen kirjoittaja ja muut kirjoittajat saavat ilmoit<PERSON>sen muuttuneesta tilasta.", "app.components.admin.PostManager.submitApiError": "Lomakkeen lähettämisessä oli on<PERSON>. Tarkista mahdolliset virheet ja yritä uudelleen.", "app.components.admin.PostManager.timelineTab": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.timelineTabTooltipText": "Vedä ja pudota viestejä kopioidaksesi ne projektin eri vaiheisiin.", "app.components.admin.PostManager.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.topicsTabTooltipText": "Lisää tunnisteita syötteeseen vetämällä ja pudotta<PERSON>.", "app.components.admin.PostManager.view": "Näytä", "app.components.admin.PostManager.votes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.votesByInputExportFileName": "äänet_tulon mukaan", "app.components.admin.PostManager.votesByUserExportFileName": "käyttäjä<PERSON>", "app.components.admin.PostManager.webMapAlreadyExists": "Voit lisätä vain yhden verkkokartan kerral<PERSON>an. Poista nykyinen tuodaksesi toisen.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Voit ladata karttatietoja vain joko GeoJSON-tasoina tai tuomalla ArcGIS Onlinesta. Poista kaikki nykyiset GeoJSON-tasot, jos haluat yhdistää verkkokartan.", "app.components.admin.PostManager.webMapTooltip": "Löydät Web Map -port<PERSON><PERSON> tunnuksen ArcGIS Online -kohdesivultasi oikealta.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON> p<PERSON>} one {<PERSON><PERSON><PERSON> päiv<PERSON>} other {# päivää}} j<PERSON><PERSON><PERSON><PERSON>ä", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "<PERSON>utt<PERSON>", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON><PERSON><PERSON>, poista kys<PERSON>n tulo<PERSON>", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "Tätä ei voi kumota", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Poista kyselyn t<PERSON>", "app.components.admin.ProjectEdit.survey.downloadResults2": "Lataa k<PERSON><PERSON> t<PERSON>", "app.components.admin.ReportExportMenu.FileName.fromFilter": "alkaen", "app.components.admin.ReportExportMenu.FileName.groupFilter": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.ReportExportMenu.FileName.projectFilter": "hanke", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "si<PERSON>en asti kun", "app.components.admin.ReportExportMenu.downloadPng": "Lataa PNG-muodossa", "app.components.admin.ReportExportMenu.downloadSvg": "Lataa SVG-muodossa", "app.components.admin.ReportExportMenu.downloadXlsx": "Lataa Excel", "app.components.admin.SlugInput.regexError": "Slug voi sisältää vain tavallisia pieniä kirjaimia (az), numeroita (0-9) ja yhdysmerkkejä (-). Ensimmäinen ja viimeinen merkki eivät voi olla yhdysmerkkejä. Peräkkäiset yhdysmerkit (--) ovat kiellettyjä.", "app.components.admin.TerminologyConfig.saveButton": "<PERSON><PERSON><PERSON>", "app.components.admin.commonGroundInputManager.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.seatSetSuccess.admin": "Admin", "app.components.admin.seatSetSuccess.allDone": "Valmista", "app.components.admin.seatSetSuccess.close": "kiinni", "app.components.admin.seatSetSuccess.manager": "Manager", "app.components.admin.seatSetSuccess.orderCompleted": "<PERSON><PERSON><PERSON> su<PERSON>", "app.components.admin.seatSetSuccess.reflectedMessage": "Suunnitelmasi muutokset näkyvät seuraavassa laskutus<PERSON>si.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} o<PERSON><PERSON><PERSON> on myönnetty valituille käyttäjille.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>ti poistaa kaikki kyselyn tulo<PERSON>?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Tämä osallistumistapa on beta-vaiheessa. Otamme sitä käyttöön vähitellen kerätäksemme palautetta ja parantaaksemme käyttökokemusta.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Palautteen kerääminen asiakirjasta on mukautettu ominaisuus, eikä se sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Päivien lukumäärä vaaditaan", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "Päivien määrä äänien vähimmäismäärän saavuttamiseen", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Lisätietoja linkin upottamisesta Google Formsille on osoitteessa {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/articles/5050525", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "täm<PERSON> tukiart<PERSON><PERSON>i", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Idea", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Aloite", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Millä nimellä s<PERSON>ötettä pitäisi kutsua?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "<PERSON>ki <PERSON>-asiakirja<PERSON> täh<PERSON>n. Lue {supportArticleLink} saadaksesi lisätietoja Konveion määrittämisestä.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Tämä ei sisälly nykyiseen suunnitelmaasi. Ota yhteyttä Government Success Manageriin tai järjestelmänvalvojaan avataksesi sen.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Enimmäisbudjetti vaaditaan", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Valinnan äänten enimmäismäärän on oltava pienempi tai yhtä suuri kuin äänten kokonaismäärä", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Äänten enimmäismäärä vaaditaan", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "Viestit", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Vähimmäisbudjetti ei voi olla suurempi kuin enimmäisbudjetti", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Vähimmäisbudjetti vaaditaan", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Äänten vähimmäismäärä ei voi olla suurempi kuin enimmäismäärä", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "Vähimmäismäärä ääniä vaaditaan", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "Päättymispäivä puuttuu", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "Aloituspäivä puuttuu", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "<PERSON><PERSON><PERSON>oeht<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Input Manager -<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Määritä äänestysasetukset Input manager -välilehdellä vaiheen luomisen jälkeen.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Määritä äänestysasetukset kohdassa {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Osallistumisvaihtoehdot", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "Järjestelmänvalvojat ja johtajat", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} voi osallistua tähän vaiheeseen.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "Peruuta", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Kommentti:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>e", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Poista vaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON><PERSON><PERSON>, poista tämä vaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän vaiheen?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "<PERSON><PERSON><PERSON> tähän vaiheeseen liittyvät tiedot poistetaan. Tätä ei voi peruuttaa.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "<PERSON><PERSON><PERSON><PERSON> tut<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Ideointivaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Tiedotusvaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Sekalaiset oikeudet", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "<PERSON><PERSON> lopet<PERSON>päivä<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Äänestysvaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "<PERSON><PERSON><PERSON><PERSON><PERSON> vaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Reagoi:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tap<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Rekisteröityneet käyttäjät", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Lähetä syötteet:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b><PERSON><PERSON><PERSON> k<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b><PERSON><PERSON><PERSON> k<PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> on vahvistettu sä<PERSON>köposti", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b>Vapaaehtoistyö:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Vapaaehtoistyövaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Äänestys:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Äänestysvaihe", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Kuka voi osallistua?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Syötteet eivät näy ennen kuin järjestelmänvalvoja tarkistaa ja hyväksyy ne. Tekijät eivät voi muokata syötteitä sen jälkeen, kun ne on tarkastettu tai reagoitu.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "<PERSON><PERSON> j<PERSON>lmänvalvojat", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "<PERSON><PERSON><PERSON> linkin saaneet voivat olla vuorovaikutuksessa projektin luonnoksen kanssa", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Hyväksy", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Hyväksyminen antaa projektipäälliköille mahdollisuuden julkaista projektin.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Hyvä<PERSON><PERSON><PERSON> {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "Luonnos", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "<PERSON>ok<PERSON><PERSON> kuva<PERSON>a", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Piilotettu", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Offline-äänestäjät", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Vain jär<PERSON>stelmänvalvojat{inFolder, select, true { tai kansion<PERSON>t} other {}} voivat julkaista projektin", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 osallistuja} other {{participantsCount} osallistujaa}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> menetelmiin (esim. ulko<PERSON><PERSON> tut<PERSON>)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Huomautus: Anonyymien tai avointen osallistumisoikeuksien salliminen voi antaa käyttäjille mahdollisuuden osallistua useita kertoja, mi<PERSON><PERSON> joh<PERSON><PERSON> har<PERSON>johtaviin tai epätäydellisiin käyttäjätietoihin.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <b>eivät sisällä</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ovat:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jotka ovat vuorovaikutuksessa Go <PERSON> -menetelmien kanssa", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "Odotetaan hyväksyntää", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "Hankkeen arvioijille on ilmoitettu.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Julkai<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Julkaistu - aktiivinen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Julkaistu - Valmis", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Päivitä projektin esikatselulinkki", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Luo projektin esikatselulinkki uudelleen. Tämä mitätöi edellisen linkin.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "<PERSON><PERSON> linkit lakka<PERSON><PERSON>, mutta voit luoda uuden milloin ta<PERSON>.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "<PERSON><PERSON><PERSON> varma? Tämä poistaa nykyisen linkin käytöstä", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "Peruuta", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON><PERSON><PERSON>, päivitä linkki", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Pyydä hyväksyntää", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Järjestelmänvalvojan{inFolder, select, true { tai jonkin kansion ylläpitäjistä} other {}} on hyväksyttävä projekti ennen kuin voit julkaista sen. Pyydä hyväksyntää napsauttamalla alla olevaa painiketta.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Asetukset", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Jaa", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Yksityisten linkkien jakaminen ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Jaa tämä projekti", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Kenellä on pääsy", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Näytä", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekti", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Huomioon otettavien äänten vähimmäismäärä", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "Äänten vähimmäismäärä vaaditaan", "app.components.app.containers.AdminPage.ProjectEdit.report": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Liikenne", "app.components.formBuilder.cancelMethodChange1": "Peruuta", "app.components.formBuilder.changeMethodWarning1": "Menetelmien muuttaminen voi johtaa edellisen menetelmän käytön aikana luodun tai vastaanotetun syötetietojen piilottamiseen.", "app.components.formBuilder.changingMethod1": "<PERSON><PERSON><PERSON><PERSON><PERSON>lmä", "app.components.formBuilder.confirmMethodChange1": "Kyllä, jatka", "app.components.formBuilder.copySurveyModal.cancel": "Peruuta", "app.components.formBuilder.copySurveyModal.description": "Tämä kopioi kaikki kysymykset ja logiikan ilman vastauksia.", "app.components.formBuilder.copySurveyModal.duplicate": "Ko<PERSON>i", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "Tästä projektista ei löytynyt sopivia vaiheita", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Vaihetta ei ole valittu. Valitse ensin vaihe.", "app.components.formBuilder.copySurveyModal.noProject": "Ei projektia", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Projektia ei ole valittu. Valitse ensin projekti.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "<PERSON>t jo tallentanut muutokset tähän kysely<PERSON>. <PERSON><PERSON> kop<PERSON>it toisen kyselyn, muutokset menetetään.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Kyselyvaihe", "app.components.formBuilder.copySurveyModal.title": "Valitse kopioitava kysely", "app.components.formBuilder.editWarningModal.addOrReorder": "Lisää tai järjestä kysymyksiä uudelleen", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Vastaustietosi voivat olla epätarkkoja", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Muokkaa tekstiä", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>? Se ei vaikuta vast<PERSON>", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Menetät tähän kysymykseen linkitetyt vastaustiedot", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Poista k<PERSON>ymys", "app.components.formBuilder.editWarningModal.exportYouResponses2": "vie vastau<PERSON>.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Varoitus: <PERSON><PERSON><PERSON> menettää vastaustiedot pysyvästi. <PERSON>nen kuin jatkat,", "app.components.formBuilder.editWarningModal.noCancel": "Ei, peruuta", "app.components.formBuilder.editWarningModal.title4": "Muokkaa live-kyselyä", "app.components.formBuilder.editWarningModal.yesContinue": "Kyllä, jatka", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "tä<PERSON>än kyselyn k<PERSON>yttöoikeusasetukset", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "Väestötietokentät kyselylomakkeessa on käytössä. Kun kyselylomake tulee näkyviin, kaikki määritetyt demografiset kysymykset lisätään uudelle sivulle välittömästi ennen kyselyn loppua. Näitä kysymyksiä voi muuttaa kohdassa {accessRightsSettingsLink}.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "käyttöoikeusasetukset tälle vai<PERSON>lle.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "<PERSON><PERSON><PERSON><PERSON> vast<PERSON>en ei tarvitse rekisteröityä tai kirjautua sisään lähettääkseen kyselyvastauksia, mik<PERSON> voi johtaa päällekkäisiin lähetyksiin", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Ohitamalla rekisteröitymis-/sisäänkirjautumisvaiheen hyväksyt, että et kerää väestötietoja kyselyyn vast<PERSON>, mikä saattaa vaikuttaa data-analyysikykyysi.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "<PERSON><PERSON><PERSON><PERSON> kysely on as<PERSON>ttu sallimaan pä<PERSON><PERSON> \"Kaikelle\" Käyttöoikeudet-välilehdellä.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "<PERSON><PERSON> haluat muuttaa tätä, voit tehdä sen kohdassa {accessRightsSettingsLink}", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Esität seuraavat demografiset kysymykset kyselyyn vastanneilta rekisteröitymis-/kirjautumisvaiheessa.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Demografisten tietojen keruun tehostamiseksi ja niiden integroimisen varmistamiseksi käyttäjätietokantaasi suosittelemme sisällyttämään kaikki demografiset kysymykset suoraan rekisteröitymis-/kirjautumisprosessiin. Käytä tätä varten {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Pyydä k<PERSON>täjiä seuraamaan alueita tai aiheita", "app.components.onboarding.followHelperText": "Tämä aktivoi rekisteröintiprosessin vaiheen, jossa k<PERSON>yttäjät voivat seurata alla valitsemiasi alueita tai aiheita", "app.components.onboarding.followPreferences": "<PERSON><PERSON><PERSON>", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} su<PERSON><PERSON><PERSON><PERSON>, {noOfAdditionalSeats} y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.seatsWithinPlan.seatsWithinPlanText": "Istuimet suunnitelman sisällä", "app.containers.Admin.Campaigns.campaignFrom": "Lähettäjä:", "app.containers.Admin.Campaigns.campaignTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.containers.Admin.Campaigns.customEmails": "Mukautetut sähköpostit", "app.containers.Admin.Campaigns.customEmailsDescription": "Lähetä mukautettuja sähköposteja ja tarkista tilastot.", "app.containers.Admin.Campaigns.noAccess": "<PERSON><PERSON>, mutta näyttää siltä, että sinulla ei ole pääsyä sähköpostiosaan", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automaattiset sähköpostit", "app.containers.Admin.Insights.tabReports": "Raportit", "app.containers.Admin.Invitations.a11y_removeInvite": "Poista kutsu", "app.containers.Admin.Invitations.addToGroupLabel": "Lisää nämä ihmiset tiettyihin manuaalisiin käyttäjäryhmiin", "app.containers.Admin.Invitations.adminLabel1": "<PERSON> kutsut<PERSON>le j<PERSON>rjestelmänvalvo<PERSON>t", "app.containers.Admin.Invitations.adminLabelTooltip": "Kun valitset tämän vai<PERSON>, kutsumasi ihmiset pääsevät käyttämään kaikkia alustaasetuksiasi.", "app.containers.Admin.Invitations.configureInvitations": "3. Mää<PERSON><PERSON> kutsut", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "<PERSON><PERSON><PERSON> vast<PERSON> kutsuja ei ole", "app.containers.Admin.Invitations.deleteInvite": "Poistaa", "app.containers.Admin.Invitations.deleteInviteConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kutsun?", "app.containers.Admin.Invitations.deleteInviteTooltip": "Peruuttamalla kutsun voit lähettää kutsun uudelleen tälle henkilölle.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. <PERSON><PERSON><PERSON> ja täytä malli", "app.containers.Admin.Invitations.downloadTemplate": "Lataa malli", "app.containers.Admin.Invitations.email": "Sähköposti", "app.containers.Admin.Invitations.emailListLabel": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>i niiden ihmisten sähköpostiosoitteet, jotka haluat kutsua. Erottele jokainen osoite pilkulla.", "app.containers.Admin.Invitations.exportInvites": "<PERSON>ie kaikki kutsut", "app.containers.Admin.Invitations.fileRequirements": "Tärkeää: <PERSON><PERSON> kutsut lähetettäisi<PERSON>, tuontimallista ei voi poistaa sarak<PERSON>. Jätä käyttämättömät sarakkeet tyhjiksi.", "app.containers.Admin.Invitations.filetypeError": "Väärä tiedostotyyppi. Vain XLSX-tied<PERSON><PERSON> tue<PERSON>.", "app.containers.Admin.Invitations.groupsPlaceholder": "Ryhmää ei ole valittu", "app.containers.Admin.Invitations.helmetDescription": "Kutsu käyttäjiä alustalle", "app.containers.Admin.Invitations.helmetTitle": "Järjestelmänvalvojan kutsun hallintapaneeli", "app.containers.Admin.Invitations.importOptionsInfo": "Nämä vaihtoehdot otetaan huomioon vain, jos niitä ei ole määritetty Excel-tiedostossa.\n      Saat lisätietoja käymällä osoitteessa {supportPageLink} .", "app.containers.Admin.Invitations.importTab": "<PERSON><PERSON> s<PERSON>köpostiosoitteet", "app.containers.Admin.Invitations.invitationExpirationWarning": "<PERSON><PERSON><PERSON>, että kutsut vanhenevat 30 päivän kuluttua. Tämän a<PERSON> jälkeen voit edelleen lähettää ne uudelleen.", "app.containers.Admin.Invitations.invitationOptions": "Kutsuvaihtoehdot", "app.containers.Admin.Invitations.invitationSubtitle": "Kutsu ihmisiä alustalle milloin tahansa. He saavat neutra<PERSON>n kutsusähköpostin logollas<PERSON>, jossa heitä pyydetään rekisteröitymään alustalle.", "app.containers.Admin.Invitations.invitePeople": "Kutsu ihmisiä sähköpostitse", "app.containers.Admin.Invitations.inviteStatus": "Tila", "app.containers.Admin.Invitations.inviteStatusAccepted": "Hyväksytty", "app.containers.Admin.Invitations.inviteStatusPending": "Odottaa", "app.containers.Admin.Invitations.inviteTextLabel": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> viesti, joka lisätään kutsupostiin.", "app.containers.Admin.Invitations.invitedSince": "Kutsuttu", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Valitse kutsun kieli", "app.containers.Admin.Invitations.moderatorLabel": "<PERSON> n<PERSON>ille ihmisille projektin<PERSON>", "app.containers.Admin.Invitations.moderatorLabelTooltip": "Kun valitset tämän v<PERSON>, kutsutuille henkilöille myönnetään projektipäällikön oikeudet valittuihin projekteihin. Lisätietoja projektipäällikön oikeuksista {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "tässä", "app.containers.Admin.Invitations.name": "<PERSON><PERSON>", "app.containers.Admin.Invitations.processing": "Kutsujen lähettäminen. Odota...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "Ei valittuja projekteja", "app.containers.Admin.Invitations.save": "Lähetä kutsut", "app.containers.Admin.Invitations.saveErrorMessage": "Tapahtui yksi tai useampi virhe, e<PERSON><PERSON> kutsuja lähe<PERSON>. Korjaa alla luetellut virheet ja yritä uudelleen.", "app.containers.Admin.Invitations.saveSuccess": "<PERSON>est<PERSON>!", "app.containers.Admin.Invitations.saveSuccessMessage": "Kutsu lähetetty onnistuneesti.", "app.containers.Admin.Invitations.supportPage": "tukisivu", "app.containers.Admin.Invitations.supportPageLinkText": "<PERSON><PERSON><PERSON> tuki<PERSON>lla", "app.containers.Admin.Invitations.tabAllInvitations": "<PERSON><PERSON><PERSON> kutsut", "app.containers.Admin.Invitations.tabInviteUsers": "Kutsu käyttäjiä", "app.containers.Admin.Invitations.textTab": "Syötä sähköpostiosoitteet manuaalisesti", "app.containers.Admin.Invitations.unknownError": "<PERSON><PERSON> meni pieleen. Yritä uudelleen myö<PERSON>.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. <PERSON><PERSON><PERSON> valmis mallitied<PERSON>o", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} , jos haluat lisätietoja kaikista tuontimallin tuetuista sarakkeista.", "app.containers.Admin.Moderation.all": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.belongsTo": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.collapse": "nähdä vähemmän", "app.containers.Admin.Moderation.comment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.commentDeletionCancelButton": "<PERSON>utt<PERSON>", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Poistaa", "app.containers.Admin.Moderation.confirmCommentDeletion": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kommentin? Tämä on pysyvä toiminto, eikä sitä voi perua.", "app.containers.Admin.Moderation.content": "Sisältö", "app.containers.Admin.Moderation.date": "Päivämäärä", "app.containers.Admin.Moderation.deleteComment": "Poista kommentti", "app.containers.Admin.Moderation.goToComment": "Avaa tämä kommentti uudessa välilehdessä", "app.containers.Admin.Moderation.goToPost": "Avaa tämä viesti uudessa välilehdessä", "app.containers.Admin.Moderation.goToProposal": "Avaa tämä ehdotus uudessa välilehdessä", "app.containers.Admin.Moderation.markFlagsError": "Kohteita ei voitu merkitä. Yritä uudelleen.", "app.containers.Admin.Moderation.markNotSeen": "Merkitse {selectedItemsCount, plural, one {# kohde} other {# kohdetta}} näkymättömäksi", "app.containers.Admin.Moderation.markSeen": "Merkitse {selectedItemsCount, plural, one {# kohde} other {# kohdetta}} nähdyksi", "app.containers.Admin.Moderation.moderationsTooltip": "Tällä sivulla voit tarkistaa nopeasti kaikki alustallesi julkaistut uudet viestit, mukaan lukien ideat ja kommentit. Voit merkitä viestit nähdyiksi, jotta muut tietävät, mitä on vielä käsiteltävä.", "app.containers.Admin.Moderation.noUnviewedItems": "Näkymättömiä kohteita ei ole", "app.containers.Admin.Moderation.noViewedItems": "<PERSON>i näky<PERSON>t kohteita", "app.containers.Admin.Moderation.pageTitle1": "Syötä", "app.containers.Admin.Moderation.post": "Lähettää", "app.containers.Admin.Moderation.profanityBlockerSetting": "<PERSON><PERSON><PERSON><PERSON> esto", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "<PERSON><PERSON><PERSON>, jotka sisältävät yleisimmin raportoituja loukkaavia sanoja.", "app.containers.Admin.Moderation.project": "Projekti", "app.containers.Admin.Moderation.read": "Nähty", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON> l<PERSON>", "app.containers.Admin.Moderation.removeFlagsError": "Varoituksia ei voitu poistaa. Yritä u<PERSON>.", "app.containers.Admin.Moderation.rowsPerPage": "Rivit per sivu", "app.containers.Admin.Moderation.settings": "asetukset", "app.containers.Admin.Moderation.settingsSavingError": "<PERSON>i voitu tallentaa. Yritä muuttaa as<PERSON>ta u<PERSON>.", "app.containers.Admin.Moderation.show": "Näytä", "app.containers.Admin.Moderation.status": "Tila", "app.containers.Admin.Moderation.successfulUpdateSettings": "Asetukset päivitetty onnistuneesti.", "app.containers.Admin.Moderation.type": "Tyyppi", "app.containers.Admin.Moderation.unread": "<PERSON><PERSON> n<PERSON>hty", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Tämä sivu koostuu seuraavista osioista. Voit kytke<PERSON> ne päälle/pois ja muokata niitä tarpeen mukaan.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Osat", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Näytä sivu", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "<PERSON>i näy sivulla", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Näytetää<PERSON> sivulla", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "Li<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "<PERSON><PERSON><PERSON><PERSON> (max. 50 Mt), jotka ovat ladatt<PERSON>ssa sivulta.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Lisää oma sisältösi muokattavaan osioon sivun alareuna<PERSON>.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Tapahtumaluettelo", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Näytä projekteihin liittyvät tapahtumat.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Sankaribanneri", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "<PERSON><PERSON><PERSON> sivun bannerin kuvaa ja tekstiä.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Projektien luettelo", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Näytä projektit sivuasetustesi perusteella. Voit es<PERSON>ella näytettäviä projekteja.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Tärkeimmät tiedot -osio", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Lisää oma sisältösi muokattavaan osioon sivun yläreunassa.", "app.containers.Admin.PagesAndMenu.addButton": "Lisää navigointipalkkiin", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän sivun? Tätä ei voi peruuttaa.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "<PERSON> ka<PERSON> k<PERSON>", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Muut saatavilla olevat sivut", "app.containers.Admin.PagesAndMenu.components.savePage": "<PERSON><PERSON><PERSON> sivu", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "<PERSON>vu tallennettu onnist<PERSON>esti", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "Liitteet (enintään 50 Mt)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "<PERSON><PERSON><PERSON> tall<PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Sisältö", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Liitteitä ei voitu tallentaa", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Tiedostot eivät saa olla suurempia kuin 50 Mt. Lisätyt tiedostot näkyvät tämän sivun alao<PERSON>ssa", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "<PERSON><PERSON><PERSON> tallenne<PERSON>u", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Liitteet | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "Li<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Tallenna ja ota liitteet k<PERSON>yttöön", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "<PERSON><PERSON><PERSON> l<PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "Ta<PERSON>joa sisältöä kaikille kielille", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "<PERSON><PERSON><PERSON> tall<PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Sisältö", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Alempaa tietoosiota ei voitu tallentaa", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Tallenna ja ota k<PERSON>öö<PERSON> alempi tieto<PERSON>io", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "<PERSON><PERSON><PERSON> alin <PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Mukautettujen sivujen luominen ei sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Valitse vähintään yksi tunniste", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "<PERSON><PERSON><PERSON> tall<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON><PERSON><PERSON> mukaan", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "tun<PERSON><PERSON><PERSON> mukaan", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Projektien näyttäminen tunnisteen tai alueen mukaan ei ole osa nykyistä lisenssiäsi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Sisältö", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Muokkaa mukautettua sivua", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Linkitetyt projektit", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "<PERSON><PERSON><PERSON>, mitkä projektit ja niihin liittyvät tapahtumat voidaan näyttää sivulla.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "<PERSON>vu luotu on<PERSON>i", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "<PERSON>vu tallennettu onnist<PERSON>esti", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Mukautettu sivu tallennettu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "<PERSON>tsik<PERSON> na<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "<PERSON>o mukautettu sivu | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "<PERSON><PERSON> mukautettu sivu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON> mit<PERSON>n", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "<PERSON><PERSON><PERSON> muka<PERSON>u sivu", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "Valitse alue", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "<PERSON><PERSON><PERSON> al<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "<PERSON><PERSON><PERSON> tunnist<PERSON>t", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Slug voi sisältää vain tavallisia pieniä kirjaimia (az), numeroita (0-9) ja yhdysmerkkejä (-). Ensimmäinen ja viimeinen merkki eivät voi olla yhdysmerkkejä. Peräkkäiset yhdysmerkit (--) ovat kiellettyjä.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "<PERSON>un on syötettävä etana", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> ka<PERSON> kiel<PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Näytä mukautettu sivu", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Muokkaa mukautettua sivua | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "<PERSON><PERSON><PERSON> si<PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.automatedEmailsLinkText": "automaattiset sähköpostit", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Yksivaiheisissa projekteissa, jos lo<PERSON>päivämäärä on tyhjä ja kuvausta ei ole tä<PERSON>, aikajanaa ei näytetä projektisivulla.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "Ei saatavilla olevia projekteja {pageSettingsLink}perusteella.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Tässä projektissa ei ole tunnistetta tai alues<PERSON>da<PERSON>ta, joten projekteja ei näytetä.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "sivun as<PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projektien luettelo", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Seuraavat projektit näytetään tällä sivulla {pageSettingsLink}perusteella.", "app.containers.Admin.PagesAndMenu.defaultTag": "OLETUS", "app.containers.Admin.PagesAndMenu.deleteButton": "Poistaa", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "<PERSON><PERSON><PERSON> tall<PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerError": "Sankaribanneria ei voitu tallentaa", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "Sankaribanneri tallennettu", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.homeTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "Tarjoa sisältöä vähintään yhdelle kielelle", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Voit lisätä enintään 5 kohdetta navigointipalkkiin", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Sivut ja valikko | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Poista navigointipalkista", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Tallenna ja ota k<PERSON><PERSON> sank<PERSON>", "app.containers.Admin.PagesAndMenu.title": "Sivut ja valikko", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "<PERSON><PERSON><PERSON> tall<PERSON>", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Sisältö", "app.containers.Admin.PagesAndMenu.topInfoError": "Tärkeimmän tietoosion tallentaminen epäonnistui", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Tärkeimmät tiedot -<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Top info-osio | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Tärkeimmät tiedot -osio", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Tallenna ja ota k<PERSON>töön tärkeimmät tiedot -osio", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Tallenna tärkeimmät tiedot -osio", "app.containers.Admin.PagesAndMenu.viewButton": "Näytä", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Ikä", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Yhteisö", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Tiivistelmä", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> osallistamisindikaattoreita ja korostetaan edistymistäsi osallistavamman ja edustavamman osallistumisalustan edistämisessä.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Seuraavassa osiossa ha<PERSON> tärkeimmät osallistumisindikaattorit valitulla a<PERSON>jaksolla, ja se tarjoaa yleiskatsauksen sitoutumistrendeistä ja suorituskykymittareista.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projektit", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "ha<PERSON><PERSON><PERSON> jul<PERSON>u", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Alustaraportti", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Sinun projektisi", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Seuraavassa osiossa on yleiskatsaus julkisesti näkyvistä hankkeista, jotka menevät päällekkäin valitun a<PERSON>jakson kanssa, näissä projekteissa käytetyimmät menetelmät sekä osallistumisen kokonaismäärää koskevat mittarit.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Ilmoittautumisten <PERSON>", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Estetyt käyttäjät", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Hallinnoi estettyjä k<PERSON>täjiä.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Po<PERSON> r<PERSON>", "app.containers.Admin.Users.GroupsHeader.editGroup": "Muokkaa ryhmää", "app.containers.Admin.Users.GroupsPanel.admins": "Järjestelmänvalvojat", "app.containers.Admin.Users.GroupsPanel.allUsers": "Rekisteröityneet käyttäjät", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.GroupsPanel.managers": "Projektipäälliköt", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Määritetyt kohteet", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Saat yleiskatsauksen kaikista alustalle rekisteröityneistä ihmisistä ja organisaatioista. Lisää joukko käyttäjiä manuaalisiin ryhmiin tai älykkäisiin ryhmiin.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "<PERSON><PERSON> odottaa", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.assign": "Määritä", "app.containers.Admin.Users.assignedItems": "Määrite<PERSON>t kohteet {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "Osta yksi lisäpaikka", "app.containers.Admin.Users.changeUserRights": "<PERSON>uta k<PERSON>yttäjän o<PERSON>uk<PERSON>", "app.containers.Admin.Users.confirm": "Vahvistaa", "app.containers.Admin.Users.confirmAdminQuestion": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> antaa {name} al<PERSON><PERSON> j<PERSON>val<PERSON>?", "app.containers.Admin.Users.confirmNormalUserQuestion": "<PERSON><PERSON><PERSON><PERSON> as<PERSON> {name} tavalliseksi käyttäjäksi?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "<PERSON><PERSON><PERSON><PERSON> as<PERSON> {name} tavalliseksi käyttäjäksi? <PERSON><PERSON><PERSON>, että he menettävät hallinnoijan oikeudet kaikkiin projekteihin ja kans<PERSON>in, jotka heille on vahvistettu vahvistuksen yhteydessä.", "app.containers.Admin.Users.deleteUser": "Poista käyttäjä", "app.containers.Admin.Users.email": "Sähköposti", "app.containers.Admin.Users.folder": "Kansio", "app.containers.Admin.Users.folderManager": "Kansionhallinta", "app.containers.Admin.Users.helmetDescription": "Käyttäjälista järjestelmänvalvojassa", "app.containers.Admin.Users.helmetTitle": "Järjestelmänvalvoja - käyttäjien kojelauta", "app.containers.Admin.Users.inviteUsers": "Kutsu käyttäjiä", "app.containers.Admin.Users.joined": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.lastActive": "Viimeksi aktiivinen", "app.containers.Admin.Users.name": "<PERSON><PERSON>", "app.containers.Admin.Users.noAssignedItems": "<PERSON><PERSON> m<PERSON>r<PERSON><PERSON> kohteita", "app.containers.Admin.Users.options": "Vaihtoehdot", "app.containers.Admin.Users.permissionToBuy": "<PERSON><PERSON> voit antaa {name} järjestelmänval<PERSON><PERSON>, sinun on ostettava 1 lisäpaikka.", "app.containers.Admin.Users.platformAdmin": "<PERSON><PERSON><PERSON>änvalvo<PERSON>", "app.containers.Admin.Users.projectManager": "Projektipäällikkö", "app.containers.Admin.Users.reachedLimitMessage": "<PERSON><PERSON>t suunnitelmasi enimmäismäärän, 1 lisäpaikka {name} :lle lisätään.", "app.containers.Admin.Users.registeredUser": "Rekisteröitynyt käyttäjä", "app.containers.Admin.Users.remove": "Poista", "app.containers.Admin.Users.removeModeratorFrom": "K<PERSON>yttäj<PERSON> valvoo kans<PERSON>a, johon tämä projekti kuuluu. Poista tehtävä sen sijaan kohteesta \"{folderTitle}\".", "app.containers.Admin.Users.role": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.seeProfile": "<PERSON><PERSON> pro<PERSON>", "app.containers.Admin.Users.selectPublications": "Valitse projektit tai kansiot", "app.containers.Admin.Users.selectPublicationsPlaceholder": "<PERSON><PERSON> kir<PERSON><PERSON>", "app.containers.Admin.Users.setAsAdmin": "Aseta järjestelmänvalvojaksi", "app.containers.Admin.Users.setAsNormalUser": "Aseta tavalliseksi käyttäjäksi", "app.containers.Admin.Users.setAsProjectModerator": "Aseta projektipäälliköksi", "app.containers.Admin.Users.setUserAsProjectModerator": "Määritä {name} projektipäälliköks<PERSON>", "app.containers.Admin.Users.userBlockModal.allDone": "Valmista", "app.containers.Admin.Users.userBlockModal.blockAction": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Tä<PERSON>än käyttäjän sisältöä ei poisteta tällä toiminnolla. Ä<PERSON>ä unohda moderoida niiden sisältöä tarvittaessa.", "app.containers.Admin.Users.userBlockModal.blocked": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "<PERSON><PERSON><PERSON><PERSON> käyttäjä on estetty vuodesta {from}. <PERSON><PERSON> kestää {to}asti.", "app.containers.Admin.Users.userBlockModal.cancel": "Peruuta", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> k<PERSON>ota {name}eston?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} on estetty, kunnes {date}.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 päivä} other {{numberOfDays} päivää}}", "app.containers.Admin.Users.userBlockModal.header": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Admin.Users.userBlockModal.reasonLabel": "Syy", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Tästä ilmoitetaan estetylle käyttäjälle.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Valittu käyttäjä ei voi kirjautua sisään alustalle {daysBlocked}. <PERSON><PERSON> haluat peruuttaa tämän, voit poistaa eston estettyjen käyttäjien luettelosta.", "app.containers.Admin.Users.userBlockModal.unblockAction": "<PERSON><PERSON><PERSON> esto", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON><PERSON><PERSON>, haluan poistaa tämän käyttäjän eston", "app.containers.Admin.Users.userDeletionConfirmation": "Poistetaanko tämä käyttäjä pysyvästi?", "app.containers.Admin.Users.userDeletionFailed": "T<PERSON>ä käyttäjää poistettaessa tapahtui virhe. Yritä uudelleen.", "app.containers.Admin.Users.userDeletionProposalVotes": "Tämä poistaa myös kaikki tämän käyttäjän äänet ehdotuksista, jotka ovat vielä avoimia äänestykseen.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Käyttäjien oivalluksia", "app.containers.Admin.Users.youCantDeleteYourself": "Et voi poistaa omaa tiliäsi käyttäjän admin-sivun kautta", "app.containers.Admin.Users.youCantUnadminYourself": "Et voi luopua roolistasi järjestelmänvalvojana nyt", "app.containers.Admin.communityMonitor.communityMonitorLabel": "yhteisön valvoja", "app.containers.Admin.communityMonitor.healthScore": "Terveyspisteet", "app.containers.Admin.communityMonitor.healthScoreDescription": "Tämä pistemäär<PERSON> on keskiarvo kaikista mielipideasteikon kysymyksistä, j<PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON>t ovat vastanneet valitulla ajanjaksolla.", "app.containers.Admin.communityMonitor.lastQuarter": "viimeisellä neljänneksellä", "app.containers.Admin.communityMonitor.liveMonitor": "Live-monitor<PERSON>", "app.containers.Admin.communityMonitor.noResults": "<PERSON>i tuloksia tälle a<PERSON>.", "app.containers.Admin.communityMonitor.noSurveyResponses": "Ei k<PERSON>elyvas<PERSON>uk<PERSON>", "app.containers.Admin.communityMonitor.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Raportit", "app.containers.Admin.communityMonitor.settings": "Asetukset", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Community Monitor Survey ottaa vastaan palautetta.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Käyttöoikeudet", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "<PERSON><PERSON> k<PERSON><PERSON><PERSON> on rekisteröitynyt tapahtumaan, äänestänyt tai palaa projektisivulle kyselyn lähettämisen jälkeen.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Community Monitor Managerit voivat käyttää ja hallita kaikkia Community Monitorin asetuksia ja tietoja.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Yhteisön valvontapäälliköt", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Esimiehet voivat muokata Community Monitorin kyselyä ja käyttöoikeuksia, nähdä vastaustiedot ja luoda raportteja.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "<PERSON><PERSON><PERSON>aj<PERSON>usar<PERSON> on 100 %.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> taaju<PERSON> (0 - 100)", "app.containers.Admin.communityMonitor.settings.management2": "Hall<PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.popup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.popupDescription3": "Käyttäjille näytetään ajoittain ponnahdusik<PERSON>na, jossa heitä kehotetaan suorittamaan Community Monitor Survey. Voit säätää tiheyttä, joka määrittää niiden käyttäjien prosentti<PERSON>uden, jotka näkevät ponnahdusikkunan satunnaisesti, kun alla kuvatut ehdot täyttyvät.", "app.containers.Admin.communityMonitor.settings.popupSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "app.containers.Admin.communityMonitor.settings.preview": "Esikatselu", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Käyttäjä ei ole vielä täyttänyt kyselyä viimeisen 3 kuukauden aikana.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Käyttäjä ei ole vielä nähnyt ponnahdusikkunaa viimeisen 3 kuukauden aikana.", "app.containers.Admin.communityMonitor.settings.save": "Tallentaa", "app.containers.Admin.communityMonitor.settings.saved": "Tallennettu", "app.containers.Admin.communityMonitor.settings.settings": "Asetukset", "app.containers.Admin.communityMonitor.settings.survey2": "<PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.surveySettings3": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "Kotisivua tai mukautettua sivua lad<PERSON>.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymisoi kaikki k<PERSON>äj<PERSON>ot", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "<PERSON><PERSON><PERSON> k<PERSON><PERSON> antamat tiedot anonymisoidaan ennen kuin ne tallennetaan", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Käyttäjien on silti noudatettava \"Pääsyoikeuksien\" mukaisia osallistumisvaatimuksia. Käyttäjäprofiilitiedot eivät ole käytettävissä kyselytietojen viennissä.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Missä olosuhteissa ponnahdus<PERSON>kuna voi näkyä käyttäjille?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "<PERSON><PERSON><PERSON> ovat johta<PERSON>t?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Kyselyvastaukset y<PERSON>sä", "app.containers.Admin.communityMonitor.upsell.aiSummary": "AI yhteenveto", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Ota Community Monitor käyttöön", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Tämä ominaisuus ei sisälly nykyiseen suunnitelmaasi. Ota yhteyttä Government Success Manageriin tai järjestelmänvalvojaan avataksesi sen.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Terveyspisteet", "app.containers.Admin.communityMonitor.upsell.learnMore": "<PERSON><PERSON> l<PERSON>", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "<PERSON><PERSON><PERSON> ajan mit<PERSON>", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Community Monitor auttaa sinua pysymään edellä seuraamalla asukkaiden luotta<PERSON>, tyytyväisyyttä palveluihin ja yhteisön elämää – jatkuvasti.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Saat selk<PERSON>ät pist<PERSON>t, teho<PERSON><PERSON> la<PERSON> ja neljännesvuosittaisen raportin, jonka voit jakaa kollegoiden tai valittujen virkamiesten kanssa.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "Helposti luettavat partituurit, jotka kehittyvät ajan <PERSON>", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Tärkeimmät as<PERSON><PERSON>, AI:n yhteenveto", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jotka on räätälöity kaupunkisi kontekstiin", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Asukkaat rekry<PERSON>itiin satunnaisesti alustalle", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Neljännesvuosittaiset PDF-raportit, val<PERSON><PERSON> j<PERSON>i", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, milt<PERSON> yhteisöst<PERSON><PERSON> tuntuu, ennen kuin ongelmat kasvavat", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "Laskea", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Työpöytä tai muu", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "<PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "<PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Laitetyypit", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Laitetyyppi", "app.containers.Admin.earlyAccessLabel": "Ennakkokäyttöoikeus", "app.containers.Admin.earlyAccessLabelExplanation": "<PERSON><PERSON><PERSON><PERSON> on uusi ominaisuus, joka on saatavilla Early Accessissa.", "app.containers.Admin.emails.addCampaign": "<PERSON><PERSON>", "app.containers.Admin.emails.addCampaignTitle": "Luo uusi sä<PERSON>köposti", "app.containers.Admin.emails.allParticipantsInProject": "Kaikki projektiin osallistujat", "app.containers.Admin.emails.allUsers": "Rekisteröityneet käyttäjät", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automaattiset sähköpostit lähetetään automaattisesti, ja ne käynnistyvät käyttäjän toimien perusteel<PERSON>. Voit poistaa osan niistä käytöstä kaikilta alustasi käyttäjiltä. Muita automaattisia sähköposteja ei voi sammuttaa, koska niitä tarvitaan alustasi moitteettoman toim<PERSON>an kannalta.", "app.containers.Admin.emails.automatedEmails": "Automaattiset sähköpostit", "app.containers.Admin.emails.automatedEmailsDigest": "Sähköposti lähetetään vain, jos siin<PERSON> on sisältöä", "app.containers.Admin.emails.automatedEmailsRecipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jotka saavat tämän sähköpostin", "app.containers.Admin.emails.automatedEmailsTriggers": "<PERSON><PERSON><PERSON><PERSON>, joka käynnistää tämän sähköpostin", "app.containers.Admin.emails.changeRecipientsButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.clickOnButtonForExamples": "Napsauta alla olevaa painiketta nähdäksesi esimerkkejä tästä sähköpostista tukisivultamme.", "app.containers.Admin.emails.confirmSendHeader": "Sähköposti kaikille kä<PERSON>täjille?", "app.containers.Admin.emails.deleteButtonLabel": "Poistaa", "app.containers.Admin.emails.draft": "Luonnos", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON><PERSON><PERSON> kampan<PERSON>a", "app.containers.Admin.emails.editDisabledTooltip2": "Tulossa pian: T<PERSON>ä sähköpostia ei voi tällä hetkellä muokata.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Painikkeen te<PERSON>ti", "app.containers.Admin.emails.editRegion_intro_multiloc": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.emptyCampaignsDescription": "Ota helposti yhteyttä osallistujiisi lähettämällä heille sähköpostia. <PERSON><PERSON><PERSON>, keneen otat yhteyttä ja seuraa sitoutumistasi.", "app.containers.Admin.emails.emptyCampaignsHeader": "Lähetä ensimmäinen sähköpostisi", "app.containers.Admin.emails.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBody": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldBodyError": "Lähetä sähköpostiviesti kaikille kielille", "app.containers.Admin.emails.fieldReplyTo": "Vastausten pitäisi mennä o<PERSON>en", "app.containers.Admin.emails.fieldReplyToEmailError": "<PERSON>öpostiosoite oikeassa muodossa, esimerkiksi <EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "<PERSON>", "app.containers.Admin.emails.fieldReplyToTooltip": "<PERSON><PERSON> valita, mihin lähetät vastaukset sähköpostiisi.", "app.containers.Admin.emails.fieldSender": "From", "app.containers.Admin.emails.fieldSenderError": "<PERSON><PERSON>ita s<PERSON>hköpostin lähettäjä", "app.containers.Admin.emails.fieldSenderTooltip": "<PERSON><PERSON> p<PERSON>, kenet vastaan<PERSON>t näkevät sähköpostin lähettäjänä.", "app.containers.Admin.emails.fieldSubject": "Sähköpostin aihe", "app.containers.Admin.emails.fieldSubjectError": "<PERSON> aihe kaikille kielille", "app.containers.Admin.emails.fieldSubjectTooltip": "Tämä näkyy sähköpostin aiherivillä ja käyttäjän postilaatikon yleiskatsauksessa. Tee siitä selkeä ja mukaansatemp<PERSON>.", "app.containers.Admin.emails.fieldTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldToTooltip": "Voit valita k<PERSON>äj<PERSON><PERSON>hm<PERSON>, jotka saavat sähköpostisi", "app.containers.Admin.emails.formSave": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.from": "Lähettäjä:", "app.containers.Admin.emails.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.helmetDescription": "Lähetä manuaalisia sähköposteja käyttäjäryhmille ja aktivoi automaattisia kampanjoita", "app.containers.Admin.emails.nameVariablesInfo2": "Voit puhua suoraan kansalaisille käyttämällä muuttujia {firstName} {lastName}. Esim. \"Rakas {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Sähköpostiosoitteeseesi on lähetetty esikatseluviesti", "app.containers.Admin.emails.previewTitle": "Esikatselu", "app.containers.Admin.emails.regionMultilocError": "<PERSON> arvo ka<PERSON> k<PERSON>", "app.containers.Admin.emails.seeEmailHereText": "<PERSON><PERSON> kun tämäntyyppinen sähköposti on lähetetty, voit tarkistaa sen täältä.", "app.containers.Admin.emails.send": "Lähettää", "app.containers.Admin.emails.sendNowButton": "Lähetä nyt", "app.containers.Admin.emails.sendTestEmailButton": "Lähetä minulle testisähköposti", "app.containers.Admin.emails.sendTestEmailTooltip": "Kun napsautat tätä linkkiä, testisähköposti lähetetään vain sähköpostiosoitteeseesi. Tämän avulla voit tarkistaa, miltä sähköposti näyttää tosielämässä.", "app.containers.Admin.emails.senderRecipients": "Lähettäjä ja vast<PERSON>t", "app.containers.Admin.emails.sending": "Lähetetään", "app.containers.Admin.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.sentToUsers": "Nämä ovat käyttäji<PERSON> lähetettyjä sähköposteja", "app.containers.Admin.emails.subject": "Aihe:", "app.containers.Admin.emails.supportButtonLabel": "Katso es<PERSON>jä tukisivultamme", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/en/articles/7042664-changing-the-settings-of-the-automated-email-notifications", "app.containers.Admin.emails.to": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.containers.Admin.emails.toAllUsers": "Haluatko lähettää tämän sähköpostin kaikille rekisteröityneille käyttäjille?", "app.containers.Admin.emails.viewExample": "Näytä", "app.containers.Admin.ideas.import": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.AllProjects": "Kaikki projektit", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "yhteisön valvoja", "app.containers.Admin.inspirationHub.DocumentAnnotation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ExternalSurvey": "<PERSON><PERSON><PERSON><PERSON> kysely", "app.containers.Admin.inspirationHub.Filters.Country": "Maa", "app.containers.Admin.inspirationHub.Filters.Method": "Menetelmä", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.population": "Väestö", "app.containers.Admin.inspirationHub.Highlighted": "Korostettu", "app.containers.Admin.inspirationHub.Ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.Information": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Valitse maa nähdäksesi kiinnitetyt projektit", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Maa", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "Kiinnitettyjä projekteja ei löytynyt tästä maasta. Vaihda maa nähdäksesi muiden maiden kiinnitetyt projektit", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Vai<PERSON>da maata nähdäksesi lisää kiinnitettyjä projekteja", "app.containers.Admin.inspirationHub.Poll": "Poll", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "<PERSON>e lisä<PERSON>...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Vaihe {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "<PERSON><PERSON> et halua, että projektisi sisällytetään inspiraatiokeskukseen, ota yhteyttä GovSuccess-päällikköösi.", "app.containers.Admin.inspirationHub.Proposals": "ehdotuksia", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Lajitteluperuste", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (pienin ensin)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (kor<PERSON>in ensin)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Al<PERSON>usp<PERSON><PERSON><PERSON> (<PERSON><PERSON> en<PERSON>)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Aloituspäivä (uusin ensin)", "app.containers.Admin.inspirationHub.Survey": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Ku<PERSON><PERSON> luettelo parhaista projekteista ympäri ma<PERSON>.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Keskustele muiden harjoittajien kanssa ja opi heiltä.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "<PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON> koon ja maan mukaan.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Ota Inspiration Hub käyttöön", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Tämä ominaisuus ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "<PERSON><PERSON> l<PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Inspiration Hub yhdistää sinut kuratoituun syötteeseen poikkeuksellisista osallistumisprojekteista Go Vocal -alustoilla ympäri maail<PERSON>a. Opi kuinka muut kaupungit toteuttavat onnistuneita projekteja ja keskustele muiden harjoittajien kanssa.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "<PERSON><PERSON>en demokratian har<PERSON><PERSON><PERSON><PERSON> verkostoon", "app.containers.Admin.inspirationHub.Volunteering": "Vapaaehtoistyö", "app.containers.Admin.inspirationHub.Voting": "Äänestäminen", "app.containers.Admin.inspirationHub.commonGround": "<PERSON><PERSON><PERSON><PERSON> pohja", "app.containers.Admin.inspirationHub.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.resetFilters": "<PERSON><PERSON><PERSON>", "app.containers.Admin.inspirationHub.seemsLike": "Näyttää siltä, että projekteja ei enää ole. Yritä muuttaa {filters}.", "app.containers.Admin.messaging.automated.editModalTitle": "Muokkaa kampanjakenttiä", "app.containers.Admin.messaging.automated.variablesToolTip": "Voit käyttää viestissäsi seuraavia muuttujia:", "app.containers.Admin.messaging.helmetTitle": "Viestit", "app.containers.Admin.messaging.newProjectPhaseModal.alternatively": "Vaihtoehtoisesti voit poistaa tämän sähköpostikampanjan käytöstä tietyissä vaiheissa kunkin vaiheen asetuksista.", "app.containers.Admin.messaging.newProjectPhaseModal.cancel": "Peruuta", "app.containers.Admin.messaging.newProjectPhaseModal.disabledMessage1": "Tämä myös poistaa käytöstä {emailCampaignName} sähköpostikampanjan kaikissa olemassa olevissa projektin vaiheissa. Et voi määrittää tätä sähköpostikampanjaa millekään vaiheelle niin kauan kuin tämä asetus on poistettu käytöstä.", "app.containers.Admin.messaging.newProjectPhaseModal.enabledMessage1": "Tämä ei ota automaattisesti käyttöön {emailCampaignName} sähköpostikampanjaa olemassa oleville projektin vaiheille. Tämän asetuksen ottaminen käyttöön sallii vain tämän sähköpostikampanjan määrittämisen kullekin vaiheelle.", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOff1": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> pois<PERSON> {emailCampaignName} sähköpostikampanja-asetuksen käytöstä?", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOn1": "O<PERSON><PERSON>anko {emailCampaignName} sähköpostikampanja-as<PERSON> k<PERSON>?", "app.containers.Admin.messaging.newProjectPhaseModal.turnOff": "K<PERSON><PERSON>ä, sammuta", "app.containers.Admin.messaging.newProjectPhaseModal.turnOn": "Kyllä, käynnistä", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Tä<PERSON><PERSON> widget näyttää jokaisen käyttäjän projektit <b>heid<PERSON>n seuranta-as<PERSON><PERSON><PERSON></b>perust<PERSON><PERSON>. Tämä sisältää hankkeet, joita he se<PERSON>t, se<PERSON><PERSON> hank<PERSON>et, jois<PERSON> he seura<PERSON>t pan<PERSON>, ja hank<PERSON><PERSON>, jotka liittyvät heitä kiinnostaviin aiheisiin tai alueisiin.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Tämä widget näytetään käyttäj<PERSON> vain, jos hän voi osallistua projekteihin. <PERSON><PERSON> näet tämän viestin, se tarkoit<PERSON>, että et (järjestelmänvalvoja) voi osallistua projekteihin tällä hetkellä. Tämä viesti ei näy oikealla etusivulla.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "<PERSON><PERSON><PERSON> o<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Tämä widget esittelee projekteja, j<PERSON><PERSON> k<PERSON>j<PERSON> voi tällä hetkellä <b>o<PERSON><PERSON><PERSON></b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "<PERSON>ä<PERSON><PERSON> widget näytet<PERSON>än k<PERSON>ytt<PERSON><PERSON><PERSON><PERSON> vain, jos on o<PERSON><PERSON>a projekteja, jotka ovat hänelle tärkeitä seuraamisasetusten perusteella. Jo<PERSON> näet tämän viestin, se tark<PERSON><PERSON>, että sinä (järjestelmänvalvoja) et seuraa mitään tällä hetkellä. Tämä viesti ei näy oikealla etusivulla.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "<PERSON><PERSON><PERSON><PERSON> koh<PERSON>t", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Suodatusperust<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "Val<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Valmis ja arkist<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "Tietoja ei ole sa<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "<PERSON><PERSON><PERSON><PERSON> widget näyttää <b>projektit, jotka on valmis ja/tai arkistoitu.</b>. \"Valmis\" sisältää myös projektit, jotka ovat viimeisessä vaiheessa ja joissa viimeinen vaihe on raportti.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Valmiit projektit", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "Sanoit, teimme...", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "<PERSON> nimi ka<PERSON> k<PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Projekti ei voi olla tyhjä", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projekti", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Tuloksena oleva URL-osoite", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Lisää projekti", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "Navigointipalkki näyttää vain projektit, joi<PERSON> k<PERSON>täjillä on pääsy.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "Tämä widget näkyy vain kotisivulla, kun Community Monitor hyväksyy vastauksia.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "<PERSON><PERSON><PERSON>s<PERSON><PERSON> v<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Tärkeää:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Esimerkki mielipidek<PERSON><PERSON>n k<PERSON>ymyksestä", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "<PERSON><PERSON> lopet<PERSON>päivä<PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "<PERSON><PERSON>lla Esc-näppäintä", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projektit ja kansiot (vanhat)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Proje<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} työske<PERSON><PERSON> parhai<PERSON>an", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Painikkeen te<PERSON>ti", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "Osallistu nyt!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "kansio", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Valitse projekti tai kansio", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Valitse projekti tai kansio", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "Näytä avatarit", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Valokeila", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Alkaa {days} p<PERSON><PERSON><PERSON>n kulu<PERSON>ua", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Alkaa {weeks} viikon kuluttua", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "{days} p<PERSON><PERSON><PERSON><PERSON> sitten", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "{weeks} viik<PERSON>a sitten", "app.containers.Admin.project.Campaigns.campaignFrom": "Lähettäjä:", "app.containers.Admin.project.Campaigns.campaignTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.containers.Admin.project.Campaigns.customEmails": "Mukautetut sähköpostit", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Lähetä mukautettuja sähköposteja ja tarkista tilastot.", "app.containers.Admin.project.Campaigns.noAccess": "<PERSON><PERSON>, mutta näyttää siltä, että sinulla ei ole pääsyä sähköpostiosaan", "app.containers.Admin.project.emails.addCampaign": "<PERSON><PERSON>", "app.containers.Admin.project.emails.addCampaignTitle": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "<PERSON><PERSON><PERSON> {participants} ja se<PERSON>ajat projektista", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Tämä sisältää rekisteröityneet käyttäjät, jotka suorittivat minkä tahansa toiminnon projektissa. Rekisteröimättömät tai anonymisoidut käyttäjät eivät sisälly.", "app.containers.Admin.project.emails.dateSent": "Lähetyspäivämäärä", "app.containers.Admin.project.emails.deleteButtonLabel": "Poistaa", "app.containers.Admin.project.emails.draft": "Luonnos", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON><PERSON><PERSON> kampan<PERSON>a", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Ota helposti yhteyttä osallistujiisi lähettämällä heille sähköpostia. <PERSON><PERSON><PERSON>, keneen otat yhteyttä ja seuraa sitoutumistasi.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Lähetä ensimmäinen sähköpostisi", "app.containers.Admin.project.emails.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldBody": "Sähköposti Viesti", "app.containers.Admin.project.emails.fieldBodyError": "Lähetä sähköpostiviesti kaikille kielille", "app.containers.Admin.project.emails.fieldReplyTo": "Vastausten pitäisi mennä o<PERSON>en", "app.containers.Admin.project.emails.fieldReplyToEmailError": "<PERSON>öpostiosoite oikeassa muodossa, esimerkiksi <EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "<PERSON>", "app.containers.Admin.project.emails.fieldReplyToTooltip": "<PERSON><PERSON><PERSON>, mihin sähköpostiosoitteeseen lähetetään suorat vastaukset käyttäjiltä sähköpostiisi.", "app.containers.Admin.project.emails.fieldSender": "From", "app.containers.Admin.project.emails.fieldSenderError": "<PERSON><PERSON>ita s<PERSON>hköpostin lähettäjä", "app.containers.Admin.project.emails.fieldSenderTooltip": "Valitse, kenet käyttäjät näkevät sähköpostin lähettäjänä.", "app.containers.Admin.project.emails.fieldSubject": "Sähköpostin aihe", "app.containers.Admin.project.emails.fieldSubjectError": "<PERSON> aihe kaikille kielille", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Tämä näkyy sähköpostin aiherivillä ja käyttäjän postilaatikon yleiskatsauksessa. Tee siitä selkeä ja mukaansatemp<PERSON>.", "app.containers.Admin.project.emails.fieldTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.formSave": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.from": "Lähettäjä:", "app.containers.Admin.project.emails.helmetDescription": "Lähetä manuaalisia sähköposteja projektin osallistujille", "app.containers.Admin.project.emails.infoboxAdminText": "Project Messaging -välilehdeltä voit lähettää sähköpostia vain kaikille projektin osallistujille. Jos haluat lähettää sähköpostia muille osallistujille tai käyttäjien alajou<PERSON>ille, siirry {link} -välilehteen.", "app.containers.Admin.project.emails.infoboxLinkText": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.infoboxModeratorText": "Project Messaging -välilehdeltä voit lähettää sähköpostia vain kaikille projektin osallistujille. Järjestelmänvalvojat voivat lähettää sähköposteja muille osallistujille tai käyttäjien alajoukoille Platform Messaging -välilehden kautta.", "app.containers.Admin.project.emails.message": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.nameVariablesInfo2": "Voit puhua suoraan kansalaisille käyttämällä muuttujia {firstName} {lastName}. Esim \"Rakas {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.previewSentConfirmation": "Sähköpostiosoitteeseesi on lähetetty esikatseluviesti", "app.containers.Admin.project.emails.previewTitle": "Esikatselu", "app.containers.Admin.project.emails.projectParticipants": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.recipients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.send": "Lähettää", "app.containers.Admin.project.emails.sendTestEmailButton": "Lähetä esikatselu", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Lähetä tämä sähköpostiluonnos sähköpostiosoitteeseen, jolla olet kir<PERSON>ut si<PERSON>, jotta voit tark<PERSON>, miltä se näyttää \"todellisessa elämässä\".", "app.containers.Admin.project.emails.senderRecipients": "Lähettäjä ja vast<PERSON>t", "app.containers.Admin.project.emails.sending": "Lähetetään", "app.containers.Admin.project.emails.sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.sentToUsers": "Nämä ovat käyttäji<PERSON> lähetettyjä sähköposteja", "app.containers.Admin.project.emails.status": "Tila", "app.containers.Admin.project.emails.subject": "Aihe:", "app.containers.Admin.project.emails.to": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.containers.Admin.project.messaging.helmetTitle": "Viestit", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "<PERSON>ämä kuva on osa kansio<PERSON>; kans<PERSON> yhteenvetokortti, joka näkyy esimerkiksi kotisivulla. Lisätietoja suositelluista kuvar<PERSON>utioista on {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Tämä kuva näkyy kansiosivun yläosassa. Lisätietoja suositelluista kuvaresoluutioista on {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "viera<PERSON> tuki<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.askPersonalData3": "Lisää kentät nimelle ja sähköpostiosoitteelle", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Kaikki kysymykset näkyvät PDF-tiedostossa. Seuraavia ei kuitenkaan tällä hetkellä tueta tuonnissa FormSyncin kautta: kuvat, tunnist<PERSON>t ja tiedost<PERSON>jen lata<PERSON>.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Kaikki kysymykset näkyvät PDF-tiedostossa. Seuraavia kysymyksiä ei kuitenkaan tällä hetkellä tueta tuotaessa FormSyncin kautta: kartoituskysymykset (pudotusnasta, reitin piirtäminen ja alueen piirtäminen), ranking-<PERSON><PERSON><PERSON><PERSON><PERSON>, matriisikysymykset ja tiedoston latauskysymykset.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Lomakkeen loppu", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Lomakkeen alku", "app.containers.Admin.projects.all.components.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.draft": "Luonnos", "app.containers.Admin.projects.all.components.manageButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "Kopioi projekti", "app.containers.Admin.projects.all.copyProjectError": "<PERSON><PERSON><PERSON>än projektin kopioinnissa tapahtui virhe. <PERSON>rit<PERSON> my<PERSON> u<PERSON>.", "app.containers.Admin.projects.all.customiseEnd": "Mukauta lomakkeen loppua.", "app.containers.Admin.projects.all.customiseStart": "Mukauta lomakkeen alkua.", "app.containers.Admin.projects.all.deleteFolderButton1": "Poista kansio", "app.containers.Admin.projects.all.deleteFolderConfirm": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän kansion? <PERSON><PERSON><PERSON> kaikki kansion projektit poistetaan. Tätä toimintoa ei voi kumota.", "app.containers.Admin.projects.all.deleteFolderError": "Tämän kansion poistamisessa oli on<PERSON>. Yritä uude<PERSON>en.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Poista projekti", "app.containers.Admin.projects.all.deleteProjectConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän projektin? Tätä ei voi peruuttaa.", "app.containers.Admin.projects.all.deleteProjectError": "<PERSON><PERSON><PERSON>än projektin poistamisessa tapahtui virhe. <PERSON>rit<PERSON> my<PERSON> u<PERSON>.", "app.containers.Admin.projects.all.exportAsPDF1": "Lataa PDF-lomake", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Voit yhdistää online- ja offline-vastauksia. <PERSON><PERSON> haluat ladata offline-vastauksia, siirry tämän projektin Syötteiden hallinta -välilehteen ja napsauta <PERSON>.", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Voit yhdistää online- ja offline-vastauksia. Voit ladata offline-vastauksia siirtymällä tämän projektin K<PERSON>ely-vä<PERSON>lehdelle ja napsautt<PERSON>.", "app.containers.Admin.projects.all.logicNotInPDF": "Kyselylogiikka ei näy ladatussa PDF-tiedostossa. Paperivastaajat näkevät kaikki kyselyn kysym<PERSON>.", "app.containers.Admin.projects.all.new.Folders.Filters.search": "Hae kansioista", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "<PERSON><PERSON><PERSON> kansiot on ladattu", "app.containers.Admin.projects.all.new.Folders.Table.folder": "Kansio", "app.containers.Admin.projects.all.new.Folders.Table.managers": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {# projekti} other {# projektit}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Projektin aloituspäivämäärä", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Piilotettu", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "<PERSON><PERSON><PERSON> vaiheen o<PERSON>sta<PERSON> mukaan", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Osallistumismenetelmä", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "<PERSON><PERSON><PERSON><PERSON> pohja", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Ehdotukset", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Vapaaehtoistyö", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Äänestäminen", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Tiedottaminen", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "<PERSON><PERSON> al<PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Osallistumistila", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Menne<PERSON>yys", "app.containers.Admin.projects.all.new.Projects.Filters.Search.search": "Hae projektia", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Aakkosjärjestyksessä (az)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Aakkosjärjestyksessä (za)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "<PERSON>aihe alkaa tai päättyy pian", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created": "<PERSON><PERSON><PERSON><PERSON><PERSON> luotu", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "<PERSON><PERSON><PERSON><PERSON><PERSON> katsotut", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Ylläpitäjät", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Näkyvyys", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "Lisää suodatin", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Tyhjennä", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "Ei enää lisättäviä suodattimia", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Ylläpitäjät", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Kaikki projektit on ladattu", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Nykyinen vaihe", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "{days}d <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days}d <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Löydettävyys:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "Luonnos", "app.containers.Admin.projects.all.new.Projects.Table.end": "Loppu", "app.containers.Admin.projects.all.new.Projects.Table.ended": "Päättynyt", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "Päättyy tänään", "app.containers.Admin.projects.all.new.Projects.Table.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Piilotettu", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "Ladataan lisää…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "{months}kk jäljellä", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "{months}kk alkuun", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "<PERSON><PERSON><PERSON> vaihe:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.phase": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "Julkaisua edeltävä", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projekti", "app.containers.Admin.projects.all.new.Projects.Table.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.published": "Julkaistu", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Vieritä alas ladataksesi lisää", "app.containers.Admin.projects.all.new.Projects.Table.start": "Aloita", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Näkyvyys", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Näkyvyys:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} ryhmät", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} joh<PERSON><PERSON>t", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "{years}v<PERSON><PERSON> j<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "{years}y al<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Nykyinen vaihe: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} päivä<PERSON> jäljellä", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Kansio: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "<PERSON><PERSON> nyk<PERSON> vai<PERSON>tta", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "Ei päättymispäivämäärää", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "<PERSON>i vaiheita", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Vaihe {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Vaiheet:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projekti", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Aloituspäivämäärä: {date}", "app.containers.Admin.projects.all.new.folders": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.ordering": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.projects": "Projektit", "app.containers.Admin.projects.all.new.timeline": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> ep<PERSON>.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Projektilla ei ole päättymispäivää", "app.containers.Admin.projects.all.new.timeline.project": "Projekti", "app.containers.Admin.projects.all.notes": "Huomautuksia", "app.containers.Admin.projects.all.personalDataExplanation5": "Tämä vaihtoehto lisää etunimen, sukunimen ja sähköpostiosoitteen kentät vietyyn PDF-tiedostoon. Kun paperilomake ladataan, käytämme näitä tietoja luodaksemme automaattisesti tilin offline-kyselyn vast<PERSON>.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "AI yhteenveto", "app.containers.Admin.projects.project.analysis.Comments.comments": "Ko<PERSON>ntit", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "<PERSON><PERSON><PERSON><PERSON> on käytettävissä, kun kommentteja on 5 tai enemmän.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "Tee yhteenveto kommenteista", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {<PERSON><PERSON><PERSON><PERSON><PERSON>} =1 {1 uusi kommentti} other {# uutta kommenttia}}", "app.containers.Admin.projects.project.analysis.aiSummary": "AI yhteenveto", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Tämä on tekoälyn luomaa sisältöä. Se ei välttämättä ole 100 % tarkka. Tarkista ja vertaa todellisia syötteitä tarkkuuden varmistamiseksi. <PERSON><PERSON><PERSON>, että tarkkuus todennäköisesti paranee, jos valittujen tulojen määrää vähennetään.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "Sähköposti-ilmoitukset lähetetään vain osallistujille", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Piilotettu", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "Hakukoneet eivät indeksoi", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Ei näy etusivulla tai widgeteissä", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Vain suoran URL-osoitteen kautta käytettävissä", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "<PERSON><PERSON><PERSON>, kuinka löydettävissä tämä projekti on.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Tämä projekti on näkyvissä kaikille, j<PERSON><PERSON> on si<PERSON>en k<PERSON>töoikeus, ja se näkyy kotisivulla ja widgeteissä.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Tämä projekti piilotetaan suurelta yleisöltä, ja se näkyy vain ni<PERSON>, j<PERSON><PERSON> on linkki.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Kuka voi löytää tämän projektin?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Avaa AI-analyysi", "app.containers.Admin.projects.project.ideas.analysisText2": "Tu<PERSON>tu te<PERSON>lypohjaisiin yht<PERSON>vedoihin ja tarkastele yksittäisiä lähetyksiä.", "app.containers.Admin.projects.project.ideas.importInputs": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "<PERSON>n o<PERSON> luonut raportin, voit halutessasi jakaa sen julkisesti vaiheen al<PERSON>a.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "<PERSON><PERSON><PERSON>i sivu tiedon jakamista varten", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "<PERSON><PERSON>:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "<PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "<PERSON><PERSON> raportti men<PERSON> vaiheesta tai aloita alusta.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "<PERSON><PERSON><PERSON><PERSON> raportti ei ole julkinen. <PERSON><PERSON> haluat tehdä sen jul<PERSON>, ota <PERSON>-kyt<PERSON> k<PERSON>yttöön.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "<PERSON><PERSON><PERSON><PERSON> vaihe on alkanut, mutta raportti ei ole vielä julkinen. <PERSON><PERSON> haluat tehdä sen julkis<PERSON>, ota <PERSON>-kytkin k<PERSON>töön.", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "<PERSON><PERSON><PERSON> v<PERSON>", "app.containers.Admin.projects.project.information.ReportTab.report": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Jaa aiemman kyselyn tai ideointivaiheen tulokset", "app.containers.Admin.projects.project.information.ReportTab.visible": "Näkyy", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "<PERSON>ämä raportti julkis<PERSON>taan heti vaiheen alka<PERSON>a. <PERSON><PERSON>, että se ei ole julkinen, poista Näkyvä-kytkin käytöstä.", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "<PERSON><PERSON><PERSON><PERSON> raportti on tä<PERSON><PERSON> hetkellä julkinen. <PERSON><PERSON>, että se ei ole julkinen, poista Näkyvä-kytkin käytöstä.", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän raportin? Tätä toimintoa ei voi kumota.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Lisää vaiheeseen", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "<PERSON>un on hyväksyttävä tämä ennen kuin voit jatkaa", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Lomakkeen voi ladata täältä.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Ladattu lomake luoti<PERSON>-o<PERSON>ssa", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Lomakkeen kieli", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Täten suostun käsittelemään tätä tiedostoa Google Cloud Form Parser -sovelluksella", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Lähetä tied<PERSON><PERSON> j<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "<PERSON>in voi ladata täältä.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Lataa", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Lataa valmis <b>Excel-tiedosto</b> (.xlsx). Sen on käytettävä tälle projektille annettua mallia. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Lataa <b>PDF-<PERSON><PERSON><PERSON> skannatuista lomakkeista</b>. Sen on käytettävä tästä vaiheesta tulostettua lomaketta. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Käytä tätä sähköpostia uudelle käyttäjälle", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "Luo uusi tili antamalla kelvollinen sähköpostiosoite", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "Tekijä<PERSON> luodaan uusi tili näillä tiedoilla. Tämä syöte lisätään siihen.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "<PERSON> s<PERSON>öpostiosoite ja/tai etu- ja sukunimi määrittääksesi tämän syötteen tekijälle. Tai poista suostumus<PERSON>udun valinta.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Tähän sähköpostiin on jo liitetty tili. Tämä syöte lisätään siihen.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> suostum<PERSON> (luo käyttäjät<PERSON>)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Hyväksy", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Hyväksy kaikki syötteet", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Tekijä:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "Sähköposti:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "<PERSON><PERSON><PERSON> a<PERSON> vir<PERSON>, ja joitain s<PERSON> ei ole tuotu. <PERSON><PERSON><PERSON><PERSON> virheet ja tuo puuttuvat syötteet uudelleen.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Virheelliset lomaketiedot. Tarkista yllä olevasta lomakkeesta virheitä.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "<PERSON><PERSON> lo<PERSON> (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "<PERSON><PERSON> skan<PERSON> lo<PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "<PERSON><PERSON><PERSON> tulot", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Tuodaan. Tämä prosessi voi kestää muutaman minuutin.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Tämä syöte tuotiin anonyymisti.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} syötteet on tuotu ja vaativat hyväksynnän.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} syötteitä ei voitu hyväksyä. Tarkista jokainen syöte vahvistusongelmien varalta ja vahvista yksitellen.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Alue:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Ei vielä mitään arvosteltavaa. Napsauta \"{importFile}\" tuodaksesi skannatut syöttölomakkeita sisältävän PDF-tiedoston tai syötteitä sisältävän Excel-tiedoston.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Ei vielä mitään arvosteltavaa. Napsauta \"{importFile}\" tuodaksesi syötteitä sisältävän Excel-tiedoston.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Syöte", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "<PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Tuotua tiedostoa ei voi näyttää. <PERSON>ot<PERSON>jen tiedostojen katselu on käytettävissä vain PDF-tuonnissa.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Vaihe:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Valittu vaihe ei voi sisältää tuloja. <PERSON><PERSON><PERSON>.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Tämä projekti ei sisällä vaiheita, jotka voivat sisältää ideoita.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "<PERSON><PERSON><PERSON>, mihin vaiheeseen haluat lisätä nämä tulot.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Syötteiden ma<PERSON>", "app.containers.Admin.projects.project.participation.comments": "Ko<PERSON>ntit", "app.containers.Admin.projects.project.participation.inputs": "Toiminnat", "app.containers.Admin.projects.project.participation.participantsTimeline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.participation.selectPeriod": "<PERSON>its<PERSON>", "app.containers.Admin.projects.project.participation.usersByAge": "Käyttäjät iän mukaan", "app.containers.Admin.projects.project.participation.usersByGender": "Käyttäjät sukupuolen mukaan", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Lisää kysymys", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Mahdollisuus lisätä tai muokata käyttäjäkenttiä vaihetasolla ei sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} vaihtoehdot", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "<PERSON><PERSON><PERSON> tila", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Nämä kysymykset lisätään kyselylomakkeen viimeiseksi sivuksi, koska 'Näytä kentät kyselyssä?' on valittu vaiheasetuksissa.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Ylimääräisiä kysymyksiä ei esitetä.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Valinnainen - aina k<PERSON>, koska ryhmä viittaa siihen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "Poista kenttä", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Pakollinen - aina k<PERSON>, koska ryhmä viittaa siihen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Todennus komennolla {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Täytä alla olevat lisäkysymykset", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "Vahvista sähköpostiosoitteesi", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Vahvistusmenetelmästä palautetut tiedot:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>köpostiosoite ja salasana", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "<PERSON><PERSON><PERSON><PERSON>köpostiosoitteesi", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "<PERSON><PERSON>a hilja<PERSON>in kä<PERSON>äj<PERSON> tulee vahvistaa?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Henkilöllisyyden vahvistus komennolla {verificationMethod} (perustuu käyttäjäryhmään)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "Osallistuminen ei vaadi toimenpiteitä", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Käytä älykkäitä ryhmiä osallistumisen rajoittamiseen yllä lueteltujen vahvistettujen tietojen perusteella", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Käyttäjien on oltava vahvistettu viimeisen 30 minuutin aikana.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "<PERSON><PERSON><PERSON><PERSON>äjien on oltava vahvistettu viimeisten {days} päivän aikana.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "Viimeisten 30 päivän aikana", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "Viimeisten 30 minuutin aikana", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "Viimeisen 7 päivän aikana", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Vahvistetut kentät:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} v<PERSON><PERSON>us", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "<PERSON><PERSON><PERSON> luominen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on luotava täydelli<PERSON> tili <PERSON>, v<PERSON><PERSON><PERSON>ulla sä<PERSON>köpostiosoitteella ja salas<PERSON>lla.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on luotava täydelli<PERSON> tili <PERSON>, sähköpostiosoitteella ja salasanalla.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "To<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "Sähköpostivahvistus", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Osallistujien tulee vahvistaa sähköpostinsa kertakoodilla.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "<PERSON><PERSON><PERSON><PERSON> roska<PERSON>in tunnistus", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "Tämä ominaisuus auttaa estämään päällekkäisiä kyselylähetyksiä uloskirjautuneilta käyttäjiltä analysoimalla IP-osoitteita ja laitetietoja. Vaikka se ei ole yhtä tarkka kuin sisäänkirjautumisen vaatiminen, se voi auttaa vähentämään päällekkäisten vastausten määrää.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Huomautus: <PERSON><PERSON><PERSON><PERSON> verkoissa (kuten toimistoissa tai julkisissa Wi-Fi-verkoissa) on pie<PERSON>, että eri käyttäjät merkitään kaksoiskappaleiksi.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "<PERSON><PERSON> k<PERSON><PERSON> edistynyt roskapostin tunnistus", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Osallistujille esitetään lisäkysymyksiä", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "<PERSON><PERSON> mit<PERSON>n", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "Kuka tahansa voi osallistua ilman rekisteröitymistä tai sisäänkirjautumista.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Nollaa l<PERSON>äkysymykset ja ryhm<PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "<PERSON><PERSON>a osallistuminen k<PERSON>yttäjäryhmiin", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO-vahvistus", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on vahvistettava henkilöllisyytensä {verificationMethod}.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Avaa AI-analyysi", "app.containers.Admin.projects.project.survey.allFiles": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.allResponses": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Tarkkuus: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Tekoälyyhteenvedon luomisessa tapahtui virhe. Yritä luoda se uudelleen alla.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Avaa AI-analyysi", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Pi<PERSON>ta tämän kysym<PERSON> yhteenvedot", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "tulot valittu", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Avaa analyysitoiminnot", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} uusia vastauksia", "app.containers.Admin.projects.project.survey.analysis.regenerate": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "Näytä AI-näkemyksiä", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Voit tehdä yhteenvedon enintään 30 syötteestä kerralla nykyiseen suunnitelmaasi. Keskustele GovSuccess Managerin tai järjestelmänvalvojan kanssa avataksesi lisää.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Valitse aiheeseen liittyvät kysymykset analysoitavaksi", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "<PERSON><PERSON><PERSON><PERSON> sisällyttää muita aiheeseen liittyviä kysymyk<PERSON>ä {question}-analyysiisi?", "app.containers.Admin.projects.project.survey.cancel": "Peruuta", "app.containers.Admin.projects.project.survey.consentModalButton": "Jatka", "app.containers.Admin.projects.project.survey.consentModalCancel": "Peruuta", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "Hyväksyn OpenAI:n käytön tietojen käsittelijänä tässä projektissa", "app.containers.Admin.projects.project.survey.consentModalText1": "Jatkamalla hyväksyt OpenAI:n käytön tietojen käsittelijänä tässä projektissa.", "app.containers.Admin.projects.project.survey.consentModalText2": "OpenAI-sovellusliittymät tarjoavat automaattisia tekstiyhteenvetoja ja osia automaattisesta taggauksesta.", "app.containers.Admin.projects.project.survey.consentModalText3": "Lähetämme OpenAI-sovellusliittymille vain sen, mitä käyttäjät kirjoittivat kyselyihinsä, ideoihinsa ja kommentteihinsa, emme koskaan mitään tietoja heidän profiilista<PERSON>.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI ei käytä näitä tietoja malliensa jatkokoulutukseen. Lisätietoja OpenAI:n tietosuojakäsittelystä löytyy {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "tässä", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON> kuin jatkat", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "Et voi kirjoittaa analyysiä ennen kuin olet muokannut lo<PERSON>ta", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Poistaa", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän analyysin? Tätä toimintoa ei voi kumota.", "app.containers.Admin.projects.project.survey.explore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.followUpResponses": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> keskiarvo", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Vie GeoJSON-muodossa", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Vie vastaukset tähän kysymykseen GeoJSON-tiedostona. Jokaisen GeoJSON-ominaisuuden osalta kaikki asiaan liittyvät vastaajien kyselyvastaukset luetellaan kyseisen ominaisuuden \"ominaisuudet\" -objektissa.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} vastaajia} one {{respondentCount} vastaaja} other {{respondentCount} vastaajia}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} valintoja} one {{numberChoices} valinta} other {{numberChoices} valintoja}}", "app.containers.Admin.projects.project.survey.heatMap": "Lämpökartta", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Lue lisää Esri Smart Mappingin avulla luoduista lämpökartoista.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Lämpökartta luodaan Esri Smart Mappingin avulla. Lämpökartat ovat hyödyllisiä, kun datapisteitä on paljon. <PERSON><PERSON> pisteit<PERSON> on vähemmän, voi olla parempi katsoa vain sijaintipisteitä suoraan. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Lämpökarttanäkymä", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- <PERSON><PERSON><PERSON><PERSON> p<PERSON>", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Kun käyttäjä valitsee tämän vast<PERSON>, logiikka ohittaa kaikki sivut sivulle {pageNumber} asti ({numQuestionsSkipped} kysymykset ohitetaan). Napsauta piilottaaksesi tai näyttääksesi ohitetut sivut ja kysymykset.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "Kun käyttäjä valitsee tämän vast<PERSON>, logiikka hyppää kyselyn loppu<PERSON> ({numQuestionsSkipped} kysymystä ohitetaan). Napsauta piilottaaksesi tai näyttääksesi ohitetut sivut ja kysymykset.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "<PERSON>ämän sivun logiikka ohittaa kaikki sivut sivulle {pageNumber} asti ({numQuestionsSkipped} kysymykset ohitetaan). Napsauta piilottaaksesi tai näyttääksesi ohitetut sivut ja kysymykset.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Tä<PERSON>än sivun logiikka hyppää kyselyn loppuun ({numQuestionsSkipped} kysymystä ohitetaan). Napsauta piilottaaksesi tai näyttääksesi ohitetut sivut ja kysymykset.", "app.containers.Admin.projects.project.survey.newAnalysis": "Uusi analyysi", "app.containers.Admin.projects.project.survey.nextInsight": "<PERSON><PERSON><PERSON> o<PERSON>lus", "app.containers.Admin.projects.project.survey.openAnalysis": "Avaa AI-analyysi", "app.containers.Admin.projects.project.survey.otherResponses": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.page": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.previousInsight": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.responses": "Vastaukset", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Tämän sivun vastausten määrä on pienempi kuin kyselyvastausten kokonaismäärä, koska jotkut vastaajat eivät ole nähneet tätä sivua kyselyn logiikan vuoksi.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "<PERSON><PERSON><PERSON>ä<PERSON> kysymykseen annettujen vastausten määrä on pienempi kuin kyselyvastausten kokonaismäärä, koska jotkut vastaajat eivät ole nähneet tätä kysymystä kyselyn logiikan vuoksi.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Tee välittömästi yhteenveto kaikista vastauksistasi.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "<PERSON><PERSON><PERSON><PERSON><PERSON> luonnollisella kielellä.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Hanki viittauksia yksittäisiin vastauksiin tekoälyn luomista tiivistelmistä.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "<PERSON><PERSON> {link} sa<PERSON><PERSON><PERSON> t<PERSON> yleiskatsauksen.", "app.containers.Admin.projects.project.survey.upsell.button": "Avaa AI-analyysi", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.Admin.projects.project.survey.upsell.title": "Analysoi dataa nopeammin te<PERSON> a<PERSON>", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Tämä ominaisuus ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Näytä", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Tu<PERSON>tu te<PERSON>lypohjaisiin yht<PERSON>vedoihin ja tarkastele yksittäisiä lähetyksiä.", "app.containers.Admin.projects.project.traffic.selectPeriod": "<PERSON>its<PERSON>", "app.containers.Admin.projects.project.traffic.trafficSources": "Liikenteen lähteet", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Olemme muuttaneet tapaamme kerätä ja näyttää kävijätietoja. Tämän seurauksena kävijätiedot ovat tarkempia ja saatavilla on useampia datatyyppejä, mutta ne ovat edelleen GDPR-yhteensopivia. Aloitimme näiden uusien tietojen keräämisen vasta marraskuussa 2024, joten sitä ennen tietoja ei ole saatavilla.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Lisää tekstiä vaiheesta", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Tämä on vähän tekstiä. Voit muokata ja muotoilla sitä käyttämällä oikeanpuoleisen paneelin editoria.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Hank<PERSON>en tulokset", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Lisää projektin tavoite, käytetyt osallistumismenetelmät ja tulos", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Viera<PERSON>jat", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Tämä raportti sisältää tallentamattomia muutoksia. Tallenna ennen tulostusta.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "<PERSON><PERSON><PERSON><PERSON> on jo varattu", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "<PERSON><PERSON><PERSON><PERSON><PERSON> ed<PERSON>n {days} päivään", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "<PERSON><PERSON><PERSON> til<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Näytä vertailu viimeiseen ajanjaksoon", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "<PERSON><PERSON> on ensin valittava a<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Ko<PERSON>ntit", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Toiminnat", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Näytä kommentit", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "<PERSON><PERSON><PERSON><PERSON> tulot", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "Näytä <PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Väestötiedot", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Rekisteröintikenttä", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Tuntematon", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Käyttäjät: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Venyttää", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktiivinen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "Val<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "Avoin", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projektit", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "<PERSON><PERSON><PERSON> tila", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "Julkaistu", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "<PERSON><PERSON><PERSON><PERSON><PERSON> widgetin tiedot puuttuvat. Määritä se uudelleen tai poista se, jotta voit tallentaa raportin.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "<PERSON><PERSON><PERSON>s<PERSON><PERSON>in terveyspisteet", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "vuosineljä<PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "vuosi", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "Tästä projektista ei löytynyt sopivia vaiheita", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Vaihetta ei ole valittu. Valitse ensin vaihe.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "Ei projektia", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Projektia ei ole valittu. Valitse ensin projekti.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "Et voi kopioida tätä raporttia, koska se sisältää tietoja, joihin sinulla ei ole pääsyä.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "Et voi muokata tätä raporttia, koska se sisältää tietoja, joihin sinulla ei ole käyttöoikeutt<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>ti poistaa \"{reportName}\"? Tätä toimintoa ei voi kumota.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän raportin? Tätä toimintoa ei voi kumota.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Poistaa", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Ko<PERSON>i", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "Muokattu {days, plural, no {# päivää} one {# päivä} other {# päivää}} sitten", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "<PERSON><PERSON>ä raporttia luotaessa tapahtui virhe. Yritä uudelleen my<PERSON>.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Aloita tyhjältä sivulta", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Aloita Community Monitor -mallilla", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "<PERSON><PERSON><PERSON> rap<PERSON> ja jaa se sisäisten si<PERSON>ryhmien tai yhteisön kanssa PDF-tiedostona.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "<PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON> rap<PERSON>i", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Projektia ei ole valittu", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "<PERSON><PERSON><PERSON> al<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Tulosta PDF-muotoon", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Neljännes {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Raportti<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "<PERSON><PERSON><PERSON><PERSON><PERSON> ots<PERSON><PERSON><PERSON> raportti on jo olemassa. Valitse toinen otsikko.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Valitse neljännes", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Valitse vuosi", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Jaa PDF-muodossa", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Voit jakaa kaikkien kanssa tulostamalla raportin PDF-tiedostona.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Jaa verkkolinkkinä", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Tämä ve<PERSON>link<PERSON> on vain järjestelmänvalvojien käytettävissä.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Jaa", "app.containers.Admin.reporting.contactToAccess": "Mukautetun raportin luominen on osa premium-lisenssiä. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja siitä.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Yhteisön valvontaraportit", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Nämä raportit liittyvät Community Monitoriin. Raportit luodaan automaattisesti neljännesvuosittain.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "<PERSON><PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "<PERSON><PERSON><PERSON> rap<PERSON> ja jaa se sisäisten si<PERSON>hmien tai yhteisön kanssa verk<PERSON>linkin avulla.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Raporttisi näkyvät täällä.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "<PERSON><PERSON> rap<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Edistymisraportit", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Nämä ovat hallituksen menestyspäällikkösi luomia raportteja", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON> raportit", "app.containers.Admin.reporting.deprecated": "POISTETTU", "app.containers.Admin.reporting.helmetDescription": "Järjestelmänvalvojan raportointisivu", "app.containers.Admin.reporting.helmetTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.printPrepare": "Valmistellaan tulostamista...", "app.containers.Admin.reporting.reportBuilder": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.reportHeader": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.warningBanner3": "<PERSON><PERSON><PERSON><PERSON><PERSON> raportin kaaviot ja luvut päivittyvät automaattisesti vain tällä sivulla. Tallenna raportti päivittääks<PERSON> ne muille sivuille.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "<PERSON><PERSON><PERSON><PERSON> pohja", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Käytetyt menetelmät", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Poll", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Edelliset {days} päivää: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "ehdotuksia", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "<PERSON><PERSON><PERSON><PERSON> kysely", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Vapaaehtoistyö", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Äänestäminen", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "Näytä", "app.containers.Admin.surveyFormTab.downloads": "Lataukset", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "<PERSON><PERSON><PERSON> to<PERSON> kysely", "app.containers.Admin.surveyFormTab.editSurveyForm": "Muokkaa k<PERSON>", "app.containers.Admin.surveyFormTab.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mitä tietoja on annett<PERSON>, lisää lyhyitä kuvauksia tai ohjeita osallistujien vastausten tueksi ja määritä, onko kukin kenttä valinnainen vai pakollinen.", "app.containers.Admin.surveyFormTab.surveyForm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenButton": "<PERSON><PERSON> uusi tunnus", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Peruuta", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "<PERSON>nn<PERSON> on luotu. <PERSON><PERSON><PERSON> alla oleva {secret} ja säilytä se turvallisesti.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "Luo uusi tunnus käytettäväksi julkisen API:n kanssa.", "app.containers.Admin.tools.apiTokens.createTokenError": "<PERSON> t<PERSON> nimi", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "<PERSON><PERSON> tunnus", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>Tärkeää!</b> Voit kopioida tämän {secret} vain kerran. Jos suljet tämän ikkunan, et näe sitä enää.", "app.containers.Admin.tools.apiTokens.createTokenName": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "<PERSON> t<PERSON> nimi", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "<PERSON><PERSON><PERSON> on luotu", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "kiinni", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "<PERSON><PERSON><PERSON> {secret}", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Kopioitu!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "<PERSON><PERSON> uusi tunnus", "app.containers.Admin.tools.apiTokens.createdAt": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.delete": "Po<PERSON> tunnus", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän tunnuksen?", "app.containers.Admin.tools.apiTokens.description": "Hallinnoi julkisen sovellusliittymämme API-tunnuksia. Lisätietoja on osoitteessa {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.link": "API-dokumentaatio", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "<PERSON><PERSON>", "app.containers.Admin.tools.apiTokens.noTokens": "<PERSON>ulla ei ole vielä tunnuksia.", "app.containers.Admin.tools.apiTokens.title": "Julkiset API-tunnukset", "app.containers.Admin.tools.esriDisabled": "Esri-integraatio on lisäominaisuus. Ota yhteyttä GovSuccess Manageriin, jos haluat lisätietoja tästä.", "app.containers.Admin.tools.esriIntegration2": "Esri-integraatio", "app.containers.Admin.tools.esriIntegrationButton": "<PERSON><PERSON>öö<PERSON>", "app.containers.Admin.tools.esriIntegrationDescription3": "Yhdistä <PERSON>-tilisi ja tuo tiedot ArcGIS Onlinesta suoraan karttaprojekteihisi.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri logo", "app.containers.Admin.tools.esriKeyInputDescription": "Lisää Esri API -avain salliaksesi karttatasosi tuomisen ArcGIS Onlinesta projektien karttavälilehdillä.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API avain", "app.containers.Admin.tools.esriKeyInputPlaceholder": "Liitä API-ava<PERSON> t<PERSON>n", "app.containers.Admin.tools.esriMaps": "Esri Maps", "app.containers.Admin.tools.esriSaveButtonError": "Avaimesi tallentamisessa tapahtui virhe. Yritä uudelleen.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API-avain tall<PERSON>u", "app.containers.Admin.tools.esriSaveButtonText": "<PERSON><PERSON><PERSON> a<PERSON>", "app.containers.Admin.tools.learnMore": "<PERSON><PERSON> l<PERSON>", "app.containers.Admin.tools.managePublicAPIKeys": "Hallinnoi API-avaimia", "app.containers.Admin.tools.manageWidget": "Muok<PERSON>a widgetiä", "app.containers.Admin.tools.manageWorkshops": "Muokkaa työpajoja", "app.containers.Admin.tools.powerBIAPIImage": "Power BI -kuva", "app.containers.Admin.tools.powerBIDescription": "Käytä plug & play Power BI -mallejamme käyttääksesi Go Vocalin tietoja Microsoft Power BI -työtilassasi.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI ei ole osa lisenssiäsi. Ota yhteyttä GovSuccess Manageriin, jos haluat lisätietoja tästä.", "app.containers.Admin.tools.powerBIDownloadTemplates": "<PERSON><PERSON><PERSON> malleja", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "<PERSON><PERSON> a<PERSON> k<PERSON> Go Vocal-tietojasi Power BI -<PERSON><PERSON><PERSON><PERSON>, tä<PERSON><PERSON><PERSON> mallin avulla voit määrittää uuden tietovirran, joka muodostaa yhteyden Go Vocal-tietoihisi. Kun olet ladannut tämän mallin, sinun on ensin löydettävä ja korvattava seuraavat merkkijonot ##CLIENT_ID## ja ##CLIENT_SECRET## mallista julkisilla API-tunnistetiedoilla ennen lataamista PowerBI:hen.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Tietovirran malli", "app.containers.Admin.tools.powerBITemplates.intro": "Huomautus: <PERSON><PERSON> ha<PERSON> k<PERSON> jompaakumpaa näistä Power BI -malleista, sinun on ensin {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "luo joukon valtuustietoja julkiselle sovellusliittymällemme", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Tämä malli luo Power BI -raport<PERSON> Go <PERSON>-tieto<PERSON><PERSON> perusteel<PERSON>. Se määrittää kaikki tieto<PERSON>hteydet Go Vocal-alustallesi, luo tietomallin ja joitain oletusarvoisia hallintapaneeleja. Kun avaat mallin Power BI:ssä, sinua pyydetään antamaan julkiset API-kirjautumistietosi. Sinun on myös annettava alustasi perus-URL, joka on: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Raportti<PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Lisätietoja Go Vocal-tietojen käyttämisestä Power BI:ssä on osoitteessa {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/en/articles/8512834-use-citizenlab-data-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI -malleja", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Hallinnoi valtuustietoja luodaksesi mukautettuja integraatioita julkiseen sovellusliittymäämme.", "app.containers.Admin.tools.publicAPIDisabled1": "<PERSON><PERSON><PERSON> sovellusliittymä ei ole osa nykyistä lisenssiäsi. Ota yhteyttä GovSuccess Manageriin, jos haluat lisätietoja tästä.", "app.containers.Admin.tools.publicAPIImage": "Julkinen API-kuva", "app.containers.Admin.tools.publicAPITitle": "Julkinen API-käyttö", "app.containers.Admin.tools.toolsLabel": "Työkalut", "app.containers.Admin.tools.widgetDescription": "Voit luoda widgetin, muokata sitä ja lisätä sen omalle verkkosivustollesi houkutellaksesi ihmisiä tälle alustalle.", "app.containers.Admin.tools.widgetImage": "<PERSON><PERSON><PERSON> kuva", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Järjestä suoria <PERSON>, helpota samanaikaisia ryhmäkeskusteluja ja -keskusteluja. Kerää palautetta, äänestä ja saavuta konsensus aivan kuten offline-tilassa.", "app.containers.Admin.tools.workshopsImage": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>va", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/en/articles/4155778-setting-up-an-online-workshop", "app.containers.Admin.tools.workshopsTitle": "Online-keskustelutyöpajat", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "alustan k<PERSON>hteen<PERSON>ä", "app.containers.AdminPage.DashboardPage._blank": "tunt<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.allGroups": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.allProjects": "Kaikki projektit", "app.containers.AdminPage.DashboardPage.allTime": "<PERSON><PERSON> a<PERSON>", "app.containers.AdminPage.DashboardPage.comments": "Ko<PERSON>ntit", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Ko<PERSON>ntit", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "<PERSON><PERSON><PERSON> k<PERSON>en edustavuuden mittaamiseen tarvitaan perustietojoukko.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "<PERSON><PERSON><PERSON> pian", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {fieldName} hallint<PERSON><PERSON><PERSON> parissa, se on saatavilla pian", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# kohde on} other {# kohdetta on}} piilotettu tähän kaavioon. Vaihda arvoon {tableViewLink} nähd<PERSON><PERSON><PERSON> kaikki tiedot.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} käyttäjän rekisteröintiä varten", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} / {total} käyttäjistä mukana ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "Näytä {numberOfHiddenItems} lisää", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "<PERSON>.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Edustavuuspis<PERSON>t:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Tämä pistemä<PERSON><PERSON><PERSON> kertoo, kuinka tarkasti alustan käyttäjätiedot heijastavat koko väestöä. Lisätietoja {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Lähetä <PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "pöytänäkymä", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Kokonaisväestö", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Käyttäjät", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Lisää ikäryhmä", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} ja en<PERSON><PERSON><PERSON>n", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "{upperBound} ja sitä vanhemmat ikäryhmät eivät sisälly.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Ikäryhm<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "ja yli", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Käytä esimerkkiryhmittelyä", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Tyhjennä", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "From", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "Aseta ikäryhmät perustietojoukkosi mukaisiksi.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Muokkaa ikäryhmiä", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Tätä tuotetta ei lasketa.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON><PERSON>ä<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Katso {numberOfHiddenItems} lisää...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (valinnainen)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Ikäryhmät (syntymävuosi)", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "<PERSON><PERSON><PERSON> pian", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Saattaa loppuun", "app.containers.AdminPage.DashboardPage.components.Field.default": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Täytä kaikki käytössä olevat vaihtoehdot tai poista vaihtoehdot, jotka haluat jättää pois kaaviosta. Vähintään yksi vaihtoehto on täytettävä.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Epätäydellinen", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Asukkaiden kokonaismäärä", "app.containers.AdminPage.DashboardPage.components.Field.options": "Vaihtoehdot", "app.containers.AdminPage.DashboardPage.components.Field.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Tallennettu", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Ole hyvä ja {setAgeGroupsLink} ensin aloit<PERSON><PERSON><PERSON> perustietojen syöttämisen.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "as<PERSON><PERSON> i<PERSON>hm<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Keskim. vasteaika: {days} päivää", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Päivien keskimääräinen vastausaika", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Syötön tila", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Syötteet tilan mukaan", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Virallinen päivitys", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Vast<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Tila", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "<PERSON><PERSON> mu<PERSON>u", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.editBaseData": "<PERSON>ok<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "miten laskemme edustavuuspisteet", "app.containers.AdminPage.DashboardPage.continuousType": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> koko<PERSON>umma", "app.containers.AdminPage.DashboardPage.customDateRange": "Muka<PERSON>ttu", "app.containers.AdminPage.DashboardPage.day": "päivä", "app.containers.AdminPage.DashboardPage.false": "väärä", "app.containers.AdminPage.DashboardPage.female": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "5 parasta syötettä reaktioiden mukaan", "app.containers.AdminPage.DashboardPage.fromTo": "{from} - {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Hallintapaneeli alustalla tapahtuville toimille", "app.containers.AdminPage.DashboardPage.helmetTitle": "Järjestelmänvalvojan hallintapaneelisivu", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Valitse näytettävä resurssi projektin mukaan", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Valitse tunnisteella näytettävä resurssi", "app.containers.AdminPage.DashboardPage.inputs1": "Toiminnat", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Syötteet tilan mukaan", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "Valitse käyttäjäryhmä", "app.containers.AdminPage.DashboardPage.male": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.month": "ku<PERSON><PERSON>i", "app.containers.AdminPage.DashboardPage.noData": "Näytettäviä tietoja ei ole.", "app.containers.AdminPage.DashboardPage.noPhase": "<PERSON><PERSON>lle projektille ei ole luotu vai<PERSON>tta", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Osallist<PERSON><PERSON><PERSON>, jotka lähettivät palautetta, reagoivat tai kommentoivat.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "<PERSON>i pidä", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Tykkää", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Yhteensä reaktioita", "app.containers.AdminPage.DashboardPage.overview.management": "Hall<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projektit ja osallistuminen", "app.containers.AdminPage.DashboardPage.overview.showLess": "Näytä vähemmän", "app.containers.AdminPage.DashboardPage.overview.showMore": "Näytä lisää", "app.containers.AdminPage.DashboardPage.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.participationPerProject": "Osallistuminen proje<PERSON>", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Osallist<PERSON>nen <PERSON>", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Edelliset 30 päivää", "app.containers.AdminPage.DashboardPage.previous90Days": "Edelliset 90 päivää", "app.containers.AdminPage.DashboardPage.previousWeek": "Edellinen viikko", "app.containers.AdminPage.DashboardPage.previousYear": "<PERSON><PERSON><PERSON> vuosi", "app.containers.AdminPage.DashboardPage.projectType": "Projektin tyyppi: {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Tämä perustietojoukko tarvitaan alustan käyttäjien edustavuuden laskemiseen suhteessa kokonaisväestöön.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "<PERSON>.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "<PERSON><PERSON>, kuinka edustavia alustasi käyttäjät ovat verrattuna koko väestöön – käyttäjien rekisteröinnin yhteydessä kerättyjen tietojen perusteella. Lisätietoja {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "<PERSON><PERSON>, kuinka edustavia alustasi käyttäjät ovat verrattuna koko väestöön – käyttäjien rekisteröinnin yhteydessä kerättyjen tietojen perusteella.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> edustavuus", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Mitään käytössä olevista rekisteröintikentistä ei tueta tällä hetkellä.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Täällä voit näyttää/pii<PERSON><PERSON><PERSON> kohteita kojelau<PERSON>la ja syöttää perustiedot. Vain käytössä olevat kentät {userRegistrationLink} näkyvät tässä.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "<PERSON>ok<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "käyttäjän rekisteröinti", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Lähetä <PERSON>", "app.containers.AdminPage.DashboardPage.resolutionday": "Päivissä", "app.containers.AdminPage.DashboardPage.resolutionmonth": "k<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.resolutionweek": "Viikoissa", "app.containers.AdminPage.DashboardPage.selectProject": "Valitse projekti", "app.containers.AdminPage.DashboardPage.selectedProject": "<PERSON><PERSON><PERSON>sen projektin suodatin", "app.containers.AdminPage.DashboardPage.selectedTopic": "nykyinen tunnist<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "<PERSON><PERSON>, mitä alustallasi tapah<PERSON>u.", "app.containers.AdminPage.DashboardPage.tabOverview": "Yleiskatsaus", "app.containers.AdminPage.DashboardPage.tabReports": "Raportit", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabUsers": "Käyttäjät", "app.containers.AdminPage.DashboardPage.timelineType": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.titleDashboard": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.total": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Tämä {period}", "app.containers.AdminPage.DashboardPage.true": "totta", "app.containers.AdminPage.DashboardPage.unspecified": "määrittelemätön", "app.containers.AdminPage.DashboardPage.users": "Käyttäjät", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Käyttäjät iän mukaan", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Käyttäjät maantieteellisen alueen mukaan", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Käyttäjät sukupuolen mukaan", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Rekisteröinnit", "app.containers.AdminPage.DashboardPage.week": "viikko", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Vinkkejä favicon-kuvan valintaan: vali<PERSON>e yks<PERSON>n kuva, koska näytettävä kuva<PERSON>ko on hyvin pieni. Kuva tulee tallentaa PNG-muo<PERSON>sa, ja sen tulee olla neliömäinen ja taustalla on läpinäkyvä (tai tarvitta<PERSON>a valkoinen tausta). Suosikkikuvakkeesi tulisi as<PERSON>a vain kerran, koska muutokset edellyttävät teknistä tukea.", "app.containers.AdminPage.FaviconPage.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Jokin meni pieleen. Ole hyvä ja koke<PERSON> my<PERSON>.", "app.containers.AdminPage.FaviconPage.saveSuccess": "<PERSON>est<PERSON>!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Lisätä", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Poistaa", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Kansion ylläpitäjät", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Kansion ylläpitäjät voivat muokata kansion kuvausta, luoda uusia projekteja kansiossa ja heillä on projektinhallintaoikeudet kaikkiin kansion projekteihin. He eivät voi poistaa projekteja, eik<PERSON> heillä ole pääsyä projekteihin, jotka eivät ole heidän kansiossaan. Voit {projectManagementInfoCenterLink} löytää lisätietoja projektinhallintaoikeuksista.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "Vastaavaa ei l<PERSON>", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "k<PERSON><PERSON> ohje<PERSON>ksessamme", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Etsi k<PERSON>äjiä", "app.containers.AdminPage.FoldersEdit.addToFolder": "Lisää kansioon", "app.containers.AdminPage.FoldersEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Poista tämä kansio", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.draftStatus": "Luonnos", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Lisää tiedostoja tähän kansioon", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Tiedostot eivät saa olla suurempia kuin 50 Mt. Lisätyt tiedostot näkyvät kansiosivulla.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "Tässä kansiossa ei ole projekteja. Palaa Projektit-päävälilehteen luodaksesi ja lisätäksesi projekteja.", "app.containers.AdminPage.FoldersEdit.folderName": "<PERSON><PERSON><PERSON> nimi", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.multilocError": "Kaikki tekstikentät on täytettävä jokaisella kielellä.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "<PERSON><PERSON><PERSON>än kansioon ei voi lisätä projekteja.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Kans<PERSON><PERSON><PERSON> kuva", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Käyttöoikeudet", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Kansioprojektit", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "asetukset", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projektit lisätty tähän kansioon", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projektit, jotka voit lisätä tähän kansioon", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "<PERSON><PERSON><PERSON>, onko tämä kansio \"luonnos\", \"julkaistu\" vai \"arkistoitu\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Julkaistu", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Poista kansiosta", "app.containers.AdminPage.FoldersEdit.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Jokin meni pieleen. Ole hyvä ja koke<PERSON> my<PERSON>.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "<PERSON>est<PERSON>!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Lyhyt k<PERSON>", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "näkyy etusivulla", "app.containers.AdminPage.FoldersEdit.statusLabel": "<PERSON><PERSON><PERSON> tila", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Selitä miksi projektit kuuluvat yhteen, määritä visuaalinen ilme ja jaa tietoa.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Selitä miksi projektit kuuluvat yhteen, määritä visuaalinen ilme ja jaa tietoa.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Kaikki tekstikentät on täytettävä.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "<PERSON><PERSON> uusi kansio", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "asetukset", "app.containers.AdminPage.FoldersEdit.url": "URL-osoite", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Näytä kansio", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "<PERSON><PERSON><PERSON> sank<PERSON> kuvaa ja tekstiä.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Sankaribanneri", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspiration Hub on paikka, josta voit löytää inspiraatiota projekteihisi selaamalla projekteja muilla al<PERSON>oilla.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Muokkaa alustasi eh<PERSON> ja tietosuojakäytäntöä. <PERSON><PERSON>, mukaan lukien <PERSON>- ja <PERSON><PERSON>sivu<PERSON>, voidaan muokata {navigationLink} -välilehdellä.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Alustakäytännöt", "app.containers.AdminPage.PagesEdition.privacy-policy": "Tietosuojakäytäntö", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Käyttöehdot", "app.containers.AdminPage.Project.confirmation.description": "Tätä toimintoa ei voi kumota.", "app.containers.AdminPage.Project.confirmation.no": "Peruuta", "app.containers.AdminPage.Project.confirmation.title": "<PERSON><PERSON><PERSON><PERSON> varmasti nollata kaikki o<PERSON>?", "app.containers.AdminPage.Project.confirmation.yes": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.AdminPage.Project.data.descriptionText1": "<PERSON><PERSON><PERSON><PERSON><PERSON> ideat, kom<PERSON><PERSON>, <PERSON><PERSON><PERSON>, re<PERSON><PERSON><PERSON>, kys<PERSON><PERSON><PERSON><PERSON>set, kys<PERSON><PERSON><PERSON><PERSON>set, vapaaehtoiset ja tapahtumaan ilmoittautuneet. Äänestysvaiheiden tapauksessa tämä toiminto tyhjentää <PERSON>, mutta ei vai<PERSON>.", "app.containers.AdminPage.Project.data.title": "Poista kaikki osallistumistiedot tästä projektista", "app.containers.AdminPage.Project.resetParticipationData": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.AdminPage.Project.settings.accessRights": "Käyttöoikeudet", "app.containers.AdminPage.Project.settings.back": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.data": "Data", "app.containers.AdminPage.Project.settings.description": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.general": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Project.settings.projectTags": "<PERSON><PERSON><PERSON><PERSON> tun<PERSON>", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "Luettelo alustalla olevista projekteista", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "<PERSON>je<PERSON><PERSON>", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Luo uusia projekteja tai hallinnoi olemassa olevia projekteja.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projektit", "app.containers.AdminPage.ProjectDashboard.published": "Julkaistu", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "Keskusta", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "<PERSON><PERSON><PERSON><PERSON> leveys", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "<PERSON><PERSON><PERSON><PERSON> koh<PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Painikkeen te<PERSON>ti", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Syötä teksti painik<PERSON>elle", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Painikkeen t<PERSON>i", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "Painikkeen URL-osoite", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "<PERSON>-osoite", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "<PERSON><PERSON><PERSON><PERSON> as<PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "<PERSON><PERSON><PERSON><PERSON> kuvaus", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Näkyy kotisivun projektikortissa.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Näkyy projektin sivulla. <PERSON><PERSON><PERSON>, mistä projektissa on kyse, mitä odotat käyttäjiltäsi ja mitä he voivat odottaa sinulta.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Jokin meni pieleen. Ole hyvä ja kokeile my<PERSON>", "app.containers.AdminPage.ProjectDescription.preview": "Esikatselu", "app.containers.AdminPage.ProjectDescription.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.ProjectDescription.saved": "Tallennettu!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "<PERSON><PERSON><PERSON><PERSON>, mink<PERSON> viestin haluat antaa yleisöllesi. Muokkaa projektiasi ja rikasta sitä kuvilla, videoilla, tiedostoliitteillä,… Nämä tiedot auttavat vierailijoita ymmärtämään, mistä projektissasi on kyse.", "app.containers.AdminPage.ProjectDescription.titleDescription": "<PERSON><PERSON><PERSON> kuvaus", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON><PERSON> tila", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "Sisällytä reuna", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "Keski<PERSON>koinen", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "<PERSON><PERSON> muokkaus", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Kartan keskipisteen oletusleveysaste. Hyväksyy arvon -90 ja 90 välillä.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Kartan keskipisteen oletuspituusaste. Hyväksyy arvon -90 ja 90 välillä.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "<PERSON>ok<PERSON><PERSON> karttata<PERSON>a", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "<PERSON><PERSON><PERSON><PERSON> tasoa", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Jokin meni pieleen. Ole hyvä ja kokeile my<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.here": "tässä", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Tuo GeoJSON-tiedosto", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Kerroksen väri", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "<PERSON><PERSON><PERSON> ker<PERSON>sen ominaisuudet muotoillaan tällä värillä. Tämä väri korvaa myös kaikki GeoJSON-tiedostosi n<PERSON>yi<PERSON> tyylit.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "<PERSON><PERSON> kuvake", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "<PERSON><PERSON><PERSON> vali<PERSON> k<PERSON>, joka näkyy merkeissä. Napsauta {url} nähdäks<PERSON> luette<PERSON> kuva<PERSON>ke<PERSON>, jotka voit valita.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "<PERSON><PERSON> nimi", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Tämä tason nimi näkyy kartan selitteessä", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "<PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Tämä teksti näytetään työkaluvihjeenä, kun hiiren osoitin viedään kartan tasoominaisuuksien päälle", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Tuemme tällä hetkellä GeoJSON-tiedostoja. Lue {supportArticle} saadaksesi vinkkejä karttatasojen muuntamiseen ja tyyliin.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Kartan oletuskeskus ja zoomaus", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Kartan oletuskeskipiste ja zoomaustaso. Säädä alla olevia arvoja manuaalisesti tai napsauta {button} -painiketta kartan vasemmassa alakulmassa tallentaaksesi kartan nykyisen keskipisteen ja zoomaustaso oletusarvoiksi.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "<PERSON><PERSON><PERSON> ka<PERSON><PERSON>, mukaan lukien karttatasojen lataaminen ja muotoilu sekä kartan keskipisteen ja zoomaustason asettaminen.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Kartan määritys", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "<PERSON><PERSON><PERSON>kokoonpano on tällä hetkellä jaettu eri vaiheiden kesken, et voi luoda erilaisia karttakonfiguraatioita vaiheittain.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "<PERSON><PERSON> kerros", "app.containers.AdminPage.ProjectEdit.MapTab.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "<PERSON><PERSON><PERSON> zoomaus", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/en/articles/7022129-collecting-input-and-feedback-list-and-map-view", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "<PERSON><PERSON><PERSON><PERSON> taso", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Kartan zoomaustaso", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Kartan oletuszoomaustaso. Hyväksyy arvon väliltä 1–17, jossa 1 on t<PERSON><PERSON>in loitonnettu (k<PERSON> on näkyvissä) ja 17 on täysin zoomattu (korttelin ja rakennukset näkyvät)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonymisoi kaikki k<PERSON>äj<PERSON>ot", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "<PERSON><PERSON><PERSON> k<PERSON><PERSON> antamat tiedot anonymisoidaan ennen kuin ne tallennetaan", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Käyttäjien on silti noudatettava \"Pääsyoikeudet\"-välilehden osallistumisvaatimuksia. Käyttäjäprofiilitiedot eivät ole käytettävissä kyselytietojen viennissä.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "<PERSON><PERSON> otat tä<PERSON>än vaihtoe<PERSON>don k<PERSON>öö<PERSON>, käyttäjien rekisteröintikentät näkyvät kyselyn viimeisenä sivuna, eivät osana rekisteröitymisprosessia.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demografiset kentät kyselymuodossa", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Näytetäänkö demografiset kentät kyselyssä?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "<PERSON>e lisää siitä, miten automaattinen jakaminen toimii tässä artikkelissa.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/en/articles/8124630-voting-and-prioritization-methods-for-enhanced-decision-making#h_dde3253b64", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Jaa tulokset automaattisesti", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Äänestystulokset jaetaan alustalla ja sähköpostitse osallistujille vaiheen päätyttyä. Tämä varmistaa oletuksena läpinäkyvyyden.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "<PERSON><PERSON><PERSON> j<PERSON>n", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Lisää <PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Lisää kys<PERSON>yn liittyvä kysymys", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Peruuta", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Peruuta", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Peruuta", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Poistaa", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Poistaa", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Muok<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Muokkaa kysymystä", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "<PERSON><PERSON> k<PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Vaihtoehtojen enimmäismäärä on suurempi kuin vaihtoehtojen määrä", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Monivalinta", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "<PERSON><PERSON> v<PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "<PERSON><PERSON><PERSON><PERSON> tulee olla <PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Vain yksi vai<PERSON>o", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "<PERSON><PERSON><PERSON><PERSON> on vain yksi vaiht<PERSON>o", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "<PERSON><PERSON><PERSON>: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Täällä voit luoda kyselyn k<PERSON>, määrittää vastausvaih<PERSON>ehdot, joista osallistu<PERSON>t voivat valita jokaiseen kysymykseen, päättää, halu<PERSON><PERSON> osallistujien valita vain yhden vastausvaih<PERSON>ehdon (yksivalinta) vai useita vastausvaihtoehtoja (monivalinta) ja viedä kyselyn tulokset. Voit luoda useita kyselykysymyksiä yhdessä kyselyssä.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "<PERSON><PERSON><PERSON><PERSON><PERSON> valinta", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Äänestysasetukset ja tulokset", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Vää<PERSON><PERSON> maksimi", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON>, lis<PERSON><PERSON> tunnisteita tai kopioi viestejä projektin se<PERSON>an vaiheeseen.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "<PERSON><PERSON><PERSON><PERSON>, anna pala<PERSON> ja määritä aiheita.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Syöttöhallinta", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "<PERSON><PERSON><PERSON> jakaminen on poistettu käytöstä.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Äänestystuloksia ei jaeta vaiheen lo<PERSON>, ellet muuta niitä vaiheen aset<PERSON>.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Nämä tulokset jaetaan automaattisesti, kun vaihe päättyy. Muokkaa tämän vaiheen päättymispäivää tulosten jakamisen aikana.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "<PERSON><PERSON><PERSON> t<PERSON> (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Tulokset", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Täältä voit ladata tämän projektin Typeform-kyselyn (kyselyjen) tulokset Excel-tiedostona.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Tutkimustulokset", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Lisää syy", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "<PERSON><PERSON><PERSON>?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Käytä tätä se<PERSON>, mitä vapaaehtoisilta vaaditaan ja mitä he voivat odottaa.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "<PERSON><PERSON> voitu tall<PERSON>, koska lomake sisältää virheitä.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Poistaa", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Syy on toiminta tai toiminta, johon o<PERSON>t voivat tehdä vapaaehtoistyötä.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Muokkaa s<PERSON>ytä", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Lisää kuvaus", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Lisää otsikko", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Vapaaehtoisten vienti", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Syy on toiminta tai toiminta, johon o<PERSON>t voivat tehdä vapaaehtoistyötä.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "Uusi syy", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Täällä voit mä<PERSON>rittä<PERSON> s<PERSON>yt, miksi käyttäjät voivat tehdä vapaaehtoistyötä, ja ladata vapaaeht<PERSON>t.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Vapaaehtoistyö", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {ei osallistujia} one {# osallistuja} other {# osallistujaa}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "bud<PERSON><PERSON> jako", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "Määritä vaiht<PERSON><PERSON><PERSON><PERSON> budjetti ja pyydä osallistujia valitsemaan halu<PERSON> vaiht<PERSON>, jotka sopivat kokonaisbudjettiin.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "<PERSON><PERSON><PERSON> jako", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Käyttäjien kommentoimisen salliminen voi vääristää äänestysprosessia.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Vaihtoehtojen o<PERSON>näkymä", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Toiminnot käyttäjille", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "<PERSON><PERSON><PERSON>, mitä lisätoimintoja käyttäjät voivat tehdä.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "Kiinteä määrä", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> on \"äänestys\".", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "<PERSON><PERSON> lisä<PERSON> <b> {voteTypeDescription} </b> k<PERSON><PERSON><PERSON>st<PERSON> {optionAnalysisArticleLink}-sovelluksesta.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Äänten enimmäismäärä vaihtoehtoa kohden", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Äänten enimmäismäärä", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Voit rajoittaa käyttäjän antamien äänten kokonaismäärää (enintään yksi ääni vaihtoehtoa kohden).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "useita ää<PERSON>ä vaihtoehtoa kohden", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Käyttäjille annetaan tietty määrä tokeneita jaetta<PERSON>ksi vaihtoehtojen välillä", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "<PERSON>ita ä<PERSON> per vaiht<PERSON>hto", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "Äänten määrä käyttäjää kohden", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "Vaihtoehtoan<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Äänestysvaihtoehdot", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "yksi <PERSON> vai<PERSON>ht<PERSON> kohden", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Käyttäjät voivat hyväksyä minkä tahansa vaiht<PERSON>don", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "<PERSON><PERSON><PERSON> per vaiht<PERSON>hto", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Millä nimellä äänestystä pitäisi kutsua?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "<PERSON><PERSON><PERSON>, pisteet, hiilidioksidihyvitykset...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "Esim token, piste, hiilihyvitys...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Äänestää", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Jokaisella äänestysmenetelmällä on erilaiset esiasetukset", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Äänestysmenetelmä", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Äänestysmenetelmä määrittää s<PERSON>ännöt, miten käyttäjät äänestävät", "app.containers.AdminPage.ProjectEdit.addNewInput": "Lisää syöte", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Voit lisätä projektisi kansioon nyt tai tehdä sen myöhemmin projektin asetuksissa.", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "<PERSON><PERSON><PERSON><PERSON> tun<PERSON>", "app.containers.AdminPage.ProjectEdit.altText": "Vaihtoehtoinen teksti", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Nimetön äänestys", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "Kun se on k<PERSON>ytössä, on mah<PERSON>tonta nähdä, kuka ä<PERSON><PERSON><PERSON> mitä<PERSON>. Käyttäjät tarvitsevat edelleen tilin ja voivat äänestää vain kerran.", "app.containers.AdminPage.ProjectEdit.approved": "Hyväksytty", "app.containers.AdminPage.ProjectEdit.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Arkistoidut projektit ovat edelleen näkyvissä, mutta ne eivät salli enää osallistumista", "app.containers.AdminPage.ProjectEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Tätä aluetta ei voi poistaa, koska sitä käytetään projektien näyttämiseen seuraavilla muokatuilla sivuilla. Sinun on poistettava alueen linkitys sivusta tai poistettava sivu ennen kuin voit poistaa alueen.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Projekti näkyy jokaisessa aluesuodattimessa.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projektit voidaan suodattaa etusivulla alueiden avulla. Alueet voidaan asettaa {areasLabelTooltipLink}.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "tässä", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "<PERSON><PERSON> erity<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Projekti ei näy, kun suodatetaan alueen mukaan.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Valitut", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Projekti näkyy valituilla aluesuodattimilla.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "Eniten keskusteltu", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Lisää kyselyn si<PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> kysely<PERSON> on alettu lähettää palautetta. <PERSON><PERSON><PERSON><PERSON> tehdyt muutokset voivat johtaa tietojen menetykseen ja puutteellisiin tiedoihin viedyissä tiedostoissa.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "<PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Valitse äänestystapa ja pyydä käyttäjiä priorisoimaan muutaman eri vaiht<PERSON>don välillä.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "<PERSON><PERSON><PERSON>tys tai priorisointiharjoitus", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Luo projekti mallista", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Upota ulkoinen kysely", "app.containers.AdminPage.ProjectEdit.createInput": "Lisää uusi s<PERSON>te", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "<PERSON><PERSON> al<PERSON><PERSON> si<PERSON> kysely", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "<PERSON><PERSON> kysely poistum<PERSON>a alustalta.", "app.containers.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Määritä monivalintakysely.", "app.containers.AdminPage.ProjectEdit.createProject": "<PERSON><PERSON>i projekti", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Upota Typeform-, Google Form-, Enalyzer-, SurveyXact-, Qualtrics-, SmartSurvey-, Snap Survey- tai Microsoft Forms -kysely.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Voit mä<PERSON>rittää olet<PERSON>järjestyksen, jossa viestit näkyvät projektin pääsivulla.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Lajittelu", "app.containers.AdminPage.ProjectEdit.departments": "Osastot", "app.containers.AdminPage.ProjectEdit.descriptionTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "Tämä ottaa inhoamisen käyt<PERSON>öön tai poistaa sen käytöstä, mutta tykk<PERSON>n on silti käytössä. Suosittelemme jättämään tämän pois päältä, ellet suorita optioanalyysiä.", "app.containers.AdminPage.ProjectEdit.disabled": "<PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Ei-tykkäysten määrä osallistujaa kohti", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "<PERSON>ta inhoaminen käyttöön", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Kerää palautetta asiakirjasta", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Upota interaktiivinen PDF ja kerää kommentteja ja palautetta Konveion avulla.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "<PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Käytössä", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Projektiluonnokset piilotetaan kaikilta ihmisiltä paitsi järjestelmänvalvojilta ja määrätyiltä projektipäälliköiltä.", "app.containers.AdminPage.ProjectEdit.draft": "Luonnos", "app.containers.AdminPage.ProjectEdit.draftStatus": "Luonnos", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "Käytössä", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "<PERSON><PERSON><PERSON>, mitä osallistuvia toimia käyttäjät voivat tehdä.", "app.containers.AdminPage.ProjectEdit.enalyzer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.eventsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Liitteet (enintään 50 Mt)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Tiedostot eivät saa olla suurempia kuin 50 Mt. Lisätyt tiedostot näkyvät projektin tietosivulla.", "app.containers.AdminPage.ProjectEdit.filesTab": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Etsi vapaaehtoisia", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Pyydä osallistujia tekemään vapaaehtoistyötä toimintoihin ja syihin.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Kansioiden hallintaohjelman käyttäjä voi valita kansion projektin luomisen yhteydessä, mutta vain järjestelmänvalvoja voi muuttaa sitä jälkikäteen.", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Kansiokorttikuvan vaihtoehtoinen teksti", "app.containers.AdminPage.ProjectEdit.folderSelectError": "<PERSON><PERSON><PERSON> kans<PERSON>, johon haluat lisätä tämän projektin.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Mukautettu si<PERSON>ältö", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "<PERSON><PERSON><PERSON> loma<PERSON>elle lähe<PERSON>tyt tiedot ovat alkaneet sa<PERSON>ua. Lomakkeen muutokset voivat johtaa tietojen menetykseen ja epätäydellisiin tiedoihin viedyissä tiedostoissa.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "<PERSON><PERSON><PERSON> tall<PERSON>u onnist<PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Mallista", "app.containers.AdminPage.ProjectEdit.generalTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Otsikkokuvan vaihtoehtoinen teksti", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.ProjectEdit.information.new1": "UUSI", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "<PERSON> tietoja k<PERSON>yt<PERSON>äjille tai käytä raporttien rakennustyökalua tulosten jakamiseen menneistä vaiheista.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Jaa tietoa tai tuloksia", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Ke<PERSON><PERSON><PERSON> palautetta ja palautetta", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "Luo tai kerää syötteitä, reakti<PERSON><PERSON> ja/tai kommentteja. Valitse erityyppisten syötteiden välillä: ideoiden ker<PERSON>, v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kys<PERSON><PERSON> ja vastaus, ongelman tunnistaminen ja paljon muuta.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Kuka vastaa viestien käsittelystä?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "<PERSON><PERSON><PERSON> u<PERSON>t tiedot tässä projektissa määritetään tälle henkilölle. Valtuutettua voi vaihtaa kohdassa {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Kommentoi viestejä", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Syöttölomake", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "syötte<PERSON> hallinta", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Syöttöhallinta", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Lähetetään viestejä", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "<PERSON><PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Oletusnäkymä", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Valitse oletusnäkymä syötettä varten: kortit ruudukkonäkymässä tai nastat kartalla. Osallistujat voivat vaihtaa manuaalisesti kahden näkymän välillä.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "Inspiraati<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Upota Konveion URL-osoite", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "Tykkäyksiä per osallistuja", "app.containers.AdminPage.ProjectEdit.limited": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Lataa lisää malleja", "app.containers.AdminPage.ProjectEdit.mapDisplay": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "<PERSON><PERSON><PERSON>i ei-tykkäyksiä", "app.containers.AdminPage.ProjectEdit.maxLikes": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Valinnan äänten enimmäismäärän on oltava pienempi tai yhtä suuri kuin äänten kokonaismäärä", "app.containers.AdminPage.ProjectEdit.maximum": "Enimmäismäärä", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Osallistujat eivät voi ylittää tätä budjettia koriaan lähettäessään.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Vaadi osallistujia täyttämään vähimmäisbudjetti ostoskorinsa lähettämiseksi (kirjoita \"0\", jos et halua asettaa vähimmäismäärää).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "k<PERSON><PERSON> ohje<PERSON>ksessamme", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Ketkä ovat projektipäälliköt?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Lisätietoja", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Tarvitsetko inspiraatiota? Tutustu vastaaviin projekteihin muista kaupungeista {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "Lisää panos", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON><PERSON><PERSON> idea", "app.containers.AdminPage.ProjectEdit.newInitiative": "Lisää aloite", "app.containers.AdminPage.ProjectEdit.newIssue": "Lisää on<PERSON>", "app.containers.AdminPage.ProjectEdit.newOption": "Lisää vaiht<PERSON>hto", "app.containers.AdminPage.ProjectEdit.newPetition": "<PERSON><PERSON><PERSON><PERSON>us", "app.containers.AdminPage.ProjectEdit.newProject": "<PERSON><PERSON>i projekti", "app.containers.AdminPage.ProjectEdit.newProposal": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newQuestion": "Lisää kysymys", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON>i kelvollinen summa", "app.containers.AdminPage.ProjectEdit.noFolder": "<PERSON><PERSON> kans<PERSON>a", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "— <PERSON><PERSON> kans<PERSON> —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "Malleja ei lö<PERSON>", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "<PERSON> projektin nimi", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Ei kelvollinen numero", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Näkyy vain järjestelmänvalvojalle", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON>i", "app.containers.AdminPage.ProjectEdit.optionYes": "<PERSON><PERSON><PERSON><PERSON> (valitse kansio)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Osallistumistasot", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Mitä haluat tehdä?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Val<PERSON><PERSON>, miten käyttäjät voivat osallistua.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "<PERSON><PERSON> m<PERSON>, kuka voi suorittaa kunkin to<PERSON>, ja kys<PERSON><PERSON> osallistujille lisäkysymyksiä saadaksesi lisät<PERSON>oja.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Osallistujavaatimukset ja kysymykset", "app.containers.AdminPage.ProjectEdit.pendingReview": "Odottaa hyväksyntää", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Käyttöoikeudet", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Käyttöoikeudet", "app.containers.AdminPage.ProjectEdit.pollTab": "Poll", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "<PERSON><PERSON><PERSON> o<PERSON> re<PERSON>", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Projekt<PERSON><PERSON><PERSON> k<PERSON>va", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Tä<PERSON>ä kuva on osa projektikorttia; k<PERSON><PERSON>, joka tiivistää projektin ja näkyy esimerkiksi kotisivulla.\n\n    Lisätietoja suositelluista kuvaresoluutioista on {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "Kansio", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Tämä kuva näkyy projektisivun yläosassa.\n\n    Lisätietoja suositelluista kuvaresoluutioista on {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Projektikortin kuvan vaihtoehtoinen teksti", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "<PERSON> ly<PERSON>t kuvaus kuvasta näkövammaisille käyttäjille. Tämä auttaa n<PERSON>ytönluk<PERSON><PERSON><PERSON> kert<PERSON>, mistä ku<PERSON> on kyse.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Projektinhallinta", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projektipäälliköt voivat muokata projekteja, hallita viestejä ja lähettää sähköpostia osallistujille. Voit {moderationInfoCenterLink} löytää lisätietoja projektipäälliköille annetuista o<PERSON>uksista.", "app.containers.AdminPage.ProjectEdit.projectName": "<PERSON><PERSON><PERSON><PERSON> nimi", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Aikajanalla varustetuilla projekteilla on selkeä alku ja loppu, ja niissä voi olla eri vaiheita. Projektit ilman aikajanaa ovat jatkuvia.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Projektin tyyppiä ei voi muuttaa my<PERSON>.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Voit asettaa projektin näkymättömäksi tietyille käyttäjille.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Proje<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Etsitkö projektin tilaa? Nyt voit muuttaa sitä milloin tahansa suoraan projektisivun otsikosta.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Julkaistut projektit nä<PERSON>vät kaikille tai ryhmän al<PERSON>, jos ne on valittu.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Julkaistu", "app.containers.AdminPage.ProjectEdit.purposes": "Tarkoitukset", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "Tietojesi tallentamisessa tapahtui virhe. Yritä uudelleen.", "app.containers.AdminPage.ProjectEdit.saveProject": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.saveSuccess": "<PERSON>est<PERSON>!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Lo<PERSON>k<PERSON><PERSON> on tallennettu!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "<PERSON><PERSON><PERSON> malleja", "app.containers.AdminPage.ProjectEdit.selectGroups": "Valitse ryhmä(t)", "app.containers.AdminPage.ProjectEdit.setup": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON><PERSON> tie<PERSON>", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Määritä ja mukauta projektisi.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "viera<PERSON> tuki<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "Lisää kyselyn si<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Peruuta", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# valintoja} one {# valinta} other {# valintoja}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON><PERSON><PERSON>, haluan poistua", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> kysely<PERSON> on alkanut saapua palautetta. <PERSON><PERSON><PERSON><PERSON> tehdyt muutokset voivat johtaa tietojen katoamiseen ja epätäydellisiin tietoihin viedyissä tiedostoissa.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "Tiedoston lataus", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "Tekoälyyhteenvedot lyhyitä vastauksia, pitkiä vastauksia ja mielipideasteikon seurantakysymyksiä varten ovat käytettävissä vasemman sivupalkin AI-välilehdellä.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "<PERSON><PERSON><PERSON> asteikko", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrix", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Pitkä <PERSON>", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Monivalinta - vali<PERSON>e monta", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "<PERSON><PERSON> valinta - valitse useita", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Ei vielä vastauksia k<PERSON>elyyn", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.optional2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Jos logiikkaa ei lisätä, kysely noudattaa normaalia kulkuaan. Jos sekä sivulla että sen kysymyksissä on logiikkaa, kysymyslogiikka on etusijalla. Varmista, että tämä vastaa aiottua kyselyprosessia. Lisätietoja on osoitteessa {supportPageLink}", "app.containers.AdminPage.ProjectEdit.survey.point": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "<PERSON><PERSON><PERSON>, ett<PERSON> haluat poistua?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Nykyisiä muutoksiasi ei tallenneta.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.rating": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.required2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {vastauksia} one {vastaus} other {vastauksia}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# vastausta} one {# vastaus} other {# vastausta}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Monivalinta - valitse yksi", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Sentimentti lineaarinen asteikko", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "Esri-muo<PERSON><PERSON><PERSON><PERSON> lataus", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/en/articles/6673873-creating-an-in-platform-survey", "app.containers.AdminPage.ProjectEdit.survey.survey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.text2": "<PERSON>y<PERSON>t <PERSON>", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "<PERSON><PERSON><PERSON>sä {count} vastausta", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "Näytä", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Upota URL-osoite", "app.containers.AdminPage.ProjectEdit.surveyService": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Löydät lisätietoja kyselyn upottamisesta {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/en/articles/7025887-creating-an-external-survey-project", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "tässä", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Tätä tunnistetta ei voi poistaa, koska sitä käytetään projektien näyttämiseen seuraavilla muokatuilla sivuilla. \nSinun on poistettava tunniste sivulta tai poistettava sivu ennen kuin voit poistaa tunnisteen.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Y<PERSON>iset asetukset projektille", "app.containers.AdminPage.ProjectEdit.titleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "<PERSON><PERSON><PERSON>, j<PERSON> on ly<PERSON><PERSON>, muka<PERSON><PERSON><PERSON><PERSON><PERSON> ja selke<PERSON>. Se näkyy avattavassa yleiskatsauksessa ja kotisivun projektikorteissa.", "app.containers.AdminPage.ProjectEdit.topicLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "Valitse {topicsCopy} tälle projektille. Käyttäjät voivat käyttää näitä suodattaessaan projekteja.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Kokonais<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Trendaavat", "app.containers.AdminPage.ProjectEdit.typeform": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.unassigned": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.unlimited": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.url": "URL-osoite", "app.containers.AdminPage.ProjectEdit.useTemplate": "Käytä mallia", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "Näytä projekti", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Vapaaehtoistyö", "app.containers.AdminPage.ProjectEdit.voteTermError": "Äänestysehdot on määritettävä kaikille al<PERSON>ille", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# ryhmää voi katsella} one {# ryhmä voi katsella} other {# ryhmää voi katsella}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Lisä<PERSON> tap<PERSON>a", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Lisäinformaatio", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "osoite 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Tapahtumapaikan katuosoite", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Osoite 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "Esim. asunto, svi<PERSON>i, rakennus", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "<PERSON><PERSON>, jotka voivat au<PERSON>a tun<PERSON><PERSON><PERSON>, kuten r<PERSON><PERSON>, ker<PERSON><PERSON><PERSON> j<PERSON>.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/en/articles/5481527-adding-events-to-your-platform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonLink": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "Lisää linkki ulkoiseen URL-osoitteeseen (esim. tapahtumapalveluun tai lipunmyyntisivustoon). Tämän asettaminen ohittaa oletusarvoisen läsnäolopainikkeen toiminnan.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Mukautettu painik<PERSON>en teksti", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Aseta painikkeen tekstiksi jokin muu arvo kuin \"Rekisteröidy\", kun ulkoinen URL-osoite on asetettu.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "alkaa", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "Loppu", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Poistaa", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän tapah<PERSON>? Tätä ei voi kumota!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Tapa<PERSON><PERSON> kuvaus", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "<PERSON><PERSON><PERSON><PERSON> tap<PERSON>", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Jotta ylläpitäjät voivat lähettää sähköpostia rekisteröityneille suoraan alustalta, he<PERSON><PERSON><PERSON> on luotava käyttäjäryhmä {userTabLink} -välilehdellä. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Tapahtumapäivät", "app.containers.AdminPage.ProjectEvents.eventImage": "<PERSON><PERSON><PERSON><PERSON> kuva", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Tapahtumakuvan vaihtoehtoinen teksti", "app.containers.AdminPage.ProjectEvents.eventLocation": "<PERSON><PERSON><PERSON><PERSON><PERSON>ik<PERSON>", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Vientirekisteröijät", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Liitteet (enintään 50 Mt)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Liitteet näkyvät tapahtuman kuva<PERSON>sen alla.", "app.containers.AdminPage.ProjectEvents.locationLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Rekisteröityneiden enimmäismäärä", "app.containers.AdminPage.ProjectEvents.newEventTitle": "<PERSON><PERSON> uusi tap<PERSON>a", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Tapahtuman nettilinkki", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "<PERSON><PERSON> on verk<PERSON>, lis<PERSON><PERSON> linkki siihen tähän.", "app.containers.AdminPage.ProjectEvents.preview": "Esikatselu", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "<PERSON><PERSON><PERSON><PERSON> sija<PERSON>ia kartalla", "app.containers.AdminPage.ProjectEvents.refineOnMap": "<PERSON><PERSON><PERSON><PERSON> sija<PERSON>ia kartalla", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "<PERSON><PERSON>, <PERSON><PERSON>ik<PERSON>mer<PERSON><PERSON><PERSON><PERSON> n<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> alla olevaa karttaa.", "app.containers.AdminPage.ProjectEvents.register": "Rekisteröidy", "app.containers.AdminPage.ProjectEvents.registerButton": "Rekisteröintipainike", "app.containers.AdminPage.ProjectEvents.registrant": "rekisteröijä", "app.containers.AdminPage.ProjectEvents.registrants": "rekisteröityneet", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Rekisteröintiraja", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "<PERSON><PERSON> voineet tallentaa muuto<PERSON>, y<PERSON><PERSON> u<PERSON>.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "<PERSON>est<PERSON>!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "<PERSON><PERSON><PERSON> si<PERSON>", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Linkitä tulevat tapahtumat tähän projektiin ja näytä ne projektin tapahtumasivulla.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "<PERSON>tsikko ja päivämäärät", "app.containers.AdminPage.ProjectEvents.titleEvents": "Projektitapahtumat", "app.containers.AdminPage.ProjectEvents.titleLabel": "<PERSON><PERSON><PERSON><PERSON> nimi", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Linkitä painike ulkoiseen URL-osoitteeseen", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Oletusarvoisesti alustan sisäinen tapahtuman rekisteröintipainike näkyy, jotta käyttäjät voivat rekisteröityä tapahtumaan. Voit muuttaa tämän linkittämään ulkoiseen URL-osoitteeseen.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "<PERSON><PERSON><PERSON> il<PERSON>aut<PERSON>iden määrää", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "<PERSON><PERSON> osallistujien enimmäismäärä. <PERSON><PERSON>, uusia ilmoittautumisia ei oteta vastaan.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/käyttäjät", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Käyttäjät", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "Lisää tiedostoja projektiisi", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Liitä tiedostoja tästä luettelosta projektiisi, vaiheisiisi ja tapahtumiisi.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Lisää tiedostoja kontekstiksi Sensemakingiin", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "Lisää Sensemaking-projektiisi tiedostoja kontekstin ja oivallusten tarjoamiseksi.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "<PERSON><PERSON><PERSON> pian", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, la<PERSON>a haastatt<PERSON>ja ja anna te<PERSON>stää datasi pisteet.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "<PERSON><PERSON><PERSON> mik<PERSON> ta<PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Käytä tekoälyä tiedostojen analysointiin", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Prosessiselvitykset jne.", "app.containers.AdminPage.ProjectFiles.addFiles": "Lisää <PERSON>", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "Te<PERSON><PERSON><PERSON><PERSON><PERSON> tuote<PERSON>t tiedot", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analysoi ladattuja tiedostoja tärkeiden aiheiden esiin nostamiseksi.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "<PERSON><PERSON> n<PERSON>iden tiedostojen edistynyt analysointi tekoä<PERSON> a<PERSON>.", "app.containers.AdminPage.ProjectFiles.askButton": "Kysyä", "app.containers.AdminPage.ProjectFiles.categoryLabel": "Kategoria", "app.containers.AdminPage.ProjectFiles.chooseFiles": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.close": "Lähellä", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Vahvista ja lataa", "app.containers.AdminPage.ProjectFiles.confirmDelete": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän tied<PERSON>?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Markdown-tied<PERSON><PERSON> lataaminen epäonnistui.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "CSV-esikatselun lataaminen epäonnistui.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "CSV-esikatseluissa näytetään enintään 50 riviä.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "CSV-<PERSON><PERSON><PERSON> on liian suuri es<PERSON>.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "Poista tiedosto", "app.containers.AdminPage.ProjectFiles.description": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.done": "Tehty", "app.containers.AdminPage.ProjectFiles.downloadFile": "Lataa <PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Lataa koko <PERSON>o", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "<PERSON>ed<PERSON> ja pudota tiedostot tähän tai", "app.containers.AdminPage.ProjectFiles.editFile": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Tiedoston nimi ei saa sisältää pistettä.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Tiedoston nimi", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "Tied<PERSON><PERSON> nimi on pakollinen.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "Lataa <PERSON>", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Esikatselu", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Tätä tiedostoa ei ladata, koska se ylittää 50 Mt:n enimmäiskoon.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> ladattiin onnistuneesti", "app.containers.AdminPage.ProjectFiles.info_sheet": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "Esim. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Ä<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kaupungintalon tallenteet", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "Esim. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "<PERSON><PERSON><PERSON>, tiedotte<PERSON>", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "Esim. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.interview": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Voit ladata ker<PERSON><PERSON>an enint<PERSON>än {maxFiles} tiedost<PERSON>.", "app.containers.AdminPage.ProjectFiles.meeting": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.noFilesFound": "<PERSON><PERSON> tied<PERSON> lö<PERSON>yn<PERSON>.", "app.containers.AdminPage.ProjectFiles.other": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.policy": "Käytäntö", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Esikatselua ei vielä tueta tälle tiedostotyypille.", "app.containers.AdminPage.ProjectFiles.report": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.retryUpload": "Yrit<PERSON> latausta u<PERSON>en", "app.containers.AdminPage.ProjectFiles.save": "Tallentaa", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "<PERSON>ied<PERSON><PERSON> p<PERSON>tty onnistuneesti.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.selectFileType": "Tiedostotyyppi", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strateginen suunnitelma", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Voit ladata ker<PERSON><PERSON>an enint<PERSON>än {maxFiles} tiedost<PERSON>.", "app.containers.AdminPage.ProjectFiles.unknown": "Tuntematon", "app.containers.AdminPage.ProjectFiles.upload": "Lataa", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# tiedosto} other {# tiedostot}} ladattu onnist<PERSON>esti, {numberOfErrors, plural, one {# virhe} other {# virheitä}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "Näytä <PERSON>", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "<PERSON><PERSON> ka<PERSON> kent<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Muokkaa syöttölomaketta", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Käytössä", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Sisällytä tämä kenttä.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Jokin meni pieleen. Ole hyvä ja kokeile my<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "<PERSON><PERSON><PERSON>na kaikki kent<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Syöttölomake", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mitä tietoja on annett<PERSON>, lisää lyhyitä kuvauksia tai ohjeita oh<PERSON>an osallistujien vastauksia ja määritä, onko jokainen kenttä valinnainen vai pakollinen.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mitä tietoja on annett<PERSON>, lisää lyhyitä kuvauksia tai ohjeita oh<PERSON>an osallistujien vastauksia ja määritä, onko jokainen kenttä valinnainen vai pakollinen", "app.containers.AdminPage.ProjectIdeaForm.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Vaadi tämän kentän täyttäminen.", "app.containers.AdminPage.ProjectIdeaForm.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Tallennettu!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "Näytä lo<PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automaattiset sähköpostit", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Voit määrittää vaihetasolla käynnistyvät sähköpostit", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Päivämäärät", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Vastaa k<PERSON>elyyn", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän vaiheen?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.ProjectTimeline.disabledProjectPhaseEmailMessage": "<PERSON>äm<PERSON> vaihtoehto on tällä hetkellä pois päältä kaikista {automatedEmailsLink} -sivun projekteista. Tämän seurauksena et voi vaihtaa tätä asetusta erikseen tälle vaiheelle.", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "<PERSON><PERSON><PERSON><PERSON> vai<PERSON>tta", "app.containers.AdminPage.ProjectTimeline.endDate": "Päättymispäivä", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Päättymispäivä", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Liitteet (enintään 50 Mt)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "<PERSON><PERSON><PERSON> vaihe", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "<PERSON><PERSON><PERSON><PERSON> vaiheella ei ole ennalta määritettyä päättymispäivää.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Joidenkin menetelmien tulosten jakaminen (kuten äänestystulokset) ei käynnisty ennen kuin lopetuspäivä on valittu.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "<PERSON><PERSON> kun lisäät vaiheen tämän jälkeen, se lisää lopetuspäivän tähän vaiheeseen.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "Lopetuspäivämäärän valitsematta jättäminen tarkoittaa seuraavaa:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Esikatselu", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "Lomakkeen lähettämisessä tapahtui virhe. Yritä uudelleen.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Tallennettu!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.ProjectTimeline.startDate": "Aloituspäivämäärä", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Aloituspäivämäärä", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.titleLabel": "<PERSON><PERSON><PERSON><PERSON> nimi", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Lataa liitteet", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_<PERSON><PERSON><PERSON><PERSON>sio", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologia (etusivun suodatin)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Miten etusivun suodattimen tageja pitäisi kutsua? <PERSON><PERSON><PERSON> tun<PERSON>, luo<PERSON>, o<PERSON><PERSON><PERSON>, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "<PERSON><PERSON><PERSON><PERSON><PERSON> voidaan mä<PERSON>rittää {topicManagerLink}.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "tässä", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "<PERSON><PERSON><PERSON><PERSON> tun<PERSON> (yksikkö)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "tag", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Termi useille tunnisteille (monikko)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "tun<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Lisää kenttä", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Lisää uusi rekisteröintikenttä", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Lisää vaiht<PERSON>hto", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> muoto", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "<PERSON> muoto", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Vastausva<PERSON><PERSON>ehto", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> kiel<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Vastausvaihtoehto tallennettu onnistuneesti", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Vastausvaihtoehdot", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Kentät", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Vedä ja pudota kentät määrittääks<PERSON> jä<PERSON>, jossa ne näkyvät kirjautumislomakkeessa.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "Oletuskenttä", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Poistaa", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "<PERSON><PERSON><PERSON><PERSON>, joka näkyy ilmoittautumislomakkeen kentän nimen alla.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "<PERSON>uin<PERSON><PERSON>n vastausva<PERSON>ehdot voidaan asettaa {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "<PERSON><PERSON><PERSON> nimi", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "<PERSON> k<PERSON>n nimi kaikille kiel<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Kenttäasetukset", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON><PERSON>ä-ei (valintaruutu)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Päivämäärä", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Pitkä <PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "<PERSON><PERSON><PERSON><PERSON> (valitse useita)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numeerinen arvo", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Monivalinta (valitse yksi)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "<PERSON>y<PERSON>t <PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Maantieteelliset al<PERSON> -välilehti", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Piilotettu kenttä", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Onko tähän kenttään vastaaminen pakollinen?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Mukautetut kentät", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Lisää <PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Peruuta", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Poistaa", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän rekisteröintikysymyksen vastausvaihtoehdon? <PERSON><PERSON><PERSON> tie<PERSON>, joihin tietyt käyttäjät vastasivat tällä vai<PERSON>, poiste<PERSON><PERSON> pysyvästi. Tätä toimintoa ei voi kumota.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän rekisteröintikysymyksen? Kaikki käyttäjien tähän kysymykseen antamat vastaukset poistetaan pysyvästi, eikä niitä enää kysytä projekteissa tai ehdotuksissa. Tätä toimintoa ei voi kumota.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON><PERSON> kent<PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "Kenttä <PERSON>ttu onnistuneesti", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.addAreaButton": "Lisää maantieteellinen alue", "app.containers.AdminPage.SettingsPage.addTopicButton": "Lis<PERSON><PERSON> merkin<PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Eläin - esim. Elefanttikissa", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "Käyttäjä - esim. käyttäjä 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "Valits<PERSON>, ketkä järjestelmänvalvojat saavat ilmoituksia projektien hyväksymisestä. Kansionhallintaohjelmat ovat oletusarvoisesti kaikkien kansioidensa projektien hyväksyjiä.", "app.containers.AdminPage.SettingsPage.approvalSave": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.approvalTitle": "Projektin hyväksym<PERSON>set", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän al<PERSON>en?", "app.containers.AdminPage.SettingsPage.areaTerm": "<PERSON><PERSON><PERSON><PERSON> (yksikkö)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "al<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Poistaa", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Termi useille alueille (monikko)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "al<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "Valitse vähintään yksi kieli.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatarit", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Näytä avatarit", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Näytä osallistujien profiilikuvat ja niiden lukumäärä rekisteröimättömille vierailijoille", "app.containers.AdminPage.SettingsPage.bannerHeader": "Otsikkoteksti", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Otsikkoteksti rekisteröimättömille vierailijoille", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Alaotsikon teksti rekisteröimättömille vierailijoille", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Alaotsikon teksti", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Näytä esikatselu kohteelle", "app.containers.AdminPage.SettingsPage.brandingDescription": "Lisää logosi ja aseta alustan värit.", "app.containers.AdminPage.SettingsPage.brandingTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.cancel": "Peruuta", "app.containers.AdminPage.SettingsPage.chooseLayout": "Layout", "app.containers.AdminPage.SettingsPage.color_primary": "Pääväri", "app.containers.AdminPage.SettingsPage.color_secondary": "<PERSON><PERSON><PERSON>inen väri", "app.containers.AdminPage.SettingsPage.color_text": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.colorsTitle": "v<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän tunnisteen?", "app.containers.AdminPage.SettingsPage.contentModeration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.ctaHeader": "Painikkeet", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Muokattu sivun ots<PERSON> | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "Muka<PERSON>ttu", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Painikkeen te<PERSON>ti", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.defaultTopic": "Oletustunniste", "app.containers.AdminPage.SettingsPage.delete": "Poistaa", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Poistaa", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "Tämä poistaa tunnisteen kaikista olemassa olevista viesteistä. Tämä muutos koskee kaikkia projekteja.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Lisää ja poista tunnisteita, joita haluat käyttää alustallasi viestien luokitteluun. Voit lisätä tunnisteet tiettyihin projekteihin kohdassa {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Työpöytä", "app.containers.AdminPage.SettingsPage.editFormTitle": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "<PERSON><PERSON><PERSON><PERSON> tunnist<PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescription": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "<PERSON>ämä kuvaus on tarkoitettu vain sisäiseen yhteistyöhön, eikä sitä näytetä käyttäjille.", "app.containers.AdminPage.SettingsPage.fieldTitle": "<PERSON><PERSON><PERSON> nimi", "app.containers.AdminPage.SettingsPage.fieldTitleError": "<PERSON> nimi kaikille kiel<PERSON>", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "<PERSON><PERSON><PERSON> al<PERSON> valitsemaasi nimeä voidaan käyttää rekisteröintikentän vaihtoehtona ja suodattaa projekteja kotisivulla.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "<PERSON><PERSON><PERSON> tun<PERSON>", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "<PERSON><PERSON> nimi", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "<PERSON> tunnisteen nimi kaikille kiel<PERSON>", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "<PERSON><PERSON><PERSON> tun<PERSON><PERSON>le valitsemasi nimi näkyy alustan k<PERSON>ä<PERSON>", "app.containers.AdminPage.SettingsPage.fixedRatio": "Kiinteä suhde", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Tämä bannerityyppi toimii parhaiten kuvien kanssa, joita ei pidä rajata, kuten kuvissa, joissa on tekstiä, logo tai tiettyjä elementtejä, jotka ovat tärkeitä kansalaisille. Tämä banneri korvataan yhtenäisellä laatikolla, jonka pääväri on, kun käyttäjät ovat kirjautuneet sisään. Voit määrittää tämän värin yleisissä asetuksissa. Lisätietoja suositellusta kuvien käytöstä löytyy osoitteesta {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "tietopohja", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "<PERSON><PERSON><PERSON><PERSON> leveys", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Tämä banneri ulottuu koko leveydelle upean visuaalisen vaikutelman saavuttamiseksi. Kuva yrittää peittää mahdollisimman paljon tilaa, jolloin se ei aina ole aina näkyvissä. Voit yhdistää tämän bannerin minkä tahansa väriseen peittoon. Lisätietoja suositellusta kuvien käytöstä löytyy osoitteesta {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "tietopohja", "app.containers.AdminPage.SettingsPage.header": "<PERSON><PERSON><PERSON><PERSON> banneri", "app.containers.AdminPage.SettingsPage.headerDescription": "Mukauta etusivun bannerin kuvaa ja tekstiä.", "app.containers.AdminPage.SettingsPage.header_bg": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.helmetDescription": "Järjestelmänval<PERSON>jan as<PERSON>sivu", "app.containers.AdminPage.SettingsPage.helmetTitle": "Järjestelmänval<PERSON>jan as<PERSON>sivu", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Lisää oma sisältösi muokattavaan osioon etusivun al<PERSON>.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Peittokuvan <PERSON>", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Tunnista sopimaton sisältö", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Tunnista alustalle lähetetty sopimaton sisältö automaattisesti.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Kun tämä ominaisuus on k<PERSON><PERSON><PERSON>ssä, osallistujien lähettämät syöttötiedot, ehdotukset ja kommentit tarkistetaan automaattisesti. Mahdollisesti sopimatonta sisältöä sisältäväksi merkittyjä viestejä ei estetä, mutta ne korostetaan {linkToActivityPage} -sivulla tarkistettavaksi.", "app.containers.AdminPage.SettingsPage.languages": "Kieli (kielet", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Voit valita useita kieli<PERSON>, j<PERSON><PERSON> haluat tarjota alustasi k<PERSON>ä<PERSON>. <PERSON>un on luotava sisältöä jokaiselle valitulle kielelle.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Lähetä otsikkokuva", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.organizationName": "Kaupungin tai organisaation nimi", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Anna organisaation nimi tai kaupunki kaikille kiel<PERSON>.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "<PERSON><PERSON> p<PERSON> k<PERSON>töö<PERSON>", "app.containers.AdminPage.SettingsPage.phone": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.platformConfiguration": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.population": "Väestö", "app.containers.AdminPage.SettingsPage.populationMinError": "V<PERSON><PERSON><PERSON><PERSON>n on oltava positiivinen luku.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Alueesi asukkaiden kokonaismäärä. Tätä käytetään osallistumisasteen laskemiseen. Jätä ty<PERSON>ks<PERSON>, jos se ei sovellu.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "<PERSON><PERSON><PERSON><PERSON> esto", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Estä <PERSON>, ehdotukset ja kommentit, jotka sisältävät yleisimmin raportoituja loukkaavia sanoja", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Tämä teksti näkyy kotisivulla hankkeiden yläpuolella.", "app.containers.AdminPage.SettingsPage.projectsSettings": "projekt<PERSON>", "app.containers.AdminPage.SettingsPage.projects_header": "Projektien otsikko", "app.containers.AdminPage.SettingsPage.registrationFields": "Rekisteröintikentät", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "<PERSON> l<PERSON>t kuvaus ilmoittautumislomakkeesi ylä<PERSON>.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Rekisteröinti", "app.containers.AdminPage.SettingsPage.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveArea": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Jokin meni pieleen. Ole hyvä ja koke<PERSON> my<PERSON>.", "app.containers.AdminPage.SettingsPage.saveSuccess": "<PERSON>est<PERSON>!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Valitse hyväksyjät", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "<PERSON><PERSON><PERSON>, jotka näytetään käyttäjille rekisteröitymisen jälkeen", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "<PERSON><PERSON><PERSON> a<PERSON>, jotka n<PERSON>ytetään käyttäjille rekisteröitymisen jälkeen", "app.containers.AdminPage.SettingsPage.settingsSavingError": "<PERSON>i voitu tallentaa. Yritä muuttaa as<PERSON>ta u<PERSON>.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Kirjaudu\"", "app.containers.AdminPage.SettingsPage.signed_in": "Painike rekisteröityneille vierailijoille", "app.containers.AdminPage.SettingsPage.signed_out": "Painike re<PERSON>teröimättömille vierailijoille", "app.containers.AdminPage.SettingsPage.signupFormText": "Re<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Lisää lyhyt kuvaus ilmoittautumislomakkeen yläosaan.", "app.containers.AdminPage.SettingsPage.statuses": "Tilat", "app.containers.AdminPage.SettingsPage.step1": "Sähköposti ja salasana vaihe", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Tämä näkyy ilmoittautumislomakkeen ensimmäisen sivun yläosassa (nimi, sähköpostiosoite, salasana).", "app.containers.AdminPage.SettingsPage.step2": "Rekisteröintikysymykset vaihe", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Tämä näkyy rekisteröitymislomakkeen toisen sivun yläosassa (lisärekisteröintikentät).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Määritä maantieteelliset alueet, joit<PERSON> haluat käyttää alustassasi, kuten kaupunginosat, kaupunginosat tai piirit. Voit liittää nämä maantieteelliset alueet projekteihin (suodatettavissa aloitussivulla) tai pyytää osallistujia valitsemaan asuinalueensa rekisteröintikenttään luodaksesi älykkäitä ryhmiä ja määrittääksesi käyttöoikeudet.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "<PERSON><PERSON><PERSON>, miten ihmiset näkevät organisaatiosi nimen, valitse alustasi kielet ja linkki verkkosivustollesi.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Annettu tekstitys ylittää suurimman sallitun merk<PERSON><PERSON><PERSON> (90 merkkiä)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mitä tietoja ihmisiltä pyydetään ilmoittautumisen yhteydessä.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologia", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Asetukset päivitetty onnistuneesti.", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Syöttötilat", "app.containers.AdminPage.SettingsPage.tabPolicies": "Käytännöt", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Hankkeen hyväksyntä", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Ehdotuksen tilat", "app.containers.AdminPage.SettingsPage.tabRegistration": "Rekisteröinti", "app.containers.AdminPage.SettingsPage.tabSettings": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabTopics2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mitä maantieteellistä yksikköä haluat käyttää projekteissasi (esim. kaupunginosat, piirit, kaupunginosat jne.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Ma<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.titleBasic": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "<PERSON><PERSON><PERSON> ots<PERSON>ko y<PERSON> suurimman sallitun merk<PERSON><PERSON><PERSON> (35 merkkiä)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tag Manager", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Tämä banneri on erityisen hyödyllinen kuvissa, jotka eivät toimi hyvin otsikon, alaotsikon tai painikkeen tekstin kanssa. Nämä kohteet työnnetään bannerin alle. Lisätietoja suositellusta kuvien käytöstä löytyy osoitteesta {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "tietopohja", "app.containers.AdminPage.SettingsPage.twoRowLayout": "<PERSON><PERSON><PERSON> riviä", "app.containers.AdminPage.SettingsPage.urlError": "URL-osoite ei kelpaa", "app.containers.AdminPage.SettingsPage.urlPatternError": "<PERSON>en URL-osoite.", "app.containers.AdminPage.SettingsPage.urlTitle": "Verkkosivusto", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "Voit lisätä linkin omalle verkkosivustollesi. Tätä linkkiä käytetään etusivun alareunassa.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "<PERSON><PERSON><PERSON>, kuin<PERSON> k<PERSON>, j<PERSON><PERSON> ei ole nime<PERSON> profi<PERSON>, näkyvät alustalla. <PERSON><PERSON><PERSON><PERSON> tap<PERSON>, kun määrität vaiheen käyttöoikeuksiksi \"Sähköpostivahvistus\". Kaikissa tapauksissa osallistuessaan käyttäjät voivat päivittää heille automaattisesti luomamme profiilinimen.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Käyttäjänimen nä<PERSON>ö (vain käyttä<PERSON><PERSON>, joiden sähköpostiosoite on vahvistettu)", "app.containers.AdminPage.SideBar.administrator": "Järjestelmänvalvoja", "app.containers.AdminPage.SideBar.communityPlatform": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>a", "app.containers.AdminPage.SideBar.community_monitor": "yhteisön valvoja", "app.containers.AdminPage.SideBar.customerPortal": "Asiakasportaali", "app.containers.AdminPage.SideBar.dashboard": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.emails": "Sähköpostit", "app.containers.AdminPage.SideBar.folderManager": "Kansionhallinta", "app.containers.AdminPage.SideBar.groups": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.guide": "Opas", "app.containers.AdminPage.SideBar.inputManager": "Syöttöhallinta", "app.containers.AdminPage.SideBar.insights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inspirationHub": "Inspiraati<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.knowledgeBase": "Tietopohja", "app.containers.AdminPage.SideBar.language": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com", "app.containers.AdminPage.SideBar.menu": "Sivut ja valikko", "app.containers.AdminPage.SideBar.messaging": "Viestit", "app.containers.AdminPage.SideBar.moderation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.notifications": "Ilmoitukset", "app.containers.AdminPage.SideBar.processing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.projectManager": "Projektipäällikkö", "app.containers.AdminPage.SideBar.projects": "Projektit", "app.containers.AdminPage.SideBar.settings": "asetukset", "app.containers.AdminPage.SideBar.signOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.support": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.toPlatform": "Alustalle", "app.containers.AdminPage.SideBar.tools": "Työkalut", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.users": "Käyttäjät", "app.containers.AdminPage.SideBar.workshops": "Työpajat", "app.containers.AdminPage.Topics.addTopics": "Lisätä", "app.containers.AdminPage.Topics.browseTopics": "<PERSON><PERSON><PERSON> tun<PERSON>", "app.containers.AdminPage.Topics.cancel": "Peruuta", "app.containers.AdminPage.Topics.confirmHeader": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän projektitunnisteen?", "app.containers.AdminPage.Topics.delete": "Poistaa", "app.containers.AdminPage.Topics.deleteTopicLabel": "Poistaa", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Tätä tunnistetta ei voi enää lisätä tämän projektin uusiin viesteihin.", "app.containers.AdminPage.Topics.inputForm": "Syöttölomake", "app.containers.AdminPage.Topics.lastTopicWarning": "Ainakin yksi tunniste on pakollinen. <PERSON><PERSON> et halua käyttää tunnisteita, voit poistaa ne käytöstä {ideaFormLink} -välilehdellä.", "app.containers.AdminPage.Topics.projectTopicsDescription": "Voit lisätä ja poistaa tunnisteita, jotka voidaan liittää tämän projektin viestei<PERSON>.", "app.containers.AdminPage.Topics.remove": "Poista", "app.containers.AdminPage.Topics.title": "<PERSON><PERSON><PERSON><PERSON> tun<PERSON>", "app.containers.AdminPage.Topics.topicManager": "Tag Manager", "app.containers.AdminPage.Topics.topicManagerInfo": "<PERSON><PERSON> ha<PERSON>at lisätä projektitunnisteita, voit tehdä sen kohdassa {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Lisää uusi ryhmä", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "<PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "<PERSON><PERSON> r<PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Millaista ryhmää tarvitset?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/en/articles/7043801-using-smart-and-manual-user-groups", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "<PERSON><PERSON> r<PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "<PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Lisätietoja ryhmistä", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Voit valita käyttäjät yleiskatsauksesta ja lisätä heidät tähän ryhmään.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Voit mä<PERSON>rittää ehtoja, ja ehdot täyttävät käyttäjät lisätään automaattisesti tähän ryhmään.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Tässä ryhmässä ei ole vielä ketään", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "<PERSON><PERSON><PERSON> k<PERSON> {allUsersLink} lisätäks<PERSON> joitakin käyttäjiä manuaalisesti.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Yksikään kä<PERSON>täjä ei vastaa hakuasi", "app.containers.AdminPage.Users.GroupsPanel.select": "Valitse", "app.containers.AdminPage.Users.UsersGroup.exportAll": "<PERSON><PERSON> kaikki", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Vie käyttäjät ryhmään", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Vienti valittu", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän ryhmän?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "Käyttäjien lisäämisessä ryhmiin tapahtui virhe. Yritä uudelleen.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Poista ryhmästä", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Poistetaanko valitut käyttäjät tästä ryhmästä?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "Käyttäjien poistamisessa ryhmästä tapahtui virhe. Yritä uudelleen.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Lisää kä<PERSON>täjiä r<PERSON>hmään", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Lisätä", "app.containers.AdminPage.groups.permissions.add": "Lisätä", "app.containers.AdminPage.groups.permissions.addAnswer": "Lisää <PERSON>", "app.containers.AdminPage.groups.permissions.addQuestion": "Lisää demografisia kysymyksiä", "app.containers.AdminPage.groups.permissions.answerChoices": "Vastausvaihtoehdot", "app.containers.AdminPage.groups.permissions.answerFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> muoto", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Vähintään yksi v<PERSON> on annettava", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Tämä käyttäjä valvoo tämän projektin sisältävää kansiota. <PERSON><PERSON> haluat poistaa heidän moderaattorin oikeutensa tälle projektille, voit joko peruuttaa heidän kansiooikeudet tai siirtää projektin toiseen kansioon.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "<PERSON><PERSON> uusi kysymys", "app.containers.AdminPage.groups.permissions.createAQuestion": "<PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.defaultField": "Oletuskenttä", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Poistaa", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Poistaa", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "<PERSON> ka<PERSON> v<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON><PERSON>ä-ei (valintaruutu)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Päivämäärä", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Pitkä <PERSON>", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "<PERSON><PERSON><PERSON><PERSON> (valitse useita)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Numeerinen arvo", "app.containers.AdminPage.groups.permissions.fieldType_select": "Monivalinta (valitse yksi)", "app.containers.AdminPage.groups.permissions.fieldType_text": "<PERSON>y<PERSON>t <PERSON>", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Yksityiskohtaisten käyttöoikeuksien muuttaminen ei ole osa käyttöoikeuttasi. Ota yhteyttä GovSuccess Manageriin saadaksesi lisätietoja.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän ryhmän projektista?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Valitse yksi tai useampi r<PERSON>hm<PERSON>", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {<PERSON>i j<PERSON>} one {1 jäsen} other {{count} jäsent<PERSON>}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Täytä otsikko kaikilla kielillä", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "<PERSON><PERSON><PERSON>?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Projektipäälliköitä ei löydy", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Mitään ei näytetä, koska käyttäjä ei voi tehdä toimintoja tässä projektissa.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "Vain jär<PERSON>stelmänvalvojat voivat luoda uuden kysymyksen.", "app.containers.AdminPage.groups.permissions.option1": "Vaihtoehto 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Odottaa kutsua", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Kuka voi merkitä asiakirja<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Kuka voi ilmoittautua tapah<PERSON>?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Kuka voi kommentoida s<PERSON>ötteitä?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Kuka voi kommentoida ehdotuksia?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Kuka voi lähettää ehdotuksen?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Kuka voi reagoida s<PERSON>ötteisiin?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Kuka voi lähettää palautetta?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Kuka voi osallistua k<PERSON>elyyn?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Kuka voi osallistua k<PERSON>elyyn?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Kuka voi tehdä vapaaehtoistyötä?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Kuka voi äänestää ehdotuksista?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Kuka voi äänestää?", "app.containers.AdminPage.groups.permissions.questionDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.groups.permissions.questionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>ik<PERSON>", "app.containers.AdminPage.groups.permissions.save": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Jokin meni pieleen. Ole hyvä ja koke<PERSON> my<PERSON>.", "app.containers.AdminPage.groups.permissions.saveSuccess": "<PERSON>est<PERSON>!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> on tallennettu.", "app.containers.AdminPage.groups.permissions.select": "Valitse", "app.containers.AdminPage.groups.permissions.selectValueError": "Valitse vastaustyyppi", "app.containers.AdminPage.new.createAProject": "<PERSON><PERSON> projekti", "app.containers.AdminPage.new.fromScratch": "Tyhjästä", "app.containers.AdminPage.phase.methodPicker.addOn1": "Lisää", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "Tekoälypohjaisia oivalluksia", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "Auta osallistujia nostamaan esiin sekä yhteisymmärrystä että erimielisyyttä, yksi a<PERSON>.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "Löydä yhteinen sävel", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Upota interaktiivinen PDF ja kerää kommentteja ja palautetta Konveion avulla.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Kerää palautetta asiakirjasta", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "Upota kolmannen osapuolen kysely", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "<PERSON><PERSON><PERSON><PERSON> kysely", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Hyödynnä käyttäjiesi kollektiivista älykkyyttä. Kutsu heitä esittämään, keskustelemaan ideoista ja/tai antamaan palautetta julkisella foorumilla.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Kerää palautetta ja palautetta julkisesti", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "<PERSON><PERSON><PERSON> tie<PERSON>", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "<PERSON><PERSON><PERSON> puuttuu alustan sisäinen AI-käyttöinen oivallus", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "<PERSON><PERSON><PERSON><PERSON><PERSON> alustan sisäinen raportointi ja tietojen visualisointi ja käsittely", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "<PERSON><PERSON> alustan sisäiseen raporttien rakennustyökal<PERSON>un", "app.containers.AdminPage.phase.methodPicker.logic1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Laaja valikoima k<PERSON>ymystyyppejä", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "<PERSON><PERSON>en ladata ideoita aika- ja <PERSON><PERSON><PERSON><PERSON>.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tai aloit<PERSON>t", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Pikakysely", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "Laadi lyhyt mon<PERSON>ely.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "<PERSON><PERSON><PERSON><PERSON> tie<PERSON>, visualisoi muiden vaiheiden tuloksia ja luo runsaasti dataa sisältäviä raportteja.", "app.containers.AdminPage.phase.methodPicker.survey1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Ymmärrä käyttäjien tarpeita ja ajattelua useiden yksityisten kysymystyyppien avulla.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Kyselyvaihtoehdot", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "<PERSON><PERSON> k<PERSON>", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Pyydä k<PERSON>äjiä vapaaehtoiseksi toimiin ja syihin tai etsi osallistujia paneeliin.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>listujia tai vapaaehtoisia", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Valitse äänestystapa ja pyydä käyttäjiä priorisoimaan muutaman eri vaiht<PERSON>don välillä.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "<PERSON><PERSON><PERSON>tys tai priorisointiharjoitus", "app.containers.AdminPage.projects.all.all": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.all.createProjectFolder": "<PERSON><PERSON><PERSON> kansio", "app.containers.AdminPage.projects.all.existingProjects": "Olemassa olevat projektit", "app.containers.AdminPage.projects.all.homepageWarning": "Mukauta kotisivusi näyttöä: järjestelmänvalvojat voivat järjestää projekteja ja kansioita täällä asettaakseen järjestyksen etusivulle.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "<PERSON><PERSON><PERSON><PERSON>, joissa olet proje<PERSON>ällikkö, näkyvät täällä.", "app.containers.AdminPage.projects.all.noProjects": "Projekteja ei löytynyt.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Vain j<PERSON>r<PERSON>stelmänvalvojat voivat luoda projektikansioita.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projektit ja kansiot", "app.containers.AdminPage.projects.all.publishedTab": "Julkaistu", "app.containers.AdminPage.projects.all.searchProjects": "Etsi projekt<PERSON>a", "app.containers.AdminPage.projects.all.yourProjects": "Sinun projektisi", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "AI-analyysi", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Tarkkuus: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Kysyä", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Yhteenvedon tekemisen sijaan voit kysyä tietojasi koskevia olennaisia kysymyksiä. Tämä ominaisuus ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Tämä oivallus sisältää seuraavat kysymykset:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Poista k<PERSON>ymys", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän k<PERSON><PERSON>?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Poista yhteenveto", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa nämä yhteen<PERSON>ot?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Tekstiyhteenvedot näkyvät täällä, mutta sinulla ei ole vielä sellaisia.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "<PERSON><PERSON><PERSON> yllä olevaa Automaattinen yhteenveto -painiketta.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "tulot valittu", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "Kysymysten esittäminen pienemmistä syötteistä johtaa suurempaan tarkkuuteen. Pienennä nykyistä syötteen valintaa käyttämällä tunnisteita, hakua tai demografisia suodattimia.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Kysymyksi<PERSON> varten", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "<PERSON><PERSON><PERSON><PERSON> tämän hava<PERSON>on laatu", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Yhteenveto", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Yhteenveto kaikista syötteistä", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "Te<PERSON>äly ei pysty käsittelemään niin montaa s<PERSON>öttöä kerralla. Jaa ne pienempiin ryhmiin.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Voit tehdä yhteenvedon enintään 30 syötteestä kerralla nykyiseen suunnitelmaasi. Keskustele GovSuccess Managerin tai järjestelmänvalvojan kanssa avataksesi lisää.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "<PERSON>ust<PERSON><PERSON> voit tutkia yd<PERSON>, tehd<PERSON> yhteenvedon tiedoista ja tarkastella erilaisia näkökulmia. <PERSON><PERSON> et<PERSON>t tarkkoja vastauksia tai oivalluk<PERSON>, hark<PERSON><PERSON> \"Kysy kysymys\" -ominaisuuden käyttämistä sukeltaaksesi syvemmälle yhteenvedon ulkopuolelle.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "<PERSON><PERSON><PERSON> te<PERSON> on ha<PERSON><PERSON><PERSON>, se saattaa toisinaan tuottaa tietoa, jota ei nimenomaisesti ollut alkuperäisessä tietojoukossa.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Hallusinaatiot:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "Tekoäly saattaa korostaa tiettyjä teemoja tai ideoita enemmän kuin toisia, mikä saattaa vääristää yleistä tulkin<PERSON>a.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "<PERSON><PERSON><PERSON><PERSON>:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Järjestelmämme on optimoitu käsittelemään 20-200 tarkasti määriteltyä syöttöä tarkimpien tulosten saamiseksi. Kun tiedon määrä kasvaa tämän alueen yli, yhteenveto voi tulla korkeatasoisempaa ja yleistyneempää. Tämä ei tarkoita, että tekoäly muuttuu \"vähemmän tarkaksi\", vaan pikemminkin sitä, että se keskittyy laajempiin trendeihin ja malleihin. Jo<PERSON> haluat tarkempia tietoja, suosittelemme käyttämään (automaattista) taggausominaisuutta suurempien tietojoukkojen segmentointiin pienempiin, paremmin hallittaviin osajoukkoon.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Tietojen määrä ja tark<PERSON>:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "Suosittelemme k<PERSON>tämään tekoälyn luomia yhteenvetoja suurten tietojoukkojen ymmärtämisen lähtökohtana, mutta ei viimeisenä sanana.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Kuinka työskennellä AI: n kanssa", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Lisää valitut syötteet tunnisteeseen", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Lis<PERSON><PERSON> merkin<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Tämä ominaisuus ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "<PERSON><PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "<PERSON><PERSON><PERSON> t<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "<PERSON><PERSON><PERSON> tun<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, teen sen", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Haluatko määrittää syötteet tunnisteeseesi automaattisesti?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "On o<PERSON><PERSON><PERSON> <b>eri <PERSON></b> syötteiden automaattiseen määrittämiseen tunnisteisiin.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "K<PERSON>ytä <b>automaattista tagipainiketta</b> k<PERSON><PERSON>istääks<PERSON> halu<PERSON> menetelmän.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "Na<PERSON>auta tunnist<PERSON> lii<PERSON>ääksesi sen valitulle s<PERSON>tteelle.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "<PERSON><PERSON><PERSON><PERSON>, automaattinen merkintä", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automaattinen tagi", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Tietokone johtaa automaattisesti automaattiset tunnisteet. Voit muuttaa tai poistaa niitä milloin ta<PERSON>.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automaattinen tagi", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "<PERSON><PERSON><PERSON>in tunnist<PERSON>in jo liitettyjä s<PERSON>tteitä ei luokitella uudelleen.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Luokittelu perustuu yks<PERSON>maan tunnisteen nimeen. Valitse osuvat avainsanat saadaksesi parhaat tulokset.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tunnisteet: <PERSON><PERSON><PERSON><PERSON> mukaan", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "<PERSON>ot tunnisteet ja määrität manuaalisesti muutamia syötteitä esimerkkinä, tietokone määrittää loput", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tunnisteet: <PERSON><PERSON><PERSON><PERSON>ä", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "<PERSON><PERSON><PERSON><PERSON> kuin \"Tagit: etike<PERSON> mukaan\", mutta ta<PERSON>, kun koulutat järjestelmää hyvillä esimerkeillä.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Sin<PERSON> luot tunnist<PERSON>t, tietokone määrittää syötteet", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tunnisteet: <PERSON><PERSON><PERSON><PERSON> mukaan", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "<PERSON><PERSON><PERSON><PERSON> to<PERSON><PERSON> hy<PERSON>, kun sinulla on ennalta määritetty joukko tunnisteita tai kun projektillasi on raj<PERSON>ettu laajuus tunnisteiden suhteen.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Tunnista syötteet, j<PERSON><PERSON> on merkittävä ei-tykkäys-suhde", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Poista tunniste", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän tunnisteen?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Älä näytä tätä uudelleen", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "<PERSON><PERSON><PERSON><PERSON> tunnist<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Lis<PERSON><PERSON> nimi", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Valitse enintään 9 tunnistetta, joiden välillä haluat syötteiden jaetun.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Luokittelu perustuu tunnisteille tällä hetkellä määritettyihin syötteisiin. Tietokone yrittää seurata esimerkkiäsi.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tunnisteet: <PERSON><PERSON><PERSON><PERSON>ä", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "<PERSON>ulla ei ole vielä omia tunnisteita.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Tietokone tunnistaa tunnisteet automaattisesti ja määrittää ne s<PERSON>ötteillesi.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tunnisteet: <PERSON><PERSON><PERSON><PERSON> auto<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "<PERSON><PERSON><PERSON> hy<PERSON>, kun projektisi kattavat laajan valiko<PERSON> tunnisteita. Hyvä paikka aloittaa.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Miten haluat merkitä?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "<PERSON><PERSON> il<PERSON> tun<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Tunnista kunkin syötteen kieli", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "Ei aktiivisia suodattimia", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Käytä tunnisteita s<PERSON>tteiden jakamiseen ja suodattamiseen, jotta voit tehdä tarkempia tai kohdennettuja yhteenvetoja.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tunnisteet: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Määritä olemassa olevat alustatunnisteet, jotka kirjoittaja valitsi lähettäessään", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "Suositeltava", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Nimeä tunniste uudelleen", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "Peruuta", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Nimeä tunniste uudelleen", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "<PERSON><PERSON><PERSON> kaikki", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "Määritä jokaiselle s<PERSON>elle tekstistä johdettu positiivinen tai negatiivinen mielipide", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "<PERSON><PERSON><PERSON><PERSON> tunnistus", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Käytä nykyisiä suodattimia", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Mitä tuloja haluat merkitä?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Automaattinen koodaustehtävä", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "Muka<PERSON>ttu", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "Päättyi klo", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Esimerkillä", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "K<PERSON>ynnissä", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "<PERSON><PERSON><PERSON><PERSON> mukaan", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP-tunniste", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "Ei viimeaikaisia tekoälytehtäviä", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "<PERSON><PERSON><PERSON> tun<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Alkoi klo", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Yhteenvetotehtävä", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "Laukaistiin klo", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Tekijä", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "<PERSON>a", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Ikä", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "Kotipaikka", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "From", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Syöte", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Reaktioiden määrä", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Äänten määrä", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "Lisää analyysiin", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonyymi <PERSON>ttö", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Tekijät iän mukaan", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Tekijät kotipaikan mukaan", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Taustatyöt", "app.containers.AdminPage.projects.project.analysis.comments": "Ko<PERSON>ntit", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Kotipaikkataulukko on liian suuri näytettäväksi", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "Piilota tyhjät vastaukset", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Vastaukset", "app.containers.AdminPage.projects.project.analysis.end": "Loppu", "app.containers.AdminPage.projects.project.analysis.filter": "Näytä vain sy<PERSON>tteet tällä arvolla", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "Pi<PERSON>ta vastaukset ilman vastausta", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Automaattiset tilastot", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Sarakearvot", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON><PERSON><PERSON><PERSON> yhdistelmästä on {count} esiintymiä.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "<PERSON>i pidä", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "Väärä", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "Tykkää", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Seuraava lämpökartta", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "<PERSON><PERSON><PERSON> o<PERSON>lus", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "<PERSON>i tilastollisesti merkitsevä havainto.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Automaattiset tilastot eivät ole käytettävissä projekteissa, jois<PERSON> on alle 30 osallistujaa.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Edellinen lämpökartta", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Tilastollisesti merkittävä oivallus.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "Totta", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Näytä kaikki <PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Näytä automaattiset tilastot", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "<PERSON><PERSON> il<PERSON> tun<PERSON>", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Virheellinen shape-<PERSON><PERSON><PERSON>, e<PERSON><PERSON> sitä voida n<PERSON>.", "app.containers.AdminPage.projects.project.analysis.limit": "<PERSON>", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Pääkysymys", "app.containers.AdminPage.projects.project.analysis.manageInput": "Muokkaa syötettä", "app.containers.AdminPage.projects.project.analysis.nextGraph": "<PERSON><PERSON><PERSON> kaavio", "app.containers.AdminPage.projects.project.analysis.noAnswer": "<PERSON><PERSON> vast<PERSON>ta", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "<PERSON>i vast<PERSON>ta.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "<PERSON><PERSON>pe-<PERSON><PERSON><PERSON> ei ladattu.", "app.containers.AdminPage.projects.project.analysis.noInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON> tulo ei vastaa nykyisiä suodattimiasi", "app.containers.AdminPage.projects.project.analysis.previousGraph": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.reactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.remove": "Poista", "app.containers.AdminPage.projects.project.analysis.removeFilter": "Poista suoda<PERSON>", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "Poista suoda<PERSON>", "app.containers.AdminPage.projects.project.analysis.search": "Hae", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapetiedostot näytetään GeoJSON-muodossa täällä. Alkuperäisen tiedoston tyyli ei välttämättä näy oikein.", "app.containers.AdminPage.projects.project.analysis.start": "alkaa", "app.containers.AdminPage.projects.project.analysis.supportArticle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis", "app.containers.AdminPage.projects.project.analysis.unknown": "Tuntematon", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "<PERSON><PERSON> ka<PERSON>", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "<PERSON><PERSON> valitut k<PERSON>ym<PERSON>", "app.containers.AdminPage.projects.project.analysis.votes": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.copied": "<PERSON><PERSON><PERSON><PERSON>öyd<PERSON>", "app.containers.AdminPage.widgets.copyToClipboard": "kopioi tämä koodi", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Kopioi HTML-koodi", "app.containers.AdminPage.widgets.fieldAccentColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldBackgroundColor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldButtonText": "Painikkeen te<PERSON>ti", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Liity nyt", "app.containers.AdminPage.widgets.fieldFont": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldFontDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> on oltava olemassa oleva fontin nimi osoitteesta {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Otsikon alaotsikko", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "Voit sanoa mielipit<PERSON>i", "app.containers.AdminPage.widgets.fieldHeaderText": "Otsikon otsikko", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Osallistumisalustamme", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Viestien määrä", "app.containers.AdminPage.widgets.fieldProjects": "Projektit", "app.containers.AdminPage.widgets.fieldRelativeLink": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "app.containers.AdminPage.widgets.fieldShowFooter": "Näytä-painike", "app.containers.AdminPage.widgets.fieldShowHeader": "Näytä otsikko", "app.containers.AdminPage.widgets.fieldShowLogo": "Näytä logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "<PERSON><PERSON><PERSON>av<PERSON>", "app.containers.AdminPage.widgets.fieldSort": "Lajitteluperuste", "app.containers.AdminPage.widgets.fieldTextColor": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldTopics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Ko<PERSON>iv<PERSON>", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Voit kopioida tämän HTML-koodin ja liittää sen verkkosivustosi siihen o<PERSON>, johon haluat lisätä widgetin.", "app.containers.AdminPage.widgets.htmlCodeTitle": "<PERSON><PERSON><PERSON> HTML-koodi", "app.containers.AdminPage.widgets.previewTitle": "Esikatselu", "app.containers.AdminPage.widgets.settingsTitle": "asetukset", "app.containers.AdminPage.widgets.sortNewest": "Uusin", "app.containers.AdminPage.widgets.sortPopular": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.sortTrending": "Trendaavat", "app.containers.AdminPage.widgets.subtitleWidgets": "Voit luoda widgetin, muokata sitä ja lisätä sen omalle verkkosivustollesi houkutellaksesi ihmisiä tälle alustalle.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "<PERSON><PERSON>", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "<PERSON><PERSON><PERSON>- ja alatun<PERSON>e", "app.containers.AdminPage.widgets.titleInputSelection": "<PERSON><PERSON> valinta", "app.containers.AdminPage.widgets.titleStyle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.PageTitle": "Ko<PERSON>iv<PERSON>", "app.containers.ContentBuilder.homepage.SaveError": "Jo<PERSON> meni pieleen etusivua tallennettaessa.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerImage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerSubtext": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.bannerText": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "Layout", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "<PERSON><PERSON> asetusten kuin kotisivun bannerin kuvan ja tekstin mukauttaminen ei sisälly nykyiseen lisenssiisi. Ota yhteyttä GovSuccess-päällikköösi saadaksesi lisätietoja.", "app.containers.ContentBuilder.homepage.customized_button": "Muka<PERSON>ttu", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Painikkeen te<PERSON>ti", "app.containers.ContentBuilder.homepage.customized_button_url_label": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Näyttää kolme seuraavaa tulevaa tapahtumaa alustallasi.", "app.containers.ContentBuilder.homepage.eventsDescription": "Näyttää kolme seuraavaa tulevaa tapahtumaa alustallasi.", "app.containers.ContentBuilder.homepage.fixedRatio": "Kiinteäsuhteinen banneri", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Tämä bannerityyppi toimii parhaiten kuvien kanssa, joita ei pidä rajata, kuten kuvissa, joissa on tekstiä, logo tai tiettyjä elementtejä, jotka ovat tärkeitä kansalaisille. Tämä banneri korvataan yhtenäisellä laatikolla, jonka pääväri on, kun käyttäjät ovat kirjautuneet sisään. Voit määrittää tämän värin yleisissä asetuksissa. Lisätietoja suositellusta kuvien käytöstä löytyy osoitteesta {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "tietopohja", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Täysleveä banneri", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Tämä banneri ulottuu koko leveydelle upean visuaalisen vaikutelman saavuttamiseksi. Kuva yrittää peittää mahdollisimman paljon tilaa, jolloin se ei aina ole aina näkyvissä. Voit yhdistää tämän bannerin minkä tahansa väriseen peittoon. Lisätietoja suositellusta kuvien käytöstä löytyy osoitteesta {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "tietopohja", "app.containers.ContentBuilder.homepage.imageOverlayColor": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Peittokuvan <PERSON>", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "app.containers.ContentBuilder.homepage.invalidUrl": "Virheellinen URL", "app.containers.ContentBuilder.homepage.no_button": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Rekisteröimättömät käyttäjät", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "<PERSON><PERSON> p<PERSON> k<PERSON>töö<PERSON>", "app.containers.ContentBuilder.homepage.projectsDescription": "Voit määrittää jä<PERSON><PERSON><PERSON><PERSON>en, jossa projektisi näytetään, järjestämällä ne uudelleen {link}.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Projektit -sivu", "app.containers.ContentBuilder.homepage.registeredUsersView": "Rekisteröityneet käyttäjät", "app.containers.ContentBuilder.homepage.showAvatars": "Näytä avatarit", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Näytä osallistujien profiilikuvat ja niiden lukumäärä rekisteröimättömille vierailijoille", "app.containers.ContentBuilder.homepage.sign_up_button": "Rekisteröidy", "app.containers.ContentBuilder.homepage.signedInDescription": "<PERSON><PERSON>in rekisteröityneet käyttäjät näkevät bannerin.", "app.containers.ContentBuilder.homepage.signedOutDescription": "<PERSON><PERSON><PERSON> bannerin näkevät vierailijat, jotka eivät ole rekisteröityneet alustalle.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Tämä banneri on erityisen hyödyllinen kuvissa, jotka eivät toimi hyvin otsikon, alaotsikon tai painikkeen tekstin kanssa. Nämä kohteet työnnetään bannerin alle. Lisätietoja suositellusta kuvien käytöstä löytyy osoitteesta {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "tietopohja", "app.containers.ContentBuilder.homepage.twoRowLayout": "<PERSON><PERSON><PERSON> riviä", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "Upotuskorkeus (pikseliä)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON>, jolla haluat upotetun sisällön näkyvän sivulla (pikseleinä).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Lyhyt kuvaus upotettavasta sisällöstä", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "On hyödyllistä antaa nämä tiedot käyttäjille, jotka käyttävät näytönlukuohjelmaa tai muuta aputekniik<PERSON>a.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Verkkosivuston osoite", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Upotettavan verkkosivuston täydellinen URL-osoite.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Näytä ulkoisen verkkosivuston sisältöä sivullasi HTML-iFrame-kehyksessä. <PERSON><PERSON><PERSON>, että kaikkia sivuja ei voi upottaa. <PERSON><PERSON> on ongelmia sivun upottamisessa, tarkista sivun o<PERSON>, onko se määritetty sallimaan upottaminen.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "<PERSON><PERSON><PERSON> tuki<PERSON>", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "Valitettavasti tätä sisältöä ei voitu upottaa. {visitLinkMessage} saadaksesi lisätietoja.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/en/articles/6354058-embedding-elements-in-the-content-builder-to-enrich-project-descriptions", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "<PERSON> verk<PERSON>, esimerkiksi https://example.com", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Upottaa", "app.containers.admin.ContentBuilder.accordionMultiloc": "Harmonikka", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Tämä on laajennettavaa harmonikkasisältöä. Voit muokata ja muotoilla sitä käyttämällä oikeanpuoleisen paneelin editoria.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "<PERSON><PERSON><PERSON><PERSON> nimi", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "Poistaa", "app.containers.admin.ContentBuilder.error": "virhe", "app.containers.admin.ContentBuilder.errorMessage": "<PERSON>s<PERSON>llössä {locale} on vir<PERSON>. <PERSON><PERSON><PERSON><PERSON>, jotta voit tallentaa muutokset", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "<PERSON><PERSON><PERSON> avatarit", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "<PERSON><PERSON><PERSON><PERSON> on neljännesvuosittain suoritettava jatkuva tutkimus, j<PERSON>, mitä mieltä olet hallinn<PERSON>a ja julkisista palveluista.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Vastaa k<PERSON>elyyn", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Auta meitä palvelemaan sinua paremmin", "app.containers.admin.ContentBuilder.homepage.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "Ensisijaisen painikkeen URL-osoite", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Ensisijainen painikkeen teksti", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "Toissijaisen painikkeen URL-osoite", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://example.com", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Toiss<PERSON>isen pain<PERSON> teksti", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "<PERSON><PERSON><PERSON><PERSON> banneri", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "<PERSON><PERSON><PERSON><PERSON> banneri", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "<PERSON><PERSON>- ja te<PERSON><PERSON>", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 sarake", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projektit", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Ota ehdotukset käyttöön hallintapaneelin Ehdotukset-osiossa avataksesi niiden lukituksen etusivulla", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "ehdotuksia", "app.containers.admin.ContentBuilder.imageMultiloc": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Lyhyt kuvaus kuvasta", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "\"Vaihtoehtoisen tekstin\" lisääminen kuviin on tärkeää, jotta alust<PERSON> on näytönlukuohjelmia käyttävien käyttäjien käytettävissä.", "app.containers.admin.ContentBuilder.participationBox": "Osallistumislaatikko", "app.containers.admin.ContentBuilder.textMultiloc": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 sarake", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 saraketta", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 sarak<PERSON>, j<PERSON>en leveys on 30 % ja 60 %", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 sa<PERSON><PERSON>, j<PERSON>en leveys on 60 % ja 30 %", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 parillista saraketta", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://example.com", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "<PERSON>iten reago<PERSON>et tulot", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Ideointivaihe", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "<PERSON><PERSON><PERSON>än projektiin tai vaiheeseen ei ole saatavilla syötteitä.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "Näytä lisää", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "<PERSON><PERSON> yhteen<PERSON>ä: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Tiivistä pitkä teksti", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "<PERSON><PERSON><PERSON>än projektiin tai vaiheeseen ei ole saatavilla syötteitä.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Valitse vaihe", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Tekijä", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Sisältö", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Syöte", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Rekis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Rekisteröinnit", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "<PERSON><PERSON><PERSON>, että osallistumisluvut eivät välttämättä ole tä<PERSON>, koska o<PERSON> on ker<PERSON><PERSON> ulko<PERSON> k<PERSON>, jota emme se<PERSON>.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.charts.noData": "Valitsemillesi suodattimille ei ole saatavilla tietoja.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Liikenteen lähteet", "app.containers.admin.ReportBuilder.charts.users": "Käyttäjät", "app.containers.admin.ReportBuilder.charts.usersByAge": "Käyttäjät iän mukaan", "app.containers.admin.ReportBuilder.charts.usersByGender": "Käyttäjät sukupuolen mukaan", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.managerLabel1": "Projektipäällikkö", "app.containers.admin.ReportBuilder.periodLabel1": "<PERSON><PERSON><PERSON>", "app.containers.admin.ReportBuilder.projectLabel1": "Projekti", "app.containers.admin.ReportBuilder.quarterReport1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "alkaa", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "Osta 1 lisäpaikka", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Vahvistaa", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Haluatko varmasti antaa 1 henkilön ylläpitäjän o<PERSON>udet?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "<PERSON>", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "<PERSON><PERSON>t suunnitelmaasi sisältyvien paikkojen enimmäismäärän, 1 lisäpaikka lisätään.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Lisää tila", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Oletustiloja ei voi poistaa.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Poistaa", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "<PERSON><PERSON><PERSON><PERSON> tilaa", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> sy<PERSON>tteelle tällä hetkellä määritettyjä tiloja ei voi poistaa. Voit poistaa tai muuttaa olemassa olevan syötteen tilaa {manageTab} -välilehdellä.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Tätä tilaa ei voi poistaa tai siirtää.", "app.containers.admin.ideaStatuses.all.manage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Mukautettujen s<PERSON>ttötilojen määrittäminen ei sisälly nykyiseen suunnitelmaasi. Keskustele hallituksen menestyspäällikön tai järjestelmänvalvojan kanssa avataksesi sen.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "<PERSON><PERSON><PERSON><PERSON> tilaa, joka <PERSON>aan määrittää osallistujan s<PERSON>tteelle projektin sisällä. <PERSON><PERSON> on julkisesti nähtävissä ja auttaa pitämään osallistujat ajan tasal<PERSON>.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "<PERSON><PERSON><PERSON><PERSON> tilaa, joka <PERSON>aan määrittää projektin sisällä oleville ehdotuksille. T<PERSON> on julkisesti nähtävissä ja auttaa pitämään osallistujat ajan tasal<PERSON>.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "Muokkaa <PERSON>öttötiloja", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "<PERSON><PERSON><PERSON><PERSON> eh<PERSON>", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Valittu k<PERSON>öönottoa tai seuraavia vaiheita varten", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Hyväksytty", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Virallinen palaute annettu", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "<PERSON><PERSON>att<PERSON>", "app.containers.admin.ideaStatuses.form.category": "Kategoria", "app.containers.admin.ideaStatuses.form.categoryDescription": "<PERSON><PERSON><PERSON> luo<PERSON>, joka parhaiten edustaa tilaasi. Tämä valinta auttaa analytiikkatyökaluamme käsittelemään ja analysoimaan viestejä tarkemmin.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Ei vastaa mitään muuta vaihtoehtoa", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescription": "T<PERSON>n kuva<PERSON>", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "<PERSON> ka<PERSON> kiel<PERSON>", "app.containers.admin.ideaStatuses.form.fieldTitle": "<PERSON><PERSON><PERSON> nimi", "app.containers.admin.ideaStatuses.form.fieldTitleError": "<PERSON> tilan nimi kaikille kiel<PERSON>", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Onnistuneesti toteutettu", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Toteutettu", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON> ei kelpaa", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "<PERSON><PERSON> kel<PERSON>a", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Ei kelvollinen tai ei valittu siirtymään eteenpäin", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Ei valittu", "app.containers.admin.ideaStatuses.form.saveStatus": "<PERSON><PERSON><PERSON> tila", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON> toteutusta tai seuraavia vaiheita varten", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "<PERSON><PERSON><PERSON><PERSON>, mutta ei vielä käsitelty", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Katsottu", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Muokkaa s<PERSON>ötteitä ja niiden til<PERSON>.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Syöttöhallinta | Osallistumisalusta {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "<PERSON>, lis<PERSON><PERSON> tunnisteita ja siirrä syötettä projektista toiseen", "app.containers.admin.ideas.all.inputManagerPageTitle": "Syöttöhallinta", "app.containers.admin.ideas.all.tabOverview": "Yleiskatsaus", "app.containers.admin.import.importInputs": "<PERSON><PERSON>", "app.containers.admin.import.importNoLongerAvailable3": "Tämä ominaisuus ei ole enää käytettävissä täällä. Tuodaksesi syötteitä ideointivaiheeseen, si<PERSON><PERSON> vai<PERSON>n ja valitse \"Tuo\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 ylimääräinen järjestelmänvalvojan paikka} other {# ylimääräistä järjestelmänvalvojan paikkaa}} ja {managerSeats, plural, one {1 ylimääräinen johtajapaikka} other {# ylimääräinen järjestelmänvalvojan paikka manageripaikkoja}} lisätään rajan yli.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 ylimääräinen järjestelmänvalvojan paikka lisätään rajan yli} other {# ylimääräistä järjestelmänvalvojan paikkaa lisätään rajan}}yli.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 ylimääräinen manageripaikka lisätään rajan yli} other {# ylimääräistä manageripaikkaa lisätään rajan}}yli.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Vahvista ja lähetä kutsut", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Vahvista vaikutus istuimen käyttöön", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "<PERSON><PERSON>t suunnitelmassasi olevien paikkojen enimmäismäärän.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "<PERSON><PERSON>män projektin ylläpitäjät ja johta<PERSON>t", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "<PERSON>ain y<PERSON>äjät ja yhteistyökumppanit", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "<PERSON>ain alustan y<PERSON>j<PERSON>, kansioiden ylläpitäjät ja projektipäälliköt voivat ryhtyä toimiin", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>öimättömät käyttäjät, voivat osallistua.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Valitut", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Tiettyjen käyttäjäryhmien käyttäjät voivat osallistua. Voit hallita käyttäjäryhmiä Käyttäjät-välilehdellä.", "app.containers.admin.project.permissions.viewingRightsTitle": "Kuka voi nähdä tämän projektin?", "app.containers.phaseConfig.enableSimilarInputDetection": "<PERSON><PERSON> k<PERSON><PERSON><PERSON> sa<PERSON>en tulon tunnistus", "app.containers.phaseConfig.similarInputDetectionTitle": "<PERSON><PERSON><PERSON><PERSON> tulon tunnistus", "app.containers.phaseConfig.similarInputDetectionTooltip": "Näytä o<PERSON><PERSON><PERSON> sa<PERSON> s<PERSON>, jotta vältytään ka<PERSON>.", "app.containers.phaseConfig.similarityThresholdBody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (runko)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "Tämä hallitsee sitä, kuinka samankaltaisten kuvausten on oltava samankaltaisia. Käytä arvoa väliltä 0 (tiukka) ja 1 (lievä). Pienemmät arvot palauttavat vähemmän mutta tarkempia osumia.", "app.containers.phaseConfig.similarityThresholdTitle": "Sam<PERSON><PERSON><PERSON><PERSON><PERSON> (otsikko)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "Tämä hallitsee sitä, kuinka samankaltaisten kahden nimikkeen on oltava samankaltaisia. Käytä arvoa väliltä 0 (tiukka) ja 1 (lievä). Pienemmät arvot palauttavat vähemmän mutta tarkempia osumia.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Tämä ominaisuus on saatavilla osana ennakkokäyttöön tarkoitettua tarjousta 30. kesäkuuta 2025 asti. <PERSON><PERSON> haluat jatkaa sen käyttöä tämän päivämäärä<PERSON> j<PERSON><PERSON>, ota yhteyttä Government Success Manageriin tai järjestelmänvalvojaan keskustellaksesi aktivointivaihtoehdoista.", "app.containers.survey.sentiment.noAnswers2": "Ei vastauksia tällä hetkellä.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {0 kommenttia} one {1 kommentti} other {# kommenttia}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Osallistujat ovat käyttäjiä tai vieraili<PERSON>, jotka ovat osallistuneet projektiin, l<PERSON>hettäneet ehdotuksen tai olleet vuorovaikutuksessa sen kanssa tai osallistuneet tapahtumiin.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joista tulee o<PERSON>.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automaattiset kampan<PERSON>t", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automaattiset sähköpostit", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "{quantity} kampanjoista", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Mukautetut kampan<PERSON>t", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Mukautetut sähköpostit", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "Sähköpostit", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Lähetetyt sähköpostit yhteensä", "app.modules.commercial.analytics.admin.components.Events.completed": "Val<PERSON>", "app.modules.commercial.analytics.admin.components.Events.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Lisätyt tapahtumat yhteensä", "app.modules.commercial.analytics.admin.components.Events.upcoming": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "Hyväksytty", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Kutsut", "app.modules.commercial.analytics.admin.components.Invitations.pending": "Odottaa", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Lähetetyt kutsut yhteensä", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "<PERSON><PERSON><PERSON> Manageriin", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Toiminnat", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktiivinen", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "<PERSON><PERSON><PERSON><PERSON>, joita ei ole arkistoitu ja jotka näkyvät kotisivun Aktiivinen-taulukossa", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "<PERSON><PERSON><PERSON> luonn<PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "Val<PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Kaikki päättyneet arkistoidut projektit ja aktiiviset aikajanaprojektit lasketaan tähän", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "Projektit", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "<PERSON>je<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Alustalla näkyvien projektien määrä", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Uudet rekisteröinnit", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Rekis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joista tulee rekisteröityneitä käyttäjiä.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Rekisteröinnit", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Ilmoittautumiset yhteensä", "app.modules.commercial.analytics.admin.components.Tab": "Viera<PERSON>jat", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Viimeiset 30 päivää:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Viimeiset 7 päivää:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "<PERSON><PERSON>n katselut käyntiä kohden", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Vierailun kesto", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Viera<PERSON>jat", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"<PERSON>ä<PERSON><PERSON><PERSON>\" on yksittäisten vierailijoiden määrä. Jos henkilö vierailee alustalla useita kertoja, hänet lasketaan vain kerran.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Vierailut", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"<PERSON><PERSON><PERSON><PERSON>\" on istuntojen määrä. <PERSON><PERSON> on käynyt alustalla useita kertoja, j<PERSON><PERSON> kä<PERSON>ti las<PERSON>.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "<PERSON><PERSON><PERSON>:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "Laskea", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "Vierailijoiden määrä", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prosentti<PERSON>uus", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Viittaaja", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "Klikkaa tästä", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Viittaajat", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "<PERSON><PERSON> t<PERSON> luettelo viittauksista {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Viera<PERSON>jat", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Liikenteen lähteet", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Vierailut", "app.modules.commercial.analytics.admin.components.totalParticipants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Kävijätietoja ei vielä ole.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Olemme muuttaneet tapaamme kerätä ja näyttää kävijätietoja. Tämän seurauksena kävijätiedot ovat tarkempia ja saatavilla on useampia datatyyppejä, mutta ne ovat edelleen GDPR-vaatimusten mukaisia. Vaikka kävijöiden aikajanalla käytetyt tiedot ulottuvat pidemmälle ajalle, al<PERSON><PERSON><PERSON> \"Käynnön kesto\", \"Sivukatselukerrat käyntiä kohden\" ja muiden kaavioiden tietojen keräämisen vasta marraskuussa 2024, joten sitä ennen tietoja ei ole saatavilla. Jos siis valitset tietoja ennen marraskuuta 2024, huoma<PERSON>, että jotkin kaaviot saattavat olla tyhjiä tai näyttää oudolta.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "Sähköpostitoimitukset ajan mittaan", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Ilmoittautumiset a<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Päivämäärä", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Vierailut ja vierailijat ajan mittaan", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Yhteensä a<PERSON> aikana", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "Kävijöiden määrä", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Viittaaja", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Viittaavat verkkosivustot", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Hakukon<PERSON>t", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Sosiaaliset verkostot", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO-u<PERSON><PERSON><PERSON><PERSON>ja<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Liikenteen lähde", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Verkkosivustot", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Voit poistaa tämän sisältölipun valitsemalla tämän kohteen ja napsauttamalla yläreunassa olevaa poista-painiketta. Se näkyy sitten uudelleen Nähty- tai Ei näkynyt -välilehdissä", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Sopimaton sisältö tunnistettiin automaattisesti.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "Yhteisö ei ole ilmoittanut tarkastettavaksi viestejä tai jotka on merkitty sopimattomaksi sisällöksi Natural Language Processing -järjestelmämme", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Poista {numberOfItems, plural, one {sisältövaroitus} other {# sisältövaroitusta}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "<PERSON><PERSON><PERSON> il<PERSON>itti sopimattomaksi.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "<PERSON><PERSON><PERSON>", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Navigointipalkissa näkyvät sivut", "app.modules.navbar.admin.containers.addProject": "Lisää projekti navigointipalkkiin", "app.modules.navbar.admin.containers.createCustomPageButton": "<PERSON><PERSON> mukautettu sivu", "app.modules.navbar.admin.containers.deletePageConfirmation": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän sivun? Tätä ei voi peruuttaa. Voit myös poistaa sivun navigointipalkista, jos et ole vielä valmis poistamaan sitä.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Voit lisätä enintään 5 kohdetta navigointipalkkiin", "app.modules.navbar.admin.containers.pageHeader": "Sivut ja valikko", "app.modules.navbar.admin.containers.pageSubtitle": "Navigointipalkkisi voi näyttää jopa viisi sivua aloitussivun ja projektisivujen lisäksi. Voit nimetä valikkokohtia uudelleen, järjestää uudelleen ja lisätä uusia sivuja omalla sisällölläsi.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "AI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "<PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Käytä alla olevaa ☰-kuvaketta vetääksesi te<PERSON>lysisältöä raporttiin.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "AI-näkemyksiä ei ole saatavilla. Voit luoda niitä projektissasi.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "<PERSON><PERSON><PERSON> proje<PERSON>n", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "<PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Valitse vaihe", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "Avaa AI-analyysi", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Hyödynnä tekoälyn luomia oivalluksia raporttiin", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "<PERSON><PERSON><PERSON> nopeammin te<PERSON> a<PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Raportointi tekoälyllä ei sisälly nykyiseen suunnitelmaasi. Ota yhteyttä Government Success Manageriin tämän ominaisuuden avaamiseksi.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Tämä ei sisälly nykyiseen suunnitelmaasi. Ota yhteyttä Government Success Manageriin tai järjestelmänvalvojaan avataksesi sen.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "<PERSON><PERSON><PERSON><PERSON><PERSON> ilmoittautumiskentän mukaan", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON> kys<PERSON>n mukaan", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Ryhmätila", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Ryhm<PERSON>le kyselyvastaukset ilmoittautumiskenttien (<PERSON><PERSON><PERSON><PERSON>, sijainti, ikä jne.) tai muiden kyselykysymysten mukaan.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "<PERSON><PERSON> mit<PERSON>n", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "<PERSON><PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Rekisteröintikenttä", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Kyselyvaihe", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "Peruuta", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Poistaa", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Lähetä kommenttisi", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Tallentaa", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON> t<PERSON>n", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "Klikkaa alla olevia painik<PERSON>ita seurataksesi tai lopetta<PERSON> se<PERSON>. Projektien lukumäärä näkyy suluissa.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "Tehty", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Seuraa-asetuksillasi ei ole tällä hetkellä aktiivisia projekteja.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Tä<PERSON>ä widget näyttää käyttäjän seuraamiin \"alueisiin\" liittyvät projektit. <PERSON><PERSON><PERSON>, että alustasi saattaa käyttää \"alueille\" eri nimeä – katso \"Alueet\"-v<PERSON><PERSON><PERSON><PERSON>i alustan asetuksista. Jo<PERSON> käyttäjä ei vielä seuraa alueita, widget näyttää seurattavissa olevat alueet. Tässä tapauksessa widget näyttää enintään 100 aluetta.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "Julkaistuja projekteja tai kansioita ei ole saatavilla", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Julkaistut projektit ja kansiot", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Tämä widget käsittelee parhaillaan julkaistuja projekteja ja kansioita noudattaen projektisivulla määritettyä järjestystä. Tämä toimintatapa on sama kuin \"vanhojen\" projektien widgetin \"aktiivinen\"-välilehti.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Projekteja tai kansioita ei ole valittu", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Valitse projektit tai kansiot", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Valitut projektit ja kansiot", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "<PERSON><PERSON><PERSON>ä widgetillä voit valita ja määrittää järjestyksen, jossa haluat projektien tai kansioiden näkyvän käyttäjille.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} projekteja", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "viera<PERSON> tuki<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/en/articles/1346397-what-are-the-recommended-dimensions-and-sizes-of-the-platform-images", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Lisätietoja suositelluista k<PERSON> on {supportPageLink}."}