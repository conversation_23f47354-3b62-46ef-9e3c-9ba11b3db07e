{"UI.FormComponents.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.action": "Aktion", "app.Admin.ManagementFeed.after": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.before": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.changed": "G<PERSON><PERSON>ndert", "app.Admin.ManagementFeed.created": "Erstellt am", "app.Admin.ManagementFeed.date": "Datum", "app.Admin.ManagementFeed.deleted": "Gelöscht", "app.Admin.ManagementFeed.folder": "<PERSON><PERSON><PERSON>", "app.Admin.ManagementFeed.idea": "Beitrag", "app.Admin.ManagementFeed.in": "im Projekt {project}", "app.Admin.ManagementFeed.item": "Element", "app.Admin.ManagementFeed.key": "Schlüssel", "app.Admin.ManagementFeed.managementFeedNudge": "Der Zugriff auf den Management-Feed ist nicht in Ihrer aktuellen Lizenz enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.Admin.ManagementFeed.noActivityFound": "Keine Aktivität gefunden", "app.Admin.ManagementFeed.phase": "Phase", "app.Admin.ManagementFeed.project": "Projekt", "app.Admin.ManagementFeed.projectReviewApproved": "Projekt freigegeben", "app.Admin.ManagementFeed.projectReviewRequested": "Projektfreigabe beantragt", "app.Admin.ManagementFeed.title": "Management-Feed", "app.Admin.ManagementFeed.user": "Nutzer*in", "app.Admin.ManagementFeed.userPlaceholder": "<PERSON><PERSON>hlen Sie eine*n <PERSON><PERSON>*in", "app.Admin.ManagementFeed.value": "Wert", "app.Admin.ManagementFeed.viewDetails": "Details anzeigen", "app.Admin.ManagementFeed.warning": "Experimentelle Funktion: Eine minimale Liste ausgewählter Aktionen, die von Admins oder Projektmanager*innen in den letzten 30 Tagen durchgeführt wurden. Es sind nicht alle Aktionen enthalten.", "app.Admin.Moderation.managementFeed": "Management-Feed", "app.Admin.Moderation.participationFeed": "Teilnahme-Feed", "app.components.Admin.Campaigns.campaignDeletionConfirmation": "Sind Sie sicher?", "app.components.Admin.Campaigns.clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deleteCampaignButton": "Kampagne löschen", "app.components.Admin.Campaigns.deliveryStatus_accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_bounced": "<PERSON>cht zugestellt", "app.components.Admin.Campaigns.deliveryStatus_clicked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_clickedTooltip": "Wenn Si<PERSON> einen oder mehrere Links zu Ihrer E-Mail hinzugefügt haben, wird hier die Anzahl der Nutzer*innen angezeigt, die auf einen Link geklickt haben.", "app.components.Admin.Campaigns.deliveryStatus_delivered": "Angekommen", "app.components.Admin.Campaigns.deliveryStatus_failed": "Fehlgeschlagen", "app.components.Admin.Campaigns.deliveryStatus_opened": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.deliveryStatus_sent": "Gesendet", "app.components.Admin.Campaigns.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.manageButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.opened": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Admin.Campaigns.project": "Projekt", "app.components.Admin.Campaigns.recipientsTitle": "Empfänger*innen", "app.components.Admin.Campaigns.sent": "Gesendet", "app.components.Admin.Campaigns.statsButton": "Statistik", "app.components.Admin.Campaigns.subject": "<PERSON><PERSON><PERSON>", "app.components.Admin.ImageCropper.imageSupportPageURL": "https://support.govocal.com/de/articles/1346397-welche-dimensionen-und-grossen-sind-fur-bilder-auf-der-plattform-empfohlen", "app.components.Admin.ImageCropper.info": "Dieses Bild wird immer auf ein bestimmtes Verhältnis zugeschnitten, um sicherzustellen, dass alle wichtigen Aspekte jederzeit zu sehen sind. Die {link} für diesen Bildtyp ist {aspect}.", "app.components.Admin.ImageCropper.infoLinkText": "empfohlenes Verhältnis", "app.components.Admin.ImageCropper.mobileCropExplanation": "Hinweis: Alle wichtigen Bereiche des Bildes sollten innerhalb der vertikalen gestrichelten Linien liegen, da das Bild auf mobilen Geräten auf ein Verhältnis von 3:1 zugeschnitten wird.", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Kopfzeilentext für registrierte Nutzer*innen", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Warnung: Die von Ihnen gewählte Farbe hat keinen ausreichend hohen Kontrast. Dies kann dazu führen, dass der Text schwer zu lesen ist. Wählen Sie eine dunklere Farbe, um die Lesbarkeit zu optimieren.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Veranstaltungen zur Navigationsleiste hinzufügen", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "<PERSON><PERSON> diese Option aktiviert ist, wird ein Link zu allen Projektereignissen in der Navigationsleiste hinzugefügt.", "app.components.AdminPage.SettingsPage.eventsSection": "Veranstaltungen", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Startseite anpassbarer <PERSON>", "app.components.AnonymousPostingToggle.userAnonymity": "Anonyme Beteiligung", "app.components.AnonymousPostingToggle.userAnonymityLabelSubtext": "Nutzer*innen können ihre Identität vor anderen <PERSON>*innen, Projektmanager*innen und Admins verbergen. Diese Beiträge können weiterhin moderiert werden.", "app.components.AnonymousPostingToggle.userAnonymityLabelText": "Ermöglichen Sie Nutzer*innen die anonyme Teilnahme", "app.components.AnonymousPostingToggle.userAnonymityLabelTooltip2": "Die Nutzer*innen können weiterhin mit ihrem echten Namen teilnehmen, haben aber auch die Möglichkeit, ihre Beiträge anonym einzure<PERSON>n, wenn sie dies wünschen. Alle Nutzer*innen müssen weiterhin die auf der Registerkarte \"Zugriffsrechte\" festgelegten Anforderungen erfüllen, damit ihre Beiträge angenommen werden können. Die Daten des Nutzer*innenprofils werden beim Export der Teilnahmedaten nicht verfügbar sein.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltip": "Erfahren Sie mehr über anonyme Beteiligung in unserer {supportArticle}.", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkText": "Wissensdatenbank", "app.components.AnonymousPostingToggle.userAnonymitySupportTooltipLinkUrl": "https://support.govocal.com/de/articles/7946486-anonyme-beteiligung-aktivieren", "app.components.BillingWarning.billingWarning": "Wenn Sie zusätzliche Zugänge hinzufügen, bekommen Sie eine separate Rechnung bzw. wird Ihre Jahresabrechnung erhöht. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin für mehr Informationen.", "app.components.CommunityMonitorModal.surveyCompletedUntilNextQuarter": "Vielen Dank für die Teilnahme an der Umfrage! Gerne können Sie im nächsten Quartal erneut teilnehmen.", "app.components.FormBuilder.components.FormBuilderTopBar.downloadPDF": "Als PDF herunterladen", "app.components.FormSync.downloadExcelTemplate": "Download einer Excel-Vorlage", "app.components.FormSync.downloadExcelTemplateTooltip2": "Die Excel-Vorlagen enthalten keine Ranking-Fragen, Matrix-Fragen, Dateiupload-Fragen und keine Fragen mit Kartierung (<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ESRI Datenupload), da diese derzeit für den Import nicht unterstützt werden.", "app.components.ProjectTemplatePreview.close": "Schließen", "app.components.ProjectTemplatePreview.createProject": "Projekt erstellen", "app.components.ProjectTemplatePreview.createProjectBasedOn2": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Projekt auf der Grundlage der Vorlage ''{templateTitle}''", "app.components.ProjectTemplatePreview.goBack": "Zurück", "app.components.ProjectTemplatePreview.goBackTo": "﻿Gehen Sie zurück zum {goBackLink}.", "app.components.ProjectTemplatePreview.govocalExpert": "CitizenLab-Experte", "app.components.ProjectTemplatePreview.infoboxLine1": "Möchtest du diese Vorlage für ein Beteiligungsprojekt verwenden?", "app.components.ProjectTemplatePreview.infoboxLine2": "<PERSON>den Si<PERSON> sich an die zuständige Person in Ihrer Stadtverwaltung oder an einen {link}.", "app.components.ProjectTemplatePreview.projectFolder": "Projekt-Ordner", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "Das ausgewählte Datum ist ungültig. Bitte geben Si<PERSON> ein Datum im folgenden Format an: JJJJ-MM-TT", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Bitte wählen Sie einen Anfangstermin für das Projekt", "app.components.ProjectTemplatePreview.projectStartDate": "Das Startdatum Ihres Projekts", "app.components.ProjectTemplatePreview.projectTitle": "Der Titel Ihres Projekts", "app.components.ProjectTemplatePreview.projectTitleError": "<PERSON>te gebe ein Projekttitel ein", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Bitte geben Sie einen Projekttitel für alle Sprachen ein", "app.components.ProjectTemplatePreview.projectsOverviewPage": "Projektübersichtsseite", "app.components.ProjectTemplatePreview.responseError": "Uups... etwas ging schief.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "<PERSON><PERSON> sehen", "app.components.ProjectTemplatePreview.successMessage": "Das Projekt wurde erfolgreich erstellt!", "app.components.ProjectTemplatePreview.typeProjectName": "Geben Sie den Namen des Projekts ein", "app.components.ProjectTemplatePreview.useTemplate": "Vor<PERSON> verwenden", "app.components.SeatInfo.additionalSeats": "Zusätzliche Zugänge", "app.components.SeatInfo.additionalSeatsToolTip": "Hier wird die Anzahl der zusätzlichen Zugänge angezeigt, die Sie zusätzlich zu den \"inbegriffenen Zugängen\" erworben haben.", "app.components.SeatInfo.adminSeats": "Admin-Zugänge", "app.components.SeatInfo.adminSeatsIncludedText": "{adminSeats} enthaltene Admin-Zugänge", "app.components.SeatInfo.adminSeatsTooltip1": "Admins sind für die Plattform zuständig und haben Verwaltungsrechte für alle Ordner und Projekte. Sie können {visitHelpCenter} besuchen, um mehr über die verschiedenen Rollen zu erfahren.", "app.components.SeatInfo.currentAdminSeatsTitle": "Aktuelle Anzahl an Admin-Zugängen", "app.components.SeatInfo.currentManagerSeatsTitle": "Aktuelle Anzahl an Ordner- und Projektmanagement-Zugängen", "app.components.SeatInfo.includedAdminToolTip": "Hier wird die Anzahl der verfügbaren Admin-Zugänge angezeigt, die in Ihrer Lizenz enthalten sind.", "app.components.SeatInfo.includedManagerToolTip": "Hier wird die Anzahl der verfügbaren Zugänge für Projekt- und Ordnermanager*innen angezeigt, die in Ihrer Lizenz enthalten sind.", "app.components.SeatInfo.includedSeats": "Enthaltene Zugänge", "app.components.SeatInfo.managerSeats": "Ordner- und Projektmanagement-Zugänge", "app.components.SeatInfo.managerSeatsTooltip": "Ordner-/Projektmanager*innen können eine unbegrenzte Anzahl von Ordnern/Projekten verwalten. Sie können {visitHelpCenter} besuchen, um mehr über die verschiedenen Rollen zu erfahren.", "app.components.SeatInfo.managersIncludedText": "{managerSeats} enthaltene Ordner- und Projektmanagement-Zugänge", "app.components.SeatInfo.remainingSeats": "Verbleibende Zugänge", "app.components.SeatInfo.rolesSupportPage": "https://support.govocal.com/de/articles/2672884-was-sind-die-verschiedenen-rollen-auf-der-plattform", "app.components.SeatInfo.totalSeats": "Zugänge insgesamt", "app.components.SeatInfo.totalSeatsTooltip": "Hier wird die Gesamtzahl der Nutzungsrechte in Ihrer Lizenz und der von Ihnen zusätzlich erworbenen Zugänge angezeigt.", "app.components.SeatInfo.usedSeats": "Verwendete Zugänge", "app.components.SeatInfo.view": "Mehr Informationen", "app.components.SeatInfo.visitHelpCenter": "unserer Wissensdatenbank", "app.components.SeatTrackerInfo.adminInfoTextWithoutBilling": "<PERSON><PERSON>e <PERSON> enthält {adminSeatsIncluded}. <PERSON>n <PERSON> alle Zugänge nutzen, werden unter \"Zusätzliche Zugänge\" weitere hinzugefügt.", "app.components.SeatTrackerInfo.managerInfoTextWithoutBilling": "<PERSON><PERSON>e <PERSON>z enthält {managerSeatsIncluded} für Ordner- und Projektmanager*innen. Wenn Sie alle Zugänge genutzt haben, werden unter \"Zusätzliche Zugänge\" weitere hinzugefügt.", "app.components.UserSearch.addModerators": "Hinzufügen", "app.components.UserSearch.searchUsers": "Tippen um Person(en) zu suchen...", "app.components.admin.ActionForm.CustomizeErrorMessage.alternativeErrorMessage": "Alternative Fehlermeldung", "app.components.admin.ActionForm.CustomizeErrorMessage.customErrorMessageExplanation": "Standardmäßig wird den Nutzer*innen die folgende Fehlermeldung angezeigt:", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessage": "Fehlermeldung anpassen", "app.components.admin.ActionForm.CustomizeErrorMessage.customizeErrorMessageExplanation": "<PERSON>e können diese Meldung für jede Sprache überschreiben, indem Sie das Textfeld \"Alternative Fehlermeldung\" unten verwenden. Wenn Si<PERSON> das Textfeld leer lassen, wird die Standardmeldung angezeigt.", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessage": "Fehlermeldung", "app.components.admin.ActionForm.CustomizeErrorMessage.errorMessageTooltip": "Das werden die Teilnehmenden sehen, wenn sie die Teilnahmebedingungen nicht erfüllen.", "app.components.admin.ActionForm.CustomizeErrorMessage.saveErrorMessage": "Fehlermeldung speichern", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.emptyField": "<PERSON>ine Frage ausgewählt. Bitte wählen Si<PERSON> zu<PERSON>t eine Frage aus.", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.noAnswer": "<PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.numberOfResponses": "{count} An<PERSON><PERSON><PERSON>", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.surveyQuestion": "Frage", "app.components.admin.ContentBuilder.Widgets.SurveyQuestionResultWidget.untilNow": "{date} bis jetzt", "app.components.admin.DatePhasePicker.Input.openEnded": "Noch nicht abgeschlossen", "app.components.admin.DatePhasePicker.Input.selectDate": "Da<PERSON> ausw<PERSON>en", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearEndDate": "Enddatum löschen", "app.components.admin.DatePickers.DateRangePicker.Calendar.clearStartDate": "Startdatum festlegen", "app.components.admin.Graphs": "Mit den aktuellen Filtern sind keine Daten verfügbar.", "app.components.admin.Graphs.noDataShort": "<PERSON><PERSON> verfüg<PERSON>.", "app.components.admin.GraphsCards.CommentsByTime.hooks.useCommentsByTime.timeSeries": "Kommentare im Laufe der Zeit", "app.components.admin.GraphsCards.PostsByTime.hooks.usePostsByTime.timeSeries": "Beiträge im Laufe der Zeit", "app.components.admin.GraphsCards.ReactionsByTime.hooks.useReactionsByTime.timeSeries": "Reaktionen im Laufe der Zeit", "app.components.admin.InputManager.onePost": "1 Beitrag", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualPickAdjustment2": "Anpassung der analogen Stimmen", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustment3": "Anpassung der analogen Stimmen", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip": "<PERSON><PERSON> dieser Option können Sie Daten zur Teilnahme aus anderen Quellen einbeziehen, z. B. analoge Abstimmungen oder Abstimmungen auf Papier:", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip2": "Es wird sich visuell von den digitalen Abstimmungen unterscheiden.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip3": "Das wird sich auf das endgültige Abstimmungsergebnis auswirken.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip4": "<PERSON>e wird nicht in den Dashboards für Beteiligungsdaten angezeigt.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVoteAdjustmentTooltip7": "Offline-Abstimmungen für eine Option können nur einmal in einem Projekt festgelegt werden und werden von allen Phasen eines Projekts gemeinsam genutzt.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersDisabledTooltip": "<PERSON><PERSON> müssen zuerst die Gesamtzahl der Offline-Teilnehmenden eingeben.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersLabel2": "Offline-Teilnehmende insgesamt", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip1a": "Um die richtigen Ergebnisse zu berechnen, müssen wir die <b>Gesamtzahl der Offline-Teilnehmenden für diese Phase</b> kennen.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.manualVotersTooltip2": "Bitte geben Sie nur diejenigen an, die offline teilgenommen haben.", "app.components.admin.PostManager.PostPreview.OfflineVoteSettings.modifiedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {name}", "app.components.admin.PostManager.PostPreview.assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON> an", "app.components.admin.PostManager.PostPreview.cancelEdit": "Bearbeitung abbrechen", "app.components.admin.PostManager.PostPreview.currentStatus": "Derzeitiger Status ", "app.components.admin.PostManager.PostPreview.delete": "Löschen", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Sind <PERSON> sicher, dass Sie diesen Beitrag löschen wollen? Diese Aktion kann nicht rückgängig gemacht werden.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Möchten Sie diesen Beitrag wirklich löschen? Er wird aus allen Projektphasen unwiderruflich gelöscht.", "app.components.admin.PostManager.PostPreview.edit": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.PostPreview.noOne": "<PERSON>cht zugewiesen", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "Wie oft ein Beitrag in den Warenkorb anderer Teilnehmenden aufgenommen wurde", "app.components.admin.PostManager.PostPreview.picks": "Auswahl: {picks<PERSON><PERSON>ber}", "app.components.admin.PostManager.PostPreview.reactionCounts": "<PERSON><PERSON><PERSON>:", "app.components.admin.PostManager.PostPreview.save": "Speichern", "app.components.admin.PostManager.PostPreview.submitError": "<PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayer": "Feature-<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.addFeatureLayerInstruction": "Kopieren Sie die URL des Feature Layers, der auf ArcGIS Online gehostet wird, und fügen Sie sie hier ein:", "app.components.admin.PostManager.addFeatureLayerTooltip": "Fügen Sie der Karte eine neue Funktionsebene hinzu", "app.components.admin.PostManager.addWebMap": "Webkarte hinzufügen", "app.components.admin.PostManager.addWebMapInstruction": "<PERSON><PERSON><PERSON> Sie die Portal-ID Ihrer Webkarte aus ArcGIS Online und fügen Sie sie hier ein:", "app.components.admin.PostManager.allPhases": "Alle Phasen", "app.components.admin.PostManager.allProjects": "{org<PERSON><PERSON>, select, <PERSON>ü<PERSON> {Alle Bezirke oder Projekte} other {Alle Projekte}}", "app.components.admin.PostManager.allStatuses": "Alle Status", "app.components.admin.PostManager.allTopics": "Alle Themen", "app.components.admin.PostManager.anyAssignment": "<PERSON>e <PERSON>mins", "app.components.admin.PostManager.assignedTo": "<PERSON><PERSON><PERSON><PERSON><PERSON> an {assigneeName}", "app.components.admin.PostManager.assignedToMe": "<PERSON>", "app.components.admin.PostManager.assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON> an", "app.components.admin.PostManager.authenticationError": "<PERSON><PERSON>, diese E<PERSON>e abzu<PERSON>, ist ein Authentifizierungsfehler aufgetreten. Bitte überprüfen Sie die URL und stellen Sie sicher, dass Ihr Esri API-Schlüssel Zugriff auf diese Ebene hat.", "app.components.admin.PostManager.automatedStatusTooltipText": "Dieser Status wird automatisch aktualisiert, wenn die Bedingungen erfüllt sind", "app.components.admin.PostManager.bodyTitle": "Beschreibung", "app.components.admin.PostManager.cancel": "Abbrechen", "app.components.admin.PostManager.cancel2": "Abbrechen", "app.components.admin.PostManager.co-sponsors": "Unterstützer*innen", "app.components.admin.PostManager.comments": "Kommentare", "app.components.admin.PostManager.components.ActionBar.deleteInputsExplanation": "<PERSON> bedeutet, dass Sie alle mit diesen Beiträgen verbundenen Daten, wie Kommentare, Reaktionen und Abstimmungen, verlieren. Diese Aktion kann nicht rückgängig gemacht werden.", "app.components.admin.PostManager.components.ActionBar.deleteInputsTitle": "Sind <PERSON> sicher, dass Sie diese Beiträge löschen möchten?", "app.components.admin.PostManager.components.PostTable.Row.removeTopic": "<PERSON><PERSON> ent<PERSON>nen", "app.components.admin.PostManager.components.PostTable.Row.theIdeaYouAreMoving": "<PERSON><PERSON> ve<PERSON>, diese Idee aus einer Phase zu entfernen, in der Stimmen erhalten wurden. Wenn <PERSON><PERSON> dies tun, gehen diese Stimmen verloren. <PERSON><PERSON> <PERSON><PERSON> sic<PERSON>, dass Sie diese Idee aus dieser Phase entfernen möchten?", "app.components.admin.PostManager.components.PostTable.Row.theVotesAssociated": "Die mit dieser Idee verbundenen Stimmen werden verloren gehen", "app.components.admin.PostManager.components.goToInputManager": "Zum Beitragsmanager", "app.components.admin.PostManager.components.goToProposalManager": "Zum Vorschlagsmanager", "app.components.admin.PostManager.contributionFormTitle": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.cost": "<PERSON><PERSON>", "app.components.admin.PostManager.createInput": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.createInputsDescription": "Eine neue Reihe an Beiträgen aus einem vergangen Projekt erstellen", "app.components.admin.PostManager.currentLat": "Längengrad Mitte", "app.components.admin.PostManager.currentLng": "Längengrad Mitte", "app.components.admin.PostManager.currentZoomLevel": "Zoom-Level", "app.components.admin.PostManager.defaultEsriError": "<PERSON><PERSON>, diese Ebene a<PERSON><PERSON><PERSON>, ist ein Fehler aufgetreten. Bitte überprüfen Sie Ihre Netzwerkverbindung und ob die URL korrekt ist.", "app.components.admin.PostManager.delete": "Löschen", "app.components.admin.PostManager.deleteAllSelectedInputs": "{count} Beiträge löschen", "app.components.admin.PostManager.deleteConfirmation": "Diese Ebene wirklich löschen?", "app.components.admin.PostManager.dislikes": "G<PERSON>ä<PERSON><PERSON> mir nicht", "app.components.admin.PostManager.edit": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.editProjects": "Projekte bearbeiten", "app.components.admin.PostManager.editStatuses": "Status bearbeiten", "app.components.admin.PostManager.editTags": "Tags bearbeiten", "app.components.admin.PostManager.editedPostSave": "Speichern", "app.components.admin.PostManager.esriAddOnFeatureTooltip": "Der Import von Daten aus Esri ArcGIS Online ist eine kostenpflichtige Zusatzfunktion. Sprechen Sie mit Ihrer Kundenbetreuerin, um sie freizuschalten.", "app.components.admin.PostManager.esriSideError": "In der ArcGIS-<PERSON>wendu<PERSON> ist ein Fehler aufgetreten. <PERSON>te warten Si<PERSON> ein paar Minuten und versuchen Sie es später noch einmal.", "app.components.admin.PostManager.esriWebMap": "Esri-Webkar<PERSON>", "app.components.admin.PostManager.exportAllInputs": "Alle Beiträge exportieren (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Alle Kommentare exportieren (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Kommentare für dieses Projekt exportieren (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Beiträge für dieses Projekt exportieren (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Ausgewählte Beiträge exportieren (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Kommentare der ausgewählten Beiträge exportieren (.xslx)", "app.components.admin.PostManager.exportVotesByInput": "Stimmen nach Beitrag exportieren (.xslx)", "app.components.admin.PostManager.exportVotesByUser": "Stimmen nach Nutzer*in exportieren (.xslx)", "app.components.admin.PostManager.exports": "Exportieren", "app.components.admin.PostManager.featureLayerRemoveGeojsonTooltip": "Sie können Kartendaten nur entweder als GeoJSON-Ebenen hochladen oder aus ArcGIS Online importieren. Bitte entfernen Sie alle aktuellen GeoJSON-Layer, wenn Sie einen Feature-Layer hinzufügen möchten.", "app.components.admin.PostManager.featureLayerTooltop": "Die URL des Feature Layers finden Sie auf der rechten Seite der Artikelseite in ArcGIS Online.", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, wie Ihr Name angezeigt werden soll", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Erklären Sie diese Statusänderung", "app.components.admin.PostManager.fileUploadError": "Eine oder mehrere Dateien konnten nicht hoch<PERSON>aden werden. Bitte überprüfen Sie die Dateigröße und das Format und versuchen Sie es erneut.", "app.components.admin.PostManager.formTitle": "<PERSON><PERSON>e bearbeiten", "app.components.admin.PostManager.generalApiError2": "<PERSON><PERSON>, dieses <PERSON>ement abzurufen, ist ein Fehler aufgetreten. Bitte überprüfen Sie, ob die URL oder Portal-ID korrekt ist und Sie Zugriff auf dieses Element haben.", "app.components.admin.PostManager.geojsonRemoveEsriTooltip2": "Sie können nur Kartendaten hochladen, die entweder als GeoJSON-Layer vorliegen oder aus ArcGIS Online importiert wurden. Bitte entfernen Sie alle ArcGIS-Daten, wenn Sie einen GeoJSON-Layer hochladen möchten.", "app.components.admin.PostManager.goToDefaultMapView": "Zum Standard-Kartenzentrum", "app.components.admin.PostManager.hiddenFieldsLink": "ausgeblendete Felder", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/de/articles/7025887-erstellen-eines-externen-umfrageprojekts", "app.components.admin.PostManager.hiddenFieldsTip": "Tipp: <PERSON><PERSON><PERSON> {hiddenFieldsLink} hi<PERSON><PERSON>, wenn Sie Ihre Typeform-Umfrage einrichten, um den Überblick zu behalten, wer auf Ihre Umfrage geantwortet hat.", "app.components.admin.PostManager.import2": "Importieren", "app.components.admin.PostManager.importError": "Die ausgewählte Datei konnte nicht importiert werden, da es sich nicht um eine gültige GeoJSON-Datei handelt", "app.components.admin.PostManager.importEsriFeatureLayer": "Esri Feature Layer importieren", "app.components.admin.PostManager.importEsriWebMap": "Esri-Webkarte importieren", "app.components.admin.PostManager.importInputs": "Beiträge importieren", "app.components.admin.PostManager.imported": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.initiativeFormTitle": "Initiative bearbeiten", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputImportProgress": "{importedCount} aus {totalCount} {totalCount, plural, one {<PERSON><PERSON>rag ist} other {Beiträge sind}} importiert worden. Der Import ist noch im Gange, bitte schauen Sie später wieder vorbei.", "app.components.admin.PostManager.inputManagerHeader": "Beiträge", "app.components.admin.PostManager.inputs": "Beiträge", "app.components.admin.PostManager.inputsExportFileName": "Beiträge", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "<PERSON>ur Beiträge anzeigen, die eine Rückmeldung erfordern", "app.components.admin.PostManager.issueFormTitle": "<PERSON><PERSON> bearbeiten", "app.components.admin.PostManager.latestFeedbackMode": "Verwenden Sie das letzte vorhandene offizielle Update als Erklärung", "app.components.admin.PostManager.layerAdded": "<PERSON><PERSON><PERSON> er<PERSON><PERSON>g<PERSON><PERSON> hinzugefügt", "app.components.admin.PostManager.likes": "Gefällt mir", "app.components.admin.PostManager.loseIdeaPhaseInfoConfirmation": "Wenn Sie diesen Beitrag von ihrem aktuellen Projekt wegbewegen, gehen die Informationen über die ihm zugewiesenen Phasen verloren. Möchten Si<PERSON> fortfahren?", "app.components.admin.PostManager.mapData": "Kartendaten", "app.components.admin.PostManager.multipleInputs": "{ideaCount} Beiträge", "app.components.admin.PostManager.newFeedbackMode": "Schreiben Sie ein neues Update, um diese Änderung zu erklären", "app.components.admin.PostManager.noFilteredResults": "<PERSON> von Ihnen gewählten Filter haben keine Treffer ergeben.\n", "app.components.admin.PostManager.noInputs": "<PERSON>ch keine Beiträge", "app.components.admin.PostManager.noInputsDescription": "Eigene Beiträge hinzufügen oder mit einem früheren Beteiligungsprojekt starten.", "app.components.admin.PostManager.noOfInputsToImport": "{count, plural, =0 {0 Beiträge} one {1 Beitrag} other {# Beiträge}} werden aus dem ausgewählten Projekt und der ausgewählten Phase importiert. Der Import läuft im Hintergrund und die Beiträge werden im Beitragsmanager angezeigt, sobald er abgeschlossen ist.", "app.components.admin.PostManager.noOne": "<PERSON>cht zugewiesen", "app.components.admin.PostManager.noProject": "<PERSON><PERSON>", "app.components.admin.PostManager.officialFeedbackModal.author": "Autor*in", "app.components.admin.PostManager.officialFeedbackModal.authorPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, wie Ihr Name er<PERSON> soll", "app.components.admin.PostManager.officialFeedbackModal.description": "Offizielle Updates he<PERSON><PERSON>, den Prozess transparent zu halten und schafft Vertrauen in die Plattform.", "app.components.admin.PostManager.officialFeedbackModal.emptyAuthorError": "Autor*in ist erforderlich", "app.components.admin.PostManager.officialFeedbackModal.emptyFeedbackError": "Feedback ist er<PERSON><PERSON>lich", "app.components.admin.PostManager.officialFeedbackModal.officialFeedback": "Offizielles Update", "app.components.admin.PostManager.officialFeedbackModal.officialFeedbackPlaceholder": "Erklären Sie den Grund für die Statusänderung", "app.components.admin.PostManager.officialFeedbackModal.postFeedback": "<PERSON><PERSON><PERSON> posten", "app.components.admin.PostManager.officialFeedbackModal.skip": "Dieses Mal überspringen", "app.components.admin.PostManager.officialFeedbackModal.title": "Erklären Sie Ihre Entscheidung", "app.components.admin.PostManager.officialUpdateAuthor": "<PERSON><PERSON><PERSON><PERSON>, wie Ihr Name angezeigt werden soll", "app.components.admin.PostManager.officialUpdateBody": "Erklären Sie diese Statusänderung", "app.components.admin.PostManager.offlinePicks": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.offlineVotes": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.onlineVotes": "Digitale Stimmen", "app.components.admin.PostManager.optionFormTitle": "Option bearbeiten", "app.components.admin.PostManager.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.participatoryBudgettingPicks": "ausgewählt", "app.components.admin.PostManager.participatoryBudgettingPicksOnline": "Digitale Stimmen", "app.components.admin.PostManager.pbItemCountTooltip": "Die Anzahl der Fälle, in denen dieser Beitrag in die partizipativen Haushaltspläne anderer Teilnehmenden aufgenommen wurde.", "app.components.admin.PostManager.petitionFormTitle": "Petition bearbeiten", "app.components.admin.PostManager.postedIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in {projectLink}", "app.components.admin.PostManager.projectFormTitle": "Projekt bearbeiten", "app.components.admin.PostManager.projectsTab": "Projekte", "app.components.admin.PostManager.projectsTabTooltipContent": "Per Drag-and-Drop können Sie Beiträge von einem Projekt in ein anderes verschieben. <PERSON>te beachten Sie, dass Sie den Beitrag bei Zeitplanprojekten nach wie vor zu einer bestimmten Phase hinzufügen müssen.", "app.components.admin.PostManager.proposalFormTitle": "Vorschlag bearbeiten", "app.components.admin.PostManager.proposedBudgetTitle": "Vorgeschlagenes Budget", "app.components.admin.PostManager.publication_date": "Veröffentlicht am", "app.components.admin.PostManager.questionFormTitle": "Frage bearbeiten", "app.components.admin.PostManager.reactions": "Reaktionen", "app.components.admin.PostManager.resetFiltersButton": "<PERSON><PERSON>", "app.components.admin.PostManager.resetInputFiltersDescription": "Setzen Sie die Filter zurück, um alle Beiträge zu sehen.", "app.components.admin.PostManager.saved": "Gespe<PERSON>rt", "app.components.admin.PostManager.screeningTooltip": "Die Vorabprüfung ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder dem Admin, um sie freizuschalten.", "app.components.admin.PostManager.screeningTooltipPhaseDisabled": "Die Vorabprüfung ist für diese Phase ausgeschaltet. Gehen Sie zur Phaseneinrichtung, um sie zu aktivieren", "app.components.admin.PostManager.selectAPhase": "Phase auswählen", "app.components.admin.PostManager.selectAProject": "Projekt auswählen", "app.components.admin.PostManager.setAsDefaultMapView": "Speichern des aktuellen Mittelpunkts und das Zoom-Level als Kartenstandard", "app.components.admin.PostManager.startFromPastInputs": "Mit vergangenen Beiträgen starten", "app.components.admin.PostManager.statusChangeGenericError": "Es ist ein Fehler aufgetreten, bitte versuchen Sie es später noch einmal oder kontaktieren Sie den Support.", "app.components.admin.PostManager.statusChangeSave": "Status ändern", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Ändern Sie den Status eines Beitrags per Drag-and-Drop. Der:die Originalautor:in und andere Mitwirkende werden über den geänderten Status benachrichtigt.\n", "app.components.admin.PostManager.submitApiError": "Es gab ein Problem beim Absenden des Formulars. Bitte prüfen Sie die Fehler und versuchen Sie es erneut.", "app.components.admin.PostManager.timelineTab": "Zeitleiste", "app.components.admin.PostManager.timelineTabTooltipText": "Ziehen Sie Beiträge per Drag & Drop, um sie in verschiedene Projektphasen hineinzukopieren.", "app.components.admin.PostManager.title": "Titel", "app.components.admin.PostManager.topicsTab": "Themen", "app.components.admin.PostManager.topicsTabTooltipText": "Ziehen Sie Beiträge per Drag & Drop in den Tag-Titel, um dem Beitrag einen Tag hinzuzufügen", "app.components.admin.PostManager.view": "Vorschau", "app.components.admin.PostManager.votes": "Stimmen", "app.components.admin.PostManager.votesByInputExportFileName": "stimmen_nach_beitrag", "app.components.admin.PostManager.votesByUserExportFileName": "stimmen_nach_nutzerin", "app.components.admin.PostManager.webMapAlreadyExists": "<PERSON>e können jeweils nur eine Webkarte hinzufügen. Entfernen Sie die aktuelle Karte, um eine andere Karte zu importieren.", "app.components.admin.PostManager.webMapRemoveGeojsonTooltip": "Sie können Kartendaten nur entweder als GeoJSON-Ebenen hochladen oder aus ArcGIS Online importieren. Bitte entfernen Sie alle aktuellen GeoJSON-Ebenen, wenn Sie eine Webkarte hinzufügen möchten.", "app.components.admin.PostManager.webMapTooltip": "Sie finden die Webkarten-Portal-ID auf Ihrer ArcGIS Online-Elementseite auf der rechten Seite.", "app.components.admin.PostManager.xDaysLeft": "{x, plural, =0 {<PERSON><PERSON> als e<PERSON>} one {Ein Tag} other {# Tage}} verbleibend", "app.components.admin.ProjectEdit.survey.cancelDeleteButtonText2": "Abbrechen", "app.components.admin.ProjectEdit.survey.confirmDeleteButtonText2": "<PERSON><PERSON>, Umfrageergebnisse löschen", "app.components.admin.ProjectEdit.survey.deleteResultsInfo2": "Dies kann nicht rückgängig gemacht werden", "app.components.admin.ProjectEdit.survey.deleteSurveyResults2": "Umfrageergebnisse löschen", "app.components.admin.ProjectEdit.survey.downloadResults2": "Umfrageergebnisse herunt<PERSON>laden", "app.components.admin.ReportExportMenu.FileName.fromFilter": "von", "app.components.admin.ReportExportMenu.FileName.groupFilter": "gruppe", "app.components.admin.ReportExportMenu.FileName.projectFilter": "projekt", "app.components.admin.ReportExportMenu.FileName.topicFilter": "<PERSON>a", "app.components.admin.ReportExportMenu.FileName.untilFilter": "bis", "app.components.admin.ReportExportMenu.downloadPng": "Als PNG herunterladen", "app.components.admin.ReportExportMenu.downloadSvg": "Als SVG herunterladen", "app.components.admin.ReportExportMenu.downloadXlsx": "Excel herunterladen", "app.components.admin.SlugInput.regexError": "Der Slug darf nur normale Kleinbuchstaben (a-z), <PERSON><PERSON><PERSON> (0-9) und Bindestriche (-) enthalten. Das erste und das letzte Zeichen dürfen keine Bindestriche sein. Aufeinanderfolgende Bindestriche (--) sind verboten.", "app.components.admin.TerminologyConfig.saveButton": "Speichern", "app.components.admin.commonGroundInputManager.title": "Titel", "app.components.admin.seatSetSuccess.admin": "Verwaltung", "app.components.admin.seatSetSuccess.allDone": "<PERSON><PERSON> er<PERSON>t", "app.components.admin.seatSetSuccess.close": "Schließen", "app.components.admin.seatSetSuccess.manager": "Manager*in", "app.components.admin.seatSetSuccess.orderCompleted": "Bestellung abgeschlossen", "app.components.admin.seatSetSuccess.reflectedMessage": "Die Änderungen an Ihrer Lizenz werden in Ihrem nächsten Abrechnungszyklus berücksichtigt.", "app.components.admin.seatSetSuccess.rightsGranted": "{seatType} <PERSON>chte wurden den ausgewählten Benutzer*innen erteilt.", "app.components.admin.survey.deleteSurveyResultsConfirmation2": "Sind <PERSON> sic<PERSON>, dass Sie alle Umfrageergebnisse löschen möchten?", "app.components.app.containers.AdminPage.ProjectEdit.beta": "Beta", "app.components.app.containers.AdminPage.ProjectEdit.betaTooltip": "Diese Beteiligungsmethode befindet sich in der Beta-Phase. Wir werden sie schrittweise einführen, um Feedback zu sammeln und die Erfahrung zu verbessern.", "app.components.app.containers.AdminPage.ProjectEdit.contactGovSuccessToAccess": "Das Sammeln von Feedback zu einem Dokument ist eine benutzerdefinierte Funktion und nicht in Ihrer aktuellen Lizenz enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Beitrag", "app.components.app.containers.AdminPage.ProjectEdit.expireDateLimitRequired": "Anzahl der Tage ist erforderlich", "app.components.app.containers.AdminPage.ProjectEdit.expireDaysLimit": "<PERSON><PERSON><PERSON> der Tage bis zum Erreichen der Mindestanzahl von Stimmen", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "Weitere Informationen zum Einbetten eines Links für Google Forms finden Sie unter {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/de/articles/7025887-erstellen-eines-externen-umfrageprojekts#h_163e33df30", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "diesen Support-Artikel", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Idee", "app.components.app.containers.AdminPage.ProjectEdit.initiativeTerm": "Initiative", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "Wie soll ein <PERSON>itrag genannt werden?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupport": "<PERSON><PERSON><PERSON> hier den Link zu Ihrem Konveio-Dokument an. Weitere Informationen zum Einrichten von Konveio finden Sie in unserem {supportArticleLink}.", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportArticle": "Support-<PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.konveioSupportPageURL": "https://support.govocal.com/en/articles/7946532-embedding-konveio-pdf-documents-for-collecting-feedback", "app.components.app.containers.AdminPage.ProjectEdit.lockedTooltip": "Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "Ein maximales Budget ist erforderlich", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesPerOptionError": "Die maximale Anzahl der Stimmen pro Option muss kleiner oder gleich der Gesamtzahl der Stimmen sein", "app.components.app.containers.AdminPage.ProjectEdit.maxVotesRequired": "Eine maximale <PERSON><PERSON><PERSON> von Stimmen ist erforderlich", "app.components.app.containers.AdminPage.ProjectEdit.messagingTab": "E-Mails", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "Das Mindestbudget kann nicht größer sein als das Höchstbudget", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "Ein Mindestbudget ist erforderlich", "app.components.app.containers.AdminPage.ProjectEdit.minTotalVotesLargerThanMaxError": "Die Mindestanzahl der Stimmen kann nicht größer sein als die Höchstzahl", "app.components.app.containers.AdminPage.ProjectEdit.minVotesRequired": "<PERSON><PERSON> Mindestanzahl von Stimmen ist erforderlich", "app.components.app.containers.AdminPage.ProjectEdit.missingEndDateError": "<PERSON><PERSON><PERSON><PERSON> Enddatum", "app.components.app.containers.AdminPage.ProjectEdit.missingStartDateError": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Option", "app.components.app.containers.AdminPage.ProjectEdit.optionsPageText2": "Beitragsmanager", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescWihoutPhase": "Konfigurieren Sie die Abstimmungsoptionen auf der Registerkarte Beitragsmanager, nachdem Sie eine Phase erstellt haben.", "app.components.app.containers.AdminPage.ProjectEdit.optionsToVoteOnDescription2": "Konfigurieren Sie die Abstimmungsoptionen im {optionsPageLink}.", "app.components.app.containers.AdminPage.ProjectEdit.participationOptions": "Optionen zur Teilnahme", "app.components.app.containers.AdminPage.ProjectEdit.participationTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.petitionTerm": "Petition", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.adminsAndManagers": "<PERSON><PERSON> und Manager*innen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.annotatingDocument": "<b><PERSON><PERSON><PERSON> kommentieren:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.canParticipateTooltip": "{participants} k<PERSON><PERSON><PERSON> an dieser Phase teilnehmen.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.cancelDeleteButtonText": "Abbrechen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.comment": "<b>Kommentar:</b> {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.commonGroundPhase": "ZusammenFinden-Phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhase": "Phase löschen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseButtonText": "<PERSON><PERSON>, diese Phase löschen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseConfirmationQuestion": "Sind <PERSON> sicher, dass Sie diese Phase löschen wollen?", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.deletePhaseInfo": "<PERSON><PERSON> Daten, die diese Phase betreffen, werden gelöscht. Dies kann nicht rückgängig gemacht werden.", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.documentPhase": "Phase für Annotation von Dokumenten", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.everyone": "Alle", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.externalSurveyPhase": "Phase für externe Umfrage", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.ideationPhase": "Phase für Ideenfindung", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.inPlatformSurveyPhase": "Umfrage-Phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.informationPhase": "Informationsphase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.mixedRights": "Gemischte Rechte", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.noEndDate": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.pollPhase": "Phase für Blitzumfrage", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.proposalsPhase": "Vorschlags-Phase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.react": "<b>Reaktion:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredForEvent": "<b><PERSON><PERSON>r die Veranstaltung angemeldet:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.registeredUsers": "Registrierte Nutzer*innen", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.submitInputs": "<b>Beiträge einreichen:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingPoll": "<b>Blitz-Umfrage:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.takingSurvey": "<b>Erweiterte Umfrage:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.usersWithConfirmedEmail": "Nutzer*innen mit bestätigter E-Mail-Adresse", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteering": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.volunteeringPhase": "Phase um Teilnehmende zu finden", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.voting": "<b>Abstimmung:</b>  {participants}", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.votingPhase": "Abstimmungsphase", "app.components.app.containers.AdminPage.ProjectEdit.phaseHeader.whoCanParticipate": "Wer kann teilnehmen?", "app.components.app.containers.AdminPage.ProjectEdit.prescreeningSubtext": "Eingaben sind erst sichtbar, wenn ein Administrator sie überprüft und genehmigt hat. Autor*innen können Beiträge nicht mehr bearbeiten, nachdem sie geprüft wurden oder auf sie reagiert wurde.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.adminsOnly": "<PERSON><PERSON> <PERSON> Admins", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.anyoneWithLink": "<PERSON><PERSON>, der den Link hat, kann mit dem Projektentwurf interagieren", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approve": "Freigeben", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approveTooltip2": "Mit der Genehmigung können die Projektmanager*innen das Projekt veröffentlichen.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.approvedBy": "Freigegeben von {name}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.editDescription": "Beschreibung bearbeiten", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.everyone": "<PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.groups": "Gruppen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.hidden": "Versteckt", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.offlineVoters": "Analog<PERSON>*innen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.onlyAdminsAndFolderManagersCanPublish2": "Nur Admins{inFolder, select, true { oder die Ordner-Manager*innen} other {}} können das Projekt veröffentlichen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participants": "{participantsCount, plural, one {1 <PERSON><PERSON><PERSON>hmer*in} other {{participantsCount} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.embeddedMethods": "Teilnehmende eingebetteter Methoden (z.B. externe Umfragen)", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.followers": "Projektfollower*innen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.note": "Hinweis: Wen<PERSON> Si<PERSON> anonyme oder offene Teilnahmerechte aktivieren, könne<PERSON> Nutzende mehrfach teilnehmen, was zu irreführenden oder unvollständigen Nutzerdaten führen kann.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsExclusionTitle2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <b>sind nicht enthalten</b>:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.participantsInfoTitle": "<PERSON><PERSON> den Teilnehmenden gehören:", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.registrants": "Teilnehmende der Veranstaltung", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.participantsInfo.users": "<PERSON><PERSON><PERSON><PERSON>, die mit Go Vocal-Methoden interagieren", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApproval": "<PERSON><PERSON> auf Freigabe", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.pendingApprovalTooltip2": "<PERSON><PERSON> wurde zum Freigeben des Projekts benachrichtigt.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.public": "<PERSON><PERSON><PERSON><PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publish": "Veröffentlichen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedActive1": "Veröffentlicht - Aktiv", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.publishedFinished1": "Veröffentlicht - Beendet", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLink": "Link zur Projektvorschau aktualisieren", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.refreshLinkTooltip": "Projektvorschau-Link neu generieren. Dadurch wird der vorherige Link ungültig.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalDescription": "Alte Links werden nicht mehr funktionieren, aber Si<PERSON> können jederzeit einen neuen Link erstellen.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenenrateLinkModalTitle": "Sind Si<PERSON> sicher? Dies wird den aktuellen Link deaktivieren", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateNo": "Abbrechen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.regenerateYes": "<PERSON><PERSON>, Link aktualisieren", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApproval": "Freigabe anfordern", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.requestApprovalDescription2": "Das Projekt muss von einem Admins{inFolder, select, true { oder einem der Ordner-Manager*innen} other {}} freigegeben werden, bevor <PERSON>e es veröffentlichen können. Klicken Sie auf den Button unten, um die Freigabe zu beantragen.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.settings": "Einstellungen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.share": "Teilen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLink": "<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkCopied": "<PERSON>", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareLinkUpsellTooltip": "Die Freigabe privater Links ist in Ihrer aktuellen Lizenz nicht enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin oder einen Admin, um diese Funktion freizuschalten.", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareTitle": "Projekt teilen", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.shareWhoHasAccess": "Wer hat Zugriff", "app.components.app.containers.AdminPage.ProjectEdit.projectHeader.view": "Vorschau", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Projekt", "app.components.app.containers.AdminPage.ProjectEdit.proposalTerm": "Vorschlag", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Frage", "app.components.app.containers.AdminPage.ProjectEdit.reactingThreshold": "Mindestanzahl der zu berücksichtigenden Stimmen", "app.components.app.containers.AdminPage.ProjectEdit.reactingThresholdRequired": "<PERSON><PERSON> Mindestanzahl von Stimmen ist erforderlich", "app.components.app.containers.AdminPage.ProjectEdit.report": "Bericht", "app.components.app.containers.AdminPage.ProjectEdit.screeningText": "Vorabprüfung der Eingaben verlangen", "app.components.app.containers.AdminPage.ProjectEdit.timelineTab": "Zeitleiste", "app.components.app.containers.AdminPage.ProjectEdit.trafficTab": "Daten", "app.components.formBuilder.cancelMethodChange1": "Abbrechen", "app.components.formBuilder.changeMethodWarning1": "Das Ändern der Methoden kann dazu führen, dass alle Eingabedaten ausgeblendet werden, die bei Verwendung der vorherigen Methode generiert oder empfangen wurden.", "app.components.formBuilder.changingMethod1": "Methode ändern", "app.components.formBuilder.confirmMethodChange1": "<PERSON><PERSON>, weiter", "app.components.formBuilder.copySurveyModal.cancel": "Abbrechen", "app.components.formBuilder.copySurveyModal.description": "Dad<PERSON>ch werden alle Fragen und die Logik ohne die Antworten kopiert.", "app.components.formBuilder.copySurveyModal.duplicate": "Duplizieren", "app.components.formBuilder.copySurveyModal.noAppropriatePhases": "<PERSON><PERSON> geeigneten Phasen in diesem Projekt gefunden", "app.components.formBuilder.copySurveyModal.noPhaseSelected": "Keine Phase ausgewählt. Bitte wählen Sie zuerst eine Phase.", "app.components.formBuilder.copySurveyModal.noProject": "<PERSON><PERSON>", "app.components.formBuilder.copySurveyModal.noProjectSelected": "Kein Projekt ausgewählt. Bitte wählen Si<PERSON> zu<PERSON>t ein Projekt aus.", "app.components.formBuilder.copySurveyModal.surveyFormPersistedWarning": "Sie haben bereits Änderungen an dieser Umfrage gespeichert. Wenn Si<PERSON> eine andere Umfrage duplizieren, gehen die Änderungen verloren.", "app.components.formBuilder.copySurveyModal.surveyPhase": "Umfrage-Phase", "app.components.formBuilder.copySurveyModal.title": "<PERSON><PERSON><PERSON>en Sie eine zu duplizierende Umfrage", "app.components.formBuilder.editWarningModal.addOrReorder": "Fragen hinzufügen oder neu anordnen", "app.components.formBuilder.editWarningModal.addOrReorderDescription": "Ihre Antwortdaten sind möglicherweise ungenau", "app.components.formBuilder.editWarningModal.changeQuestionText2": "Text bearbeiten", "app.components.formBuilder.editWarningModal.changeQuestionTextDescription": "Sie korrigieren einen Tippfehler? Das hat keinen Einfluss auf Ihre Antwortdaten", "app.components.formBuilder.editWarningModal.deleteAQuestionDescription": "Sie verlieren die mit dieser Frage verbundenen Antwortdaten", "app.components.formBuilder.editWarningModal.deteleAQuestion": "Eine Frage löschen", "app.components.formBuilder.editWarningModal.exportYouResponses2": "exportieren Sie Ihre Antworten.", "app.components.formBuilder.editWarningModal.loseDataWarning3": "Warnung: Sie könnten die Antwortdaten für immer verlieren. <PERSON>vor <PERSON> fortfahren,", "app.components.formBuilder.editWarningModal.noCancel": "<PERSON><PERSON>, abbrechen", "app.components.formBuilder.editWarningModal.title4": "Live-<PERSON><PERSON><PERSON> bearbeiten", "app.components.formBuilder.editWarningModal.yesContinue": "<PERSON><PERSON>, weiter", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.accessRightsSettings": "Einstellungen der Zugriffsrechte für diese Umfrage", "app.components.formBuilder.nativeSurvey.UserFieldsInFormNotice.fieldsEnabledMessage2": "'Demografische Felder im Umfrageformular' ist aktiviert. Wenn das Umfrageformular angezeigt wird, werden alle konfigurierten demografischen Fragen auf einer neuen Seite unmittelbar vor dem Ende der Umfrage hinzugefügt. Diese Fragen können in der {accessRightsSettingsLink} geändert werden.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.accessRightsSettings": "Einstellungen der Zugriffsrechte für diese Phase.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet1": "Die Umfrageteilnehmenden müssen sich nicht anmelden oder einloggen, um Antworten zu übermitteln, was zu doppelten Übermittlungen führen kann", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneBullet2": "Indem Sie den Schritt der Anmeldung überspringen, erk<PERSON><PERSON><PERSON> Si<PERSON> sich damit ein<PERSON>, dass keine demografischen Daten der Umfrageteilnehmenden erfasst werden, was sich auf Ihre Datenanalyse auswirken kann", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneIntro": "Diese Umfrage ist auf der Registerkarte Zugriffsrechte so eingestellt, dass der Zugriff für \"Jede Person\" möglich ist.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.anyoneOutro2": "<PERSON><PERSON> dies ändern möchten, kö<PERSON><PERSON> dies in der {accessRightsSettingsLink} tun", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsIntro": "Sie stellen die folgenden demografischen Fragen an die Umfrageteilnehmenden, wenn diese sich registrieren/anmelden.", "app.components.formBuilder.nativeSurvey.accessRightsNotice.userFieldsOutro2": "Um die Erfassung demografischer Daten zu vereinfachen und deren Integration in Ihre Nutzer*innendatenbank zu gewährleisten, empfehlen wir Ihnen, alle demografischen Fragen direkt in den Anmeldeprozess zu integrieren. Verwenden Sie dazu bitte die {accessRightsSettingsLink}", "app.components.onboarding.askFollowPreferences": "Nutzer*innen einladen, Gebieten oder Themen zu folgen", "app.components.onboarding.followHelperText": "<PERSON><PERSON><PERSON> wird ein Schritt im Registrierungsprozess aktiviert, bei dem Nutzer*innen den geografischen Gebieten oder Themen folgen können, die sie hier auswählen", "app.components.onboarding.followPreferences": "Folgen-Funktion", "app.components.seatsWithinPlan.seatsExceededPlanText": "{noOfSeatsInPlan} innerhalb der Lizenz, {noOfAdditionalSeats} zu<PERSON><PERSON><PERSON><PERSON>", "app.components.seatsWithinPlan.seatsWithinPlanText": "Zugänge in der Lizenz", "app.containers.Admin.Campaigns.campaignFrom": "Von:", "app.containers.Admin.Campaigns.campaignTo": "An:", "app.containers.Admin.Campaigns.customEmails": "Benutzerdefinierte E-Mails", "app.containers.Admin.Campaigns.customEmailsDescription": "Versenden Sie benutzerdefinierte E-Mails und überprüfen Sie Statistiken.", "app.containers.Admin.Campaigns.noAccess": "Es tut uns leid, aber es scheint, dass Si<PERSON> keinen Zugriff auf den E-Mail-Bereich haben", "app.containers.Admin.Campaigns.tabAutomatedEmails": "Automatisierte E-Mails", "app.containers.Admin.Insights.tabReports": "Berichte", "app.containers.Admin.Invitations.a11y_removeInvite": "Einladung entfernen", "app.containers.Admin.Invitations.addToGroupLabel": "Diese Personen zu bestimmten Nutzer*innengruppen hinzufügen", "app.containers.Admin.Invitations.adminLabel1": "Admin-Zugang gewähren", "app.containers.Admin.Invitations.adminLabelTooltip": "<PERSON>n <PERSON><PERSON> diese Option auswählen, haben die Personen, die <PERSON> e<PERSON>n, <PERSON><PERSON><PERSON> auf alle Ihre Plattformeinstellungen.", "app.containers.Admin.Invitations.configureInvitations": "3. Konfigurieren Sie die Einladungen", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "<PERSON>s gibt keine Einladungen, die Ihren Suchkriterien entsprechen.", "app.containers.Admin.Invitations.deleteInvite": "Löschen", "app.containers.Admin.Invitations.deleteInviteConfirmation": "Sind <PERSON> sicher, dass Sie diese Einladung löschen möchten?", "app.containers.Admin.Invitations.deleteInviteTooltip": "<PERSON>n Sie eine Einladung stornieren, können Sie erneut eine Einladung an diese Personen senden.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Herunterladen und Ausfüllen der Vorlage", "app.containers.Admin.Invitations.downloadTemplate": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.email": "E-Mail", "app.containers.Admin.Invitations.emailListLabel": "<PERSON><PERSON><PERSON> Sie die E-Mail-Adressen der Personen, die Si<PERSON> einladen möchten, manuell ein. Trennen Sie jede Adresse durch ein <PERSON>mma.", "app.containers.Admin.Invitations.exportInvites": "Alle Einladungen exportieren", "app.containers.Admin.Invitations.fileRequirements": "Wichtig: Um die Einladungen korrekt versenden zu können, darf keine Spalte aus der Importvorlage entfernt werden. Lassen Sie unbenutzte Spalten leer.", "app.containers.Admin.Invitations.filetypeError": "Ungültiges Dateiformat. Nur Excel-Dateien (.xslx) werden unterstützt.", "app.containers.Admin.Invitations.groupsPlaceholder": "Keine Gruppe ausgewählt", "app.containers.Admin.Invitations.helmetDescription": "Nutzer*innen auf die Plattform einladen", "app.containers.Admin.Invitations.helmetTitle": "Dashboard für Admin-Einladungen", "app.containers.Admin.Invitations.importOptionsInfo": "Diese Optionen werden nur berücksicht<PERSON>t, wenn sie nicht in der Excel-Datei definiert sind.\n      Bitte besuchen Sie {supportPageLink} für weitere Informationen.", "app.containers.Admin.Invitations.importTab": "E-Mail-Adressen importieren", "app.containers.Admin.Invitations.invitationExpirationWarning": "<PERSON><PERSON>, dass Einladungen nach 30 Tagen ablaufen. Nach Ablauf dieser Frist können Sie sie immer noch erneut versenden.", "app.containers.Admin.Invitations.invitationOptions": "Einladungsoptionen", "app.containers.Admin.Invitations.invitationSubtitle": "Laden Sie Personen zu einem beliebigen Zeitpunkt auf die Plattform ein. Diese erhalten eine neutrale Einladungs-E-Mail mit Ihrem Logo, in der sie aufgefordert werden, sich auf der Plattform zu registrieren.", "app.containers.Admin.Invitations.invitePeople": "Einladungen", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteStatusPending": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Invitations.inviteTextLabel": "<PERSON>eben Sie optional eine Nachricht ein, die der Einladungsmail hinzugefügt werden soll.", "app.containers.Admin.Invitations.invitedSince": "Eingeladen", "app.containers.Admin.Invitations.invitesSupportPageURL": "https://support.govocal.com/articles/1771605", "app.containers.Admin.Invitations.localeLabel": "Wählen Sie die Sprache der Einladung", "app.containers.Admin.Invitations.moderatorLabel": "Projektmanagement-Zugang gewähren", "app.containers.Admin.Invitations.moderatorLabelTooltip": "<PERSON>n <PERSON> diese Option wählen, werden diesen Personen Projektmanagementrechte für das/die ausgewählte(n) Projekt(e) zugewiesen. Weitere Informationen zu Projektmanagementrechten finden Sie {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/articles/2672884", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "hier", "app.containers.Admin.Invitations.name": "Name", "app.containers.Admin.Invitations.processing": "Einladungen werden versandt. Bitte warten...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "<PERSON>ine Projekte ausgewählt", "app.containers.Admin.Invitations.save": "Einladungen verschicken", "app.containers.Admin.Invitations.saveErrorMessage": "Es sind ein oder mehrere Fehler aufgetreten und die Einladungen wurden nicht verschickt. Bitte beheben Si<PERSON> den/die unten aufgeführten Fehler und versuchen Si<PERSON> es erneut.", "app.containers.Admin.Invitations.saveSuccess": "Fertig!", "app.containers.Admin.Invitations.saveSuccessMessage": "Die Einladung(en) wurde(n) erfolgreich versandt.", "app.containers.Admin.Invitations.supportPage": "Support-Seite", "app.containers.Admin.Invitations.supportPageLinkText": "Besuchen Sie die Wissensdatenbank", "app.containers.Admin.Invitations.tabAllInvitations": "Alle Einladungen", "app.containers.Admin.Invitations.tabInviteUsers": "Nutzer*innen einladen", "app.containers.Admin.Invitations.textTab": "<PERSON><PERSON> von E-Mail-Adressen", "app.containers.Admin.Invitations.unknownError": "Etwas ging schief. Bitte versuchen Sie es später nochmal.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Laden Sie Ihre fertige Vorlagendatei hoch", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink}, wenn Sie mehr Informationen über alle unterstützten Spalten in der Importvorlage wünschen.", "app.containers.Admin.Moderation.all": "Alle", "app.containers.Admin.Moderation.belongsTo": "<PERSON><PERSON><PERSON><PERSON> zu", "app.containers.Admin.Moderation.collapse": "Ausblenden", "app.containers.Admin.Moderation.comment": "Kommentar", "app.containers.Admin.Moderation.commentDeletionCancelButton": "Abbrechen", "app.containers.Admin.Moderation.commentDeletionConfirmButton": "Löschen", "app.containers.Admin.Moderation.confirmCommentDeletion": "Sind <PERSON> sicher, dass Sie diesen Kommentar löschen möchten? Dies ist dauerhaft und kann nicht rückgängig gemacht werden.", "app.containers.Admin.Moderation.content": "Inhalt", "app.containers.Admin.Moderation.date": "Datum", "app.containers.Admin.Moderation.deleteComment": "Kommentar löschen", "app.containers.Admin.Moderation.goToComment": "<PERSON>sen Kommentar in einer neuen Registerkarte öffnen", "app.containers.Admin.Moderation.goToPost": "<PERSON>sen Beitrag in einer neuen Registerkarte öffnen", "app.containers.Admin.Moderation.goToProposal": "Diesen Vorschlag in einer neuen Registerkarte öffnen", "app.containers.Admin.Moderation.markFlagsError": "Element(e) nicht markiert. Versuchen Sie es erneut.", "app.containers.Admin.Moderation.markNotSeen": "{selectedItemsCount, plural, one {# Element} other {# Artikel}} als ungesehen markieren", "app.containers.Admin.Moderation.markSeen": "{selectedItemsCount, plural, one {# Element} other {# Artikel}} als g<PERSON><PERSON> markieren", "app.containers.Admin.Moderation.moderationsTooltip": "<PERSON><PERSON> dieser Seite können Sie schnell alle neuen Beiträge überprüfen, die zu Ihrer Plattform hinzugefügt wurden, einsch<PERSON>ßlich Ideen und Kommentare. Sie können Beiträge als \"g<PERSON><PERSON>\" mark<PERSON><PERSON>, damit andere wissen, welche Beiträge noch bearbeitet werden müssen.", "app.containers.Admin.Moderation.noUnviewedItems": "Es gibt keine ungesehenen Elemente", "app.containers.Admin.Moderation.noViewedItems": "<PERSON>s gibt keine gesehenen Elemente", "app.containers.Admin.Moderation.pageTitle1": "Teilnahme-Feed", "app.containers.Admin.Moderation.post": "Beitrag", "app.containers.Admin.Moderation.profanityBlockerSetting": "Schimpfwort-Blocker", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Blockiert automatisch Beiträge, die die am häufigsten gemeldeten beleidigenden Wörter enthalten.", "app.containers.Admin.Moderation.project": "{<PERSON><PERSON><PERSON>, select, <PERSON><PERSON><PERSON> {Bezirke} other {Projekte}}", "app.containers.Admin.Moderation.read": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Moderation.readMore": "<PERSON><PERSON>", "app.containers.Admin.Moderation.removeFlagsError": "Die Warnung(en) konnte(n) nicht entfernt werden. Versuchen Sie es erneut.", "app.containers.Admin.Moderation.rowsPerPage": "Zeilen pro Seite", "app.containers.Admin.Moderation.settings": "Einstellungen", "app.containers.Admin.Moderation.settingsSavingError": "Speichern fehlgeschlagen. Ändern Sie die Einstellung erneut.", "app.containers.Admin.Moderation.show": "Anzeigen", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Einstellungen erfolgreich aktualisiert.", "app.containers.Admin.Moderation.type": "<PERSON><PERSON>", "app.containers.Admin.Moderation.unread": "<PERSON>cht gesehen", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionDescription": "Diese Seite besteht aus den folgenden Abschnitten. Sie können sie ein- und ausschalten und nach Bedarf bearbeiten.", "app.containers.Admin.PagesAndMenu.EditCustomPage.sectionsTitle": "Bereiche", "app.containers.Admin.PagesAndMenu.EditCustomPage.viewPage": "Seite anzeigen", "app.containers.Admin.PagesAndMenu.PageShownBadge.notShownOnPage": "Nicht auf der Seite abgebildet", "app.containers.Admin.PagesAndMenu.PageShownBadge.shownOnPage": "Abgebildet auf der Seite", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSection": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.attachmentsSectionTooltip": "<PERSON><PERSON><PERSON> (max. 50 MB) hinz<PERSON>, die auf der Seite zum Herunterladen zur Verfügung stehen werden.", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSection": "Unterer Infobereich", "app.containers.Admin.PagesAndMenu.SectionToggle.bottomInfoSectionTooltip": "Fügen Sie Ihre eigenen Inhalte in den anpassbaren Bereich unten auf der Seite ein.", "app.containers.Admin.PagesAndMenu.SectionToggle.edit": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsList": "Liste der Veranstaltungen", "app.containers.Admin.PagesAndMenu.SectionToggle.eventsListTooltip2": "Zeigen Sie Veranstaltungen im Zusammenhang mit Projekten an.", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBanner": "Hero Banner", "app.containers.Admin.PagesAndMenu.SectionToggle.heroBannerTooltip": "Passen Sie das Bild und den Text des Seitenbanners an.", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsList": "Projektliste", "app.containers.Admin.PagesAndMenu.SectionToggle.projectsListTooltip": "Zeigen Sie die Projekte basierend auf Ihren Seiteneinstellungen an. Si<PERSON> können sich eine Vorschau der Projekte anzeigen lassen.", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSection": "Oberer Infobereich", "app.containers.Admin.PagesAndMenu.SectionToggle.topInfoSectionTooltip": "Fügen Sie Ihre eigenen Inhalte in den anpassbaren Bereich oben auf der Seite ein.", "app.containers.Admin.PagesAndMenu.addButton": "Zur Nav.-<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.components.NavbarItemForm.navbarItemTitle": "Name in der Navigationsleiste", "app.containers.Admin.PagesAndMenu.components.deletePageConfirmationHidden": "Sind <PERSON> sicher, dass Sie diese Seite löschen möchten? Dies kann nicht rückgängig gemacht werden.", "app.containers.Admin.PagesAndMenu.components.emptyTitleError1": "<PERSON><PERSON><PERSON> Si<PERSON> einen Titel für alle Sprachen an", "app.containers.Admin.PagesAndMenu.components.hiddenFromNavigation": "Andere verfügbare Seiten", "app.containers.Admin.PagesAndMenu.components.savePage": "Seite speichern", "app.containers.Admin.PagesAndMenu.components.saveSuccess": "Seite erfolgreich gespeichert", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.attachmentUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.buttonSuccess": "Gespeichert!", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.contentEditorTitle": "Inhalt", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.error": "Anhänge konnten nicht gespeichert werden", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.fileUploadLabelTooltip": "Die Dateien sollten nicht größer als 50Mb sein. Die hinzugefügten Dateien werden unten auf dieser Seite angezeigt", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.messageSuccess": "Anhänge gespeichert", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageMetaTitle": "Anhänge | {orgName}", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveAndEnableButton": "Anhänge speichern und aktivieren", "app.containers.Admin.PagesAndMenu.containers.AttachmentsSection.saveButton": "Anhänge speichern", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.blankDescriptionError": "<PERSON><PERSON>en Sie Inhalte für alle Sprachen ein", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.buttonSuccess": "Gespeichert!", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.contentEditorTitle": "Inhalt", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.error": "Der untere Infobereich konnte nicht gespeichert werden", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.messageSuccess": "Unterer Infobereich gespeichert", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.pageTitle": "Unterer Infobereich", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveAndEnableButton": "Unteren Infobereich speichern und aktivieren", "app.containers.Admin.PagesAndMenu.containers.BottomInfoSection.saveButton": "Unteren Infobereich speichern", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage..contactGovSuccessToAccessPages": "Die Erstellung benutzerdefinierter Seiten ist nicht in Ihrer aktuellen Lizenz enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.atLeastOneTag": "Bitte wählen Sie mindestens einen Tag aus", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.buttonSuccess": "Gespeichert!", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byAreaFilter": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.byTagsFilter": "<PERSON><PERSON>n-Tag(s)", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contactGovSuccessToAccess1": "Die Anzeige von Projekten nach Thema oder Bereich ist nicht Teil Ihrer aktuellen Lizenz. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.contentEditorTitle": "Inhalt", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.editCustomPagePageTitle": "Benutzerdefinierte Seite bearbeiten", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsLabel": "Verknüpfte Projekte", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.linkedProjectsTooltip": "<PERSON><PERSON><PERSON>en Sie aus, welche Projekte und zugehörigen Veranstaltungen auf der Seite angezeigt werden können.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageCreatedSuccess": "Seite erfolgreich erstellt", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageEditSuccess": "Seite erfolgreich gespeichert", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.messageSuccess": "Benutzerdefinierte Seite gespeichert", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.navbarItemTitle": "Titel in der Navigationsleiste", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPageMetaTitle": "Benutzerdefinierte Seite erstellen | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.newCustomPagePageTitle": "Benutzerdefinierte Seite erstellen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.noFilter": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.pageSettingsTab": "Seiteneinstellungen", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.saveButton": "Benutzerdefinierte Seite speichern", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectAnArea": "<PERSON>te wählen Si<PERSON> ein Gebiet", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedAreasLabel": "Ausgewähltes Gebiet", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.selectedTagsLabel": "Ausgewählte Themen-Tags", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRegexError": "Der Slug darf nur normale Kleinbuchstaben (a-z), <PERSON><PERSON><PERSON> (0-9) und Bindestriche (-) enthalten. Das erste und das letzte Zeichen dürfen keine Bindestriche sein. Aufeinanderfolgende Bindestriche (--) sind verboten.", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.slugRequiredError": "<PERSON><PERSON> müssen einen Slug eingeben", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleLabel": "Titel", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleMultilocError": "<PERSON><PERSON><PERSON> Si<PERSON> einen Titel in jeder Sprache ein", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.titleSinglelocError": "Einen Titel eingeben", "app.containers.Admin.PagesAndMenu.containers.CreateCustomPage.viewCustomPage": "Benutzerdefinierte Seite anzeigen", "app.containers.Admin.PagesAndMenu.containers.CustomPages.Edit.HeroBanner.buttonTitle": "<PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.CustomPages.editCustomPageMetaTitle": "Benutzerdefinierte Seite bearbeiten | {orgName}", "app.containers.Admin.PagesAndMenu.containers.CustomPages.pageContentTab": "Seiteninhalt", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.automatedEmailsLinkText": "automatisierte E-Mails", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.editProject": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.emptyDescriptionWarning1": "Wenn bei einphasigen Projekten das Enddatum leer ist und die Beschreibung nicht ausgefüllt ist, wird auf der Projektseite keine Zeitleiste angezeigt.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noAvailableProjects": "<PERSON>ine verfügbaren Projekte auf der Grundlage Ihrer {pageSettingsLink}.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.noFilter": "Dieses Projekt hat keinen Themen- oder Gebietsfilter, daher werden keine Projekte angezeigt.", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageMetaTitle": "Liste der Projekte | {orgName}", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageSettingsLinkText": "Seiteneinstellungen", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.pageTitle": "Projektliste", "app.containers.Admin.PagesAndMenu.containers.ProjectsList.sectionDescription": "Die folgenden Projekte werden auf dieser Seite auf der Grundlage Ihrer {pageSettingsLink} angezeigt.", "app.containers.Admin.PagesAndMenu.defaultTag": "STANDARD", "app.containers.Admin.PagesAndMenu.deleteButton": "Löschen", "app.containers.Admin.PagesAndMenu.editButton": "<PERSON><PERSON><PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerButtonSuccess": "Gespeichert!", "app.containers.Admin.PagesAndMenu.heroBannerError": "Hero Banner konnte nicht gespeichert werden", "app.containers.Admin.PagesAndMenu.heroBannerMessageSuccess": "<PERSON> <PERSON> g<PERSON>", "app.containers.Admin.PagesAndMenu.heroBannerSaveButton": "<PERSON> <PERSON> s<PERSON>rn", "app.containers.Admin.PagesAndMenu.homeTitle": "Startseite", "app.containers.Admin.PagesAndMenu.missingOneLocaleError": "<PERSON><PERSON>en Sie Inhalte für mindestens eine Sprache ein", "app.containers.Admin.PagesAndMenu.navBarMaxItemsNumber": "Sie können nur bis zu 5 Elemente zur Navigationsleiste hinzufügen.", "app.containers.Admin.PagesAndMenu.pagesMenuMetaTitle": "Seiten & Menü | {orgName}", "app.containers.Admin.PagesAndMenu.removeButton": "Von der Nav.-<PERSON><PERSON><PERSON> entfernen", "app.containers.Admin.PagesAndMenu.saveAndEnableHeroBanner": "Hero-Banner speichern und aktivieren", "app.containers.Admin.PagesAndMenu.title": "Seiten & Menü", "app.containers.Admin.PagesAndMenu.topInfoButtonSuccess": "Gespeichert!", "app.containers.Admin.PagesAndMenu.topInfoContentEditorTitle": "Inhalt", "app.containers.Admin.PagesAndMenu.topInfoError": "Oberer Infobereich konnte nicht gespeichert werden", "app.containers.Admin.PagesAndMenu.topInfoMessageSuccess": "Oberer Infobereich wurde gespeichert", "app.containers.Admin.PagesAndMenu.topInfoMetaTitle": "Top-Info-Bereich | {orgName}", "app.containers.Admin.PagesAndMenu.topInfoPageTitle": "Oberer Infobereich", "app.containers.Admin.PagesAndMenu.topInfoSaveAndEnableButton": "Oberen Infobereich speichern und aktivieren", "app.containers.Admin.PagesAndMenu.topInfoSaveButton": "Oberen Infobereich speichern", "app.containers.Admin.PagesAndMenu.viewButton": "Anzeigen", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.age": "Alter", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.community": "Community", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.executiveSummary": "Zusammenfassung", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicators": "Wer sind eure Nutzer*innen?", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.inclusionIndicatorsDescription": "Im folgenden Abschnitt finden Sie Inklusions-Indikatoren, die Ihren Fortschritt bei der Förderung einer inklusiveren und repräsentativeren Beteiligungsplattform aufzeigen.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicators": "Wer sind eure Besucher*innen?", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.participationIndicatorsDescription": "Der folgende Abschnitt gibt einen Überblick über die wichtigsten Beteiligungsindikatoren für die ausgewählte Zeitspanne und bietet einen Überblick über Beteiligungstrends und Leistungskennzahlen.", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projects": "Projekte", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.projectsPublished": "veröffentlichte Projekte", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.reportTitle": "Plattform-Bericht", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjects": "Eure Projekte", "app.containers.Admin.Reporting.Templates.PlatformReportTemplate.yourProjectsDescription": "Der folgende Abschnitt gibt einen Überblick über die öffentlich sichtbaren Projekte im gewählten Zeitraum, über die in diesen Projekten am häufigsten verwendeten Methoden und über die Metriken, die den Gesamtumfang der Beteiligung betreffen.", "app.containers.Admin.Reporting.Widgets.RegistrationsWidget.registrationsTimeline": "Zeitleiste der Registrierungen", "app.containers.Admin.Users.BlockedUsers.blockedUsers": "Gesperrte <PERSON>utzer*innen", "app.containers.Admin.Users.BlockedUsers.blockedUsersSubtitle": "Verwalten Sie gesperrte Nutzer*innen.", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Gruppe löschen", "app.containers.Admin.Users.GroupsHeader.editGroup": "Gruppe bearbeiten", "app.containers.Admin.Users.GroupsPanel.admins": "Admins", "app.containers.Admin.Users.GroupsPanel.allUsers": "Registrierte Nutzer*innen", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Gruppen", "app.containers.Admin.Users.GroupsPanel.managers": "Projektmanager*innen", "app.containers.Admin.Users.GroupsPanel.seeAssignedItems": "Zugewiesene Elemente", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Verschaffen Sie sich einen Überblick über alle Personen und Organisationen, die sich auf der Plattform registriert haben. Fügen Sie eine Auswahl von Nutzer*innen zu manuellen oder intelligenten Gruppen hinzu.", "app.containers.Admin.Users.UserTableRow.userInvitationPending": "Einladung ausstehend", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.assign": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.assignedItems": "Zugewiesene Elemente an {name}", "app.containers.Admin.Users.buyOneAditionalSeat": "<PERSON>en zusätzlichen Zugang kaufen", "app.containers.Admin.Users.changeUserRights": "Nutzungsrechte ändern", "app.containers.Admin.Users.confirm": "Bestätigen", "app.containers.Admin.Users.confirmAdminQuestion": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass <PERSON> {name} einen Admin-Zugang für die Plattform gewähren wollen?", "app.containers.Admin.Users.confirmNormalUserQuestion": "<PERSON>d <PERSON> sic<PERSON>, dass <PERSON> {name} als normale*n Nutzer*in einrichten wollen?", "app.containers.Admin.Users.confirmSetManagerAsNormalUserQuestion": "Sind <PERSON><PERSON> sic<PERSON>, dass <PERSON><PERSON> {name} als normale*n <PERSON>utzer*in einrichten wollen? Bitte beachten Si<PERSON>, dass die Person nach der Bestätigung Managementrechte für alle Projekte und Ordner verliert, die ihm*ihr zugewiesen sind.", "app.containers.Admin.Users.deleteUser": "Nutzer*in löschen", "app.containers.Admin.Users.email": "E-Mail", "app.containers.Admin.Users.folder": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.folderManager": "<PERSON><PERSON><PERSON>-Manager*in", "app.containers.Admin.Users.helmetDescription": "Nutzer*innenliste", "app.containers.Admin.Users.helmetTitle": "Admin - <PERSON>utzer*innen-Dashboard", "app.containers.Admin.Users.inviteUsers": "Nutzer*innen einladen", "app.containers.Admin.Users.joined": "Beigetreten", "app.containers.Admin.Users.lastActive": "Zuletzt aktiv", "app.containers.Admin.Users.name": "Name", "app.containers.Admin.Users.noAssignedItems": "<PERSON>ine zugewiesenen Elemente", "app.containers.Admin.Users.options": "Optionen", "app.containers.Admin.Users.permissionToBuy": "Um {name} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zu g<PERSON>hren, müssen Si<PERSON> einen zusätzlichen Zugang kaufen.", "app.containers.Admin.Users.platformAdmin": "Plattform-Admin", "app.containers.Admin.Users.projectManager": "Projektmanager*in", "app.containers.Admin.Users.reachedLimitMessage": "Sie haben die Höchstzahl an Zugängen in Ihrer Lizenz erreicht. Ein zusätzlicher Zugang für {name} wird hinzugefügt.", "app.containers.Admin.Users.registeredUser": "Registrierte*r <PERSON><PERSON>*in", "app.containers.Admin.Users.remove": "Entfernen", "app.containers.Admin.Users.removeModeratorFrom": "Der Nutzer / die Nutzerin moderiert den Ordner, zu dem dieses Projekt gehört. Entfernen Sie stattdessen die Zuweisung von \"{folderTitle}\".", "app.containers.Admin.Users.role": "<PERSON><PERSON>", "app.containers.Admin.Users.seeProfile": "<PERSON><PERSON>", "app.containers.Admin.Users.selectPublications": "Projekte oder Ordner auswählen", "app.containers.Admin.Users.selectPublicationsPlaceholder": "Tippen um zu suchen", "app.containers.Admin.Users.setAsAdmin": "Admin-Zugang gewähren", "app.containers.Admin.Users.setAsNormalUser": "Als normale*n <PERSON>utzer*in einstellen", "app.containers.Admin.Users.setAsProjectModerator": "Als Projektmanager*in festlegen", "app.containers.Admin.Users.setUserAsProjectModerator": "<PERSON><PERSON> {name} als Projektmanager*in zu", "app.containers.Admin.Users.userBlockModal.allDone": "<PERSON><PERSON> er<PERSON>t", "app.containers.Admin.Users.userBlockModal.blockAction": "Nutzer*in sperren", "app.containers.Admin.Users.userBlockModal.blockInfo1": "Die veröffentlichten Inhalte dieses/dieser Nutzers*in werden dadurch nicht entfernt. Vergessen Si<PERSON> nicht, die Inhalte bei Bedarf zu moderieren.", "app.containers.Admin.Users.userBlockModal.blocked": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.bocknigInfo1": "Die<PERSON>/diese <PERSON>*in ist seit {from} gesperrt. Die Sperre dauert bis {to}.", "app.containers.Admin.Users.userBlockModal.cancel": "Abbrechen", "app.containers.Admin.Users.userBlockModal.confirmUnblock1": "<PERSON>d <PERSON> sicher, dass <PERSON> {name} entsperren wollen?", "app.containers.Admin.Users.userBlockModal.confirmation1": "{name} ist bis {date} gesperrt.", "app.containers.Admin.Users.userBlockModal.daysBlocked1": "{numberOfDays, plural, one {1 Tag} other {{numberOfDays} Tage}}", "app.containers.Admin.Users.userBlockModal.header": "Nutzer*in sperren", "app.containers.Admin.Users.userBlockModal.reasonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.Users.userBlockModal.reasonLabelTooltip": "Dies wird dem/der gesperrten Nutzer*in mitgeteilt.", "app.containers.Admin.Users.userBlockModal.subtitle1": "Der/Die ausgewählte Nutzer*in kann sich nicht bei der Plattform {daysBlocked} anmelden. Wenn <PERSON>e dies rückgängig machen möchten, können Sie die Sperrung in der Liste der gesperrten Nutzer*innen aufheben.", "app.containers.Admin.Users.userBlockModal.unblockAction": "Nutzer*in entsperren", "app.containers.Admin.Users.userBlockModal.unblockActionConfirmation": "<PERSON><PERSON>, ich möchte diese*n Nutzer*in entsperren", "app.containers.Admin.Users.userDeletionConfirmation": "Diesen User dauerhaft entfernen?", "app.containers.Admin.Users.userDeletionFailed": "Beim Löschen des Users ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut.", "app.containers.Admin.Users.userDeletionProposalVotes": "Dad<PERSON>ch werden auch alle Stimmen dieses Nutzers oder dieser Nutzerin zu Vorschlägen, die noch zur Abstimmung stehen, gel<PERSON>sch<PERSON>.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.userInsights": "Nutzer*innen-Einblicke", "app.containers.Admin.Users.youCantDeleteYourself": "Sie können Ihr eigenes Konto nicht über die Benutzerverwaltungsseite löschen", "app.containers.Admin.Users.youCantUnadminYourself": "Sie können Ihre Rolle als Admin jetzt nicht aufgeben", "app.containers.Admin.communityMonitor.communityMonitorLabel": "Gemeinschaftspuls", "app.containers.Admin.communityMonitor.healthScore": "Bewertung", "app.containers.Admin.communityMonitor.healthScoreDescription": "Dieser Wert ist der Durchschnitt aller Fragen auf der Gefühlsskala, die von den Teilnehmenden für den ausgewählten Zeitraum beantwortet wurden.", "app.containers.Admin.communityMonitor.lastQuarter": "letztes Quartal", "app.containers.Admin.communityMonitor.liveMonitor": "Live-Puls", "app.containers.Admin.communityMonitor.noResults": "<PERSON><PERSON> Ergebnisse für diesen Zeitraum.", "app.containers.Admin.communityMonitor.noSurveyResponses": "<PERSON><PERSON>", "app.containers.Admin.communityMonitor.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.communityMonitor.quarterChartLabel": "Q{quarter} {year}", "app.containers.Admin.communityMonitor.quarterYearCondensed": "Q{quarterNumber} {year}", "app.containers.Admin.communityMonitor.reports": "Berichte", "app.containers.Admin.communityMonitor.settings": "Einstellungen", "app.containers.Admin.communityMonitor.settings.acceptingSubmissions": "Die Gemeinschaftspuls-Umfrage akzeptiert Einreichungen.", "app.containers.Admin.communityMonitor.settings.accessRights2": "Zugriffsrechte", "app.containers.Admin.communityMonitor.settings.afterResidentAction2": "Nachdem Nutzer*innen sich für eine Veranstaltung angemeldet haben, eine Stimme abgegeben haben oder nach dem Absenden einer Umfrage zu einer Projektseite zurückgekehrt sind.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagerTooltip": "Gemeinschaftspuls-Manager*innen können auf alle Gemeinschaftspuls-Einstellungen und Daten zugreifen und diese verwalten.", "app.containers.Admin.communityMonitor.settings.communityMonitorManagers": "Gemeinschaftspuls-Manager*innen", "app.containers.Admin.communityMonitor.settings.communityMonitorManagersTooltip": "Manager*innen können die Gemeinschaftspuls-Umfrage und Berechtigungen bearbeiten, Antwortdaten einsehen und Berichte erstellen.", "app.containers.Admin.communityMonitor.settings.defaultFrequency2": "Der Standardwert für die Frequenz ist 100%.", "app.containers.Admin.communityMonitor.settings.frequencyInputLabel2": "Popup-Häufigkeit (0 bis 100)", "app.containers.Admin.communityMonitor.settings.management2": "Verwaltung", "app.containers.Admin.communityMonitor.settings.popup": "Popup", "app.containers.Admin.communityMonitor.settings.popupDescription3": "In regelmäßigen Abständen wird den Besucher*innen ein Popup-Fenster angezeigt, das sie dazu auffordert, die Gemeinschaftspuls-Umfrage auszufüllen. Sie können die Häufigkeit einstellen, die den Prozentsatz der Besucher*innen bestimmt, die das Popup zufällig sehen, wenn die unten beschriebenen Bedingungen erfüllt sind.", "app.containers.Admin.communityMonitor.settings.popupSettings": "Popup-Einstellungen", "app.containers.Admin.communityMonitor.settings.preview": "Vorschau", "app.containers.Admin.communityMonitor.settings.residentHasFilledOutSurvey2": "Die Person hat die Umfrage in den letzten 3 Monaten noch nicht ausgefüllt.", "app.containers.Admin.communityMonitor.settings.residentNotSeenPopup2": "Die Person hat das Popup in den letzten 3 <PERSON>ten noch nicht gesehen.", "app.containers.Admin.communityMonitor.settings.save": "Speichern", "app.containers.Admin.communityMonitor.settings.saved": "Gespe<PERSON>rt", "app.containers.Admin.communityMonitor.settings.settings": "Einstellungen", "app.containers.Admin.communityMonitor.settings.survey2": "Umfrage", "app.containers.Admin.communityMonitor.settings.surveySettings3": "Allgemeine Einstellungen", "app.containers.Admin.communityMonitor.settings.uponLoadingPage": "<PERSON>im <PERSON> der Homepage oder einer benutzerdefinierten Seite.", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelMain": "Anonymisierung aller Nutzer*innendaten", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelSubtext": "Sämtliche Eingaben der Nutzer*innen im Rahmen der Umfrage werden vor der Speicherung anonymisiert", "app.containers.Admin.communityMonitor.settings.userAnonymityLabelTooltip": "Die Nutzer*innen müssen weiterhin die Teilnahmebedingungen unter \"Zugriffsrechte\" erfüllen. Die Daten des Benutzerprofils sind im Export der Umfragedaten nicht verfügbar.", "app.containers.Admin.communityMonitor.settings.whatConditionsPopup2": "Unter welchen Bedingungen kann das Popup er<PERSON>inen?", "app.containers.Admin.communityMonitor.settings.whoAreManagers": "Wer sind die Manager*innen?", "app.containers.Admin.communityMonitor.totalSurveyResponses": "Umfragebeantwortungen insgesamt", "app.containers.Admin.communityMonitor.upsell.aiSummary": "KI-Zusammenfassung", "app.containers.Admin.communityMonitor.upsell.enableCommunityMonitor": "Gemeinschaftspuls einschalten", "app.containers.Admin.communityMonitor.upsell.featureNotIncluded": "Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.containers.Admin.communityMonitor.upsell.healthScore": "Bewertung", "app.containers.Admin.communityMonitor.upsell.learnMore": "<PERSON><PERSON> er<PERSON>", "app.containers.Admin.communityMonitor.upsell.scoreOverTime": "Ergebnis im Laufe der Zeit", "app.containers.Admin.communityMonitor.upsell.upsellDescription1": "Der Gemeinschaftspuls hilft <PERSON> da<PERSON>, immer einen Schritt voraus zu sein, indem er das Vertrauen der Bewohner*innen, die Zufriedenheit mit den Dienstleistungen und das Leben in der Kommune verfolgt - und zwar kontinuierlich.", "app.containers.Admin.communityMonitor.upsell.upsellDescription2": "Sie erhalten klare Ergebnisse, aussagekräftige Zitate und einen vierteljährlichen Bericht, den Sie mit Kolleg*innen oder gewählten Vertreter*innen teilen können.", "app.containers.Admin.communityMonitor.upsell.upsellDescription3": "<PERSON><PERSON><PERSON> zu lesende Bewertungen, die sich im Laufe der Zeit weiterentwickeln", "app.containers.Admin.communityMonitor.upsell.upsellDescription4": "Die wichtigsten Zitate der Einwohner*innen, zusammengefasst von KI", "app.containers.Admin.communityMonitor.upsell.upsellDescription5": "Auf den Kontext Ihrer Stadt zugeschnittene Fragen", "app.containers.Admin.communityMonitor.upsell.upsellDescription6": "Zufällig ausgewählte Plattform-Besucher*innen", "app.containers.Admin.communityMonitor.upsell.upsellDescription7": "Vierteljährliche PDF-Berichte, die Si<PERSON> teilen können", "app.containers.Admin.communityMonitor.upsell.upsellTitle": "<PERSON><PERSON><PERSON><PERSON>, wie Ihre Bevölkerung denkt, bevor <PERSON>e entstehen", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.count": "<PERSON><PERSON><PERSON>", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.desktop": "Desktop oder Sonstiges", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.mobile": "Mobil", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.tablet": "Tablet", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.title": "Gerätetypen", "app.containers.Admin.dashboard.visitors.DeviceTypesCard.type": "Gerätetyp", "app.containers.Admin.earlyAccessLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.earlyAccessLabelExplanation": "Dies ist eine neue Funktion, die im frühen Zugang verfügbar ist.", "app.containers.Admin.emails.addCampaign": "E-Mail erstellen", "app.containers.Admin.emails.addCampaignTitle": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue E-Mail", "app.containers.Admin.emails.allParticipantsInProject": "Alle Projektteilnehmenden", "app.containers.Admin.emails.allUsers": "Registrierte Nutzer*innen", "app.containers.Admin.emails.automatedEmailCampaignsInfo1": "Automatisierte E-Mails werden automatisch verschickt und durch die Aktionen von Nutzer*innen ausgelöst. Sie können einige von ihnen für alle Nutzer*innen Ihrer Plattform ausschalten. Andere automatisierte E-Mails können nicht deaktiviert werden, da sie für das ordnungsgemäße Funktionieren Ihrer Plattform erforderlich sind.", "app.containers.Admin.emails.automatedEmails": "Automatisierte E-Mails", "app.containers.Admin.emails.automatedEmailsDigest": "Die E-Mail wird nur versendet, wenn Inhalt vorhanden ist", "app.containers.Admin.emails.automatedEmailsRecipients": "Nutzer*innen, die diese E-Mail erhalten werden", "app.containers.Admin.emails.automatedEmailsTriggers": "<PERSON><PERSON><PERSON><PERSON>, das diese E-Mail auslöst", "app.containers.Admin.emails.changeRecipientsButton": "Empfänger*in ändern", "app.containers.Admin.emails.clickOnButtonForExamples": "Klicken Si<PERSON> auf den Button unten, um Beispiele dieser E-Mail in unserer Wissensdatenbank zu sehen.", "app.containers.Admin.emails.confirmSendHeader": "E-Mail an alle Nutzer*innen?", "app.containers.Admin.emails.deleteButtonLabel": "Löschen", "app.containers.Admin.emails.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editCampaignTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editDisabledTooltip2": "Demnächst verfügbar: Diese E-Mail kann derzeit nicht bearbeitet werden.", "app.containers.Admin.emails.editRegion_button_text_multiloc": "Text des Buttons", "app.containers.Admin.emails.editRegion_intro_multiloc": "Einführung", "app.containers.Admin.emails.editRegion_subject_multiloc": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.editRegion_title_multiloc": "Titel", "app.containers.Admin.emails.emptyCampaignsDescription": "Treten Si<PERSON> ganz einfach mit Ihren Teilnehmenden in Kontakt, indem Sie ihnen E-Mails senden. Wählen Sie aus, wen Sie kontaktieren möchten und verfolgen Sie die Ergebnisse.", "app.containers.Admin.emails.emptyCampaignsHeader": "Senden Sie Ihre erste E-Mail", "app.containers.Admin.emails.failed": "Gescheitert", "app.containers.Admin.emails.fieldBody": "Nachricht", "app.containers.Admin.emails.fieldBodyError": "Geben Sie eine E-Mail-Nachricht ein", "app.containers.Admin.emails.fieldReplyTo": "Antworten sollten gehen an", "app.containers.Admin.emails.fieldReplyToEmailError": "Geben Sie eine E-Mail-Adresse im richtigen Format an, <NAME_EMAIL>", "app.containers.Admin.emails.fieldReplyToError": "Geben Sie eine E-Mail-Adresse an", "app.containers.Admin.emails.fieldReplyToTooltip": "<PERSON><PERSON> können w<PERSON>hlen, wohin <PERSON> auf Ihre E-Mail gesendet werden sollen.", "app.containers.Admin.emails.fieldSender": "<PERSON>", "app.containers.Admin.emails.fieldSenderError": "Geben Sie den Absender / die Absenderin der E-Mail an", "app.containers.Admin.emails.fieldSenderTooltip": "<PERSON>e können entscheiden, wer den Empfänger*innen als Absender*in der E-Mail angezeigt wird.", "app.containers.Admin.emails.fieldSubject": "<PERSON><PERSON><PERSON>", "app.containers.Admin.emails.fieldSubjectError": "Geben Sie einen E-Mail-Betreff an", "app.containers.Admin.emails.fieldSubjectTooltip": "Dies wird in der Betreffzeile der E-Mail und in der Posteingangsübersicht der Nutzer*innen angezeigt. <PERSON><PERSON> klar und ansprechend.", "app.containers.Admin.emails.fieldTo": "An", "app.containers.Admin.emails.fieldToTooltip": "<PERSON>e können die Gruppe auswählen, die Ihre E-Mail erhalten sollen", "app.containers.Admin.emails.formSave": "Als Entwurf speichern", "app.containers.Admin.emails.from": "Von:", "app.containers.Admin.emails.groups": "Gruppen", "app.containers.Admin.emails.helmetDescription": "Versenden Sie manuelle E-Mails an Nutzer*innengruppen und aktivieren Sie automatisierte Kampagnen", "app.containers.Admin.emails.nameVariablesInfo2": "<PERSON>e können sich mit den Variablen {firstName} {lastName} direkt an die Bürger*innen wenden. Zum Beispiel: \"Sehr geehrte(r) {firstName} {lastName}, ...\"", "app.containers.Admin.emails.previewSentConfirmation": "Eine Vorschau-E-Mail wurde an Ihre E-Mail-Adresse gesendet", "app.containers.Admin.emails.previewTitle": "Vorschau", "app.containers.Admin.emails.regionMultilocError": "Bitte geben Si<PERSON> einen Wert für alle Sprachen an", "app.containers.Admin.emails.seeEmailHereText": "<PERSON><PERSON><PERSON> eine solche E-Mail verschickt wird, können Sie sie hier abrufen.", "app.containers.Admin.emails.send": "Senden", "app.containers.Admin.emails.sendNowButton": "Jetzt senden", "app.containers.Admin.emails.sendTestEmailButton": "Eine Test-E-Mail senden", "app.containers.Admin.emails.sendTestEmailTooltip": "<PERSON>n <PERSON> auf diesen Link klicken, wird eine Test-E-Mail nur an Ihre E-Mail-Adresse gesendet. So können Sie prüfen, wie die E-Mail in der Realität aussieht.", "app.containers.Admin.emails.senderRecipients": "Absender*innen und Empfänger*innen", "app.containers.Admin.emails.sending": "Senden", "app.containers.Admin.emails.sent": "Gesendet", "app.containers.Admin.emails.sentToUsers": "Dies sind E-Mails, die an Nutzer*innen gesendet werden", "app.containers.Admin.emails.subject": "Betreff:", "app.containers.Admin.emails.supportButtonLabel": "Beispiele finden Sie in unserer Wissensdatenbank", "app.containers.Admin.emails.supportButtonLink2": "https://support.govocal.com/de/articles/7042664-andern-der-einstellungen-fur-die-automatischen-e-mail-benachrichtigungen", "app.containers.Admin.emails.to": "An:", "app.containers.Admin.emails.toAllUsers": "Möchten Sie diese E-Mail an alle registrierten Nutzer*innen senden?", "app.containers.Admin.emails.viewExample": "Vorschau", "app.containers.Admin.ideas.import": "Importieren", "app.containers.Admin.inspirationHub.AllProjects": "Alle Projekte", "app.containers.Admin.inspirationHub.CommunityMonitorSurvey": "Gemeinschaftspuls", "app.containers.Admin.inspirationHub.DocumentAnnotation": "Dokumentanmerkung", "app.containers.Admin.inspirationHub.ExternalSurvey": "Externe Umfrage", "app.containers.Admin.inspirationHub.Filters.Country": "Land", "app.containers.Admin.inspirationHub.Filters.Method": "<PERSON>e", "app.containers.Admin.inspirationHub.Filters.Search": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.Filters.Topic": "<PERSON>a", "app.containers.Admin.inspirationHub.Filters.population": "Bevölkerung", "app.containers.Admin.inspirationHub.Highlighted": "Hervorgehoben", "app.containers.Admin.inspirationHub.Ideation": "Ideenfindung", "app.containers.Admin.inspirationHub.Information": "Informationen", "app.containers.Admin.inspirationHub.PinnedProjects.chooseCountry": "Bitte wählen Si<PERSON> ein Land aus, um angeheftete Projekte anzuzeigen", "app.containers.Admin.inspirationHub.PinnedProjects.country": "Land", "app.containers.Admin.inspirationHub.PinnedProjects.noPinnedProjectsFound": "<PERSON>ine angehefteten Projekte für dieses Land gefunden. Ändern Sie das Land, um angeheftete Projekte für andere Länder zu sehen", "app.containers.Admin.inspirationHub.PinnedProjects.wantToSeeMore": "Ändern Sie das Land, um mehr angeheftete Projekte anzuzeigen", "app.containers.Admin.inspirationHub.Poll": "Blitz-Umfrage", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.openEnded": "Noch nicht abgeschlossen", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.readMore": "Mehr lesen...", "app.containers.Admin.inspirationHub.ProjectDrawer.Phase.title": "Phase {number}: {title}", "app.containers.Admin.inspirationHub.ProjectDrawer.optOutCopy": "<PERSON><PERSON> <PERSON> nicht möchten, dass Ihr Projekt in die Ideenecke aufgenommen wird, wenden <PERSON> sich an Ihre Kundenbetreuerin.", "app.containers.Admin.inspirationHub.Proposals": "Vorschläge", "app.containers.Admin.inspirationHub.SortAndReset.Sort.sortBy": "Sortieren nach", "app.containers.Admin.inspirationHub.SortAndReset.participants_asc": "Teilnehmenden (niedrigste zu<PERSON>t)", "app.containers.Admin.inspirationHub.SortAndReset.participants_desc": "Teilnehmenden (höchste zuerst)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_asc": "Startdatum (ältestes zuerst)", "app.containers.Admin.inspirationHub.SortAndReset.start_at_desc": "Startdatum (neuestes <PERSON>)", "app.containers.Admin.inspirationHub.Survey": "Umfrage", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1": "Kuratierte Liste der besten Projekte rund um den Globus.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2": "Sprechen Sie mit anderen Verwaltungsmitarbeitenden und lernen Si<PERSON> von <PERSON>.", "app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3": "Filtern Sie nach Methode, Stadtgröße und Land.", "app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub": "Ideenecke aktivieren", "app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded": "Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.containers.Admin.inspirationHub.UpsellNudge.learnMore": "<PERSON><PERSON> er<PERSON>", "app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2": "Die Ideenecke verbindet Sie mit einem kuratierten Feed außergewöhnlicher Beteiligungsprojekte auf Go Vocal-Plattformen in der ganzen Welt. Erfahren Si<PERSON>, wie andere Städte erfolgreiche Projekte durchführen und sprechen Sie mit anderen Verwaltungsmitarbeitenden.", "app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle": "Treten Sie einem Netzwerk von Demokratie-Pionier*innen bei", "app.containers.Admin.inspirationHub.Volunteering": "Te<PERSON><PERSON><PERSON><PERSON><PERSON> finden", "app.containers.Admin.inspirationHub.Voting": "Abstimmung", "app.containers.Admin.inspirationHub.commonGround": "ZusammenFinden", "app.containers.Admin.inspirationHub.filters": "Filter", "app.containers.Admin.inspirationHub.resetFilters": "<PERSON><PERSON>", "app.containers.Admin.inspirationHub.seemsLike": "<PERSON><PERSON>, als gäbe es keine Projekte mehr. Versuchen Sie, die {filters} zu ändern.", "app.containers.Admin.messaging.automated.editModalTitle": "Kampagnenfelder bearbeiten", "app.containers.Admin.messaging.automated.variablesToolTip": "Sie können die folgenden Variablen in Ihrer Nachricht verwenden:", "app.containers.Admin.messaging.helmetTitle": "E-Mails", "app.containers.Admin.messaging.newProjectPhaseModal.alternatively": "Alternativ können Sie diese E-Mails für bestimmte Phasen in den Einstellungen der einzelnen Phasen deaktivieren.", "app.containers.Admin.messaging.newProjectPhaseModal.cancel": "Abbrechen", "app.containers.Admin.messaging.newProjectPhaseModal.disabledMessage1": "Dadurch werden auch die E-Mails {emailCampaignName} für alle bestehenden Projektphasen deaktiviert. Sie können diese E-Mails für keine Phase mehr konfigurieren, solange diese Einstellung deaktiviert ist.", "app.containers.Admin.messaging.newProjectPhaseModal.enabledMessage1": "Damit werden die E-Mails {emailCampaignName} nicht automatisch für bestehende Projektphasen aktiviert. Wenn Sie diese Einstellung aktivieren, können Sie diese E-Mails nur für jede Phase konfigurieren.", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOff1": "<PERSON>d <PERSON> sic<PERSON>, dass Sie die Einstellung {emailCampaignName} für E-Mails deaktivieren möchten?", "app.containers.Admin.messaging.newProjectPhaseModal.turnEmailCampaignOn1": "E-Mails „{emailCampaignName}“ aktivieren?", "app.containers.Admin.messaging.newProjectPhaseModal.turnOff": "<PERSON><PERSON>, auss<PERSON><PERSON>", "app.containers.Admin.messaging.newProjectPhaseModal.turnOn": "<PERSON><PERSON>, e<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.FollowedItems.thisWidgetShows": "Dieses Widget zeigt jedem Nutzenden die Projekte an, <b>die er oder sie bevorzugt folgt</b>. <PERSON><PERSON> gehö<PERSON> Projekte, denen sie folgen, so<PERSON>e Projekte, bei denen sie Beiträgen folgen, und Projekte, die sich auf Themen oder Bereiche beziehen, an denen sie interessiert sind.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.noData2": "Dieses Widget wird den Nutzenden nur angezeigt, wenn es Projekte gibt, an denen sie teilnehmen können. Wenn <PERSON><PERSON> diese Meldung sehen, bedeutet das, dass Sie (der Admin) im Moment an keinem Projekt teilnehmen können. Diese Meldung wird auf der eigentlichen Homepage nicht angezeigt.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.openToParticipation": "<PERSON>zt mitmachen", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.thisWidgetWillShowcase": "Dieses Widget zeigt <PERSON>je<PERSON>e an, bei denen Nutzende derzeit <b>eine Aktion durchführen</b> <PERSON><PERSON><PERSON><PERSON> <b>, um sich zu beteiligen</b>.", "app.containers.Admin.pagesAndMenu.ContentBuilder.CraftComponents.OpenToParticipation.title": "Titel", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.defaultTitle": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.noData2": "Dieses Widget wird den Nutzenden nur dann angezeigt, wenn es für sie relevante Projekte gibt, die auf den von ihnen gewählten Einstellungen basieren. Wenn Sie diese Meldung sehen, bedeutet das, dass Sie (der Admin) im Moment keinem Projekt folgen. Diese Meldung wird auf der eigentlichen Homepage nicht angezeigt.", "app.containers.Admin.pagesAndMenu.ContentBuilder.FollowedItems.title": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>, denen man folgt)", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.filterBy": "Filtern nach", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finished": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.finishedAndArchived": "Beendet und archiviert", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.noData": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.thisWidgetShows": "Dieses Widget zeigt <b><PERSON>je<PERSON><PERSON></b> an <b>, die abgeschlossen und/oder archiviert sind.</b>\"Abgeschlossen\" umfasst auch Projekte, die sich in der letzten Phase befinden und bei denen die letzte Phase ein Bericht ist.", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.title": "Abgeschlossene Projekte", "app.containers.Admin.pagesAndMenu.ContentBuilder.Widgets.FinishedOrArchived.youSaidWeDid": "Abgeschlossene Projekte", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyNameErrorText": "<PERSON><PERSON><PERSON> Si<PERSON> einen Namen für alle Sprachen an", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.emptyProjectError": "Das Projekt kann nicht leer sein", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.navbarItemName": "Name", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.project": "Projekt", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.resultingUrl": "Resultierende URL", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.savePage": "Speichern", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.title": "Projekt hinzufügen", "app.containers.Admin.pagesAndMenu.containers.AddProjectModal.warning": "In der Navigationsleiste werden nur Projekte angezeigt, auf die Nutzer*innen Zugriff haben.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTADescription": "<PERSON><PERSON> Widget ist nur auf der Startseite sichtbar, wenn der Gemeinschaftspuls Antworten annimmt.", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorCTATitle2": "Gemeinschaftspuls", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorDescription": "Beschreibung", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorSubmitBtnText": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.communityMonitorTitle": "Titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.important": "Wichtig:", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.CommunityMonitor.sentimentQuestionPreviewAltText": "Beispiel für eine Stimmungsumfrage", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.noEndDate": "<PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.OpenToParticipation.ProjectCarrousel.skipCarrousel": "Drücken Sie die Esc-Taste, um das Karussell zu überspringen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitle1": "Projekte und Ordner (alte Ansicht)", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitleLabel": "Titel der Projekte", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.Projects.projectsTitlePlaceholder": "{orgName} arbeitet derzeit an", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonText": "Text des Buttons", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.buttonTextDefault": "<PERSON>zt mitmachen!", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.description": "Beschreibung", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.folder": "<PERSON><PERSON><PERSON>", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.pleaseSelectAProjectOrFolder": "Bitte wählen Sie ein Projekt oder einen Ordner", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.selectProjectOrFolder": "Projekt oder Ordner auswählen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.showAvatars": "<PERSON><PERSON><PERSON> anzeigen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.spotlight": "Spotlight", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.CraftComponents.SpotlightProject.title": "Titel", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXDays": "Beginnend in {days} Tagen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.startsInXWeeks": "Beginnend in {weeks} W<PERSON>en", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xDaysAgo": "vor {days} Tagen", "app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.ProjectCarrousel.xWeeksAgo": "vor {weeks} W<PERSON>en", "app.containers.Admin.project.Campaigns.campaignFrom": "Von:", "app.containers.Admin.project.Campaigns.campaignTo": "An:", "app.containers.Admin.project.Campaigns.customEmails": "Benutzerdefinierte E-Mails", "app.containers.Admin.project.Campaigns.customEmailsDescription": "Versenden Sie benutzerdefinierte E-Mails und überprüfen Sie Statistiken.", "app.containers.Admin.project.Campaigns.noAccess": "Es tut uns leid, aber es scheint, dass Si<PERSON> keinen Zugriff auf den E-Mail-Bereich haben", "app.containers.Admin.project.emails.addCampaign": "E-Mail erstellen", "app.containers.Admin.project.emails.addCampaignTitle": "Neue Kampagne", "app.containers.Admin.project.emails.allParticipantsAndFollowers": "Alle {participants} und Follower*innen des Projekts", "app.containers.Admin.project.emails.allParticipantsTooltipText2": "Dazu gehören registrierte Nutzende, die irgendeine Aktion im Projekt durchgeführt haben. Nicht registrierte oder anonymisierte Nutzende sind nicht enthalten.", "app.containers.Admin.project.emails.dateSent": "Versanddatum", "app.containers.Admin.project.emails.deleteButtonLabel": "Löschen", "app.containers.Admin.project.emails.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.editCampaignTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.emptyCampaignsDescription": "Treten Si<PERSON> ganz einfach mit Ihren Teilnehmenden in Kontakt, indem Sie ihnen E-Mails senden. Wählen Sie aus, wen Sie kontaktieren möchten und verfolgen Sie die Ergebnisse.", "app.containers.Admin.project.emails.emptyCampaignsHeader": "Senden Sie Ihre erste E-Mail", "app.containers.Admin.project.emails.failed": "Fehlgeschlagen", "app.containers.Admin.project.emails.fieldBody": "E-Mail-Nachricht", "app.containers.Admin.project.emails.fieldBodyError": "Geben Sie eine E-Mail-Nachricht für alle Sprachen an", "app.containers.Admin.project.emails.fieldReplyTo": "Antworten gehen an", "app.containers.Admin.project.emails.fieldReplyToEmailError": "Geben Sie eine E-Mail-Adresse im richtigen Format an, <NAME_EMAIL>", "app.containers.Admin.project.emails.fieldReplyToError": "Geben Sie eine E-Mail-Adresse an", "app.containers.Admin.project.emails.fieldReplyToTooltip": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>, woh<PERSON>*innen auf Ihre E-Mail geschickt werden sollen.", "app.containers.Admin.project.emails.fieldSender": "<PERSON>", "app.containers.Admin.project.emails.fieldSenderError": "Geben Sie den Absender / die Absenderin der E-Mail an", "app.containers.Admin.project.emails.fieldSenderTooltip": "<PERSON><PERSON><PERSON>en Si<PERSON> aus, wen die Nutzer*innen als Absender*in der E-Mail sehen sollen.", "app.containers.Admin.project.emails.fieldSubject": "<PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.fieldSubjectError": "Geben Sie einen E-Mail-Betreff für alle Sprachen an", "app.containers.Admin.project.emails.fieldSubjectTooltip": "Dies wird in der Betreffzeile der E-Mail und in der Übersicht des Posteingangs der Nutzer*innen angezeigt. Machen Sie es klar und ansprechend.", "app.containers.Admin.project.emails.fieldTo": "An", "app.containers.Admin.project.emails.formSave": "Als Entwurf speichern", "app.containers.Admin.project.emails.from": "Von:", "app.containers.Admin.project.emails.helmetDescription": "Versenden Sie manuelle E-Mails an die Projektteilnehmenden", "app.containers.Admin.project.emails.infoboxAdminText": "Auf der Registerkarte Projektnachrichten können Sie nur E-Mails an alle Projektteilnehmende senden. Um E-Mails an andere Teilnehmende oder Teilgruppen von <PERSON>*innen zu senden, klicken Sie auf die Registerkarte {link}.", "app.containers.Admin.project.emails.infoboxLinkText": "Plattform-Nachrichten", "app.containers.Admin.project.emails.infoboxModeratorText": "Über die Registerkarte „Projektnachrichten“ können Sie nur allen Projektteilnehmenden E-Mails senden. Admins können über die Registerkarte „Plattformnachrichten“ E-Mails an andere Teilnehmende oder Benutzergruppen senden.", "app.containers.Admin.project.emails.message": "Nachricht", "app.containers.Admin.project.emails.nameVariablesInfo2": "<PERSON>e können sich mit den Variablen {firstName} {lastName} direkt an die Bürger*innen wenden. Zum Beispiel: \"Sehr geehrte(r) {firstName} {lastName}, ...\"", "app.containers.Admin.project.emails.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.project.emails.previewSentConfirmation": "Eine Vorschau-E-Mail wurde an Ihre E-Mail-Adresse gesendet", "app.containers.Admin.project.emails.previewTitle": "Vorschau", "app.containers.Admin.project.emails.projectParticipants": "Projektteilnehmende", "app.containers.Admin.project.emails.recipients": "Empfänger*innen", "app.containers.Admin.project.emails.send": "Senden", "app.containers.Admin.project.emails.sendTestEmailButton": "<PERSON><PERSON> Vorschau senden", "app.containers.Admin.project.emails.sendTestEmailTooltip": "Senden Sie diesen E-Mail-Entwurf an die E-Mail-Adresse, mit der Si<PERSON> angemeldet sind, um zu prüfen, wie diese wirklich aussieht.", "app.containers.Admin.project.emails.senderRecipients": "Absender*innen und Empfänger*innen", "app.containers.Admin.project.emails.sending": "Senden", "app.containers.Admin.project.emails.sent": "Gesendet", "app.containers.Admin.project.emails.sentToUsers": "Dies sind E-Mails, die an Nutzer*innen gesendet werden", "app.containers.Admin.project.emails.status": "Status", "app.containers.Admin.project.emails.subject": "Betreff:", "app.containers.Admin.project.emails.to": "An:", "app.containers.Admin.project.messaging.helmetTitle": "E-Mails", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.folderCardImageTooltip1": "Dieses Bild ist Teil der Ordnerkarte; die Karte, die den Ordner zusammenfasst und z. B. auf der Homepage angezeigt wird. Weitere Informationen zu empfohlenen Bildauflösungen finden Sie in {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.headerImageTooltip1": "Dieses Bild wird oben auf der Ordnerseite angezeigt. Weitere Informationen zu empfohlenen Bildauflösungen finden Sie in {supportPageLink}.", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.imageSupportPageURL": "https://support.govocal.com/de/articles/1346397-welche-dimensionen-und-grossen-sind-fur-bilder-auf-der-plattform-empfohlen", "app.containers.Admin.projectFolders.containers.settings.ProjectFolderForm.supportPageLinkText": "unserer Wissensdatenbank", "app.containers.Admin.projects.all.askPersonalData3": "Felder für Name und E-Mail hinzufügen", "app.containers.Admin.projects.all.clickExportToPDFIdeaForm2": "Alle Fragen werden in der PDF-Datei angezeigt. Die folgenden Punkte werden jedoch derzeit nicht für den Import über Papier-Scans unterstützt: Bilder, Tags und Datei-Upload.", "app.containers.Admin.projects.all.clickExportToPDFSurvey6": "Alle Fragen werden in der PDF-Datei angezeigt. Allerdings werden die folgenden Fragen derzeit nicht für den Import über Papier-Scans unterstützt: Mapping-Fragen (<PERSON><PERSON>, <PERSON> zei<PERSON> und Bereich zeichnen), Ranking-Fragen, Matrix-Fragen und Dateiupload-Fragen.", "app.containers.Admin.projects.all.collapsibleInstructionsEndTitle": "Ende des Formulars", "app.containers.Admin.projects.all.collapsibleInstructionsStartTitle": "Beginn des Formulars", "app.containers.Admin.projects.all.components.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.components.manageButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.copyProjectButton": "Projekt kopieren", "app.containers.Admin.projects.all.copyProjectError": "<PERSON><PERSON> dieses Projekts ist ein Fehler aufgetreten. Bitte versuchen Sie es später noch einmal.", "app.containers.Admin.projects.all.customiseEnd": "Passen Sie das Ende des Formulars an.", "app.containers.Admin.projects.all.customiseStart": "Passen Sie den Anfang des Formulars an.", "app.containers.Admin.projects.all.deleteFolderButton1": "Ordner löschen", "app.containers.Admin.projects.all.deleteFolderConfirm": "Sind <PERSON> sicher, dass Sie diesen Ordner löschen möchten? Alle Projekte innerhalb des Ordners werden ebenfalls gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.Admin.projects.all.deleteFolderError": "<PERSON>s gab ein Problem beim Entfer<PERSON> dieses Ordners. Bitte versuchen Sie es erneut.", "app.containers.Admin.projects.all.deleteProjectButtonFull": "Projekt löschen", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Sind Sie sich sicher, dass Sie dieses Projekt löschen möchten? Dieser Schritt kann nicht rückgängig gemacht werden.", "app.containers.Admin.projects.all.deleteProjectError": "Es gab einen Fehler beim Löschen dieses Projektes, bitte versuchen Si<PERSON> es später noch einmal.", "app.containers.Admin.projects.all.exportAsPDF1": "PDF-<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.itIsAlsoPossible1": "Sie können Online- und Offline-Antworten kombinieren. Um Offline-Antworten hochzuladen, gehen Sie in diesem Projekt auf die Registerkarte \"Beitragsmanager\" und klicken Sie auf \"Importieren\".", "app.containers.Admin.projects.all.itIsAlsoPossibleSurvey1": "Sie können Online- und Offline-Antworten kombinieren. Um Offline-Antworten hochzuladen, gehen Sie zur Registerkarte „Umfrage“ dieses Projekts und klicken Sie auf „Importieren“.", "app.containers.Admin.projects.all.logicNotInPDF": "Die Logik der Umfrage wird in der heruntergeladenen PDF-Datei nicht berücksichtigt. Befragte sehen auf Papier alle Fragen der Umfrage.", "app.containers.Admin.projects.all.new.Folders.Filters.search": "<PERSON><PERSON>er suchen", "app.containers.Admin.projects.all.new.Folders.Table.allFoldersHaveLoaded": "Alle Ordner wurden geladen", "app.containers.Admin.projects.all.new.Folders.Table.folder": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Folders.Table.managers": "Manager*innen", "app.containers.Admin.projects.all.new.Folders.Table.numberOfProjects": "{numberOfProjects, plural, one {~ Projekt} other {~ Projekte}}", "app.containers.Admin.projects.all.new.Folders.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate": "Datum des Projektbeginns", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel": "Auffindbarkeit", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden": "Versteckt", "app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod": "Filtern nach der Methode der aktuellen Phasenbeteiligung", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation": "Ideenfindung", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information": "Informationen", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label": "Beteiligungsmethode", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation": "Dokumentanmerkung", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround": "ZusammenFinden", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey": "Umfrage", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll": "Blitz-Umfrage", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals": "Vorschläge", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering": "Te<PERSON><PERSON><PERSON><PERSON><PERSON> finden", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting": "Abstimmung", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData": "Offene Beteiligung", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing": "Informieren", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted": "Noch nicht gestartet", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates": "Beteiligungsstatus", "app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past": "Abgeschlossen", "app.containers.Admin.projects.all.new.Projects.Filters.Search.search": "<PERSON>jekt suchen", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc": "Alphabetisch (A-Z)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc": "Alphabetisch (Z-A)", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager": "Manager*in", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon": "Phase beginnt oder endet bald", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created": "<PERSON><PERSON><PERSON><PERSON> erstellt", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed": "<PERSON><PERSON><PERSON><PERSON> angesehen", "app.containers.Admin.projects.all.new.Projects.Filters.Sort.status": "Status", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups": "Gruppen", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label": "Sichtbarkeit", "app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.addFilter": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Filters.clear": "Entfernen", "app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters": "<PERSON><PERSON> weiteren Filter zum Hinzufügen", "app.containers.Admin.projects.all.new.Projects.Table.admins": "Admins", "app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded": "Alle Projekte wurden geladen", "app.containers.Admin.projects.all.new.Projects.Table.anyone": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.currentPhase": "Aktuelle Phase", "app.containers.Admin.projects.all.new.Projects.Table.daysLeft": "noch {days} Tage", "app.containers.Admin.projects.all.new.Projects.Table.daysToStart": "{days} Tage bis zum Start", "app.containers.Admin.projects.all.new.Projects.Table.discoverability": "Auffindbarkeit:", "app.containers.Admin.projects.all.new.Projects.Table.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.end": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.ended": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.endsToday": "<PERSON>et heute", "app.containers.Admin.projects.all.new.Projects.Table.groups": "Gruppen", "app.containers.Admin.projects.all.new.Projects.Table.hidden": "Versteckt", "app.containers.Admin.projects.all.new.Projects.Table.loadingMore": "<PERSON><PERSON> laden…", "app.containers.Admin.projects.all.new.Projects.Table.manager": "Manager*in", "app.containers.Admin.projects.all.new.Projects.Table.monthsLeft": "noch {months} Monate", "app.containers.Admin.projects.all.new.Projects.Table.monthsToStart": "noch {months} bis zum Start", "app.containers.Admin.projects.all.new.Projects.Table.nextPhase": "Nächste Phase:", "app.containers.Admin.projects.all.new.Projects.Table.notAssigned": "<PERSON>cht zu<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.phase": "Phase", "app.containers.Admin.projects.all.new.Projects.Table.preLaunch": "<PERSON><PERSON> <PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.project": "Projekt", "app.containers.Admin.projects.all.new.Projects.Table.public": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore": "Scrollen Si<PERSON> nach unten, um mehr zu laden", "app.containers.Admin.projects.all.new.Projects.Table.start": "Begin<PERSON>", "app.containers.Admin.projects.all.new.Projects.Table.status": "Status", "app.containers.Admin.projects.all.new.Projects.Table.statusColon": "Status:", "app.containers.Admin.projects.all.new.Projects.Table.visibility": "Sichtbarkeit", "app.containers.Admin.projects.all.new.Projects.Table.visibilityColon": "Sichtbarkeit:", "app.containers.Admin.projects.all.new.Projects.Table.xGroups": "{numberOfGroups} Gruppen", "app.containers.Admin.projects.all.new.Projects.Table.xManagers": "{numberOfManagers} Manager*innen", "app.containers.Admin.projects.all.new.Projects.Table.yearsLeft": "noch {years} Jahre", "app.containers.Admin.projects.all.new.Projects.Table.yearsToStart": "noch {years} Jahre bis zum Start", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.currentPhase": "Aktuelle Phase: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.daysLeft": "{days} verbleibende Tage", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.folder": "Ordner: {folderName}", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noCurrentPhase": "Keine aktuelle Phase", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noEndDate": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.noPhases": "<PERSON><PERSON>", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListItem": "Phase {number}: {phaseName} ({participationMethod})", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.phaseListTitle": "Phasen:", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.project": "Projekt", "app.containers.Admin.projects.all.new.Timeline.ProjectGanttChart.startDate": "Startdatum: {date}", "app.containers.Admin.projects.all.new.folders": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.all.new.ordering": "Sortierung", "app.containers.Admin.projects.all.new.projects": "Projekte", "app.containers.Admin.projects.all.new.timeline": "Zeitleiste", "app.containers.Admin.projects.all.new.timeline.failedToLoadTimelineError": "Die Zeitleiste konnte nicht geladen werden.", "app.containers.Admin.projects.all.new.timeline.noEndDay": "Das Projekt hat kein End<PERSON>tum", "app.containers.Admin.projects.all.new.timeline.project": "Projekt", "app.containers.Admin.projects.all.notes": "Anmerkungen", "app.containers.Admin.projects.all.personalDataExplanation5": "<PERSON>t dieser Option werden die Felder Vorname, Nachname und E-Mail in die exportierte PDF-Datei eingefügt. Beim <PERSON>n des Papierformulars verwenden wir diese Daten, um automatisch ein Konto für den Offline-Umfrageteilnehmenden zu erstellen.", "app.containers.Admin.projects.project.analysis.Comments.aiSummary": "KI-Zusammenfassung", "app.containers.Admin.projects.project.analysis.Comments.comments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.analysis.Comments.commentsSummaryVisibilityExplanation": "Die Zusammenfassung der Kommentare ist verfügbar, wenn es 5 oder mehr Kommentare gibt.", "app.containers.Admin.projects.project.analysis.Comments.generateSummary": "<PERSON><PERSON><PERSON><PERSON> zu<PERSON>", "app.containers.Admin.projects.project.analysis.Comments.refreshSummary": "{count, plural, =0 {Aktualisieren} =1 {1 neuer Kommentar} other {# neue Kommentare}}", "app.containers.Admin.projects.project.analysis.aiSummary": "KI-Zusammenfassung", "app.containers.Admin.projects.project.analysis.aiSummaryTooltipText": "Dies ist ein KI-generierter Inhalt. Dieser ist möglicherweise nicht 100%ig genau. Bitte überprüfen Sie die Genauigkeit und gleichen Sie sie mit den tatsächlichen Beiträgen ab. <PERSON><PERSON>, dass sich die Genauigkeit wahrscheinlich verbessert, wenn die Anzahl der ausgewählten Beiträge reduziert wird.", "app.containers.Admin.projects.project.general.components.UnlistedInput.emailNotifications": "E-Mail-Benachrichtigungen werden nur an Teilnehmende gesendet", "app.containers.Admin.projects.project.general.components.UnlistedInput.hidden": "Versteckt", "app.containers.Admin.projects.project.general.components.UnlistedInput.notIndexed": "<PERSON><PERSON> von Suchmaschinen indiziert", "app.containers.Admin.projects.project.general.components.UnlistedInput.notVisible": "Nicht sichtbar auf der Homepage oder den Widgets", "app.containers.Admin.projects.project.general.components.UnlistedInput.onlyAccessible": "Nur über direkte URL zugänglich", "app.containers.Admin.projects.project.general.components.UnlistedInput.public": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.general.components.UnlistedInput.selectHowDiscoverableProjectIs": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> au<PERSON>, wie auffindbar dieses Projekt ist.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectIsVisibleToEveryone": "Dieses Projekt ist für jeden sichtbar, der Zugang hat, und erscheint auf der Startseite und in den Widgets.", "app.containers.Admin.projects.project.general.components.UnlistedInput.thisProjectWillBeHidden": "Dieses Projekt wird für die breite Öffentlichkeit verborgen bleiben und nur für diejenigen sichtbar sein, die den Link haben.", "app.containers.Admin.projects.project.general.components.UnlistedInput.whoCanFindThisProject": "Wer kann dieses Projekt finden?", "app.containers.Admin.projects.project.ideas.analysisAction1": "Zur KI-Analyse", "app.containers.Admin.projects.project.ideas.analysisText2": "Erkunden Sie KI-gestützte Zusammenfassungen und sehen Si<PERSON> sich einzelne Beiträge an.", "app.containers.Admin.projects.project.ideas.importInputs": "Importieren", "app.containers.Admin.projects.project.information.ReportTab.afterCreating": "Nachdem Si<PERSON> einen Bericht erstellt haben, können Sie ihn für die Öffentlichkeit freigeben, sobald die Phase beginnt.", "app.containers.Admin.projects.project.information.ReportTab.createAMoreComplex": "<PERSON><PERSON><PERSON>n Si<PERSON> eine komplexere Seite für den Informationsaustausch", "app.containers.Admin.projects.project.information.ReportTab.createAReportTo": "<PERSON>rstellen Si<PERSON> einen Bericht um:", "app.containers.Admin.projects.project.information.ReportTab.createReport": "Bericht erstellen", "app.containers.Admin.projects.project.information.ReportTab.modalDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen Bericht für eine vergangene Phase oder beginnen Sie von Grund auf neu.", "app.containers.Admin.projects.project.information.ReportTab.notVisibleNotStarted1": "<PERSON>ser Bericht ist nicht öffentlich. Um ihn öffentlich zu machen, aktivieren Sie den Schalter \"Sichtbar\".", "app.containers.Admin.projects.project.information.ReportTab.notVisibleStarted1": "Diese Phase hat begonnen, aber der Bericht ist noch nicht öffentlich. Um ihn öffentlich zu machen, aktivieren Sie den Schalter \"Sichtbar\".", "app.containers.Admin.projects.project.information.ReportTab.phaseTemplate": "Mit einer Projektvorlage beginnen", "app.containers.Admin.projects.project.information.ReportTab.report": "Bericht", "app.containers.Admin.projects.project.information.ReportTab.shareResults": "Teilen Sie die Ergebnisse einer vergangenen Umfrage oder Ideenfindung", "app.containers.Admin.projects.project.information.ReportTab.visible": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.information.ReportTab.visibleNotStarted1": "Dieser Bericht wird ver<PERSON>ffent<PERSON>, sobald die Phase beginnt. Um ihn nicht zu veröffentlichen, deaktivieren Sie den Schalter \"Sichtbar\".", "app.containers.Admin.projects.project.information.ReportTab.visibleStarted1": "Dieser Bericht ist derzeit öffentlich. Um ihn nicht zu veröffentlichen, deaktivieren Sie den Schalter \"Sichtbar\".", "app.containers.Admin.projects.project.information.areYouSureYouWantToDelete": "Sind <PERSON> sicher, dass Sie diesen Beitrag löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.addToPhase": "Zur Phase hinzufügen", "app.containers.Admin.projects.project.offlineInputs.ImportModal.consentNeeded": "<PERSON><PERSON> müssen zustim<PERSON>, bevor <PERSON> fortfahren können", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formCanBeDownloadedHere": "Das Formular kann hier heruntergeladen werden.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formHasPersonalData": "Das hochgeladene Formular wurde mit dem Abschnitt \"Persönliche Daten\" erstellt", "app.containers.Admin.projects.project.offlineInputs.ImportModal.formLanguage": "Sprache des Formulars", "app.containers.Admin.projects.project.offlineInputs.ImportModal.googleConsent2": "Hiermit stimme ich der Verarbeitung dieser Datei via Google Cloud Form Parser zu", "app.containers.Admin.projects.project.offlineInputs.ImportModal.importExcelFileTitle": "Excel-Datei importieren", "app.containers.Admin.projects.project.offlineInputs.ImportModal.pleaseUploadFile": "Bitte laden Si<PERSON> eine Date<PERSON> hoch, um fortzufahren", "app.containers.Admin.projects.project.offlineInputs.ImportModal.templateCanBeDownloadedHere": "Die Vorlage kann hier heruntergeladen werden.", "app.containers.Admin.projects.project.offlineInputs.ImportModal.upload": "Hochladen", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadExcelFile": "Laden Sie eine vollständige <b>Excel-Datei</b> (.xlsx) hoch. Sie muss die für dieses Projekt vorgesehene Vorlage verwenden. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ImportModal.uploadPdfFile1": "Laden Sie eine <b>PDF-Datei mit gescannten Formularen</b> hoch. Sie müssen ein in dieser Phase gedrucktes Formular verwenden. {hereLink}", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.addANewUser2": "Verwenden Sie diese E-Mail für den neuen Nutzer / die neue Nutzerin", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.AuthorInput.enterAValidEmail": "<PERSON><PERSON><PERSON> Si<PERSON> eine gültige E-Mail ein, um ein neues Konto zu erstellen", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.aNewAccountWillBeCreated2": "<PERSON><PERSON><PERSON> den Autor / die Autorin wird ein neues Konto mit diesen Angaben erstellt. Dieser Beitrag wird dem Konto hinzugefügt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.firstName": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.lastName": "Nachname", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.pleaseEnterValidUser": "Bitte geben Sie eine E-Mail-Adresse und/oder einen Vor- und Nachnamen ein, um diesen Beitrag einem Autor / einer Autorin zuzuordnen. Oder deaktivieren Sie das Zustimmungsfeld.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.thereIsAlreadyAnAccount": "Es gibt bereits ein Konto, das mit dieser E-Mail verknüpft ist. Diese Eingabe wird zu diesem hinzugefügt.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.IdeaEditor.userConsent": "Zustimmung des/der Nutzer*in (Nutzer*innenkonto erstellen)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approve": "Freigeben", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.approveAllInputs": "Alle Beiträge freigeben", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.author": "Autor*in:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.email": "E-Mail-Adresse:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.errorImportingLabel": "Während des Imports sind Fehler aufgetreten und einige Beiträge wurden nicht importiert. Bitte beheben Sie die Fehler und importieren Sie die fehlenden Beiträge erneut.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.formDataNotValid": "Ungültige Formulardaten. Überprüfen Sie das Formular oben auf Fehler.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importExcelFile": "Excel-Datei importieren (.xlsx)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importFile2": "Importieren", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFile": "Gescannte Formulare importieren (.pdf)", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importPDFFileTitle": "Gescannte Formulare importieren", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importedInputs": "Importierte Beiträge", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.importing2": "Importieren. Dieser Vorgang kann ein paar Minuten dauern.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputImportedAnonymously": "Dieser Beitrag wurde anonym importiert.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsImported": "{numIdeas} Beiträge wurden importiert und erfordern eine Freigabe.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.inputsNotApproved2": "{numNotApproved} Beiträge konnten nicht freigegeben werden. Bitte überprüfen Sie jeden Beitrag auf Validierungsprobleme und geben Sie sie einzeln frei.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.locale": "Sprache:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYet3": "Es gibt noch nichts zu überprüfen. Klicken Sie auf \"{importFile}\", um eine PDF-Datei mit gescannten Eingabeformularen oder eine Excel-Datei mit Beiträgen zu importieren.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noIdeasYetNoPdf": "Noch nichts zu überprüfen. Klicken Sie auf \"{importFile}\", um eine Excel-Datei mit Beiträgen zu importieren.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.noTitleInputLabel": "Beitrag", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.page": "Seite", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.pdfNotAvailable": "Die importierte Datei kann nicht angezeigt werden. Die Anzeige importierter Dateien ist nur für PDF-Importe verfügbar.", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phase": "Phase:", "app.containers.Admin.projects.project.offlineInputs.ReviewSection.phaseNotAllowed2": "Die ausgewählte Phase kann keine Beiträge enthalten. Bitte wählen Si<PERSON> eine andere.", "app.containers.Admin.projects.project.offlineInputs.TopBar.noPhasesInProject": "Dieses Projekt enthält keine Phasen, die Beitrage enthalten können.", "app.containers.Admin.projects.project.offlineInputs.TopBar.selectAPhase": "Bitte wählen Si<PERSON> aus, zu welcher Phase Sie diese Beiträge hinzufügen möchten.", "app.containers.Admin.projects.project.offlineInputs.inputImporter": "Eingabe-Importeur", "app.containers.Admin.projects.project.participation.comments": "Kommentare", "app.containers.Admin.projects.project.participation.inputs": "Alle Beiträge", "app.containers.Admin.projects.project.participation.participantsTimeline": "Zeitleiste der Teilnehmenden", "app.containers.Admin.projects.project.participation.reactions": "Reaktionen", "app.containers.Admin.projects.project.participation.selectPeriod": "Zeitraum auswählen", "app.containers.Admin.projects.project.participation.usersByAge": "Nutzer*innen nach Alter", "app.containers.Admin.projects.project.participation.usersByGender": "Nutzer*innen nach Geschlecht", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.EmailModal.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.addAQuestion": "Frage hinzufügen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.contactGovSuccessToAccessAddingAQuestion": "Die Möglichkeit, Nutzer*innenfelder auf Phasenebene hinzuzufügen oder zu bearbeiten, ist in Ihrer aktuellen Lizenz nicht enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.customFieldNameOptions": "{customFieldName} Optionen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.fieldsShownInSurveyForm": "Diese Fragen werden als letzte Seite des Umfrageformulars hinzugefügt, da in den Phaseneinstellungen „Felder in Umfrage anzeigen?“ ausgewählt wurde.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.noExtraQuestions": "Es werden keine zusätzlichen Fragen gestellt.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optional": "Optional", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.optionalGroup1": "Optional - im<PERSON> aktiv<PERSON>, <PERSON> von der Gruppe genannt", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.removeField": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.Fields.requiredGroup1": "Erforderlich - immer aktiviert, <PERSON> von der Gruppe genannt", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.authenticateWithVerificationProvider2": "Authentifizierung mit {verificationMethod}", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.completeTheExtraQuestionsBelow": "Zusätzliche Fragen unten beantworten", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.confirmYourEmail": "E-Mail-Adresse bestätigen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.dataReturned": "Von der Verifizierungsmethode zurückgegebene Daten:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterNameLastNameEmailAndPassword1": "<PERSON>orname, Nachname, E-Mail und Passwort eingeben", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.enterYourEmail": "E-Mail-Ad<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.howRecentlyShouldUsersBeVerified": "Wie oft sollten Nutzer*innen verifiziert werden?", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.identityVerificationWith": "Identitätsüberprüfung mit {verificationMethod} (basierend auf der Nutzer*innengruppe)", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.noActionsAreRequired": "<PERSON><PERSON>r die Teilnahme sind keine Aktionen erforderlich", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.useSmartGroups": "Verwenden Sie intelligente Gruppen, um die Teilnahme auf der Grundlage der oben aufgeführten verifizierten Daten einzuschränken.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanation30Min1": "Nutzer*innen müssen in den letzten 30 Minuten verifiziert worden sein.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFlowVizExplanationXDays": "Nutzer*innen müssen in den letzten {days} Tagen verifiziert worden sein.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Days1": "In den letzten 30 Tagen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency30Min1": "In den letzten 30 Minuten", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequency7Days1": "In den letzten 7 Tagen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verificationFrequencyOnce1": "Einmal ist ausreichend", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.verifiedFields": "Geprüfte Felder:", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.FlowVisualization.xVerification": "{verificationMethod} Überprüfung", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreation": "Account anlegen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle": "Die Teilnehmenden müssen ein vollständiges Konto mit ihrem Namen, ihrer bestätigten E-Mail-Adresse und ihrem Passwort erstellen.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.accountCreationSubtitle_confirmationOff": "Die Teilnehmenden müssen ein vollständiges Konto mit ihrem Namen, ihrer E-Mail-Adresse und ihrem Passwort erstellen.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.authentication": "Beteiligungsvoraussetzung", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.edit": "<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmation": "E-Mail-Bestätigung", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.emailConfirmationSubtitle": "Die Teilnehmenden müssen ihre E-Mail-Adresse mit einem einmaligen Code bestätigen.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTracking2": "Erweiterte Spam-Erkennung", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingDescription": "<PERSON>se <PERSON> hi<PERSON>, doppelte Umfrageeinreichungen von abgemeldeten Nutzer*innen durch die Analyse von IP-Adressen und Gerätedaten zu verhindern. Sie ist zwar nicht so präzise wie eine Anmeldepflicht, kann aber dazu beitragen, die Anzahl doppelter Antworten zu reduzieren.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingNote": "Hinweis: In gemeinsam genutzten Netzwerken (wie Büros oder öffentlichem WLAN) besteht eine geringe Wahrscheinlichkeit, dass verschiedene Nutzer*innen als Duplikate gekennzeichnet werden.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.everyoneTrackingToggle": "Erweiterte Spam-Erkennung", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.extraQuestions": "Zusätzliche Fragen an die Teilnehmenden", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.none": "<PERSON><PERSON>", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.noneSubtitle": "<PERSON><PERSON> kann teilnehmen, ohne sich zu registrieren oder einzuloggen.", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.resetExtraQuestionsAndGroups": "Zusätzliche Fragen und Gruppen zurücksetzen", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.restrictParticipation": "Teilnahme auf folgende Nutzer*innengruppe(n) beschränken", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerification": "SSO-Überprüfung", "app.containers.Admin.projects.project.permissions.components.PhasePermissionsNew.ActionsFormNew.ssoVerificationSubtitle2": "Die Teilnehmenden müssen ihre Identität mit {verificationMethod} bestätigen.", "app.containers.Admin.projects.project.survey.aiAnalysis2": "Zur KI-Analyse", "app.containers.Admin.projects.project.survey.allFiles": "Alle Dateien", "app.containers.Admin.projects.project.survey.allResponses": "Alle Antworten", "app.containers.Admin.projects.project.survey.analysis.accuracy": "Genauigkeit: {accuracy}{percentage}", "app.containers.Admin.projects.project.survey.analysis.backgroundTaskFailedMessage": "Bei der Erstellung der KI-Zusammenfassung ist ein Fehler aufgetreten. Bitte versuchen Sie, sie unten neu zu generieren.", "app.containers.Admin.projects.project.survey.analysis.createAIAnalysis": "Zur KI-Analyse", "app.containers.Admin.projects.project.survey.analysis.hideSummaries": "Zusammenfassungen ausblenden für diese Frage", "app.containers.Admin.projects.project.survey.analysis.inputsSelected": "Beiträge ausgewählt", "app.containers.Admin.projects.project.survey.analysis.openAnalysisActions": "Analyse-Einstellungen öffnen", "app.containers.Admin.projects.project.survey.analysis.percentage": "%", "app.containers.Admin.projects.project.survey.analysis.refresh": "{count} ne<PERSON>", "app.containers.Admin.projects.project.survey.analysis.regenerate": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.analysis.showInsights": "KI-Einblicke anzeigen", "app.containers.Admin.projects.project.survey.analysis.tooltipTextLimit": "Sie können mit Ihrer aktuellen Lizenz maximal 30 Beiträge auf einmal zusammenfassen. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihr<PERSON> Admin, um mehr freizuschalten.", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsForAnalysis": "Fragen für die Analyse auswählen", "app.containers.Admin.projects.project.survey.analysisSelectQuestionsSubtitle": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> noch andere Fragen in Ihre Analyse von {question} aufnehmen?", "app.containers.Admin.projects.project.survey.cancel": "Abbrechen", "app.containers.Admin.projects.project.survey.consentModalButton": "Fortfahren", "app.containers.Admin.projects.project.survey.consentModalCancel": "Abbrechen", "app.containers.Admin.projects.project.survey.consentModalCheckbox": "<PERSON>ch bin damit e<PERSON><PERSON>den, OpenAI als Datenverarbeiter für dieses Projekt zu nutzen.", "app.containers.Admin.projects.project.survey.consentModalText1": "<PERSON><PERSON> <PERSON> fort<PERSON>hren, stimmen Sie der Verwendung von OpenAI als Datenverarbeiter für dieses Projekt zu.", "app.containers.Admin.projects.project.survey.consentModalText2": "Die OpenAI-APIs steuern die automatischen Textzusammenfassungen und Teile der automatischen Markierung.", "app.containers.Admin.projects.project.survey.consentModalText3": "Wir senden nur die Informationen, die Nutzer*innen in ihren Umfragen, Ideen und Kommentaren geschrieben haben, an die OpenAI APIs, niemals Informationen aus ihrem Profil.", "app.containers.Admin.projects.project.survey.consentModalText4": "OpenAI wird diese Daten nicht für das weitere Training seiner Modelle verwenden. Weitere Informationen darüber, wie OpenAI mit dem Datenschutz umgeht, finden Si<PERSON> unter {link}.", "app.containers.Admin.projects.project.survey.consentModalText4Link": "https://openai.com/api-data-privacy", "app.containers.Admin.projects.project.survey.consentModalText4LinkText": "hier", "app.containers.Admin.projects.project.survey.consentModalTitle": "<PERSON><PERSON>", "app.containers.Admin.projects.project.survey.customFieldsNotPersisted3": "<PERSON>e können die Analyse erst starten, nachdem Sie das Formular bearbeitet haben", "app.containers.Admin.projects.project.survey.deleteAnalysis": "Löschen", "app.containers.Admin.projects.project.survey.deleteAnalysisConfirmation": "Sind <PERSON> sicher, dass Sie diese Analyse löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.Admin.projects.project.survey.explore": "Erkunden", "app.containers.Admin.projects.project.survey.followUpResponses": "Antworten nachverfolgen", "app.containers.Admin.projects.project.survey.formResults.averageRank": "<b>#{averageRank}</b> <PERSON><PERSON><PERSON><PERSON>tt", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSON": "Als GeoJSON exportieren", "app.containers.Admin.projects.project.survey.formResults.exportGeoJSONTooltip2": "Exportieren Sie die Antworten auf diese Frage als GeoJSON-Datei. <PERSON><PERSON><PERSON> j<PERSON><PERSON> GeoJSON-Feature werden alle Umfrageantworten des entsprechenden Befragten im Objekt \"Eigenschaften\" des Features aufgelistet.", "app.containers.Admin.projects.project.survey.formResults.hideDetails": "Details ausblenden", "app.containers.Admin.projects.project.survey.formResults.rank": "#{rank}", "app.containers.Admin.projects.project.survey.formResults.respondentsTooltip": "{respondentCount, plural, no {{respondentCount} Befragte} one {{respondentCount} Befragte*r} other {{respondentCount} Befragte}}", "app.containers.Admin.projects.project.survey.formResults.viewDetails": "Details anzeigen", "app.containers.Admin.projects.project.survey.formResults.xChoices": "{numberChoices, plural, no {{numberChoices} Mal gewählt} one {{numberChoices} Mal gewählt} other {{numberChoices} Mal gewählt}}", "app.containers.Admin.projects.project.survey.heatMap": "Heatmap", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLink": "https://storymaps.arcgis.com/collections/9dd9f03ac2554da4af78b42020fb40c1?item=13", "app.containers.Admin.projects.project.survey.heatmapToggleEsriLinkText": "Erfahren Sie mehr über Heatmaps, die mit Esri Smart Mapping erstellt wurden.", "app.containers.Admin.projects.project.survey.heatmapToggleTooltip": "Die Heatmap wird mit Esri Smart Mapping erstellt. Heatmaps sind nützlich, wenn es eine große Anzahl von Datenpunkten gibt. Bei weniger Punkten ist es möglicherweise besser, nur die Standortpunkte direkt zu betrachten. {heatmapToggleEsriLinkText}", "app.containers.Admin.projects.project.survey.heatmapView": "Heatmap-<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.hiddenByLogic2": "- Versteckt durch Logik", "app.containers.Admin.projects.project.survey.logicSkipTooltipOption4": "Wenn ein*e <PERSON><PERSON><PERSON>*in diese Antwort auswählt, überspringt die Logik alle Seiten bis zur Seite {pageNumber} ({numQuestionsSkipped} Fragen übersprungen). <PERSON><PERSON><PERSON> Si<PERSON>, um die übersprungenen Seiten und Fragen ein- oder auszublenden.", "app.containers.Admin.projects.project.survey.logicSkipTooltipOptionSurveyEnd2": "<PERSON>n ein*e <PERSON><PERSON><PERSON>*in diese Antwort auswählt, springt die Logik zum Ende der Umfrage ({numQuestionsSkipped} Fragen übersprungen). <PERSON><PERSON><PERSON> Si<PERSON>, um die übersprungenen Seiten und Fragen ein- oder auszublenden.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPage4": "Die Logik auf dieser Seite überspringt alle Seiten bis zur Seite {pageNumber} ({numQuestionsSkipped} Fragen übersprungen). <PERSON><PERSON><PERSON> Si<PERSON>, um die übersprungenen Seiten und Fragen ein- oder auszublenden.", "app.containers.Admin.projects.project.survey.logicSkipTooltipPageSurveyEnd2": "Die Logik auf dieser Seite springt zum Ende der Umfrage ({numQuestionsSkipped} Fragen übersprungen). <PERSON><PERSON><PERSON> Si<PERSON>, um die übersprungenen Seiten und Fragen ein- oder auszublenden.", "app.containers.Admin.projects.project.survey.newAnalysis": "Neue Analyse", "app.containers.Admin.projects.project.survey.nextInsight": "Nächster <PERSON>lick", "app.containers.Admin.projects.project.survey.openAnalysis": "Zur KI-Analyse", "app.containers.Admin.projects.project.survey.otherResponses": "Sonstige Antworten", "app.containers.Admin.projects.project.survey.page": "Seite", "app.containers.Admin.projects.project.survey.previousInsight": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.responses": "Antworten", "app.containers.Admin.projects.project.survey.resultsCountPageTooltip": "Die Anzahl der Beantwortungen für diese Seite ist niedriger als die Gesamtzahl der Umfragebeantwortungen, da einige Befragte diese Seite aufgrund der Logik in der Umfrage nicht gesehen haben werden.", "app.containers.Admin.projects.project.survey.resultsCountQuestionTooltip": "Die Anzahl der Antworten auf diese Frage ist niedriger als die Gesamtzahl der Antworten auf die Umfrage, da einige Befragte diese Frage aufgrund der Logik in der Umfrage nicht gesehen haben werden.", "app.containers.Admin.projects.project.survey.sentiment_linear_scale": "Stimmungsskala", "app.containers.Admin.projects.project.survey.upsell.bullet1": "Fassen Sie alle Ihre Antworten sofort zusammen.", "app.containers.Admin.projects.project.survey.upsell.bullet2": "Sprechen Sie mit Ihren Daten in natürlicher Sprache.", "app.containers.Admin.projects.project.survey.upsell.bullet3": "Erhalten Sie Referenzen zu einzelnen Antworten aus den von der KI generierten Zusammenfassungen.", "app.containers.Admin.projects.project.survey.upsell.bullet4": "Eine vollständige Übersicht finden Sie in der {link}.", "app.containers.Admin.projects.project.survey.upsell.button": "KI-<PERSON><PERSON><PERSON>", "app.containers.Admin.projects.project.survey.upsell.supportArticle": "Wissensdatenbank", "app.containers.Admin.projects.project.survey.upsell.supportArticleLink": "https://support.govocal.com/de/articles/8316692-ki-analyse", "app.containers.Admin.projects.project.survey.upsell.title": "Daten schneller analysieren mit KI", "app.containers.Admin.projects.project.survey.upsell.upsellMessage": "Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.containers.Admin.projects.project.survey.viewAnalysis": "Vorschau", "app.containers.Admin.projects.project.survey.viewIndividualSubmissions1": "Erkunden Sie KI-gestützte Zusammenfassungen und sehen Si<PERSON> sich einzelne Beiträge an.", "app.containers.Admin.projects.project.traffic.selectPeriod": "Zeitraum auswählen", "app.containers.Admin.projects.project.traffic.trafficSources": "Kommunikations-Kanäle", "app.containers.Admin.projects.project.traffic.visitorDataBanner": "Wir haben die Art und Weise, wie wir Besucherdaten sammeln und anzeigen, ge<PERSON><PERSON><PERSON>. Das Ergebnis ist, dass die Besucherdaten genauer sind und mehr Datenarten zur Verfügung stehen, ohne dass wir die Datenschutzbestimmungen verletzen. Wir haben erst im November 2024 mit der Erhebung dieser neuen Daten begonnen, so dass davor keine Daten verfügbar sind.", "app.containers.Admin.projects.project.traffic.visitorsTimeline": "Zeitleiste für Besucher*innen", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReport": "Phasenbericht", "app.containers.Admin.reporting.components.ReportBuilder.Templates.PhaseTemplate.phaseReportDescription": "Text über die Phase hinzufügen", "app.containers.Admin.reporting.components.ReportBuilder.Templates.descriptionPlaceHolder": "Dies ist ein Text. Sie können ihn mit dem Editor im rechten Bereich bearbeiten und formatieren.", "app.containers.Admin.reporting.components.ReportBuilder.Templates.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Templates.projectResults": "Ergebnisse des Projekts", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummary": "Zusammenfassung des Berichts", "app.containers.Admin.reporting.components.ReportBuilder.Templates.reportSummaryDescription": "Fügen Sie das Projektziel, die verwendeten Beteiligungsmethoden und das Ergebnis hinzu", "app.containers.Admin.reporting.components.ReportBuilder.Templates.visitors": "Besucher*innen", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.cannotPrint": "Dieser Bericht enthält ungesicherte Änderungen. Bitte speichern Si<PERSON> ihn vor dem Drucken.", "app.containers.Admin.reporting.components.ReportBuilder.TopBar.titleTaken": "Titel ist bereits vergeben", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.comparedToPreviousXDays": "Verglichen mit den vorherigen {days} Tagen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.hideStatistics": "Statistik ausblenden", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.participationRate": "Beteiligungsrate", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.showComparisonLastPeriod": "Vergleich mit letzter Periode anzeigen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ActiveUsersWidget.ChartWidgetSettings.youNeedToSelectADateRange": "Sie müssen zunächst einen Datumsbereich auswählen.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.comments": "Kommentare", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.inputs": "Beiträge", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.participation": "Beteiligung", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showComments": "Kommentare anzeigen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showInputs": "Beiträge anzeigen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.showVotes": "<PERSON><PERSON><PERSON> anzeigen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.ParticipationWidget.votes": "Stimmen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.demographics": "Demografische Daten", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationDateRange": "Zeitraum der Registrierung", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.registrationField": "Registrierungsfeld", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.unknown": "Unbekannt", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ChartWidgets.UsersWidget.users": "Nutzer*innen: {numberOfUsers} ({percentageOfUsers}%)", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ImageMultiloc.Settings.stretch": "Ausdehnen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.active": "Aktiv", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.finished": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.openEnded": "<PERSON>e offen", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.planned": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.projects": "Projekte", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.publicationStatus": "Veröffentlichungsstatus", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.ProjectsWidget.published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets._shared.missingData": "Die Daten für dieses Widget fehlen. Konfigurieren Sie es neu oder löschen Sie es, um den Bericht speichern zu können.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.communityMonitorHealthScoreTitle": "Gemeinschaftspuls-Bewertung", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarter": "Quartal", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterFour": "Q4", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterOne": "Q1", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterThree": "Q3", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.quarterTwo": "Q2", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.communityMonitorHealthScore.year": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noAppropriatePhases": "<PERSON><PERSON> geeigneten Phasen in diesem Projekt gefunden", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noPhaseSelected": "Keine Phase ausgewählt. Bitte wählen Sie zuerst eine Phase.", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProject": "<PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilder.Widgets.noProjectSelected": "Kein Projekt ausgewählt. Bitte wählen Si<PERSON> zu<PERSON>t ein Projekt aus.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotDuplicateReport": "<PERSON>e können diesen Bericht nicht duplizieren, da er Daten enthält, auf die Sie keinen Zugriff haben.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.cannotEditReport2": "<PERSON>e können diesen Bericht nicht bearbeiten, da er Daten enthält, auf die Sie keinen Zugriff haben.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteReport1": "<PERSON>d <PERSON> sicher, dass Si<PERSON> \"{reportName}\" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.confirmDeleteThisReport1": "Sind <PERSON> sicher, dass Sie diesen Bericht löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.delete": "Löschen", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.duplicate": "Duplizieren", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.edit": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.components.ReportBuilderPage.ReportRow.lastUpdate1": "<PERSON><PERSON><PERSON><PERSON><PERSON> vor {days, plural, no {# Tagen} one {# Tag} other {# Tagen}}", "app.containers.Admin.reporting.components.ReportBuilderPage.anErrorOccurred": "<PERSON><PERSON>, diesen <PERSON> zu erstellen, ist ein Fehler aufgetreten. Bitte versuchen Si<PERSON> es später noch einmal.", "app.containers.Admin.reporting.components.ReportBuilderPage.blankTemplate": "Mit einer leeren Seite beginnen", "app.containers.Admin.reporting.components.ReportBuilderPage.communityMonitorTemplate": "Mit einer Gemeinschaftspuls-Vorlage beginnen", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalInputLabel": "Titel des Berichts", "app.containers.Admin.reporting.components.ReportBuilderPage.createReportModalTitle2": "Bericht erstellen", "app.containers.Admin.reporting.components.ReportBuilderPage.customizeReport": "Passen Sie Ihren Bericht an und teilen Si<PERSON> ihn mit internen Stakeholdern oder Ihrer Bevölkerung als PDF-Datei.", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateButtonText": "Einen Bericht erstellen", "app.containers.Admin.reporting.components.ReportBuilderPage.emptyStateTitle2": "<PERSON><PERSON><PERSON><PERSON> Sie Ihren ersten Bericht", "app.containers.Admin.reporting.components.ReportBuilderPage.noProjectSelected": "Kein Projekt ausgewählt", "app.containers.Admin.reporting.components.ReportBuilderPage.platformTemplate": "Mit einer Plattformvorlage beginnen", "app.containers.Admin.reporting.components.ReportBuilderPage.printToPdf": "Als PDF drucken", "app.containers.Admin.reporting.components.ReportBuilderPage.projectTemplate": "Mit einer Projektvorlage beginnen", "app.containers.Admin.reporting.components.ReportBuilderPage.quarter": "Quartal {quarterValue}", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTemplate": "Berichtsvorlage", "app.containers.Admin.reporting.components.ReportBuilderPage.reportTitleAlreadyExists": "Ein Bericht mit diesem Titel existiert bereits. Bitte wählen Si<PERSON> einen anderen Titel.", "app.containers.Admin.reporting.components.ReportBuilderPage.selectQuarter": "Quartal auswählen", "app.containers.Admin.reporting.components.ReportBuilderPage.selectYear": "Jahr auswählen", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdf": "Als PDF teilen", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsPdfDesc": "Um den Bericht mit anderen zu teilen, drucken Si<PERSON> ihn als PDF-Datei aus.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLink": "Als Weblink teilen", "app.containers.Admin.reporting.components.ReportBuilderPage.shareAsWebLinkDesc": "Dieser Weblink ist nur für Admins zugänglich.", "app.containers.Admin.reporting.components.ReportBuilderPage.shareReportTitle": "Teilen", "app.containers.Admin.reporting.contactToAccess": "Das Erstellen eines benutzerdefinierten Berichts ist Teil der Premium-Lizenz. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.containers.Admin.reporting.containers.ReportBuilderPage.allReports": "Alle Berichte", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReports": "Gemeinschaftspuls-Bericht", "app.containers.Admin.reporting.containers.ReportBuilderPage.communityMonitorReportsTooltip": "Diese Berichte beziehen sich auf den Gemeinschaftspuls. Die Berichte werden automatisch jedes Quartal erstellt.", "app.containers.Admin.reporting.containers.ReportBuilderPage.createAReport": "Bericht erstellen", "app.containers.Admin.reporting.containers.ReportBuilderPage.createReportDescription": "Passen Sie Ihren Bericht an und geben Sie ihn über einen Weblink an interne Stakeholder oder Ihre Bevölkerung weiter.", "app.containers.Admin.reporting.containers.ReportBuilderPage.personalReportsPlaceholder": "Ihre Berichte werden hier erscheinen.", "app.containers.Admin.reporting.containers.ReportBuilderPage.searchReports": "Berichte suchen", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReports": "Fortschrittsberichte", "app.containers.Admin.reporting.containers.ReportBuilderPage.serviceReportsTooltip": "Dies sind Berichte, die von Ihrer Kundenbetreuerin erstellt wurden", "app.containers.Admin.reporting.containers.ReportBuilderPage.yourReports": "<PERSON><PERSON>e Berichte", "app.containers.Admin.reporting.deprecated": "VERALTET", "app.containers.Admin.reporting.helmetDescription": "Berichtseite für Admins", "app.containers.Admin.reporting.helmetTitle": "Berichterstattung", "app.containers.Admin.reporting.printPrepare": "Vorbereitung auf den Druck...", "app.containers.Admin.reporting.reportBuilder": "Berichte", "app.containers.Admin.reporting.reportHeader": "Be<PERSON>tskop<PERSON>", "app.containers.Admin.reporting.warningBanner3": "Die Diagramme und Zahlen in diesem Bericht werden nur auf dieser Seite automatisch aktualisiert. Speichern Sie den Bericht, um sie auf anderen Seiten zu aktualisieren.", "app.containers.Admin.reporting.widgets.MethodsUsed.commonGround": "ZusammenFinden", "app.containers.Admin.reporting.widgets.MethodsUsed.document_annotation": "Konveio", "app.containers.Admin.reporting.widgets.MethodsUsed.ideation": "Ideenfindung", "app.containers.Admin.reporting.widgets.MethodsUsed.information": "Informationen", "app.containers.Admin.reporting.widgets.MethodsUsed.methodsUsed": "Verwendete Methoden", "app.containers.Admin.reporting.widgets.MethodsUsed.nativeSurvey": "Umfrage", "app.containers.Admin.reporting.widgets.MethodsUsed.poll": "Blitz-Umfrage", "app.containers.Admin.reporting.widgets.MethodsUsed.previousXDays": "Vorherige {days} Tage: {count}", "app.containers.Admin.reporting.widgets.MethodsUsed.proposals": "Vorschläge", "app.containers.Admin.reporting.widgets.MethodsUsed.survey": "Externe Umfrage", "app.containers.Admin.reporting.widgets.MethodsUsed.volunteering": "Te<PERSON><PERSON><PERSON><PERSON><PERSON> finden", "app.containers.Admin.reporting.widgets.MethodsUsed.voting": "Abstimmung", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.chart": "Diagramm", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.table": "<PERSON><PERSON><PERSON>", "app.containers.Admin.reporting.widgets.chart.visitorsTrafficSources.view": "Vorschau", "app.containers.Admin.surveyFormTab.downloads": "Downloads", "app.containers.Admin.surveyFormTab.duplicateAnotherSurvey1": "Eine andere Umfrage duplizieren", "app.containers.Admin.surveyFormTab.editSurveyForm": "Umfrageformular bearbeiten", "app.containers.Admin.surveyFormTab.inputFormDescription": "Legen Sie fest, welche Informationen angegeben werden sollen, fügen Sie kurze Beschreibungen oder Anweisungen hinzu, um die Teilnehmenden bei der Beantwortung zu unterstützen, und geben Si<PERSON> an, ob jedes <PERSON> optional oder erforderlich ist.", "app.containers.Admin.surveyFormTab.surveyForm": "Umfrageformular", "app.containers.Admin.tools.apiTokens.createTokenButton": "Neues Token erstellen", "app.containers.Admin.tools.apiTokens.createTokenCancel": "Abbrechen", "app.containers.Admin.tools.apiTokens.createTokenCreatedDescription": "<PERSON>hr Token wurde erstellt. Bitte kopieren Sie das {secret} unten und bewahren Sie es sicher auf.", "app.containers.Admin.tools.apiTokens.createTokenDescription": "<PERSON>rstellen Si<PERSON> ein neues Token zur Verwendung mit unserer öffentlichen API.", "app.containers.Admin.tools.apiTokens.createTokenError": "<PERSON>eben Si<PERSON> einen Namen für Ihr Token an", "app.containers.Admin.tools.apiTokens.createTokenModalButton": "Token erstellen", "app.containers.Admin.tools.apiTokens.createTokenModalCreatedImportantText": "<b>W<PERSON><PERSON><PERSON>!</b> <PERSON><PERSON> können dieses {secret} nur einmal kopieren. Wenn Sie dieses Fenster schließen, können Sie es nicht mehr sehen.", "app.containers.Admin.tools.apiTokens.createTokenName": "Name", "app.containers.Admin.tools.apiTokens.createTokenNamePlaceholder": "Geben Sie Ihrem Token einen Namen", "app.containers.Admin.tools.apiTokens.createTokenSuccess": "<PERSON>hr Token wurde erstellt", "app.containers.Admin.tools.apiTokens.createTokenSuccessClose": "Schließen", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopyMessage": "{secret} kopieren", "app.containers.Admin.tools.apiTokens.createTokenSuccessCopySuccessMessage": "Kopiert!", "app.containers.Admin.tools.apiTokens.createTokenTitle": "Ein neues Token erstellen", "app.containers.Admin.tools.apiTokens.createdAt": "Erstellt am", "app.containers.Admin.tools.apiTokens.delete": "Token löschen", "app.containers.Admin.tools.apiTokens.deleteConfirmation": "Sind <PERSON> sicher, dass Sie dieses Token löschen möchten?", "app.containers.Admin.tools.apiTokens.description": "Verwalten Sie Ihre API-Tokens für unsere öffentliche API. Weitere Informationen finden Sie auf unserer Website {link}.", "app.containers.Admin.tools.apiTokens.lastUsedAt": "Zuletzt verwendet", "app.containers.Admin.tools.apiTokens.link": "API-Dokumentation", "app.containers.Admin.tools.apiTokens.linkUrl2": "https://developers.citizenlab.co/api", "app.containers.Admin.tools.apiTokens.name": "Name", "app.containers.Admin.tools.apiTokens.noTokens": "Sie haben noch keine <PERSON>.", "app.containers.Admin.tools.apiTokens.title": "Öffentliche API-Tokens", "app.containers.Admin.tools.esriDisabled": "Die Esri-Integration ist eine Zusatzfunktion. Wenden Sie sich an Ihre Kundenbetreuerin, wenn Sie weitere Informationen dazu wünschen.", "app.containers.Admin.tools.esriIntegration2": "Esri-Integration", "app.containers.Admin.tools.esriIntegrationButton": "Esri aktivieren", "app.containers.Admin.tools.esriIntegrationDescription3": "Verbinden Sie Ihr Esri-Konto und importieren Sie Daten aus ArcGIS Online direkt in Ihre Kartierungsprojekte.", "app.containers.Admin.tools.esriIntegrationImageAlt": "Esri-Logo", "app.containers.Admin.tools.esriKeyInputDescription": "Fügen Sie Ihren Esri API-Schl<PERSON><PERSON> hinzu, um den Import Ihrer Kartenebenen aus ArcGIS Online in die Kartenregisterkarten in Projekten zu ermöglichen.", "app.containers.Admin.tools.esriKeyInputLabel": "Esri API-Schlüssel", "app.containers.Admin.tools.esriKeyInputPlaceholder": "API-Schl<PERSON><PERSON> hier einfügen", "app.containers.Admin.tools.esriMaps": "<PERSON><PERSON><PERSON>", "app.containers.Admin.tools.esriSaveButtonError": "<PERSON><PERSON>rn Ihres Schlüssels ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut.", "app.containers.Admin.tools.esriSaveButtonSuccess": "API-Schlüssel gespeichert", "app.containers.Admin.tools.esriSaveButtonText": "Schlüssel speichern", "app.containers.Admin.tools.learnMore": "<PERSON><PERSON> er<PERSON>", "app.containers.Admin.tools.managePublicAPIKeys": "API-Schlüssel verwalten", "app.containers.Admin.tools.manageWidget": "Widget verwalten", "app.containers.Admin.tools.manageWorkshops": "Workshops verwalten", "app.containers.Admin.tools.powerBIAPIImage": "Power BI Bild", "app.containers.Admin.tools.powerBIDescription": "Verwenden Sie unsere Plug & Play Power BI-Vorlagen, um auf Go Vocal-Daten in Ihrem Microsoft Power BI-Arbeitsbereich zuzugreifen.", "app.containers.Admin.tools.powerBIDisabled1": "Power BI ist nicht Teil Ihrer Lizenz. Wenden Sie sich an Ihre Kundenbetreuerin, wenn Sie weitere Informationen dazu wünschen.", "app.containers.Admin.tools.powerBIDownloadTemplates": "<PERSON>orlagen herunterladen", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDescription": "Wenn Sie Ihre Go Vocal-Daten in einem Power BI-Datenfluss verwenden möchten, können Sie mit dieser Vorlage einen neuen Datenfluss einrichten, der sich mit Ihren Go Vocal-Daten verbindet. Nachdem Sie diese Vorlage herunter<PERSON>aden haben, müssen Sie zunächst die folgenden Zeichenfolgen ##CLIENT_ID## und ##CLIENT_SECRET## in der Vorlage finden und durch Ihre öffentlichen API-Zugangsdaten ersetzen, bevor <PERSON> si<PERSON> in PowerBI hochladen.", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateDownload": "Vorlage für Datenfluss herunterladen", "app.containers.Admin.tools.powerBITemplates.dataflowTemplateTitle": "Datenfluss-Vorlage", "app.containers.Admin.tools.powerBITemplates.intro": "Hinweis: Um eine dieser Power BI-Vorlagen zu verwenden, müssen Si<PERSON> zunä<PERSON>t {link}", "app.containers.Admin.tools.powerBITemplates.publicApiLinkText": "eine <PERSON>ih<PERSON> von Anmeldeinformationen für unsere öffentliche API erstellen", "app.containers.Admin.tools.powerBITemplates.reportTemplateDescription3": "Diese Vorlage erstellt einen Power BI-Bericht auf der Grundlage Ihrer Go Vocal-Daten. Sie richtet alle Datenverbindungen zu Ihrer Go Vocal-Plattform ein, erstellt das Datenmodell und einige Standard-Dashboards. Wenn Sie die Vorlage in Power BI öffnen, werden Sie aufgefordert, Ihre öffentlichen API-Anmeldedaten einzugeben. Außerdem müssen Sie die Basis-URL für Ihre Plattform eingeben, die lautet: {baseUrl}", "app.containers.Admin.tools.powerBITemplates.reportTemplateDownload": "Berichtsvorlage herunterladen", "app.containers.Admin.tools.powerBITemplates.reportTemplateTitle": "Berichtsvorlage", "app.containers.Admin.tools.powerBITemplates.supportLinkDescription": "Weitere Details zur Verwendung Ihrer Go Vocal-Daten in Power BI finden Sie in unserer {link}.", "app.containers.Admin.tools.powerBITemplates.supportLinkText": "Wissensdatenbank", "app.containers.Admin.tools.powerBITemplates.supportLinkUrl": "https://support.govocal.com/de/articles/8512834-verwenden-sie-citizenlab-daten-in-powerbi", "app.containers.Admin.tools.powerBITemplates.title": "Power BI-Vorlagen", "app.containers.Admin.tools.powerBITitle": "Power BI", "app.containers.Admin.tools.publicAPIDescription": "Verwalten Sie die Anmeldeinformationen, um benutzerdefinierte Integrationen auf unserer öffentlichen API zu erstellen.", "app.containers.Admin.tools.publicAPIDisabled1": "Die öffentliche API ist nicht Teil Ihrer aktuellen Lizenz. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, wenn Sie weitere Informationen dazu wünschen.", "app.containers.Admin.tools.publicAPIImage": "Öffentliches API-Bild", "app.containers.Admin.tools.publicAPITitle": "Öffentlicher API-Zugang", "app.containers.Admin.tools.toolsLabel": "Werkzeuge", "app.containers.Admin.tools.widgetDescription": "<PERSON>e können ein Widget erstellen, es anpassen und es Ihrer eigenen Website hinzufügen, um Leute für diese Plattform zu gewinnen.", "app.containers.Admin.tools.widgetImage": "Widget-Bild", "app.containers.Admin.tools.widgetTitle": "Widget", "app.containers.Admin.tools.workshopsDescription": "Halten Sie Live-Videokonferenzen ab, moderieren Sie simultane Gruppendiskussionen und Debatten. Sammeln Sie Beiträge, stimmen Sie ab und erzielen Si<PERSON> einen Konsens, genau wie Sie es in Person tun würden.", "app.containers.Admin.tools.workshopsImage": "Bild des Workshops", "app.containers.Admin.tools.workshopsSupportLink": "https://support.govocal.com/de/articles/4155778-e<PERSON><PERSON><PERSON>-eines-online-workshops", "app.containers.Admin.tools.workshopsTitle": "Online-Deliberationsworkshops", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "Nutzer:innen auf der Plattform insgesamt", "app.containers.AdminPage.DashboardPage._blank": "unbekannt", "app.containers.AdminPage.DashboardPage.allGroups": "Alle Gruppen", "app.containers.AdminPage.DashboardPage.allProjects": "{org<PERSON><PERSON>, select, <PERSON>ü<PERSON> {Alle Bezirke oder Projekte} other {Alle Projekte}}", "app.containers.AdminPage.DashboardPage.allTime": "Alle Zeiträume", "app.containers.AdminPage.DashboardPage.comments": "Kommentare", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Kommentare", "app.containers.AdminPage.DashboardPage.components.ChartCard.baseDatasetExplanation1": "Ein Basisdatensatz ist erforderlich, um die Repräsentativität der Nutzer*innen zu messen.", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoon": "Demnächst verfügbar", "app.containers.AdminPage.DashboardPage.components.ChartCard.comingSoonDescription": "Wir arbeiten derzeit an dem {fieldName} Dashboard. Es wird bald verfügbar sein.", "app.containers.AdminPage.DashboardPage.components.ChartCard.dataHiddenWarning": "{numberOfHiddenItems, plural, one {# Element ist} other {# Elemente sind}} versteckt in diesem Graph. Wechseln Sie zu {tableViewLink}, um alle Daten anzuzeigen.", "app.containers.AdminPage.DashboardPage.components.ChartCard.forUserRegistation": "{requiredOrOptional} bei der Registrierung", "app.containers.AdminPage.DashboardPage.components.ChartCard.includedUsersMessage2": "{known} von {total} <PERSON><PERSON><PERSON>*innen berücksichtigt ({percentage})", "app.containers.AdminPage.DashboardPage.components.ChartCard.openTableModalButtonText": "{numberOfHiddenItems} mehr anzeigen", "app.containers.AdminPage.DashboardPage.components.ChartCard.optional": "Optional", "app.containers.AdminPage.DashboardPage.components.ChartCard.provideBaseDataset": "Bitte stellen Sie einen Basisdatensatz zur Verfügung.", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreText": "Repräsentativitätswert:", "app.containers.AdminPage.DashboardPage.components.ChartCard.representativenessScoreTooltipText3": "Dieser Wert gibt an, wie genau die Nutzer*nnendaten der Plattform die Gesamtbevölkerung widerspiegeln. Erfahren Sie mehr über {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.components.ChartCard.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.ChartCard.submitBaseDataButton": "Basisdaten eingeben", "app.containers.AdminPage.DashboardPage.components.ChartCard.tableViewLinkText": "Tabellenansicht", "app.containers.AdminPage.DashboardPage.components.ChartCard.totalPopulation": "Gesamtbevölkerung", "app.containers.AdminPage.DashboardPage.components.ChartCard.users": "Nutzer*innen", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.addAnAgeGroup": "Altersgruppe hinzufügen", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageAndOver": "{age} und älter", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroup": "Altersgruppe", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupNotIncluded": "Die Altersgruppe(n) {upperBound} und älter sind nicht eingeschlossen.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroupX": "Altersgruppe {number}", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.ageGroups": "Altersgruppen", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.andOver": "und älter", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.applyExampleGrouping": "Beispiel Gruppierung anwenden", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.clearAll": "Alle löschen", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.from": "<PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.modalDescription": "<PERSON>ellen Sie die Altersgruppen so ein, dass sie mit Ihrem Basisdatensatz übereinstimmen.", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.range": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.save": "Speichern", "app.containers.AdminPage.DashboardPage.components.Field.BinModal.to": "Bis", "app.containers.AdminPage.DashboardPage.components.Field.Options.editAgeGroups": "Altersgruppen bearbeiten", "app.containers.AdminPage.DashboardPage.components.Field.Options.itemNotCalculated": "Dieser Parameter wird nicht berechnet.", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeLess": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.components.Field.Options.seeMore": "Siehe {numberOfHiddenItems} mehr...", "app.containers.AdminPage.DashboardPage.components.Field.baseMonth": "Ausgangsmonat (optional)", "app.containers.AdminPage.DashboardPage.components.Field.birthyearCustomTitle": "Alter", "app.containers.AdminPage.DashboardPage.components.Field.comingSoon": "Demnächst verfügbar", "app.containers.AdminPage.DashboardPage.components.Field.complete": "Vollständig", "app.containers.AdminPage.DashboardPage.components.Field.default": "Standard", "app.containers.AdminPage.DashboardPage.components.Field.disallowSaveMessage2": "Bitte füllen Sie alle aktivierten Optionen aus, oder deaktivieren Sie die Optionen, die Sie in der Grafik auslassen möchten. Mindestens eine Option muss ausgefüllt werden.", "app.containers.AdminPage.DashboardPage.components.Field.incomplete": "Unvollständig", "app.containers.AdminPage.DashboardPage.components.Field.numberOfTotalResidents": "Gesamtbevölkerung", "app.containers.AdminPage.DashboardPage.components.Field.options": "Optionen", "app.containers.AdminPage.DashboardPage.components.Field.save": "Speichern", "app.containers.AdminPage.DashboardPage.components.Field.saved": "Gespe<PERSON>rt", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroups": "Bitte {setAgeGroupsLink}, um mit der Eingabe der Basisdaten zu beginnen.", "app.containers.AdminPage.DashboardPage.components.Field.setAgeGroupsLink": "Altersgruppen festlegen", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTime": "Durchschnittliche Antwortzeit: {days} Tag(e)", "app.containers.AdminPage.DashboardPage.components.PostFeedback.averageTimeColumnName": "Durchschnittliche Dauer der Antwortzeit (in Tagen)", "app.containers.AdminPage.DashboardPage.components.PostFeedback.feedbackGiven": "Feed<PERSON> g<PERSON>ben", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputStatus": "Reaktionsfähigkeit", "app.containers.AdminPage.DashboardPage.components.PostFeedback.inputsByStatus": "Beiträge nach Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.numberOfInputs": "Anzahl der Beiträge", "app.containers.AdminPage.DashboardPage.components.PostFeedback.officialUpdate": "Offizielles Update", "app.containers.AdminPage.DashboardPage.components.PostFeedback.percentageOfInputs": "Prozentsatz der Beiträge", "app.containers.AdminPage.DashboardPage.components.PostFeedback.responseTime": "Reaktionszeit", "app.containers.AdminPage.DashboardPage.components.PostFeedback.status": "Status", "app.containers.AdminPage.DashboardPage.components.PostFeedback.statusChanged": "Status geändert", "app.containers.AdminPage.DashboardPage.components.PostFeedback.total": "Gesamt", "app.containers.AdminPage.DashboardPage.components.editBaseData": "Basisdaten bearbeiten", "app.containers.AdminPage.DashboardPage.components.representativenessArticleLinkText2": "wie wir die Repräsentativitätswerte berechnen", "app.containers.AdminPage.DashboardPage.continuousType": "<PERSON>ne Zeitleiste", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Kumulierte Summe", "app.containers.AdminPage.DashboardPage.customDateRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.day": "Tag", "app.containers.AdminPage.DashboardPage.false": "falsch", "app.containers.AdminPage.DashboardPage.female": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.fiveInputsWithMostReactions": "Top 5 Beiträge nach Reaktionen", "app.containers.AdminPage.DashboardPage.fromTo": "von {from} bis {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard der Aktivitäten auf der Plattform", "app.containers.AdminPage.DashboardPage.helmetTitle": "Admin-Dashboard-Seite", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Wähle die Ressource, die zum Projekt angezeigt werden soll", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Wähle die Ressource, die zum Thema angezeigt werden soll", "app.containers.AdminPage.DashboardPage.inputs1": "Beiträge", "app.containers.AdminPage.DashboardPage.inputsByStatusTitle1": "Beiträge nach Status", "app.containers.AdminPage.DashboardPage.labelGroupFilter": "NutzerInnengruppe auswählen", "app.containers.AdminPage.DashboardPage.male": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.month": "<PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.noData": "<PERSON>s können keine Daten angezeigt werden.", "app.containers.AdminPage.DashboardPage.noPhase": "Keine Phase für dieses Projekt erstellt", "app.containers.AdminPage.DashboardPage.numberOfActiveParticipantsDescription2": "Die Anzahl der Teilnehmenden, die Beiträge gepostet, reagiert oder kommentiert haben.", "app.containers.AdminPage.DashboardPage.numberOfDislikes": "G<PERSON>ä<PERSON><PERSON> mir nicht", "app.containers.AdminPage.DashboardPage.numberOfLikes": "Gefällt mir", "app.containers.AdminPage.DashboardPage.numberOfReactionsTotal": "Reaktionen insgesamt", "app.containers.AdminPage.DashboardPage.overview.management": "Verwaltung", "app.containers.AdminPage.DashboardPage.overview.projectsAndParticipation": "Projekte & Beteiligung", "app.containers.AdminPage.DashboardPage.overview.showLess": "<PERSON><PERSON> anzeigen", "app.containers.AdminPage.DashboardPage.overview.showMore": "<PERSON><PERSON> anzeigen", "app.containers.AdminPage.DashboardPage.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.participationPerProject": "Beteiligung pro Projekt", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Beteiligung pro Thema", "app.containers.AdminPage.DashboardPage.perPeriod": "Pro {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Letzte 30 Tage", "app.containers.AdminPage.DashboardPage.previous90Days": "Letzte 90 Tage", "app.containers.AdminPage.DashboardPage.previousWeek": "Letzte Woche", "app.containers.AdminPage.DashboardPage.previousYear": "Letzte 12 Monate", "app.containers.AdminPage.DashboardPage.projectType": "Projekttyp : {projectType}", "app.containers.AdminPage.DashboardPage.reactions": "Reaktionen", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateDescription": "Dieser Basisdatensatz wird ben<PERSON>, um die Repräsentativität der Nutzer*nnen im Vergleich zur Gesamtbevölkerung zu berechnen.", "app.containers.AdminPage.DashboardPage.representativeness.emptyStateTitle": "Bitte stellen Sie einen Basisdatensatz zur Verfügung.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescription3": "<PERSON><PERSON>, wie repräsentativ Ihre Nutzer*innen im Vergleich zur Gesamtbevölkerung sind - auf der Grundlage der bei der Registrierung erfassten Daten. Erfahren Sie mehr über {representativenessArticleLink}.", "app.containers.AdminPage.DashboardPage.representativeness.pageDescriptionTemporary": "<PERSON><PERSON>, wie repräsentativ Ihre Nutzer*innen im Vergleich zur Gesamtbevölkerung sind - auf der Grundlage der bei der Registrierung erfassten Daten der Nutzer*innen.", "app.containers.AdminPage.DashboardPage.representativeness.pageTitle3": "Bevölkerungsrepräsentativität", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.backToDashboard": "Zurück zum Dashboard", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.noEnabledFieldsSupported": "Zurzeit wird keines der aktivierten Registrierungsfelder unterstützt.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageDescription": "Hier können Sie Elemente auf dem Dashboard anzeigen/ausblenden und die Basisdaten eingeben. Nur die aktivierten Felder der {userRegistrationLink} werden hier angezeigt.", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.pageTitle2": "Basisdaten bearbeiten", "app.containers.AdminPage.DashboardPage.representativeness.referenceDataInterface.userRegistrationLink": "Nutzer*innenregistrierung", "app.containers.AdminPage.DashboardPage.representativeness.submitBaseDataButton": "Basisdaten eingeben", "app.containers.AdminPage.DashboardPage.resolutionday": "in Tagen", "app.containers.AdminPage.DashboardPage.resolutionmonth": "in Monaten", "app.containers.AdminPage.DashboardPage.resolutionweek": "in Wochen", "app.containers.AdminPage.DashboardPage.selectProject": "Projekt auswählen", "app.containers.AdminPage.DashboardPage.selectedProject": "aktueller Projektfilter", "app.containers.AdminPage.DashboardPage.selectedTopic": "aktueller Themenfilter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>, was auf Ihrer Plattform passiert.", "app.containers.AdminPage.DashboardPage.tabOverview": "Übersicht", "app.containers.AdminPage.DashboardPage.tabReports": "Projekte", "app.containers.AdminPage.DashboardPage.tabRepresentativeness2": "Repräsentativität", "app.containers.AdminPage.DashboardPage.tabUsers": "Nutzer*innen", "app.containers.AdminPage.DashboardPage.timelineType": "Zeitleiste", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "Gesamt", "app.containers.AdminPage.DashboardPage.totalForPeriod": "Aktuelle/r {period}", "app.containers.AdminPage.DashboardPage.true": "wahr", "app.containers.AdminPage.DashboardPage.unspecified": "divers", "app.containers.AdminPage.DashboardPage.users": "Nutzer*innen", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Nutzer*innen nach Alter", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Nutzer*innen nach geografischem Gebiet", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Nutzer*innen nach Geschlecht", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registrierungen", "app.containers.AdminPage.DashboardPage.week": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Tipps zur Auswahl eines Favicon-Bildes: <PERSON><PERSON><PERSON><PERSON> Sie ein einfaches Bild, da die angezeigte Bildgröße sehr klein ist. Das Bild sollte als PNG gespeichert werden und quadratisch sein mit einem transparenten Hintergrund (oder einem weißen Hintergrund). Ihr Favicon sollte nur einmal eingestellt werden, da weitere Änderungen einen technischen Support erfordern.", "app.containers.AdminPage.FaviconPage.save": "Speichern", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Etwas ging schief, bitte versuchen Sie es später nochmal.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Fertig!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Hinzufügen", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Löschen", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "<PERSON><PERSON>er-Manager*innen", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Ordnermanager:innen können die Ordnerbeschreibung bearbeiten, neue Projekte innerhalb des Ordners erstellen und haben Projektmanagement-Rechte über alle Projekte innerhalb des Ordners. Sie können keine Projekte löschen und sie haben keinen Zugriff auf Projekte, die sich nicht in ihrem Ordner befinden. Sie können {projectManagementInfoCenterLink} , um weitere Informationen zu den Projektverwaltungsrechten zu finden.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/articles/4648650", "app.containers.AdminPage.FolderPermissions.noMatch": "<PERSON><PERSON>einstimmung gefunden", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "besuchen Sie unser Support Center", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Person suchen", "app.containers.AdminPage.FoldersEdit.addToFolder": "Zum Ordner hinzufügen", "app.containers.AdminPage.FoldersEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Diesen Ordner löschen", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Beschreibung", "app.containers.AdminPage.FoldersEdit.draftStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "<PERSON><PERSON> zu diesem Ordner hinzufügen", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "<PERSON>ien sollten nicht größer als 50Mb sein. Hinzugefügte Dateien werden auf der Ordnerseite angezeigt.", "app.containers.AdminPage.FoldersEdit.folderDescriptions": "Beschreibungen", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "In diesem Ordner sind keine Projekte vorhanden. Gehen Sie zurück zur Hauptregisterkarte Projekte, um Projekte zu erstellen und hinzuzufügen.", "app.containers.AdminPage.FoldersEdit.folderName": "Name des Ordners", "app.containers.AdminPage.FoldersEdit.headerImageInputLabel": "Bild in der Kopfzeile", "app.containers.AdminPage.FoldersEdit.multilocError": "Alle Textfelder müssen für jede Sprache ausgefüllt werden.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "<PERSON>s gibt keine Projekte, die Sie zu diesem Ordner hinzufügen können.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Ordner Kartenbild", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Berechtigungen", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Ordner-Projekte", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Einstellungen", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "<PERSON>u diesem Ordner hinzugefügte Projekte", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "<PERSON>je<PERSON><PERSON>, die Sie zu diesem Ordner hinzufügen können", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "<PERSON><PERSON><PERSON><PERSON>, ob dieser Ordner \"Entwurf\", \"ver<PERSON><PERSON><PERSON><PERSON><PERSON>\" oder \"archiviert\" ist.", "app.containers.AdminPage.FoldersEdit.publishedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Aus Ordner entfernen", "app.containers.AdminPage.FoldersEdit.save": "Speichern", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Etwas ging schief, bitte versuchen Sie es später nochmal.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Fertig!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Kurzbeschreibung", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "Wird auf der Startseite angezeigt", "app.containers.AdminPage.FoldersEdit.statusLabel": "Veröffentlichungsstatus", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, warum die Projekte zusammengehören, definieren Sie eine visuelle Identität und teilen Sie Informationen.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "<PERSON><PERSON><PERSON><PERSON><PERSON>, warum die Projekte zusammengehören, definieren Sie eine visuelle Identität und teilen Sie Informationen.", "app.containers.AdminPage.FoldersEdit.textFieldsError": "Alle Textfelder müssen ausgefüllt werden.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Titel", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Einen neuen Ordner erstellen", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Einstellungen", "app.containers.AdminPage.FoldersEdit.url": "URL", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "Ordner anzeigen", "app.containers.AdminPage.HeroBannerForm.heroBannerInfoBar": "Passen Sie das Bild und den Text des Hero Banners an.", "app.containers.AdminPage.HeroBannerForm.heroBannerTitle": "Hero Banner", "app.containers.AdminPage.HeroBannerForm.saveHeroBanner": "<PERSON> <PERSON> s<PERSON>rn", "app.containers.AdminPage.InspirationHubPage.inspirationHubDescription": "Inspirations-<PERSON><PERSON> ist ein <PERSON>, an dem Sie Inspiration für Ihre Projekte finden können, indem Sie Projekte auf anderen Plattformen durchsuchen.", "app.containers.AdminPage.PagesEdition.policiesSubtitle": "Bearbeiten Sie die Nutzungsbedingungen, das Impressum und die Datenschutzbestimmungen Ihrer Plattform. Andere Seiten wie die Über uns- und Fragen & Antworten-Seiten können in der {navigationLink} Registerkarte bearbeitet werden.", "app.containers.AdminPage.PagesEdition.policiesTitle": "Plattform-Richtlinien", "app.containers.AdminPage.PagesEdition.privacy-policy": "Impressum & Datenschutz", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "Nutzungsbedingungen", "app.containers.AdminPage.Project.confirmation.description": "Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.AdminPage.Project.confirmation.no": "Abbrechen", "app.containers.AdminPage.Project.confirmation.title": "Sind <PERSON> sicher, dass Sie alle Teilnahmedaten löschen möchten?", "app.containers.AdminPage.Project.confirmation.yes": "Alle Teilnahmedaten löschen", "app.containers.AdminPage.Project.data.descriptionText1": "Löschen Sie Ideen, <PERSON><PERSON><PERSON>re, Stimmen, Reaktionen, Umfrageantworten, Freiwillige und Veranstaltungsteilnehmende. Bei Abstimmungsphasen löscht diese Aktion die Stimmen, aber nicht die Optionen.", "app.containers.AdminPage.Project.data.title": "Alle Daten von Teil<PERSON>hmenden aus diesem Projekt löschen", "app.containers.AdminPage.Project.resetParticipationData": "Alle Teilnahmedaten löschen", "app.containers.AdminPage.Project.settings.accessRights": "Zugriffsrechte", "app.containers.AdminPage.Project.settings.back": "Zurück", "app.containers.AdminPage.Project.settings.data": "Daten", "app.containers.AdminPage.Project.settings.description": "Beschreibung", "app.containers.AdminPage.Project.settings.events": "Veranstaltungen", "app.containers.AdminPage.Project.settings.general": "Allgemein", "app.containers.AdminPage.Project.settings.projectTags": "Themen", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "{org<PERSON><PERSON>, select, <PERSON>ü<PERSON> {Liste der Bezirke im Mängelmelder} other {Liste der Projekte auf der Plattform}}", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "{org<PERSON><PERSON>, select, <PERSON><PERSON><PERSON> {Bezirke-Dashboard} other {Projekte-Dashboard}}", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "<PERSON><PERSON><PERSON>n Sie neue Projekte oder verwalten Sie bestehende Projekte.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "Projekte", "app.containers.AdminPage.ProjectDashboard.published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.a11y_closeSettingsPanel": "Einstellungsfeld schließen", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentCenter": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentFullWidth": "Volle Breite", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentLeft": "Links", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRadioLabel": "Ausrichtung der Buttons", "app.containers.AdminPage.ProjectDescription.buttonMultilocAlignmentRight": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocText": "Text des Buttons", "app.containers.AdminPage.ProjectDescription.buttonMultilocTextErrorMessage": "Text für den Button eingeben", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypePrimaryLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeRadioLabel": "Text des Buttons", "app.containers.AdminPage.ProjectDescription.buttonMultilocTypeSecondaryLabel": "Sekundär", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrl": "URL des Buttons", "app.containers.AdminPage.ProjectDescription.buttonMultilocUrlErrorMessage": "Geben Sie eine URL für den Button ein", "app.containers.AdminPage.ProjectDescription.columnLayoutRadioLabel": "Spaltenlayout", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Beschreibung", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Kachel-Beschreibung", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Wird auf der Startseite angezeigt.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Wird auf der Projektseite angezeigt. Beschreiben Sie klar und deutlich, worum es bei dem Projekt geht, was <PERSON><PERSON> <PERSON>utzer*innen erwarten und was diese von Ihnen erwarten können.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Etwas ging schief, bitte versuchen Sie es später nochmal", "app.containers.AdminPage.ProjectDescription.preview": "Vorschau", "app.containers.AdminPage.ProjectDescription.save": "Speichern", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.ProjectDescription.saved": "Gespeichert!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Entscheiden Sie sich für die Botschaft, die Sie Ihrem Publikum vermitteln wollen. Bearbeiten Sie Ihr Projekt und bereichern Sie es mit Bildern, Videos, Dateianhängen, usw. Diese Informationen helfen den Besucher*innen zu verstehen, worum es in Ihrem Projekt geht.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Projektbeschreibung", "app.containers.AdminPage.ProjectDescription.whiteSpace": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceDividerLabel": "<PERSON><PERSON><PERSON> e<PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioLarge": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioMedium": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.whiteSpaceRadioSmall": "<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Abbrechen", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "Der Standardbreitengrad des Kartenmittelpunkts. Akzeptiert einen Wert zwischen -90 und 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "Der Standard-Längengrad des Kartenmittelpunkts. Akzeptiert einen Wert zwischen -90 und 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "<PERSON><PERSON><PERSON><PERSON> bear<PERSON>ten", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "<PERSON><PERSON><PERSON> bearbeiten", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Etwas ging schief, bitte versuchen Sie es später nochmal", "app.containers.AdminPage.ProjectEdit.MapTab.here": "hier", "app.containers.AdminPage.ProjectEdit.MapTab.import": "GeoJSON-Datei importieren", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Standard-Breitengrad", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Farbe der Ebene", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "Alle Features in der Ebene werden mit dieser Farbe hervorgehoben. Diese Farbe überschreibt auch ein eventuell vorhandenes Styling in Ihrer GeoJSON-Datei.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Markierungssymbol", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Wählen Sie optional ein Symbol aus, das in den Markern angezeigt wird. Klicken Sie {url}, um die Liste der Symbole zu sehen, die Sie auswählen können.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Layer-Name", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "Dieser Ebenen-Name wird in der Kartenlegende angezeigt", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Layer-<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "Dieser Text wird als QuickInfo angezeigt, wenn Sie den Mauszeiger über die Ebenenmerkmale in der Karte bewegen", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.layersTooltip": "Wir unterstützen derzeit GeoJSON-Dateien. Lesen Sie den {supportArticle} für Tipps zum Konvertieren und Gestalten von Karteneb<PERSON>n.", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Standard-Längengrad", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Standard-Kartenmittelpunkt und -Zoom", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoomTooltip2": "Der Standard-Mittelpunkt und die Standard-Zoomstufe der Karte. Passen Sie die Werte unten manuell an, oder klicken Sie auf den Button {button} in der oberen rechten Ecke der Karte, um den aktuellen Mittelpunkt und die Zoomstufe der Karte als Standardwerte zu speichern.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Passen Sie die Kartenansicht an, einschließlich des Hochladens und Gestaltens von Ka<PERSON> und des Festlegens des Kartenmittelpunkts und der Zoomstufe.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Konfiguration der Karte", "app.containers.AdminPage.ProjectEdit.MapTab.mapLocationWarning": "Die Kartenkonfiguration wird derzeit von allen Phasen gemeinsam genutzt. Sie können keine unterschiedlichen Kartenkonfigurationen pro Phase erstellen.", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "<PERSON><PERSON><PERSON> ent<PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Speichern", "app.containers.AdminPage.ProjectEdit.MapTab.saveZoom": "Zoom s<PERSON>ichern", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "Support-<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticleUrl2": "https://support.govocal.com/de/articles/7022129-sammeln-von-input-und-feedback-listen-und-kartenansicht", "app.containers.AdminPage.ProjectEdit.MapTab.unnamedLayer": "Unbenannte Ebene", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Zoomstufe der Karte", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "Die Standard-Zoomstufe der Karte. Akzeptiert einen Wert zwischen 1 und 17, wobei 1 vollständig herausgezoomt ist (die gesamte Welt ist sichtbar) und 17 vollständig hineingezoomt ist (Häuserblöcke und Gebäude sind sichtbar)", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelMain2": "Anonymisierung aller Nutzer*innendaten", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelSubtext2": "Sämtliche Eingaben der Nutzer*innen im Rahmen der Umfrage werden vor der Speicherung anonymisiert", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userAnonymityLabelTooltip": "Die Nutzer*innen müssen weiterhin die Teilnahmebedingungen auf der Registerkarte \"Zugriffsrechte\" erfüllen. Das Nutzer*innenprofil und die zughörigen Daten sind im Datenexport der Umfrage nicht verfügbar.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyDescription2": "<PERSON>n Si<PERSON> diese Option aktivieren, werden die Felder für die Benutzerregistrierung auf der letzten Seite der Umfrage angezeigt und nicht als Teil des Anmeldevorgangs.", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyTitle2": "Demografische Felder im Umfrageformular", "app.containers.AdminPage.ProjectEdit.NativeSurvey.userFieldsInSurveyToggle2": "Demografische Felder in der Umfrage anzeigen?", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLink": "{link}", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkText": "In diesem Artikel erfahren Si<PERSON> mehr darüber, wie die automatische Freigabe funktioniert.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultSupportArticleLinkUrl2": "https://support.govocal.com/de/articles/8124630-abstimmungs-und-priorisierungsmethoden-fur-eine-verbesserte-entscheidungsfindung", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResults": "Ergebnisse automatisch freigeben", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.autoshareResultsToggleDescription": "Die Abstimmungsergebnisse werden auf der Plattform veröffentlicht und per E-Mail an die Teilnehmenden gesendet, wenn die Phase endet. Dies gewährleistet standardmäßig Transparenz.", "app.containers.AdminPage.ProjectEdit.PhaseParticipationConfig.resultSharing": "Ergebnisfreigabe", "app.containers.AdminPage.ProjectEdit.PollTab.addAnswerOption": "Antwortmöglichkeit hinzufügen", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Frage hinzufügen", "app.containers.AdminPage.ProjectEdit.PollTab.cancelEditAnswerOptions": "Abbrechen", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Abbrechen", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Abbrechen", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Löschen", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Löschen", "app.containers.AdminPage.ProjectEdit.PollTab.editOption1": "Antwortmöglichkeit bearbeiten", "app.containers.AdminPage.ProjectEdit.PollTab.editOptionSave1": "Antwortmöglichkeit speichern", "app.containers.AdminPage.ProjectEdit.PollTab.editPollAnswersButtonLabel1": "Antwortmöglichkeit bearbeiten", "app.containers.AdminPage.ProjectEdit.PollTab.editPollQuestion": "Frage bearbeiten", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Umfrageergebnisse exportieren", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "Die maximale Anzahl der Auswahlmöglichkeiten ist größer als die Anzahl der Optionen", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "<PERSON><PERSON>en", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "Alle Fragen müssen Antwortmöglichkeiten haben", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Nur eine Option", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Umfrageteilnehmende haben nur eine Stimme", "app.containers.AdminPage.ProjectEdit.PollTab.optionsFormHeader1": "Verwalte die Antwortmöglichkeiten für: {questionTitle}", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Hier können Sie Fragen erstellen, die Antwortmöglichkeiten festlegen, aus denen die Teilnehmenden für jede Frage wählen können, entscheiden, ob diese nur eine Antwortmöglichkeit (Einfache Auswahl) oder mehrere Antwortmöglichkeiten (Mehrfachauswahl) auswählen können, und die Umfrageergebnisse exportieren. Sie können mehrere Fragen innerhalb einer Umfrage erstellen.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Speichern", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestion": "Speichern", "app.containers.AdminPage.ProjectEdit.PollTab.saveQuestionSettings": "Speichern", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Einfa<PERSON>uswahl", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Einstellungen und Ergebnisse der Blitz-Umfrage", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Falsches Maximum", "app.containers.AdminPage.ProjectEdit.PostManager.importInputs": "Importieren", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "<PERSON><PERSON><PERSON>, weisen Sie Themen zu oder kopieren Sie Beiträge in die nächste Projektphase.", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputProjectProposals": "Verwalten Sie Vorschläge, geben Sie Feedback und weisen Sie Themen zu.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Beitragsmanager", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff": "Ergebnisfreigabe deaktiviert", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOff2": "Die Abstimmungsergebnisse werden am Ende der Phase nicht geteilt, es sei denn, <PERSON><PERSON> dies in den Phaseneinstellungen.", "app.containers.AdminPage.ProjectEdit.PostManager.votingShareResultsTurnedOn2": "Diese Ergebnisse werden automatisch freigegeben, sobald die Phase endet. Ändern Sie das Enddatum dieser Phase, um zu ändern, wann die Ergebnisse freigegeben werden.", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Exportieren Sie die Umfrageergebnisse (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.resultsTab": "Ergebnisse", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Hier können Sie die Ergebnisse der Typeform-Umfrage(n) innerhalb dieses Projekts als Excel-Datei herunterladen.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyFormTab": "Umfrageformular", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Umfrageergebnisse", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyTab": "Umfrage", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "<PERSON><PERSON> sich die Umfrageergebnisse an", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Sind Sie sicher?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Beschreibung", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> damit, was von den Teilnehmenden verlangt wird und was sie erwarten können.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "<PERSON>nnte nicht gespe<PERSON>rt werden, da das Formular Fehler enthält.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Bild", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Titel", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Löschen", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "Ein Anlass ist eine Aktion oder Aktivität, an der sich Bürger:innen freiwillig beteiligen können.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "<PERSON><PERSON> bear<PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyDescriptionErrorMessage": "Eine Beschreibung hinzufügen", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.emptyTitleErrorMessage": "Einen Titel hinzufügen", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Teilnehmende exportieren", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "Ein Anlass ist eine Aktion oder Aktivität, an der sich Bürger freiwillig beteiligen können.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Speichern", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Hier können Sie Anlässe einrichten, für die sich Nutzer*innen anmelden können, und die Daten der Teilnehmenden herunterladen.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "<PERSON><PERSON>nah<PERSON>", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {kein<PERSON>} one {# Teilnehmende:r} other {# Teilnehmende}}", "app.containers.AdminPage.ProjectEdit.Voting.budgetAllocation2": "Budgetzuweisung", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodSubtitle": "<PERSON>sen Sie den Projekten ein Budget zu und bitten Sie die Teilnehmenden, ihre bevorzugten Projekte auszuwählen, die in ein Gesamtbudget passen.", "app.containers.AdminPage.ProjectEdit.Voting.budgetingVotingMethodTitle2": "Budgetzuweisung", "app.containers.AdminPage.ProjectEdit.Voting.commentingBias": "Das Erlauben von Kommentaren durch Nutzer*innen kann den Abstimmungsprozess beeinflussen.", "app.containers.AdminPage.ProjectEdit.Voting.creditTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.defaultViewOptions": "Standardansicht der Optionen", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsers": "Handlungsoptionen für Nutzer*innen", "app.containers.AdminPage.ProjectEdit.Voting.enabledActionsForUsersDescription": "<PERSON><PERSON><PERSON>en Sie aus, welche zusätzlichen Aktionen Nutzer*innen durchführen können.", "app.containers.AdminPage.ProjectEdit.Voting.fixedAmount": "<PERSON><PERSON><PERSON> begrenzen auf", "app.containers.AdminPage.ProjectEdit.Voting.ifLeftEmpty": "<PERSON><PERSON> das Feld leer ble<PERSON>t, wird standardm<PERSON><PERSON><PERSON> „Stimme(n)“ verwendet.", "app.containers.AdminPage.ProjectEdit.Voting.learnMoreVotingMethod": "<PERSON><PERSON><PERSON><PERSON> mehr darüber, wann <PERSON>e <b> {voteTypeDescription} </b> verwenden sollten, in {optionAnalysisArticleLink}.", "app.containers.AdminPage.ProjectEdit.Voting.maxVotesPerOption": "Maximale Stimmenzahl pro Option", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotes": "Maximale Anzahl von <PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.maximumVotesDescription": "Sie können die Anzahl der Stimmen, die Nutzer*innen insgesamt abgeben können, begrenzen (mit maximal einer Stimme pro Option).", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotesPerOption2": "mehrere Stimmen pro Option", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodSubtitle": "Die Nutzer*innen erhalten eine bestimmte Anzahl von Stimmen, die sie auf verschiedene Optionen verteilen können.", "app.containers.AdminPage.ProjectEdit.Voting.multipleVotingMethodTitle": "<PERSON><PERSON><PERSON> Stimmen pro Option", "app.containers.AdminPage.ProjectEdit.Voting.numberVotesPerUser": "<PERSON><PERSON><PERSON> der Stimmen pro Nutzer*in", "app.containers.AdminPage.ProjectEdit.Voting.optionAnalysisLinkText": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.optionsToVoteOn": "Abstimmungsoptionen", "app.containers.AdminPage.ProjectEdit.Voting.pointTerm": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.Voting.singleVotePerOption2": "eine Stimme pro Option", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodSubtitle": "Nutzer*innen können nur eine Option wählen", "app.containers.AdminPage.ProjectEdit.Voting.singleVotingMethodTitle": "Eine Stimme pro Option", "app.containers.AdminPage.ProjectEdit.Voting.tokenTerm": "Token", "app.containers.AdminPage.ProjectEdit.Voting.unlimited": "Unbegrenzt", "app.containers.AdminPage.ProjectEdit.Voting.voteCalled": "Wie soll eine Stimme genannt werden?", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderPlural2": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Carbon Credits...", "app.containers.AdminPage.ProjectEdit.Voting.voteCalledPlaceholderSingular2": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Carbon Credits...", "app.containers.AdminPage.ProjectEdit.Voting.voteTerm": "Stimme", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorSubtitle": "Jede Abstimmungsmethode hat unterschiedliche Voreinstellungen", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTitle": "Abstimmungsmethoden", "app.containers.AdminPage.ProjectEdit.Voting.votingMethodSelectorTooltip2": "Die Abstimmungsmethode bestimmt die Regeln, nach denen Nutzer*innen abstimmen", "app.containers.AdminPage.ProjectEdit.addNewInput": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.adminProjectFolderSelectTooltip2": "Sie können Ihr Projekt jetzt zu einem Ordner hinzufügen, oder dies später in den Projekteinstellungen tun", "app.containers.AdminPage.ProjectEdit.allowedInputTopicsTab": "Themen", "app.containers.AdminPage.ProjectEdit.altText": "Alt-Text", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonyme <PERSON>gen", "app.containers.AdminPage.ProjectEdit.anonymousPollingTooltip": "<PERSON>n diese Funktion aktiviert ist, ist es unmöglich zu sehen, wer wie abgestimmt hat. Die Nutzer*innen benötigen weiterhin ein Konto und können nur einmal abstimmen.", "app.containers.AdminPage.ProjectEdit.approved": "Freigeben", "app.containers.AdminPage.ProjectEdit.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.archivedExplanationText": "Archivierte Projekte sind noch sichtbar, erlauben aber keine weitere Beteiligung", "app.containers.AdminPage.ProjectEdit.archivedStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.areaIsLinkedToStaticPage": "Dieses Gebiet kann nicht gel<PERSON> werden, da er zur Anzeige von Projekten auf der/den folgenden benutzerdefinierten Seite(n) verwendet wird. Sie müssen die Verknüpfung des Gebiets mit der Seite aufheben oder die Seite löschen, bevor <PERSON>e das Gebiet löschen können.", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "Alle Gebiete", "app.containers.AdminPage.ProjectEdit.areasAllLabelDescription": "Das Projekt wird in jedem Gebietsfilter angezeigt.", "app.containers.AdminPage.ProjectEdit.areasLabelHint": "Gebietsfilter", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipHint": "Projekte können auf der Startseite nach Gebieten gefiltert werden. Gebiete können {areasLabelTooltipLink} festgelegt werden.", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "hier", "app.containers.AdminPage.ProjectEdit.areasNoneLabel": "<PERSON><PERSON> s<PERSON> Gebiet", "app.containers.AdminPage.ProjectEdit.areasNoneLabelDescription": "Das Projekt wird nicht angezeigt, wenn nach Gebiet gefiltert wird.", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Auswahl", "app.containers.AdminPage.ProjectEdit.areasSelectionLabelDescription": "Das Projekt wird in dem/den ausgewählten Gebietsfilter(n) angezeigt.", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Liste", "app.containers.AdminPage.ProjectEdit.commens_countSortingMethod": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.communityMonitor.addSurveyContent2": "Umfrageinhalte hinzufügen", "app.containers.AdminPage.ProjectEdit.communityMonitor.existingSubmissionsWarning": "Die ersten Einsendungen für diese Umfrage sind bereits eingetroffen. Änderungen an der Umfrage können zu Datenverlusten und unvollständigen Daten in den exportierten Dateien führen.", "app.containers.AdminPage.ProjectEdit.communityMonitor.successMessage2": "Umfrage erfolgreich gespeichert", "app.containers.AdminPage.ProjectEdit.communityMonitor.supportArticleLink2": "https://support.govocal.com/de/articles/6673873-erstellen-einer-erweiterten-umfrage", "app.containers.AdminPage.ProjectEdit.communityMonitor.survey2": "Umfrage", "app.containers.AdminPage.ProjectEdit.communityMonitor.viewSurvey2": "<PERSON><PERSON><PERSON> an<PERSON>", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationDescriptionText2": "Wählen Sie eine Abstimmungsmethode und lassen Sie die Nutzer*innen zwischen verschiedenen Optionen wählen.", "app.containers.AdminPage.ProjectEdit.conductVotingOrPrioritizationText": "Abstimmung oder Budgetzuweisung durchführen", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "<PERSON>rstellen eines Projekts aus einer Vorlage", "app.containers.AdminPage.ProjectEdit.createExternalSurveyText": "Externe Umfrage einbetten", "app.containers.AdminPage.ProjectEdit.createInput": "Neuen Beitrag hinzufügen", "app.containers.AdminPage.ProjectEdit.createNativeSurvey": "Erweiterte Umfrage erstellen", "app.containers.AdminPage.ProjectEdit.createNativeSurveyDescription": "<PERSON><PERSON><PERSON>n Sie eine ausführliche Umfrage mit verschiedenen Methoden, Logiken und Seiten direkt auf der Plattform.", "app.containers.AdminPage.ProjectEdit.createPoll": "Blitz-Umfrage erstellen", "app.containers.AdminPage.ProjectEdit.createPollDescription": "<PERSON><PERSON><PERSON>n Sie eine kurze und schnelle Umfrage.", "app.containers.AdminPage.ProjectEdit.createProject": "Neues Projekt", "app.containers.AdminPage.ProjectEdit.createSurveyDescription": "Betten Sie eine Umfrage von Typeform, Google Form, Enalyzer, SurveyXact, Qualtrics, SmartSurvey, Snap Survey oder Microsoft Forms ein.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "Sie können die Standardreihenfolge der Beiträge festlegen, die auf der Hauptprojektseite angezeigt werden sollen.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.departments": "Abteilungen", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Beschreibung", "app.containers.AdminPage.ProjectEdit.disableDislikingTooltip2": "<PERSON><PERSON><PERSON> wird die Option „Gefällt mir nicht“ aktiviert oder deaktiviert, die Option „Gefällt mir“ bleibt jedoch aktiviert. Wir empfehlen, diese Option deaktiviert zu lassen, es sei denn, Sie führen eine Optionsanalyse durch.", "app.containers.AdminPage.ProjectEdit.disabled": "Deaktiviert", "app.containers.AdminPage.ProjectEdit.dislikingMethodTitle": "Anzahl der Gegenstimmen/Gefällt mir nicht pro Teilnehmendem", "app.containers.AdminPage.ProjectEdit.dislikingPosts": "Aktivieren Sie Gegenstimmen/\"G<PERSON><PERSON><PERSON>t mir nicht\"", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethod1": "Feed<PERSON> zu einem Dokument sammeln", "app.containers.AdminPage.ProjectEdit.documentAnnotationMethodDescription1": "Betten Sie ein interaktives PDF ein und sammeln Sie Kommentare und Feedback mit Konveio.", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Deaktiviert", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Aktiviert", "app.containers.AdminPage.ProjectEdit.dradftExplanationText": "Projektentwürfe sind für alle Personen außer Admins und zugewiesenen Projektmanager*innen verborgen.", "app.containers.AdminPage.ProjectEdit.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.draftStatus": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.enabled": "Aktiviert", "app.containers.AdminPage.ProjectEdit.enabledActionsTooltip2": "<PERSON><PERSON><PERSON>en Sie aus, welche Beteiligungsmethoden Nutzer*innen durchführen können.", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Veranstaltungen", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "<PERSON>ien sollten nicht größer als 50Mb sein. Hinzugefügte Dateien werden auf der Projektinformationsseite angezeigt.", "app.containers.AdminPage.ProjectEdit.filesTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Te<PERSON><PERSON><PERSON><PERSON><PERSON> finden", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Bitten Sie Teilnehmende, sich freiwillig für Aktivitäten und Anlässe zu melden.", "app.containers.AdminPage.ProjectEdit.folderAdminProjectFolderSelectTooltip2": "Als Ordnermanager*in können Sie bei der Erstellung des Projekts einen Ordner auswählen, aber nur ein Admin kann ihn nachträglich ändern", "app.containers.AdminPage.ProjectEdit.folderImageAltTextTitle": "Alternativtext für Ordnerkartenbild", "app.containers.AdminPage.ProjectEdit.folderSelectError": "<PERSON><PERSON><PERSON>en Si<PERSON> einen Ordner aus, dem dieses Projekt hinzugefügt werden soll.", "app.containers.AdminPage.ProjectEdit.formBuilder.customToolboxTitle": "Zusätzliche Felder", "app.containers.AdminPage.ProjectEdit.formBuilder.existingSubmissionsWarning": "Die ersten Einsendungen zu diesem Formular sind bereits eingegangen. Änderungen an dem Formular können zu Datenverlusten und unvollständigen Daten in den exportierten Dateien führen.", "app.containers.AdminPage.ProjectEdit.formBuilder.successMessage": "Formular er<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>peichert", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd2": "Ende der Umfrage", "app.containers.AdminPage.ProjectEdit.fromATemplate": "Aus einer Vorlage", "app.containers.AdminPage.ProjectEdit.generalTab": "Allgemein", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.headerImageAltText": "Alt-Text für Kopfzeilenbild", "app.containers.AdminPage.ProjectEdit.headerImageInputLabel": "Bild in der Kopfzeile", "app.containers.AdminPage.ProjectEdit.imageSupportPageURL": "https://support.govocal.com/de/articles/1346397-welche-dimensionen-und-grossen-sind-fur-bilder-auf-der-plattform-empfohlen", "app.containers.AdminPage.ProjectEdit.information.new1": "NEU", "app.containers.AdminPage.ProjectEdit.information.provideInformation": "<PERSON>ellen Si<PERSON> den Nutzer*innen Informationen zur Verfügung oder erstellen Sie einen Bericht, um die Ergebnisse vergangener Phasen zu teilen.", "app.containers.AdminPage.ProjectEdit.information.shareInformationOrResults": "Informationen oder Ergebnisse teilen", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Beiträge und Feedback sammeln", "app.containers.AdminPage.ProjectEdit.inputAndFeedbackDescription2": "<PERSON>rstellen oder sammeln Sie Beiträge, Reaktionen und/oder Kommentare. Wählen Sie zwischen verschiedenen Arten von Beiträgen: Ideensammlung, Optionsanalyse, Frage und Antwort, Problemidentifizierung und mehr.", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Wer ist für die Bearbeitung der Beiträge zuständig?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "Alle neuen Beiträge in diesem Projekt werden dieser Person zugewiesen. Der Zuweisende kann in der {ideaManagerLink} geändert werden.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Kommentieren", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Eingabeformular", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "Beitragsmanager", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Beiträge", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Beiträge einreichen", "app.containers.AdminPage.ProjectEdit.inputReactingEnabled": "Beiträge liken und disliken (Daumen hoch und runter)", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Standardansicht", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Wählen Sie die Standardansicht für die Anzeige von Beiträgen: Ka<PERSON> in einer Listenansicht oder Pins auf einer Karte. Die Teilnehmenden können manuell zwischen den beiden Ansichten wechseln.", "app.containers.AdminPage.ProjectEdit.inspirationHub": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.konveioDocumentAnnotationEmbedUrl": "Konveio URL einbetten", "app.containers.AdminPage.ProjectEdit.likingMethodTitle": "<PERSON><PERSON><PERSON> der Likes/Gefällt mir pro Teilnehmendem", "app.containers.AdminPage.ProjectEdit.limited": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Weitere Vorlagen laden", "app.containers.AdminPage.ProjectEdit.mapDisplay": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.mapTab": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.maxDislikes": "Maximale Gegenstimmen/Gefällt mir nicht", "app.containers.AdminPage.ProjectEdit.maxLikes": "Maximum Likes/Gefällt mir", "app.containers.AdminPage.ProjectEdit.maxVotesPerOptionErrorText": "Die maximale Anzahl der Stimmen pro Option muss kleiner oder gleich der Gesamtzahl der Stimmen sein", "app.containers.AdminPage.ProjectEdit.maximum": "Maximum", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Teilnehmende können dieses Budget nicht überschreiten, wenn sie ihren Warenkorb einreichen.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Verlangen Sie von den Teilnehmenden ein Mindestbudget, damit diese ihren Warenkorb einreichen können (g<PERSON><PERSON> \"0\" ein, wenn Sie keinen Mindestbetrag festlegen möchten).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "Besuchen Sie unsere Wissensdatenbank", "app.containers.AdminPage.ProjectEdit.moderatorSearchFieldLabel1": "Wer sind die Projektmanager*innen?", "app.containers.AdminPage.ProjectEdit.moreDetails": "Mehr Eigenschaften", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/articles/2672884", "app.containers.AdminPage.ProjectEdit.needInspiration": "Brauchen Sie Inspiration? Entdecken Sie ähnliche Projekte aus anderen Städten auf {inspirationHubLink}", "app.containers.AdminPage.ProjectEdit.newContribution": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.newIdea": "<PERSON>euer Beitrag", "app.containers.AdminPage.ProjectEdit.newInitiative": "Initiative hinzufügen", "app.containers.AdminPage.ProjectEdit.newIssue": "Ein Problem hinzufügen", "app.containers.AdminPage.ProjectEdit.newOption": "Eine Option hinzufügen", "app.containers.AdminPage.ProjectEdit.newPetition": "Petition hinzufügen", "app.containers.AdminPage.ProjectEdit.newProject": "Neues Projekt", "app.containers.AdminPage.ProjectEdit.newProposal": "Vorschlag hinzufügen", "app.containers.AdminPage.ProjectEdit.newQuestion": "Eine Frage hinzufügen", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Neueste", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.noFolder": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.noFolderLabel": "<PERSON> <PERSON><PERSON> —", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "<PERSON><PERSON>lagen gefunden", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Dies kann nicht leer sein", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "<PERSON><PERSON> gü<PERSON>ige <PERSON>", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Älteste", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "<PERSON><PERSON> intern sic<PERSON>bar", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionYes": "Ja (Ordner auswählen)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Höhe der Beteiligung", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "Beteiligungsmethoden", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "<PERSON><PERSON><PERSON><PERSON>, wie Nutzer*innen teilnehmen können.", "app.containers.AdminPage.ProjectEdit.participationRequirementsSubtitle": "<PERSON>e können festlegen, wer jede Aktion durchführen darf, und den Teilnehmenden zusätzliche Fragen stellen, um weitere Informationen zu sammeln.", "app.containers.AdminPage.ProjectEdit.participationRequirementsTitle": "Teilnahmebedingungen & Fragen", "app.containers.AdminPage.ProjectEdit.pendingReview": "Freigabe ausstehend", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Zugriffsrechte", "app.containers.AdminPage.ProjectEdit.phaseAccessRights": "Zugriffsrechte", "app.containers.AdminPage.ProjectEdit.pollTab": "Blitz-Umfrage", "app.containers.AdminPage.ProjectEdit.popularSortingMethod2": "Meiste Reaktionen", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Projektbild", "app.containers.AdminPage.ProjectEdit.projectCardImageTooltip": "Dieses Bild ist Teil der Projektkarte, also der Karte, die das Projekt zusammenfasst und beispielsweise auf der Homepage angezeigt wird.\n\nWeitere Informationen zu empfohlenen Bildauflösungen finden Sie in {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectFolderSelectTitle1": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.projectHeaderImageTooltip": "Dieses Bild wird oben auf der Projektseite angezeigt.\n\nWeitere Informationen zu empfohlenen Bildauflösungen finden Sie in {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTitle1": "Alternativtext für das Projektkartenbild", "app.containers.AdminPage.ProjectEdit.projectImageAltTextTooltip1": "Geben Sie eine kurze Beschreibung des Bildes für sehbehinderte Nutzende an. Dies hilft Bildschirmleser*innen zu verstehen, worum es sich bei dem Bild handelt.", "app.containers.AdminPage.ProjectEdit.projectManagementTitle": "Projektmanager*innen", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Projektmanager*innen können Projekte bearbeiten und Beiträge verwalten. Sie können {moderationInfoCenterLink}, um mehr Informationen über die den Projektmanager*innen zugewiesenen Rechte zu finden.", "app.containers.AdminPage.ProjectEdit.projectName": "Projektname", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Projekttyp", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projekte mit einer Zeitleiste haben einen klaren Anfang und ein Ende und können verschiedene Phasen haben. Projekte ohne Zeitleiste sind kontinuierlich.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "Der Projekttyp kann später nicht mehr geändert werden.", "app.containers.AdminPage.ProjectEdit.projectVisibilitySubtitle": "Sie können das Projekt so einstellen, dass es für bestimmte Nutzer*innen unsichtbar ist.", "app.containers.AdminPage.ProjectEdit.projectVisibilityTitle": "Sichtbarkeit des Projekts", "app.containers.AdminPage.ProjectEdit.publicationStatusWarningMessage": "Suchst du den Projektstatus? Du kannst ihn jetzt jederzeit direkt in der Kopfzeile des Projektes ändern.", "app.containers.AdminPage.ProjectEdit.publishedExplanationText": "Veröffentlichte Projekte sind für alle oder nur für eine Teilgruppe sichtbar, wenn diese ausgewählt wurde.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.purposes": "Verwendungszwecke", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.resetParticipationData": "Teilnahme-<PERSON><PERSON> zur<PERSON>", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "<PERSON>im S<PERSON>ichern Ihrer Daten ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "app.containers.AdminPage.ProjectEdit.saveProject": "Speichern", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Gespeichert!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Ihre Änderungen wurden gespeichert!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Suche in den Vorlagen", "app.containers.AdminPage.ProjectEdit.selectGroups": "Gruppe(n) auswählen", "app.containers.AdminPage.ProjectEdit.setup": "Einstellungen", "app.containers.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON> teilen", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.snap_survey": "Snap Survey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Richten Sie Ihr Projekt ein und personalisieren Sie es.", "app.containers.AdminPage.ProjectEdit.supportPageLinkText": "unserer Wissensdatenbank", "app.containers.AdminPage.ProjectEdit.survey.addSurveyContent2": "FRAGEN", "app.containers.AdminPage.ProjectEdit.survey.cancelQuitButtonText": "Abbrechen", "app.containers.AdminPage.ProjectEdit.survey.choiceCount2": "{percentage}% ({choiceCount, plural, no {# Antworten} one {# Antwort} other {# Antworten}})", "app.containers.AdminPage.ProjectEdit.survey.confirmQuitButtonText1": "<PERSON><PERSON>, zurück ohne zu speichern", "app.containers.AdminPage.ProjectEdit.survey.editSurvey2": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.existingSubmissionsWarning": "Die ersten Einsendungen für diese Umfrage sind bereits eingetroffen. Änderungen an der Umfrage können zu Datenverlusten und unvollständigen Daten in den exportierten Dateien führen.", "app.containers.AdminPage.ProjectEdit.survey.file_upload": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.goBackButtonMessage": "Zurück", "app.containers.AdminPage.ProjectEdit.survey.importInputs": "Importieren", "app.containers.AdminPage.ProjectEdit.survey.importInputs2": "Importieren", "app.containers.AdminPage.ProjectEdit.survey.informationText4": "KI-Zusammenfassungen für kurze und lange Antworten sowie für Fragen zur Stimmungsskala können Sie über die Registerkarte KI in der linken Seitenleiste aufrufen.", "app.containers.AdminPage.ProjectEdit.survey.linear_scale2": "Lineare Skala", "app.containers.AdminPage.ProjectEdit.survey.matrix_linear_scale2": "Matrix", "app.containers.AdminPage.ProjectEdit.survey.multiline_text2": "Lange Antwort", "app.containers.AdminPage.ProjectEdit.survey.multiselectText2": "Mehrfachauswahl - wählen Si<PERSON> mehrere aus", "app.containers.AdminPage.ProjectEdit.survey.multiselect_image": "Bildauswahl - Mehr<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.newSubmission1": "Neue Einreichung", "app.containers.AdminPage.ProjectEdit.survey.noSurveyResponses2": "Noch keine Umfrageantworten", "app.containers.AdminPage.ProjectEdit.survey.openForResponses2": "Umfrage geöffnet", "app.containers.AdminPage.ProjectEdit.survey.openForResponses3": "Umfrage geöffnet", "app.containers.AdminPage.ProjectEdit.survey.optional2": "Optional", "app.containers.AdminPage.ProjectEdit.survey.pagesLogicHelperText1": "Wenn keine Logik hinzugefügt wird, folgt die Umfrage ihrem normalen Ablauf. Wenn sowohl die Seite als auch die Fragen eine Logik enthalten, hat die Fragelogik Vorrang. <PERSON><PERSON><PERSON>, dass dies mit dem von Ihnen beabsichtigten Ablauf der Umfrage übereinstimmt. Weitere Informationen finden Sie unter {supportPageLink}.", "app.containers.AdminPage.ProjectEdit.survey.point": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.quitReportConfirmationQuestion": "Sind <PERSON> sic<PERSON>, dass Sie das Fenster schließen möchten ohne zu speichern?", "app.containers.AdminPage.ProjectEdit.survey.quitReportInfo2": "Aktuelle Änderungen werden nicht gespeichert.", "app.containers.AdminPage.ProjectEdit.survey.ranking2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.rating": "Bewertung", "app.containers.AdminPage.ProjectEdit.survey.required2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.response": "{choiceCount, plural, no {Antworten} one {Antwort} other {Antworten}}", "app.containers.AdminPage.ProjectEdit.survey.responseCount": "{choiceCount, plural, no {# Antworten} one {# Antwort} other {# Antworten}}", "app.containers.AdminPage.ProjectEdit.survey.selectText2": "Multiple Choice - wählen Sie eins aus", "app.containers.AdminPage.ProjectEdit.survey.sentiment_linear_scale": "Lineare Stimmungsskala", "app.containers.AdminPage.ProjectEdit.survey.shapefile_upload": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.survey.successMessage2": "Umfrage erfolgreich gespeichert", "app.containers.AdminPage.ProjectEdit.survey.supportArticleLink2": "https://support.govocal.com/de/articles/6673873-erstellen-einer-plattforminternen-umfrage", "app.containers.AdminPage.ProjectEdit.survey.survey2": "Umfrage", "app.containers.AdminPage.ProjectEdit.survey.surveyResponses": "Umfrageantworten", "app.containers.AdminPage.ProjectEdit.survey.text2": "Kurze Antwort", "app.containers.AdminPage.ProjectEdit.survey.totalSurveyResponses2": "Insgesamt {count} Antwort(en)", "app.containers.AdminPage.ProjectEdit.survey.viewSurvey2": "<PERSON><PERSON><PERSON> an<PERSON>", "app.containers.AdminPage.ProjectEdit.survey.viewSurveyText2": "Vorschau", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "URL einbetten", "app.containers.AdminPage.ProjectEdit.surveyService": "Umfragetool", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "Weitere Informationen zum Einbetten einer Umfrage finden Sie {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLink1": "https://support.govocal.com/de/articles/7025887-erstellen-eines-externen-umfrageprojekts", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "hier", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.tagIsLinkedToStaticPage": "Dieses Themen-Tag kann nicht gel<PERSON> werden, da es zur Anzeige von Projekten auf der/den folgenden benutzerdefinierten Seite(n) verwendet wird. Sie müssen die Verknüpfung des Tags mit der Seite aufheben oder die Seite löschen, bevor <PERSON>e den Tag löschen können.", "app.containers.AdminPage.ProjectEdit.titleGeneral": "Allgemeine Einstellungen für das Projekt", "app.containers.AdminPage.ProjectEdit.titleLabel": "Titel", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "W<PERSON>hlen Sie einen Titel, der kurz, einprägsam und klar ist. Er wird in der Dropdown-Übersicht und auf den Projekten auf der Startseite angezeigt.", "app.containers.AdminPage.ProjectEdit.topicLabel": "Themen-Tags", "app.containers.AdminPage.ProjectEdit.topicLabelTooltip": "{topicsCopy} für dieses Projekt auswählen. Nutzer*nnen können diese verwenden, um Projekte zu filtern.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Gesamtbudget", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Beliebt", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "<PERSON>cht zugewiesen", "app.containers.AdminPage.ProjectEdit.unlimited": "Unbegrenzt", "app.containers.AdminPage.ProjectEdit.url": "URL", "app.containers.AdminPage.ProjectEdit.useTemplate": "Vor<PERSON> verwenden", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "<PERSON><PERSON><PERSON> an<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "<PERSON><PERSON>nah<PERSON>", "app.containers.AdminPage.ProjectEdit.voteTermError": "Abstimmungsbedingungen müssen für alle Sprachen angegeben werden", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {sichtbar für # Gruppen} one {sichtbar für # Gruppe} other {sichtbar für # Gruppen}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Veranstaltung hinzufügen", "app.containers.AdminPage.ProjectEvents.additionalInformation": "Zusätzliche Informationen", "app.containers.AdminPage.ProjectEvents.addressOneLabel": "Adresse 1", "app.containers.AdminPage.ProjectEvents.addressOneTooltip": "Straße und Hausnummer des Veranstaltungsortes", "app.containers.AdminPage.ProjectEvents.addressTwoLabel": "Adresse 2", "app.containers.AdminPage.ProjectEvents.addressTwoPlaceholder": "z.B. Gebäudenummer, etc.", "app.containers.AdminPage.ProjectEvents.addressTwoTooltip": "Zusätzliche Adressinformationen, die zur Identifizierung des Standorts beitragen können, wie z. B. der Name des Gebäudes, die Stockwerksnummer usw.", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLink": "https://support.govocal.com/de/articles/5481527-hi<PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON><PERSON><PERSON>-zu-ihrer-plattform", "app.containers.AdminPage.ProjectEvents.attendanceSupportArticleLinkText": "Support-<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonLink": "Externer Link", "app.containers.AdminPage.ProjectEvents.customButtonLinkTooltip2": "<PERSON>ügen Sie einen Link zu einer externen URL hinzu (z.B. Veranstaltungsservice oder Ticketing-Website). <PERSON><PERSON> <PERSON><PERSON> dies einstellen, wird das Standardverhalten der Teilnahme-Schaltfläche außer Kraft gesetzt.", "app.containers.AdminPage.ProjectEvents.customButtonText": "Benutzerdefiniert<PERSON>", "app.containers.AdminPage.ProjectEvents.customButtonTextTooltip3": "Setzen Sie den Text der Schaltfläche auf einen anderen Wert als \"Registrieren\", wenn eine externe URL eingestellt ist.", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Löschen", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Sind <PERSON> sic<PERSON>, dass Sie dieses Ereignis löschen möchten? Es gibt keine Möglichkeit, dies rückgängig zu machen!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Beschreibung der Veranstaltung", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Veranstaltung bearbeiten", "app.containers.AdminPage.ProjectEvents.eventAttendanceExport1": "Um registrierte Personen direkt von der Plattform aus per E-Mail zu kontaktieren, müssen Administratoren auf der Registerkarte {userTabLink} eine Benutzergruppe erstellen. {supportArticleLink}.", "app.containers.AdminPage.ProjectEvents.eventDates": "Veranstaltungstermine", "app.containers.AdminPage.ProjectEvents.eventImage": "Bild der Veranstaltung", "app.containers.AdminPage.ProjectEvents.eventImageAltTextTitle": "Alternativtext für das Veranstaltungsbild", "app.containers.AdminPage.ProjectEvents.eventLocation": "Veranstaltungsort", "app.containers.AdminPage.ProjectEvents.exportRegistrants": "Teilnehmende exportieren", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Anhänge werden unterhalb der Ereignisbeschreibung angezeigt.", "app.containers.AdminPage.ProjectEvents.locationLabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.maximumRegistrants": "Maximale Anzahl von <PERSON>", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Eine neue Veranstaltung erstellen", "app.containers.AdminPage.ProjectEvents.onlineEventLinkLabel": "Link zur Online-Veranstaltung", "app.containers.AdminPage.ProjectEvents.onlineEventLinkTooltip": "<PERSON><PERSON> Ihre Veranstaltung online ist, fügen Si<PERSON> hier einen Link hinzu.", "app.containers.AdminPage.ProjectEvents.preview": "Vorschau", "app.containers.AdminPage.ProjectEvents.refineLocationCoordinates": "Kartenstandort anpassen", "app.containers.AdminPage.ProjectEvents.refineOnMap": "Standort auf der Karte anpassen", "app.containers.AdminPage.ProjectEvents.refineOnMapInstructions": "Sie können die Anzeige Ihres Veranstaltungsortes verfeinern, indem Sie auf die Karte unten klicken.", "app.containers.AdminPage.ProjectEvents.register": "Melden Sie sich an", "app.containers.AdminPage.ProjectEvents.registerButton": "Registrieren-Button", "app.containers.AdminPage.ProjectEvents.registrant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.registrants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEvents.registrationLimit": "Anmeldebegrenzung", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Speichern", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "Wir konnten Ihre Änderungen nicht speichern, bitte versuchen Si<PERSON> es erneut.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Gespeichert!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.ProjectEvents.searchForLocation": "Nach einem Ort suchen", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Verknüpfen Sie anstehende Veranstaltungen mit diesem Projekt und zeigen Sie diese auf der Veranstaltungsseite des Projekts an.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Titel und Daten", "app.containers.AdminPage.ProjectEvents.titleEvents": "Projektveranstaltungen", "app.containers.AdminPage.ProjectEvents.titleLabel": "Name der Veranstaltung", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonLabel": "Button mit einer externen URL verknüpfen", "app.containers.AdminPage.ProjectEvents.toggleCustomRegisterButtonTooltip2": "Standardmäßig wird der plattforminterne Registrierungsbutton für Veranstaltungen angezeigt, über den sich Benutzer für eine Veranstaltung anmelden können. Sie können dies ändern und stattdessen auf eine externe URL verweisen.", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitLabel": "Anzahl der Teilnehmenden an der Veranstaltung begrenzen", "app.containers.AdminPage.ProjectEvents.toggleRegistrationLimitTooltip": "Legen Sie eine Höchstzahl von Teilnehmenden für die Veranstaltung fest. Wenn das Limit erreicht ist, werden keine weiteren Anmeldungen mehr angenommen.", "app.containers.AdminPage.ProjectEvents.usersTabLink": "/admin/users", "app.containers.AdminPage.ProjectEvents.usersTabLinkText": "Nutzer*innen", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject": "<PERSON><PERSON> zu Ihrem Projekt hinzufügen", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription": "Hängen Sie Dateien aus dieser Liste an Ihr Projekt, Ihre Phasen und Veranstaltungen an.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking": "Dateien als Kontext zur KI-Analyse hinzufügen", "app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription": "<PERSON>ügen Sie Dateien zu Ihrer KI-<PERSON><PERSON><PERSON> hi<PERSON>, um Kontext und Einblicke zu liefern.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon": "Demnächst verfügbar", "app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription": "Synchronisie<PERSON> Umfragen, laden Sie Interviews hoch und lassen Sie die KI die wesentlichen Erkenntnisse herausarbeiten.", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile": "Beliebiges Dateiformat hochladen", "app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription": "PDF, DOCX, PPTX, CSV, PNG, MP3...", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles": "Verwenden Sie AI zum Analysieren von <PERSON>", "app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription": "Bearbeiten Sie Transkripte, etc.", "app.containers.AdminPage.ProjectFiles.addFiles": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.aiPoweredInsights": "KI-gestützte Einblicke", "app.containers.AdminPage.ProjectFiles.aiPoweredInsightsDescription": "Analy<PERSON>ren Si<PERSON> hochgeladene Date<PERSON>, um wichtige Themen zu ermitteln.", "app.containers.AdminPage.ProjectFiles.allowAiProcessing": "Ermöglichen Sie eine erweiterte Analyse dieser Dateien mithilfe von KI-Verarbeitung.", "app.containers.AdminPage.ProjectFiles.askButton": "Fragen", "app.containers.AdminPage.ProjectFiles.categoryLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.chooseFiles": "<PERSON><PERSON> au<PERSON>wählen", "app.containers.AdminPage.ProjectFiles.close": "Schließen", "app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles": "Bestätigen und hochladen", "app.containers.AdminPage.ProjectFiles.confirmDelete": "Sind <PERSON> sicher, dass Sie diese Datei löschen möchten?", "app.containers.AdminPage.ProjectFiles.couldNotLoadMarkdown": "Die Markdown-<PERSON><PERSON> konnte nicht geladen werden.", "app.containers.AdminPage.ProjectFiles.csvPreviewError": "Die CSV-Vorschau konnte nicht geladen werden.", "app.containers.AdminPage.ProjectFiles.csvPreviewLimit": "In der CSV-Vorschau werden maximal 50 Zeilen angezeigt.", "app.containers.AdminPage.ProjectFiles.csvPreviewTooLarge": "Die CSV-Datei ist zu groß für die Vorschau.", "app.containers.AdminPage.ProjectFiles.deleteFile2": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.description": "Beschreibung", "app.containers.AdminPage.ProjectFiles.done": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFile": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.downloadFullFile": "Vollständige Datei herunt<PERSON>laden", "app.containers.AdminPage.ProjectFiles.dragAndDropFiles2": "<PERSON><PERSON><PERSON> Si<PERSON> beliebige Dateien per Drag & Drop hierher oder", "app.containers.AdminPage.ProjectFiles.editFile": "<PERSON><PERSON> bearbeiten", "app.containers.AdminPage.ProjectFiles.fileDescriptionLabel": "Beschreibung", "app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot": "Der Dateiname darf keinen Punkt enthalten.", "app.containers.AdminPage.ProjectFiles.fileNameLabel": "Dateiname", "app.containers.AdminPage.ProjectFiles.fileNameRequired": "Der Dateiname ist erforderlich.", "app.containers.AdminPage.ProjectFiles.filePreview.downloadFile": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.filePreviewLabel2": "Vorschau", "app.containers.AdminPage.ProjectFiles.fileSizeError2": "Diese Datei kann nicht hoch<PERSON>aden werden, da sie die maximale Größe von 50 MB überschreitet.", "app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully": "Alle Dateien erfolgreich hochgeladen", "app.containers.AdminPage.ProjectFiles.info_sheet": "Informationen", "app.containers.AdminPage.ProjectFiles.informationPoint1Description": "z.B. WAV, MP3", "app.containers.AdminPage.ProjectFiles.informationPoint1Title": "Audio-Interviews, Meeting-<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.informationPoint2Description": "z.B. PDF, DOCX, PPTX", "app.containers.AdminPage.ProjectFiles.informationPoint2Title": "Berichte, Informationsdokumente", "app.containers.AdminPage.ProjectFiles.informationPoint3Description": "z.B. PNG, JPG", "app.containers.AdminPage.ProjectFiles.informationPoint3Title": "Bilder", "app.containers.AdminPage.ProjectFiles.interview": "Interview", "app.containers.AdminPage.ProjectFiles.maxFilesError": "Sie können nur maximal {maxFiles} <PERSON>ien auf einmal hochladen.", "app.containers.AdminPage.ProjectFiles.meeting": "Meeting", "app.containers.AdminPage.ProjectFiles.noFilesFound": "<PERSON><PERSON> gefunden.", "app.containers.AdminPage.ProjectFiles.other": "Sonstiges", "app.containers.AdminPage.ProjectFiles.policy": "Rich<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectFiles.previewNotSupported": "Die Vorschau wird für diesen Dateityp noch nicht unterstützt.", "app.containers.AdminPage.ProjectFiles.report": "Bericht", "app.containers.AdminPage.ProjectFiles.retryUpload": "Upload wiederholen", "app.containers.AdminPage.ProjectFiles.save": "Speichern", "app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage": "Datei erfolgreich aktualisiert.", "app.containers.AdminPage.ProjectFiles.searchFiles": "<PERSON><PERSON> suchen", "app.containers.AdminPage.ProjectFiles.selectFileType": "Dateityp", "app.containers.AdminPage.ProjectFiles.strategic_plan": "Strategieplan", "app.containers.AdminPage.ProjectFiles.tooManyFiles": "Sie können nur maximal {maxFiles} <PERSON>ien auf einmal hochladen.", "app.containers.AdminPage.ProjectFiles.unknown": "Unbekannt", "app.containers.AdminPage.ProjectFiles.upload": "Hochladen", "app.containers.AdminPage.ProjectFiles.uploadSummary": "{numberOfFiles, plural, one {# Datei} other {# Dateien}} erfolg<PERSON><PERSON> hoch<PERSON>n, {numberOfErrors, plural, one {# <PERSON><PERSON>} other {# <PERSON><PERSON>}}.", "app.containers.AdminPage.ProjectFiles.viewFile": "<PERSON><PERSON> an<PERSON>", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Alle Felder e<PERSON>pen", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Feldbeschreibung", "app.containers.AdminPage.ProjectIdeaForm.editInputForm": "Eingabeformular bearbeiten", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Aktiviert", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "<PERSON><PERSON><PERSON> dieses Feld ein.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Etwas ging schief, bitte versuchen Sie es später nochmal", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Alle Felder aufklappen", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Eingabeformular", "app.containers.AdminPage.ProjectIdeaForm.inputFormDescription": "Legen Sie fest, welche Informationen angegeben werden sollen, fügen Sie kurze Beschreibungen oder Anweisungen hinzu, um die Teilnehmenden bei der Beantwortung zu unterstützen, und geben Si<PERSON> an, ob jedes <PERSON> optional oder erforderlich ist.", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Legen Sie fest, welche Informationen bereitgestellt werden sollen, fügen Sie kurze Beschreibungen oder Anweisungen hinzu, um die Antworten der Teilnehmenden anzuleiten, und geben Si<PERSON> an, ob jede<PERSON> oder erforderlich ist", "app.containers.AdminPage.ProjectIdeaForm.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "<PERSON><PERSON> muss ausgefüllt werden.", "app.containers.AdminPage.ProjectIdeaForm.save": "Speichern", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Gespeichert!", "app.containers.AdminPage.ProjectIdeaForm.viewFormLinkCopy": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.automatedEmails": "Automatisierte E-Mails", "app.containers.AdminPage.ProjectTimeline.automatedEmailsDescription": "Sie können E-Mails konfigurieren, die auf Phasenebene ausgelöst werden", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Zeitraum", "app.containers.AdminPage.ProjectTimeline.defaultSurveyCTALabel": "Umfrage ausfüllen", "app.containers.AdminPage.ProjectTimeline.defaultSurveyTitleLabel": "Umfrage", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Sind <PERSON> sicher, dass Sie diese Phase löschen wollen?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Phasenbeschreibung", "app.containers.AdminPage.ProjectTimeline.disabledProjectPhaseEmailMessage": "Diese Option ist derzeit für alle Projekte auf der Seite {automatedEmailsLink} deaktiviert. Da<PERSON> können Sie diese Einstellung für diese Phase nicht einzeln umschalten.", "app.containers.AdminPage.ProjectTimeline.editPhaseTitle": "Phase bearbeiten", "app.containers.AdminPage.ProjectTimeline.endDate": "Enddatum", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "Enddatum", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.containers.AdminPage.ProjectTimeline.newPhaseTitle": "Eine neue Phase erstellen", "app.containers.AdminPage.ProjectTimeline.noEndDateDescription": "Diese Phase hat kein vorgegebenes Enddatum.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet1": "Bei einigen Methoden (z.B. bei Abstimmungsergebnissen) wird die Ergebnisfreigabe erst ausgelöst, wenn ein Enddatum ausgewählt wurde.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningBullet2": "<PERSON><PERSON><PERSON> Sie eine Phase nach dieser hinzufügen, wird dieser Phase ein Enddatum hinzugefügt.", "app.containers.AdminPage.ProjectTimeline.noEndDateWarningTitle": "<PERSON>in <PERSON>tum hinzufügen bedeutet:", "app.containers.AdminPage.ProjectTimeline.previewSurveyCTALabel": "Vorschau", "app.containers.AdminPage.ProjectTimeline.saveChangesLabel": "Änderungen speichern", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "<PERSON><PERSON> des Formulars trat ein <PERSON> auf, bitte versuche es erneut.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Gespeichert!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.ProjectTimeline.startDate": "Startdatum", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Startdatum", "app.containers.AdminPage.ProjectTimeline.surveyCTALabel": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectTimeline.surveyTitleLabel": "Titel der Umfrage", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Name der Phase", "app.containers.AdminPage.ProjectTimeline.uploadAttachments": "Anhänge hochladen", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_Aufteilung", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.subtitleTerminology": "Terminologie (Titelseitenfilter)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.terminologyTooltip": "Wie sollten Tags im Titelseitenfilter genannte werden? Z. B. Tags, Kategorien, Themen, ...", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipExtraCopy": "Themen/Schlagwörter können {topicManagerLink} konfiguriert werden.", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicInputsTooltipLink": "hier", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTerm2": "Bezeichnung für einen Tag (Singular)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicTermPlaceholder": "<PERSON>a", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTerm": "Bezeichnung für mehrere Tags (Plural)", "app.containers.AdminPage.SettingsPage.AllowedInputTopics.topicsTermPlaceholder": "Themen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Ein neues Registrierungsfeld hinzufügen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Option hinzufügen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Antwortformat", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormatError": "Geben Sie ein Antwortformat an", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Antwortmöglichkeit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionError": "Bieten Sie eine Antwortmöglichkeit für alle Sprachen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSave": "Antwortoption speichern", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionSuccess": "Antwortoption erfolgreich gespeichert", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Antwortmöglichkeiten", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Registrierungsfelder", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "<PERSON><PERSON><PERSON> Sie die Felder und legen Sie diese ab, um die Reihenfolge festzulegen, in der sie im Registrierungsformular erscheinen.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "<PERSON>feld", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Löschen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Optionaler Text, der unter dem Feldnamen auf dem Anmeldeformular angezeigt wird.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Antwortmöglichkeiten für den Wohnort können in der {geographicAreasTabLink} eingestellt werden.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Antwortoption bearbeiten", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Beschreibung", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Name des Feldes", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldNameErrorMessage": "Geben Si<PERSON> einen Feldnamen für alle Sprachen an", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Feldeinstellungen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "<PERSON><PERSON> <PERSON> <PERSON><PERSON> (Checkbox)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Datum", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Lange Antwort", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Multiple Choice (Mehrfachauswahl)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numerischer Wert", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Multiple Choice (Einfachauswahl)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Kurze Antwort", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Registerkarte Geografische Gebiete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Ausgeblendetes Feld", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Die Beantwortung dieses Feldes erforderlich machen?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Benutzerdefinierte Felder", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Antwortmöglichkeit hinzufügen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Abbrechen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Löschen", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionAnswerOptionDeletionConfirmation": "<PERSON>d <PERSON><PERSON>, dass Sie diese Antwortoption für die Registrierungsfrage löschen möchten? Alle Datensätze, die bestimmte Nutzer*innen mit dieser Option beantwortet haben, werden dauerhaft gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.registrationQuestionDeletionConfirmation3": "Sind <PERSON><PERSON> sic<PERSON>, dass Sie diese Registrierungsfrage löschen möchten? Alle Antworten, die Nutzer*innen auf diese Frage gegeben haben, werden dauerhaft gelöscht, und sie wird in Projekten oder Vorschlägen nicht mehr gestellt. Diese Aktion kann nicht rückgängig gemacht werden.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveField": "<PERSON><PERSON> speichern", "app.containers.AdminPage.SettingsPage.CustomSignupFields.saveFieldSuccess": "<PERSON>ld erfolgreich gespeichert", "app.containers.AdminPage.SettingsPage.TwoColumnLayout": "Zwei Spalten", "app.containers.AdminPage.SettingsPage.addAreaButton": "Hinzufügen eines geografischen Gebiets", "app.containers.AdminPage.SettingsPage.addTopicButton": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_animal2": "Tier - z.B. Elefant Katze", "app.containers.AdminPage.SettingsPage.anonymousNameScheme_user": "User - z. B. User 123456", "app.containers.AdminPage.SettingsPage.approvalDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> aus, welche Admins Benachrichtigungen zur Genehmigung von Projekten erhalten sollen. Ordnermanager*innen sind standardmäßig Genehmiger*innen für alle Projekte in ihren Ordnern.", "app.containers.AdminPage.SettingsPage.approvalSave": "Speichern", "app.containers.AdminPage.SettingsPage.approvalTitle": "Einstellungen für die Projektfreigabe", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Sind Sie sich sicher, dass Sie dieses Gebiet löschen möchten?", "app.containers.AdminPage.SettingsPage.areaTerm": "Begriff für ein Gebiet (Singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Löschen", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.areasTerm": "Bezeichnung für mehrere Gebiete (Plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.atLeastOneLocale": "<PERSON>ählen Sie mindestens eine Sprache aus.", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatare", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "<PERSON><PERSON><PERSON> anzeigen", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Profilbilder der Teilnehmenden und deren Anzahl für nicht registrierte Besucher*innen anzeigen", "app.containers.AdminPage.SettingsPage.bannerHeader": "Text der Überschrift", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Kopfzeilentext für nicht registrierte Besucher*innen", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Sub-Header-Text für nicht registrierte Besucher*innen", "app.containers.AdminPage.SettingsPage.bannerHeaderSubtitle": "Text der Zwischenüberschrift", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Banner-Text", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Vorschau anzeigen für", "app.containers.AdminPage.SettingsPage.brandingDescription": "Fügen Sie Ihr Logo hinzu und richten Sie die Plattform-Farben ein.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Plattform-Design", "app.containers.AdminPage.SettingsPage.cancel": "Abbrechen", "app.containers.AdminPage.SettingsPage.chooseLayout": "Layout", "app.containers.AdminPage.SettingsPage.color_primary": "Primärfarbe", "app.containers.AdminPage.SettingsPage.color_secondary": "Sekundärfarbe", "app.containers.AdminPage.SettingsPage.color_text": "Textfarbe", "app.containers.AdminPage.SettingsPage.colorsTitle": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.confirmHeader": "Sind <PERSON> sic<PERSON>, dass Sie dieses Thema löschen möchten?", "app.containers.AdminPage.SettingsPage.contentModeration": "Moderation der Inhalte", "app.containers.AdminPage.SettingsPage.ctaHeader": "Buttons", "app.containers.AdminPage.SettingsPage.customPageMetaTitle": "Benutzerdefinierte Seitenüberschrift | {orgName}", "app.containers.AdminPage.SettingsPage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Text des Buttons", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Button-Link", "app.containers.AdminPage.SettingsPage.defaultTopic": "Standardthema", "app.containers.AdminPage.SettingsPage.delete": "Löschen", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Löschen", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "<PERSON><PERSON><PERSON> wird das Thema gelö<PERSON>t, auch aus allen vorhandenen Beiträgen. Diese Änderung gilt für alle Projekte.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Fügen Sie Themen hinzu und löschen Sie sie, um Sie auf Ihrer Plattform zur Kategorisierung von Beiträgen zu verwenden. Sie können die Themen zu bestimmten Projekten im {adminProjectsLink} hinzufügen.", "app.containers.AdminPage.SettingsPage.desktop": "Desktop", "app.containers.AdminPage.SettingsPage.editFormTitle": "Gebiet bearbeiten", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "<PERSON><PERSON> bearbeiten", "app.containers.AdminPage.SettingsPage.fieldDescription": "Gebietsbeschreibung", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "Diese Beschreibung dient nur der internen Zusammenarbeit und wird den Nutzer*innen nicht angezeigt.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Gebietsname", "app.containers.AdminPage.SettingsPage.fieldTitleError": "Geben Sie einen Gebietsnamen für alle Sprachen an", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "Der Name, den Sie für jedes Gebiet wählen, kann als Option für ein Registrierungsfeld und zum Filtern von Projekten auf der Homepage verwendet werden.", "app.containers.AdminPage.SettingsPage.fieldTopicSave": "<PERSON><PERSON> s<PERSON>ichern", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Name des Themas", "app.containers.AdminPage.SettingsPage.fieldTopicTitleError": "Geben Sie einen Themen-Namen für alle Sprachen an", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "Der Name, den Sie für jedes Thema wählen, wird für die Nutzer*innen der Plattform sichtbar sein", "app.containers.AdminPage.SettingsPage.fixedRatio": "Festes Verhältnis", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltip": "Dieser Bannertyp eignet sich am besten für Bilder, die nicht beschnitten werden sollen, wie z. B. Bilder mit Text oder ein Log. Dieser Banner wird durch einen einfarbigen Kasten in der Grundfarbe ersetzt, wenn NutzerInnen angemeldet sind. Sie können die Farbe in den allgemeinen Einstellungen festlegen. Weitere Informationen über die empfohlene Verwendung von Bildern finden Sie in unserer {link}.", "app.containers.AdminPage.SettingsPage.fixedRatioBannerTooltipLink": "Wissensdatenbank", "app.containers.AdminPage.SettingsPage.fullWidthBannerLayout": "Volle Breite", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltip": "Dieser Banner erstreckt sich über die gesamte Breite und sorgt für einen großartigen visuellen Effekt. Das Bild versucht, so viel Platz wie möglich einzunehmen, was dazu führt, dass es nicht immer zu jeder Zeit sichtbar ist. Sie können diesen Banner mit einem Overlay in einer beliebigen Farbe kombinieren. Weitere Informationen über die empfohlene Bildverwendung finden Sie in unserer {link}.", "app.containers.AdminPage.SettingsPage.fullWidthBannerTooltipLink": "Wissensdatenbank", "app.containers.AdminPage.SettingsPage.header": "Homepage-Banner", "app.containers.AdminPage.SettingsPage.headerDescription": "Personalisieren Sie das Banner-Bild und den Text auf der Startseite.", "app.containers.AdminPage.SettingsPage.header_bg": "Banner-Bild", "app.containers.AdminPage.SettingsPage.helmetDescription": "Admin-Einstellungen", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin-Einstellungen", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Fügen Sie dem personalisierbaren Abschnitt unten auf der Startseite Ihren eigenen Inhalt hinzu.", "app.containers.AdminPage.SettingsPage.homepageMetaTitle": "Kopfzeile der Homepage | {orgName}", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Bildüberlagerungsfarbe", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Deckkraft der Bildüberlagerung", "app.containers.AdminPage.SettingsPage.imageSupportPageURL": "https://support.govocal.com/de/articles/1346397-welche-dimensionen-und-grossen-sind-fur-bilder-auf-der-plattform-empfohlen", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Unangemessene Inhalte erkennen", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Automatische Erkennung von unangemessenen Inhalten, die auf der Plattform gepostet werden.", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionToggleTooltip": "Solange diese Funktion aktiviert ist, werden Beiträge, Vorschläge und Kommentare, die von Teilnehmenden gepostet werden, automatisch überprüft. Beiträge, die als potenziell unangemessener Inhalt gekennzeichnet sind, werden nicht blockiert, sondern auf der Seite {linkToActivityPage} zur Überprüfung hervorgehoben.", "app.containers.AdminPage.SettingsPage.languages": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.languagesTooltip": "Sie können mehrere Sprachen auswählen, in denen Sie Ihre Plattform den Nutzer*innen anbieten möchten. Sie müssen für jede ausgewählte Sprache Inhalte erstellen.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Teilnahme-Feed", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Bitte lade ein Kopfzeilenbild hoch", "app.containers.AdminPage.SettingsPage.no_button": "<PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.organizationName": "Name der Stadt oder Organisation", "app.containers.AdminPage.SettingsPage.organizationNameMultilocError": "Geben Sie für alle Sprachen einen Organisationsnamen oder eine Stadt an.", "app.containers.AdminPage.SettingsPage.overlayToggleLabel": "Overlay e<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.phone": "Telefon", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Plattform-Konfiguration", "app.containers.AdminPage.SettingsPage.population": "Bevölkerung", "app.containers.AdminPage.SettingsPage.populationMinError": "Bevölkerung muss eine positive <PERSON><PERSON> sein.", "app.containers.AdminPage.SettingsPage.populationTooltip": "Die Gesamtzahl der Einwohner*innen in Ihrem Gebiet. Dies wird zur Berechnung der Beteiligungsquote verwendet. <PERSON><PERSON>, wenn nicht zutreffend.", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Schimpfwort-Blocker", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Blockiert automatisch Beiträge, Vorschläge und Kommentare, die die am häufigsten gemeldeten anstößigen Wörter enthalten.", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "Dieser Text wird auf der Homepage oberhalb der Projekte angezeigt.", "app.containers.AdminPage.SettingsPage.projectsSettings": "Projekt-Einstellungen", "app.containers.AdminPage.SettingsPage.projects_header": "Kopfzeile Projekte", "app.containers.AdminPage.SettingsPage.registrationFields": "Registrierungsfelder", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Geben Sie oben in Ihrem Registrierungsformular eine kurze Beschreibung ein.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registrierung", "app.containers.AdminPage.SettingsPage.save": "Speichern", "app.containers.AdminPage.SettingsPage.saveArea": "Gebiet speichern", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Etwas ging schief, bitte versuchen Sie es später nochmal.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Gespeichert!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.SettingsPage.selectApprovers": "Person(en) auswählen", "app.containers.AdminPage.SettingsPage.selectOnboardingAreas": "Gebiete auswählen, die den Nutzer*innen im Registrierungsprozess angezeigt werden sollen", "app.containers.AdminPage.SettingsPage.selectOnboardingTopics": "Themen ausw<PERSON>hlen, die den Nutzer*innen im Registrierungsprozess angezeigt werden sollen", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Speichern fehlgeschlagen. Ändern Sie die Einstellung erneut.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Registrieren\"", "app.containers.AdminPage.SettingsPage.signed_in": "Button für registrierte Nutzer*innen", "app.containers.AdminPage.SettingsPage.signed_out": "Button für nicht registrierte Nutzer*innen", "app.containers.AdminPage.SettingsPage.signupFormText": "Hilfstext für die Registrierung", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Fügen Sie eine kurze Beschreibung am Anfang des Anmeldeformulars hinzu.", "app.containers.AdminPage.SettingsPage.statuses": "Status", "app.containers.AdminPage.SettingsPage.step1": "Schritt E-Mail und Passwort", "app.containers.AdminPage.SettingsPage.step1Tooltip": "Diese wird oben auf der ersten Seite des Anmeldeformulars angezeigt (Name, E-Mail, Passwort).", "app.containers.AdminPage.SettingsPage.step2": "Schritt Fragen zur Registrierung", "app.containers.AdminPage.SettingsPage.step2Tooltip": "Dies wird oben auf der zweiten Seite des Anmeldeformulars angezeigt (zusätzliche Registrierungsfelder).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Definieren Sie die geografischen Gebiete, die Sie für Ihre Plattform verwenden möchten, z<PERSON> <PERSON><PERSON>, Stadtteile oder Bezirke. Sie können diese geografischen Gebiete mit Projekten verknüpfen (filterbar auf der Startseite) oder die Teilnehmenden bei der Registrierung bitten, ihren Wohnort auszuwählen, um intelligente Gruppen zu erstellen und Zugriffsrechte zu definieren.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Wählen Sie aus, wie Menschen den Namen Ihrer Organisation sehen sollen, wählen Sie die Sprachen Ihrer Plattform und den Link zu Ihrer Webseite.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "Der zur Verfügung gestellte Untertitel überschreitet die zulässige maximale Zeichenlänge von 90 Zeichen", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Geben Si<PERSON> an, welche Informationen bei der Anmeldung abgefragt werden.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminologie", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Einstellungen erfolgreich gespeichert", "app.containers.AdminPage.SettingsPage.tabAreas1": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabBranding": "Design", "app.containers.AdminPage.SettingsPage.tabInputStatuses": "Beitragsstatus", "app.containers.AdminPage.SettingsPage.tabPolicies": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SettingsPage.tabProjectApproval": "Projektfreigabe", "app.containers.AdminPage.SettingsPage.tabProposalStatuses1": "Vorschlagsstatus", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registrierung", "app.containers.AdminPage.SettingsPage.tabSettings": "Allgemein", "app.containers.AdminPage.SettingsPage.tabTopics2": "Themen-Tags", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Defin<PERSON>en Sie, welche geografische Einheit Sie für Ihre Projekte verwenden möchten (z. B. Stadtteile, Bezirke, Stadtbezirke usw.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geografische Gebiete", "app.containers.AdminPage.SettingsPage.titleBasic": "Allgemeine Einstellungen", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "Der angegebene Titel überschreitet die maximal zulässige Zeichengrenze (35 Zeichen)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Themen-Manager", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltip": "Dieser Banner ist vor allem bei Bildern nü<PERSON>, die nicht gut mit dem Text des Titels, des Untertitels oder des Buttons harmonieren. Diese Elemente werden unterhalb des Banners eingefügt. Weitere Informationen über die empfohlene Verwendung von Bildern finden Sie in unserer {link}.", "app.containers.AdminPage.SettingsPage.twoRowBannerTooltipLink": "Wissensdatenbank", "app.containers.AdminPage.SettingsPage.twoRowLayout": "Zwei Zeilen", "app.containers.AdminPage.SettingsPage.urlError": "Die URL ist nicht gültig", "app.containers.AdminPage.SettingsPage.urlPatternError": "<PERSON>eben Si<PERSON> eine gültige URL ein.", "app.containers.AdminPage.SettingsPage.urlTitle": "Webseite", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "<PERSON>e können einen Link zu Ihrer eigenen Webseite hinzufügen. Dieser Link wird am unteren Rand der Homepage gezeigt.", "app.containers.AdminPage.SettingsPage.userNameDisplayDescription": "W<PERSON><PERSON><PERSON> Sie aus, wie Nutzer*innen ohne Namen in ihrem Profil auf der Plattform angezeigt werden. Dies ges<PERSON>, wenn Sie die Zugriffsrechte für eine Phase auf „E-Mail-Bestätigung“ setzen. In allen Fällen können Nutzer*innen nach der Teilnahme den Profilnamen aktualisieren, den wir automatisch für sie generiert haben.", "app.containers.AdminPage.SettingsPage.userNameDisplayTitle": "Anzeige des Nutzer*innennamens (nur für Nutzer*innen mit bestätigter E-Mail)", "app.containers.AdminPage.SideBar.administrator": "Admin", "app.containers.AdminPage.SideBar.communityPlatform": "Community-Plattform", "app.containers.AdminPage.SideBar.community_monitor": "Gemeinschaftspuls", "app.containers.AdminPage.SideBar.customerPortal": "Kundenportal", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "E-Mails", "app.containers.AdminPage.SideBar.folderManager": "<PERSON><PERSON><PERSON>-Manager*in", "app.containers.AdminPage.SideBar.groups": "Gruppen", "app.containers.AdminPage.SideBar.guide": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.inputManager": "Beitragsmanager", "app.containers.AdminPage.SideBar.insights": "Berichterstattung", "app.containers.AdminPage.SideBar.inspirationHub": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.knowledgeBase": "Wissensdatenbank", "app.containers.AdminPage.SideBar.language": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.linkToCommunityPlatform2": "https://community.govocal.com", "app.containers.AdminPage.SideBar.linkToSupport2": "https://support.govocal.com/de/", "app.containers.AdminPage.SideBar.menu": "Seiten & Menü", "app.containers.AdminPage.SideBar.messaging": "E-Mails", "app.containers.AdminPage.SideBar.moderation": "Teilnahme-Feed", "app.containers.AdminPage.SideBar.notifications": "Mitteilungen", "app.containers.AdminPage.SideBar.processing": "Analyse", "app.containers.AdminPage.SideBar.projectManager": "Projektmanager*in", "app.containers.AdminPage.SideBar.projects": "Projekte", "app.containers.AdminPage.SideBar.settings": "Einstellungen", "app.containers.AdminPage.SideBar.signOut": "Abmelden", "app.containers.AdminPage.SideBar.support": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.toPlatform": "Zur Plattform", "app.containers.AdminPage.SideBar.tools": "Werkzeuge", "app.containers.AdminPage.SideBar.user.myProfile": "<PERSON><PERSON>", "app.containers.AdminPage.SideBar.users": "Nutzer*innen", "app.containers.AdminPage.SideBar.workshops": "Workshops", "app.containers.AdminPage.Topics.addTopics": "Hinzufügen", "app.containers.AdminPage.Topics.browseTopics": "Themen durch<PERSON>chen", "app.containers.AdminPage.Topics.cancel": "Abbrechen", "app.containers.AdminPage.Topics.confirmHeader": "Sind <PERSON> sic<PERSON>, dass Sie dieses Projektthema löschen möchten?", "app.containers.AdminPage.Topics.delete": "Löschen", "app.containers.AdminPage.Topics.deleteTopicLabel": "Löschen", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "Dieses Thema kann nicht mehr zu neuen Beiträgen in diesem Projekt hinzugefügt werden.", "app.containers.AdminPage.Topics.inputForm": "Eingabeformular", "app.containers.AdminPage.Topics.lastTopicWarning": "Es wird mindestens ein Thema benötigt. Wenn Si<PERSON> keine Themen verwenden möchten, können diese in der Registerkarte {ideaFormLink} deaktiviert werden.", "app.containers.AdminPage.Topics.projectTopicsDescription": "<PERSON>e können die Themen, die den Beiträgen in diesem Projekt zugewiesen werden können, hinzufügen und löschen.", "app.containers.AdminPage.Topics.remove": "Entfernen", "app.containers.AdminPage.Topics.title": "Themen", "app.containers.AdminPage.Topics.topicManager": "Themen-Manager", "app.containers.AdminPage.Topics.topicManagerInfo": "<PERSON>n <PERSON> weitere Projektthemen hinzufügen möchten, könne<PERSON> <PERSON><PERSON> dies unter {topicManagerLink} tun.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Neue Gruppe hinzufügen", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Gruppenname", "app.containers.AdminPage.Users.GroupCreation.fieldGroupNameEmptyError": "Gruppennamen angeben", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "<PERSON><PERSON><PERSON><PERSON> einer manuellen Gruppe", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "Welche Art von Gruppe benötigen Sie?", "app.containers.AdminPage.Users.GroupCreation.readMoreLink3": "https://support.govocal.com/de/articles/7043801-intelligente-und-manuelle-benutzergruppen-verwenden", "app.containers.AdminPage.Users.GroupCreation.saveGroup": "Gruppe speichern", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Manuelle Gruppe er<PERSON>llen", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Intelligente Gruppe erstellen", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Mehr über Gruppen erfahren", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "Sie können Nutzer*innen in der Übersicht auswählen und zu dieser Gruppe hinzufügen.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "Sie können Attribute definieren und Nutzer*innen, die die Bedingungen erfüllen, werden automatisch zu dieser Gruppe hinzugefügt.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Manuelle Gruppe", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Intelligente Gruppe", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "Es gibt noch niemanden in dieser Gruppe", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Unter {allUsersLink} können Sie Nutzer*innen manuell hinzuzufügen.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "Kein*e <PERSON>*in gefunden", "app.containers.AdminPage.Users.GroupsPanel.select": "Auswählen", "app.containers.AdminPage.Users.UsersGroup.exportAll": "Alle exportieren", "app.containers.AdminPage.Users.UsersGroup.exportGroupUsers": "Nutzer*innen in Gruppe exportieren", "app.containers.AdminPage.Users.UsersGroup.exportSelected": "Ausgewählte exportieren", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Sind Sie sicher?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "<PERSON><PERSON> von BenutzerInnen zu den Gruppen ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Aus Gruppe entfernen", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Ausgewählte BenutzerInnen aus dieser Gruppe löschen?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "<PERSON><PERSON> von BenutzerInnen aus der Gruppe ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut.", "app.containers.AdminPage.Users.UsersGroup.moveUsersAction": "Nutzer*innen zur Gruppe hinzufügen", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Hinzufügen", "app.containers.AdminPage.groups.permissions.add": "Hinzufügen", "app.containers.AdminPage.groups.permissions.addAnswer": "Antwort hinzufügen", "app.containers.AdminPage.groups.permissions.addQuestion": "Demografische Fragen hinzufügen", "app.containers.AdminPage.groups.permissions.answerChoices": "Antwort-Möglichkeiten", "app.containers.AdminPage.groups.permissions.answerFormat": "Antwortformat", "app.containers.AdminPage.groups.permissions.atLeastOneOptionError": "Mindestens eine Wahlmöglichkeit muss gegeben sein", "app.containers.AdminPage.groups.permissions.cannotDeleteFolderModerator": "Diese*r <PERSON><PERSON><PERSON>*in moderiert den Ordner, der dieses Projekt enthält. Um ihr oder ihm die Moderatorenrechte für dieses Projekt zu entziehen, können Sie ihr oder ihm entweder die Ordnerrechte entziehen oder das Projekt in einen anderen Ordner verschieben.", "app.containers.AdminPage.groups.permissions.createANewQuestion": "Eine neue Frage erstellen", "app.containers.AdminPage.groups.permissions.createAQuestion": "Eine Frage erstellen", "app.containers.AdminPage.groups.permissions.defaultField": "<PERSON>feld", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Löschen", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Löschen", "app.containers.AdminPage.groups.permissions.emptyTitleErrorMessage": "Bitte geben Sie für alle Auswahlmöglichkeiten einen Titel an", "app.containers.AdminPage.groups.permissions.fieldType_checkbox": "<PERSON><PERSON> <PERSON> <PERSON><PERSON> (Checkbox)", "app.containers.AdminPage.groups.permissions.fieldType_date": "Datum", "app.containers.AdminPage.groups.permissions.fieldType_multiline_text": "Lange Antwort", "app.containers.AdminPage.groups.permissions.fieldType_multiselect": "Mehr<PERSON><PERSON>us<PERSON>hl (mehrere auswählen)", "app.containers.AdminPage.groups.permissions.fieldType_number": "Numerischer Wert", "app.containers.AdminPage.groups.permissions.fieldType_select": "Mehrfachauswahl (wählen Sie e<PERSON> aus)", "app.containers.AdminPage.groups.permissions.fieldType_text": "Kurze Antwort", "app.containers.AdminPage.groups.permissions.granularPermissionsOffText": "Das Ändern dieser Berechtigungen ist nicht Teil Ihrer Lizenz. Bitte kontaktieren Sie Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Sind <PERSON> sicher, dass Sie diese Gruppe aus dem Projekt entfernen möchten?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "<PERSON><PERSON><PERSON>en Si<PERSON> eine oder mehrere Gruppen", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {<PERSON><PERSON>} one {1 Mitglied} other {{count} Mitglieder}}", "app.containers.AdminPage.groups.permissions.missingTitleLocaleError": "Bitte geben Sie den Titel in allen Sprachen an", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Sind Sie sicher?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Projektmanager*in nicht gefunden", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Diese Phase enthält keine aktive Beteiligungsmöglichkeit.", "app.containers.AdminPage.groups.permissions.onlyAdminsCreateQuestion": "<PERSON><PERSON> Admins können eine neue Frage erstellen.", "app.containers.AdminPage.groups.permissions.option1": "Option 1", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Ausstehende Einladung", "app.containers.AdminPage.groups.permissions.permissionAction_annotating_document_subtitle": "Wer kann das Dokument mit An<PERSON>ngen versehen?", "app.containers.AdminPage.groups.permissions.permissionAction_attending_event_subtitle": "Wer kann sich für die Veranstaltung anmelden?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_inputs_subtitle": "Wer kann Beiträgen kommentieren?", "app.containers.AdminPage.groups.permissions.permissionAction_comment_proposals_subtitle": "Wer kann Vorschläge kommentieren?", "app.containers.AdminPage.groups.permissions.permissionAction_post_proposal_subtitle": "Wer kann einen Vorschlag einreichen?", "app.containers.AdminPage.groups.permissions.permissionAction_reaction_input_subtitle": "Wer kann auf Beiträge reagieren?", "app.containers.AdminPage.groups.permissions.permissionAction_submit_input_subtitle": "Wer kann Beiträge einreichen?", "app.containers.AdminPage.groups.permissions.permissionAction_take_poll_subtitle": "Wer kann an der Abstimmung teilnehmen?", "app.containers.AdminPage.groups.permissions.permissionAction_take_survey_subtitle": "Wer kann an der Umfrage teilnehmen?", "app.containers.AdminPage.groups.permissions.permissionAction_volunteering_subtitle": "Wer kann teilnehmen?", "app.containers.AdminPage.groups.permissions.permissionAction_vote_proposals_subtitle": "Wer kann über Vorschläge abstimmen?", "app.containers.AdminPage.groups.permissions.permissionAction_voting_subtitle": "Wer darf abstimmen?", "app.containers.AdminPage.groups.permissions.questionDescription": "Beschreibung der Frage", "app.containers.AdminPage.groups.permissions.questionTitle": "Titel der Frage", "app.containers.AdminPage.groups.permissions.save": "Speichern", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Etwas ist schief gelaufen, bitte versuchen Sie es später noch einmal.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Geschafft!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Ihre Änderungen wurden gespeichert.", "app.containers.AdminPage.groups.permissions.select": "Auswählen", "app.containers.AdminPage.groups.permissions.selectValueError": "Bitte wählen Sie einen Antworttyp", "app.containers.AdminPage.new.createAProject": "Ein Projekt erstellen", "app.containers.AdminPage.new.fromScratch": "<PERSON> auf neu", "app.containers.AdminPage.phase.methodPicker.addOn1": "Add-On", "app.containers.AdminPage.phase.methodPicker.aiPoweredInsights1": "KI-gestützte Einblicke", "app.containers.AdminPage.phase.methodPicker.commonGroundDescription": "<PERSON>lfen Sie den Teilnehmenden, Zustimmung und Ablehnung zu erkennen, eine Idee nach der anderen.", "app.containers.AdminPage.phase.methodPicker.commonGroundTitle": "<PERSON><PERSON><PERSON><PERSON> finden", "app.containers.AdminPage.phase.methodPicker.documentDescription1": "Betten Sie ein interaktives PDF ein und sammeln Sie Kommentare und Feedback mit Konveio.", "app.containers.AdminPage.phase.methodPicker.documentTitle1": "Feed<PERSON> zu einem Dokument sammeln", "app.containers.AdminPage.phase.methodPicker.embedSurvey1": "<PERSON><PERSON> Drittanbieter-Um<PERSON><PERSON> ein<PERSON>ten", "app.containers.AdminPage.phase.methodPicker.externalSurvey1": "Externe Umfrage", "app.containers.AdminPage.phase.methodPicker.ideationDescription1": "Nutzen Sie die kollektive Intelligenz Ihrer Nutzer*innen. Laden Si<PERSON> sie ein, <PERSON><PERSON><PERSON> ein<PERSON>, zu diskutieren und/oder in einem öffentlichen Forum Feedback zu geben.", "app.containers.AdminPage.phase.methodPicker.ideationTitle1": "Öffentlich Beiträge und Feedback sammeln", "app.containers.AdminPage.phase.methodPicker.informationTitle1": "<PERSON><PERSON> teilen", "app.containers.AdminPage.phase.methodPicker.lacksAIText1": "Es fehlen plattforminterne KI-gestützte Erkenntnisse", "app.containers.AdminPage.phase.methodPicker.lacksReportingText1": "Es fehlen plattforminterne Berichterstattung sowie Datenvisualisierung und -verarbeitung", "app.containers.AdminPage.phase.methodPicker.linkWithReportBuilder1": "Verknüpfung mit dem plattforminternen Report Builder", "app.containers.AdminPage.phase.methodPicker.logic1": "Logik", "app.containers.AdminPage.phase.methodPicker.manyQuestionTypes1": "Große Auswahl an Fragetypen", "app.containers.AdminPage.phase.methodPicker.proposalsDescription": "Erlauben Sie den Teilnehmern das Hochladen von Ideen mit einem Zeit- und Abstimmungslimit.", "app.containers.AdminPage.phase.methodPicker.proposalsTitle": "<PERSON><PERSON><PERSON>l<PERSON>ge, Petitionen oder Initiativen", "app.containers.AdminPage.phase.methodPicker.quickPoll1": "Blitz-Umfrage", "app.containers.AdminPage.phase.methodPicker.quickPollDescription1": "<PERSON>rstellen Si<PERSON> einen kurzen Multiple-Choice-Fragebogen.", "app.containers.AdminPage.phase.methodPicker.reportingDescription1": "<PERSON><PERSON>n <PERSON>utzer*innen Informationen zur Verfügung, visualisieren Sie Ergebnisse aus anderen Phasen, erstellen Sie datenreiche Berichte.", "app.containers.AdminPage.phase.methodPicker.survey1": "Umfrage", "app.containers.AdminPage.phase.methodPicker.surveyDescription1": "Verstehen Sie die Bedürfnisse und die Denkweise Ihrer Nutzer*innen mit Hilfe einer breiten Palette von Fragetypen.", "app.containers.AdminPage.phase.methodPicker.surveyOptions1": "Umfrage-Optionen", "app.containers.AdminPage.phase.methodPicker.surveyTitle1": "Umfrage erstellen", "app.containers.AdminPage.phase.methodPicker.volunteeringDescription1": "Bitten Sie Nutzer*innen, sich freiwillig für Aktivitäten und Anlässe zur Verfügung zu stellen oder finden Sie Teilnehmende für ein Panel.", "app.containers.AdminPage.phase.methodPicker.volunteeringTitle1": "Teilnehmende oder Freiwillige finden", "app.containers.AdminPage.phase.methodPicker.votingDescription": "Wählen Sie eine Abstimmungsmethode und lassen Sie die Nutzer*innen zwischen verschiedenen Optionen wählen.", "app.containers.AdminPage.phase.methodPicker.votingTitle1": "Abstimmung oder Budgetzuweisung durchführen", "app.containers.AdminPage.projects.all.all": "Alle", "app.containers.AdminPage.projects.all.createProjectFolder": "<PERSON><PERSON><PERSON> Ordner", "app.containers.AdminPage.projects.all.existingProjects": "Bestehende Projekte", "app.containers.AdminPage.projects.all.homepageWarning": "Passen Sie die Anzeige Ihrer Homepage an: Admins können hier Projekte und Ordner anordnen, um die Reihenfolge auf der Homepage festzulegen.", "app.containers.AdminPage.projects.all.moderatedProjectsEmpty": "<PERSON><PERSON><PERSON><PERSON>, bei denen Sie Projektmanager*in sind, werden hier angezeigt.", "app.containers.AdminPage.projects.all.noProjects": "<PERSON><PERSON> Projekte gefunden.", "app.containers.AdminPage.projects.all.onlyAdminsCanCreateFolders": "Nur Administrator*innen können Projektordner erstellen.", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projekte und Ordner", "app.containers.AdminPage.projects.all.publishedTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.all.searchProjects": "Projekte suchen", "app.containers.AdminPage.projects.all.yourProjects": "<PERSON>ne Projekte", "app.containers.AdminPage.projects.project.analysis.AIAnalysis": "KI-Analyse", "app.containers.AdminPage.projects.project.analysis.Insights.accuracy": "Genauigkeit: {accuracy}{percentage}", "app.containers.AdminPage.projects.project.analysis.Insights.ask": "Fragen", "app.containers.AdminPage.projects.project.analysis.Insights.askAQuestionUpsellMessage": "Anstatt Zusammenfassungen zu erstellen, können Sie Ihren Daten relevante Fragen stellen. Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.containers.AdminPage.projects.project.analysis.Insights.askQuestion": "Frage stellen", "app.containers.AdminPage.projects.project.analysis.Insights.customFields": "Dieser Einblick umfasst die folgenden Fragen:", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestion": "Frage löschen", "app.containers.AdminPage.projects.project.analysis.Insights.deleteQuestionConfirmation": "Sind <PERSON> sicher, dass Sie diese Frage löschen möchten?", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummary": "Zusammenfassung löschen", "app.containers.AdminPage.projects.project.analysis.Insights.deleteSummaryConfirmation": "Sind <PERSON> sic<PERSON>, dass Sie diese Zusammenfassungen löschen möchten?", "app.containers.AdminPage.projects.project.analysis.Insights.emptyList": "Ihre Textzusammenfassungen werden hier angezeigt, aber Si<PERSON> haben derzeit noch keine.", "app.containers.AdminPage.projects.project.analysis.Insights.emptyListDescription": "<PERSON><PERSON><PERSON> Sie oben auf die Schaltfläche \"Automatisch zusammenfassen\", um loszulegen.", "app.containers.AdminPage.projects.project.analysis.Insights.inputsSelected": "Beiträge ausgewählt", "app.containers.AdminPage.projects.project.analysis.Insights.percentage": "%", "app.containers.AdminPage.projects.project.analysis.Insights.questionAccuracyTooltip": "<PERSON>n Sie Fragen zu weniger Beiträgen stellen, führt dies zu einer höheren Genauigkeit. Reduzieren Sie die aktuelle Auswahl, indem Sie Tags, Such- oder demografische Filter verwenden.", "app.containers.AdminPage.projects.project.analysis.Insights.questionsFor": "Fragen für", "app.containers.AdminPage.projects.project.analysis.Insights.questionsForAllInputs": "Frage für alle Beiträge", "app.containers.AdminPage.projects.project.analysis.Insights.rate": "Bewerten Sie die Qualität dieses Einblicks", "app.containers.AdminPage.projects.project.analysis.Insights.restoreFilters": "<PERSON><PERSON> wied<PERSON>", "app.containers.AdminPage.projects.project.analysis.Insights.summarizeButton": "Zusammenfassen", "app.containers.AdminPage.projects.project.analysis.Insights.summaryFor": "Zusammenfassung für", "app.containers.AdminPage.projects.project.analysis.Insights.summaryForAllInputs": "Zusammenfassung von allen Beiträgen", "app.containers.AdminPage.projects.project.analysis.Insights.thankYou": "Vielen Dank für Ihr Feedback", "app.containers.AdminPage.projects.project.analysis.Insights.tooManyInputsMessage": "Die KI kann nicht so viele Beiträge auf einmal verarbeiten. Teilen Si<PERSON> sie in kleinere Gruppen auf.", "app.containers.AdminPage.projects.project.analysis.Insights.tooltipTextLimit": "Sie können mit Ihrer aktuellen Lizenz maximal 30 Beiträge auf einmal zusammenfassen. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihr<PERSON> Admin, um mehr freizuschalten.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.agreeButton": "<PERSON>ch verstehe", "app.containers.AdminPage.projects.project.analysis.LaunchModal.description": "Unsere Plattform ermöglicht es Ihnen, die Kernthemen zu erkunden, die Daten zusammenzufassen und verschiedene Perspektiven zu untersuchen. Wenn Sie auf der Suche nach spezifischen Antworten oder Erkenntnissen sind, sollten Sie die Funktion \"Frage stellen\" nutzen, um über die Zusammenfassung hinaus in die Tiefe zu gehen.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Text": "Auch wenn es selten vorkommt, kann die KI gelegentlich halluzinieren und Informationen generieren, die im ursprünglichen Datensatz nicht explizit vorhanden waren.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation1Title": "Halluzinationen:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Text": "Die KI könnte bestimmte Themen oder Ideen stärker betonen als andere, was die Gesamtinterpretation möglicherweise verzerrt.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation2Title": "Übertreibung:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Text": "Unser System ist für die Verarbeitung von 20-200 gut definierten Eingaben optimiert, um möglichst genaue Ergebnisse zu erzielen. Wenn das Datenvolumen über diesen Bereich hinausgeht, kann die Zusammenfassung übersichtlicher und allgemeiner werden. Das bedeutet nicht, dass die KI \"ungenauer\" wird, sondern vielmehr, dass sie sich auf breitere Trends und Muster konzentrieren wird. Für differenziertere Einblicke empfehlen wir die Verwendung der (Auto)-Tagging-Funktion, um größere Datensätze in kleinere, besser handhabbare Teilmengen zu unterteilen.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.limitation3Title": "Datenmenge und -genauigkeit:", "app.containers.AdminPage.projects.project.analysis.LaunchModal.subtitle": "<PERSON>ir empfeh<PERSON>, KI-generierte Zusammenfassungen als Ausgangspunkt für das Verständnis großer Datensätze zu verwenden, aber nicht als letztes Wort.", "app.containers.AdminPage.projects.project.analysis.LaunchModal.title": "Wie man mit KI arbeitet", "app.containers.AdminPage.projects.project.analysis.Tags.addInputToTag": "Ausgewählte Beiträge zum Tag hinzufügen", "app.containers.AdminPage.projects.project.analysis.Tags.addTag": "Themen-Tag hinzufügen", "app.containers.AdminPage.projects.project.analysis.Tags.advancedAutotaggingUpsellMessage": "Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.containers.AdminPage.projects.project.analysis.Tags.allInput": "Alle Beiträge", "app.containers.AdminPage.projects.project.analysis.Tags.allInputs": "Alle Beiträge", "app.containers.AdminPage.projects.project.analysis.Tags.allTags": "Alle Themen-Tags", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignNo": "<PERSON><PERSON>, ich werde es tun", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignQuestion": "Möchten Sie Ihrem Tag automatisch Beiträge zuweisen?", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText1": "<PERSON><PERSON> gib<PERSON> <b>verschiedene Methoden</b>, um Tags automatisch Beiträgen zuzuweisen.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2AutoText2": "Verwenden Sie <b>die Auto-Tag-Schaltfläche</b>, um Ihre bevorzugte Methode zu starten.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignStep2ManualText1": "<PERSON>licken Si<PERSON> auf ein Schlagwort, um es dem aktuell ausgewählten Beitrag zuzuweisen.", "app.containers.AdminPage.projects.project.analysis.Tags.autoAssignYes": "Ja, automatisches Taggen", "app.containers.AdminPage.projects.project.analysis.Tags.autoTag": "Automatisches Taggen", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagDescription": "Auto-Tags werden automatisch vom Computer abgeleitet. Sie können sie jederzeit ändern oder entfernen.", "app.containers.AdminPage.projects.project.analysis.Tags.autoTagTitle": "Automatisches Taggen", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die bereits mit diesen Tags verknüpft sind, werden nicht erneut klassifiziert.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelSubtitle2": "Die Klassifizierung basiert ausschließlich auf dem Namen des Tags. Wählen Sie relevante Schlüsselwörter, um die besten Ergebnisse zu erzielen.", "app.containers.AdminPage.projects.project.analysis.Tags.byLabelTitle": "Tags: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleDescription": "Sie erstellen die Tags und weisen einige Eingaben manuell zu, den Rest übernimmt der Computer", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTitle": "Tags: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByExampleTooltip": "<PERSON><PERSON><PERSON> wie bei \"Tags: nach Bezeichnung\", aber mit höherer Genauigkeit, da <PERSON>e das System mit guten Beispielen trainieren.", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelDescription": "Sie erstellen die Tags, der Computer ordnet die Eingaben zu", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTitle": "Tags: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.classificationByLabelTooltip": "Dies funktion<PERSON>t gut, wenn Sie eine vordefinierte Reihe von Tags haben oder wenn Ihr Projekt einen begrenzten Umfang in Bezug auf Tags hat.", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagDescription": "Erkennen Sie Eingaben mit einem signifikanten Verhältnis zwischen Abneigung und Gefallen", "app.containers.AdminPage.projects.project.analysis.Tags.controversialTagTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTag": "Themen-Tag löschen", "app.containers.AdminPage.projects.project.analysis.Tags.deleteTagConfirmation": "<PERSON>sen Themen-Tag wirklich entfernen?", "app.containers.AdminPage.projects.project.analysis.Tags.dontShowAgain": "Nicht mehr anzeigen", "app.containers.AdminPage.projects.project.analysis.Tags.editTag": "Themen-Tag bearbeiten", "app.containers.AdminPage.projects.project.analysis.Tags.emptyNameError": "Name hinzufügen", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle": "Wählen Sie maximal 9 Tags aus, auf die die Eingaben verteilt werden sollen.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotSubtitle2": "Die Klassifizierung basiert auf den Eingaben, die den Tags derzeit zugewiesen sind. Der Computer wird versuchen, <PERSON><PERSON><PERSON> zu folgen.", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotTitle": "Tags: <PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.fewShotsEmpty": "Sie haben noch keine benutzerdefinierten Tags.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedDescription": "Der Computer erkennt die Tags automatisch und ordnet sie Ihren Eingaben zu.", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTitle": "Tags: Vollständig automatisiert", "app.containers.AdminPage.projects.project.analysis.Tags.fullyAutomatedTooltip": "<PERSON><PERSON> gee<PERSON>, wenn Ihre Projekte eine breite Palette von Tags umfassen. Ein guter Startpunkt.", "app.containers.AdminPage.projects.project.analysis.Tags.howToTag": "Wie möchten Si<PERSON> taggen?", "app.containers.AdminPage.projects.project.analysis.Tags.inputsWithoutTags": "Beiträge ohne Themen-Tags", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagDescription": "Erkennen Sie die Sprache der einzelnen Eingaben", "app.containers.AdminPage.projects.project.analysis.Tags.languageTagTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.launch": "Start", "app.containers.AdminPage.projects.project.analysis.Tags.noActiveFilters": "<PERSON><PERSON> aktiven <PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.noTags": "Verwenden Sie Tags, um die Eingaben zu unterteilen und zu filtern, damit <PERSON><PERSON> genauere oder gezieltere Zusammenfassungen erstellen können.", "app.containers.AdminPage.projects.project.analysis.Tags.other": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.platformTags": "Tags: Plattform-Tags", "app.containers.AdminPage.projects.project.analysis.Tags.platformTagsDescription": "Weisen Sie die vorhandenen Plattform-Tags zu, die Autor*innen beim Posten ausgewählt haben", "app.containers.AdminPage.projects.project.analysis.Tags.recommended": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tags.renameTag": "Themen-Tag umbenennen", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalCancel": "Abbrechen", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalNameLabel": "Name", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalSave": "Speichern", "app.containers.AdminPage.projects.project.analysis.Tags.renameTagModalTitle": "Themen-Tag umbenennen", "app.containers.AdminPage.projects.project.analysis.Tags.selectAll": "Alles auswählen", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagDescription": "<PERSON>sen Si<PERSON> jeder Eingabe eine positive oder negative Stimmung zu, die aus dem Text abgeleitet wird", "app.containers.AdminPage.projects.project.analysis.Tags.sentimentTagTitle": "Stimmung", "app.containers.AdminPage.projects.project.analysis.Tags.tagDetection": "Tag-Erkennung", "app.containers.AdminPage.projects.project.analysis.Tags.useCurrentFilters": "Aktuelle Filter verwenden", "app.containers.AdminPage.projects.project.analysis.Tags.whatToTag": "Welche Beiträge möchten Sie markieren?", "app.containers.AdminPage.projects.project.analysis.Tasks.autotaggingTask": "Autotagging-Aufgabe", "app.containers.AdminPage.projects.project.analysis.Tasks.controversial": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.endedAt": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.failed": "Gescheitert", "app.containers.AdminPage.projects.project.analysis.Tasks.fewShotClassification": "Zum Beispiel", "app.containers.AdminPage.projects.project.analysis.Tasks.inProgress": "In Arbeit", "app.containers.AdminPage.projects.project.analysis.Tasks.labelClassification": "<PERSON><PERSON> Be<PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.language": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.Tasks.nlpTopic": "NLP-Tag", "app.containers.AdminPage.projects.project.analysis.Tasks.noJobs": "<PERSON>ine kürzlich durchgeführten KI-Aufgaben", "app.containers.AdminPage.projects.project.analysis.Tasks.platformTopic": "Plattform-Tag", "app.containers.AdminPage.projects.project.analysis.Tasks.queued": "Warteschlange", "app.containers.AdminPage.projects.project.analysis.Tasks.sentiment": "Stimmung", "app.containers.AdminPage.projects.project.analysis.Tasks.startedAt": "Gestartet", "app.containers.AdminPage.projects.project.analysis.Tasks.succeeded": "Erfolgreich", "app.containers.AdminPage.projects.project.analysis.Tasks.summarizationTask": "Aufgabe Zusammenfassen", "app.containers.AdminPage.projects.project.analysis.Tasks.triggeredAt": "<PERSON>sgelöst", "app.containers.AdminPage.projects.project.analysis.TopBar.above": "<PERSON><PERSON> als", "app.containers.AdminPage.projects.project.analysis.TopBar.all": "Alle", "app.containers.AdminPage.projects.project.analysis.TopBar.author": "Autor*in", "app.containers.AdminPage.projects.project.analysis.TopBar.below": "<PERSON><PERSON> als", "app.containers.AdminPage.projects.project.analysis.TopBar.birthyear": "Geburtsjahr", "app.containers.AdminPage.projects.project.analysis.TopBar.domicile": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.engagement": "Beteiligung", "app.containers.AdminPage.projects.project.analysis.TopBar.filters": "Filter", "app.containers.AdminPage.projects.project.analysis.TopBar.from": "<PERSON>", "app.containers.AdminPage.projects.project.analysis.TopBar.gender": "Geschlecht", "app.containers.AdminPage.projects.project.analysis.TopBar.input": "Beitrag", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfComments": "Anzahl der Kommentare", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfReactions": "Anzahl der Reaktionen", "app.containers.AdminPage.projects.project.analysis.TopBar.numberOfVotes": "Anza<PERSON> der Stimmen", "app.containers.AdminPage.projects.project.analysis.TopBar.to": "Bis", "app.containers.AdminPage.projects.project.analysis.addToAnalysis": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.anonymous": "Anonyme Ein<PERSON>", "app.containers.AdminPage.projects.project.analysis.authorsByAge": "Autor*innen nach Alter", "app.containers.AdminPage.projects.project.analysis.authorsByDomicile": "Autor*innen nach Wohnsitz", "app.containers.AdminPage.projects.project.analysis.backgroundJobs": "Hintergrund Jobs", "app.containers.AdminPage.projects.project.analysis.comments": "Kommentare", "app.containers.AdminPage.projects.project.analysis.domicileChartTooLarge": "Die Übersicht an Wohnsitzen ist zu groß für die Anzeige", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldFilter": "<PERSON><PERSON> au<PERSON>", "app.containers.AdminPage.projects.project.analysis.emptyCustomFieldsLabel": "Antworten", "app.containers.AdminPage.projects.project.analysis.end": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.filter": "Nur Eingaben mit diesem Wert anzeigen", "app.containers.AdminPage.projects.project.analysis.filterEmptyCustomFields": "<PERSON><PERSON> au<PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.autoInsights": "Automatische Einblicke", "app.containers.AdminPage.projects.project.analysis.heatmap.columnValues": "Spaltenwerte", "app.containers.AdminPage.projects.project.analysis.heatmap.count": "<PERSON><PERSON> gibt {count} Beispiele für diese Kombination.", "app.containers.AdminPage.projects.project.analysis.heatmap.dislikes": "G<PERSON>ä<PERSON><PERSON> mir nicht", "app.containers.AdminPage.projects.project.analysis.heatmap.explore": "Erkunden", "app.containers.AdminPage.projects.project.analysis.heatmap.false": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.inputs": "Beiträge", "app.containers.AdminPage.projects.project.analysis.heatmap.likes": "Gefällt mir", "app.containers.AdminPage.projects.project.analysis.heatmap.nextHeatmap": "Nächste Heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.nextInsight": "Nächster <PERSON>lick", "app.containers.AdminPage.projects.project.analysis.heatmap.noSignificant": "Keine statistisch signifikante Erkenntnis.", "app.containers.AdminPage.projects.project.analysis.heatmap.notAvailableForProjectsWithLessThan30Participants1": "Automatische Einblicke sind für Projekte mit weniger als 30 Teilnehmenden nicht verfügbar.", "app.containers.AdminPage.projects.project.analysis.heatmap.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.previousHeatmap": "Vorherige Heatmap", "app.containers.AdminPage.projects.project.analysis.heatmap.previousInsight": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.rowValues": "Zeilenwerte", "app.containers.AdminPage.projects.project.analysis.heatmap.significant": "Statistisch signifikante Erkenntnis.", "app.containers.AdminPage.projects.project.analysis.heatmap.summarize": "Zusammenfassen", "app.containers.AdminPage.projects.project.analysis.heatmap.tags": "Analyse-Tags", "app.containers.AdminPage.projects.project.analysis.heatmap.true": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.heatmap.units": "Einheiten", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAllInsights": "Alle Einblicke ansehen", "app.containers.AdminPage.projects.project.analysis.heatmap.viewAutoInsights": "Automatische Einblicke anzeigen", "app.containers.AdminPage.projects.project.analysis.inputsWIthoutTags": "Eingaben ohne Tags", "app.containers.AdminPage.projects.project.analysis.invalidShapefile": "Ein ungültiges Shapefile wurde hochgeladen und kann nicht angezeigt werden.", "app.containers.AdminPage.projects.project.analysis.limit": "Limit", "app.containers.AdminPage.projects.project.analysis.mainQuestion": "Hauptfrage", "app.containers.AdminPage.projects.project.analysis.manageInput": "<PERSON><PERSON><PERSON> ver<PERSON>", "app.containers.AdminPage.projects.project.analysis.nextGraph": "Nächste Grafik", "app.containers.AdminPage.projects.project.analysis.noAnswer": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.noAnswerProvided2": "<PERSON><PERSON> Antwo<PERSON> gegeben.", "app.containers.AdminPage.projects.project.analysis.noFileUploaded": "<PERSON><PERSON>.", "app.containers.AdminPage.projects.project.analysis.noInputs": "<PERSON>ine Eingaben entsprechen Ihren aktuellen Filtern", "app.containers.AdminPage.projects.project.analysis.previousGraph": "Vorherige Grafik", "app.containers.AdminPage.projects.project.analysis.reactions": "Reaktionen", "app.containers.AdminPage.projects.project.analysis.remove": "Entfernen", "app.containers.AdminPage.projects.project.analysis.removeFilter": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.removeFilterItem": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.search": "<PERSON><PERSON>", "app.containers.AdminPage.projects.project.analysis.shapefileUploadDisclaimer2": "* Shapefiles werden hier im GeoJSON-Format angezeigt. Daher werden die Formatierungen in der Originaldatei möglicherweise nicht korrekt angezeigt.", "app.containers.AdminPage.projects.project.analysis.start": "Start", "app.containers.AdminPage.projects.project.analysis.supportArticle": "Wissensdatenbank", "app.containers.AdminPage.projects.project.analysis.supportArticleLink": "https://support.govocal.com/en/articles/8316692-ai-analysis/de/", "app.containers.AdminPage.projects.project.analysis.unknown": "Unbekannt", "app.containers.AdminPage.projects.project.analysis.viewAllQuestions": "Alle Fragen anzeigen", "app.containers.AdminPage.projects.project.analysis.viewSelectedQuestions": "Ausgewählte Fragen anzeigen", "app.containers.AdminPage.projects.project.analysis.votes": "Stimmen", "app.containers.AdminPage.widgets.copied": "In die Zwischenablage kopieren", "app.containers.AdminPage.widgets.copyToClipboard": "<PERSON><PERSON><PERSON> diesen Code", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Ko<PERSON>ren Sie den HTML-Code", "app.containers.AdminPage.widgets.fieldAccentColor": "Akzentfarbe", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Widget-Hintergrundfarbe", "app.containers.AdminPage.widgets.fieldButtonText": "Text des Buttons", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Jetzt teilnehmen", "app.containers.AdminPage.widgets.fieldFont": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldFontDescription": "Dies muss eine vorhandene Schriftart aus {googleFontsLink} sein.", "app.containers.AdminPage.widgets.fieldFontSize": "Schriftgröße (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Kopfzeile Untertitel", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "<PERSON><PERSON> können mitbestimmen", "app.containers.AdminPage.widgets.fieldHeaderText": "Überschrift Titel", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Unsere Beteiligungsplattform", "app.containers.AdminPage.widgets.fieldHeight": "<PERSON><PERSON><PERSON> (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Anzahl der Beiträge", "app.containers.AdminPage.widgets.fieldProjects": "Projekte", "app.containers.AdminPage.widgets.fieldRelativeLink": "<PERSON><PERSON> zu", "app.containers.AdminPage.widgets.fieldShowFooter": "But<PERSON> anzeigen", "app.containers.AdminPage.widgets.fieldShowHeader": "Kopfzeile anzeigen", "app.containers.AdminPage.widgets.fieldShowLogo": "Logo anzeigen", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Hintergrundfarbe der Webseite", "app.containers.AdminPage.widgets.fieldSort": "Sort<PERSON><PERSON> nach", "app.containers.AdminPage.widgets.fieldTextColor": "Textfarbe", "app.containers.AdminPage.widgets.fieldTopics": "Themen", "app.containers.AdminPage.widgets.fieldWidth": "Breite", "app.containers.AdminPage.widgets.homepage": "Homepage", "app.containers.AdminPage.widgets.htmlCodeExplanation": "Sie können diesen HTML-Code kopieren und an der Stelle Ihrer Webseite einfügen, an der Sie Ihr Widget hinzufügen möchten.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget-HTML-Code", "app.containers.AdminPage.widgets.previewTitle": "Vorschau", "app.containers.AdminPage.widgets.settingsTitle": "Einstellungen", "app.containers.AdminPage.widgets.sortNewest": "Neueste", "app.containers.AdminPage.widgets.sortPopular": "{<PERSON><PERSON><PERSON>, select, <PERSON><PERSON><PERSON> {Meiste Bewertungen} other {<PERSON><PERSON> Stimmen}}", "app.containers.AdminPage.widgets.sortTrending": "Beliebt", "app.containers.AdminPage.widgets.subtitleWidgets": "<PERSON>e können ein Widget erstellen, es anpassen und es Ihrer eigenen Website hinzufügen, um Leute auf diese Plattform aufmerksam zu machen.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Abmessungen", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Kopfzeile & Fußzeile", "app.containers.AdminPage.widgets.titleInputSelection": "Inhalt", "app.containers.AdminPage.widgets.titleStyle": "Stil", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.ContentBuilder.Save": "Speichern", "app.containers.ContentBuilder.homepage.PageTitle": "Startseite", "app.containers.ContentBuilder.homepage.SaveError": "Beim Speichern der Homepage ist ein Fehler aufgetreten.", "app.containers.ContentBuilder.homepage.TwoColumnLayout": "Zwei Spalten", "app.containers.ContentBuilder.homepage.bannerImage": "Banner-Bild", "app.containers.ContentBuilder.homepage.bannerSubtext": "Banner-Subtext", "app.containers.ContentBuilder.homepage.bannerText": "Banner-Text", "app.containers.ContentBuilder.homepage.button": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.chooseLayout": "Layout", "app.containers.ContentBuilder.homepage.customizationNotAvailable2": "Die Anpassung anderer Einstellungen als des Bildes und des Textes auf dem Homepage-Banner ist nicht in Ihrer aktuellen Lizenz enthalten. Wenden Sie sich an Ihre Kundenberaterin, um mehr darüber zu erfahren.", "app.containers.ContentBuilder.homepage.customized_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.customized_button_text_label": "Text des Buttons", "app.containers.ContentBuilder.homepage.customized_button_url_label": "Button-Link", "app.containers.ContentBuilder.homepage.events.eventsDescription": "Zeigt die nächsten 3 anstehenden Ereignisse auf der Plattform an.", "app.containers.ContentBuilder.homepage.eventsDescription": "Zeigt die nächsten 3 anstehenden Veranstaltungen auf der Plattform an.", "app.containers.ContentBuilder.homepage.fixedRatio": "Festes Seitenverhältnis", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltip": "Dieser Bannertyp eignet sich am besten für Bilder, die nicht beschnitten werden sollen, wie z. B. Bilder mit Text oder ein Log. Dieser Banner wird durch einen einfarbigen Kasten in der Grundfarbe ersetzt, wenn Nutzer*innen angemeldet sind. Sie können die Farbe in den allgemeinen Einstellungen festlegen. Weitere Informationen über die empfohlene Verwendung von Bildern finden Sie in unserer {link}.", "app.containers.ContentBuilder.homepage.fixedRatioBannerTooltipLink": "Wissensdatenbank", "app.containers.ContentBuilder.homepage.fullWidthBannerLayout": "Volle Breite", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltip": "Dieser Banner erstreckt sich über die gesamte Breite und sorgt für einen großartigen visuellen Effekt. Das Bild versucht, so viel Platz wie möglich einzunehmen, was dazu führt, dass es nicht immer zu jeder Zeit sichtbar ist. Sie können diesen Banner mit einem Overlay in einer beliebigen Farbe kombinieren. Weitere Informationen über die empfohlene Bildverwendung finden Sie in unserer {link}.", "app.containers.ContentBuilder.homepage.fullWidthBannerTooltipLink": "Wissensdatenbank", "app.containers.ContentBuilder.homepage.imageOverlayColor": "Bildüberlagerungsfarbe", "app.containers.ContentBuilder.homepage.imageOverlayOpacity": "Deckkraft der Bildüberlagerung", "app.containers.ContentBuilder.homepage.imageSupportPageURL": "https://support.govocal.com/de/articles/1346397-welche-dimensionen-und-grossen-sind-fur-bilder-auf-der-plattform-empfohlen", "app.containers.ContentBuilder.homepage.invalidUrl": "Ungültige URL", "app.containers.ContentBuilder.homepage.no_button": "<PERSON><PERSON>", "app.containers.ContentBuilder.homepage.nonRegistedredUsersView": "Nicht registrierte Nutzer*innen", "app.containers.ContentBuilder.homepage.overlayToggleLabel": "Overlay e<PERSON><PERSON><PERSON>", "app.containers.ContentBuilder.homepage.projectsDescription": "Um die Reihenfolge zu konfigurieren, in der Ihre Projekte angezeigt werden, ordnen Sie sie hier {link} neu an.", "app.containers.ContentBuilder.homepage.projectsDescriptionLink": "Projektseite", "app.containers.ContentBuilder.homepage.registeredUsersView": "Registrierte Nutzer*innen", "app.containers.ContentBuilder.homepage.showAvatars": "<PERSON><PERSON><PERSON> anzeigen", "app.containers.ContentBuilder.homepage.showAvatarsDescription": "Profilbilder der Teilnehmenden und deren Anzahl für nicht registrierte Besucher*innen anzeigen", "app.containers.ContentBuilder.homepage.sign_up_button": "Registrieren", "app.containers.ContentBuilder.homepage.signedInDescription": "So sehen registrierte Nutzer*innen das Banner.", "app.containers.ContentBuilder.homepage.signedOutDescription": "So sehen <PERSON>*innen, die nicht auf der Plattform registriert sind, das Banner.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltip1": "Dieser Banner ist vor allem bei Bildern nü<PERSON>, die nicht gut mit dem Text des Titels, des Untertitels oder des Buttons harmonieren. Diese Elemente werden unterhalb des Banners eingefügt. Weitere Informationen über die empfohlene Verwendung von Bildern finden Sie in unserer {link}.", "app.containers.ContentBuilder.homepage.twoRowBannerTooltipLink": "Wissensdatenbank", "app.containers.ContentBuilder.homepage.twoRowLayout": "Zwei Zeilen", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabel": "<PERSON><PERSON><PERSON> (Pixel)", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeHeightLabelTooltip": "<PERSON><PERSON><PERSON>, in der Ihr eingebetteter Inhalt auf der Seite erscheinen soll (in Pixel).", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleLabel": "Kurze Beschreibung des Inhalts, den Sie einbetten", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeTitleTooltip": "<PERSON><PERSON> ist sinnvoll, diese Informationen für Nutzer*innen bereitzustellen, die auf einen Bildschirmleser oder eine andere unterstützende Technologie angewiesen sind.", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabel": "Ad<PERSON><PERSON> der Webseite", "app.containers.admin.ContentBuilder.IframeMultiloc.embedIframeUrlLabelTooltip": "Vollständige URL der Webseite, die Sie einbetten möchten.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeDescription3": "Zeigen Sie Inhalte einer externen Website auf Ihrer Seite in einem HTML-iFrame an. <PERSON><PERSON>, dass nicht jede Seite eingebettet werden kann. Wenn Sie Probleme beim Einbetten einer Seite haben, erkundigen Sie sich beim Eigentümer der Seite, ob diese so konfiguriert ist, dass sie das Einbetten erlaubt.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeEmbedVisitLinkMessage": "Besuchen Sie die Wissensdatenbank", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeHeightPlaceholder": "300", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeInvalidWhitelistUrlErrorMessage": "Dieser Inhalt konnte leider nicht eingebettet werden. {visitLinkMessage} um mehr zu erfahren.", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeSupportLink": "https://support.govocal.com/de/articles/7025826-anpassen-von-projektbeschreibungen-mit-dem-content-builder", "app.containers.admin.ContentBuilder.IframeMultiloc.iframeUrlErrorMessage": "<PERSON><PERSON><PERSON> Si<PERSON> eine gültige Webadresse ein, zum Beispiel https://beispiel.de", "app.containers.admin.ContentBuilder.IframeMultiloc.url": "Einbetten", "app.containers.admin.ContentBuilder.accordionMultiloc": "Akkordeon", "app.containers.admin.ContentBuilder.accordionMultilocDefaultOpenLabel": "Standardmäßig geöffnet", "app.containers.admin.ContentBuilder.accordionMultilocTextLabel": "Text", "app.containers.admin.ContentBuilder.accordionMultilocTextValue": "Dies ist ein erweiterbarer Akkordeoninhalt. <PERSON><PERSON> können ihn mit Hilfe des Editors im Feld auf der rechten Seite bearbeiten und formatieren.", "app.containers.admin.ContentBuilder.accordionMultilocTitleLabel": "Titel", "app.containers.admin.ContentBuilder.accordionMultilocTitleValue": "Akkordeontitel", "app.containers.admin.ContentBuilder.buttonMultiloc": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.delete": "Löschen", "app.containers.admin.ContentBuilder.error": "<PERSON><PERSON>", "app.containers.admin.ContentBuilder.errorMessage": "Es liegt ein <PERSON>hler im Inhalt von {locale} vor. Bitte beheben Sie das Problem, um Ihre Änderungen speichern zu können.", "app.containers.admin.ContentBuilder.hideParticipationAvatarsText": "Avatare der Teilnehmenden ausblenden", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription": "<PERSON>s handelt sich um eine vierteljährliche, fortlaufende Umfrage, die Ihre Meinung über die Verwaltung und die öffentlichen Dienstleistungen erfasst.", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText": "Umfrage ausfüllen", "app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle": "Wir wollen unsere Dienstleistungen verbessern", "app.containers.admin.ContentBuilder.homepage.default": "Standard", "app.containers.admin.ContentBuilder.homepage.events.eventsTitle": "Veranstaltungen", "app.containers.admin.ContentBuilder.homepage.eventsTitle": "Veranstaltungen", "app.containers.admin.ContentBuilder.homepage.highlight.callToActionTitle": "<PERSON><PERSON><PERSON><PERSON> zu<PERSON>", "app.containers.admin.ContentBuilder.homepage.highlight.highlightDescriptionLabel": "Beschreibung", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkLabel": "URL des Hauptbuttons", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonLinkPlaceholder": "https://beispiel.de", "app.containers.admin.ContentBuilder.homepage.highlight.highlightPrimaryButtonTextLabel": "Text des Hauptbuttons", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkLabel": "URL des sekundären Buttons", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonLinkPlaceholder": "https://beispiel.de", "app.containers.admin.ContentBuilder.homepage.highlight.highlightSecondaryButtonTextLabel": "Text des sekundären Buttons", "app.containers.admin.ContentBuilder.homepage.highlight.highlightTitleLabel": "Titel", "app.containers.admin.ContentBuilder.homepage.homepageBanner": "Homepage-Banner", "app.containers.admin.ContentBuilder.homepage.homepageBannerTitle": "Homepage-Banner", "app.containers.admin.ContentBuilder.homepage.imageTextCards": "Bild & Textkarten", "app.containers.admin.ContentBuilder.homepage.oneColumnLayout": "1 Spalte", "app.containers.admin.ContentBuilder.homepage.projectsTitle": "Projekte", "app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip": "Aktivieren Sie die Vorschläge im Abschnitt \"Vorschläge\" im Admin-Panel, um sie auf der Homepage freizuschalten", "app.containers.admin.ContentBuilder.homepage.proposalsTitle": "Vorschläge", "app.containers.admin.ContentBuilder.imageMultiloc": "Bild", "app.containers.admin.ContentBuilder.imageMultilocAltLabel": "Kurzbeschreibung des Bildes", "app.containers.admin.ContentBuilder.imageMultilocAltTooltip": "Das Hinzufügen von \"Alt-Text\" für Bilder ist wichtig, um Ihre Plattform für Nutzer*innen mit Bildschirmlesegeräten zugänglich zu machen.", "app.containers.admin.ContentBuilder.participationBox": "Teilnahme-Box", "app.containers.admin.ContentBuilder.textMultiloc": "Text", "app.containers.admin.ContentBuilder.threeColumnLayout": "3 Spalten", "app.containers.admin.ContentBuilder.twoColumnLayout": "2 Spalten", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant1-2": "2 Spalten mit 30% bzw. 60% Breite", "app.containers.admin.ContentBuilder.twoColumnLayoutVariant2-1": "2 Spalten mit 60% bzw. 30% Breite", "app.containers.admin.ContentBuilder.twoEvenColumnLayout": "2 gerade Spalten", "app.containers.admin.ContentBuilder.urlPlaceholder": "https://beispiel.de", "app.containers.admin.ReportBuilder.MostReactedIdeasWidget.mostReactedIdeas1": "Beiträge mit den meisten Reaktionen", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.ideationPhase": "Ideenfindungsphase", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.noIdeasAvailable2": "<PERSON><PERSON><PERSON> dieses Projekt oder diese Phase sind keine Beiträge verfügbar.", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.numberOfIdeas1": "Anzahl der Beiträge", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.showMore": "<PERSON><PERSON> anzeigen", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.title": "Titel", "app.containers.admin.ReportBuilder.MostVotedIdeasWidget.totalIdeas1": "Beiträge insgesamt: {numberOfIdeas}", "app.containers.admin.ReportBuilder.SingleIdeaWidget.collapseLongText": "Langen Text einklappen", "app.containers.admin.ReportBuilder.SingleIdeaWidget.noIdeaAvailable1": "<PERSON><PERSON><PERSON> dieses Projekt oder diese Phase sind keine Beiträge verfügbar.", "app.containers.admin.ReportBuilder.SingleIdeaWidget.selectPhase": "Phase auswählen", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showAuthor": "Autor*in", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showContent": "Inhalt", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showReactions": "Reaktionen", "app.containers.admin.ReportBuilder.SingleIdeaWidget.showVotes": "Stimmen", "app.containers.admin.ReportBuilder.SingleIdeaWidget.singleIdea1": "Beitrag", "app.containers.admin.ReportBuilder.SingleIdeaWidget.title": "Titel", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrationRate": "Registrierungsrate", "app.containers.admin.ReportBuilder.Widgets.RegistrationsWidget.registrations": "Registrierungen", "app.containers.admin.ReportBuilder.charts.activeUsersTimeline": "Zeitleiste der Teilnehmenden", "app.containers.admin.ReportBuilder.charts.adminInaccurateParticipantsWarning1": "<PERSON>te <PERSON>, dass die Teilnahmezahlen möglicherweise nicht ganz genau sind, da einige Daten in einer externen Umfrage erfasst werden, die wir nicht verfolgen können.", "app.containers.admin.ReportBuilder.charts.analyticsChart": "Diagramm", "app.containers.admin.ReportBuilder.charts.analyticsChartDateRange": "Datumsbereich", "app.containers.admin.ReportBuilder.charts.analyticsChartTitle": "Titel", "app.containers.admin.ReportBuilder.charts.noData": "<PERSON><PERSON><PERSON> die von Ihnen gewählten Filter sind keine Daten verfügbar.", "app.containers.admin.ReportBuilder.charts.trafficSources": "Kommunikations-Kanäle", "app.containers.admin.ReportBuilder.charts.users": "Nutzer*innen", "app.containers.admin.ReportBuilder.charts.usersByAge": "Nutzer*innen nach Alter", "app.containers.admin.ReportBuilder.charts.usersByGender": "Nutzer*innen nach Geschlecht", "app.containers.admin.ReportBuilder.charts.visitorTimeline": "Zeitleiste der Besucher*innen", "app.containers.admin.ReportBuilder.managerLabel1": "Projektmanager*in", "app.containers.admin.ReportBuilder.periodLabel1": "Zeitraum", "app.containers.admin.ReportBuilder.projectLabel1": "Projekt", "app.containers.admin.ReportBuilder.quarterReport1": "Gemeinschaftspuls-Bericht: {year} Q{quarter}", "app.containers.admin.ReportBuilder.start1": "Start", "app.containers.admin.addCollaboratorsModal.buyAdditionalSeats1": "1 zusätzlichen Zugang kaufen", "app.containers.admin.addCollaboratorsModal.confirmButtonText": "Bestätigen", "app.containers.admin.addCollaboratorsModal.confirmManagerRights": "Sind <PERSON> sicher, dass Sie einer Person Managementrechte erteilen wollen?", "app.containers.admin.addCollaboratorsModal.giveManagerRights": "Managementrechte erteilen", "app.containers.admin.addCollaboratorsModal.hasReachedOrIsOverLimit": "Sie haben das Limit der in Ihrem Tarif enthaltenen Plätze erreicht, {noOfSeats} zusätzliche {noOfSeats, plural, one {seat} other {seats}} werden hinzugefügt.", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Status hinzufügen", "app.containers.admin.ideaStatuses.all.defaultStatusDeleteButtonTooltip2": "Standardstatus können nicht gelöscht werden.", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Löschen", "app.containers.admin.ideaStatuses.all.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Status bearbeiten", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Status, die den Beiträgen von <PERSON>*innen zugewiesen sind, können nicht gelöscht werden. Sie können den Status von bestehenden Beiträgen auf der Registerkarte {manageTab} entfernen/ändern.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "Dieser Status kann nicht gelöscht oder verschoben werden.", "app.containers.admin.ideaStatuses.all.manage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.all.pricingPlanUpgrade": "Die Konfiguration von benutzerdefinierten Beitragsstatus ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.containers.admin.ideaStatuses.all.subtitleInputStatuses1": "Verwalten Sie den Status, der Beiträgen innerhalb eines Projekts zugewiesen werden kann. Der Status ist öffentlich sichtbar und hilft, die Teilnehmenden auf dem Laufenden zu halten.", "app.containers.admin.ideaStatuses.all.subtitleProposalStatuses": "Verwalten Sie den Status, der Vorschlägen innerhalb eines Projekts zugewiesen werden kann. Der Status ist öffentlich sichtbar und hilft dabei, die Teilnehmenden auf dem Laufenden zu halten.", "app.containers.admin.ideaStatuses.all.titleIdeaStatuses1": "<PERSON>it<PERSON><PERSON><PERSON><PERSON> bearbeiten", "app.containers.admin.ideaStatuses.all.titleProposalStatuses": "Vorschlagss<PERSON>us bearbeiten", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Ausgewählt für Implementierung oder nächste Schritte", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.answeredFieldCodeDescription": "Offizielles Update gegeben", "app.containers.admin.ideaStatuses.form.answeredFieldCodeTitle": "Beantwortet", "app.containers.admin.ideaStatuses.form.category": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.categoryDescription": "Bitte wählen Sie die Kategorie, die Ihren Status am besten repräsentiert. Diese Auswahl hilft unserem Analysetool, <PERSON>itr<PERSON><PERSON> genauer zu verarbeiten und zu analysieren.", "app.containers.admin.ideaStatuses.form.customFieldCodeDescription1": "Stimmt mit keiner der anderen Optionen überein", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "<PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.fieldColor": "Farbe", "app.containers.admin.ideaStatuses.form.fieldDescription": "Status Beschreibung", "app.containers.admin.ideaStatuses.form.fieldDescriptionError": "Geben Sie eine Statusbeschreibung für alle Sprachen an", "app.containers.admin.ideaStatuses.form.fieldTitle": "Status-Bezeichnung", "app.containers.admin.ideaStatuses.form.fieldTitleError": "G<PERSON>en Si<PERSON> einen Statusnamen für alle Sprachen an", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Erfolgreich implementiert", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Umgesetzt", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeDescription": "Der Vorschlag ist nicht zulässig", "app.containers.admin.ideaStatuses.form.ineligibleFieldCodeTitle": "Unzulässig", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Nicht wählbar oder nicht zum Weitermachen ausgewählt", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ideaStatuses.form.saveStatus": "Status speichern", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "<PERSON>ür die Implementierung oder nächste Schritte in Betracht gezogen", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Unter Berücksichtigung", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "<PERSON><PERSON><PERSON>, aber noch nicht weiterverarbeitet", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "<PERSON><PERSON><PERSON>", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Verwalten Sie Beiträge und Status.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Beitragsmanager | Beteiligungsplattform {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "<PERSON><PERSON><PERSON> g<PERSON>, <PERSON><PERSON> hi<PERSON> und Beiträge von einem Projekt in ein anderes verschieben", "app.containers.admin.ideas.all.inputManagerPageTitle": "Beitragsmanager", "app.containers.admin.ideas.all.tabOverview": "Übersicht", "app.containers.admin.import.importInputs": "Beiträge importieren", "app.containers.admin.import.importNoLongerAvailable3": "Diese Funktion ist hier nicht mehr verfügbar. Um Beiträge in eine Ideenphase zu importieren, gehen Si<PERSON> zu der Phase und wählen Sie \"Importieren\".", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminAndManagerSeats1": "{adminSeats, plural, one {1 zusätzlicher Admin-Zugang} other {# zusätzliche Admin-Zugänge}} und {managerSeats, plural, one {1 zusätzlicher Ordner- und Projektmanagement-Zugang} other {# zusätzliche Ordner- und Projektmanagement-Zugänge}} werden über das Limit hinaus hinzugefügt.", "app.containers.admin.inviteUsersWithSeatsModal.additionalAdminSeats1": "{seats, plural, one {1 zusätzlicher Admin-Zugang wird über das Limit hinaus hinzugefügt} other {# zusätzliche Admin-Zugänge werden über das Limit hinaus hinzugefügt}}.", "app.containers.admin.inviteUsersWithSeatsModal.additionalManagerSeats1": "{seats, plural, one {1 zusätzlicher Ordner- und Projektmanagement-Zugang wird über das Limit hinaus hinzugefügt} other {# zusätzliche Ordner- und Projektmanagement-Zugänge werden über das Limit hinaus hinzugefügt}}.", "app.containers.admin.inviteUsersWithSeatsModal.confirmButtonText": "Bestätigen und Einladungen versenden", "app.containers.admin.inviteUsersWithSeatsModal.confirmSeatUsageChange": "Bestätigung erforderlich", "app.containers.admin.inviteUsersWithSeatsModal.infoMessage2": "Sie haben das Limit der verfügbaren Zugänge in Ihrer Lizenz erreicht.", "app.containers.admin.project.permissions.permissionsAdministratorsAndManagers": "Admins und Manager*innen dieses Projekts", "app.containers.admin.project.permissions.permissionsAdminsAndCollaborators": "<PERSON>ur Admins und Projektmanager*innen", "app.containers.admin.project.permissions.permissionsAdminsAndCollaboratorsTooltip": "Nur Plattform-Admins, Ordnermanager*innen und Projektmanager*innen können die Aktion durchführen", "app.containers.admin.project.permissions.permissionsAnyoneLabel": "<PERSON><PERSON>", "app.containers.admin.project.permissions.permissionsAnyoneLabelDescription": "<PERSON><PERSON>, auch unregistrierte <PERSON>*innen, kann teilnehmen.", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Gruppe(n)", "app.containers.admin.project.permissions.permissionsSelectionLabelDescription": "Nutzer*innen in bestimmten Nutzer*innengruppen können teilnehmen. Sie können Gruppen auf der Registerkarte \"Nutzer*innen\" verwalten.", "app.containers.admin.project.permissions.viewingRightsTitle": "Wer kann dieses Projekt sehen?", "app.containers.phaseConfig.enableSimilarInputDetection": "Erkennung ähnlicher Beiträge aktivieren", "app.containers.phaseConfig.similarInputDetectionTitle": "Erkennung ähnlicher Beiträge", "app.containers.phaseConfig.similarInputDetectionTooltip": "Zeigen Sie den Teilnehmenden ähnliche Beiträge an, während sie tippen, um Doppeleingaben zu vermeiden.", "app.containers.phaseConfig.similarityThresholdBody": "Ähnlichkeitsschwelle (Hauptteil)", "app.containers.phaseConfig.similarityThresholdBodyTooltipMessage": "<PERSON><PERSON>, wie ähnlich zwei Beschreibungen sein müssen, damit sie als ähnlich gekennzeichnet werden. Verwenden Sie einen Wert zwischen 0 (streng) und 1 (nachsichtig). Niedrigere Werte führen zu weniger, aber genaueren Übereinstimmungen.", "app.containers.phaseConfig.similarityThresholdTitle": "Ähnlichkeitsschwelle (Titel)", "app.containers.phaseConfig.similarityThresholdTitleTooltipMessage": "<PERSON>er wird f<PERSON><PERSON>, wie ähnlich zwei Titel sein müssen, damit sie als ähnlich gekennzeichnet werden. Verwenden Sie einen Wert zwischen 0 (streng) und 1 (nachsichtig). Niedrigere Werte führen zu weniger, aber genaueren Übereinstimmungen.", "app.containers.phaseConfig.warningSimilarInputDetectionTrial": "Diese Funktion ist im Rahmen eines frühzeitigen Zugangs bis zum 30. Juni 2025 verfügbar. Wenn Sie die Funktion über dieses Datum hinaus nutzen möchten, wenden Sie sich bitte an Ihre Kundenbetreuerin oder Ihren Admin, um die Aktivierungsoptionen zu besprechen.", "app.containers.survey.sentiment.noAnswers2": "<PERSON><PERSON> Antworten zu diesem Zeitpunkt.", "app.containers.survey.sentiment.numberComments": "{count, plural, =0 {keine <PERSON>} one {1 Kommentar} other {# Kommentare}}", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.cardTitleTooltipMessage4": "Teilnehmende sind Nutzer*innen oder Besucher*innen, die an einem Projekt teilgenommen, einen Vorschlag gepostet oder mit ihm interagiert oder an Veranstaltungen teilgenommen haben.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRate": "Beteiligungsrate", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.participationRateTooltip4": "Prozentsatz der Besucher*innen, die zu Teilnehmenden werden.", "app.modules.commercial.analytics.admin.components.ActiveUsersCard.totalParticipants": "Teilnehmende insgesamt", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedCampaigns": "Automatisierte E-Mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.automatedEmails": "Automatisierte E-Mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.bottomStatLabel": "aus {quantity} E-Mail-Kampagnen", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.campaigns": "<PERSON><PERSON>ag<PERSON>", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customCampaigns": "Benutzerdefinierte Kampagnen", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.customEmails": "Benutzerdefinierte E-Mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.emails": "E-Mails", "app.modules.commercial.analytics.admin.components.EmailDeliveriesCard.totalEmailsSent": "Versendete E-Mails insgesamt", "app.modules.commercial.analytics.admin.components.Events.completed": "Abgeschlossen", "app.modules.commercial.analytics.admin.components.Events.events": "Veranstaltungen", "app.modules.commercial.analytics.admin.components.Events.totalEvents": "Insgesamt hinzugefügte Veranstaltungen", "app.modules.commercial.analytics.admin.components.Events.upcoming": "Demnächst", "app.modules.commercial.analytics.admin.components.Invitations.accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.invitations": "Einladungen", "app.modules.commercial.analytics.admin.components.Invitations.pending": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.Invitations.totalInvites": "Versendete Einladungen insgesamt", "app.modules.commercial.analytics.admin.components.PostFeedback.goToInputManager": "Zum Beitragsmanager", "app.modules.commercial.analytics.admin.components.PostFeedback.inputs": "Beiträge", "app.modules.commercial.analytics.admin.components.ProjectStatus.active": "Aktiv", "app.modules.commercial.analytics.admin.components.ProjectStatus.activeToolTip": "<PERSON><PERSON><PERSON><PERSON>, die nicht archiviert sind und in der Tabelle \"Aktiv\" auf der Startseite angezeigt werden", "app.modules.commercial.analytics.admin.components.ProjectStatus.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.draftProjects": "Projektentwürfe", "app.modules.commercial.analytics.admin.components.ProjectStatus.finished": "<PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.ProjectStatus.finishedToolTip": "Alle archivierten und aktiven Projekte, die abgeschlossen sind", "app.modules.commercial.analytics.admin.components.ProjectStatus.projects": "{<PERSON><PERSON><PERSON>, select, <PERSON><PERSON><PERSON> {Bezirke} other {Projekte}}", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjects": "Alle Projekte", "app.modules.commercial.analytics.admin.components.ProjectStatus.totalProjectsToolTip": "Die Anzahl der Projekte, die auf der Plattform sichtbar sind", "app.modules.commercial.analytics.admin.components.RegistrationsCard.newRegistrations": "Neue Registrierungen", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRate": "Registrierungsrate", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrationRateTooltip3": "Prozentsatz der Besucher*innen, die zu registrierten Nutzer*innen werden.", "app.modules.commercial.analytics.admin.components.RegistrationsCard.registrations": "Registrierungen", "app.modules.commercial.analytics.admin.components.RegistrationsCard.totalRegistrations": "Registrierungen insgesamt", "app.modules.commercial.analytics.admin.components.Tab": "Besucher*innen", "app.modules.commercial.analytics.admin.components.VisitorsCard.last30Days": "Letzte 30 Tage:", "app.modules.commercial.analytics.admin.components.VisitorsCard.last7Days": "Letzte 7 Tage:", "app.modules.commercial.analytics.admin.components.VisitorsCard.pageViews": "Seitenaufrufe pro Besuch", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitDuration": "Dauer des Besuchs", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitors": "Besucher*innen", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitorsStatTooltipMessage": "\"Besucher*innen\" ist die Anzahl der einzelnen Besucher*innen. Wenn dieselbe Person die Plattform mehrmals besucht, wird sie einmal gezählt.", "app.modules.commercial.analytics.admin.components.VisitorsCard.visits": "Besuche", "app.modules.commercial.analytics.admin.components.VisitorsCard.visitsStatTooltipMessage": "\"Besuche\" ist die Anzahl der Sitzungen. Wenn dieselbe Person die Plattform mehrmals besucht hat, wird jeder <PERSON><PERSON><PERSON> gezählt.", "app.modules.commercial.analytics.admin.components.VisitorsCard.yesterday": "Gestern:", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.count": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsLanguageCard.title": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.numberOfVisitors": "<PERSON><PERSON><PERSON> der Besucher*innen", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.percentageOfVisitors": "Prozentualer Anteil der Besucher*innen", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrer": "Weiterleitung", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrerListButton": "hier klicken", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.referrers": "Weiterleitung", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.viewReferrerList": "Für die vollständige Liste der Weiterleitungen {referrerListButton}", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitors": "Besucher*innen", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visitorsTrafficSources": "Kommunikations-Kanäle", "app.modules.commercial.analytics.admin.components.VisitorsTrafficSourcesCard.visits": "Besuche", "app.modules.commercial.analytics.admin.components.totalParticipants": "Teilnehmende insgesamt", "app.modules.commercial.analytics.admin.containers.visitors.noData": "Es gibt noch keine Besucher*innendaten.", "app.modules.commercial.analytics.admin.containers.visitors.visitorDataBanner": "Wir haben die Art und Weise geä<PERSON>t, wie wir Besucherdaten erfassen und anzeigen. Dadur<PERSON> sind die Besucherdaten genauer und es stehen mehr Datentypen zur Verfügung, wobei die DSGVO-Konformität erhalten bleibt. Die für die Besucherzeitleiste verwendeten Daten reichen zwar länger zurück, wir haben jedoch erst im November 2024 mit der Erfassung der Daten für „Besuchsdauer“, „Seitenaufrufe pro Besuch“ und die anderen Diagramme begonnen. Daher sind vorher keine Daten verfügbar. Wenn Sie Daten vor November 2024 auswählen, beachten Si<PERSON> bitte, dass einige Diagramme möglicherweise leer sind oder seltsam aussehen.", "app.modules.commercial.analytics.admin.hooks.useEmailDeliveries.timeSeries": "E-Mail-Zustellungen im Zeitverlauf", "app.modules.commercial.analytics.admin.hooks.useParticipants.timeSeries": "Teilnehmende im Laufe der Zeit", "app.modules.commercial.analytics.admin.hooks.useRegistrations.timeSeries": "Registrierungen im Laufe der Zeit", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.date": "Datum", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.statistic": "Statistik", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.stats": "Gesamtstatistik", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.timeSeries": "Besuche und Besucher*innen im Laufe der Zeit", "app.modules.commercial.analytics.admin.hooks.useVisitorsData.total": "Gesamt über den Zeitraum", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.count": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsLanguageData.language": "<PERSON><PERSON><PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.campaigns": "<PERSON><PERSON>ag<PERSON>", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.directEntry": "Direkteingabe", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.numberOfVisitors": "<PERSON><PERSON><PERSON> der Besucher*innen", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.percentageOfVisits": "Prozentsatz der Besuche", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrer": "Weiterleitung", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.referrerWebsites": "Verweisende Webseiten", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.searchEngines": "Suchmaschinen", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.socialNetworks": "Soziale Netzwerke", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.ssoRedirects": "SSO-Umleitungen", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.trafficSource": "Kommunikations-Kanäle", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.visits": "<PERSON><PERSON><PERSON> der Besuche", "app.modules.commercial.analytics.admin.hooks.useVisitorsTrafficSourcesData.websites": "Webseiten", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "Sie können diese Inhaltsmarkierung entfernen, indem Sie dieses Element auswählen und oben auf den Button \"Entfernen\" klicken. <PERSON>s erscheint dann wieder in den Registerkarten \"<PERSON><PERSON><PERSON>\" oder \"Nicht gesehen\"", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Unangemessene Inhalte werden automatisch erkannt.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "<PERSON>s gibt keine <PERSON>itr<PERSON>, die von der Community zur Überprüfung gemeldet oder von unserem Natural Language Processing-System als unangemessener Inhalt gekennzeichnet wurden", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Entfernen Sie {numberOfItems, plural, one {Inhaltswarnungen} other {# Inhaltswarnungen}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "<PERSON><PERSON> von e<PERSON>m User als unangemessen gemeldet.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Inhaltliche Warnungen", "app.modules.commercial.report_builder.admin.components.TopBar.reportBuilder": "Berichte", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Seiten, die in Ihrer Navigationsleiste angezeigt werden", "app.modules.navbar.admin.containers.addProject": "Projekt zur Navigationsleiste hinzufügen", "app.modules.navbar.admin.containers.createCustomPageButton": "Benutzerdefinierte Seite erstellen", "app.modules.navbar.admin.containers.deletePageConfirmation": "<PERSON>d <PERSON><PERSON>, dass Sie diese Seite löschen möchten? Dies kann nicht rückgängig gemacht werden. Wenn Sie diese Seite noch nicht löschen möchten, können Sie die Seite auch von Navigationsleiste entfernen.", "app.modules.navbar.admin.containers.navBarMaxItemsNumber": "Sie können nur bis zu 5 Elemente zur Navigationsleiste hinzufügen.", "app.modules.navbar.admin.containers.pageHeader": "Seiten & Menü", "app.modules.navbar.admin.containers.pageSubtitle": "In Ihrer Navigationsleiste können bis zu fünf Seiten zusätzlich zur Startseite und den Projektseiten angezeigt werden. Sie können Menü-Elemente umbenennen, neu anordnen und neue Seiten mit eigenem Inhalt hinzufügen.", "containers.Admin.reporting.components.ReportBuilder.Toolbox.ai": "KI", "containers.Admin.reporting.components.ReportBuilder.Toolbox.widgets": "Widget", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.dragAiContentInfo": "Verwenden Sie das ☰ Symbol unten, um KI-Inhalte in Ihren Bericht zu ziehen.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.noAIInsights": "Es sind keine KI-Einblicke verfügbar. Sie können sie in Ihrem Projekt erstellen.", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.openProject": "Zum Projekt", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.question": "Frage", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.selectPhase": "Phase auswählen", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellButton": "KI-<PERSON><PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellDescription": "Integrieren Sie KI-generierte Erkenntnisse in Ihren Bericht", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTitle": "Mit KI schneller Berichte erstellen", "containers.Admin.reporting.components.ReportBuilder.Widgets.Analysis.upsellTooltip": "Die Berichterstattung mit KI ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin, um diese Funktion freizuschalten.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.featureLockedReason1": "Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupByRegistrationField": "Nach Registrierungsfeld gruppieren", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupBySurveyQuestion": "Nach Frage gruppieren", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupMode": "Gruppieren", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.groupModeTooltip1": "Gruppieren Sie Umfrageantworten nach Registrierungsfeldern (Geschlecht, Ort, Alter usw.) oder anderen Fragen der Umfrage.", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.none": "<PERSON><PERSON>", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.question": "Frage", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.registrationField": "Registrierungsfelder", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyPhase": "Umfrage-Phase", "containers.Admin.reporting.components.ReportBuilder.Widgets.SurveyQuestionResultWidget.Settings.surveyQuestion": "Frage", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.areYouSureYouWantToDeleteThis": "Sind Si<PERSON> sicher, dass Si<PERSON> dies löschen möchten?", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.cancel": "Abbrechen", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.delete": "Löschen", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.edit": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.postYourComment": "Kommentare veröffentlichen", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.save": "Speichern", "front.app.containers.Admin.inspirationHub.ProjectDrawer.Comments.writeYourCommentHere": "<PERSON><PERSON><PERSON><PERSON> hier schreiben", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areaButtonsInfo2": "<PERSON>licken Si<PERSON> auf die Buttons unten, um zu folgen oder nicht zu folgen. Die Anzahl der Projekte ist in Klammern angegeben.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.areasTitle2": "In deinem Gebiet", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.done": "<PERSON><PERSON><PERSON>", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.followPreferences": "Präferenzen folgen", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thereAreCurrentlyNoProjects1": "Derzeit gibt es keine aktiven Projekte mit Ihren Folge-Einstellungen.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Areas.thisWidgetShows2": "Dieses Widget zeigt Projekte an, die mit den \"Gebieten\" verbunden sind, denen <PERSON>utzer*innen folgen. <PERSON><PERSON>, dass Ihre Plattform möglicherweise einen anderen Namen für \"Gebiete\" (z.B. Bezirk, Stadtteil) verwendet - siehe Registerkarte \"Gebiete\" in den Plattformeinstellungen. Wenn Nutzer*innen noch keinem Gebiet folgen, zeigt das Widget die verfügbaren Gebiete an, denen sie folgen können. In diesem Fall zeigt das Widget maximal 100 Gebiete an.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.noData": "<PERSON><PERSON> veröffentlichten Projekte oder Ordner verfügbar", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.publishedTitle": "Veröffentlichte Projekte und Ordner", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Published.thisWidgetWillShow": "Dieses Widget zeigt die Projekte und Ordner an, die derzeit veröffentlicht sind, wobei die auf der Projektseite festgelegte Reihenfolge beibehalten wird. Dieses Verhalten entspricht dem der Registerkarte \"Aktiv\" des Widgets \"Projekte und Ordner (alte Ansicht)\".", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.noData": "Keine Projekte oder Ordner ausgewählt", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectProjectsOrFolders": "Projekte oder Ordner auswählen", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.selectionTitle3": "Ausgewählte Projekte und Ordner", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets.Selection.withThisWidget": "Mit diesem Widget können Sie die Reihenfolge auswählen und festlegen, in der die Projekte oder Ordner den Nutzer*innen angezeigt werden sollen.", "front.app.containers.Admin.pagesAndMenu.containers.ContentBuilder.components.Widgets._shared.AdminPubsCarrousel.AdminPubCard.projects": "{numberOfProjects} Projekte", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageText": "unserer Wissensdatenbank", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageSupportPageURL": "https://support.govocal.com/de/articles/1346397-welche-dimensionen-und-grossen-sind-fur-bilder-auf-der-plattform-empfohlen", "pagesAndMenu.GenericHeroBannerForm.BannerImageFields.imageTooltip": "Weitere Informationen zu empfohlenen Bildauflösungen finden Sie in {supportPageLink}."}