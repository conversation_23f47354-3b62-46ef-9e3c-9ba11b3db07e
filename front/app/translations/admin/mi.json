{"UI.FormComponents.required": "required", "app.components.AdminPage.SettingsPage.bannerHeaderSignedIn": "Header text for registered users", "app.components.AdminPage.SettingsPage.contrastRatioTooLow": "Warning: the color you selected doesn't have a high enough contrast. This may result in text that's hard to read. Choose a darker color to optimize readability.", "app.components.AdminPage.SettingsPage.eventsPageSetting": "Add Events to navigation bar", "app.components.AdminPage.SettingsPage.eventsPageSettingDescription": "When enabled, a link to all project events will be added to the navigation bar.", "app.components.AdminPage.SettingsPage.eventsSection": "Events", "app.components.AdminPage.SettingsPage.homePageCustomizableSection": "Homepage customizable section", "app.components.ProjectTemplatePreview.close": "Close", "app.components.ProjectTemplatePreview.createProject": "Create project", "app.components.ProjectTemplatePreview.goBack": "Go back", "app.components.ProjectTemplatePreview.goBackTo": "Go back to the {goBackLink}.", "app.components.ProjectTemplatePreview.infoboxLine1": "Want to use this template for your participation project?", "app.components.ProjectTemplatePreview.infoboxLine2": "Reach out to the responsible person in your city administration, or contact a {link}.", "app.components.ProjectTemplatePreview.projectInvalidStartDateError": "The selected date is invalid. Please provide a date in the following format: YYYY-MM-DD", "app.components.ProjectTemplatePreview.projectNoStartDateError": "Please select a start date for the project", "app.components.ProjectTemplatePreview.projectStartDate": "The start date of your project", "app.components.ProjectTemplatePreview.projectTitle": "The title of your project", "app.components.ProjectTemplatePreview.projectTitleError": "Please type a project title", "app.components.ProjectTemplatePreview.projectTitleMultilocError": "Please type a project title for all languages", "app.components.ProjectTemplatePreview.projectsOverviewPage": "projects overview page", "app.components.ProjectTemplatePreview.responseError": "Oops, something went wrong.", "app.components.ProjectTemplatePreview.seeMoreTemplates": "See more templates", "app.components.ProjectTemplatePreview.successMessage": "The project was successfully created!", "app.components.ProjectTemplatePreview.typeProjectName": "Type the name of the project", "app.components.ProjectTemplatePreview.useTemplate": "Use this template", "app.components.UserSearch.addModerators": "Add", "app.components.UserSearch.searchUsers": "Type to search users...", "app.components.admin.InputManager.onePost": "1 input", "app.components.admin.PostManager.PostPreview.assignee": "Assignee", "app.components.admin.PostManager.PostPreview.cancelEdit": "Cancel edit", "app.components.admin.PostManager.PostPreview.currentStatus": "Current status", "app.components.admin.PostManager.PostPreview.delete": "Delete", "app.components.admin.PostManager.PostPreview.deleteInputConfirmation": "Are you sure you want to delete this input? This action cannot be undone.", "app.components.admin.PostManager.PostPreview.deleteInputInTimelineConfirmation": "Are you sure you want to delete this input? The input will be deleted from all project phases and cannot be recovered.", "app.components.admin.PostManager.PostPreview.edit": "Edit", "app.components.admin.PostManager.PostPreview.noOne": "Unassigned", "app.components.admin.PostManager.PostPreview.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.PostPreview.picks": "Picks: {picksN<PERSON>ber}", "app.components.admin.PostManager.PostPreview.save": "Save", "app.components.admin.PostManager.PostPreview.submitError": "Error", "app.components.admin.PostManager.allPhases": "All phases", "app.components.admin.PostManager.allProjects": "<PERSON><PERSON> kaupapa katoa", "app.components.admin.PostManager.allStatuses": "All statuses", "app.components.admin.PostManager.allTopics": "All tags", "app.components.admin.PostManager.anyAssignment": "Any administrator", "app.components.admin.PostManager.assignedTo": "Assigned to {assigneeName}", "app.components.admin.PostManager.assignedToMe": "Assigned to me", "app.components.admin.PostManager.assignee": "Assignee", "app.components.admin.PostManager.comments": "Comments", "app.components.admin.PostManager.currentLat": "Center latitude", "app.components.admin.PostManager.currentLng": "Center longitude", "app.components.admin.PostManager.currentZoomLevel": "Zoom level", "app.components.admin.PostManager.delete": "Delete", "app.components.admin.PostManager.deleteAllSelectedInputs": "Delete {count} posts", "app.components.admin.PostManager.deleteConfirmation": "Are you sure you want to delete this layer?", "app.components.admin.PostManager.edit": "Edit", "app.components.admin.PostManager.exportAllInputs": "Export all posts (.xslx)", "app.components.admin.PostManager.exportIdeasComments": "Export all comments (.xslx)", "app.components.admin.PostManager.exportIdeasCommentsProjects": "Export comments for this project (.xslx)", "app.components.admin.PostManager.exportInputsProjects": "Export posts in this project (.xslx)", "app.components.admin.PostManager.exportSelectedInputs": "Export selected posts (.xslx)", "app.components.admin.PostManager.exportSelectedInputsComments": "Export comments for selected posts (.xslx)", "app.components.admin.PostManager.exports": "Exports", "app.components.admin.PostManager.feedbackAuthorPlaceholder": "Choose how people will see your name", "app.components.admin.PostManager.feedbackBodyPlaceholder": "Explain this status change", "app.components.admin.PostManager.goToDefaultMapView": "Go to default map center", "app.components.admin.PostManager.hiddenFieldsLink": "hidden fields", "app.components.admin.PostManager.hiddenFieldsSupportArticleUrl": "https://support.govocal.com/en/articles/1641202", "app.components.admin.PostManager.hiddenFieldsTip": "Tip: If you're using Typeform, add {hiddenFieldsLink} to keep track of who has responded to your survey.", "app.components.admin.PostManager.importError": "The selected file could not be imported because it's not a valid GeoJSON file", "app.components.admin.PostManager.inputCommentsExportFileName": "input_comments", "app.components.admin.PostManager.inputManagerHeader": "Input", "app.components.admin.PostManager.inputs": "Input", "app.components.admin.PostManager.inputsExportFileName": "input", "app.components.admin.PostManager.inputsNeedFeedbackToggle": "Only show posts that need feedback", "app.components.admin.PostManager.latestFeedbackMode": "Use the latest existing official update as an explanation", "app.components.admin.PostManager.multipleInputs": "{ideaCount} posts", "app.components.admin.PostManager.newFeedbackMode": "Write a new update to explain this change", "app.components.admin.PostManager.noFilteredResults": "The filters you selected did not return any results", "app.components.admin.PostManager.noOne": "Unassigned", "app.components.admin.PostManager.officialUpdateAuthor": "Choose how people will see your name", "app.components.admin.PostManager.officialUpdateBody": "Explain this status change", "app.components.admin.PostManager.participatoryBudgettingPicks": "Picks", "app.components.admin.PostManager.pbItemCountTooltip": "The number of times this has been included in other participants' participatory budgets", "app.components.admin.PostManager.projectsTab": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.projectsTabTooltipContent": "You can drag and drop posts to move them from one project to another. Note that for timeline projects, you will still need to add the post to a specific phase.", "app.components.admin.PostManager.publication_date": "Published on", "app.components.admin.PostManager.resetFiltersButton": "Reset filters", "app.components.admin.PostManager.resetInputFiltersDescription": "Reset the filters to see all input.", "app.components.admin.PostManager.saved": "Saved", "app.components.admin.PostManager.setAsDefaultMapView": "Save the current center point and zoom level as the map defaults", "app.components.admin.PostManager.statusChangeGenericError": "There was an error, please try again later or contact support.", "app.components.admin.PostManager.statusChangeSave": "Change status", "app.components.admin.PostManager.statusesTab": "Status", "app.components.admin.PostManager.statusesTabTooltipContent": "Change the status of a post using drag and drop. The original author and other contributors will receive a notification of the changed status.", "app.components.admin.PostManager.timelineTab": "Timeline", "app.components.admin.PostManager.timelineTabTooltipText": "Drag and drop posts to copy them to different project phases.", "app.components.admin.PostManager.title": "Title", "app.components.admin.PostManager.topicsTab": "Tags", "app.components.admin.PostManager.topicsTabTooltipText": "Add tags to an input using drag and drop.", "app.components.admin.PostManager.votes": "Votes", "app.components.admin.ReportExportMenu.FileName.fromFilter": "from", "app.components.admin.ReportExportMenu.FileName.groupFilter": "group", "app.components.admin.ReportExportMenu.FileName.projectFilter": "project", "app.components.admin.ReportExportMenu.FileName.topicFilter": "tag", "app.components.admin.ReportExportMenu.FileName.untilFilter": "until", "app.components.admin.ReportExportMenu.downloadXlsx": "Download Excel", "app.components.app.containers.AdminPage.ProjectEdit.contributionTerm": "Contribution", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltip": "More information on how to embed a link for Google Forms can be found in {googleFormsTooltipLink}.", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLink": "https://support.govocal.com/en/articles/5050525-how-to-embed-your-google-forms-survey-in-a-project-phase", "app.components.app.containers.AdminPage.ProjectEdit.googleFormsTooltipLinkText": "this support article", "app.components.app.containers.AdminPage.ProjectEdit.ideaTerm": "Idea", "app.components.app.containers.AdminPage.ProjectEdit.inputTermSelectLabel": "What should an input be called?", "app.components.app.containers.AdminPage.ProjectEdit.issueTerm": "Comment", "app.components.app.containers.AdminPage.ProjectEdit.maxBudgetRequired": "A maximum budget is required", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetLargerThanMaxError": "The minimum budget can't be larger than the maximum budget", "app.components.app.containers.AdminPage.ProjectEdit.minBudgetRequired": "A minimum budget is required", "app.components.app.containers.AdminPage.ProjectEdit.optionTerm": "Option", "app.components.app.containers.AdminPage.ProjectEdit.projectTerm": "Project", "app.components.app.containers.AdminPage.ProjectEdit.questionTerm": "Question", "app.containers.Admin.Campaigns.campaignFrom": "From:", "app.containers.Admin.Campaigns.campaignTo": "To:", "app.containers.Admin.Campaigns.noAccess": "We're sorry, but it seems like you don't have access to the emails section", "app.containers.Admin.Invitations.addToGroupLabel": "Add these people to specific manual user groups", "app.containers.Admin.Invitations.adminLabelTooltip": "When you select this option, the people you're inviting will have access to all your platform settings.", "app.containers.Admin.Invitations.configureInvitations": "3. Configure the invitations", "app.containers.Admin.Invitations.currentlyNoInvitesThatMatchSearch": "There are no invites that match your search", "app.containers.Admin.Invitations.deleteInvite": "Delete", "app.containers.Admin.Invitations.deleteInviteTooltip": "Cancelling an invitation will allow you to resend an invitation to this person.", "app.containers.Admin.Invitations.downloadFillOutTemplate": "1. Download and fill out the template", "app.containers.Admin.Invitations.downloadTemplate": "Download template", "app.containers.Admin.Invitations.email": "Email", "app.containers.Admin.Invitations.emailListLabel": "Manually enter the email addresses of the people you want to invite. Seperate each address by a comma.", "app.containers.Admin.Invitations.exportInvites": "Export all invitations", "app.containers.Admin.Invitations.fileRequirements": "Important: In order to send the invitations correctly, no column can be removed from the import template. Leave unused columns empty.", "app.containers.Admin.Invitations.filetypeError": "Incorrect file type. Only XLSX files are supported.", "app.containers.Admin.Invitations.groupsPlaceholder": "No group selected", "app.containers.Admin.Invitations.helmetDescription": "Invite users to the platform", "app.containers.Admin.Invitations.helmetTitle": "Admin invitation dashboard", "app.containers.Admin.Invitations.importOptionsInfo": "These options will only be taken into account when they are not defined in the Excel file.\n      Please visit the {supportPageLink} for more information.", "app.containers.Admin.Invitations.importTab": "Import email addresses", "app.containers.Admin.Invitations.invitationOptions": "Invitation options", "app.containers.Admin.Invitations.invitationSubtitle": "Invite people to the platform at any point in time. They get a neutral invitation email with your logo, in which they are asked to register on the platform.", "app.containers.Admin.Invitations.invitePeople": "Invite people via email", "app.containers.Admin.Invitations.inviteStatus": "Status", "app.containers.Admin.Invitations.inviteStatusAccepted": "Accepted", "app.containers.Admin.Invitations.inviteStatusPending": "Pending", "app.containers.Admin.Invitations.inviteTextLabel": "Optionally type a message that will be added to the invitation mail.", "app.containers.Admin.Invitations.invitedSince": "Invited", "app.containers.Admin.Invitations.invitesSupportPageURL": "http://support.govocal.com/en/articles/1771605-invite-people-to-the-platform", "app.containers.Admin.Invitations.localeLabel": "Select the language of the invitation", "app.containers.Admin.Invitations.moderatorLabel": "Give these people project management rights", "app.containers.Admin.Invitations.moderatorLabelTooltip": "When you select this option, the invitee(s) will be assigned project manager rights for the selected project(s). More information on project manager rights {moderatorLabelTooltipLink}.", "app.containers.Admin.Invitations.moderatorLabelTooltipLink": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.containers.Admin.Invitations.moderatorLabelTooltipLinkText": "here", "app.containers.Admin.Invitations.name": "Name", "app.containers.Admin.Invitations.processing": "Sending out invitations. Please wait...", "app.containers.Admin.Invitations.projectSelectorPlaceholder": "No project(s) selected", "app.containers.Admin.Invitations.save": "Send out invitations", "app.containers.Admin.Invitations.saveErrorMessage": "One or more errors occurred and the invitations were not sent out. Please correct the error(s) listed below and try again.", "app.containers.Admin.Invitations.saveSuccess": "Success!", "app.containers.Admin.Invitations.saveSuccessMessage": "Invitation successfully sent out.", "app.containers.Admin.Invitations.supportPage": "support page", "app.containers.Admin.Invitations.supportPageLinkText": "Visit the support page", "app.containers.Admin.Invitations.tabAllInvitations": "All invitations", "app.containers.Admin.Invitations.tabInviteUsers": "Invite users", "app.containers.Admin.Invitations.textTab": "Manually enter email addresses", "app.containers.Admin.Invitations.unknownError": "Something went wrong. Please try again later.", "app.containers.Admin.Invitations.uploadCompletedFile": "2. Upload your completed template file", "app.containers.Admin.Invitations.visitSupportPage": "{supportPageLink} if you want more info about all supported columns in the import template.", "app.containers.Admin.Moderation.all": "All", "app.containers.Admin.Moderation.belongsTo": "Belongs to", "app.containers.Admin.Moderation.collapse": "see less", "app.containers.Admin.Moderation.comment": "Comment", "app.containers.Admin.Moderation.content": "Content", "app.containers.Admin.Moderation.date": "Date", "app.containers.Admin.Moderation.goToComment": "Open this comment in a new tab", "app.containers.Admin.Moderation.goToPost": "Open this post in a new tab", "app.containers.Admin.Moderation.goToProposal": "Open this proposal in a new tab", "app.containers.Admin.Moderation.markFlagsError": "Couldn't mark item(s). Try again.", "app.containers.Admin.Moderation.markNotSeen": "Mark {selectedItemsCount, plural, one {# item} other {# items}} as not seen", "app.containers.Admin.Moderation.markSeen": "Mark {selectedItemsCount, plural, one {# item} other {# items}} as seen", "app.containers.Admin.Moderation.moderationsTooltip": "This page allows you to quickly check all new posts that have been published to your platform, including ideas and comments. You can mark posts as being 'seen' so that others know what still needs to be processed.", "app.containers.Admin.Moderation.noUnviewedItems": "There are no unseen items", "app.containers.Admin.Moderation.noViewedItems": "There are no seen items", "app.containers.Admin.Moderation.post": "Post", "app.containers.Admin.Moderation.profanityBlockerSetting": "Profanity blocker", "app.containers.Admin.Moderation.profanityBlockerSettingDescription": "Block posts containing the most commonly reported offensive words.", "app.containers.Admin.Moderation.project": "Project", "app.containers.Admin.Moderation.read": "Seen", "app.containers.Admin.Moderation.readMore": "Read more", "app.containers.Admin.Moderation.removeFlagsError": "Couldn't remove warning(s). Try again.", "app.containers.Admin.Moderation.rowsPerPage": "Rows per page", "app.containers.Admin.Moderation.settings": "Settings", "app.containers.Admin.Moderation.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.Admin.Moderation.show": "Show", "app.containers.Admin.Moderation.status": "Status", "app.containers.Admin.Moderation.successfulUpdateSettings": "Settings updated successfully.", "app.containers.Admin.Moderation.type": "Type", "app.containers.Admin.Moderation.unread": "Not seen", "app.containers.Admin.Users.GroupsHeader.deleteGroup": "Delete Group", "app.containers.Admin.Users.GroupsHeader.editGroup": "Edit Group", "app.containers.Admin.Users.GroupsPanel.allUsers": "Registered users", "app.containers.Admin.Users.GroupsPanel.groupsTitle": "Groups", "app.containers.Admin.Users.GroupsPanel.usersSubtitle": "Get an overview of all the people and organisations that registered on the platform. Add a selection of users to Manual groups or Smart groups.", "app.containers.Admin.Users.admin": "Admin", "app.containers.Admin.Users.deleteUser": "Delete user", "app.containers.Admin.Users.email": "Email", "app.containers.Admin.Users.helmetDescription": "User list in admin", "app.containers.Admin.Users.helmetTitle": "Admin - users dashboard", "app.containers.Admin.Users.name": "Name", "app.containers.Admin.Users.options": "Options", "app.containers.Admin.Users.seeProfile": "See profile", "app.containers.Admin.Users.userDeletionConfirmation": "Permanently remove this user?", "app.containers.Admin.Users.userDeletionFailed": "An error occurred while deleting this user, please try again.", "app.containers.Admin.Users.userExportFileName": "user_export", "app.containers.Admin.Users.youCantDeleteYourself": "You cannot delete your own account via the user admin page", "app.containers.Admin.Users.youCantUnadminYourself": "You cannot give up your role as an admin now", "app.containers.Admin.emails.addCampaignTitle": "Create a new email", "app.containers.Admin.emails.allUsers": "Registered users", "app.containers.Admin.emails.changeRecipientsButton": "Change recipients", "app.containers.Admin.emails.confirmSendHeader": "Email to all users?", "app.containers.Admin.emails.deleteButtonLabel": "Delete", "app.containers.Admin.emails.draft": "Draft", "app.containers.Admin.emails.editButtonLabel": "Edit", "app.containers.Admin.emails.editCampaignTitle": "Edit campaign", "app.containers.Admin.emails.fieldBody": "Message", "app.containers.Admin.emails.fieldReplyTo": "Replies should go to", "app.containers.Admin.emails.fieldReplyToTooltip": "You can choose where to send replies to your email.", "app.containers.Admin.emails.fieldSender": "From", "app.containers.Admin.emails.fieldSenderTooltip": "You can decide who the recipients will see as the sender of the email.", "app.containers.Admin.emails.fieldSubject": "Email Subject", "app.containers.Admin.emails.fieldSubjectTooltip": "This will be shown in the subject line of the email and in the user’s inbox overview. Make it clear and engaging.", "app.containers.Admin.emails.fieldTo": "To", "app.containers.Admin.emails.fieldToTooltip": "You can select the user groups that will receive your email", "app.containers.Admin.emails.groups": "Groups", "app.containers.Admin.emails.helmetDescription": "Send out manual emails to user groups and activate automated campaigns", "app.containers.Admin.emails.previewSentConfirmation": "A preview email has been sent to your email address", "app.containers.Admin.emails.previewTitle": "Preview", "app.containers.Admin.emails.send": "Send", "app.containers.Admin.emails.sendNowButton": "Send now", "app.containers.Admin.emails.sendTestEmailButton": "Send me a test email", "app.containers.Admin.emails.sendTestEmailTooltip": "When you click this link, a test email will be sent to your email address only. This allows you to check how the email looks like in real life.", "app.containers.Admin.emails.senderRecipients": "Sender and recipients", "app.containers.Admin.emails.sent": "<PERSON><PERSON>", "app.containers.Admin.emails.toAllUsers": "Do you want to send this email to all registered users?", "app.containers.Admin.projects.all.components.archived": "Archived", "app.containers.Admin.projects.all.components.draft": "Draft", "app.containers.Admin.projects.all.components.manageButtonLabel": "Edit", "app.containers.Admin.projects.all.deleteFolderConfirm": "Are you sure you want to delete this folder? All of the projects within the folder will also be deleted. This action cannot be undone.", "app.containers.Admin.projects.all.deleteFolderError": "There was an issue removing this folder. Please try again.", "app.containers.Admin.projects.all.deleteProjectConfirmation": "Are you sure you want to delete this project? This cannot be undone.", "app.containers.Admin.projects.all.deleteProjectError": "There was an error deleting this project, please try again later.", "app.containers.AdminPage.DashboardPage.Report.totalUsers": "total users on the platform", "app.containers.AdminPage.DashboardPage._blank": "unknown", "app.containers.AdminPage.DashboardPage.allGroups": "All Groups", "app.containers.AdminPage.DashboardPage.allProjects": "<PERSON><PERSON> kaupapa katoa", "app.containers.AdminPage.DashboardPage.allTime": "All Time", "app.containers.AdminPage.DashboardPage.comments": "Comments", "app.containers.AdminPage.DashboardPage.commentsByTimeTitle": "Comments", "app.containers.AdminPage.DashboardPage.continuousType": "Without a timeline", "app.containers.AdminPage.DashboardPage.cumulatedTotal": "Cumulative total", "app.containers.AdminPage.DashboardPage.customDateRange": "Custom", "app.containers.AdminPage.DashboardPage.day": "day", "app.containers.AdminPage.DashboardPage.false": "false", "app.containers.AdminPage.DashboardPage.female": "female", "app.containers.AdminPage.DashboardPage.fromTo": "from {from} to {to}", "app.containers.AdminPage.DashboardPage.helmetDescription": "Dashboard for activities on the platform", "app.containers.AdminPage.DashboardPage.helmetTitle": "Admin dashboard page", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByProject": "Pick resource to show by project", "app.containers.AdminPage.DashboardPage.hiddenLabelPickResourceByTopic": "Pick a resource to show by tag", "app.containers.AdminPage.DashboardPage.male": "male", "app.containers.AdminPage.DashboardPage.month": "month", "app.containers.AdminPage.DashboardPage.noData": "There is no data to be shown.", "app.containers.AdminPage.DashboardPage.noPhase": "No phase created for this project", "app.containers.AdminPage.DashboardPage.participationPerProject": "Participation per project", "app.containers.AdminPage.DashboardPage.participationPerTopic": "Participation per tag", "app.containers.AdminPage.DashboardPage.perPeriod": "Per {period}", "app.containers.AdminPage.DashboardPage.previous30Days": "Previous 30 days", "app.containers.AdminPage.DashboardPage.previous90Days": "Previous 90 days", "app.containers.AdminPage.DashboardPage.previousWeek": "Previous week", "app.containers.AdminPage.DashboardPage.previousYear": "Previous year", "app.containers.AdminPage.DashboardPage.projectType": "Project type : {projectType}", "app.containers.AdminPage.DashboardPage.resolutionday": "in Days", "app.containers.AdminPage.DashboardPage.resolutionmonth": "in Months", "app.containers.AdminPage.DashboardPage.resolutionweek": "in Weeks", "app.containers.AdminPage.DashboardPage.selectedProject": "current project filter", "app.containers.AdminPage.DashboardPage.selectedTopic": "current tag filter", "app.containers.AdminPage.DashboardPage.subtitleDashboard": "Discover what's happening on your platform.", "app.containers.AdminPage.DashboardPage.tabReports": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.tabUsers": "Users", "app.containers.AdminPage.DashboardPage.timelineType": "Timeline", "app.containers.AdminPage.DashboardPage.titleDashboard": "Dashboard", "app.containers.AdminPage.DashboardPage.total": "Total", "app.containers.AdminPage.DashboardPage.totalForPeriod": "This {period}", "app.containers.AdminPage.DashboardPage.true": "true", "app.containers.AdminPage.DashboardPage.unspecified": "unspecified", "app.containers.AdminPage.DashboardPage.users": "Users", "app.containers.AdminPage.DashboardPage.usersByAgeTitle": "Users by age", "app.containers.AdminPage.DashboardPage.usersByDomicileTitle": "Users by geographic area", "app.containers.AdminPage.DashboardPage.usersByGenderTitle": "Users by gender", "app.containers.AdminPage.DashboardPage.usersByTimeTitle": "Registrations", "app.containers.AdminPage.DashboardPage.week": "week", "app.containers.AdminPage.FaviconPage.favicon": "Favicon", "app.containers.AdminPage.FaviconPage.faviconExplaination": "Tips for choosing a favicon image: select a simple image, as the shown image size is very small. The image should be saved as a PNG, and should be square with a transparent background (or a white background if necessary). Your favicon should only be set once as changes will require some technical support.", "app.containers.AdminPage.FaviconPage.save": "Save", "app.containers.AdminPage.FaviconPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FaviconPage.saveSuccess": "Success!", "app.containers.AdminPage.FaviconPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FolderPermissions.addFolderManager": "Add", "app.containers.AdminPage.FolderPermissions.deleteFolderManagerLabel": "Delete", "app.containers.AdminPage.FolderPermissions.folderManagerSectionTitle": "Folder managers", "app.containers.AdminPage.FolderPermissions.folderManagerTooltip": "Folder managers can edit the folder description, create new projects within the folder, and have project management rights over all projects within the folder. They cannot delete projects and they do not have access to projects that are not within their folder. You can {projectManagementInfoCenterLink} to find more information on project management rights.", "app.containers.AdminPage.FolderPermissions.moreInfoFolderManagerLink": "https://support.govocal.com/en/articles/4648650-assign-the-right-project-managers", "app.containers.AdminPage.FolderPermissions.noMatch": "No match found", "app.containers.AdminPage.FolderPermissions.projectManagementInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.FolderPermissions.searchFolderManager": "Search users", "app.containers.AdminPage.FoldersEdit.addToFolder": "Add to folder", "app.containers.AdminPage.FoldersEdit.archivedStatus": "Archived", "app.containers.AdminPage.FoldersEdit.deleteFolderLabel": "Delete this folder", "app.containers.AdminPage.FoldersEdit.descriptionInputLabel": "Description", "app.containers.AdminPage.FoldersEdit.draftStatus": "Draft", "app.containers.AdminPage.FoldersEdit.fileUploadLabel": "Add files to this folder", "app.containers.AdminPage.FoldersEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the folder page.", "app.containers.AdminPage.FoldersEdit.folderEmptyGoBackToAdd": "There are no projects in this folder. Go back to the main Projects tab to create and add projects.", "app.containers.AdminPage.FoldersEdit.multilocError": "All text fields must be filled in for every language.", "app.containers.AdminPage.FoldersEdit.noProjectsToAdd": "There are no projects that you can add to this folder.", "app.containers.AdminPage.FoldersEdit.projectFolderCardImageLabel": "Folder card image", "app.containers.AdminPage.FoldersEdit.projectFolderPermissionsTab": "Permissions", "app.containers.AdminPage.FoldersEdit.projectFolderProjectsTab": "Folder projects", "app.containers.AdminPage.FoldersEdit.projectFolderSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.projectsAlreadyAdded": "Projects added to this folder", "app.containers.AdminPage.FoldersEdit.projectsYouCanAdd": "Projects you can add to this folder", "app.containers.AdminPage.FoldersEdit.publicationStatusTooltip": "Choose whether this folder is \"draft\", \"published\" or \"archived\".", "app.containers.AdminPage.FoldersEdit.publishedStatus": "Published", "app.containers.AdminPage.FoldersEdit.removeFromFolder": "Remove from folder", "app.containers.AdminPage.FoldersEdit.save": "Save", "app.containers.AdminPage.FoldersEdit.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.FoldersEdit.saveSuccess": "Success!", "app.containers.AdminPage.FoldersEdit.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabel": "Short description", "app.containers.AdminPage.FoldersEdit.shortDescriptionInputLabelTooltip": "shown on the homepage", "app.containers.AdminPage.FoldersEdit.statusLabel": "Publication status", "app.containers.AdminPage.FoldersEdit.subtitleNewFolder": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.subtitleSettingsTab": "Explain why the projects belong together, define a visual identity and share information.", "app.containers.AdminPage.FoldersEdit.titleInputLabel": "Title", "app.containers.AdminPage.FoldersEdit.titleNewFolder": "Create a new folder", "app.containers.AdminPage.FoldersEdit.titleSettingsTab": "Settings", "app.containers.AdminPage.FoldersEdit.viewPublicProjectFolder": "View Folder", "app.containers.AdminPage.PagesEdition.policiesTitle": "Platform policies", "app.containers.AdminPage.PagesEdition.privacy-policy": "Kaupapa<PERSON> tūmata<PERSON>ga", "app.containers.AdminPage.PagesEdition.terms-and-conditions": "<PERSON><PERSON> tikanga", "app.containers.AdminPage.ProjectDashboard.helmetDescription": "List of projects on the platform", "app.containers.AdminPage.ProjectDashboard.helmetTitle": "Projects dashboard", "app.containers.AdminPage.ProjectDashboard.overviewPageSubtitle": "Create new projects or manage existing projects.", "app.containers.AdminPage.ProjectDashboard.overviewPageTitle": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectDashboard.published": "Published", "app.containers.AdminPage.ProjectDescription.descriptionLabel": "Description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewLabel": "Homepage description", "app.containers.AdminPage.ProjectDescription.descriptionPreviewTooltip": "Shown on the project card on the home page.", "app.containers.AdminPage.ProjectDescription.descriptionTooltip": "Shown on the project page. Clearly describe what the project is about, what you expect from your users and what they can expect from you.", "app.containers.AdminPage.ProjectDescription.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectDescription.save": "Save", "app.containers.AdminPage.ProjectDescription.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectDescription.saved": "Saved!", "app.containers.AdminPage.ProjectDescription.subtitleDescription": "Decide on which message you want to give to your audience. Edit your project and enrich it with images, videos, file attachments,… This information helps visitors to understand what your project is about.", "app.containers.AdminPage.ProjectDescription.titleDescription": "Project description", "app.containers.AdminPage.ProjectEdit.MapTab.cancel": "Cancel editing", "app.containers.AdminPage.ProjectEdit.MapTab.centerLatLabelTooltip": "The default latitude of the map center point. Accepts a value between -90 and 90.", "app.containers.AdminPage.ProjectEdit.MapTab.centerLngLabelTooltip": "The default longitude of the map center point. Accepts a value between -90 and 90.", "app.containers.AdminPage.ProjectEdit.MapTab.edit": "Edit map layer", "app.containers.AdminPage.ProjectEdit.MapTab.editLayer": "Edit layer", "app.containers.AdminPage.ProjectEdit.MapTab.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectEdit.MapTab.here": "here", "app.containers.AdminPage.ProjectEdit.MapTab.import": "Import GeoJSON file", "app.containers.AdminPage.ProjectEdit.MapTab.latLabel": "Default latitude", "app.containers.AdminPage.ProjectEdit.MapTab.layerColor": "Layer color", "app.containers.AdminPage.ProjectEdit.MapTab.layerColorTooltip": "All features in the layer will be styled with this color. This color will also overwrite any existing styling in your GeoJSON file.", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconName": "Marker icon", "app.containers.AdminPage.ProjectEdit.MapTab.layerIconNameTooltip": "Optionally select an icon that is displayed in the markers. Click {url} to see the list of icons you can select.", "app.containers.AdminPage.ProjectEdit.MapTab.layerName": "Layer name", "app.containers.AdminPage.ProjectEdit.MapTab.layerNameTooltip": "This layer name is shown on the map legend", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltip": "Layer tooltip", "app.containers.AdminPage.ProjectEdit.MapTab.layerTooltipTooltip": "This text is displayed as a tooltip when hovering over the layer features on the map", "app.containers.AdminPage.ProjectEdit.MapTab.layers": "Map layers", "app.containers.AdminPage.ProjectEdit.MapTab.lngLabel": "Default longitude", "app.containers.AdminPage.ProjectEdit.MapTab.mapCenterAndZoom": "Map default center and zoom", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationDescription": "Customize the map view, including uploading and styling map layers and setting the map center and zoom level.", "app.containers.AdminPage.ProjectEdit.MapTab.mapConfigurationTitle": "Map configuration", "app.containers.AdminPage.ProjectEdit.MapTab.remove": "Remove layer", "app.containers.AdminPage.ProjectEdit.MapTab.save": "Save", "app.containers.AdminPage.ProjectEdit.MapTab.supportArticle": "support article", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabel": "Map zoom level", "app.containers.AdminPage.ProjectEdit.MapTab.zoomLabelTooltip": "The default zoom level of the map. Accepts a value between 1 and 17, where 1 is fully zoomed out (the entire world is visible) and 17 is fully zoomed in (blocks and buildings are visible)", "app.containers.AdminPage.ProjectEdit.PollTab.addPollQuestion": "Add a poll question", "app.containers.AdminPage.ProjectEdit.PollTab.cancelFormQuestion": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.cancelOption": "Cancel", "app.containers.AdminPage.ProjectEdit.PollTab.deleteOption": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.deleteQuestion": "Delete", "app.containers.AdminPage.ProjectEdit.PollTab.exportPollResults": "Export the poll results", "app.containers.AdminPage.ProjectEdit.PollTab.maxOverTheMaxTooltip": "The maximum number of choices is greater than the number of options", "app.containers.AdminPage.ProjectEdit.PollTab.multipleOption": "Multiple choice", "app.containers.AdminPage.ProjectEdit.PollTab.noOptions": "No options", "app.containers.AdminPage.ProjectEdit.PollTab.noOptionsTooltip": "All questions must have answer choices", "app.containers.AdminPage.ProjectEdit.PollTab.oneOption": "Only one option", "app.containers.AdminPage.ProjectEdit.PollTab.oneOptionsTooltip": "Poll respondents have only one choice", "app.containers.AdminPage.ProjectEdit.PollTab.pollExportFileName": "poll_export", "app.containers.AdminPage.ProjectEdit.PollTab.pollTabSubtitle": "Here you can create poll questions, set the answer choices for participants to choose from for each question, decide whether you want participants to only be able to select one answer choice (single choice) or multiple answer choices (multiple choice), and export the poll results. You can create multiple poll questions within one poll.", "app.containers.AdminPage.ProjectEdit.PollTab.saveOption": "Save", "app.containers.AdminPage.ProjectEdit.PollTab.singleOption": "Single choice", "app.containers.AdminPage.ProjectEdit.PollTab.titlePollTab": "Polls settings and results", "app.containers.AdminPage.ProjectEdit.PollTab.wrongMax": "Wrong maximum", "app.containers.AdminPage.ProjectEdit.PostManager.subtitleInputManager": "Give feedback, add tags or copy posts to the next project phase.", "app.containers.AdminPage.ProjectEdit.PostManager.titleInputManager": "Input manager", "app.containers.AdminPage.ProjectEdit.SurveyResults.exportSurveyResults": "Export the survey results (.xlsx)", "app.containers.AdminPage.ProjectEdit.SurveyResults.subtitleSurveyResults": "Here, you can download the results of the Typeform survey(s) within this project as an Excel file.", "app.containers.AdminPage.ProjectEdit.SurveyResults.surveyResultsTab": "Survey Results", "app.containers.AdminPage.ProjectEdit.SurveyResults.titleSurveyResults": "Consult the survey answers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.addCauseButton": "Add cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionLabel": "Description", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeDescriptionTooltip": "Use this to explain what is required from volunteers and what they can expect.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeErrorMessage": "Couldn't save because the form contains errors.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeImageLabel": "Image", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.causeTitleLabel": "Title", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.editCauseTitle": "Edit cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.exportVolunteers": "Export volunteers", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseSubtitle": "A cause is an action or activity that participants can volunteer for.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.newCauseTitle": "New cause", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.saveCause": "Save", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.subtitleVolunteeringTab": "Here, you can set up the causes users can volunteer for and download the volunteers.", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.titleVolunteeringTab": "Volunteering", "app.containers.AdminPage.ProjectEdit.VolunteeringTab.xVolunteers": "{x, plural, =0 {no volunteers} one {# volunteer} other {# volunteers}}", "app.containers.AdminPage.ProjectEdit.anonymousPolling": "Anonymous polling", "app.containers.AdminPage.ProjectEdit.archived": "Archived", "app.containers.AdminPage.ProjectEdit.archivedStatus": "Archived", "app.containers.AdminPage.ProjectEdit.areasAllLabel": "All Areas", "app.containers.AdminPage.ProjectEdit.areasLabelTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.areasSelectionLabel": "Selection", "app.containers.AdminPage.ProjectEdit.cardDisplay": "Cards", "app.containers.AdminPage.ProjectEdit.createAProjectFromATemplate": "Create a project from a template", "app.containers.AdminPage.ProjectEdit.createPoll": "Create a poll", "app.containers.AdminPage.ProjectEdit.createPollDescription": "Set up a multiple-choice questionnaire.", "app.containers.AdminPage.ProjectEdit.defaultPostSortingTooltip": "You can set the default order for posts to be displayed on the main project page.", "app.containers.AdminPage.ProjectEdit.defaultSorting": "Sorting", "app.containers.AdminPage.ProjectEdit.departments": "Departments", "app.containers.AdminPage.ProjectEdit.descriptionTab": "Description", "app.containers.AdminPage.ProjectEdit.disabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingDisabled": "Disabled", "app.containers.AdminPage.ProjectEdit.downvotingEnabled": "Enabled", "app.containers.AdminPage.ProjectEdit.draft": "Draft", "app.containers.AdminPage.ProjectEdit.draftStatus": "Draft", "app.containers.AdminPage.ProjectEdit.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEdit.enabled": "Enabled", "app.containers.AdminPage.ProjectEdit.enalyzer": "Enalyzer", "app.containers.AdminPage.ProjectEdit.eventsTab": "Events", "app.containers.AdminPage.ProjectEdit.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectEdit.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the project information page.", "app.containers.AdminPage.ProjectEdit.findVolunteers": "Find volunteers", "app.containers.AdminPage.ProjectEdit.findVolunteersDescriptionText": "Ask participants to volunteer for activities and causes.", "app.containers.AdminPage.ProjectEdit.folderSelectError": "Select a folder to add this project to.", "app.containers.AdminPage.ProjectEdit.fromATemplate": "From a template", "app.containers.AdminPage.ProjectEdit.generalTab": "General", "app.containers.AdminPage.ProjectEdit.google_forms": "Google Forms", "app.containers.AdminPage.ProjectEdit.inputAndFeedback": "Collect input and feedback", "app.containers.AdminPage.ProjectEdit.inputAssignmentSectionTitle": "Who is responsible for processing the posts?", "app.containers.AdminPage.ProjectEdit.inputAssignmentTooltipText": "All new input in this project will be assigned to this person. The assignee can be changed in the {ideaManagerLink}.", "app.containers.AdminPage.ProjectEdit.inputCommentingEnabled": "Commenting on posts", "app.containers.AdminPage.ProjectEdit.inputFormTab": "Input form", "app.containers.AdminPage.ProjectEdit.inputManagerLinkText": "input manager", "app.containers.AdminPage.ProjectEdit.inputManagerTab": "Input manager", "app.containers.AdminPage.ProjectEdit.inputPostingEnabled": "Submitting posts", "app.containers.AdminPage.ProjectEdit.inputsDefaultView": "Default view", "app.containers.AdminPage.ProjectEdit.inputsDefaultViewTooltip": "Choose the default view to show input: cards in a grid view or pins on a map. Participants can manually switch between the two views.", "app.containers.AdminPage.ProjectEdit.limited": "Limited", "app.containers.AdminPage.ProjectEdit.loadMoreTemplates": "Load more templates", "app.containers.AdminPage.ProjectEdit.mapDisplay": "Map", "app.containers.AdminPage.ProjectEdit.mapTab": "Map", "app.containers.AdminPage.ProjectEdit.maximum": "Maximum", "app.containers.AdminPage.ProjectEdit.maximumTooltip": "Participants cannot exceed this budget when submitting their basket.", "app.containers.AdminPage.ProjectEdit.microsoft_forms": "Microsoft Forms", "app.containers.AdminPage.ProjectEdit.minimum": "Minimum", "app.containers.AdminPage.ProjectEdit.minimumTooltip": "Require participants to meet a minimum budget to submit their basket (enter '0' if you would not like to set a minimum).", "app.containers.AdminPage.ProjectEdit.moderationInfoCenterLinkText": "visit our Help Center", "app.containers.AdminPage.ProjectEdit.moreDetails": "More details", "app.containers.AdminPage.ProjectEdit.moreInfoModeratorLink": "https://support.govocal.com/en/articles/2672884-what-are-the-different-roles-on-the-platform", "app.containers.AdminPage.ProjectEdit.newProject": "New Project", "app.containers.AdminPage.ProjectEdit.newestFirstSortingMethod": "Most recent", "app.containers.AdminPage.ProjectEdit.noBudgetingAmountErrorMessage": "Not a valid amount", "app.containers.AdminPage.ProjectEdit.noTemplatesFound": "No templates found", "app.containers.AdminPage.ProjectEdit.noTitleErrorMessage": "Please enter a project title", "app.containers.AdminPage.ProjectEdit.noVotingLimitErrorMessage": "Not a valid number", "app.containers.AdminPage.ProjectEdit.oldestFirstSortingMethod": "Oldest", "app.containers.AdminPage.ProjectEdit.onlyAdminsCanView": "Only visible to admin", "app.containers.AdminPage.ProjectEdit.optionNo": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.optionYes": "Yes (select folder)", "app.containers.AdminPage.ProjectEdit.participationLevels": "Participation levels", "app.containers.AdminPage.ProjectEdit.participationMethodTitleText": "What do you want to do?", "app.containers.AdminPage.ProjectEdit.participationMethodTooltip": "Choose how users can participate.", "app.containers.AdminPage.ProjectEdit.permissionsTab": "Access rights", "app.containers.AdminPage.ProjectEdit.pollTab": "Poll", "app.containers.AdminPage.ProjectEdit.projectCardImageLabelText": "Project card image", "app.containers.AdminPage.ProjectEdit.projectManagerTooltipContent": "Project managers can edit projects, manage posts and email participants. You can {moderationInfoCenterLink} to find more information about the rights assigned to project managers.", "app.containers.AdminPage.ProjectEdit.projectName": "Project name", "app.containers.AdminPage.ProjectEdit.projectTypeTitle": "Project type", "app.containers.AdminPage.ProjectEdit.projectTypeTooltip": "Projects with a timeline have a clear beginning and end and can have different phases. Projects without a timeline are continuous.", "app.containers.AdminPage.ProjectEdit.projectTypeWarning": "The project type can not be changed later.", "app.containers.AdminPage.ProjectEdit.publishedStatus": "Published", "app.containers.AdminPage.ProjectEdit.purposes": "Purposes", "app.containers.AdminPage.ProjectEdit.qualtrics": "Qualtrics", "app.containers.AdminPage.ProjectEdit.randomSortingMethod": "Random", "app.containers.AdminPage.ProjectEdit.saveErrorMessage": "An error occurred while saving your data. Please try again.", "app.containers.AdminPage.ProjectEdit.saveProject": "Save", "app.containers.AdminPage.ProjectEdit.saveSuccess": "Success!", "app.containers.AdminPage.ProjectEdit.saveSuccessMessage": "Your form has been saved!", "app.containers.AdminPage.ProjectEdit.searchPlaceholder": "Search the templates", "app.containers.AdminPage.ProjectEdit.selectGroups": "Select group(s)", "app.containers.AdminPage.ProjectEdit.shareInformation": "Share information", "app.containers.AdminPage.ProjectEdit.smart_survey": "SmartSurvey", "app.containers.AdminPage.ProjectEdit.subtitleGeneral": "Set up and personalize your project.", "app.containers.AdminPage.ProjectEdit.surveyEmbedUrl": "Embed URL", "app.containers.AdminPage.ProjectEdit.surveyService": "Service", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltip": "You can find more information on how to embed a survey {surveyServiceTooltipLink}.", "app.containers.AdminPage.ProjectEdit.surveyServiceTooltipLinkText": "here", "app.containers.AdminPage.ProjectEdit.survey_monkey": "Survey Monkey", "app.containers.AdminPage.ProjectEdit.survey_xact": "SurveyXact", "app.containers.AdminPage.ProjectEdit.titleGeneral": "General settings for the project", "app.containers.AdminPage.ProjectEdit.titleLabel": "Title", "app.containers.AdminPage.ProjectEdit.titleLabelTooltip": "Choose a title that is short, engaging and clear. It will be shown in the dropdown overview and on the project cards on the home page.", "app.containers.AdminPage.ProjectEdit.totalBudget": "Total budget", "app.containers.AdminPage.ProjectEdit.trendingSortingMethod": "Trending", "app.containers.AdminPage.ProjectEdit.typeform": "Typeform", "app.containers.AdminPage.ProjectEdit.unassigned": "Unassigned", "app.containers.AdminPage.ProjectEdit.unlimited": "Unlimited", "app.containers.AdminPage.ProjectEdit.useTemplate": "Use template", "app.containers.AdminPage.ProjectEdit.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.volunteeringTab": "Volunteering", "app.containers.AdminPage.ProjectEdit.xGroupsHaveAccess": "{groupCount, plural, no {# groups can view} one {# group can view} other {# groups can view}}", "app.containers.AdminPage.ProjectEvents.addEventButton": "Add an event", "app.containers.AdminPage.ProjectEvents.dateStartLabel": "Start", "app.containers.AdminPage.ProjectEvents.datesEndLabel": "End", "app.containers.AdminPage.ProjectEvents.deleteButtonLabel": "Delete", "app.containers.AdminPage.ProjectEvents.deleteConfirmationModal": "Are you sure you want to delete this event? There is no way to undo this!", "app.containers.AdminPage.ProjectEvents.descriptionLabel": "Event description", "app.containers.AdminPage.ProjectEvents.editButtonLabel": "Edit", "app.containers.AdminPage.ProjectEvents.editEventTitle": "Edit Event", "app.containers.AdminPage.ProjectEvents.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectEvents.fileUploadLabelTooltip": "Attachments are shown below the event description.", "app.containers.AdminPage.ProjectEvents.locationLabel": "Location", "app.containers.AdminPage.ProjectEvents.newEventTitle": "Create a new event", "app.containers.AdminPage.ProjectEvents.saveButtonLabel": "Save", "app.containers.AdminPage.ProjectEvents.saveErrorMessage": "We could not save your changes, please try again.", "app.containers.AdminPage.ProjectEvents.saveSuccessLabel": "Success!", "app.containers.AdminPage.ProjectEvents.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectEvents.subtitleEvents": "Link upcoming events to this projects and show them on the project's event page.", "app.containers.AdminPage.ProjectEvents.titleColumnHeader": "Title and dates", "app.containers.AdminPage.ProjectEvents.titleEvents": "Project events", "app.containers.AdminPage.ProjectEvents.titleLabel": "Event name", "app.containers.AdminPage.ProjectIdeaForm.collapseAll": "Collapse all fields", "app.containers.AdminPage.ProjectIdeaForm.descriptionLabel": "Field description", "app.containers.AdminPage.ProjectIdeaForm.enabled": "Enabled", "app.containers.AdminPage.ProjectIdeaForm.enabledTooltipContent": "Include this field.", "app.containers.AdminPage.ProjectIdeaForm.errorMessage": "Something went wrong, please try again later", "app.containers.AdminPage.ProjectIdeaForm.expandAll": "Expand all fields", "app.containers.AdminPage.ProjectIdeaForm.inputForm": "Input form", "app.containers.AdminPage.ProjectIdeaForm.postDescription": "Specify what information should be provided, add short descriptions or instructions to guide participant responses and specify whether each field is optional or required", "app.containers.AdminPage.ProjectIdeaForm.required": "Required", "app.containers.AdminPage.ProjectIdeaForm.requiredTooltipContent": "Require this field to be filled in.", "app.containers.AdminPage.ProjectIdeaForm.save": "Save", "app.containers.AdminPage.ProjectIdeaForm.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectIdeaForm.saved": "Saved!", "app.containers.AdminPage.ProjectTimeline.datesLabel": "Dates", "app.containers.AdminPage.ProjectTimeline.deletePhaseConfirmation": "Are you sure you want to delete this phase?", "app.containers.AdminPage.ProjectTimeline.descriptionLabel": "Phase description", "app.containers.AdminPage.ProjectTimeline.endDatePlaceholder": "End Date", "app.containers.AdminPage.ProjectTimeline.fileUploadLabel": "Attachments (max 50MB)", "app.containers.AdminPage.ProjectTimeline.saveErrorMessage": "There was an error submitting the form, please try again.", "app.containers.AdminPage.ProjectTimeline.saveSuccessLabel": "Saved!", "app.containers.AdminPage.ProjectTimeline.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.ProjectTimeline.startDatePlaceholder": "Start date", "app.containers.AdminPage.ProjectTimeline.titleLabel": "Phase name", "app.containers.AdminPage.ReportsTab.customFieldTitleExport": "{fieldName}_repartition", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addAFieldButton": "Add field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addANewRegistrationField": "Add a new registration field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.addOption": "Add option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerFormat": "Answer format", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOption": "Answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.answerOptionsTab": "Answer choices", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsSubSectionTitle": "Fields", "app.containers.AdminPage.SettingsPage.CustomSignupFields.customFieldsTooltip": "Drag and drop the fields to determine the order in which they appear in the sign-up form.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.defaultField": "De<PERSON>ult field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.descriptionTooltip": "Optional text shown under the field name on the signup form.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.domicileManagementInfo": "Answer choices for place of residence can be set in the {geographicAreasTabLink}.", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.CustomSignupFields.editCustomFieldAnswerOptionFormTitle": "Edit answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldDescription": "Description", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldName": "Field name", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldSettingsTab": "Field settings", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_checkbox": "Yes-no (checkbox)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_date": "Date", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiline_text": "Long answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_multiselect": "Multiple choice (select multiple)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_number": "Numeric value", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_select": "Multiple choice (select one)", "app.containers.AdminPage.SettingsPage.CustomSignupFields.fieldType_text": "Short answer", "app.containers.AdminPage.SettingsPage.CustomSignupFields.geographicAreasTabLinkText": "Geographic areas tab", "app.containers.AdminPage.SettingsPage.CustomSignupFields.hiddenField": "Hidden field", "app.containers.AdminPage.SettingsPage.CustomSignupFields.isFieldRequired": "Make answering this field required?", "app.containers.AdminPage.SettingsPage.CustomSignupFields.listTitle": "Custom fields", "app.containers.AdminPage.SettingsPage.CustomSignupFields.newCustomFieldAnswerOptionFormTitle": "Add answer option", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionCancelButton": "Cancel", "app.containers.AdminPage.SettingsPage.CustomSignupFields.optionDeleteButton": "Delete", "app.containers.AdminPage.SettingsPage.CustomSignupFields.required": "Required", "app.containers.AdminPage.SettingsPage.addAreaButton": "Add a geographic area", "app.containers.AdminPage.SettingsPage.addTopicButton": "Add tag", "app.containers.AdminPage.SettingsPage.areaDeletionConfirmation": "Are you sure you want to delete this area?", "app.containers.AdminPage.SettingsPage.areaTerm": "Term for one area (singular)", "app.containers.AdminPage.SettingsPage.areaTermPlaceholder": "area", "app.containers.AdminPage.SettingsPage.areas.deleteButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.areas.editButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.areasTerm": "Term for multiple areas (plural)", "app.containers.AdminPage.SettingsPage.areasTermPlaceholder": "areas", "app.containers.AdminPage.SettingsPage.avatarsTitle": "Avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatars": "Display avatars", "app.containers.AdminPage.SettingsPage.bannerDisplayHeaderAvatarsSubtitle": "Show profile pictures of participants and number of them to non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOut": "Header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerHeaderSignedOutSubtitle": "Sub-header text for non-registered visitors", "app.containers.AdminPage.SettingsPage.bannerTextTitle": "Banner text", "app.containers.AdminPage.SettingsPage.bgHeaderPreviewSelectLabel": "Show preview for", "app.containers.AdminPage.SettingsPage.brandingDescription": "Add your logo and set the platform colors.", "app.containers.AdminPage.SettingsPage.brandingTitle": "Platform branding", "app.containers.AdminPage.SettingsPage.cancel": "Cancel", "app.containers.AdminPage.SettingsPage.color_primary": "Primary color", "app.containers.AdminPage.SettingsPage.color_secondary": "Secondary color", "app.containers.AdminPage.SettingsPage.color_text": "Text color", "app.containers.AdminPage.SettingsPage.colorsTitle": "Colors", "app.containers.AdminPage.SettingsPage.confirmHeader": "Are you sure you want to delete this tag?", "app.containers.AdminPage.SettingsPage.contentModeration": "Content moderation", "app.containers.AdminPage.SettingsPage.ctaHeader": "Buttons", "app.containers.AdminPage.SettingsPage.customized_button": "Custom", "app.containers.AdminPage.SettingsPage.customized_button_text_label": "Button text", "app.containers.AdminPage.SettingsPage.customized_button_url_label": "Button link", "app.containers.AdminPage.SettingsPage.defaultTopic": "Default tag", "app.containers.AdminPage.SettingsPage.delete": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicButtonLabel": "Delete", "app.containers.AdminPage.SettingsPage.deleteTopicConfirmation": "This will delete the tag from all existing posts. This change will apply to all projects.", "app.containers.AdminPage.SettingsPage.descriptionTopicManagerText": "Add and delete tags that you would like to use on your platform to categorize posts. You can add the tags to specific projects in the {adminProjectsLink}.", "app.containers.AdminPage.SettingsPage.desktop": "Desktop", "app.containers.AdminPage.SettingsPage.editFormTitle": "Edit area", "app.containers.AdminPage.SettingsPage.editTopicButtonLabel": "Edit", "app.containers.AdminPage.SettingsPage.editTopicFormTitle": "Edit tag", "app.containers.AdminPage.SettingsPage.fieldDescription": "Area description", "app.containers.AdminPage.SettingsPage.fieldDescriptionTooltip": "This description is only for internal collaboration and is not shown to users.", "app.containers.AdminPage.SettingsPage.fieldTitle": "Area name", "app.containers.AdminPage.SettingsPage.fieldTitleTooltip": "The name you choose for each area can be used as a registration field option and to filter projects on the homepage.", "app.containers.AdminPage.SettingsPage.fieldTopicTitle": "Tag name", "app.containers.AdminPage.SettingsPage.fieldTopicTitleTooltip": "The name you choose for each tag will be visible to the platform users", "app.containers.AdminPage.SettingsPage.header": "Homepage banner", "app.containers.AdminPage.SettingsPage.headerDescription": "Customise the homepage banner image and text.", "app.containers.AdminPage.SettingsPage.header_bg": "Banner image", "app.containers.AdminPage.SettingsPage.helmetDescription": "Admin settings page", "app.containers.AdminPage.SettingsPage.helmetTitle": "Admin settings page", "app.containers.AdminPage.SettingsPage.homePageCustomizableDescription": "Add your own content to the customizable section at the bottom of the homepage.", "app.containers.AdminPage.SettingsPage.imageOverlayColor": "Image overlay color", "app.containers.AdminPage.SettingsPage.imageOverlayOpacity": "Image overlay opacity", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSetting": "Detect inappropriate content", "app.containers.AdminPage.SettingsPage.inappropriateContentDetectionSettingDescription": "Auto-detect inappropriate content posted on the platform.", "app.containers.AdminPage.SettingsPage.languages": "Languages", "app.containers.AdminPage.SettingsPage.languagesTooltip": "You can select multiple languages in which you want to offer your platform to users. You will need to create content for every selected language.", "app.containers.AdminPage.SettingsPage.linkToActivityPageText": "Activity", "app.containers.AdminPage.SettingsPage.logo": "Logo", "app.containers.AdminPage.SettingsPage.noHeader": "Please upload a header image (1440 x 480 px)", "app.containers.AdminPage.SettingsPage.no_button": "No button", "app.containers.AdminPage.SettingsPage.organizationName": "Name of city or organization", "app.containers.AdminPage.SettingsPage.phone": "Phone", "app.containers.AdminPage.SettingsPage.platformConfiguration": "Platform configuration", "app.containers.AdminPage.SettingsPage.profanityBlockerSetting": "Profanity blocker", "app.containers.AdminPage.SettingsPage.profanityBlockerSettingDescription": "Block input, proposals and comments containing the most commonly reported offensive words", "app.containers.AdminPage.SettingsPage.projectsHeaderDescription": "This text is shown on the homepage above the projects.", "app.containers.AdminPage.SettingsPage.projectsSettings": "project settings", "app.containers.AdminPage.SettingsPage.projects_header": "Projects header", "app.containers.AdminPage.SettingsPage.registrationFields": "Registration fields", "app.containers.AdminPage.SettingsPage.registrationHelperTextDescription": "Provide a short description at the top of your registration form.", "app.containers.AdminPage.SettingsPage.registrationTitle": "Registration", "app.containers.AdminPage.SettingsPage.save": "Save", "app.containers.AdminPage.SettingsPage.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.SettingsPage.saveSuccess": "Success!", "app.containers.AdminPage.SettingsPage.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.SettingsPage.settingsSavingError": "Couldn't save. Try changing the setting again.", "app.containers.AdminPage.SettingsPage.sign_up_button": "\"Sign up\"", "app.containers.AdminPage.SettingsPage.signed_in": "Button for registered visitors", "app.containers.AdminPage.SettingsPage.signed_out": "Button for non-registered visitors", "app.containers.AdminPage.SettingsPage.signupFormText": "Registration helper text", "app.containers.AdminPage.SettingsPage.signupFormTooltip": "Add a short description at the top of the sign-up form.", "app.containers.AdminPage.SettingsPage.step1": "Email and password step", "app.containers.AdminPage.SettingsPage.step1Tooltip": "This is shown on the top of the first page of the sign-up form (name, email, password).", "app.containers.AdminPage.SettingsPage.step2": "Registration questions step", "app.containers.AdminPage.SettingsPage.step2Tooltip": "This is shown on the top of the second page of the sign-up form (additional registration fields).", "app.containers.AdminPage.SettingsPage.subtitleAreas": "Define the geographic areas that you would like to use for your platform, such as neighborhoods, boroughs or districts. You can associate these geographic areas with projects (filterable on the landing page) or ask participants to select their area of residence as a registration field to create Smart Groups and define access rights.", "app.containers.AdminPage.SettingsPage.subtitleBasic": "Choose how people will see your organization name, select the languages of your platform and link to your website.", "app.containers.AdminPage.SettingsPage.subtitleMaxCharError": "The provided subtitle exceeds the maximum allowed character limit (90 chars)", "app.containers.AdminPage.SettingsPage.subtitleRegistration": "Specify what information people are asked to provide when signing up.", "app.containers.AdminPage.SettingsPage.subtitleTerminology": "Terminology", "app.containers.AdminPage.SettingsPage.successfulUpdateSettings": "Settings updated successfully.", "app.containers.AdminPage.SettingsPage.tabPolicies": "Policies", "app.containers.AdminPage.SettingsPage.tabRegistration": "Registration", "app.containers.AdminPage.SettingsPage.tabSettings": "General", "app.containers.AdminPage.SettingsPage.tabWidgets": "Widget", "app.containers.AdminPage.SettingsPage.tablet": "Tablet", "app.containers.AdminPage.SettingsPage.terminologyTooltip": "Define what geographic unit you would like to use for your projects (e.g., neighborhoods, districts, boroughs, etc.)", "app.containers.AdminPage.SettingsPage.titleAreas": "Geographic areas", "app.containers.AdminPage.SettingsPage.titleBasic": "General settings", "app.containers.AdminPage.SettingsPage.titleMaxCharError": "The provided title exceeds the maximum allowed character limit (35 chars)", "app.containers.AdminPage.SettingsPage.titleTopicManager": "Tag manager", "app.containers.AdminPage.SettingsPage.urlError": "The URL is not valid", "app.containers.AdminPage.SettingsPage.urlTitle": "Website", "app.containers.AdminPage.SettingsPage.urlTitleTooltip": "You can add a link to your own website. This link will be used on the bottom of the homepage.", "app.containers.AdminPage.SideBar.dashboard": "Dashboard", "app.containers.AdminPage.SideBar.emails": "Emails", "app.containers.AdminPage.SideBar.groups": "Groups", "app.containers.AdminPage.SideBar.guide": "Guide", "app.containers.AdminPage.SideBar.inputManager": "Input manager", "app.containers.AdminPage.SideBar.insights": "Reporting", "app.containers.AdminPage.SideBar.moderation": "Activity", "app.containers.AdminPage.SideBar.processing": "Processing", "app.containers.AdminPage.SideBar.projects": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.SideBar.settings": "Settings", "app.containers.AdminPage.SideBar.users": "Users", "app.containers.AdminPage.SideBar.workshops": "Workshops", "app.containers.AdminPage.Topics.addTopics": "Add", "app.containers.AdminPage.Topics.browseTopics": "Browse tags", "app.containers.AdminPage.Topics.cancel": "Cancel", "app.containers.AdminPage.Topics.confirmHeader": "Are you sure you want to delete this project tag?", "app.containers.AdminPage.Topics.delete": "Delete", "app.containers.AdminPage.Topics.deleteTopicLabel": "Delete", "app.containers.AdminPage.Topics.generalTopicDeletionWarning": "This tag will no longer be able to be added to new posts in this project.", "app.containers.AdminPage.Topics.inputForm": "Input form", "app.containers.AdminPage.Topics.lastTopicWarning": "At least one tag is required. If you do not want to use tags, they can be disabled in the {ideaFormLink} tab.", "app.containers.AdminPage.Topics.projectTopicsDescription": "You can add and delete the tags that can be assigned to posts in this project.", "app.containers.AdminPage.Topics.remove": "Remove", "app.containers.AdminPage.Topics.topicManager": "Tag manager", "app.containers.AdminPage.Topics.topicManagerInfo": "If you would like to add additional project tags, you can do so in the {topicManagerLink}.", "app.containers.AdminPage.Users.GroupCreation.createGroupButton": "Add a new group", "app.containers.AdminPage.Users.GroupCreation.fieldGroupName": "Group name", "app.containers.AdminPage.Users.GroupCreation.modalHeaderManual": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.modalHeaderStep1": "What type of group do you need?", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonNormal": "Create a manual group", "app.containers.AdminPage.Users.GroupCreation.step1CreateButtonSmart": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.step1LearnMoreGroups": "Learn more about groups", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionNormal": "You can select users from the overview and add them to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeDescriptionSmart": "You can define conditions and users who meet the conditions are automatically added to this group.", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameNormal": "Manual group", "app.containers.AdminPage.Users.GroupCreation.step1TypeNameSmart": "Smart group", "app.containers.AdminPage.Users.GroupsPanel.emptyGroup": "There is no one in this group yet", "app.containers.AdminPage.Users.GroupsPanel.goToAllUsers": "Go to {allUsersLink} to manually add some users.", "app.containers.AdminPage.Users.GroupsPanel.noUserMatchesYourSearch": "No user(s) match your search", "app.containers.AdminPage.Users.GroupsPanel.select": "Select", "app.containers.AdminPage.Users.UsersGroup.groupDeletionConfirmation": "Are you sure you want to delete this group?", "app.containers.AdminPage.Users.UsersGroup.membershipAddFailed": "An error occurred while adding users to the groups, please try again.", "app.containers.AdminPage.Users.UsersGroup.membershipDelete": "Remove from group", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteConfirmation": "Delete selected users from this group?", "app.containers.AdminPage.Users.UsersGroup.membershipDeleteFailed": "An error occurred while deleting users from the group, please try again.", "app.containers.AdminPage.Users.UsersGroup.moveUsersButton": "Add", "app.containers.AdminPage.groups.permissions.add": "Add", "app.containers.AdminPage.groups.permissions.deleteButtonLabel": "Delete", "app.containers.AdminPage.groups.permissions.deleteModeratorLabel": "Delete", "app.containers.AdminPage.groups.permissions.groupDeletionConfirmation": "Are you sure you want to remove this group from the project?", "app.containers.AdminPage.groups.permissions.groupsMultipleSelectPlaceholder": "Select one or more groups", "app.containers.AdminPage.groups.permissions.members": "{count, plural, =0 {No members} one {1 member} other {{count} members}}", "app.containers.AdminPage.groups.permissions.moderatorDeletionConfirmation": "Are you sure?", "app.containers.AdminPage.groups.permissions.moderatorsNotFound": "Project managers not found", "app.containers.AdminPage.groups.permissions.noActionsCanBeTakenInThisProject": "Nothing is shown, because there are no actions the user can take in this project.", "app.containers.AdminPage.groups.permissions.pendingInvitation": "Pending invitation", "app.containers.AdminPage.groups.permissions.save": "Save", "app.containers.AdminPage.groups.permissions.saveErrorMessage": "Something went wrong, please try again later.", "app.containers.AdminPage.groups.permissions.saveSuccess": "Success!", "app.containers.AdminPage.groups.permissions.saveSuccessMessage": "Your changes have been saved.", "app.containers.AdminPage.projects.all.existingProjects": "Existing projects", "app.containers.AdminPage.projects.all.projectsAndFolders": "Projects and folders", "app.containers.AdminPage.widgets.copied": "Copied to clipboard", "app.containers.AdminPage.widgets.copyToClipboard": "Copy this code", "app.containers.AdminPage.widgets.exportHtmlCodeButton": "Copy the HTML code", "app.containers.AdminPage.widgets.fieldAccentColor": "Accent color", "app.containers.AdminPage.widgets.fieldBackgroundColor": "Widget background color", "app.containers.AdminPage.widgets.fieldButtonText": "Button text", "app.containers.AdminPage.widgets.fieldButtonTextDefault": "Join now", "app.containers.AdminPage.widgets.fieldFont": "Font", "app.containers.AdminPage.widgets.fieldFontDescription": "This must be an existing font name from {googleFontsLink}.", "app.containers.AdminPage.widgets.fieldFontSize": "Font size (px)", "app.containers.AdminPage.widgets.fieldHeaderSubText": "Header subtitle", "app.containers.AdminPage.widgets.fieldHeaderSubTextDefault": "You can have a say", "app.containers.AdminPage.widgets.fieldHeaderText": "Header title", "app.containers.AdminPage.widgets.fieldHeaderTextDefault": "Our participation platform", "app.containers.AdminPage.widgets.fieldHeight": "Height (px)", "app.containers.AdminPage.widgets.fieldInputsLimit": "Number of posts", "app.containers.AdminPage.widgets.fieldProjects": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.fieldRelativeLink": "Links to", "app.containers.AdminPage.widgets.fieldShowFooter": "Show button", "app.containers.AdminPage.widgets.fieldShowHeader": "Show header", "app.containers.AdminPage.widgets.fieldShowLogo": "Show logo", "app.containers.AdminPage.widgets.fieldSiteBackgroundColor": "Site background color", "app.containers.AdminPage.widgets.fieldSort": "Sorted by", "app.containers.AdminPage.widgets.fieldTextColor": "Text color", "app.containers.AdminPage.widgets.fieldTopics": "Tags", "app.containers.AdminPage.widgets.fieldWidth": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.widgets.homepage": "Homepage", "app.containers.AdminPage.widgets.htmlCodeExplanation": "You can copy this HTML code and paste it on that part of your website where you want to add your widget.", "app.containers.AdminPage.widgets.htmlCodeTitle": "Widget HTML code", "app.containers.AdminPage.widgets.previewTitle": "Preview", "app.containers.AdminPage.widgets.settingsTitle": "Settings", "app.containers.AdminPage.widgets.sortNewest": "Newest", "app.containers.AdminPage.widgets.sortPopular": "Popular", "app.containers.AdminPage.widgets.sortTrending": "Trending", "app.containers.AdminPage.widgets.subtitleWidgets": "You can create a widget, customize it and add it to your own website to attract people to this platform.", "app.containers.AdminPage.widgets.title": "Widget", "app.containers.AdminPage.widgets.titleDimensions": "Dimensions", "app.containers.AdminPage.widgets.titleHeaderAndFooter": "Header & Footer", "app.containers.AdminPage.widgets.titleInputSelection": "Input selection", "app.containers.AdminPage.widgets.titleStyle": "Style", "app.containers.AdminPage.widgets.titleWidgets": "Widget", "app.containers.admin.ideaStatuses.all.addIdeaStatus": "Add status", "app.containers.admin.ideaStatuses.all.deleteButtonLabel": "Delete", "app.containers.admin.ideaStatuses.all.editButtonLabel": "Edit", "app.containers.admin.ideaStatuses.all.editIdeaStatus": "Edit status", "app.containers.admin.ideaStatuses.all.inputStatusDeleteButtonTooltip": "Statuses currently assigned to participant input cannot be deleted. You can remove/change the status from existing input in the {manageTab} tab.", "app.containers.admin.ideaStatuses.all.lockedStatusTooltip": "This status cannot be deleted or moved.", "app.containers.admin.ideaStatuses.all.manage": "Edit", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeDescription": "Selected for implementation or next steps", "app.containers.admin.ideaStatuses.form.acceptedFieldCodeTitle": "Approved", "app.containers.admin.ideaStatuses.form.category": "Category", "app.containers.admin.ideaStatuses.form.categoryDescription": "Please select the category that best represents your status. This selection will help our analytics tool to more accurately process and analyze posts.", "app.containers.admin.ideaStatuses.form.customFieldCodeTitle": "Other", "app.containers.admin.ideaStatuses.form.fieldColor": "Color", "app.containers.admin.ideaStatuses.form.fieldDescription": "Status Description", "app.containers.admin.ideaStatuses.form.fieldTitle": "Status Name", "app.containers.admin.ideaStatuses.form.implementedFieldCodeDescription": "Successfully implemented", "app.containers.admin.ideaStatuses.form.implementedFieldCodeTitle": "Implemented", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeDescription": "Ineligible or not selected to move forward", "app.containers.admin.ideaStatuses.form.rejectedFieldCodeTitle": "Not Selected", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeDescription": "Considered for implementation or next steps", "app.containers.admin.ideaStatuses.form.under_considerationFieldCodeTitle": "Under Consideration", "app.containers.admin.ideaStatuses.form.viewedFieldCodeDescription": "Viewed but not yet processed", "app.containers.admin.ideaStatuses.form.viewedFieldCodeTitle": "Viewed", "app.containers.admin.ideas.all.inputManagerMetaDescription": "Manage input and their statuses.", "app.containers.admin.ideas.all.inputManagerMetaTitle": "Input manager | Participation platform of {orgName}", "app.containers.admin.ideas.all.inputManagerPageSubtitle": "Give feedback, add tags and move input from one project to another", "app.containers.admin.project.permissions.permissionsSelectionLabel": "Selection", "app.containers.admin.project.permissions.viewingRightsTitle": "Who can see this project?", "app.modules.commercial.flag_inappropriate_content.admin.components.flagTooltip": "You can remove this content flag by selecting this item and clicking the remove button at the top. It will then reappear in the Seen or Not seen tabs", "app.modules.commercial.flag_inappropriate_content.admin.components.nlpFlaggedWarningText": "Inappropriate content auto-detected.", "app.modules.commercial.flag_inappropriate_content.admin.components.noWarningItems": "There are no posts reported for review by the community or flagged for inappropriate content by our Natural Language Processing system", "app.modules.commercial.flag_inappropriate_content.admin.components.removeWarning": "Remove {numberOfItems, plural, one {content warning} other {# content warnings}}", "app.modules.commercial.flag_inappropriate_content.admin.components.userFlaggedWarningText": "Reported as inappropriate by a platform user.", "app.modules.commercial.flag_inappropriate_content.admin.components.warnings": "Content Warnings", "app.modules.navbar.admin.components.NavbarItemList.navigationItems": "Pages shown on your navigation bar", "app.modules.navbar.admin.containers.deletePageConfirmation": "Are you sure you want to delete this page? This cannot be undone. You can also remove the page from the navigation bar if you aren’t ready to delete it yet.", "app.modules.navbar.admin.containers.pageSubtitle": "Your navigation bar can display up to five pages in addition to the Home and projects pages. You can rename menu items, re-order and add new pages  with your own content."}