{"EmailSettingsPage.emailSettings": "E-pasta iestatī<PERSON>mi", "EmailSettingsPage.initialUnsubscribeError": "<PERSON><PERSON><PERSON><PERSON> pro<PERSON> ar at<PERSON> no <PERSON>, l<PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "EmailSettingsPage.initialUnsubscribeLoading": "<PERSON><PERSON><PERSON> pie<PERSON> tiek a<PERSON>, <PERSON><PERSON><PERSON><PERSON>, uz<PERSON><PERSON><PERSON>...", "EmailSettingsPage.initialUnsubscribeSuccess": "<PERSON><PERSON><PERSON> esat veiksmīgi atteicies no {campaignTitle}.", "UI.FormComponents.optional": "<PERSON>z<PERSON><PERSON><PERSON>", "app.closeIconButton.a11y_buttonActionMessage": "Aizvērt", "app.components.Areas.areaUpdateError": "Saglabā<PERSON><PERSON>, notika <PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "app.components.Areas.followedArea": "Sekojamā joma: {areaTitle}", "app.components.Areas.followedTopic": "<PERSON><PERSON><PERSON> tēma: {topicTitle}", "app.components.Areas.topicUpdateError": "<PERSON><PERSON><PERSON> tēmas saglabāšanas laikā notika kļūda. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "app.components.Areas.unfollowedArea": "Neiepazīstamā zona: {areaTitle}", "app.components.Areas.unfollowedTopic": "Neiepazīstams temats: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Cena:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.added": "Pievienots", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "<PERSON><PERSON><PERSON> esat sadal<PERSON>jis visus kredītus.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "<PERSON><PERSON><PERSON> esat sadalījis maks<PERSON> k<PERSON> skaitu šai iespējai.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "<PERSON><PERSON><PERSON> esat sadalījis visus punktus.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "<PERSON><PERSON><PERSON> esat sadalījis maksim<PERSON> punktu skaitu par šo iespēju.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "<PERSON><PERSON><PERSON> esat sadal<PERSON>jis visus savus žetonus.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "<PERSON><PERSON><PERSON> esat sadalījis maks<PERSON> skaitu šai opcijai.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "<PERSON><PERSON><PERSON> esat sadalījis visas savas balsis.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "<PERSON><PERSON><PERSON> esat sadalījis maksim<PERSON> balsu skaitu par šo iespēju.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(ieskaitot 1 bezsaistē)} other {(ieskaitot # bezsaistē)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "<PERSON><PERSON><PERSON><PERSON><PERSON> nav pieejama, jo <PERSON>is posms nav aktīvs.", "app.components.AssignMultipleVotesControl.removeVote": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "app.components.AssignMultipleVotesControl.select": "Atlasiet", "app.components.AssignMultipleVotesControl.votesSubmitted1": "<PERSON><PERSON><PERSON> jau esat balsojis. <PERSON> to <PERSON><PERSON><PERSON>, noklikšķiniet uz \"Modify your submission\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "<PERSON><PERSON><PERSON> jau esat balsojis. Lai to <PERSON><PERSON><PERSON>, dodieties atpakaļ uz projekta lapu un noklikšķiniet uz \"<PERSON><PERSON><PERSON> iesniegumu\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kred<PERSON>ts} other {kred<PERSON><PERSON>}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {punkts} other {punkti}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {balsot} other {balsis}}", "app.components.AssignVoteControl.maxVotesReached1": "<PERSON><PERSON><PERSON> esat sadalījis visas savas balsis.", "app.components.AssignVoteControl.phaseNotActive": "<PERSON><PERSON><PERSON><PERSON><PERSON> nav pieejama, jo <PERSON>is posms nav aktīvs.", "app.components.AssignVoteControl.select": "Atlasiet", "app.components.AssignVoteControl.selected2": "<PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "Balsot par vismaz 1 iespēju", "app.components.AssignVoteControl.votesSubmitted1": "<PERSON><PERSON><PERSON> jau esat balsojis. <PERSON> to <PERSON><PERSON><PERSON>, noklikšķiniet uz \"Modify your submission\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "<PERSON><PERSON><PERSON> jau esat balsojis. Lai to <PERSON><PERSON><PERSON>, dodieties atpakaļ uz projekta lapu un noklikšķiniet uz \"<PERSON><PERSON><PERSON> iesniegumu\".", "app.components.AuthProviders.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>", "app.components.AuthProviders.continueWithFakeSSO": "<PERSON><PERSON><PERSON><PERSON>t ar viltotu SSO", "app.components.AuthProviders.continueWithGoogle": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.components.AuthProviders.continueWithHoplr": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "app.components.AuthProviders.continueWithIdAustria": "Tu<PERSON><PERSON><PERSON>t ar <PERSON> Austri<PERSON>", "app.components.AuthProviders.continueWithLoginMechanism": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.franceConnectMergingFailed": "<PERSON>zman<PERSON>jot šo e-pasta adresi, jau ir izveidots konts.{br}{br}J<PERSON><PERSON> nevarat piekļ<PERSON>t platformai, i<PERSON><PERSON><PERSON>t FranceConnect, jo nesakr<PERSON>t personas dati. <PERSON>, i<PERSON><PERSON><PERSON>t FranceConnect, vispi<PERSON> šajā platformā ir jāmaina vārds vai uzvārds, lai tas atbilstu jūsu oficiāli reģistrētajiem datiem.{br}{br}J<PERSON>s varat pieteikties, kā parasti, kā norād<PERSON>ts tālāk.", "app.components.AuthProviders.goToLogIn": "Ju<PERSON> jau ir konts? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Jums nav konta? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Pierakstī<PERSON>", "app.components.AuthProviders.logInWithEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON>, izmantojot e-pastu", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "<PERSON> jūs varētu verificēt, j<PERSON> jābūt vismaz norādītajam minimālajam vecumam.", "app.components.AuthProviders.signUp2": "Reģistrēties", "app.components.AuthProviders.signUpButtonAltText": "Reģistrēties, izman<PERSON><PERSON>t {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Reģistrēties, izmantojot e-pastu", "app.components.AuthProviders.verificationRequired": "Nepieciešama verifikā<PERSON>", "app.components.Author.a11yPostedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 dalībnieks} other {{numberOfParticipants} dal<PERSON>bnieki}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} lietotāji", "app.components.AvatarBubbles.participant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.participants1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.cancel": "Atcelt", "app.components.Comments.commentingDisabledInCurrentPhase": "Paš<PERSON>iz<PERSON><PERSON>ā posmā komentāri nav iespējami.", "app.components.Comments.commentingDisabledInactiveProject": "<PERSON><PERSON><PERSON><PERSON> nav i<PERSON><PERSON><PERSON><PERSON><PERSON>, jo šis projekts pašlaik nav aktīvs.", "app.components.Comments.commentingDisabledProject": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON> šajā projektā pašlaik ir atspē<PERSON>ta.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink}, lai koment<PERSON>tu.", "app.components.Comments.commentingMaybeNotPermitted": "<PERSON><PERSON><PERSON><PERSON>, {signInLink}, lai <PERSON>, k<PERSON><PERSON> var veikt.", "app.components.Comments.inputsAssociatedWithProfile": "Pēc noklusējuma jūsu iesniegumi tiks saistīti ar jūsu profilu, ja vien neizvēlaties šo opciju.", "app.components.Comments.invisibleTitleComments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.leastRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.likeComment": "<PERSON><PERSON><PERSON><PERSON> kā šis komentārs", "app.components.Comments.mostLiked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.mostRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.official": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.postAnonymously": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.replyToComment": "Atbildēt uz komentāru", "app.components.Comments.reportAsSpam": "<PERSON><PERSON><PERSON>ot kā par surogātpastu", "app.components.Comments.seeOriginal": "Skatīt oriģinālu", "app.components.Comments.seeTranslation": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "app.components.Comments.yourComment": "<PERSON><PERSON><PERSON> koment<PERSON>", "app.components.CommonGroundResults.divisiveDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriem cilvēki vienlīdz piekrīt un nepiekrīt:", "app.components.CommonGroundResults.divisiveTitle": "Šķelšanās", "app.components.CommonGroundResults.majorityDescription": "Vairāk nekā 60 % nobalsoja par šādiem jautājumiem:", "app.components.CommonGroundResults.majorityTitle": "Vairākums", "app.components.CommonGroundResults.participantLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantsLabel1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.statementLabel": "paziņojums", "app.components.CommonGroundResults.statementsLabel1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabe": "balsot", "app.components.CommonGroundResults.votesLabel1": "balsis", "app.components.CommonGroundStatements.agreeLabel": "Piek<PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.disagreeLabel": "Nepiekrītu", "app.components.CommonGroundStatements.noMoreStatements": "<PERSON><PERSON><PERSON>k nav pazi<PERSON>, uz kuriem būtu jāreaģē", "app.components.CommonGroundStatements.noResults": "Pagaid<PERSON><PERSON> vēl nav rezultātu. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka esat piedalījies kopīgā pamatojuma posmā, un pēc tam šeit vēlreiz pārbaudiet.", "app.components.CommonGroundStatements.unsureLabel": "Nav zināms", "app.components.CommonGroundTabs.resultsTabLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.statementsTabLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommunityMonitorModal.formError": "Konstatēta k<PERSON>ūda.", "app.components.CommunityMonitorModal.surveyDescription2": "<PERSON><PERSON><PERSON>gajā aptaujā tiek noskaidrota jūsu attieksme pret pārvaldību un sabiedriskajiem pakalpojumiem.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {aizņem <1 minūti} one {aizņem 1 minūti} other {aizņem # minūtes}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "E-pasts ar aps<PERSON><PERSON><PERSON><PERSON>ma kodu ir nosūtīts uz {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Nomainiet savu e-pasta adresi.", "app.components.ConfirmationModal.codeInput": "Kods", "app.components.ConfirmationModal.confirmationCodeSent": "Nosū<PERSON>īts jauns kods", "app.components.ConfirmationModal.didntGetAnEmail": "Nesaņē<PERSON>āt e-pastu?", "app.components.ConfirmationModal.foundYourCode": "Vai esat atradis savu kodu?", "app.components.ConfirmationModal.goBack": "Atgriezties atpakaļ.", "app.components.ConfirmationModal.sendEmailWithCode": "Nosūtīt e-pastu ar kodu", "app.components.ConfirmationModal.sendNewCode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaunu kodu.", "app.components.ConfirmationModal.verifyAndContinue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un turpināt", "app.components.ConfirmationModal.wrongEmail": "Nepareizs e-pasts?", "app.components.ConsentManager.Banner.accept": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.ariaButtonClose2": "Noraidīt politiku un aizvērt baneri", "app.components.ConsentManager.Banner.close": "Aizvērt", "app.components.ConsentManager.Banner.mainText": "<PERSON><PERSON> <PERSON>a i<PERSON>to s<PERSON>us saskaņā ar mūsu {policyLink}.", "app.components.ConsentManager.Banner.manage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.policyLink": "Sīkdatņu politika", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Mēs to <PERSON><PERSON><PERSON><PERSON><PERSON>, lai personalizētu un mērītu mūsu vietnes reklāmas kampaņu efektivitāti. Šajā platformā mēs nerādīsim nekādas reklāmas, taču turpmāk minētie pakalpojumi var jums piedāvāt personalizētu reklāmu, pamatojoties uz jūsu apmeklētajām mūsu vietnes lapām.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "<PERSON><PERSON><PERSON> i<PERSON>, lai lab<PERSON> i<PERSON>prast<PERSON>, kā jū<PERSON> i<PERSON> platformu, lai mācītos un uzlabotu savu navigāciju. Šī informācija tiek izmantota tikai masveida analītikā, bet nekādā gadījumā netiek izman<PERSON>ta, lai izsekotu atsevišķus cilvēkus.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Atgriezties", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Atcelt", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "<PERSON><PERSON> ir <PERSON>, lai iespējotu un uzraudzītu vietnes pamatfunkcijas. Daži šeit uzskaitītie rīki var uz jums neattiekties. Lai iegūtu plašāku informā<PERSON>ju, <PERSON><PERSON><PERSON><PERSON>, izlasiet mūsu sīkfailu politiku.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google tagu pārvaldnieks ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Lai platforma darb<PERSON>, mēs sagla<PERSON><PERSON><PERSON><PERSON> autentifikācijas sīkfa<PERSON>, ja reģistrējaties, un valodu, kur<PERSON> i<PERSON>to<PERSON>t šo platformu.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "<PERSON><PERSON><PERSON>", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Satura augšupielā<PERSON> atruna", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> saturu, jūs <PERSON><PERSON><PERSON>, ka šis saturs nepārkāpj nekādus noteikumus vai trešo personu tiesības, pie<PERSON><PERSON><PERSON>, intelektu<PERSON><PERSON><PERSON>pašuma tiesības, priv<PERSON><PERSON><PERSON> tiesī<PERSON>, ties<PERSON>bas uz komercnoslēpumiem u. tml. Līdz ar to, augšupielādējot šo saturu, jūs uzņematies pilnu un ekskluzīvu atbildību par visiem tiešajiem un netiešajiem zaudējumiem, kas radušies augšupielādētā satura dēļ. Turklāt jūs apņematies atbrīvot platformas īpašnieku un Go Vocal no jebkādām trešo personu prasībām vai saistībām pret trešām personām, kā arī no jebkādām saistītām izmaksām, kas varētu rasties vai rasties jūsu augšupielādētā satura dēļ.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON> saprotu", "app.components.ContentUploadDisclaimer.onCancel": "Atcelt", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON><PERSON><PERSON> mums, k<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.addressInputAriaLabel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Ievadiet adresi...", "app.components.CustomFieldsForm.adminFieldTooltip": "Lauks redzams tikai <PERSON>m", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Visas š<PERSON><PERSON> a<PERSON>ujā sniegtās atbildes ir anonī<PERSON>.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Daudzst<PERSON><PERSON>ša<PERSON> vismaz trīs punkti.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON>, ir <PERSON><PERSON><PERSON><PERSON>mi vismaz divi punkti.", "app.components.CustomFieldsForm.attachmentRequired": "Nepieciešams vismaz viens pielikums", "app.components.CustomFieldsForm.authorFieldLabel": "Autors", "app.components.CustomFieldsForm.authorFieldPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, lai meklētu pēc lietotāja e-pasta vai vārda...", "app.components.CustomFieldsForm.back": "Atpakaļ", "app.components.CustomFieldsForm.budgetFieldLabel": "Budžets", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Noklikšķiniet uz kartes, lai z<PERSON>. Pēc tam velciet punktus, lai tos p<PERSON>.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "<PERSON>, noklikšķiniet uz kartes vai ierakstiet adresi z<PERSON>.", "app.components.CustomFieldsForm.confirm": "Apstipriniet", "app.components.CustomFieldsForm.descriptionMinLength": "<PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON> vismaz {min} r<PERSON><PERSON><PERSON><PERSON><PERSON> garam.", "app.components.CustomFieldsForm.descriptionRequired": "<PERSON><PERSON><PERSON> ir obligāts", "app.components.CustomFieldsForm.fieldMaximumItems": "Laukam \"{fieldName}\" var izvēlēties ne vairāk kā {maxSelections, plural, one {# opciju} other {# opcijas}} .", "app.components.CustomFieldsForm.fieldMinimumItems": "<PERSON><PERSON><PERSON> \"{fieldName}\" var atlasīt vismaz {minSelections, plural, one {# opciju} other {# opcijas}} .", "app.components.CustomFieldsForm.fieldRequired": "Lauks \"{fieldName}\" ir obligāts", "app.components.CustomFieldsForm.fileSizeLimit": "Faila lieluma ierobežojums ir {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "<PERSON><PERSON><PERSON><PERSON> ir <PERSON>", "app.components.CustomFieldsForm.minimumCoordinates2": "Nepieciešams vismaz {numPoints} kartes punktu.", "app.components.CustomFieldsForm.notPublic1": "*<PERSON><PERSON> atbilde tiks izpausta tikai projektu vad<PERSON>, bet ne sabiedrībai.", "app.components.CustomFieldsForm.otherArea": "Kaut kur citur", "app.components.CustomFieldsForm.progressBarLabel": "Progress", "app.components.CustomFieldsForm.removeAnswer": "Noņemt atbildi", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Izvēlieties tik daudz, cik vēlaties", "app.components.CustomFieldsForm.selectBetween": "*Izvēlieties starp {minItems} un {maxItems} iespējām", "app.components.CustomFieldsForm.selectExactly2": "*Izvēlieties tieši {selectExactly, plural, one {# opcija} other {# opcijas}}", "app.components.CustomFieldsForm.selectMany": "*Izvēlieties tik daudz, cik vēlaties", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Pie<PERSON><PERSON><PERSON> kartei, lai <PERSON>. <PERSON> ve<PERSON>, lai to<PERSON>.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Pieskar<PERSON>ies kartei, lai <PERSON>.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Pieskar<PERSON>ies kartei, lai pievie<PERSON>u atbildi.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Pieskarieties kartei vai ierakstiet adresi, lai pievienotu atbildi.", "app.components.CustomFieldsForm.tapToAddALine": "Pieskar<PERSON><PERSON>, lai pievienotu līniju", "app.components.CustomFieldsForm.tapToAddAPoint": "Pieskarieties, lai pievienotu punktu", "app.components.CustomFieldsForm.tapToAddAnArea": "Pie<PERSON><PERSON><PERSON>, lai pievienotu apgabalu", "app.components.CustomFieldsForm.titleMaxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> jābūt ne garākam par {max} raks<PERSON><PERSON><PERSON><PERSON>m.", "app.components.CustomFieldsForm.titleMinLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> jāb<PERSON>t vismaz {min} rakstzī<PERSON><PERSON> garam.", "app.components.CustomFieldsForm.titleRequired": "<PERSON><PERSON><PERSON><PERSON> ir obligāts", "app.components.CustomFieldsForm.topicRequired": "Nepieciešama vismaz viena birka", "app.components.CustomFieldsForm.typeYourAnswer": "Ierakstiet savu atbildi", "app.components.CustomFieldsForm.typeYourAnswerRequired": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>bilde", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zip failu, kas satur vienu vai vairākus shape failus.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "<PERSON>a atra<PERSON>n<PERSON>s vieta netiek parādīta starp opcij<PERSON>m, ieva<PERSON><PERSON> te<PERSON>, varat pievienot derīgas koordinātas formātā \"platums, garums\", lai norād<PERSON><PERSON> precīzu atrašan<PERSON> vietu (piemēram: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "<PERSON><PERSON><PERSON> lauki bija nederīgi. <PERSON><PERSON><PERSON><PERSON>, izlabojiet k<PERSON>ū<PERSON> un mēģiniet vēlreiz.", "app.components.ErrorBoundary.errorFormErrorGeneric": "<PERSON><PERSON><PERSON> zi<PERSON><PERSON> iesniegša<PERSON> laikā radās nezin<PERSON> k<PERSON>. L<PERSON>d<PERSON>, mēģiniet vēlreiz.", "app.components.ErrorBoundary.errorFormLabelClose": "Aizvērt", "app.components.ErrorBoundary.errorFormLabelComments": "Kas not<PERSON>?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-pasts", "app.components.ErrorBoundary.errorFormLabelName": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelSubmit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "<PERSON><PERSON><PERSON> komanda ir informēta.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON> <PERSON><PERSON>, lai mēs j<PERSON>, <PERSON><PERSON><PERSON><PERSON> a<PERSON>, kas notika.", "app.components.ErrorBoundary.errorFormSuccessMessage": "<PERSON><PERSON><PERSON> at<PERSON>e ir nosūt<PERSON>ta. Paldies!", "app.components.ErrorBoundary.errorFormTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka ir probl<PERSON>.", "app.components.ErrorBoundary.genericErrorWithForm": "<PERSON><PERSON><PERSON><PERSON>, un mēs nevaram parādīt šo saturu. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz vai {openForm}", "app.components.ErrorBoundary.openFormText": "p<PERSON><PERSON><PERSON><PERSON><PERSON> mums to no<PERSON><PERSON><PERSON>", "app.components.ErrorToast.budgetExceededError": "Jums nav pietiekama budžeta", "app.components.ErrorToast.votesExceededError": "Jums vairs nav pietiekami daudz balsu", "app.components.EventAttendanceButton.forwardToFriend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.maxRegistrationsReached": "<PERSON>r sasnie<PERSON>s maksim<PERSON> reģistrāciju skaits. Vietu vairs nav.", "app.components.EventAttendanceButton.register": "Reģistrēties", "app.components.EventAttendanceButton.registered": "Reģistrēts", "app.components.EventAttendanceButton.seeYouThere": "<PERSON>z t<PERSON>šanos!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON>, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "<PERSON><PERSON><PERSON><PERSON> redzams mazāk informācijas par notikumu.", "app.components.EventCard.a11y_moreContentVisible": "<PERSON><PERSON><PERSON><PERSON> redzams vairāk informācijas par notikumu.", "app.components.EventCard.a11y_readMore": "Vairāk par pasākumu \"{eventTitle}\".", "app.components.EventCard.endsAt": "<PERSON><PERSON><PERSON>", "app.components.EventCard.readMore": "<PERSON><PERSON><PERSON>", "app.components.EventCard.showLess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.EventCard.showMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.EventCard.startsAt": "<PERSON><PERSON><PERSON>", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Gaidāmie un notiekošie pasākumi saistībā ar šo projektu", "app.components.EventPreviews.eventPreviewTimelineTitle3": "<PERSON><PERSON><PERSON> posmā gaidāmie un notiekošie pasākumi", "app.components.FileUploader.a11y_file": "Fails:", "app.components.FileUploader.a11y_filesToBeUploaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Nav pievienoti faili.", "app.components.FileUploader.a11y_removeFile": "<PERSON><PERSON><PERSON><PERSON>u", "app.components.FileUploader.fileInputDescription": "Noklikšķiniet, lai atlasītu failu", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne vairāk kā 50 MB)", "app.components.FileUploader.file_too_large2": "<PERSON><PERSON><PERSON>, kas <PERSON> par {maxSizeMb}MB, nav atļauti.", "app.components.FileUploader.incorrect_extension": "{fileName} m<PERSON><PERSON> si<PERSON>, tas netiks aug<PERSON>.", "app.components.FilterBoxes.a11y_allFilterSelected": "Izvēlētais statusa filtrs: visi", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# iesnie<PERSON><PERSON><PERSON>} other {# iesniegumi}}", "app.components.FilterBoxes.a11y_removeFilter": "Noņemt filtru", "app.components.FilterBoxes.a11y_selectedFilter": "Izv<PERSON><PERSON>ētais statusa filtrs: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON>ēts {numberOfSelectedTopics, plural, =0 {zero tag filters} one {one tag filter} other {# tag filters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "<PERSON><PERSON>", "app.components.FilterBoxes.areas": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc a<PERSON>", "app.components.FilterBoxes.inputs": "ievades", "app.components.FilterBoxes.noValuesFound": "Vērtības nav pieejamas.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FilterBoxes.showTagsWithNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus ({numberTags})", "app.components.FilterBoxes.statusTitle": "Statuss", "app.components.FilterBoxes.topicsTitle": "Tagi", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Mapes apraksts:", "app.components.FolderFolderCard.a11y_folderTitle": "Mapes nosaukums:", "app.components.FolderFolderCard.archived": "Arhivēts", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projekti} one {# projekts} other {# projekti}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "<PERSON><PERSON> tipu ne<PERSON>, tik<PERSON><PERSON><PERSON><PERSON> ir iesniegti piete<PERSON>.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tips", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automātisk<PERSON> sagla<PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "<PERSON><PERSON><PERSON>, at<PERSON>ot veidlap<PERSON> redaktoru, ir iesp<PERSON>jota automātiskā saglab<PERSON>na. <PERSON><PERSON>, kad aizverat lauka iestat<PERSON><PERSON><PERSON>i, i<PERSON><PERSON><PERSON><PERSON> pogu \"X\", automātiski tiek veikta saglab<PERSON>.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Ce<PERSON><PERSON><PERSON>ļ<PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON> gadu", "app.components.GanttChart.timeRange.year": "Gads", "app.components.GanttChart.today": "Šodien", "app.components.GoBackButton.group.edit.goBack": "Atgriezties", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Atgriezties uz iepriekšējo lapu", "app.components.HookForm.Feedback.errorTitle": "<PERSON>r rad<PERSON><PERSON> problēma", "app.components.HookForm.Feedback.submissionError": "Mēģiniet vēlreiz. <PERSON>a problē<PERSON>, sazinieties ar mums", "app.components.HookForm.Feedback.submissionErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, problēma bija mūsu pusē", "app.components.HookForm.Feedback.successMessage": "Veidlapa veiksmīgi iesniegta", "app.components.HookForm.PasswordInput.passwordLabel": "Parole", "app.components.HorizontalScroll.scrollLeftLabel": "Ritiniet pa kreisi.", "app.components.HorizontalScroll.scrollRightLabel": "Ritiniet pa labi.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} idejas ir i<PERSON>.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.popular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> patika", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortBy": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Šķirošana mainīta uz: {currentSortType}", "app.components.IdeaCards.filters.trending": "Tendences", "app.components.IdeaCards.showMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.a11y_hideIdeaCard": "Paslēpt ideju karti.", "app.components.IdeasMap.a11y_mapTitle": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.clickOnMapToAdd": "Klikšķiniet uz kartes, lai pievienotu savu ieguldījumu", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Kā administrators varat noklikšķināt uz kartes, lai pievie<PERSON>u savu i<PERSON>, pat ja šis posms nav aktīvs.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "<PERSON><PERSON><PERSON><PERSON> ievadi š<PERSON> viet<PERSON>", "app.components.IdeasMap.noFilteredResults": "<PERSON><PERSON><PERSON> atlas<PERSON>tie filtri nedeva nekādus rezultātus", "app.components.IdeasMap.noResults": "Re<PERSON><PERSON><PERSON><PERSON> nav atrasti", "app.components.IdeasMap.or": "vai", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, nav nepatīk.} one {1 nepatīk.} other {, # nepatika.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, nav patīk.} one {, 1 patīk.} other {, # patīk.}}", "app.components.IdeasMap.signInLinkText": "pieraks<PERSON>ī<PERSON>", "app.components.IdeasMap.signUpLinkText": "reģistrēties", "app.components.IdeasMap.submitIdea2": "<PERSON><PERSON><PERSON><PERSON> ievades datus", "app.components.IdeasMap.tapOnMapToAdd": "Piesitiet uz kartes, lai pievienotu savu ieguldījumu", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Kā administrators varat pieskar<PERSON> kartei, lai pievie<PERSON>u savu i<PERSON>, pat ja šis posms nav aktīvs.", "app.components.IdeasMap.userInputs2": "Dalībnieku ieguldījums", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, nav komentāru} one {, 1 komentārs} other {, # komentāri}}", "app.components.IdeasShow.bodyTitle": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.editPost": "Rediģēt", "app.components.IdeasShow.goBack": "Atgriezties", "app.components.IdeasShow.moreOptions": "Vairāk i<PERSON>ēju", "app.components.IdeasShow.or": "vai", "app.components.IdeasShow.proposedBudgetTitle": "Ierosinā<PERSON>s b<PERSON>", "app.components.IdeasShow.reportAsSpam": "<PERSON><PERSON><PERSON>ot kā par surogātpastu", "app.components.IdeasShow.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON><PERSON><PERSON>, es to i<PERSON><PERSON><PERSON><PERSON><PERSON> vēl<PERSON>k", "app.components.IdeasShowPage.signIn2": "Piesakieties", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> lapa jums nav pieejama. <PERSON> tai piek<PERSON>, jums var būt nepieciešams pierakstīties vai reģistrēties.", "app.components.LocationInput.noOptions": "Iespēju nav", "app.components.Modal.closeWindow": "<PERSON><PERSON><PERSON><PERSON><PERSON> logu", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Ievadiet jaunu e-pasta adresi", "app.components.PageNotFound.goBackToHomePage": "Atpakaļ uz mājaslapu", "app.components.PageNotFound.notFoundTitle": "Lapa nav atrasta", "app.components.PageNotFound.pageNotFoundDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lapa nav atrasta.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Nodrošiniet saturu vismaz vienā valodā", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON> (ne vairāk kā 50 MB)", "app.components.PagesForm.fileUploadLabelTooltip": "<PERSON><PERSON><PERSON> nedr<PERSON><PERSON><PERSON> b<PERSON><PERSON> par 50 Mb. Pievienotie faili tiks parādīti šīs lapas apak<PERSON>.", "app.components.PagesForm.navbarItemTitle": "Nosaukums navigācijas joslā", "app.components.PagesForm.pageTitle": "Nosa<PERSON>ms", "app.components.PagesForm.savePage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lapu", "app.components.PagesForm.saveSuccess": "<PERSON><PERSON> ve<PERSON> sag<PERSON>.", "app.components.PagesForm.titleMissingOneLanguageError": "Norādiet nosaukumu vismaz vienā valodā", "app.components.Pagination.back": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "app.components.Pagination.next": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapa", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> {votesCast}, kas pā<PERSON> {votesLimit}note<PERSON>to limitu. <PERSON><PERSON><PERSON><PERSON>, izņ<PERSON><PERSON> dažas preces no sava groza un mēģiniet vēlreiz.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} pa kreisi", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Pirms varat iesniegt savu g<PERSON>zu, jums ir jāiztē<PERSON><PERSON> vismaz {votesMinimum} .", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Pirms iesniegšanas ir jāizvēlas vismaz viena iespēja.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Pirms nosūtīšanas grozā ir kaut kas jā<PERSON>.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {nav atlikuši kredīti} other {# no {totalNumberOfVotes, plural, one {1 kredīts} other {# kredīti}} atlikuši}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {nav atlikuši punkti} other {# no {totalNumberOfVotes, plural, one {1 punkts} other {# punkti}} atlicis}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {žetonu vairs nav} other {# no {totalNumberOfVotes, plural, one {1 žetons} other {# žetonu}} atlicis}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Nav atlikušo balsu} other {# no {totalNumberOfVotes, plural, one {1 balss} other {# balsis}} atlikušo}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# balsis} one {# balsot} other {# balsis}} nodot", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "<PERSON><PERSON><PERSON> {votesCast} balsis, kas pā<PERSON> {votesLimit}note<PERSON>to limitu. <PERSON><PERSON><PERSON><PERSON>, izņem<PERSON> dažas balsis un mēģiniet vēlreiz.", "app.components.ParticipationCTABars.addInput": "<PERSON><PERSON><PERSON> ievades datus", "app.components.ParticipationCTABars.allocateBudget": "Piešķiriet savu budžetu", "app.components.ParticipationCTABars.budgetSubmitSuccess": "<PERSON><PERSON><PERSON> b<PERSON> ir veiksmīgi iesniegt<PERSON>.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Atvērts dalībai", "app.components.ParticipationCTABars.poll": "Piedalieties aptaujā", "app.components.ParticipationCTABars.reviewDocument": "Dokumenta pārskatīšana", "app.components.ParticipationCTABars.seeContributions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeEvents3": "<PERSON><PERSON><PERSON><PERSON> not<PERSON>", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON><PERSON><PERSON> id<PERSON>", "app.components.ParticipationCTABars.seeInitiatives": "<PERSON><PERSON><PERSON><PERSON> inici<PERSON>", "app.components.ParticipationCTABars.seeIssues": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeProjects": "<PERSON><PERSON><PERSON><PERSON> projektus", "app.components.ParticipationCTABars.seeProposals": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeQuestions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.takeTheSurvey": "Aizpildiet aptauju", "app.components.ParticipationCTABars.userHasParticipated": "<PERSON><PERSON><PERSON> esat piedalījies šajā projektā.", "app.components.ParticipationCTABars.viewInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON> ievades datus", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "balsot", "app.components.ParticipationCTABars.votesCounter.votes": "balsis", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength1Password": "Slikts paroles stiprums", "app.components.PasswordInput.a11y_strength2Password": "<PERSON><PERSON><PERSON>š paroles stiprums", "app.components.PasswordInput.a11y_strength3Password": "Vid<PERSON>js paroles stiprums", "app.components.PasswordInput.a11y_strength4Password": "Spēcīgs paroles stiprums", "app.components.PasswordInput.a11y_strength5Password": "Ļoti spēcīgs paroles stiprums", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> (min. {minimumPasswordLength} characters)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON><PERSON><PERSON><PERSON>, kas ir vismaz {minimumPasswordLength} r<PERSON><PERSON> z<PERSON> gara", "app.components.PasswordInput.passwordEmptyError": "Ievadiet savu paroli", "app.components.PasswordInput.passwordStrengthTooltip1": "Lai paroli padarītu stiprāku:", "app.components.PasswordInput.passwordStrengthTooltip2": "<PERSON><PERSON><PERSON><PERSON>et mazo un lielo burtu, lielo burtu, cip<PERSON>, speci<PERSON>lo rakstu zīmju un interpunkcijas zīmju kombināciju, kas nav secīgi rakst<PERSON>ti.", "app.components.PasswordInput.passwordStrengthTooltip3": "Izvairieties no bieži sastopamiem vai viegli uzminamiem vārdiem", "app.components.PasswordInput.passwordStrengthTooltip4": "Palieliniet garumu", "app.components.PasswordInput.showPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "app.components.PasswordInput.strength1Password": "Ļoti vāja", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "Vidēja", "app.components.PasswordInput.strength4Password": "St<PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "Ļoti stipra", "app.components.PostCardsComponents.list": "Saraksts", "app.components.PostCardsComponents.map": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.cancel": "Atcelt", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Vai esat p<PERSON>, ka vēlaties dzēst šo of<PERSON><PERSON>?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Rediģēt", "app.components.PostComponents.OfficialFeedback.lastEdition": "<PERSON><PERSON><PERSON><PERSON><PERSON> reizi rediģēts {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kā cilvēki redz jūsu vārdu", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Ofici<PERSON><PERSON><PERSON> at<PERSON>uninājuma autora vārds", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.officialUpdates": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.postedOn": "Publicēts {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publicēt", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "<PERSON><PERSON><PERSON>...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON> pro<PERSON>", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "At<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "<PERSON><PERSON><PERSON> at<PERSON>ms tika veiksmīgi publicēts!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Atbal<PERSON><PERSON>t manu ieguldījumu '{postTitle}' {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu ieguldījumu: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu ieguldījumu: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Atbal<PERSON><PERSON>t manu ideju '{postTitle}' {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu ideju: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu ideju: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Ko jūs domājat par šo priekšlikumu? Balsojiet par to un koplietojiet diskusiju vietnē {postUrl}, lai jūsu viedoklis tiktu sadzirdēts!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Atbal<PERSON><PERSON><PERSON> manu p<PERSON>k<PERSON>umu: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Atbalstiet manu iniciatīvu: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Es publicēju komentāru '{postTitle}' {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "<PERSON>s tikko public<PERSON>ju koment<PERSON>ru: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "<PERSON>s tikko public<PERSON>ju koment<PERSON>ru: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Atbal<PERSON><PERSON>t manu ierosināto iespēju '{postTitle}' {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON>t manu ierosināto iespēju: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu iespēju: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Atbalstiet manu lūgumrakstu '{postTitle}' vietnē {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Atbalstiet manu lūgumrakstu: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Atbalstiet manu lūgumrakstu: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Atbalst<PERSON>t manu projektu '{postTitle}' {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu projektu: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu projektu: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Atbalstiet manu priekšlikumu '{postTitle}' vietnē {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Atbalstiet manu priekšlikumu: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Es tikko ievietoju priekšlikumu {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Pievienoties diskusijai par šo j<PERSON>ājumu '{postTitle}' {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Pievienoties diskusijai: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Pievienoties diskusijai: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Balsot par {postTitle}", "app.components.PostComponents.linkToHomePage": "<PERSON><PERSON> uz <PERSON>ā<PERSON>u", "app.components.PostComponents.readMore": "<PERSON><PERSON><PERSON> v<PERSON>...", "app.components.PostComponents.topics": "<PERSON><PERSON><PERSON>", "app.components.ProjectArchivedIndicator.archivedProject": "<PERSON><PERSON><PERSON><PERSON><PERSON> jūs vairs nevarat piedalīties šajā projektā, jo tas ir arhivēts", "app.components.ProjectArchivedIndicator.previewProject": "Projekta projekts:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Red<PERSON>s tikai moderatoriem un tiem, kam ir priekšskatījuma saite.", "app.components.ProjectCard.a11y_projectDescription": "Projekta apraksts:", "app.components.ProjectCard.a11y_projectTitle": "Projekta nosaukums:", "app.components.ProjectCard.addYourOption": "Pievienojiet savu iespēju", "app.components.ProjectCard.allocateYourBudget": "Piešķiriet savu budžetu", "app.components.ProjectCard.archived": "Arhivēts", "app.components.ProjectCard.comment": "Komentēt", "app.components.ProjectCard.contributeYourInput": "Sniedziet savu ieguldījumu", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Pievienoties diskusijai", "app.components.ProjectCard.learnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.reaction": "Reakcija", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON> ziņojumu", "app.components.ProjectCard.reviewDocument": "Dokumenta pārskatīšana", "app.components.ProjectCard.submitAnIssue": "<PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "app.components.ProjectCard.submitYourIdea": "<PERSON><PERSON><PERSON><PERSON> savu ideju", "app.components.ProjectCard.submitYourInitiative": "Iesniedziet savu iniciatīvu", "app.components.ProjectCard.submitYourPetition": "Iesniedziet savu lūgumrakstu", "app.components.ProjectCard.submitYourProject": "<PERSON><PERSON><PERSON>gt savu projektu", "app.components.ProjectCard.submitYourProposal": "Iesniedziet savu priekšlikumu", "app.components.ProjectCard.takeThePoll": "Veikt aptauju", "app.components.ProjectCard.takeTheSurvey": "Aizpildiet aptauju", "app.components.ProjectCard.viewTheContributions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON><PERSON><PERSON> id<PERSON>", "app.components.ProjectCard.viewTheInitiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iniciat<PERSON>", "app.components.ProjectCard.viewTheIssues": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.ProjectCard.viewTheOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewThePetitions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "<PERSON><PERSON><PERSON><PERSON> projektus", "app.components.ProjectCard.viewTheProposals": "Pārskatīt priekšlikumus", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.vote": "Balsojums", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# komentārs} other {# komentāri}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# ieguldījums} other {# ieguldījumi}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {vēl nav ideju} one {# ideja} other {# idejas}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# iniciatīvas} one {# iniciatīva} other {# iniciatīvas}}# iniciatīvas ~ ~", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# komentārs} other {# komentāri}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# iespēja} other {# iespējas}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petīcijas} one {# petīcija} other {# petīcijas}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projekts} other {# projekti}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# priek<PERSON><PERSON>umi} one {# priekšlikums} other {# priek<PERSON>likumi}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# jautājums} other {# jautājumi}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# komentāri} one {# komentāri} other {# komentāri}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# ievadi} one {# input} other {# ievadi}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projekti} one {# projekts} other {# projekti}}", "app.components.ProjectFolderCards.components.Topbar.all": "<PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.archived": "Arhivēts", "app.components.ProjectFolderCards.components.Topbar.draft": "Melnraksts", "app.components.ProjectFolderCards.components.Topbar.filterBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc", "app.components.ProjectFolderCards.components.Topbar.published2": "Publicēts", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tags", "app.components.ProjectFolderCards.noProjectYet": "Pašlaik nav atvērtu projektu", "app.components.ProjectFolderCards.noProjectsAvailable": "Projekti nav pieejami", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.stayTuned": "Atgriezties vē<PERSON><PERSON><PERSON>, lai uzzinātu par jaunām iesaistīšanās iespējām", "app.components.ProjectFolderCards.tryChangingFilters": "Mēģiniet nomainīt atlas<PERSON>tos filtrus.", "app.components.ProjectTemplatePreview.alsoUsedIn": "<PERSON><PERSON><PERSON> a<PERSON> p<PERSON>:", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.alignCenter": "Centr<PERSON><PERSON> te<PERSON>", "app.components.QuillEditor.alignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa kreisi", "app.components.QuillEditor.alignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "app.components.QuillEditor.bold": "Treknd<PERSON><PERSON>", "app.components.QuillEditor.clean": "Noņemt formatējumu", "app.components.QuillEditor.customLink": "<PERSON><PERSON><PERSON> pogu", "app.components.QuillEditor.customLinkPrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.components.QuillEditor.edit": "Rediģēt", "app.components.QuillEditor.image": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "app.components.QuillEditor.imageAltPlaceholder": "Īss attēla apraksts", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.linkPrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "app.components.QuillEditor.normalText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.orderedList": "Sarin<PERSON><PERSON>s sarak<PERSON>", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.subtitle": "Apakšvirsraksts", "app.components.QuillEditor.title": "Nosa<PERSON>ms", "app.components.QuillEditor.unorderedList": "Nesakārtots saraksts", "app.components.QuillEditor.video": "Pievienot video", "app.components.QuillEditor.videoPrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> video:", "app.components.QuillEditor.visitPrompt": "Apmekl<PERSON><PERSON> sa<PERSON>:", "app.components.ReactionControl.completeProfileToReact": "Aizpildiet savu profilu, lai reaģētu", "app.components.ReactionControl.dislike": "Nepatīk", "app.components.ReactionControl.dislikingDisabledMaxReached": "<PERSON><PERSON><PERSON> esat sasniedzis maks<PERSON><PERSON> s<PERSON>tu vietnē {projectName}.", "app.components.ReactionControl.like": "T<PERSON><PERSON><PERSON> kā", "app.components.ReactionControl.likingDisabledMaxReached": "<PERSON><PERSON><PERSON> esat sasniedzis ma<PERSON><PERSON><PERSON> \"patīk\" skaitu vietnē {projectName}.", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reaģēšana tiks i<PERSON>, tik<PERSON><PERSON><PERSON><PERSON> sāks<PERSON> šis posms.", "app.components.ReactionControl.reactingDisabledPhaseOver": "<PERSON><PERSON><PERSON> fāzē vairs nav iespējams reaģēt.", "app.components.ReactionControl.reactingDisabledProjectInactive": "<PERSON><PERSON><PERSON> vairs nevarat reaģēt uz idejām vietnē {projectName}.", "app.components.ReactionControl.reactingNotEnabled": "Reaģēšana pašlaik nav iespējota šim projektam.", "app.components.ReactionControl.reactingNotPermitted": "Reaģēšana ir iespējota tikai noteiktām grupām", "app.components.ReactionControl.reactingNotSignedIn": "Pierakstīties, lai reaģētu.", "app.components.ReactionControl.reactingPossibleLater": "Reaģēšana sāksies vietnē {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "<PERSON> varētu reaģēt, pārbaudiet savu identitāti.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Pasākuma datums: {startDate} vietnē {startTime} līdz {endDate} vietnē {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Pasākuma datums: {eventDate} no {startTime} līdz {endTime}.", "app.components.Sharing.linkCopied": "<PERSON><PERSON>", "app.components.Sharing.or": "vai", "app.components.Sharing.share": "Koplietot", "app.components.Sharing.shareByEmail": "Koplietot e-pastā", "app.components.Sharing.shareByLink": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Sharing.shareOnFacebook": "Koplietot vietnē Facebook", "app.components.Sharing.shareOnTwitter": "Koplietot vietnē Twitter", "app.components.Sharing.shareThisEvent": "Kopīgojiet šo notikumu", "app.components.Sharing.shareThisFolder": "Koplietot", "app.components.Sharing.shareThisProject": "Koplietot šo projektu", "app.components.Sharing.shareViaMessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Messenger", "app.components.Sharing.shareViaWhatsApp": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>zman<PERSON>jot WhatsApp", "app.components.SideModal.closeButtonAria": "Aizvērt", "app.components.StatusModule.futurePhase": "<PERSON><PERSON><PERSON> s<PERSON> fāzi, kas vēl nav sākusies. <PERSON><PERSON><PERSON> varē<PERSON>t piedal<PERSON>, kad posms sāksies.", "app.components.StatusModule.modifyYourSubmission1": "G<PERSON>ziet savu iesniegumu", "app.components.StatusModule.submittedUntil3": "<PERSON><PERSON><PERSON> b<PERSON> var iesniegt lī<PERSON>z", "app.components.TopicsPicker.numberOfSelectedTopics": "<PERSON>z<PERSON><PERSON><PERSON>ēts {numberOfSelectedTopics, plural, =0 {zero tags} one {one tag} other {# tags}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.moreOptions": "Vairāk i<PERSON>ēju", "app.components.UI.MoreActionsMenu.showMoreActions": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> darb<PERSON>", "app.components.UI.PhaseFilter.noAppropriatePhases": "<PERSON><PERSON> projektam nav atrasti piemēroti posmi", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Oriģināls", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "<PERSON> varētu piedal<PERSON>, ir ne<PERSON><PERSON><PERSON><PERSON> papildu informāci<PERSON>.", "app.components.Unauthorized.completeProfile": "Pilns profils", "app.components.Unauthorized.completeProfileTitle": "Aizpildiet savu profilu, lai piedal<PERSON>", "app.components.Unauthorized.noPermission": "Jums nav tiesību a<PERSON>t šo lapu", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON><PERSON>, neesat autorizēts piekļūt šai lapai.", "app.components.Upload.errorImageMaxSizeExceeded": "<PERSON><PERSON><PERSON> iz<PERSON><PERSON><PERSON><PERSON><PERSON> attēls ir lielāks par {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Viens vai vairāki jūsu atlasītie attēli ir lielāki par {maxFileSize}MB", "app.components.Upload.onlyOneImage": "<PERSON><PERSON><PERSON> varat augšupielādēt tikai 1 attēlu", "app.components.Upload.onlyXImages": "<PERSON><PERSON><PERSON> varat augšupielādēt tikai {maxItemsCount} attēlus", "app.components.Upload.remaining": "atliek", "app.components.Upload.uploadImageLabel": "Iz<PERSON><PERSON><PERSON>ē<PERSON> attēlu (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Izvēlieties vienu vai vairākus attēlus", "app.components.UpsellTooltip.tooltipContent": "<PERSON><PERSON> funkcija nav iekļauta jūsu pašreizējā plānā. Lai to atbloķētu, sazinieties ar savu valdības veiksmes menedžeri vai administratoru.", "app.components.UserName.anonymous": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UserName.anonymousTooltip2": "<PERSON><PERSON> lietotājs ir nolēmis anonimizēt savu ieguldījumu", "app.components.UserName.authorWithNoNameTooltip": "<PERSON><PERSON><PERSON> vārds ir automātiski ģenerēts, jo neesat ievadījis savu vārdu. <PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON><PERSON>t savu profilu, ja vēlaties to main<PERSON><PERSON>.", "app.components.UserName.deletedUser": "nezināms autors", "app.components.UserName.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.VerificationModal.verifyAuth0": "Pārbaudiet ar NemID", "app.components.VerificationModal.verifyBOSA": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, izmantojot itsme vai eID", "app.components.VerificationModal.verifyBosaFas": "Pārbaud<PERSON>, izmantojot itsme vai eID", "app.components.VerificationModal.verifyClaveUnica": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Verificēšana ar viltotu SSO", "app.components.VerificationModal.verifyIdAustria": "Pārbaudiet ar ID Austrija", "app.components.VerificationModal.verifyKeycloak": "Pārbaudiet ar ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Pārbaudiet ar MitID", "app.components.VerificationModal.verifyTwoday2": "Verifikācija ar BankID vai Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Pārbaudiet savu identitāti", "app.components.VoteControl.budgetingFutureEnabled": "<PERSON><PERSON><PERSON> varat piešķirt savu budžetu, sākot no {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Pašlaik līdzdalības budžeta veidošana nav iespējota.", "app.components.VoteControl.budgetingNotPossible": "Pašlaik veikt izmaiņas jūsu budžetā nav iespējams.", "app.components.VoteControl.budgetingNotVerified": "<PERSON><PERSON><PERSON><PERSON>, {verifyAccountLink}, lai tur<PERSON><PERSON>.", "app.components.VoteInputs._shared.currencyLeft1": "Jums ir palicis {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Jums ir {votesLeft, plural, =0 {nav atlikuši} other {# no {totalNumberOfVotes, plural, one {1 kredīts} other {# kredīti}} atlikuši}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Jums ir {votesLeft, plural, =0 {nav atlikuši} other {# no {totalNumberOfVotes, plural, one {1 punkts} other {# punkti}} atlikuši}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Jums ir {votesLeft, plural, =0 {nav atlikuši} other {# no {totalNumberOfVotes, plural, one {1 žetons} other {# žetoni}} atlikuši}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Jums ir {votesLeft, plural, =0 {nav atlikušas} other {# no {totalNumberOfVotes, plural, one {1 balss} other {# balsis}} atlik<PERSON><PERSON><PERSON>}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "<PERSON><PERSON><PERSON> jau esat iesniedzis savu budžetu. Lai to <PERSON><PERSON><PERSON>, noklikšķiniet uz \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "<PERSON><PERSON><PERSON> jau esat iesniedzis savu budžetu. Lai to <PERSON><PERSON><PERSON>, atgriezieties projekta lapā un noklikšķiniet uz \"Main<PERSON><PERSON> iesniegumu\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budžets nav pieejams, jo šis posms nav aktīvs.", "app.components.VoteInputs.single.youHaveVotedForX2": "<PERSON><PERSON><PERSON> par {votes, plural, =0 {# iespējas} one {# opcija} other {# iespējas}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON><PERSON>, ka tiks zaudēti visi ar šo ievades veidu saistītie dati, <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON>, reak<PERSON>jas un balsojumi. <PERSON><PERSON> darbību nevar atcelt.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Vai esat pā<PERSON>, ka vēlaties dzēst šo ievades failu?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Atcelt", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Apstipriniet", "app.components.admin.SlugInput.resultingURL": "Rezultātā iegūtais URL", "app.components.admin.SlugInput.slugTooltip": "Slugs ir unikāls vārdu kopums lapas tīmekļa adreses jeb URL adreses beig<PERSON>s.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> URL, sa<PERSON> u<PERSON>, i<PERSON><PERSON><PERSON><PERSON> iepriekšējo URL, vairs ne<PERSON>.", "app.components.admin.SlugInput.urlSlugLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.addCondition": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_email": "E-pasts", "app.components.admin.UserFilterConditions.field_event_attendance": "Pasākumu reģistrācija", "app.components.admin.UserFilterConditions.field_follow": "Sekojiet", "app.components.admin.UserFilterConditions.field_lives_in": "Dzīvo", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Kopienas monitoringa apsekojums", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar ieguldījumu ar statusu", "app.components.admin.UserFilterConditions.field_participated_in_project": "Ieguldīts projektā", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Publicēts kaut kas ar tagu", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Reģistrācijas datums", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Verifikācija", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideju i<PERSON>strā<PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Priekšlikumi", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "nav reģistrēts nevienam no šiem pasākumiem.", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "nav reģistrēts nevienam pasākumam.", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "ir reģistrējies kādam no šiem pasākumiem.", "app.components.admin.UserFilterConditions.predicate_attends_something": "ir reģistrējies vismaz vienam pasākumam.", "app.components.admin.UserFilterConditions.predicate_begins_with": "s<PERSON><PERSON> ar", "app.components.admin.UserFilterConditions.predicate_commented_in": "komentēja", "app.components.admin.UserFilterConditions.predicate_contains": "satur", "app.components.admin.UserFilterConditions.predicate_ends_on": "be<PERSON><PERSON> ar", "app.components.admin.UserFilterConditions.predicate_has_value": "ir v<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_in": "veica jeb<PERSON> da<PERSON>", "app.components.admin.UserFilterConditions.predicate_is": "ir", "app.components.admin.UserFilterConditions.predicate_is_admin": "ir administrators", "app.components.admin.UserFilterConditions.predicate_is_after": "ir pēc", "app.components.admin.UserFilterConditions.predicate_is_before": "ir pirms", "app.components.admin.UserFilterConditions.predicate_is_checked": "ir <PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_empty": "ir tuk<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_equal": "ir", "app.components.admin.UserFilterConditions.predicate_is_exactly": "ir tie<PERSON>i", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "ir liel<PERSON><PERSON> par", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "ir liel<PERSON><PERSON> vai vienāds ar", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "ir parasts lietot<PERSON>js", "app.components.admin.UserFilterConditions.predicate_is_not_area": "neietver teritoriju", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "izslēdz mapi", "app.components.admin.UserFilterConditions.predicate_is_not_input": "ne<PERSON>ver ievades datus", "app.components.admin.UserFilterConditions.predicate_is_not_project": "neietver projektu", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "neattiecas uz tematu", "app.components.admin.UserFilterConditions.predicate_is_one_of": "ir viens no", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "viena no jomām", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "viena no mapēm", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "viens no ievades elementiem", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "viens no projektiem", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "viena no tēmām", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "ir projektu v<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "ir maz<PERSON><PERSON> par", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "ir maz<PERSON>ks vai vienāds ar", "app.components.admin.UserFilterConditions.predicate_is_verified": "ir a<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ne<PERSON><PERSON><PERSON> ar", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "nekomentēja", "app.components.admin.UserFilterConditions.predicate_not_contains": "nesatur", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "ne<PERSON><PERSON><PERSON> ar", "app.components.admin.UserFilterConditions.predicate_not_has_value": "nav v<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_in": "ne<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is": "nav", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "nav administrators", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "nav p<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "nav tukšs", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "nav", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nav parasts lietotājs", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "nav viens no", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "nav projektu vad<PERSON>s", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "nav apstiprināts", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "nepublicēja ieguldījumu", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "nereaģēja uz komentāriem", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "nereaģēja uz ievades", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "nav reģistrējies pasākumam", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "nav veikusi apta<PERSON>ju", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> br<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_nothing": "nekas", "app.components.admin.UserFilterConditions.predicate_posted_input": "publicēja ieguldījumu", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reaģēja uz komentāru", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reaģēja uz ievadī<PERSON><PERSON>em datiem", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "reģistrējies pasākumam", "app.components.admin.UserFilterConditions.predicate_something": "kaut kas", "app.components.admin.UserFilterConditions.predicate_taken_survey": "ir veikusi a<PERSON>", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "pie<PERSON><PERSON><PERSON><PERSON> brī<PERSON>", "app.components.admin.UserFilterConditions.predicate_voted_in3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "<PERSON><PERSON><PERSON> paziņojumus par savu ieguldījumu", "app.components.anonymousParticipationModal.cancel": "Atcelt", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "Piedalieties anonīmi", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON> <b>pasl<PERSON><PERSON> jūsu profilu</b> no administratoriem, projektu vadītājiem un citiem šī konkrētā ieguldījuma iedzīvotājiem, lai neviens nevarētu saistīt šo ieguldījumu ar jums. Anonīmus ieguldījumus nevar rediģēt, un tie tiek uzskatīti par galīgiem.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "<PERSON><PERSON><PERSON><PERSON> <PERSON>as nodrošināšana ikvienam lietotājam ir mūsu galvenā prioritāte. <PERSON><PERSON><PERSON> ir svarī<PERSON>, t<PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON>, es<PERSON> la<PERSON>ni cits pret citu.", "app.components.avatar.titleForAccessibility": "{fullName}profils", "app.components.customFields.mapInput.removeAnswer": "Noņemt atbildi", "app.components.customFields.mapInput.undo": "Atcelt", "app.components.customFields.mapInput.undoLastPoint": "<PERSON>cel<PERSON> pēd<PERSON><PERSON>", "app.components.followUnfollow.follow": "Sekojiet", "app.components.followUnfollow.followADiscussion": "Se<PERSON><PERSON><PERSON> l<PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "Pēc tiek aktivizēti e-pasta atjauninājumi par statusa izmaiņām, oficiāliem atjauninājumiem un komentāriem. Jebkurā laikā varat rakstīt uz {unsubscribeLink} .", "app.components.followUnfollow.followTooltipProjects2": "Pēc tiek aktivizēti e-pasta atjauninājumi par projekta izmaiņām. Jebkur<PERSON> laikā varat {unsubscribeLink} .", "app.components.followUnfollow.unFollow": "Atcelt", "app.components.followUnfollow.unsubscribe": "atteikties no abonementa", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "m<PERSON><PERSON> v<PERSON>", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.previous": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, lai meklētu pēc lietotāja e-pasta adreses vai vārda...", "app.components.form.anonymousSurveyMessage2": "Visas š<PERSON><PERSON> a<PERSON>ujā sniegtās atbildes ir anonī<PERSON>.", "app.components.form.backToInputManager": "Atgriezties pie ievades pārvaldnieka", "app.components.form.backToProject": "Atgriezties pie projekta", "app.components.form.components.controls.mapInput.removeAnswer": "Noņemt atbildi", "app.components.form.components.controls.mapInput.undo": "Atcelt", "app.components.form.components.controls.mapInput.undoLastPoint": "<PERSON>cel<PERSON> pēd<PERSON><PERSON>", "app.components.form.controls.addressInputAriaLabel": "<PERSON><PERSON><PERSON>", "app.components.form.controls.addressInputPlaceholder6": "Ievadiet adresi...", "app.components.form.controls.adminFieldTooltip": "Lauks ir redzams tikai <PERSON>m", "app.components.form.controls.allStatementsError": "Jāizvēlas atbilde uz visiem apgalvojumiem.", "app.components.form.controls.back": "Atpakaļ", "app.components.form.controls.clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus", "app.components.form.controls.clearAllScreenreader": "Notīriet visas atbildes no iepriekš minētā matricas jautājuma", "app.components.form.controls.clickOnMapMultipleToAdd3": "Noklikšķiniet uz kartes, lai z<PERSON>. Pēc tam velciet punktus, lai tos p<PERSON>.", "app.components.form.controls.clickOnMapToAddOrType": "<PERSON>, noklikšķiniet uz kartes vai ierakstiet adresi z<PERSON>.", "app.components.form.controls.confirm": "Apstipriniet", "app.components.form.controls.cosponsorsPlaceholder": "Sā<PERSON>t iera<PERSON>, lai veiktu me<PERSON>", "app.components.form.controls.currentRank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rangs:", "app.components.form.controls.minimumCoordinates2": "Nepieciešams vismaz {numPoints} kartes punktu.", "app.components.form.controls.noRankSelected": "Nav izv<PERSON><PERSON><PERSON><PERSON> rangs", "app.components.form.controls.notPublic1": "*<PERSON><PERSON> atbilde tiks izpausta tikai projektu vad<PERSON>, bet ne sabiedrībai.", "app.components.form.controls.optionalParentheses": "(pēc izv<PERSON>)", "app.components.form.controls.rankingInstructions": "Velciet un nometiet, lai ierindotu opcijas.", "app.components.form.controls.selectAsManyAsYouLike": "*Izvēlieties tik daudz, cik vēlaties", "app.components.form.controls.selectBetween": "*Izvēlieties starp {minItems} un {maxItems} iespējām", "app.components.form.controls.selectExactly2": "*Izvēlēties tieši {selectExactly, plural, one {# opcija} other {# opcijas}}", "app.components.form.controls.selectMany": "*Izvēlieties, cik vēlaties", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Pie<PERSON><PERSON><PERSON> kartei, lai <PERSON>. <PERSON> ve<PERSON>, lai to<PERSON>.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Pieskar<PERSON>ies kartei, lai <PERSON>.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Pieskar<PERSON>ies kartei, lai pievie<PERSON>u atbildi.", "app.components.form.controls.tapOnMapToAddOrType": "Pieskarieties kartei vai ierakstiet adresi, lai pievienotu atbildi.", "app.components.form.controls.tapToAddALine": "Pieskar<PERSON><PERSON>, lai pievienotu līniju", "app.components.form.controls.tapToAddAPoint": "Pieskarieties, lai pievienotu punktu", "app.components.form.controls.tapToAddAnArea": "Pie<PERSON><PERSON><PERSON>, lai pievienotu apgabalu", "app.components.form.controls.uploadShapefileInstructions": "* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zip failu, kas satur vienu vai vairākus shape failus.", "app.components.form.controls.validCordinatesTooltip2": "<PERSON>a atra<PERSON>n<PERSON>s vieta netiek parādīta starp opcij<PERSON>m, ieva<PERSON><PERSON> te<PERSON>, varat pievienot derīgas koordinātas formātā \"platums, garums\", lai norād<PERSON><PERSON> precīzu atrašan<PERSON> vietu (piemēram: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} no {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} no {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} no {total}, kur {maxValue} ir {maxLabel}.", "app.components.form.error": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "Nevarēja <PERSON>ēt google maps norādīto at<PERSON> vietas lauku.", "app.components.form.progressBarLabel": "Apsekojuma norise", "app.components.form.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.submitApiError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai nav kļūdu, un mēģiniet vēlreiz.", "app.components.form.verifiedBlocked": "<PERSON><PERSON> la<PERSON> nevar rediģēt, jo tajā ir i<PERSON><PERSON><PERSON><PERSON> p<PERSON>.", "app.components.formBuilder.Page": "<PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "paziņojums par pieejamību", "app.components.formBuilder.addAnswer": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.addStatement": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.agree": "Piek<PERSON><PERSON><PERSON>", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "Ja jums ir piekļuve mūsu mākslīgā intelekta paketei, jūs varēsiet apkopot un kategorizēt teksta atbildes, izmantojot mākslīgo intelektu.", "app.components.formBuilder.askFollowUpToggleLabel": "Jautājiet par turpm<PERSON><PERSON> r<PERSON>", "app.components.formBuilder.bad": "Slikts", "app.components.formBuilder.buttonLabel": "Pogas etiķete", "app.components.formBuilder.buttonLink": "Pogas saite", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Atcelt", "app.components.formBuilder.category": "Kategorija", "app.components.formBuilder.chooseMany": "Izvēlie<PERSON> daudzus", "app.components.formBuilder.chooseOne": "Izvēlieties vienu", "app.components.formBuilder.close": "Aizvērt", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Kon<PERSON>gurēt karti", "app.components.formBuilder.confirmLeaveBuilderButtonText": "Jā, es gribu aizbraukt", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "Turpina", "app.components.formBuilder.cosponsors": "L<PERSON><PERSON>zfinansētā<PERSON>", "app.components.formBuilder.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.defaultContent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> saturs", "app.components.formBuilder.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.deleteButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.description": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Tas jau ir pievienots veid<PERSON>. Noklusējuma saturu var izman<PERSON>t tikai vienu reizi.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Pielāgota satura pievienošana nav daļa no jūsu pašreizējās licences. Sazinieties ar savu GovSuc<PERSON> men<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON> par to.", "app.components.formBuilder.disagree": "Nepiekrītu", "app.components.formBuilder.displayAsDropdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.formBuilder.displayAsDropdownTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> logā parādiet opcijas. Ja jums ir daudz iespēju, ieteicams izmantot šo iespēju.", "app.components.formBuilder.done": "Paveikts", "app.components.formBuilder.drawArea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.components.formBuilder.drawRoute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.dropPin": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.editButtonLabel": "Rediģēt", "app.components.formBuilder.emptyImageOptionError": "Sniedziet vismaz 1 atbildi. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, ka katrai atbildei ir jāb<PERSON>t no<PERSON>m.", "app.components.formBuilder.emptyOptionError": "Sniedziet vismaz 1 atbildi", "app.components.formBuilder.emptyStatementError": "Sniedziet vismaz 1 paziņojumu", "app.components.formBuilder.emptyTitleError": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON> v<PERSON>", "app.components.formBuilder.emptyTitleMessage": "Norādiet nosaukumu visām atbildēm", "app.components.formBuilder.emptyTitleStatementMessage": "Norādiet visu paziņ<PERSON><PERSON> virsrak<PERSON>u", "app.components.formBuilder.enable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.errorMessage": "<PERSON><PERSON>v problēma, l<PERSON><PERSON><PERSON>, novē<PERSON><PERSON> problēmu, lai varētu saglabāt i<PERSON>.", "app.components.formBuilder.fieldGroup.description": "Apraksts (pēc izvēles)", "app.components.formBuilder.fieldGroup.title": "Nosaukums (pēc izvēles)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "<PERSON><PERSON><PERSON><PERSON> atbildes uz šiem jautājumiem ir pieejamas tikai eksportētajā Excel failā ievades pārvaldniekā, bet nav redzamas lietotājiem.", "app.components.formBuilder.fieldLabel": "Atbildes variantu izvēle", "app.components.formBuilder.fieldLabelStatement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.fileUpload": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "<PERSON><PERSON>, kas balst<PERSON>ta uz karti", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Ievietojiet karti kā kontekstu vai uzdodiet dalībniekiem uz atrašanās vietu balstītus j<PERSON>.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "<PERSON> optim<PERSON><PERSON> lieto<PERSON>, mē<PERSON> <PERSON> pievie<PERSON>, ma<PERSON><PERSON><PERSON><PERSON> vai apgabalu jautājumus uz kartēm balstītām lapām.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "<PERSON><PERSON><PERSON>a", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Apsekojumu kartēšanas funkcijas nav iekļautas jūsu pašreizējā licencē. Sazinieties ar savu GovSuc<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Lapas tips", "app.components.formBuilder.formEnd": "Veidlapas beigas", "app.components.formBuilder.formField.cancelDeleteButtonText": "Atcelt", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON>, dzēst lapu", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.delete": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo lapu, tiks dzēsta arī ar to saistītā loģika. Vai esat pārliecināts, ka vēlaties to dzēst?", "app.components.formBuilder.formField.deleteResultsInfo": "To nevar atcelt", "app.components.formBuilder.goToPageInputLabel": "<PERSON> lapa ir:", "app.components.formBuilder.good": "<PERSON><PERSON>", "app.components.formBuilder.helmetTitle": "<PERSON><PERSON>dl<PERSON><PERSON> ve<PERSON>", "app.components.formBuilder.imageFileUpload": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "app.components.formBuilder.invalidLogicBadgeMessage": "Nederīga loģika", "app.components.formBuilder.labels2": "Etiķetes (pēc izv<PERSON>les)", "app.components.formBuilder.labelsTooltipContent2": "Izvēlieties papildu marķējumus jeb<PERSON>i lineārās skalas vērtībai.", "app.components.formBuilder.lastPage": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.layout": "Izkārtojums", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Vai esat pārl<PERSON>, ka vēlaties doties prom?", "app.components.formBuilder.leaveBuilderText": "Jums ir nesaglabātas izmaiņas. <PERSON><PERSON><PERSON><PERSON>, saglabājiet pirms aiziešanas. <PERSON>a a<PERSON>, jū<PERSON>t savas izmaiņ<PERSON>.", "app.components.formBuilder.limitAnswersTooltip": "Kad tas ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, respondentiem ir j<PERSON><PERSON><PERSON><PERSON><PERSON>, lai turpin<PERSON> darbu.", "app.components.formBuilder.limitNumberAnswers": "Ierobežot atbilžu skaitu", "app.components.formBuilder.linePolygonMapWarning2": "Līniju un daudzstūru zīmējums var neatbilst pieejamības standartiem. Plašāka informācija atrodama vietnē {accessibilityStatement}.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON><PERSON> skala", "app.components.formBuilder.locationDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta", "app.components.formBuilder.logic": "Loģika", "app.components.formBuilder.logicAnyOtherAnswer": "Jebkura cita atbilde", "app.components.formBuilder.logicConflicts.conflictingLogic": "Pretrunīga loģika", "app.components.formBuilder.logicConflicts.interQuestionConflict": "<PERSON><PERSON><PERSON> lap<PERSON> ir j<PERSON><PERSON>, kas ved uz dažād<PERSON>m lapām. Ja dal<PERSON><PERSON><PERSON> atbild uz vairākiem jautājumiem, tiks parād<PERSON>ta vistālā<PERSON><PERSON> lapa. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON><PERSON> rīcība atbilst jūsu paredzētajai plūsmai.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "<PERSON><PERSON><PERSON> lapā ir piemēroti vairāki loģikas noteikumi: vairāku jautājumu izvēles loģika, lapas līmeņa loģika un jautājumu savstarpējā loģika. Ja šie nosacījumi pārklājas, jautājuma loģikai būs priekšroka pār lapas loģiku, un tiks parādīta vistālāk esošā lapa. Pārbaudiet loģiku, lai pārl<PERSON>, ka tā atbilst jūsu paredzētajai plūsmai.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "<PERSON><PERSON><PERSON> lap<PERSON> ir ietverts jautā<PERSON><PERSON> ar vairākiem atbilžu variantiem, kas ved uz dažādām lapām. <PERSON>a dal<PERSON>bnieki izvēlas vairā<PERSON> iespē<PERSON>, tiks parād<PERSON>ta tālākā lapa. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON><PERSON> uzvedība atbilst jūsu paredzētajai plūsmai.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "<PERSON><PERSON><PERSON> lapā ir ietverts jautājums ar vairākiem atbilžu variantiem, kas ved uz dažādām lapām, un tajā ir jautājumi, kas ved uz citām lapām. Ja šie nosacījumi p<PERSON>, tiks parād<PERSON>ta tālā<PERSON>ā lapa. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka šāda uzvedība atbilst jūsu paredzētajai plūsmai.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "<PERSON><PERSON><PERSON> lapā ir ietverts jautājums ar vairākiem atbilžu variantiem, kura atbildes ved uz dažādām lapām, un loģika ir iestatīta gan lapas, gan jautā<PERSON><PERSON> līmenī. Jautājuma loģikai būs prioritāte, un tiks parādīta vistālāk esošā lapa. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON>ī uzvedība atbilst jūsu paredzētajai plūsmai.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "<PERSON><PERSON><PERSON> lapā ir iestatīta loģika gan lapas, gan j<PERSON><PERSON><PERSON><PERSON> lī<PERSON>. Jautājuma loģikai ir prioritāte pār lapas līmeņa loģiku. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON>ī uzvedība atbilst jūsu paredzētajai plūsmai.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "<PERSON><PERSON><PERSON> lapā ir iestatīta loģika gan lapas, gan jaut<PERSON><PERSON><PERSON> līmen<PERSON>, un vairāki jautājumi ir novirzīti uz dažādām lapām. Jautājuma loģikai ir priek<PERSON>roka, un tiek parādīta vistālāk esošā lapa. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON><PERSON> uzvedība atbilst jūsu paredzētajai plūsmai.", "app.components.formBuilder.logicNoAnswer2": "Nav atbildēts", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Ja ir kāda cita atbilde", "app.components.formBuilder.logicPanelNoAnswer": "<PERSON>a atbilde nav sniegta", "app.components.formBuilder.logicValidationError": "Loģika nedrīkst veidot saites uz iepriekšējām lapām", "app.components.formBuilder.longAnswer": "Garā <PERSON>bilde", "app.components.formBuilder.mapConfiguration": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.mapping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.mappingNotInCurrentLicense": "Apsekojumu kartēšanas funkcijas nav iekļautas jūsu pašreizējā licencē. Sazinieties ar savu GovSuc<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "app.components.formBuilder.matrix": "Matrica", "app.components.formBuilder.matrixSettings.columns": "Kolonnas", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoiceHelperText": "Ja vairākas opcijas ved uz dažādām lapām un dalībnieki izvēlas vairāk nekā vienu, tiks parādīta vistālāk esošā lapa. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON>āda uzvedība atbilst plānotajai plūsmai.", "app.components.formBuilder.multipleChoiceImage": "Attēlu izvēle", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.neutral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.newField": "<PERSON><PERSON><PERSON> lauks", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "<PERSON><PERSON>", "app.components.formBuilder.open": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.optional": "Pēc izvēles", "app.components.formBuilder.other": "Citi", "app.components.formBuilder.otherOption": "\"Cita\" opcija", "app.components.formBuilder.otherOptionTooltip": "<PERSON><PERSON> ievadīt pie<PERSON> atbildi, ja piedāv<PERSON>t<PERSON><PERSON> atbildes neatbilst viņu vēlmēm.", "app.components.formBuilder.page": "<PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "<PERSON><PERSON> lapu nevar d<PERSON>.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "<PERSON><PERSON> lapu ne<PERSON>, un tajā nav iespējams pievienot papildu la<PERSON>.", "app.components.formBuilder.pageRuleLabel": "<PERSON><PERSON>ka<PERSON><PERSON> lapa ir:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Ja loģika nav pievienota, veidlapa darbosies saskaņā ar parasto plūsmu. <PERSON>a gan lapā, gan tās jautājumos ir loģika, priek<PERSON><PERSON>a tiks dota jautājumu loģikai. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka tas atbilst jūsu paredz<PERSON>tajai plūsmai Lai iegūtu vairāk informācijas, apmeklējiet vietni {supportPageLink}.", "app.components.formBuilder.preview": "Priekšskatījums:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Līdzautori nav redzami lejupielādētajā PDF failā, un tos nevar importēt, izmantojot FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Lejupielādētajā PDF failā failu augšupielādes jautājumi ir norādīti kā neatbalstīti, un tie netiek atbalstīti importē<PERSON>, izmantojot FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "<PERSON><PERSON><PERSON><PERSON><PERSON> jautājumi ir redzami lejupielādētajā PDF failā, bet slāņi nav redzami. Ka<PERSON><PERSON><PERSON>nas jautājumi netiek atbalstīti importē<PERSON>, izmantojot FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matricas jautāju<PERSON> ir redzami lejupielādētajā PDF failā, taču pašlaik tie netiek atbalstīti importē<PERSON>nai, izmantojot FormSync.", "app.components.formBuilder.printSupportTooltip.page": "<PERSON><PERSON> nosaukumi un apraksti lejupielādētajā PDF failā tiek parādīti kā sadaļas galvene.", "app.components.formBuilder.printSupportTooltip.ranking": "Ranga jautāju<PERSON> ir redzami lejupielādētajā PDF failā, taču pašlaik tie netiek atbalstīti importē<PERSON>nai, izmantojot FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Lejuplādētajā PDF failā ir norādītas kā neatbalstītas, un tās nav atbalstītas importēšanai, izmantojot FormSync.", "app.components.formBuilder.proposedBudget": "Ierosinā<PERSON>s b<PERSON>", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON><PERSON> ne<PERSON>.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON><PERSON><PERSON><PERSON> (pēc izvēles)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.randomize": "Randomizēt", "app.components.formBuilder.randomizeToolTip": "Atbilžu secība katram lietotājam tiks mainīta pēc nejaušības principa.", "app.components.formBuilder.range": "Diapazons", "app.components.formBuilder.ranking": "Reitings", "app.components.formBuilder.rating": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>", "app.components.formBuilder.removeAnswer": "Noņemt atbildi", "app.components.formBuilder.required": "Nepieciešams", "app.components.formBuilder.requiredToggleLabel": "Atbildi uz šo jautājumu uzdodiet kā obligātu", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON>a at<PERSON><PERSON> ir:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "<PERSON>a atbildes ietver:", "app.components.formBuilder.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "Izvēlieties skalas maks<PERSON> v<PERSON>.", "app.components.formBuilder.sentiment": "Noskaņojuma skala", "app.components.formBuilder.shapefileUpload": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> au<PERSON>", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON>", "app.components.formBuilder.showResponseToUsersToggleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> atbildi lietotājiem", "app.components.formBuilder.singleChoice": "Viena izvēle", "app.components.formBuilder.staleDataErrorMessage2": "Ir radusies problēma. <PERSON><PERSON> ievades veidlapa nesen ir saglabāta citur. Tas var būt tāpēc, ka jūs vai cits lietotājs to ir atvēris rediģēšanai citā pārlūkprogrammas logā. <PERSON><PERSON><PERSON><PERSON>, ats<PERSON><PERSON><PERSON><PERSON> lapu, lai iegūtu jaun<PERSON> veid<PERSON>, un pēc tam atkal veiciet izmaiņas.", "app.components.formBuilder.stronglyAgree": "Pilnīgi <PERSON>rī<PERSON>", "app.components.formBuilder.stronglyDisagree": "<PERSON><PERSON><PERSON>gi <PERSON>", "app.components.formBuilder.supportArticleLinkText": "<PERSON>o lapu", "app.components.formBuilder.tags": "Tags", "app.components.formBuilder.title": "Nosa<PERSON>ms", "app.components.formBuilder.toLabel": "uz", "app.components.formBuilder.unsavedChanges": "Jums ir nesaglabātas izmaiņ<PERSON>", "app.components.formBuilder.useCustomButton2": "Pielāgotas lapas pogas <PERSON>", "app.components.formBuilder.veryBad": "Ļoti slikti", "app.components.formBuilder.veryGood": "Ļoti labi", "app.components.ideas.similarIdeas.engageHere": "Iesaistieties šeit", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Līdzīgi iesniegumi nav atrasti.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Mēs atradām līdzīgus zemessargus - iesaistīšanās ar tiem var palīdzēt tos stiprināt!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Līd<PERSON><PERSON>gi iesniegumi jau public<PERSON>ti:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Me<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON> iesniegumus...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON><PERSON><PERSON> par dienu} one {# diena} other {# dienas}} pa kreisi", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  at<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AED": "A<PERSON>vienoto Arābu <PERSON> dirhams", "app.components.screenReaderCurrency.AFN": "Afganist<PERSON><PERSON> afgāņ<PERSON> afgāņu", "app.components.screenReaderCurrency.ALL": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>", "app.components.screenReaderCurrency.AMD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ANG": "Nīderlandes <PERSON> g<PERSON>e", "app.components.screenReaderCurrency.AOA": "Angolas Kwanza", "app.components.screenReaderCurrency.ARS": "Argentīnas peso", "app.components.screenReaderCurrency.AUD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AWG": "A<PERSON>nas florīns", "app.components.screenReaderCurrency.AZN": "Azerbaidž<PERSON><PERSON> manats", "app.components.screenReaderCurrency.BAM": "Bosnija un Hercegovina Convertible Mark", "app.components.screenReaderCurrency.BBD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BDT": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BGN": "Bulgāru valoda Lev", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "Burundi franču franks", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BND": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BOB": "Bolīvijas bolivietis <PERSON>no", "app.components.screenReaderCurrency.BOV": "Bolīvijas Mvdol", "app.components.screenReaderCurrency.BRL": "<PERSON>raz<PERSON><PERSON><PERSON> re<PERSON>", "app.components.screenReaderCurrency.BSD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BTN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BWP": "<PERSON>ts<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Baltkrievijas rublis", "app.components.screenReaderCurrency.BZD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CAD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CDF": "<PERSON><PERSON> franks", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Šveices franks", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Čīles norēķinu vienība (UF)", "app.components.screenReaderCurrency.CLP": "<PERSON><PERSON><PERSON> peso", "app.components.screenReaderCurrency.CNY": "Ķīnas jua<PERSON>i", "app.components.screenReaderCurrency.COP": "Kolumbijas peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Kostarika <PERSON>ón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Ku<PERSON> konvertējamais peso", "app.components.screenReaderCurrency.CUP": "Kubas peso", "app.components.screenReaderCurrency.CVE": "Kaboverdes Escudo", "app.components.screenReaderCurrency.CZK": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.DJF": "Džibutijas franks", "app.components.screenReaderCurrency.DKK": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.DOP": "Dominikānas peso", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "Ēģiptes mā<PERSON>i<PERSON>a", "app.components.screenReaderCurrency.ERN": "Eritrejas Nakfa", "app.components.screenReaderCurrency.ETB": "<PERSON>ti<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.FKP": "Folklenda salu mā<PERSON>a", "app.components.screenReaderCurrency.GBP": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON><PERSON> cedi", "app.components.screenReaderCurrency.GIP": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GMD": "Gambian Dalasi", "app.components.screenReaderCurrency.GNF": "<PERSON><PERSON><PERSON><PERSON> franks", "app.components.screenReaderCurrency.GTQ": "Gvatemalas kecals", "app.components.screenReaderCurrency.GYD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HKD": "Honkongas dolārs", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Horvātijas kunas", "app.components.screenReaderCurrency.HTG": "Haiti Gourde", "app.components.screenReaderCurrency.HUF": "Ung<PERSON><PERSON><PERSON> forints", "app.components.screenReaderCurrency.IDR": "Indonēzijas rūpija", "app.components.screenReaderCurrency.ILS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.INR": "Indijas rūpija", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON><PERSON><PERSON> ri<PERSON>", "app.components.screenReaderCurrency.ISK": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KGS": "Kirgizstānas So<PERSON>", "app.components.screenReaderCurrency.KHR": "Kambodž<PERSON> r<PERSON>", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON><PERSON> salu franks", "app.components.screenReaderCurrency.KPW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "app.components.screenReaderCurrency.KRW": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON><PERSON> din<PERSON>", "app.components.screenReaderCurrency.KYD": "<PERSON><PERSON><PERSON> salu dol<PERSON>", "app.components.screenReaderCurrency.KZT": "Kazahstānas tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LKR": "Šrilankas rūpija", "app.components.screenReaderCurrency.LRD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LSL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LTL": "Lietuvas liti", "app.components.screenReaderCurrency.LVL": "Latvijas lats", "app.components.screenReaderCurrency.LYD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON><PERSON> dirhams", "app.components.screenReaderCurrency.MDL": "Moldovas leja", "app.components.screenReaderCurrency.MGA": "Malagasijas Ariary", "app.components.screenReaderCurrency.MKD": "Maķedonijas denārs", "app.components.screenReaderCurrency.MMK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MNT": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MOP": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MRO": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MUR": "Ma<PERSON><PERSON><PERSON>jas rūpija", "app.components.screenReaderCurrency.MVR": "Maldīvu sa<PERSON>uf<PERSON>a", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Meks<PERSON>s peso", "app.components.screenReaderCurrency.MXV": "Meksikas Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaizijas ringits", "app.components.screenReaderCurrency.MZN": "Mozambikas metiks", "app.components.screenReaderCurrency.NAD": "Namībijas do<PERSON>", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON>", "app.components.screenReaderCurrency.NIO": "Nik<PERSON>g<PERSON>", "app.components.screenReaderCurrency.NOK": "Norvēģijas krona", "app.components.screenReaderCurrency.NPR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NZD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON><PERSON><PERSON> ri<PERSON>", "app.components.screenReaderCurrency.PAB": "Panamas Balboa", "app.components.screenReaderCurrency.PEN": "<PERSON><PERSON>s saule", "app.components.screenReaderCurrency.PGK": "Papua-Jaung<PERSON><PERSON><PERSON> kina", "app.components.screenReaderCurrency.PHP": "<PERSON><PERSON><PERSON><PERSON> peso", "app.components.screenReaderCurrency.PKR": "Pakist<PERSON><PERSON>", "app.components.screenReaderCurrency.PLN": "<PERSON><PERSON><PERSON> valo<PERSON>", "app.components.screenReaderCurrency.PYG": "Paragvajas guaranie", "app.components.screenReaderCurrency.QAR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RUB": "Krievijas rublis", "app.components.screenReaderCurrency.RWF": "<PERSON><PERSON><PERSON> franks", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON><PERSON> rijā<PERSON>", "app.components.screenReaderCurrency.SBD": "<PERSON><PERSON><PERSON><PERSON> salu dol<PERSON>", "app.components.screenReaderCurrency.SCR": "Seišelu rūpi<PERSON>", "app.components.screenReaderCurrency.SDG": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SEK": "Zviedrijas kronas", "app.components.screenReaderCurrency.SGD": "Singapūras do<PERSON>", "app.components.screenReaderCurrency.SHP": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SLL": "Sjerraleonean Leone", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SSP": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.STD": "Santome un Prinsipi Dobra", "app.components.screenReaderCurrency.SYP": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SZL": "Svaziju lībieši Lilangeni", "app.components.screenReaderCurrency.THB": "Taizemes bats", "app.components.screenReaderCurrency.TJS": "Tadžikist<PERSON><PERSON>", "app.components.screenReaderCurrency.TMT": "Turkmen<PERSON><PERSON><PERSON> manats", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TOK": "Žetons", "app.components.screenReaderCurrency.TOP": "Tongiešu valoda Paʻanga", "app.components.screenReaderCurrency.TRY": "<PERSON><PERSON><PERSON><PERSON> liras", "app.components.screenReaderCurrency.TTD": "Trinid<PERSON><PERSON> un To<PERSON>āgo do<PERSON>", "app.components.screenReaderCurrency.TWD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrainas grivna", "app.components.screenReaderCurrency.UGX": "<PERSON><PERSON>", "app.components.screenReaderCurrency.USD": "ASV dolārs", "app.components.screenReaderCurrency.USN": "ASV dolārs (Nākamajā dienā)", "app.components.screenReaderCurrency.USS": "ASV dolārs (tajā pašā dienā)", "app.components.screenReaderCurrency.UYI": "Urugvaja Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Urugvajas peso", "app.components.screenReaderCurrency.UZS": "Uzbekistān<PERSON>", "app.components.screenReaderCurrency.VEF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.VND": "Vjet<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XAF": "Centrālāfrikas CFA franks", "app.components.screenReaderCurrency.XAG": "Sudrabs (viena Trojas unce)", "app.components.screenReaderCurrency.XAU": "Zelts (viena Trojas unce)", "app.components.screenReaderCurrency.XBA": "Eiropas apvienotā vienība (EURCO)", "app.components.screenReaderCurrency.XBB": "<PERSON><PERSON><PERSON> (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Eiropas 9. norēķinu vien<PERSON><PERSON> (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "17. <PERSON><PERSON><PERSON> norēķinu vien<PERSON>ba (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XDR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Rietumāfrikas CFA franks", "app.components.screenReaderCurrency.XPD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (viena Trojas unce)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON><PERSON> (viena Trojas unce)", "app.components.screenReaderCurrency.XTS": "<PERSON><PERSON>, kas <PERSON><PERSON><PERSON>i rezervēti test<PERSON>", "app.components.screenReaderCurrency.XXX": "Nav valūtas", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ZAR": "Dienvidāfrikas rands", "app.components.screenReaderCurrency.ZMW": "Zambijas kvaša", "app.components.screenReaderCurrency.amount": "Summa", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "p<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "app.containers.AccessibilityStatement.applicability": "<PERSON>is piekļuves apliecinājums attiecas uz {demoPlatformLink}, kas ir raksturīgs šai tīmekļa vietnei; tajā i<PERSON> to pašu kodu, un tam ir tāda pati funkcionalitāte.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> metode", "app.containers.AccessibilityStatement.assesmentText2022": "<PERSON><PERSON>s vietnes pieejamību novērtēja ārēja struktūra, kas nav iesaistīta projektēšanas un izstrādes procesā. Iepriekšminētā {demoPlatformLink} atbilstību var noteikt šajā {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "varat mainīt savas preferences", "app.containers.AccessibilityStatement.changePreferencesText": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Atbilstības izņēmumi", "app.containers.AccessibilityStatement.conformanceStatus": "Atbilstības statuss", "app.containers.AccessibilityStatement.contentConformanceExceptions": "<PERSON><PERSON><PERSON>, lai mūsu saturs būtu pieejams visiem. <PERSON><PERSON><PERSON> <PERSON> gad<PERSON> platform<PERSON> var būt nepieejams saturs, kā nor<PERSON><PERSON><PERSON><PERSON> turpmāk:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo tīmekļa vietne", "app.containers.AccessibilityStatement.email": "E-pasts:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "<PERSON>ebū<PERSON><PERSON><PERSON>, kas ir pieejami lietoša<PERSON> š<PERSON>, ir trešās puses programmatūra, un tie var nebūt pieejami.", "app.containers.AccessibilityStatement.exception_1": "<PERSON><PERSON><PERSON> digitālās iesaistīšanās platformas atvieglo lietotāju radīto saturu, ko publicē privātpersonas un organizācijas. Iespējams, ka platformā kā pielikumus vai teksta laukos platformas lietotāji augšupielādējuši PDF failus, attēlus vai cita veida failus, tostarp multivides failus. <PERSON>ie dokumenti var nebūt pilnībā pieejami.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Gaid<PERSON>m jūsu atsauksmes par šīs vietnes pieejamību. <PERSON><PERSON><PERSON><PERSON>, sazin<PERSON>ies ar mums, i<PERSON><PERSON><PERSON><PERSON> kādu no turpmāk minētajām metodēm:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Atgriezeniskās saites process", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 <PERSON><PERSON>le, Beļģija", "app.containers.AccessibilityStatement.headTitle": "Paziņojums par pieejamību | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} ir apņē<PERSON>s nodro<PERSON>t platformu, kas ir pieejama visiem lietotājiem neatkarīgi no tehnoloģijas vai spējām. Aktuālie pieejamības standarti atbilst mūsu pašreizējiem centieniem maksimāli uzlabot mūsu platformu pieejamību visiem lietotājiem.", "app.containers.AccessibilityStatement.mapping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.mapping_1": "Platformas kartes daļēji atbilst pieejamības standartiem. <PERSON><PERSON><PERSON> kartes, kartes ap<PERSON><PERSON>, tālum<PERSON>iņu un lietotāja interfeisa logrīkus var kontrolēt, izmantojot tastatūru. Administratori var arī konfigurēt karšu slāņu stilu atbalsta birojā vai izmantojot Esri integrāciju, lai izveidotu pieejamākas krāsu paletes un simboliku. Dažādu līniju vai poligonu stilu (piemēram, pārtrauktu līniju) izmantošana arī palīdzēs atšķirt kartes slāņ<PERSON>, kad vien iespējams, un, lai gan šādu stilu pašlaik nevar konfigurēt mūsu platformā, to var konfigur<PERSON>t, izmantojot kartes ar Esri integrāciju.", "app.containers.AccessibilityStatement.mapping_2": "Platformas kartes nav piln<PERSON><PERSON><PERSON>, jo t<PERSON><PERSON>, kas i<PERSON><PERSON> e<PERSON><PERSON><PERSON><PERSON>, nesnie<PERSON>z dzirdamu informāciju par pamatkartēm, kar<PERSON><PERSON> slāņiem vai datu tendencēm. Pilnībā pieejamās kartēs būtu jāiekļauj kartes slāņi un jāapraksta visas attiecīgās datu tendences. Turklāt līniju un poligonu karšu zīmēšana apsekojumos nav pieejama, jo figū<PERSON> nevar zīm<PERSON>t, izmantojot tastatūru. Tehniskās sarežģītības dēļ pašlaik nav pieejamas alternatīvas ievades metodes.", "app.containers.AccessibilityStatement.mapping_3": "Lai padarītu pieejamāku līniju un poligonu karšu <PERSON>, iesakām aptaujas jautājumā vai lapas aprakstā iekļaut ievadu vai paskaidrojumu par to, kas attēlots kartē, un attiecīgajām tendencēm. Turklāt varētu paredzēt īsas vai garas atbildes teksta jautājumu, lai respondenti vajadzības gadījumā varētu vienkārši aprakstīt savu atbildi (nevis noklikšķināt uz kartes). M<PERSON>s arī iesakām iekļaut projekta vadītāja kontaktinformāciju, lai respondenti, kuri nevar aizpildīt kartes jautājumu, varētu pieprasīt alternatīvu metodi, kā atbildēt uz šo jautājumu (piemēram, video tikšanās).", "app.containers.AccessibilityStatement.mapping_4": "Ideju projektiem un priekšlikumiem ir iespēja rādīt ievades datus kartes skatā, kas nav pieejams. <PERSON><PERSON><PERSON>īm metodēm ir pieejams alternatīvs ievades datu saraksta skats, kas ir pieejams.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "<PERSON><PERSON><PERSON> tiešsaistes semin<PERSON> ir tiešrai<PERSON> video straum<PERSON><PERSON><PERSON> komponents, ka<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> subtitrus.", "app.containers.AccessibilityStatement.pageDescription": "Paziņoju<PERSON> par šīs tīmekļa vietnes pieejamību", "app.containers.AccessibilityStatement.postalAddress": "Pasta adrese:", "app.containers.AccessibilityStatement.publicationDate": "<PERSON><PERSON><PERSON><PERSON> datums", "app.containers.AccessibilityStatement.publicationDate2024": "Šis paziņojums par pieejamību tika publicēts 2024. gada 21. augustā.", "app.containers.AccessibilityStatement.responsiveness": "<PERSON><PERSON><PERSON> c<PERSON> atbildēt uz atsauksmēm 1-2 darba dienu la<PERSON>.", "app.containers.AccessibilityStatement.statusPageText": "statusa lapa", "app.containers.AccessibilityStatement.technologiesIntro": "<PERSON><PERSON><PERSON> viet<PERSON> pieejamība ir atkarīga no šādām tehnoloģijām:", "app.containers.AccessibilityStatement.technologiesTitle": "Tehnoloģijas", "app.containers.AccessibilityStatement.title": "Paziņojums par pieejamību", "app.containers.AccessibilityStatement.userGeneratedContent": "Lietotāja ģenerēts saturs", "app.containers.AccessibilityStatement.workshops": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Izvēlieties projektu", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "<PERSON>zman<PERSON>jot Satura konstruktoru, varat izmantot plašākas izkārtojuma iespējas. <PERSON><PERSON><PERSON><PERSON>, kurās satura konstruktorā nav pieejams saturs, tā vietā tiks parādīts parastais projekta apraksta saturs.", "app.containers.AdminPage.ProjectDescription.linkText": "Apraksta rediģēšana Satura konstruktorā", "app.containers.AdminPage.ProjectDescription.saveError": "Saglabā<PERSON><PERSON> proje<PERSON>a <PERSON>, kaut kas notika neparei<PERSON>.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Satura konstruktora izmantošana aprakstam", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Izmantojot Satura konstruktoru, varat izmantot plašākas izkārtojuma iespējas.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON><PERSON><PERSON><PERSON><PERSON> projektu", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Aptaujas beigas", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Izveidot viedo grupu", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas atbilst visiem š<PERSON>m <PERSON>, tiks automātiski pievienoti grupai:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON><PERSON><PERSON><PERSON><PERSON> vismaz vienu noteikumu", "app.containers.AdminPage.Users.UsersGroup.rulesError": "<PERSON>ži <PERSON>cīju<PERSON> ir ne<PERSON>lnī<PERSON>", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Saglabāt grupu", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Viedo grupu konfigurēšana nav daļa no jūsu pašreizējās licences. Sazinieties ar savu GovSuc<PERSON> men<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> vair<PERSON><PERSON> par to.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "<PERSON><PERSON><PERSON><PERSON>t grupas nosaukumu", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Jūsu platformai verifikācija ir atspējota, noņemiet verifikācijas noteikumu vai sazinieties ar atbal<PERSON> dienes<PERSON>.", "app.containers.App.appMetaDescription": "Lai<PERSON><PERSON> lūgti {orgName} tiešsaistes līdzdalības platformā. \nIepazīstieties ar vietējiem projektiem un iesaistieties diskusijā!", "app.containers.App.loading": "Notiek iel<PERSON>de...", "app.containers.App.metaTitle1": "Iedzīvotāju iesaistes platforma | {orgName}", "app.containers.App.skipLinkText": "Pāriet uz galveno saturu", "app.containers.AreaTerms.areaTerm": "apgabals", "app.containers.AreaTerms.areasTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Konts ar šo e-pasta adresi jau pastāv. <PERSON><PERSON><PERSON> varat i<PERSON>, pieteikties ar šo e-pasta adresi un pārbaudīt savu kontu iestatījumu lapā.", "app.containers.Authentication.steps.AccessDenied.close": "Aizvērt", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "<PERSON><PERSON><PERSON>, lai piedal<PERSON> š<PERSON> proces<PERSON>.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Atgriešanās pie vienotās pieteikšanās verifi<PERSON>", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "<PERSON><PERSON><PERSON><PERSON>, ievadiet žetonu", "app.containers.Authentication.steps.Invitation.token": "Žetons", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "<PERSON><PERSON> jau ir konts? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Piesakieties", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-pasti šajā kategorijā", "app.containers.CampaignsConsentForm.messageError": "<PERSON>ūsu e-pasta preferenču saglab<PERSON>šanā ir pie<PERSON>a k<PERSON>.", "app.containers.CampaignsConsentForm.messageSuccess": "<PERSON><PERSON>su e-pasta preferences ir saglab<PERSON><PERSON>.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Kādus e-pasta paziņojumus vēlaties saņemt? ", "app.containers.CampaignsConsentForm.notificationsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "Atgriezties pie profila iestatījumiem", "app.containers.ChangeEmail.confirmationModalTitle": "Apstipriniet savu e-pasta adresi", "app.containers.ChangeEmail.emailEmptyError": "Sniedziet e-pasta adresi", "app.containers.ChangeEmail.emailInvalidError": "Norādiet e-pasta adresi pareizā formātā, piem<PERSON>ram, <EMAIL>.", "app.containers.ChangeEmail.emailRequired": "<PERSON><PERSON><PERSON><PERSON>, ievadiet e-pasta adresi.", "app.containers.ChangeEmail.emailTaken": "Šis e-pasts jau tiek <PERSON>.", "app.containers.ChangeEmail.emailUpdateCancelled": "E-pasta at<PERSON><PERSON><PERSON><PERSON><PERSON> ir atcelta.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "<PERSON>tu savu e-pasta adresi, atsāciet procesu no jauna.", "app.containers.ChangeEmail.helmetDescription": "E-pasta lapas maiņa", "app.containers.ChangeEmail.helmetTitle": "Mainiet savu e-pasta adresi", "app.containers.ChangeEmail.newEmailLabel": "Jauns e-pasts", "app.containers.ChangeEmail.submitButton": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.titleAddEmail": "Pievienojiet savu e-pasta adresi", "app.containers.ChangeEmail.titleChangeEmail": "Mainiet savu e-pasta adresi", "app.containers.ChangeEmail.updateSuccessful": "<PERSON><PERSON><PERSON> e-pasta adrese ir veik<PERSON><PERSON>gi at<PERSON>.", "app.containers.ChangePassword.currentPasswordLabel": "Pašreizējā <PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "Ievadiet savu pašreizējo paroli", "app.containers.ChangePassword.goHome": "Iet uz s<PERSON>ku<PERSON>u", "app.containers.ChangePassword.helmetDescription": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.newPasswordLabel": "Jauna parole", "app.containers.ChangePassword.newPasswordRequired": "Ievadiet savu jauno paroli", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Sniedziet vismaz {minimumPasswordLength} rakstzīmju garu paroli.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON><PERSON><PERSON> ir ve<PERSON>", "app.containers.ChangePassword.passwordEmptyError": "Ievadiet savu paroli", "app.containers.ChangePassword.passwordsDontMatch": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroles a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.titleAddPassword": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.titleChangePassword": "<PERSON><PERSON><PERSON>", "app.containers.Comments.a11y_commentDeleted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.a11y_commentPosted": "Komentārs <PERSON>", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {nav patīk} one {1 patīk} other {# patīk}}", "app.containers.Comments.a11y_undoLike": "Atcelt, piemēram", "app.containers.Comments.addCommentError": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.Comments.adminCommentDeletionCancelButton": "Atcelt", "app.containers.Comments.adminCommentDeletionConfirmButton": "Dzēst <PERSON> kome<PERSON>", "app.containers.Comments.cancelCommentEdit": "Atcelt", "app.containers.Comments.childCommentBodyPlaceholder": "Uzrakstīt atbildi...", "app.containers.Comments.commentCancelUpvote": "Atcelt", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON><PERSON> koment<PERSON> ir d<PERSON>.", "app.containers.Comments.commentDeletionCancelButton": "Saglab<PERSON>t manu komentāru", "app.containers.Comments.commentDeletionConfirmButton": "D<PERSON>ē<PERSON> manu komentāru", "app.containers.Comments.commentLike": "T<PERSON><PERSON><PERSON> kā", "app.containers.Comments.commentReplyButton": "Atbildēt", "app.containers.Comments.commentsSortTitle": "<PERSON><PERSON><PERSON><PERSON> komentārus pēc", "app.containers.Comments.completeProfileLinkText": "aizpildiet savu profilu", "app.containers.Comments.completeProfileToComment": "<PERSON><PERSON><PERSON><PERSON>, {completeRegistrationLink} , lai koment<PERSON><PERSON>.", "app.containers.Comments.confirmCommentDeletion": "Vai esat pārl<PERSON>cināts, ka vēlaties izdzēst šo komentāru? Atpakaļ<PERSON>ļa nebūs!", "app.containers.Comments.deleteComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "<PERSON>niegt plašāku informāciju par savu iemeslu", "app.containers.Comments.deleteReasonError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReason_inappropriate": "<PERSON>s ir nepiemērots vai a<PERSON>skar<PERSON>š<PERSON>", "app.containers.Comments.deleteReason_irrelevant": "Tas nav saist<PERSON>ts", "app.containers.Comments.deleteReason_other": "Cits iemesls", "app.containers.Comments.editComment": "Rediģēt", "app.containers.Comments.guidelinesLinkText": "mūsu kop<PERSON>as vadlī<PERSON>", "app.containers.Comments.ideaCommentBodyPlaceholder": "Rakstiet savu koment<PERSON><PERSON>eit", "app.containers.Comments.internalCommentingNudgeMessage": "Iek<PERSON><PERSON><PERSON> komentāru izveide nav iekļauta jūsu pašreizējā licencē. Sazinieties ar savu GovSuc<PERSON> men<PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON> par to.", "app.containers.Comments.internalConversation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "app.containers.Comments.loadMoreComments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vair<PERSON>k koment<PERSON>", "app.containers.Comments.loadingComments": "Notiek komentāru i<PERSON>...", "app.containers.Comments.loadingMoreComments": "Notiek papildu komentāru i<PERSON>...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Šis komentārs nav redzams parastajiem lietotājiem", "app.containers.Comments.postInternalComment": "Post iekšējais komentārs", "app.containers.Comments.postPublicComment": "Publicējiet publiskus komentārus", "app.containers.Comments.profanityError": "<PERSON>! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka jūsu publikācijā ir valoda, kas neatbilst {guidelinesLink}. Mēs mēģinām uzturēt šo vietu drošu ikvienam. <PERSON><PERSON><PERSON><PERSON>, rediģējiet savu ieguldījumu un mēģiniet vēlreiz.", "app.containers.Comments.publicDiscussion": "Publiskā apspriešana", "app.containers.Comments.publishComment": "Publicējiet savu komentāru", "app.containers.Comments.reportAsSpamModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> jūs vēlaties ziņot par šo kā surogātpastu?", "app.containers.Comments.saveComment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.signInLinkText": "pieraks<PERSON>ī<PERSON>", "app.containers.Comments.signInToComment": "<PERSON><PERSON><PERSON><PERSON>, {signInLink}, lai koment<PERSON><PERSON>.", "app.containers.Comments.signUpLinkText": "reģistrēties", "app.containers.Comments.verifyIdentityLinkText": "Pārbaudiet savu identitāti", "app.containers.Comments.visibleToUsersPlaceholder": "<PERSON><PERSON> komentā<PERSON> ir redzams regulārajiem lie<PERSON>em", "app.containers.Comments.visibleToUsersWarning": "Šeit publicētie komentāri būs redzami regulārajiem lietot<PERSON>.", "app.containers.ContentBuilder.PageTitle": "Projekta apraksts", "app.containers.CookiePolicy.advertisingContent": "Re<PERSON><PERSON><PERSON><PERSON> sīkdatnes var i<PERSON>, lai personalizētu un novērtētu ārējo mārketinga kampaņu efektivitāti attiecībā uz iesaistīšanos šajā platformā. Mēs šajā platformā nerādīsim nekādu reklāmu, taču jūs varat saņemt personalizētas reklāmas, pamatojoties uz jūsu apmeklētajām lapām.", "app.containers.CookiePolicy.advertisingTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "Analītikas sīkdatnes izseko lietotāja uzvedību, pie<PERSON><PERSON><PERSON>, kuras lapas ir apmeklētas un uz cik ilgu laiku. Tās var arī apkopot dažādus tehniskus datus, kas ietver pārlūka informāciju, aptuveno atrašanās vietu un IP adresi. Mēs šos datus izmantojam tikai iekš<PERSON>ji, lai turpinātu uzlabot vispārējo lietotāja pieredzi un platformas darbību. Šādus datus var arī koplietot starp Go Vocal un {orgName}, lai novērtētu un uzlabotu iesaisti platformas projektos. <PERSON><PERSON><PERSON><PERSON>jiet, ka dati ir anonīmi un tiek izmantoti apkopotā līmenī — tie neveic jūsu personas identifikāciju. <PERSON><PERSON><PERSON> iespē<PERSON>, ka gadījumā, ja dati tiktu apkopoti ar citiem datu avotiem, šāda identifikācija varētu notikt.", "app.containers.CookiePolicy.analyticsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.cookiePolicyDescription": "Detalizēts skaidrojums par to, kā mēs šajā platformā izmantojam sīk<PERSON>.", "app.containers.CookiePolicy.cookiePolicyTitle": "Sīkdatņu politika", "app.containers.CookiePolicy.essentialContent": "<PERSON><PERSON><PERSON> sīk<PERSON> ir b<PERSON>, lai nod<PERSON><PERSON><PERSON><PERSON><PERSON> atbilstošu šīs platformas darbību. <PERSON><PERSON><PERSON> pamata sīkdatnes tiek galvenok<PERSON>rt izman<PERSON>, lai autentificētu jūsu kontu, kad a<PERSON> platformu, un saglabātu jūsu vēlamo valodu.", "app.containers.CookiePolicy.essentialTitle": "<PERSON><PERSON> s<PERSON>", "app.containers.CookiePolicy.externalContent": "<PERSON><PERSON><PERSON><PERSON> mūsu lapās var tikt attēlots saturs no ārējiem pakalpojumu sniedzējiem, piemēram, YouTube vai Typeform. Mēs nekontrolējam šīs trešo personu sī<PERSON>, un šo ārējo pakalpojumu sniedzēju satura skatīšanas rezultātā jūsu ierīcē arī var tikt uzstādītas sīkdatnes.", "app.containers.CookiePolicy.externalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.functionalContents": "Funkcionālās s<PERSON> var iespējot apmeklētājiem, lai saņemtu paziņojumus par atjauninājumiem un piekļūtu atbalsta kanāliem tieši no platformas.", "app.containers.CookiePolicy.functionalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.headCookiePolicyTitle": "Sīkfailu politika | {orgName}", "app.containers.CookiePolicy.intro": "Sīkdatnes ir teksta datnes, kas tiek saglabātas jūsu datora vai mobilās ierīces pārlūkprogrammā vai cietajā diskā, kad apmeklējat vietni, un uz kuriem vietne var atsaukties turpmāko apmeklējumu laikā. <PERSON><PERSON><PERSON> i<PERSON> s<PERSON>, lai saprastu, kā apmeklētāji izmanto šo platformu, lai uzlabotu tās dizainu un pieredzi, lai atcerētos jūsu preferences (piemēram, vēlamo valodu) un atbalstītu galvenās funkcijas reģistrētajiem lietotājiem un platformas administratoriem.", "app.containers.CookiePolicy.manageCookiesDescription": "Jūs savā sīkdatņu preferenču sadaļā jebkurā brīdī varat iespējot vai atspējot analītikas, mārketinga un funkcionālās sīkdatnes. <PERSON><PERSON><PERSON><PERSON>, izmantojot savu interneta pārlūku, jūs varat manuāli vai automātiski izdzēst jebkuras esoš<PERSON>s sīkdat<PERSON>. <PERSON><PERSON><PERSON> sīk<PERSON>t<PERSON> var tikt uzstādītas no jauna, tiklīdz sniegsiet piekrišanu nākamo šīs platformas apmeklējumu laikā. Ja neizdzēs<PERSON><PERSON>t sīk<PERSON>t<PERSON>, j<PERSON><PERSON> sīkdatņu preferences tiks saglabātas 60 dienas, pēc tam jums atkal tiks lūgts sniegt piek<PERSON>.", "app.containers.CookiePolicy.manageCookiesPreferences": "Doties uz savu {manageCookiesPreferencesButtonText}, lai apskatītu pilnu šajā platformā izmantoto trešo personu integrāciju sarakstu un pārvaldītu savas preferences.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "app.containers.CookiePolicy.manageCookiesTitle": "<PERSON><PERSON><PERSON> s<PERSON> p<PERSON>", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON>k<PERSON><PERSON><PERSON><PERSON> i<PERSON>īju<PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "<PERSON><PERSON>āk nor<PERSON>d<PERSON><PERSON><PERSON><PERSON> sīkdatņu kategorija<PERSON> var neattiekties uz visiem apmeklētājiem vai platformām; lai iegūtu pilnu uz jums attiecināmo trešo personu integrāciju sarakstu, skatiet savu {viewPreferencesButton}.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Kā<PERSON> nolūkam mēs i<PERSON>m sīk<PERSON>?", "app.containers.CustomPageShow.editPage": "Rediģēt lapu", "app.containers.CustomPageShow.goBack": "Atgriezties", "app.containers.CustomPageShow.notFound": "Lapa nav atrasta", "app.containers.DisabledAccount.bottomText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> pier<PERSON> no {date}.", "app.containers.DisabledAccount.termsAndConditions": "<PERSON><PERSON><PERSON> un nosacījumi", "app.containers.DisabledAccount.text2": "<PERSON><PERSON><PERSON> konts līdzdalības platformā {orgName} ir uz laiku atspējots par kopienas vadlīniju pārkāpumu. Sīkāku informāciju par to var iegūt vietnē {TermsAndConditions}.", "app.containers.DisabledAccount.title": "<PERSON><PERSON><PERSON> kont<PERSON> ir uz laiku atspējots", "app.containers.EventsShow.addToCalendar": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.editEvent": "Rediģēt notikumu", "app.containers.EventsShow.emailSharingBody2": "Apmeklējiet šo pasākumu: {eventTitle}. Vairāk lasiet {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Pa<PERSON><PERSON><PERSON><PERSON> datums un laiks", "app.containers.EventsShow.eventFrom2": "No \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Atgriezties", "app.containers.EventsShow.goToProject": "Dodieties uz projektu", "app.containers.EventsShow.haveRegistered": "ir reģistrējušies", "app.containers.EventsShow.icsError": "<PERSON><PERSON><PERSON><PERSON>, lejupielādējot ICS failu", "app.containers.EventsShow.linkToOnlineEvent": "Saite uz tiešsaistes pasākumu", "app.containers.EventsShow.locationIconAltText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta", "app.containers.EventsShow.metaTitle": "Pasākums: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "app.containers.EventsShow.onlineLinkIconAltText": "Tiešsaistes saite uz sanāksmi", "app.containers.EventsShow.registered": "reģistrēts", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 reģistrētāju} one {1 reģistrētājs} other {# reģistrētāji}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} reģistrējušies", "app.containers.EventsShow.registrantsIconAltText": "Reģistrētāji", "app.containers.EventsShow.socialMediaSharingMessage": "Apmeklējiet šo pasākumu: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# dalībnieks} other {# dalībnieki}}", "app.containers.EventsViewer.allTime": "<PERSON><PERSON>u laiku", "app.containers.EventsViewer.date": "Datums", "app.containers.EventsViewer.thisMonth2": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>is", "app.containers.EventsViewer.thisWeek2": "<PERSON>āka<PERSON><PERSON>", "app.containers.EventsViewer.today": "Šodien", "app.containers.IdeaButton.addAContribution": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAPetition": "Pievienojiet lūgumrakstu", "app.containers.IdeaButton.addAProject": "Pievienot projektu", "app.containers.IdeaButton.addAProposal": "Pievienot priekšlikumu", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnInitiative": "Iniciatī<PERSON>", "app.containers.IdeaButton.addAnOption": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.postingDisabled": "Jauni pieteikumi pašlaik netiek pieņemti", "app.containers.IdeaButton.postingInNonActivePhases": "Jaunus iesniegumus var pievienot tikai aktīvajos posmos.", "app.containers.IdeaButton.postingInactive": "Pašlaik jauni iesniegumi netiek pieņemti.", "app.containers.IdeaButton.postingLimitedMaxReached": "<PERSON><PERSON>s jau esat aizpildījis šo aptauju. Paldies par atbildi!", "app.containers.IdeaButton.postingNoPermission": "Jauni pieteikumi pašlaik netiek pieņemti", "app.containers.IdeaButton.postingNotYetPossible": "Jauni iesniegumi vēl netiek pieņemti.", "app.containers.IdeaButton.signInLinkText": "pieraks<PERSON>ī<PERSON>", "app.containers.IdeaButton.signUpLinkText": "reģistrēties", "app.containers.IdeaButton.submitAnIssue": "<PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "app.containers.IdeaButton.submitYourIdea": "<PERSON><PERSON><PERSON><PERSON> savu ideju", "app.containers.IdeaButton.takeTheSurvey": "Aizpildiet aptauju", "app.containers.IdeaButton.verificationLinkText": "Pārbaudiet savu identitāti tūl<PERSON>t.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {nav komentāru} one {1 komentārs} other {# komentāri}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {bez balsīm} one {1 balss} other {# balsis}} ārpus {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Aizvērt filtru paneli", "app.containers.IdeaCards.a11y_totalItems": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>: {ideasCount}", "app.containers.IdeaCards.all": "<PERSON><PERSON>", "app.containers.IdeaCards.allStatuses": "Visi statusi", "app.containers.IdeaCards.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.list": "Saraksts", "app.containers.IdeaCards.map": "<PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.noFilteredResults": "Rezultāti nav atrasti. <PERSON><PERSON><PERSON><PERSON>, mēģiniet izmantot citu filtru vai meklēšanas terminu.", "app.containers.IdeaCards.numberResults": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "<PERSON>es<PERSON>ējas", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>u s<PERSON>", "app.containers.IdeaCards.projectFilterTitle": "Projekti", "app.containers.IdeaCards.projectTerm": "Projekti", "app.containers.IdeaCards.proposals": "Priekšlikumi", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "Atiestatīt filtrus", "app.containers.IdeaCards.showXResults": "<PERSON><PERSON><PERSON><PERSON>t {ideasCount, plural, one {# result} other {# results}}", "app.containers.IdeaCards.sortTitle": "Šķirošana", "app.containers.IdeaCards.statusTitle": "Statuss", "app.containers.IdeaCards.statusesTitle": "Statuss", "app.containers.IdeaCards.topics": "Tagi", "app.containers.IdeaCards.topicsTitle": "Tagi", "app.containers.IdeaCards.trending": "Tendences", "app.containers.IdeaCards.tryDifferentFilters": "Rezultāti nav atrasti. <PERSON><PERSON><PERSON><PERSON>, mēģiniet izmantot citu filtru vai meklēšanas terminu.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} komentāri} one {~{ideasCount} komentārs} other {~{ideasCount} komentāri}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} iemaksas} one {~{ideasCount} iemaksas} other {~{ideasCount} iemaksas}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} idejas} one {~{ideasCount} ideja} other {~{ideasCount} idejas}}.", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} iniciatīvas} one {~{ideasCount} iniciatīvas} other {~{ideasCount} iniciatīvas}}.", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opcijas} one {{ideasCount} opcija} other {~{ideasCount} opcijas}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petīcijas} one {~{ideasCount} petīcija} other {~{ideasCount} petīcijas}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projekti} one {~{ideasCount} projekts} other {~{ideasCount} projekti}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} priek<PERSON><PERSON><PERSON>} one {~{ideasCount} priekšlikums} other {~{ideasCount} priek<PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} jaut<PERSON><PERSON><PERSON>} one {~{ideasCount} jautājums} other {~{ideasCount} jaut<PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# rezultāts} other {# rezultāti}}", "app.containers.IdeasEditPage.contributionFormTitle": "Rediģēt ieguldījumu", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "Neizdev<PERSON><PERSON> aug<PERSON>d<PERSON>t vienu vai vairākus failus. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet faila izmēru un formātu un mēģiniet vēlreiz.", "app.containers.IdeasEditPage.formTitle": "Rediģēt ideju", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Rediģēt savu publikāciju. <PERSON>vienot jaunu un mainīt veco informāciju.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Rediģēt {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Rediģēšanas iniciatīva", "app.containers.IdeasEditPage.issueFormTitle": "Rediģēt komentāru", "app.containers.IdeasEditPage.optionFormTitle": "Rediģēt iespēju", "app.containers.IdeasEditPage.petitionFormTitle": "Rediģēt lūgumrakstu", "app.containers.IdeasEditPage.projectFormTitle": "Rediģēt projektu", "app.containers.IdeasEditPage.proposalFormTitle": "Rediģēt priekšlikumu", "app.containers.IdeasEditPage.questionFormTitle": "Rediģēt j<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai nav kļūdu, un mēģiniet vēlreiz.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Visi publicētie ievades dati", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visu <PERSON>, kas publicēts {orgName} līdzdalības platformā.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasIndexPage.loadMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vēl...", "app.containers.IdeasIndexPage.loading": "Notiek iel<PERSON>de...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Pēc noklusējuma jūsu iesniegumi tiks saistīti ar jūsu profilu, ja vien neizvēlaties šo opciju.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Profila <PERSON>", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Pašlaik atbildes uz šo apsekojumu nav pieejamas. <PERSON><PERSON><PERSON><PERSON>, atgriezieties pie projekta, lai ieg<PERSON><PERSON> vairāk informācijas.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Šis apsekojums pašlaik nav aktīvs.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Atgriezties pie projekta", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "<PERSON><PERSON><PERSON> jau esat aizpildījis šo a<PERSON>.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Iesniegtais apsekojums", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Paldies par atbildi!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jābūt īsākam par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "<PERSON><PERSON><PERSON> tekstam ir jāb<PERSON>t garā<PERSON>m par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON>ju<PERSON> nosaukumam jābūt īsākam par {limit} r<PERSON><PERSON> zī<PERSON>m", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON>m jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties vismaz vienu līdzfinansētāju", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "<PERSON><PERSON><PERSON> aprakstam jāb<PERSON>t ī<PERSON>m par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "<PERSON><PERSON><PERSON> apra<PERSON>tam jā<PERSON>t garā<PERSON>m par {limit} r<PERSON><PERSON>m", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "<PERSON><PERSON><PERSON><PERSON>, snied<PERSON>t aprakstu", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "<PERSON><PERSON>jas nosaukumam jābūt īsākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "<PERSON><PERSON><PERSON> no<PERSON>ukumam jābūt garākam par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Iniciatīvas aprakstam jābūt īsākam par {limit} rakstz<PERSON>mēm.", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Iniciatīvas aprakstam jāb<PERSON>t garā<PERSON>m par {limit} raks<PERSON><PERSON>mēm.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Iniciat<PERSON><PERSON> nosaukumam jābūt īsākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Iniciat<PERSON><PERSON> nosaukumam jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jābūt ī<PERSON>m par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jāb<PERSON>t garā<PERSON>m par {limit} r<PERSON><PERSON>m", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>ukumam jābūt īsākam par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> nosaukumam jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON><PERSON> lauks ir obli<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ieva<PERSON>t derīgu numuru.", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Iespējas aprakstam jābūt ī<PERSON>m par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "<PERSON>es<PERSON><PERSON>jas aprakstam jāb<PERSON>t garā<PERSON>m par {limit} raks<PERSON><PERSON>mēm.", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Iespējas nosaukumam jābūt īsākam par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Iespējas nosaukumam jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties vismaz vienu tagu", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jābūt ī<PERSON>m par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jāb<PERSON>t garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON> nosaukumam jābūt īsākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON> nosaukumam jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Projekta aprakstam jāb<PERSON>t ī<PERSON>m par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Projekta apraks<PERSON> jā<PERSON>t garā<PERSON>m par {limit} r<PERSON><PERSON>m", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Projekta nosaukumam jābūt īsākam par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Projekta nosaukumam jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Priek<PERSON><PERSON><PERSON> aprakstam jābūt īsākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Priek<PERSON><PERSON><PERSON> aprakstam jāb<PERSON>t garā<PERSON>m par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Priek<PERSON>likuma nosaukumam jābūt īsākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Priekšlikuma nosaukumam jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "<PERSON><PERSON><PERSON><PERSON>, ievadiet skaitli", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "<PERSON><PERSON><PERSON><PERSON>, ievadiet skaitli", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jāb<PERSON>t ī<PERSON>m par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> apra<PERSON> jāb<PERSON>t garā<PERSON>m par {limit} r<PERSON><PERSON>m", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>ukumam jābūt īsākam par {limit} r<PERSON><PERSON> z<PERSON>m", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>m jābūt garākam par {limit} rakstzīmēm.", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON><PERSON><PERSON><PERSON>, norādiet nosaukumu", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jāb<PERSON>t ī<PERSON> par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Ieguldījuma apraks<PERSON> jā<PERSON>t vismaz 30 rakstu zī<PERSON> garam", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nosaukumam jābūt īsākam par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Ieguldījuma no<PERSON>ukumam jābūt vismaz 10 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "<PERSON><PERSON><PERSON> aprakstam jāb<PERSON><PERSON> par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Idejas aprakstam j<PERSON><PERSON> vismaz 30 rakstu zīm<PERSON> garam.", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON>, norādiet nosaukumu", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "<PERSON><PERSON><PERSON> nosaukumam jābūt īsākam par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Idejas nosaukumam jābūt vismaz 10 rakstu zīmju garam", "app.containers.IdeasNewPage.api_error_includes_banned_words": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jūs esat i<PERSON>tojis vienu vai vairākus vārdus, kas saskaņ<PERSON> ar {guidelinesLink} uzskatāmi par lamu vārdiem. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> savu tekstu, iz<PERSON><PERSON><PERSON><PERSON><PERSON>t no tā jebkādus iespējamus lamu vārdus.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Iniciatīvas aprakstam jābūt īsākam par 80 rakstzīmēm.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Iniciatīvas aprakstam jāb<PERSON>t vismaz 30 rakstzīmju garam.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Iniciat<PERSON><PERSON> nosaukumam jābūt īsākam par 80 rakstzīmēm.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Iniciatīvas no<PERSON>ukumam jābūt vismaz 10 rakstzīmju garam.", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jāb<PERSON>t <PERSON> par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Problēmas aprakstam jābūt vismaz 30 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>mam jābūt īsākam par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Problē<PERSON> jābūt vismaz 10 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Iespējas aprakstam jāb<PERSON>t <PERSON> par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Iespējas aprakstam jābūt vismaz 30 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Iespējas nosaukumam jābūt īsākam par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Iespējas nosaukumam jābūt vismaz 10 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jābūt īs<PERSON> par 80 rakstzīmēm.", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Lūgumraksta aprakstam jābūt vismaz 30 rakstzīmju garam.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> nosaukumam jābūt īsākam par 80 rakstzīmēm.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Petīcijas nosaukumam jābūt vismaz 10 rakstzīmju garam.", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Projekta aprakstam jāb<PERSON>t īs<PERSON>m par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Projekta aprakstam jābūt vismaz 30 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Projekta nosaukumam jābūt īsākam par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Projekta nosaukumam jābūt vismaz 10 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Priek<PERSON><PERSON><PERSON> aprakstam jābūt īsākam par 80 rakstzīmēm.", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Priekšlikuma aprakstam jābūt vismaz 30 rakstzīmju garam.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Priek<PERSON><PERSON>uma nosaukumam jābūt īsākam par 80 rakstzīmēm.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Priekšlikuma nosaukumam jābūt vismaz 10 rakstzīmju garam.", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON>, snied<PERSON>t aprakstu", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> aprakstam jāb<PERSON><PERSON> par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> jā<PERSON><PERSON><PERSON> vismaz 30 rakstu zīmes garam", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON><PERSON> no<PERSON>ukumam jābūt īsākam par 80 rakstu zīmēm", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "<PERSON><PERSON><PERSON><PERSON><PERSON> jāb<PERSON>t vismaz 10 rakstu zīmes garam", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Atcelt", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "Jā, es gribu aizbraukt", "app.containers.IdeasNewPage.contributionMetaTitle1": "<PERSON><PERSON><PERSON> jaunu ieg<PERSON>ījumu projektā | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Rediģēt pētījumu", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Publicēt iesniegumu un pievienoties sarunai {orgName} līdzdalības platformā.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "<PERSON><PERSON><PERSON> jaunu ideju projektam | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Pievienot projektam jaunu iniciatīvu | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "<PERSON><PERSON>nas problēmas <PERSON> projektam | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Vai esat pārl<PERSON>, ka vēlaties doties prom?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "<PERSON><PERSON><PERSON> atbil<PERSON>u projekts ir saglabāts privāti, un jūs varat atgriezties, lai to p<PERSON><PERSON><PERSON> vē<PERSON>.", "app.containers.IdeasNewPage.leaveSurvey": "Atvaļinā<PERSON><PERSON>", "app.containers.IdeasNewPage.leaveSurveyText": "<PERSON><PERSON><PERSON> atbildes netiks saglabātas.", "app.containers.IdeasNewPage.optionMetaTitle1": "<PERSON><PERSON><PERSON> jaunu opciju projektam | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "<PERSON><PERSON><PERSON> jaunu l<PERSON>u projektam | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "<PERSON><PERSON><PERSON> jaunu projektu projektam | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Pievie<PERSON> jaunu p<PERSON>š<PERSON>umu projektam | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "<PERSON><PERSON><PERSON> jaunu j<PERSON><PERSON> projektam | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "<PERSON><PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "L<PERSON><PERSON>zfinansētā<PERSON>", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "<PERSON>ūs esat aicināts kļūt par līdzsponsoru.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Uzaicinājums <PERSON>ņemts", "app.containers.IdeasShow.Cosponsorship.pending": "gaida", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Pašreizējais statuss", "app.containers.IdeasShow.MetaInformation.location": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta", "app.containers.IdeasShow.MetaInformation.postedBy": "Publicēja", "app.containers.IdeasShow.MetaInformation.similar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ievades dati", "app.containers.IdeasShow.MetaInformation.topics": "Tagi", "app.containers.IdeasShow.commentCTA": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.contributionEmailSharingBody": "Atbalstīt šo ieguldījumu '{postTitle}' {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Atbalst<PERSON>t šo ieguldījumu: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "<PERSON>ld<PERSON>, ka iesniedzāt savu ieguldījumu!", "app.containers.IdeasShow.contributionTwitterMessage": "Atbalst<PERSON>t šo ieguldījumu: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Atbalst<PERSON>t šo ieguldījumu: {postTitle}", "app.containers.IdeasShow.currentStatus": "Pašreizējais statuss", "app.containers.IdeasShow.deletedUser": "nezināms autors", "app.containers.IdeasShow.ideaEmailSharingBody": "Atbal<PERSON><PERSON>t manu ideju '{ideaTitle}' {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu ideju: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "<PERSON>bal<PERSON><PERSON><PERSON> šo ideju: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "<PERSON>bal<PERSON><PERSON><PERSON> šo ideju: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Atbal<PERSON><PERSON>t šo komentāru: {postTitle}", "app.containers.IdeasShow.imported": "Importēts", "app.containers.IdeasShow.importedTooltip": "Šis {inputTerm} tika apkopots bezsaistē un automātiski augšupielādēts platformā.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Atbalstiet šo iniciatīvu '{ideaTitle}' vietnē {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Atbalstiet šo iniciatīvu: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Paldies par iniciatīvas i<PERSON>g<PERSON>nu!", "app.containers.IdeasShow.initiativeTwitterMessage": "Atbalstiet šo iniciatīvu: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Atbalstiet šo iniciatīvu: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Atbalst<PERSON>t šo komentāru '{postTitle}' {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Atbal<PERSON><PERSON>t šo komentāru: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Paldies par komentāra i<PERSON>!", "app.containers.IdeasShow.issueTwitterMessage": "Atbal<PERSON><PERSON>t šo komentāru: {postTitle}", "app.containers.IdeasShow.metaTitle": "<PERSON><PERSON><PERSON> dati: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Atbalst<PERSON>t šo iespēju '{postTitle}' {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo iespēju: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "<PERSON><PERSON><PERSON> iespēja ir veiksmīgi publicēta!", "app.containers.IdeasShow.optionTwitterMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo iespēju: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo iespēju: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Atbalstiet šo lūgumrakstu '{ideaTitle}' vietnē {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Atbalstiet šo l<PERSON>gum<PERSON>: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON>ld<PERSON>, ka iesniedzāt savu lūgumrakstu!", "app.containers.IdeasShow.petitionTwitterMessage": "Atbalstiet šo l<PERSON>gum<PERSON>: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Atbalstiet šo l<PERSON>gum<PERSON>: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Atbalst<PERSON>t šo projektu '{postTitle}' {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo projektu: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Paldies par jūsu projekta iesniegšanu!", "app.containers.IdeasShow.projectTwitterMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo projektu: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> šo projektu: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Atbalstiet šo p<PERSON>kš<PERSON>umu \"{ideaTitle}\" vietnē {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Atbalst<PERSON>t š<PERSON> p<PERSON>: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Paldies par priekš<PERSON>uma i<PERSON>gšanu!", "app.containers.IdeasShow.proposalTwitterMessage": "Atbalst<PERSON>t š<PERSON> p<PERSON>: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Atbalst<PERSON>t š<PERSON> p<PERSON>: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} no {votingThreshold} v<PERSON><PERSON><PERSON><PERSON><PERSON> balsu skaita", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Atcelt balsojumu", "app.containers.IdeasShow.proposals.VoteControl.days": "dienas", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "m<PERSON><PERSON> v<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.hours": "stun<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Statuss un balsojumi", "app.containers.IdeasShow.proposals.VoteControl.minutes": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.vote": "Balsojums", "app.containers.IdeasShow.proposals.VoteControl.voted": "Balsots", "app.containers.IdeasShow.proposals.VoteControl.votedText": "<PERSON><PERSON><PERSON>, kad <PERSON><PERSON> iniciatīva tiks virzīta uz nākamo posmu. {x, plural, =0 {Vēl ir {xDays} atlicis.} one {Vēl ir {xDays} atlicis.} other {Ir {xDays} atlicis.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "<PERSON><PERSON><PERSON> balso<PERSON> ir iesniegts!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "<PERSON><PERSON><PERSON><PERSON><PERSON> par šo p<PERSON>kš<PERSON>umu nevar balsot. <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {ma<PERSON><PERSON><PERSON> nek<PERSON> dienu} one {vienu dienu} other {# dienas}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {bez balsīm} one {1 balss} other {# balsis}}", "app.containers.IdeasShow.questionEmailSharingBody": "Pievienoties diskusijai par šo j<PERSON>ājumu '{postTitle}' {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Pievienojieties diskusijai: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "<PERSON><PERSON><PERSON> j<PERSON> ir veiksmīgi publicēts!", "app.containers.IdeasShow.questionTwitterMessage": "Pievienojieties diskusijai: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Pievienojieties diskusijai: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> jūs vēlaties ziņot par šo kā surogātpastu?", "app.containers.IdeasShow.share": "Koplietot", "app.containers.IdeasShow.sharingModalSubtitle": "Uzrunājiet vairāk cilvēku un nodrošiniet, ka jūsu viedoklis tiek sadzirdēts.", "app.containers.IdeasShow.sharingModalTitle": "<PERSON><PERSON><PERSON>, ka iesniedzāt savu ideju!", "app.containers.Navbar.completeOnboarding": "Pilnīga uzņemšana da<PERSON>", "app.containers.Navbar.completeProfile": "Pilns profils", "app.containers.Navbar.confirmEmail2": "Apstiprināt e-pastu", "app.containers.Navbar.unverified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Navbar.verified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.beforeYouFollow": "Pirms sekojat", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON>rms dalības", "app.containers.NewAuthModal.completeYourProfile": "Aizpildiet savu profilu", "app.containers.NewAuthModal.confirmYourEmail": "Apstipriniet savu e-pasta adresi", "app.containers.NewAuthModal.logIn": "Piesakieties", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "<PERSON>, iepazīst<PERSON><PERSON> ar tā<PERSON><PERSON>k minēta<PERSON>em note<PERSON>.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "<PERSON><PERSON><PERSON><PERSON>, aizpildiet savu profilu.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Atgriezieties pie pieteikšanās opcijām", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Jums nav konta? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Reģistrējieties", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Kodam jābūt 4 cipariem.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Turpināt ar FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Šajā <PERSON>ā autentifikācijas metodes nav iespējotas.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON><PERSON><PERSON><PERSON>, jūs piekr<PERSON>tat saņemt e-pastus no šīs platformas. E-pasta vēstules, kuras vēlaties saņ<PERSON>, varat izvēlēties lapā \"Mani iestatījumi\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-pasts", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Norādiet e-pasta adresi pareizā formātā, piem<PERSON>ram, <EMAIL>.", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Sniedziet e-pasta adresi", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "<PERSON>, ievadiet savu e-pasta adresi.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Piesakieties savā kontā: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu paroli", "app.containers.NewAuthModal.steps.Password.password": "Parole", "app.containers.NewAuthModal.steps.Password.rememberMe": "Atceries mani", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ja izman<PERSON>jat publiski pieejamu datoru", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON>", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Tagad turpiniet piedalīties.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "<PERSON>ūsu identitāte ir pārb<PERSON>. Tagad jūs esat pilntiesīgs šīs platformas kopienas dalībnieks.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Jūs tagad esat pārbaud<PERSON>ts !", "app.containers.NewAuthModal.steps.close": "Aizvērt", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Kas jūs interesē?", "app.containers.NewAuthModal.youCantParticipate": "<PERSON><PERSON><PERSON> ne<PERSON> piedal<PERSON>", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {nav neizskatītu paziņojumu} one {1 neizskatīts paziņojums} other {# neredz<PERSON>ti paziņojumi}}", "app.containers.NotificationMenu.adminRightsReceived": "Tagad esat platformas administrators", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "<PERSON><PERSON><PERSON> koment<PERSON> par \"{postTitle}\" ir izdzēsis administrators, jo\n      {reasonCode, select, irrelevant {tas ir neatbil<PERSON>} inappropriate {tā saturs ir nepiem<PERSON>} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} <PERSON><PERSON><PERSON><PERSON> j<PERSON> lī<PERSON>ons<PERSON>a i<PERSON>.", "app.containers.NotificationMenu.deletedUser": "Nezināms autors", "app.containers.NotificationMenu.error": "Nevar<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} i<PERSON><PERSON><PERSON><PERSON> komentēja jums piešķirto ievadi.", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} i<PERSON><PERSON><PERSON><PERSON> komentēja ievades datus, kurus komentēja iekšēji.", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} i<PERSON><PERSON><PERSON><PERSON> komentēja jūsu vadītā projekta ievades datus.", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} i<PERSON><PERSON><PERSON><PERSON> komentēja nepiešķirtu ievadi neapsaimniekotā projektā.", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} koment<PERSON>ja jūsu iekš<PERSON> komentāru", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} a<PERSON><PERSON><PERSON><PERSON> jūs l<PERSON> ieguldījumu", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} a<PERSON><PERSON><PERSON><PERSON> jūs l<PERSON>t kādu ideju", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} aicin<PERSON><PERSON> jūs kļūt par priekš<PERSON>uma līdzpriekšsēdētāju.", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} aici<PERSON><PERSON><PERSON> jūs k<PERSON>t par kādu no jautājuma sponsoriem.", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} a<PERSON><PERSON><PERSON><PERSON> jūs l<PERSON> iespēju", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} aicin<PERSON>ja jūs k<PERSON>t par lūgumraksta līdzpriekšsēdētāju.", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} aicin<PERSON><PERSON> jūs k<PERSON>ūt par projekta līdzfinansētāju", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} aicin<PERSON><PERSON> jūs k<PERSON>t par priekš<PERSON>uma līdzfinansētāju.", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} aicin<PERSON>ja jūs k<PERSON> par jautā<PERSON><PERSON> lī<PERSON>", "app.containers.NotificationMenu.loadMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vēl...", "app.containers.NotificationMenu.loading": "Notiek paziņojumu i<PERSON>...", "app.containers.NotificationMenu.mentionInComment": "{name} pie<PERSON><PERSON><PERSON> jūs koment<PERSON>", "app.containers.NotificationMenu.mentionInInternalComment": "{name} pie<PERSON><PERSON><PERSON> jūs i<PERSON> komentārā", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} pie<PERSON><PERSON><PERSON> jūs of<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "<PERSON><PERSON><PERSON> savu apta<PERSON>", "app.containers.NotificationMenu.noNotifications": "Jums vēl nav neviena paziņ<PERSON>", "app.containers.NotificationMenu.notificationsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} snied<PERSON> <PERSON><PERSON><PERSON><PERSON> atjauninājumu par ieguldījumu, kuram jūs seko<PERSON>t.", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} snied<PERSON> <PERSON><PERSON><PERSON><PERSON> at<PERSON>uni<PERSON>jumu par ideju, kurai jūs seko<PERSON>t.", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} snied<PERSON> <PERSON><PERSON><PERSON><PERSON> atjauninātu informāciju par iniciatīvu, kurai jūs sekojat.", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} snied<PERSON> <PERSON><PERSON><PERSON><PERSON> atjauninātu informāciju par jautājumu, kuru jūs seko<PERSON>t.", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} snied<PERSON> <PERSON><PERSON><PERSON><PERSON> atjauninājumu par iespēju, kurai jūs sekojat", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} snie<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> at<PERSON>uni<PERSON>jumu par lū<PERSON>, kuram jūs seko<PERSON>t.", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} snie<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> atjauninājumu par projektu, kuram jūs seko<PERSON>t.", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} snie<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> at<PERSON>uninājumu par p<PERSON>, kuram jūs seko<PERSON>t.", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} snie<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> at<PERSON>unin<PERSON>jumu par jautāju<PERSON>, kuru jūs seko<PERSON>t", "app.containers.NotificationMenu.postAssignedToYou": "jums tika piešķirts {postTitle}", "app.containers.NotificationMenu.projectModerationRightsReceived": "<PERSON><PERSON><PERSON> tagad esat {projectLink} projekta vadīt<PERSON>js", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} uzsākts jauns posms", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} s<PERSON><PERSON> jaunu posmu {phaseStartAt}.", "app.containers.NotificationMenu.projectPublished": "Publicēts jauns projekts", "app.containers.NotificationMenu.projectReviewRequest": "{name} piepra<PERSON><PERSON><PERSON> apstiprinājumu publicēt projektu \"{projectTitle}\".", "app.containers.NotificationMenu.projectReviewStateChange": "{name} apstiprināts \"{projectTitle}\" publicēšanai", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} statuss ir mainīts uz {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} sasniedza balsošanas slieksni", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} <PERSON><PERSON><PERSON><PERSON> j<PERSON>", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} koment<PERSON>ja kādu jūsu sekotu ziņu", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} k<PERSON><PERSON><PERSON><PERSON> idej<PERSON>, kurai jūs se<PERSON>t", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} k<PERSON><PERSON><PERSON><PERSON> iniciat<PERSON>, kurai jūs seko<PERSON>t.", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} k<PERSON>nt<PERSON><PERSON>, kuram jūs se<PERSON>t", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} koment<PERSON><PERSON>, ka jums seko", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} koment<PERSON><PERSON> l<PERSON>, kuram jūs se<PERSON>t", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} koment<PERSON>ja kādu no projektiem, kam jūs seko<PERSON>t.", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} koment<PERSON><PERSON>, kuram jūs <PERSON>.", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} k<PERSON><PERSON><PERSON><PERSON>, kas jums seko", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} z<PERSON><PERSON><PERSON> \"{postTitle}\" kā surogā<PERSON>tu", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reaģēja uz jūsu komentāru", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} zi<PERSON><PERSON> komentāru par \"{postTitle}\" kā surogātpastu", "app.containers.NotificationMenu.votingBasketNotSubmitted": "<PERSON><PERSON><PERSON> savas balsis", "app.containers.NotificationMenu.votingBasketSubmitted": "<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.votingLastChance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON>ē<PERSON> balsot par {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} at<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> rezultāti", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} piešķīra jums {postTitle}", "app.containers.PasswordRecovery.emailError": "<PERSON><PERSON> ne<PERSON>s pēc derīga e-pasta", "app.containers.PasswordRecovery.emailLabel": "E-pasts", "app.containers.PasswordRecovery.emailPlaceholder": "Mana e-pasta adrese", "app.containers.PasswordRecovery.helmetDescription": "Atiestatīt jūsu paroles lapu", "app.containers.PasswordRecovery.helmetTitle": "Atiestat<PERSON><PERSON> jū<PERSON> paroli", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Ja šī e-pasta adrese ir reģistrēta platformā, ir nosūtīta paroles atiestatīšanas saite.", "app.containers.PasswordRecovery.resetPassword": "Nosūtīt paroles atiestatīšanas saiti", "app.containers.PasswordRecovery.submitError": "<PERSON>ēs <PERSON>m atrast kontu, kas būtu saistīts ar šo e-pastu. Tā vietā varat mēģināt reģistrēties.", "app.containers.PasswordRecovery.subtitle": "Kur varam nosūt<PERSON>t <PERSON>iti, lai izv<PERSON><PERSON><PERSON>tos jaunu paroli?", "app.containers.PasswordRecovery.title": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.helmetDescription": "Atiestatīt jūsu paroles lapu", "app.containers.PasswordReset.helmetTitle": "Atiestat<PERSON><PERSON> jū<PERSON> paroli", "app.containers.PasswordReset.login": "Piesakieties", "app.containers.PasswordReset.passwordError": "Parolei jābūt vismaz 8 rakstu zīmju garai", "app.containers.PasswordReset.passwordLabel": "Parole", "app.containers.PasswordReset.passwordPlaceholder": "Jaunā parole", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON><PERSON><PERSON> ir ve<PERSON>.", "app.containers.PasswordReset.pleaseLogInMessage": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> jauno paroli.", "app.containers.PasswordReset.requestNewPasswordReset": "<PERSON><PERSON><PERSON><PERSON><PERSON> jaunas paroles at<PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordReset.submitError": "<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.PasswordReset.title": "Atiestat<PERSON><PERSON> jū<PERSON> paroli", "app.containers.PasswordReset.updatePassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jauno paroli", "app.containers.ProjectFolderCards.allProjects": "Visi projekti", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} pa<PERSON><PERSON><PERSON> strādā pie", "app.containers.ProjectFolderShowPage.editFolder": "Rediģēt mapi", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informācija par šo projektu", "app.containers.ProjectFolderShowPage.metaTitle1": "mape: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.share": "Koplietot", "app.containers.Projects.PollForm.document": "Dokuments", "app.containers.Projects.PollForm.formCompleted": "Paldies! <PERSON><PERSON><PERSON> atbilde ir sa<PERSON>.", "app.containers.Projects.PollForm.maxOptions": "maks. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "<PERSON><PERSON><PERSON> jau esat piedalījies šajā aptaujā.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "<PERSON><PERSON> aptauju var veikt tikai tad, kad šis posms ir aktīvs.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Šī aptauja pašlaik nav iespējota", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Šobrīd nav iespējams veikt šo aptauju.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Apta<PERSON>ja vairs nav pieejama, jo šis projekts vairs nav aktīvs.", "app.containers.Projects.PollForm.sendAnswer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_phase": "Fāze {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_titleInputs": "Viss šim projektam iesniegtais ieguldījums", "app.containers.Projects.a11y_titleInputsPhase": "<PERSON><PERSON> šajā posmā sniegtais ieguldījums", "app.containers.Projects.accessRights": "Piek<PERSON><PERSON><PERSON>", "app.containers.Projects.addedToBasket": "Pievienots jūsu grozam", "app.containers.Projects.allocateBudget": "Piešķiriet savu budžetu", "app.containers.Projects.archived": "Arhivēts", "app.containers.Projects.basketSubmitted": "<PERSON><PERSON><PERSON> groz<PERSON> ir iesniegts!", "app.containers.Projects.contributions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.createANewPhase": "Izveidot jaunu posmu", "app.containers.Projects.currentPhase": "Pašreizējais posms", "app.containers.Projects.document": "Dokuments", "app.containers.Projects.editProject": "Rediģēt projektu", "app.containers.Projects.emailSharingBody": "Ko jūs domā<PERSON>t par šo iniciatīvu? Balsojiet par to un koplietojiet diskusiju {initiativeUrl}, lai jūsu viedoklis tiktu sadzird<PERSON>!", "app.containers.Projects.emailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> manu iniciatīvu: {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.Projects.events": "<PERSON><PERSON><PERSON>", "app.containers.Projects.header": "Projekti", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informācija", "app.containers.Projects.initiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Dokumenta pārskatīšana", "app.containers.Projects.invisibleTitlePhaseAbout": "Par <PERSON>o posmu", "app.containers.Projects.invisibleTitlePoll": "Veikt aptauju", "app.containers.Projects.invisibleTitleSurvey": "Aizpildiet aptauju", "app.containers.Projects.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "<PERSON><PERSON><PERSON> skatā<PERSON> reā<PERSON>ika datus. Dalībnieku skaits administratoriem tiek pastāvīgi atjaunināts. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vēr<PERSON>, ka parastie lietotāji redz kešētos datus, tā<PERSON><PERSON><PERSON> skaitļi var nedaudz atšķirties.", "app.containers.Projects.location": "Atraša<PERSON><PERSON><PERSON> vieta:", "app.containers.Projects.manageBasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>zu", "app.containers.Projects.meetMinBudgetRequirement": "Nodrošiniet atbilstību minimālajam budžetam, lai iesniegtu savu grozu.", "app.containers.Projects.meetMinSelectionRequirement": "Nodrošiniet atbilstību nepieciešamajai atlasei, lai iesniegtu savu grozu.", "app.containers.Projects.metaTitle1": "Projekts: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "<PERSON><PERSON>ie<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.myBasket": "Basket", "app.containers.Projects.navPoll": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.newPhase": "Jauns posms", "app.containers.Projects.nextPhase": "Nākamais posms", "app.containers.Projects.noEndDate": "Beigu datums nav norādīts", "app.containers.Projects.noItems": "<PERSON><PERSON><PERSON> vēl neesat atlas<PERSON>jis nevienu vienumu", "app.containers.Projects.noPastEvents": "Nav attē<PERSON>jamu pagātnes notikumu", "app.containers.Projects.noPhaseSelected": "Nav izvēlēts neviens posms", "app.containers.Projects.noUpcomingOrOngoingEvents": "Šobrīd gra<PERSON> nav iekļauti gaidāmie vai aktuālie notikumi.", "app.containers.Projects.offlineVotersTooltip": "<PERSON><PERSON> skaitl<PERSON> nek<PERSON> be<PERSON>sai<PERSON> balsotāju skaitī<PERSON>.", "app.containers.Projects.options": "<PERSON>es<PERSON>ējas", "app.containers.Projects.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.participantsTooltip4": "<PERSON><PERSON> skaitlis atspoguļo arī anonī<PERSON> aptaujas iesniegumus. Anonīmi aptauju iesniegšana ir iesp<PERSON>, ja aptaujas ir pieejamas ikvie<PERSON> (skat<PERSON>t šā projekta cilni {accessRightsLink}).", "app.containers.Projects.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON> notik<PERSON>", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "<PERSON><PERSON><PERSON>", "app.containers.Projects.previousPhase": "Iepriekšējais posms", "app.containers.Projects.project": "Projekts", "app.containers.Projects.projectTwitterMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka jūsu viedoklis tiek sadzirdēts! Piedalieties {projectName} | {orgName}", "app.containers.Projects.projects": "Projekti", "app.containers.Projects.proposals": "Priekšlikumi", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readMore": "<PERSON><PERSON><PERSON>", "app.containers.Projects.removeItem": "Noņemt vienumu", "app.containers.Projects.requiredSelection": "Nepieciešamā atlase", "app.containers.Projects.reviewDocument": "Dokumenta pārskatīšana", "app.containers.Projects.seeTheContributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON><PERSON><PERSON> id<PERSON>", "app.containers.Projects.seeTheInitiatives": "Skatiet iniciatīvas", "app.containers.Projects.seeTheIssues": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "app.containers.Projects.seeTheOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeThePetitions": "Skatiet lūgumrakstus", "app.containers.Projects.seeTheProjects": "<PERSON><PERSON><PERSON><PERSON> projektus", "app.containers.Projects.seeTheProposals": "Skatiet priekšlikumus", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeUpcomingEvents": "Skatiet gaidāmos p<PERSON>", "app.containers.Projects.share": "Koplietot", "app.containers.Projects.shareThisProject": "Koplietot šo projektu", "app.containers.Projects.submitMyBasket": "Submit basket", "app.containers.Projects.survey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.takeThePoll": "Veikt aptauju", "app.containers.Projects.takeTheSurvey": "Aizpildiet aptauju", "app.containers.Projects.timeline": "Laika līnija", "app.containers.Projects.upcomingAndOngoingEvents": "Gaidāmie un aktuālie notikumi", "app.containers.Projects.upcomingEvents": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.whatsAppMessage": "{projectName} | no {orgName} līdzdalības platformas", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "Iepazīstieties ar visiem {orgName} aktu<PERSON><PERSON><PERSON><PERSON> projektiem, lai sapra<PERSON>u, kā jūs varat tajos piedalīties.\n Nāciet apspriest vietējos projektus, kas jums ir vissvar<PERSON>.", "app.containers.ProjectsIndexPage.metaTitle1": "Projekti | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projekti", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON><PERSON> v<PERSON> k<PERSON> par brīvprātīgo", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "<PERSON> pieteiktos brīvprātīgajam darbam šajā aktivitātē, lū<PERSON><PERSON>, vispirms {signInLink} vai {signUpLink}.", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "<PERSON><PERSON><PERSON><PERSON> š<PERSON>ā aktivitātē nav iespējams piedalīties.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "pieraks<PERSON>ī<PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "reģistrēties", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Es atsaucu savu brīvprātīgā darba piedāvājumu", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nav brīvprātīgo} one {# brīvprātīgais} other {# brīvprātīgie}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Brīdinājums: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> apsekoju<PERSON>ā var būt problēmas ar ekrānlasītāju lietotāju piekļuvi. <PERSON>a rodas problē<PERSON>, <PERSON><PERSON><PERSON><PERSON>, sazin<PERSON>ies ar platformas administratoru, lai saņemtu saiti uz aptauju no sākotnējās platformas. Varat ar<PERSON> pieprasīt citus veidus, kā aizpildīt aptauju.", "app.containers.ProjectsShowPage.process.survey.survey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "<PERSON>, vai varat piedalīties šajā pēt<PERSON>, vispi<PERSON> lūdzu, {logInLink} platformai.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "<PERSON><PERSON> pētīju<PERSON> var veikt tikai tad, kad šis laika līnijas posms ir aktīvs.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "<PERSON><PERSON><PERSON><PERSON>, {completeRegistrationLink} , lai piedal<PERSON> aptauj<PERSON>.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Šis pētījums pašlaik nav aktivizēts", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "<PERSON> pie<PERSON>, ir jā<PERSON><PERSON><PERSON><PERSON>a jūsu identitāte. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Apta<PERSON>ja vairs nav pieejama, jo šis projekts vairs nav aktīvs.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "pabe<PERSON>t reģistrāciju", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "pieteikties", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "reģistrēties", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Tagad pārbaudiet savu kontu.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Šo dokumentu var pārskatīt tikai noteikti lietotāji. <PERSON><PERSON><PERSON><PERSON>, vispirms rakstiet uz {signUpLink} vai {logInLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Šo dokumentu var pārskatīt tikai tad, kad šis posms ir aktīvs.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "<PERSON><PERSON><PERSON><PERSON>, {completeRegistrationLink} , lai pārskat<PERSON>tu dokumentu.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Diemžēl jums nav tiesību pārskatīt šo dokumentu.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "<PERSON> do<PERSON>, ir <PERSON><PERSON><PERSON><PERSON><PERSON> jūsu konta verifikācija. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Dokuments vairs nav pieejams, jo šis projekts vairs nav aktīvs.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(ieskaitot 1 bezsaistē)} other {(ieskaitot # bezsaistē)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "To dal<PERSON><PERSON><PERSON>ku procentu<PERSON><PERSON><PERSON>, kuri izv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>ēju.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Procentu<PERSON><PERSON><PERSON> daļa no kopējā balsu skaita, ko saņēma š<PERSON> iespēja.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Izmaksas:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ReactionControl.a11y_likesDislikes": "<PERSON><PERSON><PERSON>k: {likesCount}, k<PERSON><PERSON><PERSON><PERSON> skaits nepat<PERSON>k: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "<PERSON><PERSON><PERSON> ve<PERSON> atcēlāt savu nepatiku pret šo ievadi.", "app.containers.ReactionControl.cancelLikeSuccess": "<PERSON><PERSON><PERSON> ve<PERSON> atc<PERSON>l<PERSON>t savu simpātiju šim ievadam.", "app.containers.ReactionControl.dislikeSuccess": "Jums šis ievads veiksmīgi nepatika.", "app.containers.ReactionControl.likeSuccess": "Jums patika šis ievade ve<PERSON>gi.", "app.containers.ReactionControl.reactionErrorSubTitle": "<PERSON><PERSON><PERSON><PERSON> dēļ jūsu reakciju nevarēja reģistrēt. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz pēc dažām minūtēm.", "app.containers.ReactionControl.reactionSuccessTitle": "<PERSON><PERSON><PERSON> reakcija tika veiksmīgi reģistrēta!", "app.containers.ReactionControl.vote": "Balsojums", "app.containers.ReactionControl.voted": "Balsots", "app.containers.SearchInput.a11y_cancelledPostingComment": "Atcelts norīkošanu komentāru.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} koment<PERSON><PERSON> ir i<PERSON>.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# notikumi ir ielādēti} one {# notikums ir ielādēts} other {# notikumi ir ielādēti}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# rezultāti ir ielādēti} one {# rezultāts ir ielādēts} other {# rezultāti ir ielādēti}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# mekl<PERSON>šanas rezultāti ir ielādēti} one {# mekl<PERSON>šanas rezultāts ir ielādēts} other {# meklēšanas rezultāti ir ielādēti}}.", "app.containers.SearchInput.removeSearchTerm": "Noņemt meklēšanas terminu", "app.containers.SearchInput.searchAriaLabel": "Meklēt", "app.containers.SearchInput.searchLabel": "Meklēt", "app.containers.SearchInput.searchPlaceholder": "Meklēt", "app.containers.SearchInput.searchTerm": "Mekl<PERSON><PERSON>nas termins: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect ir Francijas valsts piedāvātais risin<PERSON>, lai nodrošinātu un vienkāršotu reģistrēšanos vairāk nekā 700 tiešsaistes pakalpojumiem.", "app.containers.SignIn.or": "Vai", "app.containers.SignIn.signInError": "Sniegtā informācija nav pareiza. <PERSON> paroli, nospiediet uz “Aizmir<PERSON> paroli?”.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "<PERSON><PERSON><PERSON><PERSON> FranceConnect, la<PERSON>, reģistrētos vai pārbaud<PERSON>tu savu kontu.", "app.containers.SignIn.whatIsFranceConnect": "Kas ir France Connect?", "app.containers.SignUp.adminOptions2": "Administratoriem un projektu vadī<PERSON>ājiem", "app.containers.SignUp.backToSignUpOptions": "Atgriezties pie reģistrēšanās iespējām", "app.containers.SignUp.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.emailConsent": "Reģistrējoties jūs piekrītat saņemt e-pasta ziņojumus no šīs platformas. La<PERSON><PERSON> \"Mani iestatījumi\" jūs varat izvēlēties, kurus e-pastus vēlaties saņemt.", "app.containers.SignUp.emptyFirstNameError": "Ievadiet savu vārdu", "app.containers.SignUp.emptyLastNameError": "Ievadiet savu uzvārdu", "app.containers.SignUp.firstNamesLabel": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.goToLogIn": "Ju<PERSON> jau ir konts? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "<PERSON><PERSON><PERSON> un piekrī<PERSON> {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "<PERSON><PERSON><PERSON> un piekrī<PERSON> {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON>, ka visi dati tiks izmantoti mitgestalten.wien.gv.at. Sīkāka informācija pieejama {link}.", "app.containers.SignUp.invitationErrorText": "<PERSON><PERSON><PERSON> ielūguma derīguma termiņš ir beidzies vai tas jau ir izmanto<PERSON>. Ja esat jau izmantojis uzaicin<PERSON>ju<PERSON> saiti, lai izveidotu kontu, mēģiniet pierakstīties. Pretējā gadījumā reģistrējieties, lai izveidotu jaunu kontu.", "app.containers.SignUp.lastNameLabel": "Uzvārds", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Se<PERSON><PERSON>et savām u<PERSON> jomām, lai saņ<PERSON> paziņojumus par tām:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Se<PERSON><PERSON>et savām iecienītākajām tēmām, lai saņemtu paziņojumus par tām:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "<PERSON>g<PERSON><PERSON><PERSON><PERSON> preferences", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.privacyPolicyNotAcceptedError": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mū<PERSON> privātuma politikai", "app.containers.SignUp.signUp2": "Reģistrēties", "app.containers.SignUp.skip": "<PERSON><PERSON><PERSON><PERSON> soli", "app.containers.SignUp.tacError": "<PERSON>, pie<PERSON><PERSON><PERSON><PERSON><PERSON> mūsu noteikumiem un nosacījumiem", "app.containers.SignUp.thePrivacyPolicy": "privātuma politika", "app.containers.SignUp.theTermsAndConditions": "<PERSON><PERSON><PERSON> un nosacījumi", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {Šķiet, ka jūs mēģinājāt reģistrēties iep<PERSON>k<PERSON>, nepabeid<PERSON>t procesu. Tā vietā noklikšķiniet uz Log In, izmanto<PERSON>t iepriekšējā mēģinājumā izvēlētos akreditācijas datus.} other {Kaut kas ir noticis nepareizi. <PERSON>ū<PERSON><PERSON>, mēģiniet vēlreiz vēlāk.}}", "app.containers.SignUp.viennaConsentEmail": "E-pasta adrese", "app.containers.SignUp.viennaConsentFirstName": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.viennaConsentFooter": "<PERSON><PERSON><PERSON> pier<PERSON>šan<PERSON>s jūs varat mainīt savu profila informāciju. Ja jums vietnē mitgestalten.wien.gv.at jau ir konts ar to pašu e-pasta adresi, tas tiks piesaistīts jūsu pašreizējam kontam.", "app.containers.SignUp.viennaConsentHeader": "Tiks pā<PERSON><PERSON><PERSON>ti šādi dati:", "app.containers.SignUp.viennaConsentLastName": "Uzvārds", "app.containers.SignUp.viennaConsentUserName": "Lietotājvārds", "app.containers.SignUp.viennaDataProtection": "vienna privātuma politika", "app.containers.SiteMap.contributions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.cookiePolicyLinkTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.options": "<PERSON>es<PERSON>ējas", "app.containers.SiteMap.projects": "Projekti", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Veiksmīgi", "app.containers.SpamReport.inappropriate": "<PERSON>s ir nepiemērots vai a<PERSON>skar<PERSON>š<PERSON>", "app.containers.SpamReport.messageError": "Veidlapas i<PERSON>gša<PERSON> brīdī radā<PERSON>, l<PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "app.containers.SpamReport.messageSuccess": "<PERSON><PERSON><PERSON> z<PERSON> ir nosūt<PERSON>", "app.containers.SpamReport.other": "Cits iemesls", "app.containers.SpamReport.otherReasonPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "Tas nav saist<PERSON>ts", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Noņemt profila att<PERSON>lu", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "<PERSON><PERSON>su balsis par priek<PERSON><PERSON>umiem, par kuriem vēl ir iesp<PERSON><PERSON><PERSON> balsot, tiks dzēstas. Balsis par priekšlikumiem, par kuriem balso<PERSON> periods ir be<PERSON><PERSON><PERSON>, netiks dzēsta<PERSON>.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Piedal<PERSON><PERSON> projektos, kam nepie<PERSON>ma verifikācija.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Pārbaudiet savu identitāti", "app.containers.UsersEditPage.bio": "Par jums", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "<PERSON>o lauku jūs ne<PERSON> rediģēt, jo tas satur apstiprin<PERSON><PERSON>.", "app.containers.UsersEditPage.buttonSuccessLabel": "Veiksmīgi", "app.containers.UsersEditPage.cancel": "Atcelt", "app.containers.UsersEditPage.changeEmail": "Mainīt e-pastu", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON><PERSON><PERSON><PERSON>, noklikšķiniet šeit, lai at<PERSON>unin<PERSON>tu savu pārbaudi.", "app.containers.UsersEditPage.conditionsLinkText": "m<PERSON><PERSON> no<PERSON>", "app.containers.UsersEditPage.contactUs": "Vēl viens iemesls doties prom? {feedbackLink}, un varbūt mēs varēsim palīdzēt.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON>, ka jūs dodaties prom.", "app.containers.UsersEditPage.deleteMyAccount": "<PERSON><PERSON><PERSON><PERSON> manu kontu", "app.containers.UsersEditPage.deleteYourAccount": "Dzēst jū<PERSON> kontu", "app.containers.UsersEditPage.deletionSection": "Dzēst jū<PERSON> kontu", "app.containers.UsersEditPage.deletionSubtitle": "<PERSON>o darb<PERSON>bu nevar atsaukt. Jūsu platformā publicētais saturs tiks anonimizēts. Ja vēlaties dzēst visu savu saturu, varat sazināties ar mums pa e-pastu <EMAIL>.", "app.containers.UsersEditPage.email": "E-pasts", "app.containers.UsersEditPage.emailEmptyError": "Norādīt e-pasta adresi", "app.containers.UsersEditPage.emailInvalidError": "Norādīt e-pasta adresi pareizā formātā, piem<PERSON>ram, <EMAIL>.", "app.containers.UsersEditPage.feedbackLinkText": "Informējiet mūs", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON><PERSON><PERSON><PERSON> vārdu", "app.containers.UsersEditPage.h1": "<PERSON><PERSON><PERSON> k<PERSON>", "app.containers.UsersEditPage.h1sub": "Rediģēt jūsu konta <PERSON>", "app.containers.UsersEditPage.image": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Noklikšķiniet, lai atlas<PERSON>tu profila attēlu (ne lielāku par 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Visi jūsu profila i<PERSON>", "app.containers.UsersEditPage.language": "Valoda", "app.containers.UsersEditPage.lastName": "Uzvārds", "app.containers.UsersEditPage.lastNameEmptyError": "<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>", "app.containers.UsersEditPage.loading": "Notiek iel<PERSON>de...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Šeit varat mainīt savu e-pasta adresi vai paroli.", "app.containers.UsersEditPage.loginCredentialsTitle": "Pie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> dati", "app.containers.UsersEditPage.messageError": "<PERSON><PERSON><PERSON> ne<PERSON>m saglabāt jūsu profilu. Mēģiniet vēlāk vēlreiz vai <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON><PERSON><PERSON> profils ir saglab<PERSON>.", "app.containers.UsersEditPage.metaDescription": "<PERSON>ī ir {firstName} {lastName} profila iestatīju<PERSON> lapa {tenantName} tiešsaistes līdzdalības platformā. Šeit varat pārbaudīt savu identitāti, rediģēt konta informāciju, dzēst kontu un rediģēt e-pasta preferences.", "app.containers.UsersEditPage.metaTitle1": "Profila i<PERSON>ījumu lapa {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Tiklīdz noklikšķināsiet uz šīs pogas, mēs nevarēsim atja<PERSON>t jūsu kontu.", "app.containers.UsersEditPage.noNameWarning2": "Pašlaik platformā jūsu vārds un uzvārds tiek parādīts kā: \"{displayName}\", jo neesat ievadījis savu vārdu. Tas ir automātiski ģenerēts vārds. Ja vēlaties to main<PERSON><PERSON>, lū<PERSON><PERSON>, ievadiet savu vārdu zemāk.", "app.containers.UsersEditPage.notificationsSubTitle": "Kādus e-pasta paziņojumus vēlaties saņemt? ", "app.containers.UsersEditPage.notificationsTitle": "E-pasta paziņ<PERSON>ju<PERSON>", "app.containers.UsersEditPage.password": "Izvēlieties jaunu paroli", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON><PERSON><PERSON><PERSON>, kas ir vismaz {minimumPasswordLength} r<PERSON><PERSON> z<PERSON> gara", "app.containers.UsersEditPage.passwordAddSection": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.passwordAddSubtitle2": "Iestatiet paroli un viegli autorizējieties platformā, katru reizi neapstiprinot savu e-pasta adresi.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.passwordChangeSubtitle": "Apstipriniet pašreizējo paroli un nomainiet to uz jaunu paroli.", "app.containers.UsersEditPage.privacyReasons": "Ja uztraucaties par savu konfiden<PERSON>, varat i<PERSON>t {conditionsLink}.", "app.containers.UsersEditPage.processing": "Nos<PERSON><PERSON>...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "<PERSON><PERSON><PERSON> ir oblig<PERSON>ts, ja tiek norād<PERSON>ts uzvārds", "app.containers.UsersEditPage.reasonsToStayListTitle": "Pirms dodaties...", "app.containers.UsersEditPage.submit": "Saglabāt iz<PERSON>", "app.containers.UsersEditPage.tooManyEmails": "Saņ<PERSON>t pārāk daudz e-pasta vēstuļu? Savas e-pasta preferences varat pārvaldīt profila iestatīju<PERSON>.", "app.containers.UsersEditPage.updateverification": "<PERSON>ai jūsu ofici<PERSON>ā informācija ir mainījusies? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON> v<PERSON><PERSON>, lai mēs jums nosūtām e-pasta vēstuli ar paziņojumu?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "<PERSON><PERSON><PERSON> projekt<PERSON>, kuro<PERSON>.", "app.containers.UsersEditPage.verifiedIdentityTitle": "<PERSON><PERSON><PERSON> es<PERSON>", "app.containers.UsersEditPage.verifyNow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Lejupielādēji<PERSON> savas atbildes (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {nav patīk} one {1 patīk} other {# patīk}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ka <PERSON>is komentārs tika public<PERSON>ts, atbildot uz:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "<PERSON><PERSON><PERSON><PERSON> ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Rediģēt manu profilu", "app.containers.UsersShowPage.emptyInfoText": "<PERSON><PERSON><PERSON> neseko<PERSON>t nevienam no iepriekš norādītā filtra elementiem.", "app.containers.UsersShowPage.eventsWithCount": "<PERSON><PERSON><PERSON> ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Pē<PERSON> ({followingCount})", "app.containers.UsersShowPage.inputs": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.invisibleTitlePostsList": "<PERSON><PERSON> <PERSON><PERSON>nieka sniegtais ieguldījums", "app.containers.UsersShowPage.invisibleTitleUserComments": "<PERSON><PERSON> šī dalībnieka publicētie komentāri", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.loadMoreComments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vair<PERSON>k koment<PERSON>", "app.containers.UsersShowPage.loadingComments": "Notiek komentāru i<PERSON>...", "app.containers.UsersShowPage.loadingEvents": "Notikumu <PERSON>...", "app.containers.UsersShowPage.memberSince": "Dalīb<PERSON>ks kopš {datums}", "app.containers.UsersShowPage.metaTitle1": "<PERSON><PERSON> lapa {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "<PERSON><PERSON> persona vēl nav publicē<PERSON>si komentā<PERSON>.", "app.containers.UsersShowPage.noCommentsForYou": "Šeit vēl nav komentāru.", "app.containers.UsersShowPage.noEventsForUser": "<PERSON><PERSON><PERSON> v<PERSON>l neesat apm<PERSON>l<PERSON><PERSON>s nevienu p<PERSON>.", "app.containers.UsersShowPage.postsWithCount": "<PERSON><PERSON>niegumi ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projektu mapes", "app.containers.UsersShowPage.projects": "Projekti", "app.containers.UsersShowPage.proposals": "Priekšlikumi", "app.containers.UsersShowPage.seePost": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.surveyResponses": "Atbildes ({responses})", "app.containers.UsersShowPage.topics": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.tryAgain": "Ra<PERSON><PERSON> k<PERSON>, l<PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.UsersShowPage.userShowPageMetaDescription": "<PERSON>ī ir {firstName} {lastName} profila lapa {orgName} tiešsaistes līdzdalības platformā. Šeit ir sniegts pārskats par visu viņu ieguldījumu.", "app.containers.VoteControl.close": "Aizvērt", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageTextCards": "Attēlu un teksta kartes", "app.containers.admin.ContentBuilder.infoWithAccordions": "Informācija un akordeoni", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 sleja", "app.containers.admin.ContentBuilder.projectDescription": "Projekta apraksts", "app.containers.app.navbar.admin": "Pārvaldīt platformu", "app.containers.app.navbar.allProjects": "Visi projekti", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Aizvērt mobilās navigācijas izvēlni", "app.containers.app.navbar.editProfile": "<PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Pilns mobilais", "app.containers.app.navbar.logIn": "Pierakstī<PERSON>", "app.containers.app.navbar.logoImgAltText": "{orgName} Sākums", "app.containers.app.navbar.myProfile": "<PERSON><PERSON>", "app.containers.app.navbar.search": "Meklēt", "app.containers.app.navbar.showFullMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> i<PERSON>i", "app.containers.app.navbar.signOut": "Izrakstīties", "app.containers.eventspage.errorWhenFetchingEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet pārlād<PERSON>t lapu.", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "Rā<PERSON>īt visus {orgName} platformā publicētos notikumus.", "app.containers.eventspage.eventsPageTitle1": "Notikumi | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projekti", "app.containers.eventspage.noPastEvents": "Nav attē<PERSON>jamu pagātnes notikumu", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Šobrīd gra<PERSON> nav iekļauti gaidāmie vai aktuālie notikumi.", "app.containers.eventspage.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON> notik<PERSON>", "app.containers.eventspage.upcomingAndOngoingEvents": "Gaidāmie un aktuālie notikumi", "app.containers.footer.accessibility-statement": "Paziņojums par pieejamību", "app.containers.footer.ariaLabel": "Sekundārais", "app.containers.footer.cookie-policy": "Sīkdatņu politika", "app.containers.footer.cookieSettings": "<PERSON><PERSON>k<PERSON><PERSON><PERSON><PERSON> i<PERSON>īju<PERSON>", "app.containers.footer.feedbackEmptyError": "Atsauksm<PERSON> lauks nedr<PERSON> b<PERSON>t tuk<PERSON>.", "app.containers.footer.poweredBy": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.privacy-policy": "Privātuma politika", "app.containers.footer.siteMap": "Vietnes karte", "app.containers.footer.terms-and-conditions": "<PERSON><PERSON><PERSON> un nosacījumi", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Atcelt", "app.containers.ideaHeading.confirmLeaveFormButtonText": "Jā, es gribu aizbraukt", "app.containers.ideaHeading.editForm": "Rediģēt veidlapu", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Vai esat pārl<PERSON>, ka vēlaties doties prom?", "app.containers.ideaHeading.leaveIdeaForm": "<PERSON><PERSON><PERSON><PERSON> id<PERSON> ve<PERSON>", "app.containers.ideaHeading.leaveIdeaText": "<PERSON><PERSON><PERSON> atbildes netiks saglabātas.", "app.containers.landing.cityProjects": "Projekti", "app.containers.landing.completeProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> savu profilu", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON> l<PERSON>, {firstName}. Ir pien<PERSON><PERSON> la<PERSON>s a<PERSON>t savu profilu.", "app.containers.landing.createAccount": "Reģistrēties", "app.containers.landing.defaultSignedInMessage": "{orgName} jūs u<PERSON>. <PERSON>ad ir jūsu kā<PERSON>, lai jūsu viedok<PERSON> tiktu sadzird<PERSON>!", "app.containers.landing.doItLater": "Es to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>k", "app.containers.landing.new": "jauns", "app.containers.landing.subtitleCity": "Laipni lūgti {orgName} līdzdalības platformā", "app.containers.landing.titleCity": "<PERSON><PERSON><PERSON><PERSON> {orgName} nākotni kopā", "app.containers.landing.twitterMessage": "Balsot par {ideaTitle}", "app.containers.landing.upcomingEventsWidgetTitle": "Gaidāmie un aktuālie notikumi", "app.containers.landing.userDeletedSubtitle": "<PERSON><PERSON><PERSON> jeb<PERSON>r<PERSON> laikā varat izveidot jaunu kontu vai arī {contactLink}, lai paziņotu mums, ko mēs varam u<PERSON>.", "app.containers.landing.userDeletedSubtitleLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON> mums", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON><PERSON> k<PERSON> ir d<PERSON>.", "app.containers.landing.userDeletionFailed": "<PERSON><PERSON><PERSON> k<PERSON>a d<PERSON> laikā radā<PERSON>, mēs esam informēti par problēmu un darīsim visu iespējamo, lai to novērstu. <PERSON><PERSON><PERSON><PERSON>, vēlāk mēģiniet vēlreiz.", "app.containers.landing.verifyNow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.landing.verifyYourIdentity": "Pārbaudiet savu identitāti", "app.containers.landing.viewAllEventsText": "<PERSON><PERSON><PERSON><PERSON> visus notikumus", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Atpakaļ uz mapi", "app.errors.after_end_at": "<PERSON><PERSON><PERSON><PERSON> datums ir pēc beigu datuma", "app.errors.avatar_carrierwave_download_error": "Nevar<PERSON><PERSON> le<PERSON> avatāra <PERSON>u.", "app.errors.avatar_carrierwave_integrity_error": "<PERSON><PERSON><PERSON><PERSON> fails nav atļaut<PERSON> tipa.", "app.errors.avatar_carrierwave_processing_error": "<PERSON>eva<PERSON><PERSON><PERSON> a<PERSON> a<PERSON>.", "app.errors.avatar_extension_blacklist_error": "<PERSON><PERSON><PERSON><PERSON> attēla faila paplašinājums nav atļauts. Atļautie paplašinājuma veidi ir: jpg, jpeg, gif un png.", "app.errors.avatar_extension_whitelist_error": "<PERSON><PERSON><PERSON><PERSON> attēla faila paplašinājums nav atļauts. Atļautie paplašinājuma veidi ir: jpg, jpeg, gif un png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> te<PERSON>.", "app.errors.banner_cta_button_url_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_button_url_url": "Ievadīt derīgu saiti. P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka saite sākas ar 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> te<PERSON>.", "app.errors.banner_cta_signed_in_url_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_signed_in_url_url": "Ievadīt derīgu saiti. P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka saite sākas ar 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> te<PERSON>.", "app.errors.banner_cta_signed_out_url_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.errors.banner_cta_signed_out_url_url": "Ievadīt derīgu saiti. P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka saite sākas ar 'https://'.", "app.errors.base_includes_banned_words": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, esat lietojis vienu vai vairākus vārdus, kas tiek uzskatīti par rupjībām. <PERSON><PERSON><PERSON><PERSON>, mainiet savu tekstu, lai no tā izņemtu visus iespējamos rupjos vārdus.", "app.errors.body_multiloc_includes_banned_words": "<PERSON><PERSON><PERSON><PERSON> <PERSON>r v<PERSON>, kas tiek uzskatīti par nepie<PERSON>ērotiem.", "app.errors.bulk_import_idea_not_valid": "<PERSON><PERSON><PERSON><PERSON>ā ideja nav derīga: {value}.", "app.errors.bulk_import_image_url_not_valid": "No {value}nevarēja lejupielādēt nevienu attēlu. <PERSON><PERSON><PERSON><PERSON>, ka URL ir derīgs un beidzas ar faila pap<PERSON>, <PERSON><PERSON><PERSON><PERSON>, .png vai .jpg. <PERSON><PERSON> problēma rodas rindā ar ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idejas vieta ar trūksto<PERSON>u koordin<PERSON>tu {value}. <PERSON><PERSON> problēma rodas rindā ar ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "<PERSON><PERSON>jas atrašanās vieta ar nenumerisku koordinātu {value}. <PERSON><PERSON> problēma rodas rindā ar ID {row}.", "app.errors.bulk_import_malformed_pdf": "Augšupielādētais PDF fails, šķiet, ir nepareizi noformēts. Mēģiniet vēlreiz eksportēt PDF failu no avota un pēc tam vēlreiz augšupielādēt.", "app.errors.bulk_import_maximum_ideas_exceeded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {value} ideju skaits ir pā<PERSON>.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maks<PERSON> {value} lapu skaits PDF formātā.", "app.errors.bulk_import_not_enough_pdf_pages": "Augšupielādētajā PDF failā nav pietiekami daudz lappušu - tam jābūt vismaz tikpat daudz lappušu, cik lejupielādētaj<PERSON> veidnē.", "app.errors.bulk_import_publication_date_invalid_format": "Ideja ar nederīgu publicēšanas datuma formātu \"{value}\". <PERSON><PERSON><PERSON><PERSON>, <PERSON>zman<PERSON>jiet formātu \"DD-MM-YYYYY\".", "app.errors.cannot_contain_ideas": "<PERSON><PERSON><PERSON> izvēlētā dalības metode neatbalsta šāda veida ziņojumus. <PERSON><PERSON><PERSON><PERSON>, rediģējiet savu izvēli un mēģiniet vēlreiz.", "app.errors.cant_change_after_first_response": "Jūs to vairs ne<PERSON>, jo da<PERSON>i lietot<PERSON>ji jau ir atbildēju<PERSON>i", "app.errors.category_name_taken": "Kategorija ar <PERSON> no<PERSON> jau e<PERSON>", "app.errors.confirmation_code_expired": "<PERSON><PERSON> derīguma term<PERSON> ir be<PERSON>. <PERSON><PERSON><PERSON><PERSON>, piepra<PERSON><PERSON> jaunu kodu.", "app.errors.confirmation_code_invalid": "Nederīgs apstiprinājuma kods. <PERSON><PERSON><PERSON><PERSON>, skatiet pareizu kodu savā e-pastā vai mēģiniet 'Nosūtīt jaunu kodu'", "app.errors.confirmation_code_too_many_resets": "<PERSON><PERSON>s esat pārsūtījis apstiprinājuma kodu pārāk daudz rei<PERSON>u. <PERSON><PERSON><PERSON><PERSON>, sazinieties ar mums, lai tā vietā saņemtu uzaicinājuma kodu.", "app.errors.confirmation_code_too_many_retries": "<PERSON>ūs esat mēģinājis pārāk daudz re<PERSON>u. <PERSON><PERSON><PERSON><PERSON>, pieprasiet jaunu kodu vai mēģiniet nomainīt savu e-pasta adresi.", "app.errors.email_already_active": "Rind<PERSON> {row} atrodamā e-pasta adrese {value} jau pieder reģistrētam dalībniekam", "app.errors.email_already_invited": "<PERSON><PERSON><PERSON> {row} atrodamā e-pasta adrese {value} jau tika uzai<PERSON>ta", "app.errors.email_blank": "<PERSON><PERSON> nevar būt tuk<PERSON>", "app.errors.email_domain_blacklisted": "<PERSON><PERSON><PERSON><PERSON>, reģistrācijai izmantojiet citu e-pasta domēnu.", "app.errors.email_invalid": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> derīgu e-pasta adresi.", "app.errors.email_taken": "Konts ar šo e-pasta adresi jau pastāv. Tā vietā varat pierakstīties.", "app.errors.email_taken_by_invite": "{vērt<PERSON><PERSON>} jau ir aizņemta ar aktu<PERSON>lu uzaicin<PERSON>jumu. Ja nevarat to atrast, pārbaudiet savu surogātpasta mapi vai sazinieties ar {supportEmail}.", "app.errors.emails_duplicate": "Viens vai vairāki e-pasta adreses {value} dublikāti tika atrasti šādā(-s) rindā(-s): {rows}", "app.errors.extension_whitelist_error": "<PERSON><PERSON><PERSON>, kuru mēģinājāt augš<PERSON>, nav atbalst<PERSON>ts.", "app.errors.file_extension_whitelist_error": "<PERSON><PERSON><PERSON>, kuru mēģinājāt augš<PERSON>, nav atbalst<PERSON>ts.", "app.errors.first_name_blank": "<PERSON><PERSON> nevar būt tuk<PERSON>", "app.errors.generics.blank": "<PERSON><PERSON> nevar būt tuk<PERSON>.", "app.errors.generics.invalid": "<PERSON><PERSON> ne<PERSON><PERSON><PERSON> pēc derīgas vērt<PERSON>", "app.errors.generics.taken": "Šis e-pasts jau pastāv. <PERSON> ir piesaistīts cits konts.", "app.errors.generics.unsupported_locales": "<PERSON><PERSON> lauks <PERSON> pašreizējo darbības vietu.", "app.errors.group_ids_unauthorized_choice_moderator": "Kā projekta vadītājs jūs varat sūtīt e-pasta vēstules tikai tām personām, kuras var piekļūt jūsu projektam(-iem).", "app.errors.has_other_overlapping_phases": "Projektu posmi nedrī<PERSON> p<PERSON>rkl<PERSON>.", "app.errors.invalid_email": "Rindā {row} atrodamais e-pasts {value} nav derīga e-pasta adrese", "app.errors.invalid_row": "Mēģinot apstrād<PERSON>t rindu {row}, rad<PERSON><PERSON> ne<PERSON>.", "app.errors.is_not_timeline_project": "Pašreizējais projekts neatbalsta posmus.", "app.errors.key_invalid": "Atslēgā var būt tikai burti, cipari un pasvītrojumi(_).", "app.errors.last_name_blank": "<PERSON><PERSON> nevar būt tuk<PERSON>", "app.errors.locale_blank": "Lūdzu, izvēlieties valodu", "app.errors.locale_inclusion": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties atbalstīto valodu", "app.errors.malformed_admin_value": "<PERSON><PERSON><PERSON> {row} atrodamā admin vērtība {value} nav derīga", "app.errors.malformed_groups_value": "<PERSON><PERSON><PERSON> {row} atrod<PERSON><PERSON> grupa {vērtība} nav derīga grupa", "app.errors.max_invites_limit_exceeded1": "<PERSON>el<PERSON><PERSON>u skaits pārsniedz 1000.", "app.errors.maximum_attendees_greater_than1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēto personu skaitam jābūt liel<PERSON>m par 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrētāju skaitam jābūt lielākam vai vienādam ar pašreizējo reģistrētāju skaitu.", "app.errors.no_invites_specified": "Nevarēja atrast nevienu e-pasta adresi.", "app.errors.no_recipients": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON>, jo nav saņ<PERSON>. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ir vai nu tuk<PERSON>, vai arī neviens nav piekritis saņemt e-pasta vēstules.", "app.errors.number_invalid": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu numuru.", "app.errors.password_blank": "<PERSON><PERSON> nevar būt tuk<PERSON>", "app.errors.password_invalid": "<PERSON><PERSON><PERSON><PERSON>, vē<PERSON><PERSON><PERSON> pārbaudiet savu pašre<PERSON><PERSON><PERSON> paroli.", "app.errors.password_too_short": "Parolei jābūt vismaz 8 rakstu zīmju garai", "app.errors.resending_code_failed": "<PERSON>ut kas nepareizi notika, nos<PERSON><PERSON><PERSON> apstiprin<PERSON><PERSON> kodu.", "app.errors.slug_taken": "Šis projekta URL jau eksistē. <PERSON><PERSON>, nomainiet projekta aprakstošo daļu uz citu.", "app.errors.tag_name_taken": "Tags ar <PERSON>o no<PERSON> jau <PERSON>āv", "app.errors.title_multiloc_blank": "<PERSON><PERSON><PERSON><PERSON> lauks nedr<PERSON> būt tuk<PERSON>.", "app.errors.title_multiloc_includes_banned_words": "Nosauku<PERSON> satur vārdus, kas tiek uzskatīti par nepiemērotiem.", "app.errors.token_invalid": "Paroles atiestatīšanas saites var izmantot tikai vienu reizi, un tās ir derīgas vienu stundu pēc to nos<PERSON>tīšanas. {passwordResetLink}.", "app.errors.too_common": "Šo paroli var viegli uzminēt. Lūdzu, izvēlieties stiprāku paroli.", "app.errors.too_long": "<PERSON><PERSON><PERSON><PERSON>, izv<PERSON><PERSON><PERSON> īs<PERSON><PERSON> paroli (ne vairāk kā 72 rakstu zīmes)", "app.errors.too_short": "<PERSON><PERSON><PERSON><PERSON>, izvēlieties paroli ar vismaz 8 rakstu z<PERSON>m", "app.errors.uncaught_error": "<PERSON>ika nezin<PERSON>.", "app.errors.unknown_group": "<PERSON><PERSON><PERSON> {row} atrod<PERSON><PERSON> grupa {vērtība} nav zināma grupa", "app.errors.unknown_locale": "<PERSON><PERSON><PERSON> {row} atrod<PERSON><PERSON> valoda {vērtība} nav konfigurēta valoda", "app.errors.unparseable_excel": "Izvēlēto Excel failu nav iespējams apstrādāt.", "app.errors.url": "Ievadiet derīgu saiti. P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka saite sākas ar https://", "app.errors.verification_taken": "Verifik<PERSON><PERSON><PERSON> nevar <PERSON>, jo, <PERSON><PERSON><PERSON><PERSON><PERSON> to pa<PERSON>u inform<PERSON>, ir verificēts cits konts.", "app.errors.view_name_taken": "Skats ar <PERSON><PERSON><PERSON> no<PERSON> jau e<PERSON>", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Publikācijā vai komentārā tika automātiski konstatēts neatbilstošs saturs", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Pierakstīties ar StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Reģistrējieties ar StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Izveidojiet Stadt Wien kontu un izmantojiet vienu pieteikšanās vārdu daudziem Vīnes digitālajiem pakalpojumiem.", "app.modules.id_cow.cancel": "Atcelt", "app.modules.id_cow.emptyFieldError": "<PERSON><PERSON> lauk<PERSON> ne<PERSON> būt tuk<PERSON>.", "app.modules.id_cow.helpAltText": "<PERSON><PERSON><PERSON>, kur uz identitātes kartes atrast ID sērijas numuru", "app.modules.id_cow.invalidIdSerialError": "Nederīgs ID sērijas numurs", "app.modules.id_cow.invalidRunError": "Nederīgs RUN", "app.modules.id_cow.noMatchFormError": "Sakritība netika atrasta.", "app.modules.id_cow.notEntitledFormError": "Nav <PERSON><PERSON><PERSON>.", "app.modules.id_cow.showCOWHelp": "Kur es varu atrast savu ID sērijas numuru?", "app.modules.id_cow.somethingWentWrongError": "<PERSON><PERSON><PERSON> j<PERSON>, jo rad<PERSON><PERSON>.", "app.modules.id_cow.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.takenFormError": "<PERSON><PERSON>.", "app.modules.id_cow.verifyCow": "<PERSON><PERSON><PERSON><PERSON><PERSON>, izmantojot COW", "app.modules.id_franceconnect.verificationButtonAltText": "Pārbaudiet ar FranceConnect", "app.modules.id_gent_rrn.cancel": "Atcelt", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON><PERSON> lauk<PERSON> ne<PERSON> būt tuk<PERSON>.", "app.modules.id_gent_rrn.gentRrnHelp": "<PERSON><PERSON>su sociālā nodro<PERSON>juma numurs ir norādīts jūsu digitālās identitātes kartes pretējā pusē", "app.modules.id_gent_rrn.invalidRrnError": "Nederīgs sociālā <PERSON>ma numurs", "app.modules.id_gent_rrn.noMatchFormError": "<PERSON><PERSON><PERSON>m atrast pamatojuma informāciju par jūsu sociālā nodrošinājuma numuru", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "<PERSON><PERSON>s jūs <PERSON>, jo jūs d<PERSON> ārpus Ģentes", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "<PERSON><PERSON><PERSON> jūs <PERSON>, jo jūs esat jaun<PERSON> par 14 gadiem", "app.modules.id_gent_rrn.rrnLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> numurs", "app.modules.id_gent_rrn.rrnTooltip": "<PERSON><PERSON><PERSON> l<PERSON> jūsu so<PERSON> numuru, la<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, ka esat par 14 gadiem vecāks Ģentes pilsonis.", "app.modules.id_gent_rrn.showGentRrnHelp": "Kur es varu atrast savu ID sērijas numuru?", "app.modules.id_gent_rrn.somethingWentWrongError": "<PERSON><PERSON><PERSON> j<PERSON>, jo rad<PERSON><PERSON>.", "app.modules.id_gent_rrn.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.takenFormError": "<PERSON><PERSON><PERSON> so<PERSON>ju<PERSON> numurs jau ir i<PERSON>ts cita konta pārbaudei", "app.modules.id_gent_rrn.verifyGentRrn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, izmantojot GentRrn", "app.modules.id_id_card_lookup.cancel": "Atcelt", "app.modules.id_id_card_lookup.emptyFieldError": "<PERSON><PERSON> lauk<PERSON> ne<PERSON> būt tuk<PERSON>.", "app.modules.id_id_card_lookup.helpAltText": "ID kartes skai<PERSON>", "app.modules.id_id_card_lookup.invalidCardIdError": "Šis id nav derīgs.", "app.modules.id_id_card_lookup.noMatchFormError": "Sakritība netika atrasta.", "app.modules.id_id_card_lookup.showHelp": "Kur es varu atrast savu ID sērijas numuru?", "app.modules.id_id_card_lookup.somethingWentWrongError": "<PERSON><PERSON><PERSON> j<PERSON>, jo rad<PERSON><PERSON>.", "app.modules.id_id_card_lookup.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON>.", "app.modules.id_oostende_rrn.cancel": "Atcelt", "app.modules.id_oostende_rrn.emptyFieldError": "<PERSON><PERSON> lauk<PERSON> ne<PERSON> būt tuk<PERSON>.", "app.modules.id_oostende_rrn.invalidRrnError": "Nederīgs sociālā <PERSON>ma numurs", "app.modules.id_oostende_rrn.noMatchFormError": "<PERSON><PERSON><PERSON>m atrast pamatojuma informāciju par jūsu sociālā nodrošinājuma numuru", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "<PERSON><PERSON><PERSON> jū<PERSON>, jo jūs d<PERSON> ā<PERSON><PERSON>.", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "<PERSON><PERSON><PERSON> jūs <PERSON>, jo jūs esat jaun<PERSON> par 14 gadiem", "app.modules.id_oostende_rrn.oostendeRrnHelp": "<PERSON><PERSON>su sociālā nodro<PERSON>juma numurs ir norādīts jūsu digitālās identitātes kartes pretējā pusē", "app.modules.id_oostende_rrn.rrnLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> numurs", "app.modules.id_oostende_rrn.rrnTooltip": "<PERSON><PERSON><PERSON> l<PERSON> jūsu so<PERSON> numuru, la<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, ka esat par 14 gadiem vecāks Ostendes pilsonis.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Kur es varu atrast manu sociālā nodrošinājuma numuru?", "app.modules.id_oostende_rrn.somethingWentWrongError": "<PERSON><PERSON><PERSON> j<PERSON>, jo rad<PERSON><PERSON>.", "app.modules.id_oostende_rrn.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.takenFormError": "<PERSON><PERSON><PERSON> so<PERSON>ju<PERSON> numurs jau ir i<PERSON>ts cita konta pārbaudei", "app.modules.id_oostende_rrn.verifyOostendeRrn": "<PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> so<PERSON> numuru", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "<PERSON><PERSON><PERSON> esat saņēmis administratora tiesības mapei \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Koplietot", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Apskatiet projektus {folderUrl}, lai jū<PERSON> vied<PERSON> tiktu sad<PERSON>!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | no {orgName} līdzdalības platformas", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | no {orgName} līdzdalības platformas", "app.sessionRecording.accept": "Jā, es piekrītu", "app.sessionRecording.modalDescription1": "<PERSON> lab<PERSON>k izprastu savu<PERSON>, <PERSON><PERSON><PERSON>ētāju izlases veidā lūdzam detalizēti izsekot viņu pā<PERSON> sesijai.", "app.sessionRecording.modalDescription2": "Vienīgais reģistrēto datu mērķis ir uzlabot tīmekļa vietni. Neviens no jūsu datiem netiks nodots trešajām pusēm. Jebkura jūsu ievadītā sensitīvā informācija tiks filtrēta.", "app.sessionRecording.modalDescription3": "Vai jūs <PERSON>?", "app.sessionRecording.modalDescriptionFaq": "Biežāk uzdotie jautājumi šeit.", "app.sessionRecording.modalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mums u<PERSON><PERSON><PERSON> v<PERSON>", "app.sessionRecording.reject": "Nē, es noraidu", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Veikt budžeta sadali", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Atsauksmes par dokumentu vākšana", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Izveidot platformā veiktu apsekojumu", "app.utils.AdminPage.ProjectEdit.createPoll": "Izveidot aptauju", "app.utils.AdminPage.ProjectEdit.createSurveyText": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON> br<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "<PERSON><PERSON><PERSON><PERSON> dati un atsauksmes", "app.utils.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.credits": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.tokens": "žetoni", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# kredīti} one {# kredīts} other {# kredīti}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# žetoni} one {# žetons} other {# žetoni}}", "app.utils.IdeaCards.mostDiscussed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Tendences", "app.utils.IdeasNewPage.contributionFormTitle": "<PERSON><PERSON><PERSON> j<PERSON>", "app.utils.IdeasNewPage.ideaFormTitle": "<PERSON><PERSON><PERSON> j<PERSON>u", "app.utils.IdeasNewPage.initiativeFormTitle": "<PERSON><PERSON><PERSON> j<PERSON> inici<PERSON>", "app.utils.IdeasNewPage.issueFormTitle1": "<PERSON><PERSON><PERSON> j<PERSON> k<PERSON>", "app.utils.IdeasNewPage.optionFormTitle": "<PERSON><PERSON><PERSON> j<PERSON>", "app.utils.IdeasNewPage.petitionFormTitle": "<PERSON><PERSON><PERSON> j<PERSON>", "app.utils.IdeasNewPage.projectFormTitle": "<PERSON><PERSON><PERSON> jaunu projektu", "app.utils.IdeasNewPage.proposalFormTitle": "<PERSON><PERSON><PERSON> j<PERSON>", "app.utils.IdeasNewPage.questionFormTitle": "<PERSON><PERSON><PERSON> j<PERSON>", "app.utils.IdeasNewPage.surveyTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourComment": "<PERSON><PERSON><PERSON><PERSON> savu komentāru", "app.utils.IdeasNewPage.viewYourContribution": "Pārskatīt savu ieguldījumu", "app.utils.IdeasNewPage.viewYourIdea": "Pārskatīt savu ideju", "app.utils.IdeasNewPage.viewYourInitiative": "Pārskatīt savu iniciatīvu", "app.utils.IdeasNewPage.viewYourInput": "Skatiet savu ieguldījumu", "app.utils.IdeasNewPage.viewYourIssue": "P<PERSON><PERSON><PERSON><PERSON><PERSON> savu j<PERSON>", "app.utils.IdeasNewPage.viewYourOption": "P<PERSON><PERSON><PERSON><PERSON><PERSON> savu opciju", "app.utils.IdeasNewPage.viewYourPetition": "<PERSON><PERSON><PERSON><PERSON> savu lū<PERSON>u", "app.utils.IdeasNewPage.viewYourProject": "<PERSON><PERSON><PERSON><PERSON> savu projektu", "app.utils.IdeasNewPage.viewYourProposal": "Pārskatīt savu p<PERSON>kšlikumu", "app.utils.IdeasNewPage.viewYourQuestion": "<PERSON><PERSON><PERSON><PERSON> savu j<PERSON>", "app.utils.Projects.sendSubmission": "Nosūtīt iesniegšanas identifikatoru uz manu e-pastu", "app.utils.Projects.sendSurveySubmission": "Nosūtīt aptaujas iesniegšanas identifikatoru uz manu e-pastu", "app.utils.Projects.surveySubmission": "Aptaujas iesniegšana", "app.utils.Projects.yourResponseHasTheFollowingId": "<PERSON><PERSON><PERSON> at<PERSON> ir šāds identifikators: {identifier}.", "app.utils.Promessagesjects.ifYouLaterDecide": "<PERSON>a v<PERSON><PERSON><PERSON><PERSON>, ka v<PERSON><PERSON><PERSON>, lai jūsu at<PERSON>de tiktu <PERSON>, l<PERSON><PERSON><PERSON>, sazin<PERSON><PERSON> ar mums, nor<PERSON><PERSON><PERSON> šādu unikālu identifikatoru:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "<PERSON>, jums ir j<PERSON><PERSON><PERSON><PERSON><PERSON> savs profils.", "app.utils.actionDescriptors.attendingEventNotInGroup": "<PERSON><PERSON><PERSON>, lai piedal<PERSON> š<PERSON>ā pasākum<PERSON>.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Jums nav atļaut<PERSON> apmeklēt šo pas<PERSON>ku<PERSON>.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "<PERSON> p<PERSON>, jums ir jā<PERSON> vai jāreģistrējas.", "app.utils.actionDescriptors.attendingEventNotVerified": "Pirms piedalīties šajā p<PERSON>, jums ir jā<PERSON><PERSON><PERSON><PERSON> savs konts.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "<PERSON> k<PERSON>tu par brīv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jums ir j<PERSON><PERSON><PERSON><PERSON><PERSON> savs profils.", "app.utils.actionDescriptors.volunteeringNotInGroup": "<PERSON><PERSON><PERSON>t brīvprātīgajam darbam izvirzītajām prasībām.", "app.utils.actionDescriptors.volunteeringNotPermitted": "<PERSON><PERSON> nav at<PERSON><PERSON><PERSON> strādāt kā brīvprātīgajam.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "<PERSON> pie<PERSON> brīvpr<PERSON><PERSON><PERSON><PERSON><PERSON> darb<PERSON>, jums ir jā<PERSON><PERSON> vai jāreģistrējas.", "app.utils.actionDescriptors.volunteeringNotVerified": "Pirms brīvprātīg<PERSON> darba ve<PERSON> jums ir jāapst<PERSON><PERSON> savs konts.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "<PERSON><PERSON><PERSON><PERSON>, raks<PERSON><PERSON> uz {completeRegistrationLink} , lai pieteiktos kā brīvprātīgais.", "app.utils.errors.api_error_default.in": "Nav par<PERSON><PERSON>", "app.utils.errors.default.ajv_error_birthyear_required": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu dzi<PERSON>šanas gadu", "app.utils.errors.default.ajv_error_date_any": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu datumu", "app.utils.errors.default.ajv_error_domicile_required": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu d<PERSON>īves<PERSON>", "app.utils.errors.default.ajv_error_gender_required": "<PERSON><PERSON><PERSON><PERSON>, ievadiet savu d<PERSON>u", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.ajv_error_maxItems": "<PERSON><PERSON><PERSON> iet<PERSON> v<PERSON> par {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_minItems": "J<PERSON>ietver vismaz {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_number_any": "<PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu numuru", "app.utils.errors.default.ajv_error_politician_required": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, vai esat politiķis", "app.utils.errors.default.ajv_error_required3": "<PERSON><PERSON> ir obligāts: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_accepted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_blank": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_confirmation": "Neatbilst", "app.utils.errors.default.api_error_empty": "<PERSON><PERSON><PERSON> b<PERSON>", "app.utils.errors.default.api_error_equal_to": "Nav par<PERSON><PERSON>", "app.utils.errors.default.api_error_even": "<PERSON><PERSON><PERSON><PERSON><PERSON> pāra s<PERSON>", "app.utils.errors.default.api_error_exclusion": "<PERSON><PERSON>", "app.utils.errors.default.api_error_greater_than": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>s", "app.utils.errors.default.api_error_greater_than_or_equal_to": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>s", "app.utils.errors.default.api_error_inclusion": "<PERSON>v <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.api_error_less_than": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> liels", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> liels", "app.utils.errors.default.api_error_not_a_number": "<PERSON>v skai<PERSON><PERSON>", "app.utils.errors.default.api_error_not_an_integer": "<PERSON><PERSON><PERSON><PERSON><PERSON> veselam skaitlim", "app.utils.errors.default.api_error_other_than": "Nav par<PERSON><PERSON>", "app.utils.errors.default.api_error_present": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_too_long": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> garš", "app.utils.errors.default.api_error_too_short": "<PERSON>r <PERSON><PERSON><PERSON><PERSON><PERSON>ss", "app.utils.errors.default.api_error_wrong_length": "Nepareizs garums", "app.utils.errors.defaultapi_error_.odd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.notInGroup": "<PERSON><PERSON><PERSON>, lai piedal<PERSON> konku<PERSON>.", "app.utils.participationMethod.onSurveySubmission": "Paldies. <PERSON><PERSON><PERSON> at<PERSON> ir <PERSON>.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "<PERSON><PERSON><PERSON><PERSON><PERSON> vairs nav pieejama, jo <PERSON>is posms vairs nav aktīvs.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "<PERSON><PERSON><PERSON> b<PERSON> p<PERSON>.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Ju<PERSON> nav at<PERSON><PERSON><PERSON> balsot.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "<PERSON> b<PERSON>, jums ir jā<PERSON> vai jāreģistrējas.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Pirms balsošanas jums ir jāapstip<PERSON> savs konts.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Budžeta iesniegšana tika slēgta {endDate}.</b> <b> <PERSON><PERSON></b> dal<PERSON>bniekam bija pieejams <b>{maxBudget} , ko sadal<PERSON>t starp {optionCount} iespējām.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Iesniegtais budžets", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Iesniegtais budžets 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "<PERSON><PERSON><PERSON> budžeta piešķiršanas prasībām.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Ju<PERSON> nav at<PERSON><PERSON><PERSON> piešķirt bud<PERSON><PERSON>.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "<PERSON> piešķirtu bud<PERSON><PERSON>, ir j<PERSON><PERSON><PERSON><PERSON><PERSON> vai jāreģistrējas.", "app.utils.votingMethodUtils.budgetingNotVerified": "Pirms budžetu piešķiršanas jums ir j<PERSON><PERSON><PERSON><PERSON><PERSON> kont<PERSON>.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b><PERSON><PERSON><PERSON> budžets netiks ieskaitīts</b> , kam<PERSON><PERSON> j<PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "<PERSON><PERSON><PERSON><PERSON><PERSON> bud<PERSON> ir {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON> <PERSON><PERSON>, noklikšķiniet uz \"<PERSON><PERSON>nie<PERSON>\", lai iesniegtu budžetu.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Izvēlieties vēlamās opcijas, piesitot \"Pievienot\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Ju<PERSON> ir pieejami <b>{maxBudget} , ko sadal<PERSON>t starp {optionCount} iespējām</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, jū<PERSON> budžets ir iesniegts!</b> <PERSON><PERSON><PERSON><PERSON><PERSON> brīdī varat pārbaudīt turpmāk norādītās iespējas vai mainīt tās pirms <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, jū<PERSON> budžets ir iesniegts!</b> Turpmāk jebkurā brīdī varat pārbaudīt savas iespējas.", "app.utils.votingMethodUtils.castYourVote": "Nododiet savu balsi", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Vienai opcijai var pievienot ne vairāk kā {maxVotes, plural, one {# kredītu} other {# kredītu}} .", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Vienai opcijai var pievienot ne vairāk kā {maxVotes, plural, one {# punktu} other {# punktu}} .", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Vienai opcijai var pievienot ne vairāk kā {maxVotes, plural, one {# žetonu} other {# žetonu}} .", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Vienai opcijai var pievienot ne vairāk kā {maxVotes, plural, one {# balsot} other {# balsis}} .", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON><PERSON> <PERSON>, noklikšķiniet uz \"<PERSON><PERSON><PERSON><PERSON>\", lai nodotu savu balsi.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Izvēlieties vē<PERSON>s opci<PERSON>, piesitot \"Atlasīt\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Galīgie rezultāti", "app.utils.votingMethodUtils.finalTally": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.howToParticipate": "Kā piedalīties", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON> balsot", "app.utils.votingMethodUtils.multipleVotingEnded1": "<PERSON><PERSON><PERSON><PERSON><PERSON> tika slēgta <b> vietnē{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 kredīti} one {1 kredīts} other {# kredīti}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 punktu} one {1 punkts} other {# punkti}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 žetonu} one {1 žetons} other {# žetoni}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 balsis} one {1 balss} other {# balsis}}", "app.utils.votingMethodUtils.results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.singleVotingEnded": "<PERSON><PERSON><PERSON><PERSON><PERSON> tika slēgta vietnē <b>{endDate}.</b> <PERSON><PERSON><PERSON><PERSON><PERSON> varēja <b>balsot par {maxVotes} iespējām.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Izvēlieties vēlamo opciju, piesitot \"Balsot\".", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Jums ir <b>{totalVotes} balsis</b> , kuras varat piešķirt opcijām.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON><PERSON> <PERSON>, noklikšķiniet uz \"<PERSON><PERSON><PERSON><PERSON>\", lai nodotu savu balsi.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "<PERSON><PERSON><PERSON><PERSON><PERSON> tika slēgta vietnē <b>{endDate}.</b> <PERSON><PERSON><PERSON><PERSON><PERSON> varēja <b>balsot par 1 iespēju.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Izvēlieties vēlamo opciju, piesitot \"Balsot\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Jums ir <b>1 balss</b> , ko varat piešķirt vienai no iespējām.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "<PERSON>ls<PERSON><PERSON>na tika slēgta vietnē <b>{endDate}.</b> <PERSON><PERSON><PERSON><PERSON><PERSON> varēja <b>balsot par tik variantiem, cik viņi vēlējās.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "<PERSON><PERSON>t balsot par tik iespējām, cik vēlaties.", "app.utils.votingMethodUtils.submitYourBudget": "Iesniedziet savu budžetu", "app.utils.votingMethodUtils.submittedBudgetCountText2": "persona iesniedza savu budžetu tiešsaistē", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "cilvēki iesniedza savus bud<PERSON>.", "app.utils.votingMethodUtils.submittedVoteCountText2": "persona iesniedza savu balso<PERSON><PERSON>ē", "app.utils.votingMethodUtils.submittedVotesCountText2": "cilv<PERSON><PERSON> b<PERSON>.", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON> 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON><PERSON><PERSON><PERSON> balsis", "app.utils.votingMethodUtils.votingClosed": "<PERSON>ls<PERSON><PERSON><PERSON> ir slēgta", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b><PERSON><PERSON><PERSON> b<PERSON>s net<PERSON>,</b> kam<PERSON><PERSON> j<PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\".", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, jū<PERSON> balsojums ir iesniegts!</b> <PERSON><PERSON><PERSON> varat pārbaudīt vai mainīt savu iesniegumu pirms <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, jū<PERSON> balsojums ir iesniegts!</b> Turpmāk jebkurā brīdī varat pārbaudīt vai mainīt savu iesniegumu.", "components.UI.IdeaSelect.noIdeaAvailable": "Ideju nav.", "components.UI.IdeaSelect.selectIdea": "Izvēlieties ideju", "containers.SiteMap.allProjects": "Visi projekti", "containers.SiteMap.customPageSection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.folderInfo": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.headSiteMapTitle": "Vietnes karte | {orgName}", "containers.SiteMap.homeSection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.pageContents": "Lapas saturs", "containers.SiteMap.profilePage": "<PERSON><PERSON><PERSON> profila lapa", "containers.SiteMap.profileSettings": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informācija", "containers.SiteMap.projectPoll": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectSurvey": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectsArchived": "Arhivētie projekti", "containers.SiteMap.projectsCurrent": "Pašreizējie projekti", "containers.SiteMap.projectsDraft": "Projektu sagataves", "containers.SiteMap.projectsSection": "{orgName} projekti", "containers.SiteMap.signInPage": "Pierakstī<PERSON>", "containers.SiteMap.signUpPage": "Reģistrēties", "containers.SiteMap.siteMapDescription": "No šīs lapas varat pāriet uz jebkuru saturu platformā.", "containers.SiteMap.siteMapTitle": "{orgName} līdzdalības platformas vietnes karte", "containers.SiteMap.successStories": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.timeline": "Projekta posmi", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON><PERSON> konts", "containers.SubscriptionEndedPage.accessDenied": "Jums vairs nav piekļuves", "containers.SubscriptionEndedPage.subscriptionEnded": "<PERSON><PERSON> lapa ir pieejama tikai platformām ar aktīvu abonementu."}