{"EmailSettingsPage.emailSettings": "E-Mail Astellungen", "EmailSettingsPage.initialUnsubscribeError": "<PERSON><PERSON> aus der Mailinglëscht ass e Feeler opgetratt. Probéiert et wgl. nach eng Kéier.", "EmailSettingsPage.initialUnsubscribeLoading": "Är Ufro g<PERSON> beaar<PERSON>cht, hutt wgl. e bësse Gedold...", "EmailSettingsPage.initialUnsubscribeSuccess": "Dir hutt Iech erfollegräich vun {campaignTitle} ofgemellt.", "UI.FormComponents.optional": "optional", "app.closeIconButton.a11y_buttonActionMessage": "Zoumaachen", "app.components.Areas.areaUpdateError": "E Feeler ass geschitt beim Späicheren vun Ärem Gebitt. Probéiert w.e.g. nach eng Kéier.", "app.components.Areas.followedArea": "Gefollegt Gebitt: {areaTitle}", "app.components.Areas.followedTopic": "Gefollegt Thema: {topicTitle}", "app.components.Areas.topicUpdateError": "<PERSON> Feeler ass geschitt beim Späicheren vun Ärem Thema. Probéiert w.e.g. nach eng Kéier.", "app.components.Areas.unfollowedArea": "Net gefollegt Gebitt: {areaTitle}", "app.components.Areas.unfollowedTopic": "Net gefollegt Thema: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Präis:", "app.components.AssignBudgetControl.add": "Bäisetzen", "app.components.AssignBudgetControl.added": "Dobäigesat", "app.components.AssignMultipleVotesControl.addVote": "Vote derbäi", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Dir hutt all Är Kreditter verdeelt.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Dir hutt déi maximal <PERSON>uel vu Kreditter fir dës <PERSON> verde<PERSON>t.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Dir hutt all Är Punkten verdeelt.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Dir hutt déi maximal Zuel vu Punkten fir dës Op<PERSON> verde<PERSON>t.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Dir hutt all Är Tokens verdeelt.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Dir hutt déi maximal <PERSON><PERSON> vun Tokens fir dës Optiou<PERSON> verdeelt.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Dir hutt all Är Stëmmen verdeelt.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Dir hutt déi maximal Zuel vu Stëmme fir dës Optiou<PERSON> verdeelt.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(inkl. 1 offline)} other {(inkl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Ofstëmmung ass net verfügbar, well dës Phase net aktiv ass.", "app.components.AssignMultipleVotesControl.removeVote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.select": "<PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Dir hutt Är Stëmm schonn ofginn. Fir se z<PERSON>änneren, klickt op \"Är Stëmm änneren\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Dir hutt Är Stëmm schonn ofginn. Fir se z'änneren, gitt z<PERSON> op d'Projetsäit a klickt op \"Är Stëmm änneren\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON>}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {Token} other {Tokenen}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "app.components.AssignVoteControl.maxVotesReached1": "Dir hutt all Är Stëmmen verdeelt.", "app.components.AssignVoteControl.phaseNotActive": "Ofstëmmung ass net verfügbar, well dës Phase net aktiv ass.", "app.components.AssignVoteControl.select": "<PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.selected2": "Ausgewielt", "app.components.AssignVoteControl.voteForAtLeastOne": "Wielt op d'mannst 1 Optioun", "app.components.AssignVoteControl.votesSubmitted1": "Dir hutt Är Stëmm schonn ofginn. Fir se z<PERSON>änneren, klickt op \"Är Stëmm änneren\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Dir hutt Är Stëmm schonn ofginn. Fir se z'änneren, gitt z<PERSON> op d'Projetsäit a klickt op \"Är Stëmm änneren\".", "app.components.AuthProviders.continue": "Weiderfueren", "app.components.AuthProviders.continueWithAzure": "Weider mat {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Weiderfuere mat Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Fuert weider mat Fake SSO", "app.components.AuthProviders.continueWithGoogle": "Weiderfuere mat Google", "app.components.AuthProviders.continueWithHoplr": "<PERSON><PERSON> mat <PERSON>", "app.components.AuthProviders.continueWithIdAustria": "<PERSON><PERSON> mat <PERSON>", "app.components.AuthProviders.continueWithLoginMechanism": "Fuert weider mat {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "<PERSON>ert weider mat <PERSON>", "app.components.AuthProviders.franceConnectMergingFailed": "Et gëtt schonn e Kont mat dëser E-Mail-Adress.{br}{br}Dir hutt mat FranceConnect keen Zougr<PERSON><PERSON> op d'Plattform, well d'Personalien net iwwereneestëmmen. Fir Iech mat FranceConnect anzeloggen, musst Dir fir d'éischt Äre Vir- oder Nonumm op dëser Plattform änneren, fir datt si mat Ären offiziellen Donnéeë iwwereneestëmmen.{br}{br}Dir kënnt Iech hei ënne wéi Dir et gewinnt sidd aloggen.", "app.components.AuthProviders.goToLogIn": "Hutt <PERSON>r schonn e <PERSON>? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Dir hutt kee Kont? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "<PERSON><PERSON><PERSON>", "app.components.AuthProviders.logInWithEmail": "<PERSON> E-Mail-<PERSON><PERSON>", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Dir musst de spezifizéierte Mindestalter oder méi héich sinn fir verifizéiert ze ginn.", "app.components.AuthProviders.signUp2": "As<PERSON><PERSON><PERSON>wen", "app.components.AuthProviders.signUpButtonAltText": "Mat {loginMechanismName} umellen", "app.components.AuthProviders.signUpWithEmail": "<PERSON> E-Mail-<PERSON><PERSON>", "app.components.AuthProviders.verificationRequired": "Verifikat<PERSON><PERSON> er<PERSON>", "app.components.Author.a11yPostedBy": "Gepost vum", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 Participant} other {{numberOfParticipants} Participanten}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} <PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.participant": "Participant", "app.components.AvatarBubbles.participants1": "Participanten", "app.components.Comments.cancel": "Ofbriechen", "app.components.Comments.commentingDisabledInCurrentPhase": "Kommentéieren ass an der aktueller Phas net méiglech.", "app.components.Comments.commentingDisabledInactiveProject": "Kommentéieren ass net méiglech, well dëse Projet momentan net aktiv ass.", "app.components.Comments.commentingDisabledProject": "D'Kommentarfunktioun ass an dësem Projet momentan desaktivéiert", "app.components.Comments.commentingDisabledUnverified": "fir ze kommentéieren", "app.components.Comments.commentingMaybeNotPermitted": "Wgl {signInLink} fir ze gesinn, wat fir Aktiounen ausgeféiert kënne ginn.", "app.components.Comments.inputsAssociatedWithProfile": "Par dé<PERSON>ut ginn Är Soumissioun mat Ärem Profil verbonnen, ausser Dir wielt dë<PERSON>.", "app.components.Comments.invisibleTitleComments": "Kommentaren", "app.components.Comments.leastRecent": "Am mannsten rezent", "app.components.Comments.likeComment": "Like <PERSON><PERSON><PERSON>", "app.components.Comments.mostLiked": "<PERSON><PERSON><PERSON>", "app.components.Comments.mostRecent": "<PERSON><PERSON><PERSON>", "app.components.Comments.official": "Offiziell", "app.components.Comments.postAnonymously": "Post anonym", "app.components.Comments.replyToComment": "Op Kommentar äntweren", "app.components.Comments.reportAsSpam": "<PERSON>s Spam mellen", "app.components.Comments.seeOriginal": "Original uweisen", "app.components.Comments.seeTranslation": "Iwwersetzung uweisen", "app.components.Comments.yourComment": "Äre Kommentar", "app.components.CommonGroundResults.divisiveDescription": "<PERSON><PERSON><PERSON>, bei deenen d'Leit gläichméisseg averstane sinn an net averstane sinn:", "app.components.CommonGroundResults.divisiveTitle": "Spaltend", "app.components.CommonGroundResults.majorityDescription": "Méi wéi 60 % hunn op déi eng oder aner Manéier fir folgend Froen gestëmmt:", "app.components.CommonGroundResults.majorityTitle": "Majoritéit", "app.components.CommonGroundResults.participantLabel": "Participant", "app.components.CommonGroundResults.participantsLabel1": "Participanten", "app.components.CommonGroundResults.statementLabel": "<PERSON><PERSON>", "app.components.CommonGroundResults.statementsLabel1": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabe": "stëmmen", "app.components.CommonGroundResults.votesLabel1": "Stëmmen", "app.components.CommonGroundStatements.agreeLabel": "Zous<PERSON><PERSON><PERSON>n", "app.components.CommonGroundStatements.disagreeLabel": "Net averstanen", "app.components.CommonGroundStatements.noMoreStatements": "Et g<PERSON>tt k<PERSON>, op déi ee reagéiere kann, am Moment", "app.components.CommonGroundStatements.noResults": "Et gëtt nach keng Resultater fir ze weisen. Vergewëssert Iech w.e.g., datt Dir un der Common Ground Phase deelgeholl hutt a kuckt duerno nach eng Kéier hei no.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Resultater", "app.components.CommonGroundTabs.statementsTabLabel": "<PERSON><PERSON><PERSON>", "app.components.CommunityMonitorModal.formError": "<PERSON> <PERSON><PERSON> begéint.", "app.components.CommunityMonitorModal.surveyDescription2": "<PERSON><PERSON><PERSON> lafend Ëmfro verfollegt wéi Dir Iech iwwer Gouvernance an ëffentlech Servicer fillt.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<PERSON><PERSON><PERSON> <1 Minutt} one {Huelt 1 Minutt} other {Huel<PERSON> # Minutten}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Eng E-Mail mat engem Bestätegungscode gouf u(n) {userEmail} geschéckt.", "app.components.ConfirmationModal.changeYourEmail": "Är E-Mail änneren.", "app.components.ConfirmationModal.codeInput": "Code", "app.components.ConfirmationModal.confirmationCodeSent": "Neie Code gouf geschéckt", "app.components.ConfirmationModal.didntGetAnEmail": "Keng E-Mail kritt?", "app.components.ConfirmationModal.foundYourCode": "Hu Dir Äre Code fonnt?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON>.", "app.components.ConfirmationModal.sendEmailWithCode": "E-Mail mat <PERSON> s<PERSON>n", "app.components.ConfirmationModal.sendNewCode": "Neie Code schécken.", "app.components.ConfirmationModal.verifyAndContinue": "Iwwerpréiwen a weidergoen", "app.components.ConfirmationModal.wrongEmail": "Falsch E-Mail-Adress", "app.components.ConsentManager.Banner.accept": "Akzeptéieren", "app.components.ConsentManager.Banner.ariaButtonClose2": "<PERSON><PERSON><PERSON> ofleenen a Banner zoumaachen", "app.components.ConsentManager.Banner.close": "Zoumaachen", "app.components.ConsentManager.Banner.mainText": "Dësen Internetsite benotzt Cookieë geméiss eiser {policyLink}.", "app.components.ConsentManager.Banner.manage": "Beaarbechten", "app.components.ConsentManager.Banner.policyLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Cookië fir Publicitéit", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Mir benotzen dëst fir d'Effektivitéit vu Reklammekampagne vun eiser Websäit ze moossen an ze personaliséieren. Mir weise keng Reklamm op dëser Plattform. Allerdéngs kennen, baséierend op de Säiten déi Dir op eisem Site besicht, Ennerkontrakter (sous-traitant)  Iech personaliséiert Reklammen ubidden.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Erlaben", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "<PERSON> benotzen d<PERSON><PERSON>, fir besser ze verstoen, wéi Dir d<PERSON>Plattform notzt. Dat erla<PERSON>t eis Är Navigatioun ze verbesseren. Dës Informatiounen ginn nëmmen a Massenanalyse genotzt an op kee Fall, fir eenzel <PERSON> ze tracken.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON><PERSON><PERSON> goen", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Ofbriechen", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Net erlaben", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funk<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Dëst ass noutwenneg fir d'Basisfunktiounen vum Site ze erméiglechen an ze iwwerpréiwen. E puer vun den Toolen op dëser Lëscht kéinte fir Iech net gëllen. Fir weider Informatiounen liest wgl. eis Cookie-Richtlinnen.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Noutwenneg", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Fir eng funktionell Plattform ze hunn, späichere mir en Authentifizéierungs-<PERSON><PERSON> wann dir <PERSON><PERSON> umellt souwéi d’Sprooch an däer Dir dës Plattform notzt. ", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Späicheren", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON><PERSON>-Prefer<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Tool", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Inhalt eropluede Verzichterklärung", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "And<PERSON><PERSON> Dir Inhalt eropluet, erk<PERSON><PERSON>rt Dir datt dësen Inhalt keng Reglementer oder Rechter vun Dr<PERSON> verletz<PERSON>, wéi zum Beispill intellektuell Propriétéitsrechter, Pri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> op Handelsgeheimnisser, as<PERSON><PERSON> <PERSON><PERSON><PERSON>, and<PERSON><PERSON> <PERSON>r dësen Inhalt eropluet, verpflicht Dir eng voll an exklusiv Verantwortung fir all direkten an indirekten Schued, deen aus dem eropgeluedenen Inhalt entstinn. Ausserdeem verpflicht Dir de Plattformbesëtzer an Go Vocal géint all Drëtt Partei Fuerderungen oder Verbëndlechkeete gé<PERSON>, an all assoziéiert Käschten, déi entstinn oder entstinn aus dem Inhalt deen Dir eropgelueden hutt.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON>ch verstinn", "app.components.ContentUploadDisclaimer.onCancel": "Ofbriechen", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Sot eis firwat", "app.components.CustomFieldsForm.addressInputAriaLabel": "Adresseingabe", "app.components.CustomFieldsForm.addressInputPlaceholder6": "<PERSON><PERSON> eng Adress an...", "app.components.CustomFieldsForm.adminFieldTooltip": "<PERSON>ld nëmme fir Administrateuren sichtbar", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "All Äntwerten op dës Ëmfro ginn anonymiséiert.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Fir e Polygon sinn op d'mannst dräi Punkte noutwendeg.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Fir eng Linn sinn op d'mannst zwee Punkten noutwendeg.", "app.components.CustomFieldsForm.attachmentRequired": "Mindestens een Uschloss ass erfuerderlech", "app.components.CustomFieldsForm.authorFieldLabel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Fänkt un ze tippen fir no Benotzer-E-Mail oder Numm ze sichen...", "app.components.CustomFieldsForm.back": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "<PERSON><PERSON>t op d'Kaart fir ze zeechnen. Zitt dann un d'Punkten fir se ze verschieben.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "<PERSON><PERSON>t op d'Kaart oder gitt eng Adress hei ënnendrënner an, fir Är Äntwert bäizefügen.", "app.components.CustomFieldsForm.confirm": "Bestätegen", "app.components.CustomFieldsForm.descriptionMinLength": "<PERSON>'<PERSON><PERSON><PERSON><PERSON>wung muss mindestens {min} <PERSON><PERSON><PERSON> laang sinn", "app.components.CustomFieldsForm.descriptionRequired": "D'Besch<PERSON>iwung ass obligatoresch", "app.components.CustomFieldsForm.fieldMaximumItems": "Maximal {maxSelections, plural, one {# Optioun} other {# Optiounen}} kënnen fir d'Feld \"{fieldName}\" ausgewielt ginn.", "app.components.CustomFieldsForm.fieldMinimumItems": "<PERSON>r d'Feld \"{fieldName}\" kënne mindestens {minSelections, plural, one {# Optioun} other {# Optiounen}} ausgewielt ginn.", "app.components.CustomFieldsForm.fieldRequired": "<PERSON>'<PERSON><PERSON> \"{fieldName}\" ass obligatoresch", "app.components.CustomFieldsForm.fileSizeLimit": "D'Dateigréisstlimit ass {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "D'Bild ass obligatoresch", "app.components.CustomFieldsForm.minimumCoordinates2": "E Minimum vun {numPoints} Kaartepunkten ass erfuerderlech.", "app.components.CustomFieldsForm.notPublic1": "*<PERSON><PERSON><PERSON> gëtt nëmme mat de Projetmanager gedeelt, an net mat der Ëffentlechkeet.", "app.components.CustomFieldsForm.otherArea": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>", "app.components.CustomFieldsForm.progressBarLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.removeAnswer": "Äntwert ewechhuelen", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Wielt sou vill wéi Dir wëllt", "app.components.CustomFieldsForm.selectBetween": "*<PERSON><PERSON> tëscht den Optiounen {minItems} an {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*Genau auswielen {selectExactly, plural, one {# Optioun} other {# Optiounen}}", "app.components.CustomFieldsForm.selectMany": "*Wielt sou vill wéi Dir wëllt", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tippt op d'Kaart fir ze zeechnen. Zitt dann un d'Punkten fir se ze verschieben.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tippt op d'Kaart fir ze zeechnen.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tippt op d'Kaart fir Är Äntwert bäizefügen.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Tippt op d'Kaart oder gitt eng Adress hei ënnendrënner an, fir Är Äntwert bäizefügen.", "app.components.CustomFieldsForm.tapToAddALine": "Tip<PERSON> fir eng Zeil derbäizefügen", "app.components.CustomFieldsForm.tapToAddAPoint": "T<PERSON><PERSON> fir e <PERSON>t derbäizefügen", "app.components.CustomFieldsForm.tapToAddAnArea": "Tippen fir eng Regioun derbäizefügen", "app.components.CustomFieldsForm.titleMaxLength": "Den Titel däerf maximal {max} <PERSON><PERSON><PERSON> sinn", "app.components.CustomFieldsForm.titleMinLength": "Den Titel muss mindestens {min} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.components.CustomFieldsForm.titleRequired": "Den Titel ass obligatoresch", "app.components.CustomFieldsForm.topicRequired": "Mindestens een Tag ass erfuerderlech", "app.components.CustomFieldsForm.typeYourAnswer": "Schreift Är Äntwert an", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Et ass obligatoresch Är Äntwert ze tippen", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* <PERSON>et eng <PERSON>-<PERSON><PERSON> er<PERSON>, déi eng oder méi S<PERSON> enthält.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "<PERSON><PERSON> <PERSON>'<PERSON>z net während dem Tippen ënnert den Optiounen ugewise gëtt, kënnt Dir gülteg Koordinaten am Format 'Breet, Längt' der<PERSON>en, fir eng präzi<PERSON>ginn (z.B.: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "E puer Felder sinn ongëlteg. Wgl. korrigéiert d’Feeler a probéiert nach eng Kéier.", "app.components.ErrorBoundary.errorFormErrorGeneric": "En onbekannte Feeler ass beim Iwwermëttele vun Äre Rapport opgetratt. Wgl. probéiert nach eng Kéier.", "app.components.ErrorBoundary.errorFormLabelClose": "Zoumaachen", "app.components.ErrorBoundary.errorFormLabelComments": "Wat ass geschitt?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-Mail", "app.components.ErrorBoundary.errorFormLabelName": "Numm", "app.components.ErrorBoundary.errorFormLabelSubmit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "<PERSON><PERSON> gouf ben<PERSON>gt.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON>n Dir eis h<PERSON><PERSON><PERSON> w<PERSON>, deelt eis hei dr<PERSON><PERSON> mat, wat genee geschitt ass.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Merci! Äre Feedback gouf verschéckt.", "app.components.ErrorBoundary.errorFormTitle": "Et gesäit aus, wéi wann et e <PERSON> géing ginn. ", "app.components.ErrorBoundary.genericErrorWithForm": "<PERSON> <PERSON>er ass opgetratt a mir kënnen den Inhalt net duerstellen. Wgl. probéiert nach eng Kéier, oder {openForm}", "app.components.ErrorBoundary.openFormText": "Hëlleft eis ze verstoen wat geschitt ass ", "app.components.ErrorToast.budgetExceededError": "Dir hutt net genuch Budget", "app.components.ErrorToast.votesExceededError": "Dir hutt net genuch Stëmmen méi", "app.components.EventAttendanceButton.forwardToFriend": "<PERSON> engem <PERSON>", "app.components.EventAttendanceButton.maxRegistrationsReached": "Déi maximal Zuel vun den Aschreiwunge fir d'Evenementer ass erreecht. Et si keng <PERSON>ze méi fräi.", "app.components.EventAttendanceButton.register": "Registréieren", "app.components.EventAttendanceButton.registered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.seeYouThere": "Bis dann dohannen!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON> dohin, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Manner Informatiounen iwwer d'Event si siichtbar ginn.", "app.components.EventCard.a11y_moreContentVisible": "Méi Informatiounen iwwer d'Event si siichtbar ginn.", "app.components.EventCard.a11y_readMore": "<PERSON>t méi iwwer den Event \"{eventTitle}\".", "app.components.EventCard.endsAt": "Hält op den/ um", "app.components.EventCard.readMore": "<PERSON><PERSON> méi", "app.components.EventCard.showLess": "<PERSON><PERSON> weisen", "app.components.EventCard.showMore": "<PERSON><PERSON><PERSON> weisen", "app.components.EventCard.startsAt": "Fänkt un de(n)/ um", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Upëff a lafend Eventer an dësem Projet", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Upëff a lafend Eventer an dëser Phase", "app.components.FileUploader.a11y_file": "Datei:", "app.components.FileUploader.a11y_filesToBeUploaded": "<PERSON>ie fir eropzelueden: {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON><PERSON>(en) ugehaangen", "app.components.FileUploader.a11y_removeFile": "<PERSON><PERSON><PERSON>", "app.components.FileUploader.fileInputDescription": "<PERSON><PERSON><PERSON>, fir eng Datei auszewielen", "app.components.FileUploader.fileUploadLabel": "Annexen (max. 50MB)", "app.components.FileUploader.file_too_large2": "<PERSON><PERSON>, déi méi grouss wéi {maxSizeMb}MB sinn, sinn net erlaabt.", "app.components.FileUploader.incorrect_extension": "{fileName} gëtt net vun eisem System ënnerstëtzt, et wäert net eropgeluede ginn.", "app.components.FilterBoxes.a11y_allFilterSelected": "Ausgewielte Statusfilter: all", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# <PERSON><PERSON><PERSON><PERSON>} other {# Bäiträg}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON>", "app.components.FilterBoxes.a11y_selectedFilter": "Ausgewielte Statusfilter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Ausgewielt {numberOfSelectedTopics, plural, =0 {null Themefilter} one {een Themefilter} other {# Themefilteren}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "All", "app.components.FilterBoxes.areas": "No Plaze filteren", "app.components.FilterBoxes.inputs": "Input", "app.components.FilterBoxes.noValuesFound": "<PERSON><PERSON> verfügbar.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON> manner", "app.components.FilterBoxes.showTagsWithNumber": "<PERSON><PERSON> weisen ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Themen", "app.components.FiltersModal.filters": "Filteren", "app.components.FolderFolderCard.a11y_folderDescription": "Beschreiwung vum Dossier:", "app.components.FolderFolderCard.a11y_folderTitle": "Titel vum Dossier: ", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# Projeten} one {# Projet} other {# Projeten}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Den Terrain Typ kann net geännert ginn eemol do Soumissioun.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "<PERSON><PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autospäicheren", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Autospäicheren ass als Standard aktivéiert wann Dir de Formulaireeditor opmaacht. All Kéier wann Dir d'Feldastellungspanel mat der \"X\" Knäppchen zou<PERSON>t, wäert et automatesch e Späicheren ausléisen.", "app.components.GanttChart.timeRange.month": "Mount", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "<PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "<PERSON><PERSON> z<PERSON> op déi virdrun Säit", "app.components.HookForm.Feedback.errorTitle": "Et gëtt e Problem.", "app.components.HookForm.Feedback.submissionError": "Prob<PERSON>ier<PERSON> et nach eng Kéier. Wann de Problem weiderbesteet, kontaktéiert eis", "app.components.HookForm.Feedback.submissionErrorTitle": "<PERSON>t gouf e <PERSON> op eiser Säit, pardon.", "app.components.HookForm.Feedback.successMessage": "Formulaire erfollegräich iwwermëttelt", "app.components.HookForm.PasswordInput.passwordLabel": "<PERSON><PERSON><PERSON>", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON><PERSON>.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON><PERSON> riets.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} <PERSON><PERSON><PERSON> sinn gelueden.", "app.components.IdeaCards.filters": "Filteren", "app.components.IdeaCards.filters.mostDiscussed": "<PERSON><PERSON><PERSON> me<PERSON>cht <PERSON>", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "Alt", "app.components.IdeaCards.filters.popular": "Am meeschte gär", "app.components.IdeaCards.filters.random": "Zoufälleg", "app.components.IdeaCards.filters.sortBy": "Sortéieren no", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sortéierung geännert op: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trending", "app.components.IdeaCards.showMore": "<PERSON><PERSON><PERSON> weisen", "app.components.IdeasMap.a11y_hideIdeaCard": "Iddiskaart verstoppen.", "app.components.IdeasMap.a11y_mapTitle": "Kaarteniwwersiicht", "app.components.IdeasMap.clickOnMapToAdd": "<PERSON><PERSON>t op d<PERSON>Ka<PERSON> fir Äre Bäitrag anzefügen", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Als Administrateur kënnt Dir op d'Kaart klickt fir Ären Input ze addéieren, och wann dës Phase net aktiv ass.", "app.components.IdeasMap.filters": "Filteren", "app.components.IdeasMap.multipleInputsAtLocation": "Multiple Input op dëser Plaz", "app.components.IdeasMap.noFilteredResults": "D'Filteren déi Dir ausgewielt hutt ergi keng Resultater", "app.components.IdeasMap.noResults": "<PERSON><PERSON>tater fonnt", "app.components.IdeasMap.or": "oder", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, keng net gär.} one {1 net gär.} other {, # net gär.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, keng Likes.} one {, 1 Like.} other {, # gär.}}", "app.components.IdeasMap.signInLinkText": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.signUpLinkText": "As<PERSON><PERSON><PERSON>wen", "app.components.IdeasMap.submitIdea2": "Input ofginn", "app.components.IdeasMap.tapOnMapToAdd": "<PERSON><PERSON>t op d<PERSON>Ka<PERSON> fir Äre Bäitrag bäizesetzen", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Als Administrateur kënnt Dir op der Kaart tippen fir Ären Input ze addéieren, och wann dës Phase net aktiv ass.", "app.components.IdeasMap.userInputs2": "Input vun de Participanten", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, keng <PERSON>} one {, 1 Kommentar} other {, # Kommentarer}}", "app.components.IdeasShow.bodyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.deletePost": "Läschen", "app.components.IdeasShow.editPost": "Beaarbechten", "app.components.IdeasShow.goBack": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.moreOptions": "<PERSON><PERSON>", "app.components.IdeasShow.or": "oder", "app.components.IdeasShow.proposedBudgetTitle": "Virgeschloene Budget", "app.components.IdeasShow.reportAsSpam": "<PERSON>s Spam mellen", "app.components.IdeasShow.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ech maachen et méi spéit", "app.components.IdeasShowPage.signIn2": "<PERSON><PERSON>ech un", "app.components.IdeasShowPage.sorryNoAccess": "Sorry, Dir hutt keen <PERSON><PERSON><PERSON><PERSON> op dës Säit. Dir musst I<PERSON> méig<PERSON>cherweis aloggen oder umellen, fir op si zouzegräifen.", "app.components.LocationInput.noOptions": "<PERSON><PERSON>", "app.components.Modal.closeWindow": "Fënster zoumaachen", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "<PERSON><PERSON> eng nei E-Mailadress un", "app.components.PageNotFound.goBackToHomePage": "Zeréck op d’Homepage", "app.components.PageNotFound.notFoundTitle": "Säit net fonnt", "app.components.PageNotFound.pageNotFoundDescription": "Déi gewënschte Säit konnt net fonnt ginn.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Stellt Inhalter fir mindestens eng <PERSON><PERSON><PERSON> bereet", "app.components.PagesForm.editContent": "Inhalt", "app.components.PagesForm.fileUploadLabel": "Annexen (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "<PERSON><PERSON> sollten net méi grouss sinn wéi 50MB. Bäigefüügten Dateie ginn um Enn vun dëser Säit gewisen.", "app.components.PagesForm.navbarItemTitle": "Numm an Navigatiounsleescht", "app.components.PagesForm.pageTitle": "Titel", "app.components.PagesForm.savePage": "<PERSON><PERSON><PERSON> s<PERSON>", "app.components.PagesForm.saveSuccess": "Säit erfollegräich gespäichert.", "app.components.PagesForm.titleMissingOneLanguageError": "Stellt en Titel fir mindestems eng Sprooch bereet", "app.components.Pagination.back": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Pagination.next": "Nächst Säit", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "<PERSON>r hutt {votesCast}ausginn, wat d'Limite vun {votesLimit}iwwerschreift. Huelt w.e.g. e puer Elementer aus Ärem Kuerf a probéiert nach eng Kéier.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} lénks", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Dir musst e Minimum vu {votesMinimum} ausginn ier Dir Ä<PERSON> Kuerf ofginn.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "<PERSON>r musst mindestens eng Optioun auswielen, ier <PERSON><PERSON> <PERSON><PERSON><PERSON> kënnt.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "<PERSON>r musst eppes an Äre Ku<PERSON>f bäidroen ier Dir se ofginn.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Keng Kreditter méi iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 Kreditt} other {# Kreditter}} iwwreg}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Keng <PERSON>ten iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 <PERSON>t} other {# <PERSON>ten}} iwwreg}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Keng Jetonen iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 Jeton} other {# Jetonen}} iwwreg}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Keng <PERSON>mmen iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 Stëmm} other {# Stëmmen}} iwwreg}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {#<PERSON><PERSON><PERSON><PERSON>} one {#vommen} other {#<PERSON>ëmmen}} Besetzung", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "<PERSON>r gitt {votesCast} <PERSON><PERSON><PERSON><PERSON>, wat d'Limite vun {votesLimit}iwwerschreift. Huelt w.e.g. e puer Stëmmen ewech a probéiert nach eng Kéier.", "app.components.ParticipationCTABars.addInput": "Input derbäisetzen", "app.components.ParticipationCTABars.allocateBudget": "Verdeelt Äre Budget", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Äre Budget gouf erfollegräich presentéiert.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Op fir Bedeelegung", "app.components.ParticipationCTABars.poll": "Huelt d'Ëmfro", "app.components.ParticipationCTABars.reviewDocument": "Iwwerpréift d'Dokument", "app.components.ParticipationCTABars.seeContributions": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeEvents3": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeInitiatives": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeIssues": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeOptions": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeProjects": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeProposals": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeQuestions": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.submit": "Ofginn", "app.components.ParticipationCTABars.takeTheSurvey": "Un der Ëmfro deelhuelen", "app.components.ParticipationCTABars.userHasParticipated": "Dir hutt Iech un dësem Projet bedeelegt.", "app.components.ParticipationCTABars.viewInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "stëmmen", "app.components.ParticipationCTABars.votesCounter.votes": "Stëmmen", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON><PERSON><PERSON> ausgeblent", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength1Password": "<PERSON><PERSON><PERSON><PERSON><PERSON> ass net sécher genuch", "app.components.PasswordInput.a11y_strength2Password": "Schwaacht Passwuert", "app.components.PasswordInput.a11y_strength3Password": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON> au<PERSON>", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON> kuerz (min. {minimumPasswordLength} <PERSON><PERSON><PERSON>)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON><PERSON> e <PERSON>, dat mindestens {minimumPasswordLength} <PERSON><PERSON><PERSON> laang ass.", "app.components.PasswordInput.passwordEmptyError": "<PERSON><PERSON> <PERSON><PERSON> an.", "app.components.PasswordInput.passwordStrengthTooltip1": "<PERSON><PERSON> méi s<PERSON>cher maachen:", "app.components.PasswordInput.passwordStrengthTooltip2": "Benotzt eng Kombinatioun vu net openeenfolgend Klengbuschstawen, Groussbuschstawen an Zifferen, Sonderzeechen an Zeechesetzung", "app.components.PasswordInput.passwordStrengthTooltip3": "Vermeit gängeg Wierder a Wierder, déi liicht ze errode sinn", "app.components.PasswordInput.passwordStrengthTooltip4": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.showPassword": "<PERSON><PERSON><PERSON> weisen", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "Mëttel", "app.components.PasswordInput.strength4Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON>", "app.components.PostCardsComponents.list": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostCardsComponents.map": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "En offiziellen Update afügen", "app.components.PostComponents.OfficialFeedback.cancel": "Ofbriechen", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Läschen", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "<PERSON>d <PERSON><PERSON>, datt <PERSON><PERSON> dësen offiziellen Update läsche wëllt?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Beaarbechten", "app.components.PostComponents.OfficialFeedback.lastEdition": "Lescht Ännerung de(n) {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Leschten Update: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Offiziell", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON>, wéi d’Leit ären Numm gesinn", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Numm vum Auteur vum offiziellen Update", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Text vum offiziellen Update", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Offiziell Updaten", "app.components.PostComponents.OfficialFeedback.postedOn": "Gepost de(n) {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Verëffentlechen", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "<PERSON><PERSON><PERSON> Updates <PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "En Update verëffentlechen", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON> gouf et e Problem", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "De Message updaten", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Ären Update gouf erfollegräich verëffentlecht!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Ënnerstëtzt mäi Bäitrag '{postTitle}' ënner {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Mäi Bäitrag ënnerstëtzen: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Mäi Bäitrag ënnerstëtzen: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Wat haalt Dir vun dëser Iddi? Ënnerstëtzt meng Iddi '{postTitle}' ënner {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Ënnertstëtzt meng Iddi: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON><PERSON> <PERSON><PERSON><PERSON>: {postTitle}", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Wat haalt Dir vun dësem Virschlag? Stëmmt of an diskutéiert mat ënner {postUrl}, fir datt Är Stëmm gehéiert gëtt!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Ënnerstëtzt mäi Virschlag: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Ënnerstëtzt meng Initiativ: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Ech hunn e Kommentar '{postTitle}' ënner {postUrl} gepost!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Ech hu grad e Kommentar gepost: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Ech hunn a Kommentar '{postTitle}' bei {postUrl}gepost!", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Ënnerstëtzt meng virgeschloen Optioun '{postTitle}' ënner {postUrl}", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Ënnerstëtzt meng virgeschloen Optioun: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Ënnerstëtzt meng <PERSON>n: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Ënnerstëtzt meng Petitioun '{postTitle}' um {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Ënnerstëtzt meng <PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Ënnerstëtzt meng <PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Ënnerstëtzt meng Projet: {postTitle} ënner {postUrl}", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Ënnerstëtzt mäi Projet: {postTitle}. ", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Mäi Projet ënnerstëtzen: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Ënnerstëtzt meng Propositioun '{postTitle}' um {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Ënnerstëtzt meng Propositioun: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Ech hunn just eng Propositioun fir {orgName}gepost: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Diskutéiert mat iwwer d’Fro '{postTitle}' ënner {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Diskutéiert mat: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Diskutéiert mat: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Stëmmt fir {postTitle} ënner", "app.components.PostComponents.linkToHomePage": "Link op d'Haaptsäit", "app.components.PostComponents.readMore": "Weiderliesen", "app.components.PostComponents.topics": "Themen", "app.components.ProjectArchivedIndicator.archivedProject": "Leider kënnt Dir net méi un dësem Projet deelhuelen well en archivéiert gouf en well e gouf archivéiert", "app.components.ProjectArchivedIndicator.previewProject": "Entworf Projet:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Siichtbar nëmme fir Moderatoren an déi mat engem Virschaulink.", "app.components.ProjectCard.a11y_projectDescription": "Beschreiwung vum Projet", "app.components.ProjectCard.a11y_projectTitle": "Titel vum Projet", "app.components.ProjectCard.addYourOption": "<PERSON><PERSON><PERSON><PERSON> Optiou<PERSON> bäi", "app.components.ProjectCard.allocateYourBudget": "Verdeelt Äre Budget", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "Kommentar", "app.components.ProjectCard.contributeYourInput": "Drot Äre Bäitrag bäi", "app.components.ProjectCard.finished": "Fäerdeg", "app.components.ProjectCard.joinDiscussion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mat", "app.components.ProjectCard.learnMore": "<PERSON><PERSON><PERSON> gewuer ginn", "app.components.ProjectCard.reaction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON>", "app.components.ProjectCard.reviewDocument": "Iwwerpréift d'Dokument", "app.components.ProjectCard.submitAnIssue": "E Kommentar ofginn", "app.components.ProjectCard.submitYourIdea": "<PERSON>echt Är Iddi an", "app.components.ProjectCard.submitYourInitiative": "Gitt Är Initiativ of", "app.components.ProjectCard.submitYourPetition": "<PERSON><PERSON><PERSON><PERSON>t Är Petitioun un", "app.components.ProjectCard.submitYourProject": "Reecht Äre Projet an", "app.components.ProjectCard.submitYourProposal": "Gitt Är Propositioun of", "app.components.ProjectCard.takeThePoll": "Un der Ëmfro deelhuelen", "app.components.ProjectCard.takeTheSurvey": "Un der Ëmfro deelhuelen", "app.components.ProjectCard.viewTheContributions": "Bäiträg ukucken", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheInitiatives": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheIssues": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewThePetitions": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "Projeten ukucken", "app.components.ProjectCard.viewTheProposals": "<PERSON><PERSON><PERSON> d'Pro<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.vote": "Stëmmen", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# Kommentaren} other {# Kommentaren}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# <PERSON><PERSON><PERSON><PERSON>} other {# Bäiträg}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {nach keng Iddi} one {# Iddi} other {# Iddien}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# Initiativen} one {# Initiative} other {# Initiativen}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# Kommentar} other {# Kommentaren}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# <PERSON>tioun} other {# <PERSON><PERSON>ou<PERSON>}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# <PERSON><PERSON>unen} one {# <PERSON>ioun} other {# <PERSON><PERSON>unen}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# Projet} other {# Projeten}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# Propositioune} one {# Propositioun} other {# Propositioune}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# Fro} other {# Froen}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {#kommentarer} one {#kommentarer} other {#kommentarer}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# Entréen} one {#input} other {# Entréen}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projeten} one {# projet} other {# projeten}}", "app.components.ProjectFolderCards.components.Topbar.all": "All", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "Entworf", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filteren no", "app.components.ProjectFolderCards.components.Topbar.published2": "Verëffentlecht", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "Nach ginn et keng Projeten", "app.components.ProjectFolderCards.noProjectsAvailable": "Et si keng Projete verfügbar", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.stayTuned": "<PERSON><PERSON>t erëm fir ze kucken ob et nei Méiglechkeete gëtt Iech ze bedeelegen. ", "app.components.ProjectFolderCards.tryChangingFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON>, déi ausgewielte Filteren ze changéieren.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Och an dëse Stied gebraucht:", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON>", "app.components.QuillEditor.alignCenter": "Text zentréieren", "app.components.QuillEditor.alignLeft": "Lenks ausriichten", "app.components.QuillEditor.alignRight": "<PERSON><PERSON><PERSON> ausri<PERSON>", "app.components.QuillEditor.bold": "<PERSON><PERSON>", "app.components.QuillEditor.clean": "Formatéierung ewechhuelen", "app.components.QuillEditor.customLink": "Knäppche bäifügen", "app.components.QuillEditor.customLinkPrompt": "<PERSON> aginn:", "app.components.QuillEditor.edit": "Beaarbechten", "app.components.QuillEditor.image": "Bild erop<PERSON>den", "app.components.QuillEditor.imageAltPlaceholder": "<PERSON><PERSON>ildbes<PERSON>reiwu<PERSON>", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "<PERSON>", "app.components.QuillEditor.linkPrompt": "<PERSON> aginn:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "Späicheren", "app.components.QuillEditor.subtitle": "Ënnertitel", "app.components.QuillEditor.title": "Titel", "app.components.QuillEditor.unorderedList": "Onsortéiert <PERSON>", "app.components.QuillEditor.video": "Video bäifügen", "app.components.QuillEditor.videoPrompt": "Video aginn:", "app.components.QuillEditor.visitPrompt": "Gidd op de Link: ", "app.components.ReactionControl.completeProfileToReact": "Fëllt Äre Profil aus fir ze reagéieren", "app.components.ReactionControl.dislike": "Net gär", "app.components.ReactionControl.dislikingDisabledMaxReached": "Dir hutt Är maximal Unzuel un Dislikes an {projectName}erreecht", "app.components.ReactionControl.like": "<PERSON><PERSON><PERSON>", "app.components.ReactionControl.likingDisabledMaxReached": "Dir hutt Är maximal Unzuel u Likes an {projectName}erreecht", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reaktioun gëtt aktivéiert wann dës Phase ufänkt", "app.components.ReactionControl.reactingDisabledPhaseOver": "Et ass net méi méiglech an dëser Phase ze reagéieren", "app.components.ReactionControl.reactingDisabledProjectInactive": "Dir kënnt net méi op Iddien an {projectName}reagéieren", "app.components.ReactionControl.reactingNotEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> ass de Moment net fir dëse Projet aktivéiert", "app.components.ReactionControl.reactingNotPermitted": "Reaktioun ass nëmme fir bestëmmte Gruppen aktivéiert", "app.components.ReactionControl.reactingNotSignedIn": "<PERSON><PERSON>ech un fir ze reagéieren.", "app.components.ReactionControl.reactingPossibleLater": "D'Reak<PERSON>oun fänkt um {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verifizéiert Är Identitéit fir ze reagéieren.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Event Datum: {startDate} um {startTime} bis {endDate} um {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Event Datum: {eventDate} vun {startTime} bis {endTime}.", "app.components.Sharing.linkCopied": "<PERSON>", "app.components.Sharing.or": "oder", "app.components.Sharing.share": "<PERSON><PERSON>", "app.components.Sharing.shareByEmail": "Per E-Mail deelen", "app.components.Sharing.shareByLink": "<PERSON>", "app.components.Sharing.shareOnFacebook": "Op Facebook deelen", "app.components.Sharing.shareOnTwitter": "Op Twitter deelen", "app.components.Sharing.shareThisEvent": "<PERSON><PERSON> Event", "app.components.Sharing.shareThisFolder": "<PERSON><PERSON>", "app.components.Sharing.shareThisProject": "Dëse Projet deelen", "app.components.Sharing.shareViaMessenger": "<PERSON>", "app.components.Sharing.shareViaWhatsApp": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.components.SideModal.closeButtonAria": "Zoumaachen", "app.components.StatusModule.futurePhase": "Dir kuckt eng Phase déi nach net ugefaang huet. Dir kënnt matmaachen wann d'Phase ufänkt.", "app.components.StatusModule.modifyYourSubmission1": "Ännert Är Soumissioun", "app.components.StatusModule.submittedUntil3": "Dir kënnt wielen bis den", "app.components.TopicsPicker.numberOfSelectedTopics": "Ausgewielt {numberOfSelectedTopics, plural, =0 {null Sujeten} one {ee Sujet} other {# Sujet'en}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Bild erweideren", "app.components.UI.MoreActionsMenu.moreOptions": "<PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.showMoreActions": "<PERSON><PERSON>i Akt<PERSON> weisen", "app.components.UI.PhaseFilter.noAppropriatePhases": "<PERSON>g passende Phasen fir dëse Projet fonnt", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Iwwersetzen", "app.components.Unauthorized.additionalInformationRequired": "Zousätzlech Informatioun ass néideg fir I<PERSON>.", "app.components.Unauthorized.completeProfile": "Komplett Profil", "app.components.Unauthorized.completeProfileTitle": "Fëllt Äre Profil aus fir matzemaachen", "app.components.Unauthorized.noPermission": "Dir hutt keng Berechtegung, d<PERSON><PERSON> ze gesinn", "app.components.Unauthorized.notAuthorized": "Sorry, <PERSON><PERSON> sidd net berecht<PERSON>, op dës Säit zouzegräifen.", "app.components.Upload.errorImageMaxSizeExceeded": "D'ausgewielte Bild ass méi grouss wéi {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Eent oder méi vun den ausgewieltene Biller ass/ si méi grouss wéi {maxFileSize}MB", "app.components.Upload.onlyOneImage": "Dir kënnt nëmmen 1 Bild eroplueden", "app.components.Upload.onlyXImages": "Dir kënnt nëmme(n) {maxItemsCount} <PERSON><PERSON> er<PERSON>", "app.components.Upload.remaining": "verbleiwend", "app.components.Upload.uploadImageLabel": "Wielt e Bild (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Wielt een oder méi Biller aus", "app.components.UpsellTooltip.tooltipContent": "Dës Feature ass net an Ärem aktuelle Plang abegraff. Schwätzt mat Ärem Government Success Manager oder Admin fir et opzemaachen.", "app.components.UserName.anonymous": "Anonym", "app.components.UserName.anonymousTooltip2": "<PERSON><PERSON><PERSON> huet decidéiert hire Bäitrag anonymiséieren", "app.components.UserName.authorWithNoNameTooltip": "Ären Numm gouf autogeneréiert well Dir Ären Numm net aginn hutt. Update w.e.g. Äre Profil wann Dir et wëllt änneren.", "app.components.UserName.deletedUser": "Onbekannten Auteur", "app.components.UserName.verified": "Iwwerpréift", "app.components.VerificationModal.verifyAuth0": "Verifizéiert mat NemID", "app.components.VerificationModal.verifyBOSA": "Mat <PERSON>me oder eID verifizéieren", "app.components.VerificationModal.verifyBosaFas": "Verifizéiert mat itsme oder eID", "app.components.VerificationModal.verifyClaveUnica": "<PERSON> Clave Unica verifizéieren", "app.components.VerificationModal.verifyFakeSSO": "Verifiz<PERSON><PERSON><PERSON> mat Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Verifiz<PERSON><PERSON><PERSON> mat <PERSON>", "app.components.VerificationModal.verifyKeycloak": "Verifiz<PERSON><PERSON>t mat <PERSON>-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verifiz<PERSON><PERSON><PERSON> mat Mi<PERSON>", "app.components.VerificationModal.verifyTwoday2": "Verifizéiert mat BankID oder Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Iwwerpréift Är Identitéit", "app.components.VoteControl.budgetingFutureEnabled": "Dir kënnt Äre Budget ab dem {enabledFromDate} verdeelen.", "app.components.VoteControl.budgetingNotPermitted": "D'partizipativ Budgetéierung ass momentan net aktivéiert.", "app.components.VoteControl.budgetingNotPossible": "Et ass momentan net méiglech, Äre Budget ze änneren.", "app.components.VoteControl.budgetingNotVerified": "Wgl. {verifyAccountLink}, fir weider ze fueren. ", "app.components.VoteInputs._shared.currencyLeft1": "Dir hutt {budgetLeft} / {totalBudget} lénks", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Dir hutt {votesLeft, plural, =0 {keng <PERSON>itter méi iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 Kreditt} other {# Kreditter}} iwwreg}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Du hues {votesLeft, plural, =0 {keng <PERSON>ten iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 <PERSON>t} other {# <PERSON><PERSON>}} iwwreg}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Dir hutt {votesLeft, plural, =0 {keng Token méi iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 Token} other {# Token}} iwwreg}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Dir hutt {votesLeft, plural, =0 {keng <PERSON>ëmmen iwwreg} other {# vun {totalNumberOfVotes, plural, one {1 Stëmm} other {# Stëmmen}} iwwreg}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Dir hutt Äre Budget scho agereecht. Fir en z'änneren, klickt op \"Är Areeche änneren\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Dir hutt Äre Budget scho agereecht. <PERSON>r en z'änneren, gitt zer<PERSON>ck op d'Projetsäit a klickt op \"Är Areeche änneren\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgetéierung ass net verfügbar, well dës Phase net aktiv ass.", "app.components.VoteInputs.single.youHaveVotedForX2": "Dir hutt gestëmmt {votes, plural, =0 {# Optiounen} one {#Optioun} other {# Optiounen}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON><PERSON><PERSON> bedeit datt Dir all <PERSON> ve<PERSON>, déi mat dësem Input verbonne sinn, w<PERSON><PERSON>, <PERSON><PERSON><PERSON>ounen a Stëmmen. Dës Aktioun kann net réckgängeg gemaach ginn.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> d<PERSON>sen Input läschen?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Ofbriechen", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Confirm<PERSON><PERSON><PERSON>", "app.components.admin.SlugInput.resultingURL": "Resultéierend URL", "app.components.admin.SlugInput.slugTooltip": "De Slug ass den eemolege Set vu Wierder um Enn vun der Säitenadress oder URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON><PERSON><PERSON><PERSON>, funktionéiere Linken op d<PERSON>Säit net méi.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "<PERSON><PERSON> bäifügen", "app.components.admin.UserFilterConditions.field_email": "E-Mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Event Aschreiwungen", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON><PERSON> zu", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Communautéit Monitor Ëmfro", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Huet mat engem Bäitrag mat Status interagéiert", "app.components.admin.UserFilterConditions.field_participated_in_project": "<PERSON><PERSON> zu engem Projet bäigedroen", "app.components.admin.UserFilterConditions.field_participated_in_topic": "<PERSON><PERSON> eppes mat <PERSON> gepost", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Umeldungsdatum", "app.components.admin.UserFilterConditions.field_role": "Roll", "app.components.admin.UserFilterConditions.field_verified": "Verificatioun", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Propo<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "ass net fir eng vun dësen Evenementer registréiert", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "ass net fir all Event registréiert", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "ass fir eng vun dësen <PERSON> ugemellt", "app.components.admin.UserFilterConditions.predicate_attends_something": "ass fir op d'mannst een Event ugemellt", "app.components.admin.UserFilterConditions.predicate_begins_with": "Fänkt u mat", "app.components.admin.UserFilterConditions.predicate_commented_in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_contains": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_ends_on": "endegt den", "app.components.admin.UserFilterConditions.predicate_has_value": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_in": "huet eng Aktioun ausgeféiert", "app.components.admin.UserFilterConditions.predicate_is": "<PERSON>s", "app.components.admin.UserFilterConditions.predicate_is_admin": "ass e Verwalter", "app.components.admin.UserFilterConditions.predicate_is_after": "ass duerno", "app.components.admin.UserFilterConditions.predicate_is_before": "ass virdrun", "app.components.admin.UserFilterConditions.predicate_is_checked": "ass iwwerpréift", "app.components.admin.UserFilterConditions.predicate_is_empty": "ass eidel", "app.components.admin.UserFilterConditions.predicate_is_equal": "<PERSON>s", "app.components.admin.UserFilterConditions.predicate_is_exactly": "ass genee", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "ass méi grouss wéi", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "ass méi grouss oder d’selwecht wéi", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "ass en normale Benotzer", "app.components.admin.UserFilterConditions.predicate_is_not_area": "ausgeschloss Beräich", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "ausgeschloss Dossier", "app.components.admin.UserFilterConditions.predicate_is_not_input": "ausgeschloss Input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "ausgeschloss Projet", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "ausgeschloss Thema", "app.components.admin.UserFilterConditions.predicate_is_one_of": "ass ee vun", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "ee vun de <PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "ee vun de Classeure", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "ee vun den Input", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "ee vun de Projeten", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "ee vun den Themen", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "ass e Projetsmanager", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "ass méi kleng wéi", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "ass méi kleng oder d’selwecht wéi", "app.components.admin.UserFilterConditions.predicate_is_verified": "ass veri<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "fänkt net u mat", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "huet net kommentéiert", "app.components.admin.UserFilterConditions.predicate_not_contains": "enthält net", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "hält net op mat", "app.components.admin.UserFilterConditions.predicate_not_has_value": "huet kee <PERSON>", "app.components.admin.UserFilterConditions.predicate_not_in": "huet net bäigedroen", "app.components.admin.UserFilterConditions.predicate_not_is": "ass net", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "ass kee <PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "ass net iwwerpréift", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "ass net eidel", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "ass net", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "ass keen normale <PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "ass kee vun", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "ass kee Projetsmanager", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "ass net verifizéiert", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "huet kee Bäitrag gepost", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "huet net op de Kommentar reagéiert", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "huet net op Input reagéiert", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "huet sech net op en Event ugemellt", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "huet keng Ëmfro gemaach", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "huet sech net engagéiert", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "net un der Ofstëmmung deelgeholl", "app.components.admin.UserFilterConditions.predicate_nothing": "n<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_posted_input": "huet e Bäitrag gepost", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "op de Kommentar reagéiert", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "op Input reagéiert", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "op en Event registréiert", "app.components.admin.UserFilterConditions.predicate_something": "eppes", "app.components.admin.UserFilterConditions.predicate_taken_survey": "Ëmfro gemaach huet", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "huet sech engagé<PERSON>t", "app.components.admin.UserFilterConditions.predicate_voted_in3": "un der Ofstëmmung deelgeholl", "app.components.admin.UserFilterConditions.rulesFormLabelField": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Dir kritt keng Notifikatiounen iwwer Äre Bäitrag", "app.components.anonymousParticipationModal.cancel": "Ofbriechen", "app.components.anonymousParticipationModal.continue": "<PERSON>ert weider", "app.components.anonymousParticipationModal.participateAnonymously": "Maacht anonym mat", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON><PERSON> wäert <PERSON></b> s<PERSON><PERSON> <b>v<PERSON>, Projektmanager an aner Awunner fir dëse spezifesche Bäitrag verstoppen, sou datt kee fäeg ass dëse Bäitrag mat Iech ze verbannen. Anonym Contributiounen kënnen net geännert ginn, a ginn als final ugesinn.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Eis Plattform sécher fir all Benotzer ze maachen ass eng Haaptprioritéit fir eis. <PERSON><PERSON><PERSON>, also w.e.g. <PERSON><PERSON><PERSON>.", "app.components.avatar.titleForAccessibility": "Profil vun {fullName}", "app.components.customFields.mapInput.removeAnswer": "Äntwert ewechhuelen", "app.components.customFields.mapInput.undo": "Réckgängeg maachen", "app.components.customFields.mapInput.undoLastPoint": "Leschte Punkt réckgängeg maachen", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "Follegt d'Diskussioun", "app.components.followUnfollow.followTooltipInputPage2": "Folgend triggert E-Mailupdates iwwer Statusännerungen, offiziell Updates a Kommentaren. Dir kënnt {unsubscribeLink} zu all Moment.", "app.components.followUnfollow.followTooltipProjects2": "Folgend triggert E-Mailupdates iwwer Projet Ännerungen. Dir kënnt {unsubscribeLink} zu all Moment.", "app.components.followUnfollow.unFollow": "Unfollow", "app.components.followUnfollow.unsubscribe": "auszeschreiwen", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "e<PERSON>", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON>", "app.components.form.ErrorDisplay.previous": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.save": "Späicheren", "app.components.form.ErrorDisplay.userPickerPlaceholder": "<PERSON>ä<PERSON>t un ze tippen, fir no Benotzer-E-Mail oder Numm ze sichen......", "app.components.form.anonymousSurveyMessage2": "All Äntwerten op dës Ëmfro sinn anonymiséiert.", "app.components.form.backToInputManager": "<PERSON><PERSON><PERSON> zum Input Manager", "app.components.form.backToProject": "Zréck op de Projet", "app.components.form.components.controls.mapInput.removeAnswer": "Ewechzehuelen Äntwert", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "Läschte Punkt undoen", "app.components.form.controls.addressInputAriaLabel": "Adressinput", "app.components.form.controls.addressInputPlaceholder6": "<PERSON><PERSON> eng Adress un ...", "app.components.form.controls.adminFieldTooltip": "<PERSON>ld nëmme fir Verwalter zu gesinn", "app.components.form.controls.allStatementsError": "Eng Äntwert muss fir all Aussoen ausgewielt ginn.", "app.components.form.controls.back": "<PERSON><PERSON><PERSON>", "app.components.form.controls.clearAll": "Alles läschen", "app.components.form.controls.clearAllScreenreader": "Kloer all Äntwerten vun uewen Matrixentgasung Fro", "app.components.form.controls.clickOnMapMultipleToAdd3": "<PERSON><PERSON>t op d'<PERSON> fir ze z<PERSON>ien. <PERSON><PERSON>, zitt op Punkten fir se ze r<PERSON>cke<PERSON>.", "app.components.form.controls.clickOnMapToAddOrType": "<PERSON><PERSON>t op d'Kaart oder gitt eng Adress hei ënnen fir Är Äntwert ze addéieren.", "app.components.form.controls.confirm": "Confirm<PERSON><PERSON><PERSON>", "app.components.form.controls.cosponsorsPlaceholder": "<PERSON>änkt un en <PERSON>umm ze schreiwen fir ze sichen", "app.components.form.controls.currentRank": "Aktuelle Rang:", "app.components.form.controls.minimumCoordinates2": "E Minimum vu {numPoints} Kaartpunkte sinn erfuerderlech.", "app.components.form.controls.noRankSelected": "<PERSON><PERSON> Rang ausgewielt", "app.components.form.controls.notPublic1": "*<PERSON><PERSON><PERSON> gëtt nëmme mat Projektmanager gedeelt, an net un de Public.", "app.components.form.controls.optionalParentheses": "(optional)", "app.components.form.controls.rankingInstructions": "Drag and drop to Ranking Optiounen.", "app.components.form.controls.selectAsManyAsYouLike": "* W<PERSON>t esou vill wéi Dir wëllt", "app.components.form.controls.selectBetween": "* Wielt tëscht {minItems} an {maxItems} <PERSON><PERSON>ounen", "app.components.form.controls.selectExactly2": "* Wielt genau {selectExactly, plural, one {#Optioun} other {# Optiounen}}", "app.components.form.controls.selectMany": "*<PERSON><PERSON><PERSON>, wéi <PERSON>r wëllt", "app.components.form.controls.tapOnFullscreenMapToAdd4": "<PERSON><PERSON><PERSON> op d'Ka<PERSON> fir ze zéien. <PERSON><PERSON>, zitt op Punkten fir se ze r<PERSON>.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "<PERSON><PERSON><PERSON> op d'Ka<PERSON> fir ze zéien.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tip<PERSON> op der Kaart fir Är Äntwert ze addéieren.", "app.components.form.controls.tapOnMapToAddOrType": "Tip<PERSON> op der Ka<PERSON> oder gitt eng Adress hei ënnen fir Är Äntwert derbäi ze ginn.", "app.components.form.controls.tapToAddALine": "T<PERSON><PERSON> fir eng Zeil ze addéieren", "app.components.form.controls.tapToAddAPoint": "T<PERSON><PERSON> fir e Punkt ze addéieren", "app.components.form.controls.tapToAddAnArea": "T<PERSON><PERSON> fir e Gebitt ze addéieren", "app.components.form.controls.uploadShapefileInstructions": "* Eroplueden eng Zip Datei mat enger oder méi Formdateien.", "app.components.form.controls.validCordinatesTooltip2": "<PERSON><PERSON> d'Plaz net ënnert den Optiounen ugewise gëtt wéi Dir tippt, kënnt Dir gëlteg Koordinaten am Format 'Latitude, Longitude' addéieren fir eng präzis Plaz ze spezifizéieren (zB: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} aus {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} aus {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} aus {total}, wou {maxValue} {maxLabel}ass", "app.components.form.error": "Feeler", "app.components.form.locationGoogleUnavailable": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, dat vu Google Maps zur Verfügung gestallt gouf, net lueden.", "app.components.form.progressBarLabel": "Ëmfro <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.submitApiError": "Et gouf e <PERSON> beim Ofsch<PERSON>cken vum Formulaire. Préift dësen wgl. ob  Feeler a probéiert et nach eng Kéier.", "app.components.form.verifiedBlocked": "Dir kënnt dëst Feld net änneren well et verifizéiert Informatioun enthält", "app.components.formBuilder.Page": "Säit", "app.components.formBuilder.accessibilityStatement": "Accessibilitéit <PERSON>", "app.components.formBuilder.addAnswer": "Äntwert bäifügen", "app.components.formBuilder.addStatement": "<PERSON><PERSON>", "app.components.formBuilder.agree": "Averstanen", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "<PERSON><PERSON> <PERSON><PERSON>ougang zu eisem AI Package hutt, kënnt Dir Textreaktiounen mat AI zesummefaassen an kategoriséieren", "app.components.formBuilder.askFollowUpToggleLabel": "Frot de Suivi", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Knäppchen Label", "app.components.formBuilder.buttonLink": "Knäppchen Link", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Ofbriechen", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "Vill auswielen", "app.components.formBuilder.chooseOne": "<PERSON><PERSON>", "app.components.formBuilder.close": "Zoumaachen", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "<PERSON><PERSON> k<PERSON>", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON>, ech w<PERSON><PERSON> verl<PERSON>sen", "app.components.formBuilder.content": "Inhalt", "app.components.formBuilder.continuePageLabel": "<PERSON>ert weider", "app.components.formBuilder.cosponsors": "Co-<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.default": "Standardastellung", "app.components.formBuilder.defaultContent": "Standardastellung Inhalt", "app.components.formBuilder.delete": "Läschen", "app.components.formBuilder.deleteButtonLabel": "Läschen", "app.components.formBuilder.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "<PERSON><PERSON><PERSON> gouf schonn an de Formulaire agedroen. Standardinhalter kënnen nëmmen eng Kéier benotzt ginn.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Benotzerdefinéiert Inhalt bäizefügen ass net Deel vun Ärer aktueller Lizenz. Kontaktéiert Äre GovSuccess Manager fir méi doriwwer ze léieren.", "app.components.formBuilder.disagree": "Desaccord", "app.components.formBuilder.displayAsDropdown": "Weist als Dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "Weist d'Optiounen an engem Dropdown-<PERSON><PERSON>. <PERSON>n Dir vill Méiglechkeeten hutt, ass dëst recommandéiert.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.drawRoute": "<PERSON><PERSON>", "app.components.formBuilder.dropPin": "Drop Pin", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Gitt op d'mannst 1 Äntwert. Maacht weg datt all Äntwert en Titel muss hunn.", "app.components.formBuilder.emptyOptionError": "Gitt mindestens 1 Äntwert", "app.components.formBuilder.emptyStatementError": "Gitt op d'mannst 1 Ausso", "app.components.formBuilder.emptyTitleError": "Gitt e Froen-Titel un", "app.components.formBuilder.emptyTitleMessage": "Gitt en Titel fir all Äntwerten", "app.components.formBuilder.emptyTitleStatementMessage": "Gitt en Titel fir all Aussoen", "app.components.formBuilder.enable": "Aktivéieren", "app.components.formBuilder.errorMessage": "<PERSON>t g<PERSON> e <PERSON>, wgl. behief<PERSON> de Problem, fir Är Ännerunge späicheren ze kënnen", "app.components.formBuilder.fieldGroup.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (optionell)", "app.components.formBuilder.fieldGroup.title": "Titel (optionell)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Momentan sinn Äntwerten op dës Froen nëmmen an der exportéierter Excel-Datei am Input Manager disponibel, a fir Benotzer net siichtbar.", "app.components.formBuilder.fieldLabel": "Äntwertenauswiel", "app.components.formBuilder.fieldLabelStatement": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.fileUpload": "Datei-Upload", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "<PERSON><PERSON> <PERSON><PERSON> als Kontext oder stellt Locationbaséiert Froen un d'Participanten.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Fir eng optimal Benotzererfarung empfeelen mir net Punkt-, Streck- oder Beräich Froen op Kaart-baséiert Säiten ze addéieren.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal Säit", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Survey Mapping Features sinn net an Ärer aktueller Lizenz abegraff. Kontaktéiert Äre GovSuccess Manager fir méi ze léieren.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Säit <PERSON>", "app.components.formBuilder.formEnd": "Enn vum Formulaire", "app.components.formBuilder.formField.cancelDeleteButtonText": "Ofbriechen", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON>, <PERSON><PERSON><PERSON> läschen", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON>", "app.components.formBuilder.formField.delete": "Läschen", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "<PERSON><PERSON>s S<PERSON>it läschen wäert och d'Logik verbonne mat et läschen. Sidd Dir sécher datt Dir et läschen wëllt?", "app.components.formBuilder.formField.deleteResultsInfo": "<PERSON><PERSON><PERSON> kann net réckgängeg gemaach ginn", "app.components.formBuilder.goToPageInputLabel": "Déi nächst Säit ass:", "app.components.formBuilder.good": "<PERSON><PERSON>", "app.components.formBuilder.helmetTitle": "Form Builder", "app.components.formBuilder.imageFileUpload": "Bild-Upload", "app.components.formBuilder.invalidLogicBadgeMessage": "Ongëlteg Lo<PERSON>", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON> (optional)", "app.components.formBuilder.labelsTooltipContent2": "Wielt fakultativ Etiketten fir eng vun de linear Skala Wäerter.", "app.components.formBuilder.lastPage": "Enn", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Sidd Dir sécher datt Dir fortgoe wëllt?", "app.components.formBuilder.leaveBuilderText": "Dir hutt net gespäichert Ännerungen. Spuert w.e.g. ier Dir fortgeet. Wann Dir fortgeet, verléiert Dir Är Ännerungen.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON><PERSON>, mussen d'Befroten déi spezifizéiert Zuel vun Äntwerten auswielen fir weiderzekommen.", "app.components.formBuilder.limitNumberAnswers": "Limitéiert d'Zuel vun den Äntwerten", "app.components.formBuilder.linePolygonMapWarning2": "Linn a Polygon Zeechnen kënnen net Zougänglechkeetsnormen entspriechen. Méi Informatioun fannt Dir am {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Linearmossstaf", "app.components.formBuilder.locationDescription": "<PERSON><PERSON>", "app.components.formBuilder.logic": "Logik", "app.components.formBuilder.logicAnyOtherAnswer": "All aner Äntwert", "app.components.formBuilder.logicConflicts.conflictingLogic": "Konflikt Logik", "app.components.formBuilder.logicConflicts.interQuestionConflict": "<PERSON>ës <PERSON> enthält Froen déi op verschidde Säiten féieren. Wann d'Participanten méi Froen beäntweren, gëtt déi wäitst Säit ugewisen. Vergewëssert Iech datt dëst Verhalen mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Dës Sä<PERSON> huet verschidde Logik Regelen applizéiert: Multi-Select Fro <PERSON>k, Säit-Niveau Logik, an Inter-Fro Logik. Wann dës Konditiounen iwwerlappt, gëtt Fro Logik Virrang iwwer Säit Logik, an déi wäit Säit gëtt gewisen. Iwwerpréift d'Logik fir sécherzestellen datt se mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Dës S<PERSON> enthält eng Multi-Select Fro wou Optiounen op verschidde Säiten féieren. Wann d'Participanten méi Optiounen auswielen, gëtt déi wäitst Säit ugewisen. Vergewëssert Iech datt dëst Verhalen mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Dës Säit enthält eng Multi-Select Fro wou Optiounen op verschidde Säiten féieren an huet Froen déi op aner Säiten féieren. Déi wäitst Säit gëtt gewisen wann dës Konditiounen iwwerlappen. Vergewëssert Iech datt dëst Verhalen mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Dës Säit enthält eng Multi-Select Fro wou Optiounen op verschidde Säiten féieren an huet Logik op béid Säit a Fro Niveau gesat. Fro Logik wäert Virrang huelen, an déi wäit Säit gëtt gewisen. Vergewëssert Iech datt dëst Verhalen mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Dës Säit huet Logik op béide Säitenniveau a Froniveau gesat. Fro Logik wäert Virrang iwwer Säit-Niveau Logik huelen. Vergewëssert Iech datt dëst Verhalen mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Dës Säit huet Logik op béide Säiten a Fro Niveauen gesat, a verschidde Froen direkt op verschidde Säiten. Fro Logik wäert Virrang huelen, an déi wäit Säit gëtt gewisen. Vergewëssert Iech datt dëst Verhalen mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.logicNoAnswer2": "Net geäntwert", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Wann all aner Äntwert", "app.components.formBuilder.logicPanelNoAnswer": "Wann net geäntwert", "app.components.formBuilder.logicValidationError": "Logik kann net mat viregte Säite verknëppt ginn", "app.components.formBuilder.longAnswer": "Laang Äntwerten", "app.components.formBuilder.mapConfiguration": "<PERSON><PERSON>", "app.components.formBuilder.mapping": "Mapping", "app.components.formBuilder.mappingNotInCurrentLicense": "Survey Mapping Features sinn net an Ärer aktueller Lizenz abegraff. Kontaktéiert Äre GovSuccess Manager fir méi ze léieren.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "Kolonnen", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "Multiple Choice", "app.components.formBuilder.multipleChoiceHelperText": "Wann verschidde Méiglechkeeten op verschidde Säiten féieren an d'Participanten méi wéi eng auswielen, gëtt déi wäitst Säit gewisen. Vergewëssert Iech datt dëst Verhalen mat Ärem beabsichtigte Floss ausriicht.", "app.components.formBuilder.multipleChoiceImage": "Bild <PERSON>", "app.components.formBuilder.multiselect.maximum": "Maximum", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "<PERSON><PERSON>", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Opmaachen", "app.components.formBuilder.optional": "Optionell", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "\"<PERSON><PERSON>\" <PERSON><PERSON><PERSON>", "app.components.formBuilder.otherOptionTooltip": "Erlaabt d'Participanten eng personaliséiert Äntwert anzeginn wann déi geliwwert Äntwerten net mat hirer Preferenz entspriechen", "app.components.formBuilder.page": "Säit", "app.components.formBuilder.pageCannotBeDeleted": "<PERSON><PERSON><PERSON> kann net geläscht ginn.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "<PERSON><PERSON><PERSON> kann net geläscht ginn an erlaabt keng zousätzlech Felder dobäi ginn.", "app.components.formBuilder.pageRuleLabel": "Nächst Säit ass:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Wann keng Logik derbäi ass, wäert d'Form säin normale Flux verfollegen. Wann souwuel d'Säit a seng Froen Logik hunn, wäert d'Frologik Virrang huelen. Vergewëssert Iech datt dëst mat Ärem geplangte Floss ausriicht. Fir méi Informatioun, besicht {supportPageLink}", "app.components.formBuilder.preview": "Virschau:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Co-Sponsore ginn net am erofgeluedene PDF ugewisen an den Import iwwer FormSync gëtt net ënnerstëtzt.", "app.components.formBuilder.printSupportTooltip.fileupload": "Froen zum Eroplueden vun Dateien ginn am erofgeluedene PDF als net ënnerstëtzt ugewisen an den Import iwwer FormSync gëtt net ënnerstëtzt.", "app.components.formBuilder.printSupportTooltip.mapping": "Mapping-Froen ginn am erofgeluedene PDF ugewisen, awer d'Schichten sinn net sichtbar. Mapping-Froen ginn net fir den Import iwwer FormSync ënnerstëtzt.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrixfroen ginn am erofgeluedene PDF ugewisen, awer si ginn de Moment net iwwer FormSync importéiert.", "app.components.formBuilder.printSupportTooltip.page": "Säitentitelen a Beschreiwunge ginn als Sektiounstitel am erofgeluedene PDF ugewisen.", "app.components.formBuilder.printSupportTooltip.ranking": "Rankingfroen ginn am erofgeluedene PDF ugewisen, awer si ginn de Moment net iwwer FormSync importéiert.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags ginn am erofgeluedene PDF als net ënnerstëtzt ugewisen an den Import iwwer FormSync gëtt net ënnerstëtzt.", "app.components.formBuilder.proposedBudget": "Proposéierte Budget", "app.components.formBuilder.question": "<PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON><PERSON><PERSON> kann net geläscht ginn.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (optionell)", "app.components.formBuilder.questionTitle": "Froen-Titel", "app.components.formBuilder.randomize": "Randomiséieren", "app.components.formBuilder.randomizeToolTip": "D'Uerdnung vun den Äntwerten gëtt pro Benotzer zoufälleg", "app.components.formBuilder.range": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.ranking": "Ranking", "app.components.formBuilder.rating": "Bewäertung", "app.components.formBuilder.removeAnswer": "Äntwert läschen", "app.components.formBuilder.required": "Erfuerderlech", "app.components.formBuilder.requiredToggleLabel": "D’Beäntwerte vun dëser Fro erfuerderlech maachen", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON><PERSON> <PERSON> ass:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Wann d'Äntwerten enthalen:", "app.components.formBuilder.save": "Späicheren", "app.components.formBuilder.selectRangeTooltip": "Wielt de Maximalwäert fir Är Skala.", "app.components.formBuilder.sentiment": "Sentiment <PERSON><PERSON>a", "app.components.formBuilder.shapefileUpload": "Esri shapefile eropluede", "app.components.formBuilder.shortAnswer": "Kuerzäntwert", "app.components.formBuilder.showResponseToUsersToggleLabel": "Äntwert de Benotzer weisen", "app.components.formBuilder.singleChoice": "Single Choice", "app.components.formBuilder.staleDataErrorMessage2": "Et gouf e Problem. Dësen Input Formulaire gouf méi viru kuerzem anzwousch anescht gespäichert. Dëst kann sinn well Dir oder en anere Benotzer et opgemaach huet fir an enger anerer Browserfenster z'änneren. Erfrëscht w.e.g. d'Säit fir dee leschte Formulaire ze kréien an dann erëm Är Ännerungen ze maachen.", "app.components.formBuilder.stronglyAgree": "Ganz averstanen", "app.components.formBuilder.stronglyDisagree": "Ganz averstanen", "app.components.formBuilder.supportArticleLinkText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.tags": "Taggen", "app.components.formBuilder.title": "Titel", "app.components.formBuilder.toLabel": "zu", "app.components.formBuilder.unsavedChanges": "Dir hutt net gespäichert Ännerungen", "app.components.formBuilder.useCustomButton2": "Benotzt Benotzerdefinéiert Säit Knäppchen", "app.components.formBuilder.veryBad": "Ganz schlecht", "app.components.formBuilder.veryGood": "<PERSON><PERSON><PERSON> gutt", "app.components.ideas.similarIdeas.engageHere": "Engagéiert hei", "app.components.ideas.similarIdeas.noSimilarSubmissions": "<PERSON><PERSON> Soumissioun fonnt.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Mir hunn ähnlech Soumissioun fonnt - mat hinnen engagéieren kann hëllefen se méi staark ze maachen!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Ähnlech Soumissioun scho gepost:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Sich no ähnlechen Soumissioun ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> wéi een Dag} one {# Dag} other {# Deeg}} iwwereg", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  Wochen iwwereg", "app.components.screenReaderCurrency.AED": "<PERSON><PERSON><PERSON><PERSON> Arabesch Emirater Dirham", "app.components.screenReaderCurrency.AFN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ALL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ANG": "Holland Antilliaanse Gulden", "app.components.screenReaderCurrency.AOA": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ARS": "Argentinesch peso", "app.components.screenReaderCurrency.AUD": "Australeschen Dollar", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "<PERSON><PERSON><PERSON><PERSON><PERSON> Manat", "app.components.screenReaderCurrency.BAM": "Bosnien-Herzegowina Cabriolet uerg", "app.components.screenReaderCurrency.BBD": "Barbados Dollar", "app.components.screenReaderCurrency.BDT": "Bangladesch <PERSON>", "app.components.screenReaderCurrency.BGN": "Bulgaresch Lev", "app.components.screenReaderCurrency.BHD": "Bahrain Dinar", "app.components.screenReaderCurrency.BIF": "Burundi Frang", "app.components.screenReaderCurrency.BMD": "Bermuda Dollar", "app.components.screenReaderCurrency.BND": "Brunei Dollar", "app.components.screenReaderCurrency.BOB": "Bolivianesche <PERSON>no", "app.components.screenReaderCurrency.BOV": "Bolivianesche <PERSON>l", "app.components.screenReaderCurrency.BRL": "Brasilianesche Real", "app.components.screenReaderCurrency.BSD": "Bahamian Dollar", "app.components.screenReaderCurrency.BTN": "Bhutan Ngultrum", "app.components.screenReaderCurrency.BWP": "Botswana Pula", "app.components.screenReaderCurrency.BYR": "Wäissrussland ruble", "app.components.screenReaderCurrency.BZD": "Belize Dollar", "app.components.screenReaderCurrency.CAD": "kanadeschen Dollar", "app.components.screenReaderCurrency.CDF": "Kongolesesche Frang", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Schwäizer Frang", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "<PERSON><PERSON><PERSON> (UF)", "app.components.screenReaderCurrency.CLP": "Chilenesche Peso", "app.components.screenReaderCurrency.CNY": "<PERSON><PERSON>", "app.components.screenReaderCurrency.COP": "Kolumbianesche Peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rica Colón", "app.components.screenReaderCurrency.CRE": "Kredit", "app.components.screenReaderCurrency.CUC": "Kubanesch Convertible Peso", "app.components.screenReaderCurrency.CUP": "Kubanesch Peso", "app.components.screenReaderCurrency.CVE": "Kap Verde escudo", "app.components.screenReaderCurrency.CZK": "Tschechesch Kroun", "app.components.screenReaderCurrency.DJF": "Djibouti Frang", "app.components.screenReaderCurrency.DKK": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.DOP": "Dominikanesch peso", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "egypteschen Pond", "app.components.screenReaderCurrency.ERN": "Eritreesch Nakfa", "app.components.screenReaderCurrency.ETB": "Ethiopian Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fidschi Dollar", "app.components.screenReaderCurrency.FKP": "Falkland Islands Pound", "app.components.screenReaderCurrency.GBP": "<PERSON><PERSON>cht Pond", "app.components.screenReaderCurrency.GEL": "Georgian Lari", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Gibraltar Pound", "app.components.screenReaderCurrency.GMD": "Gambian Dalasi", "app.components.screenReaderCurrency.GNF": "Guinea Frang", "app.components.screenReaderCurrency.GTQ": "Guatemalan <PERSON>", "app.components.screenReaderCurrency.GYD": "Guyana Dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong Dollar", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "Ungaresche forint", "app.components.screenReaderCurrency.IDR": "Indonesesch Rupiah", "app.components.screenReaderCurrency.ILS": "Israeli nei shekel", "app.components.screenReaderCurrency.INR": "Indesch Rupie", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "iraneschen rial", "app.components.screenReaderCurrency.ISK": "Islännesch Kroun", "app.components.screenReaderCurrency.JMD": "Jamaikaneschen Dollar", "app.components.screenReaderCurrency.JOD": "<PERSON>", "app.components.screenReaderCurrency.JPY": "<PERSON>esch <PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON>ia shilling", "app.components.screenReaderCurrency.KGS": "Kirgisistan Som", "app.components.screenReaderCurrency.KHR": "Kambodscha Riel", "app.components.screenReaderCurrency.KMF": "Comoresche Frang", "app.components.screenReaderCurrency.KPW": "Nordkoreanesche Won", "app.components.screenReaderCurrency.KRW": "Südkoreanesch gewonn", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Cayman Islands Dollar", "app.components.screenReaderCurrency.KZT": "Kasachstanesch Tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libanesche Pond", "app.components.screenReaderCurrency.LKR": "Sri Lanka rupee", "app.components.screenReaderCurrency.LRD": "Liberian Dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Li<PERSON>ues<PERSON> Li<PERSON>", "app.components.screenReaderCurrency.LVL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LYD": "Libesch Dinar", "app.components.screenReaderCurrency.MAD": "Marokkaneschen dirham", "app.components.screenReaderCurrency.MDL": "Moldawesche Leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Mazedonesch denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauritanian Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritius rupee", "app.components.screenReaderCurrency.MVR": "Mal<PERSON><PERSON>", "app.components.screenReaderCurrency.MWK": "Malawi Kwacha", "app.components.screenReaderCurrency.MXN": "Mexikanesche Peso", "app.components.screenReaderCurrency.MXV": "Mexikanesch Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malayseschen <PERSON>git", "app.components.screenReaderCurrency.MZN": "Mozambican Metical", "app.components.screenReaderCurrency.NAD": "Namibia Dollar", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Nicaraguan Cordoba", "app.components.screenReaderCurrency.NOK": "Norwegesch Kroun", "app.components.screenReaderCurrency.NPR": "Nepalese rupee", "app.components.screenReaderCurrency.NZD": "Neiséiland Dollar", "app.components.screenReaderCurrency.OMR": "Omanesche Rial", "app.components.screenReaderCurrency.PAB": "Panameseschen Balboa", "app.components.screenReaderCurrency.PEN": "Peruanesch Sol", "app.components.screenReaderCurrency.PGK": "Papua New Guinean Kina", "app.components.screenReaderCurrency.PHP": "Philippinen peso", "app.components.screenReaderCurrency.PKR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.PLN": "polnes<PERSON> Zloty", "app.components.screenReaderCurrency.PYG": "Paraguayan Guaraní", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RSD": "Serbesche Dinar", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RWF": "Rwandan <PERSON>", "app.components.screenReaderCurrency.SAR": "Saudi Riyal", "app.components.screenReaderCurrency.SBD": "Solomon Islands Dollar", "app.components.screenReaderCurrency.SCR": "Seychelles rupee", "app.components.screenReaderCurrency.SDG": "Sudanesescht Pond", "app.components.screenReaderCurrency.SEK": "Schwedesch krona", "app.components.screenReaderCurrency.SGD": "Singapur Dollar", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somalesch shilling", "app.components.screenReaderCurrency.SRD": "Surinamesch Dollar", "app.components.screenReaderCurrency.SSP": "Südsudanesescht Pond", "app.components.screenReaderCurrency.STD": "São Tomé a Príncipe Dobra", "app.components.screenReaderCurrency.SYP": "syresche Pond", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thai baht", "app.components.screenReaderCurrency.TJS": "Tadschikistan Somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistan Manat", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TRY": "tierkesch Lira", "app.components.screenReaderCurrency.TTD": "Trinidad an Tobago Dollar", "app.components.screenReaderCurrency.TWD": "Neien Taiwan Dollar", "app.components.screenReaderCurrency.TZS": "Tanzanian shilling", "app.components.screenReaderCurrency.UAH": "<PERSON><PERSON>", "app.components.screenReaderCurrency.UGX": "Uganda shilling", "app.components.screenReaderCurrency.USD": "US Dollar", "app.components.screenReaderCurrency.USN": "US Dollar (nächsten Dag)", "app.components.screenReaderCurrency.USS": "US Dollar (Selwechten Dag)", "app.components.screenReaderCurrency.UYI": "Uruguayesche Peso an Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayanesche Peso", "app.components.screenReaderCurrency.UZS": "Usbekistaner <PERSON>", "app.components.screenReaderCurrency.VEF": "Venezolanesche Bolívar", "app.components.screenReaderCurrency.VND": "<PERSON><PERSON>", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Zentralafrikanesche CFA Frang", "app.components.screenReaderCurrency.XAG": "<PERSON><PERSON><PERSON> (eng <PERSON>)", "app.components.screenReaderCurrency.XAU": "Gold (eng <PERSON>)", "app.components.screenReaderCurrency.XBA": "Europäesch Komposit Eenheet (EURCO)", "app.components.screenReaderCurrency.XBB": "Europäesch Währungsunitéit (EMU-6)", "app.components.screenReaderCurrency.XBC": "Europäesch Kont Eenheet 9 (EUA-9)", "app.components.screenReaderCurrency.XBD": "Europäesch Kont Eenheet 17 (EUA-17)", "app.components.screenReaderCurrency.XCD": "East Karibik Dollar", "app.components.screenReaderCurrency.XDR": "Special <PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XFU": "UIC Frang", "app.components.screenReaderCurrency.XOF": "Westafrikaneschen CFA Frang", "app.components.screenReaderCurrency.XPD": "Palladium (eng <PERSON>)", "app.components.screenReaderCurrency.XPF": "CFP Frang", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (eng <PERSON>)", "app.components.screenReaderCurrency.XTS": "<PERSON>n speziell reservéiert fir Testzwecker", "app.components.screenReaderCurrency.XXX": "<PERSON><PERSON>", "app.components.screenReaderCurrency.YER": "Yemeni rial", "app.components.screenReaderCurrency.ZAR": "Südafrikanesche Rand", "app.components.screenReaderCurrency.ZMW": "Zambian <PERSON>", "app.components.screenReaderCurrency.amount": "Betrag", "app.components.screenReaderCurrency.currency": "Währung", "app.components.trendIndicator.lastQuarter2": "<PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.applicability": "<PERSON><PERSON><PERSON>chkeetserkläerung gëllt fir eng {demoPlatformLink}, déi representativ fir dëse Site ass; si benotzt deselwechte Quellcode an déiselwecht Funktionalitéit.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Bewäertungsmethod", "app.containers.AccessibilityStatement.assesmentText2022": "D’Zougänglechkeet vun dësem Site gouf vun enger externer Instanz ausgewäert, déi net an den Design and d’Entwécklung abezunn ass. {demoPlatformLink} kann hei {statusPageLink} identifizéiert ginn.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "<PERSON>r kënnt Är Preferenzen änneren", "app.containers.AccessibilityStatement.changePreferencesText": "Jidderzäit, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Ausname vun der Konformitéit", "app.containers.AccessibilityStatement.conformanceStatus": "Konformitéitsstatus", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Mir maachen eis Bescht eis Inhalter fir jiddereen zougänglech ze maachen. Allerdéngs kënne verschidden Inhalter vun der Plattform ënner de folgenden Ëmstänn onzougänglech sinn: ", "app.containers.AccessibilityStatement.demoPlatformLinkText": "Demosite", "app.containers.AccessibilityStatement.email": "E-Mail:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Embedded Ëmfro Tools", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Déi embedded Ëmfro Tools déi verfügbar sinn fir op dëser Plattform ze benotzen sinn Drëtt Partei Software a kënnen net zougänglech sinn.", "app.containers.AccessibilityStatement.exception_1": "Eis Plattforme fir digital Bedeelegung erméiglechen notzergeneréiert Inhalter, déi vun <PERSON>zelpersounen an Organisatioune gepost ginn. Et ass méig<PERSON>ch, datt <PERSON>, Biller oder aner Dateitypen inklusiv Multimediadateien vu Plattformbenotzer als Annex op d'Plattform eropgelueden oder an Textfelder agefüügt ginn. Dës Inhalter sinn ënner Ëmstänn net vollstänneg zougänglech.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Är Kommentaren hëllefen eis d'Zougänglechkeet vun eisem Site ze verbesseren. Kontaktéiert eis wgl. per:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Bemierkungen a Virschléi", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bréissel, Belsch", "app.containers.AccessibilityStatement.headTitle": "Zougänglechkeetserklärung | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} verf<PERSON>t sech, eng Plattform bereetzestellen, déi alle Benotzer zouhänglech ass, onofhängeg vun Technik oder Fäegkeeten. Déi betreffend aktuell Zougänglechkeetsnormen hale mir duerch eis lafend Ustrengungen, d’Zougänglechkeet and d’Benotzerfrëndlechkeet vun eise Plattformen fir all Benotzer ze maximéieren, an.", "app.containers.AccessibilityStatement.mapping": "Mapping", "app.containers.AccessibilityStatement.mapping_1": "Kaarten op der Plattform entspriechen deelweis Accessibilitéitsnormen. <PERSON><PERSON>, Zoom, an UI Widgets kënne mat enger Tastatur kontrolléiert ginn wann Dir Kaarten kuckt. Administrateuren kënnen och de Stil vun de Kaartschichten am Back Office konfiguréieren, oder d'Esri Integratioun ben<PERSON>, fir méi zougänglech Faarfpalette a Symbologie ze kreéieren. D'Benotzung vu verschiddene Linnen- oder Polygonstiler (zB gestreckte Linnen) hëlleft och d'Kaartschichten z'ënnerscheeden wou et méiglech ass, an och wann esou Styling net an eiser Plattform zu dësem Zäitpunkt konfiguréiert ka ginn, kann et konfiguréiert ginn wann Dir Kaarten mat der Esri Integratioun benotzt.", "app.containers.AccessibilityStatement.mapping_2": "Kaarten op der Plattform sinn net voll zougänglech well se net hörbar Basiskaarten, Kaartschichten oder Trends an den Daten u Benotzer presentéieren déi Écran Lieser benotzen. Voll zougänglech Kaarte missten d'Kaartschichten hörbar presentéieren an all relevant Trends an den Daten beschreiwen. Ausserdeem ass d'Zeechnen vun enger Zeil a Polygon Kaart an Ëmfroen net zougänglech well Formen net mat enger Tastatur gezeechent kënne ginn. Alternativ Input Methoden sinn net disponibel zu dësem Zäitpunkt wéinst technescher Komplexitéit.", "app.containers.AccessibilityStatement.mapping_3": "Fir d'Zeechnung vun enger Linn a Polygon Kaart méi zougänglech ze maachen, empfeelen mir eng Aféierung oder Erklärung an der Ëmfro Fro oder Säit Beschreiwung vun deem wat d'<PERSON>art weist an all relevant Trends. Ausserdeem kéint eng kuerz oder laang Äntwert Textfro zur Verfügung gestallt ginn, sou datt d'Befroten hir Äntwert einfach beschreiwen kënnen, wann néideg (anstatt op d'Kaart ze klicken). Mir recommandéieren och Kontaktinformatioune fir de Projektmanager abegraff, sou datt Befroten déi eng Kaartfro net ausfëllen kënnen eng alternativ Method ufroen fir d'Fro ze beäntweren (zB Videotreffen).", "app.containers.AccessibilityStatement.mapping_4": "Fir Ideatiounsprojeten a Virschléi gëtt et eng Optioun fir Inputen an enger Kaartvisioun ze weisen, déi net zougänglech ass. W<PERSON><PERSON> och <PERSON>, fir dës Methoden gëtt et eng alternativ Lëscht vun Input verfügbar, déi zougänglech ass.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Eis Online-Workshoppen hunn eng Live-Videostreaming-Komponent, déi aktuell Ënnertitelen net ënnerstëtzt.", "app.containers.AccessibilityStatement.pageDescription": "Zougänglechkeetsstandards vun dëser Websäit", "app.containers.AccessibilityStatement.postalAddress": "Postadress:", "app.containers.AccessibilityStatement.publicationDate": "Verëffentlechungsdatum", "app.containers.AccessibilityStatement.publicationDate2024": "Dës Accessibilitéitserklärung gouf den 21. August 2024 publizéiert.", "app.containers.AccessibilityStatement.responsiveness": "Eist <PERSON>, op Feedback bannent 1-2 Aarbechtsdeeg ze äntweren.", "app.containers.AccessibilityStatement.statusPageText": "Statussäit", "app.containers.AccessibilityStatement.technologiesIntro": "Den Zougang zu dësem Site erfuerdert déi folgend Technologien: ", "app.containers.AccessibilityStatement.technologiesTitle": "Technologien", "app.containers.AccessibilityStatement.title": "Zougänglechkeetserklärung", "app.containers.AccessibilityStatement.userGeneratedContent": "Benotzergeneréier<PERSON>halter", "app.containers.AccessibilityStatement.workshops": "Workshoppen", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Wielt Projet", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "D'Benotzung vum Inhalt Builder léisst Iech méi fortgeschratt Layoutoptiounen benotzen. Fir Sprooche wou keen Inhalt am Inhalt Builder verfügbar ass, gëtt de reguläre Projetbeschreiwungsinhalt amplaz ugewisen.", "app.containers.AdminPage.ProjectDescription.linkText": "Änneren Beschreiwung am Inhalt Builder", "app.containers.AdminPage.ProjectDescription.saveError": "<PERSON>ppes ass falsch gaangen beim Späicheren vun der Projektbeschreiwung.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Benotzt Inhalt Builder fir Beschreiwung", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "D'Benotzung vum Inhalt Builder léisst Iech méi fortgeschratt Layoutoptiounen benotzen.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON><PERSON><PERSON> Projet", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Enn vun der Ëmfo", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Eng Smart Grupp uleeën", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "<PERSON><PERSON><PERSON>, déi all déi follgend Konditiounen erfëllen, ginn automatesch dem Grupp bäigefüügt:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON><PERSON> eng Roll an", "app.containers.AdminPage.Users.UsersGroup.rulesError": "E puer Konditioune sinn onvollstänneg", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Grupp späicheren", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Smart Gruppen konfiguréieren ass net Deel vun Ärer aktueller Lizenz. Kontaktéiert Äre GovSuccess Manager fir méi doriwwer ze léieren.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Stellt e Gruppennumm zur Verfügung", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "D’Verifikatioun ass op Ärer Plattform gespaart, huelt d’Verificatiounsroll ewech oder kontaktéiert de Support", "app.containers.App.appMetaDescription": "Wëllkomm op der participativer Plattform vum {Zesumme Vereinfachen}. Kuckt d'Projeten an diskutéiert mat!", "app.containers.App.loading": "Um Lueden ... ", "app.containers.App.metaTitle1": "Bierger Engagement Plattform | {orgName}", "app.containers.App.skipLinkText": "Wiesselt op den Haaptinhalt", "app.containers.AreaTerms.areaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AreaTerms.areasTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "<PERSON> Kont mat dëser E-Mail gëtt et schonn. Dir kënnt Iech ausloggen, Iech mat dëser E-Mailadress aloggen an Äre Kont op der Astellungssäit verifizéieren.", "app.containers.Authentication.steps.AccessDenied.close": "Zoumaachen", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Dir erfëllt net den Ufuerderunge fir un dësem Prozess deelzehuelen.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "<PERSON><PERSON> z<PERSON>ck op Single Sign-on Verifizéierung", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Gitt w.e.g. en <PERSON> un", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "<PERSON>tt <PERSON> schonn e <PERSON>? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "<PERSON><PERSON>ech un", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-Mailen an dëser Kategorie", "app.containers.CampaignsConsentForm.messageError": "<PERSON><PERSON> vun Ären E-Mail-Präferenzen ass e Feeler opgetratt.", "app.containers.CampaignsConsentForm.messageSuccess": "Är E-Mail-Präferenze goufe gespäichert.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Wéi eng Zort E-Mail-Benoriichtunge wëllt Dir kréien? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Benoriichtungen", "app.containers.CampaignsConsentForm.submit": "Späicheren", "app.containers.ChangeEmail.backToProfile": "Zréck op Profil Astellungen", "app.containers.ChangeEmail.confirmationModalTitle": "Confirméier<PERSON>", "app.containers.ChangeEmail.emailEmptyError": "Gitt eng E-Mail Adress un", "app.containers.ChangeEmail.emailInvalidError": "Gitt eng E-Mailadress am richtege Format un, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "Gitt weg eng <PERSON><PERSON>.", "app.containers.ChangeEmail.emailTaken": "Dës E-Mail ass scho benotzt.", "app.containers.ChangeEmail.emailUpdateCancelled": "Email Update gouf annu<PERSON>t.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Fir Är E-Mail ze aktualiséieren, start w.e.g. de Prozess nei.", "app.containers.ChangeEmail.helmetDescription": "Ännert Är E-Mail Säit", "app.containers.ChangeEmail.helmetTitle": "Änneren Är Email", "app.containers.ChangeEmail.newEmailLabel": "Nei E-Mail", "app.containers.ChangeEmail.submitButton": "Ofginn", "app.containers.ChangeEmail.titleAddEmail": "Füügt Är E-Mail un", "app.containers.ChangeEmail.titleChangeEmail": "Änneren Är Email", "app.containers.ChangeEmail.updateSuccessful": "Är E-Mail gouf erfollegräich aktualiséiert.", "app.containers.ChangePassword.currentPasswordLabel": "Aktuellt Passwuert", "app.containers.ChangePassword.currentPasswordRequired": "Gitt Äert aktuellt Passwuert an", "app.containers.ChangePassword.goHome": "Gitt op Home", "app.containers.ChangePassword.helmetDescription": "Säit fir f’Passwuert z<PERSON>änneren", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON>", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON>", "app.containers.ChangePassword.newPasswordRequired": "<PERSON><PERSON> <PERSON><PERSON> neit <PERSON> an", "app.containers.ChangePassword.password.minimumPasswordLengthError": "<PERSON><PERSON> e <PERSON>, dat mindestens {minimumPasswordLength} <PERSON><PERSON><PERSON> laang ass", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON><PERSON> gouf erfollegräich aktualiséiert", "app.containers.ChangePassword.passwordEmptyError": "<PERSON><PERSON> <PERSON><PERSON> an", "app.containers.ChangePassword.passwordsDontMatch": "<PERSON><PERSON>ätegen", "app.containers.ChangePassword.titleAddPassword": "<PERSON><PERSON><PERSON><PERSON> e <PERSON> der<PERSON>", "app.containers.ChangePassword.titleChangePassword": "<PERSON><PERSON><PERSON>", "app.containers.Comments.a11y_commentDeleted": "Kommentar gel<PERSON>scht", "app.containers.Comments.a11y_commentPosted": "Kommentar gepost", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {keng <PERSON>} one {1 gär} other {#gefällt}}", "app.containers.Comments.a11y_undoLike": "Liken undoen", "app.containers.Comments.addCommentError": "Eppes ass schifgaangen. Wgl. probéiert spéider nach eng Kéier.", "app.containers.Comments.adminCommentDeletionCancelButton": "Ofbriechen", "app.containers.Comments.adminCommentDeletionConfirmButton": "<PERSON><PERSON><PERSON>nta<PERSON> läschen", "app.containers.Comments.cancelCommentEdit": "Ofbriechen", "app.containers.Comments.childCommentBodyPlaceholder": "Eng Äntwert schreiwen...", "app.containers.Comments.commentCancelUpvote": "Réckgängeg maachen", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON><PERSON><PERSON> gouf gel<PERSON>t.", "app.containers.Comments.commentDeletionCancelButton": "<PERSON><PERSON><PERSON> behalen", "app.containers.Comments.commentDeletionConfirmButton": "Mäi Kommentar läschen", "app.containers.Comments.commentLike": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentReplyButton": "Äntweren", "app.containers.Comments.commentsSortTitle": "Kommentare sortéiere no ", "app.containers.Comments.completeProfileLinkText": "komplett Äre Profil", "app.containers.Comments.completeProfileToComment": "W.e.g. {completeRegistrationLink} fir ze kommentéieren.", "app.containers.Comments.confirmCommentDeletion": "<PERSON>d <PERSON><PERSON>, datt Dir dëse Kommentar läsche wëllt? Et ass net méiglech dës Aktioun réckgängeg ze maachen!", "app.containers.Comments.deleteComment": "Läschen", "app.containers.Comments.deleteReasonDescriptionError": "Stellt méi Informatiounen zu Ärem Ulass zur Verfügung", "app.containers.Comments.deleteReasonError": "Stellt en Ulass zur Verfügung", "app.containers.Comments.deleteReason_inappropriate": "Et ass net ubruecht oder beleidegend", "app.containers.Comments.deleteReason_irrelevant": "<PERSON>t gehé<PERSON>t net heihin", "app.containers.Comments.deleteReason_other": "<PERSON>ere <PERSON>", "app.containers.Comments.editComment": "Beaarbechten", "app.containers.Comments.guidelinesLinkText": "eis Gemeinschaftsrichtlinnen", "app.containers.Comments.ideaCommentBodyPlaceholder": "Schreift Äre Kommentar hei", "app.containers.Comments.internalCommentingNudgeMessage": "Intern Kommentarer maachen ass net an Ärer aktueller Lizenz abegraff. Kontaktéiert Äre GovSuccess Manager fir méi doriwwer ze léieren.", "app.containers.Comments.internalConversation": "<PERSON><PERSON>", "app.containers.Comments.loadMoreComments": "<PERSON><PERSON><PERSON>", "app.containers.Comments.loadingComments": "Kommentare gi gelueden", "app.containers.Comments.loadingMoreComments": "<PERSON><PERSON><PERSON> gi <PERSON>en", "app.containers.Comments.notVisibleToUsersPlaceholder": "Dëse Kommentar ass net fir regelméisseg Benotzer ze gesinn", "app.containers.Comments.postInternalComment": "Post intern <PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.postPublicComment": "Post ëffentleche Kommentar", "app.containers.Comments.profanityError": "Ups! Dir hutt vläicht een oder méi Wierder benotzt, déi vun {guidelinesLink} als onpassend ugesi ginn. Ännert wgl. Ären Text fir beleidegend Sprooch ze läschen an dës Plattform zu enger sécherer Plaz fir jiddereen ze maachen. ", "app.containers.Comments.publicDiscussion": "Ëffentlech <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.publishComment": "Post Äre Kommentar", "app.containers.Comments.reportAsSpamModalTitle": "<PERSON>r<PERSON> wëllt Dir dëst als Spam mellen?", "app.containers.Comments.saveComment": "Späicheren", "app.containers.Comments.signInLinkText": "<PERSON><PERSON><PERSON>", "app.containers.Comments.signInToComment": "Wgl. {signInLink} fir ze kommentéieren.", "app.containers.Comments.signUpLinkText": "As<PERSON><PERSON><PERSON>wen ", "app.containers.Comments.verifyIdentityLinkText": "Iwwerpréift Är Identitéit", "app.containers.Comments.visibleToUsersPlaceholder": "<PERSON>ë<PERSON>nta<PERSON> ass fir regelméisseg Benotzer sichtbar", "app.containers.Comments.visibleToUsersWarning": "Kommentarer hei gepost ginn fir regelméisseg Benotzer siichtbar.", "app.containers.ContentBuilder.PageTitle": "Projet Beschreiwung", "app.containers.CookiePolicy.advertisingContent": "Cookië fir Publicitéit kënne benotzt ginn, fir d'Wierkung ze moossen déi extern Marketing-Campagnen op d'Participatioun op dëser Plattform hunn. Mir weise keng Reklammen op dëser Plattform. Dir kënnt awer personaliséiert Annoncen op Basis vun de Säite kréien, déi Dir besicht.", "app.containers.CookiePolicy.advertisingTitle": "Cookië fir Publicitéit", "app.containers.CookiePolicy.analyticsContents": "Analytesch <PERSON> tracken d’Verhale vun de Visiteuren, wéi beispillsweis wat fir Säite fir wéi laang besicht goufen. Si kënnen och gewëssen technesch Donnéeën <PERSON>mme<PERSON>, dor<PERSON><PERSON> Browserinformatioun, ongeféier<PERSON> Standuert an IP Adressen. Go Vocal verwent dë<PERSON> intern, fir di global Notzerexperienz an d’Funktioun vun der Plattform weider ze verbesseren. Dës <PERSON>n<PERSON> kënnen och tëschent Go Vocal an {orgName} gedeelt gi fir d'Participatioun bei Projeten op der Plattform auszewäerten an ze verbesseren. <PERSON><PERSON><PERSON>, datt d’Donnéeën anonym sinn a just en Gros zesummefaasst als Ganzes, benotzt gi – si identifizéieren Iech net perséinlech. Et ass awer méiglech, datt wann dës Donn<PERSON> mat aneren Date<PERSON>le kombin<PERSON>iert ginn, esou eng Identifikatioun méiglech ka sinn.", "app.containers.CookiePolicy.analyticsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.cookiePolicyDescription": "Eng detaillé<PERSON>t Erklärung, wéi mir Cookieen op dëser Plattform notzen.", "app.containers.CookiePolicy.cookiePolicyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.essentialContent": "E puer Cookieën sinn noutwenneg fir de richtege Fonctionnement vun dëser Plattform ze garantéieren. Dës noutwendeg Cookië gi virun allem verwent, fir Äre Kont ze authentifizéiere wann Dir d’Plattform besicht a fir Är gewënschte Sprooch ze späicheren.", "app.containers.CookiePolicy.essentialTitle": "Noutwendeg Cookien", "app.containers.CookiePolicy.externalContent": "E puer vun eise Sä<PERSON> kënnen Inhalt vun externen Ubidder weisen, z. B. YouTube oder Typeform. Mir hunn iwwer dës Cook<PERSON> vun externen Ubidder keng Kontroll. Wann Dir Inhalter vun externen Ubidder kuckt, kann et dozou féieren dass Cookien op Ärem Apparat installéiert ginn.", "app.containers.CookiePolicy.externalTitle": "Extern Cook<PERSON>", "app.containers.CookiePolicy.functionalContents": "Funktionell Cookië kënnen aktivéiert ginn, fir dass Visiteure Benoriichtegungen an Updates kréien a fir dass Supportkanäl direkt vun der Plattform aus erreecht kënne ginn.", "app.containers.CookiePolicy.functionalTitle": "Funk<PERSON><PERSON>", "app.containers.CookiePolicy.headCookiePolicyTitle": "<PERSON><PERSON> | {orgName}", "app.containers.CookiePolicy.intro": "<PERSON><PERSON><PERSON> sinn Text-<PERSON><PERSON>, déi am Browser oder op der Festplack vun Ärem Computer oder mobillen Apparat gespäichert ginn, wann Dir en Internetsite besicht an op déi den Internetsite bei spéidere Visitten zeréckgräife kann. <PERSON>, fir ze verst<PERSON>, wéi Visiteuren dës Plattform verwenden, fir hiren Design an d’Experienz ze verbesseren, fir Är Preferenzen ze späicheren (wéi zum Beispill Är favoriséiert Sproch), a fir Schlësselfunktioune, fir registréiert Benotzeren souwéi den Administrateuren vun der Plattform, ze ënnerstëtzen.", "app.containers.CookiePolicy.manageCookiesDescription": "Dir kënnt analytesch, Publizitéits- a funktionell Cookieë jidderzäit an Äre Cookiepreferenzen aktivéieren oder desaktivéieren. Dir kënnt och all bestoend Cookieë manuell oder automatesch iwwer Ären Internetbrowser läschen. D’Cookië kënnen awer mat Ärem Averständnes bei engem spéidere Besuch op dëser Plattform erëm gesat ginn. Wann Dir Cookien net läscht, ginn Är Cookie-Präferenze fir 60 Deeg gespäichert, duerno gitt Dir nach eng Kéier ëm Ärem Averständnes gefrot.", "app.containers.CookiePolicy.manageCookiesPreferences": "Gitt op Är {manageCookiesPreferencesButtonText} fir eng vollstänneg Lëscht vun den Integratioune vun Drëttubidder ze gesinn déi op dëser Plattform benotzt ginn, a fir Är Preferenzen ze verwalten.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "<PERSON>ie-Astellunge", "app.containers.CookiePolicy.manageCookiesTitle": "<PERSON>r Cook<PERSON>ë verwalten", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON>ie-Astellunge", "app.containers.CookiePolicy.viewPreferencesText": "<PERSON><PERSON> ka <PERSON>n, datt <PERSON><PERSON>-Kategorien hei ënnen net op all Visiteuren oder Plattformen zoutreffen; Dir fannt an Äre {viewPreferencesButton} eng vollstänneg Lëscht vun Integratioune vun <PERSON>dder, déi <PERSON> betreffen.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Fir wat benotze mir <PERSON>?", "app.containers.CustomPageShow.editPage": "Säit editéieren", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON><PERSON><PERSON> goen", "app.containers.CustomPageShow.notFound": "Säit net fonnt", "app.containers.DisabledAccount.bottomText": "<PERSON>r kënnt erëm ab {date}umellen.", "app.containers.DisabledAccount.termsAndConditions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.DisabledAccount.text2": "Äre Kont op der Participatiounsplattform vun {orgName} gouf temporär deaktivéiert wéinst enger Verletzung vu Gemeinschaftsrichtlinnen. Fir méi Informatioun doriwwer, kënnt Dir den {TermsAndConditions}.", "app.containers.DisabledAccount.title": "<PERSON><PERSON> Kont gouf temporär deaktiv<PERSON>", "app.containers.EventsShow.addToCalendar": "<PERSON><PERSON><PERSON><PERSON> an <PERSON> Kalenner", "app.containers.EventsShow.editEvent": "Event änneren", "app.containers.EventsShow.emailSharingBody2": "Bei dësem Event deelhuelen: {eventTitle}. Liest méi op {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Event Datum an Zäit", "app.containers.EventsShow.eventFrom2": "Vun \"{projectTitle}\"", "app.containers.EventsShow.goBack": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.goToProject": "Gitt op de Projet", "app.containers.EventsShow.haveRegistered": "registr<PERSON><PERSON><PERSON> hunn", "app.containers.EventsShow.icsError": "Feeler beim <PERSON>load vun der ICS Datei", "app.containers.EventsShow.linkToOnlineEvent": "Link op Online Event", "app.containers.EventsShow.locationIconAltText": "Plaz", "app.containers.EventsShow.metaTitle": "Evenement: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online Versammlung", "app.containers.EventsShow.onlineLinkIconAltText": "Online Reunioun Link", "app.containers.EventsShow.registered": "registr<PERSON><PERSON><PERSON>", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 Umeldungen} one {1 Umeldung} other {# Umeldungen}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} Umeldungen", "app.containers.EventsShow.registrantsIconAltText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.EventsShow.socialMediaSharingMessage": "Bei dësem Event deelhuelen: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {#Participant} other {#Participanten}}", "app.containers.EventsViewer.allTime": "Ëmmer", "app.containers.EventsViewer.date": "Datum", "app.containers.EventsViewer.thisMonth2": "nächste Mount", "app.containers.EventsViewer.thisWeek2": "nächst Woch", "app.containers.EventsViewer.today": "<PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "E Bäitrag bäifügen", "app.containers.IdeaButton.addAPetition": "Dobäizemaachen eng Petitioun", "app.containers.IdeaButton.addAProject": "E Projet bäifügen", "app.containers.IdeaButton.addAProposal": "<PERSON><PERSON><PERSON><PERSON> eng Propositioun", "app.containers.IdeaButton.addAQuestion": "<PERSON>g <PERSON>", "app.containers.IdeaButton.addAnInitiative": "<PERSON><PERSON><PERSON><PERSON> eng Initiativ", "app.containers.IdeaButton.addAnOption": "<PERSON><PERSON> bäifügen", "app.containers.IdeaButton.postingDisabled": "Momentan gi keng nei Bäiträg méi ugeholl.", "app.containers.IdeaButton.postingInNonActivePhases": "Nei Bäiträg kënnen nëmmen an aktive Phasen bäigefüügt ginn.", "app.containers.IdeaButton.postingInactive": "Momentan gi keng nei Bäiträg ugeholl.", "app.containers.IdeaButton.postingLimitedMaxReached": "Dir hutt dës Ëmfro schonn ofgeschloss. Merci fir Är Äntwert!", "app.containers.IdeaButton.postingNoPermission": "Momentan gi keng nei Bäiträg ugeholl.", "app.containers.IdeaButton.postingNotYetPossible": "Nei Bäiträg ginn nach net ugeholl.", "app.containers.IdeaButton.signInLinkText": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.signUpLinkText": "As<PERSON><PERSON><PERSON>wen", "app.containers.IdeaButton.submitAnIssue": "E Kommentar ofginn", "app.containers.IdeaButton.submitYourIdea": "<PERSON>echt Är Iddi an", "app.containers.IdeaButton.takeTheSurvey": "Un der Ëmfro deelhuelen", "app.containers.IdeaButton.verificationLinkText": "Iwwerpréift Är Identitéit elo.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON> méi", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {keng <PERSON>} one {1 Kommentar} other {# Kommentaren}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {keng <PERSON>} one {1 Stëmmen} other {# <PERSON>ë<PERSON>n}} aus {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Filter Panel zoumaachen", "app.containers.IdeaCards.a11y_totalItems": "Gesamtzuel <PERSON>äiträg: {ideasCount}", "app.containers.IdeaCards.all": "All", "app.containers.IdeaCards.allStatuses": "All Status", "app.containers.IdeaCards.contributions": "Contributiounen", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Initiativen", "app.containers.IdeaCards.issueTerm": "<PERSON>er", "app.containers.IdeaCards.list": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.map": "<PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "<PERSON><PERSON><PERSON> me<PERSON>cht <PERSON>", "app.containers.IdeaCards.newest": "Rezentst", "app.containers.IdeaCards.noFilteredResults": "<PERSON><PERSON>sultater fonnt. Probéiert wgl. en anere Filter oder sicht mat engem anere Begrëff.", "app.containers.IdeaCards.numberResults": "Resultater ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "Meescht Stëmmen", "app.containers.IdeaCards.projectFilterTitle": "Projeten", "app.containers.IdeaCards.projectTerm": "Projeten", "app.containers.IdeaCards.proposals": "Propo<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "Zoufälleg", "app.containers.IdeaCards.resetFilters": "Filteren zeré<PERSON>zen", "app.containers.IdeaCards.showXResults": "Weist {ideasCount, plural, one {# Resultat} other {# Resultater}}", "app.containers.IdeaCards.sortTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Themen", "app.containers.IdeaCards.topicsTitle": "Themen", "app.containers.IdeaCards.trending": "Beléift", "app.containers.IdeaCards.tryDifferentFilters": "<PERSON><PERSON>sultater fonnt. Probéiert wgl. en anere Filter oder sicht mat engem anere Begrëff.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} Kommentarer} one {{ideasCount} Kommentar} other {{ideasCount} Kommentarer}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} Bäiträg} one {{ideasCount} Bäitrag} other {{ideasCount} Bäiträg}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} Iddien} one {{ideasCount} Iddi} other {{ideasCount} Idd<PERSON>}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} Initiativen} one {{ideasCount} Initiative} other {{ideasCount} Initiativen}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} Optiounen} one {{ideasCount} Optioun} other {{ideasCount} Op<PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} <PERSON><PERSON><PERSON><PERSON>} one {{ideasCount} <PERSON><PERSON>un} other {{ideasCount} <PERSON><PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} Projeten} one {{ideasCount} Projet} other {{ideasCount} Projeten}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} Propositioune} one {{ideasCount} Propositioun} other {{ideasCount} Propositioune}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} Froen} one {{ideasCount} Froen} other {{ideasCount} Froen}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# Resultat} other {# Resultater}}", "app.containers.IdeasEditPage.contributionFormTitle": "Bäitrag beaarbechten", "app.containers.IdeasEditPage.editedPostSave": "Späicheren", "app.containers.IdeasEditPage.fileUploadError": "Eng oder méi Dateie konnten net eropgeluede ginn. Iwwerpréift wgl. Dateiegréisst a -format a probéiert nach eng Kéier.", "app.containers.IdeasEditPage.formTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Beaarbecht Äre Post. Nei Informatioune bäifügen oder al Informatiounen änneren.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "{postTitle} | {projectName} beaarbechten", "app.containers.IdeasEditPage.initiativeFormTitle": "Initiativ änneren", "app.containers.IdeasEditPage.issueFormTitle": "Kommentar beaarbechten", "app.containers.IdeasEditPage.optionFormTitle": "<PERSON>ti<PERSON><PERSON> be<PERSON>en", "app.containers.IdeasEditPage.petitionFormTitle": "Ännerung Petitioun", "app.containers.IdeasEditPage.projectFormTitle": "Projet beaarbechten", "app.containers.IdeasEditPage.proposalFormTitle": "Propositioun ä<PERSON>", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON>", "app.containers.IdeasEditPage.save": "Späicheren", "app.containers.IdeasEditPage.submitApiError": "Et gouf e <PERSON> beim Ofsch<PERSON>cken vum Formulaire. Préift wgl. ob et e Feeler gëtt a probéiert et nach eng Kéier.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "All Input verëffentlecht", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Entdeckt all d'Bäiträg déi op der participativer Plattform {orgName} gepost goufen. ", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Posts | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Bäiträg", "app.containers.IdeasIndexPage.loadMore": "<PERSON><PERSON><PERSON> lueden...", "app.containers.IdeasIndexPage.loading": "Um Lueden...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Par dé<PERSON>ut ginn Är Soumissioun mat Ärem Profil verbonnen, ausser Dir wielt dë<PERSON>.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Post anonym", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Profil Visibilitéit", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Dës Ëmfro ass de Moment net op fir Äntwerten. Weg zréck op de Projet fir méi Informatiounen.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Dës Ëmfro ass am Moment net aktiv.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Zréck op de Projet", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Dir hutt dës Ëmfro schonn ofgeschloss.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Ëmfro ofgeschloss", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Merci fir Är Äntwert!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "D’Bäitragsbeschreiwung muss manner wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "<PERSON> vun der Iddi muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "<PERSON> Bäitragstitel muss manner wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "<PERSON> Bäitragstitel muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Wielt w.e.g. op d'mannst ee Cosponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "D’Iddiesbeschreiwung muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "<PERSON>’Iddisbeschreiwung muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Gidd wgl. eng <PERSON>", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Den Titel vun der Iddi muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "<PERSON>dd<PERSON> muss méi wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "D'Initiativbeschreiwung muss manner wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "D'Initiativbeschreiwung muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Den Initiativtitel muss manner wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Den Initiativtitel muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "D’Besch<PERSON>iwung vun der Ugeleeënheet muss manner wéi {limit} <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "D’Besch<PERSON>iwung vun der Ugeleeënheet muss méi wéi {limit} Z<PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Den Titel vun der Ugeleeënheet muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Den Titel vum Thema muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON><PERSON><PERSON>ld ass er<PERSON>, gitt w.e.g. eng valabel Zuel un", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "D’Optiounsbeschreiwung muss manner wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "D'Optiounsbeschreiwung muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Den Optiounstitel muss manner wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "<PERSON> Optiounstitel muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "W.e.g. wielt op d<PERSON>mannst een Tag aus", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "D'Be<PERSON><PERSON><PERSON>wung vun der Petitioun muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "D'Be<PERSON><PERSON><PERSON>wu<PERSON> vun der Petitioun muss méi wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Den Titel vun der Petitioun muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Den Titel vun der Petitioun muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "D’Projetsbeschreiwung muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "D’Projetsbeschreiwung muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "De Projetstitel muss manner wéi {limit} <PERSON><PERSON><PERSON> la<PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Den Titel vum Projet muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "D'Propositiounsbeschreiwung muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "D'Propositiounsbeschreiwung muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "De Propositiounstitel muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "De Propositiounstitel muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "W.e.g. gitt eng <PERSON> an", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "W.e.g. gitt eng <PERSON> an", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "<PERSON>’Be<PERSON><PERSON><PERSON>wung vun der Fro muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "<PERSON><PERSON>Be<PERSON><PERSON><PERSON>wung vun der Fro muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Den Titel vun der Fro muss manner wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Den Titel vun der Fro muss méi wéi {limit} <PERSON><PERSON><PERSON> sinn", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Gitt wgl. en Titel un", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "D’Bäitrgsbeschreiwung muss manner wéi 80 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "D’Bäitragsbeschreiwung muss op d’mannst 30 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "De Bäitragstitel muss manner wéi 80 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "De Bäitragstitel muss mindestens 10 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "D’Iddisbeschreiwung muss manner wéi 80 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "D’Beschreiwung vun der Iddi muss mindestens 30 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Gitt wgl. en Titel un", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "<PERSON> Iddistitel muss manner wéi 80 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Den Titel vun der Iddi muss mindestens 10 Z<PERSON>che hunn", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Dir hutt méiglecherweis een oder e puer Wierder gebra<PERSON>t, déi vun {guidelinesLink} als Profanitéit betruecht ginn. W.e.g. <PERSON><PERSON><PERSON> Ären Text, fir eventuelle Profanitéiten ze eliminéieren.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "D'Initiativbeschreiwung muss manner wéi 80 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "D'Initiativbeschreiwung muss mindestens 30 Zeeche laang sinn", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Den Initiativtitel muss manner wéi 80 <PERSON><PERSON>chen laang sinn", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Den Initiativtitel muss mindestens 10 Zeeche laang sinn", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "D’Beschreiwung vun der Ugeleeënheet muss manner wéi 80 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "D’Beschreiwung vun der Ugeleeënheet muss op d’mannst 30 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Den Titel vun der Ugeleeënheet muss manner wéi 80 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Den Titel vun der Ugeleeënheet muss op d’mannst 10 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "D’Optiounsbeschreiwung muss manner wéi 80 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "D’Optiounsbeschreiwung muss op d’mannst 30 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Den Optiounstitel muss manner wéi 80 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Den Optiounstitel muss op d’mannst 10 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "D'Besch<PERSON>iwu<PERSON> vun der Petitioun muss manner wéi 80 <PERSON><PERSON>chen laang sinn", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "D'Beschreiwung vun der Petitioun muss mindestens 30 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Den Titel vun der Petitioun muss manner wéi 80 <PERSON><PERSON>chen laang sinn", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Den Titel vun der Petitioun muss mindestens 10 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "D’Projetsbeschreiwung muss manner wéi 80 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "D’Projetsbeschreiwung muss op d’mannst 30 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "De Projetstitel muss manner wéi 80 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "De Projetstitel muss op d’mannst 10 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "D'Propositiounsbeschreiwung muss manner wéi 80 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "D'Propositiounsbeschreiwung muss mindestens 30 Z<PERSON>chen laang sinn", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "De Propositiounstitel muss manner wéi 80 <PERSON><PERSON>chen laang sinn", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "De Propositiounstitel muss mindestens 10 Z<PERSON>chen laang sinn", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Gidd wgl. eng <PERSON>", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "<PERSON>’Besch<PERSON>iwung vun der Fro muss manner wéi 80 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "D’Besch<PERSON>iwung vun der Fro muss op d’mannst 30 Z<PERSON>che laang sinn", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "<PERSON>’Besch<PERSON>iwung vun der Fro muss manner wéi 80 <PERSON><PERSON><PERSON> laang sinn", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "D’Besch<PERSON>iwung vun der Fro muss op d’mannst 10 <PERSON><PERSON>che laang sinn", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Ofbriechen", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON>, ech w<PERSON><PERSON> verl<PERSON>sen", "app.containers.IdeasNewPage.contributionMetaTitle1": "<PERSON><PERSON><PERSON>gt neie Bäitrag zum Projet | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Ëmfro editéieren", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Verëffentlecht e Bäitrag a bedeelegt Iech um Austausch op der participativer Plattform {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "<PERSON><PERSON><PERSON>gt nei Iddi un de Projet | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "<PERSON><PERSON><PERSON>gt nei Initiativ zum Projet | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "<PERSON><PERSON><PERSON><PERSON> nei Ausgab zum Projet | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Sidd Dir sécher datt Dir fortgoe wëllt?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Är Entworf Äntwerte goufe privat gespäichert an Dir kënnt zréckkommen fir dëst spéider ofzeschléissen.", "app.containers.IdeasNewPage.leaveSurvey": "Verloossen Ëmfro", "app.containers.IdeasNewPage.leaveSurveyText": "Är Äntwerte ginn net gespäichert.", "app.containers.IdeasNewPage.optionMetaTitle1": "<PERSON><PERSON><PERSON><PERSON> nei <PERSON>tioun fir de Projet | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "<PERSON><PERSON><PERSON><PERSON> nei <PERSON> un de Projet | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "<PERSON><PERSON><PERSON>gt neie Projet un de Projet | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "<PERSON><PERSON><PERSON>gt nei Propositioun un de Projet | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "<PERSON><PERSON><PERSON><PERSON> nei <PERSON> zum Projet | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Akzeptéieren Invitatioun", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Co-Sponsoring Invitatioun", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Co-<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Dir sidd invitéiert fir Co-Sponsor ze ginn.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitat<PERSON><PERSON> u<PERSON>l", "app.containers.IdeasShow.Cosponsorship.pending": "amgaang", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} de(n) {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Aktuelle Status", "app.containers.IdeasShow.MetaInformation.location": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.postedBy": "Gepost vun/vum", "app.containers.IdeasShow.MetaInformation.similar": "Ähnlech Input", "app.containers.IdeasShow.MetaInformation.topics": "Sujet<PERSON>", "app.containers.IdeasShow.commentCTA": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.contributionEmailSharingBody": "Ënnerstëtzt de Bäitrag '{postTitle}' ënner {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Ënnerstëtzt dëse <PERSON>: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Merci fir Äre Bäitrag!", "app.containers.IdeasShow.contributionTwitterMessage": "Ënnerstëtzt dëse <PERSON>: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Ënnerstëtzt dëse <PERSON>: {postTitle}", "app.containers.IdeasShow.currentStatus": "Aktuelle Status", "app.containers.IdeasShow.deletedUser": "Onbekannten Auteur", "app.containers.IdeasShow.ideaEmailSharingBody": "Ënnerstëtzt meng Iddi '{ideaTitle}' ënner {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Ënnerstëtzt meng Iddi: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Ënnerstëtzt dës <PERSON>ddi: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Ënnerstëtzt dës <PERSON>ddi: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Ënnerstëtzt dëse <PERSON>: {postTitle}", "app.containers.IdeasShow.imported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.importedTooltip": "Dës {inputTerm} gouf offline gesammelt an automatesch op d'Plattform eropgelueden.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Ënnerstëtzt dës Initiativ '{ideaTitle}' um {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Ënnerstëtzt dës Initiativ: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Merci fir Är Initiativ!", "app.containers.IdeasShow.initiativeTwitterMessage": "Ënnerstëtzt dës Initiativ: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Ënnerstëtzt dës Initiativ: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Ënnerstëtzt dëse <PERSON>ntar '{postTitle}' ënner {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Ënnerstëtzt dëse <PERSON> '{postTitle}'", "app.containers.IdeasShow.issueSharingModalTitle": "Merci fir Äre Kommentar!", "app.containers.IdeasShow.issueTwitterMessage": "Ënnerstëtzt dëse <PERSON> '{postTitle}'", "app.containers.IdeasShow.metaTitle": "Input: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Ënnerstëtzt dës Opti<PERSON>n '{postTitle}' ënner {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Ënnerstëtzt dës <PERSON>n '{postTitle}'", "app.containers.IdeasShow.optionSharingModalTitle": "Är Optioun gouf erfollegräich gepost!", "app.containers.IdeasShow.optionTwitterMessage": "Ënnerstëtzt dës <PERSON>n '{postTitle}'", "app.containers.IdeasShow.optionWhatsAppMessage": "Ënnerstëtzt dës <PERSON>n '{postTitle}'", "app.containers.IdeasShow.petitionEmailSharingBody": "Ënnerstëtzt dës <PERSON> '{ideaTitle}' um {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Ënnerstëtzt dës <PERSON>: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON>rci fir Ä<PERSON>!", "app.containers.IdeasShow.petitionTwitterMessage": "Ënnerstëtzt dës <PERSON>: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Ënnerstëtzt dës <PERSON>: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Ënnerstëtzt dëse Projet '{postTitle}' ënner {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Ënnerstëtzt dëse Projet: '{postTitle}'", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON><PERSON>, datt Dir Äre Projet agereecht hutt!", "app.containers.IdeasShow.projectTwitterMessage": "Ënnerstëtzt dëse Projet: '{postTitle}'", "app.containers.IdeasShow.projectWhatsAppMessage": "Ënnerstëtzt dëse Projet: '{postTitle}'", "app.containers.IdeasShow.proposalEmailSharingBody": "Ënnerstëtzt dës Propositioun '{ideaTitle}' um {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Ënnerstëtzt dës <PERSON>: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Merci fir Är Propositioun ofzeginn!", "app.containers.IdeasShow.proposalTwitterMessage": "Ënnerstëtzt dës <PERSON>: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Ënnerstëtzt dës <PERSON>: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Zäit fir ze stëmmen:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} aus {votingThreshold} erfuerderlech Stëmmen", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Ofbriechen Vote", "app.containers.IdeasShow.proposals.VoteControl.days": "<PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "e<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.hours": "Stonnen", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status a Stëmmen", "app.containers.IdeasShow.proposals.VoteControl.minutes": "min", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.vote": "Stëmmen", "app.containers.IdeasShow.proposals.VoteControl.voted": "gest<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.votedText": "<PERSON>r kritt Notifikatioun wann dës Initiativ op de nächste Schrëtt geet. {x, plural, =0 {Et gëtt {xDays} lénks.} one {Et gëtt {xDays} lénks.} other {Et sinn {xDays} lénks.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "<PERSON>r Stëmm gouf ofginn!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "<PERSON>r kënnt leider net iwwer dës Propositioun ofstëmmen. Liest firwat an {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {manner wéi engem Dag} one {een Dag} other {# Deeg}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {keng <PERSON>} one {1 Stëmmen} other {# <PERSON>}}", "app.containers.IdeasShow.questionEmailSharingBody": "Diskutéiert mat iwwer d’Fro '{postTitle}' bei {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Diskutéiert mat: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Är Fro gouf erfollegräich gepost!", "app.containers.IdeasShow.questionTwitterMessage": "Diskutéiert mat: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Diskutéiert mat: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "<PERSON>r<PERSON> wëllt Dir dëst als Spam mellen?", "app.containers.IdeasShow.share": "<PERSON><PERSON>", "app.containers.IdeasShow.sharingModalSubtitle": "Erreecht méi Leit a verschaaft Ärer Stëmm Gehéier.", "app.containers.IdeasShow.sharingModalTitle": "<PERSON>rci fir Är Iddi!", "app.containers.Navbar.completeOnboarding": "Ko<PERSON>tt Onboarding", "app.containers.Navbar.completeProfile": "Komplett Profil", "app.containers.Navbar.confirmEmail2": "Con<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Navbar.unverified": "Net iwwerpréift", "app.containers.Navbar.verified": "Iwwerpréift", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON> <PERSON>", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON><PERSON><PERSON> datt <PERSON>", "app.containers.NewAuthModal.completeYourProfile": "Fëllt Äre Profil aus", "app.containers.NewAuthModal.confirmYourEmail": "Bestätegt Är E-Mail", "app.containers.NewAuthModal.logIn": "Aloggen", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Iwwerpréift d'Konditioune hei ënnen fir weiderzemaachen.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Gitt weg Äre Profil aus.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Gitt zréck op Loginoptiounen", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Hutt <PERSON>r kee <PERSON>? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Code muss 4 <PERSON><PERSON>chen hunn.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Fuert weider mat FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Keng Authentifikatiounsmethoden sinn op dëser Plattform aktivéiert.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON><PERSON><PERSON>, er<PERSON><PERSON><PERSON><PERSON> Dir Iech averstanen E-Mailen vun dëser Plattform ze kréien. Dir kënnt an \"Meng Astellungen\" auswielen, wéieng E-Mailen Dir wëllt kréien.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-Mail", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Gitt eng E-Mail-Adress am richtege Format un, z. B. <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "<PERSON>itt eng E-Mail-Adress un", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Gitt Är E-Mail-Adress un fir weiderzefueren.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Passwuert vergiess?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Loggt Iech an Äre Kont an: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Gitt wgl. <PERSON><PERSON> an", "app.containers.NewAuthModal.steps.Password.password": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Password.rememberMe": "U mech erënneren", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON> auswielen, wann Dir en ëffentleche Computer benotzt", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON> er<PERSON>", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Maacht elo mat Ärer Participatioun weider.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Är Identitéit gouf verifizéiert. Dir sidd elo e vollwäertege Member vun der Gemeinschaft op dëser Plattform.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Dir sidd elo verifizéiert!", "app.containers.NewAuthModal.steps.close": "Zoumaachen", "app.containers.NewAuthModal.steps.continue": "Weidermaachen", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Wat sidd Dir interesséiert?", "app.containers.NewAuthModal.youCantParticipate": "Dir kënnt net matmaachen", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {keng ongeliese Benoriichtegungen} one {1 ongeliese Benoriichtegung} other {# ongeliese Benoriichtegungen}}", "app.containers.NotificationMenu.adminRightsReceived": "Dir sidd elo en Administrateur vun der Plattform", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "<PERSON>re Kommentar op \"{postTitle}\" gouf vun engem Administrateur geläscht well\n      {reasonCode, select, irrelevant {et ass irrelevant} inappropriate {säin Inhalt ass net passend} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} huet Är Co-Sponsorinvitatioun ugeholl", "app.containers.NotificationMenu.deletedUser": "Onbekannten Auteur", "app.containers.NotificationMenu.error": "Benoriichtegunge kënnen net geluede ginn", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} kommentéiert intern op en Input deen Iech zougewisen gouf", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} kommentéiert intern op en Input deen Dir intern kommentéiert hutt", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} kom<PERSON><PERSON><PERSON>t intern op en Input an engem Projet deen Dir ger<PERSON>t", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} kommentéiert intern op en net zougewisenen Input an engem net verwalteten Projet", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} kommentéiert Ären internen Kommentar", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} invi<PERSON>iert Iech fir e Bäitrag ze sponseren", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} invi<PERSON><PERSON><PERSON>ech fir eng Iddi mat<PERSON>ren", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} invi<PERSON><PERSON><PERSON> Iech eng Propositioun ze cosponséieren", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} in<PERSON><PERSON><PERSON><PERSON> fir en Thema matzesponséieren", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} invi<PERSON><PERSON><PERSON> Iech fir eng Optioun ze sponsoren", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} invi<PERSON><PERSON><PERSON> Iech fir eng Petitioun ze sponsoren", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} invi<PERSON><PERSON>t Iech fir e Projet matzesponsoren", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} invi<PERSON><PERSON><PERSON> Iech fir eng Propositioun ze sponseren", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} invi<PERSON><PERSON><PERSON> eng Fro ze co-sponsoren", "app.containers.NotificationMenu.loadMore": "<PERSON><PERSON><PERSON> lueden...", "app.containers.NotificationMenu.loading": "Benoriichtegunge lueden...", "app.containers.NotificationMenu.mentionInComment": "{name} huet <PERSON> an engem Kommentar ernimmt", "app.containers.NotificationMenu.mentionInInternalComment": "{name} huet <PERSON> an engem internen Kommentar ernimmt", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} huet <PERSON>ech an engem offiziellen Update ernimmt", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Dir hutt Är Ëmfro net ofginn", "app.containers.NotificationMenu.noNotifications": "Dir hutt nach keng Benoriichtegungen", "app.containers.NotificationMenu.notificationsLabel": "Benoriichtegungen", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} huet en offiziellen Update iwwer e Bäitrag ginn, deen Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} huet en offiziellen Update iwwer eng Iddi ginn déi Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} huet en offiziellen Update iwwer eng Initiative ginn, déi Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} huet en offiziellen Update iwwer en Thema ginn deen Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} huet en offiziellen Update iwwer eng Optioun ginn déi Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} huet en offiziellen Update iwwer eng Petitioun ginn déi Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} huet en offiziellen Update iwwer e Projet deen Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} huet en offiziellen Update iwwer eng Propositioun ginn, déi Dir verfollegt", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} huet en offiziellen Update iwwer eng Fro déi Dir verfollegt", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} gouf <PERSON> z<PERSON>n", "app.containers.NotificationMenu.projectModerationRightsReceived": "Dir sidd elo Administrateur vun {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} ass an eng nei Phas gangen", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} geet de(n) {phaseStartAt} an eng nei Phas", "app.containers.NotificationMenu.projectPublished": "En neie Projet gouf publizé<PERSON>t", "app.containers.NotificationMenu.projectReviewRequest": "{name} huet Genehmegung gefrot fir de Projet ze publizéieren \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} guttgeheescht \"{projectTitle}\" fir Publikatioun", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} Status ass op {status}geännert", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} huet di néideg Unzuel u Stëmmen erreecht", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} huet <PERSON>r Invitatioun ugeholl", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} kommentéiert e Bäitrag deen Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} kommenté<PERSON>t eng Iddi déi Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} kommentéiert eng Initiativ déi Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} kommentéiert op en Thema dat Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} kommentéiert eng Optioun déi Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} kommenté<PERSON>t eng Petitioun déi Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} kommentéiert e Projet deen Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} kommentéiert eng Propositioun déi Dir verfollegt", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} kommentéiert op eng Fro déi Dir verfollegt", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} gemellt \"{postTitle}\" als Spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} huet op Äre Kommentar reagéiert", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} huet e Kommentar op \"{postTitle}\" als Spam gemellt", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Dir hutt Är Stëmmen net ofginn", "app.containers.NotificationMenu.votingBasketSubmitted": "<PERSON>r hutt erfollegräich gestëmmt", "app.containers.NotificationMenu.votingLastChance": "<PERSON><PERSON><PERSON> lescht <PERSON> fir {phaseTitle}ze wielen", "app.containers.NotificationMenu.votingResults": "{phaseTitle} Stëmmen Resultater opgedeckt", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} huet <PERSON> {postTitle} zougewisen", "app.containers.PasswordRecovery.emailError": "<PERSON>ëst gesäit net no enger gëlteger E-Mail-Adress aus", "app.containers.PasswordRecovery.emailLabel": "E-Mail-Adress", "app.containers.PasswordRecovery.emailPlaceholder": "<PERSON>g <PERSON>-<PERSON>-Adress", "app.containers.PasswordRecovery.helmetDescription": "<PERSON><PERSON>", "app.containers.PasswordRecovery.helmetTitle": "<PERSON><PERSON>", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "<PERSON>n dës E-Mail-Adress op der Plattform registréiert ass, gouf e <PERSON> gesent.", "app.containers.PasswordRecovery.resetPassword": "E Link fir d’<PERSON>wuert zeréckzesetze kréien", "app.containers.PasswordRecovery.submitError": "<PERSON> hu kee <PERSON> fonnt, dee mat dëser E-Mail-Adress verbonnen ass. Schreift Iech wgl. an. ", "app.containers.PasswordRecovery.subtitle": "Wou kënne mir de <PERSON>, fir dass Dir Äert <PERSON>wuert ännere kënnt?", "app.containers.PasswordRecovery.title": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.helmetDescription": "Säit fir d'Passwuert z<PERSON>ckzesetzen", "app.containers.PasswordReset.helmetTitle": "Setzt Äert <PERSON>", "app.containers.PasswordReset.login": "Log-in", "app.containers.PasswordReset.passwordError": "<PERSON><PERSON><PERSON>wuert muss mindestens 8 Z<PERSON>chen hunn", "app.containers.PasswordReset.passwordLabel": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON>", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON><PERSON> gouf erfollegräich aktualiséiert.", "app.containers.PasswordReset.pleaseLogInMessage": "Wgl. loggt <PERSON><PERSON> mat <PERSON>wu<PERSON> an.", "app.containers.PasswordReset.requestNewPasswordReset": "Ufroen fir d'Pass<PERSON><PERSON> z<PERSON>zes<PERSON>zen", "app.containers.PasswordReset.submitError": "Et ass e Feeler opgetrueden. Probéiert wgl. méi spéit nach eng Kéier. ", "app.containers.PasswordReset.title": "<PERSON><PERSON>", "app.containers.PasswordReset.updatePassword": "<PERSON><PERSON>ätegen", "app.containers.ProjectFolderCards.allProjects": "All Projeten", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} schafft momentan un", "app.containers.ProjectFolderShowPage.editFolder": "<PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informatiounen iwwer dëse Projet", "app.containers.ProjectFolderShowPage.metaTitle1": "Dossier: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON><PERSON> liesen", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON> g<PERSON>nn", "app.containers.ProjectFolderShowPage.share": "<PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Dokument", "app.containers.Projects.PollForm.formCompleted": "Merci! Mir hunn Är Äntwert(e) kritt.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Dir hutt dës Ëmfro scho gemaach.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Dës Ëmfro kann nëmme gemaach ginn wann dës Phase aktiv ass.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Dës Ëmfro ass momentan net aktivéiert", "app.containers.Projects.PollForm.pollDisabledNotPossible": "<PERSON><PERSON> kann net un dëser Ëmfro deelgeholl ginn.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Dës Ëmfro ass net méi verfügbar, well de Projet net méi aktiv ass.", "app.containers.Projects.PollForm.sendAnswer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_phase": "Phas {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Iwwersiicht vun de Phases", "app.containers.Projects.a11y_titleInputs": "All Bäiträg, déi zu dësem Projet agereecht goufen", "app.containers.Projects.a11y_titleInputsPhase": "All Bäiträg, déi an dëser Phas agereecht goufen", "app.containers.Projects.accessRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.addedToBasket": "Ärem Akafskuerf bäigefüügt", "app.containers.Projects.allocateBudget": "Verdeelt Äre Budget", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "Ären Akafskuerf gouf iwwermëttelt!", "app.containers.Projects.contributions": "Bäiträg", "app.containers.Projects.createANewPhase": "Erstellt eng nei Phase", "app.containers.Projects.currentPhase": "Aktuell Phas", "app.containers.Projects.document": "Dokument", "app.containers.Projects.editProject": "Projet beaarbechten", "app.containers.Projects.emailSharingBody": "Wat denkt Dir iwwer dës Initiativ? Stëmmt doriwwer of an deelt d’Diskussioun bei {initiativeUrl}, fir datt Är Stëmm gehéiert gëtt!", "app.containers.Projects.emailSharingSubject": "Meng Initiativ ënnerstëtzen: {initiativeTitle}.", "app.containers.Projects.endedOn": "Ofgeschloss de(n) {date}", "app.containers.Projects.events": "<PERSON><PERSON><PERSON>", "app.containers.Projects.header": "Projeten", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informatioun", "app.containers.Projects.initiatives": "Initiativen", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Iwwerpréift d'Dokument", "app.containers.Projects.invisibleTitlePhaseAbout": "<PERSON>w<PERSON> d<PERSON><PERSON>", "app.containers.Projects.invisibleTitlePoll": "Un der Ëmfro deelhuelen", "app.containers.Projects.invisibleTitleSurvey": "Un der Ëmfro deelhuelen", "app.containers.Projects.issues": "Kommentaren", "app.containers.Projects.liveDataMessage": "<PERSON>r gesitt <PERSON>daten. D'Zuel vun de Participanten gëtt kontinuéierlech fir Administrateuren aktualiséiert. Notéiert w.e.g. datt regelméisseg Benotzer cachedaten gesinn, wat zu liicht Differenzen an den Zuelen resultéiere kann.", "app.containers.Projects.location": "Standuert:", "app.containers.Projects.manageBasket": "Akafs<PERSON><PERSON><PERSON> verwalten", "app.containers.Projects.meetMinBudgetRequirement": "<PERSON><PERSON><PERSON><PERSON> Mindestbudget, <PERSON>r <PERSON><PERSON> Akafskuerf an<PERSON>.", "app.containers.Projects.meetMinSelectionRequirement": "<PERSON><PERSON><PERSON><PERSON> déi erfu<PERSON><PERSON><PERSON><PERSON> Auswiel, fir <PERSON><PERSON>ku<PERSON>f an<PERSON>.", "app.containers.Projects.metaTitle1": "Projet: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Mindestbudget erfuerderlech", "app.containers.Projects.myBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navPoll": "Ëmfro", "app.containers.Projects.navSurvey": "Ëmfro", "app.containers.Projects.newPhase": "Nei Phase", "app.containers.Projects.nextPhase": "Nächst Phas", "app.containers.Projects.noEndDate": "<PERSON><PERSON>", "app.containers.Projects.noItems": "Dir hutt nach näischt ausgewielt", "app.containers.Projects.noPastEvents": "<PERSON>g verga<PERSON>en Evenementer fir unze<PERSON>sen", "app.containers.Projects.noPhaseSelected": "Keng <PERSON> ausgewielt", "app.containers.Projects.noUpcomingOrOngoingEvents": "Aktuell si keng bevirstoend oder lafend Evenementer geplangt.", "app.containers.Projects.offlineVotersTooltip": "Dës Z<PERSON> reflektéiert keng offline Wielerzuelen.", "app.containers.Projects.options": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.participants": "Participanten", "app.containers.Projects.participantsTooltip4": "<PERSON>ës Z<PERSON> reflektéiert och anonym Ëmfro Soumissioun. Anonym Ëmfroe sinn méiglech wann Ëmfroe fir jiddereen op sinn (kuckt den Tab {accessRightsLink} fir dëse Projet).", "app.containers.Projects.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "Phasen", "app.containers.Projects.previousPhase": "<PERSON><PERSON><PERSON>", "app.containers.Projects.project": "Projet", "app.containers.Projects.projectTwitterMessage": "Verschaaft Ärer Stëmm Gehéier! Huelt deel un {projectName} | {orgName}", "app.containers.Projects.projects": "Projeten", "app.containers.Projects.proposals": "Propo<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON> wéineg liesen", "app.containers.Projects.readMore": "<PERSON><PERSON><PERSON> liesen", "app.containers.Projects.removeItem": "Element e<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.requiredSelection": "obligatore<PERSON> Auswiel", "app.containers.Projects.reviewDocument": "Iwwerpréift d'Dokument", "app.containers.Projects.seeTheContributions": "Bäiträg ukucken", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheInitiatives": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheIssues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeThePetitions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheProjects": "Projeten ukucken", "app.containers.Projects.seeTheProposals": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeUpcomingEvents": "<PERSON><PERSON><PERSON> zu<PERSON>ünfteg Evenementer", "app.containers.Projects.share": "<PERSON><PERSON>", "app.containers.Projects.shareThisProject": "Projet deelen", "app.containers.Projects.submitMyBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.survey": "Ëmfro", "app.containers.Projects.takeThePoll": "Un der Ëmfro deelhuelen", "app.containers.Projects.takeTheSurvey": "Un der Ëmfro deelhuelen", "app.containers.Projects.timeline": "Zäitlinn", "app.containers.Projects.upcomingAndOngoingEvents": "Bevirstoend oder lafend Evenementer", "app.containers.Projects.upcomingEvents": "<PERSON><PERSON><PERSON><PERSON> Evenementer", "app.containers.Projects.whatsAppMessage": "{projectName} | vun der participativer Plattform {orgName}", "app.containers.Projects.yourBudget": "Gesamtbudget", "app.containers.ProjectsIndexPage.metaDescription": "All lafend Projeten op {orgName} <PERSON><PERSON><PERSON><PERSON>, fir ze verstoen, wéi <PERSON>r de<PERSON><PERSON><PERSON> kënnt. Kommt an diskutéiert lokal Projeten, déi Iech am wichtegste sinn.", "app.containers.ProjectsIndexPage.metaTitle1": "Projeten | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projeten", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON>ch maache mat", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Wgl. {signInLink} oder {signUpLink} fir d’éischt fir deelzehuelen", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "D'Participatioun ass momentan net op fir dës Aktivitéit.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "as<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Ech ka net méi hël<PERSON>fen", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, one {# partizipant} other {# partizipanten}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Opgepasst: Déi embedded Ëmfro kann Accessibilitéitsprobleemer fir Screenreader Benot<PERSON> hunn. <PERSON><PERSON> Dir Erausfuerderunge erliewt, kontaktéiert w.e.g. den Plattformadministrator fir e Link op d'Ëmfro vun der ursprénglecher Plattform ze kréien. Alternativ kënnt Dir aner Weeër ufroen fir d'Ëmfro auszefëllen.", "app.containers.ProjectsShowPage.process.survey.survey": "Ëmfro", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Fir gewuer ze ginn, ob Dir un dëser Ëmfro de<PERSON><PERSON><PERSON> kënnt, wgl. {logInLink} fir d’éischt op d’Plattform.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Un dëser Ëmfro kann nëmmen de<PERSON> ginn, wann dës <PERSON>as vum Prozess aktiv ass.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Weg {completeRegistrationLink} fir d'Ëmfro ze huelen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Dës Ëmfro ass momentan net aktivéiert", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Fir un dëser Ëmfro <PERSON><PERSON><PERSON><PERSON><PERSON>, muss <PERSON><PERSON> Ko<PERSON> iwwerpréiwt ginn. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "D'Ëmfro ass net méi verf<PERSON>, well dëse Projet net méi aktiv ass.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "komplett Aschreiwung", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "aloggen", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verifizéiert elo Äre Kont.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Nëmme verschidde Benotzer kënnen dëst Dokument iwwerpréiwen. Weg {signUpLink} oder {logInLink} éischt.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Dëst Dokument kann nëmmen iwwerpréift ginn wann dës Phase aktiv ass.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Weg {completeRegistrationLink} fir d'Dokument ze iwwerpréiwen.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Leider hutt Dir net d'Rechter dëst Dokument ze iwwerpréiwen.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Iwwerpréift vun dësem Dokument erfuerdert d'Verifizéierung vun Ärem Kont. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "D'Dokument ass net méi verfüg<PERSON>, well dëse Projet net méi aktiv ass.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(inkl. 1 offline)} other {(inkl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 Pick} other {# Picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "De Prozentsaz vun de Participanten déi dës Optioun gewielt hunn.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "De Prozentsaz vun de <PERSON>n, déi dë<PERSON> k<PERSON>.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Käschten:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "<PERSON><PERSON><PERSON> weisen", "app.containers.ReactionControl.a11y_likesDislikes": "Total Likes: {likesCount}, Total Dislikes: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Dir hutt Är Dislike fir dësen Input erfollegräich annuléiert.", "app.containers.ReactionControl.cancelLikeSuccess": "Dir hutt Äre Like fir dësen Input erfollegräich annu<PERSON>iert.", "app.containers.ReactionControl.dislikeSuccess": "Dir hutt dësen Input erfollegräich net gär.", "app.containers.ReactionControl.likeSuccess": "Dir hutt dësen Input erfollegräich gefall.", "app.containers.ReactionControl.reactionErrorSubTitle": "Wéinst engem Feeler konnt Är Reaktioun net registréiert ginn. Probéiert w.e.g. nach eng Kéier an e puer Minutten.", "app.containers.ReactionControl.reactionSuccessTitle": "Är Reaktioun gouf erfollegräich registréiert!", "app.containers.ReactionControl.vote": "Stëmmen", "app.containers.ReactionControl.voted": "gest<PERSON><PERSON><PERSON>", "app.containers.SearchInput.a11y_cancelledPostingComment": "<PERSON><PERSON><PERSON><PERSON><PERSON> Astelle <PERSON>.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} Kommentarer sinn gelueden.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# Events hu gelueden} one {# Event ass gelueden} other {# Eventer hu gelueden}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# Resultater goufen gelueden} one {# Resultat ass gelueden} other {# Resultater hu gelueden}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# Sichresultater hu gelueden} one {# Sichresultat gouf gelueden} other {# Sichresultater hu gelueden}}.", "app.containers.SearchInput.removeSearchTerm": "<PERSON><PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "<PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect ass déi vum franséische Stat ugebuede Léisung, fir d’Umeldung op iwwer 700 Online-Servicer sécher an einfach ze maachen.", "app.containers.SignIn.or": "<PERSON><PERSON>", "app.containers.SignIn.signInError": "Déi zur Verfügung gestallten Informatioun ass net korrekt. Klickt op \"Passwuert vergiess\" fir Äert Passwuert zeréckzesetzen.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Benotzt FranceConnect fir I<PERSON> an<PERSON>, unzemellen oder Äre Kont ze verifizéieren.", "app.containers.SignIn.whatIsFranceConnect": "Wat ass France Connect?", "app.containers.SignUp.adminOptions2": "Fir Administrateuren a Projet Manager", "app.containers.SignUp.backToSignUpOptions": "Zeréck bei d'Umeldungsoptiounen", "app.containers.SignUp.continue": "<PERSON>ert weider", "app.containers.SignUp.emailConsent": "Mat <PERSON>rer Umeldung erkläert <PERSON>, E-Maile vun dëser Plattform ze kréien. Dir kënnt op der Säit 'Meng Astellungen' auswielen, wat fir E-Mailen Dir kréie wëllt.", "app.containers.SignUp.emptyFirstNameError": "Gitt Äre V<PERSON>um<PERSON> an", "app.containers.SignUp.emptyLastNameError": "Gitt Äre Familljennumm an", "app.containers.SignUp.firstNamesLabel": "Virnumm", "app.containers.SignUp.goToLogIn": "Hutt <PERSON>r schonn e <PERSON>? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Ech hunn {link} gelies a sinn averstanen.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Ech hunn {link} gelies a sinn averstanen.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>, datt d'<PERSON> ob mitgestalten.wien.gv.at verwennt ginn. Weider Informatioune fannt Dir {link}.", "app.containers.SignUp.invitationErrorText": "Är Invitatioun ass ofgelaf oder gouf scho benotzt. <PERSON><PERSON> Dir schonn den Invitatiounslink benotzt hutt fir e Kont ze kreéieren, probé<PERSON>t Iech umellen. <PERSON><PERSON>, registréiert Iech fir en neie Kont ze kreéieren.", "app.containers.SignUp.lastNameLabel": "Familljennumm", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Follegt Är Fokusberäicher fir iwwer si informéiert ze ginn:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Follegt Är Liiblingsthemen fir iwwer si informéiert ze ginn:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Späichert Virléiften", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Iwwersprangen fir elo", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Akzeptéiert eis Dateschutzrichtlinn fir weiderzefueren", "app.containers.SignUp.signUp2": "As<PERSON><PERSON><PERSON>wen", "app.containers.SignUp.skip": "<PERSON><PERSON><PERSON>wwersprang<PERSON>", "app.containers.SignUp.tacError": "Akzeptéiert eis Geschäftsbedéngungen fir weiderzefueren", "app.containers.SignUp.thePrivacyPolicy": "d’Dateschutzrichtlinn", "app.containers.SignUp.theTermsAndConditions": "d’Geschäftsbedéngungen", "app.containers.SignUp.unknownError": "Et ass e Feeler opgetrueden. Probéiert et wgl. spéider nach eng Kéier.", "app.containers.SignUp.viennaConsentEmail": "E-Mail-Adress", "app.containers.SignUp.viennaConsentFirstName": "Virnumm", "app.containers.SignUp.viennaConsentFooter": "Dir kënnt Är Profilinformatiounen änneren, nodeem Dir Iech ageloggt hutt. Wann Dir schonn e Kont mat derselwechter E-Mail-Adress op mitgestalten.wien.gv.at hutt, gëtt e mat Ärerem aktuelle Kont verbonnen.", "app.containers.SignUp.viennaConsentHeader": "Déi folgend Donnéeë ginn iwwermëttelt:", "app.containers.SignUp.viennaConsentLastName": "Familljennumm", "app.containers.SignUp.viennaConsentUserName": "Benotzernumm", "app.containers.SignUp.viennaDataProtection": "D'Dateschutzrichtlinn vu Wien", "app.containers.SiteMap.contributions": "Bäiträg", "app.containers.SiteMap.cookiePolicyLinkTitle": "<PERSON><PERSON>", "app.containers.SiteMap.issues": "Kommentaren", "app.containers.SiteMap.options": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.projects": "Projeten", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Gespäichert!", "app.containers.SpamReport.inappropriate": "Et ass onpassend oder beleidegend", "app.containers.SpamReport.messageError": "Et ass e Feeler beim Iwwermëttele vun Ärem Formulaire opgetruden, probéiert et wgl. nach eng Kéier.", "app.containers.SpamReport.messageSuccess": "<PERSON>r Meldung gouf versch<PERSON>t", "app.containers.SpamReport.other": "<PERSON>ere <PERSON>", "app.containers.SpamReport.otherReasonPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "Dat hei gehéiert net hei hin", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "<PERSON>il<PERSON><PERSON>", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Är Stëmmen iwwer Propositiounen déi nach op fir ofstëmmen sinn, ginn geläscht. D'Stëmmen iwwer Propositiounen, wou d'Wahlzäit ofgeschloss ass, ginn net geläscht.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON><PERSON> do<PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "U <PERSON>jet<PERSON>, déi fir iwwerpréifte Benotzer bestëmmt sinn. ", "app.containers.UsersEditPage.becomeVerifiedTitle": "Iwwerpréift Är Identitéit", "app.containers.UsersEditPage.bio": "<PERSON>w<PERSON>", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "<PERSON>r kënnt dëst Feld net beaarbechten, well et iwwerpréiften Informatiounen enthält", "app.containers.UsersEditPage.buttonSuccessLabel": "Gespäichert", "app.containers.UsersEditPage.cancel": "Ofbriechen", "app.containers.UsersEditPage.changeEmail": "E-Mail änneren", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON><PERSON>t wgl. hei fir Är Iwwerpréiwung ze aktualiséieren. ", "app.containers.UsersEditPage.conditionsLinkText": "<PERSON><PERSON>", "app.containers.UsersEditPage.contactUs": "En anere Grond fir ze goen? {feedbackLink} a vläicht kënne mir hëllefen.", "app.containers.UsersEditPage.deleteAccountSubtext": "Et deet eis leed, datt Dir gitt.", "app.containers.UsersEditPage.deleteMyAccount": "<PERSON><PERSON><PERSON> Ko<PERSON> l<PERSON>schen", "app.containers.UsersEditPage.deleteYourAccount": "Äre Kont läschen", "app.containers.UsersEditPage.deletionSection": "Äre Kont läschen", "app.containers.UsersEditPage.deletionSubtitle": "<PERSON>ës Aktioun kann net réckgängeg gemaach ginn. D’Beiträg, déi Dir op der Plattform gepost hutt ginn anonymiséiert. Wann Dir all Är Beiträg läsche wëllt, kënnt Dir eis ënner <EMAIL> kontaktéieren.", "app.containers.UsersEditPage.email": "E-Mail", "app.containers.UsersEditPage.emailEmptyError": "Stellt eng E-Mail-Adress zur Verfügung", "app.containers.UsersEditPage.emailInvalidError": "Gitt eng E-Mail-Adress am richtege Format an, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "<PERSON><PERSON>t hei", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Virnumm", "app.containers.UsersEditPage.firstNamesEmptyError": "Stellt Virnimm zur Verfügung", "app.containers.UsersEditPage.h1": "<PERSON><PERSON>", "app.containers.UsersEditPage.h1sub": "D'Informatiounen vun Ärem Kont beaarbechten", "app.containers.UsersEditPage.image": "Avatar-Bild", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON><PERSON>, fir e Profilbild auszewielen (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "All Astellungen vun Ärem Profil", "app.containers.UsersEditPage.language": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "Familljennumm", "app.containers.UsersEditPage.lastNameEmptyError": "Stellt Familljennimm zur Verfügung", "app.containers.UsersEditPage.loading": "Lueden...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Dir kënnt Är E-Mail oder Passwuert hei änneren.", "app.containers.UsersEditPage.loginCredentialsTitle": "<PERSON><PERSON>sinformatiounen", "app.containers.UsersEditPage.messageError": "Mir konnten Äre Profil net späicheren. Probéiert méi spéit nach eng Kéier oder kontaktéiert <EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Äre Profil gouf gespäichert.", "app.containers.UsersEditPage.metaDescription": "<PERSON><PERSON><PERSON> ass d’Säit fir d’Profilastellunge vum {firstName} {lastName} op der participativer Plattform vum {tenantName}. Hei kënnt Dir Är Identitéit iwwerpréiwen, <PERSON><PERSON> Kontin<PERSON>ati<PERSON>, <PERSON><PERSON> Kont läschen an Är E-Mail-Präferenzen upassen.", "app.containers.UsersEditPage.metaTitle1": "Profil Astellungssäit vun {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "<PERSON><PERSON> <PERSON> d<PERSON><PERSON> k<PERSON>, hu mir keng <PERSON>iglechkeet méi Äre Kont erëm hier<PERSON>.", "app.containers.UsersEditPage.noNameWarning2": "<PERSON>ren Numm gëtt de Moment op der Plattform ugewisen als: \"{displayName}\" well Dir Ären Numm net aginn hutt. Dëst ass en autogeneréierten Numm. Wann Dir et wëllt änneren, gitt w.e.g. Ären Numm hei ënnen.", "app.containers.UsersEditPage.notificationsSubTitle": "Wéi eng E-Mail-Benoriichtunge wëllt Dir kréien?", "app.containers.UsersEditPage.notificationsTitle": "E-Mail-Benoriichtegungen", "app.containers.UsersEditPage.password": "Wielt en neit Passwuert", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON><PERSON> e <PERSON>, dat mindestens {minimumPasswordLength} <PERSON><PERSON><PERSON> laang ass.", "app.containers.UsersEditPage.passwordAddSection": "<PERSON><PERSON><PERSON><PERSON> e <PERSON> der<PERSON>", "app.containers.UsersEditPage.passwordAddSubtitle2": "Setzt e Passwuert a loggt Iech einfach op d'Plattform un, ouni Är E-Mail all Kéier ze bestätegen.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON>", "app.containers.UsersEditPage.passwordChangeSubtitle": "Bestätegt Äert aktuellt Passwuert a vergitt en neit Passwuert.", "app.containers.UsersEditPage.privacyReasons": "Wann Dir iwwer Är Privatsphär besuergt sidd, kënnt Dir {conditionsLink} liesen.", "app.containers.UsersEditPage.processing": "Verschécken...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Virnumm ass erfuerderlech wann de Familljennumm gëtt", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON>er <PERSON>r fortgitt...", "app.containers.UsersEditPage.submit": "Ännerunge späicheren", "app.containers.UsersEditPage.tooManyEmails": "Kritt Dir ze vill E-Mailen? Dir kënnt Är E-Mail-Präferenzen an Äre Profilastellunge änneren.", "app.containers.UsersEditPage.updateverification": "Hu sech Är offiziell Donnéeë geännert? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON><PERSON> h<PERSON>, datt mir <PERSON>ech eng E-Mail s<PERSON>cken, fir Iech ze benoriichtegen?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Dir kënnt u Projeten <PERSON>, déi just fir iwwerpréifte Benotzer zougänglech sinn. ", "app.containers.UsersEditPage.verifiedIdentityTitle": "Dir sidd en iwwerpréift<PERSON> Benotzer", "app.containers.UsersEditPage.verifyNow": "Elo iwwerpréiwen", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "<PERSON><PERSON> Är Äntwerten erof (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {keng <PERSON>} one {1 gär} other {#gefällt}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "<PERSON><PERSON><PERSON><PERSON>, an deem dëse Kommentar als Äntwert gepost gouf:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Kommentaren ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Mäi Profil be<PERSON>en", "app.containers.UsersShowPage.emptyInfoText": "Dir verfollegt keng Elementer vum spezifizéierte Filter hei uewen.", "app.containers.UsersShowPage.eventsWithCount": "Evenementer ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Folgend ({followingCount})", "app.containers.UsersShowPage.inputs": "Input", "app.containers.UsersShowPage.invisibleTitlePostsList": "All Bäiträg, déi vun dësem Benotzer agereecht goufen", "app.containers.UsersShowPage.invisibleTitleUserComments": "All Kommentaren, déi vun dësem Ben<PERSON>zer gepost goufen", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.loadMoreComments": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.loadingComments": "Komme<PERSON><PERSON> lueden...", "app.containers.UsersShowPage.loadingEvents": "<PERSON><PERSON><PERSON>er ...", "app.containers.UsersShowPage.memberSince": "Member säit {date}", "app.containers.UsersShowPage.metaTitle1": "Profil Säit vun {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "<PERSON><PERSON><PERSON> huet nach kee Kommentar gepost.", "app.containers.UsersShowPage.noCommentsForYou": "Momentan ginn et nach keng Kommentaren", "app.containers.UsersShowPage.noEventsForUser": "Dir hutt nach keng Eventer deelgeholl.", "app.containers.UsersShowPage.postsWithCount": "Bäiträg ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projet Classeure", "app.containers.UsersShowPage.projects": "Projeten", "app.containers.UsersShowPage.proposals": "Propo<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.seePost": "Bäiträg ukucken", "app.containers.UsersShowPage.surveyResponses": "Äntwerten ({responses})", "app.containers.UsersShowPage.topics": "Themen", "app.containers.UsersShowPage.tryAgain": "Et ass e Feeler opgetrueden. Wgl. probéiert et méi spéit nach eng Kéier.", "app.containers.UsersShowPage.userShowPageMetaDescription": "<PERSON><PERSON><PERSON> ass d’Profilsäit vum {firstName} {lastName} op der participativer Plattform {orgName}. Hei ass en Iwwerbléck iwwer all hiere/ senge Bäiträg.", "app.containers.VoteControl.close": "Zoumaachen", "app.containers.VoteControl.voteErrorTitle": "Et ass e <PERSON>er opgetrueden", "app.containers.admin.ContentBuilder.default": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageTextCards": "Bild & Text Kaarten", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & Akkordeonen", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 kolonne", "app.containers.admin.ContentBuilder.projectDescription": "Projet Beschreiwung", "app.containers.app.navbar.admin": "Plattform verwalten", "app.containers.app.navbar.allProjects": "All Projeten", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Menü fir d’mobil <PERSON>", "app.containers.app.navbar.editProfile": "<PERSON><PERSON>", "app.containers.app.navbar.fullMobileNavigation": "Vollstänneg mobil", "app.containers.app.navbar.logIn": "<PERSON><PERSON><PERSON>", "app.containers.app.navbar.logoImgAltText": "{orgName} Home", "app.containers.app.navbar.myProfile": "Mäi Profil", "app.containers.app.navbar.search": "<PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "Show voll Menü", "app.containers.app.navbar.signOut": "Ofmellen", "app.containers.eventspage.errorWhenFetchingEvents": "<PERSON><PERSON> vun Evenementer ass e Feeler opgetratt. Probéiert wgl. d'Säit nei ze lueden. ", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "All Evenementer weisen, déi op der Plattform {orgName} gepost goufen.", "app.containers.eventspage.eventsPageTitle1": "Evenementer | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projeten", "app.containers.eventspage.noPastEvents": "<PERSON>g verga<PERSON>en Evenementer fir unze<PERSON>sen", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Aktuell si keng bevirstoend oder lafend Evenementer geplangt.", "app.containers.eventspage.pastEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.upcomingAndOngoingEvents": "Bevirstoend oder lafend Evenementer", "app.containers.footer.accessibility-statement": "Zougänglechkeetserklärung", "app.containers.footer.ariaLabel": "Secondaire", "app.containers.footer.cookie-policy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.cookieSettings": "<PERSON>ie-Astellungen", "app.containers.footer.feedbackEmptyError": "<PERSON><PERSON><PERSON> Feld dierf net eidel sinn ", "app.containers.footer.poweredBy": "<PERSON>rméig<PERSON><PERSON> duerch (Citizen Lab)", "app.containers.footer.privacy-policy": "Dateschutzrichtlinn", "app.containers.footer.siteMap": "Säiteniwwersiicht", "app.containers.footer.terms-and-conditions": "Allgemeng Geschäftsbedéngungen", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Ofbriechen", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON>, ech w<PERSON><PERSON> verl<PERSON>sen", "app.containers.ideaHeading.editForm": "Form änneren", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Sidd Dir sécher datt Dir fortgoe wëllt?", "app.containers.ideaHeading.leaveIdeaForm": "Loosst Iddi Form", "app.containers.ideaHeading.leaveIdeaText": "Är Äntwerte ginn net gespäichert.", "app.containers.landing.cityProjects": "Projeten", "app.containers.landing.completeProfile": "Vervollstännegt Äre Profil", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {firstName}. <PERSON><PERSON> ass Zäit, Äre Profil ze vervollstännegen.", "app.containers.landing.createAccount": "As<PERSON><PERSON><PERSON>wen", "app.containers.landing.defaultSignedInMessage": "{orgName} la<PERSON><PERSON><PERSON> Iech no. Dir sidd un der Rei, fir Ärer Stëmm Gehéier ze verschafen!", "app.containers.landing.doItLater": "<PERSON>ch maachen et méi spéit", "app.containers.landing.new": "<PERSON><PERSON>", "app.containers.landing.subtitleCity": "Wëllkomm op der participativer Plattform {orgName}", "app.containers.landing.titleCity": "Loosst eis d'administrativ Vereinfachung op {orgName} gemeinsam gestalten", "app.containers.landing.twitterMessage": "Stëmmt fir {ideaTitle} ënner", "app.containers.landing.upcomingEventsWidgetTitle": "Bevirstoend oder lafend Evenementer", "app.containers.landing.userDeletedSubtitle": "Dir kënnt jidderzäit en neie Ko<PERSON> uleeën oder {contactLink}, fir eis wëssen ze loossen, wat mir verbessere kënnen.", "app.containers.landing.userDeletedSubtitleLinkText": "Schreift eis e puer Zeilen", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON> Kont gouf gelä<PERSON>t.", "app.containers.landing.userDeletionFailed": "<PERSON><PERSON> vun Ärem Kont ass e Feeler opgetrueden. Mir goufen informéiert a maachen eist Bescht fir dëse Problem ze léisen. Probéiert wgl. méi spéit nach eng Kéier. ", "app.containers.landing.verifyNow": "Elo iwwerpréiwen", "app.containers.landing.verifyYourIdentity": "Iwwerpréift Är Identitéit", "app.containers.landing.viewAllEventsText": "All Evenementer weisen ", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Z<PERSON><PERSON> an den Dossier", "app.errors.after_end_at": "De Startdatum läit no dem Enddatum", "app.errors.avatar_carrierwave_download_error": "D’Profilbild konnt net erofgeluede ginn.", "app.errors.avatar_carrierwave_integrity_error": "Den Dateityp vum gewielte Profilbild ass net erlaabt. ", "app.errors.avatar_carrierwave_processing_error": "D'Profilbild konnt net veraarbecht ginn. ", "app.errors.avatar_extension_blacklist_error": "Den Dateityp vum gewielte Profilbild ass net erlaabt. Erlaabten Endunge sinn: jpg, jpeg, gif a png.", "app.errors.avatar_extension_whitelist_error": "Den Dateityp vum gewielte Profilbild ass net erlaabt. Erlaabten Endunge sinn: jpg, jpeg, gif a png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON> <PERSON>-Text an.", "app.errors.banner_cta_button_url_blank": "<PERSON><PERSON> e <PERSON> an.", "app.errors.banner_cta_button_url_url": "<PERSON><PERSON> e gël<PERSON>ge Link an. <PERSON>, datt de <PERSON> mat 'https://' ufänkt.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON> <PERSON>-Text an.", "app.errors.banner_cta_signed_in_url_blank": "<PERSON><PERSON> e <PERSON> an.", "app.errors.banner_cta_signed_in_url_url": "<PERSON><PERSON> e gël<PERSON>ge Link an. <PERSON>, datt de <PERSON> mat 'https://' ufänkt.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON> <PERSON>-Text an.", "app.errors.banner_cta_signed_out_url_blank": "<PERSON><PERSON> e <PERSON> an.", "app.errors.banner_cta_signed_out_url_url": "<PERSON><PERSON> e gël<PERSON>ge Link an. <PERSON>, datt de <PERSON> mat 'https://' ufänkt.", "app.errors.base_includes_banned_words": "Dir hutt vläicht een oder méi Wierder ben<PERSON>, déi als Schwéierwierder ugesi ginn. Ännert w.e.g. Ären Text fir all Schwéierwierder ze läschen, déi eventuell do sinn.", "app.errors.body_multiloc_includes_banned_words": "D'Beschreiwung enthält Wierder déi als onpassend ugesi ginn.", "app.errors.bulk_import_idea_not_valid": "Déi resultéierend Iddi ass net valabel: {value}.", "app.errors.bulk_import_image_url_not_valid": "Kee Bild konnt vun {value}erofgeluede ginn. Vergewëssert Iech datt d'URL gëlteg ass a mat enger Dateierweiterung wéi .png oder .jpg endet. Dëst Thema geschitt an der Rei mat ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "<PERSON><PERSON><PERSON> mat enger fehlend <PERSON> an {value}. Dëst Thema geschitt an der Rei mat ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "<PERSON><PERSON><PERSON> mat enger net-numerescher Koordinat an {value}. Dëst Thema geschitt an der Rei mat ID {row}.", "app.errors.bulk_import_malformed_pdf": "Déi eropgeluede PDF Datei schéngt falsch ze sinn. Probéiert de PDF nach eng Kéier vun Ärer Quell exportéieren an dann erëm eropzelueden.", "app.errors.bulk_import_maximum_ideas_exceeded": "De Maximum vun {value} <PERSON><PERSON><PERSON> gouf iwwer<PERSON>t.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "De Maximum vu {value} Säiten an engem PDF gouf iwwerschratt.", "app.errors.bulk_import_not_enough_pdf_pages": "Den eropgeluedene PDF huet net genuch Säiten - et soll op d'mannst déiselwecht Zuel vu Säiten hunn wéi déi erofgelued<PERSON> Schabloun.", "app.errors.bulk_import_publication_date_invalid_format": "<PERSON>ddi mat ongëlteg Verëffentlechungsdatumformat \"{value}\". Benotzt w.e.g. de Format \"DD-MM-JJJJ\".", "app.errors.cannot_contain_ideas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON> hutt, <PERSON><PERSON><PERSON><PERSON>tz<PERSON> dësen Typ vu Bäiträg net. Ännert wgl. Är Auswiel a probéiert nach eng Kéier.", "app.errors.cant_change_after_first_response": "<PERSON>r kënnt dëst net méi änneren, well aner <PERSON><PERSON><PERSON> scho <PERSON>eänt<PERSON>t hunn.", "app.errors.category_name_taken": "<PERSON><PERSON> g<PERSON>tt schonn eng Kategorie mat dësem Numm", "app.errors.confirmation_code_expired": "De Code ass ofgelaf. Frot wgl. en neie Code un.", "app.errors.confirmation_code_invalid": "Ongëltegen Bestätegungscode. Kuckt wgl. an Ärer E-Mail fir de richtege Code oder klickt op \"Neie Code schécken\" ", "app.errors.confirmation_code_too_many_resets": "Dir hutt de Bestätegungscode ze oft erëm verschéckt. Wgl. kontaktéiert eis, fir amplaz en Invitatiounscode ze kréien.", "app.errors.confirmation_code_too_many_retries": "Dir hutt ze oft probéiert. Frot wgl. e neie Code un oder probéiert Är E-Mail-Adress ze änneren.", "app.errors.email_already_active": "D’E-Mail-<PERSON>ress {value}, aus der Rei {row}, geh<PERSON><PERSON>t schonn engem ageschriwwene <PERSON>", "app.errors.email_already_invited": "D’E-Mail-<PERSON>ress {value}, aus der Rei {row}, gouf schonn invité<PERSON>t", "app.errors.email_blank": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.errors.email_domain_blacklisted": "Benotzt wgl. eng aner E-Mail-Domain, fir Iech anzeschreiwen.", "app.errors.email_invalid": "Benotzt wgl. eng gëlteg E-Mail-Adress.", "app.errors.email_taken": "<PERSON>nt mat där E-Mail-Adress existé<PERSON>t schonn. <PERSON><PERSON> wgl domat un.", "app.errors.email_taken_by_invite": "{value} g<PERSON>tt schonn vun enger ausstoender Invitatioun benotzt. Wann Dir de Mail net fannt kontaktéiert wgl.  {supportEmail} ", "app.errors.emails_duplicate": "Een oder méi duebel Wäerter fir d’E-Mail-Adress {value} goufen an de(r) folgende(r) Rei(e) fonnt: {rows}", "app.errors.extension_whitelist_error": "De Format vun der <PERSON>, d<PERSON><PERSON> er<PERSON> w<PERSON>, g<PERSON>tt net <PERSON>nnerstëtzt.", "app.errors.file_extension_whitelist_error": "De Format vun der Datei, dé<PERSON>r versicht hutt erop<PERSON>, g<PERSON>tt net ënnerstëtzt.", "app.errors.first_name_blank": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.errors.generics.blank": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.errors.generics.invalid": "Dëst gesäit net wéi e gëltege Wäert aus.", "app.errors.generics.taken": "Dës E-Mail-<PERSON><PERSON> g<PERSON> et schonn. En anere Kont ass domat verbonnen.", "app.errors.generics.unsupported_locales": "Dëst Feld ënnerstëtzt net déi aktuell regional Astellungen. ", "app.errors.group_ids_unauthorized_choice_moderator": "Als Projetsmanager kënnt Dir nëmme Leit e-mailen, d<PERSON><PERSON> zu Äre Projet(en) hunn.", "app.errors.has_other_overlapping_phases": "<PERSON>jeten d<PERSON> keng <PERSON> hunn, déi sech iwwerschneiden.", "app.errors.invalid_email": "D’E-Mail-Adress {value}, aus der Rei{row}, ass keng gëlteg E-Mail-Adress", "app.errors.invalid_row": "En onbekannte Feeler ass wärend der Veraarbechtung vu Rei {row} opgetrueden", "app.errors.is_not_timeline_project": "Den aktuelle Projet ënnerstëtzt keng Phasen.", "app.errors.key_invalid": "<PERSON> Schlëssel dierf nëmme aus Buschstawen, <PERSON><PERSON><PERSON> an Ënnerstrécher bestoen (_)", "app.errors.last_name_blank": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.errors.locale_blank": "Wielt wgl. eng <PERSON>", "app.errors.locale_inclusion": "Wielt wgl. eng vun den ënnerstëtzten Sproochen", "app.errors.malformed_admin_value": "<PERSON>-<PERSON><PERSON><PERSON>{value}, deen a <PERSON> {row} fonnt gouf, ass net gëlteg", "app.errors.malformed_groups_value": "<PERSON> {value}, aus der Re<PERSON> {row}, ass kee g<PERSON><PERSON><PERSON> G<PERSON>", "app.errors.max_invites_limit_exceeded1": "D'Zuel vun den Invitatiounen iwwerschreift d'Limite vun 1000.", "app.errors.maximum_attendees_greater_than1": "Déi maximal Zuel vun den Umeldungen däerf méi wéi 0 sinn.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Déi maximal Zuel vun den Umeldungen muss méi grouss oder gläich wéi déi aktuell Zuel vun den Umeldungen sinn.", "app.errors.no_invites_specified": "<PERSON>g <PERSON>-<PERSON>-<PERSON><PERSON> fonnt.", "app.errors.no_recipients": "D'Campagne kann net verschéckt ginn well et keng Empfänger sinn. De Grupp un deen Dir schéckt ass entweder eidel, oder keen huet zoustëmmen E-Mailen ze kréien.", "app.errors.number_invalid": "Gitt weg eng vala<PERSON>.", "app.errors.password_blank": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.errors.password_invalid": "Wgl. iwwerpréift Äert neit Passwuert nach eng Kéier.", "app.errors.password_too_short": "<PERSON><PERSON><PERSON>wuert muss mindestens 8 Z<PERSON>chen hunn", "app.errors.resending_code_failed": "<PERSON>ppes ass falsch gaang beim Ausschécken vum Confirmatiounscode.", "app.errors.slug_taken": "Dës Projets-URL existéiert schonn. Ännert wgl. de Projet-Slug.", "app.errors.tag_name_taken": "En Tag mat dësem Numm g<PERSON> et schonn", "app.errors.title_multiloc_blank": "Den Titel dierf net eidel sinn.", "app.errors.title_multiloc_includes_banned_words": "Den Titel enthält Wierder déi als onpassend ugesi ginn.", "app.errors.token_invalid": "D'Linke fir d'Passwuert z<PERSON>ckzesetzen kennen nëmmen eemol benotzt ginn a si just eng Stonn nodeems se verschéckt goufe valabel. {passwordResetLink}. ", "app.errors.too_common": "<PERSON><PERSON><PERSON> ass liicht ze erroden. Wielt wgl. e méi séchert <PERSON>wuert.", "app.errors.too_long": "Wielt wgl. e méi kuerzt Passwuert (max 72 <PERSON><PERSON>chen)", "app.errors.too_short": "Wielt wgl. e <PERSON><PERSON><PERSON> mat mindestens 8 Zeechen", "app.errors.uncaught_error": "En onbekannte Feeler ass geschitt.", "app.errors.unknown_group": "<PERSON> {value}, aus der Rei {row} , ass onbekannt", "app.errors.unknown_locale": "<PERSON><PERSON><PERSON>prooch {value}, aus der Rei {row}, ass keng konfiguréiert Sprooch", "app.errors.unparseable_excel": "Déi ausgewielten Excel-Datei kann net beaarbecht ginn.", "app.errors.url": "Gitt eng valabel Link. Gitt sécher datt de Link mat https:// ufänkt", "app.errors.verification_taken": "D'Verifikat<PERSON><PERSON> kann net ofgeschloss ginn well en anere Kont mat de selwechte Detailer verifizéiert gouf.", "app.errors.view_name_taken": "<PERSON>t g<PERSON>tt schonn eng Usiicht mat dësem Numm", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Net ugemiessenen Inhalt gouf automatesch an engem Post oder Kommentar erkannt", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Mat StandardPortal aloggen", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "<PERSON> umellen", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Leet elo e Staad-Wien-Kont un a benotzt ee Login fir vill digital Servicer vu Wien.", "app.modules.id_cow.cancel": "Ofbriechen", "app.modules.id_cow.emptyFieldError": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.modules.id_cow.helpAltText": "<PERSON><PERSON>, wou d’ID-Seriennummer op enger Identitéitskaart ze fannen ass", "app.modules.id_cow.invalidIdSerialError": "Ongëlteg ID-Serie", "app.modules.id_cow.invalidRunError": "Ongëlteg RUN", "app.modules.id_cow.noMatchFormError": "<PERSON><PERSON>estëmmung fonnt.", "app.modules.id_cow.notEntitledFormError": "Net berechtegt.", "app.modules.id_cow.showCOWHelp": "Wou fannen ech meng ID-Seriennummer?", "app.modules.id_cow.somethingWentWrongError": "Mir kënnen Iech net verifizéieren, well eppes schifga<PERSON> ass", "app.modules.id_cow.submit": "Erreechen", "app.modules.id_cow.takenFormError": "<PERSON><PERSON> verginn.", "app.modules.id_cow.verifyCow": "COW-Notzung verifizéieren", "app.modules.id_franceconnect.verificationButtonAltText": "Mat FranceConnect verifizéieren", "app.modules.id_gent_rrn.cancel": "Ofbriechen", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.modules.id_gent_rrn.gentRrnHelp": "Är Sozialversécherungsnummer steet op der Récksäit vun Ärer digitaler Identitéitskaart", "app.modules.id_gent_rrn.invalidRrnError": "Ongëlteg Sozialversécherungsnummer", "app.modules.id_gent_rrn.noMatchFormError": "Mir hu keng Informatioun iwwer Är Sozialversécherungsnummer op der Récksäit fonnt", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Mir kënnen Iech net verifizéieren, well <PERSON><PERSON> net zu <PERSON> wunnt.", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Mir kënnen Iech net verifizéieren, well Dir méi jonk wéi 14 Joer sidd", "app.modules.id_gent_rrn.rrnLabel": "Sozialversécherungsnummer", "app.modules.id_gent_rrn.rrnTooltip": "Mir froen no Ärer Sozialversécherunsnummer, fir ze verifizéieren, ob <PERSON>r en Awunner vu Gent iwwer 14 Joer sidd.", "app.modules.id_gent_rrn.showGentRrnHelp": "Wou fannen ech meng ID-Seriennummer?", "app.modules.id_gent_rrn.somethingWentWrongError": "Mir kënnen Iech net verifizéieren, well eppes schifga<PERSON> ass", "app.modules.id_gent_rrn.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.takenFormError": "Är Sozialversécherungsnummer gouf scho <PERSON>zt, fir en anere Kont ze verifizéieren", "app.modules.id_gent_rrn.verifyGentRrn": "<PERSON> veri<PERSON>", "app.modules.id_id_card_lookup.cancel": "Ofbriechen", "app.modules.id_id_card_lookup.emptyFieldError": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.modules.id_id_card_lookup.helpAltText": "ID-Kaartenexplikatioun", "app.modules.id_id_card_lookup.invalidCardIdError": "Dëse Ben<PERSON>zernumm ass net gëlteg.", "app.modules.id_id_card_lookup.noMatchFormError": "<PERSON><PERSON>estëmmung fonnt.", "app.modules.id_id_card_lookup.showHelp": "Wou fannen ech meng ID-Seriennummer?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Mir kënnen Iech net verifizéieren, well eppes schifga<PERSON> ass", "app.modules.id_id_card_lookup.submit": "Erreechen", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON> verginn.", "app.modules.id_oostende_rrn.cancel": "Ofbriechen", "app.modules.id_oostende_rrn.emptyFieldError": "<PERSON><PERSON><PERSON> dierf net eidel sinn", "app.modules.id_oostende_rrn.invalidRrnError": "Ongëlteg Sozialversécherungsnummer", "app.modules.id_oostende_rrn.noMatchFormError": "Mir hu keng Informatioun iwwer Är Sozialversécherungsnummer op der Récksäit fonnt", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Mir kënnen Iech net verifizéieren, well <PERSON><PERSON> ausserhalb vun <PERSON>e wunnt", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Mir kënnen Iech net verifizéieren, well Dir méi jonk wéi 14 Joer sidd", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Är Sozialversécherungsnummer steet op der Récksäit vun Ärer digitaler Identitéitskaart", "app.modules.id_oostende_rrn.rrnLabel": "Sozialversécherungsnummer", "app.modules.id_oostende_rrn.rrnTooltip": "Mir froen no Ärer Matricule fir ze préiwen, ob <PERSON><PERSON> en Awunner vun O<PERSON> sidd, iwwer14 Joer al.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Wou fannen ech meng Matricule?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Mir kënnen Iech net verifizéieren, well eppes schifga<PERSON> ass", "app.modules.id_oostende_rrn.submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.takenFormError": "Är Sozialversécherungsnummer gouf scho <PERSON>zt, fir en anere Kont ze verifizéieren", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Gebrauch vun der Matricule verifizéieren", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "<PERSON>r hutt Administrateursrechter iwwer den \"{folderName}\"-<PERSON><PERSON> kritt.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "<PERSON><PERSON>", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Projeten bei {folderUrl} ukucken, fir datt Är <PERSON>ëmm gehé<PERSON>t gëtt!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | vun der participativer Plattform vum {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | vun der participativer Plattform vum {orgName}", "app.sessionRecording.accept": "<PERSON>, ech akzeptéieren", "app.sessionRecording.modalDescription1": "Fir eis Ben<PERSON> besser ze verstoen, froe mir zoufälleg e klenge Prozentsaz vun de Besucher fir hir Surfen Sessioun am Detail ze verfolgen.", "app.sessionRecording.modalDescription2": "Den eenzegen Zweck vun den opgehollen Donnéeën ass d'Websäit ze verbesseren. Keen vun Ären Donnéeë gëtt mat enger Drëtt Partei gedeelt. All sensibel Informatioun, dé<PERSON>, g<PERSON>tt gef<PERSON>.", "app.sessionRecording.modalDescription3": "<PERSON>k<PERSON>pt<PERSON>iert Dir?", "app.sessionRecording.modalDescriptionFaq": "FAQ hei.", "app.sessionRecording.modalTitle": "Hëlleft eis dës Websäit ze verbesseren", "app.sessionRecording.reject": "<PERSON><PERSON>, ech refuséieren", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Budgetverdeelungsexercice duerchféieren", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Sammelt Feedback op engem Dokument", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "eng plattformintern Ëmfro uleeën", "app.utils.AdminPage.ProjectEdit.createPoll": "Eng Ëmfro uleeën", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Eng extern Ëmfro abetten", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Volontaire fannen", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Bäiträg a Feedback sammelen", "app.utils.AdminPage.ProjectEdit.shareInformation": "Informatiounen <PERSON>", "app.utils.FormattedCurrency.credits": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.tokens": "Jetonen", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# Credits} one {# Kredit} other {# Credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# Tokens} one {# Token} other {# Tokens}}", "app.utils.IdeaCards.mostDiscussed": "<PERSON><PERSON><PERSON> me<PERSON>cht <PERSON>", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON><PERSON>", "app.utils.IdeaCards.newest": "<PERSON><PERSON><PERSON>", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "Zoufälleg", "app.utils.IdeaCards.trending": "Trending", "app.utils.IdeasNewPage.contributionFormTitle": "Neie Bäitrag bäifügen", "app.utils.IdeasNewPage.ideaFormTitle": "<PERSON><PERSON>", "app.utils.IdeasNewPage.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON> nei <PERSON>", "app.utils.IdeasNewPage.issueFormTitle1": "<PERSON><PERSON><PERSON><PERSON> neie Kommentar", "app.utils.IdeasNewPage.optionFormTitle": "<PERSON><PERSON> b<PERSON>ifügen", "app.utils.IdeasNewPage.petitionFormTitle": "<PERSON><PERSON><PERSON><PERSON> eng nei <PERSON>", "app.utils.IdeasNewPage.projectFormTitle": "Neie Projet bäifügen", "app.utils.IdeasNewPage.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON> nei Propositi<PERSON>", "app.utils.IdeasNewPage.questionFormTitle": "<PERSON><PERSON>", "app.utils.IdeasNewPage.surveyTitle": "Ëmfro", "app.utils.IdeasNewPage.viewYourComment": "<PERSON>ckt Äre Kommentar", "app.utils.IdeasNewPage.viewYourContribution": "Kuckt Äre Bäitrag", "app.utils.IdeasNewPage.viewYourIdea": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourInitiative": "Kuckt Är Initiativ", "app.utils.IdeasNewPage.viewYourInput": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourIssue": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourOption": "<PERSON><PERSON><PERSON> Ä<PERSON> Optiou<PERSON>", "app.utils.IdeasNewPage.viewYourPetition": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourProject": "Kuckt Äre Projet", "app.utils.IdeasNewPage.viewYourProposal": "<PERSON><PERSON><PERSON> Är Propositioun", "app.utils.IdeasNewPage.viewYourQuestion": "<PERSON><PERSON><PERSON>", "app.utils.Projects.sendSubmission": "Schéckt Soumissioun Identifizéierer op meng E-Mail", "app.utils.Projects.sendSurveySubmission": "Schéckt Ëmfro Soumissioun Identifizéierer op meng E-Mail", "app.utils.Projects.surveySubmission": "Ëmfro <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.Projects.yourResponseHasTheFollowingId": "Är Äntwert huet de folgenden Identifizéierer: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "<PERSON><PERSON> spéider décidéiert datt Dir wëllt datt Är Äntwert ewechgeholl gëtt, kontaktéiert eis w.e.g. mat dem folgenden eenzegaartegen Identifizéierer:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Dir musst Äre Profil komplett fir dëst Evenement deelzehuelen.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Dir entsprécht net den Ufuerderunge fir dëst Evenement deelzehuelen.", "app.utils.actionDescriptors.attendingEventNotPermitted": "<PERSON>r sidd net erla<PERSON>t dëst Evenement deelzehuelen.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Dir musst I<PERSON> aloggen oder registréieren fir un dësem Event deelzehuelen.", "app.utils.actionDescriptors.attendingEventNotVerified": "Dir musst Äre Kont verifizéieren ier Dir op dësem Event kënnt deelhuelen.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Dir musst Äre Profil ausfëllen fir fräiwëlleg ze sinn.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Dir erfëllt net den Ufuerderunge fir fräiwëlleg ze sinn.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Dir sidd net erlaabt Iech fräiwëlleg ze maachen.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Dir musst Iech aloggen oder aschreiwen fir fräiwëlleg ze sinn.", "app.utils.actionDescriptors.volunteeringNotVerified": "Dir musst Äre Kont verifizéieren ier Dir fräiwëlleg sidd.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "W.e.g. {completeRegistrationLink} fir fräiwëlleg ze sinn.", "app.utils.errors.api_error_default.in": "Ass net richteg", "app.utils.errors.default.ajv_error_birthyear_required": "W.e.g. drot <PERSON><PERSON> an", "app.utils.errors.default.ajv_error_date_any": "W.e.g. drot e g<PERSON>tegen Datum an", "app.utils.errors.default.ajv_error_domicile_required": "W.e.g. drot <PERSON><PERSON> an", "app.utils.errors.default.ajv_error_gender_required": "W.e.g. drot <PERSON><PERSON>eschlecht an", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.ajv_error_maxItems": "<PERSON><PERSON><PERSON><PERSON> net méi wéi {limit, plural, one {# objet} other {# objeten}}enthalen", "app.utils.errors.default.ajv_error_minItems": "Muss mindestens {limit, plural, one {# objet} other {# objeten}}enthalen", "app.utils.errors.default.ajv_error_number_any": "W.e.g. drot eng g<PERSON><PERSON><PERSON> an", "app.utils.errors.default.ajv_error_politician_required": "W.e.g. drot an, ob <PERSON><PERSON> e Politiker sidd", "app.utils.errors.default.ajv_error_required3": "Feld ass erfuerderlech: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Däerf net eidel sinn", "app.utils.errors.default.api_error_accepted": "Muss akzeptéiert ginn", "app.utils.errors.default.api_error_blank": "Däerf net eidel sinn", "app.utils.errors.default.api_error_confirmation": "Stëmmt net iwwereneen", "app.utils.errors.default.api_error_empty": "Däerf net eidel sinn", "app.utils.errors.default.api_error_equal_to": "Ass net richteg", "app.utils.errors.default.api_error_even": "Muss gerued sinn", "app.utils.errors.default.api_error_exclusion": "<PERSON><PERSON> reserv<PERSON>", "app.utils.errors.default.api_error_greater_than": "Ass ze kleng", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Ass ze kleng", "app.utils.errors.default.api_error_inclusion": "Ass net an der Lëscht opgeféiert", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.api_error_less_than": "Ass ze grouss", "app.utils.errors.default.api_error_less_than_or_equal_to": "Ass ze grouss", "app.utils.errors.default.api_error_not_a_number": "<PERSON><PERSON> k<PERSON>", "app.utils.errors.default.api_error_not_an_integer": "Muss eng ganz <PERSON> sinn", "app.utils.errors.default.api_error_other_than": "Ass net richteg", "app.utils.errors.default.api_error_present": "Muss eidel sinn", "app.utils.errors.default.api_error_too_long": "<PERSON><PERSON> ze laang", "app.utils.errors.default.api_error_too_short": "Ass ze kuerz", "app.utils.errors.default.api_error_wrong_length": "<PERSON>et déi falsch Längt", "app.utils.errors.defaultapi_error_.odd": "<PERSON>ss ongerued sinn", "app.utils.notInGroup": "<PERSON>r erfëllt net den Ufuerderunge fir matzemaachen.", "app.utils.participationMethod.onSurveySubmission": "Merci. <PERSON> hunn Är Äntwert kritt.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Ofstëmmung ass net méi méiglech, well dës Phase net méi aktiv ass.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Dir erfëllt net den Ufuerderunge fir ze wielen.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "<PERSON>r sidd net erlaabt ze wielen.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Dir musst Iech aloggen oder aschreiwen fir ze wielen.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Dir musst Äre Kont verifizéieren ier Dir kënnt wielen.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>D'Budgeten ofzeginn zougemaach op {endDate}.</b> D'Participanten haten insgesamt <b>{maxBudget} jidderee fir tëscht {optionCount} Optiounen ze verdeelen.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Budget present<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budget presentéiert 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Dir entsprécht net den Ufuerderunge fir Budgeten ze ginn.", "app.utils.votingMethodUtils.budgetingNotPermitted": "<PERSON>r sidd net erlaabt Budgeten ze zouzeschreiwen.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Dir musst Iech aloggen oder registréieren fir Budgeten ze ginn.", "app.utils.votingMethodUtils.budgetingNotVerified": "Dir musst Äre Kont z'iwwerpréiwen ier Dir Budgeten zoudeele kënnt.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Äre Budget gëtt net</b> gezielt bis Dir op \"Submit\" klickt", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "De Minimum néideg Budget ass {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON>n Dir fä<PERSON><PERSON> sidd, klickt op \"Ofginn\" fir Äre verdeelte Budget ze validéieren.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Wielt Äre gewënschte Projet andeems Dir op \"Bäisetzen\" klickt.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Dir hutt am Ganzen <b>{maxBudget} fir tëscht {optionCount} Optiounen</b>ze verdeelen.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Äre Budget gouf virgeluecht!</b> Dir kënnt Är Optiounen hei ënnen zu all Moment iwwerpréiwen oder se virum <b>{endDate}</b>änneren.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Äre Budget gouf virgeluecht!</b> <PERSON>r kënnt Är Optiounen hei ënnen zu all Moment kontrolléieren.", "app.utils.votingMethodUtils.castYourVote": "Gitt Är Stëmm", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Dir kënnt maximal {maxVotes, plural, one {# <PERSON><PERSON><PERSON>} other {# <PERSON><PERSON><PERSON>}} pro <PERSON>ti<PERSON><PERSON> der<PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Dir kënnt maximal {maxVotes, plural, one {# <PERSON><PERSON>} other {# <PERSON><PERSON>}} pro <PERSON>ti<PERSON><PERSON> derb<PERSON>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Dir kënnt maximal {maxVotes, plural, one {# Token} other {# Tokens}} pro <PERSON>ti<PERSON><PERSON> derb<PERSON>en.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Dir kënnt maximal {maxVotes, plural, one {# Stëmmen} other {# Stëmmen}} pro <PERSON>ti<PERSON><PERSON> derb<PERSON>tzen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON>n Dir f<PERSON><PERSON> sidd, klickt op \"Submit\" fir Är Stëmm ofzeginn.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Wielt Är gewënschte Optiounen andeems Dir op \"Wielt\" tippt.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Dir hutt insgesamt <b>{totalVotes, plural, one {# Kreditt} other {# Kreditter}} fir ze verdeelen tëscht {optionCount, plural, one {# Optioun} other {# Optiounen}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Dir hutt insgesamt <b>{totalVotes, plural, one {# <PERSON>t} other {# <PERSON>ten}} fir ze verdeelen tëscht {optionCount, plural, one {# Optioun} other {# Optiounen}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Dir hutt insgesamt <b>{totalVotes, plural, one {# Token} other {# Tokens}} fir ze verdeelen tëscht {optionCount, plural, one {# Optioun} other {# Optiounen}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Dir hutt insgesamt <b>{totalVotes, plural, one {# Stëmm} other {# Stëmmen}} fir ze verdeelen tëscht {optionCount, plural, one {# Optioun} other {# Optiounen}}</b>.", "app.utils.votingMethodUtils.finalResults": "Finale Resultater", "app.utils.votingMethodUtils.finalTally": "Finale Zuel", "app.utils.votingMethodUtils.howToParticipate": "Sou kënnt Dir <PERSON>\n", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON><PERSON> wielen", "app.utils.votingMethodUtils.multipleVotingEnded1": "D'Ofstëmmung ass zougemaach den <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 <PERSON><PERSON>itte<PERSON>} one {1 Kreditt} other {# Kreditter}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 <PERSON><PERSON>} one {1 <PERSON><PERSON>} other {# <PERSON><PERSON>}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 Tokenen} one {1 Token} other {# Tokenen}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 <PERSON><PERSON><PERSON><PERSON>} one {1 Stëmm} other {# <PERSON><PERSON>mmen}}", "app.utils.votingMethodUtils.results": "Resultater", "app.utils.votingMethodUtils.singleVotingEnded": "Ofstëmmung zougemaach op <b>{endDate}.</b> Participanten konnten <b>fir {maxVotes} Optiounen ofstëmmen.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Wielt Är gewënschte Optiounen andeems Dir op \"Stëmmen\" tippt", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "<PERSON>r hutt <b>{totalVotes} Stëmmen</b> déi Dir un d'Optiounen zouzeechne kënnt.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON>n Dir f<PERSON><PERSON> sidd, klickt op \"Submit\" fir Är Stëmm ofzeginn.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Ofstëmmung zougemaach op <b>{endDate}.</b> Participanten konnten <b>fir 1 Optioun wielen.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "<PERSON>ielt Är gewënschte Optioun andeems Dir op \"Stëmmen\" tippt.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "<PERSON>r hutt <b>1 Vote</b> datt Dir op eng vun den Optiounen zouzeschreiwen kann.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Ofstëmmung zougemaach op <b>{endDate}.</b> Participanten konnten <b>fir esou vill Méiglechkeeten ofstëmmen wéi se wollten.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Dir kënnt fir esou vill Optiounen ofstëmmen wéi Dir wëllt.", "app.utils.votingMethodUtils.submitYourBudget": "Gitt Äre Budget of", "app.utils.votingMethodUtils.submittedBudgetCountText2": "Persoun huet hire Budget online ofginn", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "Leit hir <PERSON>en online ofginn", "app.utils.votingMethodUtils.submittedVoteCountText2": "<PERSON><PERSON><PERSON> huet hir <PERSON> online ofginn", "app.utils.votingMethodUtils.submittedVotesCountText2": "<PERSON>it hir Stëmmen online ofginn", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Vote presentéiert 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON><PERSON><PERSON> ofginn", "app.utils.votingMethodUtils.votingClosed": "Ofstëmmung zougemaach", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b><PERSON><PERSON> Stëmm gëtt net gezielt</b> bis Dir op \"Ofschécken\" klickt.", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Stëmm gouf ofginn!</b> Dir kënnt Är Stëmm kontrolléieren oder änneren ier <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ëmm gouf ofginn!</b> <PERSON>r kënnt Är Stëmm zu all Moment hei ënnendrënner kontrolléieren oder änneren.", "components.UI.IdeaSelect.noIdeaAvailable": "Et gi keng Iddien verfügbar.", "components.UI.IdeaSelect.selectIdea": "<PERSON><PERSON><PERSON>", "containers.SiteMap.allProjects": "All Projeten", "containers.SiteMap.customPageSection": "Benotzerdefinéiert Säiten", "containers.SiteMap.folderInfo": "<PERSON><PERSON><PERSON>", "containers.SiteMap.headSiteMapTitle": "Site Plang | {orgName}", "containers.SiteMap.homeSection": "Allgemeng", "containers.SiteMap.pageContents": "Säiteninhalt", "containers.SiteMap.profilePage": "Är Profilsäit", "containers.SiteMap.profileSettings": "Är Astellungen", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informatioun", "containers.SiteMap.projectPoll": "Ëmfro", "containers.SiteMap.projectSurvey": "Ëmfro", "containers.SiteMap.projectsArchived": "Archivéiert Projeten", "containers.SiteMap.projectsCurrent": "Aktuell Projeten", "containers.SiteMap.projectsDraft": "Projetsentwërf", "containers.SiteMap.projectsSection": "Projete vun {orgName}", "containers.SiteMap.signInPage": "<PERSON><PERSON><PERSON>", "containers.SiteMap.signUpPage": "As<PERSON><PERSON><PERSON>wen", "containers.SiteMap.siteMapDescription": "<PERSON>un dë<PERSON> aus kënnt Dir zu all den Inhalter op der Plattform navigéieren.", "containers.SiteMap.siteMapTitle": "Iwwersiichtssäit vun der participativer Plattform vum {orgName}", "containers.SiteMap.successStories": "Erfollegsgeschichten", "containers.SiteMap.timeline": "Projetsphasen", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON>", "containers.SubscriptionEndedPage.accessDenied": "Dir hutt keen Z<PERSON>gang méi", "containers.SubscriptionEndedPage.subscriptionEnded": "<PERSON><PERSON><PERSON> ass nëmmen zougänglech mat engem aktivem Abonnement."}