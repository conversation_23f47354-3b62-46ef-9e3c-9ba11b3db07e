{"EmailSettingsPage.emailSettings": "Configurações de e-mail", "EmailSettingsPage.initialUnsubscribeError": "Ocorreu um problema ao cancelar a inscrição desta campanha, por favor tente novamente.", "EmailSettingsPage.initialUnsubscribeLoading": "O seu pedido está sendo processado, por favor aguarde...", "EmailSettingsPage.initialUnsubscribeSuccess": "Você cancelou com sucesso a inscrição de {campaignTitle}.", "UI.FormComponents.optional": "Campo opcional", "app.closeIconButton.a11y_buttonActionMessage": "<PERSON><PERSON><PERSON>", "app.components.Areas.areaUpdateError": "Ocorreu um erro ao guardar a sua área. Por favor, tente novamente.", "app.components.Areas.followedArea": "<PERSON><PERSON> seguida: {areaTitle}", "app.components.Areas.followedTopic": "Tópico seguido: {topicTitle}", "app.components.Areas.topicUpdateError": "Ocorreu um erro ao guardar o seu tópico. Por favor, tente novamente.", "app.components.Areas.unfollowedArea": "<PERSON><PERSON> que você deixou de seguir: {areaTitle}", "app.components.Areas.unfollowedTopic": "Tópico não seguido: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Preço:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.added": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.addVote": "Adicionar voto", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Você distribuiu todos os seus créditos.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Você distribuiu o número máximo de créditos para essa opção.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Você distribuiu todos os seus pontos.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Você distribuiu o número máximo de pontos para essa opção.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Você distribuiu todos os seus tokens.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Você distribuiu o número máximo de tokens para essa opção.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Você distribuiu todos os seus votos.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Você distribuiu o número máximo de votos para essa opção.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 off-line)} other {(incl. # off-line)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "A votação não está disponível, pois essa fase não está ativa.", "app.components.AssignMultipleVotesControl.removeVote": "Remover voto", "app.components.AssignMultipleVotesControl.select": "Selecione", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Você já enviou seu voto. Para modificá-lo, clique em \"Modificar seu envio\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Você já enviou seu voto. Para modificá-lo, volte à página do projeto e clique em \"Modify your submission\" (Modificar seu envio).", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {crédito} other {créditos}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {ponto} other {pontos}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {vote}}", "app.components.AssignVoteControl.maxVotesReached1": "Você distribuiu todos os seus votos.", "app.components.AssignVoteControl.phaseNotActive": "A votação não está disponível, pois essa fase não está ativa.", "app.components.AssignVoteControl.select": "Selecione", "app.components.AssignVoteControl.selected2": "Selecionado", "app.components.AssignVoteControl.voteForAtLeastOne": "Votar em pelo menos 1 opção", "app.components.AssignVoteControl.votesSubmitted1": "Você já enviou seu voto. Para modificá-lo, clique em \"Modificar seu envio\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Você já enviou seu voto. Para modificá-lo, volte à página do projeto e clique em \"Modify your submission\" (Modificar seu envio).", "app.components.AuthProviders.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "Continuar com {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Continuar com o Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Continuar com o SSO falso", "app.components.AuthProviders.continueWithGoogle": "Continuar com o Google", "app.components.AuthProviders.continueWithHoplr": "Continuar com Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Continuar com a ID Áustria", "app.components.AuthProviders.continueWithLoginMechanism": "Continuar com {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continuar com o MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Já existe uma conta com este endereço de correio electrónico.{br}{br}Não é possível acessar à plataforma utilizando o FranceConnect, uma vez que os dados pessoais não correspondem. Para entrar na plataforma utilizando FranceConnect, terá primeiro de mudar o seu nome ou apelido nesta plataforma para corresponder aos seus dados oficiais.{br}{br}Pode iniciar sessão como normalmente faz abaixo.", "app.components.AuthProviders.goToLogIn": "Já tem uma conta? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Não tem uma conta? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "<PERSON><PERSON><PERSON>", "app.components.AuthProviders.logInWithEmail": "Conectar-se com e-mail", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Para ser verificado, o utilizador deve ter a idade mínima especificada ou superior.", "app.components.AuthProviders.signUp2": "<PERSON>rie seu login", "app.components.AuthProviders.signUpButtonAltText": "Faça seu login com {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "<PERSON>rie seu login com o email", "app.components.AuthProviders.verificationRequired": "Verificação necessária", "app.components.Author.a11yPostedBy": "Postado por", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 participante} other {{numberOfParticipants} participantes}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} usu<PERSON><PERSON>s", "app.components.AvatarBubbles.participant": "participante", "app.components.AvatarBubbles.participants1": "participantes", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Não é possível comentar na fase atual.", "app.components.Comments.commentingDisabledInactiveProject": "Não é possível comentar porque esse projeto não está disponível no momento.", "app.components.Comments.commentingDisabledProject": "Os comentários neste projeto estão atualmente desativados.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} para comentar.", "app.components.Comments.commentingMaybeNotPermitted": "Por favor {signInLink} para ver que ações você pode tomar.", "app.components.Comments.inputsAssociatedWithProfile": "<PERSON><PERSON> pad<PERSON><PERSON>, seus envios serão associados ao seu perfil, a menos que você selecione esta opção.", "app.components.Comments.invisibleTitleComments": "Comentários", "app.components.Comments.leastRecent": "Menos recente", "app.components.Comments.likeComment": "Curtir este comentário", "app.components.Comments.mostLiked": "<PERSON><PERSON>", "app.components.Comments.mostRecent": "<PERSON><PERSON>e", "app.components.Comments.official": "Oficial", "app.components.Comments.postAnonymously": "Publicar anonimamente", "app.components.Comments.replyToComment": "Responder ao coment<PERSON>rio", "app.components.Comments.reportAsSpam": "Denunciar como spam", "app.components.Comments.seeOriginal": "Ver original", "app.components.Comments.seeTranslation": "Ver tradução", "app.components.Comments.yourComment": "O seu comentário", "app.components.CommonGroundResults.divisiveDescription": "Declarações em que as pessoas concordam e discordam igualmente:", "app.components.CommonGroundResults.divisiveTitle": "Divisivo", "app.components.CommonGroundResults.majorityDescription": "Mais de 60% votaram de uma forma ou de outra nas seguintes questões:", "app.components.CommonGroundResults.majorityTitle": "Maioria", "app.components.CommonGroundResults.participantLabel": "participante", "app.components.CommonGroundResults.participantsLabel1": "participantes", "app.components.CommonGroundResults.statementLabel": "declaração", "app.components.CommonGroundResults.statementsLabel1": "declaraç<PERSON><PERSON>", "app.components.CommonGroundResults.votesLabe": "voto", "app.components.CommonGroundResults.votesLabel1": "votos", "app.components.CommonGroundStatements.agreeLabel": "Concordar", "app.components.CommonGroundStatements.disagreeLabel": "Não concordo", "app.components.CommonGroundStatements.noMoreStatements": "Não há declarações a serem respondidas no momento", "app.components.CommonGroundStatements.noResults": "Ainda não há resultados para mostrar. Certifique-se de que você tenha participado da fase Common Ground e volte aqui depois.", "app.components.CommonGroundStatements.unsureLabel": "Não tenho certeza", "app.components.CommonGroundTabs.resultsTabLabel": "Resul<PERSON><PERSON>", "app.components.CommonGroundTabs.statementsTabLabel": "Declaraç<PERSON><PERSON>", "app.components.CommunityMonitorModal.formError": "Você encontrou um erro.", "app.components.CommunityMonitorModal.surveyDescription2": "Essa pesquisa contínua rastreia como você se sente em relação à governança e aos serviços públicos.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Leva <1 minuto} one {Leva 1 minuto} other {Leva # minutos}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Um e-mail com um código de confirmação foi enviado para {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "<PERSON><PERSON> seu e-mail.", "app.components.ConfirmationModal.codeInput": "Código", "app.components.ConfirmationModal.confirmationCodeSent": "Novo código enviado", "app.components.ConfirmationModal.didntGetAnEmail": "Não recebeu um e-mail?", "app.components.ConfirmationModal.foundYourCode": "Encontrou o código?", "app.components.ConfirmationModal.goBack": "Voltar.", "app.components.ConfirmationModal.sendEmailWithCode": "Enviar e-mail com código", "app.components.ConfirmationModal.sendNewCode": "Enviar novo código.", "app.components.ConfirmationModal.verifyAndContinue": "Verificar e Continuar", "app.components.ConfirmationModal.wrongEmail": "E-mail errado?", "app.components.ConsentManager.Banner.accept": "Aceite", "app.components.ConsentManager.Banner.ariaButtonClose2": "Rejeitar a política e fechar a bandeira", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "<PERSON><PERSON>, você concorda com nossas {policyLink}.", "app.components.ConsentManager.Banner.manage": "Gerenciar   ", "app.components.ConsentManager.Banner.policyLink": "Política de Cookies", "app.components.ConsentManager.Banner.reject": "Recusar", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Publicidade", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Usamos isto para personalizar e medir a eficácia das campanhas publicitárias do nosso site. Não mostraremos qualquer publicidade nesta plataforma, mas os seguintes serviços podem oferecer-lhe um anúncio personalizado com base nas páginas que visitar no nosso site.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Permitido", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analíticos", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Nós usamos este rastreio para entender melhor como você usa a plataforma, a fim de aprender e melhorar sua navegação. Esta informação é usada apenas em análise de massa, e de forma alguma para rastrear pessoas individuais.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Voltar", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Cancelado", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Não permitido", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funcional", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Isto é necessário para habilitar e monitorar as funcionalidades básicas do site. Algumas ferramentas listadas podem não se aplicar a você. Por favor leia a nossa política de cookies para mais informações.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Gerenciar Google Tag ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Campo requerido", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Para ter uma plataforma funcional, nós salvamos uma autenticação de cookie no caso de você se inscrever, e a língua a qual você usa na plataforma", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "As suas preferências de cookies", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Ferramentas", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Isenção de responsabilidade sobre o upload de conteúdo", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Ao enviar conteúdo, você declara que esse conteúdo não viola quaisquer regulamentos ou direitos de terceiros, como direitos de propriedade intelectual, direitos de privacidade, direitos a segredos comerciais e assim por diante. Consequentemente, ao carregar este conteúdo, você se compromete a assumir total e exclusiva responsabilidade por todos os danos diretos e indiretos resultantes do conteúdo carregado. Além disso, você se compromete a indenizar o proprietário da plataforma e a Go Vocal contra quaisquer reclamações ou responsabilidades de terceiros contra terceiros, e quaisquer custos associados, que possam surgir ou resultar do conteúdo que você carregou.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Diga-nos por quê", "app.components.CustomFieldsForm.addressInputAriaLabel": "Entrada de endereço", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Digite um endereço...", "app.components.CustomFieldsForm.adminFieldTooltip": "Campo visível apenas para administradores", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "<PERSON><PERSON> as respostas a esta pesquisa são anônimas.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "São necessários pelo menos três pontos para um polígono.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON>elo menos dois pontos são necessários para uma linha.", "app.components.CustomFieldsForm.attachmentRequired": "É necessário pelo menos um anexo", "app.components.CustomFieldsForm.authorFieldLabel": "Autor", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Comece a digitar para pesquisar por nome ou e-mail do usuário...", "app.components.CustomFieldsForm.back": "Voltar", "app.components.CustomFieldsForm.budgetFieldLabel": "Orçamento", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Clique no mapa para desenhar. Em seguida, arraste os pontos para movê-los.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Clique no mapa ou digite um endereço abaixo para adicionar sua resposta.", "app.components.CustomFieldsForm.confirm": "Confirmar", "app.components.CustomFieldsForm.descriptionMinLength": "A descrição deve ter no mínimo {min} caracteres", "app.components.CustomFieldsForm.descriptionRequired": "A descrição é obrigatória", "app.components.CustomFieldsForm.fieldMaximumItems": "No máximo {maxSelections, plural, one {# opção} other {# opções}} podem ser selecionadas para o campo \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "Pelo menos {minSelections, plural, one {# opção} other {# opções}} podem ser selecionadas para o campo \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "O campo \"{fieldName}\" é obrigatório", "app.components.CustomFieldsForm.fileSizeLimit": "O limite de tamanho do arquivo é {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "A imagem é necessária", "app.components.CustomFieldsForm.minimumCoordinates2": "É necessário um mínimo de {numPoints} pontos no mapa.", "app.components.CustomFieldsForm.notPublic1": "*Essa resposta será compartilhada apenas com os gerentes de projeto, e não com o público.", "app.components.CustomFieldsForm.otherArea": "Em outro lugar", "app.components.CustomFieldsForm.progressBarLabel": "Progresso", "app.components.CustomFieldsForm.removeAnswer": "Remover resposta", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Selecione quantos você quiser", "app.components.CustomFieldsForm.selectBetween": "*Selecione entre as opções {minItems} e {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*Selecione exatamente {selectExactly, plural, one {# opção} other {# opções}}", "app.components.CustomFieldsForm.selectMany": "*Escolha quantos você quiser", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Toque no mapa para desenhar. Em seguida, arraste os pontos para movê-los.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Toque no mapa para desenhar.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Toque no mapa para adicionar sua resposta.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Toque no mapa ou digite um endereço abaixo para adicionar sua resposta.", "app.components.CustomFieldsForm.tapToAddALine": "Toque para adicionar uma linha", "app.components.CustomFieldsForm.tapToAddAPoint": "Toque para adicionar um ponto", "app.components.CustomFieldsForm.tapToAddAnArea": "Toque para adicionar uma área", "app.components.CustomFieldsForm.titleMaxLength": "O título deve ter no máximo {max} caracteres", "app.components.CustomFieldsForm.titleMinLength": "O título deve ter no mínimo {min} caracteres", "app.components.CustomFieldsForm.titleRequired": "O título é obrigatório", "app.components.CustomFieldsForm.topicRequired": "<PERSON>elo menos uma tag é necessária", "app.components.CustomFieldsForm.typeYourAnswer": "Digite sua resposta", "app.components.CustomFieldsForm.typeYourAnswerRequired": "É necessário que você digite sua resposta", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Carregue um arquivo zip contendo um ou mais shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Se o local não for exibido entre as opções à medida que você digita, é possível adicionar coordenadas válidas no formato \"latitude, longitude\" para especificar um local preciso (por exemplo: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Alguns campos são inválidos. Por favor, corrija os erros e tente novamente.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Ocorreu um erro desconhecido durante o envio do seu relatório. Por favor, tente novamente.", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "Que aconteceu?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-mail", "app.components.ErrorBoundary.errorFormLabelName": "Nome  ", "app.components.ErrorBoundary.errorFormLabelSubmit": "Enviar", "app.components.ErrorBoundary.errorFormSubtitle": "A nossa equipe foi informada", "app.components.ErrorBoundary.errorFormSubtitle2": "Se você quiser que ajudemos, diga-nos abaixo o que aconteceu.", "app.components.ErrorBoundary.errorFormSuccessMessage": "A sua opinião foi enviada. Obrigado!", "app.components.ErrorBoundary.errorFormTitle": "Parece que ocorreu um problema.", "app.components.ErrorBoundary.genericErrorWithForm": "Ocorreu um erro e não podemos exibir esse conteúdo. Por favor tente novamente, ou {openForm}", "app.components.ErrorBoundary.openFormText": "ajude-nos a descobrir", "app.components.ErrorToast.budgetExceededError": "Não tem orçamento suficiente", "app.components.ErrorToast.votesExceededError": "Você não tem mais votos suficientes", "app.components.EventAttendanceButton.forwardToFriend": "Encaminhe para um amigo", "app.components.EventAttendanceButton.maxRegistrationsReached": "O número máximo de inscrições no evento foi atingido. Não há mais vagas disponíveis.", "app.components.EventAttendanceButton.register": "Registro", "app.components.EventAttendanceButton.registered": "Registrado", "app.components.EventAttendanceButton.seeYouThere": "Vejo você lá!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON><PERSON><PERSON> você lá, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Menos informações de eventos tornaram-se visíveis.", "app.components.EventCard.a11y_moreContentVisible": "Mais informações de eventos visíveis.", "app.components.EventCard.a11y_readMore": "<PERSON>ia mais sobre o evento \"{eventTitle}\".", "app.components.EventCard.endsAt": "Termina em", "app.components.EventCard.readMore": "Ler mais", "app.components.EventCard.showLess": "Mostrar menos ideias", "app.components.EventCard.showMore": "<PERSON><PERSON> mais", "app.components.EventCard.startsAt": "Inicia em", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Eventos futuros e em andamento neste projeto", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Eventos futuros e em andamento nesta fase", "app.components.FileUploader.a11y_file": "Arquivo:", "app.components.FileUploader.a11y_filesToBeUploaded": "Arqui<PERSON> a serem carregados: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Nenhum arquivo adicionado.", "app.components.FileUploader.a11y_removeFile": "Remover este arquivo", "app.components.FileUploader.fileInputDescription": "Clique para selecionar um arquivo", "app.components.FileUploader.fileUploadLabel": "Anexos (max. 50MB)", "app.components.FileUploader.file_too_large2": "Não são permitidos arquivos maiores que {maxSizeMb}MB.", "app.components.FileUploader.incorrect_extension": "{fileName} não é suportado pelo nosso sistema, ele não será carregado.", "app.components.FilterBoxes.a11y_allFilterSelected": "Filtro de status selecionado: todos", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, no {# entradas} one {# entrada} other {# entradas}}", "app.components.FilterBoxes.a11y_removeFilter": "Remover filtro", "app.components.FilterBoxes.a11y_selectedFilter": "Filtro de status selecionado: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Selecionado {numberOfSelectedTopics, plural, =0 {zero filtros tópicos} one {um filtro tópico} other {# filtros de tópico}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "<PERSON><PERSON>", "app.components.FilterBoxes.areas": "Filtrar por área", "app.components.FilterBoxes.inputs": "insumos", "app.components.FilterBoxes.noValuesFound": "Não há valores disponíveis.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON> menos", "app.components.FilterBoxes.showTagsWithNumber": "Mostrar tudo ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Tópicos", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Descrição da pasta:", "app.components.FolderFolderCard.a11y_folderTitle": "<PERSON><PERSON><PERSON><PERSON> da pasta:", "app.components.FolderFolderCard.archived": "Arquivado", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projetos} one {# projeto} other {# projetos}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "O tipo de campo não pode ser alterado depois que houver envios.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tipo", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Salvamento automático", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "O salvamento automático é ativado por padrão quando você abre o editor de formulários. Sempre que você fechar o painel de configurações de campo usando o botão \"X\", ele acionará automaticamente um salvamento.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Trimestre", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Plurianual", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "Hoje", "app.components.GoBackButton.group.edit.goBack": "Voltar", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Voltar à página anterior", "app.components.HookForm.Feedback.errorTitle": "<PERSON><PERSON> um problema", "app.components.HookForm.Feedback.submissionError": "Tente novamente. Se o problema persistir, contate-nos", "app.components.HookForm.Feedback.submissionErrorTitle": "Havia um problema por aqui, desculpe", "app.components.HookForm.Feedback.successMessage": "Formulário enviado com sucesso", "app.components.HookForm.PasswordInput.passwordLabel": "<PERSON><PERSON>", "app.components.HorizontalScroll.scrollLeftLabel": "Role para a esquerda.", "app.components.HorizontalScroll.scrollRightLabel": "Role para a direita.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideias foram carregadas.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "<PERSON><PERSON> discutido", "app.components.IdeaCards.filters.newest": "Novo", "app.components.IdeaCards.filters.oldest": "Antiga", "app.components.IdeaCards.filters.popular": "<PERSON><PERSON> curtidas", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortBy": "Ordenar por", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "A classificação foi alterada para: {currentSortType}", "app.components.IdeaCards.filters.trending": "Tendências", "app.components.IdeaCards.showMore": "<PERSON><PERSON> mais", "app.components.IdeasMap.a11y_hideIdeaCard": "Esconder o cartão de ideia.", "app.components.IdeasMap.a11y_mapTitle": "Visão geral do mapa", "app.components.IdeasMap.clickOnMapToAdd": "Clique no mapa para adicionar a sua contribuição", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Como administrador, você pode clicar no mapa para adicionar sua opinião, mesmo que essa fase não esteja ativa.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "Várias entradas neste local", "app.components.IdeasMap.noFilteredResults": "Os filtros que você selecionou não retornaram nenhum resultado", "app.components.IdeasMap.noResults": "Nenhum resultado encontrado", "app.components.IdeasMap.or": "ou", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, você não gostou.} one {1 não gostei.} other {, # não gostei.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, sem curtidas.} one {, 1 like.} other {, # curtidas.}}", "app.components.IdeasMap.signInLinkText": "conecte-se", "app.components.IdeasMap.signUpLinkText": "cadastrar-se", "app.components.IdeasMap.submitIdea2": "Enviar entrada", "app.components.IdeasMap.tapOnMapToAdd": "Clique no mapa para adicionar a sua contribuição", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Como administrador, você pode tocar no mapa para adicionar sua opinião, mesmo que essa fase não esteja ativa.", "app.components.IdeasMap.userInputs2": "Entradas dos participantes", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, sem comentários} one {, 1 comentário} other {, # comentários}}", "app.components.IdeasShow.bodyTitle": "Descrição", "app.components.IdeasShow.deletePost": "Deletar", "app.components.IdeasShow.editPost": "<PERSON><PERSON>", "app.components.IdeasShow.goBack": "Voltar", "app.components.IdeasShow.moreOptions": "<PERSON><PERSON>", "app.components.IdeasShow.or": "ou", "app.components.IdeasShow.proposedBudgetTitle": "Orçamento proposto", "app.components.IdeasShow.reportAsSpam": "Denunciar como spam", "app.components.IdeasShow.send": "enviar   ", "app.components.IdeasShow.skipSharing": "eu faço-o mais tarde.", "app.components.IdeasShowPage.signIn2": "<PERSON><PERSON><PERSON>", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON>, não pode acessar esta página. Você precisa fazer login ou se inscrever para acessá-la.", "app.components.LocationInput.noOptions": "Sem opções", "app.components.Modal.closeWindow": "<PERSON><PERSON><PERSON> a janela", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Introduzir um novo endereço de correio electrónico", "app.components.PageNotFound.goBackToHomePage": "Voltar para a página inicial", "app.components.PageNotFound.notFoundTitle": "Página não encontrada", "app.components.PageNotFound.pageNotFoundDescription": "A página solicitada não pôde ser encontrada.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Por favor, forneça o conteúdo pelo menos em um idioma", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "Anexos (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Os arquivos adicionados serão mostrados na parte inferior desta página", "app.components.PagesForm.navbarItemTitle": "Nome na barra de navegação", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "<PERSON><PERSON>", "app.components.PagesForm.saveSuccess": "Página salva com sucesso.", "app.components.PagesForm.titleMissingOneLanguageError": "Fornecer título para pelo menos uma língua", "app.components.Pagination.back": "Página anterior", "app.components.Pagination.next": "Próxima página", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Voc<PERSON> gastou {votesCast}, que excede o limite de {votesLimit}. Por favor, remova alguns itens da sua cesta e tente novamente.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} esquerda", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Você precisa gastar um mínimo de {votesMinimum} para poder enviar sua cesta.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Você precisa selecionar pelo menos uma opção antes de enviar.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Você precisa adicionar algo à sua cesta antes de poder enviá-la.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Nenhum crédito restante} other {# out of {totalNumberOfVotes, plural, one {1 crédito} other {# credits}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Nenhum ponto restante} other {# out of {totalNumberOfVotes, plural, one {1 ponto} other {# points}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Não há tokens restantes} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Nenhum voto restante} other {# out of {totalNumberOfVotes, plural, one {1 voto} other {# votes}} left}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# Voto} one {# vote} other {# votos}} lan<PERSON><PERSON>", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Você deu {votesCast} votos, o que excede o limite de {votesLimit}. Remova alguns votos e tente novamente.", "app.components.ParticipationCTABars.addInput": "Adicionar entrada", "app.components.ParticipationCTABars.allocateBudget": "Distribua o seu orçamento", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Seu orçamento foi enviado com sucesso.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Aberto à participação", "app.components.ParticipationCTABars.poll": "Faça a sondagem", "app.components.ParticipationCTABars.reviewDocument": "Revisar o documento", "app.components.ParticipationCTABars.seeContributions": "<PERSON><PERSON><PERSON> as contribui<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeEvents3": "Ver eventos", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON> ideias", "app.components.ParticipationCTABars.seeInitiatives": "<PERSON>er iniciativas", "app.components.ParticipationCTABars.seeIssues": "Ver problemas", "app.components.ParticipationCTABars.seeOptions": "Ver opções", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON> <PERSON>", "app.components.ParticipationCTABars.seeProjects": "Ver projetos", "app.components.ParticipationCTABars.seeProposals": "Ver propostas", "app.components.ParticipationCTABars.seeQuestions": "<PERSON>er pergun<PERSON>", "app.components.ParticipationCTABars.submit": "Enviar", "app.components.ParticipationCTABars.takeTheSurvey": "Faça a pesquisa", "app.components.ParticipationCTABars.userHasParticipated": "Participou neste projeto.", "app.components.ParticipationCTABars.viewInputs": "<PERSON><PERSON><PERSON> entra<PERSON>", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "voto", "app.components.ParticipationCTABars.votesCounter.votes": "votos", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON><PERSON> oculta", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON> vis<PERSON>", "app.components.PasswordInput.a11y_strength1Password": "Força da senha baixa", "app.components.PasswordInput.a11y_strength2Password": "Força da senha fraca", "app.components.PasswordInput.a11y_strength3Password": "Força da senha média", "app.components.PasswordInput.a11y_strength4Password": "Força da senha forte", "app.components.PasswordInput.a11y_strength5Password": "Força da senha muito forte", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON> curto (mín. {minimumPasswordLength} caracteres)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON>rie uma senha com, pelo menos, {minimumPasswordLength} caracteres", "app.components.PasswordInput.passwordEmptyError": "Insira a sua senha", "app.components.PasswordInput.passwordStrengthTooltip1": "Para tornar sua senha mais forte:", "app.components.PasswordInput.passwordStrengthTooltip2": "Utilize uma combinação de letras minúsculas não consecutivas, letras maiúsculas, dígitos, caracteres especiais e pontuação", "app.components.PasswordInput.passwordStrengthTooltip3": "<PERSON><PERSON><PERSON> pala<PERSON>ras comuns ou fáceis de adiv<PERSON>", "app.components.PasswordInput.passwordStrengthTooltip4": "Aumente o tamanho", "app.components.PasswordInput.showPassword": "<PERSON><PERSON> senha", "app.components.PasswordInput.strength1Password": "Baixa", "app.components.PasswordInput.strength2Password": "Fraca", "app.components.PasswordInput.strength3Password": "Média", "app.components.PasswordInput.strength4Password": "Forte", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON> forte", "app.components.PostCardsComponents.list": "Listagem", "app.components.PostCardsComponents.map": "Mapa", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Adicionar uma atualização oficial", "app.components.PostComponents.OfficialFeedback.cancel": "Cancelado", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Deletar", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Tem certeza que deseja apagar essa atualização oficial?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Última edição em {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Última atualização: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Oficial", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> como as pessoas vêem o seu nome", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Nome do autor da atualização oficial", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Texto da atualização oficial", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Atualizações oficiais", "app.components.PostComponents.OfficialFeedback.postedOn": "Publicado em {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publique", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Mostrar atualizações anteriores", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Faça uma atualização …", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON>, ocurreu um erro", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Mensagem de atualização", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Sua atualização foi publicada com sucesso!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "A<PERSON>ie minha contribuição '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON> minha contribuição: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> minha contribuição: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Apoie minha ideia '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "A<PERSON><PERSON> a minha ideia: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "A<PERSON><PERSON> a minha ideia: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "O que você acha desta idéia? Vote nela e compartilhe a discussão em  {postUrl} para fazer ouvir a sua voz!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Apoie a minha proposta: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON> minha iniciativa: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Publiquei um problema '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Acabei de publicar um problema: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Acabei de publicar um problema: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "A<PERSON>ie minha opção proposta '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "<PERSON><PERSON><PERSON> minha opção proposta: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON><PERSON>ha opção: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "A<PERSON>ie minha petição '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON> minha peti<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON> minha peti<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Apoie meu projeto '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Apoie meu projeto: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Apoie meu projeto: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Apoie minha proposta '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON> minha proposta: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Acabei de publicar uma proposta para {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Participe da discussão sobre essa pergunta '{postTitle}' em {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Participe da <PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Participe da <PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Votar por {postTitle} em", "app.components.PostComponents.linkToHomePage": "Link para a página inicial", "app.components.PostComponents.readMore": "Ler mais...", "app.components.PostComponents.topics": "Tópicos", "app.components.ProjectArchivedIndicator.archivedProject": "Infelizmente, você não pode mais participar deste projeto porque ele foi arquivado.", "app.components.ProjectArchivedIndicator.previewProject": "Projeto preliminar:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visível apenas para moderadores e para aqueles com um link de visualização.", "app.components.ProjectCard.a11y_projectDescription": "Descrição do projeto:", "app.components.ProjectCard.a11y_projectTitle": "Título do projeto:", "app.components.ProjectCard.addYourOption": "Adicionar sua opção", "app.components.ProjectCard.allocateYourBudget": "Distribua o seu orçamento", "app.components.ProjectCard.archived": "Arquivado", "app.components.ProjectCard.comment": "Comentários", "app.components.ProjectCard.contributeYourInput": "Contribua com sua entrada", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Jun<PERSON>-<PERSON> <PERSON>", "app.components.ProjectCard.learnMore": "<PERSON><PERSON> mais", "app.components.ProjectCard.reaction": "Reação", "app.components.ProjectCard.readTheReport": "Leia o relatório", "app.components.ProjectCard.reviewDocument": "Revisar o documento", "app.components.ProjectCard.submitAnIssue": "Envie um problema", "app.components.ProjectCard.submitYourIdea": "Envie sua ideia", "app.components.ProjectCard.submitYourInitiative": "Envie sua iniciativa", "app.components.ProjectCard.submitYourPetition": "Envie sua petição", "app.components.ProjectCard.submitYourProject": "Enviar seu projeto", "app.components.ProjectCard.submitYourProposal": "Envie sua proposta", "app.components.ProjectCard.takeThePoll": "Faça a pesquisa", "app.components.ProjectCard.takeTheSurvey": "Faça a pesquisa", "app.components.ProjectCard.viewTheContributions": "<PERSON><PERSON><PERSON> as contribui<PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON><PERSON> as idei<PERSON>", "app.components.ProjectCard.viewTheInitiatives": "<PERSON><PERSON><PERSON> as iniciativas", "app.components.ProjectCard.viewTheIssues": "<PERSON><PERSON>a os <PERSON>as", "app.components.ProjectCard.viewTheOptions": "<PERSON><PERSON><PERSON> as opçõ<PERSON>", "app.components.ProjectCard.viewThePetitions": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "Veja os projetos", "app.components.ProjectCard.viewTheProposals": "<PERSON><PERSON><PERSON> as propostas", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON><PERSON> as pergun<PERSON>", "app.components.ProjectCard.vote": "Votação", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# comentários} other {# comentários}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, no {# contributions} one {# contribution} other {# contributions}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {ainda sem ideias} one {# ideia} other {# ideias}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# iniciativas} one {# iniciativa} other {# iniciativas}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, no {#problemas} one {# problema} other {# problemas}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, no {# opções} one {# opção} other {# opções}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petições} one {# petição} other {# petições}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, não {# projetos} um {# projeto} other {# projetos}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# propostas} one {# proposta} other {# propostas}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, no {# perguntas} one {# pergunta} other {# perguntas}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# comentários} one {# Comentários} other {# comentários}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, não {# projetos} um {# projeto} other {# projetos}}", "app.components.ProjectFolderCards.components.Topbar.all": "<PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.archived": "Arquivado", "app.components.ProjectFolderCards.components.Topbar.draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtrar por", "app.components.ProjectFolderCards.components.Topbar.published2": "Publicado", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tópicos", "app.components.ProjectFolderCards.noProjectYet": "Não há nenhum projeto", "app.components.ProjectFolderCards.noProjectsAvailable": "Não há projetos disponíveis", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON> mais", "app.components.ProjectFolderCards.stayTuned": "Fique atento, um projeto vai aparecer muito em breve.", "app.components.ProjectFolderCards.tryChangingFilters": "Tente mudar os filtros selecionados.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Também é utilizado nestas cidades", "app.components.ProjectTemplatePreview.copied": "Copiado", "app.components.ProjectTemplatePreview.copyLink": "Copiar link", "app.components.QuillEditor.alignCenter": "centralizar o texto", "app.components.QuillEditor.alignLeft": "Alinhar à esquerda", "app.components.QuillEditor.alignRight": "Alinhar à direita", "app.components.QuillEditor.bold": "Negrita", "app.components.QuillEditor.clean": "Remover formatação", "app.components.QuillEditor.customLink": "<PERSON><PERSON><PERSON><PERSON> bot<PERSON>", "app.components.QuillEditor.customLinkPrompt": "Entrar link:", "app.components.QuillEditor.edit": "<PERSON><PERSON>", "app.components.QuillEditor.image": "<PERSON>eg<PERSON> imagem", "app.components.QuillEditor.imageAltPlaceholder": "Breve descrição da imagem", "app.components.QuillEditor.italic": "Itálico", "app.components.QuillEditor.link": "Adicionar link", "app.components.QuillEditor.linkPrompt": "Entrar link:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Listagem de pedidos", "app.components.QuillEditor.remove": "Remover", "app.components.QuillEditor.save": "<PERSON><PERSON>", "app.components.QuillEditor.subtitle": "Subtítulo", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Lista não-ordenada", "app.components.QuillEditor.video": "Adicionar vídeo", "app.components.QuillEditor.videoPrompt": "Insira o vídeo:", "app.components.QuillEditor.visitPrompt": "Visite o link:", "app.components.ReactionControl.completeProfileToReact": "Complete seu perfil para reagir", "app.components.ReactionControl.dislike": "Não curtir", "app.components.ReactionControl.dislikingDisabledMaxReached": "Você atingiu o número máximo de não gostei em {projectName}", "app.components.ReactionControl.like": "Curtir", "app.components.ReactionControl.likingDisabledMaxReached": "Atingiu o número máximo de gostei em {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "As reações serão ativadas assim que esta fase começar", "app.components.ReactionControl.reactingDisabledPhaseOver": "Já não é possível reagir nesta fase", "app.components.ReactionControl.reactingDisabledProjectInactive": "Já não é possível reagir a ideias em {projectName}", "app.components.ReactionControl.reactingNotEnabled": "A reação não está ativada para este projeto no momento", "app.components.ReactionControl.reactingNotPermitted": "A reação só é ativada para determinados grupos", "app.components.ReactionControl.reactingNotSignedIn": "Faça login para votar.", "app.components.ReactionControl.reactingPossibleLater": "A reação terá início em {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verifique sua identidade para reagir.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Data do evento: {startDate} em {startTime} a {endDate} em {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Data do evento: {eventDate} de {startTime} a {endTime}.", "app.components.Sharing.linkCopied": "<PERSON> copiado", "app.components.Sharing.or": "ou", "app.components.Sharing.share": "Compartilhar", "app.components.Sharing.shareByEmail": "Compartilhe por e-mail", "app.components.Sharing.shareByLink": "Copiar link", "app.components.Sharing.shareOnFacebook": "Compartilhe no Facebook", "app.components.Sharing.shareOnTwitter": "Compartilhe no Twitter", "app.components.Sharing.shareThisEvent": "Compartilhar este evento", "app.components.Sharing.shareThisFolder": "Compartilhar", "app.components.Sharing.shareThisProject": "Compartilhe este projeto", "app.components.Sharing.shareViaMessenger": "Compart<PERSON><PERSON> via <PERSON>", "app.components.Sharing.shareViaWhatsApp": "Compartilhe via WhatsApp", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON><PERSON>", "app.components.StatusModule.futurePhase": "Você está visualizando uma fase que ainda não começou. Você poderá participar quando a fase começar.", "app.components.StatusModule.modifyYourSubmission1": "Modificar seu envio", "app.components.StatusModule.submittedUntil3": "Seu voto pode ser enviado até", "app.components.TopicsPicker.numberOfSelectedTopics": "Selecionado {numberOfSelectedTopics, plural, =0 {zero tópicos} one {um tópico} other {# tópicos}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Expandir imagem", "app.components.UI.MoreActionsMenu.moreOptions": "<PERSON><PERSON>", "app.components.UI.MoreActionsMenu.showMoreActions": "Mostrar mais ac<PERSON>", "app.components.UI.PhaseFilter.noAppropriatePhases": "Não foram encontradas fases apropriadas para este projeto", "app.components.UI.RemoveImageButton.a11y_removeImage": "Remover", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Traduzir", "app.components.Unauthorized.additionalInformationRequired": "Informações adicionais são necessárias para que você possa participar.", "app.components.Unauthorized.completeProfile": "<PERSON><PERSON>l completo", "app.components.Unauthorized.completeProfileTitle": "Complete seu perfil para participar", "app.components.Unauthorized.noPermission": "Não tem permissão para ver esta página", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON>, não está autorizado a aceder a esta página.", "app.components.Upload.errorImageMaxSizeExceeded": "A imagem seleccionada é maior que {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Uma ou várias imagens selecionadas são maiores que {maxFileSize}MB", "app.components.Upload.onlyOneImage": "Você só pode carregar 1 imagem", "app.components.Upload.onlyXImages": "Você só pode carregar {maxItemsCount} imagens", "app.components.Upload.remaining": "restante", "app.components.Upload.uploadImageLabel": "Selecione uma imagem (máx. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Selecione uma ou mais imagens", "app.components.UpsellTooltip.tooltipContent": "Esse recurso não está incluído em seu plano atual. Fale com seu gerente de sucesso do governo ou administrador para desbloqueá-lo.", "app.components.UserName.anonymous": "<PERSON><PERSON><PERSON>", "app.components.UserName.anonymousTooltip2": "Este usuário decidiu tornar a sua contribuição anônima", "app.components.UserName.authorWithNoNameTooltip": "Seu nome foi gerado automaticamente porque você não inseriu seu nome. Atualize seu perfil se você quiser alterá-lo.", "app.components.UserName.deletedUser": "autor desconhe<PERSON>o", "app.components.UserName.verified": "Verificado", "app.components.VerificationModal.verifyAuth0": "Verificar com o NemID", "app.components.VerificationModal.verifyBOSA": "Verificar com o seu nome ou seu usuário", "app.components.VerificationModal.verifyBosaFas": "Verificar com itsme ou eID", "app.components.VerificationModal.verifyClaveUnica": "Verificar com Chave Única", "app.components.VerificationModal.verifyFakeSSO": "Verificar com SSO falso", "app.components.VerificationModal.verifyIdAustria": "Verificar com o ID Áustria", "app.components.VerificationModal.verifyKeycloak": "Verificar com o ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verificar com MitID", "app.components.VerificationModal.verifyTwoday2": "Verificar com o BankID ou Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Verifique o seu usuário", "app.components.VoteControl.budgetingFutureEnabled": "Você pode alocar seu orçamento a partir de {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "O orçamento participativo só está habilitado para determinados grupos.", "app.components.VoteControl.budgetingNotPossible": "Não é possível fazer alterações em seu orçamento no momento.", "app.components.VoteControl.budgetingNotVerified": "{verifyAccountLink} para continuar.", "app.components.VoteInputs._shared.currencyLeft1": "Você tem {budgetLeft} / {totalBudget} restantes", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Você tem {votesLeft, plural, =0 {nenhum crédito restante} other {# out of {totalNumberOfVotes, plural, one {1 crédito} other {# credits}} left}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Você tem {votesLeft, plural, =0 {nenhum ponto restante} other {# fora de {totalNumberOfVotes, plural, one {1 ponto} other {# pontos}} restantes}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Você tem {votesLeft, plural, =0 {nenhum token restante} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Você tem {votesLeft, plural, =0 {nenhum voto restante} other {# fora de {totalNumberOfVotes, plural, one {1 voto} other {# votos}} restantes}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Você já enviou seu orçamento. Para modificá-lo, clique em \"Modificar seu envio\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Você já enviou seu orçamento. Para modificá-lo, volte à página do projeto e clique em \"Modify your submission\" (Modificar seu envio).", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "O orçamento não está disponível, pois essa fase não está ativa.", "app.components.VoteInputs.single.youHaveVotedForX2": "Você votou em {votes, plural, =0 {#opções} one {#opção} other {#opções}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "Isto significa que perderá todos os dados associados a esta entrada, como comentários, reacções e votos. Esta ação não pode ser anulada.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Tem a certeza de que pretende apagar esta entrada?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Confirmar", "app.components.admin.SlugInput.resultingURL": "URL resultante", "app.components.admin.SlugInput.slugTooltip": "A lesma é o conjunto único de palavras no final do endereço web, ou URL da página.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Se você mudar o URL, os links para esta página usando o antigo URL não funcionarão mais.", "app.components.admin.SlugInput.urlSlugLabel": "<PERSON><PERSON><PERSON><PERSON> da última parte da url", "app.components.admin.UserFilterConditions.addCondition": "Adicione uma condição", "app.components.admin.UserFilterConditions.field_email": "E-mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Registos de eventos", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON> <PERSON>", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Pesquisa do monitor comunitário", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interagiu com uma entrada com status", "app.components.admin.UserFilterConditions.field_participated_in_project": "Contribuiu para o projeto", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Contribuiu para o tópico", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Data de registro", "app.components.admin.UserFilterConditions.field_role": "Função", "app.components.admin.UserFilterConditions.field_verified": "Verificação", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideação", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Propostas", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "não está inscrito em nenhum destes eventos", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "não está inscrito em nenhum evento", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "está inscrito num dos seguintes eventos", "app.components.admin.UserFilterConditions.predicate_attends_something": "está inscrito em pelo menos um evento", "app.components.admin.UserFilterConditions.predicate_begins_with": "começa com", "app.components.admin.UserFilterConditions.predicate_commented_in": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_contains": "contem", "app.components.admin.UserFilterConditions.predicate_ends_on": "termina em", "app.components.admin.UserFilterConditions.predicate_has_value": "tem como valor", "app.components.admin.UserFilterConditions.predicate_in": "executou qualquer ação", "app.components.admin.UserFilterConditions.predicate_is": "é", "app.components.admin.UserFilterConditions.predicate_is_admin": "é um administrador", "app.components.admin.UserFilterConditions.predicate_is_after": "é depois de", "app.components.admin.UserFilterConditions.predicate_is_before": "é antes de", "app.components.admin.UserFilterConditions.predicate_is_checked": "está checado", "app.components.admin.UserFilterConditions.predicate_is_empty": "está vazia", "app.components.admin.UserFilterConditions.predicate_is_equal": "é", "app.components.admin.UserFilterConditions.predicate_is_exactly": "é exatamente", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "é maior que", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "é maior ou igual a", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "é um usuário normal", "app.components.admin.UserFilterConditions.predicate_is_not_area": "exclui a área", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "excluir pasta", "app.components.admin.UserFilterConditions.predicate_is_not_input": "exclui a entrada", "app.components.admin.UserFilterConditions.predicate_is_not_project": "excluir o projeto", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "exclui o tópico", "app.components.admin.UserFilterConditions.predicate_is_one_of": "é um dos", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "uma das área<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "uma das pastas", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "uma das entradas", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "um dos projetos", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "um dos tópicos", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "é um gerente de projeto", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "é menor que", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "é menor ou igual a", "app.components.admin.UserFilterConditions.predicate_is_verified": "está verificado", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "não começa com", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "não comentou", "app.components.admin.UserFilterConditions.predicate_not_contains": "não contém", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "não termina em", "app.components.admin.UserFilterConditions.predicate_not_has_value": "não tem como valor", "app.components.admin.UserFilterConditions.predicate_not_in": "não contribuiu", "app.components.admin.UserFilterConditions.predicate_not_is": "não é", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "não é o administrador", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "não foi checado", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "não está vazio", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "não é", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "não é um usuário normal", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "não é um de", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "não é gerente de projeto", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "não é verificado", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "não publicou uma entrada", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "não reagiu ao comentário", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "não reagiu à entrada", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "não se inscreveu em um evento", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "não respondeu à pesquisa", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "não se ofereceu como voluntário", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "não participou na votação", "app.components.admin.UserFilterConditions.predicate_nothing": "nada", "app.components.admin.UserFilterConditions.predicate_posted_input": "publicou uma entrada", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reagiu ao comentário", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reagiu à entrada", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "registrado em um evento", "app.components.admin.UserFilterConditions.predicate_something": "algo", "app.components.admin.UserFilterConditions.predicate_taken_survey": "fez a pesquisa", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "voluntariou-se", "app.components.admin.UserFilterConditions.predicate_voted_in3": "participou na votação", "app.components.admin.UserFilterConditions.rulesFormLabelField": "A", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "B", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "C", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Não receberá notificações sobre a sua contribuição", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "Participar anonimamente", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "Isso ocultará com segurança <b>o seu perfil</b> dos administradores, gerentes de projeto e outros residentes para essa contribuição específica, para que ninguém possa vincular essa contribuição a você. As contribuições anônimas não podem ser editadas e são consideradas finais.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Tornar nossa plataforma segura para todos os usuários é uma prioridade para nós. Palavras importam, então, por favor, sejam gentis uns com os outros.", "app.components.avatar.titleForAccessibility": "Perfil do site {fullName}", "app.components.customFields.mapInput.removeAnswer": "Remover resposta", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "Desfazer o último ponto", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "Acompanhe a discussão", "app.components.followUnfollow.followTooltipInputPage2": "Você receberá atualizações por e-mail sobre alterações de status, atualizações oficiais e comentários. Você pode acessar {unsubscribeLink} a qualquer momento.", "app.components.followUnfollow.followTooltipProjects2": "Você receberá atualizações por e-mail sobre alterações no projeto. Você pode acessar {unsubscribeLink} a qualquer momento.", "app.components.followUnfollow.unFollow": "<PERSON><PERSON><PERSON>", "app.components.followUnfollow.unsubscribe": "cancelar a assinatura", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "nossas diretrizes", "app.components.form.ErrorDisplay.next": "Próximo", "app.components.form.ErrorDisplay.previous": "Anterior", "app.components.form.ErrorDisplay.save": "Guardar", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Comece a digitar para pesquisar por e-mail ou nome do usuário...", "app.components.form.anonymousSurveyMessage2": "<PERSON><PERSON> as respostas a esta pesquisa são anônimas.", "app.components.form.backToInputManager": "Voltar ao gerenciador de entradas", "app.components.form.backToProject": "Voltar ao projeto", "app.components.form.components.controls.mapInput.removeAnswer": "Remover resposta", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "Desfazer o último ponto", "app.components.form.controls.addressInputAriaLabel": "Entrada de endereço", "app.components.form.controls.addressInputPlaceholder6": "Digite um endereço...", "app.components.form.controls.adminFieldTooltip": "Somente os administradores podem ver", "app.components.form.controls.allStatementsError": "Você deve selecionar uma resposta para todas as afirmações.", "app.components.form.controls.back": "Voltar", "app.components.form.controls.clearAll": "<PERSON><PERSON> tudo", "app.components.form.controls.clearAllScreenreader": "<PERSON><PERSON> as respostas da pergunta da matriz acima", "app.components.form.controls.clickOnMapMultipleToAdd3": "Clique no mapa para desenhar. Em seguida, arraste os pontos para movê-los.", "app.components.form.controls.clickOnMapToAddOrType": "Clique no mapa ou digite um endereço abaixo para adicionar sua resposta.", "app.components.form.controls.confirm": "Confirmar", "app.components.form.controls.cosponsorsPlaceholder": "Comece a digitar um nome para pesquisar", "app.components.form.controls.currentRank": "Classificação atual:", "app.components.form.controls.minimumCoordinates2": "É necessário um mínimo de {numPoints} pontos no mapa.", "app.components.form.controls.noRankSelected": "Nenhuma classificação selecionada", "app.components.form.controls.notPublic1": "*Essa resposta será compartilhada apenas com os gerentes de projeto, e não com o público.", "app.components.form.controls.optionalParentheses": "(opcional)", "app.components.form.controls.rankingInstructions": "Arraste e solte para classificar as opções.", "app.components.form.controls.selectAsManyAsYouLike": "*Selecione quantos quiser", "app.components.form.controls.selectBetween": "*Selecionar entre as opções {minItems} e {maxItems}", "app.components.form.controls.selectExactly2": "*Selecione exatamente {selectExactly, plural, one {#opção} other {# opções}}", "app.components.form.controls.selectMany": "*Escolha quantos quiser", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Toque no mapa para desenhar. Em seguida, arraste os pontos para movê-los.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Toque no mapa para desenhar.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Toque no mapa para adicionar sua resposta.", "app.components.form.controls.tapOnMapToAddOrType": "Toque no mapa ou digite um endereço abaixo para adicionar sua resposta.", "app.components.form.controls.tapToAddALine": "Toque para adicionar uma linha", "app.components.form.controls.tapToAddAPoint": "Toque para adicionar um ponto", "app.components.form.controls.tapToAddAnArea": "Toque para adicionar uma área", "app.components.form.controls.uploadShapefileInstructions": "* Carregue um arquivo zip contendo um ou mais shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "Se o local não for exibido entre as opções à medida que você digita, é possível adicionar coordenadas válidas no formato \"latitude, longitude\" para especificar um local preciso (por exemplo: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} em {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} fora do site {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} de {total}, onde {maxValue} é {maxLabel}", "app.components.form.error": "Erro", "app.components.form.locationGoogleUnavailable": "Não foi possível carregar o campo de localização fornecido pelo google maps.", "app.components.form.progressBarLabel": "Progresso da pesquisa", "app.components.form.submit": "Enviar", "app.components.form.submitApiError": "Houve um problema com o envio do formulário. Por favor, verifique se há algum erro e tente novamente.", "app.components.form.verifiedBlocked": "Você não pode editar esse campo porque ele contém informações verificadas", "app.components.formBuilder.Page": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "declaração de acessibilidade", "app.components.formBuilder.addAnswer": "Acrescentar resposta", "app.components.formBuilder.addStatement": "Adicionar <PERSON>", "app.components.formBuilder.agree": "Concordar", "app.components.formBuilder.ai1": "IA", "app.components.formBuilder.aiUpsellText1": "Se você tiver acesso ao nosso pacote de IA, poderá resumir e categorizar respostas de texto com IA", "app.components.formBuilder.askFollowUpToggleLabel": "Solicitar acompanhamento", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Etiqueta do botão", "app.components.formBuilder.buttonLink": "Link do botão", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "Categoria", "app.components.formBuilder.chooseMany": "Escolha muitos", "app.components.formBuilder.chooseOne": "Escolha um", "app.components.formBuilder.close": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Configurar mapa", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON>m, eu quero sair", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "Continua a", "app.components.formBuilder.cosponsors": "Co-patrocinadores", "app.components.formBuilder.default": "Predefinição", "app.components.formBuilder.defaultContent": "<PERSON><PERSON><PERSON><PERSON> predefin<PERSON>", "app.components.formBuilder.delete": "Eliminar", "app.components.formBuilder.deleteButtonLabel": "Eliminar", "app.components.formBuilder.description": "Descrição", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Isto já foi acrescentado no formulário. O conteúdo por defeito só pode ser utilizado uma vez.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "A adição de conteúdo personalizado não faz parte de sua licença atual. Entre em contato com o seu gerente do GovSuccess para saber mais sobre isso.", "app.components.formBuilder.disagree": "Não concordo", "app.components.formBuilder.displayAsDropdown": "Exibir como menu suspenso", "app.components.formBuilder.displayAsDropdownTooltip": "Exiba as opções em um menu suspenso. Se você tiver muitas opções, isso é recomendado.", "app.components.formBuilder.done": "<PERSON><PERSON>", "app.components.formBuilder.drawArea": "<PERSON><PERSON>", "app.components.formBuilder.drawRoute": "Rota de sorteio", "app.components.formBuilder.dropPin": "Pino de queda", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Forneça pelo menos uma resposta. Observe que cada resposta deve ter um título.", "app.components.formBuilder.emptyOptionError": "Fornecer pelo menos 1 resposta", "app.components.formBuilder.emptyStatementError": "Forneça pelo menos uma declaração", "app.components.formBuilder.emptyTitleError": "Fornecer um título de pergunta", "app.components.formBuilder.emptyTitleMessage": "Forneça um título para todas as respostas", "app.components.formBuilder.emptyTitleStatementMessage": "Forneça um título para todas as declarações", "app.components.formBuilder.enable": "Habilitar", "app.components.formBuilder.errorMessage": "<PERSON><PERSON> um problema, por favor resolva o problema para poder salvar as suas alterações", "app.components.formBuilder.fieldGroup.description": "Descrição (opcional)", "app.components.formBuilder.fieldGroup.title": "T<PERSON><PERSON><PERSON> (opcional)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Atualmente, as respostas a estas perguntas só estão disponíveis no ficheiro Excel baixado no Input Manager, e não são visíveis para os usuários.", "app.components.formBuilder.fieldLabel": "Escolhas de resposta", "app.components.formBuilder.fieldLabelStatement": "Declaraç<PERSON><PERSON>", "app.components.formBuilder.fileUpload": "Carregamento de ficheiros", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Página baseada em mapas", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Incorpore o mapa como contexto ou faça perguntas aos participantes com base no local.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Para otimizar a experiência do usuário, não recomendamos adicionar perguntas sobre pontos, rotas ou áreas às páginas baseadas em mapas.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Página normal", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Os recursos de mapeamento de pesquisas não estão incluídos na sua licença atual. Entre em contato com o seu gerente do GovSuccess para saber mais.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Tipo de página", "app.components.formBuilder.formEnd": "Fim do formulário", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON>, excluir página", "app.components.formBuilder.formField.copyNoun": "Cópia", "app.components.formBuilder.formField.copyVerb": "Cópia", "app.components.formBuilder.formField.delete": "Deletar", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Ao excluir esta página, você também excluirá a lógica associada a ela. Você tem certeza de que deseja excluí-la?", "app.components.formBuilder.formField.deleteResultsInfo": "<PERSON><PERSON> não pode ser desfeito", "app.components.formBuilder.goToPageInputLabel": "Então a página seguinte é:", "app.components.formBuilder.good": "Bo<PERSON>", "app.components.formBuilder.helmetTitle": "Constru<PERSON> de formulários", "app.components.formBuilder.imageFileUpload": "Carregamento de imagens", "app.components.formBuilder.invalidLogicBadgeMessage": "Lógica inválida", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON> (opcional)", "app.components.formBuilder.labelsTooltipContent2": "Escolha rótulos opcionais para qualquer um dos valores da escala linear.", "app.components.formBuilder.lastPage": "Finalização", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Tem a certeza de que quer sair?", "app.components.formBuilder.leaveBuilderText": "Você tem alterações não salvas. Salve antes de sair. Se você sair, perderá suas alterações.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON>uando ativado, os entrevistados precisam selecionar o número especificado de respostas para prosseguir.", "app.components.formBuilder.limitNumberAnswers": "Limitar o número de respostas", "app.components.formBuilder.linePolygonMapWarning2": "Os desenhos de linhas e polígonos podem não atender aos padrões de acessibilidade. Mais informações podem ser encontradas em {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Escala linear", "app.components.formBuilder.locationDescription": "Localização", "app.components.formBuilder.logic": "Lógica", "app.components.formBuilder.logicAnyOtherAnswer": "<PERSON>ual<PERSON> outra resposta", "app.components.formBuilder.logicConflicts.conflictingLogic": "Lógica conflitante", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Esta página contém perguntas que levam a diferentes páginas. Se os participantes responderem a várias perguntas, será mostrada a página mais distante. Certifique-se de que esse comportamento esteja alinhado com o fluxo pretendido por você.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Essa página tem várias regras lógicas aplicadas: lógica de seleção múltipla de perguntas, lógica em nível de página e lógica entre perguntas. Quando essas condições se sobrepõem, a lógica da pergunta terá precedência sobre a lógica da página, e a página mais distante será mostrada. Revise a lógica para garantir que ela se alinhe ao fluxo que você pretende.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Esta página contém uma pergunta de seleção múltipla em que as opções levam a páginas diferentes. Se os participantes selecionarem várias opções, será mostrada a página mais distante. Certifique-se de que esse comportamento esteja alinhado com o fluxo pretendido por você.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Esta página contém uma pergunta de seleção múltipla em que as opções levam a páginas diferentes e tem perguntas que levam a outras páginas. A página mais distante será mostrada se essas condições se sobrepuserem. Certifique-se de que esse comportamento esteja alinhado com o fluxo pretendido por você.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Esta página contém uma pergunta de seleção múltipla em que as opções levam a páginas diferentes e tem lógica definida no nível da página e da pergunta. A lógica da pergunta terá precedência, e a página mais distante será mostrada. Certifique-se de que esse comportamento esteja alinhado ao fluxo pretendido por você.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Esta página tem lógica definida tanto no nível da página quanto no nível da pergunta. A lógica da pergunta terá precedência sobre a lógica em nível de página. Certifique-se de que esse comportamento esteja alinhado ao fluxo pretendido por você.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Esta página tem lógica definida nos níveis de página e de pergunta, e várias perguntas direcionam para páginas diferentes. A lógica da pergunta terá precedência, e a página mais distante será mostrada. Certifique-se de que esse comportamento esteja alinhado com o fluxo pretendido por você.", "app.components.formBuilder.logicNoAnswer2": "Não respondido", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Se você tiver outra resposta", "app.components.formBuilder.logicPanelNoAnswer": "Se você não respondeu", "app.components.formBuilder.logicValidationError": "A lógica não pode ligar a páginas anteriores", "app.components.formBuilder.longAnswer": "<PERSON>sp<PERSON><PERSON> longa", "app.components.formBuilder.mapConfiguration": "Configuração do mapa", "app.components.formBuilder.mapping": "Mapeamento", "app.components.formBuilder.mappingNotInCurrentLicense": "Os recursos de mapeamento de pesquisas não estão incluídos na sua licença atual. Entre em contato com o seu gerente do GovSuccess para saber mais.", "app.components.formBuilder.matrix": "<PERSON><PERSON>", "app.components.formBuilder.matrixSettings.columns": "Colunas", "app.components.formBuilder.matrixSettings.rows": "Fileiras", "app.components.formBuilder.multipleChoice": "Múltipla escolha", "app.components.formBuilder.multipleChoiceHelperText": "Se várias opções levarem a páginas diferentes e os participantes selecionarem mais de uma, será mostrada a página mais distante. Certifique-se de que esse comportamento esteja alinhado com o fluxo pretendido por você.", "app.components.formBuilder.multipleChoiceImage": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>", "app.components.formBuilder.multiselect.maximum": "Máximo", "app.components.formBuilder.multiselect.minimum": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.neutral": "Neutro", "app.components.formBuilder.newField": "Novo campo", "app.components.formBuilder.number": "Número", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Abe<PERSON>o", "app.components.formBuilder.optional": "Opcional", "app.components.formBuilder.other": "Outros", "app.components.formBuilder.otherOption": "Opção \"Outro\"", "app.components.formBuilder.otherOptionTooltip": "Permitir que os participantes insiram uma resposta personalizada se as respostas fornecidas não corresponderem às suas preferências", "app.components.formBuilder.page": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "Esta página não pode ser excluída.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Essa página não pode ser excluída e não permite que nenhum campo adicional seja adicionado.", "app.components.formBuilder.pageRuleLabel": "A página seguinte é:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Se nenhuma lógica for adicionada, o formulário seguirá seu fluxo normal. Se tanto a página quanto as perguntas tiverem lógica, a lógica da pergunta terá precedência. Para obter mais informações, visite {supportPageLink}", "app.components.formBuilder.preview": "Prévia:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Os co-patrocinadores não são exibidos no PDF baixado e não são compatíveis com a importação via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "As perguntas sobre upload de arquivos são mostradas como não compatíveis no PDF baixado e não são compatíveis com a importação via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "As perguntas de mapeamento são mostradas no PDF baixado, mas as camadas não estarão visíveis. As perguntas de mapeamento não são compatíveis com a importação via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "As perguntas de matriz são mostradas no PDF baixado, mas atualmente não são compatíveis com a importação via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Os títulos e as descrições das páginas são mostrados como um cabeçalho de seção no PDF baixado.", "app.components.formBuilder.printSupportTooltip.ranking": "As perguntas de classificação são mostradas no PDF baixado, mas atualmente não são compatíveis com a importação via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "As tags são mostradas como não suportadas no PDF baixado e não são suportadas para importação via FormSync.", "app.components.formBuilder.proposedBudget": "Orçamento proposto", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "Esta questão não pode ser eliminada.", "app.components.formBuilder.questionDescriptionOptional": "Descrição da pergunta (opcional)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.randomize": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.randomizeToolTip": "A ordem das respostas será aleatória por usuário", "app.components.formBuilder.range": "Gama", "app.components.formBuilder.ranking": "Classificação", "app.components.formBuilder.rating": "Classificação", "app.components.formBuilder.removeAnswer": "Remover resposta", "app.components.formBuilder.required": "Obrigatório", "app.components.formBuilder.requiredToggleLabel": "Faça com que a resposta a esta pergunta seja necessária", "app.components.formBuilder.ruleForAnswerLabel": "Se a resposta for:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Se as respostas incluírem:", "app.components.formBuilder.save": "Guardar", "app.components.formBuilder.selectRangeTooltip": "Escolha o valor máximo para a sua escala.", "app.components.formBuilder.sentiment": "Escala de sentimento", "app.components.formBuilder.shapefileUpload": "Carregamento de shapefile do Esri", "app.components.formBuilder.shortAnswer": "Resposta curta", "app.components.formBuilder.showResponseToUsersToggleLabel": "Mostrar resposta aos usuários", "app.components.formBuilder.singleChoice": "Escolha única", "app.components.formBuilder.staleDataErrorMessage2": "Houve um problema. Este formulário de entrada foi salvo mais recentemente em outro lugar. <PERSON><PERSON> pode ocorrer porque você ou outro usuário o abriu para edição em outra janela do navegador. Atualize a página para obter o formulário mais recente e, em seguida, faça suas alterações novamente.", "app.components.formBuilder.stronglyAgree": "Concordo totalmente", "app.components.formBuilder.stronglyDisagree": "Discordo totalmente", "app.components.formBuilder.supportArticleLinkText": "esta página", "app.components.formBuilder.tags": "Etiquetas", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "para", "app.components.formBuilder.unsavedChanges": "Você tem alterações não salvas", "app.components.formBuilder.useCustomButton2": "Usar botão de página personalizado", "app.components.formBuilder.veryBad": "<PERSON><PERSON> ruim", "app.components.formBuilder.veryGood": "<PERSON><PERSON> bom", "app.components.ideas.similarIdeas.engageHere": "Envolva-se aqui", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Não foram encontrados envios semelhantes.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Encontramos submissões semelhantes - o envolvimento com elas pode ajudar a torná-las mais fortes!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Envios semel<PERSON> já foram publicados:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Você está procurando por envios semelhantes ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON>os de um dia} one {# dia} other {# dias}} esquerda", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  semanas restantes", "app.components.screenReaderCurrency.AED": "<PERSON><PERSON><PERSON> dos Emirados Árabes Unidos", "app.components.screenReaderCurrency.AFN": "Afegão Afegão", "app.components.screenReaderCurrency.ALL": "Lek albanês", "app.components.screenReaderCurrency.AMD": "Dramática armênia", "app.components.screenReaderCurrency.ANG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AOA": "<PERSON><PERSON><PERSON> angol<PERSON>", "app.components.screenReaderCurrency.ARS": "Peso argentino", "app.components.screenReaderCurrency.AUD": "<PERSON><PERSON><PERSON> austra<PERSON>", "app.components.screenReaderCurrency.AWG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AZN": "Manat azerbaijano", "app.components.screenReaderCurrency.BAM": "Marca conversível da Bósnia-Herzegovina", "app.components.screenReaderCurrency.BBD": "Dólar barbadiano", "app.components.screenReaderCurrency.BDT": "Taka de Bangladesh", "app.components.screenReaderCurrency.BGN": "<PERSON>", "app.components.screenReaderCurrency.BHD": "Dinar <PERSON>ein", "app.components.screenReaderCurrency.BIF": "<PERSON>", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON><PERSON> das Bermudas", "app.components.screenReaderCurrency.BND": "Dólar de Brunei", "app.components.screenReaderCurrency.BOB": "Boliviano <PERSON>no", "app.components.screenReaderCurrency.BOV": "<PERSON>v<PERSON><PERSON> boli<PERSON>o", "app.components.screenReaderCurrency.BRL": "Real brasileiro", "app.components.screenReaderCurrency.BSD": "Dólar bah<PERSON>", "app.components.screenReaderCurrency.BTN": "Ngultrum <PERSON>", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BZD": "Dólar de Belize", "app.components.screenReaderCurrency.CAD": "Dólar canadense", "app.components.screenReaderCurrency.CDF": "Franco <PERSON>", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Franco suíço", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Unidade de conta chilena (UF)", "app.components.screenReaderCurrency.CLP": "Peso chileno", "app.components.screenReaderCurrency.CNY": "<PERSON> chinês", "app.components.screenReaderCurrency.COP": "Peso colombiano", "app.components.screenReaderCurrency.COU": "Unidade de Valor Real", "app.components.screenReaderCurrency.CRC": "Colón da Costa Rica", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Peso cubano conversível", "app.components.screenReaderCurrency.CUP": "Peso cubano", "app.components.screenReaderCurrency.CVE": "Escudo cabo-verdiano", "app.components.screenReaderCurrency.CZK": "<PERSON>roa tcheca", "app.components.screenReaderCurrency.DJF": "Franco do Djibuti", "app.components.screenReaderCurrency.DKK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.DOP": "Peso dominicano", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "Libra egípcia", "app.components.screenReaderCurrency.ERN": "Nakfa da Eritreia", "app.components.screenReaderCurrency.ETB": "Birr etíope", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Dólar de Fiji", "app.components.screenReaderCurrency.FKP": "Libra das Ilhas Falkland", "app.components.screenReaderCurrency.GBP": "Libra esterlina", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON> da Geórgia", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Libra de Gibraltar", "app.components.screenReaderCurrency.GMD": "<PERSON><PERSON> Gâmb<PERSON>", "app.components.screenReaderCurrency.GNF": "Franco guineen<PERSON>", "app.components.screenReaderCurrency.GTQ": "<PERSON><PERSON><PERSON> da Guatemala", "app.components.screenReaderCurrency.GYD": "Dólar g<PERSON>", "app.components.screenReaderCurrency.HKD": "Dólar de Hong Kong", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "<PERSON><PERSON> croata", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "<PERSON><PERSON> h<PERSON>", "app.components.screenReaderCurrency.IDR": "<PERSON><PERSON><PERSON> in<PERSON>ia", "app.components.screenReaderCurrency.ILS": "<PERSON>o shekel israelense", "app.components.screenReaderCurrency.INR": "Rúpia indiana", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "Króna islandesa", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KGS": "Som do Quirguistão", "app.components.screenReaderCurrency.KHR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KMF": "Franco das Comores", "app.components.screenReaderCurrency.KPW": "Won norte-coreano", "app.components.screenReaderCurrency.KRW": "Won sul-coreano", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON> k<PERSON>", "app.components.screenReaderCurrency.KYD": "Dólar das Ilhas Cayman", "app.components.screenReaderCurrency.KZT": "Tenge do Cazaquistão", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libra libanesa", "app.components.screenReaderCurrency.LKR": "Rúpia do Sri Lanka", "app.components.screenReaderCurrency.LRD": "Dólar liberiano", "app.components.screenReaderCurrency.LSL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LTL": "Litas lituanas", "app.components.screenReaderCurrency.LVL": "<PERSON>ts letão", "app.components.screenReaderCurrency.LYD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON><PERSON> marro<PERSON>o", "app.components.screenReaderCurrency.MDL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MGA": "<PERSON><PERSON> malgaxe", "app.components.screenReaderCurrency.MKD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MMK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MNT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da Mongólia", "app.components.screenReaderCurrency.MOP": "Pataca macaense", "app.components.screenReaderCurrency.MRO": "Ouguiya da Mauritânia", "app.components.screenReaderCurrency.MUR": "<PERSON>ú<PERSON> ma<PERSON>", "app.components.screenReaderCurrency.MVR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Peso mexicano", "app.components.screenReaderCurrency.MXV": "Unidade de Inversão Mexicana (UDI)", "app.components.screenReaderCurrency.MYR": "<PERSON><PERSON> malaio", "app.components.screenReaderCurrency.MZN": "Metical m<PERSON>çamb<PERSON>", "app.components.screenReaderCurrency.NAD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NGN": "Nair<PERSON> nigeriana", "app.components.screenReaderCurrency.NIO": "Córdoba da Nicarágua", "app.components.screenReaderCurrency.NOK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.NPR": "Rú<PERSON> ne<PERSON>", "app.components.screenReaderCurrency.NZD": "Dólar da Nova Zelândia", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.PAB": "Balboa panamenho", "app.components.screenReaderCurrency.PEN": "Sol peruano", "app.components.screenReaderCurrency.PGK": "Kina de Papua Nova Guiné", "app.components.screenReaderCurrency.PHP": "Peso filipino", "app.components.screenReaderCurrency.PKR": "<PERSON>ú<PERSON> paqui<PERSON>", "app.components.screenReaderCurrency.PLN": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.PYG": "Guarani paraguaio", "app.components.screenReaderCurrency.QAR": "Riyal do Catar", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON> r<PERSON>no", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON>lo russo", "app.components.screenReaderCurrency.RWF": "<PERSON> r<PERSON>", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON><PERSON> saudita", "app.components.screenReaderCurrency.SBD": "Dólar das Ilhas Salomão", "app.components.screenReaderCurrency.SCR": "Rúpia de Seychellois", "app.components.screenReaderCurrency.SDG": "Libra sudanesa", "app.components.screenReaderCurrency.SEK": "Coroa sueca", "app.components.screenReaderCurrency.SGD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SHP": "Libra de Santa Helena", "app.components.screenReaderCurrency.SLL": "<PERSON>", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SSP": "Libra sul-sudanesa", "app.components.screenReaderCurrency.STD": "São Tomé e Príncipe Do<PERSON>", "app.components.screenReaderCurrency.SYP": "Libra síria", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "<PERSON><PERSON>", "app.components.screenReaderCurrency.TJS": "Somoni do Tajiquistão", "app.components.screenReaderCurrency.TMT": "Manat do Turcomenistão", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON> tuni<PERSON>", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TRY": "Lira turca", "app.components.screenReaderCurrency.TTD": "Dólar de Trinidad e Tobago", "app.components.screenReaderCurrency.TWD": "Novo dólar ta<PERSON>", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Hryvnia ucraniano", "app.components.screenReaderCurrency.UGX": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.USD": "Dólar dos Estados Unidos", "app.components.screenReaderCurrency.USN": "Dólar dos Estados Unidos (dia seguinte)", "app.components.screenReaderCurrency.USS": "<PERSON><PERSON><PERSON> dos Estados Unidos (no mesmo dia)", "app.components.screenReaderCurrency.UYI": "Uruguai Peso em Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Peso uruguaio", "app.components.screenReaderCurrency.UZS": "Som do Uzbequistão", "app.components.screenReaderCurrency.VEF": "Bolívar venezu<PERSON>", "app.components.screenReaderCurrency.VND": "Đồng vietnamita", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "<PERSON><PERSON>", "app.components.screenReaderCurrency.XAF": "Franco CFA da África Central", "app.components.screenReaderCurrency.XAG": "Prata (uma onça troy)", "app.components.screenReaderCurrency.XAU": "<PERSON><PERSON> (uma onça troy)", "app.components.screenReaderCurrency.XBA": "Unidade composta europeia (EURCO)", "app.components.screenReaderCurrency.XBB": "Unidade Monetária Europeia (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Unidade Europeia de Conta 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Unidade Europeia de Conta 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Dólar do Caribe Oriental", "app.components.screenReaderCurrency.XDR": "Direitos Especiais de Saque", "app.components.screenReaderCurrency.XFU": "<PERSON> da UIC", "app.components.screenReaderCurrency.XOF": "Franco CFA da África Ocidental", "app.components.screenReaderCurrency.XPD": "<PERSON><PERSON><PERSON><PERSON> (uma onça troy)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (uma onça troy)", "app.components.screenReaderCurrency.XTS": "Códigos especificamente reservados para fins de teste", "app.components.screenReaderCurrency.XXX": "Sem moeda", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON> i<PERSON>ta", "app.components.screenReaderCurrency.ZAR": "Rand sul-africano", "app.components.screenReaderCurrency.ZMW": "<PERSON><PERSON>cha <PERSON>am<PERSON>", "app.components.screenReaderCurrency.amount": "Valor", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "último trimestre", "app.containers.AccessibilityStatement.applicability": "Esta declaração de acessibilidade aplica-se a um {demoPlatformLink} que é representativa deste website; utiliza o mesmo código fonte e tem a mesma funcionalidade.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Método de avaliação", "app.containers.AccessibilityStatement.assesmentText2022": "A acessibilidade deste site foi avaliada por uma entidade externa não envolvida no processo de concepção e desenvolvimento. A conformidade mencionada {demoPlatformLink} pode ser identificada neste {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "você pode alterar as suas preferências", "app.containers.AccessibilityStatement.changePreferencesText": "A qualquer momento, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Excepções de conformidade", "app.containers.AccessibilityStatement.conformanceStatus": "Status de conformidade", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Nós nos esforçamos para tornar o nosso conteúdo inclusivo para todos. No entanto, em alguns casos, pode haver conteúdos inacessíveis na plataforma, conforme descrito abaixo:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "site de demonstração", "app.containers.AccessibilityStatement.email": "E-mail:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Ferramentas de pesquisa incorporadas", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "As ferramentas de pesquisa incorporadas que estão disponíveis para uso nesta plataforma são softwares de terceiros e podem não estar acessíveis.", "app.containers.AccessibilityStatement.exception_1": "Nossas plataformas de engajamento digital facilitam o conteúdo gerado pelo usuário postado por indivíduos e organizações. É possível que PDFs, imagens ou outros tipos de arquivos, incluindo multimídia, sejam carregados na plataforma como anexos ou adicionados em campos de texto pelos usuários da plataforma. Esses documentos podem não estar totalmente acessíveis.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Agradecemos o seu feedback sobre a acessibilidade deste site. Por favor contate-nos através de um dos seguintes canais:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Processo de feedback", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bruxelas, Bélgica", "app.containers.AccessibilityStatement.headTitle": "Declaração de Acessibilidade | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} está empenhado em fornecer uma plataforma que seja acessível a todos os usuários, independentemente da tecnologia ou da capacidade. As atuais normas de acessibilidade relevantes são respeitadas nos nossos esforços contínuos para maximizar a acessibilidade e a usabilidade das nossas plataformas para todos os usuários.", "app.containers.AccessibilityStatement.mapping": "Mapeamento", "app.containers.AccessibilityStatement.mapping_1": "Os mapas na plataforma atendem parcialmente aos padrões de acessibilidade. A extensão do mapa, o zoom e os widgets da interface do usuário podem ser controlados por meio de um teclado durante a visualização dos mapas. Os administradores também podem configurar o estilo das camadas do mapa no back office ou usar a integração com o Esri para criar paletas de cores e simbologia mais acessíveis. O uso de diferentes estilos de linha ou polígono (por exemplo, linhas tracejadas) também ajudará a diferenciar as camadas do mapa sempre que possível e, embora esse estilo não possa ser configurado em nossa plataforma no momento, ele pode ser configurado se você estiver usando mapas com a integração Esri.", "app.containers.AccessibilityStatement.mapping_2": "Os mapas na plataforma não são totalmente acessíveis, pois não apresentam mapas base, camadas de mapas ou tendências nos dados de forma audível para usuários que utilizam leitores de tela. Mapas totalmente acessíveis precisariam apresentar de forma audível as camadas do mapa e descrever quaisquer tendências relevantes nos dados. Além disso, o desenho de mapas de linhas e polígonos em pesquisas não é acessível, pois as formas não podem ser desenhadas usando um teclado. Métodos alternativos de entrada não estão disponíveis no momento devido à complexidade técnica.", "app.containers.AccessibilityStatement.mapping_3": "Para tornar o desenho de mapas de linhas e polígonos mais acessível, recomendamos que você inclua uma introdução ou explicação na pergunta da pesquisa ou na descrição da página sobre o que o mapa está mostrando e quaisquer tendências relevantes. Além disso, uma pergunta de texto com resposta curta ou longa pode ser fornecida para que os entrevistados possam descrever suas respostas em termos simples, se necessário (em vez de clicar no mapa). Também recomendamos incluir informações de contato do gerente de projeto para que os questionados que não puderem preencher uma pergunta do mapa possam solicitar um método alternativo para responder à pergunta (por exemplo, reunião por vídeo).", "app.containers.AccessibilityStatement.mapping_4": "Para projetos e propostas de ideação, há uma opção para exibir os inputs em uma visualização de mapa, que não é acessível. No entanto, para esses métodos, há uma visualização de lista alternativa dos inputs disponíveis, que pode ser acessada.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Os nossos workshops online têm um componente de streaming ao vivo, que atualmente não suporta legendas.", "app.containers.AccessibilityStatement.pageDescription": "Uma declaração sobre acessibilidade deste site", "app.containers.AccessibilityStatement.postalAddress": "Endereço:", "app.containers.AccessibilityStatement.publicationDate": "Data de publicação", "app.containers.AccessibilityStatement.publicationDate2024": "Esta declaração de acessibilidade foi publicada em 21 de agosto de 2024.", "app.containers.AccessibilityStatement.responsiveness": "O nosso objetivo é responder ao feedback dentro de 1-2 dias úteis.", "app.containers.AccessibilityStatement.statusPageText": "status da página", "app.containers.AccessibilityStatement.technologiesIntro": "A acessibilidade deste site depende das seguintes tecnologias para funcionar:", "app.containers.AccessibilityStatement.technologiesTitle": "Tecnologia", "app.containers.AccessibilityStatement.title": "Declaração de Acessibilidade", "app.containers.AccessibilityStatement.userGeneratedContent": "Conte<PERSON><PERSON> gerado pelo utilizador", "app.containers.AccessibilityStatement.workshops": "Seminário", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Selecione o projeto", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Usar o Content Builder permitirá que você use opções de layout mais avançadas. Nos idiomas em que não houver conteúdo disponível no Content Builder, será exibido o conteúdo normal da descrição do projeto.", "app.containers.AdminPage.ProjectDescription.linkText": "Editar descrição no Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "Algo deu errado ao salvar a descrição do projeto.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Use o Content Builder para descrição", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Usar o Content Builder permitirá que você use opções de layout mais avançadas.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON><PERSON><PERSON> projeto", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON>m da pesquisa", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Criar um grupo inteligente", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Os usuários que atenderem a todas as condições a seguir serão adicionados automaticamente ao grupo:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON><PERSON>cer pelo menos uma regra", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Algumas condições estão incompletas", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Salvar grupo", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "A configuração de grupos inteligentes não faz parte de sua licença atual. Entre em contato com o seu gerente do GovSuccess para saber mais sobre isso.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Fornecer um nome para o grupo", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "A verificação está desabilitada para sua plataforma, remova a regra de verificação ou entre em contato com o suporte.", "app.containers.App.appMetaDescription": "Bem-vindo à plataforma de participação online de {orgName}.\nExplore projetos locais e participe da discussão!", "app.containers.App.loading": "Carregando…", "app.containers.App.metaTitle1": "Plataforma de envolvimento do cidadão | {orgName}", "app.containers.App.skipLinkText": "Pular para o conteúdo principal", "app.containers.AreaTerms.areaTerm": "<PERSON><PERSON>", "app.containers.AreaTerms.areasTerm": "<PERSON><PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Já existe uma conta com este e-mail. Você pode sair, fazer login com este endereço de e-mail e verificar sua conta na página de configurações.", "app.containers.Authentication.steps.AccessDenied.close": "<PERSON><PERSON><PERSON>", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Você não atende aos requisitos para participar desse processo.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Voltar à verificação de logon único", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "<PERSON><PERSON><PERSON><PERSON><PERSON> um <PERSON>", "app.containers.Authentication.steps.Invitation.token": "<PERSON><PERSON>", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Você já tem uma conta? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Fazer login", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-mails nesta categoria", "app.containers.CampaignsConsentForm.messageError": "Ocorreu um erro ao salvar as as suas preferências de e-mail.", "app.containers.CampaignsConsentForm.messageSuccess": "As suas preferências de e-mail foram salvas.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Que tipos de notificações você quer receber por e-mail? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Notificações", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "Voltar às definições do perfil", "app.containers.ChangeEmail.confirmationModalTitle": "Confirmar o seu correio electrónico", "app.containers.ChangeEmail.emailEmptyError": "Fornecer um endereço electrónico", "app.containers.ChangeEmail.emailInvalidError": "Fornecer um endereço de correio electrónico no formato correcto, por exemplo, <EMAIL>", "app.containers.ChangeEmail.emailRequired": "Introduza um endereço de correio electrónico.", "app.containers.ChangeEmail.emailTaken": "Este correio electrónico já está a ser utilizado.", "app.containers.ChangeEmail.emailUpdateCancelled": "A actualização do e-mail foi cancelada.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Para actualizar o seu e-mail, reinicie o processo.", "app.containers.ChangeEmail.helmetDescription": "Alterar a sua página de correio electrónico", "app.containers.ChangeEmail.helmetTitle": "Alterar o seu correio electrónico", "app.containers.ChangeEmail.newEmailLabel": "Novo correio electrónico", "app.containers.ChangeEmail.submitButton": "Enviar", "app.containers.ChangeEmail.titleAddEmail": "Adicionar o seu e-mail", "app.containers.ChangeEmail.titleChangeEmail": "Alterar o seu correio electrónico", "app.containers.ChangeEmail.updateSuccessful": "O seu e-mail foi actualizado com sucesso.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON> atual", "app.containers.ChangePassword.currentPasswordRequired": "Digite sua senha atual", "app.containers.ChangePassword.goHome": "Ir para casa", "app.containers.ChangePassword.helmetDescription": "Página para redefinir a sua senha", "app.containers.ChangePassword.helmetTitle": "Altere a sua senha", "app.containers.ChangePassword.newPasswordLabel": "Nova senha", "app.containers.ChangePassword.newPasswordRequired": "Insira a sua senha", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Forneça uma senha que tenha pelo menos {minimumPasswordLength} caracteres", "app.containers.ChangePassword.passwordChangeSuccessMessage": "A sua senha foi atualizada com sucesso", "app.containers.ChangePassword.passwordEmptyError": "Insira a sua senha", "app.containers.ChangePassword.passwordsDontMatch": "Confirmar nova senha", "app.containers.ChangePassword.titleAddPassword": "Adicionar uma palavra-passe", "app.containers.ChangePassword.titleChangePassword": "Alterar a sua palavra-passe", "app.containers.Comments.a11y_commentDeleted": "comentário <PERSON>clu<PERSON>", "app.containers.Comments.a11y_commentPosted": "coment<PERSON>rio postado", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {sem gostos} one {1 gosto} other {# likes}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON> curtida", "app.containers.Comments.addCommentError": "\nAlgo deu errado. Por favor, tente novamente mais tarde.", "app.containers.Comments.adminCommentDeletionCancelButton": "Cancelado", "app.containers.Comments.adminCommentDeletionConfirmButton": "Deletar o comentário", "app.containers.Comments.cancelCommentEdit": "Cancelado", "app.containers.Comments.childCommentBodyPlaceholder": "Escreva uma resposta...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "Este comentário foi excluido", "app.containers.Comments.commentDeletionCancelButton": "Mantenha meu coment<PERSON>rio", "app.containers.Comments.commentDeletionConfirmButton": "Excluir o meu comentário", "app.containers.Comments.commentLike": "Curtir", "app.containers.Comments.commentReplyButton": "Resposta", "app.containers.Comments.commentsSortTitle": "Classificar comentários por", "app.containers.Comments.completeProfileLinkText": "completar o seu perfil", "app.containers.Comments.completeProfileToComment": "Por favor, {completeRegistrationLink} para comentar.", "app.containers.Comments.confirmCommentDeletion": "Tem a certeza que quer apagar este comentário? Não há volta a dar!", "app.containers.Comments.deleteComment": "Deletar", "app.containers.Comments.deleteReasonDescriptionError": "Providencie mais informações sobre seu motivo", "app.containers.Comments.deleteReasonError": "Forneça um motivo", "app.containers.Comments.deleteReason_inappropriate": "É inapropriado ou ofensivo", "app.containers.Comments.deleteReason_irrelevant": "Isto não pertence aqui.", "app.containers.Comments.deleteReason_other": "Outro motivo", "app.containers.Comments.editComment": "<PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "nossas diretrizes", "app.containers.Comments.ideaCommentBodyPlaceholder": "Escreva aqui o seu comentário", "app.containers.Comments.internalCommentingNudgeMessage": "Fazer comentários internos não está incluído em sua licença atual. Entre em contato com seu gerente do GovSuccess para saber mais sobre isso.", "app.containers.Comments.internalConversation": "Conversa interna", "app.containers.Comments.loadMoreComments": "<PERSON><PERSON><PERSON> mais come<PERSON>", "app.containers.Comments.loadingComments": "Carregando comentários...", "app.containers.Comments.loadingMoreComments": "Carregando mais coment<PERSON>rio<PERSON>...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Este comentário não é visível para os usuários comuns", "app.containers.Comments.postInternalComment": "Publicar comentário interno", "app.containers.Comments.postPublicComment": "Publicar comentário p<PERSON>o", "app.containers.Comments.profanityError": "Você pode ter usado uma ou mais palavras que são consideradas inapropriadas por {guidelinesLink}. Por favor, altere o seu texto para remover quaisquer palavras impróprias que possam estar presentes.", "app.containers.Comments.publicDiscussion": "Discussão pública", "app.containers.Comments.publishComment": "Publique seu comentário", "app.containers.Comments.reportAsSpamModalTitle": "Porque deseja denunciar isto como spam?", "app.containers.Comments.saveComment": "<PERSON><PERSON>", "app.containers.Comments.signInLinkText": "conecte-se", "app.containers.Comments.signInToComment": "Por favor {signInLink} para comentar.", "app.containers.Comments.signUpLinkText": "cadastrar-se", "app.containers.Comments.verifyIdentityLinkText": "Verifique o seu usuário", "app.containers.Comments.visibleToUsersPlaceholder": "Este comentário é visível para os usuários comuns", "app.containers.Comments.visibleToUsersWarning": "Os comentários aqui publicados serão visíveis para os usuários comuns.", "app.containers.ContentBuilder.PageTitle": "Descrição do projeto", "app.containers.CookiePolicy.advertisingContent": "Os cookies de publicidade podem ser usados para personalizar e medir a eficácia que as campanhas de marketing externo têm no envolvimento com esta plataforma. Não exibiremos nenhuma publicidade nesta plataforma, mas você poderá receber anúncios personalizados com base nas páginas que visitar.", "app.containers.CookiePolicy.advertisingTitle": "Cookies de publicidade", "app.containers.CookiePolicy.analyticsContents": "Os cookies analíticos rastreiam o comportamento do visitante, como quais páginas são visitadas e por quanto tempo. Além disso, também podem coletar alguns dados técnicos, incluindo informações do navegador e localização aproximada e endereços IP. Só usamos esses dados internamente para continuar a melhorar a experiência geral do usuário e o funcionamento da plataforma. Esses dados também podem ser compartilhados entre Go Vocal e {orgName} para avaliar e melhorar o envolvimento com projetos na plataforma. Observe que os dados são anônimos e usados em um nível agregado e não identificam você pessoalmente. No entanto, se esses dados forem combinados com outras fontes de dados, tal identificação poderia ocorrer.", "app.containers.CookiePolicy.analyticsTitle": "Cookies analíticos", "app.containers.CookiePolicy.cookiePolicyDescription": "Uma explicação detalhada de como são usados as cookies nesta plataforma", "app.containers.CookiePolicy.cookiePolicyTitle": "Política de Cookies", "app.containers.CookiePolicy.essentialContent": "Alguns cookies são essenciais para garantir o bom funcionamento desta plataforma. Esses cookies essenciais são usados principalmente para autenticar sua conta quando você visita a plataforma e para salvar seu idioma preferido.", "app.containers.CookiePolicy.essentialTitle": "<PERSON><PERSON> essenciais", "app.containers.CookiePolicy.externalContent": "Algumas de nossas páginas podem exibir conteúdo de fornecedores externos, por exemplo: YouTube ou Typeform. Não temos controle sobre esses cookies de terceiros e a visualização do conteúdo desses provedores externos também pode resultar na instalação de cookies em seu dispositivo.", "app.containers.CookiePolicy.externalTitle": "Cookies externos", "app.containers.CookiePolicy.functionalContents": "Os cookies funcionais podem ser habilitados para que os visitantes recebam notificações sobre atualizações e acessem canais de suporte diretamente da plataforma.", "app.containers.CookiePolicy.functionalTitle": "Cookies funcionais", "app.containers.CookiePolicy.headCookiePolicyTitle": "Política de cookies | {orgName}", "app.containers.CookiePolicy.intro": "Cookies são arquivos de texto armazenados no navegador ou no disco rígido do seu computador ou dispositivo móvel quando você visita um site e que podem ser acessados pelo usuário durante as visitas subsequentes. Usamos cookies para entender como os visitantes estão usando esta plataforma para melhorar seu design e experiência, para lembrar suas preferências (como seu idioma preferido) e para oferecer suporte a funções básicas para usuários registrados e administradores da plataforma.", "app.containers.CookiePolicy.manageCookiesDescription": "Você pode ativar ou desativar os cookies analíticos, de marketing e funcionais a qualquer momento em suas preferências de cookies. Você também pode excluir manualmente ou automaticamente quaisquer cookies existentes por meio do navegador. No entanto, os cookies podem ser colocados novamente após o seu consentimento em quaisquer visitas subsequentes a esta plataforma. Se você não excluir os cookies, suas preferências de cookies serão armazenadas por 60 dias, após os quais o seu consentimento será solicitado novamente.", "app.containers.CookiePolicy.manageCookiesPreferences": "Acesse as {manageCookiesPreferencesButtonText} para ver uma lista completa de integrações com terceiros usadas nesta plataforma e para gerenciar suas preferências.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "configurações de cookies", "app.containers.CookiePolicy.manageCookiesTitle": "Gerenciamentos do cookies", "app.containers.CookiePolicy.viewPreferencesButtonText": "Configurações de cookies", "app.containers.CookiePolicy.viewPreferencesText": "As categorias de cookies abaixo não se aplicam a todos os visitantes ou plataformas. Consulte as {viewPreferencesButton} para obter uma lista completa de integrações de terceiros aplicáveis a você.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Para que usamos cookies?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON>", "app.containers.CustomPageShow.goBack": "Voltar", "app.containers.CustomPageShow.notFound": "Página não encontrada", "app.containers.DisabledAccount.bottomText": "Você pode entrar novamente a partir de {date}.", "app.containers.DisabledAccount.termsAndConditions": "termos e condições", "app.containers.DisabledAccount.text2": "Sua conta na plataforma de participação do {orgName} foi temporariamente desativada por violação das diretrizes da comunidade. Para mais informações sobre isso, você pode consultar o {TermsAndConditions}.", "app.containers.DisabledAccount.title": "A sua conta foi temporariamente desativada", "app.containers.EventsShow.addToCalendar": "Adicionar ao calendário", "app.containers.EventsShow.editEvent": "Editar evento", "app.containers.EventsShow.emailSharingBody2": "Participe deste evento: {eventTitle}. Leia mais em {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Data e hora do evento", "app.containers.EventsShow.eventFrom2": "De \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Voltar atrás", "app.containers.EventsShow.goToProject": "Ir para o projeto", "app.containers.EventsShow.haveRegistered": "se registraram", "app.containers.EventsShow.icsError": "Erro ao baixar o arquivo ICS", "app.containers.EventsShow.linkToOnlineEvent": "Link para evento on-line", "app.containers.EventsShow.locationIconAltText": "Localização", "app.containers.EventsShow.metaTitle": "Evento: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Reunião on-line", "app.containers.EventsShow.onlineLinkIconAltText": "Link da reunião on-line", "app.containers.EventsShow.registered": "registrado", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registrantes} one {1 registrante} other {# registrantes}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registrantes", "app.containers.EventsShow.registrantsIconAltText": "Registradores", "app.containers.EventsShow.socialMediaSharingMessage": "Participe deste evento: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# Participante} other {# participantes}}", "app.containers.EventsViewer.allTime": "Todo o tempo", "app.containers.EventsViewer.date": "Data", "app.containers.EventsViewer.thisMonth2": "<PERSON>r<PERSON><PERSON><PERSON>", "app.containers.EventsViewer.thisWeek2": "Próxima semana", "app.containers.EventsViewer.today": "Hoje", "app.containers.IdeaButton.addAContribution": "Adicionar uma contribuição", "app.containers.IdeaButton.addAPetition": "Adicionar uma peti<PERSON>", "app.containers.IdeaButton.addAProject": "Adicionar um projeto", "app.containers.IdeaButton.addAProposal": "Adicionar uma proposta", "app.containers.IdeaButton.addAQuestion": "Adicionar uma pergunta", "app.containers.IdeaButton.addAnInitiative": "Adicionar uma iniciativa", "app.containers.IdeaButton.addAnOption": "Adicionar uma opção", "app.containers.IdeaButton.postingDisabled": "Novos envios não estão sendo aceitos no momento", "app.containers.IdeaButton.postingInNonActivePhases": "Novos envios só podem ser adicionados em fases ativas.", "app.containers.IdeaButton.postingInactive": "Novos envios não estão sendo aceitos no momento.", "app.containers.IdeaButton.postingLimitedMaxReached": "Você já respondeu a esta pesquisa. Obrigado pela sua resposta!", "app.containers.IdeaButton.postingNoPermission": "No momento não é possível fazer novas contribuições", "app.containers.IdeaButton.postingNotYetPossible": "Novos envios ainda não são aceitos aqui.", "app.containers.IdeaButton.signInLinkText": "conecte-se", "app.containers.IdeaButton.signUpLinkText": "cadastrar-se", "app.containers.IdeaButton.submitAnIssue": "Enviar um problema", "app.containers.IdeaButton.submitYourIdea": "Enviar uma ideia", "app.containers.IdeaButton.takeTheSurvey": "Faça a pesquisa", "app.containers.IdeaButton.verificationLinkText": "Verifique o seu usuário agora", "app.containers.IdeaCard.readMore": "<PERSON><PERSON> mais", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {sem comentários} one {1 comentário} other {# comentários}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {nenhum voto} one {1 voto} other {# votos}} fora de {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "<PERSON><PERSON><PERSON> o <PERSON> de filtros", "app.containers.IdeaCards.a11y_totalItems": "Total de entradas: {ideasCount}", "app.containers.IdeaCards.all": "<PERSON><PERSON>", "app.containers.IdeaCards.allStatuses": "Todos os status", "app.containers.IdeaCards.contributions": "Contribuições", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Iniciativas", "app.containers.IdeaCards.issueTerm": "Problemas", "app.containers.IdeaCards.list": "Listagem", "app.containers.IdeaCards.map": "Mapa", "app.containers.IdeaCards.mostDiscussed": "<PERSON><PERSON> discutido", "app.containers.IdeaCards.newest": "<PERSON><PERSON> recentes", "app.containers.IdeaCards.noFilteredResults": "Nenhum resultado encontrado. Tente um filtro ou termo de pesquisa diferente.", "app.containers.IdeaCards.numberResults": "Resultados ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON> ve<PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "Opções", "app.containers.IdeaCards.petitions": "Petições", "app.containers.IdeaCards.popular": "<PERSON><PERSON> votados", "app.containers.IdeaCards.projectFilterTitle": "Projetos", "app.containers.IdeaCards.projectTerm": "Projetos", "app.containers.IdeaCards.proposals": "Propostas", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "Redefinir filt<PERSON>", "app.containers.IdeaCards.showXResults": "Mostrar {ideasCount, plural, no {# results} one {# result} other {# results}}", "app.containers.IdeaCards.sortTitle": "Ordenar", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Tópicos", "app.containers.IdeaCards.topicsTitle": "Tópicos", "app.containers.IdeaCards.trending": "Tendência", "app.containers.IdeaCards.tryDifferentFilters": "Nenhum resultado encontrado. Tente um filtro ou termo de pesquisa diferente.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} coment<PERSON>rios} one {{ideasCount} coment<PERSON><PERSON>} other {{ideasCount} coment<PERSON>rios}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contribuições} one {{ideasCount} contribuição} other {{ideasCount} contribuições}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideias} one {{ideasCount} ideia} other {{ideasCount} ideias}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} iniciativas} one {{ideasCount} iniciativa} other {{ideasCount} iniciativas}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opções} one {{ideasCount} opção} other {{ideasCount} opções}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petições} one {{ideasCount} petição} other {{ideasCount} petições}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projetos} one {{ideasCount} projeto} other {{ideasCount} projetos}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} propostas} one {{ideasCount} proposta} other {{ideasCount} propostas}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} perguntas} one {{ideasCount} pergunta} other {{ideasCount} perguntas}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, no {# results} one {# result} other {# results}}", "app.containers.IdeasEditPage.contributionFormTitle": "Editar contribuição", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "Falha no envio de um ou mais arquivos. Verifique o tamanho e o formato do arquivo e tente novamente.", "app.containers.IdeasEditPage.formTitle": "<PERSON>ar ideia", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Edite sua publicação. Adicione informações novas e altere informações antigas.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Editar {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Editar iniciativa", "app.containers.IdeasEditPage.issueFormTitle": "<PERSON>ar problema", "app.containers.IdeasEditPage.optionFormTitle": "Editar <PERSON>", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "<PERSON>ar projeto", "app.containers.IdeasEditPage.proposalFormTitle": "<PERSON><PERSON> proposta", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON>", "app.containers.IdeasEditPage.save": "Salvo!", "app.containers.IdeasEditPage.submitApiError": "Houve um problema com o envio do formulário. Por favor, verifique se há algum erro e tente novamente.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "<PERSON><PERSON> as entradas publicadas", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Explore todas as entradas publicadas na plataforma de participação de {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Postagens | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Entradas", "app.containers.IdeasIndexPage.loadMore": "<PERSON><PERSON><PERSON> mais...", "app.containers.IdeasIndexPage.loading": "Carregando…", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "<PERSON><PERSON> pad<PERSON><PERSON>, seus envios serão associados ao seu perfil, a menos que você selecione esta opção.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Publicar anonimamente", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Visibilidade do perfil", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Esta pesquisa não está aberta para respostas no momento. Volte ao projeto para obter mais informações.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Essa pesquisa não está ativa no momento.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Retornar ao projeto", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Você já concluiu esta pesquisa.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Pesquisa enviada", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "<PERSON><PERSON><PERSON> por sua resposta!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "A descrição da contribuição deve ser inferior a {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "O corpo da ideia deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "O título da contribuição deve ser inferior a {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "O título da contribuição deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Selecione pelo menos um copatrocinador", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "A descrição da ideia deve ser inferior a {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "A descrição da ideia deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Por favor, forneça uma descrição", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "O título da ideia deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "O título da ideia deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "A descrição da iniciativa deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "A descrição da iniciativa deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "O título da iniciativa deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "O título da iniciativa deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "A descrição da ideia deve ser inferior a {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "A descrição da ideia deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "O título da ideia deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "O título da edição deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_number_required": "Este campo é obrigatório, digite um número válido", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "A descrição da ideia deve ser inferior a {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "A descrição da opção deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "O título da ideia deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "O título da opção deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Por favor selecione pelo menos uma etiqueta", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "A descrição da petição deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "A descrição da petição deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "O título da petição deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "O título da petição deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "A descrição da ideia deve ser inferior a {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "A descrição da ideia deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "O título do projeto deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "O título do projeto deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "A descrição da proposta deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "A descrição da proposta deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "O título da proposta deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "O título da proposta deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Por favor, introduza um número", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Por favor, introduza um número", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "A descrição da ideia deve ser inferior a {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "A descrição da ideia deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "O título da pergunta deve ter menos de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "O título da opção deve ter mais de {limit} caracteres", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Por favor, forneça um título", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "A descrição da contribuição deve ser inferior a 80 caracteres", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "A descrição da contribuição deve ter ao menos 30 caracteres", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "O título da contribuição deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "O título da contribuição deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "A descrição da ideia deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "A descrição da ideia deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Por favor, forneça um título", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "O título da ideia deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "O título da ideia deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Você pode ter usado uma ou mais palavras que são consideradas impóprias por {guidelinesLink}. Por favor, altere o seu texto para remover quaisquer obscenidades que possam estar presentes.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "A descrição da iniciativa deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "A descrição da iniciativa deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "O título da iniciativa deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "O título da iniciativa deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "A descrição da ideia deve ser inferior a 80 caracteres", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "A descrição da ideia deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "O título da ideia deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "O título da ideia deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "A descrição da opção deve ser inferior a 80 caracteres", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "A descrição da proposta deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "O título da proposta deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "O título da proposta deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "A descrição da petição deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "A descrição da petição deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "O título da petição deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "O título da petição deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "A descrição do projeto deve ser inferior a 80 caracteres", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "A descrição do projeto deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "O título do projeto deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "O título da proposta deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "A descrição da proposta deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "A descrição da proposta deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "O título da proposta deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "O título da proposta deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Por favor, forneça uma descrição", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "A descrição da ideia deve ser inferior a 80 caracteres", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "A descrição da pergunta deve ter pelo menos 30 caracteres", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "O título da pergunta deve ter menos de 80 caracteres", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "O título da pergunta deve ter pelo menos 10 caracteres", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, eu quero ir embora", "app.containers.IdeasNewPage.contributionMetaTitle1": "Adicionar nova contribuição ao projeto | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON><PERSON> pesquisa", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Publique um envio e participe da conversa na plataforma de participação de {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Adicionar nova ideia ao projeto | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Adicionar nova iniciativa ao projeto | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Adicionar novo problema ao projeto | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Você tem certeza de que quer ir embora?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Suas respostas de rascunho foram salvas de forma privada e você pode voltar para completá-las mais tarde.", "app.containers.IdeasNewPage.leaveSurvey": "Pesquisa de licença", "app.containers.IdeasNewPage.leaveSurveyText": "As suas respostas não serão salvas.", "app.containers.IdeasNewPage.optionMetaTitle1": "Adicionar nova opção ao projeto | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Adicionar nova petição ao projeto | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Adicionar novo projeto ao projeto | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Adicionar nova proposta ao projeto | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Adicionar nova pergunta ao projeto | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Aceitar o convite", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Convite de copatrocínio", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Co-patrocinadores", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Você foi convidado a se tornar um copatrocinador.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "<PERSON><PERSON><PERSON> ace<PERSON>", "app.containers.IdeasShow.Cosponsorship.pending": "pendente", "app.containers.IdeasShow.MetaInformation.attachments": "Anexos", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} en {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Status atual", "app.containers.IdeasShow.MetaInformation.location": "Localização", "app.containers.IdeasShow.MetaInformation.postedBy": "Postado por", "app.containers.IdeasShow.MetaInformation.similar": "<PERSON><PERSON><PERSON> se<PERSON>", "app.containers.IdeasShow.MetaInformation.topics": "Tópicos", "app.containers.IdeasShow.commentCTA": "Adicionar um comentário", "app.containers.IdeasShow.contributionEmailSharingBody": "Apoie esta contribuição '{postTitle}' em {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Apoie esta contribuição: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "<PERSON><PERSON><PERSON> por enviar sua contribuição!", "app.containers.IdeasShow.contributionTwitterMessage": "Apoie esta contribuição: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Apoie esta contribuição: {postTitle}", "app.containers.IdeasShow.currentStatus": "Status atual", "app.containers.IdeasShow.deletedUser": "autor desconhe<PERSON>o", "app.containers.IdeasShow.ideaEmailSharingBody": "Apoie minha ideia '{ideaTitle}' em {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "A<PERSON><PERSON> a minha ideia: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Apoie esta ideia: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Apoie esta ideia: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Apoie este problema: {postTitle}", "app.containers.IdeasShow.imported": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.importedTooltip": "Este {inputTerm} foi coletado offline e carregado automaticamente na plataforma.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Apoie esta iniciativa '{ideaTitle}' em {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Apoie esta iniciativa: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "<PERSON><PERSON><PERSON> por enviar sua iniciativa!", "app.containers.IdeasShow.initiativeTwitterMessage": "Apoie esta iniciativa: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Apoie esta iniciativa: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Apoie este problema '{postTitle}' em {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Apoie este problema: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "<PERSON><PERSON><PERSON> por enviar seu problema!", "app.containers.IdeasShow.issueTwitterMessage": "Apoie este problema: {postTitle}", "app.containers.IdeasShow.metaTitle": "Entrada: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Apoie esta opção '{postTitle}' em {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Apoie esta opção: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Sua opção foi publicada com sucesso!", "app.containers.IdeasShow.optionTwitterMessage": "Apoie esta opção: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Apoie esta opção: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Apoie esta petição '{ideaTitle}' em {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "A<PERSON><PERSON> esta petição: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "<PERSON><PERSON><PERSON> por enviar sua petição!", "app.containers.IdeasShow.petitionTwitterMessage": "A<PERSON><PERSON> esta petição: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "A<PERSON><PERSON> esta petição: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Apoie este projeto '{postTitle}' em {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "A<PERSON>ie este projeto: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON><PERSON> por enviar seu projeto!", "app.containers.IdeasShow.projectTwitterMessage": "A<PERSON>ie este projeto: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "A<PERSON>ie este projeto: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Apoie esta proposta '{ideaTitle}' em {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "A<PERSON><PERSON> esta proposta: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "<PERSON><PERSON><PERSON> por enviar sua proposta!", "app.containers.IdeasShow.proposalTwitterMessage": "A<PERSON><PERSON> esta proposta: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "A<PERSON><PERSON> esta proposta: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Tempo restante para votar:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} de {votingThreshold} votos necessários", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Cancelar voto", "app.containers.IdeasShow.proposals.VoteControl.days": "dias", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "Nossas diretrizes", "app.containers.IdeasShow.proposals.VoteControl.hours": "horas", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status e votos", "app.containers.IdeasShow.proposals.VoteControl.minutes": "minutos", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Mais informaç<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.vote": "Votação", "app.containers.IdeasShow.proposals.VoteControl.voted": "Votado", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Você será notificado quando essa iniciativa passar para a próxima etapa. {x, plural, =0 {Há {xDays} à esquerda.} one {Há {xDays} à esquerda.} other {Há {xDays} à esquerda.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Seu voto foi enviado!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Infelizmente, você não pode votar nessa proposta. Leia o motivo em {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {menos de um dia} one {um dia} other {# dias}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {nenhum voto} one {1 voto} other {# votos}}", "app.containers.IdeasShow.questionEmailSharingBody": "Participe da discussão sobre esta pergunta '{postTitle}' em {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Participe da <PERSON>: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Sua pergunta foi publicada com sucesso!", "app.containers.IdeasShow.questionTwitterMessage": "Participe da <PERSON>: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Participe da <PERSON>: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Porque deseja denunciar isto como spam?", "app.containers.IdeasShow.share": "Compartilhar", "app.containers.IdeasShow.sharingModalSubtitle": "Alcance mais pessoas e faça ouvir a sua voz.", "app.containers.IdeasShow.sharingModalTitle": "<PERSON><PERSON><PERSON> por enviar sua ideia!", "app.containers.Navbar.completeOnboarding": "Integração completa", "app.containers.Navbar.completeProfile": "<PERSON><PERSON>l completo", "app.containers.Navbar.confirmEmail2": "Confirmar e-mail", "app.containers.Navbar.unverified": "Não verificado", "app.containers.Navbar.verified": "Verificado", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON> de se<PERSON>ir", "app.containers.NewAuthModal.beforeYouParticipate": "Antes de participar", "app.containers.NewAuthModal.completeYourProfile": "Preencher o seu perfil", "app.containers.NewAuthModal.confirmYourEmail": "Confirme o seu e-mail", "app.containers.NewAuthModal.logIn": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Rever os termos abaixo para continuar.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Preencha o seu perfil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Voltar às opções de início de sessão", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Não tem uma conta? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Inscrever-se", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "O código deve ter 4 dígitos.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Continuar com FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Nenhum método de autenticação está activado nesta plataforma.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON>o continuar, concorda em receber e-mails desta plataforma. <PERSON><PERSON> as mensagens de correio electrónico que deseja receber na página 'Minhas configurações'.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Email", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Forneça um endereço de correio electrónico no formato correcto, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Forneça um endereço de correio electrónico", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Introduza o seu endereço de correio electrónico para continuar.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Esqueceu-se da palavra-passe?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Inicie sessão na sua conta: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Por favor introduza a sua palavra-passe", "app.containers.NewAuthModal.steps.Password.password": "<PERSON><PERSON>", "app.containers.NewAuthModal.steps.Password.rememberMe": "Lembra-te de mim", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Não seleccionar se estiver a utilizar um computador público", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON> feito", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Agora continue a sua participação.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "A sua identidade foi verificada. Agora é um membro de pleno direito da comunidade nesta plataforma.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Já está verificado!", "app.containers.NewAuthModal.steps.close": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Em quê você está interessado?", "app.containers.NewAuthModal.youCantParticipate": "Você não pode participar", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {no unviewed notifications} um {1 unviewed notification} other {# unviewed notifications}}", "app.containers.NotificationMenu.adminRightsReceived": "Você é agora um administrador da plataforma", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "O seu comentário em \"{postTitle}\" foi eliminado por um administrador porque\n      {reasonCode, select, irrelevant {é irrelevante} inappropriate {o seu conteúdo é inadequado} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} aceitou seu convite de co-patrocínio", "app.containers.NotificationMenu.deletedUser": "Autor <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.error": "Não consegui carregar notificações", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} comentou dentro de uma entrada que lhe foi atribuída", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} comentou dentro de uma entrada que também comentou dentro", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} comentou dentro de uma entrada num projeto que gerencia", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} comentou dentro de uma entrada não atribuída num projeto não gerenciado", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} comentou o seu comentário interno", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} convid<PERSON> você a co-patrocinar uma contribuição", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} convidou você para co-patrocinar uma ideia", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} convidou você para co-patrocinar uma proposta", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} convid<PERSON> você a co-patrocinar uma questão", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} convid<PERSON> você a co-patrocinar uma opção", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} convid<PERSON> você a co-patrocinar uma petição", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} convidou você a co-patrocinar um projeto", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} convid<PERSON> você a co-patrocinar uma proposta", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} convid<PERSON> você a co-patrocinar uma pergunta", "app.containers.NotificationMenu.loadMore": "<PERSON><PERSON><PERSON> mais...", "app.containers.NotificationMenu.loading": "Carregando notificações…", "app.containers.NotificationMenu.mentionInComment": "{name} mencionou você em um comentário", "app.containers.NotificationMenu.mentionInInternalComment": "{name} mencionou-o num comentário interno", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} mencionou-o numa atualização oficial", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Você não enviou sua pesquisa", "app.containers.NotificationMenu.noNotifications": "Você ainda não tem nenhuma notificação", "app.containers.NotificationMenu.notificationsLabel": "Notificações", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} deu uma atualização oficial sobre uma contribuição que você segue", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} deu uma atualização oficial sobre uma ideia que você segue", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} deu uma atualização oficial sobre uma iniciativa que você segue", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} deu uma atualização oficial sobre um problema que você segue", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} deu uma atualização oficial sobre uma opção que você segue", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} deu uma atualização oficial sobre uma petição que você segue", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} deu uma atualização oficial sobre um projeto que você segue", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} deu uma atualização oficial sobre uma proposta que você segue", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} deu uma atualização oficial sobre uma questão que você segue", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} foi atribuído a você", "app.containers.NotificationMenu.projectModerationRightsReceived": "Você é agora um gerente de projeto de {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} entrou numa nova fase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} Vai entrar numa nova fase em {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Um novo projeto foi publicado", "app.containers.NotificationMenu.projectReviewRequest": "{name} solicitou aprovação para publicar o projeto \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} aprovado \"{projectTitle}\" para publicação", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} status mudou para {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} estourou o limite de votação!", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} aceitou o seu convite", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} comentou uma contribuição que segue", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} comentou uma ideia que segue", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} comentou uma iniciativa que segue", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} comentou um assunto que segue", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} comentou uma opção que segue", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} comentou em uma petição que você segue", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} comentou um projeto que segue", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} comentou sobre uma proposta que você segue", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} comentou uma pergunta que segue", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} reportou \"{postTitle}\" como spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reagiu ao seu coment<PERSON>rio", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} reportou um comentário sobre \"{postTitle}\" como spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Você não enviou seus votos", "app.containers.NotificationMenu.votingBasketSubmitted": "Você votou com sucesso", "app.containers.NotificationMenu.votingLastChance": "Última oportunidade para votar em {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} resultados da votação revelados", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} atribu<PERSON>do {postTitle} a você", "app.containers.PasswordRecovery.emailError": "Isto não parece um e-mail válido.", "app.containers.PasswordRecovery.emailLabel": "E-mail", "app.containers.PasswordRecovery.emailPlaceholder": "O meu endereço de e-mail", "app.containers.PasswordRecovery.helmetDescription": "Página para redifinir a sua senha", "app.containers.PasswordRecovery.helmetTitle": "<PERSON><PERSON><PERSON> sua senha", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Se este endereço de e-mail estiver registado na plataforma, foi enviado um link para redefinir a senha.", "app.containers.PasswordRecovery.resetPassword": "Enviar um link para resetar a senha", "app.containers.PasswordRecovery.submitError": "Não conseguimos encontrar uma conta ligada neste e-mail. Você pode tentar se cadastrar", "app.containers.PasswordRecovery.subtitle": "Para onde podemos enviar um link para escolher uma nova senha?", "app.containers.PasswordRecovery.title": "Redefinir a senha", "app.containers.PasswordReset.helmetDescription": "Página para redifinir a sua senha", "app.containers.PasswordReset.helmetTitle": "<PERSON><PERSON><PERSON> sua senha", "app.containers.PasswordReset.login": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordError": "A sua senha deve conter pelo menos 8 caracteres", "app.containers.PasswordReset.passwordLabel": "<PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "Nova senha", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "A sua senha foi atualizada com sucesso.", "app.containers.PasswordReset.pleaseLogInMessage": "Por favor, inicie sessão com a sua nova senha.", "app.containers.PasswordReset.requestNewPasswordReset": "Solicite a definição de uma nova senha", "app.containers.PasswordReset.submitError": "\nAlgo deu errado. Por favor, tente novamente mais tarde.", "app.containers.PasswordReset.title": "<PERSON><PERSON><PERSON> sua senha", "app.containers.PasswordReset.updatePassword": "Confirmar nova senha", "app.containers.ProjectFolderCards.allProjects": "Todos os projetos", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} atualmente está trabalhando em", "app.containers.ProjectFolderShowPage.editFolder": "Editar pasta", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informações sobre este projeto", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Ler mais", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON>er menos", "app.containers.ProjectFolderShowPage.share": "Comp<PERSON><PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Documento", "app.containers.Projects.PollForm.formCompleted": "Obrigado! Seu envio foi recebido.", "app.containers.Projects.PollForm.maxOptions": "máximo. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Você já respondeu a esta enquete.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Esta pesquisa só pode ser realizada quando esta fase estiver ativa.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Infelizmente, você não tem permissão de fazer esta sondagem.", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Atualmente, é impossível fazer esta votação.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "A pesquisa não está mais disponível, devido que o projeto não está mais ativo.", "app.containers.Projects.PollForm.sendAnswer": "Enviar", "app.containers.Projects.a11y_phase": "Fase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Visão geral das fases", "app.containers.Projects.a11y_titleInputs": "<PERSON><PERSON> as entradas enviadas para este projeto", "app.containers.Projects.a11y_titleInputsPhase": "<PERSON><PERSON> as entradas enviadas para esta fase", "app.containers.Projects.accessRights": "Direitos de acesso", "app.containers.Projects.addedToBasket": "Adicionado à sua cesta", "app.containers.Projects.allocateBudget": "Distribua o seu orçamento", "app.containers.Projects.archived": "Arquivado", "app.containers.Projects.basketSubmitted": "Sua solicitação foi submetida!", "app.containers.Projects.contributions": "Propostas", "app.containers.Projects.createANewPhase": "Criar uma nova fase", "app.containers.Projects.currentPhase": "Fase atual", "app.containers.Projects.document": "Documento", "app.containers.Projects.editProject": "<PERSON>ar projeto", "app.containers.Projects.emailSharingBody": "O que você acha desta idéia? Vote nela e compartilhe a discussão em {initiativeUrl} para fazer ouvir a sua voz!", "app.containers.Projects.emailSharingSubject": "A<PERSON><PERSON> a minha proposta: {initiativeTitle}.", "app.containers.Projects.endedOn": "Terminado em {date}", "app.containers.Projects.events": "Eventos", "app.containers.Projects.header": "Projetos", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informação", "app.containers.Projects.initiatives": "Iniciativas", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Revisar o documento", "app.containers.Projects.invisibleTitlePhaseAbout": "Sobre esta fase", "app.containers.Projects.invisibleTitlePoll": "Faça a pesquisa", "app.containers.Projects.invisibleTitleSurvey": "Faça a pesquisa", "app.containers.Projects.issues": "Problemas", "app.containers.Projects.liveDataMessage": "Você está visualizando dados em tempo real. As contagens de participantes são atualizadas continuamente para os administradores. Observe que os usuários comuns veem dados em cache, o que pode resultar em pequenas diferenças nos números.", "app.containers.Projects.location": "Localização:", "app.containers.Projects.manageBasket": "Administrar pedido", "app.containers.Projects.meetMinBudgetRequirement": "Cump<PERSON>r o orçamento mínimo para submeter a sua cesta.", "app.containers.Projects.meetMinSelectionRequirement": "Conheça a seleção necessária para submeter a sua cesta.", "app.containers.Projects.metaTitle1": "Projeto: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Orçamento mínimo <PERSON>", "app.containers.Projects.myBasket": "Cesta", "app.containers.Projects.navPoll": "Pesquisa", "app.containers.Projects.navSurvey": "Pesquisa", "app.containers.Projects.newPhase": "Nova fase", "app.containers.Projects.nextPhase": "Próxima fase", "app.containers.Projects.noEndDate": "Sem data final", "app.containers.Projects.noItems": "Você ainda não selecionou nenhum item", "app.containers.Projects.noPastEvents": "Nenhum evento passado a exibir", "app.containers.Projects.noPhaseSelected": "Nenhuma fase selecionada", "app.containers.Projects.noUpcomingOrOngoingEvents": "Nenhum evento futuro está agendado no momento.", "app.containers.Projects.offlineVotersTooltip": "Esse número não reflete nenhuma contagem de eleitores off-line.", "app.containers.Projects.options": "Opções", "app.containers.Projects.participants": "Participantes", "app.containers.Projects.participantsTooltip4": "Esse número também reflete os envios anônimos de pesquisas. Os envios anônimos de pesquisas são possíveis se as pesquisas forem abertas a todos (consulte a guia {accessRightsLink} para este projeto).", "app.containers.Projects.pastEvents": "Eventos passados", "app.containers.Projects.petitions": "Petições", "app.containers.Projects.phases": "Fases", "app.containers.Projects.previousPhase": "Fase anterior", "app.containers.Projects.project": "Projeto", "app.containers.Projects.projectTwitterMessage": "Faça sua voz ser ouvida! Participe de {projectName} | {orgName}", "app.containers.Projects.projects": "Projetos", "app.containers.Projects.proposals": "Propostas", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON>r menos", "app.containers.Projects.readMore": "Ler mais", "app.containers.Projects.removeItem": "Remover item", "app.containers.Projects.requiredSelection": "Seleção necessária", "app.containers.Projects.reviewDocument": "Revisar o documento", "app.containers.Projects.seeTheContributions": "Ver as contribuiç<PERSON><PERSON>", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON> as ideias", "app.containers.Projects.seeTheInitiatives": "<PERSON><PERSON><PERSON> as iniciativas", "app.containers.Projects.seeTheIssues": "Ver os problemas", "app.containers.Projects.seeTheOptions": "<PERSON><PERSON> as opçõ<PERSON>", "app.containers.Projects.seeThePetitions": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheProjects": "Ver os projetos", "app.containers.Projects.seeTheProposals": "<PERSON><PERSON><PERSON> as propostas", "app.containers.Projects.seeTheQuestions": "Ver as perguntas", "app.containers.Projects.seeUpcomingEvents": "Ver eventos futuros", "app.containers.Projects.share": "Compartilhar", "app.containers.Projects.shareThisProject": "Compartilhe este projeto", "app.containers.Projects.submitMyBasket": "Enviar cesta", "app.containers.Projects.survey": "Pesquisa", "app.containers.Projects.takeThePoll": "Faça a pesquisa", "app.containers.Projects.takeTheSurvey": "Faça a pesquisa", "app.containers.Projects.timeline": "Linha do tempo", "app.containers.Projects.upcomingAndOngoingEvents": "Eventos próximos e em curso", "app.containers.Projects.upcomingEvents": "Próximos eventos", "app.containers.Projects.whatsAppMessage": "{projectName} | a partir da plataforma de participação de {orgName}", "app.containers.Projects.yourBudget": "Orçamento total", "app.containers.ProjectsIndexPage.metaDescription": "Explore todos os projetos em andamento de {orgName} para entender como você pode participar.\nVenha discutir os projetos locais mais importantes para você.", "app.containers.ProjectsIndexPage.metaTitle1": "Projetos | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projetos", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Eu quero ser voluntário.", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Por favor {signInLink} ou {signUpLink} primeiro para ser voluntário para esta atividade", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "No momento, a participação não está aberta para essa atividade.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "conecte-se", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "cadastrar-se", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Descadastrar-se", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nenhum voluntá<PERSON>} one {# voluntário} other {# voluntários}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Aviso: A pesquisa incorporada pode ter problemas de acessibilidade para usuários de leitores de tela. Se você tiver algum problema, entre em contato com o administrador da plataforma para receber um link para a pesquisa da plataforma original. Como alternativa, você pode solicitar outras maneiras de preencher a pesquisa.", "app.containers.ProjectsShowPage.process.survey.survey": "Pesquisa", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Para saber se você pode participar desta pesquisa, por favor {logInLink} à plataforma primeiro.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Esta pesquisa só pode ser realizada quando esta fase da linha do tempo estiver ativa.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Por favor {completeRegistrationLink} para fazer o inquérito.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Esta pesquisa não está ativada no momento", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "A realização desta pesquisa requer a verificação so seu usuário. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "A pesquisa não está mais disponível, pois esse projeto não está mais ativo.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "registo completo", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "iniciar <PERSON>", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "crie seu login", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verifique sua conta agora.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Apenas certos usuários podem participar desta pesquisa. <PERSON>r favor {signUpLink} ou {logInLink} primeiro.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Esta pesquisa só pode ser realizada quando esta fase estiver ativa.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Por favor, {completeRegistrationLink} para revisar o documento.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Infelizmente, você não tem permissão para revisar este documento.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "A revisão deste documento requer a verificação da sua conta. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "O documento não está mais disponível, uma vez que este projeto não está mais ativo.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 off-line)} other {(incl. # off-line)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 escolha} other {# escolhas}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "A porcentagem de participantes que escolheram essa opção.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "A porcentagem do total de votos que essa opção recebeu.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Custo:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "<PERSON><PERSON> mais", "app.containers.ReactionControl.a11y_likesDislikes": "Total de curtidas: {likesCount}, total de não curtidas: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Você cancelou sua antipatia por essa entrada com sucesso.", "app.containers.ReactionControl.cancelLikeSuccess": "Você cancelou seu like para essa entrada com sucesso.", "app.containers.ReactionControl.dislikeSuccess": "Você não gostou dessa entrada com sucesso.", "app.containers.ReactionControl.likeSuccess": "Você gostou dessa entrada com sucesso.", "app.containers.ReactionControl.reactionErrorSubTitle": "<PERSON>do a um erro, não foi possível registar a sua reação. Por favor, tente novamente dentro de alguns minutos.", "app.containers.ReactionControl.reactionSuccessTitle": "A sua reação foi registada com sucesso!", "app.containers.ReactionControl.vote": "Votação", "app.containers.ReactionControl.voted": "Votado", "app.containers.SearchInput.a11y_cancelledPostingComment": "Comentário de postagem cancelado.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} os coment<PERSON>rios foram carregados.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# os eventos foram carregados} one {# o evento foi carregado} other {# os eventos foram carregados}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# os resultados foram carregados} one {# o resultado foi carregado} other {# os resultados foram carregados}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# os resultados da pesquisa foram carregados} one {# o resultado da pesquisa foi carregado} other {# os resultados da pesquisa foram carregados}}.", "app.containers.SearchInput.removeSearchTerm": "Remover termo de pesquisa", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "Termo de pesquisa: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect é a solução proposta pelo Estado francês para garantir e simplificar a assinatura de mais de 700 serviços online.", "app.containers.SignIn.or": "Ou", "app.containers.SignIn.signInError": "A informação fornecida não está correcta. Clique em 'Senha esquecida' para redefinir sua senha.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Utilize FranceConnect para iniciar sessão, inscrever-se ou verificar a sua conta.", "app.containers.SignIn.whatIsFranceConnect": "O que é a France Connect?", "app.containers.SignUp.adminOptions2": "Para administradores e gerentes de projeto", "app.containers.SignUp.backToSignUpOptions": "Voltar às opções de cadastro", "app.containers.SignUp.continue": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.emailConsent": "Ao inscrever-se, você concorda em receber e-mails desta plataforma. Você pode selecionar quais e-mails deseja receber de suas configurações de usuário.", "app.containers.SignUp.emptyFirstNameError": "Digite o seu primeiro nome", "app.containers.SignUp.emptyLastNameError": "Digite o seu sobrenome", "app.containers.SignUp.firstNamesLabel": "Primeiro nome", "app.containers.SignUp.goToLogIn": "Já tem uma conta? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Eu li e concordo com {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Eu li e concordo com {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Aceito que os dados serão utilizados em mitgestalten.wien.gv.at. Mais informações podem ser encontradas em {link}.", "app.containers.SignUp.invitationErrorText": "O seu convite expirou ou já foi utilizado. Se já utilizou a ligação de convite para criar uma conta, tente iniciar sessão. <PERSON>aso contrário, inscreva-se para criar uma nova conta.", "app.containers.SignUp.lastNameLabel": "Sobrenome", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Siga suas áreas de foco para ser notificado sobre elas:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Siga os seus tópicos favoritos para ser notificado sobre eles:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "<PERSON><PERSON> preferências", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "<PERSON><PERSON> por enquanto", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Aceitar a nossa política de privacidade para prosseguir", "app.containers.SignUp.signUp2": "<PERSON>rie seu login", "app.containers.SignUp.skip": "<PERSON><PERSON>", "app.containers.SignUp.tacError": "Aceitar os nossos termos e condições é necessário para prosseguir", "app.containers.SignUp.thePrivacyPolicy": "a política de privacidade", "app.containers.SignUp.theTermsAndConditions": "os termos e condições", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {Parece que você tentou se inscrever sem completar o processo. Clique em Log In no lugar, usando as credenciais escolhidas durante a tentativa anterior.} other {Algo deu errado. Por favor, tente novamente mais tarde.}}", "app.containers.SignUp.viennaConsentEmail": "E-mail", "app.containers.SignUp.viennaConsentFirstName": "Primeiro nome", "app.containers.SignUp.viennaConsentFooter": "Você pode alterar as suas informações de perfil após a sua inscrição. Se já tiver uma conta com o mesmo endereço electrónico em mitgestalten.wien.gv.at, esta será ligada à sua conta corrente.", "app.containers.SignUp.viennaConsentHeader": "Serão transmitidos os seguintes dados:", "app.containers.SignUp.viennaConsentLastName": "Sobrenome", "app.containers.SignUp.viennaConsentUserName": "Nome do usuário", "app.containers.SignUp.viennaDataProtection": "a política de privacidade de viena", "app.containers.SiteMap.contributions": "Propostas", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Problemas", "app.containers.SiteMap.options": "Opções", "app.containers.SiteMap.projects": "Projetos", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Relat<PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Sucesso", "app.containers.SpamReport.inappropriate": "É inapropriado ou ofensivo", "app.containers.SpamReport.messageError": "Ocorreu um erro ao enviar o formulário, por favor tente novamente.", "app.containers.SpamReport.messageSuccess": "O seu relatório foi enviado com sucesso", "app.containers.SpamReport.other": "Outro motivo", "app.containers.SpamReport.otherReasonPlaceholder": "Descrição", "app.containers.SpamReport.wrong_content": "Isto não pertence aqui.", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Remover imagem de perfil", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Seus votos nas propostas que ainda estiverem abertas para votação serão deletados. As votações em propostas cujo período de votação tenha encerrado não serão eliminadas.", "app.containers.UsersEditPage.addPassword": "Adicionar pala<PERSON>-passe", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Participar em projetos para usuários verificados.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verifique o seu usuário", "app.containers.UsersEditPage.bio": "Sobre você", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Você não pode editar este campo porque ele contém informações confirmadas", "app.containers.UsersEditPage.buttonSuccessLabel": "Sucesso", "app.containers.UsersEditPage.cancel": "Cancelado", "app.containers.UsersEditPage.changeEmail": "Alterar o correio electrónico", "app.containers.UsersEditPage.changePassword2": "Alterar a palavra-passe", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Por favor clique aqui para atualizar a sua verificação.", "app.containers.UsersEditPage.conditionsLinkText": "nossas condições", "app.containers.UsersEditPage.contactUs": "Outra razão para sair? {feedbackLink} e talvez nós possamos ajudar.", "app.containers.UsersEditPage.deleteAccountSubtext": "Lamentamos vê-lo partir.", "app.containers.UsersEditPage.deleteMyAccount": "Excluir a minha conta", "app.containers.UsersEditPage.deleteYourAccount": "Excluir minha conta", "app.containers.UsersEditPage.deletionSection": "Excluir minha conta", "app.containers.UsersEditPage.deletionSubtitle": "Esta ação não pode ser desfeita. O conteúdo que você publicou na plataforma vai ficar anônimo. Se desejar apagar todo o seu conteúdo, pode contactar-<NAME_EMAIL>.", "app.containers.UsersEditPage.email": "E-mail", "app.containers.UsersEditPage.emailEmptyError": "Forneça um endereço de e-mail", "app.containers.UsersEditPage.emailInvalidError": "Introduzir um endereço de correio electrônico no formato correto, como <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Nos informe", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Primeiro nome", "app.containers.UsersEditPage.firstNamesEmptyError": "Fornecer um nome", "app.containers.UsersEditPage.h1": "Informações da sua conta", "app.containers.UsersEditPage.h1sub": "Edite as informações da sua conta", "app.containers.UsersEditPage.image": "<PERSON><PERSON>", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Clique para seleccionar uma imagem de perfil (máx. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "<PERSON><PERSON> as configurações para o seu perfil", "app.containers.UsersEditPage.language": "Idioma", "app.containers.UsersEditPage.lastName": "Sobrenome", "app.containers.UsersEditPage.lastNameEmptyError": "Fornecer um sobrenome", "app.containers.UsersEditPage.loading": "Carregando…", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Pode alterar o seu e-mail ou palavra-passe aqui.", "app.containers.UsersEditPage.loginCredentialsTitle": "Credenciais de início de sessão", "app.containers.UsersEditPage.messageError": "Não pudimos salvar as suas alteraçoes. Tente novamente mais tarde <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "O seu perfil foi salvo.", "app.containers.UsersEditPage.metaDescription": "Esta é a página de configurações de perfil de {firstName} {lastName} na plataforma de participação online de {tenantName}. Aqui você pode verificar seu usuário, editar as informações de sua conta, excluir sua conta e editar suas preferências de e-mail.", "app.containers.UsersEditPage.metaTitle1": "Página de configurações de perfil de {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Uma vez que você clique neste botão, não teremos como restaurar a sua conta.", "app.containers.UsersEditPage.noNameWarning2": "No momento, seu nome é exibido na plataforma como: \"{displayName}\" porque você não digitou seu nome. Esse é um nome gerado automaticamente. Se você quiser alterá-lo, digite seu nome abaixo.", "app.containers.UsersEditPage.notificationsSubTitle": "Que tipos de notificações você quer receber por e-mali? ", "app.containers.UsersEditPage.notificationsTitle": "Notificações por e-mail", "app.containers.UsersEditPage.password": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON>rie uma senha com, pelo menos, {minimumPasswordLength} caracteres", "app.containers.UsersEditPage.passwordAddSection": "Adicionar uma palavra-passe", "app.containers.UsersEditPage.passwordAddSubtitle2": "Defina uma palavra-passe e inicie facilmente sessão na plataforma, sem ter de confirmar sempre o seu e-mail.", "app.containers.UsersEditPage.passwordChangeSection": "Altere a sua senha", "app.containers.UsersEditPage.passwordChangeSubtitle": "Confirme sua senha atual e altere para a nova senha.", "app.containers.UsersEditPage.privacyReasons": "Se você está preocupado com sua privacidade, você pode ler {conditionsLink}.", "app.containers.UsersEditPage.processing": "Enviado", "app.containers.UsersEditPage.provideFirstNameIfLastName": "O primeiro nome é obrigatório ao fornecer o sobrenome", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON><PERSON> de ir embora", "app.containers.UsersEditPage.submit": "<PERSON><PERSON>", "app.containers.UsersEditPage.tooManyEmails": "A receber muitos e-mails? Você pode gerenciar suas preferências de e-mail em suas configurações de perfil.", "app.containers.UsersEditPage.updateverification": "mudou a sua informação oficial? {reverifyButton}", "app.containers.UsersEditPage.user": "Quando você deseja receber email para notificá-lo?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Você pode participar de projetos que são acessíveis apenas aos cidadãos verificados.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Você é um usuário confirmado", "app.containers.UsersEditPage.verifyNow": "Verifique agora", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Faça o download de suas respostas (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {sem gostos} one {1 gosto} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Publicação em que este comentário foi publicado:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Comentários ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Editar meu perfil.", "app.containers.UsersShowPage.emptyInfoText": "Você não está seguindo nenhum item do filtro especificado acima.", "app.containers.UsersShowPage.eventsWithCount": "Eventos ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Seguindo ({followingCount})", "app.containers.UsersShowPage.inputs": "Entradas", "app.containers.UsersShowPage.invisibleTitlePostsList": "<PERSON><PERSON> as publicações enviadas por este participante", "app.containers.UsersShowPage.invisibleTitleUserComments": "Todos os comentários postados por este usuario", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON><PERSON> mais", "app.containers.UsersShowPage.loadMoreComments": "<PERSON><PERSON><PERSON> mais come<PERSON>", "app.containers.UsersShowPage.loadingComments": "Carregando comentários...", "app.containers.UsersShowPage.loadingEvents": "Carregando eventos...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON><PERSON> {date}", "app.containers.UsersShowPage.metaTitle1": "P<PERSON>gina de perfil de {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Este usuário ainda não fez nenhum comentário.", "app.containers.UsersShowPage.noCommentsForYou": "Não há comentários.", "app.containers.UsersShowPage.noEventsForUser": "Você ainda não participou de nenhum evento.", "app.containers.UsersShowPage.postsWithCount": "Publicações ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Pasta de projetos", "app.containers.UsersShowPage.projects": "Projetos", "app.containers.UsersShowPage.proposals": "Propostas", "app.containers.UsersShowPage.seePost": "Ver publicação", "app.containers.UsersShowPage.surveyResponses": "Respostas ({responses})", "app.containers.UsersShowPage.topics": "Tópicos", "app.containers.UsersShowPage.tryAgain": "Ocorreu um erro, por favor tente novamente mais tarde.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Esta é a página de perfil de {firstName} {lastName} na plataforma de participação online de {orgName}. Aqui está uma visão geral de todas as suas publicações.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "Ocorreu um erro", "app.containers.admin.ContentBuilder.default": "<PERSON><PERSON><PERSON>", "app.containers.admin.ContentBuilder.imageTextCards": "Cartões de imagem e texto", "app.containers.admin.ContentBuilder.infoWithAccordions": "Informações e acordeões", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 coluna", "app.containers.admin.ContentBuilder.projectDescription": "Descrição do projeto", "app.containers.app.navbar.admin": "Administrar", "app.containers.app.navbar.allProjects": "Todos os projetos", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Fechar menu de navegação", "app.containers.app.navbar.editProfile": "Minhas configurações", "app.containers.app.navbar.fullMobileNavigation": "<PERSON><PERSON><PERSON> completo", "app.containers.app.navbar.logIn": "<PERSON><PERSON><PERSON>", "app.containers.app.navbar.logoImgAltText": "{orgName} Iní<PERSON>", "app.containers.app.navbar.myProfile": "O meu perfil", "app.containers.app.navbar.search": "<PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "Mostrar menu completo", "app.containers.app.navbar.signOut": "<PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Ocorreu um erro durante o carregamento dos eventos. Por favor, tente recarregar a página.", "app.containers.eventspage.events": "Eventos", "app.containers.eventspage.eventsPageDescription": "Explore todas as entradas publicadas na plataforma de participação de {orgName}.", "app.containers.eventspage.eventsPageTitle1": "Eventos | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projetos", "app.containers.eventspage.noPastEvents": "Nenhum evento passado a exibir", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Nenhum evento futuro está agendado no momento.", "app.containers.eventspage.pastEvents": "Eventos passados", "app.containers.eventspage.upcomingAndOngoingEvents": "Eventos próximos e em curso", "app.containers.footer.accessibility-statement": "Declaração de Acessibilidade", "app.containers.footer.ariaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.cookie-policy": "Política de Cookies", "app.containers.footer.cookieSettings": "Configurações de cookies", "app.containers.footer.feedbackEmptyError": "O campo de opinião não pode ficar vazio.", "app.containers.footer.poweredBy": "<PERSON><PERSON><PERSON> por", "app.containers.footer.privacy-policy": "Política de Privacidade", "app.containers.footer.siteMap": "Mapa do site", "app.containers.footer.terms-and-conditions": "Termos e condições", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, eu quero ir embora", "app.containers.ideaHeading.editForm": "<PERSON><PERSON>", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Você tem certeza de que quer ir embora?", "app.containers.ideaHeading.leaveIdeaForm": "Deixar formulário de ideias", "app.containers.ideaHeading.leaveIdeaText": "Suas respostas não serão salvas.", "app.containers.landing.cityProjects": "Projetos", "app.containers.landing.completeProfile": "Complete o seu perfil", "app.containers.landing.completeYourProfile": "{tenantN<PERSON>, select, Unicef {Be<PERSON>-vinda, {firstName}. Completar o seu perfil agora.} other {Bem-vindo, {firstName}. Completar o seu perfil agora.}}", "app.containers.landing.createAccount": "<PERSON>rie seu login", "app.containers.landing.defaultSignedInMessage": "{orgName} está te ouvindo. É a sua chance de fazer ouvir a sua voz!", "app.containers.landing.doItLater": "<PERSON><PERSON>i fazer mais tarde.", "app.containers.landing.new": "novo", "app.containers.landing.subtitleCity": "Bem vindo à plataforma de participação oficial de {orgName}", "app.containers.landing.titleCity": "Vamos construir o futuro de {orgName} juntos", "app.containers.landing.twitterMessage": "Votar por {ideaTitle} em", "app.containers.landing.upcomingEventsWidgetTitle": "Eventos próximos e em curso", "app.containers.landing.userDeletedSubtitle": "Você pode criar uma nova conta a qualquer momento ou {contactLink} nos dizer o que podemos melhorar.", "app.containers.landing.userDeletedSubtitleLinkText": "Deixe-nos uma mensagem", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "A sua conta foi excluida", "app.containers.landing.userDeletionFailed": "Ocorreu um erro ao apagar a sua conta, fomos notificados do problema e faremos o possivel para corrigi-lo. Por favor, tente novamente mais tarde.", "app.containers.landing.verifyNow": "Verifique agora", "app.containers.landing.verifyYourIdentity": "Confirmar que o seu usuário foi verificado", "app.containers.landing.viewAllEventsText": "Ver todos os eventos", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Voltar para a pasta", "app.errors.after_end_at": "A data de início ocorre após a data final", "app.errors.avatar_carrierwave_download_error": "Não foi possível fazer o download do arquivo avatar.", "app.errors.avatar_carrierwave_integrity_error": "O arquivo Avatar não é de um tipo permitido.", "app.errors.avatar_carrierwave_processing_error": "Não foi possível processar o avatar.", "app.errors.avatar_extension_blacklist_error": "A extensão do arquivo da imagem do avatar não é permitida. As extensões permitidas são: jpg, jpeg, gif e png.", "app.errors.avatar_extension_whitelist_error": "A extensão do arquivo da imagem do avatar não é permitida. As extensões permitidas são: jpg, jpeg, gif e png.", "app.errors.banner_cta_button_multiloc_blank": "Introduzir um texto.", "app.errors.banner_cta_button_url_blank": "Coloque um link.", "app.errors.banner_cta_button_url_url": "Introduzir um endereço válido. Certifique-se de que o endereço comece com 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Introduzir um texto.", "app.errors.banner_cta_signed_in_url_blank": "Coloque um link.", "app.errors.banner_cta_signed_in_url_url": "Introduzir um endereço válido. Certifique-se de que o endereço comece com 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Introduzir um texto.", "app.errors.banner_cta_signed_out_url_blank": "Coloque um link.", "app.errors.banner_cta_signed_out_url_url": "Introduzir um endereço válido. Certifique-se de que o endereço comece com 'https://'.", "app.errors.base_includes_banned_words": "Você pode ter usado uma ou mais palavras que são consideradas palavrões. Altere seu texto para remover quaisquer palavrões que possam estar presentes.", "app.errors.body_multiloc_includes_banned_words": "A descrição contém palavras que são consideradas inadequadas.", "app.errors.bulk_import_idea_not_valid": "A ideia resultante não é válida: {value}.", "app.errors.bulk_import_image_url_not_valid": "Não foi possível fazer o download de nenhuma imagem em {value}. Verifique se o URL é válido e termina com uma extensão de arquivo, como .png ou .jpg. Esse problema ocorre na linha com ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Local da ideia com uma coordenada ausente em {value}. Esse problema ocorre na linha com ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Local da ideia com uma coordenada não numérica em {value}. Esse problema ocorre na linha com ID {row}.", "app.errors.bulk_import_malformed_pdf": "O arquivo PDF carregado parece estar malformado. Tente exportar o PDF novamente de sua fonte e, em seguida, faça o upload novamente.", "app.errors.bulk_import_maximum_ideas_exceeded": "O máximo de {value} ideias foi excedido.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "O máximo de {value} páginas em um PDF foi excedido.", "app.errors.bulk_import_not_enough_pdf_pages": "O PDF carregado não tem páginas suficientes - ele deve ter pelo menos o mesmo número de páginas que o modelo baixado.", "app.errors.bulk_import_publication_date_invalid_format": "Ideia com formato de data de publicação inválido \"{value}\". Por favor, use o formato \"DD-MM-AAAA\".", "app.errors.cannot_contain_ideas": "Esta fase contém {ideasCount, plural, one {uma ideia} other {{ideasCount} ideias}} e o método de participação que você está tentando mudar para não suportar ideias. Por favor remova {ideasCount, plural, one {a ideia} other {as ideias}} da fase e tente novamente.", "app.errors.cant_change_after_first_response": "Você não pode mais alterar isso, pois alguns usuários já responderam", "app.errors.category_name_taken": "Já existe uma categoria com este nome", "app.errors.confirmation_code_expired": "O código expirou. Por favor, solicite um novo código.", "app.errors.confirmation_code_invalid": "Código de confirmação inválido. Verifique o código correto no seu e-mail ou tente 'Enviar novo código'", "app.errors.confirmation_code_too_many_resets": "Você solicitou muitas vezes um código de confirmação. Por favor contacte-nos para receber um código de convite.", "app.errors.confirmation_code_too_many_retries": "Você já tentou muitas vezes. Por favor, solicite um novo código ou tente mudar o seu e-mail.", "app.errors.email_already_active": "O e-mail {value} encontrado na linha {row} já pertence a um usuário registrado", "app.errors.email_already_invited": "O e-mail {value} encontrado na linha {row} já foi convidado", "app.errors.email_blank": "Não pode estar vazio", "app.errors.email_domain_blacklisted": "Por favor, use um domínio de e-mail diferente para registrar.", "app.errors.email_invalid": "Por favor, use um endereço de e-mail válido.", "app.errors.email_taken": "Existe um cadastro com este e-mail. você pode fazer o login.", "app.errors.email_taken_by_invite": "{value} você já tem um convite pendente. Verifique sua pasta de spam <NAME_EMAIL> se você não conseguir encontrá-la.", "app.errors.emails_duplicate": "Um ou mais valores duplicados para o endereço de e-mail {value} foram encontrados na(s) linha(s) seguinte(s): {rows}", "app.errors.extension_whitelist_error": "O formato do arquivo que você tentou carregar não é suportado.", "app.errors.file_extension_whitelist_error": "O formato do arquivo que você tentou carregar não é suportado.", "app.errors.first_name_blank": "Não pode estar vazio", "app.errors.generics.blank": "Isto não pode ficar vazio.", "app.errors.generics.invalid": "Isto não parece um valor válido.", "app.errors.generics.taken": "Este e-mail já existe. Outra conta está ligada ele.", "app.errors.generics.unsupported_locales": "Este campo não suporta a configuração atual.", "app.errors.group_ids_unauthorized_choice_moderator": "Como administrador do projeto, você só pode enviar e-mail para pessoas que podem acessar seu (s) projeto (s)", "app.errors.has_other_overlapping_phases": "Os projetos não podem ter fases sobrepostas.", "app.errors.invalid_email": "O e-mail {value} encontrado na linha {row} não é um endereço de e-mail válido", "app.errors.invalid_row": "Ocorreu um erro desconhecido ao tentar processar a linha {row}", "app.errors.is_not_timeline_project": "O projeto atual não suporta passos", "app.errors.key_invalid": "A sua senha só pode conter letras, números e sublinhados", "app.errors.last_name_blank": "Não pode estar vazio", "app.errors.locale_blank": "Por favor escolha um idioma", "app.errors.locale_inclusion": "Por favor escolha um idioma compatível", "app.errors.malformed_admin_value": "O valor admin {value} encontrado na linha {row} não é válido", "app.errors.malformed_groups_value": "O grupo {value} encontrado na linha {row} não é um grupo válido", "app.errors.max_invites_limit_exceeded1": "O número de convites excede o limite de 1000.", "app.errors.maximum_attendees_greater_than1": "O número máximo de registrantes deve ser maior que 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "O número máximo de registrantes deve ser maior ou igual ao número atual de registrantes.", "app.errors.no_invites_specified": "Não foi encontrado nenhum endereço de e-mail.", "app.errors.no_recipients": "A campanha não pode ser enviada porque não há destinatários. O grupo para o qual você está enviando está vazio ou ninguém consentiu em receber e-mails.", "app.errors.number_invalid": "Digite um número válido.", "app.errors.password_blank": "Não pode estar vazio", "app.errors.password_invalid": "Verifique novamente a sua senha atual.", "app.errors.password_too_short": "A sua senha deve conter pelo menos 8 caracteres", "app.errors.resending_code_failed": "Algo deu errado ao enviar o código de confirmação.", "app.errors.slug_taken": "Este URL de projeto já existe. Altere o slug do projeto para outra coisa.", "app.errors.tag_name_taken": "Já existe uma tag com este nome", "app.errors.title_multiloc_blank": "O título não pode ficar vazio.", "app.errors.title_multiloc_includes_banned_words": "O título contém palavras que são consideradas inadequadas.", "app.errors.token_invalid": "Os links de redefinição de senha só podem ser usados uma vez e são válidos por uma hora após terem sido enviados. {passwordResetLink}.", "app.errors.too_common": "Essa senha não é segura. Por favor escolha uma senha mais segura.", "app.errors.too_long": "A senha deve ter pelo menos 72 caracteres", "app.errors.too_short": "A senha deve ter pelo menos 8 caracteres", "app.errors.uncaught_error": "Ocorreu um erro desconhecido.", "app.errors.unknown_group": "O grupo {value} encontrado na linha {row} não é um grupo conhecido", "app.errors.unknown_locale": "O idioma {value} encontrado na linha {row} não é um idioma configurado", "app.errors.unparseable_excel": "O arquivo Excel selecionado não pôde ser processado.", "app.errors.url": "Insira um link válido. Certifique-se de que o link comece com https://", "app.errors.verification_taken": "A verificação não pode ser concluída porque outra conta foi verificada usando os mesmos detalhes.", "app.errors.view_name_taken": "Já existe uma categoria com este nome", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "<PERSON><PERSON><PERSON><PERSON> inapropriado foi detectado automaticamente em uma postagem ou comentário", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Iniciar sess<PERSON> com StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Iniciar sess<PERSON> com StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Crie agora uma conta Stadt Wien e utilize um login para muitos serviços digitais de Viena.", "app.modules.id_cow.cancel": "Cancelado", "app.modules.id_cow.emptyFieldError": "Este campo não pode ficar vazio.", "app.modules.id_cow.helpAltText": "Mostrar onde encontrar o número de série de identificação no RG", "app.modules.id_cow.invalidIdSerialError": "Série de identificação inválida", "app.modules.id_cow.invalidRunError": "RUN inválido", "app.modules.id_cow.noMatchFormError": "Nenhuma coincidência foi encontrada.", "app.modules.id_cow.notEntitledFormError": "Não autorizado.", "app.modules.id_cow.showCOWHelp": "Onde posso encontrar o meu número de série de identificação ?", "app.modules.id_cow.somethingWentWrongError": "Não foi possível verificar por que ocorreu um erro", "app.modules.id_cow.submit": "<PERSON><PERSON> para", "app.modules.id_cow.takenFormError": "Ocupado.", "app.modules.id_cow.verifyCow": "Verificar usando COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verifique com a FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Este campo não pode ficar vazio.", "app.modules.id_gent_rrn.gentRrnHelp": "O seu Rg é mostrado no verso do seu cartão de identidade digital", "app.modules.id_gent_rrn.invalidRrnError": "Número de CPF inválido", "app.modules.id_gent_rrn.noMatchFormError": "Não conseguimos encontrar informações sobre o seu CPF", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Não podemos verificá-lo porque você vive fora de Ghent.", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Não podemos verificá-lo porque você é tem menos de 14 anos", "app.modules.id_gent_rrn.rrnLabel": "Número de CPF", "app.modules.id_gent_rrn.rrnTooltip": "Pedimos o seu número de CPF para verificar se você é um cidadão de Ghent, com mais de 14 anos de idade.", "app.modules.id_gent_rrn.showGentRrnHelp": "Onde posso encontrar o meu número de Série de Identificação?", "app.modules.id_gent_rrn.somethingWentWrongError": "Não foi possível verificar seus dados, porque ocorreu algum erro", "app.modules.id_gent_rrn.submit": "Enviar", "app.modules.id_gent_rrn.takenFormError": "O seu número de CPF já foi utilizado para verificar outra conta", "app.modules.id_gent_rrn.verifyGentRrn": "Verificar usando GentRrn", "app.modules.id_id_card_lookup.cancel": "Cancelado", "app.modules.id_id_card_lookup.emptyFieldError": "Este campo não pode ficar vazio.", "app.modules.id_id_card_lookup.helpAltText": "Explicação do cartão de identificação", "app.modules.id_id_card_lookup.invalidCardIdError": "Este usuário não é válido.", "app.modules.id_id_card_lookup.noMatchFormError": "Nenhuma coincidência foi encontrada.", "app.modules.id_id_card_lookup.showHelp": "Onde posso encontrar o meu número de série de identificação?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Não foi possível verificar por que ocorreu um erro", "app.modules.id_id_card_lookup.submit": "<PERSON><PERSON> para", "app.modules.id_id_card_lookup.takenFormError": "Ocupado.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "Este campo não pode ficar vazio.", "app.modules.id_oostende_rrn.invalidRrnError": "Número de CPF inválido", "app.modules.id_oostende_rrn.noMatchFormError": "Não conseguimos encontrar informações sobre o seu CPF", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Não podemos verificá-lo porque você vive fora de Ghent", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Não podemos verificá-lo porque você é tem menos de 14 anos", "app.modules.id_oostende_rrn.oostendeRrnHelp": "O seu Rg é mostrado no verso do seu cartão de identidade digital", "app.modules.id_oostende_rrn.rrnLabel": "Número de CPF", "app.modules.id_oostende_rrn.rrnTooltip": "Pedimos o seu número de Cpf para verificar se você é um cidadão de Ghent, com mais de 14 anos de idade.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Onde posso encontrar o meu número de Cpf?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Não foi possível verificar por que ocorreu um erro", "app.modules.id_oostende_rrn.submit": "Enviar", "app.modules.id_oostende_rrn.takenFormError": "O seu número de CPF já foi utilizado para verificar outra conta", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verificar a utilização do número de Cpf", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Você recebeu direitos de administrador sobre a pasta \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Compartilhar", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Veja os projetos em {folderUrl} para fazer ouvir a sua voz!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | da plataforma de participação de {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | da plataforma de participação de {orgName}", "app.sessionRecording.accept": "<PERSON>m, eu aceito", "app.sessionRecording.modalDescription1": "Para melhor compreender nossos usuário<PERSON>, pedimos aleatoriamente a uma pequena percentagem de visitantes que rastreie sua sessão de navegação em detalhes.", "app.sessionRecording.modalDescription2": "A única finalidade dos dados registrados é melhorar o site. Nenhum dos seus dados será compartilhado com terceiros. Todas as informações confidenciais que você inserir serão filtradas.", "app.sessionRecording.modalDescription3": "Você aceita?", "app.sessionRecording.modalDescriptionFaq": "<PERSON><PERSON><PERSON> frequentes aqui.", "app.sessionRecording.modalTitle": "Ajude-nos a melhorar este site", "app.sessionRecording.reject": "Não, eu rejeito", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Realizar um exercício de alocação de orçamento", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Coletar feedback em um documento", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Criar uma enquete na plataforma", "app.utils.AdminPage.ProjectEdit.createPoll": "Criar uma pesquisa", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Incorporar uma pesquisa externa", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Encontrar voluntá<PERSON>s", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Colete comentários e feedback", "app.utils.AdminPage.ProjectEdit.shareInformation": "Compartilhar informação", "app.utils.FormattedCurrency.credits": "c<PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.tokens": "fi<PERSON>s", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# créditos} one {# crédito} other {# créditos}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "<PERSON><PERSON> discutido", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON>", "app.utils.IdeaCards.newest": "<PERSON><PERSON>e", "app.utils.IdeaCards.oldest": "<PERSON><PERSON> anti<PERSON>", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Tendência", "app.utils.IdeasNewPage.contributionFormTitle": "Adicionar uma nova contribuição", "app.utils.IdeasNewPage.ideaFormTitle": "Adicionar uma nova ideia", "app.utils.IdeasNewPage.initiativeFormTitle": "Adicionar nova iniciativa", "app.utils.IdeasNewPage.issueFormTitle1": "Adicionar novo comentário", "app.utils.IdeasNewPage.optionFormTitle": "Adicionar uma nova opção", "app.utils.IdeasNewPage.petitionFormTitle": "Adicionar nova petição", "app.utils.IdeasNewPage.projectFormTitle": "Adicionar um novo projeto", "app.utils.IdeasNewPage.proposalFormTitle": "Adicionar nova proposta", "app.utils.IdeasNewPage.questionFormTitle": "Adicionar uma nova pergunta", "app.utils.IdeasNewPage.surveyTitle": "Pesquisa", "app.utils.IdeasNewPage.viewYourComment": "<PERSON>eja seu coment<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourContribution": "Ver sua contribuição", "app.utils.IdeasNewPage.viewYourIdea": "Veja sua ideia", "app.utils.IdeasNewPage.viewYourInitiative": "Veja sua iniciativa", "app.utils.IdeasNewPage.viewYourInput": "Veja sua contribuição", "app.utils.IdeasNewPage.viewYourIssue": "Ver seu problema", "app.utils.IdeasNewPage.viewYourOption": "Ver sua opção", "app.utils.IdeasNewPage.viewYourPetition": "Veja sua petição", "app.utils.IdeasNewPage.viewYourProject": "Visualizar seu projeto", "app.utils.IdeasNewPage.viewYourProposal": "Ver sua proposta", "app.utils.IdeasNewPage.viewYourQuestion": "Veja sua pergunta", "app.utils.Projects.sendSubmission": "Enviar identificador de envio para meu e-mail", "app.utils.Projects.sendSurveySubmission": "Enviar identificador de envio de pesquisa para meu e-mail", "app.utils.Projects.surveySubmission": "<PERSON><PERSON>", "app.utils.Projects.yourResponseHasTheFollowingId": "Sua resposta tem o seguinte identificador: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Se, posteriormente, você decidir que deseja que sua resposta seja removida, entre em contato conosco com o seguinte identificador exclusivo:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Você deve completar seu perfil para participar desse evento.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Você não atende aos requisitos para participar deste evento.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Você não tem permissão para participar desse evento.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Você deve fazer login ou se registrar para participar desse evento.", "app.utils.actionDescriptors.attendingEventNotVerified": "Você deve verificar sua conta antes de poder participar deste evento.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Você deve preencher seu perfil para ser voluntário.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Você não atende aos requisitos para ser voluntário.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Você não tem permissão para ser voluntário.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Você deve fazer login ou se registrar para ser voluntário.", "app.utils.actionDescriptors.volunteeringNotVerified": "Você deve verificar sua conta antes de se voluntariar.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Entre em contato com {completeRegistrationLink} para ser voluntário.", "app.utils.errors.api_error_default.in": "Não está certo", "app.utils.errors.default.ajv_error_birthyear_required": "Por favor, preencha o seu ano de nascimento", "app.utils.errors.default.ajv_error_date_any": "Por favor, preencha uma data válida", "app.utils.errors.default.ajv_error_domicile_required": "Por favor, preencha o seu local de residência", "app.utils.errors.default.ajv_error_gender_required": "Por favor, preencha o seu gênero", "app.utils.errors.default.ajv_error_invalid": "<PERSON> inválido", "app.utils.errors.default.ajv_error_maxItems": "<PERSON><PERSON> pode incluir mais de {limit, plural, one {# item} other {# itens}}", "app.utils.errors.default.ajv_error_minItems": "Pode incluir ao menos {limit, plural, one {# item} other {# itens}}", "app.utils.errors.default.ajv_error_number_any": "Por favor, preencha uma data válida", "app.utils.errors.default.ajv_error_politician_required": "Por favor, preencha se você é um político", "app.utils.errors.default.ajv_error_required3": "O campo é obrigatório: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Não pode estar em branco", "app.utils.errors.default.api_error_accepted": "<PERSON><PERSON> ser aceito", "app.utils.errors.default.api_error_blank": "Não pode estar em branco", "app.utils.errors.default.api_error_confirmation": "Não corresponde", "app.utils.errors.default.api_error_empty": "Não pode estar vazio", "app.utils.errors.default.api_error_equal_to": "Não está certo", "app.utils.errors.default.api_error_even": "Deve ser igual", "app.utils.errors.default.api_error_exclusion": "É reservado", "app.utils.errors.default.api_error_greater_than": "É muito pequeno", "app.utils.errors.default.api_error_greater_than_or_equal_to": "É muito pequeno", "app.utils.errors.default.api_error_inclusion": "Não está incluído na lista", "app.utils.errors.default.api_error_invalid": "<PERSON> inválido", "app.utils.errors.default.api_error_less_than": "É muito grande", "app.utils.errors.default.api_error_less_than_or_equal_to": "É muito grande", "app.utils.errors.default.api_error_not_a_number": "Não é um número", "app.utils.errors.default.api_error_not_an_integer": "Deve ser um número inteiro", "app.utils.errors.default.api_error_other_than": "Não está certo", "app.utils.errors.default.api_error_present": "Tem que estar em branco", "app.utils.errors.default.api_error_too_long": "É muito grande", "app.utils.errors.default.api_error_too_short": "É muito curto", "app.utils.errors.default.api_error_wrong_length": "Tem o comprimento errado", "app.utils.errors.defaultapi_error_.odd": "<PERSON><PERSON> ser estranho", "app.utils.notInGroup": "Não preenche os requisitos para participar.", "app.utils.participationMethod.onSurveySubmission": "Obrigado. A sua resposta foi recebida.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "A votação não está mais disponível, pois essa fase não está mais ativa.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Você não atende aos requisitos para votar.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Você não tem permissão para votar.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Você deve fazer login ou se registrar para votar.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Você deve verificar sua conta antes de poder votar.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>O envio de orçamentos foi encerrado em {endDate}.</b> Os participant<PERSON> tinham um total de <b>{maxBudget} para distribuir entre as opções de {optionCount} .</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Orçamento enviado", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Orçamento enviado 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Você não atende aos requisitos para atribuir orçamentos.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Você não tem permissão para atribuir orçamentos.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Você deve fazer login ou se registrar para atribuir orçamentos.", "app.utils.votingMethodUtils.budgetingNotVerified": "Você deve verificar sua conta antes de poder atribuir orçamentos.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Seu orçamento não será contabilizado</b> até que você clique em \"Enviar\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "O orçamento mínimo exigido é {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Quando tiver terminado, clique em \"Enviar\" para apresentar o seu orçamento.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "<PERSON><PERSON><PERSON><PERSON> as suas opções preferidas tocando em \"Adicionar\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Você tem um total de <b>{maxBudget} para distribuir entre as opções de {optionCount} </b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON>, o seu orçamento foi enviado!</b> <PERSON><PERSON> verificar as suas opções abaixo em qualquer momento ou modificá-las antes de <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON>, seu orçamento foi enviado!</b> Você pode verificar suas opções abaixo a qualquer momento.", "app.utils.votingMethodUtils.castYourVote": "Faça seu voto", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Você pode adicionar um máximo de {maxVotes, plural, one {# créditos} other {# créditos}} por opção.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Você pode adicionar um máximo de {maxVotes, plural, one {# pontos} other {# pontos}} por opção.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Você pode adicionar um máximo de {maxVotes, plural, one {# token} other {# tokens}} por opção.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Você pode adicionar um máximo de {maxVotes, plural, one {# votos} other {# votos}} por opção.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON>uando terminar, clique em “Enviar” para votar.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Selecione suas opções preferidas tocando em \"Select\" (Selecionar).", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Resultados finais", "app.utils.votingMethodUtils.finalTally": "Apuração final", "app.utils.votingMethodUtils.howToParticipate": "Como participar", "app.utils.votingMethodUtils.howToVote": "Como votar", "app.utils.votingMethodUtils.multipleVotingEnded1": "A votação foi encerrada em <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 créditos} one {1 crédito} other {# créditos}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 pontos} one {1 ponto} other {# pontos}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 votos} one {1 voto} other {# votos}}", "app.utils.votingMethodUtils.results": "Resul<PERSON><PERSON>", "app.utils.votingMethodUtils.singleVotingEnded": "A votação foi encerrada em <b>{endDate}.</b> <PERSON>s participant<PERSON> podiam <b>votar em {maxVotes} opções.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "<PERSON><PERSON><PERSON><PERSON> as suas opções preferidas tocando em \"Votar\"", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Você tem <b>{totalVotes} votos</b> que pode atribuir às opções.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON>uando terminar, clique em “Enviar” para votar.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "A votação foi encerrada em <b>{endDate}.</b> <PERSON>s participant<PERSON> podiam <b>votar numa única opção.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Selecione a sua opção preferida tocando em \"Votar\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Você tem <b>1 voto</b> que pode atribuir a uma das opções.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "A votação foi encerrada em <b>{endDate}.</b> Os participant<PERSON> podiam <b>votar em todas as opções que desejassem.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Você pode votar em quantas opções quiser.", "app.utils.votingMethodUtils.submitYourBudget": "Enviar o seu orçamento", "app.utils.votingMethodUtils.submittedBudgetCountText2": "pessoa enviou seu orçamento on-line", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "as pessoas enviaram seus orçamentos on-line", "app.utils.votingMethodUtils.submittedVoteCountText2": "pessoa enviou seu voto on-line", "app.utils.votingMethodUtils.submittedVotesCountText2": "as pessoas enviaram seus votos on-line", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Voto enviado 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON>otos <PERSON>", "app.utils.votingMethodUtils.votingClosed": "Votação encerrada", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Seu voto não será contado</b> até que você clique em \"Submit\" (Enviar)", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, seu voto foi enviado!</b> Você pode verificar ou modificar seu envio antes de <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, seu voto foi enviado!</b> Você pode verificar ou modificar seu envio abaixo a qualquer momento.", "components.UI.IdeaSelect.noIdeaAvailable": "Não há ideias disponíveis.", "components.UI.IdeaSelect.selectIdea": "Selecione a ideia", "containers.SiteMap.allProjects": "Todos os projetos", "containers.SiteMap.customPageSection": "Páginas personalizadas", "containers.SiteMap.folderInfo": "Mais informaç<PERSON>", "containers.SiteMap.headSiteMapTitle": "Mapa do site | {orgName}", "containers.SiteMap.homeSection": "G<PERSON>", "containers.SiteMap.pageContents": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.profilePage": "O seu perfil", "containers.SiteMap.profileSettings": "As suas configurações", "containers.SiteMap.projectEvents": "Eventos", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informação", "containers.SiteMap.projectPoll": "Pesquisa", "containers.SiteMap.projectSurvey": "Pesquisa", "containers.SiteMap.projectsArchived": "Projetos arquivados", "containers.SiteMap.projectsCurrent": "Projetos atuais", "containers.SiteMap.projectsDraft": "Projetos preliminares", "containers.SiteMap.projectsSection": "Projetos de {orgName}", "containers.SiteMap.signInPage": "<PERSON><PERSON><PERSON>", "containers.SiteMap.signUpPage": "<PERSON>rie seu login", "containers.SiteMap.siteMapDescription": "A partir desta página, você pode navegar para qualquer conteúdo da plataforma.", "containers.SiteMap.siteMapTitle": "Mapa do site da plataforma de participação de {orgName}", "containers.SiteMap.successStories": "Histórias de sucesso", "containers.SiteMap.timeline": "Fases do projeto", "containers.SiteMap.userSpaceSection": "A sua conta", "containers.SubscriptionEndedPage.accessDenied": "Você não tem mais acesso", "containers.SubscriptionEndedPage.subscriptionEnded": "Parece que você não pode mais acessar esta página, pois sua assinatura do Go Vocal terminou."}