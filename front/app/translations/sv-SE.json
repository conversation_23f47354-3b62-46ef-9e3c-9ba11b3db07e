{"EmailSettingsPage.emailSettings": "E-postinställningar", "EmailSettingsPage.initialUnsubscribeError": "Det uppstod ett problem med att avsluta prenumerationen på denna kamp<PERSON>, vänligen försök igen.", "EmailSettingsPage.initialUnsubscribeLoading": "<PERSON> beg<PERSON>ran bear<PERSON>, vänligen vänta...", "EmailSettingsPage.initialUnsubscribeSuccess": "Du har framgångsrikt avslutat prenumerationen på {campaignTitle}.", "UI.FormComponents.optional": "val<PERSON>ri", "app.closeIconButton.a11y_buttonActionMessage": "Stäng", "app.components.Areas.areaUpdateError": "<PERSON>tt fel uppstod när du sparade ditt område. Vänligen försök igen.", "app.components.Areas.followedArea": "Följt område: {areaTitle}", "app.components.Areas.followedTopic": "<PERSON><PERSON><PERSON>jt ämne: {topicTitle}", "app.components.Areas.topicUpdateError": "<PERSON>tt fel uppstod när du sparade ditt ämne. Vänligen försök igen.", "app.components.Areas.unfollowedArea": "<PERSON><PERSON> föl<PERSON>t område: {areaTitle}", "app.components.Areas.unfollowedTopic": "<PERSON><PERSON> föl<PERSON>t äm<PERSON>: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Pris:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON> till", "app.components.AssignBudgetControl.added": "Tillagd", "app.components.AssignMultipleVotesControl.addVote": "Lägg till röst", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Du har fördelat alla dina krediter.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Du har fördelat det maximala antalet högskolepoäng för detta alternativ.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Du har fördelat alla dina punkter.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Du har delat ut maximalt antal poäng för detta alternativ.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Du har delat ut alla dina polletter.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Du har delat ut det maximala antalet polletter för detta alternativ.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Du har fördelat alla dina röster.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Du har delat ut maximalt antal röster för detta alternativ.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(inkl. 1 offline)} other {(inkl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Omröstning är inte tillgänglig, eftersom denna fas inte är aktiv.", "app.components.AssignMultipleVotesControl.removeVote": "Ta bort omrö<PERSON>ning", "app.components.AssignMultipleVotesControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Du har redan skickat in din röst. Om du vill ändra den klickar du på \"Ändra din röst\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Du har redan skickat in din röst. Om du vill ändra den går du tillbaka till projektsidan och klickar på \"Ändra din röst\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kredit} other {kredit}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {punkt} other {punkt}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {röst} other {röst}}", "app.components.AssignVoteControl.maxVotesReached1": "Du har fördelat alla dina röster.", "app.components.AssignVoteControl.phaseNotActive": "Omröstning är inte tillgänglig, eftersom denna fas inte är aktiv.", "app.components.AssignVoteControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.selected2": "U<PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "Rösta på minst 1 alternativ", "app.components.AssignVoteControl.votesSubmitted1": "Du har redan skickat in din röst. Om du vill ändra den klickar du på \"Ändra din röst\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Du har redan skickat in din röst. Om du vill ändra den går du tillbaka till projektsidan och klickar på \"Ändra din röst\".", "app.components.AuthProviders.continue": "Fortsätt", "app.components.AuthProviders.continueWithAzure": "Fortsätt med {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Fortsätt med Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Fortsätt med falsk SSO", "app.components.AuthProviders.continueWithGoogle": "Fortsätt med Google", "app.components.AuthProviders.continueWithHoplr": "Fortsätt med Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Fortsätt med ID Österrike", "app.components.AuthProviders.continueWithLoginMechanism": "Fortsätt med {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Fortsätt med MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Det finns redan ett konto med den här e-postadressen.{br}{br}Du kan inte få tillgång till plattformen via FranceConnect eftersom personuppgifterna inte stämmer överens. Om du vill logga in via FranceConnect måste du först ändra ditt för- eller efternamn på denna plattform så att de stämmer överens med dina officiella uppgifter.{br}{br}Du kan logga in som vanligt nedan.", "app.components.AuthProviders.goToLogIn": "Har du redan ett konto? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Har du inget konto? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Logga in", "app.components.AuthProviders.logInWithEmail": "Logga in med e-postadress", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Du måste ha uppnått den angivna minimiåldern eller vara äldre för att verifieras.", "app.components.AuthProviders.signUp2": "Registrera dig", "app.components.AuthProviders.signUpButtonAltText": "Registrera dig med {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Registrera dig med e-postadress", "app.components.AuthProviders.verificationRequired": "Verifiering krävs", "app.components.Author.a11yPostedBy": "Publicerad av", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 deltagare} other {{numberOfParticipants} deltagare}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} användare", "app.components.AvatarBubbles.participant": "deltagare", "app.components.AvatarBubbles.participants1": "deltagare", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Kommentarer är inte möjliga i den aktuella fasen.", "app.components.Comments.commentingDisabledInactiveProject": "Kommentarer är inte möjliga eftersom projektet för närvarande inte är aktivt.", "app.components.Comments.commentingDisabledProject": "Kommentarer i detta projekt är för närvarande inaktiverade.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} till kommentar.", "app.components.Comments.commentingMaybeNotPermitted": "Vänligen {signInLink} för att se vilka åtgärder som kan vidtas.", "app.components.Comments.inputsAssociatedWithProfile": "Som standard kommer dina inlägg att kopplas till din profil, om du inte väljer det här alternativet.", "app.components.Comments.invisibleTitleComments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.leastRecent": "Senaste", "app.components.Comments.likeComment": "<PERSON><PERSON> denna kommentar", "app.components.Comments.mostLiked": "<PERSON><PERSON><PERSON> reaktioner", "app.components.Comments.mostRecent": "Senast genomförda", "app.components.Comments.official": "Officiell", "app.components.Comments.postAnonymously": "Skicka anonymt", "app.components.Comments.replyToComment": "<PERSON><PERSON><PERSON> på kommentar", "app.components.Comments.reportAsSpam": "Rapportera som spam", "app.components.Comments.seeOriginal": "Se original", "app.components.Comments.seeTranslation": "Se översättning", "app.components.Comments.yourComment": "<PERSON> kom<PERSON>ar", "app.components.CommonGroundResults.divisiveDescription": "Uttalanden där människor håller med och inte håller med lika mycket:", "app.components.CommonGroundResults.divisiveTitle": "Splittrande", "app.components.CommonGroundResults.majorityDescription": "Mer än 60% röstade på ett eller annat sätt på följande frågor:", "app.components.CommonGroundResults.majorityTitle": "Majoritet", "app.components.CommonGroundResults.participantLabel": "deltagare", "app.components.CommonGroundResults.participantsLabel1": "deltagare", "app.components.CommonGroundResults.statementLabel": "uttalande", "app.components.CommonGroundResults.statementsLabel1": "uttalanden", "app.components.CommonGroundResults.votesLabe": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabel1": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.agreeLabel": "Överens", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.noMoreStatements": "Det finns inga uttalanden att svara på just nu", "app.components.CommonGroundStatements.noResults": "Det finns inga resultat att visa ännu. Se till att du har deltagit i Common Ground-fasen och kom tillbaka hit efteråt.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Resultat", "app.components.CommonGroundTabs.statementsTabLabel": "Uttalanden", "app.components.CommunityMonitorModal.formError": "<PERSON><PERSON> fel har up<PERSON><PERSON><PERSON><PERSON>.", "app.components.CommunityMonitorModal.surveyDescription2": "<PERSON><PERSON> p<PERSON>ende undersökning visar vad du tycker om styrning och offentliga tjänster.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Tar <1 minut} one {Tar 1 minut} other {Tar # minuter}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Ett e-postmeddelande med en bekräftelsekod har skickats till {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "<PERSON><PERSON> din e-postadress.", "app.components.ConfirmationModal.codeInput": "Kod", "app.components.ConfirmationModal.confirmationCodeSent": "<PERSON>y kod skickad", "app.components.ConfirmationModal.didntGetAnEmail": "Fick du inget e-postmeddelande?", "app.components.ConfirmationModal.foundYourCode": "Hittade du din kod?", "app.components.ConfirmationModal.goBack": "Gå tillbaka.", "app.components.ConfirmationModal.sendEmailWithCode": "Skicka e-postmeddelande med kod", "app.components.ConfirmationModal.sendNewCode": "Skicka ny kod.", "app.components.ConfirmationModal.verifyAndContinue": "Verifiera och fortsätt", "app.components.ConfirmationModal.wrongEmail": "Fel e-postadress?", "app.components.ConsentManager.Banner.accept": "Acceptera", "app.components.ConsentManager.Banner.ariaButtonClose2": "Avvisa policy och stäng banderoll", "app.components.ConsentManager.Banner.close": "Stäng", "app.components.ConsentManager.Banner.mainText": "Genom att surfa godkänner du vår {policyLink}.", "app.components.ConsentManager.Banner.manage": "Hantera", "app.components.ConsentManager.Banner.policyLink": "Policy webbkakor", "app.components.ConsentManager.Banner.reject": "Avvisa", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Vi använder detta för att anpassa och mäta effektiviteten av reklamkampanjer på vår webbplats. Vi kommer inte att visa någon reklam på denna plattform, men följande tjänster kan erbjuda dig en personlig annons baserat på de sidor du besöker på vår plats.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analyser", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Vi använder den här spårningen för att på ett bättre sätt förstå hur du använder plattformen för att lära dig och förbättra din navigering. Den här informationen används endast i massanalys och inte på något sätt för att spåra enskilda personer.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Gå tillbaka", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Till<PERSON>t inte", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funk<PERSON>ell", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Detta krävs för att aktivera och övervaka grundläggande funktioner på webbplatsen. Vissa verktyg som listas här kanske inte gäller dig. Läs vår cookiepolicy om du vill ha mer information.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "<PERSON><PERSON><PERSON> att ha en funktionell plattform sparar vi en autentiseringscookie om du registrerar dig, samt språket som du använder den här plattformen på.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Spara", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Verktyg", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Ansvarsfriskrivning för uppladdning av innehåll", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Genom att ladda upp innehåll försäkrar du att detta innehåll inte bryter mot några bestämmelser eller rättigheter för tredje part, såsom immateriella rättigheter, integritetsrättigheter, rättigheter till affärshemligheter och så vidare. Genom att ladda upp detta innehåll åtar du dig följaktligen att bära fullt och exklusivt ansvar för alla direkta och indirekta skador till följd av det uppladdade innehållet. Vidare åtar du dig att hålla plattformsägaren och Go Vocal skadeslösa mot alla krav från tredje part eller ansvar gentemot tredje part, och alla tillhörande kostnader, som skulle uppstå eller vara ett resultat av det innehåll du laddade upp.", "app.components.ContentUploadDisclaimer.onAccept": "Jag förstår", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON><PERSON><PERSON> för oss varför", "app.components.CustomFieldsForm.addressInputAriaLabel": "Inmatning av adress", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Ange en adress...", "app.components.CustomFieldsForm.adminFieldTooltip": "Fältet är endast synligt för administratörer", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "<PERSON>a svar på denna enkät är anonymiserade.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Minst tre punkter krävs för en polygon.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON><PERSON>r en linje krävs minst två punkter.", "app.components.CustomFieldsForm.attachmentRequired": "Minst en bifogad fil krävs", "app.components.CustomFieldsForm.authorFieldLabel": "Författaren", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Börja skriva för att söka efter användarens e-post eller namn...", "app.components.CustomFieldsForm.back": "Tillbaka", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Klicka på kartan för att rita. Dra sedan i punkterna för att flytta dem.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "<PERSON><PERSON><PERSON> på kartan eller skriv in en adress nedan för att lägga till ditt svar.", "app.components.CustomFieldsForm.confirm": "Bekräfta", "app.components.CustomFieldsForm.descriptionMinLength": "Beskrivningen måste vara minst {min} tecken lång", "app.components.CustomFieldsForm.descriptionRequired": "Beskrivningen är obligatorisk", "app.components.CustomFieldsForm.fieldMaximumItems": "Högst {maxSelections, plural, one {# alternativ} other {# alternativ}} kan väljas för fältet \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "Minst {minSelections, plural, one {# alternativ} other {# alternativ}} kan väl<PERSON> för fältet \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "Fältet \"{fieldName}\" är obligatoriskt", "app.components.CustomFieldsForm.fileSizeLimit": "Gränsen för filstorlek är {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "Bilden är nödvändig", "app.components.CustomFieldsForm.minimumCoordinates2": "Ett minimum av {numPoints} kartpunkter krävs.", "app.components.CustomFieldsForm.notPublic1": "*<PERSON>ta svar kommer endast att delas med projektledare och inte med allmänheten.", "app.components.CustomFieldsForm.otherArea": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.progressBarLabel": "Framsteg", "app.components.CustomFieldsForm.removeAnswer": "<PERSON> bort svar", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*V<PERSON><PERSON>j så många du vill", "app.components.CustomFieldsForm.selectBetween": "*<PERSON><PERSON><PERSON><PERSON> mellan <PERSON>n {minItems} och {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*V<PERSON><PERSON>j så många du vill", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tryck på kartan för att rita. Dra sedan på punkterna för att flytta dem.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tryck på kartan för att rita.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tryck på kartan för att lägga till ditt svar.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "<PERSON><PERSON><PERSON> på kartan eller skriv in en adress nedan för att lägga till ditt svar.", "app.components.CustomFieldsForm.tapToAddALine": "<PERSON>ck för att lägga till en rad", "app.components.CustomFieldsForm.tapToAddAPoint": "<PERSON><PERSON> för att lägga till en punkt", "app.components.CustomFieldsForm.tapToAddAnArea": "<PERSON>ck för att lägga till ett område", "app.components.CustomFieldsForm.titleMaxLength": "<PERSON>ite<PERSON> får vara högst {max} tecken lång", "app.components.CustomFieldsForm.titleMinLength": "Titeln måste vara minst {min} tecken lång", "app.components.CustomFieldsForm.titleRequired": "Titeln är obligatorisk", "app.components.CustomFieldsForm.topicRequired": "Minst en tagg krävs", "app.components.CustomFieldsForm.typeYourAnswer": "<PERSON>k<PERSON>v in ditt svar", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Det är nödvändigt att skriva in ditt svar", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Ladda upp en zip-fil som innehåller en eller flera shapefiler.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "<PERSON>m platsen inte visas bland alternativen när du skriver kan du lägga till giltiga koordinater i formatet \"latitud, longitud\" för att ange en exakt plats (t.ex. -33,019808, -71,495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Vissa fält var ogiltiga. Korrigera felen och försök igen.", "app.components.ErrorBoundary.errorFormErrorGeneric": "<PERSON>tt okänt fel uppstod när din rapport skickades. Försök igen.", "app.components.ErrorBoundary.errorFormLabelClose": "Stäng", "app.components.ErrorBoundary.errorFormLabelComments": "Vad hände?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-post", "app.components.ErrorBoundary.errorFormLabelName": "<PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelSubmit": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "Vårt team har meddelats.", "app.components.ErrorBoundary.errorFormSubtitle2": "Berätta vad som hände nedan om du vill att vi ska hjälpa dig.", "app.components.ErrorBoundary.errorFormSuccessMessage": "<PERSON>ling har skickats. Tack!", "app.components.ErrorBoundary.errorFormTitle": "Det verkar som att det finns ett problem.", "app.components.ErrorBoundary.genericErrorWithForm": "<PERSON>tt fel uppstod och vi kan inte visa det här innehållet. Fö<PERSON><PERSON><PERSON> igen, eller {openForm}", "app.components.ErrorBoundary.openFormText": "hj<PERSON><PERSON><PERSON> oss att ta reda på det", "app.components.ErrorToast.budgetExceededError": "Du har inte tillräckligt med budget", "app.components.ErrorToast.votesExceededError": "Du har inte tillr<PERSON>ckligt med röster kvar", "app.components.EventAttendanceButton.forwardToFriend": "Vidarebefordra till en vän", "app.components.EventAttendanceButton.maxRegistrationsReached": "Det maximala antalet an<PERSON>inga<PERSON> till evenemanget har uppnåtts. Det finns inga platser kvar.", "app.components.EventAttendanceButton.register": "Registrera", "app.components.EventAttendanceButton.registered": "Registrerad", "app.components.EventAttendanceButton.seeYouThere": "Vi ses där!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON>i ses där, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "<PERSON>re evenemangsinformation blev synlig.", "app.components.EventCard.a11y_moreContentVisible": "<PERSON>r evenemangsin<PERSON> blev synlig.", "app.components.EventCard.a11y_readMore": "<PERSON><PERSON><PERSON> mer om evenemanget \"{eventTitle}\".", "app.components.EventCard.endsAt": "<PERSON><PERSON><PERSON>", "app.components.EventCard.readMore": "<PERSON><PERSON><PERSON>r på", "app.components.EventCard.showLess": "Visa mindre", "app.components.EventCard.showMore": "Visa mer", "app.components.EventCard.startsAt": "<PERSON><PERSON><PERSON><PERSON>", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Kommande och pågående händelser i detta projekt", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Kommande och pågående händelser i denna fas", "app.components.FileUploader.a11y_file": "Fil:", "app.components.FileUploader.a11y_filesToBeUploaded": "Filer som ska laddas upp: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Inga filer har lagts till.", "app.components.FileUploader.a11y_removeFile": "Ta bort den här filen", "app.components.FileUploader.fileInputDescription": "<PERSON>licka för att välja en fil", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON> (max. 50 MB)", "app.components.FileUploader.file_too_large2": "Filer som är större än {maxSizeMb}MB tillåts inte.", "app.components.FileUploader.incorrect_extension": "{fileName} stöds inte av vårt system, den kommer inte att laddas upp.", "app.components.FilterBoxes.a11y_allFilterSelected": "Valt statusfilter: alla", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# bidrag} other {# bidrag}}", "app.components.FilterBoxes.a11y_removeFilter": "Ta bort filter", "app.components.FilterBoxes.a11y_selectedFilter": "Valt statusfilter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Valde {numberOfSelectedTopics, plural, =0 {noll ämnesfilter} one {ett ämnesfilter} other {# ämnesfilter}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Allt", "app.components.FilterBoxes.areas": "Filtrera efter område", "app.components.FilterBoxes.inputs": "Bidrag", "app.components.FilterBoxes.noValuesFound": "Inga värden tillgängliga.", "app.components.FilterBoxes.showLess": "Visa mindre", "app.components.FilterBoxes.showTagsWithNumber": "Visa alla ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Ämnen", "app.components.FiltersModal.filters": "Filter", "app.components.FolderFolderCard.a11y_folderDescription": "Mappbeskrivning:", "app.components.FolderFolderCard.a11y_folderTitle": "Mapptitel:", "app.components.FolderFolderCard.archived": "Arkiverad", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projekt} one {# projekt} other {# projekt}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Fälttypen kan inte ändras när det finns inlämningar.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "<PERSON><PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autospara", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automatisk sparning är aktiverad som standard när du öppnar formulärredigeraren. Varje gång du stänger fältinställningspanelen med hjälp av \"X\"-knappen kommer den automatiskt att utlösa en sparning.", "app.components.GanttChart.timeRange.month": "Månad", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "F<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "I dag", "app.components.GoBackButton.group.edit.goBack": "Gå tillbaka", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Gå tillbaka till föregående sida", "app.components.HookForm.Feedback.errorTitle": "Det finns ett problem", "app.components.HookForm.Feedback.submissionError": "Försök igen. Kontakta oss om problemet kvarstår", "app.components.HookForm.Feedback.submissionErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, det var ett problem hos oss", "app.components.HookForm.Feedback.successMessage": "<PERSON><PERSON><PERSON><PERSON> har skickats in", "app.components.HookForm.PasswordInput.passwordLabel": "L<PERSON>senord", "app.components.HorizontalScroll.scrollLeftLabel": "Skrolla till vänster.", "app.components.HorizontalScroll.scrollRightLabel": "Skrolla till höger.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} idéer har laddats.", "app.components.IdeaCards.filters": "Filter", "app.components.IdeaCards.filters.mostDiscussed": "Mest diskuterade", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "Gammal", "app.components.IdeaCards.filters.popular": "Mest omtyckt", "app.components.IdeaCards.filters.random": "Slumpmässig", "app.components.IdeaCards.filters.sortBy": "Sortera efter", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sortering ändrad till: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trendande", "app.components.IdeaCards.showMore": "Visa mer", "app.components.IdeasMap.a11y_hideIdeaCard": "<PERSON><PERSON><PERSON><PERSON>.", "app.components.IdeasMap.a11y_mapTitle": "Kartöversikt", "app.components.IdeasMap.clickOnMapToAdd": "Klicka på kartan för att lägga till dina indata", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Som administratör kan du klicka på kartan för att lägga till dina synpunkter, även om denna fas inte är aktiv.", "app.components.IdeasMap.filters": "Filter", "app.components.IdeasMap.multipleInputsAtLocation": "<PERSON><PERSON>a ing<PERSON>ngar på denna plats", "app.components.IdeasMap.noFilteredResults": "Filtren du valde gav inga resultat", "app.components.IdeasMap.noResults": "Inga resultat hittades", "app.components.IdeasMap.or": "eller", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, inga ogillanden.} one {1 ogillar.} other {, # ogillar.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, inga gillamarkeringar.} one {, 1 gilla.} other {, # gillar.}}", "app.components.IdeasMap.signInLinkText": "logga in", "app.components.IdeasMap.signUpLinkText": "registrera dig", "app.components.IdeasMap.submitIdea2": "<PERSON><PERSON><PERSON> in<PERSON>", "app.components.IdeasMap.tapOnMapToAdd": "Tryck på kartan för att lägga till dina indata", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Som administratör kan du trycka på kartan för att lägga till dina synpunkter, även om denna fas inte är aktiv.", "app.components.IdeasMap.userInputs2": "Bid<PERSON> från <PERSON>", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, inga kommentarer} one {, 1 kommentar} other {, # kommentarer}}", "app.components.IdeasShow.bodyTitle": "Beskrivning", "app.components.IdeasShow.deletePost": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.editPost": "Rediger<PERSON>", "app.components.IdeasShow.goBack": "Gå tillbaka", "app.components.IdeasShow.moreOptions": "Fler alternativ", "app.components.IdeasShow.or": "eller", "app.components.IdeasShow.proposedBudgetTitle": "Budgetförslag", "app.components.IdeasShow.reportAsSpam": "Rapportera som spam", "app.components.IdeasShow.send": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON> över det, jag gör det senare", "app.components.IdeasShowPage.signIn2": "Logga in", "app.components.IdeasShowPage.sorryNoAccess": "Du kan tyvärr inte komma åt den här sidan. Du kan behöva logga in eller registrera dig för att komma åt den.", "app.components.LocationInput.noOptions": "Inga alternativ", "app.components.Modal.closeWindow": "<PERSON>äng fönster", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "<PERSON>e en ny e-postadress", "app.components.PageNotFound.goBackToHomePage": "Tillbaka till startsidan", "app.components.PageNotFound.notFoundTitle": "<PERSON><PERSON> hittades inte", "app.components.PageNotFound.pageNotFoundDescription": "Den begärda sidan hittades inte.", "app.components.PagesForm.descriptionMissingOneLanguageError": "<PERSON><PERSON> inn<PERSON>ll för minst ett språk", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON> (max. 50 MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Filer bör inte vara större än 50 MB. Tillagda filer kommer att visas längst ner på den här sidan.", "app.components.PagesForm.navbarItemTitle": "Namn i navigeringsfältet", "app.components.PagesForm.pageTitle": "Titel", "app.components.PagesForm.savePage": "Spara sida", "app.components.PagesForm.saveSuccess": "<PERSON><PERSON> har sparats.", "app.components.PagesForm.titleMissingOneLanguageError": "<PERSON><PERSON> titel för minst ett språk", "app.components.Pagination.back": "Föregående sida", "app.components.Pagination.next": "<PERSON><PERSON><PERSON> sida", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Du har använt {votesCast}, vilket överskrider gränsen för {votesLimit}. Ta bort några artiklar från din varukorg och försök igen.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} kvar", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "<PERSON> måste spendera minst {votesMinimum} innan du kan skicka din korg.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Du måste välja minst ett alternativ innan du kan skicka in.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Du måste lägga till något i din varukorg innan du kan skicka den.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Inga krediter kvar} other {# ut ur {totalNumberOfVotes, plural, one {1 kredit} other {# krediter}} kvar}}~", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Inga poäng kvar} other {# ut ur {totalNumberOfVotes, plural, one {1 poäng} other {# poäng}} kvar}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Inga polletter kvar} other {# ur {totalNumberOfVotes, plural, one {1 pollett} other {# polletter}} kvar}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Inga röster kvar} other {# utav {totalNumberOfVotes, plural, one {1 röst} other {# röster}} kvar}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# röster} one {# röst} other {# röster}} röster", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Du har avgivit {votesCast} r<PERSON><PERSON>, vilket överskrider gränsen för {votesLimit}. Ta bort några röster och försök igen.", "app.components.ParticipationCTABars.addInput": "Lägg till input", "app.components.ParticipationCTABars.allocateBudget": "Fördela din budget", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Din budget har skickats in framgångsrikt.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Öppet för deltagande", "app.components.ParticipationCTABars.poll": "<PERSON><PERSON>a på enkäten", "app.components.ParticipationCTABars.reviewDocument": "Granska dokumentet", "app.components.ParticipationCTABars.seeContributions": "Se bidrag", "app.components.ParticipationCTABars.seeEvents3": "<PERSON>", "app.components.ParticipationCTABars.seeIdeas": "Se idéer", "app.components.ParticipationCTABars.seeInitiatives": "Se initiativ", "app.components.ParticipationCTABars.seeIssues": "<PERSON> frågor", "app.components.ParticipationCTABars.seeOptions": "Se alternativ", "app.components.ParticipationCTABars.seePetitions": "Se framställningar", "app.components.ParticipationCTABars.seeProjects": "Se projekt", "app.components.ParticipationCTABars.seeProposals": "Se förslag", "app.components.ParticipationCTABars.seeQuestions": "<PERSON> frågor", "app.components.ParticipationCTABars.submit": "<PERSON><PERSON><PERSON> in", "app.components.ParticipationCTABars.takeTheSurvey": "<PERSON><PERSON>r <PERSON>ö<PERSON>", "app.components.ParticipationCTABars.userHasParticipated": "Du har deltagit i det här projektet.", "app.components.ParticipationCTABars.viewInputs": "Visa ingångar", "app.components.ParticipationCTABars.volunteer": "<PERSON>m<PERSON><PERSON> sig frivilligt", "app.components.ParticipationCTABars.votesCounter.vote": "omrö<PERSON>ning", "app.components.ParticipationCTABars.votesCounter.votes": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_passwordHidden": "Lösenordet är dolt", "app.components.PasswordInput.a11y_passwordVisible": "Lösenordet är synligt", "app.components.PasswordInput.a11y_strength1Password": "Dålig lösenordsstyrka", "app.components.PasswordInput.a11y_strength2Password": "Svag lösenordsstyrka", "app.components.PasswordInput.a11y_strength3Password": "Medelstark lösenordsstyrka", "app.components.PasswordInput.a11y_strength4Password": "Stark lösenordsstyrka", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON> stark lösenordsstyrka", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON> k<PERSON> (minst {minimumPasswordLength} tecken)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON>e ett lösenord som är minst {minimumPasswordLength} tecken långt", "app.components.PasswordInput.passwordEmptyError": "<PERSON><PERSON> l<PERSON>", "app.components.PasswordInput.passwordStrengthTooltip1": "<PERSON><PERSON> här gör du ditt lösenord starkare:", "app.components.PasswordInput.passwordStrengthTooltip2": "Använd en kombination av sm<PERSON> bokstäver, versaler, siff<PERSON><PERSON>, specialtecken och skiljetecken som inte följer efter varandra", "app.components.PasswordInput.passwordStrengthTooltip3": "Undvik vanliga eller lättgissade ord", "app.components.PasswordInput.passwordStrengthTooltip4": "Öka längden", "app.components.PasswordInput.showPassword": "Visa lösenord", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "Svag", "app.components.PasswordInput.strength3Password": "Medel", "app.components.PasswordInput.strength4Password": "<PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON> stark", "app.components.PostCardsComponents.list": "Lista", "app.components.PostCardsComponents.map": "Karta", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Lägg till en officiell uppdatering", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Är du säker på att du vill ta bort den här officiella uppdateringen?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Rediger<PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Redigerades senast {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Senaste uppdateringen: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Officiell", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> hur andra personer ser ditt namn", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Officiell uppdatering – författarens namn", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Officiell uppdatering – brödtext", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Officiella uppdateringar", "app.components.PostComponents.OfficialFeedback.postedOn": "Publicerades {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publicera", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Visa tidigare uppdateringar", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Ge en uppdatering...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "Det uppstod tyvärr ett problem", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Uppdatera meddelande", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Din uppdatering publicerades!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "<PERSON><PERSON><PERSON> mitt bidrag {postTitle} på {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON> mitt bidrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> mitt bidrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "<PERSON><PERSON><PERSON> min idé {postTitle} på {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "<PERSON><PERSON><PERSON> min idé: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON> min idé: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Vad tycker du om det här förslaget? <PERSON><PERSON><PERSON> på det och dela diskussionen på {postUrl} för att göra din röst hörd!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "<PERSON><PERSON><PERSON> mitt förslag: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON> mitt initiativ: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Jag publicerade en kommentar {postTitle} på {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Jag publicerade just en kommentar: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Jag publicerade just en kommentar: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "<PERSON><PERSON><PERSON> mitt föreslagna alternativ {postTitle} på {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "<PERSON><PERSON><PERSON> mitt förslagna alternativ: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON><PERSON> mitt alternativ: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "<PERSON><PERSON><PERSON> min namninsamling \"{postTitle}\" på {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON> min namninsamling: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON> min namninsamling: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "<PERSON><PERSON><PERSON> mitt projekt {postTitle} på {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "<PERSON><PERSON><PERSON> mitt projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "<PERSON><PERSON><PERSON> mitt projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "<PERSON><PERSON><PERSON> mitt förslag '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON> mitt förslag: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Jag publicerade just ett förslag för {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Gå med i diskussionen om den här frågan {postTitle} på {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Gå med i diskussionen: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Gå med i diskussionen: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "<PERSON><PERSON><PERSON> på {postTitle} på", "app.components.PostComponents.linkToHomePage": "Länk till <PERSON>ida", "app.components.PostComponents.readMore": "<PERSON><PERSON><PERSON> mer...", "app.components.PostComponents.topics": "Ämnen", "app.components.ProjectArchivedIndicator.archivedProject": "<PERSON><PERSON><PERSON><PERSON> kan du inte delta i det här projektet längre eftersom det har arkiverats", "app.components.ProjectArchivedIndicator.previewProject": "Utkast till projekt:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "<PERSON>yn<PERSON>g endast för moderatorer och de som har en förhandsgranskningslänk.", "app.components.ProjectCard.a11y_projectDescription": "Projektbeskrivning:", "app.components.ProjectCard.a11y_projectTitle": "Projekttitel:", "app.components.ProjectCard.addYourOption": "Lägg till ditt alternativ", "app.components.ProjectCard.allocateYourBudget": "Fördela din budget", "app.components.ProjectCard.archived": "Arkiverad", "app.components.ProjectCard.comment": "Kommentar", "app.components.ProjectCard.contributeYourInput": "Bidra med dina indata", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Gå med i diskussionen", "app.components.ProjectCard.learnMore": "<PERSON><PERSON><PERSON>r", "app.components.ProjectCard.reaction": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON><PERSON> rap<PERSON>", "app.components.ProjectCard.reviewDocument": "Granska dokumentet", "app.components.ProjectCard.submitAnIssue": "Skicka en kommentar", "app.components.ProjectCard.submitYourIdea": "<PERSON><PERSON><PERSON> din <PERSON>", "app.components.ProjectCard.submitYourInitiative": "<PERSON><PERSON>a in ditt initiativ", "app.components.ProjectCard.submitYourPetition": "<PERSON><PERSON><PERSON> in din petition", "app.components.ProjectCard.submitYourProject": "<PERSON><PERSON><PERSON> ditt projekt", "app.components.ProjectCard.submitYourProposal": "Skicka in ditt förslag", "app.components.ProjectCard.takeThePoll": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.takeTheSurvey": "<PERSON><PERSON>r <PERSON>ö<PERSON>", "app.components.ProjectCard.viewTheContributions": "Se bidragen", "app.components.ProjectCard.viewTheIdeas": "Se idéerna", "app.components.ProjectCard.viewTheInitiatives": "Se initiativen", "app.components.ProjectCard.viewTheIssues": "Se kommentarerna", "app.components.ProjectCard.viewTheOptions": "Se <PERSON>n", "app.components.ProjectCard.viewThePetitions": "Ta del av framställningarna", "app.components.ProjectCard.viewTheProjects": "Se projekten", "app.components.ProjectCard.viewTheProposals": "Se förslagen", "app.components.ProjectCard.viewTheQuestions": "Se frågorna", "app.components.ProjectCard.vote": "Omr<PERSON><PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# kommentarer} other {# kommentarer}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# bidrag} other {# bidrag}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {inga idéer ännu} one {# idé} other {# idéer}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# initiativ} one {# initiativ} other {# initiativ}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# kommentar} other {# kommentarer}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# alternativ} other {# alternativ}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petitioner} one {# petition} other {# petitioner}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projekt} other {# projekt}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# proposals} one {# proposal} other {# proposals}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# frå<PERSON>} other {# frå<PERSON>}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# kommentarer} one {# kommentarer} other {# kommentarer}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# ingångar} one {# input} other {# ingångar}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projects} one {# projekt} other {# projekt}}", "app.components.ProjectFolderCards.components.Topbar.all": "Allt", "app.components.ProjectFolderCards.components.Topbar.archived": "Arkiverad", "app.components.ProjectFolderCards.components.Topbar.draft": "Utkast", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtrera efter", "app.components.ProjectFolderCards.components.Topbar.published2": "Publicerad", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Ämne", "app.components.ProjectFolderCards.noProjectYet": "Det finns för närvarande inga öppna projekt", "app.components.ProjectFolderCards.noProjectsAvailable": "Inga projekt är tillgängliga", "app.components.ProjectFolderCards.showMore": "Visa mer", "app.components.ProjectFolderCards.stayTuned": "Kom tillbaka igen för nya interaktionsmöjligheter", "app.components.ProjectFolderCards.tryChangingFilters": "<PERSON><PERSON> att ä<PERSON> de valda filtren.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Används även i dessa städer:", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON><PERSON> länk", "app.components.QuillEditor.alignCenter": "Centrera text", "app.components.QuillEditor.alignLeft": "Justera vänster", "app.components.QuillEditor.alignRight": "<PERSON><PERSON>", "app.components.QuillEditor.bold": "Fet", "app.components.QuillEditor.clean": "Ta bort formatering", "app.components.QuillEditor.customLink": "Lägg till knapp", "app.components.QuillEditor.customLinkPrompt": "<PERSON><PERSON> länk:", "app.components.QuillEditor.edit": "Rediger<PERSON>", "app.components.QuillEditor.image": "Ladda upp bild", "app.components.QuillEditor.imageAltPlaceholder": "<PERSON>rt beskrivning av bilden", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "Lägg till länk", "app.components.QuillEditor.linkPrompt": "<PERSON><PERSON> länk:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Ordnad lista", "app.components.QuillEditor.remove": "<PERSON> bort", "app.components.QuillEditor.save": "Spara", "app.components.QuillEditor.subtitle": "Undertext", "app.components.QuillEditor.title": "Titel", "app.components.QuillEditor.unorderedList": "Oordnad lista", "app.components.QuillEditor.video": "<PERSON><PERSON><PERSON> till video", "app.components.QuillEditor.videoPrompt": "<PERSON><PERSON> video:", "app.components.QuillEditor.visitPrompt": "Besök länk:", "app.components.ReactionControl.completeProfileToReact": "Komplettera din profil för att reagera", "app.components.ReactionControl.dislike": "<PERSON><PERSON><PERSON>", "app.components.ReactionControl.dislikingDisabledMaxReached": "Du har nått ditt maximala antal ogillanden i {projectName}", "app.components.ReactionControl.like": "Som", "app.components.ReactionControl.likingDisabledMaxReached": "Du har nått ditt maximala antal likes på {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reacting kommer att aktiveras när denna fas startar", "app.components.ReactionControl.reactingDisabledPhaseOver": "Det är inte längre möjligt att reagera i denna fas", "app.components.ReactionControl.reactingDisabledProjectInactive": "Du kan inte längre reagera på idéer i {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Reacting är för närvarande inte aktiverat för detta projekt", "app.components.ReactionControl.reactingNotPermitted": "Reagera är endast aktiverat för vissa grupper", "app.components.ReactionControl.reactingNotSignedIn": "<PERSON>gga in för att reagera.", "app.components.ReactionControl.reactingPossibleLater": "Reacting kommer att starta på {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verifiera din identitet för att kunna reagera.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Händelsedatum: {startDate} på {startTime} till {endDate} på {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Händelsedatum: {eventDate} från {startTime} till {endTime}.", "app.components.Sharing.linkCopied": "<PERSON><PERSON><PERSON> k<PERSON>", "app.components.Sharing.or": "eller", "app.components.Sharing.share": "Dela", "app.components.Sharing.shareByEmail": "Dela via e-post", "app.components.Sharing.shareByLink": "<PERSON><PERSON><PERSON> länk", "app.components.Sharing.shareOnFacebook": "Dela på Facebook", "app.components.Sharing.shareOnTwitter": "Dela på Twitter", "app.components.Sharing.shareThisEvent": "<PERSON><PERSON> de<PERSON> even<PERSON>g", "app.components.Sharing.shareThisFolder": "Dela", "app.components.Sharing.shareThisProject": "Dela det här projektet", "app.components.Sharing.shareViaMessenger": "Dela via Messenger", "app.components.Sharing.shareViaWhatsApp": "Dela via WhatsApp", "app.components.SideModal.closeButtonAria": "Stäng", "app.components.StatusModule.futurePhase": "Du tittar på en fas som inte har startat ännu. Du kommer att kunna delta när fasen startar.", "app.components.StatusModule.modifyYourSubmission1": "<PERSON><PERSON> din inlämning", "app.components.StatusModule.submittedUntil3": "Din röst kan avges fram till", "app.components.TopicsPicker.numberOfSelectedTopics": "Valde {numberOfSelectedTopics, plural, =0 {noll ämnen} one {ett ämne} other {# ämnen}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Expandera bilden", "app.components.UI.MoreActionsMenu.moreOptions": "Fler alternativ", "app.components.UI.MoreActionsMenu.showMoreActions": "Visa fler åtgärder", "app.components.UI.PhaseFilter.noAppropriatePhases": "Inga lämpliga faser hittades för detta projekt", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON> bort", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Översätt", "app.components.Unauthorized.additionalInformationRequired": "Ytterligare information krävs för att du ska kunna delta.", "app.components.Unauthorized.completeProfile": "<PERSON><PERSON>tt profil", "app.components.Unauthorized.completeProfileTitle": "Fyll i din profil för att delta", "app.components.Unauthorized.noPermission": "Du har inte behörighet att visa den här sidan", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON> har du inte behörighet att komma åt den här sidan.", "app.components.Upload.errorImageMaxSizeExceeded": "<PERSON><PERSON><PERSON> du valde är större än {maxFileSize} MB", "app.components.Upload.errorImagesMaxSizeExceeded": "En eller flera bilder som du valde är större än {maxFileSize} MB", "app.components.Upload.onlyOneImage": "Du kan bara ladda upp en bild", "app.components.Upload.onlyXImages": "Du kan bara ladda upp {maxItemsCount} bilder", "app.components.Upload.remaining": "återstående", "app.components.Upload.uploadImageLabel": "Välj en bild (max. {maxImageSizeInMb} MB)", "app.components.Upload.uploadMultipleImagesLabel": "<PERSON><PERSON><PERSON><PERSON> en eller flera bilder", "app.components.UpsellTooltip.tooltipContent": "Den här funktionen ingår inte i din nuvarande plan. Prata med din Government Success Manager el<PERSON> administra<PERSON><PERSON><PERSON> för att låsa upp den.", "app.components.UserName.anonymous": "Anonymt", "app.components.UserName.anonymousTooltip2": "Den här användaren har beslutat att anonymisera sitt bidrag.", "app.components.UserName.authorWithNoNameTooltip": "Ditt namn har autogenererats eftersom du inte har angett ditt namn. Uppdatera din profil om du vill ändra det.", "app.components.UserName.deletedUser": "okänd författare", "app.components.UserName.verified": "Verifierad", "app.components.VerificationModal.verifyAuth0": "Verifiera med NemID", "app.components.VerificationModal.verifyBOSA": "Verifiera med itsme eller eID", "app.components.VerificationModal.verifyBosaFas": "Verifiera med itsme eller eID", "app.components.VerificationModal.verifyClaveUnica": "Verifiera med Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Verifiera med falsk SSO", "app.components.VerificationModal.verifyIdAustria": "Verifiera med ID Österrike", "app.components.VerificationModal.verifyKeycloak": "Verifiera med ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verifiera med MitID", "app.components.VerificationModal.verifyTwoday2": "Verifiera med BankID eller Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Verifiera din identitet", "app.components.VoteControl.budgetingFutureEnabled": "Du kan fördela din budget från och med {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Deltagandebudgetering är för närvarande inte aktiverad.", "app.components.VoteControl.budgetingNotPossible": "Det är inte möjligt att göra ändringar i din budget just nu.", "app.components.VoteControl.budgetingNotVerified": "{verifyAccountLink} om du vill fortsätta.", "app.components.VoteInputs._shared.currencyLeft1": "Du har {budgetLeft} / {totalBudget} kvar", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Du har {votesLeft, plural, =0 {inga krediter kvar} other {# ut ur {totalNumberOfVotes, plural, one {1 kredit} other {# krediter}} kvar}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "<PERSON> har {votesLeft, plural, =0 {inga poäng kvar} other {# utav {totalNumberOfVotes, plural, one {1 poäng} other {# poäng}} kvar}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Du har {votesLeft, plural, =0 {inga polletter kvar} other {# utav {totalNumberOfVotes, plural, one {1 pollett} other {# polletter}} kvar}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "<PERSON> har {votesLeft, plural, =0 {inga röster kvar} other {# utav {totalNumberOfVotes, plural, one {1 röst} other {# röster}} kvar}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Du har redan skickat in din budget. Om du vill ändra den klickar du på \"Ändra din inlämning\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Du har redan skickat in din budget. Om du vill ändra den går du tillbaka till projektsidan och klickar på \"Ändra din ansökan\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgetering är inte tillgänglig, eftersom denna fas inte är aktiv.", "app.components.VoteInputs.single.youHaveVotedForX2": "<PERSON> har rö<PERSON>t på {votes, plural, =0 {# alternativ} one {# alternativ} other {# alternativ}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON>ta innebär att du kommer att förlora alla data som är kopplade till denna inmatning, som kommentarer, reaktioner och röster. Denna <PERSON>rd kan inte ångras.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Är du säker på att du vill ta bort denna inmatning?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Bekräfta", "app.components.admin.SlugInput.resultingURL": "Resulterande webbadress", "app.components.admin.SlugInput.slugTooltip": "En slug är den unika uppsättningen ord i slutet av sidans webbadress (URL).", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Om du ändrar webbadressen kommer länkar till sidan som använder den gamla webbadressen inte längre att fungera.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Lägg till ett villkor", "app.components.admin.UserFilterConditions.field_email": "E-post", "app.components.admin.UserFilterConditions.field_event_attendance": "Registreringar till evenemang", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON> i", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Undersökning av gemenskapens övervakare", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interagerade med indata med status", "app.components.admin.UserFilterConditions.field_participated_in_project": "Bidrog till projekt", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Publicerade något med ämne", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Registreringsdatum", "app.components.admin.UserFilterConditions.field_role": "Roll", "app.components.admin.UserFilterConditions.field_verified": "Verifiering", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Förslag till beslut", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "inte är registrerad för något av dessa evenemang", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "är inte registrerad för n<PERSON>g", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "är registrerad för ett av dessa evenemang", "app.components.admin.UserFilterConditions.predicate_attends_something": "är registrerad för minst ett evenemang", "app.components.admin.UserFilterConditions.predicate_begins_with": "b<PERSON><PERSON><PERSON> med", "app.components.admin.UserFilterConditions.predicate_commented_in": "kommenterad", "app.components.admin.UserFilterConditions.predicate_contains": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_ends_on": "slutar på", "app.components.admin.UserFilterConditions.predicate_has_value": "har värde", "app.components.admin.UserFilterConditions.predicate_in": "utförde någon åtgärd", "app.components.admin.UserFilterConditions.predicate_is": "är", "app.components.admin.UserFilterConditions.predicate_is_admin": "är en admin", "app.components.admin.UserFilterConditions.predicate_is_after": "är efter", "app.components.admin.UserFilterConditions.predicate_is_before": "är innan", "app.components.admin.UserFilterConditions.predicate_is_checked": "är kontrollerad", "app.components.admin.UserFilterConditions.predicate_is_empty": "är tom", "app.components.admin.UserFilterConditions.predicate_is_equal": "är", "app.components.admin.UserFilterConditions.predicate_is_exactly": "är exakt", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "är st<PERSON><PERSON> än", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "är större än eller lika med", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "är en normal användare", "app.components.admin.UserFilterConditions.predicate_is_not_area": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "u<PERSON><PERSON><PERSON> mapp", "app.components.admin.UserFilterConditions.predicate_is_not_input": "u<PERSON><PERSON>er inmatning", "app.components.admin.UserFilterConditions.predicate_is_not_project": "u<PERSON><PERSON><PERSON> projekt", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of": "är en av", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "ett av områdena", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "en av mapparna", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "en av ingångarna", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "ett av projekten", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "ett av ämnena", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "är en projektledare", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "är mindre än", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "är mindre än eller lika med", "app.components.admin.UserFilterConditions.predicate_is_verified": "är verifierad", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "b<PERSON><PERSON><PERSON> inte med", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "kommenterade inte", "app.components.admin.UserFilterConditions.predicate_not_contains": "innehåller inte", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "slutar inte på", "app.components.admin.UserFilterConditions.predicate_not_has_value": "har inte något värde", "app.components.admin.UserFilterConditions.predicate_not_in": "bidrog inte", "app.components.admin.UserFilterConditions.predicate_not_is": "är inte", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "är inte en admin", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "är inte kontrollerad", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "är inte tom", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "är inte", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "är inte en normal användare", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "är inte en av", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "är inte en projektledare", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "är inte verifierad", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "publicerade inte indata", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "reagerade inte på kommentaren", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "reagerade inte på inmatning", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "inte anmält dig till ett evenemang", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "har inte deltagit i undersökningen", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "anmälde sig inte frivilligt", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "deltog inte i omröstningen", "app.components.admin.UserFilterConditions.predicate_nothing": "ingenting", "app.components.admin.UserFilterConditions.predicate_posted_input": "publicerade indata", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reagerade på kommentar", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reagerade på inmatning", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "registrerad till ett evenemang", "app.components.admin.UserFilterConditions.predicate_something": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_taken_survey": "har deltagit i undersökningen", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "an<PERSON><PERSON><PERSON> sig frivilligt", "app.components.admin.UserFilterConditions.predicate_voted_in3": "deltog i omröstningen", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attribut", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Villkor", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "<PERSON> får inga meddelanden om ditt bidrag.", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "Fortsätt", "app.components.anonymousParticipationModal.participateAnonymously": "Delta anonymt", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON> <b><PERSON><PERSON><PERSON><PERSON> din profil</b> på ett säkert sätt för administratörer, projektledare och andra invånare för detta specifika bidrag så att ingen kan koppla detta bidrag till dig. Anonyma bidrag kan inte redigeras och anses vara slutgiltiga.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Att göra vår plattform säker för alla användare är en topprioritet för oss. Ord är viktiga, så var snälla mot varandra.", "app.components.avatar.titleForAccessibility": "<PERSON>il p<PERSON> {fullName}", "app.components.customFields.mapInput.removeAnswer": "<PERSON> bort svar", "app.components.customFields.mapInput.undo": "Å<PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "<PERSON><PERSON><PERSON> sista <PERSON>ten", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "Följande triggar e-postuppdateringar om statusändringar, officiella uppdateringar och kommentarer. Du kan när som helst lämna {unsubscribeLink} .", "app.components.followUnfollow.followTooltipProjects2": "Följande triggar e-postuppdateringar om projektförändringar. Du kan när som helst avsluta {unsubscribeLink} .", "app.components.followUnfollow.unFollow": "Avfölj", "app.components.followUnfollow.unsubscribe": "avsluta prenumeration", "app.components.followUnfollow.unsubscribeUrl": "/profil/redigera", "app.components.form.ErrorDisplay.guidelinesLinkText": "<PERSON><PERSON><PERSON> r<PERSON>", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.previous": "Föregående", "app.components.form.ErrorDisplay.save": "Spara", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Börja skriva för att söka efter användarens e-postadress eller namn...", "app.components.form.anonymousSurveyMessage2": "<PERSON>a svar på denna enkät är anonymiserade.", "app.components.form.backToInputManager": "Tillbaka till input manager", "app.components.form.backToProject": "Tillbaka till projektet", "app.components.form.components.controls.mapInput.removeAnswer": "<PERSON> bort svar", "app.components.form.components.controls.mapInput.undo": "Å<PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "<PERSON><PERSON><PERSON> sista <PERSON>ten", "app.components.form.controls.addressInputAriaLabel": "Inmatning av adress", "app.components.form.controls.addressInputPlaceholder6": "Ange en adress...", "app.components.form.controls.adminFieldTooltip": "Fältet är endast synligt för administratörer", "app.components.form.controls.allStatementsError": "<PERSON><PERSON> svar måste väljas för alla påståenden.", "app.components.form.controls.back": "Tillbaka", "app.components.form.controls.clearAll": "<PERSON><PERSON> alla", "app.components.form.controls.clearAllScreenreader": "<PERSON>sa alla svar från ovan<PERSON>e matrisfråga", "app.components.form.controls.clickOnMapMultipleToAdd3": "Klicka på kartan för att rita. Dra sedan i punkterna för att flytta dem.", "app.components.form.controls.clickOnMapToAddOrType": "<PERSON><PERSON><PERSON> på kartan eller skriv in en adress nedan för att lägga till ditt svar.", "app.components.form.controls.confirm": "Bekräfta", "app.components.form.controls.cosponsorsPlaceholder": "Börja skriva in ett namn för att söka", "app.components.form.controls.currentRank": "<PERSON><PERSON><PERSON><PERSON><PERSON> rankning:", "app.components.form.controls.minimumCoordinates2": "Ett minimum av {numPoints} kartpunkter krävs.", "app.components.form.controls.noRankSelected": "Ingen rang vald", "app.components.form.controls.notPublic1": "*<PERSON>ta svar kommer endast att delas med projektledare och inte med allmänheten.", "app.components.form.controls.optionalParentheses": "(valfritt)", "app.components.form.controls.rankingInstructions": "Dra och släpp för att rangordna alternativ.", "app.components.form.controls.selectAsManyAsYouLike": "*V<PERSON><PERSON>j så många du vill", "app.components.form.controls.selectBetween": "*Väl minst {minItems} och upp till {maxItems} alternativ", "app.components.form.controls.selectExactly2": "* Välj exakt {selectExactly, plural, one {# alternativ} other {# alternativ}}", "app.components.form.controls.selectMany": "*<PERSON><PERSON><PERSON><PERSON> hur många du vill", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tryck på kartan för att rita. Dra sedan på punkterna för att flytta dem.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tryck på kartan för att rita.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tryck på kartan för att lägga till ditt svar.", "app.components.form.controls.tapOnMapToAddOrType": "<PERSON>ck på kartan eller skriv in en adress nedan för att lägga till ditt svar.", "app.components.form.controls.tapToAddALine": "<PERSON>ck för att lägga till en rad", "app.components.form.controls.tapToAddAPoint": "<PERSON><PERSON> för att lägga till en punkt", "app.components.form.controls.tapToAddAnArea": "<PERSON>ck för att lägga till ett område", "app.components.form.controls.uploadShapefileInstructions": "* Ladda upp en zip-fil som innehåller en eller flera shapefiler.", "app.components.form.controls.validCordinatesTooltip2": "<PERSON>m platsen inte visas bland alternativen när du skriver kan du lägga till giltiga koordinater i formatet \"latitud, longitud\" för att ange en exakt plats (t.ex. -33,019808, -71,495676).", "app.components.form.controls.valueOutOfTotal": "{value} från {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} ur {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} ur {total}, där {maxValue} är {maxLabel}", "app.components.form.error": "<PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "Det gick inte att läsa in platsfältet från Google Maps.", "app.components.form.progressBarLabel": "Framsteg i undersökningen", "app.components.form.submit": "<PERSON><PERSON><PERSON>", "app.components.form.submitApiError": "Ett problem uppstod när formuläret skulle skickas. Kontrollera om det finns några fel och försök igen.", "app.components.form.verifiedBlocked": "Du kan inte redigera det här fältet eftersom det innehåller verifierad information", "app.components.formBuilder.Page": "<PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "tillgänglighetsutlåtande", "app.components.formBuilder.addAnswer": "Lägg till svar", "app.components.formBuilder.addStatement": "Lägg till uttalande", "app.components.formBuilder.agree": "Överens", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "Om du har tillgång till vårt AI-paket kommer du att kunna sammanfatta och kategorisera textsvar med AI", "app.components.formBuilder.askFollowUpToggleLabel": "Be om uppföljning", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Knappetikett", "app.components.formBuilder.buttonLink": "Länk till knapp", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "<PERSON><PERSON><PERSON><PERSON>era", "app.components.formBuilder.chooseOne": "Välj en", "app.components.formBuilder.close": "Stäng", "app.components.formBuilder.closed": "Stängd", "app.components.formBuilder.configureMap": "Konfigurera karta", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, jag vill lämna", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "Fortsätter att", "app.components.formBuilder.cosponsors": "Medförslagsställare", "app.components.formBuilder.default": "Standard", "app.components.formBuilder.defaultContent": "Standardinnehåll", "app.components.formBuilder.delete": "<PERSON> bort", "app.components.formBuilder.deleteButtonLabel": "<PERSON> bort", "app.components.formBuilder.description": "Beskrivning", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Det här har redan lagts till i formuläret. Standardinnehåll får endast användas en gång.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Att lägga till anpassat innehåll är inte en del av din nuvarande licens. Kontakta din GovSuccess Manager för att lära dig mer om det.", "app.components.formBuilder.disagree": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.displayAsDropdown": "Visa som rullgardinsmeny", "app.components.formBuilder.displayAsDropdownTooltip": "Visa alternativen i en rullgardinsmeny. Om du har många alternativ är detta att rekommendera.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "<PERSON>", "app.components.formBuilder.drawRoute": "<PERSON> rutt", "app.components.formBuilder.dropPin": "Släppstift", "app.components.formBuilder.editButtonLabel": "Rediger<PERSON>", "app.components.formBuilder.emptyImageOptionError": "Ange minst 1 svar. Observera att varje svar måste ha en rubrik.", "app.components.formBuilder.emptyOptionError": "Lämna minst 1 svar", "app.components.formBuilder.emptyStatementError": "Ange minst 1 uttalande", "app.components.formBuilder.emptyTitleError": "Ange en frågetitel", "app.components.formBuilder.emptyTitleMessage": "<PERSON>e en titel för alla svar", "app.components.formBuilder.emptyTitleStatementMessage": "<PERSON>e en rubrik för alla påståenden", "app.components.formBuilder.enable": "Aktivera", "app.components.formBuilder.errorMessage": "Det finns ett problem – åtgärda problemet för att kunna spara dina änd<PERSON>ar", "app.components.formBuilder.fieldGroup.description": "Beskrivning (valfritt)", "app.components.formBuilder.fieldGroup.title": "<PERSON>ite<PERSON> (valfritt)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "<PERSON><PERSON>r nä<PERSON>rande är svaren på de här frågorna endast tillgängliga i den exporterade Excel-filen i Indatahanteraren, och visas inte för användarna.", "app.components.formBuilder.fieldLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.fieldLabelStatement": "Uttalanden", "app.components.formBuilder.fileUpload": "Filuppladdning", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "<PERSON><PERSON><PERSON><PERSON> sida", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "B<PERSON>dda in kartan som kontext eller ställ platsbaserade frågor till deltagarna.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "<PERSON><PERSON><PERSON> en optimal användarupplevelse rekommenderar vi inte att du lägger till fr<PERSON><PERSON> om punkter, rutter eller områden på kartbaserade sidor.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal sida", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Kartläggningsfunktioner för undersökningar ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att få veta mer.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Typ av sida", "app.components.formBuilder.formEnd": "Formulärets slut", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON>a, ta bort sidan", "app.components.formBuilder.formField.copyNoun": "Kopia", "app.components.formBuilder.formField.copyVerb": "Kopia", "app.components.formBuilder.formField.delete": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Om du raderar den här sidan raderas även den logik som är kopplad till den. Är du säker på att du vill radera den?", "app.components.formBuilder.formField.deleteResultsInfo": "<PERSON>ta kan inte göras ogjort", "app.components.formBuilder.goToPageInputLabel": "Nästa sida är sedan:", "app.components.formBuilder.good": "Bra", "app.components.formBuilder.helmetTitle": "Formulärbyggare", "app.components.formBuilder.imageFileUpload": "Bilduppladdning", "app.components.formBuilder.invalidLogicBadgeMessage": "Ogiltig logik", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON> (tillval)", "app.components.formBuilder.labelsTooltipContent2": "Välj valfria etiketter för något av värdena i den linjära skalan.", "app.components.formBuilder.lastPage": "Avslutning", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Är du säker på att du vill lämna?", "app.components.formBuilder.leaveBuilderText": "Du har osparade ändringar. Spara innan du går. Om du lämnar förlorar du dina ändringar.", "app.components.formBuilder.limitAnswersTooltip": "När den är aktiverad måste respondenterna välja det angivna antalet svar för att gå vidare.", "app.components.formBuilder.limitNumberAnswers": "<PERSON>g<PERSON><PERSON><PERSON><PERSON> antalet svar", "app.components.formBuilder.linePolygonMapWarning2": "Linje- och polygonritningar kanske inte uppfyller tillgänglighetsstandarder. Mer information finns på {accessibilityStatement}.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON><PERSON> skala", "app.components.formBuilder.locationDescription": "Plats", "app.components.formBuilder.logic": "Logik", "app.components.formBuilder.logicAnyOtherAnswer": "<PERSON><PERSON><PERSON> annat svar", "app.components.formBuilder.logicConflicts.conflictingLogic": "Motstridig logik", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Den här sidan innehåller frågor som leder till olika sidor. Om deltagarna svarar på flera frågor visas den sida som ligger längst bort. Se till att detta beteende stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "På den här sidan tillämpas flera logikregler: frågelogik för flera val, logik på sidnivå och logik mellan frågor. När dessa villkor överlappar varandra kommer frågelogiken att ha företräde framför sidlogiken och den sida som ligger längst bort kommer att visas. Granska logiken för att se till att den stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Denna sida innehåller en flervalsfråga där alternativen leder till olika sidor. Om deltagarna väljer flera alternativ visas den sida som ligger längst bort. Se till att detta beteende stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Den här sidan innehåller en flervalsfråga där alternativen leder till olika sidor och har frågor som leder till andra sidor. Den längst bort belägna sidan visas om dessa villkor överlappar varandra. Se till att detta beteende stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Den här sidan innehåller en flervalsfråga där alternativen leder till olika sidor och har logik inställd på både sid- och frågenivå. Frågelogiken kommer att ha företräde och den sida som ligger längst bort kommer att visas. Se till att detta beteende stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Denna sida har logik inställd på både sidnivå och frågenivå. Frågelogik kommer att ha företräde framför logik på sidnivå. Se till att detta beteende stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Den här sidan har logik på både sid- och frågenivå, och flera fr<PERSON><PERSON> leder till olika sidor. Frågelogiken kommer att ha företräde och den sida som ligger längst bort kommer att visas. Se till att detta beteende stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "<PERSON>m n<PERSON>got annat svar", "app.components.formBuilder.logicPanelNoAnswer": "Om ej besvarat", "app.components.formBuilder.logicValidationError": "Logik får inte länka till tidigare sidor", "app.components.formBuilder.longAnswer": "L<PERSON>ngt svar", "app.components.formBuilder.mapConfiguration": "Konfiguration av karta", "app.components.formBuilder.mapping": "Kartläggning", "app.components.formBuilder.mappingNotInCurrentLicense": "Kartläggningsfunktioner för undersökningar ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att få veta mer.", "app.components.formBuilder.matrix": "<PERSON><PERSON>", "app.components.formBuilder.matrixSettings.columns": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoiceHelperText": "Om flera alternativ leder till olika sidor och deltagarna väljer mer än ett, visas den sida som ligger längst bort. Se till att detta beteende stämmer överens med ditt avsedda flöde.", "app.components.formBuilder.multipleChoiceImage": "Val av bild", "app.components.formBuilder.multiselect.maximum": "Maximalt", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "Nytt fält", "app.components.formBuilder.number": "<PERSON>l", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Öppna", "app.components.formBuilder.optional": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "<PERSON>t \"Annat\"", "app.components.formBuilder.otherOptionTooltip": "<PERSON><PERSON><PERSON> deltagarna ange ett eget svar om de angivna svaren inte stämmer överens med deras preferenser", "app.components.formBuilder.page": "<PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "Den här sidan kan inte raderas.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Den här sidan kan inte raderas och det går inte att lägga till några ytterligare fält.", "app.components.formBuilder.pageRuleLabel": "<PERSON><PERSON><PERSON> sida är:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Om ingen logik läggs till kommer formuläret att följa sitt normala flöde. Om både sidan och dess frågor har logik, kommer frågelogiken att ha företräde. Se till att detta stämmer överens med ditt avsedda flöde För mer information, besök {supportPageLink}", "app.components.formBuilder.preview": "Förhandsgranskning:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Medsponsorer visas inte på den nedladdade PDF-filen och stöds inte för import via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Frågor om filuppladdning visas som \"stöds inte\" i den nedladdade PDF-filen och stöds inte för import via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Mappningsfrågor visas på den nedladdade PDF-filen, men lagren är inte synliga. Mappningsfrågor stöds inte för import via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrisfrågor visas på den nedladdade PDF-filen men stöds för närvarande inte för import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Sidtitlar och beskrivningar visas som en avsnittsrubrik i den nedladdade PDF-filen.", "app.components.formBuilder.printSupportTooltip.ranking": "Rangordningsfrågor visas på den nedladdade PDF-filen men stöds för närvarande inte för import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Taggar visas som ej stödda i den nedladdade PDF-filen och stöds inte för import via FormSync.", "app.components.formBuilder.proposedBudget": "Budgetförslag", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON> här frågan kan inte tas bort.", "app.components.formBuilder.questionDescriptionOptional": "Frågebeskrivning (valfri)", "app.components.formBuilder.questionTitle": "Frågetitel", "app.components.formBuilder.randomize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.randomizeToolTip": "Ordningen på svaren kommer att slumpas per användare", "app.components.formBuilder.range": "Intervall", "app.components.formBuilder.ranking": "Ranking", "app.components.formBuilder.rating": "Betyg", "app.components.formBuilder.removeAnswer": "<PERSON> bort svar", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "<PERSON><PERSON><PERSON> det obligatoriskt att svara på den här frågan", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON>m svaret är:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "<PERSON>m svaren inkluderar:", "app.components.formBuilder.save": "Spara", "app.components.formBuilder.selectRangeTooltip": "Välj minimivärdet för din skala.", "app.components.formBuilder.sentiment": "Sentiments<PERSON><PERSON>", "app.components.formBuilder.shapefileUpload": "Uppladdning av Esri shapefile", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON> svar", "app.components.formBuilder.showResponseToUsersToggleLabel": "Visa svar för användare", "app.components.formBuilder.singleChoice": "Ett enda val", "app.components.formBuilder.staleDataErrorMessage2": "Det har uppstått ett problem. Det här inmatningsformuläret har nyligen sparats någon annanstans. Det kan bero på att du eller någon annan användare har det öppet för redigering i ett annat webbläsarfönster. Uppdatera sidan för att få det senaste formuläret och gör sedan dina ändringar igen.", "app.components.formBuilder.stronglyAgree": "Instämmer helt och hållet", "app.components.formBuilder.stronglyDisagree": "Instämmer inte alls", "app.components.formBuilder.supportArticleLinkText": "denna sida", "app.components.formBuilder.tags": "Ämnen", "app.components.formBuilder.title": "Titel", "app.components.formBuilder.toLabel": "till", "app.components.formBuilder.unsavedChanges": "Du har obefintliga förändringar", "app.components.formBuilder.useCustomButton2": "<PERSON><PERSON><PERSON><PERSON> knapp för an<PERSON>ad sida", "app.components.formBuilder.veryBad": "<PERSON><PERSON>", "app.components.formBuilder.veryGood": "Mycket bra", "app.components.ideas.similarIdeas.engageHere": "Engagera dig här", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Inga liknande inlagor hittades.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Vi hittade liknande underkastelser - att engagera sig i dem kan bidra till att göra dem starkare!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Liknande förslag har redan publicerats:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Letar du efter liknande inlagor ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> än en dag} one {En dag} other {# dagar}} kvar", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  veckor kvar", "app.components.screenReaderCurrency.AED": "Förenade Arabemiraten Dirham", "app.components.screenReaderCurrency.AFN": "Afghansk Afghansk", "app.components.screenReaderCurrency.ALL": "Albanska Lek", "app.components.screenReaderCurrency.AMD": "Armenisk dramaserie", "app.components.screenReaderCurrency.ANG": "Nederländska Antillen Gulden", "app.components.screenReaderCurrency.AOA": "Angolanska Kwanza", "app.components.screenReaderCurrency.ARS": "Argentinsk peso", "app.components.screenReaderCurrency.AUD": "Australiska dollarn", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbajdzjansk manat", "app.components.screenReaderCurrency.BAM": "Bosnien-<PERSON><PERSON><PERSON>vina Konvertibel Mark", "app.components.screenReaderCurrency.BBD": "Barbadisk dollar", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgariska Lev", "app.components.screenReaderCurrency.BHD": "Bahrainsk dinar", "app.components.screenReaderCurrency.BIF": "Burundisk franc", "app.components.screenReaderCurrency.BMD": "Bermudiansk dollar", "app.components.screenReaderCurrency.BND": "Brunei Dollar", "app.components.screenReaderCurrency.BOB": "Bolivianska Boliviano", "app.components.screenReaderCurrency.BOV": "Bolivianska Mvdol", "app.components.screenReaderCurrency.BRL": "Brasilianska Real", "app.components.screenReaderCurrency.BSD": "Bahamas dollar", "app.components.screenReaderCurrency.BTN": "Bhutanesisk Ngultrum", "app.components.screenReaderCurrency.BWP": "Botswana Pula", "app.components.screenReaderCurrency.BYR": "<PERSON><PERSON><PERSON>ska rubel", "app.components.screenReaderCurrency.BZD": "Belize-dollar", "app.components.screenReaderCurrency.CAD": "Kanadensisk dollar", "app.components.screenReaderCurrency.CDF": "Kongolesisk franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Schweiziska franc", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilensk beräkningsenhet (UF)", "app.components.screenReaderCurrency.CLP": "Chilensk peso", "app.components.screenReaderCurrency.CNY": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.COP": "Colombiansk peso", "app.components.screenReaderCurrency.COU": "Enhet med verkligt värde", "app.components.screenReaderCurrency.CRC": "Costa Rica Colón", "app.components.screenReaderCurrency.CRE": "Kredit", "app.components.screenReaderCurrency.CUC": "Kubansk konvertibel peso", "app.components.screenReaderCurrency.CUP": "Kubansk peso", "app.components.screenReaderCurrency.CVE": "Kap <PERSON> Escudo", "app.components.screenReaderCurrency.CZK": "Tjeckiska koruna", "app.components.screenReaderCurrency.DJF": "Djiboutiska franc", "app.components.screenReaderCurrency.DKK": "Dansk krona", "app.components.screenReaderCurrency.DOP": "Dominikansk peso", "app.components.screenReaderCurrency.DZD": "Algerisk dinar", "app.components.screenReaderCurrency.EGP": "Egyptiska pund", "app.components.screenReaderCurrency.ERN": "Eritreansk Nakfa", "app.components.screenReaderCurrency.ETB": "Etiopiska Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijiansk dollar", "app.components.screenReaderCurrency.FKP": "Falklandsöarnas pund", "app.components.screenReaderCurrency.GBP": "Brittiska pund", "app.components.screenReaderCurrency.GEL": "Georg<PERSON><PERSON>", "app.components.screenReaderCurrency.GHS": "Ghanansk cedi", "app.components.screenReaderCurrency.GIP": "Gibraltar pund", "app.components.screenReaderCurrency.GMD": "Gambiansk dalasi", "app.components.screenReaderCurrency.GNF": "Guineansk franc", "app.components.screenReaderCurrency.GTQ": "Guatemalansk quetzal", "app.components.screenReaderCurrency.GYD": "Guyanesisk dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong-dollar", "app.components.screenReaderCurrency.HNL": "Honduransk Lempira", "app.components.screenReaderCurrency.HRK": "Kroatiska Kuna", "app.components.screenReaderCurrency.HTG": "Haitisk Gourde", "app.components.screenReaderCurrency.HUF": "Ungersk forint", "app.components.screenReaderCurrency.IDR": "Indonesisk rupiah", "app.components.screenReaderCurrency.ILS": "Israelisk ny shekel", "app.components.screenReaderCurrency.INR": "Indiska rupier", "app.components.screenReaderCurrency.IQD": "Irakisk dinar", "app.components.screenReaderCurrency.IRR": "Iransk rial", "app.components.screenReaderCurrency.ISK": "Isländska Króna", "app.components.screenReaderCurrency.JMD": "Jamaicansk dollar", "app.components.screenReaderCurrency.JOD": "Jordanska dinaren", "app.components.screenReaderCurrency.JPY": "Japanska yen", "app.components.screenReaderCurrency.KES": "Kenyansk shilling", "app.components.screenReaderCurrency.KGS": "Kyrgyzstani Som", "app.components.screenReaderCurrency.KHR": "Kambodjanska Riel", "app.components.screenReaderCurrency.KMF": "Komorisk franc", "app.components.screenReaderCurrency.KPW": "Nordkoreansk Won", "app.components.screenReaderCurrency.KRW": "Sydkoreansk Won", "app.components.screenReaderCurrency.KWD": "Kuwaitisk dinar", "app.components.screenReaderCurrency.KYD": "Cayman Islands Dollar", "app.components.screenReaderCurrency.KZT": "Kazakstansk tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libanesiskt pund", "app.components.screenReaderCurrency.LKR": "Sri Lankas rupie", "app.components.screenReaderCurrency.LRD": "Liberianska dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Litauiska Litas", "app.components.screenReaderCurrency.LVL": "Lettiska Lats", "app.components.screenReaderCurrency.LYD": "Libyska dinaren", "app.components.screenReaderCurrency.MAD": "Marockansk dirham", "app.components.screenReaderCurrency.MDL": "Moldaviska Leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Makedonska denaren", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolisk Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MUR": "<PERSON><PERSON><PERSON><PERSON> rupier", "app.components.screenReaderCurrency.MVR": "<PERSON><PERSON><PERSON>a fr<PERSON><PERSON>", "app.components.screenReaderCurrency.MWK": "Malawiska Kwacha", "app.components.screenReaderCurrency.MXN": "Mexikansk peso", "app.components.screenReaderCurrency.MXV": "Mexikansk Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysiska Ringgit", "app.components.screenReaderCurrency.MZN": "Moçambikisk metical", "app.components.screenReaderCurrency.NAD": "Namibisk dollar", "app.components.screenReaderCurrency.NGN": "Nigerianska Naira", "app.components.screenReaderCurrency.NIO": "Nicaraguanska Córdoba", "app.components.screenReaderCurrency.NOK": "Norska kronan", "app.components.screenReaderCurrency.NPR": "Nepalese Rupee", "app.components.screenReaderCurrency.NZD": "Nya Zeelands dollar", "app.components.screenReaderCurrency.OMR": "Omansk rial", "app.components.screenReaderCurrency.PAB": "Panamanska Balboa", "app.components.screenReaderCurrency.PEN": "Peruansk sol", "app.components.screenReaderCurrency.PGK": "Papua Nya Guineas Kina", "app.components.screenReaderCurrency.PHP": "Filippinsk peso", "app.components.screenReaderCurrency.PKR": "Pakistanska rupier", "app.components.screenReaderCurrency.PLN": "Polska Złoty", "app.components.screenReaderCurrency.PYG": "Guaran<PERSON> från <PERSON>", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "Rumänska Leu", "app.components.screenReaderCurrency.RSD": "Serbiska din<PERSON>", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON><PERSON> rubel", "app.components.screenReaderCurrency.RWF": "Rwandisk franc", "app.components.screenReaderCurrency.SAR": "Saudiarabiska Riyal", "app.components.screenReaderCurrency.SBD": "Salomonöarnas dollar", "app.components.screenReaderCurrency.SCR": "Seychellisk rupie", "app.components.screenReaderCurrency.SDG": "Sudanesiska pund", "app.components.screenReaderCurrency.SEK": "Svenska kronor", "app.components.screenReaderCurrency.SGD": "Singapore Dollar", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leone Leone", "app.components.screenReaderCurrency.SOS": "Somalisk shilling", "app.components.screenReaderCurrency.SRD": "Surinamesisk dollar", "app.components.screenReaderCurrency.SSP": "Sydsudanesiskt pund", "app.components.screenReaderCurrency.STD": "São Tomé och Prín<PERSON>pe <PERSON>", "app.components.screenReaderCurrency.SYP": "Syriska pund", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thailändska baht", "app.components.screenReaderCurrency.TJS": "Tadzjikistansk somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistani Manat", "app.components.screenReaderCurrency.TND": "Tunisisk dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongansk Paʻanga", "app.components.screenReaderCurrency.TRY": "Turkiska lira", "app.components.screenReaderCurrency.TTD": "Trinidad och Tobago Dollar", "app.components.screenReaderCurrency.TWD": "Ny Taiwan-dollar", "app.components.screenReaderCurrency.TZS": "Tanzaniansk shilling", "app.components.screenReaderCurrency.UAH": "Ukrainsk Hryvnia", "app.components.screenReaderCurrency.UGX": "Ugandisk shilling", "app.components.screenReaderCurrency.USD": "Amerikanska dollar", "app.components.screenReaderCurrency.USN": "Förenta staternas dollar (nästa dag)", "app.components.screenReaderCurrency.USS": "Amerikanska dollar (samma dag)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayansk peso", "app.components.screenReaderCurrency.UZS": "Uzbekistansk Som", "app.components.screenReaderCurrency.VEF": "Venezuelanska Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamesiska Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Centralafrikanska CFA Franc", "app.components.screenReaderCurrency.XAG": "Silver (ett troy ounce)", "app.components.screenReaderCurrency.XAU": "Guld (ett troy ounce)", "app.components.screenReaderCurrency.XBA": "Europeisk sammansatt enhet (EURCO)", "app.components.screenReaderCurrency.XBB": "Europeiska monetära enheten (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Europeisk beräkningsenhet 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Den europeiska redovisningsenheten 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Östkaribiska dollar", "app.components.screenReaderCurrency.XDR": "Särskilda dragningsrätter", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Västafrikanska CFA-franc", "app.components.screenReaderCurrency.XPD": "Palladium (ett troy ounce)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Platina (ett troy ounce)", "app.components.screenReaderCurrency.XTS": "Koder som är särskilt reserverade för teständamål", "app.components.screenReaderCurrency.XXX": "Ingen valuta", "app.components.screenReaderCurrency.YER": "Jemenitiska rial", "app.components.screenReaderCurrency.ZAR": "Sydafrikanska Rand", "app.components.screenReaderCurrency.ZMW": "Zambiska Kwacha", "app.components.screenReaderCurrency.amount": "Belopp", "app.components.screenReaderCurrency.currency": "Valuta", "app.components.trendIndicator.lastQuarter2": "sista k<PERSON>t", "app.containers.AccessibilityStatement.applicability": "Den här tillgänglighetsförklaringen gäller för en {demoPlatformLink} som är representativ för den här webbplatsen – den använder samma källkod och har samma funktioner.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Bedömningsmetod", "app.containers.AccessibilityStatement.assesmentText2022": "Tillgängligheten för den här webbplatsen utvärderades av en extern enhet som inte var involverad i design- och utvecklingsprocessen. Efterlevnaden för den tidigare nämnda {demoPlatformLink} kan identifieras på den här {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "du kan ändra dina inställningar", "app.containers.AccessibilityStatement.changePreferencesText": "{changePreferencesButton} när som helst.", "app.containers.AccessibilityStatement.conformanceExceptions": "Undantag från överensstämmelse", "app.containers.AccessibilityStatement.conformanceStatus": "Överensstämmelsestatus", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Vi strävar efter att göra vårt innehåll inkluderande för alla. Men i vissa fall kan det finnas otillgängligt innehåll på plattformen enligt nedan:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demowebbplats", "app.containers.AccessibilityStatement.email": "E-post:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Inbyggda enkätverktyg", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "De inbäddade undersökningsverktyg som finns tillgängliga för användning på denna plattform är programvara från tredje part och kanske inte är tillgängliga.", "app.containers.AccessibilityStatement.exception_1": "Våra digitala interaktionsplattformar underlättar användargenererat innehåll som publiceras av individer och organisationer. Det är möjligt att PDF-filer, bilder eller andra filtyper inklusive multimedia laddas upp till plattformen som bilagor eller läggs till i textfält av plattformsdeltagare. De här dokumenten kanske inte är helt tillgängliga.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Vi välkomnar din återkoppling om tillgängligheten för denna webbplats. Kontakta oss på något av följande sätt:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Återkopplingsprocess", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brussels, Belgien", "app.containers.AccessibilityStatement.headTitle": "Tillgänglighetsutlåtande | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} har åtagit sig att tillhandahålla en plattform som är tillgänglig för alla användare, oavsett teknik eller förmåga. Nuvarande relevanta tillgänglighetsstandarder följs i vårt pågående arbete med att maximera våra plattformars tillgänglighet och användbarhet för alla användare.", "app.containers.AccessibilityStatement.mapping": "Kartläggning", "app.containers.AccessibilityStatement.mapping_1": "Kartor på plattformen uppfyller delvis tillgänglighetsstandarder. Kartans omfattning, zoom och UI-widgets kan styras med hjälp av ett tangentbord när du visar kartor. Administratörer kan också konfigurera stilen för kartlager i backoffice eller med hjälp av Esri-integrationen för att skapa mer tillgängliga färgpaletter och symbologi. Att använda olika linje- eller polygonstilar (t.ex. streckade linjer) hjälper också till att skilja kartlager när det är möjligt, och även om sådan styling inte kan konfigureras i vår plattform för närvarande kan den konfigureras om du använder kartor med Esri-integrationen.", "app.containers.AccessibilityStatement.mapping_2": "Kartorna i plattformen är inte fullt tillgängliga eftersom de inte presenterar baskartor, kartlager eller trender i data på ett hörbart sätt för användare som använder skärmläsare. Fullt tillgängliga kartor skulle behöva presentera kartlagren på ett hörbart sätt och beskriva relevanta trender i data. Dessutom är kartritning med linjer och polygoner i undersökningar inte tillgänglig eftersom former inte kan ritas med hjälp av ett tangentbord. Alternativa inmatningsmetoder är inte tillgängliga i nuläget på grund av teknisk komplexitet.", "app.containers.AccessibilityStatement.mapping_3": "<PERSON><PERSON>r att göra kartritning med linjer och polygoner mer tillgänglig rekommenderar vi att man inkluderar en introduktion eller förklaring i enkätfrågan eller sidbeskrivningen av vad kartan visar och eventuella relevanta trender. Dessutom kan en textfråga med ett kort eller långt svar tillhandahållas så att respondenterna kan beskriva sitt svar i klartext om det behövs (i stället för att klicka på kartan). Vi rekommenderar också att du anger kontaktinformation till projektledaren så att respondenter som inte kan fylla i en kartfråga kan begära en alternativ metod för att besvara frågan (t.ex. videomöte).", "app.containers.AccessibilityStatement.mapping_4": "<PERSON><PERSON>r idéprojekt och förslag finns en möjlighet att visa inmatningar i en kartvy som inte är tillgänglig. <PERSON><PERSON>r dessa metoder finns det emellertid en alternativ lista över tillgängliga insatser, som är tillgänglig.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Våra onlineworkshops har en komponent för livestreaming av video, som för närvarande inte stöder undertexter.", "app.containers.AccessibilityStatement.pageDescription": "Ett uttalande om tillgängligheten för den här webbplatsen", "app.containers.AccessibilityStatement.postalAddress": "Postadress:", "app.containers.AccessibilityStatement.publicationDate": "Publiceringsdatum", "app.containers.AccessibilityStatement.publicationDate2024": "Detta tillgänglighetsutlåtande publicerades den 21 augusti 2024.", "app.containers.AccessibilityStatement.responsiveness": "Vi strävar efter att svara på återkoppling inom 1–2 arbetsdagar.", "app.containers.AccessibilityStatement.statusPageText": "statussida", "app.containers.AccessibilityStatement.technologiesIntro": "Den här webbplatsens tillgänglighet är beroende av att följande tekniker fungerar:", "app.containers.AccessibilityStatement.technologiesTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.title": "Tillgänglighetsförklaring", "app.containers.AccessibilityStatement.userGeneratedContent": "Användargenererat innehåll", "app.containers.AccessibilityStatement.workshops": "Workshops", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Om du använder Content Builder kan du använda mer avancerade layoutalternativ. F<PERSON>r språk där inget innehåll finns tillgängligt i innehållsbyggaren visas istället det vanliga innehållet i projektbeskrivningen.", "app.containers.AdminPage.ProjectDescription.linkText": "Redigera beskrivning i Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "<PERSON><PERSON>got gick fel när du sparade projektbeskrivningen.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Använd Content Builder för beskrivning", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Med hjälp av Content Builder kan du använda mer avancerade layoutalternativ.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Visa projekt", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Undersökningens slut", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Skapa en smart grupp", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Användare som matchar samtliga följande villkor läggs automatiskt till i gruppen:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "<PERSON><PERSON> minst en regel", "app.containers.AdminPage.Users.UsersGroup.rulesError": "<PERSON><PERSON> vill<PERSON> ofullständiga", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Spara grupp", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Konfigurering av smarta grupper ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att lära dig mer om det.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Ange ett gruppnamn", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verifiering har inaktiverats för din plattform – ta bort verifieringsregeln eller kontakta supporten.", "app.containers.App.appMetaDescription": "Välkommen till onlineplattformen för {orgName}. \nUpptäck lokala projekt och delta i diskussionen!", "app.containers.App.loading": "<PERSON><PERSON><PERSON> in...", "app.containers.App.metaTitle1": "Plattform för medborgarengagemang | {orgName}", "app.containers.App.skipLinkText": "Hoppa till huvudinnehåll", "app.containers.AreaTerms.areaTerm": "område", "app.containers.AreaTerms.areasTerm": "områden", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Ett konto med den här e-postadressen finns redan. Du kan logga ut, logga in med den här e-postadressen och verifiera ditt konto på inställningssidan.", "app.containers.Authentication.steps.AccessDenied.close": "Nä<PERSON>", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Du uppfyller inte kraven för att delta i denna process.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Gå tillbaka till verifiering med enkel inloggning", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "<PERSON>e en token", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Har du redan ett konto? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Logga in", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-postmeddelanden i den här kategorin", "app.containers.CampaignsConsentForm.messageError": "Det uppstod ett fel när dina e-postinställningar skulle sparas.", "app.containers.CampaignsConsentForm.messageSuccess": "<PERSON>a e-postinställningar har sparats.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Vilka typer av e-postmeddelanden vill du få?", "app.containers.CampaignsConsentForm.notificationsTitle": "Notiser", "app.containers.CampaignsConsentForm.submit": "Spara", "app.containers.ChangeEmail.backToProfile": "Tillbaka till profilinställningar", "app.containers.ChangeEmail.confirmationModalTitle": "Bekräfta ditt e-postmeddelande", "app.containers.ChangeEmail.emailEmptyError": "<PERSON><PERSON> en e-postadress", "app.containers.ChangeEmail.emailInvalidError": "Ange en e-postadress i rätt format, <NAME_EMAIL>.", "app.containers.ChangeEmail.emailRequired": "<PERSON><PERSON> en e-postadress.", "app.containers.ChangeEmail.emailTaken": "Denna e-postadress anvä<PERSON> redan.", "app.containers.ChangeEmail.emailUpdateCancelled": "Uppdateringen av e-postmeddelandet har avbrutits.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Om du vill uppdatera din e-postadress startar du om processen.", "app.containers.ChangeEmail.helmetDescription": "<PERSON><PERSON> e-postsida", "app.containers.ChangeEmail.helmetTitle": "<PERSON><PERSON> din e-postadress", "app.containers.ChangeEmail.newEmailLabel": "Ny e-post", "app.containers.ChangeEmail.submitButton": "<PERSON><PERSON><PERSON> in", "app.containers.ChangeEmail.titleAddEmail": "<PERSON><PERSON><PERSON> till din e-postadress", "app.containers.ChangeEmail.titleChangeEmail": "<PERSON><PERSON> din e-postadress", "app.containers.ChangeEmail.updateSuccessful": "Din e-postadress har uppdaterats.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "<PERSON><PERSON> ditt nuvarande l<PERSON>", "app.containers.ChangePassword.goHome": "Gå till startsidan", "app.containers.ChangePassword.helmetDescription": "<PERSON><PERSON>", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON>", "app.containers.ChangePassword.newPasswordLabel": "Nytt lösenord", "app.containers.ChangePassword.newPasswordRequired": "<PERSON><PERSON> ditt nya l<PERSON>", "app.containers.ChangePassword.password.minimumPasswordLengthError": "<PERSON>e ett lösenord som är minst {minimumPasswordLength} tecken långt", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON><PERSON> l<PERSON> har uppdate<PERSON>s", "app.containers.ChangePassword.passwordEmptyError": "<PERSON><PERSON> l<PERSON>", "app.containers.ChangePassword.passwordsDontMatch": "Bekräfta nytt lösenord", "app.containers.ChangePassword.titleAddPassword": "Lägg till ett lösenord", "app.containers.ChangePassword.titleChangePassword": "<PERSON><PERSON>", "app.containers.Comments.a11y_commentDeleted": "<PERSON><PERSON><PERSON><PERSON> har tagits bort", "app.containers.Comments.a11y_commentPosted": "Komme<PERSON>r har publicerats", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {inga likes} one {1 gilla} other {# likes}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON> l<PERSON>", "app.containers.Comments.addCommentError": "Något gick fel. Försök igen senare.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "Ta bort den här kommentaren", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Skriv ett svar...", "app.containers.Comments.commentCancelUpvote": "Å<PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "Den här kommentaren har tagits bort.", "app.containers.Comments.commentDeletionCancelButton": "Beh<PERSON><PERSON> min kommentar", "app.containers.Comments.commentDeletionConfirmButton": "Ta bort min kommentar", "app.containers.Comments.commentLike": "Som", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "Sortera kommentarer efter", "app.containers.Comments.completeProfileLinkText": "fylla i din profil", "app.containers.Comments.completeProfileToComment": "Vänligen {completeRegistrationLink} för att kommentera.", "app.containers.Comments.confirmCommentDeletion": "<PERSON>r du säker på att du vill ta bort den här kommentaren? Det går inte att ångra!", "app.containers.Comments.deleteComment": "<PERSON><PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "Ge mer information om din anledning", "app.containers.Comments.deleteReasonError": "<PERSON><PERSON> en anledning", "app.containers.Comments.deleteReason_inappropriate": "Det är olämpligt eller stötande", "app.containers.Comments.deleteReason_irrelevant": "Det här är inte relevant", "app.containers.Comments.deleteReason_other": "<PERSON><PERSON>", "app.containers.Comments.editComment": "Rediger<PERSON>", "app.containers.Comments.guidelinesLinkText": "våra riktl<PERSON>jer för <PERSON>", "app.containers.Comments.ideaCommentBodyPlaceholder": "Skriv din kommentar här", "app.containers.Comments.internalCommentingNudgeMessage": "Att göra interna kommentarer ingår inte i din nuvarande licens. Kontakta din GovSuccess Manager för att få veta mer om det.", "app.containers.Comments.internalConversation": "Intern konversation", "app.containers.Comments.loadMoreComments": "<PERSON><PERSON><PERSON> in fler kommentarer", "app.containers.Comments.loadingComments": "<PERSON><PERSON><PERSON> in kommentarer...", "app.containers.Comments.loadingMoreComments": "<PERSON><PERSON><PERSON> in fler kommentarer...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Denna kommentar är inte synlig för vanliga användare", "app.containers.Comments.postInternalComment": "<PERSON><PERSON><PERSON> intern kommentar", "app.containers.Comments.postPublicComment": "Offentlig kommentar", "app.containers.Comments.profanityError": "Du kan ha använt ett eller flera ord som anses vara svordomar av {guidelinesLink}. Vänligen redigera din text för att ta bort alla svordomar som kan finnas.", "app.containers.Comments.publicDiscussion": "Offentlig diskussion", "app.containers.Comments.publishComment": "Publicera din kommentar", "app.containers.Comments.reportAsSpamModalTitle": "Var<PERSON><PERSON>r vill du anmäla det här som skräppost?", "app.containers.Comments.saveComment": "Spara", "app.containers.Comments.signInLinkText": "logga in", "app.containers.Comments.signInToComment": "{signInLink} för att kommentera.", "app.containers.Comments.signUpLinkText": "registrera dig", "app.containers.Comments.verifyIdentityLinkText": "Verifiera din identitet", "app.containers.Comments.visibleToUsersPlaceholder": "Denna kommentar är synlig för vanliga användare", "app.containers.Comments.visibleToUsersWarning": "Kommentarer som publiceras här kommer att vara synliga för vanliga användare.", "app.containers.ContentBuilder.PageTitle": "Projektbeskrivning", "app.containers.CookiePolicy.advertisingContent": "Marknadsföringscookies kan användas för att anpassa och mäta effektiviteten som externa marknadsföringskampanjer har på interaktion med den här plattformen. Vi kommer inte att visa någon reklam på den här plattformen, men du kan få personliga annonser baserat på sidorna som du besöker.", "app.containers.CookiePolicy.advertisingTitle": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "Analyscookies spårar besökares beteende, till exempel vilka sidor som de besöker och hur länge. De kan också samla in vissa tekniska data inklusive webbläsarinformation, ungefärlig plats och IP-adresser. Vi använder endast dessa data internt för att fortsätta förbättra den övergripande användarupplevelsen och plattformens funktioner. Sådana data kan också delas mellan Go Vocal och {orgName} för att utvärdera och förbättra interaktionen med projekt på plattformen. Obs! Data är anonyma och används på en samlingsnivå – den identifierar inte dig personligen. Om dessa data skulle kombineras med andra datakällor skulle en sådan identifiering dock kunna göras.", "app.containers.CookiePolicy.analyticsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.cookiePolicyDescription": "En detaljerad förklaring av hur vi använder cookies på den här plattformen", "app.containers.CookiePolicy.cookiePolicyTitle": "Policy webbkakor", "app.containers.CookiePolicy.essentialContent": "Vissa cookies är nödvändiga för att säkerställa att den här plattformen fungerar korrekt. Dessa nödvändiga cookies används främst för att autentisera ditt konto när du besöker plattformen och för att spara ditt önskade språk.", "app.containers.CookiePolicy.essentialTitle": "Nödvändiga cookies", "app.containers.CookiePolicy.externalContent": "Vissa av våra sidor kan visa innehåll från externa leverantörer, t.ex. YouTube eller Typeform. Vi har inte kontroll över dessa tredjepartscookies och om du tittar på innehåll från de här externa leverantörerna kan det också leda till att cookies installeras på din enhet.", "app.containers.CookiePolicy.externalTitle": "Externa cookies", "app.containers.CookiePolicy.functionalContents": "Funktionscookies kan aktiveras för att besökare ska få aviseringar om uppdateringar och få tillgång till supportkanaler direkt från plattformen.", "app.containers.CookiePolicy.functionalTitle": "Funktionscookies", "app.containers.CookiePolicy.headCookiePolicyTitle": "Cookiepolicy | {orgName}", "app.containers.CookiePolicy.intro": "Cookies är textfiler som lagras i webbläsaren eller på din dators eller mobila enhets hårddisk när du besöker en webbplats och som kan refereras av webbplatsen vid efterföljande besök. Vi använder cookies för att förstå hur besökare använder den här plattformen för att göra designen och upplevelsen bättre, för att komma ihåg dina inställningar (t.ex. ditt önskade språk) och för att stödja viktiga funktioner för registrerade användare och plattformsadministratörer.", "app.containers.CookiePolicy.manageCookiesDescription": "Du kan aktivera eller inaktivera analys-, marknadsförings- och funktionscookies när som helst i dina cookieinställningar. Du kan också manuellt eller automatiskt ta bort befintliga cookies via din webbläsare. Cookies kan dock placeras igen efter ditt samtycke vid eventuella efterföljande besök på den här plattformen. Om du inte tar bort cookies lagras dina cookieinställningar i 60 dagar, varefter du kommer att bli tillfrågad igen om ditt samtycke.", "app.containers.CookiePolicy.manageCookiesPreferences": "Gå till {manageCookiesPreferencesButtonText} om du vill se en fullständig lista över tredjepartsintegrationer som används på den här plattformen och hantera dina inställningar.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "cookieinställningar", "app.containers.CookiePolicy.manageCookiesTitle": "Hantera dina cookies", "app.containers.CookiePolicy.viewPreferencesButtonText": "Cookieinställningar", "app.containers.CookiePolicy.viewPreferencesText": "Nedanstående cookiekategorier kanske inte gäller för alla besökare eller plattformar – se {viewPreferencesButton} för en fullständig lista över tredjepartsintegreringar som är tillämpliga för dig.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Vad använder vi cookies till?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON>a sida", "app.containers.CustomPageShow.goBack": "Gå tillbaka", "app.containers.CustomPageShow.notFound": "<PERSON><PERSON> hittades inte", "app.containers.DisabledAccount.bottomText": "Du kan logga in igen från {date}.", "app.containers.DisabledAccount.termsAndConditions": "vill<PERSON> och bestämmelser", "app.containers.DisabledAccount.text2": "Ditt konto på deltagarplattformen {orgName} har tillfä<PERSON>gt stängts av på grund av brott mot gemenskapens riktlinjer. För mer information om detta kan du konsultera {TermsAndConditions}.", "app.containers.DisabledAccount.title": "<PERSON>tt konto har till<PERSON><PERSON><PERSON><PERSON> inaktiverats", "app.containers.EventsShow.addToCalendar": "Lägg till i kalender", "app.containers.EventsShow.editEvent": "<PERSON><PERSON><PERSON> h<PERSON>e", "app.containers.EventsShow.emailSharingBody2": "Delta i detta evenemang: {eventTitle}. <PERSON><PERSON><PERSON> mer på {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Da<PERSON> och tid för hä<PERSON>", "app.containers.EventsShow.eventFrom2": "<PERSON><PERSON><PERSON> \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Gå tillbaka", "app.containers.EventsShow.goToProject": "Gå till projektet", "app.containers.EventsShow.haveRegistered": "har registrerat sig", "app.containers.EventsShow.icsError": "Fel vid nedladdning av filen ICS", "app.containers.EventsShow.linkToOnlineEvent": "Länk till evenemanget online", "app.containers.EventsShow.locationIconAltText": "Plats", "app.containers.EventsShow.metaTitle": "Händelse: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Möte online", "app.containers.EventsShow.onlineLinkIconAltText": "Länk till online-möte", "app.containers.EventsShow.registered": "registrerad", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 anm<PERSON><PERSON><PERSON><PERSON>} one {1 anmälan} other {# anmälningar}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registrerade", "app.containers.EventsShow.registrantsIconAltText": "Registrerade", "app.containers.EventsShow.socialMediaSharingMessage": "Delta i detta evenemang: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# deltagare} other {# deltagare}}", "app.containers.EventsViewer.allTime": "<PERSON><PERSON>n", "app.containers.EventsViewer.date": "Datum", "app.containers.EventsViewer.thisMonth2": "Kommande månad", "app.containers.EventsViewer.thisWeek2": "<PERSON><PERSON><PERSON> vecka", "app.containers.EventsViewer.today": "<PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "Lägg till ett bidrag", "app.containers.IdeaButton.addAPetition": "Lägg till en namninsamling", "app.containers.IdeaButton.addAProject": "Lägg till ett projekt", "app.containers.IdeaButton.addAProposal": "Lägg till ett förslag", "app.containers.IdeaButton.addAQuestion": "Lägg till en fråga", "app.containers.IdeaButton.addAnInitiative": "Lägg till ett initiativ", "app.containers.IdeaButton.addAnOption": "Lägg till ett alternativ", "app.containers.IdeaButton.postingDisabled": "<PERSON>ya bidrag tas för n<PERSON>e inte emot", "app.containers.IdeaButton.postingInNonActivePhases": "Nya bidrag kan endast läggas till i aktiva faser.", "app.containers.IdeaButton.postingInactive": "<PERSON>ya bidrag tas för n<PERSON> inte emot.", "app.containers.IdeaButton.postingLimitedMaxReached": "Du har redan slutfört den här undersökningen. Tack för ditt svar!", "app.containers.IdeaButton.postingNoPermission": "<PERSON>ya bidrag tas för n<PERSON>e inte emot", "app.containers.IdeaButton.postingNotYetPossible": "<PERSON>ya bidrag tas inte emot ännu.", "app.containers.IdeaButton.signInLinkText": "logga in", "app.containers.IdeaButton.signUpLinkText": "registrera dig", "app.containers.IdeaButton.submitAnIssue": "Skicka en kommentar", "app.containers.IdeaButton.submitYourIdea": "<PERSON><PERSON><PERSON> din <PERSON>", "app.containers.IdeaButton.takeTheSurvey": "<PERSON><PERSON>r <PERSON>ö<PERSON>", "app.containers.IdeaButton.verificationLinkText": "Verifiera din identitet nu.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON><PERSON>r på", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {inga kommentarer} one {1 kommentar} other {# kommentarer}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {inga rö<PERSON>} one {1 röst} other {# röster}} ut från {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Stäng filterpanelen", "app.containers.IdeaCards.a11y_totalItems": "Totalt antal inlägg: {ideasCount}", "app.containers.IdeaCards.all": "Allt", "app.containers.IdeaCards.allStatuses": "Alla statusar", "app.containers.IdeaCards.contributions": "Bidrag", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Initiativ", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.list": "Lista", "app.containers.IdeaCards.map": "Karta", "app.containers.IdeaCards.mostDiscussed": "Mest diskuterade", "app.containers.IdeaCards.newest": "<PERSON>", "app.containers.IdeaCards.noFilteredResults": "Inga resultat hittades. Prova ett annat filter eller en annan sökterm.", "app.containers.IdeaCards.numberResults": "Resultat ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "Alternativ", "app.containers.IdeaCards.petitions": "Framställningar", "app.containers.IdeaCards.popular": "De flesta röstade", "app.containers.IdeaCards.projectFilterTitle": "Projekt", "app.containers.IdeaCards.projectTerm": "Projekt", "app.containers.IdeaCards.proposals": "Förslag till beslut", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON> och svar", "app.containers.IdeaCards.random": "Slumpmässig", "app.containers.IdeaCards.resetFilters": "<PERSON><PERSON>tä<PERSON> filter", "app.containers.IdeaCards.showXResults": "Visa {ideasCount, plural, one {# resultat} other {# resultat}}", "app.containers.IdeaCards.sortTitle": "Sortering", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Ämnen", "app.containers.IdeaCards.topicsTitle": "Ämnen", "app.containers.IdeaCards.trending": "Trender", "app.containers.IdeaCards.tryDifferentFilters": "Inga resultat hittades. Prova ett annat filter eller en annan sökterm.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} kommentarer} one {{ideasCount} kommentar} other {{ideasCount} kommentarer}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} bidrag} one {{ideasCount} ~ bidrag} other {{ideasCount} bidrag}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ~ idéer} one {{ideasCount} ~ idé} other {{ideasCount} idéer}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiativ} one {{ideasCount} initiativ} other {{ideasCount} initiativ}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} optioner} one {{ideasCount} ~ option} other {{ideasCount} optioner}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} framställningar} one {{ideasCount} framställning} other {{ideasCount} framställningar}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} ~ projekt} one {{ideasCount} ~ projekt} other {{ideasCount} projekt}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} förslag} one {{ideasCount} ~ förslag} other {{ideasCount} förslag}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} fr<PERSON><PERSON>} one {{ideasCount} fr<PERSON><PERSON>} other {{ideasCount} fr<PERSON><PERSON>}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# resultat} other {# resultat}}", "app.containers.IdeasEditPage.contributionFormTitle": "<PERSON><PERSON><PERSON> bidrag", "app.containers.IdeasEditPage.editedPostSave": "Spara", "app.containers.IdeasEditPage.fileUploadError": "En eller flera filer kunde inte laddas upp. Kontrollera filstorleken och formatet och försök igen.", "app.containers.IdeasEditPage.formTitle": "<PERSON><PERSON>a idé", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Redigera ditt inlägg. Lägg till ny information och ändra gammal information.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Redigera {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Redigera initiativ", "app.containers.IdeasEditPage.issueFormTitle": "<PERSON><PERSON><PERSON> kommentar", "app.containers.IdeasEditPage.optionFormTitle": "Redigera alternativ", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON><PERSON> f<PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "Redigera projekt", "app.containers.IdeasEditPage.proposalFormTitle": "Redigera förslag", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON><PERSON> fråga", "app.containers.IdeasEditPage.save": "Spara", "app.containers.IdeasEditPage.submitApiError": "Det fanns ett problem med att skicka in formuläret. Vänligen kontrollera om det finns några fel och försök igen.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Alla inmatningar publicerade", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Utforska alla indata som har publicerats på plattformen för deltagande för {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Inlägg | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Inlägg", "app.containers.IdeasIndexPage.loadMore": "<PERSON><PERSON><PERSON> in mer...", "app.containers.IdeasIndexPage.loading": "<PERSON><PERSON><PERSON> in...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Som standard kommer dina inlägg att kopplas till din profil, om du inte väljer det här alternativet.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Skicka anonymt", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Profilens synlighet", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Denna undersökning är för närvarande inte öppen för svar. Vänligen återkom till projektet för mer information.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Denna undersökning är för närvarande inte aktiv.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Återgå till projektet", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Du har redan besvarat denna enk<PERSON>.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Undersökning inlämnad", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Tack för ditt svar!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Bidragsbeskrivningen måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON> brö<PERSON>t måste vara längre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Bidragstiteln måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Bidragstiteln måste vara mer än {limit} tecken lång.", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Vänligen välj minst en medhjälpare", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Idébeskrivningen måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Idébeskrivningen måste vara längre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Ange en beskrivning", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Titeln på idén måste vara kortare än {limit} tecken.", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Idétiteln måste vara längre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Initiativbeskrivningen måste vara mindre än {limit} tecken lång", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Initiativbeskrivningen måste vara mer än {limit} tecken lång", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "<PERSON>ts titel måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "<PERSON>ts titel måste innehålla mer än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Problembeskrivningen måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Problembeskrivningen måste vara längre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Problemtiteln måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Titeln på numret måste vara mer än {limit} tecken lång", "app.containers.IdeasNewPage.ajv_error_number_required": "Detta fält är obligatoriskt, ange ett giltigt nummer", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Alternativbeskrivningen måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Alternativbeskrivningen måste innehålla mer än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Alternativtiteln måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "<PERSON>ts titel måste vara mer än {limit} tecken lång.", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "<PERSON><PERSON><PERSON><PERSON> minst ett ämne", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Beskrivningen av petitionen måste vara mindre än {limit} tecken lång", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Beskrivningen av framställningen måste vara längre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Petitionens titel måste vara mindre än {limit} tecken lång", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Petitionens titel måste innehålla mer än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Projektbeskrivningen måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Projektbeskrivningen måste vara längre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Projekttiteln måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Projektets titel måste vara mer än {limit} tecken lång.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Förslagsbeskrivningen måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Förslagsbeskrivningen måste innehålla mer än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Förslagets titel måste innehålla mindre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Förslagets titel måste innehålla mer än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "<PERSON><PERSON> ett nummer", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "<PERSON><PERSON> ett nummer", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Frågebeskrivningen måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Frågebeskrivningen måste vara längre än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Frågetiteln måste vara kortare än {limit} tecken", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON> titel måste vara längre än {limit} tecken.", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON>e en titel", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Bidragsbeskrivningen måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Bidragsbeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Bidragstiteln måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Bidragstiteln måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Idébeskrivningen måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Idébeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON>e en titel", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Idétiteln måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Idétiteln måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Du kan ha använt ett eller flera ord som anses vara svordomar av {guidelinesLink}. <PERSON>ndra din text för att ta bort svordomar som kanske förekommer.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Initiativbeskrivningen måste vara mindre än 80 tecken lång", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Initiativbeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Initiativets titel måste vara mindre än 80 tecken lång", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Initiativets titel måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Problembeskrivningen måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Problembeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Problemtiteln måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Problemtiteln måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Alternativbeskrivningen måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Alternativbeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Alternativtiteln måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Alternativtiteln måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Beskrivningen av framställningen måste vara mindre än 80 tecken lång", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Beskrivningen av framställningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Petitionens titel måste vara mindre än 80 tecken lång", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Petitionens titel måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Projektbeskrivningen måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Projektbeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Projekttiteln måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Projekttiteln måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Förslagsbeskrivningen måste vara mindre än 80 tecken lång", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Förslagsbeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Förslagets titel måste vara mindre än 80 tecken lång", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Förslagets titel måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Ange en beskrivning", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Frågebeskrivningen måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Frågebeskrivningen måste vara minst 30 tecken lång", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Frågetiteln måste vara kortare än 80 tecken", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Frågetiteln måste vara minst 10 tecken lång", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, jag vill lämna", "app.containers.IdeasNewPage.contributionMetaTitle1": "Lägg till nytt bidrag till projektet | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Redigera under<PERSON>ö<PERSON>ning", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Publicera ett bidrag och gå med i konversationen på {orgName}s plattform för deltagande.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "<PERSON><PERSON><PERSON> till ny idé i projektet | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Lägg till nytt initiativ i projektet | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Lägg till ny fråga i projektet | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Är du säker på att du vill lämna?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "<PERSON><PERSON> ut<PERSON>t till svar har sparats privat och du kan återkomma för att slutföra detta senare.", "app.containers.IdeasNewPage.leaveSurvey": "Undersökning om ledighet", "app.containers.IdeasNewPage.leaveSurveyText": "<PERSON>a svar kommer inte att sparas.", "app.containers.IdeasNewPage.optionMetaTitle1": "<PERSON>ägg till ett nytt alternativ i projektet | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "<PERSON>ägg till ny petition till projektet | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Lägg till nytt projekt till projekt | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Lägg till nytt förslag till projekt | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Lägg till ny fråga i projektet | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Acceptera inbjudan", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Inbjudan till medsponsring", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Medförslagsställare", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Du har blivit inbjuden att bli en medsponsor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.Cosponsorship.pending": "väntande", "app.containers.IdeasShow.MetaInformation.attachments": "Bilagor", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Aktuell status", "app.containers.IdeasShow.MetaInformation.location": "Plats", "app.containers.IdeasShow.MetaInformation.postedBy": "Publicerades av", "app.containers.IdeasShow.MetaInformation.similar": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.topics": "Ämnen", "app.containers.IdeasShow.commentCTA": "Lägg till en kommentar", "app.containers.IdeasShow.contributionEmailSharingBody": "<PERSON><PERSON><PERSON> det här bidraget {postTitle} på {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON> det här bidraget: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Tack för att du skickade ditt bidrag!", "app.containers.IdeasShow.contributionTwitterMessage": "<PERSON><PERSON><PERSON> det här bidraget: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> det här bidraget: {postTitle}", "app.containers.IdeasShow.currentStatus": "Aktuell status", "app.containers.IdeasShow.deletedUser": "okänd författare", "app.containers.IdeasShow.ideaEmailSharingBody": "<PERSON><PERSON><PERSON> min idé {ideaTitle} på {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "<PERSON><PERSON><PERSON> min idé: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "<PERSON><PERSON><PERSON> den här idén: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON> den här idén: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "<PERSON><PERSON><PERSON> den här kommentaren: {postTitle}", "app.containers.IdeasShow.imported": "Importerad", "app.containers.IdeasShow.importedTooltip": "Denna {inputTerm} samlades in offline och laddades automatiskt upp till plattformen.", "app.containers.IdeasShow.initiativeEmailSharingBody": "<PERSON><PERSON><PERSON> detta initiativ '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "<PERSON><PERSON><PERSON> detta initiativ: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Tack för att du skickade in ditt initiativ!", "app.containers.IdeasShow.initiativeTwitterMessage": "<PERSON><PERSON><PERSON> detta initiativ: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON> detta initiativ: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "<PERSON><PERSON><PERSON> den här kommentaren {postTitle} på {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "<PERSON><PERSON><PERSON> den här kommentaren: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Tack för att du skickade din kommentar!", "app.containers.IdeasShow.issueTwitterMessage": "<PERSON><PERSON><PERSON> den här kommentaren: {postTitle}", "app.containers.IdeasShow.metaTitle": "Inmatning: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "<PERSON><PERSON><PERSON> det här alternativet {postTitle} på {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "<PERSON><PERSON><PERSON> det här alternativet: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Ditt alternativ har publicerats!", "app.containers.IdeasShow.optionTwitterMessage": "<PERSON><PERSON><PERSON> det här alternativet: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "<PERSON><PERSON><PERSON> det här alternativet: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "<PERSON><PERSON><PERSON> denna namnin<PERSON> '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON> denna na<PERSON>: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Tack för att du skickade in din petition!", "app.containers.IdeasShow.petitionTwitterMessage": "<PERSON><PERSON><PERSON> denna na<PERSON>: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON> denna na<PERSON>: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "<PERSON><PERSON><PERSON> det här projektet {postTitle} på {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "<PERSON><PERSON><PERSON> det här projektet: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Tack för att du skickade ditt projekt!", "app.containers.IdeasShow.projectTwitterMessage": "<PERSON><PERSON><PERSON> det här projektet: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "<PERSON><PERSON><PERSON> det här projektet: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "<PERSON><PERSON><PERSON> de<PERSON> fö<PERSON> '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON> fö<PERSON>: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Tack för att du skickade in ditt förslag!", "app.containers.IdeasShow.proposalTwitterMessage": "<PERSON><PERSON><PERSON> fö<PERSON>: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "<PERSON><PERSON><PERSON> fö<PERSON>: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Tid kvar att rösta:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} av {votingThreshold} erforderliga röster", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.days": "dagar", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "<PERSON><PERSON><PERSON> r<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.hours": "timmar", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status och omröstningar", "app.containers.IdeasShow.proposals.VoteControl.minutes": "min", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Mer information", "app.containers.IdeasShow.proposals.VoteControl.vote": "Omr<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.voted": "Röstade", "app.containers.IdeasShow.proposals.VoteControl.votedText": "<PERSON> kommer att få ett meddelande när detta initiativ går till nästa steg. {x, plural, =0 {There's {xDays} left.} one {Det finns {xDays} kvar.} other {Det finns {xDays} kvar.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Din röst har skickats in!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "<PERSON><PERSON><PERSON><PERSON> kan du inte rösta om detta förslag. <PERSON><PERSON><PERSON> varför på {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {mindre än en dag} one {en dag} other {# dagar}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {inga röster} one {1 röst} other {# röster}}", "app.containers.IdeasShow.questionEmailSharingBody": "Gå med i diskussionen om den här frågan {postTitle} på {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Gå med i diskussionen: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Din fråga har publicerats!", "app.containers.IdeasShow.questionTwitterMessage": "Gå med i diskussionen: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Gå med i diskussionen: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Var<PERSON><PERSON>r vill du anmäla det här som skräppost?", "app.containers.IdeasShow.share": "Dela", "app.containers.IdeasShow.sharingModalSubtitle": "<PERSON><PERSON> fler personer och gör din röst hörd.", "app.containers.IdeasShow.sharingModalTitle": "Tack för att du skickade din idé!", "app.containers.Navbar.completeOnboarding": "Fullständig introduktion", "app.containers.Navbar.completeProfile": "Fullständig profil", "app.containers.Navbar.confirmEmail2": "Bekräfta e-post", "app.containers.Navbar.unverified": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "app.containers.Navbar.verified": "Verifierad", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON>", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON><PERSON>", "app.containers.NewAuthModal.completeYourProfile": "Komplettera din profil", "app.containers.NewAuthModal.confirmYourEmail": "Bekräfta din e-postadress", "app.containers.NewAuthModal.logIn": "Logga in", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "<PERSON><PERSON><PERSON> igenom villkoren nedan för att fortsätta.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Vänligen fyll i din profil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Gå tillbaka till inloggningsalternativen", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Har du inget konto? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Registrera dig", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Koden måste ha 4 siffror.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Fortsätt med FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Inga autentiseringsmetoder är aktiverade på den här plattformen.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Genom att fortsätta godkänner du att få e-post från denna plattform. Du kan välja vilka e-postmeddelanden du vill ta emot på sidan \"Mina inställningar\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-post", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Ange en e-postadress i rätt format, t.ex. <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "<PERSON><PERSON> en e-postadress", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Ange din e-postadress för att fortsätta.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Har du glömt ditt lösenord?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "<PERSON>gg<PERSON> in på ditt konto: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "<PERSON><PERSON> l<PERSON>", "app.containers.NewAuthModal.steps.Password.password": "L<PERSON>senord", "app.containers.NewAuthModal.steps.Password.rememberMe": "<PERSON>m ih<PERSON>g mig", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Markera inte om du använder en offentlig dator", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Fortsätt nu att delta.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Din identitet har verifierats. Du är nu en fullvärdig medlem av gemenskapen på den här plattformen.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Du är nu verifierad!", "app.containers.NewAuthModal.steps.close": "Stäng", "app.containers.NewAuthModal.steps.continue": "Fortsätt", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Vad är du intresserad av?", "app.containers.NewAuthModal.youCantParticipate": "Du kan inte delta", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {inga ovisade aviseringar} one {1 ovisad avisering} other {# ovisade aviseringar}}", "app.containers.NotificationMenu.adminRightsReceived": "Du är nu administratör av plattformen", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Din kommentar på \"{postTitle}\" har tagits bort av en administratör eftersom\n      {reasonCode, select, irrelevant {den är irrelevant} inappropriate {dess innehåll är olämpligt} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} accepterade din inbjudan till medsponsring", "app.containers.NotificationMenu.deletedUser": "<PERSON><PERSON>nd författare", "app.containers.NotificationMenu.error": "Det gick inte att läsa in aviseringar", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} kommenterat internt på en input som tilldelats dig", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} kommenterat internt på ett inlägg som du kommenterat internt", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} kommenterat internt på en input i ett projekt som du hanterar", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} kommenterade internt på en icke tilldelad inlaga i ett icke hanterat projekt", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} kommenterade din interna kommentar", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} uppmanade dig att sponsra ett bidrag", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} bjudit in dig att sponsra en idé", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} uppmanade dig att stödja ett förslag", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} uppmanade dig att stödja en fråga", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} uppmanade dig att sponsra ett alternativ", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} uppmanade dig att medverka i en framställning", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} b<PERSON><PERSON> in dig att sponsra ett projekt", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} uppmanat dig att medverka som sponsor för ett förslag", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} uppmanade dig att ställa en fråga", "app.containers.NotificationMenu.loadMore": "<PERSON><PERSON><PERSON> in mer...", "app.containers.NotificationMenu.loading": "<PERSON><PERSON><PERSON> in aviseringar...", "app.containers.NotificationMenu.mentionInComment": "{name} nämnde dig i en kommentar", "app.containers.NotificationMenu.mentionInInternalComment": "{name} nämnde dig i en intern kommentar", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} nämnde dig i en officiell uppdatering", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Du har inte skickat in din enkät", "app.containers.NotificationMenu.noNotifications": "Du har inga aviseringar än", "app.containers.NotificationMenu.notificationsLabel": "Notiser", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} gav en officiell uppdatering om ett bidrag som du följer", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} gav en officiell uppdatering om en idé som du följer", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} gav en officiell uppdatering om ett initiativ som du följer", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} gav en officiell uppdatering om en fråga som du följer", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} gav en officiell uppdatering om ett alternativ som du följer", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gav en officiell uppdatering om en framställning som du följer", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} gav en officiell uppdatering om ett projekt du följer", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gav en officiell uppdatering om ett förslag som du följer", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} gav en officiell uppdatering om en fråga som du följer", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} till<PERSON><PERSON> dig", "app.containers.NotificationMenu.projectModerationRightsReceived": "Du är nu projektledare för {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} gick in i en ny fas", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} går in i en ny fas {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Ett nytt projekt publicerades", "app.containers.NotificationMenu.projectReviewRequest": "{name} begärde godkännande för att publicera projektet \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} godk<PERSON>nt \"{projectTitle}\" för publicering", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} status har ändrats till {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} nådde rösttröskeln", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} tackade ja till din inbjudan", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} kommenterat ett bidrag som du följer", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} kommenterade en idé som du följer", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} kommenterat ett initiativ som du följer", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} kommenterat en fråga som du följer", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} kommenterat ett alternativ som du följer", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} kommenterat ett upprop som du följer", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} kommenterat ett projekt som du följer", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} kommenterat ett förslag som du följer", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} har kommenterat ett förslag som du följer", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} rapporterade \"{postTitle}\" som spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reagerade på din kommentar", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} rapporterade en kommentar på \"{postTitle}\" som spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Du har inte skickat in dina val", "app.containers.NotificationMenu.votingBasketSubmitted": "<PERSON><PERSON> val är registrerat", "app.containers.NotificationMenu.votingLastChance": "<PERSON>sta chansen att rösta på {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} omröstningsresultatet klart", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} till<PERSON>de dig {postTitle}", "app.containers.PasswordRecovery.emailError": "Det här ser inte ut som en giltig e-postadress", "app.containers.PasswordRecovery.emailLabel": "E-post", "app.containers.PasswordRecovery.emailPlaceholder": "Min e-postadress", "app.containers.PasswordRecovery.helmetDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt l<PERSON>-sida", "app.containers.PasswordRecovery.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt l<PERSON>", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Om den här e-postadressen är registrerad på plattformen har en länk för återställning av lösenordet skickats.", "app.containers.PasswordRecovery.resetPassword": "Skicka en länk för återställning av lösenord", "app.containers.PasswordRecovery.submitError": "Vi kunde inte hitta ett konto som har länkats till den här e-postadressen. Du kan försöka registrera dig istället.", "app.containers.PasswordRecovery.subtitle": "Vart kan vi skicka en länk för att välja ett nytt lösenord?", "app.containers.PasswordRecovery.title": "Återställning av lösenord", "app.containers.PasswordReset.helmetDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt l<PERSON>-sida", "app.containers.PasswordReset.helmetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt l<PERSON>", "app.containers.PasswordReset.login": "Logga in", "app.containers.PasswordReset.passwordError": "Lösenordet måste vara minst 8 tecken långt", "app.containers.PasswordReset.passwordLabel": "L<PERSON>senord", "app.containers.PasswordReset.passwordPlaceholder": "Nytt lösenord", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON><PERSON> l<PERSON>ord har uppdaterats.", "app.containers.PasswordReset.pleaseLogInMessage": "<PERSON><PERSON><PERSON> in med ditt nya lösen<PERSON>.", "app.containers.PasswordReset.requestNewPasswordReset": "Begär en ny lösenordsåterställning", "app.containers.PasswordReset.submitError": "Något gick fel. Försök igen senare.", "app.containers.PasswordReset.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt l<PERSON>", "app.containers.PasswordReset.updatePassword": "Bekräfta nytt lösenord", "app.containers.ProjectFolderCards.allProjects": "Alla projekt", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} arb<PERSON>r för n<PERSON> med", "app.containers.ProjectFolderShowPage.editFolder": "Redigera mapp", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Information om det här projektet", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON><PERSON>r", "app.containers.ProjectFolderShowPage.seeLess": "Se mindre", "app.containers.ProjectFolderShowPage.share": "Dela", "app.containers.Projects.PollForm.document": "Dokument", "app.containers.Projects.PollForm.formCompleted": "Tack! Ditt svar har mottagits.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Du har redan gjort den här omröstningen.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Denna omröstning kan endast genomföras när denna fas är aktiv.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Den här omröstningen är för närvarande inte aktiverad", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Det är för närvarande omöjligt att göra den här omröstningen.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Omröstningen är inte längre tillgänglig eftersom det här projektet inte längre är aktivt.", "app.containers.Projects.PollForm.sendAnswer": "<PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_phase": "Fas {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Översikt över faser", "app.containers.Projects.a11y_titleInputs": "Alla indata som har skickats till det här projektet", "app.containers.Projects.a11y_titleInputsPhase": "Alla indata som har skickats till den här fasen", "app.containers.Projects.accessRights": "Behörigheter", "app.containers.Projects.addedToBasket": "Lades till i din varukorg", "app.containers.Projects.allocateBudget": "Fördela din budget", "app.containers.Projects.archived": "Arkiverad", "app.containers.Projects.basketSubmitted": "Din varukorg har skickats!", "app.containers.Projects.contributions": "Bidrag", "app.containers.Projects.createANewPhase": "Skapa en ny fas", "app.containers.Projects.currentPhase": "Aktuell fas", "app.containers.Projects.document": "Dokument", "app.containers.Projects.editProject": "Redigera projekt", "app.containers.Projects.emailSharingBody": "Vad tycker du om det här initiativet? R<PERSON><PERSON> på det och dela diskussionen på {initiativeUrl} för att göra din röst hörd!", "app.containers.Projects.emailSharingSubject": "<PERSON><PERSON><PERSON> mitt initiativ: {initiativeTitle}.", "app.containers.Projects.endedOn": "Slutade {date}", "app.containers.Projects.events": "<PERSON><PERSON><PERSON>", "app.containers.Projects.header": "Projekt", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Information", "app.containers.Projects.initiatives": "Initiativ", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Granska dokumentet", "app.containers.Projects.invisibleTitlePhaseAbout": "<PERSON><PERSON> den här fasen", "app.containers.Projects.invisibleTitlePoll": "<PERSON><PERSON><PERSON>", "app.containers.Projects.invisibleTitleSurvey": "<PERSON><PERSON>r <PERSON>ö<PERSON>", "app.containers.Projects.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "Du tittar på data i realtid. Deltagarantalet uppdateras kontinuerligt för administratörer. Observera att vanliga användare ser cachad data, vilket kan resultera i små skillnader i siffrorna.", "app.containers.Projects.location": "Plats:", "app.containers.Projects.manageBasket": "<PERSON><PERSON> var<PERSON>", "app.containers.Projects.meetMinBudgetRequirement": "Uppfyll minimibudgeten för att skicka din varukorg.", "app.containers.Projects.meetMinSelectionRequirement": "Uppfyll det nödvändiga urvalet för att skicka din varukorg.", "app.containers.Projects.metaTitle1": "Projekt: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Minimibudget krävs", "app.containers.Projects.myBasket": "<PERSON><PERSON>", "app.containers.Projects.navPoll": "Omr<PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "Undersö<PERSON>ning", "app.containers.Projects.newPhase": "Ny fas", "app.containers.Projects.nextPhase": "Nästa fas", "app.containers.Projects.noEndDate": "Inget slutdatum", "app.containers.Projects.noItems": "Du har inte valt några objekt än", "app.containers.Projects.noPastEvents": "Inga tidigare evenemang att visa", "app.containers.Projects.noPhaseSelected": "Ingen fas har valts", "app.containers.Projects.noUpcomingOrOngoingEvents": "Inga kommande eller pågående evenemang är för närvarande schemalagda.", "app.containers.Projects.offlineVotersTooltip": "<PERSON>na siffra <PERSON>g<PERSON> inte några offline rösträkningar.", "app.containers.Projects.options": "Alternativ", "app.containers.Projects.participants": "Deltagare", "app.containers.Projects.participantsTooltip4": "Denna siffra återspeglar även anonyma enkätsvar. Anonyma enkätsvar är möjliga om enkäterna är öppna för alla (se fliken {accessRightsLink} för detta projekt).", "app.containers.Projects.pastEvents": "<PERSON><PERSON><PERSON><PERSON> even<PERSON>g", "app.containers.Projects.petitions": "Framställningar", "app.containers.Projects.phases": "<PERSON><PERSON><PERSON>", "app.containers.Projects.previousPhase": "Föregående fas", "app.containers.Projects.project": "Projekt", "app.containers.Projects.projectTwitterMessage": "<PERSON><PERSON><PERSON> din röst hörd! Delta i {projectName} | {orgName}", "app.containers.Projects.projects": "Projekt", "app.containers.Projects.proposals": "Förslag till beslut", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readMore": "<PERSON><PERSON><PERSON>r", "app.containers.Projects.removeItem": "Ta bort objekt", "app.containers.Projects.requiredSelection": "Obligatoriskt val", "app.containers.Projects.reviewDocument": "Granska dokumentet", "app.containers.Projects.seeTheContributions": "Se bidragen", "app.containers.Projects.seeTheIdeas": "Se idéerna", "app.containers.Projects.seeTheInitiatives": "Se initiativen", "app.containers.Projects.seeTheIssues": "Se kommentarerna", "app.containers.Projects.seeTheOptions": "Se <PERSON>n", "app.containers.Projects.seeThePetitions": "Se framställningarna", "app.containers.Projects.seeTheProjects": "Se projekten", "app.containers.Projects.seeTheProposals": "Se förslagen", "app.containers.Projects.seeTheQuestions": "Se frågorna", "app.containers.Projects.seeUpcomingEvents": "Se kommande evenemang", "app.containers.Projects.share": "Dela", "app.containers.Projects.shareThisProject": "Dela det här projektet", "app.containers.Projects.submitMyBasket": "Skicka in korg", "app.containers.Projects.survey": "Undersö<PERSON>ning", "app.containers.Projects.takeThePoll": "<PERSON><PERSON><PERSON>", "app.containers.Projects.takeTheSurvey": "<PERSON><PERSON>r <PERSON>ö<PERSON>", "app.containers.Projects.timeline": "Tidslinje", "app.containers.Projects.upcomingAndOngoingEvents": "Kommande och pågående evenemang", "app.containers.Projects.upcomingEvents": "Kommande evenemang", "app.containers.Projects.whatsAppMessage": "{projectName} | från plattformen för deltagande för {orgName}", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "Upptäck alla pågående projekt för {orgName} för att förstå hur du kan delta.\nKom och diskutera lokala projekt som är viktigast för dig.", "app.containers.ProjectsIndexPage.metaTitle1": "Projekt | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projekt", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Jag vill anmäla mig frivilligt", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "{signInLink} eller {signUpLink} först för att anmäla dig frivilligt för den här aktiviteten", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Deltagande är för nä<PERSON>rande inte öppet för denna aktivitet.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "logga in", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "registrera dig", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Jag drar tillbaka mitt erbjudande om att anmäla mig frivilligt", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {inga anmälde sig frivilligt} one {# anmälde sig frivilligt} other {# anmälde sig frivilligt}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Varning: Den inbäddade enkäten Den inbäddade enkäten kan ha tillgänglighetsproblem för användare av skärmläsare. Om du upplever några utmaningar, vänligen kontakta plattformsadministratören för att få en länk till undersökningen från den ursprungliga plattformen. Alternativt kan du begära andra sätt att fylla i enkäten.", "app.containers.ProjectsShowPage.process.survey.survey": "Undersö<PERSON>ning", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "{logInLink} på plattformen först för att få veta om du kan delta i den här undersökningen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Den här undersökningen kan endast göras när denna fas på tidslinjen är aktiv.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Vänligen {completeRegistrationLink} för att svara på enkäten.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Den här undersökningen är för närvarande inte aktiverad", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "<PERSON><PERSON><PERSON> att göra den här undersökningen krävs verifiering av din identitet. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Undersökningen är inte längre tillgänglig, eftersom projektet inte längre är aktivt.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "fullständig registrering", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "logga in", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "Registrera dig", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verifiera ditt konto nu.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Endast vissa användare kan granska detta dokument. Vänligen ange {signUpLink} eller {logInLink} först.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Detta dokument kan endast granskas när denna fas är aktiv.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Vänligen {completeRegistrationLink} för att granska dokumentet.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "<PERSON><PERSON><PERSON><PERSON> har du inte rätt att granska detta dokument.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "<PERSON><PERSON><PERSON> att granska detta dokument krävs verifiering av ditt konto. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Dokumentet är inte längre tillgäng<PERSON>gt, eftersom detta projekt inte längre är aktivt.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(inkl. 1 offline)} other {(inkl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 val} other {# val}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Procentandel av deltagarna som valde detta alternativ.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Den procentandel av det totala antalet röster som detta alternativ fick.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Kostnad:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Visa mer", "app.containers.ReactionControl.a11y_likesDislikes": "Totalt antal likes: {likesCount}, totalt antal ogillanden: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Du har avbrutit din motvilja mot denna inmatning framgångsrikt.", "app.containers.ReactionControl.cancelLikeSuccess": "Du har avbrutit din prenumeration på den här bidraget framgångsrikt.", "app.containers.ReactionControl.dislikeSuccess": "Du ogillade denna inmatning framgångsrikt.", "app.containers.ReactionControl.likeSuccess": "Du gillade den här bidraget framgångsrikt.", "app.containers.ReactionControl.reactionErrorSubTitle": "På grund av ett fel kunde din reaktion inte registreras. Vänligen försök igen om några minuter.", "app.containers.ReactionControl.reactionSuccessTitle": "Din reaktion har registrerats framgångsrikt!", "app.containers.ReactionControl.vote": "<PERSON><PERSON><PERSON>", "app.containers.ReactionControl.voted": "Röstade", "app.containers.SearchInput.a11y_cancelledPostingComment": "Avbruten postning av kommentar.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} kommentarer har laddats.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# händelser har laddats} one {# händelse har laddats} other {# händelser har laddats}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# resultat har laddats} one {# resultat har laddats} other {# resultat har laddats}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# sökresultat har laddats} one {# sökresultat har laddats} other {# sökresultat har laddats}}.", "app.containers.SearchInput.removeSearchTerm": "Ta bort sökterm", "app.containers.SearchInput.searchAriaLabel": "<PERSON>ö<PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON>ö<PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON>ö<PERSON>", "app.containers.SearchInput.searchTerm": "Sökterm: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect är den lösning som har föreslagits av den franska staten för att göra registreringen till mer än 700 onlinetjänster säkrare och enklare.", "app.containers.SignIn.or": "Eller", "app.containers.SignIn.signInError": "Den angivna informationen är inte korrekt. Klicka på Har du glömt ditt lösenord? för att återställa ditt lösenord.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Använd FranceConnect för att logga in, registrera dig eller verifiera ditt konto.", "app.containers.SignIn.whatIsFranceConnect": "Vad är FranceConnect?", "app.containers.SignUp.adminOptions2": "<PERSON><PERSON><PERSON> administrat<PERSON><PERSON> och projektledare", "app.containers.SignUp.backToSignUpOptions": "Gå tillbaka till registreringsalternativ", "app.containers.SignUp.continue": "Fortsätta", "app.containers.SignUp.emailConsent": "Genom att registrera dig godkänner du att ta emot e-postmeddelanden från den här plattformen. Du kan välja vilka e-postmeddelanden du vill ta emot på sidan Mina inställningar.", "app.containers.SignUp.emptyFirstNameError": "<PERSON><PERSON> ditt förna<PERSON>n", "app.containers.SignUp.emptyLastNameError": "<PERSON><PERSON> ditt efter<PERSON>n", "app.containers.SignUp.firstNamesLabel": "Förnamn", "app.containers.SignUp.goToLogIn": "Har du redan ett konto? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "<PERSON>ag har läst och godkänner {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "<PERSON>ag har läst och godkänner {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON>ag godkänner att dessa data kommer att användas på mitgestalten.wien.gv.at. Ytterligare information finns på {link}.", "app.containers.SignUp.invitationErrorText": "<PERSON> inbjudan har gått ut eller har redan använts. Om du redan har använt inbjudningslänken för att skapa ett konto kan du försöka logga in. Annars kan du registrera dig för att skapa ett nytt konto.", "app.containers.SignUp.lastNameLabel": "E<PERSON>nam<PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "<PERSON><PERSON><PERSON><PERSON> dina fokusområden för att få information om dem:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "<PERSON><PERSON><PERSON>j dina favoritämnen för att få meddelanden om dem:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Spara inställningar", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Hoppa över för <PERSON>", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Godkänn vår sekretesspolicy för att fortsätta", "app.containers.SignUp.signUp2": "Registrera dig", "app.containers.SignUp.skip": "Hoppa över det här steget", "app.containers.SignUp.tacError": "Godkänn våra villkor för att fortsätta", "app.containers.SignUp.thePrivacyPolicy": "sekretesspolicyn", "app.containers.SignUp.theTermsAndConditions": "vill<PERSON><PERSON>", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {Det verkar som om du har försökt registrera dig tidigare utan att slutföra processen. <PERSON><PERSON><PERSON> på Logga in istället och använd uppgifterna som du valde under det föregående försöket.} other {Något gick fel. Försök igen senare.}}", "app.containers.SignUp.viennaConsentEmail": "E-postadress", "app.containers.SignUp.viennaConsentFirstName": "Förnamn", "app.containers.SignUp.viennaConsentFooter": "Du kan ändra din profilinformation när du har loggat in. Om du redan har ett konto med samma e-postadress på mitgestalten.wien.gv.at kommer det att länkas till ditt nuvarande konto.", "app.containers.SignUp.viennaConsentHeader": "Följande data kommer att överföras:", "app.containers.SignUp.viennaConsentLastName": "E<PERSON>nam<PERSON>", "app.containers.SignUp.viennaConsentUserName": "Användarnamn", "app.containers.SignUp.viennaDataProtection": "wiens sekretesspolicy", "app.containers.SiteMap.contributions": "Bidrag", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.options": "Alternativ", "app.containers.SiteMap.projects": "Projekt", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Rapport", "app.containers.SpamReport.buttonSuccess": "Lyckades", "app.containers.SpamReport.inappropriate": "Det är olämpligt eller stötande", "app.containers.SpamReport.messageError": "Det uppstod ett fel när formuläret skickades, försök igen.", "app.containers.SpamReport.messageSuccess": "Din rapport har skickats", "app.containers.SpamReport.other": "<PERSON><PERSON>", "app.containers.SpamReport.otherReasonPlaceholder": "Beskrivning", "app.containers.SpamReport.wrong_content": "Det här är inte relevant", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Ta bort profilbild", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Dina röster på förslag som fortfarande är öppna för omröstning kommer att raderas. Röster på förslag där omröstningsperioden har avslutats kommer inte att raderas.", "app.containers.UsersEditPage.addPassword": "Lägg till lösenord", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Att delta i projekt som kräver verifiering.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verifiera din identitet", "app.containers.UsersEditPage.bio": "Om dig", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Du kan inte redigera det här fältet eftersom det innehåller verifierad information.", "app.containers.UsersEditPage.buttonSuccessLabel": "Lyckades", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Ändra e-post", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON><PERSON><PERSON> här för att uppdatera din verifiering.", "app.containers.UsersEditPage.conditionsLinkText": "v<PERSON>ra vill<PERSON>", "app.containers.UsersEditPage.contactUs": "<PERSON>nnu en anledning att lämna? {feedbackLink} så kanske vi kan hjälpa dig.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON>i kommer att sakna dig.", "app.containers.UsersEditPage.deleteMyAccount": "Ta bort mitt konto", "app.containers.UsersEditPage.deleteYourAccount": "Ta bort ditt konto", "app.containers.UsersEditPage.deletionSection": "Ta bort ditt konto", "app.containers.UsersEditPage.deletionSubtitle": "Den här åtgärden kan inte ångras. Innehållet du publicerar på plattformen kommer att anonymiseras. Om du vill ta bort allt ditt innehåll kan du kontakta oss på <EMAIL>.", "app.containers.UsersEditPage.email": "E-post", "app.containers.UsersEditPage.emailEmptyError": "<PERSON><PERSON> en e-postadress", "app.containers.UsersEditPage.emailInvalidError": "Ange en e-postadress i rätt format, t.ex. <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Be<PERSON><PERSON><PERSON> för oss", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Förnamn", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON> f<PERSON>n", "app.containers.UsersEditPage.h1": "<PERSON>", "app.containers.UsersEditPage.h1sub": "Redigera din kontoinformation", "app.containers.UsersEditPage.image": "Avatarbild", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON><PERSON> för att välja en profilbild (max. 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Alla inställningar för din profil", "app.containers.UsersEditPage.language": "Språk", "app.containers.UsersEditPage.lastName": "E<PERSON>nam<PERSON>", "app.containers.UsersEditPage.lastNameEmptyError": "Ange ett efternamn", "app.containers.UsersEditPage.loading": "<PERSON><PERSON><PERSON> in...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Du kan ändra din e-postadress eller ditt lösenord här.", "app.containers.UsersEditPage.loginCredentialsTitle": "Inloggningsuppgifter", "app.containers.UsersEditPage.messageError": "Vi kunde inte spara din profil. Försök igen senare <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON> profil har sparats.", "app.containers.UsersEditPage.metaDescription": "Det här är sidan med profilinställningar för {firstName} {lastName} på onlineplattformen för deltagande för {tenantName}. Här kan du verifiera din identitet, redigera din kontoinformation, ta bort ditt konto och redigera dina e-postinställningar.", "app.containers.UsersEditPage.metaTitle1": "Profilinställningssida för {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "<PERSON><PERSON><PERSON> du klickar på den här knappen kan vi inte återställa ditt konto på något sätt.", "app.containers.UsersEditPage.noNameWarning2": "Ditt namn visas för närvarande på plattformen som: \"{displayName}\" eftersom du inte har angett ditt namn. Detta är ett autogenererat namn. Om du vill ändra det, vänligen ange ditt namn nedan.", "app.containers.UsersEditPage.notificationsSubTitle": "Vilka typer av e-postmeddelanden vill du få?", "app.containers.UsersEditPage.notificationsTitle": "E-postmeddelanden", "app.containers.UsersEditPage.password": "Välj ett nytt lösenord", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON>e ett lösenord som är minst {minimumPasswordLength} tecken långt", "app.containers.UsersEditPage.passwordAddSection": "Lägg till ett lösenord", "app.containers.UsersEditPage.passwordAddSubtitle2": "Ange ett lösenord och logga in på plattformen utan att behöva bekräfta din e-postadress varje gång.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON>", "app.containers.UsersEditPage.passwordChangeSubtitle": "Bekräfta ditt nuvarande lösenord och ändra det till ett nytt lösenord.", "app.containers.UsersEditPage.privacyReasons": "Om du är orolig för din sekretess kan du läsa {conditionsLink}.", "app.containers.UsersEditPage.processing": "<PERSON><PERSON><PERSON>...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Förnamn krävs när du anger efternamn", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON><PERSON> du gå<PERSON>...", "app.containers.UsersEditPage.submit": "<PERSON><PERSON>", "app.containers.UsersEditPage.tooManyEmails": "<PERSON><PERSON>r du för många e-postmeddelanden? Du kan hantera dina e-postinställningar i profilinställningarna.", "app.containers.UsersEditPage.updateverification": "Har din officiella information ändrats? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON><PERSON> vill du att vi ska skicka ett e-postmeddelande till dig?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Du kan delta i projekt som kräver verifiering.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Du är verifierad", "app.containers.UsersEditPage.verifyNow": "Verifiera nu", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Ladda ner dina svar (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {inga likes} one {1 gilla} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Indata som den här kommentaren publicerades som svar på:", "app.containers.UsersShowPage.areas": "Områden", "app.containers.UsersShowPage.commentsWithCount": "Kommentarer ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Redigera min profil", "app.containers.UsersShowPage.emptyInfoText": "Du följer inte några objekt i det angivna filtret ovan.", "app.containers.UsersShowPage.eventsWithCount": "H<PERSON><PERSON><PERSON>er ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Följande ({followingCount})", "app.containers.UsersShowPage.inputs": "Bidrag", "app.containers.UsersShowPage.invisibleTitlePostsList": "Alla indata som har skickats av den här deltagaren", "app.containers.UsersShowPage.invisibleTitleUserComments": "Alla kommentarer som publicerats av den här deltagaren", "app.containers.UsersShowPage.loadMore": "Ladda mer", "app.containers.UsersShowPage.loadMoreComments": "<PERSON><PERSON><PERSON> in fler kommentarer", "app.containers.UsersShowPage.loadingComments": "<PERSON><PERSON><PERSON> in kommentarer...", "app.containers.UsersShowPage.loadingEvents": "<PERSON><PERSON><PERSON> hände<PERSON>...", "app.containers.UsersShowPage.memberSince": "Medlem sedan {date}", "app.containers.UsersShowPage.metaTitle1": "<PERSON><PERSON><PERSON> för {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Den här personen har inte skrivit några kommentarer än.", "app.containers.UsersShowPage.noCommentsForYou": "Det finns inga kommentarer här än.", "app.containers.UsersShowPage.noEventsForUser": "Du har inte deltagit i några evenemang ännu.", "app.containers.UsersShowPage.postsWithCount": "Bidrag ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Projektmappar", "app.containers.UsersShowPage.projects": "Projekt", "app.containers.UsersShowPage.proposals": "Förslag", "app.containers.UsersShowPage.seePost": "Se bidrag", "app.containers.UsersShowPage.surveyResponses": "Svar ({responses})", "app.containers.UsersShowPage.topics": "Ämnen", "app.containers.UsersShowPage.tryAgain": "<PERSON>tt fel har uppstått. Försök igen senare.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Det här är profilsidan för {firstName} {lastName} på onlineplattformen för deltagande för {orgName}. Här finns en översikt över personens alla indata.", "app.containers.VoteControl.close": "Stäng", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON><PERSON> gick fel", "app.containers.admin.ContentBuilder.default": "standard", "app.containers.admin.ContentBuilder.imageTextCards": "Bild- och textkort", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & dragspel", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 kolumn", "app.containers.admin.ContentBuilder.projectDescription": "Projektbeskrivning", "app.containers.app.navbar.admin": "Hantera plattform", "app.containers.app.navbar.allProjects": "Alla projekt", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Stäng mobilnavigeringsmenyn", "app.containers.app.navbar.editProfile": "Mina inställningar", "app.containers.app.navbar.fullMobileNavigation": "Hel mobil", "app.containers.app.navbar.logIn": "Logga in", "app.containers.app.navbar.logoImgAltText": "{orgName} Start", "app.containers.app.navbar.myProfile": "Min aktivitet", "app.containers.app.navbar.search": "<PERSON>ö<PERSON>", "app.containers.app.navbar.showFullMenu": "Visa hela menyn", "app.containers.app.navbar.signOut": "Logga ut", "app.containers.eventspage.errorWhenFetchingEvents": "<PERSON>tt fel uppstod när evenemang laddades. Försök ladda om sidan.", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "Visa alla evenemang som har publicerats på {orgName}s plattform.", "app.containers.eventspage.eventsPageTitle1": "Evenemang | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projekt", "app.containers.eventspage.noPastEvents": "Inga tidigare evenemang att visa", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Inga kommande eller pågående evenemang är för närvarande schemalagda.", "app.containers.eventspage.pastEvents": "<PERSON><PERSON><PERSON><PERSON> even<PERSON>g", "app.containers.eventspage.upcomingAndOngoingEvents": "Kommande och pågående evenemang", "app.containers.footer.accessibility-statement": "Tillgänglighetsförklaring", "app.containers.footer.ariaLabel": "Sekundär", "app.containers.footer.cookie-policy": "Policy webbkakor", "app.containers.footer.cookieSettings": "Cookieinställningar", "app.containers.footer.feedbackEmptyError": "Återkopplingsfältet får inte vara tomt.", "app.containers.footer.poweredBy": "Drivs av", "app.containers.footer.privacy-policy": "Sekretesspolicy", "app.containers.footer.siteMap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.terms-and-conditions": "Villkor", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, jag vill lämna", "app.containers.ideaHeading.editForm": "<PERSON><PERSON><PERSON> form<PERSON>", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Är du säker på att du vill lämna?", "app.containers.ideaHeading.leaveIdeaForm": "Lämna idéformulär", "app.containers.ideaHeading.leaveIdeaText": "<PERSON>a svar kommer inte att sparas.", "app.containers.landing.cityProjects": "Projekt", "app.containers.landing.completeProfile": "<PERSON><PERSON><PERSON> klart din profil", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {firstName}. Det är dags att slutföra din profil.", "app.containers.landing.createAccount": "Registrera dig", "app.containers.landing.defaultSignedInMessage": "{orgName} lys<PERSON><PERSON> på dig. Det är din tur att göra din röst hörd!", "app.containers.landing.doItLater": "<PERSON><PERSON> gör det senare", "app.containers.landing.new": "nytt", "app.containers.landing.subtitleCity": "Välkommen till plattformen för involvering från {orgName}", "app.containers.landing.titleCity": "Vi formar framtiden för {orgName} tillsammans", "app.containers.landing.twitterMessage": "<PERSON><PERSON><PERSON> på {ideaTitle} på", "app.containers.landing.upcomingEventsWidgetTitle": "Kommande och pågående evenemang", "app.containers.landing.userDeletedSubtitle": "Du kan när som helst skapa ett nytt konto eller {contactLink} för att berätta för oss vad vi kan förbättra.", "app.containers.landing.userDeletedSubtitleLinkText": "skriv till oss", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON>tt konto har tagits bort.", "app.containers.landing.userDeletionFailed": "<PERSON>tt fel uppstod när ditt konto skulle tas bort – vi har meddelats om problemet och kommer att göra vårt bästa för att åtgärda det. Försök igen senare.", "app.containers.landing.verifyNow": "Verifiera nu", "app.containers.landing.verifyYourIdentity": "Verifiera din identitet", "app.containers.landing.viewAllEventsText": "Visa alla evenemang", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Tillbaka till mappen", "app.errors.after_end_at": "Startdatumet infaller efter slutdatumet", "app.errors.avatar_carrierwave_download_error": "Det gick inte att ladda ner avatarfilen.", "app.errors.avatar_carrierwave_integrity_error": "Avatarfilen är av en otillåten typ.", "app.errors.avatar_carrierwave_processing_error": "Det gick inte att bearbeta avataren.", "app.errors.avatar_extension_blacklist_error": "Avatarbildens filtillägg är inte tillåtet. Tillåtna tillägg är: jpg, jpeg, gif och png.", "app.errors.avatar_extension_whitelist_error": "Avatarbildens filtillägg är inte tillåtet. Tillåtna tillägg är: jpg, jpeg, gif och png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON> en knapptext.", "app.errors.banner_cta_button_url_blank": "<PERSON><PERSON> en länk.", "app.errors.banner_cta_button_url_url": "Ange en giltig länk. Se till att länken börjar med 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON> en knapptext.", "app.errors.banner_cta_signed_in_url_blank": "<PERSON><PERSON> en länk.", "app.errors.banner_cta_signed_in_url_url": "Ange en giltig länk. Se till att länken börjar med 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON> en knapptext.", "app.errors.banner_cta_signed_out_url_blank": "<PERSON><PERSON> en länk.", "app.errors.banner_cta_signed_out_url_url": "Ange en giltig länk. Se till att länken börjar med 'https://'.", "app.errors.base_includes_banned_words": "Du kan ha använt ett eller flera ord som betraktas som svordomar. Vänligen ändra din text för att ta bort eventuella svordomar som kan förekomma.", "app.errors.body_multiloc_includes_banned_words": "Beskrivningen innehåller ord som anses vara olämpliga.", "app.errors.bulk_import_idea_not_valid": "Den resulterande idén är inte giltig: {value}.", "app.errors.bulk_import_image_url_not_valid": "Ingen bild kunde laddas ner från {value}. Kontrollera att URL:en är giltig och slutar med ett filnamnstillägg som .png eller .jpg. Detta problem uppstår i raden med ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idéplats med saknad koordinat i {value}. Detta problem uppstår i raden med ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idéplats med en icke-numerisk koordinat i {value}. Detta problem uppstår i raden med ID {row}.", "app.errors.bulk_import_malformed_pdf": "Den uppladdade PDF-filen verkar vara felformad. Försök exportera PDF-filen igen från din källa och ladda sedan upp den igen.", "app.errors.bulk_import_maximum_ideas_exceeded": "Maximiantalet {value} idéer har ö<PERSON>krid<PERSON>.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Det maximala antalet {value} sidor i en PDF har överskridits.", "app.errors.bulk_import_not_enough_pdf_pages": "Den uppladdade PDF-filen har inte tillräckligt många sidor - den bör ha minst samma antal sidor som den nedladdade mallen.", "app.errors.bulk_import_publication_date_invalid_format": "Idé med ogiltigt format för publiceringsdatum \"{value}\". Vänligen använd formatet \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "Metoden för deltagande du valde stödjer inte den här typen av inlägg. Redigera ditt val och försök igen.", "app.errors.cant_change_after_first_response": "Du kan inte längre ändra det här eftersom vissa användare redan har svarat", "app.errors.category_name_taken": "En kategori med det här namnet finns redan", "app.errors.confirmation_code_expired": "<PERSON>den har löpt ut. Begär en ny kod.", "app.errors.confirmation_code_invalid": "Ogiltig bekräftelsekod. Kontrollera din e-post för rätt kod eller prova med Skicka ny kod", "app.errors.confirmation_code_too_many_resets": "Du har skickat om bekräftelsekoden för många gånger. Kontakta oss för att få en inbjudningskod istället.", "app.errors.confirmation_code_too_many_retries": "Du har försökt för många gånger. Begär en ny kod eller prova att ändra din e-postadress.", "app.errors.email_already_active": "E-postadressen {value} som finns på rad {row} till<PERSON><PERSON><PERSON> redan en registrerad deltagare", "app.errors.email_already_invited": "E-postadressen {value} som hittades på rad {row} var redan inbjuden", "app.errors.email_blank": "<PERSON> här får inte vara tomt", "app.errors.email_domain_blacklisted": "Använd en annan e-postdomän för att registrera dig.", "app.errors.email_invalid": "Använd en giltig e-postadress.", "app.errors.email_taken": "Ett konto med den här e-postadressen finns redan. Du kan logga in istället.", "app.errors.email_taken_by_invite": "{value} har redan tagits av en väntande inbjudan. Kontrollera din skräppostmapp eller kontakta {supportEmail} om du inte kan hitta den.", "app.errors.emails_duplicate": "<PERSON>tt eller flera dubblettvärden för e-postadressen {value} hittades i följande rad(er): {rows}", "app.errors.extension_whitelist_error": "Formatet på filen som du försökte ladda upp stöds inte.", "app.errors.file_extension_whitelist_error": "Formatet på filen du försökte ladda upp stöds inte.", "app.errors.first_name_blank": "<PERSON> här får inte vara tomt", "app.errors.generics.blank": "<PERSON><PERSON> får inte vara tomt.", "app.errors.generics.invalid": "Det här verkar inte vara ett giltigt värde", "app.errors.generics.taken": "Den här e-postadressen finns redan. Ett annat konto är kopplat till den.", "app.errors.generics.unsupported_locales": "Det här fältet stödjer inte det aktuella språket.", "app.errors.group_ids_unauthorized_choice_moderator": "Som projektledare kan du bara skicka e-post till personer som har tillgång till dina projekt", "app.errors.has_other_overlapping_phases": "Projekt får inte ha överlappande faser.", "app.errors.invalid_email": "E-postadressen {value} som hittades på rad {row} är inte en giltig e-postadress", "app.errors.invalid_row": "<PERSON>tt okänt fel inträffade vid försök att bearbeta rad {row}", "app.errors.is_not_timeline_project": "Det aktuella projektet stödjer inte faser.", "app.errors.key_invalid": "<PERSON>yckeln får bara innehålla bokstäver, siff<PERSON>r och understreck (_)", "app.errors.last_name_blank": "<PERSON> här får inte vara tomt", "app.errors.locale_blank": "<PERSON><PERSON><PERSON><PERSON> ett språk", "app.errors.locale_inclusion": "<PERSON><PERSON><PERSON><PERSON> ett språk som stöds", "app.errors.malformed_admin_value": "Administratörsvärdet {value} på rad {row} är inte giltigt", "app.errors.malformed_groups_value": "Gruppen {value} som finns på rad {row} är inte en giltig grupp", "app.errors.max_invites_limit_exceeded1": "Antalet inbjudningar överskrider gränsen på 1000.", "app.errors.maximum_attendees_greater_than1": "Det maximala antalet registrerade måste vara större än 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Det högsta antalet registrerade måste vara större än eller lika med det aktuella antalet registrerade.", "app.errors.no_invites_specified": "Ingen e-postadress hittades.", "app.errors.no_recipients": "Kampanjen kan inte skickas ut eftersom det inte finns några mottagare. Gruppen du skickar till är antingen tom eller så har ingen samtyckt till att ta emot e-post.", "app.errors.number_invalid": "Vänligen ange ett giltigt nummer.", "app.errors.password_blank": "<PERSON> här får inte vara tomt", "app.errors.password_invalid": "Kontrollera ditt nuvarande lösenord igen.", "app.errors.password_too_short": "Lösenordet måste vara minst 8 tecken långt", "app.errors.resending_code_failed": "<PERSON><PERSON><PERSON> gick fel när bekräftelsekoden skickades ut.", "app.errors.slug_taken": "Den här projektwebbadressen finns redan. Ändra projektets slug till något annat.", "app.errors.tag_name_taken": "En tagg med detta namn finns redan", "app.errors.title_multiloc_blank": "Titeln får inte vara tom.", "app.errors.title_multiloc_includes_banned_words": "Titeln innehåller ord som anses vara olämpliga.", "app.errors.token_invalid": "Länkar för återställning av lösenord kan endast användas en gång och är giltiga i en timme efter att de har skickats. {passwordResetLink}.", "app.errors.too_common": "Det här lösenordet är lätt att gissa. V<PERSON><PERSON><PERSON> ett starkare lösenord.", "app.errors.too_long": "<PERSON><PERSON><PERSON><PERSON> ett kortare lö<PERSON>ord (max 72 tecken)", "app.errors.too_short": "Välj ett lösenord med minst 8 tecken", "app.errors.uncaught_error": "<PERSON>tt okänt fel har inträffat.", "app.errors.unknown_group": "Gruppen {value} som finns på rad {row} är inte en känd grupp", "app.errors.unknown_locale": "Språket {value} som finns på rad {row} är inte ett konfigurerat språk", "app.errors.unparseable_excel": "Den valda Excel-filen kunde inte bearbetas.", "app.errors.url": "Ange en giltig länk. Se till att länken börjar med https://", "app.errors.verification_taken": "Verifieringen kan inte slutföras eftersom ett annat konto har verifierats med samma uppgifter.", "app.errors.view_name_taken": "En vy med det här namnet finns redan", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Olämpligt innehåll upptäcktes automatiskt i ett inlägg eller en kommentar", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Logga in med StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Registrera dig med StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Skapa ett Stadt Wien-konto nu och använd en inloggning för många digitala tjänster i Wien.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "Det här fältet får inte vara tomt.", "app.modules.id_cow.helpAltText": "<PERSON>r var man hittar ID-serienumret på ett identitetskort", "app.modules.id_cow.invalidIdSerialError": "Ogiltigt ID-serienummer", "app.modules.id_cow.invalidRunError": "Ogiltigt RUN", "app.modules.id_cow.noMatchFormError": "Ingen matchning hittades.", "app.modules.id_cow.notEntitledFormError": "Inte berättigad.", "app.modules.id_cow.showCOWHelp": "Var kan jag hitta mitt ID-serienummer?", "app.modules.id_cow.somethingWentWrongError": "Vi kan inte verifiera dig eftersom något gick fel", "app.modules.id_cow.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_cow.takenFormError": "<PERSON><PERSON> taget.", "app.modules.id_cow.verifyCow": "Verifiera med COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verifiera med FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Det här fältet får inte vara tomt.", "app.modules.id_gent_rrn.gentRrnHelp": "Ditt personnummer visas på baksidan av ditt digitala identitetskort", "app.modules.id_gent_rrn.invalidRrnError": "Ogiltigt personnummer", "app.modules.id_gent_rrn.noMatchFormError": "Vi kunde inte hitta tidigare information om ditt personnummer", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Vi kan inte verifiera dig eftersom du bor utanför Gent", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Vi kan inte verifiera dig eftersom du är yngre än 14 år", "app.modules.id_gent_rrn.rrnLabel": "Personnummer", "app.modules.id_gent_rrn.rrnTooltip": "Vi ber om ditt personnummer för att verifiera om du är medborgare i Gent och är äldre än 14 år.", "app.modules.id_gent_rrn.showGentRrnHelp": "Var kan jag hitta mitt ID-serienummer?", "app.modules.id_gent_rrn.somethingWentWrongError": "Vi kan inte verifiera dig eftersom något gick fel", "app.modules.id_gent_rrn.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.takenFormError": "Ditt personnummer har redan använts för att verifiera ett annat konto", "app.modules.id_gent_rrn.verifyGentRrn": "Verifiera med GentRrn", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "Det här fältet får inte vara tomt.", "app.modules.id_id_card_lookup.helpAltText": "ID-kort – förklaring", "app.modules.id_id_card_lookup.invalidCardIdError": "Detta <PERSON> är inte giltigt.", "app.modules.id_id_card_lookup.noMatchFormError": "Ingen matchning hittades.", "app.modules.id_id_card_lookup.showHelp": "Var kan jag hitta mitt ID-serienummer?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Vi kan inte verifiera dig eftersom något gick fel", "app.modules.id_id_card_lookup.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON> taget.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "Det här fältet får inte vara tomt.", "app.modules.id_oostende_rrn.invalidRrnError": "Ogiltigt personnummer", "app.modules.id_oostende_rrn.noMatchFormError": "Vi kunde inte hitta tidigare information om ditt personnummer", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Vi kan inte verifiera dig eftersom du bor utanför <PERSON>", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Vi kan inte verifiera dig eftersom du är yngre än 14 år", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Ditt personnummer visas på baksidan av ditt digitala identitetskort", "app.modules.id_oostende_rrn.rrnLabel": "Personnummer", "app.modules.id_oostende_rrn.rrnTooltip": "Vi ber om ditt personnummer för att verifiera om du är medborgare i Oostende och är äldre än 14 år.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Var kan jag hitta mitt personnummer?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Vi kan inte verifiera dig eftersom något gick fel", "app.modules.id_oostende_rrn.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.takenFormError": "Ditt personnummer har redan använts för att verifiera ett annat konto", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verifiera med personnummer", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Du fick administratörsrättigheter för mappen {folderName}.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Dela", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Se projekten på {folderUrl} för att göra din röst hörd!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | från plattformen för deltagande för {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | från plattformen för deltagande för {orgName}", "app.sessionRecording.accept": "<PERSON>a, jag accepterar", "app.sessionRecording.modalDescription1": "<PERSON><PERSON><PERSON> att bättre förstå våra användare ber vi slumpmässigt en liten andel av bes<PERSON>karna att spåra sin webbläsarsession i detalj.", "app.sessionRecording.modalDescription2": "Det enda syftet med de registrerade uppgifterna är att förbättra webbplatsen. Ingen av dina uppgifter kommer att delas med en tredje part. All känslig information som du anger kommer att filtreras.", "app.sessionRecording.modalDescription3": "Accepterar ni?", "app.sessionRecording.modalDescriptionFaq": "<PERSON><PERSON><PERSON> och svar här.", "app.sessionRecording.modalTitle": "Hjälp oss att förbättra denna webbplats", "app.sessionRecording.reject": "<PERSON><PERSON>, jag avvisar", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Genomför en övning för budgettilldelning", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Insamling av feedback på ett dokument", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Skapa en undersökning på plattformen", "app.utils.AdminPage.ProjectEdit.createPoll": "Skapa en omröstning", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Bädda in en extern undersökning", "app.utils.AdminPage.ProjectEdit.findVolunteers": "<PERSON>ta personer som anmäler sig frivilligt", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Samla in indata och återkoppling", "app.utils.AdminPage.ProjectEdit.shareInformation": "Information", "app.utils.FormattedCurrency.credits": "krediter", "app.utils.FormattedCurrency.tokens": "token", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Mest diskuterade", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON><PERSON> reaktioner", "app.utils.IdeaCards.newest": "Nyaste", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "Slumpmässig", "app.utils.IdeaCards.trending": "Trendande", "app.utils.IdeasNewPage.contributionFormTitle": "Lägg till nytt bidrag", "app.utils.IdeasNewPage.ideaFormTitle": "<PERSON><PERSON><PERSON> till ny idé", "app.utils.IdeasNewPage.initiativeFormTitle": "Lägg till nytt initiativ", "app.utils.IdeasNewPage.issueFormTitle1": "Lägg till ny kommentar", "app.utils.IdeasNewPage.optionFormTitle": "Lägg till nytt alternativ", "app.utils.IdeasNewPage.petitionFormTitle": "Lägg till ny petition", "app.utils.IdeasNewPage.projectFormTitle": "Lägg till nytt projekt", "app.utils.IdeasNewPage.proposalFormTitle": "Lägg till nytt förslag", "app.utils.IdeasNewPage.questionFormTitle": "Lägg till ny fråga", "app.utils.IdeasNewPage.surveyTitle": "Undersö<PERSON>ning", "app.utils.IdeasNewPage.viewYourComment": "Visa din kommentar", "app.utils.IdeasNewPage.viewYourContribution": "Se ditt bidrag", "app.utils.IdeasNewPage.viewYourIdea": "Visa din idé", "app.utils.IdeasNewPage.viewYourInitiative": "Visa ditt initiativ", "app.utils.IdeasNewPage.viewYourInput": "Se dina synpunkter", "app.utils.IdeasNewPage.viewYourIssue": "Se din utgåva", "app.utils.IdeasNewPage.viewYourOption": "Visa ditt alternativ", "app.utils.IdeasNewPage.viewYourPetition": "<PERSON> <PERSON> petition", "app.utils.IdeasNewPage.viewYourProject": "Visa ditt projekt", "app.utils.IdeasNewPage.viewYourProposal": "Se ditt fö<PERSON>", "app.utils.IdeasNewPage.viewYourQuestion": "Visa din fråga", "app.utils.Projects.sendSubmission": "Skicka inlämningsidentifierare till min e-post", "app.utils.Projects.sendSurveySubmission": "Skicka identifierare för enkätinlämning till min e-post", "app.utils.Projects.surveySubmission": "Inlämning av enkät", "app.utils.Projects.yourResponseHasTheFollowingId": "<PERSON>tt svar har följande identifierare: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Om du senare bestämmer dig för att du vill att ditt svar ska tas bort, vänligen kontakta oss med följande unika identifierare:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Du måste fylla i din profil för att delta i detta evenemang.", "app.utils.actionDescriptors.attendingEventNotInGroup": "<PERSON> uppfyller inte kraven för att delta i detta evenemang.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Du har inte rätt att närvara vid detta evenemang.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Du måste logga in eller registrera dig för att delta i detta evenemang.", "app.utils.actionDescriptors.attendingEventNotVerified": "Du måste verifiera ditt konto innan du kan delta i detta evenemang.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Du måste fylla i din profil för att kunna anmäla dig som volontär.", "app.utils.actionDescriptors.volunteeringNotInGroup": "<PERSON> uppfyller inte kraven för att arbeta som volontär.", "app.utils.actionDescriptors.volunteeringNotPermitted": "<PERSON> får inte arbeta som volontär.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Du måste logga in eller registrera dig för att bli volontär.", "app.utils.actionDescriptors.volunteeringNotVerified": "Du måste verifiera ditt konto innan du kan arbeta som volontär.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Vänligen {completeRegistrationLink} för att anmäla dig som volontär.", "app.utils.errors.api_error_default.in": "Det är inte rätt", "app.utils.errors.default.ajv_error_birthyear_required": "<PERSON><PERSON><PERSON> i ditt födelseår", "app.utils.errors.default.ajv_error_date_any": "Fyll i ett giltigt datum", "app.utils.errors.default.ajv_error_domicile_required": "F<PERSON>l i din bostadsort", "app.utils.errors.default.ajv_error_gender_required": "<PERSON><PERSON><PERSON> i ditt kön", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.ajv_error_maxItems": "<PERSON><PERSON>r inte inkludera mer än {limit, plural, one {# objekt} other {# objekt}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON><PERSON> inkludera minst {limit, plural, one {# objekt} other {# objekt}}", "app.utils.errors.default.ajv_error_number_any": "<PERSON>yll i ett giltigt nummer", "app.utils.errors.default.ajv_error_politician_required": "<PERSON><PERSON>l i om du är politiker", "app.utils.errors.default.ajv_error_required3": "Fältet är obligatoriskt: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "<PERSON><PERSON><PERSON> inte vara tomt", "app.utils.errors.default.api_error_accepted": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_blank": "<PERSON><PERSON><PERSON> inte vara tomt", "app.utils.errors.default.api_error_confirmation": "Matchar inte", "app.utils.errors.default.api_error_empty": "<PERSON><PERSON><PERSON> inte vara tomt", "app.utils.errors.default.api_error_equal_to": "Det är inte rätt", "app.utils.errors.default.api_error_even": "<PERSON><PERSON><PERSON> vara jämnt", "app.utils.errors.default.api_error_exclusion": "Är reserverat", "app.utils.errors.default.api_error_greater_than": "<PERSON><PERSON> för litet", "app.utils.errors.default.api_error_greater_than_or_equal_to": "<PERSON><PERSON> för litet", "app.utils.errors.default.api_error_inclusion": "Finns inte med i listan", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.api_error_less_than": "<PERSON><PERSON> för stort", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON><PERSON> för stort", "app.utils.errors.default.api_error_not_a_number": "Är inte ett nummer", "app.utils.errors.default.api_error_not_an_integer": "Måste vara ett heltal", "app.utils.errors.default.api_error_other_than": "Det är inte rätt", "app.utils.errors.default.api_error_present": "<PERSON><PERSON><PERSON> vara tomt", "app.utils.errors.default.api_error_too_long": "<PERSON><PERSON> <PERSON>ör <PERSON>", "app.utils.errors.default.api_error_too_short": "<PERSON><PERSON> för kort", "app.utils.errors.default.api_error_wrong_length": "Är fel längd", "app.utils.errors.defaultapi_error_.odd": "<PERSON><PERSON><PERSON> vara udda", "app.utils.notInGroup": "<PERSON> uppfyller inte kraven för att delta.", "app.utils.participationMethod.onSurveySubmission": "Tack. <PERSON><PERSON> svar har mottagits.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Rö<PERSON>ning är inte längre tillgäng<PERSON>g, eftersom denna fas inte längre är aktiv.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "<PERSON>pfyller inte kraven för att få rösta.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Du har inte rätt att rösta.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Du måste logga in eller registrera dig för att rösta.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Du måste verifiera ditt konto innan du kan rösta.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Inlämningen av budgetar avslutades på {endDate}.</b> <PERSON><PERSON><PERSON> hade totalt <b>{maxBudget} var att fördela mellan {optionCount} alternativ.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Inlämnad budget", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budget inlämnad 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "<PERSON>pfyller inte kraven för att tilldela budgetar.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Det är inte tillåtet att tilldela budgetar.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Du måste logga in eller registrera dig för att tilldela budgetar.", "app.utils.votingMethodUtils.budgetingNotVerified": "Du måste verifiera ditt konto innan du kan tilldela budgetar.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b><PERSON> <PERSON> kommer inte att räknas</b> fö<PERSON><PERSON><PERSON> du klickar på \"Skicka\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Den minsta budget som krävs är {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON>r du är klar klickar du på \"<PERSON>cka\" för att skicka in din budget.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Välj önskade alternativ genom att trycka på \"Lägg till\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Du har totalt <b>{maxBudget} att fördela mellan {optionCount} alternativ.</b>", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON>, din budget har skickats in!</b> Du kan när som helst kontrollera dina alternativ nedan eller ändra dem före <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON>, din budget har skickats in!</b> Du kan när som helst kontrollera dina alternativ nedan.", "app.utils.votingMethodUtils.castYourVote": "<PERSON><PERSON>ge din röst", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Du kan lägga till högst {maxVotes, plural, one {# credit} other {# credits}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Du kan lägga till högst {maxVotes, plural, one {# poäng} other {# poäng}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Du kan lägga till högst {maxVotes, plural, one {# token} other {# tokens}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Du kan lägga till högst {maxVotes, plural, one {# vote} other {# votes}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON><PERSON>r du är klar klickar du på \"<PERSON><PERSON>a\" för att avge din röst.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Välj önskade alternativ genom att trycka på \"Välj\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Slutliga resultat", "app.utils.votingMethodUtils.finalTally": "S<PERSON><PERSON>g sammanräkning", "app.utils.votingMethodUtils.howToParticipate": "<PERSON>r man deltar", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON> man r<PERSON>star", "app.utils.votingMethodUtils.multipleVotingEnded1": "Omröstningen avslutades på <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 hp} one {1 hp} other {# hp}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 poäng} one {1 poäng} other {# poäng}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 röster} one {1 röst} other {# röster}}", "app.utils.votingMethodUtils.results": "Resultat", "app.utils.votingMethodUtils.singleVotingEnded": "Omröstningen avslutades på <b>{endDate}.</b> Deltagarna kunde <b>rö<PERSON> på {maxVotes} alternativ.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Välj önskat alternativ genom att trycka på \"Rösta\"", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "<PERSON> har <b>{totalVotes} r<PERSON><PERSON></b> som du kan tilldela alternativen.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON><PERSON>r du är klar klickar du på \"<PERSON><PERSON>a\" för att avge din röst.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Omröstningen avslutades den <b>{endDate}.</b> Deltagarna kunde <b>rösta på 1 alternativ.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Välj önskat alternativ genom att trycka på \"Rösta\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "<PERSON> har <b>1 röst</b> som du kan tilldela ett av alternativen.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Omröstningen avslutades den <b>{endDate}.</b> Deltagarna kunde <b>r<PERSON><PERSON> på så många alternativ som de önskade.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Du kan rösta på så många alternativ som du vill.", "app.utils.votingMethodUtils.submitYourBudget": "Lämna in din budget", "app.utils.votingMethodUtils.submittedBudgetCountText2": "person lämnade in sin budget online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "människor lämnade in sina budgetar online", "app.utils.votingMethodUtils.submittedVoteCountText2": "person lämnade sin röst online", "app.utils.votingMethodUtils.submittedVotesCountText2": "människor lämnade sina röster online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Avlagd röst 🎉", "app.utils.votingMethodUtils.votesCast": "Lämnade röster", "app.utils.votingMethodUtils.votingClosed": "Omröstningen avslutad", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b><PERSON> röst kommer inte att räknas</b> fö<PERSON><PERSON><PERSON> du klickar på \"<PERSON><PERSON><PERSON>\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, din röst har skickats in!</b> Du kan kontrollera eller ändra din röst innan du <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, din röst har skickats in!</b> Du kan när som helst kontrollera eller ändra din röst nedan.", "components.UI.IdeaSelect.noIdeaAvailable": "Det finns inga tillgängliga idéer.", "components.UI.IdeaSelect.selectIdea": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.allProjects": "Alla projekt", "containers.SiteMap.customPageSection": "Anpassade sidor", "containers.SiteMap.folderInfo": "Mer information", "containers.SiteMap.headSiteMapTitle": "Webbplatskarta | {orgName}", "containers.SiteMap.homeSection": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.pageContents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.profilePage": "<PERSON> profilsida", "containers.SiteMap.profileSettings": "<PERSON><PERSON> inställningar", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Information", "containers.SiteMap.projectPoll": "Omr<PERSON><PERSON><PERSON>", "containers.SiteMap.projectSurvey": "Undersö<PERSON>ning", "containers.SiteMap.projectsArchived": "Arkiverade projekt", "containers.SiteMap.projectsCurrent": "Aktuella projekt", "containers.SiteMap.projectsDraft": "Utkast till projekt", "containers.SiteMap.projectsSection": "<PERSON><PERSON><PERSON> för {orgName}", "containers.SiteMap.signInPage": "Logga in", "containers.SiteMap.signUpPage": "Registrera dig", "containers.SiteMap.siteMapDescription": "<PERSON><PERSON><PERSON> den här sidan kan du navigera till vilket innehåll som helst på plattformen.", "containers.SiteMap.siteMapTitle": "Webbplatskarta över plattformen för deltagande för {orgName}", "containers.SiteMap.successStories": "Framgångsberättelser", "containers.SiteMap.timeline": "Projektfaser", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON> konto", "containers.SubscriptionEndedPage.accessDenied": "Du har inte längre åtkomst", "containers.SubscriptionEndedPage.subscriptionEnded": "Den här sidan är endast tillgänglig för plattformar med ett aktivt abonnemang."}