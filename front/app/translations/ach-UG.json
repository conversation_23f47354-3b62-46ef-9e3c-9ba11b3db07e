{"EmailSettingsPage.emailSettings": "crwdns2256734:0crwdne2256734:0", "EmailSettingsPage.initialUnsubscribeError": "crwdns212802:0crwdne212802:0", "EmailSettingsPage.initialUnsubscribeLoading": "crwdns212804:0crwdne212804:0", "EmailSettingsPage.initialUnsubscribeSuccess": "crwdns212806:0{campaignTitle}crwdne212806:0", "UI.FormComponents.optional": "crwdns212812:0crwdne212812:0", "app.closeIconButton.a11y_buttonActionMessage": "crwdns212816:0crwdne212816:0", "app.components.Areas.areaUpdateError": "crwdns1081578:0crwdne1081578:0", "app.components.Areas.followedArea": "crwdns2400630:0{areaTitle}crwdne2400630:0", "app.components.Areas.followedTopic": "crwdns2400632:0{topicTitle}crwdne2400632:0", "app.components.Areas.topicUpdateError": "crwdns1081580:0crwdne1081580:0", "app.components.Areas.unfollowedArea": "crwdns2400634:0{areaTitle}crwdne2400634:0", "app.components.Areas.unfollowedTopic": "crwdns2400636:0{topicTitle}crwdne2400636:0", "app.components.AssignBudgetControl.a11y_price": "crwdns212818:0crwdne212818:0", "app.components.AssignBudgetControl.add": "crwdns212820:0crwdne212820:0", "app.components.AssignBudgetControl.added": "crwdns777507:0crwdne777507:0", "app.components.AssignMultipleVotesControl.addVote": "crwdns777509:0crwdne777509:0", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "crwdns4916095:0crwdne4916095:0", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "crwdns4916097:0crwdne4916097:0", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "crwdns4916099:0crwdne4916099:0", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "crwdns4916101:0crwdne4916101:0", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "crwdns4916103:0crwdne4916103:0", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "crwdns4916105:0crwdne4916105:0", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "crwdns4916107:0crwdne4916107:0", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "crwdns4916109:0crwdne4916109:0", "app.components.AssignMultipleVotesControl.numberManualVotes2": "crwdns3549651:0manualVotes={manualVotes}crwdne3549651:0", "app.components.AssignMultipleVotesControl.phaseNotActive": "crwdns3338601:0crwdne3338601:0", "app.components.AssignMultipleVotesControl.removeVote": "crwdns777515:0crwdne777515:0", "app.components.AssignMultipleVotesControl.select": "crwdns1400768:0crwdne1400768:0", "app.components.AssignMultipleVotesControl.votesSubmitted1": "crwdns4916111:0crwdne4916111:0", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "crwdns4916113:0crwdne4916113:0", "app.components.AssignMultipleVotesControl.xCredits1": "crwdns4916115:0votes={votes}crwdne4916115:0", "app.components.AssignMultipleVotesControl.xPoints1": "crwdns4916117:0votes={votes}crwdne4916117:0", "app.components.AssignMultipleVotesControl.xTokens1": "crwdns4916119:0votes={votes}crwdne4916119:0", "app.components.AssignMultipleVotesControl.xVotes3": "crwdns4916121:0votes={votes}crwdne4916121:0", "app.components.AssignVoteControl.maxVotesReached1": "crwdns4916123:0crwdne4916123:0", "app.components.AssignVoteControl.phaseNotActive": "crwdns3338603:0crwdne3338603:0", "app.components.AssignVoteControl.select": "crwdns1400770:0crwdne1400770:0", "app.components.AssignVoteControl.selected2": "crwdns1400772:0crwdne1400772:0", "app.components.AssignVoteControl.voteForAtLeastOne": "crwdns777531:0crwdne777531:0", "app.components.AssignVoteControl.votesSubmitted1": "crwdns4916125:0crwdne4916125:0", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "crwdns4916127:0crwdne4916127:0", "app.components.AuthProviders.continue": "crwdns212830:0crwdne212830:0", "app.components.AuthProviders.continueWithAzure": "crwdns212832:0{azureProviderName}crwdne212832:0", "app.components.AuthProviders.continueWithFacebook": "crwdns212834:0crwdne212834:0", "app.components.AuthProviders.continueWithFakeSSO": "crwdns2747341:0crwdne2747341:0", "app.components.AuthProviders.continueWithGoogle": "crwdns212836:0crwdne212836:0", "app.components.AuthProviders.continueWithHoplr": "crwdns836121:0crwdne836121:0", "app.components.AuthProviders.continueWithIdAustria": "crwdns3548607:0crwdne3548607:0", "app.components.AuthProviders.continueWithLoginMechanism": "crwdns594887:0{loginMechanismName}crwdne594887:0", "app.components.AuthProviders.continueWithNemlogIn": "crwdns2999235:0crwdne2999235:0", "app.components.AuthProviders.franceConnectMergingFailed": "crwdns212838:0{br}crwdnd212838:0{br}crwdnd212838:0{br}crwdnd212838:0{br}crwdne212838:0", "app.components.AuthProviders.goToLogIn": "crwdns212840:0{goToOtherFlowLink}crwdne212840:0", "app.components.AuthProviders.goToSignUp": "crwdns212842:0{goToOtherFlowLink}crwdne212842:0", "app.components.AuthProviders.logIn2": "crwdns212844:0crwdne212844:0", "app.components.AuthProviders.logInWithEmail": "crwdns212846:0crwdne212846:0", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "crwdns1140374:0crwdne1140374:0", "app.components.AuthProviders.signUp2": "crwdns212850:0crwdne212850:0", "app.components.AuthProviders.signUpButtonAltText": "crwdns212852:0{loginMechanismName}crwdne212852:0", "app.components.AuthProviders.signUpWithEmail": "crwdns212854:0crwdne212854:0", "app.components.AuthProviders.verificationRequired": "crwdns2888825:0crwdne2888825:0", "app.components.Author.a11yPostedBy": "crwdns2136230:0crwdne2136230:0", "app.components.AvatarBubbles.numberOfParticipants1": "crwdns3373801:0numberOfParticipants={numberOfParticipants}crwdnd3373801:0numberOfParticipants={numberOfParticipants}crwdne3373801:0", "app.components.AvatarBubbles.numberOfUsers": "crwdns3561763:0{numberOfUsers}crwdne3561763:0", "app.components.AvatarBubbles.participant": "crwdns3373803:0crwdne3373803:0", "app.components.AvatarBubbles.participants1": "crwdns3373805:0crwdne3373805:0", "app.components.Comments.cancel": "crwdns212864:0crwdne212864:0", "app.components.Comments.commentingDisabledInCurrentPhase": "crwdns212866:0crwdne212866:0", "app.components.Comments.commentingDisabledInactiveProject": "crwdns212868:0crwdne212868:0", "app.components.Comments.commentingDisabledProject": "crwdns212870:0crwdne212870:0", "app.components.Comments.commentingDisabledUnverified": "crwdns212872:0{verifyIdentityLink}crwdne212872:0", "app.components.Comments.commentingMaybeNotPermitted": "crwdns212878:0{signInLink}crwdne212878:0", "app.components.Comments.inputsAssociatedWithProfile": "crwdns604447:0crwdne604447:0", "app.components.Comments.invisibleTitleComments": "crwdns212880:0crwdne212880:0", "app.components.Comments.leastRecent": "crwdns703277:0crwdne703277:0", "app.components.Comments.likeComment": "crwdns777543:0crwdne777543:0", "app.components.Comments.mostLiked": "crwdns777545:0crwdne777545:0", "app.components.Comments.mostRecent": "crwdns478775:0crwdne478775:0", "app.components.Comments.official": "crwdns212884:0crwdne212884:0", "app.components.Comments.postAnonymously": "crwdns604449:0crwdne604449:0", "app.components.Comments.replyToComment": "crwdns212888:0crwdne212888:0", "app.components.Comments.reportAsSpam": "crwdns212890:0crwdne212890:0", "app.components.Comments.seeOriginal": "crwdns212892:0crwdne212892:0", "app.components.Comments.seeTranslation": "crwdns212894:0crwdne212894:0", "app.components.Comments.yourComment": "crwdns212898:0crwdne212898:0", "app.components.CommonGroundResults.divisiveDescription": "crwdns4747493:0crwdne4747493:0", "app.components.CommonGroundResults.divisiveTitle": "crwdns4747495:0crwdne4747495:0", "app.components.CommonGroundResults.majorityDescription": "crwdns4747497:0crwdne4747497:0", "app.components.CommonGroundResults.majorityTitle": "crwdns4747499:0crwdne4747499:0", "app.components.CommonGroundResults.participantLabel": "crwdns4747501:0crwdne4747501:0", "app.components.CommonGroundResults.participantsLabel1": "crwdns4747503:0crwdne4747503:0", "app.components.CommonGroundResults.statementLabel": "crwdns4747505:0crwdne4747505:0", "app.components.CommonGroundResults.statementsLabel1": "crwdns4747507:0crwdne4747507:0", "app.components.CommonGroundResults.votesLabe": "crwdns4747509:0crwdne4747509:0", "app.components.CommonGroundResults.votesLabel1": "crwdns4747511:0crwdne4747511:0", "app.components.CommonGroundStatements.agreeLabel": "crwdns4747513:0crwdne4747513:0", "app.components.CommonGroundStatements.disagreeLabel": "crwdns4747515:0crwdne4747515:0", "app.components.CommonGroundStatements.noMoreStatements": "crwdns4747517:0crwdne4747517:0", "app.components.CommonGroundStatements.noResults": "crwdns4747519:0crwdne4747519:0", "app.components.CommonGroundStatements.unsureLabel": "crwdns4747521:0crwdne4747521:0", "app.components.CommonGroundTabs.resultsTabLabel": "crwdns4747523:0crwdne4747523:0", "app.components.CommonGroundTabs.statementsTabLabel": "crwdns4747525:0crwdne4747525:0", "app.components.CommunityMonitorModal.formError": "crwdns4305656:0crwdne4305656:0", "app.components.CommunityMonitorModal.surveyDescription2": "crwdns4305658:0crwdne4305658:0", "app.components.CommunityMonitorModal.xMinutesToComplete": "crwdns4863569:0minutes={minutes}crwdne4863569:0", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "crwdns212900:0{userEmail}crwdne212900:0", "app.components.ConfirmationModal.changeYourEmail": "crwdns212904:0crwdne212904:0", "app.components.ConfirmationModal.codeInput": "crwdns212908:0crwdne212908:0", "app.components.ConfirmationModal.confirmationCodeSent": "crwdns212912:0crwdne212912:0", "app.components.ConfirmationModal.didntGetAnEmail": "crwdns212914:0crwdne212914:0", "app.components.ConfirmationModal.foundYourCode": "crwdns212920:0crwdne212920:0", "app.components.ConfirmationModal.goBack": "crwdns212922:0crwdne212922:0", "app.components.ConfirmationModal.sendEmailWithCode": "crwdns212926:0crwdne212926:0", "app.components.ConfirmationModal.sendNewCode": "crwdns212928:0crwdne212928:0", "app.components.ConfirmationModal.verifyAndContinue": "crwdns212930:0crwdne212930:0", "app.components.ConfirmationModal.wrongEmail": "crwdns212932:0crwdne212932:0", "app.components.ConsentManager.Banner.accept": "crwdns212934:0crwdne212934:0", "app.components.ConsentManager.Banner.ariaButtonClose2": "crwdns212936:0crwdne212936:0", "app.components.ConsentManager.Banner.close": "crwdns212938:0crwdne212938:0", "app.components.ConsentManager.Banner.mainText": "crwdns212940:0{policyLink}crwdne212940:0", "app.components.ConsentManager.Banner.manage": "crwdns212942:0crwdne212942:0", "app.components.ConsentManager.Banner.policyLink": "crwdns212944:0crwdne212944:0", "app.components.ConsentManager.Banner.reject": "crwdns1487452:0crwdne1487452:0", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "crwdns212952:0crwdne212952:0", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "crwdns212954:0crwdne212954:0", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "crwdns212956:0crwdne212956:0", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "crwdns212958:0crwdne212958:0", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "crwdns212960:0crwdne212960:0", "app.components.ConsentManager.Modal.PreferencesDialog.back": "crwdns212962:0crwdne212962:0", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "crwdns212964:0crwdne212964:0", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "crwdns212966:0crwdne212966:0", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "crwdns212968:0crwdne212968:0", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "crwdns212970:0crwdne212970:0", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "crwdns212972:0{destinations}crwdne212972:0", "app.components.ConsentManager.Modal.PreferencesDialog.required": "crwdns212974:0crwdne212974:0", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "crwdns212976:0crwdne212976:0", "app.components.ConsentManager.Modal.PreferencesDialog.save": "crwdns212978:0crwdne212978:0", "app.components.ConsentManager.Modal.PreferencesDialog.title": "crwdns212980:0crwdne212980:0", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "crwdns212982:0crwdne212982:0", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "crwdns1242506:0crwdne1242506:0", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "crwdns1242508:0crwdne1242508:0", "app.components.ContentUploadDisclaimer.onAccept": "crwdns1242510:0crwdne1242510:0", "app.components.ContentUploadDisclaimer.onCancel": "crwdns1242512:0crwdne1242512:0", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "crwdns4721687:0crwdne4721687:0", "app.components.CustomFieldsForm.addressInputAriaLabel": "crwdns4786349:0crwdne4786349:0", "app.components.CustomFieldsForm.addressInputPlaceholder6": "crwdns4786351:0crwdne4786351:0", "app.components.CustomFieldsForm.adminFieldTooltip": "crwdns4721689:0crwdne4721689:0", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "crwdns4786353:0crwdne4786353:0", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "crwdns4786355:0crwdne4786355:0", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "crwdns4786357:0crwdne4786357:0", "app.components.CustomFieldsForm.attachmentRequired": "crwdns4721691:0crwdne4721691:0", "app.components.CustomFieldsForm.authorFieldLabel": "crwdns4721693:0crwdne4721693:0", "app.components.CustomFieldsForm.authorFieldPlaceholder": "crwdns4721695:0crwdne4721695:0", "app.components.CustomFieldsForm.back": "crwdns4786359:0crwdne4786359:0", "app.components.CustomFieldsForm.budgetFieldLabel": "crwdns4721697:0crwdne4721697:0", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "crwdns4786361:0crwdne4786361:0", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "crwdns4786363:0crwdne4786363:0", "app.components.CustomFieldsForm.confirm": "crwdns4786365:0crwdne4786365:0", "app.components.CustomFieldsForm.descriptionMinLength": "crwdns4721699:0{min}crwdne4721699:0", "app.components.CustomFieldsForm.descriptionRequired": "crwdns4721701:0crwdne4721701:0", "app.components.CustomFieldsForm.fieldMaximumItems": "crwdns4747527:0maxSelections={maxSelections}crwdnd4747527:0fieldName={fieldName}crwdne4747527:0", "app.components.CustomFieldsForm.fieldMinimumItems": "crwdns4747529:0minSelections={minSelections}crwdnd4747529:0fieldName={fieldName}crwdne4747529:0", "app.components.CustomFieldsForm.fieldRequired": "crwdns4721707:0{fieldName}crwdne4721707:0", "app.components.CustomFieldsForm.fileSizeLimit": "crwdns4786367:0{maxFileSize}crwdne4786367:0", "app.components.CustomFieldsForm.imageRequired": "crwdns4721709:0crwdne4721709:0", "app.components.CustomFieldsForm.minimumCoordinates2": "crwdns4786369:0{numPoints}crwdne4786369:0", "app.components.CustomFieldsForm.notPublic1": "crwdns4721711:0crwdne4721711:0", "app.components.CustomFieldsForm.otherArea": "crwdns4812167:0crwdne4812167:0", "app.components.CustomFieldsForm.progressBarLabel": "crwdns4721713:0crwdne4721713:0", "app.components.CustomFieldsForm.removeAnswer": "crwdns4786371:0crwdne4786371:0", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "crwdns4721715:0crwdne4721715:0", "app.components.CustomFieldsForm.selectBetween": "crwdns4721717:0{minItems}crwdnd4721717:0{maxItems}crwdne4721717:0", "app.components.CustomFieldsForm.selectExactly2": "crwdns4721719:0selectExactly={selectExactly}crwdne4721719:0", "app.components.CustomFieldsForm.selectMany": "crwdns4721721:0crwdne4721721:0", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "crwdns4786373:0crwdne4786373:0", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "crwdns4786375:0crwdne4786375:0", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "crwdns4786377:0crwdne4786377:0", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "crwdns4786379:0crwdne4786379:0", "app.components.CustomFieldsForm.tapToAddALine": "crwdns4786381:0crwdne4786381:0", "app.components.CustomFieldsForm.tapToAddAPoint": "crwdns4786383:0crwdne4786383:0", "app.components.CustomFieldsForm.tapToAddAnArea": "crwdns4786385:0crwdne4786385:0", "app.components.CustomFieldsForm.titleMaxLength": "crwdns4721723:0{max}crwdne4721723:0", "app.components.CustomFieldsForm.titleMinLength": "crwdns4721725:0{min}crwdne4721725:0", "app.components.CustomFieldsForm.titleRequired": "crwdns4721727:0crwdne4721727:0", "app.components.CustomFieldsForm.topicRequired": "crwdns4721729:0crwdne4721729:0", "app.components.CustomFieldsForm.typeYourAnswer": "crwdns4721731:0crwdne4721731:0", "app.components.CustomFieldsForm.typeYourAnswerRequired": "crwdns4721733:0crwdne4721733:0", "app.components.CustomFieldsForm.uploadShapefileInstructions": "crwdns4786387:0crwdne4786387:0", "app.components.CustomFieldsForm.validCordinatesTooltip2": "crwdns4721735:0crwdne4721735:0", "app.components.ErrorBoundary.errorFormErrorFormEntry": "crwdns212992:0crwdne212992:0", "app.components.ErrorBoundary.errorFormErrorGeneric": "crwdns212994:0crwdne212994:0", "app.components.ErrorBoundary.errorFormLabelClose": "crwdns212996:0crwdne212996:0", "app.components.ErrorBoundary.errorFormLabelComments": "crwdns212998:0crwdne212998:0", "app.components.ErrorBoundary.errorFormLabelEmail": "crwdns213000:0crwdne213000:0", "app.components.ErrorBoundary.errorFormLabelName": "crwdns213002:0crwdne213002:0", "app.components.ErrorBoundary.errorFormLabelSubmit": "crwdns213004:0crwdne213004:0", "app.components.ErrorBoundary.errorFormSubtitle": "crwdns213006:0crwdne213006:0", "app.components.ErrorBoundary.errorFormSubtitle2": "crwdns213008:0crwdne213008:0", "app.components.ErrorBoundary.errorFormSuccessMessage": "crwdns213010:0crwdne213010:0", "app.components.ErrorBoundary.errorFormTitle": "crwdns213012:0crwdne213012:0", "app.components.ErrorBoundary.genericErrorWithForm": "crwdns213014:0{openForm}crwdne213014:0", "app.components.ErrorBoundary.openFormText": "crwdns213016:0crwdne213016:0", "app.components.ErrorToast.budgetExceededError": "crwdns777547:0crwdne777547:0", "app.components.ErrorToast.votesExceededError": "crwdns777549:0crwdne777549:0", "app.components.EventAttendanceButton.forwardToFriend": "crwdns953623:0crwdne953623:0", "app.components.EventAttendanceButton.maxRegistrationsReached": "crwdns4902947:0crwdne4902947:0", "app.components.EventAttendanceButton.register": "crwdns4902949:0crwdne4902949:0", "app.components.EventAttendanceButton.registered": "crwdns4902951:0crwdne4902951:0", "app.components.EventAttendanceButton.seeYouThere": "crwdns953625:0crwdne953625:0", "app.components.EventAttendanceButton.seeYouThereName": "crwdns953627:0{userFirstName}crwdne953627:0", "app.components.EventCard.a11y_lessContentVisible": "crwdns213018:0crwdne213018:0", "app.components.EventCard.a11y_moreContentVisible": "crwdns213020:0crwdne213020:0", "app.components.EventCard.a11y_readMore": "crwdns4760285:0{eventTitle}crwdne4760285:0", "app.components.EventCard.endsAt": "crwdns213022:0crwdne213022:0", "app.components.EventCard.readMore": "crwdns953629:0crwdne953629:0", "app.components.EventCard.showLess": "crwdns213024:0crwdne213024:0", "app.components.EventCard.showMore": "crwdns213026:0crwdne213026:0", "app.components.EventCard.startsAt": "crwdns213028:0crwdne213028:0", "app.components.EventPreviews.eventPreviewContinuousTitle2": "crwdns953631:0crwdne953631:0", "app.components.EventPreviews.eventPreviewTimelineTitle3": "crwdns953633:0crwdne953633:0", "app.components.FileUploader.a11y_file": "crwdns213030:0crwdne213030:0", "app.components.FileUploader.a11y_filesToBeUploaded": "crwdns213032:0{fileNames}crwdne213032:0", "app.components.FileUploader.a11y_noFiles": "crwdns213034:0crwdne213034:0", "app.components.FileUploader.a11y_removeFile": "crwdns213036:0crwdne213036:0", "app.components.FileUploader.fileInputDescription": "crwdns213038:0crwdne213038:0", "app.components.FileUploader.fileUploadLabel": "crwdns213040:0crwdne213040:0", "app.components.FileUploader.file_too_large2": "crwdns4747361:0{maxSizeMb}crwdne4747361:0", "app.components.FileUploader.incorrect_extension": "crwdns213042:0{fileName}crwdne213042:0", "app.components.FilterBoxes.a11y_allFilterSelected": "crwdns213044:0crwdne213044:0", "app.components.FilterBoxes.a11y_numberOfInputs": "crwdns213048:0inputsCount={inputsCount}crwdne213048:0", "app.components.FilterBoxes.a11y_removeFilter": "crwdns213050:0crwdne213050:0", "app.components.FilterBoxes.a11y_selectedFilter": "crwdns213052:0{filter}crwdne213052:0", "app.components.FilterBoxes.a11y_selectedTopicFilters": "crwdns213054:0numberOfSelectedTopics={numberOfSelectedTopics}crwdnd213054:0selectedTopicNames={selectedTopicNames}crwdne213054:0", "app.components.FilterBoxes.all": "crwdns213056:0crwdne213056:0", "app.components.FilterBoxes.areas": "crwdns213058:0crwdne213058:0", "app.components.FilterBoxes.inputs": "crwdns3645111:0crwdne3645111:0", "app.components.FilterBoxes.noValuesFound": "crwdns3716673:0crwdne3716673:0", "app.components.FilterBoxes.showLess": "crwdns3645113:0crwdne3645113:0", "app.components.FilterBoxes.showTagsWithNumber": "crwdns3645115:0{numberTags}crwdne3645115:0", "app.components.FilterBoxes.statusTitle": "crwdns213060:0crwdne213060:0", "app.components.FilterBoxes.topicsTitle": "crwdns213062:0crwdne213062:0", "app.components.FiltersModal.filters": "crwdns213066:0crwdne213066:0", "app.components.FolderFolderCard.a11y_folderDescription": "crwdns213070:0crwdne213070:0", "app.components.FolderFolderCard.a11y_folderTitle": "crwdns213072:0crwdne213072:0", "app.components.FolderFolderCard.archived": "crwdns213074:0crwdne213074:0", "app.components.FolderFolderCard.numberOfProjectsInFolder": "crwdns213076:0{# projects}crwdnd213076:0{# project}crwdnd213076:0{# projects}crwdne213076:0", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "crwdns3251119:0crwdne3251119:0", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "crwdns3142403:0crwdne3142403:0", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "crwdns3142397:0crwdne3142397:0", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "crwdns3142399:0crwdne3142399:0", "app.components.GanttChart.timeRange.month": "crwdns4889705:0crwdne4889705:0", "app.components.GanttChart.timeRange.quarter": "crwdns4889707:0crwdne4889707:0", "app.components.GanttChart.timeRange.timeRangeMultiyear": "crwdns4889709:0crwdne4889709:0", "app.components.GanttChart.timeRange.year": "crwdns4889711:0crwdne4889711:0", "app.components.GanttChart.today": "crwdns4889713:0crwdne4889713:0", "app.components.GoBackButton.group.edit.goBack": "crwdns213078:0crwdne213078:0", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "crwdns953635:0crwdne953635:0", "app.components.HookForm.Feedback.errorTitle": "crwdns213080:0crwdne213080:0", "app.components.HookForm.Feedback.submissionError": "crwdns213082:0crwdne213082:0", "app.components.HookForm.Feedback.submissionErrorTitle": "crwdns213084:0crwdne213084:0", "app.components.HookForm.Feedback.successMessage": "crwdns213086:0crwdne213086:0", "app.components.HookForm.PasswordInput.passwordLabel": "crwdns3251039:0crwdne3251039:0", "app.components.HorizontalScroll.scrollLeftLabel": "crwdns2677447:0crwdne2677447:0", "app.components.HorizontalScroll.scrollRightLabel": "crwdns2677449:0crwdne2677449:0", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "crwdns2400638:0{sortOder}crwdne2400638:0", "app.components.IdeaCards.filters": "crwdns3716675:0crwdne3716675:0", "app.components.IdeaCards.filters.mostDiscussed": "crwdns3645013:0crwdne3645013:0", "app.components.IdeaCards.filters.newest": "crwdns3561587:0crwdne3561587:0", "app.components.IdeaCards.filters.oldest": "crwdns3561589:0crwdne3561589:0", "app.components.IdeaCards.filters.popular": "crwdns3561591:0crwdne3561591:0", "app.components.IdeaCards.filters.random": "crwdns3561593:0crwdne3561593:0", "app.components.IdeaCards.filters.sortBy": "crwdns3561595:0crwdne3561595:0", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "crwdns3716677:0{currentSortType}crwdne3716677:0", "app.components.IdeaCards.filters.trending": "crwdns3561597:0crwdne3561597:0", "app.components.IdeaCards.showMore": "crwdns213088:0crwdne213088:0", "app.components.IdeasMap.a11y_hideIdeaCard": "crwdns213144:0crwdne213144:0", "app.components.IdeasMap.a11y_mapTitle": "crwdns213146:0crwdne213146:0", "app.components.IdeasMap.clickOnMapToAdd": "crwdns213148:0crwdne213148:0", "app.components.IdeasMap.clickOnMapToAddAdmin2": "crwdns1834640:0crwdne1834640:0", "app.components.IdeasMap.filters": "crwdns3716679:0crwdne3716679:0", "app.components.IdeasMap.multipleInputsAtLocation": "crwdns1834642:0crwdne1834642:0", "app.components.IdeasMap.noFilteredResults": "crwdns213152:0crwdne213152:0", "app.components.IdeasMap.noResults": "crwdns213154:0crwdne213154:0", "app.components.IdeasMap.or": "crwdns213158:0crwdne213158:0", "app.components.IdeasMap.screenReaderDislikesText": "crwdns2423286:0noOfDislikes={noOfDislikes}crwdne2423286:0", "app.components.IdeasMap.screenReaderLikesText": "crwdns2423288:0noOfLikes={noOfLikes}crwdne2423288:0", "app.components.IdeasMap.signInLinkText": "crwdns213170:0crwdne213170:0", "app.components.IdeasMap.signUpLinkText": "crwdns213172:0crwdne213172:0", "app.components.IdeasMap.submitIdea2": "crwdns1834644:0crwdne1834644:0", "app.components.IdeasMap.tapOnMapToAdd": "crwdns213174:0crwdne213174:0", "app.components.IdeasMap.tapOnMapToAddAdmin2": "crwdns1834646:0crwdne1834646:0", "app.components.IdeasMap.userInputs2": "crwdns1834648:0crwdne1834648:0", "app.components.IdeasMap.xComments": "crwdns2423290:0noOfComments={noOfComments}crwdne2423290:0", "app.components.IdeasShow.bodyTitle": "crwdns213176:0crwdne213176:0", "app.components.IdeasShow.deletePost": "crwdns213186:0crwdne213186:0", "app.components.IdeasShow.editPost": "crwdns213188:0crwdne213188:0", "app.components.IdeasShow.goBack": "crwdns213190:0crwdne213190:0", "app.components.IdeasShow.moreOptions": "crwdns213192:0crwdne213192:0", "app.components.IdeasShow.or": "crwdns213194:0crwdne213194:0", "app.components.IdeasShow.proposedBudgetTitle": "crwdns213196:0crwdne213196:0", "app.components.IdeasShow.reportAsSpam": "crwdns213198:0crwdne213198:0", "app.components.IdeasShow.send": "crwdns213200:0crwdne213200:0", "app.components.IdeasShow.skipSharing": "crwdns213202:0crwdne213202:0", "app.components.IdeasShowPage.signIn2": "crwdns327746:0crwdne327746:0", "app.components.IdeasShowPage.sorryNoAccess": "crwdns213208:0crwdne213208:0", "app.components.LocationInput.noOptions": "crwdns1199814:0crwdne1199814:0", "app.components.Modal.closeWindow": "crwdns2136232:0crwdne2136232:0", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "crwdns522973:0crwdne522973:0", "app.components.PageNotFound.goBackToHomePage": "crwdns213328:0crwdne213328:0", "app.components.PageNotFound.notFoundTitle": "crwdns213330:0crwdne213330:0", "app.components.PageNotFound.pageNotFoundDescription": "crwdns213332:0crwdne213332:0", "app.components.PagesForm.descriptionMissingOneLanguageError": "crwdns213334:0crwdne213334:0", "app.components.PagesForm.editContent": "crwdns213336:0crwdne213336:0", "app.components.PagesForm.fileUploadLabel": "crwdns213338:0crwdne213338:0", "app.components.PagesForm.fileUploadLabelTooltip": "crwdns213340:0crwdne213340:0", "app.components.PagesForm.navbarItemTitle": "crwdns213342:0crwdne213342:0", "app.components.PagesForm.pageTitle": "crwdns213344:0crwdne213344:0", "app.components.PagesForm.savePage": "crwdns213346:0crwdne213346:0", "app.components.PagesForm.saveSuccess": "crwdns213348:0crwdne213348:0", "app.components.PagesForm.titleMissingOneLanguageError": "crwdns213350:0crwdne213350:0", "app.components.Pagination.back": "crwdns4015941:0crwdne4015941:0", "app.components.Pagination.next": "crwdns4015943:0crwdne4015943:0", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "crwdns777551:0{votesCast}crwdnd777551:0{votesLimit}crwdne777551:0", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "crwdns4132547:0{budgetLeft}crwdnd4132547:0{totalBudget}crwdne4132547:0", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "crwdns4132549:0{votesMinimum}crwdne4132549:0", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "crwdns4916129:0crwdne4916129:0", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "crwdns777559:0crwdne777559:0", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "crwdns4916131:0votesLeft={votesLeft}crwdnd4916131:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916131:0", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "crwdns4916133:0votesLeft={votesLeft}crwdnd4916133:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916133:0", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "crwdns4916135:0votesLeft={votesLeft}crwdnd4916135:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916135:0", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "crwdns4916137:0votesLeft={votesLeft}crwdnd4916137:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916137:0", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "crwdns836137:0votes={votes}crwdne836137:0", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "crwdns777561:0{votesCast}crwdnd777561:0{votesLimit}crwdne777561:0", "app.components.ParticipationCTABars.addInput": "crwdns4747531:0crwdne4747531:0", "app.components.ParticipationCTABars.allocateBudget": "crwdns213352:0crwdne213352:0", "app.components.ParticipationCTABars.budgetSubmitSuccess": "crwdns2490442:0crwdne2490442:0", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "crwdns213354:0crwdne213354:0", "app.components.ParticipationCTABars.poll": "crwdns213356:0crwdne213356:0", "app.components.ParticipationCTABars.reviewDocument": "crwdns649383:0crwdne649383:0", "app.components.ParticipationCTABars.seeContributions": "crwdns3299887:0crwdne3299887:0", "app.components.ParticipationCTABars.seeEvents3": "crwdns953637:0crwdne953637:0", "app.components.ParticipationCTABars.seeIdeas": "crwdns213360:0crwdne213360:0", "app.components.ParticipationCTABars.seeInitiatives": "crwdns3299889:0crwdne3299889:0", "app.components.ParticipationCTABars.seeIssues": "crwdns3299891:0crwdne3299891:0", "app.components.ParticipationCTABars.seeOptions": "crwdns3299893:0crwdne3299893:0", "app.components.ParticipationCTABars.seePetitions": "crwdns3299895:0crwdne3299895:0", "app.components.ParticipationCTABars.seeProjects": "crwdns3299897:0crwdne3299897:0", "app.components.ParticipationCTABars.seeProposals": "crwdns3299899:0crwdne3299899:0", "app.components.ParticipationCTABars.seeQuestions": "crwdns3299901:0crwdne3299901:0", "app.components.ParticipationCTABars.submit": "crwdns777565:0crwdne777565:0", "app.components.ParticipationCTABars.takeTheSurvey": "crwdns213362:0crwdne213362:0", "app.components.ParticipationCTABars.userHasParticipated": "crwdns213364:0crwdne213364:0", "app.components.ParticipationCTABars.viewInputs": "crwdns4747533:0crwdne4747533:0", "app.components.ParticipationCTABars.volunteer": "crwdns213366:0crwdne213366:0", "app.components.ParticipationCTABars.votesCounter.vote": "crwdns836141:0crwdne836141:0", "app.components.ParticipationCTABars.votesCounter.votes": "crwdns777573:0crwdne777573:0", "app.components.PasswordInput.a11y_passwordHidden": "crwdns213372:0crwdne213372:0", "app.components.PasswordInput.a11y_passwordVisible": "crwdns213374:0crwdne213374:0", "app.components.PasswordInput.a11y_strength1Password": "crwdns213376:0crwdne213376:0", "app.components.PasswordInput.a11y_strength2Password": "crwdns213378:0crwdne213378:0", "app.components.PasswordInput.a11y_strength3Password": "crwdns213380:0crwdne213380:0", "app.components.PasswordInput.a11y_strength4Password": "crwdns213382:0crwdne213382:0", "app.components.PasswordInput.a11y_strength5Password": "crwdns213384:0crwdne213384:0", "app.components.PasswordInput.hidePassword": "crwdns213386:0crwdne213386:0", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "crwdns213388:0{minimumPasswordLength}crwdne213388:0", "app.components.PasswordInput.minimumPasswordLengthError": "crwdns213390:0{minimumPasswordLength}crwdne213390:0", "app.components.PasswordInput.passwordEmptyError": "crwdns213392:0crwdne213392:0", "app.components.PasswordInput.passwordStrengthTooltip1": "crwdns213394:0crwdne213394:0", "app.components.PasswordInput.passwordStrengthTooltip2": "crwdns213396:0crwdne213396:0", "app.components.PasswordInput.passwordStrengthTooltip3": "crwdns213398:0crwdne213398:0", "app.components.PasswordInput.passwordStrengthTooltip4": "crwdns213400:0crwdne213400:0", "app.components.PasswordInput.showPassword": "crwdns213402:0crwdne213402:0", "app.components.PasswordInput.strength1Password": "crwdns213404:0crwdne213404:0", "app.components.PasswordInput.strength2Password": "crwdns213406:0crwdne213406:0", "app.components.PasswordInput.strength3Password": "crwdns213408:0crwdne213408:0", "app.components.PasswordInput.strength4Password": "crwdns213410:0crwdne213410:0", "app.components.PasswordInput.strength5Password": "crwdns213412:0crwdne213412:0", "app.components.PostCardsComponents.list": "crwdns213414:0crwdne213414:0", "app.components.PostCardsComponents.map": "crwdns213416:0crwdne213416:0", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "crwdns213418:0crwdne213418:0", "app.components.PostComponents.OfficialFeedback.cancel": "crwdns213420:0crwdne213420:0", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "crwdns213422:0crwdne213422:0", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "crwdns213424:0crwdne213424:0", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "crwdns213426:0crwdne213426:0", "app.components.PostComponents.OfficialFeedback.lastEdition": "crwdns213428:0{date}crwdne213428:0", "app.components.PostComponents.OfficialFeedback.lastUpdate": "crwdns213430:0{lastUpdateDate}crwdne213430:0", "app.components.PostComponents.OfficialFeedback.official": "crwdns213432:0crwdne213432:0", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "crwdns213434:0crwdne213434:0", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "crwdns213436:0crwdne213436:0", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "crwdns213438:0crwdne213438:0", "app.components.PostComponents.OfficialFeedback.officialUpdates": "crwdns213440:0crwdne213440:0", "app.components.PostComponents.OfficialFeedback.postedOn": "crwdns213442:0{date}crwdne213442:0", "app.components.PostComponents.OfficialFeedback.publishButtonText": "crwdns213444:0crwdne213444:0", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "crwdns213446:0crwdne213446:0", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "crwdns213448:0crwdne213448:0", "app.components.PostComponents.OfficialFeedback.updateButtonError": "crwdns213450:0crwdne213450:0", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "crwdns213452:0crwdne213452:0", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "crwdns213454:0crwdne213454:0", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "crwdns213456:0{postTitle}crwdnd213456:0{postUrl}crwdne213456:0", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "crwdns213458:0{postTitle}crwdne213458:0", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "crwdns213460:0{postTitle}crwdne213460:0", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "crwdns213464:0{postTitle}crwdnd213464:0{postUrl}crwdne213464:0", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "crwdns213466:0{postTitle}crwdne213466:0", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "crwdns213468:0{postTitle}crwdne213468:0", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "crwdns213470:0{postUrl}crwdne213470:0", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "crwdns213472:0{postTitle}crwdne213472:0", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "crwdns3335079:0{postTitle}crwdne3335079:0", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "crwdns213474:0{postTitle}crwdnd213474:0{postUrl}crwdne213474:0", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "crwdns213476:0{postTitle}crwdne213476:0", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "crwdns213478:0{postTitle}crwdne213478:0", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "crwdns213480:0{postTitle}crwdnd213480:0{postUrl}crwdne213480:0", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "crwdns213482:0{postTitle}crwdne213482:0", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "crwdns213484:0{postTitle}crwdne213484:0", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "crwdns3335081:0{postTitle}crwdnd3335081:0{postUrl}crwdne3335081:0", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "crwdns3335083:0{postTitle}crwdne3335083:0", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "crwdns3335085:0{postTitle}crwdne3335085:0", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "crwdns213486:0{postTitle}crwdnd213486:0{postUrl}crwdne213486:0", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "crwdns213488:0{postTitle}crwdne213488:0", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "crwdns213490:0{postTitle}crwdne213490:0", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "crwdns3335087:0{postTitle}crwdnd3335087:0{postUrl}crwdne3335087:0", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "crwdns3335089:0{postTitle}crwdne3335089:0", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "crwdns3335091:0{orgName}crwdnd3335091:0{postTitle}crwdne3335091:0", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "crwdns213492:0{postTitle}crwdnd213492:0{postUrl}crwdne213492:0", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "crwdns213494:0{postTitle}crwdne213494:0", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "crwdns213496:0{postTitle}crwdne213496:0", "app.components.PostComponents.SharingModalContent.twitterMessage": "crwdns213498:0{postTitle}crwdne213498:0", "app.components.PostComponents.linkToHomePage": "crwdns213502:0crwdne213502:0", "app.components.PostComponents.readMore": "crwdns213504:0crwdne213504:0", "app.components.PostComponents.topics": "crwdns1081582:0crwdne1081582:0", "app.components.ProjectArchivedIndicator.archivedProject": "crwdns213506:0crwdne213506:0", "app.components.ProjectArchivedIndicator.previewProject": "crwdns3585479:0crwdne3585479:0", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "crwdns3585481:0crwdne3585481:0", "app.components.ProjectCard.a11y_projectDescription": "crwdns213508:0crwdne213508:0", "app.components.ProjectCard.a11y_projectTitle": "crwdns213510:0crwdne213510:0", "app.components.ProjectCard.addYourOption": "crwdns213512:0crwdne213512:0", "app.components.ProjectCard.allocateYourBudget": "crwdns213514:0crwdne213514:0", "app.components.ProjectCard.archived": "crwdns213516:0crwdne213516:0", "app.components.ProjectCard.comment": "crwdns213518:0crwdne213518:0", "app.components.ProjectCard.contributeYourInput": "crwdns213520:0crwdne213520:0", "app.components.ProjectCard.finished": "crwdns213522:0crwdne213522:0", "app.components.ProjectCard.joinDiscussion": "crwdns213524:0crwdne213524:0", "app.components.ProjectCard.learnMore": "crwdns213526:0crwdne213526:0", "app.components.ProjectCard.reaction": "crwdns777577:0crwdne777577:0", "app.components.ProjectCard.readTheReport": "crwdns3621249:0crwdne3621249:0", "app.components.ProjectCard.reviewDocument": "crwdns649385:0crwdne649385:0", "app.components.ProjectCard.submitAnIssue": "crwdns213530:0crwdne213530:0", "app.components.ProjectCard.submitYourIdea": "crwdns213532:0crwdne213532:0", "app.components.ProjectCard.submitYourInitiative": "crwdns3335093:0crwdne3335093:0", "app.components.ProjectCard.submitYourPetition": "crwdns3335095:0crwdne3335095:0", "app.components.ProjectCard.submitYourProject": "crwdns213534:0crwdne213534:0", "app.components.ProjectCard.submitYourProposal": "crwdns3335097:0crwdne3335097:0", "app.components.ProjectCard.takeThePoll": "crwdns213536:0crwdne213536:0", "app.components.ProjectCard.takeTheSurvey": "crwdns213538:0crwdne213538:0", "app.components.ProjectCard.viewTheContributions": "crwdns213540:0crwdne213540:0", "app.components.ProjectCard.viewTheIdeas": "crwdns213542:0crwdne213542:0", "app.components.ProjectCard.viewTheInitiatives": "crwdns3335099:0crwdne3335099:0", "app.components.ProjectCard.viewTheIssues": "crwdns213544:0crwdne213544:0", "app.components.ProjectCard.viewTheOptions": "crwdns213546:0crwdne213546:0", "app.components.ProjectCard.viewThePetitions": "crwdns3335101:0crwdne3335101:0", "app.components.ProjectCard.viewTheProjects": "crwdns213548:0crwdne213548:0", "app.components.ProjectCard.viewTheProposals": "crwdns3335103:0crwdne3335103:0", "app.components.ProjectCard.viewTheQuestions": "crwdns213550:0crwdne213550:0", "app.components.ProjectCard.vote": "crwdns1453562:0crwdne1453562:0", "app.components.ProjectCard.xComments": "crwdns213554:0commentsCount={commentsCount}crwdne213554:0", "app.components.ProjectCard.xContributions": "crwdns213556:0ideasCount={ideasCount}crwdne213556:0", "app.components.ProjectCard.xIdeas": "crwdns213558:0ideasCount={ideasCount}crwdne213558:0", "app.components.ProjectCard.xInitiatives": "crwdns3335105:0{# initiatives}crwdnd3335105:0{# initiative}crwdnd3335105:0{# initiatives}crwdne3335105:0", "app.components.ProjectCard.xIssues": "crwdns213560:0ideasCount={ideasCount}crwdne213560:0", "app.components.ProjectCard.xOptions": "crwdns213562:0ideasCount={ideasCount}crwdne213562:0", "app.components.ProjectCard.xPetitions": "crwdns3335107:0{# petitions}crwdnd3335107:0{# petition}crwdnd3335107:0{# petitions}crwdne3335107:0", "app.components.ProjectCard.xProjects": "crwdns213564:0ideasCount={ideasCount}crwdne213564:0", "app.components.ProjectCard.xProposals": "crwdns3335109:0{# proposals}crwdnd3335109:0{# proposal}crwdnd3335109:0{# proposals}crwdne3335109:0", "app.components.ProjectCard.xQuestions": "crwdns213566:0ideasCount={ideasCount}crwdne213566:0", "app.components.ProjectFolderCard.xComments": "crwdns721603:0{# comments}crwdnd721603:0{# comments}crwdnd721603:0{# comments}crwdne721603:0", "app.components.ProjectFolderCard.xInputs": "crwdns721605:0{# inputs}crwdnd721605:0{# input}crwdnd721605:0{# inputs}crwdne721605:0", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "crwdns213568:0{# projects}crwdnd213568:0{# project}crwdnd213568:0{# projects}crwdne213568:0", "app.components.ProjectFolderCards.components.Topbar.all": "crwdns213570:0crwdne213570:0", "app.components.ProjectFolderCards.components.Topbar.archived": "crwdns213572:0crwdne213572:0", "app.components.ProjectFolderCards.components.Topbar.draft": "crwdns213576:0crwdne213576:0", "app.components.ProjectFolderCards.components.Topbar.filterBy": "crwdns213578:0crwdne213578:0", "app.components.ProjectFolderCards.components.Topbar.published2": "crwdns3763223:0crwdne3763223:0", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "crwdns213582:0crwdne213582:0", "app.components.ProjectFolderCards.noProjectYet": "crwdns213584:0crwdne213584:0", "app.components.ProjectFolderCards.noProjectsAvailable": "crwdns213586:0crwdne213586:0", "app.components.ProjectFolderCards.showMore": "crwdns213588:0crwdne213588:0", "app.components.ProjectFolderCards.stayTuned": "crwdns213590:0crwdne213590:0", "app.components.ProjectFolderCards.tryChangingFilters": "crwdns213592:0crwdne213592:0", "app.components.ProjectTemplatePreview.alsoUsedIn": "crwdns213594:0crwdne213594:0", "app.components.ProjectTemplatePreview.copied": "crwdns213596:0crwdne213596:0", "app.components.ProjectTemplatePreview.copyLink": "crwdns213598:0crwdne213598:0", "app.components.QuillEditor.alignCenter": "crwdns213600:0crwdne213600:0", "app.components.QuillEditor.alignLeft": "crwdns213602:0crwdne213602:0", "app.components.QuillEditor.alignRight": "crwdns213604:0crwdne213604:0", "app.components.QuillEditor.bold": "crwdns213606:0crwdne213606:0", "app.components.QuillEditor.clean": "crwdns213608:0crwdne213608:0", "app.components.QuillEditor.customLink": "crwdns213610:0crwdne213610:0", "app.components.QuillEditor.customLinkPrompt": "crwdns213612:0crwdne213612:0", "app.components.QuillEditor.edit": "crwdns213614:0crwdne213614:0", "app.components.QuillEditor.image": "crwdns213616:0crwdne213616:0", "app.components.QuillEditor.imageAltPlaceholder": "crwdns213618:0crwdne213618:0", "app.components.QuillEditor.italic": "crwdns213620:0crwdne213620:0", "app.components.QuillEditor.link": "crwdns213622:0crwdne213622:0", "app.components.QuillEditor.linkPrompt": "crwdns213624:0crwdne213624:0", "app.components.QuillEditor.normalText": "crwdns213626:0crwdne213626:0", "app.components.QuillEditor.orderedList": "crwdns213628:0crwdne213628:0", "app.components.QuillEditor.remove": "crwdns213630:0crwdne213630:0", "app.components.QuillEditor.save": "crwdns213632:0crwdne213632:0", "app.components.QuillEditor.subtitle": "crwdns213634:0crwdne213634:0", "app.components.QuillEditor.title": "crwdns213636:0crwdne213636:0", "app.components.QuillEditor.unorderedList": "crwdns213638:0crwdne213638:0", "app.components.QuillEditor.video": "crwdns213640:0crwdne213640:0", "app.components.QuillEditor.videoPrompt": "crwdns213642:0crwdne213642:0", "app.components.QuillEditor.visitPrompt": "crwdns213644:0crwdne213644:0", "app.components.ReactionControl.completeProfileToReact": "crwdns777579:0crwdne777579:0", "app.components.ReactionControl.dislike": "crwdns777581:0crwdne777581:0", "app.components.ReactionControl.dislikingDisabledMaxReached": "crwdns777583:0{projectName}crwdne777583:0", "app.components.ReactionControl.like": "crwdns777585:0crwdne777585:0", "app.components.ReactionControl.likingDisabledMaxReached": "crwdns777587:0{projectName}crwdne777587:0", "app.components.ReactionControl.reactingDisabledFutureEnabled": "crwdns777589:0crwdne777589:0", "app.components.ReactionControl.reactingDisabledPhaseOver": "crwdns777591:0crwdne777591:0", "app.components.ReactionControl.reactingDisabledProjectInactive": "crwdns777593:0{projectName}crwdne777593:0", "app.components.ReactionControl.reactingNotEnabled": "crwdns777595:0crwdne777595:0", "app.components.ReactionControl.reactingNotPermitted": "crwdns777597:0crwdne777597:0", "app.components.ReactionControl.reactingNotSignedIn": "crwdns777599:0crwdne777599:0", "app.components.ReactionControl.reactingPossibleLater": "crwdns777601:0{enabledFromDate}crwdne777601:0", "app.components.ReactionControl.reactingVerifyToReact": "crwdns777603:0crwdne777603:0", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "crwdns2400762:0{startDate}crwdnd2400762:0{startTime}crwdnd2400762:0{endDate}crwdnd2400762:0{endTime}crwdne2400762:0", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "crwdns2400764:0{eventDate}crwdnd2400764:0{startTime}crwdnd2400764:0{endTime}crwdne2400764:0", "app.components.Sharing.linkCopied": "crwdns213654:0crwdne213654:0", "app.components.Sharing.or": "crwdns904273:0crwdne904273:0", "app.components.Sharing.share": "crwdns213658:0crwdne213658:0", "app.components.Sharing.shareByEmail": "crwdns213660:0crwdne213660:0", "app.components.Sharing.shareByLink": "crwdns213662:0crwdne213662:0", "app.components.Sharing.shareOnFacebook": "crwdns213664:0crwdne213664:0", "app.components.Sharing.shareOnTwitter": "crwdns213666:0crwdne213666:0", "app.components.Sharing.shareThisEvent": "crwdns826045:0crwdne826045:0", "app.components.Sharing.shareThisFolder": "crwdns213668:0crwdne213668:0", "app.components.Sharing.shareThisProject": "crwdns213672:0crwdne213672:0", "app.components.Sharing.shareViaMessenger": "crwdns213674:0crwdne213674:0", "app.components.Sharing.shareViaWhatsApp": "crwdns213676:0crwdne213676:0", "app.components.SideModal.closeButtonAria": "crwdns213678:0crwdne213678:0", "app.components.StatusModule.futurePhase": "crwdns777605:0crwdne777605:0", "app.components.StatusModule.modifyYourSubmission1": "crwdns4916139:0crwdne4916139:0", "app.components.StatusModule.submittedUntil3": "crwdns1464690:0crwdne1464690:0", "app.components.TopicsPicker.numberOfSelectedTopics": "crwdns213680:0numberOfSelectedTopics={numberOfSelectedTopics}crwdnd213680:0selectedTopicNames={selectedTopicNames}crwdne213680:0", "app.components.UI.FullscreenImage.expandImage": "crwdns2829805:0crwdne2829805:0", "app.components.UI.MoreActionsMenu.moreOptions": "crwdns2400640:0crwdne2400640:0", "app.components.UI.MoreActionsMenu.showMoreActions": "crwdns213682:0crwdne213682:0", "app.components.UI.PhaseFilter.noAppropriatePhases": "crwdns1845202:0crwdne1845202:0", "app.components.UI.RemoveImageButton.a11y_removeImage": "crwdns213684:0crwdne213684:0", "app.components.UI.TranslateButton.original": "crwdns213686:0crwdne213686:0", "app.components.UI.TranslateButton.translate": "crwdns213688:0crwdne213688:0", "app.components.Unauthorized.additionalInformationRequired": "crwdns1326148:0crwdne1326148:0", "app.components.Unauthorized.completeProfile": "crwdns1326150:0crwdne1326150:0", "app.components.Unauthorized.completeProfileTitle": "crwdns1326152:0crwdne1326152:0", "app.components.Unauthorized.noPermission": "crwdns213690:0crwdne213690:0", "app.components.Unauthorized.notAuthorized": "crwdns213692:0crwdne213692:0", "app.components.Upload.errorImageMaxSizeExceeded": "crwdns213694:0{maxFileSize}crwdne213694:0", "app.components.Upload.errorImagesMaxSizeExceeded": "crwdns213696:0{maxFileSize}crwdne213696:0", "app.components.Upload.onlyOneImage": "crwdns213698:0crwdne213698:0", "app.components.Upload.onlyXImages": "crwdns213700:0{maxItemsCount}crwdne213700:0", "app.components.Upload.remaining": "crwdns213702:0crwdne213702:0", "app.components.Upload.uploadImageLabel": "crwdns213704:0{maxImageSizeInMb}crwdne213704:0", "app.components.Upload.uploadMultipleImagesLabel": "crwdns213706:0crwdne213706:0", "app.components.UpsellTooltip.tooltipContent": "crwdns4193313:0crwdne4193313:0", "app.components.UserName.anonymous": "crwdns604451:0crwdne604451:0", "app.components.UserName.anonymousTooltip2": "crwdns604453:0crwdne604453:0", "app.components.UserName.authorWithNoNameTooltip": "crwdns3338249:0crwdne3338249:0", "app.components.UserName.deletedUser": "crwdns213708:0crwdne213708:0", "app.components.UserName.verified": "crwdns213712:0crwdne213712:0", "app.components.VerificationModal.verifyAuth0": "crwdns3848719:0crwdne3848719:0", "app.components.VerificationModal.verifyBOSA": "crwdns213738:0crwdne213738:0", "app.components.VerificationModal.verifyBosaFas": "crwdns3848721:0crwdne3848721:0", "app.components.VerificationModal.verifyClaveUnica": "crwdns213740:0crwdne213740:0", "app.components.VerificationModal.verifyFakeSSO": "crwdns3669011:0crwdne3669011:0", "app.components.VerificationModal.verifyIdAustria": "crwdns3548609:0crwdne3548609:0", "app.components.VerificationModal.verifyKeycloak": "crwdns3669013:0crwdne3669013:0", "app.components.VerificationModal.verifyNemLogIn": "crwdns845963:0crwdne845963:0", "app.components.VerificationModal.verifyTwoday2": "crwdns4419150:0crwdne4419150:0", "app.components.VerificationModal.verifyYourIdentity": "crwdns213742:0crwdne213742:0", "app.components.VoteControl.budgetingFutureEnabled": "crwdns213744:0{enabledFromDate}crwdne213744:0", "app.components.VoteControl.budgetingNotPermitted": "crwdns213746:0crwdne213746:0", "app.components.VoteControl.budgetingNotPossible": "crwdns213748:0crwdne213748:0", "app.components.VoteControl.budgetingNotVerified": "crwdns213750:0{verifyAccountLink}crwdne213750:0", "app.components.VoteInputs._shared.currencyLeft1": "crwdns4132551:0{budgetLeft}crwdnd4132551:0{totalBudget}crwdne4132551:0", "app.components.VoteInputs._shared.numberOfCreditsLeft": "crwdns4916141:0votesLeft={votesLeft}crwdnd4916141:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916141:0", "app.components.VoteInputs._shared.numberOfPointsLeft": "crwdns4916143:0votesLeft={votesLeft}crwdnd4916143:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916143:0", "app.components.VoteInputs._shared.numberOfTokensLeft": "crwdns4916145:0votesLeft={votesLeft}crwdnd4916145:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916145:0", "app.components.VoteInputs._shared.numberOfVotesLeft": "crwdns4916147:0votesLeft={votesLeft}crwdnd4916147:0totalNumberOfVotes={totalNumberOfVotes}crwdne4916147:0", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "crwdns4916149:0crwdne4916149:0", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "crwdns4916151:0crwdne4916151:0", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "crwdns3338605:0crwdne3338605:0", "app.components.VoteInputs.single.youHaveVotedForX2": "crwdns836149:0votes={votes}crwdne836149:0", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "crwdns1091384:0crwdne1091384:0", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "crwdns1091386:0crwdne1091386:0", "app.components.admin.PostManager.components.PostTable.Row.cancel": "crwdns1091388:0crwdne1091388:0", "app.components.admin.PostManager.components.PostTable.Row.confirm": "crwdns1091390:0crwdne1091390:0", "app.components.admin.SlugInput.resultingURL": "crwdns213776:0crwdne213776:0", "app.components.admin.SlugInput.slugTooltip": "crwdns213778:0crwdne213778:0", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "crwdns213780:0crwdne213780:0", "app.components.admin.SlugInput.urlSlugLabel": "crwdns1400780:0crwdne1400780:0", "app.components.admin.UserFilterConditions.addCondition": "crwdns213784:0crwdne213784:0", "app.components.admin.UserFilterConditions.field_email": "crwdns213786:0crwdne213786:0", "app.components.admin.UserFilterConditions.field_event_attendance": "crwdns1081494:0crwdne1081494:0", "app.components.admin.UserFilterConditions.field_follow": "crwdns855761:0crwdne855761:0", "app.components.admin.UserFilterConditions.field_lives_in": "crwdns213788:0crwdne213788:0", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "crwdns4305662:0crwdne4305662:0", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "crwdns213790:0crwdne213790:0", "app.components.admin.UserFilterConditions.field_participated_in_project": "crwdns213792:0crwdne213792:0", "app.components.admin.UserFilterConditions.field_participated_in_topic": "crwdns213794:0crwdne213794:0", "app.components.admin.UserFilterConditions.field_registration_completed_at": "crwdns213796:0crwdne213796:0", "app.components.admin.UserFilterConditions.field_role": "crwdns213798:0crwdne213798:0", "app.components.admin.UserFilterConditions.field_verified": "crwdns213800:0crwdne213800:0", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "crwdns3238945:0crwdne3238945:0", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "crwdns3238947:0crwdne3238947:0", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "crwdns1081496:0crwdne1081496:0", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "crwdns1081498:0crwdne1081498:0", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "crwdns1081500:0crwdne1081500:0", "app.components.admin.UserFilterConditions.predicate_attends_something": "crwdns1081502:0crwdne1081502:0", "app.components.admin.UserFilterConditions.predicate_begins_with": "crwdns213802:0crwdne213802:0", "app.components.admin.UserFilterConditions.predicate_commented_in": "crwdns213806:0crwdne213806:0", "app.components.admin.UserFilterConditions.predicate_contains": "crwdns213808:0crwdne213808:0", "app.components.admin.UserFilterConditions.predicate_ends_on": "crwdns213810:0crwdne213810:0", "app.components.admin.UserFilterConditions.predicate_has_value": "crwdns213812:0crwdne213812:0", "app.components.admin.UserFilterConditions.predicate_in": "crwdns213814:0crwdne213814:0", "app.components.admin.UserFilterConditions.predicate_is": "crwdns213816:0crwdne213816:0", "app.components.admin.UserFilterConditions.predicate_is_admin": "crwdns213818:0crwdne213818:0", "app.components.admin.UserFilterConditions.predicate_is_after": "crwdns213820:0crwdne213820:0", "app.components.admin.UserFilterConditions.predicate_is_before": "crwdns213822:0crwdne213822:0", "app.components.admin.UserFilterConditions.predicate_is_checked": "crwdns213824:0crwdne213824:0", "app.components.admin.UserFilterConditions.predicate_is_empty": "crwdns213826:0crwdne213826:0", "app.components.admin.UserFilterConditions.predicate_is_equal": "crwdns213828:0crwdne213828:0", "app.components.admin.UserFilterConditions.predicate_is_exactly": "crwdns213830:0crwdne213830:0", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "crwdns213832:0crwdne213832:0", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "crwdns213834:0crwdne213834:0", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "crwdns213836:0crwdne213836:0", "app.components.admin.UserFilterConditions.predicate_is_not_area": "crwdns1081584:0crwdne1081584:0", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "crwdns855763:0crwdne855763:0", "app.components.admin.UserFilterConditions.predicate_is_not_input": "crwdns3238949:0crwdne3238949:0", "app.components.admin.UserFilterConditions.predicate_is_not_project": "crwdns855769:0crwdne855769:0", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "crwdns1081586:0crwdne1081586:0", "app.components.admin.UserFilterConditions.predicate_is_one_of": "crwdns213838:0crwdne213838:0", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "crwdns1081588:0crwdne1081588:0", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "crwdns855771:0crwdne855771:0", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "crwdns3238951:0crwdne3238951:0", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "crwdns855777:0crwdne855777:0", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "crwdns1081590:0crwdne1081590:0", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "crwdns213840:0crwdne213840:0", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "crwdns213842:0crwdne213842:0", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "crwdns213844:0crwdne213844:0", "app.components.admin.UserFilterConditions.predicate_is_verified": "crwdns213846:0crwdne213846:0", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "crwdns213848:0crwdne213848:0", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "crwdns213852:0crwdne213852:0", "app.components.admin.UserFilterConditions.predicate_not_contains": "crwdns213854:0crwdne213854:0", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "crwdns213856:0crwdne213856:0", "app.components.admin.UserFilterConditions.predicate_not_has_value": "crwdns213858:0crwdne213858:0", "app.components.admin.UserFilterConditions.predicate_not_in": "crwdns213860:0crwdne213860:0", "app.components.admin.UserFilterConditions.predicate_not_is": "crwdns213862:0crwdne213862:0", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "crwdns213864:0crwdne213864:0", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "crwdns213866:0crwdne213866:0", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "crwdns213868:0crwdne213868:0", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "crwdns213870:0crwdne213870:0", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "crwdns213872:0crwdne213872:0", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "crwdns213874:0crwdne213874:0", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "crwdns213876:0crwdne213876:0", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "crwdns213878:0crwdne213878:0", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "crwdns213880:0crwdne213880:0", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "crwdns777615:0crwdne777615:0", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "crwdns777617:0crwdne777617:0", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "crwdns2147432:0crwdne2147432:0", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "crwdns4305664:0crwdne4305664:0", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "crwdns213882:0crwdne213882:0", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "crwdns777619:0crwdne777619:0", "app.components.admin.UserFilterConditions.predicate_nothing": "crwdns855779:0crwdne855779:0", "app.components.admin.UserFilterConditions.predicate_posted_input": "crwdns213888:0crwdne213888:0", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "crwdns777621:0crwdne777621:0", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "crwdns777623:0crwdne777623:0", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "crwdns2147434:0crwdne2147434:0", "app.components.admin.UserFilterConditions.predicate_something": "crwdns855781:0crwdne855781:0", "app.components.admin.UserFilterConditions.predicate_taken_survey": "crwdns4305666:0crwdne4305666:0", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "crwdns213890:0crwdne213890:0", "app.components.admin.UserFilterConditions.predicate_voted_in3": "crwdns777625:0crwdne777625:0", "app.components.admin.UserFilterConditions.rulesFormLabelField": "crwdns213896:0crwdne213896:0", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "crwdns213898:0crwdne213898:0", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "crwdns213900:0crwdne213900:0", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "crwdns604455:0crwdne604455:0", "app.components.anonymousParticipationModal.cancel": "crwdns604457:0crwdne604457:0", "app.components.anonymousParticipationModal.continue": "crwdns604459:0crwdne604459:0", "app.components.anonymousParticipationModal.participateAnonymously": "crwdns604461:0crwdne604461:0", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "crwdns2400478:0crwdne2400478:0", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "crwdns604465:0crwdne604465:0", "app.components.avatar.titleForAccessibility": "crwdns2503144:0{fullName}crwdne2503144:0", "app.components.customFields.mapInput.removeAnswer": "crwdns4786389:0crwdne4786389:0", "app.components.customFields.mapInput.undo": "crwdns4786391:0crwdne4786391:0", "app.components.customFields.mapInput.undoLastPoint": "crwdns4786393:0crwdne4786393:0", "app.components.followUnfollow.follow": "crwdns855783:0crwdne855783:0", "app.components.followUnfollow.followADiscussion": "crwdns1081592:0crwdne1081592:0", "app.components.followUnfollow.followTooltipInputPage2": "crwdns2016740:0{unsubscribeLink}crwdne2016740:0", "app.components.followUnfollow.followTooltipProjects2": "crwdns2016742:0{unsubscribeLink}crwdne2016742:0", "app.components.followUnfollow.unFollow": "crwdns855785:0crwdne855785:0", "app.components.followUnfollow.unsubscribe": "crwdns2016744:0crwdne2016744:0", "app.components.followUnfollow.unsubscribeUrl": "crwdns2016746:0crwdne2016746:0", "app.components.form.ErrorDisplay.guidelinesLinkText": "crwdns213902:0crwdne213902:0", "app.components.form.ErrorDisplay.next": "crwdns213904:0crwdne213904:0", "app.components.form.ErrorDisplay.previous": "crwdns213906:0crwdne213906:0", "app.components.form.ErrorDisplay.save": "crwdns353654:0crwdne353654:0", "app.components.form.ErrorDisplay.userPickerPlaceholder": "crwdns213910:0crwdne213910:0", "app.components.form.anonymousSurveyMessage2": "crwdns2747049:0crwdne2747049:0", "app.components.form.backToInputManager": "crwdns4747535:0crwdne4747535:0", "app.components.form.backToProject": "crwdns4156803:0crwdne4156803:0", "app.components.form.components.controls.mapInput.removeAnswer": "crwdns2747289:0crwdne2747289:0", "app.components.form.components.controls.mapInput.undo": "crwdns2747291:0crwdne2747291:0", "app.components.form.components.controls.mapInput.undoLastPoint": "crwdns2747293:0crwdne2747293:0", "app.components.form.controls.addressInputAriaLabel": "crwdns1962936:0crwdne1962936:0", "app.components.form.controls.addressInputPlaceholder6": "crwdns1962938:0crwdne1962938:0", "app.components.form.controls.adminFieldTooltip": "crwdns213914:0crwdne213914:0", "app.components.form.controls.allStatementsError": "crwdns4132853:0crwdne4132853:0", "app.components.form.controls.back": "crwdns1962940:0crwdne1962940:0", "app.components.form.controls.clearAll": "crwdns4061559:0crwdne4061559:0", "app.components.form.controls.clearAllScreenreader": "crwdns4132855:0crwdne4132855:0", "app.components.form.controls.clickOnMapMultipleToAdd3": "crwdns2747295:0crwdne2747295:0", "app.components.form.controls.clickOnMapToAddOrType": "crwdns2747297:0crwdne2747297:0", "app.components.form.controls.confirm": "crwdns1962944:0crwdne1962944:0", "app.components.form.controls.cosponsorsPlaceholder": "crwdns3238303:0crwdne3238303:0", "app.components.form.controls.currentRank": "crwdns4061561:0crwdne4061561:0", "app.components.form.controls.minimumCoordinates2": "crwdns2747299:0{numPoints}crwdne2747299:0", "app.components.form.controls.noRankSelected": "crwdns4061563:0crwdne4061563:0", "app.components.form.controls.notPublic1": "crwdns2400480:0crwdne2400480:0", "app.components.form.controls.optionalParentheses": "crwdns4218533:0crwdne4218533:0", "app.components.form.controls.rankingInstructions": "crwdns4061565:0crwdne4061565:0", "app.components.form.controls.selectAsManyAsYouLike": "crwdns1159898:0crwdne1159898:0", "app.components.form.controls.selectBetween": "crwdns1159900:0{minItems}crwdnd1159900:0{maxItems}crwdne1159900:0", "app.components.form.controls.selectExactly2": "crwdns1159902:0selectExactly={selectExactly}crwdne1159902:0", "app.components.form.controls.selectMany": "crwdns213918:0crwdne213918:0", "app.components.form.controls.tapOnFullscreenMapToAdd4": "crwdns2747301:0crwdne2747301:0", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "crwdns2770831:0crwdne2770831:0", "app.components.form.controls.tapOnMapMultipleToAdd3": "crwdns2747303:0crwdne2747303:0", "app.components.form.controls.tapOnMapToAddOrType": "crwdns2747305:0crwdne2747305:0", "app.components.form.controls.tapToAddALine": "crwdns2747307:0crwdne2747307:0", "app.components.form.controls.tapToAddAPoint": "crwdns1962950:0crwdne1962950:0", "app.components.form.controls.tapToAddAnArea": "crwdns2747309:0crwdne2747309:0", "app.components.form.controls.uploadShapefileInstructions": "crwdns2747311:0crwdne2747311:0", "app.components.form.controls.validCordinatesTooltip2": "crwdns2411866:0crwdne2411866:0", "app.components.form.controls.valueOutOfTotal": "crwdns2829807:0{value}crwdnd2829807:0{total}crwdne2829807:0", "app.components.form.controls.valueOutOfTotalWithLabel": "crwdns2829809:0{value}crwdnd2829809:0{total}crwdnd2829809:0{label}crwdne2829809:0", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "crwdns2829811:0{value}crwdnd2829811:0{total}crwdnd2829811:0{maxValue}crwdnd2829811:0{maxLabel}crwdne2829811:0", "app.components.form.error": "crwdns213920:0crwdne213920:0", "app.components.form.locationGoogleUnavailable": "crwdns213922:0crwdne213922:0", "app.components.form.progressBarLabel": "crwdns4156943:0crwdne4156943:0", "app.components.form.submit": "crwdns213924:0crwdne213924:0", "app.components.form.submitApiError": "crwdns213926:0crwdne213926:0", "app.components.form.verifiedBlocked": "crwdns2502688:0crwdne2502688:0", "app.components.formBuilder.Page": "crwdns213928:0crwdne213928:0", "app.components.formBuilder.accessibilityStatement": "crwdns2948599:0crwdne2948599:0", "app.components.formBuilder.addAnswer": "crwdns213930:0crwdne213930:0", "app.components.formBuilder.addStatement": "crwdns4132857:0crwdne4132857:0", "app.components.formBuilder.agree": "crwdns4132859:0crwdne4132859:0", "app.components.formBuilder.ai1": "crwdns1962762:0crwdne1962762:0", "app.components.formBuilder.aiUpsellText1": "crwdns1962764:0crwdne1962764:0", "app.components.formBuilder.askFollowUpToggleLabel": "crwdns4218535:0crwdne4218535:0", "app.components.formBuilder.bad": "crwdns4218537:0crwdne4218537:0", "app.components.formBuilder.buttonLabel": "crwdns4268384:0crwdne4268384:0", "app.components.formBuilder.buttonLink": "crwdns4268386:0crwdne4268386:0", "app.components.formBuilder.cancelLeaveBuilderButtonText": "crwdns1782268:0crwdne1782268:0", "app.components.formBuilder.category": "crwdns4368250:0crwdne4368250:0", "app.components.formBuilder.chooseMany": "crwdns213934:0crwdne213934:0", "app.components.formBuilder.chooseOne": "crwdns213936:0crwdne213936:0", "app.components.formBuilder.close": "crwdns213938:0crwdne213938:0", "app.components.formBuilder.closed": "crwdns213940:0crwdne213940:0", "app.components.formBuilder.configureMap": "crwdns2948601:0crwdne2948601:0", "app.components.formBuilder.confirmLeaveBuilderButtonText": "crwdns1782270:0crwdne1782270:0", "app.components.formBuilder.content": "crwdns213944:0crwdne213944:0", "app.components.formBuilder.continuePageLabel": "crwdns4156655:0crwdne4156655:0", "app.components.formBuilder.cosponsors": "crwdns3238305:0crwdne3238305:0", "app.components.formBuilder.default": "crwdns213946:0crwdne213946:0", "app.components.formBuilder.defaultContent": "crwdns213948:0crwdne213948:0", "app.components.formBuilder.delete": "crwdns213950:0crwdne213950:0", "app.components.formBuilder.deleteButtonLabel": "crwdns213952:0crwdne213952:0", "app.components.formBuilder.description": "crwdns213958:0crwdne213958:0", "app.components.formBuilder.disabledBuiltInFieldTooltip": "crwdns213960:0crwdne213960:0", "app.components.formBuilder.disabledCustomFieldsTooltip1": "crwdns3799147:0crwdne3799147:0", "app.components.formBuilder.disagree": "crwdns4132861:0crwdne4132861:0", "app.components.formBuilder.displayAsDropdown": "crwdns2888701:0crwdne2888701:0", "app.components.formBuilder.displayAsDropdownTooltip": "crwdns2888703:0crwdne2888703:0", "app.components.formBuilder.done": "crwdns213964:0crwdne213964:0", "app.components.formBuilder.drawArea": "crwdns2747313:0crwdne2747313:0", "app.components.formBuilder.drawRoute": "crwdns2747315:0crwdne2747315:0", "app.components.formBuilder.dropPin": "crwdns2747317:0crwdne2747317:0", "app.components.formBuilder.editButtonLabel": "crwdns213966:0crwdne213966:0", "app.components.formBuilder.emptyImageOptionError": "crwdns1782194:0crwdne1782194:0", "app.components.formBuilder.emptyOptionError": "crwdns213968:0crwdne213968:0", "app.components.formBuilder.emptyStatementError": "crwdns4132863:0crwdne4132863:0", "app.components.formBuilder.emptyTitleError": "crwdns213970:0crwdne213970:0", "app.components.formBuilder.emptyTitleMessage": "crwdns1782196:0crwdne1782196:0", "app.components.formBuilder.emptyTitleStatementMessage": "crwdns4132865:0crwdne4132865:0", "app.components.formBuilder.enable": "crwdns213972:0crwdne213972:0", "app.components.formBuilder.errorMessage": "crwdns213974:0crwdne213974:0", "app.components.formBuilder.fieldGroup.description": "crwdns213976:0crwdne213976:0", "app.components.formBuilder.fieldGroup.title": "crwdns213978:0crwdne213978:0", "app.components.formBuilder.fieldIsNotVisibleTooltip": "crwdns213980:0crwdne213980:0", "app.components.formBuilder.fieldLabel": "crwdns213982:0crwdne213982:0", "app.components.formBuilder.fieldLabelStatement": "crwdns4132867:0crwdne4132867:0", "app.components.formBuilder.fileUpload": "crwdns213984:0crwdne213984:0", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "crwdns2747051:0crwdne2747051:0", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "crwdns2747053:0crwdne2747053:0", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "crwdns2984445:0crwdne2984445:0", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "crwdns2747055:0crwdne2747055:0", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "crwdns2984447:0crwdne2984447:0", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "crwdns2747057:0crwdne2747057:0", "app.components.formBuilder.formEnd": "crwdns213986:0crwdne213986:0", "app.components.formBuilder.formField.cancelDeleteButtonText": "crwdns1667566:0crwdne1667566:0", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "crwdns1667568:0crwdne1667568:0", "app.components.formBuilder.formField.copyNoun": "crwdns1667570:0crwdne1667570:0", "app.components.formBuilder.formField.copyVerb": "crwdns1667572:0crwdne1667572:0", "app.components.formBuilder.formField.delete": "crwdns1667574:0crwdne1667574:0", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "crwdns1667576:0crwdne1667576:0", "app.components.formBuilder.formField.deleteResultsInfo": "crwdns1667578:0crwdne1667578:0", "app.components.formBuilder.goToPageInputLabel": "crwdns213988:0crwdne213988:0", "app.components.formBuilder.good": "crwdns4218539:0crwdne4218539:0", "app.components.formBuilder.helmetTitle": "crwdns213990:0crwdne213990:0", "app.components.formBuilder.imageFileUpload": "crwdns213992:0crwdne213992:0", "app.components.formBuilder.invalidLogicBadgeMessage": "crwdns213994:0crwdne213994:0", "app.components.formBuilder.labels2": "crwdns2876847:0crwdne2876847:0", "app.components.formBuilder.labelsTooltipContent2": "crwdns2876849:0crwdne2876849:0", "app.components.formBuilder.lastPage": "crwdns4156805:0crwdne4156805:0", "app.components.formBuilder.layout": "crwdns214000:0crwdne214000:0", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "crwdns1782272:0crwdne1782272:0", "app.components.formBuilder.leaveBuilderText": "crwdns1782274:0crwdne1782274:0", "app.components.formBuilder.limitAnswersTooltip": "crwdns1159904:0crwdne1159904:0", "app.components.formBuilder.limitNumberAnswers": "crwdns1159906:0crwdne1159906:0", "app.components.formBuilder.linePolygonMapWarning2": "crwdns2948603:0{accessibilityStatement}crwdne2948603:0", "app.components.formBuilder.linearScale": "crwdns214002:0crwdne214002:0", "app.components.formBuilder.locationDescription": "crwdns214004:0crwdne214004:0", "app.components.formBuilder.logic": "crwdns214006:0crwdne214006:0", "app.components.formBuilder.logicAnyOtherAnswer": "crwdns4156657:0crwdne4156657:0", "app.components.formBuilder.logicConflicts.conflictingLogic": "crwdns4156659:0crwdne4156659:0", "app.components.formBuilder.logicConflicts.interQuestionConflict": "crwdns4156661:0crwdne4156661:0", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "crwdns4156663:0crwdne4156663:0", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "crwdns4156665:0crwdne4156665:0", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "crwdns4156667:0crwdne4156667:0", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "crwdns4156669:0crwdne4156669:0", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "crwdns4156671:0crwdne4156671:0", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "crwdns4156673:0crwdne4156673:0", "app.components.formBuilder.logicNoAnswer2": "crwdns4156675:0crwdne4156675:0", "app.components.formBuilder.logicPanelAnyOtherAnswer": "crwdns4156677:0crwdne4156677:0", "app.components.formBuilder.logicPanelNoAnswer": "crwdns4156679:0crwdne4156679:0", "app.components.formBuilder.logicValidationError": "crwdns214008:0crwdne214008:0", "app.components.formBuilder.longAnswer": "crwdns214010:0crwdne214010:0", "app.components.formBuilder.mapConfiguration": "crwdns2948605:0crwdne2948605:0", "app.components.formBuilder.mapping": "crwdns2747319:0crwdne2747319:0", "app.components.formBuilder.mappingNotInCurrentLicense": "crwdns2984449:0crwdne2984449:0", "app.components.formBuilder.matrix": "crwdns4132869:0crwdne4132869:0", "app.components.formBuilder.matrixSettings.columns": "crwdns4132871:0crwdne4132871:0", "app.components.formBuilder.matrixSettings.rows": "crwdns4132873:0crwdne4132873:0", "app.components.formBuilder.multipleChoice": "crwdns214012:0crwdne214012:0", "app.components.formBuilder.multipleChoiceHelperText": "crwdns4156681:0crwdne4156681:0", "app.components.formBuilder.multipleChoiceImage": "crwdns1667580:0crwdne1667580:0", "app.components.formBuilder.multiselect.maximum": "crwdns1159908:0crwdne1159908:0", "app.components.formBuilder.multiselect.minimum": "crwdns1159910:0crwdne1159910:0", "app.components.formBuilder.neutral": "crwdns4132875:0crwdne4132875:0", "app.components.formBuilder.newField": "crwdns214014:0crwdne214014:0", "app.components.formBuilder.number": "crwdns214018:0crwdne214018:0", "app.components.formBuilder.ok": "crwdns4218541:0crwdne4218541:0", "app.components.formBuilder.open": "crwdns214020:0crwdne214020:0", "app.components.formBuilder.optional": "crwdns214022:0crwdne214022:0", "app.components.formBuilder.other": "crwdns1782198:0crwdne1782198:0", "app.components.formBuilder.otherOption": "crwdns1667582:0crwdne1667582:0", "app.components.formBuilder.otherOptionTooltip": "crwdns1667584:0crwdne1667584:0", "app.components.formBuilder.page": "crwdns214024:0crwdne214024:0", "app.components.formBuilder.pageCannotBeDeleted": "crwdns4243963:0crwdne4243963:0", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "crwdns4368124:0crwdne4368124:0", "app.components.formBuilder.pageRuleLabel": "crwdns214026:0crwdne214026:0", "app.components.formBuilder.pagesLogicHelperTextDefault1": "crwdns4156683:0{supportPageLink}crwdne4156683:0", "app.components.formBuilder.preview": "crwdns4268388:0crwdne4268388:0", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "crwdns4760289:0crwdne4760289:0", "app.components.formBuilder.printSupportTooltip.fileupload": "crwdns4760291:0crwdne4760291:0", "app.components.formBuilder.printSupportTooltip.mapping": "crwdns4760293:0crwdne4760293:0", "app.components.formBuilder.printSupportTooltip.matrix": "crwdns4760295:0crwdne4760295:0", "app.components.formBuilder.printSupportTooltip.page": "crwdns4760297:0crwdne4760297:0", "app.components.formBuilder.printSupportTooltip.ranking": "crwdns4760299:0crwdne4760299:0", "app.components.formBuilder.printSupportTooltip.topics2": "crwdns4760301:0crwdne4760301:0", "app.components.formBuilder.proposedBudget": "crwdns214030:0crwdne214030:0", "app.components.formBuilder.question": "crwdns214032:0crwdne214032:0", "app.components.formBuilder.questionCannotBeDeleted": "crwdns214034:0crwdne214034:0", "app.components.formBuilder.questionDescriptionOptional": "crwdns214036:0crwdne214036:0", "app.components.formBuilder.questionTitle": "crwdns214040:0crwdne214040:0", "app.components.formBuilder.randomize": "crwdns1667586:0crwdne1667586:0", "app.components.formBuilder.randomizeToolTip": "crwdns1667588:0crwdne1667588:0", "app.components.formBuilder.range": "crwdns214042:0crwdne214042:0", "app.components.formBuilder.ranking": "crwdns4061567:0crwdne4061567:0", "app.components.formBuilder.rating": "crwdns4156875:0crwdne4156875:0", "app.components.formBuilder.removeAnswer": "crwdns214044:0crwdne214044:0", "app.components.formBuilder.required": "crwdns214046:0crwdne214046:0", "app.components.formBuilder.requiredToggleLabel": "crwdns214048:0crwdne214048:0", "app.components.formBuilder.ruleForAnswerLabel": "crwdns214050:0crwdne214050:0", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "crwdns4156685:0crwdne4156685:0", "app.components.formBuilder.save": "crwdns214052:0crwdne214052:0", "app.components.formBuilder.selectRangeTooltip": "crwdns214058:0crwdne214058:0", "app.components.formBuilder.sentiment": "crwdns4218543:0crwdne4218543:0", "app.components.formBuilder.shapefileUpload": "crwdns2747321:0crwdne2747321:0", "app.components.formBuilder.shortAnswer": "crwdns214060:0crwdne214060:0", "app.components.formBuilder.showResponseToUsersToggleLabel": "crwdns214062:0crwdne214062:0", "app.components.formBuilder.singleChoice": "crwdns214064:0crwdne214064:0", "app.components.formBuilder.staleDataErrorMessage2": "crwdns3944847:0crwdne3944847:0", "app.components.formBuilder.stronglyAgree": "crwdns4132877:0crwdne4132877:0", "app.components.formBuilder.stronglyDisagree": "crwdns4132879:0crwdne4132879:0", "app.components.formBuilder.supportArticleLinkText": "crwdns214066:0crwdne214066:0", "app.components.formBuilder.tags": "crwdns214068:0crwdne214068:0", "app.components.formBuilder.title": "crwdns214070:0crwdne214070:0", "app.components.formBuilder.toLabel": "crwdns214072:0crwdne214072:0", "app.components.formBuilder.unsavedChanges": "crwdns1782276:0crwdne1782276:0", "app.components.formBuilder.useCustomButton2": "crwdns4268390:0crwdne4268390:0", "app.components.formBuilder.veryBad": "crwdns4218545:0crwdne4218545:0", "app.components.formBuilder.veryGood": "crwdns4218547:0crwdne4218547:0", "app.components.ideas.similarIdeas.engageHere": "crwdns4368126:0crwdne4368126:0", "app.components.ideas.similarIdeas.noSimilarSubmissions": "crwdns4368128:0crwdne4368128:0", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "crwdns4368130:0crwdne4368130:0", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "crwdns4368132:0crwdne4368132:0", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "crwdns4368134:0crwdne4368134:0", "app.components.phaseTimeLeft.xDayLeft": "crwdns3251025:0timeLeft={timeLeft}crwdne3251025:0", "app.components.phaseTimeLeft.xWeeksLeft": "crwdns3251027:0{timeLeft}crwdne3251027:0", "app.components.screenReaderCurrency.AED": "crwdns2490444:0crwdne2490444:0", "app.components.screenReaderCurrency.AFN": "crwdns2490446:0crwdne2490446:0", "app.components.screenReaderCurrency.ALL": "crwdns2490448:0crwdne2490448:0", "app.components.screenReaderCurrency.AMD": "crwdns2490450:0crwdne2490450:0", "app.components.screenReaderCurrency.ANG": "crwdns2490452:0crwdne2490452:0", "app.components.screenReaderCurrency.AOA": "crwdns2490454:0crwdne2490454:0", "app.components.screenReaderCurrency.ARS": "crwdns2490456:0crwdne2490456:0", "app.components.screenReaderCurrency.AUD": "crwdns2490458:0crwdne2490458:0", "app.components.screenReaderCurrency.AWG": "crwdns2490460:0crwdne2490460:0", "app.components.screenReaderCurrency.AZN": "crwdns2490462:0crwdne2490462:0", "app.components.screenReaderCurrency.BAM": "crwdns2490464:0crwdne2490464:0", "app.components.screenReaderCurrency.BBD": "crwdns2490466:0crwdne2490466:0", "app.components.screenReaderCurrency.BDT": "crwdns2490468:0crwdne2490468:0", "app.components.screenReaderCurrency.BGN": "crwdns2490470:0crwdne2490470:0", "app.components.screenReaderCurrency.BHD": "crwdns2490472:0crwdne2490472:0", "app.components.screenReaderCurrency.BIF": "crwdns2490474:0crwdne2490474:0", "app.components.screenReaderCurrency.BMD": "crwdns2490476:0crwdne2490476:0", "app.components.screenReaderCurrency.BND": "crwdns2490478:0crwdne2490478:0", "app.components.screenReaderCurrency.BOB": "crwdns2490480:0crwdne2490480:0", "app.components.screenReaderCurrency.BOV": "crwdns2490482:0crwdne2490482:0", "app.components.screenReaderCurrency.BRL": "crwdns2490484:0crwdne2490484:0", "app.components.screenReaderCurrency.BSD": "crwdns2490486:0crwdne2490486:0", "app.components.screenReaderCurrency.BTN": "crwdns2490488:0crwdne2490488:0", "app.components.screenReaderCurrency.BWP": "crwdns2490490:0crwdne2490490:0", "app.components.screenReaderCurrency.BYR": "crwdns2490492:0crwdne2490492:0", "app.components.screenReaderCurrency.BZD": "crwdns2490494:0crwdne2490494:0", "app.components.screenReaderCurrency.CAD": "crwdns2490496:0crwdne2490496:0", "app.components.screenReaderCurrency.CDF": "crwdns2490498:0crwdne2490498:0", "app.components.screenReaderCurrency.CHE": "crwdns2490500:0crwdne2490500:0", "app.components.screenReaderCurrency.CHF": "crwdns2490502:0crwdne2490502:0", "app.components.screenReaderCurrency.CHW": "crwdns2490504:0crwdne2490504:0", "app.components.screenReaderCurrency.CLF": "crwdns2490506:0crwdne2490506:0", "app.components.screenReaderCurrency.CLP": "crwdns2490508:0crwdne2490508:0", "app.components.screenReaderCurrency.CNY": "crwdns2490510:0crwdne2490510:0", "app.components.screenReaderCurrency.COP": "crwdns2490512:0crwdne2490512:0", "app.components.screenReaderCurrency.COU": "crwdns2490514:0crwdne2490514:0", "app.components.screenReaderCurrency.CRC": "crwdns2490516:0crwdne2490516:0", "app.components.screenReaderCurrency.CRE": "crwdns2490518:0crwdne2490518:0", "app.components.screenReaderCurrency.CUC": "crwdns2490520:0crwdne2490520:0", "app.components.screenReaderCurrency.CUP": "crwdns2490522:0crwdne2490522:0", "app.components.screenReaderCurrency.CVE": "crwdns2490524:0crwdne2490524:0", "app.components.screenReaderCurrency.CZK": "crwdns2490526:0crwdne2490526:0", "app.components.screenReaderCurrency.DJF": "crwdns2490528:0crwdne2490528:0", "app.components.screenReaderCurrency.DKK": "crwdns2490530:0crwdne2490530:0", "app.components.screenReaderCurrency.DOP": "crwdns2490532:0crwdne2490532:0", "app.components.screenReaderCurrency.DZD": "crwdns2490534:0crwdne2490534:0", "app.components.screenReaderCurrency.EGP": "crwdns2490536:0crwdne2490536:0", "app.components.screenReaderCurrency.ERN": "crwdns2490538:0crwdne2490538:0", "app.components.screenReaderCurrency.ETB": "crwdns2490540:0crwdne2490540:0", "app.components.screenReaderCurrency.EUR": "crwdns2490542:0crwdne2490542:0", "app.components.screenReaderCurrency.FJD": "crwdns2490544:0crwdne2490544:0", "app.components.screenReaderCurrency.FKP": "crwdns2490546:0crwdne2490546:0", "app.components.screenReaderCurrency.GBP": "crwdns2490548:0crwdne2490548:0", "app.components.screenReaderCurrency.GEL": "crwdns2490550:0crwdne2490550:0", "app.components.screenReaderCurrency.GHS": "crwdns2490552:0crwdne2490552:0", "app.components.screenReaderCurrency.GIP": "crwdns2490554:0crwdne2490554:0", "app.components.screenReaderCurrency.GMD": "crwdns2490556:0crwdne2490556:0", "app.components.screenReaderCurrency.GNF": "crwdns2490558:0crwdne2490558:0", "app.components.screenReaderCurrency.GTQ": "crwdns2490560:0crwdne2490560:0", "app.components.screenReaderCurrency.GYD": "crwdns2490562:0crwdne2490562:0", "app.components.screenReaderCurrency.HKD": "crwdns2490564:0crwdne2490564:0", "app.components.screenReaderCurrency.HNL": "crwdns2490566:0crwdne2490566:0", "app.components.screenReaderCurrency.HRK": "crwdns2490568:0crwdne2490568:0", "app.components.screenReaderCurrency.HTG": "crwdns2490570:0crwdne2490570:0", "app.components.screenReaderCurrency.HUF": "crwdns2490572:0crwdne2490572:0", "app.components.screenReaderCurrency.IDR": "crwdns2490574:0crwdne2490574:0", "app.components.screenReaderCurrency.ILS": "crwdns2490576:0crwdne2490576:0", "app.components.screenReaderCurrency.INR": "crwdns2490578:0crwdne2490578:0", "app.components.screenReaderCurrency.IQD": "crwdns2490580:0crwdne2490580:0", "app.components.screenReaderCurrency.IRR": "crwdns2490582:0crwdne2490582:0", "app.components.screenReaderCurrency.ISK": "crwdns2490584:0crwdne2490584:0", "app.components.screenReaderCurrency.JMD": "crwdns2490586:0crwdne2490586:0", "app.components.screenReaderCurrency.JOD": "crwdns2490588:0crwdne2490588:0", "app.components.screenReaderCurrency.JPY": "crwdns2490590:0crwdne2490590:0", "app.components.screenReaderCurrency.KES": "crwdns2490592:0crwdne2490592:0", "app.components.screenReaderCurrency.KGS": "crwdns2490594:0crwdne2490594:0", "app.components.screenReaderCurrency.KHR": "crwdns2490596:0crwdne2490596:0", "app.components.screenReaderCurrency.KMF": "crwdns2490598:0crwdne2490598:0", "app.components.screenReaderCurrency.KPW": "crwdns2490600:0crwdne2490600:0", "app.components.screenReaderCurrency.KRW": "crwdns2490602:0crwdne2490602:0", "app.components.screenReaderCurrency.KWD": "crwdns2490604:0crwdne2490604:0", "app.components.screenReaderCurrency.KYD": "crwdns2490606:0crwdne2490606:0", "app.components.screenReaderCurrency.KZT": "crwdns2490608:0crwdne2490608:0", "app.components.screenReaderCurrency.LAK": "crwdns2490610:0crwdne2490610:0", "app.components.screenReaderCurrency.LBP": "crwdns2490612:0crwdne2490612:0", "app.components.screenReaderCurrency.LKR": "crwdns2490614:0crwdne2490614:0", "app.components.screenReaderCurrency.LRD": "crwdns2490616:0crwdne2490616:0", "app.components.screenReaderCurrency.LSL": "crwdns2490618:0crwdne2490618:0", "app.components.screenReaderCurrency.LTL": "crwdns2490620:0crwdne2490620:0", "app.components.screenReaderCurrency.LVL": "crwdns2490622:0crwdne2490622:0", "app.components.screenReaderCurrency.LYD": "crwdns2490624:0crwdne2490624:0", "app.components.screenReaderCurrency.MAD": "crwdns2490626:0crwdne2490626:0", "app.components.screenReaderCurrency.MDL": "crwdns2490628:0crwdne2490628:0", "app.components.screenReaderCurrency.MGA": "crwdns2490630:0crwdne2490630:0", "app.components.screenReaderCurrency.MKD": "crwdns2490632:0crwdne2490632:0", "app.components.screenReaderCurrency.MMK": "crwdns2490634:0crwdne2490634:0", "app.components.screenReaderCurrency.MNT": "crwdns2490636:0crwdne2490636:0", "app.components.screenReaderCurrency.MOP": "crwdns2490638:0crwdne2490638:0", "app.components.screenReaderCurrency.MRO": "crwdns2490640:0crwdne2490640:0", "app.components.screenReaderCurrency.MUR": "crwdns2490642:0crwdne2490642:0", "app.components.screenReaderCurrency.MVR": "crwdns2490644:0crwdne2490644:0", "app.components.screenReaderCurrency.MWK": "crwdns2490646:0crwdne2490646:0", "app.components.screenReaderCurrency.MXN": "crwdns2490648:0crwdne2490648:0", "app.components.screenReaderCurrency.MXV": "crwdns2490650:0crwdne2490650:0", "app.components.screenReaderCurrency.MYR": "crwdns2490652:0crwdne2490652:0", "app.components.screenReaderCurrency.MZN": "crwdns2490654:0crwdne2490654:0", "app.components.screenReaderCurrency.NAD": "crwdns2490656:0crwdne2490656:0", "app.components.screenReaderCurrency.NGN": "crwdns2490658:0crwdne2490658:0", "app.components.screenReaderCurrency.NIO": "crwdns2490660:0crwdne2490660:0", "app.components.screenReaderCurrency.NOK": "crwdns2490662:0crwdne2490662:0", "app.components.screenReaderCurrency.NPR": "crwdns2490664:0crwdne2490664:0", "app.components.screenReaderCurrency.NZD": "crwdns2490666:0crwdne2490666:0", "app.components.screenReaderCurrency.OMR": "crwdns2490668:0crwdne2490668:0", "app.components.screenReaderCurrency.PAB": "crwdns2490670:0crwdne2490670:0", "app.components.screenReaderCurrency.PEN": "crwdns2490672:0crwdne2490672:0", "app.components.screenReaderCurrency.PGK": "crwdns2490674:0crwdne2490674:0", "app.components.screenReaderCurrency.PHP": "crwdns2490676:0crwdne2490676:0", "app.components.screenReaderCurrency.PKR": "crwdns2490678:0crwdne2490678:0", "app.components.screenReaderCurrency.PLN": "crwdns2490680:0crwdne2490680:0", "app.components.screenReaderCurrency.PYG": "crwdns2490682:0crwdne2490682:0", "app.components.screenReaderCurrency.QAR": "crwdns2490684:0crwdne2490684:0", "app.components.screenReaderCurrency.RON": "crwdns2490686:0crwdne2490686:0", "app.components.screenReaderCurrency.RSD": "crwdns2490688:0crwdne2490688:0", "app.components.screenReaderCurrency.RUB": "crwdns2490690:0crwdne2490690:0", "app.components.screenReaderCurrency.RWF": "crwdns2490692:0crwdne2490692:0", "app.components.screenReaderCurrency.SAR": "crwdns2490694:0crwdne2490694:0", "app.components.screenReaderCurrency.SBD": "crwdns2490696:0crwdne2490696:0", "app.components.screenReaderCurrency.SCR": "crwdns2490698:0crwdne2490698:0", "app.components.screenReaderCurrency.SDG": "crwdns2490700:0crwdne2490700:0", "app.components.screenReaderCurrency.SEK": "crwdns2490702:0crwdne2490702:0", "app.components.screenReaderCurrency.SGD": "crwdns2490704:0crwdne2490704:0", "app.components.screenReaderCurrency.SHP": "crwdns2490706:0crwdne2490706:0", "app.components.screenReaderCurrency.SLL": "crwdns2490708:0crwdne2490708:0", "app.components.screenReaderCurrency.SOS": "crwdns2490710:0crwdne2490710:0", "app.components.screenReaderCurrency.SRD": "crwdns2490712:0crwdne2490712:0", "app.components.screenReaderCurrency.SSP": "crwdns2490714:0crwdne2490714:0", "app.components.screenReaderCurrency.STD": "crwdns2490716:0crwdne2490716:0", "app.components.screenReaderCurrency.SYP": "crwdns2490718:0crwdne2490718:0", "app.components.screenReaderCurrency.SZL": "crwdns2490720:0crwdne2490720:0", "app.components.screenReaderCurrency.THB": "crwdns2490722:0crwdne2490722:0", "app.components.screenReaderCurrency.TJS": "crwdns2490724:0crwdne2490724:0", "app.components.screenReaderCurrency.TMT": "crwdns2490726:0crwdne2490726:0", "app.components.screenReaderCurrency.TND": "crwdns2490728:0crwdne2490728:0", "app.components.screenReaderCurrency.TOK": "crwdns2490730:0crwdne2490730:0", "app.components.screenReaderCurrency.TOP": "crwdns2490732:0crwdne2490732:0", "app.components.screenReaderCurrency.TRY": "crwdns2490734:0crwdne2490734:0", "app.components.screenReaderCurrency.TTD": "crwdns2490736:0crwdne2490736:0", "app.components.screenReaderCurrency.TWD": "crwdns2490738:0crwdne2490738:0", "app.components.screenReaderCurrency.TZS": "crwdns2490740:0crwdne2490740:0", "app.components.screenReaderCurrency.UAH": "crwdns2490742:0crwdne2490742:0", "app.components.screenReaderCurrency.UGX": "crwdns2490744:0crwdne2490744:0", "app.components.screenReaderCurrency.USD": "crwdns2490746:0crwdne2490746:0", "app.components.screenReaderCurrency.USN": "crwdns2490748:0crwdne2490748:0", "app.components.screenReaderCurrency.USS": "crwdns2490750:0crwdne2490750:0", "app.components.screenReaderCurrency.UYI": "crwdns2490752:0crwdne2490752:0", "app.components.screenReaderCurrency.UYU": "crwdns2490754:0crwdne2490754:0", "app.components.screenReaderCurrency.UZS": "crwdns2490756:0crwdne2490756:0", "app.components.screenReaderCurrency.VEF": "crwdns2490758:0crwdne2490758:0", "app.components.screenReaderCurrency.VND": "crwdns2490760:0crwdne2490760:0", "app.components.screenReaderCurrency.VUV": "crwdns2490762:0crwdne2490762:0", "app.components.screenReaderCurrency.WST": "crwdns2490764:0crwdne2490764:0", "app.components.screenReaderCurrency.XAF": "crwdns2490766:0crwdne2490766:0", "app.components.screenReaderCurrency.XAG": "crwdns2490768:0crwdne2490768:0", "app.components.screenReaderCurrency.XAU": "crwdns2490770:0crwdne2490770:0", "app.components.screenReaderCurrency.XBA": "crwdns2490772:0crwdne2490772:0", "app.components.screenReaderCurrency.XBB": "crwdns2490774:0crwdne2490774:0", "app.components.screenReaderCurrency.XBC": "crwdns2490776:0crwdne2490776:0", "app.components.screenReaderCurrency.XBD": "crwdns2490778:0crwdne2490778:0", "app.components.screenReaderCurrency.XCD": "crwdns2490780:0crwdne2490780:0", "app.components.screenReaderCurrency.XDR": "crwdns2490782:0crwdne2490782:0", "app.components.screenReaderCurrency.XFU": "crwdns2490784:0crwdne2490784:0", "app.components.screenReaderCurrency.XOF": "crwdns2490786:0crwdne2490786:0", "app.components.screenReaderCurrency.XPD": "crwdns2490788:0crwdne2490788:0", "app.components.screenReaderCurrency.XPF": "crwdns2490790:0crwdne2490790:0", "app.components.screenReaderCurrency.XPT": "crwdns2490792:0crwdne2490792:0", "app.components.screenReaderCurrency.XTS": "crwdns2490794:0crwdne2490794:0", "app.components.screenReaderCurrency.XXX": "crwdns2490796:0crwdne2490796:0", "app.components.screenReaderCurrency.YER": "crwdns2490798:0crwdne2490798:0", "app.components.screenReaderCurrency.ZAR": "crwdns2490800:0crwdne2490800:0", "app.components.screenReaderCurrency.ZMW": "crwdns2490802:0crwdne2490802:0", "app.components.screenReaderCurrency.amount": "crwdns2490804:0crwdne2490804:0", "app.components.screenReaderCurrency.currency": "crwdns2490806:0crwdne2490806:0", "app.components.trendIndicator.lastQuarter2": "crwdns4305668:0crwdne4305668:0", "app.containers.AccessibilityStatement.applicability": "crwdns214094:0{demoPlatformLink}crwdne214094:0", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "crwdns214096:0crwdne214096:0", "app.containers.AccessibilityStatement.assesmentText2022": "crwdns214098:0{demoPlatformLink}crwdnd214098:0{statusPageLink}crwdne214098:0", "app.containers.AccessibilityStatement.changePreferencesButtonText": "crwdns214100:0crwdne214100:0", "app.containers.AccessibilityStatement.changePreferencesText": "crwdns214102:0{changePreferencesButton}crwdne214102:0", "app.containers.AccessibilityStatement.conformanceExceptions": "crwdns214108:0crwdne214108:0", "app.containers.AccessibilityStatement.conformanceStatus": "crwdns214110:0crwdne214110:0", "app.containers.AccessibilityStatement.contentConformanceExceptions": "crwdns214112:0crwdne214112:0", "app.containers.AccessibilityStatement.demoPlatformLinkText": "crwdns214114:0crwdne214114:0", "app.containers.AccessibilityStatement.email": "crwdns214116:0crwdne214116:0", "app.containers.AccessibilityStatement.embeddedSurveyTools": "crwdns3597343:0crwdne3597343:0", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "crwdns3597345:0crwdne3597345:0", "app.containers.AccessibilityStatement.exception_1": "crwdns214118:0crwdne214118:0", "app.containers.AccessibilityStatement.feedbackProcessIntro": "crwdns214120:0crwdne214120:0", "app.containers.AccessibilityStatement.feedbackProcessTitle": "crwdns214122:0crwdne214122:0", "app.containers.AccessibilityStatement.govocalAddress2022": "crwdns2504050:0crwdne2504050:0", "app.containers.AccessibilityStatement.headTitle": "crwdns2747167:0{orgName}crwdne2747167:0", "app.containers.AccessibilityStatement.intro2022": "crwdns214124:0{goVocalLink}crwdne214124:0", "app.containers.AccessibilityStatement.mapping": "crwdns2999179:0crwdne2999179:0", "app.containers.AccessibilityStatement.mapping_1": "crwdns2999181:0crwdne2999181:0", "app.containers.AccessibilityStatement.mapping_2": "crwdns2999183:0crwdne2999183:0", "app.containers.AccessibilityStatement.mapping_3": "crwdns2999185:0crwdne2999185:0", "app.containers.AccessibilityStatement.mapping_4": "crwdns2999187:0crwdne2999187:0", "app.containers.AccessibilityStatement.onlineWorkshopsException": "crwdns214130:0crwdne214130:0", "app.containers.AccessibilityStatement.pageDescription": "crwdns214132:0crwdne214132:0", "app.containers.AccessibilityStatement.postalAddress": "crwdns214134:0crwdne214134:0", "app.containers.AccessibilityStatement.publicationDate": "crwdns214136:0crwdne214136:0", "app.containers.AccessibilityStatement.publicationDate2024": "crwdns2999189:0crwdne2999189:0", "app.containers.AccessibilityStatement.responsiveness": "crwdns214140:0crwdne214140:0", "app.containers.AccessibilityStatement.statusPageText": "crwdns214148:0crwdne214148:0", "app.containers.AccessibilityStatement.technologiesIntro": "crwdns214154:0crwdne214154:0", "app.containers.AccessibilityStatement.technologiesTitle": "crwdns214156:0crwdne214156:0", "app.containers.AccessibilityStatement.title": "crwdns214158:0crwdne214158:0", "app.containers.AccessibilityStatement.userGeneratedContent": "crwdns214160:0crwdne214160:0", "app.containers.AccessibilityStatement.workshops": "crwdns214162:0crwdne214162:0", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "crwdns1603860:0crwdne1603860:0", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "crwdns3106609:0crwdne3106609:0", "app.containers.AdminPage.ProjectDescription.linkText": "crwdns3106611:0crwdne3106611:0", "app.containers.AdminPage.ProjectDescription.saveError": "crwdns3106613:0crwdne3106613:0", "app.containers.AdminPage.ProjectDescription.toggleLabel": "crwdns3106615:0crwdne3106615:0", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "crwdns3106617:0crwdne3106617:0", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "crwdns3106621:0crwdne3106621:0", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "crwdns214168:0crwdne214168:0", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "crwdns214170:0crwdne214170:0", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "crwdns214172:0crwdne214172:0", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "crwdns214174:0crwdne214174:0", "app.containers.AdminPage.Users.UsersGroup.rulesError": "crwdns214178:0crwdne214178:0", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "crwdns214180:0crwdne214180:0", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "crwdns3799149:0crwdne3799149:0", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "crwdns214184:0crwdne214184:0", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "crwdns214186:0crwdne214186:0", "app.containers.App.appMetaDescription": "crwdns214188:0{orgName}crwdne214188:0", "app.containers.App.loading": "crwdns214190:0crwdne214190:0", "app.containers.App.metaTitle1": "crwdns2278724:0{orgName}crwdne2278724:0", "app.containers.App.skipLinkText": "crwdns2677521:0crwdne2677521:0", "app.containers.AreaTerms.areaTerm": "crwdns4268320:0crwdne4268320:0", "app.containers.AreaTerms.areasTerm": "crwdns4268322:0crwdne4268322:0", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "crwdns748861:0crwdne748861:0", "app.containers.Authentication.steps.AccessDenied.close": "crwdns2999237:0crwdne2999237:0", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "crwdns2999239:0crwdne2999239:0", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "crwdns2888827:0crwdne2888827:0", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "crwdns522977:0crwdne522977:0", "app.containers.Authentication.steps.Invitation.token": "crwdns522979:0crwdne522979:0", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "crwdns2888829:0{loginLink}crwdne2888829:0", "app.containers.Authentication.steps.SSOVerification.logIn": "crwdns2888831:0crwdne2888831:0", "app.containers.CampaignsConsentForm.ally_categoryLabel": "crwdns214196:0crwdne214196:0", "app.containers.CampaignsConsentForm.messageError": "crwdns214208:0crwdne214208:0", "app.containers.CampaignsConsentForm.messageSuccess": "crwdns214210:0crwdne214210:0", "app.containers.CampaignsConsentForm.notificationsSubTitle": "crwdns214212:0crwdne214212:0", "app.containers.CampaignsConsentForm.notificationsTitle": "crwdns214214:0crwdne214214:0", "app.containers.CampaignsConsentForm.submit": "crwdns214220:0crwdne214220:0", "app.containers.ChangeEmail.backToProfile": "crwdns522981:0crwdne522981:0", "app.containers.ChangeEmail.confirmationModalTitle": "crwdns522983:0crwdne522983:0", "app.containers.ChangeEmail.emailEmptyError": "crwdns522985:0crwdne522985:0", "app.containers.ChangeEmail.emailInvalidError": "crwdns522987:0crwdne522987:0", "app.containers.ChangeEmail.emailRequired": "crwdns522989:0crwdne522989:0", "app.containers.ChangeEmail.emailTaken": "crwdns522991:0crwdne522991:0", "app.containers.ChangeEmail.emailUpdateCancelled": "crwdns522993:0crwdne522993:0", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "crwdns522995:0crwdne522995:0", "app.containers.ChangeEmail.helmetDescription": "crwdns522997:0crwdne522997:0", "app.containers.ChangeEmail.helmetTitle": "crwdns522999:0crwdne522999:0", "app.containers.ChangeEmail.newEmailLabel": "crwdns523001:0crwdne523001:0", "app.containers.ChangeEmail.submitButton": "crwdns523003:0crwdne523003:0", "app.containers.ChangeEmail.titleAddEmail": "crwdns523005:0crwdne523005:0", "app.containers.ChangeEmail.titleChangeEmail": "crwdns523007:0crwdne523007:0", "app.containers.ChangeEmail.updateSuccessful": "crwdns523009:0crwdne523009:0", "app.containers.ChangePassword.currentPasswordLabel": "crwdns214226:0crwdne214226:0", "app.containers.ChangePassword.currentPasswordRequired": "crwdns214228:0crwdne214228:0", "app.containers.ChangePassword.goHome": "crwdns214230:0crwdne214230:0", "app.containers.ChangePassword.helmetDescription": "crwdns214232:0crwdne214232:0", "app.containers.ChangePassword.helmetTitle": "crwdns214234:0crwdne214234:0", "app.containers.ChangePassword.newPasswordLabel": "crwdns214236:0crwdne214236:0", "app.containers.ChangePassword.newPasswordRequired": "crwdns214238:0crwdne214238:0", "app.containers.ChangePassword.password.minimumPasswordLengthError": "crwdns214240:0{minimumPasswordLength}crwdne214240:0", "app.containers.ChangePassword.passwordChangeSuccessMessage": "crwdns214242:0crwdne214242:0", "app.containers.ChangePassword.passwordEmptyError": "crwdns214244:0crwdne214244:0", "app.containers.ChangePassword.passwordsDontMatch": "crwdns214246:0crwdne214246:0", "app.containers.ChangePassword.titleAddPassword": "crwdns523011:0crwdne523011:0", "app.containers.ChangePassword.titleChangePassword": "crwdns523013:0crwdne523013:0", "app.containers.Comments.a11y_commentDeleted": "crwdns214250:0crwdne214250:0", "app.containers.Comments.a11y_commentPosted": "crwdns214252:0crwdne214252:0", "app.containers.Comments.a11y_likeCount": "crwdns777627:0likeCount={likeCount}crwdne777627:0", "app.containers.Comments.a11y_undoLike": "crwdns777629:0crwdne777629:0", "app.containers.Comments.addCommentError": "crwdns214258:0crwdne214258:0", "app.containers.Comments.adminCommentDeletionCancelButton": "crwdns214260:0crwdne214260:0", "app.containers.Comments.adminCommentDeletionConfirmButton": "crwdns214262:0crwdne214262:0", "app.containers.Comments.cancelCommentEdit": "crwdns214264:0crwdne214264:0", "app.containers.Comments.childCommentBodyPlaceholder": "crwdns214266:0crwdne214266:0", "app.containers.Comments.commentCancelUpvote": "crwdns214268:0crwdne214268:0", "app.containers.Comments.commentDeletedPlaceholder": "crwdns214270:0crwdne214270:0", "app.containers.Comments.commentDeletionCancelButton": "crwdns214272:0crwdne214272:0", "app.containers.Comments.commentDeletionConfirmButton": "crwdns214274:0crwdne214274:0", "app.containers.Comments.commentLike": "crwdns777631:0crwdne777631:0", "app.containers.Comments.commentReplyButton": "crwdns214276:0crwdne214276:0", "app.containers.Comments.commentsSortTitle": "crwdns214280:0crwdne214280:0", "app.containers.Comments.completeProfileLinkText": "crwdns523015:0crwdne523015:0", "app.containers.Comments.completeProfileToComment": "crwdns523017:0{completeRegistrationLink}crwdne523017:0", "app.containers.Comments.confirmCommentDeletion": "crwdns214282:0crwdne214282:0", "app.containers.Comments.deleteComment": "crwdns214284:0crwdne214284:0", "app.containers.Comments.deleteReasonDescriptionError": "crwdns214286:0crwdne214286:0", "app.containers.Comments.deleteReasonError": "crwdns214288:0crwdne214288:0", "app.containers.Comments.deleteReason_inappropriate": "crwdns214290:0crwdne214290:0", "app.containers.Comments.deleteReason_irrelevant": "crwdns214292:0crwdne214292:0", "app.containers.Comments.deleteReason_other": "crwdns214294:0crwdne214294:0", "app.containers.Comments.editComment": "crwdns214296:0crwdne214296:0", "app.containers.Comments.guidelinesLinkText": "crwdns214298:0crwdne214298:0", "app.containers.Comments.ideaCommentBodyPlaceholder": "crwdns214300:0crwdne214300:0", "app.containers.Comments.internalCommentingNudgeMessage": "crwdns3645129:0crwdne3645129:0", "app.containers.Comments.internalConversation": "crwdns748829:0crwdne748829:0", "app.containers.Comments.loadMoreComments": "crwdns214304:0crwdne214304:0", "app.containers.Comments.loadingComments": "crwdns214306:0crwdne214306:0", "app.containers.Comments.loadingMoreComments": "crwdns214308:0crwdne214308:0", "app.containers.Comments.notVisibleToUsersPlaceholder": "crwdns748831:0crwdne748831:0", "app.containers.Comments.postInternalComment": "crwdns748833:0crwdne748833:0", "app.containers.Comments.postPublicComment": "crwdns748835:0crwdne748835:0", "app.containers.Comments.profanityError": "crwdns214310:0{guidelinesLink}crwdne214310:0", "app.containers.Comments.publicDiscussion": "crwdns748837:0crwdne748837:0", "app.containers.Comments.publishComment": "crwdns214312:0crwdne214312:0", "app.containers.Comments.reportAsSpamModalTitle": "crwdns214314:0crwdne214314:0", "app.containers.Comments.saveComment": "crwdns214316:0crwdne214316:0", "app.containers.Comments.signInLinkText": "crwdns214320:0crwdne214320:0", "app.containers.Comments.signInToComment": "crwdns214322:0{signInLink}crwdne214322:0", "app.containers.Comments.signUpLinkText": "crwdns214326:0crwdne214326:0", "app.containers.Comments.verifyIdentityLinkText": "crwdns214328:0crwdne214328:0", "app.containers.Comments.visibleToUsersPlaceholder": "crwdns748839:0crwdne748839:0", "app.containers.Comments.visibleToUsersWarning": "crwdns748841:0crwdne748841:0", "app.containers.ContentBuilder.PageTitle": "crwdns3106623:0crwdne3106623:0", "app.containers.CookiePolicy.advertisingContent": "crwdns214330:0crwdne214330:0", "app.containers.CookiePolicy.advertisingTitle": "crwdns214332:0crwdne214332:0", "app.containers.CookiePolicy.analyticsContents": "crwdns214334:0{orgName}crwdne214334:0", "app.containers.CookiePolicy.analyticsTitle": "crwdns214336:0crwdne214336:0", "app.containers.CookiePolicy.cookiePolicyDescription": "crwdns214338:0crwdne214338:0", "app.containers.CookiePolicy.cookiePolicyTitle": "crwdns214340:0crwdne214340:0", "app.containers.CookiePolicy.essentialContent": "crwdns214342:0crwdne214342:0", "app.containers.CookiePolicy.essentialTitle": "crwdns214344:0crwdne214344:0", "app.containers.CookiePolicy.externalContent": "crwdns214346:0crwdne214346:0", "app.containers.CookiePolicy.externalTitle": "crwdns214348:0crwdne214348:0", "app.containers.CookiePolicy.functionalContents": "crwdns214350:0crwdne214350:0", "app.containers.CookiePolicy.functionalTitle": "crwdns214352:0crwdne214352:0", "app.containers.CookiePolicy.headCookiePolicyTitle": "crwdns2747169:0{orgName}crwdne2747169:0", "app.containers.CookiePolicy.intro": "crwdns214354:0crwdne214354:0", "app.containers.CookiePolicy.manageCookiesDescription": "crwdns214356:0crwdne214356:0", "app.containers.CookiePolicy.manageCookiesPreferences": "crwdns214358:0{manageCookiesPreferencesButtonText}crwdne214358:0", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "crwdns214360:0crwdne214360:0", "app.containers.CookiePolicy.manageCookiesTitle": "crwdns214362:0crwdne214362:0", "app.containers.CookiePolicy.viewPreferencesButtonText": "crwdns214364:0crwdne214364:0", "app.containers.CookiePolicy.viewPreferencesText": "crwdns214366:0{viewPreferencesButton}crwdne214366:0", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "crwdns214368:0crwdne214368:0", "app.containers.CustomPageShow.editPage": "crwdns214370:0crwdne214370:0", "app.containers.CustomPageShow.goBack": "crwdns214372:0crwdne214372:0", "app.containers.CustomPageShow.notFound": "crwdns214374:0crwdne214374:0", "app.containers.DisabledAccount.bottomText": "crwdns417944:0{date}crwdne417944:0", "app.containers.DisabledAccount.termsAndConditions": "crwdns417946:0crwdne417946:0", "app.containers.DisabledAccount.text2": "crwdns417948:0{orgName}crwdnd417948:0{TermsAndConditions}crwdne417948:0", "app.containers.DisabledAccount.title": "crwdns417950:0crwdne417950:0", "app.containers.EventsShow.addToCalendar": "crwdns1032353:0crwdne1032353:0", "app.containers.EventsShow.editEvent": "crwdns1081504:0crwdne1081504:0", "app.containers.EventsShow.emailSharingBody2": "crwdns2677509:0{eventTitle}crwdnd2677509:0{eventUrl}crwdne2677509:0", "app.containers.EventsShow.eventDateTimeIcon": "crwdns2136236:0crwdne2136236:0", "app.containers.EventsShow.eventFrom2": "crwdns1336766:0{projectTitle}crwdne1336766:0", "app.containers.EventsShow.goBack": "crwdns953643:0crwdne953643:0", "app.containers.EventsShow.goToProject": "crwdns953645:0crwdne953645:0", "app.containers.EventsShow.haveRegistered": "crwdns4902953:0crwdne4902953:0", "app.containers.EventsShow.icsError": "crwdns1032355:0crwdne1032355:0", "app.containers.EventsShow.linkToOnlineEvent": "crwdns1032373:0crwdne1032373:0", "app.containers.EventsShow.locationIconAltText": "crwdns2136238:0crwdne2136238:0", "app.containers.EventsShow.metaTitle": "crwdns4773501:0{eventTitle}crwdnd4773501:0{orgName}crwdne4773501:0", "app.containers.EventsShow.online2": "crwdns1032375:0crwdne1032375:0", "app.containers.EventsShow.onlineLinkIconAltText": "crwdns2136240:0crwdne2136240:0", "app.containers.EventsShow.registered": "crwdns4902955:0crwdne4902955:0", "app.containers.EventsShow.registrantCount": "crwdns4902957:0attendeesCount={attendeesCount}crwdne4902957:0", "app.containers.EventsShow.registrantCountWithMaximum": "crwdns4902959:0{attendeesCount}crwdnd4902959:0{maximumNumberOfAttendees}crwdne4902959:0", "app.containers.EventsShow.registrantsIconAltText": "crwdns4902961:0crwdne4902961:0", "app.containers.EventsShow.socialMediaSharingMessage": "crwdns826049:0{eventTitle}crwdne826049:0", "app.containers.EventsShow.xParticipants": "crwdns953647:0count={count}crwdne953647:0", "app.containers.EventsViewer.allTime": "crwdns1032411:0crwdne1032411:0", "app.containers.EventsViewer.date": "crwdns1032413:0crwdne1032413:0", "app.containers.EventsViewer.thisMonth2": "crwdns1032415:0crwdne1032415:0", "app.containers.EventsViewer.thisWeek2": "crwdns1032417:0crwdne1032417:0", "app.containers.EventsViewer.today": "crwdns1032419:0crwdne1032419:0", "app.containers.IdeaButton.addAContribution": "crwdns214376:0crwdne214376:0", "app.containers.IdeaButton.addAPetition": "crwdns3335111:0crwdne3335111:0", "app.containers.IdeaButton.addAProject": "crwdns214378:0crwdne214378:0", "app.containers.IdeaButton.addAProposal": "crwdns3335113:0crwdne3335113:0", "app.containers.IdeaButton.addAQuestion": "crwdns214380:0crwdne214380:0", "app.containers.IdeaButton.addAnInitiative": "crwdns3335115:0crwdne3335115:0", "app.containers.IdeaButton.addAnOption": "crwdns214382:0crwdne214382:0", "app.containers.IdeaButton.postingDisabled": "crwdns214384:0crwdne214384:0", "app.containers.IdeaButton.postingInNonActivePhases": "crwdns214386:0crwdne214386:0", "app.containers.IdeaButton.postingInactive": "crwdns214388:0crwdne214388:0", "app.containers.IdeaButton.postingLimitedMaxReached": "crwdns214390:0crwdne214390:0", "app.containers.IdeaButton.postingNoPermission": "crwdns214394:0crwdne214394:0", "app.containers.IdeaButton.postingNotYetPossible": "crwdns214396:0crwdne214396:0", "app.containers.IdeaButton.signInLinkText": "crwdns214398:0crwdne214398:0", "app.containers.IdeaButton.signUpLinkText": "crwdns214400:0crwdne214400:0", "app.containers.IdeaButton.submitAnIssue": "crwdns214402:0crwdne214402:0", "app.containers.IdeaButton.submitYourIdea": "crwdns214404:0crwdne214404:0", "app.containers.IdeaButton.takeTheSurvey": "crwdns214406:0crwdne214406:0", "app.containers.IdeaButton.verificationLinkText": "crwdns214408:0crwdne214408:0", "app.containers.IdeaCard.readMore": "crwdns777633:0crwdne777633:0", "app.containers.IdeaCard.xComments": "crwdns214410:0commentsCount={commentsCount}crwdne214410:0", "app.containers.IdeaCard.xVotesOfY": "crwdns2735461:0xVotes={xVotes}crwdnd2735461:0votingThreshold={votingThreshold}crwdne2735461:0", "app.containers.IdeaCards.a11y_closeFilterPanel": "crwdns3716681:0crwdne3716681:0", "app.containers.IdeaCards.a11y_totalItems": "crwdns214414:0{ideasCount}crwdne214414:0", "app.containers.IdeaCards.all": "crwdns214416:0crwdne214416:0", "app.containers.IdeaCards.allStatuses": "crwdns3335117:0crwdne3335117:0", "app.containers.IdeaCards.contributions": "crwdns3716683:0crwdne3716683:0", "app.containers.IdeaCards.ideaTerm": "crwdns3716685:0crwdne3716685:0", "app.containers.IdeaCards.initiatives": "crwdns3716689:0crwdne3716689:0", "app.containers.IdeaCards.issueTerm": "crwdns3716691:0crwdne3716691:0", "app.containers.IdeaCards.list": "crwdns214420:0crwdne214420:0", "app.containers.IdeaCards.map": "crwdns214422:0crwdne214422:0", "app.containers.IdeaCards.mostDiscussed": "crwdns3645015:0crwdne3645015:0", "app.containers.IdeaCards.newest": "crwdns214424:0crwdne214424:0", "app.containers.IdeaCards.noFilteredResults": "crwdns214426:0crwdne214426:0", "app.containers.IdeaCards.numberResults": "crwdns3716693:0{postCount}crwdne3716693:0", "app.containers.IdeaCards.oldest": "crwdns214428:0crwdne214428:0", "app.containers.IdeaCards.optionTerm": "crwdns3716695:0crwdne3716695:0", "app.containers.IdeaCards.petitions": "crwdns3716697:0crwdne3716697:0", "app.containers.IdeaCards.popular": "crwdns214430:0crwdne214430:0", "app.containers.IdeaCards.projectFilterTitle": "crwdns214432:0crwdne214432:0", "app.containers.IdeaCards.projectTerm": "crwdns3716699:0crwdne3716699:0", "app.containers.IdeaCards.proposals": "crwdns3716701:0crwdne3716701:0", "app.containers.IdeaCards.questionTerm": "crwdns3716703:0crwdne3716703:0", "app.containers.IdeaCards.random": "crwdns214434:0crwdne214434:0", "app.containers.IdeaCards.resetFilters": "crwdns214436:0crwdne214436:0", "app.containers.IdeaCards.showXResults": "crwdns214440:0ideasCount={ideasCount}crwdne214440:0", "app.containers.IdeaCards.sortTitle": "crwdns214442:0crwdne214442:0", "app.containers.IdeaCards.statusTitle": "crwdns214444:0crwdne214444:0", "app.containers.IdeaCards.statusesTitle": "crwdns3335119:0crwdne3335119:0", "app.containers.IdeaCards.topics": "crwdns214446:0crwdne214446:0", "app.containers.IdeaCards.topicsTitle": "crwdns214448:0crwdne214448:0", "app.containers.IdeaCards.trending": "crwdns214450:0crwdne214450:0", "app.containers.IdeaCards.tryDifferentFilters": "crwdns214452:0crwdne214452:0", "app.containers.IdeaCards.xComments3": "crwdns3763283:0{ideasCount}crwdnd3763283:0{ideasCount}crwdnd3763283:0{ideasCount}crwdne3763283:0", "app.containers.IdeaCards.xContributions2": "crwdns3763285:0{ideasCount}crwdnd3763285:0{ideasCount}crwdnd3763285:0{ideasCount}crwdne3763285:0", "app.containers.IdeaCards.xIdeas2": "crwdns3763287:0{ideasCount}crwdnd3763287:0{ideasCount}crwdnd3763287:0{ideasCount}crwdne3763287:0", "app.containers.IdeaCards.xInitiatives2": "crwdns3763289:0{ideasCount}crwdnd3763289:0{ideasCount}crwdnd3763289:0{ideasCount}crwdne3763289:0", "app.containers.IdeaCards.xOptions2": "crwdns3763291:0{ideasCount}crwdnd3763291:0{ideasCount}crwdnd3763291:0{ideasCount}crwdne3763291:0", "app.containers.IdeaCards.xPetitions2": "crwdns3763293:0{ideasCount}crwdnd3763293:0{ideasCount}crwdnd3763293:0{ideasCount}crwdne3763293:0", "app.containers.IdeaCards.xProjects3": "crwdns3763295:0{ideasCount}crwdnd3763295:0{ideasCount}crwdnd3763295:0{ideasCount}crwdne3763295:0", "app.containers.IdeaCards.xProposals2": "crwdns3763297:0{ideasCount}crwdnd3763297:0{ideasCount}crwdnd3763297:0{ideasCount}crwdne3763297:0", "app.containers.IdeaCards.xQuestion2": "crwdns3763299:0{ideasCount}crwdnd3763299:0{ideasCount}crwdnd3763299:0{ideasCount}crwdne3763299:0", "app.containers.IdeaCards.xResults": "crwdns214454:0ideasCount={ideasCount}crwdne214454:0", "app.containers.IdeasEditPage.contributionFormTitle": "crwdns214456:0crwdne214456:0", "app.containers.IdeasEditPage.editedPostSave": "crwdns214458:0crwdne214458:0", "app.containers.IdeasEditPage.fileUploadError": "crwdns214460:0crwdne214460:0", "app.containers.IdeasEditPage.formTitle": "crwdns214462:0crwdne214462:0", "app.containers.IdeasEditPage.ideasEditMetaDescription": "crwdns214464:0crwdne214464:0", "app.containers.IdeasEditPage.ideasEditMetaTitle": "crwdns214466:0{postTitle}crwdnd214466:0{projectName}crwdne214466:0", "app.containers.IdeasEditPage.initiativeFormTitle": "crwdns3335121:0crwdne3335121:0", "app.containers.IdeasEditPage.issueFormTitle": "crwdns214468:0crwdne214468:0", "app.containers.IdeasEditPage.optionFormTitle": "crwdns214470:0crwdne214470:0", "app.containers.IdeasEditPage.petitionFormTitle": "crwdns3335123:0crwdne3335123:0", "app.containers.IdeasEditPage.projectFormTitle": "crwdns214472:0crwdne214472:0", "app.containers.IdeasEditPage.proposalFormTitle": "crwdns3335125:0crwdne3335125:0", "app.containers.IdeasEditPage.questionFormTitle": "crwdns214474:0crwdne214474:0", "app.containers.IdeasEditPage.save": "crwdns214476:0crwdne214476:0", "app.containers.IdeasEditPage.submitApiError": "crwdns214478:0crwdne214478:0", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "crwdns2278726:0crwdne2278726:0", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "crwdns214482:0{orgName}crwdne214482:0", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "crwdns2278728:0{orgName}crwdne2278728:0", "app.containers.IdeasIndexPage.inputsPageTitle": "crwdns214486:0crwdne214486:0", "app.containers.IdeasIndexPage.loadMore": "crwdns214488:0crwdne214488:0", "app.containers.IdeasIndexPage.loading": "crwdns214490:0crwdne214490:0", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "crwdns604469:0crwdne604469:0", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "crwdns604471:0crwdne604471:0", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "crwdns604473:0crwdne604473:0", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "crwdns1919660:0crwdne1919660:0", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "crwdns1919662:0crwdne1919662:0", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "crwdns1326154:0crwdne1326154:0", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "crwdns1326156:0crwdne1326156:0", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "crwdns1326158:0crwdne1326158:0", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "crwdns1326160:0crwdne1326160:0", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "crwdns214492:0{limit}crwdne214492:0", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "crwdns214494:0{limit}crwdne214494:0", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "crwdns214496:0{limit}crwdne214496:0", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "crwdns400334:0{limit}crwdne400334:0", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "crwdns3238307:0crwdne3238307:0", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "crwdns214500:0{limit}crwdne214500:0", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "crwdns214502:0{limit}crwdne214502:0", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "crwdns214504:0crwdne214504:0", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "crwdns400336:0{limit}crwdne400336:0", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "crwdns214508:0{limit}crwdne214508:0", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "crwdns3335127:0{limit}crwdne3335127:0", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "crwdns3335129:0{limit}crwdne3335129:0", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "crwdns3335131:0{limit}crwdne3335131:0", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "crwdns3335133:0{limit}crwdne3335133:0", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "crwdns214510:0{limit}crwdne214510:0", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "crwdns214512:0{limit}crwdne214512:0", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "crwdns214514:0{limit}crwdne214514:0", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "crwdns2502690:0{limit}crwdne2502690:0", "app.containers.IdeasNewPage.ajv_error_number_required": "crwdns1782200:0crwdne1782200:0", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "crwdns214518:0{limit}crwdne214518:0", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "crwdns2502692:0{limit}crwdne2502692:0", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "crwdns214522:0{limit}crwdne214522:0", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "crwdns400340:0{limit}crwdne400340:0", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "crwdns214526:0crwdne214526:0", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "crwdns3335135:0{limit}crwdne3335135:0", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "crwdns3335137:0{limit}crwdne3335137:0", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "crwdns3335139:0{limit}crwdne3335139:0", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "crwdns3335141:0{limit}crwdne3335141:0", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "crwdns214528:0{limit}crwdne214528:0", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "crwdns214530:0{limit}crwdne214530:0", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "crwdns214532:0{limit}crwdne214532:0", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "crwdns400342:0{limit}crwdne400342:0", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "crwdns3335143:0{limit}crwdne3335143:0", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "crwdns3335145:0{limit}crwdne3335145:0", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "crwdns3335147:0{limit}crwdne3335147:0", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "crwdns3335149:0{limit}crwdne3335149:0", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "crwdns214536:0crwdne214536:0", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "crwdns214538:0crwdne214538:0", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "crwdns214540:0{limit}crwdne214540:0", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "crwdns214542:0{limit}crwdne214542:0", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "crwdns214544:0{limit}crwdne214544:0", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "crwdns400344:0{limit}crwdne400344:0", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "crwdns214548:0crwdne214548:0", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "crwdns214550:0crwdne214550:0", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "crwdns214552:0crwdne214552:0", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "crwdns214554:0crwdne214554:0", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "crwdns214556:0crwdne214556:0", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "crwdns214558:0crwdne214558:0", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "crwdns214560:0crwdne214560:0", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "crwdns214562:0crwdne214562:0", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "crwdns214564:0crwdne214564:0", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "crwdns214566:0crwdne214566:0", "app.containers.IdeasNewPage.api_error_includes_banned_words": "crwdns214568:0{guidelinesLink}crwdne214568:0", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "crwdns3335151:0crwdne3335151:0", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "crwdns3335153:0crwdne3335153:0", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "crwdns3335155:0crwdne3335155:0", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "crwdns3335157:0crwdne3335157:0", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "crwdns214570:0crwdne214570:0", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "crwdns214572:0crwdne214572:0", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "crwdns214574:0crwdne214574:0", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "crwdns214576:0crwdne214576:0", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "crwdns214578:0crwdne214578:0", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "crwdns214580:0crwdne214580:0", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "crwdns214582:0crwdne214582:0", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "crwdns214584:0crwdne214584:0", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "crwdns3335159:0crwdne3335159:0", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "crwdns3335161:0crwdne3335161:0", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "crwdns3335163:0crwdne3335163:0", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "crwdns3335165:0crwdne3335165:0", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "crwdns214586:0crwdne214586:0", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "crwdns214588:0crwdne214588:0", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "crwdns214590:0crwdne214590:0", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "crwdns214592:0crwdne214592:0", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "crwdns3335167:0crwdne3335167:0", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "crwdns3335169:0crwdne3335169:0", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "crwdns3335171:0crwdne3335171:0", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "crwdns3335173:0crwdne3335173:0", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "crwdns214594:0crwdne214594:0", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "crwdns214596:0crwdne214596:0", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "crwdns214598:0crwdne214598:0", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "crwdns214600:0crwdne214600:0", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "crwdns214602:0crwdne214602:0", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "crwdns214604:0crwdne214604:0", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "crwdns1963118:0crwdne1963118:0", "app.containers.IdeasNewPage.contributionMetaTitle1": "crwdns2278730:0{orgName}crwdne2278730:0", "app.containers.IdeasNewPage.editSurvey": "crwdns214612:0crwdne214612:0", "app.containers.IdeasNewPage.ideaNewMetaDescription": "crwdns214620:0{orgName}crwdne214620:0", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "crwdns2278732:0{orgName}crwdne2278732:0", "app.containers.IdeasNewPage.initiativeMetaTitle1": "crwdns3335175:0{orgName}crwdne3335175:0", "app.containers.IdeasNewPage.issueMetaTitle1": "crwdns2278734:0{orgName}crwdne2278734:0", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "crwdns1963120:0crwdne1963120:0", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "crwdns1963122:0crwdne1963122:0", "app.containers.IdeasNewPage.leaveSurvey": "crwdns1963124:0crwdne1963124:0", "app.containers.IdeasNewPage.leaveSurveyText": "crwdns214630:0crwdne214630:0", "app.containers.IdeasNewPage.optionMetaTitle1": "crwdns2278736:0{orgName}crwdne2278736:0", "app.containers.IdeasNewPage.petitionMetaTitle1": "crwdns3335177:0{orgName}crwdne3335177:0", "app.containers.IdeasNewPage.projectMetaTitle1": "crwdns2278738:0{orgName}crwdne2278738:0", "app.containers.IdeasNewPage.proposalMetaTitle1": "crwdns3335179:0{orgName}crwdne3335179:0", "app.containers.IdeasNewPage.questionMetaTitle1": "crwdns2278740:0{orgName}crwdne2278740:0", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "crwdns3420703:0{surveyTitle}crwdnd3420703:0{orgName}crwdne3420703:0", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "crwdns3238309:0crwdne3238309:0", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "crwdns3238311:0crwdne3238311:0", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "crwdns3238313:0crwdne3238313:0", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "crwdns3238315:0crwdne3238315:0", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "crwdns3238317:0crwdne3238317:0", "app.containers.IdeasShow.Cosponsorship.pending": "crwdns3238319:0crwdne3238319:0", "app.containers.IdeasShow.MetaInformation.attachments": "crwdns214654:0crwdne214654:0", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "crwdns214656:0{userName}crwdnd214656:0{date}crwdne214656:0", "app.containers.IdeasShow.MetaInformation.currentStatus": "crwdns214658:0crwdne214658:0", "app.containers.IdeasShow.MetaInformation.location": "crwdns214660:0crwdne214660:0", "app.containers.IdeasShow.MetaInformation.postedBy": "crwdns214662:0crwdne214662:0", "app.containers.IdeasShow.MetaInformation.similar": "crwdns3751127:0crwdne3751127:0", "app.containers.IdeasShow.MetaInformation.topics": "crwdns214666:0crwdne214666:0", "app.containers.IdeasShow.commentCTA": "crwdns214668:0crwdne214668:0", "app.containers.IdeasShow.contributionEmailSharingBody": "crwdns214670:0{postTitle}crwdnd214670:0{postUrl}crwdne214670:0", "app.containers.IdeasShow.contributionEmailSharingSubject": "crwdns214672:0{postTitle}crwdne214672:0", "app.containers.IdeasShow.contributionSharingModalTitle": "crwdns214676:0crwdne214676:0", "app.containers.IdeasShow.contributionTwitterMessage": "crwdns214678:0{postTitle}crwdne214678:0", "app.containers.IdeasShow.contributionWhatsAppMessage": "crwdns214680:0{postTitle}crwdne214680:0", "app.containers.IdeasShow.currentStatus": "crwdns214682:0crwdne214682:0", "app.containers.IdeasShow.deletedUser": "crwdns214684:0crwdne214684:0", "app.containers.IdeasShow.ideaEmailSharingBody": "crwdns214686:0{ideaTitle}crwdnd214686:0{ideaUrl}crwdne214686:0", "app.containers.IdeasShow.ideaEmailSharingSubject": "crwdns214688:0{ideaTitle}crwdne214688:0", "app.containers.IdeasShow.ideaTwitterMessage": "crwdns214692:0{postTitle}crwdne214692:0", "app.containers.IdeasShow.ideaWhatsAppMessage": "crwdns214694:0{postTitle}crwdne214694:0", "app.containers.IdeasShow.ideasWhatsAppMessage": "crwdns214696:0{postTitle}crwdne214696:0", "app.containers.IdeasShow.imported": "crwdns1159988:0crwdne1159988:0", "app.containers.IdeasShow.importedTooltip": "crwdns1159990:0{inputTerm}crwdne1159990:0", "app.containers.IdeasShow.initiativeEmailSharingBody": "crwdns3335181:0{ideaTitle}crwdnd3335181:0{ideaUrl}crwdne3335181:0", "app.containers.IdeasShow.initiativeEmailSharingSubject": "crwdns3335183:0{ideaTitle}crwdne3335183:0", "app.containers.IdeasShow.initiativeSharingModalTitle": "crwdns3335185:0crwdne3335185:0", "app.containers.IdeasShow.initiativeTwitterMessage": "crwdns3335187:0{postTitle}crwdne3335187:0", "app.containers.IdeasShow.initiativeWhatsAppMessage": "crwdns3335189:0{postTitle}crwdne3335189:0", "app.containers.IdeasShow.issueEmailSharingBody": "crwdns214698:0{postTitle}crwdnd214698:0{postUrl}crwdne214698:0", "app.containers.IdeasShow.issueEmailSharingSubject": "crwdns214700:0{postTitle}crwdne214700:0", "app.containers.IdeasShow.issueSharingModalTitle": "crwdns214704:0crwdne214704:0", "app.containers.IdeasShow.issueTwitterMessage": "crwdns214706:0{postTitle}crwdne214706:0", "app.containers.IdeasShow.metaTitle": "crwdns2278744:0{inputTitle}crwdnd2278744:0{orgName}crwdne2278744:0", "app.containers.IdeasShow.optionEmailSharingBody": "crwdns214708:0{postTitle}crwdnd214708:0{postUrl}crwdne214708:0", "app.containers.IdeasShow.optionEmailSharingSubject": "crwdns214710:0{postTitle}crwdne214710:0", "app.containers.IdeasShow.optionSharingModalTitle": "crwdns214714:0crwdne214714:0", "app.containers.IdeasShow.optionTwitterMessage": "crwdns214716:0{postTitle}crwdne214716:0", "app.containers.IdeasShow.optionWhatsAppMessage": "crwdns214718:0{postTitle}crwdne214718:0", "app.containers.IdeasShow.petitionEmailSharingBody": "crwdns3335191:0{ideaTitle}crwdnd3335191:0{ideaUrl}crwdne3335191:0", "app.containers.IdeasShow.petitionEmailSharingSubject": "crwdns3335193:0{ideaTitle}crwdne3335193:0", "app.containers.IdeasShow.petitionSharingModalTitle": "crwdns3335195:0crwdne3335195:0", "app.containers.IdeasShow.petitionTwitterMessage": "crwdns3335197:0{postTitle}crwdne3335197:0", "app.containers.IdeasShow.petitionWhatsAppMessage": "crwdns3335199:0{postTitle}crwdne3335199:0", "app.containers.IdeasShow.projectEmailSharingBody": "crwdns214720:0{postTitle}crwdnd214720:0{postUrl}crwdne214720:0", "app.containers.IdeasShow.projectEmailSharingSubject": "crwdns214722:0{postTitle}crwdne214722:0", "app.containers.IdeasShow.projectSharingModalTitle": "crwdns214726:0crwdne214726:0", "app.containers.IdeasShow.projectTwitterMessage": "crwdns214728:0{postTitle}crwdne214728:0", "app.containers.IdeasShow.projectWhatsAppMessage": "crwdns214730:0{postTitle}crwdne214730:0", "app.containers.IdeasShow.proposalEmailSharingBody": "crwdns3335201:0{ideaTitle}crwdnd3335201:0{ideaUrl}crwdne3335201:0", "app.containers.IdeasShow.proposalEmailSharingSubject": "crwdns3335203:0{ideaTitle}crwdne3335203:0", "app.containers.IdeasShow.proposalSharingModalTitle": "crwdns3335205:0crwdne3335205:0", "app.containers.IdeasShow.proposalTwitterMessage": "crwdns3335207:0{postTitle}crwdne3335207:0", "app.containers.IdeasShow.proposalWhatsAppMessage": "crwdns3335209:0{postTitle}crwdne3335209:0", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "crwdns2747105:0crwdne2747105:0", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "crwdns2747107:0{xVotes}crwdnd2747107:0{votingThreshold}crwdne2747107:0", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "crwdns2747113:0crwdne2747113:0", "app.containers.IdeasShow.proposals.VoteControl.days": "crwdns2747115:0crwdne2747115:0", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "crwdns2747121:0crwdne2747121:0", "app.containers.IdeasShow.proposals.VoteControl.hours": "crwdns2747123:0crwdne2747123:0", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "crwdns2747129:0crwdne2747129:0", "app.containers.IdeasShow.proposals.VoteControl.minutes": "crwdns2747131:0crwdne2747131:0", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "crwdns2747133:0crwdne2747133:0", "app.containers.IdeasShow.proposals.VoteControl.vote": "crwdns2747149:0crwdne2747149:0", "app.containers.IdeasShow.proposals.VoteControl.voted": "crwdns2747151:0crwdne2747151:0", "app.containers.IdeasShow.proposals.VoteControl.votedText": "crwdns2747153:0x={x}crwdnd2747153:0xDays={xDays}crwdnd2747153:0xDays={xDays}crwdnd2747153:0xDays={xDays}crwdne2747153:0", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "crwdns2747155:0crwdne2747155:0", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "crwdns2747157:0{link}crwdne2747157:0", "app.containers.IdeasShow.proposals.VoteControl.xDays": "crwdns2747159:0x={x}crwdne2747159:0", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "crwdns2747161:0count={count}crwdne2747161:0", "app.containers.IdeasShow.questionEmailSharingBody": "crwdns214732:0{postTitle}crwdnd214732:0{postUrl}crwdne214732:0", "app.containers.IdeasShow.questionEmailSharingSubject": "crwdns214734:0{postTitle}crwdne214734:0", "app.containers.IdeasShow.questionSharingModalTitle": "crwdns214738:0crwdne214738:0", "app.containers.IdeasShow.questionTwitterMessage": "crwdns214740:0{postTitle}crwdne214740:0", "app.containers.IdeasShow.questionWhatsAppMessage": "crwdns214742:0{postTitle}crwdne214742:0", "app.containers.IdeasShow.reportAsSpamModalTitle": "crwdns214744:0crwdne214744:0", "app.containers.IdeasShow.share": "crwdns214746:0crwdne214746:0", "app.containers.IdeasShow.sharingModalSubtitle": "crwdns214748:0crwdne214748:0", "app.containers.IdeasShow.sharingModalTitle": "crwdns214750:0crwdne214750:0", "app.containers.Navbar.completeOnboarding": "crwdns1081594:0crwdne1081594:0", "app.containers.Navbar.completeProfile": "crwdns523019:0crwdne523019:0", "app.containers.Navbar.confirmEmail2": "crwdns1379664:0crwdne1379664:0", "app.containers.Navbar.unverified": "crwdns214906:0crwdne214906:0", "app.containers.Navbar.verified": "crwdns214908:0crwdne214908:0", "app.containers.NewAuthModal.beforeYouFollow": "crwdns855787:0crwdne855787:0", "app.containers.NewAuthModal.beforeYouParticipate": "crwdns523023:0crwdne523023:0", "app.containers.NewAuthModal.completeYourProfile": "crwdns523025:0crwdne523025:0", "app.containers.NewAuthModal.confirmYourEmail": "crwdns523027:0crwdne523027:0", "app.containers.NewAuthModal.logIn": "crwdns523029:0crwdne523029:0", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "crwdns523031:0crwdne523031:0", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "crwdns523033:0crwdne523033:0", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "crwdns523041:0crwdne523041:0", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "crwdns523043:0{goToOtherFlowLink}crwdne523043:0", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "crwdns523045:0crwdne523045:0", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "crwdns523047:0crwdne523047:0", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "crwdns523049:0crwdne523049:0", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "crwdns523051:0crwdne523051:0", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "crwdns523053:0crwdne523053:0", "app.containers.NewAuthModal.steps.EmailSignUp.email": "crwdns523055:0crwdne523055:0", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "crwdns523057:0crwdne523057:0", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "crwdns523059:0crwdne523059:0", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "crwdns523061:0crwdne523061:0", "app.containers.NewAuthModal.steps.Password.forgotPassword": "crwdns523063:0crwdne523063:0", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "crwdns523065:0{account}crwdne523065:0", "app.containers.NewAuthModal.steps.Password.noPasswordError": "crwdns523067:0crwdne523067:0", "app.containers.NewAuthModal.steps.Password.password": "crwdns523069:0crwdne523069:0", "app.containers.NewAuthModal.steps.Password.rememberMe": "crwdns523071:0crwdne523071:0", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "crwdns523073:0crwdne523073:0", "app.containers.NewAuthModal.steps.Success.allDone": "crwdns523075:0crwdne523075:0", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "crwdns523077:0crwdne523077:0", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "crwdns523079:0crwdne523079:0", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "crwdns523081:0crwdne523081:0", "app.containers.NewAuthModal.steps.close": "crwdns523083:0crwdne523083:0", "app.containers.NewAuthModal.steps.continue": "crwdns523085:0crwdne523085:0", "app.containers.NewAuthModal.whatAreYouInterestedIn": "crwdns1081596:0crwdne1081596:0", "app.containers.NewAuthModal.youCantParticipate": "crwdns2999241:0crwdne2999241:0", "app.containers.NotificationMenu.a11y_notificationsLabel": "crwdns214910:0count={count}crwdne214910:0", "app.containers.NotificationMenu.adminRightsReceived": "crwdns214912:0crwdne214912:0", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "crwdns904303:0postTitle={postTitle}crwdnd904303:0reasonCode={reasonCode}crwdnd904303:0otherReason={otherReason}crwdne904303:0", "app.containers.NotificationMenu.cosponsorOfYourIdea": "crwdns3251157:0{name}crwdne3251157:0", "app.containers.NotificationMenu.deletedUser": "crwdns214916:0crwdne214916:0", "app.containers.NotificationMenu.error": "crwdns214918:0crwdne214918:0", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "crwdns748843:0{name}crwdne748843:0", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "crwdns748845:0{name}crwdne748845:0", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "crwdns748847:0{name}crwdne748847:0", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "crwdns748855:0{name}crwdne748855:0", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "crwdns748857:0{name}crwdne748857:0", "app.containers.NotificationMenu.invitationToCosponsorContribution": "crwdns3251159:0{name}crwdne3251159:0", "app.containers.NotificationMenu.invitationToCosponsorIdea": "crwdns3251161:0{name}crwdne3251161:0", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "crwdns904309:0{name}crwdne904309:0", "app.containers.NotificationMenu.invitationToCosponsorIssue": "crwdns3251163:0{name}crwdne3251163:0", "app.containers.NotificationMenu.invitationToCosponsorOption": "crwdns3251165:0{name}crwdne3251165:0", "app.containers.NotificationMenu.invitationToCosponsorPetition": "crwdns3251167:0{name}crwdne3251167:0", "app.containers.NotificationMenu.invitationToCosponsorProject": "crwdns3251169:0{name}crwdne3251169:0", "app.containers.NotificationMenu.invitationToCosponsorProposal": "crwdns3251171:0{name}crwdne3251171:0", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "crwdns3251173:0{name}crwdne3251173:0", "app.containers.NotificationMenu.loadMore": "crwdns214920:0crwdne214920:0", "app.containers.NotificationMenu.loading": "crwdns214922:0crwdne214922:0", "app.containers.NotificationMenu.mentionInComment": "crwdns214924:0{name}crwdne214924:0", "app.containers.NotificationMenu.mentionInInternalComment": "crwdns748859:0{name}crwdne748859:0", "app.containers.NotificationMenu.mentionInOfficialFeedback": "crwdns214926:0{name}crwdne214926:0", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "crwdns1866534:0crwdne1866534:0", "app.containers.NotificationMenu.noNotifications": "crwdns214928:0crwdne214928:0", "app.containers.NotificationMenu.notificationsLabel": "crwdns214930:0crwdne214930:0", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "crwdns1081598:0{officialName}crwdne1081598:0", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "crwdns1081600:0{officialName}crwdne1081600:0", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "crwdns1081602:0{officialName}crwdne1081602:0", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "crwdns1081604:0{officialName}crwdne1081604:0", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "crwdns1081606:0{officialName}crwdne1081606:0", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "crwdns3335211:0{officialName}crwdne3335211:0", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "crwdns1081608:0{officialName}crwdne1081608:0", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "crwdns3335213:0{officialName}crwdne3335213:0", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "crwdns1081610:0{officialName}crwdne1081610:0", "app.containers.NotificationMenu.postAssignedToYou": "crwdns214974:0{postTitle}crwdne214974:0", "app.containers.NotificationMenu.projectModerationRightsReceived": "crwdns214976:0{projectLink}crwdne214976:0", "app.containers.NotificationMenu.projectPhaseStarted": "crwdns214978:0{projectTitle}crwdne214978:0", "app.containers.NotificationMenu.projectPhaseUpcoming": "crwdns214980:0{projectTitle}crwdnd214980:0{phaseStartAt}crwdne214980:0", "app.containers.NotificationMenu.projectPublished": "crwdns1081612:0crwdne1081612:0", "app.containers.NotificationMenu.projectReviewRequest": "crwdns3848639:0{name}crwdnd3848639:0{projectTitle}crwdne3848639:0", "app.containers.NotificationMenu.projectReviewStateChange": "crwdns3848641:0{name}crwdnd3848641:0{projectTitle}crwdne3848641:0", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "crwdns1081614:0{ideaTitle}crwdnd1081614:0{status}crwdne1081614:0", "app.containers.NotificationMenu.thresholdReachedForAdmin": "crwdns215014:0{post}crwdne215014:0", "app.containers.NotificationMenu.userAcceptedYourInvitation": "crwdns215016:0{name}crwdne215016:0", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "crwdns1081618:0{name}crwdne1081618:0", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "crwdns1081620:0{name}crwdne1081620:0", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "crwdns1081622:0{name}crwdne1081622:0", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "crwdns1081624:0{name}crwdne1081624:0", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "crwdns1081626:0{name}crwdne1081626:0", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "crwdns3335215:0{name}crwdne3335215:0", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "crwdns1081628:0{name}crwdne1081628:0", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "crwdns3335217:0{name}crwdne3335217:0", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "crwdns1081630:0{name}crwdne1081630:0", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "crwdns904311:0{name}crwdnd904311:0{postTitle}crwdne904311:0", "app.containers.NotificationMenu.userReactedToYourComment": "crwdns215034:0{name}crwdne215034:0", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "crwdns904313:0{name}crwdnd904313:0{postTitle}crwdne904313:0", "app.containers.NotificationMenu.votingBasketNotSubmitted": "crwdns777659:0crwdne777659:0", "app.containers.NotificationMenu.votingBasketSubmitted": "crwdns777661:0crwdne777661:0", "app.containers.NotificationMenu.votingLastChance": "crwdns777663:0{phaseTitle}crwdne777663:0", "app.containers.NotificationMenu.votingResults": "crwdns777665:0{phaseTitle}crwdne777665:0", "app.containers.NotificationMenu.xAssignedPostToYou": "crwdns215038:0{name}crwdnd215038:0{postTitle}crwdne215038:0", "app.containers.PasswordRecovery.emailError": "crwdns215040:0crwdne215040:0", "app.containers.PasswordRecovery.emailLabel": "crwdns215042:0crwdne215042:0", "app.containers.PasswordRecovery.emailPlaceholder": "crwdns215044:0crwdne215044:0", "app.containers.PasswordRecovery.helmetDescription": "crwdns215046:0crwdne215046:0", "app.containers.PasswordRecovery.helmetTitle": "crwdns215048:0crwdne215048:0", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "crwdns215050:0crwdne215050:0", "app.containers.PasswordRecovery.resetPassword": "crwdns215052:0crwdne215052:0", "app.containers.PasswordRecovery.submitError": "crwdns215054:0crwdne215054:0", "app.containers.PasswordRecovery.subtitle": "crwdns215056:0crwdne215056:0", "app.containers.PasswordRecovery.title": "crwdns215058:0crwdne215058:0", "app.containers.PasswordReset.helmetDescription": "crwdns215060:0crwdne215060:0", "app.containers.PasswordReset.helmetTitle": "crwdns215062:0crwdne215062:0", "app.containers.PasswordReset.login": "crwdns215064:0crwdne215064:0", "app.containers.PasswordReset.passwordError": "crwdns215066:0crwdne215066:0", "app.containers.PasswordReset.passwordLabel": "crwdns215068:0crwdne215068:0", "app.containers.PasswordReset.passwordPlaceholder": "crwdns215070:0crwdne215070:0", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "crwdns215072:0crwdne215072:0", "app.containers.PasswordReset.pleaseLogInMessage": "crwdns215074:0crwdne215074:0", "app.containers.PasswordReset.requestNewPasswordReset": "crwdns215076:0crwdne215076:0", "app.containers.PasswordReset.submitError": "crwdns215078:0crwdne215078:0", "app.containers.PasswordReset.title": "crwdns215080:0crwdne215080:0", "app.containers.PasswordReset.updatePassword": "crwdns215082:0crwdne215082:0", "app.containers.ProjectFolderCards.allProjects": "crwdns215084:0crwdne215084:0", "app.containers.ProjectFolderCards.currentlyWorkingOn": "crwdns215086:0{orgName}crwdne215086:0", "app.containers.ProjectFolderShowPage.editFolder": "crwdns215088:0crwdne215088:0", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "crwdns215090:0crwdne215090:0", "app.containers.ProjectFolderShowPage.metaTitle1": "crwdns2278762:0{title}crwdnd2278762:0{orgName}crwdne2278762:0", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "crwdns215094:0{title}crwdnd215094:0{orgName}crwdne215094:0", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "crwdns215096:0{title}crwdnd215096:0{orgName}crwdne215096:0", "app.containers.ProjectFolderShowPage.readMore": "crwdns215098:0crwdne215098:0", "app.containers.ProjectFolderShowPage.seeLess": "crwdns215100:0crwdne215100:0", "app.containers.ProjectFolderShowPage.share": "crwdns215102:0crwdne215102:0", "app.containers.Projects.PollForm.document": "crwdns649387:0crwdne649387:0", "app.containers.Projects.PollForm.formCompleted": "crwdns215104:0crwdne215104:0", "app.containers.Projects.PollForm.maxOptions": "crwdns215108:0{maxNumber}crwdne215108:0", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "crwdns649389:0crwdne649389:0", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "crwdns649391:0crwdne649391:0", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "crwdns215116:0crwdne215116:0", "app.containers.Projects.PollForm.pollDisabledNotPossible": "crwdns215118:0crwdne215118:0", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "crwdns215122:0crwdne215122:0", "app.containers.Projects.PollForm.sendAnswer": "crwdns215124:0crwdne215124:0", "app.containers.Projects.a11y_phase": "crwdns215130:0{phaseNumber}crwdnd215130:0{phaseTitle}crwdne215130:0", "app.containers.Projects.a11y_phasesOverview": "crwdns215132:0crwdne215132:0", "app.containers.Projects.a11y_titleInputs": "crwdns215134:0crwdne215134:0", "app.containers.Projects.a11y_titleInputsPhase": "crwdns215136:0crwdne215136:0", "app.containers.Projects.accessRights": "crwdns1294758:0crwdne1294758:0", "app.containers.Projects.addedToBasket": "crwdns215140:0crwdne215140:0", "app.containers.Projects.allocateBudget": "crwdns215142:0crwdne215142:0", "app.containers.Projects.archived": "crwdns215144:0crwdne215144:0", "app.containers.Projects.basketSubmitted": "crwdns215146:0crwdne215146:0", "app.containers.Projects.contributions": "crwdns215150:0crwdne215150:0", "app.containers.Projects.createANewPhase": "crwdns1442892:0crwdne1442892:0", "app.containers.Projects.currentPhase": "crwdns215152:0crwdne215152:0", "app.containers.Projects.document": "crwdns649393:0crwdne649393:0", "app.containers.Projects.editProject": "crwdns215154:0crwdne215154:0", "app.containers.Projects.emailSharingBody": "crwdns215156:0{initiativeUrl}crwdne215156:0", "app.containers.Projects.emailSharingSubject": "crwdns215158:0{initiativeTitle}crwdne215158:0", "app.containers.Projects.endedOn": "crwdns215160:0{date}crwdne215160:0", "app.containers.Projects.events": "crwdns215162:0crwdne215162:0", "app.containers.Projects.header": "crwdns215166:0crwdne215166:0", "app.containers.Projects.ideas": "crwdns215168:0crwdne215168:0", "app.containers.Projects.information": "crwdns215170:0crwdne215170:0", "app.containers.Projects.initiatives": "crwdns3335219:0crwdne3335219:0", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "crwdns649395:0crwdne649395:0", "app.containers.Projects.invisibleTitlePhaseAbout": "crwdns215172:0crwdne215172:0", "app.containers.Projects.invisibleTitlePoll": "crwdns215174:0crwdne215174:0", "app.containers.Projects.invisibleTitleSurvey": "crwdns215176:0crwdne215176:0", "app.containers.Projects.issues": "crwdns215178:0crwdne215178:0", "app.containers.Projects.liveDataMessage": "crwdns3373807:0crwdne3373807:0", "app.containers.Projects.location": "crwdns215180:0crwdne215180:0", "app.containers.Projects.manageBasket": "crwdns215182:0crwdne215182:0", "app.containers.Projects.meetMinBudgetRequirement": "crwdns215184:0crwdne215184:0", "app.containers.Projects.meetMinSelectionRequirement": "crwdns215186:0crwdne215186:0", "app.containers.Projects.metaTitle1": "crwdns2278764:0{projectTitle}crwdnd2278764:0{orgName}crwdne2278764:0", "app.containers.Projects.minBudgetRequired": "crwdns215190:0crwdne215190:0", "app.containers.Projects.myBasket": "crwdns215192:0crwdne215192:0", "app.containers.Projects.navPoll": "crwdns215194:0crwdne215194:0", "app.containers.Projects.navSurvey": "crwdns215196:0crwdne215196:0", "app.containers.Projects.newPhase": "crwdns1442894:0crwdne1442894:0", "app.containers.Projects.nextPhase": "crwdns215198:0crwdne215198:0", "app.containers.Projects.noEndDate": "crwdns1294632:0crwdne1294632:0", "app.containers.Projects.noItems": "crwdns215200:0crwdne215200:0", "app.containers.Projects.noPastEvents": "crwdns215202:0crwdne215202:0", "app.containers.Projects.noPhaseSelected": "crwdns215204:0crwdne215204:0", "app.containers.Projects.noUpcomingOrOngoingEvents": "crwdns215206:0crwdne215206:0", "app.containers.Projects.offlineVotersTooltip": "crwdns3763167:0crwdne3763167:0", "app.containers.Projects.options": "crwdns215210:0crwdne215210:0", "app.containers.Projects.participants": "crwdns3166357:0crwdne3166357:0", "app.containers.Projects.participantsTooltip4": "crwdns1294760:0{accessRightsLink}crwdne1294760:0", "app.containers.Projects.pastEvents": "crwdns215212:0crwdne215212:0", "app.containers.Projects.petitions": "crwdns3335221:0crwdne3335221:0", "app.containers.Projects.phases": "crwdns215214:0crwdne215214:0", "app.containers.Projects.previousPhase": "crwdns215220:0crwdne215220:0", "app.containers.Projects.project": "crwdns215222:0crwdne215222:0", "app.containers.Projects.projectTwitterMessage": "crwdns215224:0{projectName}crwdnd215224:0{orgName}crwdne215224:0", "app.containers.Projects.projects": "crwdns215226:0crwdne215226:0", "app.containers.Projects.proposals": "crwdns3335223:0crwdne3335223:0", "app.containers.Projects.questions": "crwdns215228:0crwdne215228:0", "app.containers.Projects.readLess": "crwdns215230:0crwdne215230:0", "app.containers.Projects.readMore": "crwdns215232:0crwdne215232:0", "app.containers.Projects.removeItem": "crwdns215234:0crwdne215234:0", "app.containers.Projects.requiredSelection": "crwdns215236:0crwdne215236:0", "app.containers.Projects.reviewDocument": "crwdns649405:0crwdne649405:0", "app.containers.Projects.seeTheContributions": "crwdns215240:0crwdne215240:0", "app.containers.Projects.seeTheIdeas": "crwdns215242:0crwdne215242:0", "app.containers.Projects.seeTheInitiatives": "crwdns3335225:0crwdne3335225:0", "app.containers.Projects.seeTheIssues": "crwdns215244:0crwdne215244:0", "app.containers.Projects.seeTheOptions": "crwdns215246:0crwdne215246:0", "app.containers.Projects.seeThePetitions": "crwdns3335227:0crwdne3335227:0", "app.containers.Projects.seeTheProjects": "crwdns215248:0crwdne215248:0", "app.containers.Projects.seeTheProposals": "crwdns3335229:0crwdne3335229:0", "app.containers.Projects.seeTheQuestions": "crwdns215250:0crwdne215250:0", "app.containers.Projects.seeUpcomingEvents": "crwdns953649:0crwdne953649:0", "app.containers.Projects.share": "crwdns215254:0crwdne215254:0", "app.containers.Projects.shareThisProject": "crwdns215256:0crwdne215256:0", "app.containers.Projects.submitMyBasket": "crwdns215258:0crwdne215258:0", "app.containers.Projects.survey": "crwdns215260:0crwdne215260:0", "app.containers.Projects.takeThePoll": "crwdns215262:0crwdne215262:0", "app.containers.Projects.takeTheSurvey": "crwdns215264:0crwdne215264:0", "app.containers.Projects.timeline": "crwdns215266:0crwdne215266:0", "app.containers.Projects.upcomingAndOngoingEvents": "crwdns215268:0crwdne215268:0", "app.containers.Projects.upcomingEvents": "crwdns215270:0crwdne215270:0", "app.containers.Projects.whatsAppMessage": "crwdns215272:0{projectName}crwdnd215272:0{orgName}crwdne215272:0", "app.containers.Projects.yourBudget": "crwdns215322:0crwdne215322:0", "app.containers.ProjectsIndexPage.metaDescription": "crwdns215324:0{orgName}crwdne215324:0", "app.containers.ProjectsIndexPage.metaTitle1": "crwdns2278766:0{orgName}crwdne2278766:0", "app.containers.ProjectsIndexPage.pageTitle": "crwdns215328:0crwdne215328:0", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "crwdns215330:0crwdne215330:0", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "crwdns215332:0{signInLink}crwdnd215332:0{signUpLink}crwdne215332:0", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "crwdns1159934:0crwdne1159934:0", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "crwdns215334:0crwdne215334:0", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "crwdns215336:0crwdne215336:0", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "crwdns215338:0crwdne215338:0", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "crwdns215340:0x={x}crwdne215340:0", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "crwdns2770823:0crwdne2770823:0", "app.containers.ProjectsShowPage.process.survey.survey": "crwdns215346:0crwdne215346:0", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "crwdns215348:0{logInLink}crwdne215348:0", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "crwdns215352:0crwdne215352:0", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "crwdns523093:0{completeRegistrationLink}crwdne523093:0", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "crwdns215354:0crwdne215354:0", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "crwdns215358:0{verificationLink}crwdne215358:0", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "crwdns2411980:0crwdne2411980:0", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "crwdns649407:0crwdne649407:0", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "crwdns649409:0crwdne649409:0", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "crwdns649411:0crwdne649411:0", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "crwdns649413:0crwdne649413:0", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "crwdns649415:0{signUpLink}crwdnd649415:0{logInLink}crwdne649415:0", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "crwdns649417:0crwdne649417:0", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "crwdns649419:0{completeRegistrationLink}crwdne649419:0", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "crwdns649421:0crwdne649421:0", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "crwdns649423:0{verificationLink}crwdne649423:0", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "crwdns649425:0crwdne649425:0", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "crwdns3549653:0manualVoters={manualVoters}crwdne3549653:0", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "crwdns2503146:0baskets={baskets}crwdne2503146:0", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "crwdns1646276:0crwdne1646276:0", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "crwdns1646278:0crwdne1646278:0", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "crwdns777669:0crwdne777669:0", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "crwdns2573824:0crwdne2573824:0", "app.containers.ReactionControl.a11y_likesDislikes": "crwdns777671:0{likesCount}crwdnd777671:0{dislikesCount}crwdne777671:0", "app.containers.ReactionControl.cancelDislikeSuccess": "crwdns2423294:0crwdne2423294:0", "app.containers.ReactionControl.cancelLikeSuccess": "crwdns2423296:0crwdne2423296:0", "app.containers.ReactionControl.dislikeSuccess": "crwdns2423298:0crwdne2423298:0", "app.containers.ReactionControl.likeSuccess": "crwdns2423300:0crwdne2423300:0", "app.containers.ReactionControl.reactionErrorSubTitle": "crwdns777673:0crwdne777673:0", "app.containers.ReactionControl.reactionSuccessTitle": "crwdns777675:0crwdne777675:0", "app.containers.ReactionControl.vote": "crwdns2747163:0crwdne2747163:0", "app.containers.ReactionControl.voted": "crwdns2747165:0crwdne2747165:0", "app.containers.SearchInput.a11y_cancelledPostingComment": "crwdns2400642:0crwdne2400642:0", "app.containers.SearchInput.a11y_commentsHaveChanged": "crwdns2400644:0{sortOder}crwdne2400644:0", "app.containers.SearchInput.a11y_eventsHaveChanged1": "crwdns2400646:0numberOfEvents={numberOfEvents}crwdne2400646:0", "app.containers.SearchInput.a11y_projectsHaveChanged1": "crwdns2400650:0numberOfFilteredResults={numberOfFilteredResults}crwdne2400650:0", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "crwdns2400652:0numberOfSearchResults={numberOfSearchResults}crwdne2400652:0", "app.containers.SearchInput.removeSearchTerm": "crwdns215366:0crwdne215366:0", "app.containers.SearchInput.searchAriaLabel": "crwdns215368:0crwdne215368:0", "app.containers.SearchInput.searchLabel": "crwdns215370:0crwdne215370:0", "app.containers.SearchInput.searchPlaceholder": "crwdns215372:0crwdne215372:0", "app.containers.SearchInput.searchTerm": "crwdns215374:0{searchTerm}crwdne215374:0", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "crwdns215388:0crwdne215388:0", "app.containers.SignIn.or": "crwdns215394:0crwdne215394:0", "app.containers.SignIn.signInError": "crwdns215402:0crwdne215402:0", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "crwdns215410:0crwdne215410:0", "app.containers.SignIn.whatIsFranceConnect": "crwdns215412:0crwdne215412:0", "app.containers.SignUp.adminOptions2": "crwdns3337405:0crwdne3337405:0", "app.containers.SignUp.backToSignUpOptions": "crwdns215414:0crwdne215414:0", "app.containers.SignUp.continue": "crwdns1081632:0crwdne1081632:0", "app.containers.SignUp.emailConsent": "crwdns215426:0crwdne215426:0", "app.containers.SignUp.emptyFirstNameError": "crwdns215436:0crwdne215436:0", "app.containers.SignUp.emptyLastNameError": "crwdns215438:0crwdne215438:0", "app.containers.SignUp.firstNamesLabel": "crwdns215442:0crwdne215442:0", "app.containers.SignUp.goToLogIn": "crwdns215444:0{goToOtherFlowLink}crwdne215444:0", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "crwdns1159858:0{link}crwdne1159858:0", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "crwdns1159860:0{link}crwdne1159860:0", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "crwdns215450:0{link}crwdne215450:0", "app.containers.SignUp.invitationErrorText": "crwdns1042153:0crwdne1042153:0", "app.containers.SignUp.lastNameLabel": "crwdns215454:0crwdne215454:0", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "crwdns1081634:0crwdne1081634:0", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "crwdns1081636:0crwdne1081636:0", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "crwdns1081638:0crwdne1081638:0", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "crwdns1081640:0crwdne1081640:0", "app.containers.SignUp.privacyPolicyNotAcceptedError": "crwdns215464:0crwdne215464:0", "app.containers.SignUp.signUp2": "crwdns215466:0crwdne215466:0", "app.containers.SignUp.skip": "crwdns215470:0crwdne215470:0", "app.containers.SignUp.tacError": "crwdns215476:0crwdne215476:0", "app.containers.SignUp.thePrivacyPolicy": "crwdns215478:0crwdne215478:0", "app.containers.SignUp.theTermsAndConditions": "crwdns215480:0crwdne215480:0", "app.containers.SignUp.unknownError": "crwdns215488:0tenantName={tenantName}crwdne215488:0", "app.containers.SignUp.viennaConsentEmail": "crwdns215492:0crwdne215492:0", "app.containers.SignUp.viennaConsentFirstName": "crwdns215494:0crwdne215494:0", "app.containers.SignUp.viennaConsentFooter": "crwdns215496:0crwdne215496:0", "app.containers.SignUp.viennaConsentHeader": "crwdns215498:0crwdne215498:0", "app.containers.SignUp.viennaConsentLastName": "crwdns215500:0crwdne215500:0", "app.containers.SignUp.viennaConsentUserName": "crwdns215502:0crwdne215502:0", "app.containers.SignUp.viennaDataProtection": "crwdns215504:0crwdne215504:0", "app.containers.SiteMap.contributions": "crwdns215508:0crwdne215508:0", "app.containers.SiteMap.cookiePolicyLinkTitle": "crwdns4456965:0crwdne4456965:0", "app.containers.SiteMap.issues": "crwdns215510:0crwdne215510:0", "app.containers.SiteMap.options": "crwdns215512:0crwdne215512:0", "app.containers.SiteMap.projects": "crwdns215514:0crwdne215514:0", "app.containers.SiteMap.questions": "crwdns215516:0crwdne215516:0", "app.containers.SpamReport.buttonSave": "crwdns215518:0crwdne215518:0", "app.containers.SpamReport.buttonSuccess": "crwdns215520:0crwdne215520:0", "app.containers.SpamReport.inappropriate": "crwdns215522:0crwdne215522:0", "app.containers.SpamReport.messageError": "crwdns215524:0crwdne215524:0", "app.containers.SpamReport.messageSuccess": "crwdns215526:0crwdne215526:0", "app.containers.SpamReport.other": "crwdns215528:0crwdne215528:0", "app.containers.SpamReport.otherReasonPlaceholder": "crwdns215530:0crwdne215530:0", "app.containers.SpamReport.wrong_content": "crwdns215532:0crwdne215532:0", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "crwdns215534:0crwdne215534:0", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "crwdns1199810:0crwdne1199810:0", "app.containers.UsersEditPage.addPassword": "crwdns523095:0crwdne523095:0", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "crwdns215536:0crwdne215536:0", "app.containers.UsersEditPage.becomeVerifiedTitle": "crwdns215538:0crwdne215538:0", "app.containers.UsersEditPage.bio": "crwdns215540:0crwdne215540:0", "app.containers.UsersEditPage.bio_placeholder": "crwdns215542:0crwdne215542:0", "app.containers.UsersEditPage.blockedVerified": "crwdns215544:0crwdne215544:0", "app.containers.UsersEditPage.buttonSuccessLabel": "crwdns215546:0crwdne215546:0", "app.containers.UsersEditPage.cancel": "crwdns215548:0crwdne215548:0", "app.containers.UsersEditPage.changeEmail": "crwdns523097:0crwdne523097:0", "app.containers.UsersEditPage.changePassword2": "crwdns523099:0crwdne523099:0", "app.containers.UsersEditPage.clickHereToUpdateVerification": "crwdns215552:0crwdne215552:0", "app.containers.UsersEditPage.conditionsLinkText": "crwdns215554:0crwdne215554:0", "app.containers.UsersEditPage.contactUs": "crwdns215556:0{feedbackLink}crwdne215556:0", "app.containers.UsersEditPage.deleteAccountSubtext": "crwdns215558:0crwdne215558:0", "app.containers.UsersEditPage.deleteMyAccount": "crwdns215560:0crwdne215560:0", "app.containers.UsersEditPage.deleteYourAccount": "crwdns215562:0crwdne215562:0", "app.containers.UsersEditPage.deletionSection": "crwdns215564:0crwdne215564:0", "app.containers.UsersEditPage.deletionSubtitle": "crwdns215566:0crwdne215566:0", "app.containers.UsersEditPage.email": "crwdns215568:0crwdne215568:0", "app.containers.UsersEditPage.emailEmptyError": "crwdns215570:0crwdne215570:0", "app.containers.UsersEditPage.emailInvalidError": "crwdns215572:0crwdne215572:0", "app.containers.UsersEditPage.feedbackLinkText": "crwdns215574:0crwdne215574:0", "app.containers.UsersEditPage.feedbackLinkUrl": "crwdns215576:0{url}crwdne215576:0", "app.containers.UsersEditPage.firstNames": "crwdns215578:0crwdne215578:0", "app.containers.UsersEditPage.firstNamesEmptyError": "crwdns215580:0crwdne215580:0", "app.containers.UsersEditPage.h1": "crwdns215582:0crwdne215582:0", "app.containers.UsersEditPage.h1sub": "crwdns215584:0crwdne215584:0", "app.containers.UsersEditPage.image": "crwdns215586:0crwdne215586:0", "app.containers.UsersEditPage.imageDropzonePlaceholder": "crwdns215588:0crwdne215588:0", "app.containers.UsersEditPage.invisibleTitleUserSettings": "crwdns215590:0crwdne215590:0", "app.containers.UsersEditPage.language": "crwdns215592:0crwdne215592:0", "app.containers.UsersEditPage.lastName": "crwdns215594:0crwdne215594:0", "app.containers.UsersEditPage.lastNameEmptyError": "crwdns215596:0crwdne215596:0", "app.containers.UsersEditPage.loading": "crwdns215598:0crwdne215598:0", "app.containers.UsersEditPage.loginCredentialsSubtitle": "crwdns523101:0crwdne523101:0", "app.containers.UsersEditPage.loginCredentialsTitle": "crwdns523103:0crwdne523103:0", "app.containers.UsersEditPage.messageError": "crwdns215600:0crwdne215600:0", "app.containers.UsersEditPage.messageSuccess": "crwdns215602:0crwdne215602:0", "app.containers.UsersEditPage.metaDescription": "crwdns215604:0{firstName}crwdnd215604:0{lastName}crwdnd215604:0{tenantName}crwdne215604:0", "app.containers.UsersEditPage.metaTitle1": "crwdns2278768:0{firstName}crwdnd2278768:0{lastName}crwdnd2278768:0{orgName}crwdne2278768:0", "app.containers.UsersEditPage.noGoingBack": "crwdns215608:0crwdne215608:0", "app.containers.UsersEditPage.noNameWarning2": "crwdns3338251:0{displayName}crwdne3338251:0", "app.containers.UsersEditPage.notificationsSubTitle": "crwdns215610:0crwdne215610:0", "app.containers.UsersEditPage.notificationsTitle": "crwdns215612:0crwdne215612:0", "app.containers.UsersEditPage.password": "crwdns215614:0crwdne215614:0", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "crwdns215616:0{minimumPasswordLength}crwdne215616:0", "app.containers.UsersEditPage.passwordAddSection": "crwdns523105:0crwdne523105:0", "app.containers.UsersEditPage.passwordAddSubtitle2": "crwdns523107:0crwdne523107:0", "app.containers.UsersEditPage.passwordChangeSection": "crwdns215618:0crwdne215618:0", "app.containers.UsersEditPage.passwordChangeSubtitle": "crwdns215620:0crwdne215620:0", "app.containers.UsersEditPage.privacyReasons": "crwdns215622:0{conditionsLink}crwdne215622:0", "app.containers.UsersEditPage.processing": "crwdns215624:0crwdne215624:0", "app.containers.UsersEditPage.provideFirstNameIfLastName": "crwdns550205:0crwdne550205:0", "app.containers.UsersEditPage.reasonsToStayListTitle": "crwdns215626:0crwdne215626:0", "app.containers.UsersEditPage.submit": "crwdns215628:0crwdne215628:0", "app.containers.UsersEditPage.tooManyEmails": "crwdns215630:0crwdne215630:0", "app.containers.UsersEditPage.updateverification": "crwdns215632:0{reverifyButton}crwdne215632:0", "app.containers.UsersEditPage.user": "crwdns215634:0crwdne215634:0", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "crwdns215636:0crwdne215636:0", "app.containers.UsersEditPage.verifiedIdentityTitle": "crwdns215638:0crwdne215638:0", "app.containers.UsersEditPage.verifyNow": "crwdns215640:0crwdne215640:0", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "crwdns4004105:0crwdne4004105:0", "app.containers.UsersShowPage.a11y_likesCount": "crwdns777677:0likesCount={likesCount}crwdne777677:0", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "crwdns215644:0crwdne215644:0", "app.containers.UsersShowPage.areas": "crwdns1081642:0crwdne1081642:0", "app.containers.UsersShowPage.commentsWithCount": "crwdns215648:0{commentsCount}crwdne215648:0", "app.containers.UsersShowPage.editProfile": "crwdns215650:0crwdne215650:0", "app.containers.UsersShowPage.emptyInfoText": "crwdns855789:0crwdne855789:0", "app.containers.UsersShowPage.eventsWithCount": "crwdns953651:0{eventsCount}crwdne953651:0", "app.containers.UsersShowPage.followingWithCount": "crwdns1081644:0{followingCount}crwdne1081644:0", "app.containers.UsersShowPage.inputs": "crwdns1368378:0crwdne1368378:0", "app.containers.UsersShowPage.invisibleTitlePostsList": "crwdns215654:0crwdne215654:0", "app.containers.UsersShowPage.invisibleTitleUserComments": "crwdns215656:0crwdne215656:0", "app.containers.UsersShowPage.loadMore": "crwdns1081646:0crwdne1081646:0", "app.containers.UsersShowPage.loadMoreComments": "crwdns215658:0crwdne215658:0", "app.containers.UsersShowPage.loadingComments": "crwdns215660:0crwdne215660:0", "app.containers.UsersShowPage.loadingEvents": "crwdns953653:0crwdne953653:0", "app.containers.UsersShowPage.memberSince": "crwdns215662:0{date}crwdne215662:0", "app.containers.UsersShowPage.metaTitle1": "crwdns2278770:0{firstName}crwdnd2278770:0{lastName}crwdnd2278770:0{orgName}crwdne2278770:0", "app.containers.UsersShowPage.noCommentsForUser": "crwdns215666:0crwdne215666:0", "app.containers.UsersShowPage.noCommentsForYou": "crwdns215668:0crwdne215668:0", "app.containers.UsersShowPage.noEventsForUser": "crwdns953655:0crwdne953655:0", "app.containers.UsersShowPage.postsWithCount": "crwdns215670:0{ideasCount}crwdne215670:0", "app.containers.UsersShowPage.projectFolders": "crwdns855797:0crwdne855797:0", "app.containers.UsersShowPage.projects": "crwdns855799:0crwdne855799:0", "app.containers.UsersShowPage.proposals": "crwdns1368380:0crwdne1368380:0", "app.containers.UsersShowPage.seePost": "crwdns215674:0crwdne215674:0", "app.containers.UsersShowPage.surveyResponses": "crwdns4004107:0{responses}crwdne4004107:0", "app.containers.UsersShowPage.topics": "crwdns1081648:0crwdne1081648:0", "app.containers.UsersShowPage.tryAgain": "crwdns215676:0crwdne215676:0", "app.containers.UsersShowPage.userShowPageMetaDescription": "crwdns215680:0{firstName}crwdnd215680:0{lastName}crwdnd215680:0{orgName}crwdne215680:0", "app.containers.VoteControl.close": "crwdns215684:0crwdne215684:0", "app.containers.VoteControl.voteErrorTitle": "crwdns215688:0crwdne215688:0", "app.containers.admin.ContentBuilder.default": "crwdns3106625:0crwdne3106625:0", "app.containers.admin.ContentBuilder.imageTextCards": "crwdns3106627:0crwdne3106627:0", "app.containers.admin.ContentBuilder.infoWithAccordions": "crwdns3106629:0crwdne3106629:0", "app.containers.admin.ContentBuilder.oneColumnLayout": "crwdns3106631:0crwdne3106631:0", "app.containers.admin.ContentBuilder.projectDescription": "crwdns3106633:0crwdne3106633:0", "app.containers.app.navbar.admin": "crwdns215692:0crwdne215692:0", "app.containers.app.navbar.allProjects": "crwdns215694:0crwdne215694:0", "app.containers.app.navbar.ariaLabel": "crwdns215696:0crwdne215696:0", "app.containers.app.navbar.closeMobileNavMenu": "crwdns215698:0crwdne215698:0", "app.containers.app.navbar.editProfile": "crwdns215702:0crwdne215702:0", "app.containers.app.navbar.fullMobileNavigation": "crwdns215704:0crwdne215704:0", "app.containers.app.navbar.logIn": "crwdns215706:0crwdne215706:0", "app.containers.app.navbar.logoImgAltText": "crwdns215708:0{orgName}crwdne215708:0", "app.containers.app.navbar.myProfile": "crwdns215710:0crwdne215710:0", "app.containers.app.navbar.search": "crwdns215712:0crwdne215712:0", "app.containers.app.navbar.showFullMenu": "crwdns1368382:0crwdne1368382:0", "app.containers.app.navbar.signOut": "crwdns215716:0crwdne215716:0", "app.containers.eventspage.errorWhenFetchingEvents": "crwdns215720:0crwdne215720:0", "app.containers.eventspage.events": "crwdns953657:0crwdne953657:0", "app.containers.eventspage.eventsPageDescription": "crwdns215722:0{orgName}crwdne215722:0", "app.containers.eventspage.eventsPageTitle1": "crwdns2278772:0{orgName}crwdne2278772:0", "app.containers.eventspage.filterDropdownTitle": "crwdns215726:0crwdne215726:0", "app.containers.eventspage.noPastEvents": "crwdns215728:0crwdne215728:0", "app.containers.eventspage.noUpcomingOrOngoingEvents": "crwdns215730:0crwdne215730:0", "app.containers.eventspage.pastEvents": "crwdns215732:0crwdne215732:0", "app.containers.eventspage.upcomingAndOngoingEvents": "crwdns215734:0crwdne215734:0", "app.containers.footer.accessibility-statement": "crwdns215736:0crwdne215736:0", "app.containers.footer.ariaLabel": "crwdns215738:0crwdne215738:0", "app.containers.footer.cookie-policy": "crwdns215740:0crwdne215740:0", "app.containers.footer.cookieSettings": "crwdns215742:0crwdne215742:0", "app.containers.footer.feedbackEmptyError": "crwdns215744:0crwdne215744:0", "app.containers.footer.poweredBy": "crwdns215746:0crwdne215746:0", "app.containers.footer.privacy-policy": "crwdns215748:0crwdne215748:0", "app.containers.footer.siteMap": "crwdns215750:0crwdne215750:0", "app.containers.footer.terms-and-conditions": "crwdns215752:0crwdne215752:0", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "crwdns4243965:0crwdne4243965:0", "app.containers.ideaHeading.confirmLeaveFormButtonText": "crwdns4243967:0crwdne4243967:0", "app.containers.ideaHeading.editForm": "crwdns4243969:0crwdne4243969:0", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "crwdns4243971:0crwdne4243971:0", "app.containers.ideaHeading.leaveIdeaForm": "crwdns4243973:0crwdne4243973:0", "app.containers.ideaHeading.leaveIdeaText": "crwdns4243975:0crwdne4243975:0", "app.containers.landing.cityProjects": "crwdns215756:0crwdne215756:0", "app.containers.landing.completeProfile": "crwdns215758:0crwdne215758:0", "app.containers.landing.completeYourProfile": "crwdns215760:0{firstName}crwdne215760:0", "app.containers.landing.createAccount": "crwdns215762:0crwdne215762:0", "app.containers.landing.defaultSignedInMessage": "crwdns215764:0{orgName}crwdne215764:0", "app.containers.landing.doItLater": "crwdns215766:0crwdne215766:0", "app.containers.landing.new": "crwdns215772:0crwdne215772:0", "app.containers.landing.subtitleCity": "crwdns215776:0{orgName}crwdne215776:0", "app.containers.landing.titleCity": "crwdns215778:0{orgName}crwdne215778:0", "app.containers.landing.twitterMessage": "crwdns215780:0{ideaTitle}crwdne215780:0", "app.containers.landing.upcomingEventsWidgetTitle": "crwdns215782:0crwdne215782:0", "app.containers.landing.userDeletedSubtitle": "crwdns215784:0{contactLink}crwdne215784:0", "app.containers.landing.userDeletedSubtitleLinkText": "crwdns215786:0crwdne215786:0", "app.containers.landing.userDeletedSubtitleLinkUrl": "crwdns215788:0{url}crwdne215788:0", "app.containers.landing.userDeletedTitle": "crwdns215790:0crwdne215790:0", "app.containers.landing.userDeletionFailed": "crwdns215792:0crwdne215792:0", "app.containers.landing.verifyNow": "crwdns215794:0crwdne215794:0", "app.containers.landing.verifyYourIdentity": "crwdns215796:0crwdne215796:0", "app.containers.landing.viewAllEventsText": "crwdns215798:0crwdne215798:0", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "crwdns1442890:0crwdne1442890:0", "app.errors.after_end_at": "crwdns215800:0crwdne215800:0", "app.errors.avatar_carrierwave_download_error": "crwdns215802:0crwdne215802:0", "app.errors.avatar_carrierwave_integrity_error": "crwdns215804:0crwdne215804:0", "app.errors.avatar_carrierwave_processing_error": "crwdns215806:0crwdne215806:0", "app.errors.avatar_extension_blacklist_error": "crwdns215808:0crwdne215808:0", "app.errors.avatar_extension_whitelist_error": "crwdns215810:0crwdne215810:0", "app.errors.banner_cta_button_multiloc_blank": "crwdns215812:0crwdne215812:0", "app.errors.banner_cta_button_url_blank": "crwdns215814:0crwdne215814:0", "app.errors.banner_cta_button_url_url": "crwdns215816:0crwdne215816:0", "app.errors.banner_cta_signed_in_text_multiloc_blank": "crwdns215818:0crwdne215818:0", "app.errors.banner_cta_signed_in_url_blank": "crwdns215820:0crwdne215820:0", "app.errors.banner_cta_signed_in_url_url": "crwdns215822:0crwdne215822:0", "app.errors.banner_cta_signed_out_text_multiloc_blank": "crwdns215824:0crwdne215824:0", "app.errors.banner_cta_signed_out_url_blank": "crwdns215826:0crwdne215826:0", "app.errors.banner_cta_signed_out_url_url": "crwdns215828:0crwdne215828:0", "app.errors.base_includes_banned_words": "crwdns4721737:0crwdne4721737:0", "app.errors.body_multiloc_includes_banned_words": "crwdns1159862:0crwdne1159862:0", "app.errors.bulk_import_idea_not_valid": "crwdns2158518:0{value}crwdne2158518:0", "app.errors.bulk_import_image_url_not_valid": "crwdns2158520:0{value}crwdnd2158520:0{row}crwdne2158520:0", "app.errors.bulk_import_location_point_blank_coordinate": "crwdns2158522:0{value}crwdnd2158522:0{row}crwdne2158522:0", "app.errors.bulk_import_location_point_non_numeric_coordinate": "crwdns2158524:0{value}crwdnd2158524:0{row}crwdne2158524:0", "app.errors.bulk_import_malformed_pdf": "crwdns2158526:0crwdne2158526:0", "app.errors.bulk_import_maximum_ideas_exceeded": "crwdns2158528:0{value}crwdne2158528:0", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "crwdns2158530:0{value}crwdne2158530:0", "app.errors.bulk_import_not_enough_pdf_pages": "crwdns2169478:0crwdne2169478:0", "app.errors.bulk_import_publication_date_invalid_format": "crwdns2158532:0{value}crwdne2158532:0", "app.errors.cannot_contain_ideas": "crwdns215860:0crwdne215860:0", "app.errors.cant_change_after_first_response": "crwdns215862:0crwdne215862:0", "app.errors.category_name_taken": "crwdns215864:0crwdne215864:0", "app.errors.confirmation_code_expired": "crwdns215866:0crwdne215866:0", "app.errors.confirmation_code_invalid": "crwdns215868:0crwdne215868:0", "app.errors.confirmation_code_too_many_resets": "crwdns215870:0crwdne215870:0", "app.errors.confirmation_code_too_many_retries": "crwdns215872:0crwdne215872:0", "app.errors.email_already_active": "crwdns215874:0{value}crwdnd215874:0{row}crwdne215874:0", "app.errors.email_already_invited": "crwdns215876:0{value}crwdnd215876:0{row}crwdne215876:0", "app.errors.email_blank": "crwdns215878:0crwdne215878:0", "app.errors.email_domain_blacklisted": "crwdns215880:0crwdne215880:0", "app.errors.email_invalid": "crwdns215882:0crwdne215882:0", "app.errors.email_taken": "crwdns215884:0crwdne215884:0", "app.errors.email_taken_by_invite": "crwdns215886:0{value}crwdnd215886:0{supportEmail}crwdne215886:0", "app.errors.emails_duplicate": "crwdns215888:0{value}crwdnd215888:0{rows}crwdne215888:0", "app.errors.extension_whitelist_error": "crwdns4982903:0crwdne4982903:0", "app.errors.file_extension_whitelist_error": "crwdns215890:0crwdne215890:0", "app.errors.first_name_blank": "crwdns215892:0crwdne215892:0", "app.errors.generics.blank": "crwdns215894:0crwdne215894:0", "app.errors.generics.invalid": "crwdns215896:0crwdne215896:0", "app.errors.generics.taken": "crwdns215898:0crwdne215898:0", "app.errors.generics.unsupported_locales": "crwdns215900:0crwdne215900:0", "app.errors.group_ids_unauthorized_choice_moderator": "crwdns215902:0crwdne215902:0", "app.errors.has_other_overlapping_phases": "crwdns215904:0crwdne215904:0", "app.errors.invalid_email": "crwdns215906:0{value}crwdnd215906:0{row}crwdne215906:0", "app.errors.invalid_row": "crwdns215908:0{row}crwdne215908:0", "app.errors.is_not_timeline_project": "crwdns215910:0crwdne215910:0", "app.errors.key_invalid": "crwdns215912:0crwdne215912:0", "app.errors.last_name_blank": "crwdns215914:0crwdne215914:0", "app.errors.locale_blank": "crwdns215916:0crwdne215916:0", "app.errors.locale_inclusion": "crwdns215918:0crwdne215918:0", "app.errors.malformed_admin_value": "crwdns215920:0{value}crwdnd215920:0{row}crwdne215920:0", "app.errors.malformed_groups_value": "crwdns215922:0{value}crwdnd215922:0{row}crwdne215922:0", "app.errors.max_invites_limit_exceeded1": "crwdns478777:0crwdne478777:0", "app.errors.maximum_attendees_greater_than1": "crwdns4902963:0crwdne4902963:0", "app.errors.maximum_attendees_greater_than_attendees_count1": "crwdns4902965:0crwdne4902965:0", "app.errors.no_invites_specified": "crwdns215926:0crwdne215926:0", "app.errors.no_recipients": "crwdns1211668:0crwdne1211668:0", "app.errors.number_invalid": "crwdns4305670:0crwdne4305670:0", "app.errors.password_blank": "crwdns215928:0crwdne215928:0", "app.errors.password_invalid": "crwdns215930:0crwdne215930:0", "app.errors.password_too_short": "crwdns215932:0crwdne215932:0", "app.errors.resending_code_failed": "crwdns1211600:0crwdne1211600:0", "app.errors.slug_taken": "crwdns215934:0crwdne215934:0", "app.errors.tag_name_taken": "crwdns787433:0crwdne787433:0", "app.errors.title_multiloc_blank": "crwdns215936:0crwdne215936:0", "app.errors.title_multiloc_includes_banned_words": "crwdns1159864:0crwdne1159864:0", "app.errors.token_invalid": "crwdns215938:0{passwordResetLink}crwdne215938:0", "app.errors.too_common": "crwdns215940:0crwdne215940:0", "app.errors.too_long": "crwdns215942:0crwdne215942:0", "app.errors.too_short": "crwdns215944:0crwdne215944:0", "app.errors.uncaught_error": "crwdns2245858:0crwdne2245858:0", "app.errors.unknown_group": "crwdns215946:0{value}crwdnd215946:0{row}crwdne215946:0", "app.errors.unknown_locale": "crwdns215948:0{value}crwdnd215948:0{row}crwdne215948:0", "app.errors.unparseable_excel": "crwdns215950:0crwdne215950:0", "app.errors.url": "crwdns1032377:0crwdne1032377:0", "app.errors.verification_taken": "crwdns2999243:0crwdne2999243:0", "app.errors.view_name_taken": "crwdns215952:0crwdne215952:0", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "crwdns215954:0crwdne215954:0", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "crwdns215956:0crwdne215956:0", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "crwdns215958:0crwdne215958:0", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "crwdns215960:0crwdne215960:0", "app.modules.id_cow.cancel": "crwdns215962:0crwdne215962:0", "app.modules.id_cow.emptyFieldError": "crwdns215964:0crwdne215964:0", "app.modules.id_cow.helpAltText": "crwdns215966:0crwdne215966:0", "app.modules.id_cow.invalidIdSerialError": "crwdns215968:0crwdne215968:0", "app.modules.id_cow.invalidRunError": "crwdns215970:0crwdne215970:0", "app.modules.id_cow.noMatchFormError": "crwdns215972:0crwdne215972:0", "app.modules.id_cow.notEntitledFormError": "crwdns215974:0crwdne215974:0", "app.modules.id_cow.showCOWHelp": "crwdns215976:0crwdne215976:0", "app.modules.id_cow.somethingWentWrongError": "crwdns215978:0crwdne215978:0", "app.modules.id_cow.submit": "crwdns215980:0crwdne215980:0", "app.modules.id_cow.takenFormError": "crwdns215982:0crwdne215982:0", "app.modules.id_cow.verifyCow": "crwdns215984:0crwdne215984:0", "app.modules.id_franceconnect.verificationButtonAltText": "crwdns215988:0crwdne215988:0", "app.modules.id_gent_rrn.cancel": "crwdns215990:0crwdne215990:0", "app.modules.id_gent_rrn.emptyFieldError": "crwdns215992:0crwdne215992:0", "app.modules.id_gent_rrn.gentRrnHelp": "crwdns215994:0crwdne215994:0", "app.modules.id_gent_rrn.invalidRrnError": "crwdns215996:0crwdne215996:0", "app.modules.id_gent_rrn.noMatchFormError": "crwdns215998:0crwdne215998:0", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "crwdns216000:0crwdne216000:0", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "crwdns216002:0crwdne216002:0", "app.modules.id_gent_rrn.rrnLabel": "crwdns216004:0crwdne216004:0", "app.modules.id_gent_rrn.rrnTooltip": "crwdns216006:0crwdne216006:0", "app.modules.id_gent_rrn.showGentRrnHelp": "crwdns216008:0crwdne216008:0", "app.modules.id_gent_rrn.somethingWentWrongError": "crwdns216010:0crwdne216010:0", "app.modules.id_gent_rrn.submit": "crwdns216012:0crwdne216012:0", "app.modules.id_gent_rrn.takenFormError": "crwdns216014:0crwdne216014:0", "app.modules.id_gent_rrn.verifyGentRrn": "crwdns216016:0crwdne216016:0", "app.modules.id_id_card_lookup.cancel": "crwdns216020:0crwdne216020:0", "app.modules.id_id_card_lookup.emptyFieldError": "crwdns216022:0crwdne216022:0", "app.modules.id_id_card_lookup.helpAltText": "crwdns216024:0crwdne216024:0", "app.modules.id_id_card_lookup.invalidCardIdError": "crwdns216026:0crwdne216026:0", "app.modules.id_id_card_lookup.noMatchFormError": "crwdns216028:0crwdne216028:0", "app.modules.id_id_card_lookup.showHelp": "crwdns216030:0crwdne216030:0", "app.modules.id_id_card_lookup.somethingWentWrongError": "crwdns216032:0crwdne216032:0", "app.modules.id_id_card_lookup.submit": "crwdns216034:0crwdne216034:0", "app.modules.id_id_card_lookup.takenFormError": "crwdns216036:0crwdne216036:0", "app.modules.id_oostende_rrn.cancel": "crwdns216040:0crwdne216040:0", "app.modules.id_oostende_rrn.emptyFieldError": "crwdns216042:0crwdne216042:0", "app.modules.id_oostende_rrn.invalidRrnError": "crwdns216044:0crwdne216044:0", "app.modules.id_oostende_rrn.noMatchFormError": "crwdns216046:0crwdne216046:0", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "crwdns216048:0crwdne216048:0", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "crwdns216050:0crwdne216050:0", "app.modules.id_oostende_rrn.oostendeRrnHelp": "crwdns216052:0crwdne216052:0", "app.modules.id_oostende_rrn.rrnLabel": "crwdns216054:0crwdne216054:0", "app.modules.id_oostende_rrn.rrnTooltip": "crwdns216056:0crwdne216056:0", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "crwdns216058:0crwdne216058:0", "app.modules.id_oostende_rrn.somethingWentWrongError": "crwdns216060:0crwdne216060:0", "app.modules.id_oostende_rrn.submit": "crwdns216062:0crwdne216062:0", "app.modules.id_oostende_rrn.takenFormError": "crwdns216064:0crwdne216064:0", "app.modules.id_oostende_rrn.verifyOostendeRrn": "crwdns216066:0crwdne216066:0", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "crwdns216070:0{folderName}crwdne216070:0", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "crwdns216072:0crwdne216072:0", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "crwdns216074:0{folderUrl}crwdne216074:0", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "crwdns216076:0{projectFolderName}crwdnd216076:0{orgName}crwdne216076:0", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "crwdns216080:0{projectFolderName}crwdne216080:0", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "crwdns216082:0{projectFolderName}crwdnd216082:0{orgName}crwdne216082:0", "app.sessionRecording.accept": "crwdns1508646:0crwdne1508646:0", "app.sessionRecording.modalDescription1": "crwdns1508648:0crwdne1508648:0", "app.sessionRecording.modalDescription2": "crwdns1508650:0crwdne1508650:0", "app.sessionRecording.modalDescription3": "crwdns1508652:0crwdne1508652:0", "app.sessionRecording.modalDescriptionFaq": "crwdns3337403:0crwdne3337403:0", "app.sessionRecording.modalTitle": "crwdns1508654:0crwdne1508654:0", "app.sessionRecording.reject": "crwdns1508656:0crwdne1508656:0", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "crwdns216084:0crwdne216084:0", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "crwdns649427:0crwdne649427:0", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "crwdns216086:0crwdne216086:0", "app.utils.AdminPage.ProjectEdit.createPoll": "crwdns216088:0crwdne216088:0", "app.utils.AdminPage.ProjectEdit.createSurveyText": "crwdns216090:0crwdne216090:0", "app.utils.AdminPage.ProjectEdit.findVolunteers": "crwdns216092:0crwdne216092:0", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "crwdns216094:0crwdne216094:0", "app.utils.AdminPage.ProjectEdit.shareInformation": "crwdns216096:0crwdne216096:0", "app.utils.FormattedCurrency.credits": "crwdns216098:0crwdne216098:0", "app.utils.FormattedCurrency.tokens": "crwdns216104:0crwdne216104:0", "app.utils.FormattedCurrency.xCredits": "crwdns4132543:0numberOfCredits={numberOfCredits}crwdne4132543:0", "app.utils.FormattedCurrency.xTokens": "crwdns4132545:0numberOfTokens={numberOfTokens}crwdne4132545:0", "app.utils.IdeaCards.mostDiscussed": "crwdns3645017:0crwdne3645017:0", "app.utils.IdeaCards.mostReacted": "crwdns777679:0crwdne777679:0", "app.utils.IdeaCards.newest": "crwdns216108:0crwdne216108:0", "app.utils.IdeaCards.oldest": "crwdns216110:0crwdne216110:0", "app.utils.IdeaCards.random": "crwdns216112:0crwdne216112:0", "app.utils.IdeaCards.trending": "crwdns216114:0crwdne216114:0", "app.utils.IdeasNewPage.contributionFormTitle": "crwdns216116:0crwdne216116:0", "app.utils.IdeasNewPage.ideaFormTitle": "crwdns216118:0crwdne216118:0", "app.utils.IdeasNewPage.initiativeFormTitle": "crwdns3335249:0crwdne3335249:0", "app.utils.IdeasNewPage.issueFormTitle1": "crwdns2924713:0crwdne2924713:0", "app.utils.IdeasNewPage.optionFormTitle": "crwdns216122:0crwdne216122:0", "app.utils.IdeasNewPage.petitionFormTitle": "crwdns3335251:0crwdne3335251:0", "app.utils.IdeasNewPage.projectFormTitle": "crwdns216124:0crwdne216124:0", "app.utils.IdeasNewPage.proposalFormTitle": "crwdns3335253:0crwdne3335253:0", "app.utils.IdeasNewPage.questionFormTitle": "crwdns216126:0crwdne216126:0", "app.utils.IdeasNewPage.surveyTitle": "crwdns216128:0crwdne216128:0", "app.utils.IdeasNewPage.viewYourComment": "crwdns4243977:0crwdne4243977:0", "app.utils.IdeasNewPage.viewYourContribution": "crwdns4243979:0crwdne4243979:0", "app.utils.IdeasNewPage.viewYourIdea": "crwdns4243981:0crwdne4243981:0", "app.utils.IdeasNewPage.viewYourInitiative": "crwdns4243983:0crwdne4243983:0", "app.utils.IdeasNewPage.viewYourInput": "crwdns4747537:0crwdne4747537:0", "app.utils.IdeasNewPage.viewYourIssue": "crwdns4243985:0crwdne4243985:0", "app.utils.IdeasNewPage.viewYourOption": "crwdns4243987:0crwdne4243987:0", "app.utils.IdeasNewPage.viewYourPetition": "crwdns4243989:0crwdne4243989:0", "app.utils.IdeasNewPage.viewYourProject": "crwdns4243991:0crwdne4243991:0", "app.utils.IdeasNewPage.viewYourProposal": "crwdns4243993:0crwdne4243993:0", "app.utils.IdeasNewPage.viewYourQuestion": "crwdns4243995:0crwdne4243995:0", "app.utils.Projects.sendSubmission": "crwdns4419142:0crwdne4419142:0", "app.utils.Projects.sendSurveySubmission": "crwdns4004109:0crwdne4004109:0", "app.utils.Projects.surveySubmission": "crwdns4004111:0crwdne4004111:0", "app.utils.Projects.yourResponseHasTheFollowingId": "crwdns4004113:0{identifier}crwdne4004113:0", "app.utils.Promessagesjects.ifYouLaterDecide": "crwdns4004115:0crwdne4004115:0", "app.utils.actionDescriptors.attendingEventMissingRequirements": "crwdns2677511:0crwdne2677511:0", "app.utils.actionDescriptors.attendingEventNotInGroup": "crwdns2677513:0crwdne2677513:0", "app.utils.actionDescriptors.attendingEventNotPermitted": "crwdns2677515:0crwdne2677515:0", "app.utils.actionDescriptors.attendingEventNotSignedIn": "crwdns2677517:0crwdne2677517:0", "app.utils.actionDescriptors.attendingEventNotVerified": "crwdns2677519:0crwdne2677519:0", "app.utils.actionDescriptors.volunteeringMissingRequirements": "crwdns2677569:0crwdne2677569:0", "app.utils.actionDescriptors.volunteeringNotInGroup": "crwdns2677571:0crwdne2677571:0", "app.utils.actionDescriptors.volunteeringNotPermitted": "crwdns2677573:0crwdne2677573:0", "app.utils.actionDescriptors.volunteeringNotSignedIn": "crwdns2677575:0crwdne2677575:0", "app.utils.actionDescriptors.volunteeringNotVerified": "crwdns2677577:0crwdne2677577:0", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "crwdns2677579:0{completeRegistrationLink}crwdne2677579:0", "app.utils.errors.api_error_default.in": "crwdns216130:0crwdne216130:0", "app.utils.errors.default.ajv_error_birthyear_required": "crwdns216132:0crwdne216132:0", "app.utils.errors.default.ajv_error_date_any": "crwdns216134:0crwdne216134:0", "app.utils.errors.default.ajv_error_domicile_required": "crwdns216136:0crwdne216136:0", "app.utils.errors.default.ajv_error_gender_required": "crwdns216140:0crwdne216140:0", "app.utils.errors.default.ajv_error_invalid": "crwdns216142:0crwdne216142:0", "app.utils.errors.default.ajv_error_maxItems": "crwdns216144:0limit={limit}crwdne216144:0", "app.utils.errors.default.ajv_error_minItems": "crwdns216146:0limit={limit}crwdne216146:0", "app.utils.errors.default.ajv_error_number_any": "crwdns216148:0crwdne216148:0", "app.utils.errors.default.ajv_error_politician_required": "crwdns216150:0crwdne216150:0", "app.utils.errors.default.ajv_error_required3": "crwdns2503666:0{fieldName}crwdne2503666:0", "app.utils.errors.default.ajv_error_type": "crwdns216154:0crwdne216154:0", "app.utils.errors.default.api_error_accepted": "crwdns216156:0crwdne216156:0", "app.utils.errors.default.api_error_blank": "crwdns216158:0crwdne216158:0", "app.utils.errors.default.api_error_confirmation": "crwdns216160:0crwdne216160:0", "app.utils.errors.default.api_error_empty": "crwdns216162:0crwdne216162:0", "app.utils.errors.default.api_error_equal_to": "crwdns216164:0crwdne216164:0", "app.utils.errors.default.api_error_even": "crwdns216166:0crwdne216166:0", "app.utils.errors.default.api_error_exclusion": "crwdns216168:0crwdne216168:0", "app.utils.errors.default.api_error_greater_than": "crwdns216170:0crwdne216170:0", "app.utils.errors.default.api_error_greater_than_or_equal_to": "crwdns216172:0crwdne216172:0", "app.utils.errors.default.api_error_inclusion": "crwdns216174:0crwdne216174:0", "app.utils.errors.default.api_error_invalid": "crwdns216176:0crwdne216176:0", "app.utils.errors.default.api_error_less_than": "crwdns216178:0crwdne216178:0", "app.utils.errors.default.api_error_less_than_or_equal_to": "crwdns216180:0crwdne216180:0", "app.utils.errors.default.api_error_not_a_number": "crwdns216182:0crwdne216182:0", "app.utils.errors.default.api_error_not_an_integer": "crwdns216184:0crwdne216184:0", "app.utils.errors.default.api_error_other_than": "crwdns216186:0crwdne216186:0", "app.utils.errors.default.api_error_present": "crwdns216188:0crwdne216188:0", "app.utils.errors.default.api_error_too_long": "crwdns216190:0crwdne216190:0", "app.utils.errors.default.api_error_too_short": "crwdns216192:0crwdne216192:0", "app.utils.errors.default.api_error_wrong_length": "crwdns216194:0crwdne216194:0", "app.utils.errors.defaultapi_error_.odd": "crwdns216196:0crwdne216196:0", "app.utils.notInGroup": "crwdns576953:0crwdne576953:0", "app.utils.participationMethod.onSurveySubmission": "crwdns216198:0crwdne216198:0", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "crwdns3338607:0crwdne3338607:0", "app.utils.participationMethodConfig.voting.votingNotInGroup": "crwdns2411982:0crwdne2411982:0", "app.utils.participationMethodConfig.voting.votingNotPermitted": "crwdns2411984:0crwdne2411984:0", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "crwdns2411986:0crwdne2411986:0", "app.utils.participationMethodConfig.voting.votingNotVerified": "crwdns2411988:0crwdne2411988:0", "app.utils.votingMethodUtils.budgetParticipationEnded1": "crwdns4132583:0{endDate}crwdnd4132583:0{maxBudget}crwdnd4132583:0{optionCount}crwdne4132583:0", "app.utils.votingMethodUtils.budgetSubmitted": "crwdns777685:0crwdne777685:0", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "crwdns777687:0crwdne777687:0", "app.utils.votingMethodUtils.budgetingNotInGroup": "crwdns2411990:0crwdne2411990:0", "app.utils.votingMethodUtils.budgetingNotPermitted": "crwdns2411992:0crwdne2411992:0", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "crwdns2411994:0crwdne2411994:0", "app.utils.votingMethodUtils.budgetingNotVerified": "crwdns2411996:0crwdne2411996:0", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "crwdns777689:0crwdne777689:0", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "crwdns4132553:0{amount}crwdne4132553:0", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "crwdns777693:0crwdne777693:0", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "crwdns777695:0crwdne777695:0", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "crwdns4132555:0{maxBudget}crwdnd4132555:0{optionCount}crwdne4132555:0", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "crwdns777699:0{endDate}crwdne777699:0", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "crwdns1294634:0crwdne1294634:0", "app.utils.votingMethodUtils.castYourVote": "crwdns777705:0crwdne777705:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "crwdns4916153:0maxVotes={maxVotes}crwdne4916153:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "crwdns4916155:0maxVotes={maxVotes}crwdne4916155:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "crwdns4916157:0maxVotes={maxVotes}crwdne4916157:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "crwdns4916159:0maxVotes={maxVotes}crwdne4916159:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "crwdns777709:0crwdne777709:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "crwdns2147470:0crwdne2147470:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "crwdns4916161:0totalVotes={totalVotes}crwdnd4916161:0optionCount={optionCount}crwdne4916161:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "crwdns4916163:0totalVotes={totalVotes}crwdnd4916163:0optionCount={optionCount}crwdne4916163:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "crwdns4916165:0totalVotes={totalVotes}crwdnd4916165:0optionCount={optionCount}crwdne4916165:0", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "crwdns4916167:0totalVotes={totalVotes}crwdnd4916167:0optionCount={optionCount}crwdne4916167:0", "app.utils.votingMethodUtils.finalResults": "crwdns777715:0crwdne777715:0", "app.utils.votingMethodUtils.finalTally": "crwdns777717:0crwdne777717:0", "app.utils.votingMethodUtils.howToParticipate": "crwdns777719:0crwdne777719:0", "app.utils.votingMethodUtils.howToVote": "crwdns777721:0crwdne777721:0", "app.utils.votingMethodUtils.multipleVotingEnded1": "crwdns4916169:0{endDate}crwdne4916169:0", "app.utils.votingMethodUtils.numberOfCredits": "crwdns4916171:0numberOfVotes={numberOfVotes}crwdne4916171:0", "app.utils.votingMethodUtils.numberOfPoints": "crwdns4916173:0numberOfVotes={numberOfVotes}crwdne4916173:0", "app.utils.votingMethodUtils.numberOfTokens": "crwdns4916175:0numberOfVotes={numberOfVotes}crwdne4916175:0", "app.utils.votingMethodUtils.numberOfVotes1": "crwdns4916177:0numberOfVotes={numberOfVotes}crwdne4916177:0", "app.utils.votingMethodUtils.results": "crwdns777725:0crwdne777725:0", "app.utils.votingMethodUtils.singleVotingEnded": "crwdns777727:0{endDate}crwdnd777727:0{maxVotes}crwdne777727:0", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "crwdns777729:0crwdne777729:0", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "crwdns777731:0{totalVotes}crwdne777731:0", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "crwdns777733:0crwdne777733:0", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "crwdns777735:0{endDate}crwdne777735:0", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "crwdns777737:0crwdne777737:0", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "crwdns777739:0crwdne777739:0", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "crwdns777741:0{endDate}crwdne777741:0", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "crwdns777743:0crwdne777743:0", "app.utils.votingMethodUtils.submitYourBudget": "crwdns777745:0crwdne777745:0", "app.utils.votingMethodUtils.submittedBudgetCountText2": "crwdns3549655:0crwdne3549655:0", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "crwdns3549657:0crwdne3549657:0", "app.utils.votingMethodUtils.submittedVoteCountText2": "crwdns3549659:0crwdne3549659:0", "app.utils.votingMethodUtils.submittedVotesCountText2": "crwdns3549661:0crwdne3549661:0", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "crwdns777757:0crwdne777757:0", "app.utils.votingMethodUtils.votesCast": "crwdns777761:0crwdne777761:0", "app.utils.votingMethodUtils.votingClosed": "crwdns777763:0crwdne777763:0", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "crwdns4916179:0crwdne4916179:0", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "crwdns4916181:0{endDate}crwdne4916181:0", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "crwdns4916183:0crwdne4916183:0", "components.UI.IdeaSelect.noIdeaAvailable": "crwdns1761136:0crwdne1761136:0", "components.UI.IdeaSelect.selectIdea": "crwdns1761138:0crwdne1761138:0", "containers.SiteMap.allProjects": "crwdns216200:0crwdne216200:0", "containers.SiteMap.customPageSection": "crwdns216202:0crwdne216202:0", "containers.SiteMap.folderInfo": "crwdns216204:0crwdne216204:0", "containers.SiteMap.headSiteMapTitle": "crwdns2747171:0{orgName}crwdne2747171:0", "containers.SiteMap.homeSection": "crwdns216206:0crwdne216206:0", "containers.SiteMap.pageContents": "crwdns216214:0crwdne216214:0", "containers.SiteMap.profilePage": "crwdns216216:0crwdne216216:0", "containers.SiteMap.profileSettings": "crwdns216218:0crwdne216218:0", "containers.SiteMap.projectEvents": "crwdns216220:0crwdne216220:0", "containers.SiteMap.projectIdeas": "crwdns216222:0crwdne216222:0", "containers.SiteMap.projectInfo": "crwdns216224:0crwdne216224:0", "containers.SiteMap.projectPoll": "crwdns216226:0crwdne216226:0", "containers.SiteMap.projectSurvey": "crwdns216228:0crwdne216228:0", "containers.SiteMap.projectsArchived": "crwdns216230:0crwdne216230:0", "containers.SiteMap.projectsCurrent": "crwdns216232:0crwdne216232:0", "containers.SiteMap.projectsDraft": "crwdns216234:0crwdne216234:0", "containers.SiteMap.projectsSection": "crwdns216236:0{orgName}crwdne216236:0", "containers.SiteMap.signInPage": "crwdns216238:0crwdne216238:0", "containers.SiteMap.signUpPage": "crwdns216240:0crwdne216240:0", "containers.SiteMap.siteMapDescription": "crwdns216242:0crwdne216242:0", "containers.SiteMap.siteMapTitle": "crwdns216244:0{orgName}crwdne216244:0", "containers.SiteMap.successStories": "crwdns216246:0crwdne216246:0", "containers.SiteMap.timeline": "crwdns216248:0crwdne216248:0", "containers.SiteMap.userSpaceSection": "crwdns216250:0crwdne216250:0", "containers.SubscriptionEndedPage.accessDenied": "crwdns216252:0crwdne216252:0", "containers.SubscriptionEndedPage.subscriptionEnded": "crwdns216254:0crwdne216254:0"}