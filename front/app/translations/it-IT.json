{"EmailSettingsPage.emailSettings": "Impostazioni e-mail", "EmailSettingsPage.initialUnsubscribeError": "C'è stato un problema nel cancellarsi da questa campagna, per favore riprova.", "EmailSettingsPage.initialUnsubscribeLoading": "La sua richiesta è in fase di elaborazione, la preghiamo di attendere...", "EmailSettingsPage.initialUnsubscribeSuccess": "Ti sei cancellato con successo da {campaignTitle}.", "UI.FormComponents.optional": "opzionale", "app.closeIconButton.a11y_buttonActionMessage": "<PERSON><PERSON><PERSON>", "app.components.Areas.areaUpdateError": "Si è verificato un errore durante il salvataggio dell'area. Riprova.", "app.components.Areas.followedArea": "Area seguita: {areaTitle}", "app.components.Areas.followedTopic": "<PERSON>rgo<PERSON> seguito: {topicTitle}", "app.components.Areas.topicUpdateError": "Si è verificato un errore durante il salvataggio dell'argomento. Riprova.", "app.components.Areas.unfollowedArea": "Area non seguita: {areaTitle}", "app.components.Areas.unfollowedTopic": "Argomento non seguito: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Prezzo:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.added": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.addVote": "Aggiungi voto", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Hai distribuito tutti i tuoi crediti.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Hai distribuito il numero massimo di crediti per questa opzione.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Hai distribuito tutti i tuoi punti.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Hai distribuito il numero massimo di punti per questa opzione.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Hai distribuito tutti i tuoi gettoni.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Hai distribuito il numero massimo di gettoni per questa opzione.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Hai distribuito tutti i tuoi voti.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Hai distribuito il numero massimo di voti per questa opzione.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Il voto non è disponibile, poiché questa fase non è attiva.", "app.components.AssignMultipleVotesControl.removeVote": "R<PERSON><PERSON><PERSON> il voto", "app.components.AssignMultipleVotesControl.select": "Seleziona", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Hai già inviato il tuo voto. Per modificarlo, clicca su \"Modifica il tuo voto\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Hai già inviato il tuo voto. Per modificarlo, torna alla pagina del progetto e clicca su \"Modifica il tuo voto\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credito} other {crediti}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {punto} other {punti}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vota} other {vota}}", "app.components.AssignVoteControl.maxVotesReached1": "Hai distribuito tutti i tuoi voti.", "app.components.AssignVoteControl.phaseNotActive": "Il voto non è disponibile, poiché questa fase non è attiva.", "app.components.AssignVoteControl.select": "Seleziona", "app.components.AssignVoteControl.selected2": "Selezionato", "app.components.AssignVoteControl.voteForAtLeastOne": "Vota per almeno 1 opzione", "app.components.AssignVoteControl.votesSubmitted1": "Hai già inviato il tuo voto. Per modificarlo, clicca su \"Modifica il tuo voto\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Hai già inviato il tuo voto. Per modificarlo, torna alla pagina del progetto e clicca su \"Modifica il tuo voto\".", "app.components.AuthProviders.continue": "Continua", "app.components.AuthProviders.continueWithAzure": "Continua con {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Continua con Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Continua con il falso SSO", "app.components.AuthProviders.continueWithGoogle": "Continua con Google", "app.components.AuthProviders.continueWithHoplr": "Continua con Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Continua con ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Continua con {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continua con MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Esiste già un account con questo indirizzo e-mail.{br}{br}Non è possibile accedere alla piattaforma utilizzando FranceConnect poiché i dati personali non corrispondono. Per accedere utilizzando FranceConnect, è necessario prima cambiare il nome o il cognome su questa piattaforma in modo che corrisponda ai dati ufficiali.{br}{br}Puoi accedere come fai normalmente qui sotto.", "app.components.AuthProviders.goToLogIn": "Hai già un account? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Non hai un account? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Accedi", "app.components.AuthProviders.logInWithEmail": "Accedi con l'e-mail", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Per essere verificato devi avere l'età minima specificata o superiore.", "app.components.AuthProviders.signUp2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.signUpButtonAltText": "Is<PERSON>rivi<PERSON> con {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Iscriviti con l'e-mail", "app.components.AuthProviders.verificationRequired": "Verifica necessaria", "app.components.Author.a11yPostedBy": "<PERSON><PERSON>", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 partecipante} other {{numberOfParticipants} partecipanti}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} utenti", "app.components.AvatarBubbles.participant": "partecipante", "app.components.AvatarBubbles.participants1": "partecipanti", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Non è possibile commentare nella fase attuale.", "app.components.Comments.commentingDisabledInactiveProject": "Non è possibile commentare perché questo progetto non è attualmente attivo.", "app.components.Comments.commentingDisabledProject": "I commenti in questo progetto sono attualmente disabilitati.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} per commentare.", "app.components.Comments.commentingMaybeNotPermitted": "Per favore {signInLink} per vedere quali azioni possono essere intraprese.", "app.components.Comments.inputsAssociatedWithProfile": "Per impostazione predefinita i tuoi invii saranno associati al tuo profilo, a meno che tu non selezioni questa opzione.", "app.components.Comments.invisibleTitleComments": "Commenti", "app.components.Comments.leastRecent": "<PERSON>o recente", "app.components.Comments.likeComment": "Come questo commento", "app.components.Comments.mostLiked": "La maggior parte delle reazioni", "app.components.Comments.mostRecent": "Il più recente", "app.components.Comments.official": "Ufficiale", "app.components.Comments.postAnonymously": "Pubblica in forma anonima", "app.components.Comments.replyToComment": "Rispondi al commento", "app.components.Comments.reportAsSpam": "<PERSON><PERSON><PERSON> come spam", "app.components.Comments.seeOriginal": "Vedi originale", "app.components.Comments.seeTranslation": "Vedere la traduzione", "app.components.Comments.yourComment": "Il tuo commento", "app.components.CommonGroundResults.divisiveDescription": "Affermazioni in cui le persone sono d'accordo e in disaccordo allo stesso modo:", "app.components.CommonGroundResults.divisiveTitle": "Divisivo", "app.components.CommonGroundResults.majorityDescription": "Più del 60% ha votato in un senso o nell'altro su quanto segue:", "app.components.CommonGroundResults.majorityTitle": "Maggioranza", "app.components.CommonGroundResults.participantLabel": "partecipante", "app.components.CommonGroundResults.participantsLabel1": "partecipanti", "app.components.CommonGroundResults.statementLabel": "dichiarazione", "app.components.CommonGroundResults.statementsLabel1": "dichiarazioni", "app.components.CommonGroundResults.votesLabe": "voto", "app.components.CommonGroundResults.votesLabel1": "voti", "app.components.CommonGroundStatements.agreeLabel": "Accetta", "app.components.CommonGroundStatements.disagreeLabel": "Non sono d'accordo", "app.components.CommonGroundStatements.noMoreStatements": "Non ci sono dichiarazioni a cui rispondere in questo momento", "app.components.CommonGroundStatements.noResults": "Non ci sono ancora risultati da mostrare. Assicurati di aver partecipato alla fase Common Ground e controlla qui dopo.", "app.components.CommonGroundStatements.unsureLabel": "Incerto", "app.components.CommonGroundTabs.resultsTabLabel": "Risultati", "app.components.CommonGroundTabs.statementsTabLabel": "Dichiarazioni", "app.components.CommunityMonitorModal.formError": "Si è verificato un errore.", "app.components.CommunityMonitorModal.surveyDescription2": "<PERSON><PERSON> sonda<PERSON> in corso analizza le tue opinioni sulla governance e sui servizi pubblici.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Ci vuole <1 minuto} one {Ci vuole 1 minuto} other {Ci vogliono # minuti}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Una mail con un codice di conferma è stata inviata a {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Cambia la tua email.", "app.components.ConfirmationModal.codeInput": "Codice", "app.components.ConfirmationModal.confirmationCodeSent": "Nuovo codice inviato", "app.components.ConfirmationModal.didntGetAnEmail": "Non hai ricevuto un'e-mail?", "app.components.ConfirmationModal.foundYourCode": "Hai trovato il tuo codice?", "app.components.ConfirmationModal.goBack": "Torna indietro.", "app.components.ConfirmationModal.sendEmailWithCode": "Invia un'e-mail con il codice", "app.components.ConfirmationModal.sendNewCode": "Invia un nuovo codice.", "app.components.ConfirmationModal.verifyAndContinue": "Verifica e continua", "app.components.ConfirmationModal.wrongEmail": "Un'email sbagliata?", "app.components.ConsentManager.Banner.accept": "Accettare", "app.components.ConsentManager.Banner.ariaButtonClose2": "Rifiuto della politica e chiusura del banner", "app.components.ConsentManager.Banner.close": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "Navigando, accetti il nostro {policyLink}.", "app.components.ConsentManager.Banner.manage": "Gestire", "app.components.ConsentManager.Banner.policyLink": "Politica dei cookie", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Pubblicità", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Lo utilizziamo per personalizzare e misurare l'efficacia delle campagne pubblicitarie del nostro sito. Non mostreremo alcuna pubblicità su questa piattaforma, ma i seguenti servizi potrebbero offrirti un annuncio personalizzato in base alle pagine che visiti sul nostro sito.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analytics", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Utilizziamo questo tracciamento per capire meglio come utilizzi la piattaforma al fine di imparare e migliorare la navigazione. Queste informazioni sono utilizzate solo in analisi di massa, e in nessun modo per tracciare le singole persone.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Torna indietro", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Can<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Disabilita", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funzionale", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Questo è necessario per abilitare e monitorare le funzionalità di base del sito web. Alcuni strumenti elencati qui potrebbero non essere applicabili a te. Si prega di leggere la nostra politica sui cookie per ulteriori informazioni.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Per avere una piattaforma funzionale, salviamo un cookie di autenticazione se ti iscrivi, e la lingua in cui usi questa piattaforma.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Le tue preferenze sui cookie", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Strumenti", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Esclusione di responsabilità per il caricamento dei contenuti", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Caricando un contenuto, dichiari che questo contenuto non viola alcuna norma o diritto di terzi, come ad esempio i diritti di proprietà intellettuale, i diritti alla privacy, i diritti ai segreti commerciali e così via. Di conseguenza, caricando questo contenuto, ti impegni ad assumerti la piena ed esclusiva responsabilità per tutti i danni diretti e indiretti derivanti dal contenuto caricato. Inoltre, l'utente si impegna a tenere indenne il proprietario della piattaforma e Go Vocal da qualsiasi reclamo o responsabilità di terzi nei confronti di terzi, e da qualsiasi costo associato, che possa sorgere o derivare dal contenuto caricato.", "app.components.ContentUploadDisclaimer.onAccept": "Capisco", "app.components.ContentUploadDisclaimer.onCancel": "Annullamento", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON>", "app.components.CustomFieldsForm.addressInputAriaLabel": "Indirizzo di ingresso", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Inserisci un indirizzo...", "app.components.CustomFieldsForm.adminFieldTooltip": "Campo visibile solo agli amministratori", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "<PERSON>tte le risposte a questo sondaggio sono anonime.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Per un poligono sono necessari almeno tre punti.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Per una linea sono necessari almeno due punti.", "app.components.CustomFieldsForm.attachmentRequired": "È richiesto almeno un allegato", "app.components.CustomFieldsForm.authorFieldLabel": "Autore", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Inizia a digitare per cercare l'email o il nome dell'utente...", "app.components.CustomFieldsForm.back": "Indietro", "app.components.CustomFieldsForm.budgetFieldLabel": "Bilancio", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Clicca sulla mappa per disegnare. Poi, trascina i punti per spostarli.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Clicca sulla mappa o digita un indirizzo qui sotto per aggiungere la tua risposta.", "app.components.CustomFieldsForm.confirm": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.descriptionMinLength": "La descrizione deve avere una lunghezza minima di {min} caratteri.", "app.components.CustomFieldsForm.descriptionRequired": "La descrizione è necessaria", "app.components.CustomFieldsForm.fieldMaximumItems": "Al massimo {maxSelections, plural, one {# opzione} other {# opzioni}} possono essere selezionate per il campo \"{fieldName}\".", "app.components.CustomFieldsForm.fieldMinimumItems": "Almeno {minSelections, plural, one {# opzione} other {# opzioni}} possono essere selezionate per il campo \"{fieldName}\".", "app.components.CustomFieldsForm.fieldRequired": "Il campo \"{fieldName}\" è obbligatorio", "app.components.CustomFieldsForm.fileSizeLimit": "Il limite di dimensione dei file è {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "L'immagine è necessaria", "app.components.CustomFieldsForm.minimumCoordinates2": "È richiesto un minimo di {numPoints} punti mappa.", "app.components.CustomFieldsForm.notPublic1": "*Questa risposta sarà condivisa solo con i responsabili del progetto e non con il pubblico.", "app.components.CustomFieldsForm.otherArea": "Da qualche altra parte", "app.components.CustomFieldsForm.progressBarLabel": "Progressi", "app.components.CustomFieldsForm.removeAnswer": "Rimuovi la risposta", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Seleziona tutti quelli che vuoi", "app.components.CustomFieldsForm.selectBetween": "*Seleziona tra le opzioni {minItems} e {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*Seleziona esattamente {selectExactly, plural, one {# opzione} other {# opzioni}}", "app.components.CustomFieldsForm.selectMany": "*Scegli quanti ne vuoi", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tocca la mappa per disegnare. Poi, trascina i punti per spostarli.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tocca la mappa per disegnare.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tocca la mappa per aggiungere la tua risposta.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Tocca la mappa o digita un indirizzo qui sotto per aggiungere la tua risposta.", "app.components.CustomFieldsForm.tapToAddALine": "Tocca per aggiungere una riga", "app.components.CustomFieldsForm.tapToAddAPoint": "Tocca per aggiungere un punto", "app.components.CustomFieldsForm.tapToAddAnArea": "Tocca per aggiungere un'area", "app.components.CustomFieldsForm.titleMaxLength": "Il titolo deve avere una lunghezza massima di {max} caratteri.", "app.components.CustomFieldsForm.titleMinLength": "Il titolo deve avere una lunghezza minima di {min} caratteri.", "app.components.CustomFieldsForm.titleRequired": "Il titolo è obbligatorio", "app.components.CustomFieldsForm.topicRequired": "È richiesto almeno un tag", "app.components.CustomFieldsForm.typeYourAnswer": "Scrivi la tua risposta", "app.components.CustomFieldsForm.typeYourAnswerRequired": "È necessario digitare la risposta", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Carica un file zip contenente uno o più shapefile.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Se la posizione non viene visualizzata tra le opzioni durante la digitazione, puoi aggiungere delle coordinate valide nel formato \"latitudine, longitudine\" per specificare una posizione precisa (ad esempio: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Alcuni campi non sono validi. Si prega di correggere gli errori e riprovare.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Si è verificato un errore sconosciuto durante l'invio del tuo rapporto. Si prega di riprovare.", "app.components.ErrorBoundary.errorFormLabelClose": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "Che cosa è successo?", "app.components.ErrorBoundary.errorFormLabelEmail": "Email", "app.components.ErrorBoundary.errorFormLabelName": "Nome", "app.components.ErrorBoundary.errorFormLabelSubmit": "Invia", "app.components.ErrorBoundary.errorFormSubtitle": "Il nostro team è stato avvisato.", "app.components.ErrorBoundary.errorFormSubtitle2": "Se vuoi che ti aiutiamo, dicci cosa è successo qui sotto.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Il tuo feedback è stato inviato. Grazie!", "app.components.ErrorBoundary.errorFormTitle": "<PERSON><PERSON>ra che ci sia un problema.", "app.components.ErrorBoundary.genericErrorWithForm": "Si è verificato un errore e non possiamo visualizzare questo contenuto. Per favore rip<PERSON>, o {openForm}", "app.components.ErrorBoundary.openFormText": "aiutaci a capirlo", "app.components.ErrorToast.budgetExceededError": "Non hai un budget sufficiente", "app.components.ErrorToast.votesExceededError": "Non hai abbastanza voti a disposizione", "app.components.EventAttendanceButton.forwardToFriend": "Inoltra ad un amico", "app.components.EventAttendanceButton.maxRegistrationsReached": "Il numero massimo di iscrizioni all'evento è stato raggiunto. Non ci sono più posti disponibili.", "app.components.EventAttendanceButton.register": "Registro", "app.components.EventAttendanceButton.registered": "Registrato", "app.components.EventAttendanceButton.seeYouThere": "Ci vediamo lì!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON>i vediamo lì, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Meno informazioni sugli eventi sono diventate visibili.", "app.components.EventCard.a11y_moreContentVisible": "Altre informazioni sull'evento sono diventate visibili.", "app.components.EventCard.a11y_readMore": "Per saperne di più sull'evento \"{eventTitle}\".", "app.components.EventCard.endsAt": "Finisce a", "app.components.EventCard.readMore": "<PERSON><PERSON>i tutto", "app.components.EventCard.showLess": "<PERSON>ra meno", "app.components.EventCard.showMore": "Mostra di più", "app.components.EventCard.startsAt": "Inizia a", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Eventi imminenti e in corso nell'ambito di questo progetto", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Eventi imminenti e in corso in questa fase", "app.components.FileUploader.a11y_file": "File:", "app.components.FileUploader.a11y_filesToBeUploaded": "File da caricare: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Nessun file aggiunto.", "app.components.FileUploader.a11y_removeFile": "Rimuovi questo file", "app.components.FileUploader.fileInputDescription": "Clicca per selezionare un file", "app.components.FileUploader.fileUploadLabel": "Allegati (max. 50MB)", "app.components.FileUploader.file_too_large2": "Non sono ammessi file di dimensioni superiori a {maxSizeMb}MB.", "app.components.FileUploader.incorrect_extension": "{fileName} non è supportato dal nostro sistema, non sarà caricato.", "app.components.FilterBoxes.a11y_allFilterSelected": "Filtro di stato selezionato: tutti", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# presentazione} other {# presentazione}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON><PERSON><PERSON> filtro", "app.components.FilterBoxes.a11y_selectedFilter": "Filtro di stato selezionato: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Selezionato {numberOfSelectedTopics, plural, =0 {zero filtri argomento} one {un filtro argomento} other {# filtri argomento}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "<PERSON><PERSON>", "app.components.FilterBoxes.areas": "Filtrare per area", "app.components.FilterBoxes.inputs": "ingressi", "app.components.FilterBoxes.noValuesFound": "Nessun valore disponibile.", "app.components.FilterBoxes.showLess": "<PERSON>ra meno", "app.components.FilterBoxes.showTagsWithNumber": "<PERSON>ra tutti ({numberTags})", "app.components.FilterBoxes.statusTitle": "Stato", "app.components.FilterBoxes.topicsTitle": "Argomenti", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Descrizione della cartella:", "app.components.FolderFolderCard.a11y_folderTitle": "<PERSON><PERSON>:", "app.components.FolderFolderCard.archived": "Archiviato", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, one {# progetto} other {# progetti}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Il tipo di campo non può essere cambiato una volta che sono state inviate delle richieste.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tipo", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Salvataggio automatico", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Il salvataggio automatico è abilitato per impostazione predefinita quando apri l'editor del modulo. Ogni volta che chiudi il pannello delle impostazioni del campo utilizzando il pulsante \"X\", si attiverà automaticamente un salvataggio.", "app.components.GanttChart.timeRange.month": "Mese", "app.components.GanttChart.timeRange.quarter": "Trimestre", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Pluriennale", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "<PERSON><PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBack": "Torna indietro", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Torna alla pagina precedente", "app.components.HookForm.Feedback.errorTitle": "C'è un problema", "app.components.HookForm.Feedback.submissionError": "Riprovare. Se il problema persiste, contattaci", "app.components.HookForm.Feedback.submissionErrorTitle": "C'è stato un problema da parte nostra, siamo spiacenti", "app.components.HookForm.Feedback.successMessage": "<PERSON><PERSON>lo inviato correttamente", "app.components.HookForm.PasswordInput.passwordLabel": "Password", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON><PERSON><PERSON> a sinistra.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON><PERSON><PERSON> a destra.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} le idee sono state caricate.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "I più discussi", "app.components.IdeaCards.filters.newest": "Nuovo", "app.components.IdeaCards.filters.oldest": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.popular": "I più apprezzati", "app.components.IdeaCards.filters.random": "Casuale", "app.components.IdeaCards.filters.sortBy": "Ordina per", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "L'ordinamento è cambiato in: {currentSortType}", "app.components.IdeaCards.filters.trending": "Tendenza", "app.components.IdeaCards.showMore": "Mostra di più", "app.components.IdeasMap.a11y_hideIdeaCard": "Nascondere la carta delle idee.", "app.components.IdeasMap.a11y_mapTitle": "Panoramica della mappa", "app.components.IdeasMap.clickOnMapToAdd": "Clicca sulla mappa per aggiungere il tuo contributo", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Come amministratore, puoi cliccare sulla mappa per aggiungere il tuo contributo, anche se questa fase non è attiva.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "Ingressi multipli in questa posizione", "app.components.IdeasMap.noFilteredResults": "I filtri che hai selezionato non hanno dato alcun risultato", "app.components.IdeasMap.noResults": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "app.components.IdeasMap.or": "o", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, nessuna antipatia.} one {1 antipatia.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, nessun mi piace.} one {, 1 mi piace.} other {, # mi piace.}}", "app.components.IdeasMap.signInLinkText": "accedi", "app.components.IdeasMap.signUpLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.submitIdea2": "Invia un input", "app.components.IdeasMap.tapOnMapToAdd": "Clicca sulla mappa per aggiungere il tuo contributo", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Come amministratore, puoi toccare la mappa per aggiungere il tuo contributo, anche se questa fase non è attiva.", "app.components.IdeasMap.userInputs2": "Input dei partecipanti", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, nessun commento} one {, 1 commento} other {, # commenti}}", "app.components.IdeasShow.bodyTitle": "Descrizione", "app.components.IdeasShow.deletePost": "Cancellare", "app.components.IdeasShow.editPost": "Modifica", "app.components.IdeasShow.goBack": "Torna indietro", "app.components.IdeasShow.moreOptions": "Altre opzioni", "app.components.IdeasShow.or": "o", "app.components.IdeasShow.proposedBudgetTitle": "Bilancio proposto", "app.components.IdeasShow.reportAsSpam": "<PERSON><PERSON><PERSON> come spam", "app.components.IdeasShow.send": "Invia", "app.components.IdeasShow.skipSharing": "Saltalo, lo farò più tardi", "app.components.IdeasShowPage.signIn2": "Accedi", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON>, non puoi accedere a questa pagina. Potrebbe essere necessario effettuare il login o registrarsi per accedervi.", "app.components.LocationInput.noOptions": "Nessuna opzione", "app.components.Modal.closeWindow": "<PERSON><PERSON>", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Inserire un nuovo indirizzo e-mail", "app.components.PageNotFound.goBackToHomePage": "Torna alla homepage", "app.components.PageNotFound.notFoundTitle": "Pagina non trovata", "app.components.PageNotFound.pageNotFoundDescription": "Impossibile trovare la pagina richiesta.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Fornire contenuto per almeno una lingua", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "Allegati (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "I file non dovrebbero essere più grandi di 50Mb. I file aggiunti saranno mostrati in fondo a questa pagina.", "app.components.PagesForm.navbarItemTitle": "Nome nella barra di navigazione", "app.components.PagesForm.pageTitle": "<PERSON><PERSON>", "app.components.PagesForm.savePage": "Salva la pagina", "app.components.PagesForm.saveSuccess": "Pagina salvata correttamente.", "app.components.PagesForm.titleMissingOneLanguageError": "Fornire titolo per almeno una lingua", "app.components.Pagination.back": "<PERSON><PERSON><PERSON>e", "app.components.Pagination.next": "Pagina successiva", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Hai speso {votesCast}, che supera il limite di {votesLimit}. Rimuovi alcuni articoli dal carrello e riprova.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} sinistra", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Devi spendere un minimo di {votesMinimum} prima di poter inviare il tuo carrello.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Devi selezionare almeno un'opzione prima di poterla inviare.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Devi aggiungere qualcosa al tuo carrello prima di poterlo inviare.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Nessun credito residuo} other {# esaurito {totalNumberOfVotes, plural, one {1 credito} other {# crediti}} residuo}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Nessun punto rimasto} other {# fuori da {totalNumberOfVotes, plural, one {1 punto} other {# punti}} rimasti}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Non ci sono gettoni rimasti} other {# fuori da {totalNumberOfVotes, plural, one {1 gettone} other {# gettoni}} rimasti}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Non ci sono più voti} other {# fuori da {totalNumberOfVotes, plural, one {1 voto} other {# voti}} sinistra}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# voti} one {# voti} other {# voti}} voto", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Hai espresso {votesCast} voti, che superano il limite di {votesLimit}. Rimuovi alcuni voti e riprova.", "app.components.ParticipationCTABars.addInput": "Aggiungi un input", "app.components.ParticipationCTABars.allocateBudget": "Allocate il vostro budget", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Il tuo budget è stato inviato con successo.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Aperto alla partecipazione", "app.components.ParticipationCTABars.poll": "Partecipa al sondaggio", "app.components.ParticipationCTABars.reviewDocument": "Esamina il documento", "app.components.ParticipationCTABars.seeContributions": "Vedi i contributi", "app.components.ParticipationCTABars.seeEvents3": "Vedi gli eventi", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON><PERSON> idee", "app.components.ParticipationCTABars.seeInitiatives": "Vedi le iniziative", "app.components.ParticipationCTABars.seeIssues": "Vedi i problemi", "app.components.ParticipationCTABars.seeOptions": "Vedi le opzioni", "app.components.ParticipationCTABars.seePetitions": "Vedi petizioni", "app.components.ParticipationCTABars.seeProjects": "Vedi i progetti", "app.components.ParticipationCTABars.seeProposals": "<PERSON>edi le proposte", "app.components.ParticipationCTABars.seeQuestions": "<PERSON><PERSON><PERSON> le do<PERSON>e", "app.components.ParticipationCTABars.submit": "Invia", "app.components.ParticipationCTABars.takeTheSurvey": "Prendi il sondaggio", "app.components.ParticipationCTABars.userHasParticipated": "Avete partecipato a questo progetto.", "app.components.ParticipationCTABars.viewInputs": "Visualizza gli ingressi", "app.components.ParticipationCTABars.volunteer": "Volontario", "app.components.ParticipationCTABars.votesCounter.vote": "voto", "app.components.ParticipationCTABars.votesCounter.votes": "voti", "app.components.PasswordInput.a11y_passwordHidden": "Password nascosta", "app.components.PasswordInput.a11y_passwordVisible": "Password visibile", "app.components.PasswordInput.a11y_strength1Password": "Scarsa forza della password", "app.components.PasswordInput.a11y_strength2Password": "Forza della password debole", "app.components.PasswordInput.a11y_strength3Password": "Forza media della password", "app.components.PasswordInput.a11y_strength4Password": "Forte forza della password", "app.components.PasswordInput.a11y_strength5Password": "Forza della password molto forte", "app.components.PasswordInput.hidePassword": "Nascondi la password", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON> corto (min. {minimumPasswordLength} caratteri)", "app.components.PasswordInput.minimumPasswordLengthError": "Fornire una password lunga almeno {minimumPasswordLength} caratteri", "app.components.PasswordInput.passwordEmptyError": "Inserisci la tua password", "app.components.PasswordInput.passwordStrengthTooltip1": "Per rendere la tua password più forte:", "app.components.PasswordInput.passwordStrengthTooltip2": "Usa una combinazione di lettere minuscole non consecutive, lettere maiuscole, cifre, caratteri speciali e punteggiatura", "app.components.PasswordInput.passwordStrengthTooltip3": "Evitare parole comuni o facilmente intuibili", "app.components.PasswordInput.passwordStrengthTooltip4": "Aumentare la lunghezza", "app.components.PasswordInput.showPassword": "<PERSON>ra la password", "app.components.PasswordInput.strength1Password": "Povero", "app.components.PasswordInput.strength2Password": "De<PERSON>e", "app.components.PasswordInput.strength3Password": "Medio", "app.components.PasswordInput.strength4Password": "Forte", "app.components.PasswordInput.strength5Password": "Molto forte", "app.components.PostCardsComponents.list": "Lista", "app.components.PostCardsComponents.map": "Mappa", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Aggiungere un aggiornamento ufficiale", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Cancellare", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Sei sicuro di voler cancellare questo aggiornamento ufficiale?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Modifica", "app.components.PostComponents.OfficialFeedback.lastEdition": "Ultima modifica di {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Ultimo a<PERSON>rnamento: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Ufficiale", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON> come la gente vede il tuo nome", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Nome dell'autore dell'aggiornamento ufficiale", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Testo ufficiale del corpo dell'aggiornamento", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Aggiornamenti ufficiali", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON><PERSON> {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Pubblica", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Mostra gli aggiornamenti precedenti", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Dare un aggiornamento...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON>, c'è stato un problema", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Messaggio di aggiornamento", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Il tuo aggiornamento è stato pubblicato con successo!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Sostieni il mio contributo '{postTitle}' a {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Sostieni il mio contributo: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Sostieni il mio contributo: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Sostenete la mia idea '{postTitle}' a {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Sost<PERSON>i la mia idea: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Sostieni la mia idea: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Cosa ne pensi di questa proposta? Vota e condividi la discussione su {postUrl} per far sentire la tua voce!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Sostieni la mia idea: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Sostieni la mia iniziativa: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Ho inviato un problema '{postTitle}' a {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Ho appena postato un problema: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Ho appena postato un problema: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Sostenete la mia opzione proposta '{postTitle}' a {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Sostenete l'opzione da me proposta: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Supporta la mia opzione: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Sostieni la mia petizione \"{postTitle}\" su {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Sostenete la mia petizione: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Sostenete la mia petizione: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Sostenete il mio progetto '{postTitle}' a {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Sostieni il mio progetto: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Sostieni il mio progetto: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Sostieni la mia proposta '{postTitle}' su {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Sostieni la mia proposta: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Ho appena pubblicato una proposta per {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Partecipa alla discussione su questa domanda '{postTitle}' a {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Partecipa alla discussione: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Partecipa alla discussione: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Vota per {postTitle} su", "app.components.PostComponents.linkToHomePage": "Link alla home page", "app.components.PostComponents.readMore": "<PERSON><PERSON>i altro...", "app.components.PostComponents.topics": "Argomenti", "app.components.ProjectArchivedIndicator.archivedProject": "Purtroppo non puoi più partecipare a questo progetto perché è stato archiviato", "app.components.ProjectArchivedIndicator.previewProject": "Bozza di progetto:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visibile solo ai moderatori e a chi ha un link di anteprima.", "app.components.ProjectCard.a11y_projectDescription": "Descrizione del progetto:", "app.components.ProjectCard.a11y_projectTitle": "<PERSON><PERSON> pro<PERSON>to:", "app.components.ProjectCard.addYourOption": "Aggiungi la tua opzione", "app.components.ProjectCard.allocateYourBudget": "Allocate il vostro budget", "app.components.ProjectCard.archived": "Archiviato", "app.components.ProjectCard.comment": "Commento", "app.components.ProjectCard.contributeYourInput": "Contribuisci con il tuo contributo", "app.components.ProjectCard.finished": "<PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Partecipa alla discussione", "app.components.ProjectCard.learnMore": "<PERSON><PERSON><PERSON> al<PERSON>", "app.components.ProjectCard.reaction": "Reazione", "app.components.ProjectCard.readTheReport": "Leggi il rapporto", "app.components.ProjectCard.reviewDocument": "Esamina il documento", "app.components.ProjectCard.submitAnIssue": "Invia un numero", "app.components.ProjectCard.submitYourIdea": "Invia la tua idea", "app.components.ProjectCard.submitYourInitiative": "Invia la tua iniziativa", "app.components.ProjectCard.submitYourPetition": "Invia la tua petizione", "app.components.ProjectCard.submitYourProject": "Invia il tuo progetto", "app.components.ProjectCard.submitYourProposal": "Invia la tua proposta", "app.components.ProjectCard.takeThePoll": "Fai il sondaggio", "app.components.ProjectCard.takeTheSurvey": "Prendi il sondaggio", "app.components.ProjectCard.viewTheContributions": "Visualizza i contributi", "app.components.ProjectCard.viewTheIdeas": "Visualizza le idee", "app.components.ProjectCard.viewTheInitiatives": "Guarda le iniziative", "app.components.ProjectCard.viewTheIssues": "Visualizza i problemi", "app.components.ProjectCard.viewTheOptions": "Visualizza le opzioni", "app.components.ProjectCard.viewThePetitions": "Visualizza le petizioni", "app.components.ProjectCard.viewTheProjects": "Visualizza i progetti", "app.components.ProjectCard.viewTheProposals": "Visualizza le proposte", "app.components.ProjectCard.viewTheQuestions": "Visualizza le domande", "app.components.ProjectCard.vote": "Vota", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# commenti} other {# commenti}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# contributo} other {# contributi}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {nessuna idea ancora} one {# idea} other {# idee}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# iniziative} one {# iniziative} other {# iniziative}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# numero} other {# numeri}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# opzione} other {# opzioni}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petizioni} one {# petizione} other {# petizioni}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# progetto} other {# progetti}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# proposte} one {# proposta} other {# proposte}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# domanda} other {# domande}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# commenti} one {# commenti} other {# Commenti}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# ingressi} one {# input} other {# ingressi}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projects} one {# project} other {# projects}}", "app.components.ProjectFolderCards.components.Topbar.all": "<PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.archived": "Archiviato", "app.components.ProjectFolderCards.components.Topbar.draft": "<PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtrare per", "app.components.ProjectFolderCards.components.Topbar.published2": "Pubblicato", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "Attualmente non ci sono progetti aperti", "app.components.ProjectFolderCards.noProjectsAvailable": "<PERSON><PERSON><PERSON> progetto disponibile", "app.components.ProjectFolderCards.showMore": "Mostra di più", "app.components.ProjectFolderCards.stayTuned": "Controlla di nuovo per nuove opportunità di impegno", "app.components.ProjectFolderCards.tryChangingFilters": "Prova a cambiare i filtri selezionati.", "app.components.ProjectTemplatePreview.alsoUsedIn": "<PERSON>ato anche in queste città:", "app.components.ProjectTemplatePreview.copied": "Copiato", "app.components.ProjectTemplatePreview.copyLink": "Copiare il link", "app.components.QuillEditor.alignCenter": "Testo centrale", "app.components.QuillEditor.alignLeft": "Allinea a sinistra", "app.components.QuillEditor.alignRight": "Allinea a destra", "app.components.QuillEditor.bold": "Grassetto", "app.components.QuillEditor.clean": "Rimuovere la formattazione", "app.components.QuillEditor.customLink": "Aggiungi pulsante", "app.components.QuillEditor.customLinkPrompt": "Inserisci il link:", "app.components.QuillEditor.edit": "Modifica", "app.components.QuillEditor.image": "Carica immagine", "app.components.QuillEditor.imageAltPlaceholder": "Breve descrizione dell'immagine", "app.components.QuillEditor.italic": "Corsivo", "app.components.QuillEditor.link": "Aggiungi link", "app.components.QuillEditor.linkPrompt": "Inserisci il link:", "app.components.QuillEditor.normalText": "Normale", "app.components.QuillEditor.orderedList": "Elenco ordinato", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "<PERSON><PERSON>", "app.components.QuillEditor.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.title": "<PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Elenco non ordinato", "app.components.QuillEditor.video": "Aggiungi video", "app.components.QuillEditor.videoPrompt": "Inser<PERSON>ci il video:", "app.components.QuillEditor.visitPrompt": "Visita il link:", "app.components.ReactionControl.completeProfileToReact": "Completa il tuo profilo per reagire", "app.components.ReactionControl.dislike": "Non mi piace", "app.components.ReactionControl.dislikingDisabledMaxReached": "Hai raggiunto il numero massimo di commenti negativi in {projectName}", "app.components.ReactionControl.like": "Come", "app.components.ReactionControl.likingDisabledMaxReached": "Hai raggiunto il numero massimo di Mi piace in {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "La reazione sarà attivata una volta avviata questa fase.", "app.components.ReactionControl.reactingDisabledPhaseOver": "Non è più possibile reagire in questa fase", "app.components.ReactionControl.reactingDisabledProjectInactive": "Non puoi più reagire alle idee in {projectName}", "app.components.ReactionControl.reactingNotEnabled": "La reattività non è attualmente abilitata per questo progetto", "app.components.ReactionControl.reactingNotPermitted": "La reazione è abilitata solo per alcuni gruppi", "app.components.ReactionControl.reactingNotSignedIn": "Accedi per reagire.", "app.components.ReactionControl.reactingPossibleLater": "La reazione inizierà su {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verifica la tua identità per poter reagire.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Data dell'evento: da {startDate} a {startTime} a {endDate} a {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Data dell'evento: {eventDate} da {startTime} a {endTime}.", "app.components.Sharing.linkCopied": "<PERSON> copiato", "app.components.Sharing.or": "o", "app.components.Sharing.share": "Condi<PERSON><PERSON>", "app.components.Sharing.shareByEmail": "Condividi per e-mail", "app.components.Sharing.shareByLink": "Copiare il link", "app.components.Sharing.shareOnFacebook": "Condividi su <PERSON>", "app.components.Sharing.shareOnTwitter": "Condividi su <PERSON>", "app.components.Sharing.shareThisEvent": "Condividi questo evento", "app.components.Sharing.shareThisFolder": "Condi<PERSON><PERSON>", "app.components.Sharing.shareThisProject": "Condividi questo progetto", "app.components.Sharing.shareViaMessenger": "<PERSON><PERSON><PERSON><PERSON> via <PERSON>", "app.components.Sharing.shareViaWhatsApp": "Condividi via WhatsApp", "app.components.SideModal.closeButtonAria": "<PERSON><PERSON><PERSON>", "app.components.StatusModule.futurePhase": "Stai visualizzando una fase che non è ancora iniziata. Potrai partecipare quando la fase inizierà.", "app.components.StatusModule.modifyYourSubmission1": "Modifica il tuo invio", "app.components.StatusModule.submittedUntil3": "Il tuo voto può essere inviato fino a", "app.components.TopicsPicker.numberOfSelectedTopics": "Selezionato {numberOfSelectedTopics, plural, =0 {zero argomenti} one {un argomento} other {# argomenti}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Espandi immagine", "app.components.UI.MoreActionsMenu.moreOptions": "Altre opzioni", "app.components.UI.MoreActionsMenu.showMoreActions": "Mostra più azioni", "app.components.UI.PhaseFilter.noAppropriatePhases": "Non sono state trovate fasi appropriate per questo progetto", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Originale", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "Per poter partecipare sono necessarie ulteriori informazioni.", "app.components.Unauthorized.completeProfile": "<PERSON>ilo completo", "app.components.Unauthorized.completeProfileTitle": "Completa il tuo profilo per partecipare", "app.components.Unauthorized.noPermission": "Non hai i permessi per visualizzare questa pagina", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON>, non sei autorizzato ad accedere a questa pagina.", "app.components.Upload.errorImageMaxSizeExceeded": "L'immagine che hai selezionato è più grande di {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Una o più immagini selezionate sono più grandi di {maxFileSize}MB", "app.components.Upload.onlyOneImage": "<PERSON><PERSON>i caricare solo 1 immagine", "app.components.Upload.onlyXImages": "Puoi caricare solo {maxItemsCount} immagini", "app.components.Upload.remaining": "<PERSON><PERSON><PERSON>", "app.components.Upload.uploadImageLabel": "Seleziona un'immagine (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Seleziona una o più immagini", "app.components.UpsellTooltip.tooltipContent": "Questa funzione non è inclusa nel tuo piano attuale. Parla con il tuo Government Success Manager o con l'amministratore per sbloccarla.", "app.components.UserName.anonymous": "<PERSON><PERSON><PERSON>", "app.components.UserName.anonymousTooltip2": "Questo utente ha deciso di rendere anonimo il proprio contributo", "app.components.UserName.authorWithNoNameTooltip": "Il tuo nome è stato autogenerato perché non hai inserito il tuo nome. Se vuoi cambiarlo, aggiorna il tuo profilo.", "app.components.UserName.deletedUser": "<PERSON>re scon<PERSON>", "app.components.UserName.verified": "Verificato", "app.components.VerificationModal.verifyAuth0": "Verifica con NemID", "app.components.VerificationModal.verifyBOSA": "Verifica con itsme o eID", "app.components.VerificationModal.verifyBosaFas": "Verifica con itsme o eID", "app.components.VerificationModal.verifyClaveUnica": "Verifica con Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Verifica con SSO falso", "app.components.VerificationModal.verifyIdAustria": "Verifica con ID Austria", "app.components.VerificationModal.verifyKeycloak": "Verifica con ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verifica con MitID", "app.components.VerificationModal.verifyTwoday2": "Verifica con BankID o Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Verifica la tua identità", "app.components.VoteControl.budgetingFutureEnabled": "Puoi allocare il tuo budget a partire da {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Il bilancio partecipativo non è attualmente abilitato.", "app.components.VoteControl.budgetingNotPossible": "In questo momento non è possibile apportare modifiche al tuo budget.", "app.components.VoteControl.budgetingNotVerified": "Si prega di {verifyAccountLink} per continuare.", "app.components.VoteInputs._shared.currencyLeft1": "Ti rimane {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Hai {votesLeft, plural, =0 {nessun credito residuo} other {# fuori da {totalNumberOfVotes, plural, one {1 credito} other {# crediti}} rimasti}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Hai {votesLeft, plural, =0 {nessun punto rimasto} other {# su {totalNumberOfVotes, plural, one {1 punto} other {# punti}} rimasti}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Hai {votesLeft, plural, =0 {nessun gettone rimasto} other {# fuori da {totalNumberOfVotes, plural, one {1 gettone} other {# gettoni}} rimasti}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Hai {votesLeft, plural, =0 {nessun voto rimasto} other {# su {totalNumberOfVotes, plural, one {1 voto} other {# voti}} rimasti}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Hai già inviato il tuo preventivo. Per modificarlo, clicca su \"Modifica il tuo preventivo\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Hai già inviato il tuo preventivo. Per modific<PERSON>lo, torna alla pagina del progetto e clicca su \"Modifica il tuo preventivo\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Il budget non è disponibile, poiché questa fase non è attiva.", "app.components.VoteInputs.single.youHaveVotedForX2": "Hai votato per {votes, plural, =0 {# opzioni} one {# opzione} other {# opzioni}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "Questo significa che perderai tutti i dati associati a questo inserimento, come i commenti, le reazioni e i voti. Questa azione non può essere annullata.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Sei sicuro di voler eliminare questo input?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Annullamento", "app.components.admin.PostManager.components.PostTable.Row.confirm": "<PERSON><PERSON><PERSON>", "app.components.admin.SlugInput.resultingURL": "URL risultante", "app.components.admin.SlugInput.slugTooltip": "Il campo dati dinamico è l'insieme univoco di parole alla fine dell'indirizzo Web o URL della pagina.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "Se si modifica l'URL, i collegamenti alla pagina utilizzando l'URL precedente non funzioneranno più.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Aggiungere una condizione", "app.components.admin.UserFilterConditions.field_email": "Email", "app.components.admin.UserFilterConditions.field_event_attendance": "Iscrizioni agli eventi", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "Vive in", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Sondaggio di monitoraggio della comunità", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Ha interagito con un ingresso con stato", "app.components.admin.UserFilterConditions.field_participated_in_project": "Ha contribuito al progetto", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Pubblicato qualcosa con tag", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Data di registrazione", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Verifica", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideazione", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Proposte", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "non è iscritto a nessuno di questi eventi", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "non è iscritto a nessun evento", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "è iscritto a uno di questi eventi", "app.components.admin.UserFilterConditions.predicate_attends_something": "è iscritto ad almeno un evento", "app.components.admin.UserFilterConditions.predicate_begins_with": "inizia con", "app.components.admin.UserFilterConditions.predicate_commented_in": "ha commentato", "app.components.admin.UserFilterConditions.predicate_contains": "contiene", "app.components.admin.UserFilterConditions.predicate_ends_on": "finisce su", "app.components.admin.UserFilterConditions.predicate_has_value": "ha valore", "app.components.admin.UserFilterConditions.predicate_in": "eseguito qualsiasi azione", "app.components.admin.UserFilterConditions.predicate_is": "è", "app.components.admin.UserFilterConditions.predicate_is_admin": "è un amministratore", "app.components.admin.UserFilterConditions.predicate_is_after": "è dopo", "app.components.admin.UserFilterConditions.predicate_is_before": "è prima di", "app.components.admin.UserFilterConditions.predicate_is_checked": "è controllato", "app.components.admin.UserFilterConditions.predicate_is_empty": "è vuoto", "app.components.admin.UserFilterConditions.predicate_is_equal": "è", "app.components.admin.UserFilterConditions.predicate_is_exactly": "è esattamente", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "è più grande di", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "è maggiore o uguale a", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "è un utente normale", "app.components.admin.UserFilterConditions.predicate_is_not_area": "esclude l'area", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "esclude la cartella", "app.components.admin.UserFilterConditions.predicate_is_not_input": "esclude l'input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "esclude il progetto", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "esclude l'argomento", "app.components.admin.UserFilterConditions.predicate_is_one_of": "è uno dei", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "una delle aree", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "una delle cartelle", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "uno degli ingressi", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "uno dei progetti", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "uno degli argomenti", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "è un project manager", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "è più piccolo di", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "è minore o uguale a", "app.components.admin.UserFilterConditions.predicate_is_verified": "è verificato", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "non inizia con", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "non ha commentato", "app.components.admin.UserFilterConditions.predicate_not_contains": "non contiene", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "non finisce su", "app.components.admin.UserFilterConditions.predicate_not_has_value": "non ha valore", "app.components.admin.UserFilterConditions.predicate_not_in": "non ha contribuito", "app.components.admin.UserFilterConditions.predicate_not_is": "non è", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "non è un amministratore", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "non è controllato", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "non è vuoto", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "non è", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "non è un utente normale", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "non è uno dei", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "non è un project manager", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "non è verificato", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "non ha postato un input", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "non ha reagito al commento", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "non ha reagito agli input", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "non si è registrato ad un evento", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "non ha partecipato al sondaggio", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "non si è offerto volontario", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "non ha partecipato al voto", "app.components.admin.UserFilterConditions.predicate_nothing": "nulla", "app.components.admin.UserFilterConditions.predicate_posted_input": "ha inserito un input", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "ha reagito al commento", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "ha reagito agli input", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "iscritti ad un evento", "app.components.admin.UserFilterConditions.predicate_something": "qualcosa", "app.components.admin.UserFilterConditions.predicate_taken_survey": "ha partecipato al sondaggio", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "volontariato", "app.components.admin.UserFilterConditions.predicate_voted_in3": "ha partecipato alle votazioni", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attributo", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Condizione", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Valore", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Non riceverai notifiche sui tuoi contributi", "app.components.anonymousParticipationModal.cancel": "Annullamento", "app.components.anonymousParticipationModal.continue": "Continua", "app.components.anonymousParticipationModal.participateAnonymously": "Partecipa in modo anonimo", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON> <b>nasconder<PERSON></b> in modo sicuro il <b>tuo profilo</b> agli amministratori, ai project manager e agli altri residenti per questo specifico contributo, in modo che nessuno sia in grado di collegarlo a te. I contributi anonimi non possono essere modificati e sono considerati definitivi.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Rendere la nostra piattaforma sicura per tutti gli utenti è per noi una priorità assoluta. Le parole contano, quindi ti preghiamo di essere gentile con gli altri.", "app.components.avatar.titleForAccessibility": "<PERSON>ilo di {fullName}", "app.components.customFields.mapInput.removeAnswer": "Rimuovi la risposta", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "<PERSON>ull<PERSON> l'ultimo punto", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON>e", "app.components.followUnfollow.followTooltipInputPage2": "Seguendo questa procedura, riceverai aggiornamenti via e-mail sui cambiamenti di stato, sugli aggiornamenti ufficiali e sui commenti. Puoi {unsubscribeLink} in qualsiasi momento.", "app.components.followUnfollow.followTooltipProjects2": "Seguendo questa procedura si ricevono aggiornamenti via e-mail sulle modifiche al progetto. Puoi {unsubscribeLink} in qualsiasi momento.", "app.components.followUnfollow.unFollow": "Non seguire", "app.components.followUnfollow.unsubscribe": "Annullamento dell'iscrizione", "app.components.followUnfollow.unsubscribeUrl": "/profilo/modifica", "app.components.form.ErrorDisplay.guidelinesLinkText": "le nostre linee guida", "app.components.form.ErrorDisplay.next": "Successivo", "app.components.form.ErrorDisplay.previous": "Precedente", "app.components.form.ErrorDisplay.save": "Risparmiare", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Inizia a digitare per cercare per email o nome dell'utente...", "app.components.form.anonymousSurveyMessage2": "<PERSON>tte le risposte a questo sondaggio sono anonime.", "app.components.form.backToInputManager": "Torna al gestore degli input", "app.components.form.backToProject": "Torna al progetto", "app.components.form.components.controls.mapInput.removeAnswer": "Rimuovi la risposta", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "<PERSON>ull<PERSON> l'ultimo punto", "app.components.form.controls.addressInputAriaLabel": "Indirizzo di ingresso", "app.components.form.controls.addressInputPlaceholder6": "Inserisci un indirizzo...", "app.components.form.controls.adminFieldTooltip": "Campo visibile solo agli amministratori", "app.components.form.controls.allStatementsError": "È necessario selezionare una risposta per tutte le affermazioni.", "app.components.form.controls.back": "Indietro", "app.components.form.controls.clearAll": "<PERSON><PERSON>a tutto", "app.components.form.controls.clearAllScreenreader": "Can<PERSON>a tutte le risposte dalla domanda della matrice precedente", "app.components.form.controls.clickOnMapMultipleToAdd3": "Clicca sulla mappa per disegnare. Poi, trascina i punti per spostarli.", "app.components.form.controls.clickOnMapToAddOrType": "Clicca sulla mappa o digita un indirizzo qui sotto per aggiungere la tua risposta.", "app.components.form.controls.confirm": "<PERSON><PERSON><PERSON>", "app.components.form.controls.cosponsorsPlaceholder": "Inizia a digitare un nome da cercare", "app.components.form.controls.currentRank": "Classifica attuale:", "app.components.form.controls.minimumCoordinates2": "È richiesto un minimo di {numPoints} punti mappa.", "app.components.form.controls.noRankSelected": "Nessun grado selezionato", "app.components.form.controls.notPublic1": "*Questa risposta sarà condivisa solo con i responsabili del progetto e non con il pubblico.", "app.components.form.controls.optionalParentheses": "(opzionale)", "app.components.form.controls.rankingInstructions": "Trascina e rilascia per classificare le opzioni.", "app.components.form.controls.selectAsManyAsYouLike": "*Seleziona tutti quelli che vuoi", "app.components.form.controls.selectBetween": "*Seleziona tra le opzioni {minItems} e {maxItems}", "app.components.form.controls.selectExactly2": "*Seleziona esattamente {selectExactly, plural, one {# opzione} other {# opzioni}}", "app.components.form.controls.selectMany": "*Scegli quanti ne vuoi", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tocca la mappa per disegnare. Poi, trascina i punti per spostarli.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tocca la mappa per disegnare.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tocca la mappa per aggiungere la tua risposta.", "app.components.form.controls.tapOnMapToAddOrType": "Tocca la mappa o digita un indirizzo qui sotto per aggiungere la tua risposta.", "app.components.form.controls.tapToAddALine": "Tocca per aggiungere una riga", "app.components.form.controls.tapToAddAPoint": "Tocca per aggiungere un punto", "app.components.form.controls.tapToAddAnArea": "Tocca per aggiungere un'area", "app.components.form.controls.uploadShapefileInstructions": "* Carica un file zip contenente uno o più shapefile.", "app.components.form.controls.validCordinatesTooltip2": "Se la posizione non viene visualizzata tra le opzioni durante la digitazione, puoi aggiungere delle coordinate valide nel formato \"latitudine, longitudine\" per specificare una posizione precisa (ad esempio: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} da {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} su {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} da {total}, dove {maxValue} è {maxLabel}", "app.components.form.error": "Errore", "app.components.form.locationGoogleUnavailable": "Impossibile caricare il campo posizione fornito da google maps.", "app.components.form.progressBarLabel": "Progressi dell'indagine", "app.components.form.submit": "Invia", "app.components.form.submitApiError": "C'è stato un problema nell'invio del modulo. Si prega di controllare eventuali errori e riprovare.", "app.components.form.verifiedBlocked": "Non è possibile modificare questo campo perché contiene informazioni verificate.", "app.components.formBuilder.Page": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "dichiarazione di accessibilità", "app.components.formBuilder.addAnswer": "Aggiungi risposta", "app.components.formBuilder.addStatement": "Aggiungi una dichiarazione", "app.components.formBuilder.agree": "Accetta", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "Se hai accesso al nostro pacchetto AI, sarai in grado di riassumere e categorizzare le risposte al testo con l'AI.", "app.components.formBuilder.askFollowUpToggleLabel": "<PERSON><PERSON>i un follow-up", "app.components.formBuilder.bad": "Male", "app.components.formBuilder.buttonLabel": "Etichetta del pulsante", "app.components.formBuilder.buttonLink": "Link al pulsante", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Annullamento", "app.components.formBuilder.category": "Categoria", "app.components.formBuilder.chooseMany": "<PERSON><PERSON><PERSON><PERSON> molti", "app.components.formBuilder.chooseOne": "Scegliere uno", "app.components.formBuilder.close": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Configura la mappa", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, voglio and<PERSON>", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "Continua a", "app.components.formBuilder.cosponsors": "Co-sponsor", "app.components.formBuilder.default": "Predefinito", "app.components.formBuilder.defaultContent": "Contenuto predefinito", "app.components.formBuilder.delete": "Cancellare", "app.components.formBuilder.deleteButtonLabel": "Cancellare", "app.components.formBuilder.description": "Descrizione", "app.components.formBuilder.disabledBuiltInFieldTooltip": "È già stato aggiunto nel modulo. Il contenuto predefinito può essere utilizzato una sola volta.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "L'aggiunta di contenuti personalizzati non fa parte della tua licenza attuale. Contatta il tuo GovSuccess Manager per saperne di più.", "app.components.formBuilder.disagree": "Non sono d'accordo", "app.components.formBuilder.displayAsDropdown": "Visualizza come discesa", "app.components.formBuilder.displayAsDropdownTooltip": "Visualizza le opzioni in un menu a tendina. Se hai molte opzioni, questa soluzione è consigliata.", "app.components.formBuilder.done": "<PERSON><PERSON>", "app.components.formBuilder.drawArea": "Area di disegno", "app.components.formBuilder.drawRoute": "Disegna il percorso", "app.components.formBuilder.dropPin": "<PERSON><PERSON><PERSON> a goccia", "app.components.formBuilder.editButtonLabel": "Modifica", "app.components.formBuilder.emptyImageOptionError": "Fornisci almeno 1 risposta. Ricorda che ogni risposta deve avere un titolo.", "app.components.formBuilder.emptyOptionError": "Fornire almeno 1 risposta", "app.components.formBuilder.emptyStatementError": "Fornisci almeno 1 dichiarazione", "app.components.formBuilder.emptyTitleError": "Fornire un titolo alla domanda", "app.components.formBuilder.emptyTitleMessage": "Fornisci un titolo per tutte le risposte", "app.components.formBuilder.emptyTitleStatementMessage": "Fornisci un titolo per tutte le affermazioni", "app.components.formBuilder.enable": "Abilitazione", "app.components.formBuilder.errorMessage": "C'è un problema, risolvere il problema per poter salvare le modifiche.", "app.components.formBuilder.fieldGroup.description": "Descrizione (opzionale)", "app.components.formBuilder.fieldGroup.title": "<PERSON><PERSON> (facoltativo)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Attual<PERSON><PERSON>, le risposte a queste domande sono disponibili solo nel file excel esportato su Input Manager e non sono visibili agli utenti.", "app.components.formBuilder.fieldLabel": "Scelte di risposta", "app.components.formBuilder.fieldLabelStatement": "Dichiarazioni", "app.components.formBuilder.fileUpload": "Caricamento dei file", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "<PERSON><PERSON><PERSON> basata sulla mappa", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Inserisci la mappa come contesto o fai domande ai partecipanti basate sulla posizione.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Per un'esperienza utente ottimale, non consigliamo di aggiungere domande su punti, percorsi o aree alle pagine basate sulle mappe.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Pagina normale", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Le funzioni di mappatura dei rilievi non sono incluse nella tua licenza attuale. Contatta il tuo GovSuccess Manager per saperne di più.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Tipo di pagina", "app.components.formBuilder.formEnd": "Fine modulo", "app.components.formBuilder.formField.cancelDeleteButtonText": "Annullamento", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Sì, cancella la pagina", "app.components.formBuilder.formField.copyNoun": "Copia", "app.components.formBuilder.formField.copyVerb": "Copia", "app.components.formBuilder.formField.delete": "Elimina", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "L'eliminazione di questa pagina cancellerà anche la logica ad essa associata. Sei sicuro di volerla eliminare?", "app.components.formBuilder.formField.deleteResultsInfo": "Questo non può essere annullato", "app.components.formBuilder.goToPageInputLabel": "La pagina successiva è:", "app.components.formBuilder.good": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.helmetTitle": "Costruttore di moduli", "app.components.formBuilder.imageFileUpload": "Caricamento dell'immagine", "app.components.formBuilder.invalidLogicBadgeMessage": "Logica non valida", "app.components.formBuilder.labels2": "Etic<PERSON>tte (facoltative)", "app.components.formBuilder.labelsTooltipContent2": "Scegli le etichette opzionali per qualsiasi valore della scala lineare.", "app.components.formBuilder.lastPage": "Fine", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Sei sicuro di volertene andare?", "app.components.formBuilder.leaveBuilderText": "Le modifiche non sono state salvate. Salva prima di andartene. Se esci, perderai le tue modifiche.", "app.components.formBuilder.limitAnswersTooltip": "Quando è attivata, i rispondenti devono selezionare il numero di risposte specificato per procedere.", "app.components.formBuilder.limitNumberAnswers": "Limita il numero di risposte", "app.components.formBuilder.linePolygonMapWarning2": "Il disegno di linee e poligoni potrebbe non soddisfare gli standard di accessibilità. Maggiori informazioni sono disponibili sul sito {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Scala lineare", "app.components.formBuilder.locationDescription": "Posizione", "app.components.formBuilder.logic": "Logica", "app.components.formBuilder.logicAnyOtherAnswer": "Qualsiasi altra risposta", "app.components.formBuilder.logicConflicts.conflictingLogic": "Logica conflittuale", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Questa pagina contiene domande che conducono a pagine diverse. Se i partecipanti rispondono a più domande, verrà mostrata la pagina più lontana. Assicurati che questo comportamento sia in linea con il flusso previsto.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "A questa pagina vengono applicate diverse regole logiche: logica della domanda a selezione multipla, logica a livello di pagina e logica tra le domande. Quando queste condizioni si sovrappongono, la logica delle domande avrà la precedenza su quella delle pagine e verrà mostrata la pagina più lontana. Esamina la logica per assicurarti che sia in linea con il flusso previsto.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Questa pagina contiene una domanda a selezione multipla in cui le opzioni portano a pagine diverse. Se i partecipanti selezionano più opzioni, verrà mostrata la pagina più lontana. Assicurati che questo comportamento sia in linea con il flusso previsto.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Questa pagina contiene una domanda a selezione multipla in cui le opzioni conducono a pagine diverse e ha domande che conducono ad altre pagine. Se queste condizioni si sovrappongono, verrà mostrata la pagina più lontana. Assicurati che questo comportamento sia in linea con il flusso previsto.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Questa pagina contiene una domanda a selezione multipla in cui le opzioni conducono a pagine diverse e ha una logica impostata sia a livello di pagina che di domanda. La logica della domanda avrà la precedenza e verrà mostrata la pagina più lontana. Assicurati che questo comportamento sia in linea con il flusso previsto.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Questa pagina ha una logica impostata sia a livello di pagina che a livello di domanda. La logica delle domande avrà la precedenza sulla logica a livello di pagina. Assicurati che questo comportamento sia in linea con il flusso previsto.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Questa pagina ha una logica impostata sia a livello di pagina che di domanda e più domande rimandano a pagine diverse. La logica delle domande avrà la precedenza e verrà mostrata la pagina più lontana. Assicurati che questo comportamento sia in linea con il flusso previsto.", "app.components.formBuilder.logicNoAnswer2": "Non risponde", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Se la risposta è un'altra", "app.components.formBuilder.logicPanelNoAnswer": "Se non si risponde", "app.components.formBuilder.logicValidationError": "La logica non può collegarsi a pagine precedenti", "app.components.formBuilder.longAnswer": "<PERSON><PERSON><PERSON><PERSON> lunga", "app.components.formBuilder.mapConfiguration": "Configurazione della mappa", "app.components.formBuilder.mapping": "Mappatura", "app.components.formBuilder.mappingNotInCurrentLicense": "Le funzioni di mappatura dei rilievi non sono incluse nella tua licenza attuale. Contatta il tuo GovSuccess Manager per saperne di più.", "app.components.formBuilder.matrix": "<PERSON><PERSON>", "app.components.formBuilder.matrixSettings.columns": "Colonne", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "Scelta multipla", "app.components.formBuilder.multipleChoiceHelperText": "Se più opzioni portano a pagine diverse e i partecipanti ne selezionano più di una, verrà mostrata la pagina più lontana. Assicurati che questo comportamento sia in linea con il flusso previsto.", "app.components.formBuilder.multipleChoiceImage": "Scelta dell'immagine", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "Minimo", "app.components.formBuilder.neutral": "Neutrale", "app.components.formBuilder.newField": "Nuovo campo", "app.components.formBuilder.number": "Numero", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Aperto", "app.components.formBuilder.optional": "Opzionale", "app.components.formBuilder.other": "Altro", "app.components.formBuilder.otherOption": "\"Opzione \"Altro", "app.components.formBuilder.otherOptionTooltip": "Permetti ai partecipanti di inserire una risposta personalizzata se le risposte fornite non corrispondono alle loro preferenze", "app.components.formBuilder.page": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "Questa pagina non può essere cancellata.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Questa pagina non può essere cancellata e non consente di aggiungere altri campi.", "app.components.formBuilder.pageRuleLabel": "La pagina successiva è:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Se non viene aggiunta alcuna logica, il modulo seguirà il suo normale flusso. Se sia la pagina che le sue domande hanno una logica, la logica delle domande avrà la precedenza. Assicurati che questo sia in linea con il flusso che intendi seguire. Per maggiori informazioni, visita il sito {supportPageLink}.", "app.components.formBuilder.preview": "Anteprima:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "I co-sponsor non vengono visualizzati nel PDF scaricato e non sono supportati per l'importazione tramite FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Le domande con caricamento di file sono indicate come non supportate nel PDF scaricato e non sono supportate per l'importazione tramite FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Le domande di mappatura vengono visualizzate nel PDF scaricato, ma i livelli non sono visibili. Le domande di mappatura non sono supportate per l'importazione tramite FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Le domande a matrice sono visualizzate nel PDF scaricato ma non sono attualmente supportate per l'importazione tramite FormSync.", "app.components.formBuilder.printSupportTooltip.page": "I titoli e le descrizioni delle pagine vengono visualizzati come intestazione di sezione nel PDF scaricato.", "app.components.formBuilder.printSupportTooltip.ranking": "Le domande di classificazione sono visualizzate nel PDF scaricato, ma al momento non sono supportate per l'importazione tramite FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "I tag sono indicati come non supportati nel PDF scaricato e non sono supportati per l'importazione tramite FormSync.", "app.components.formBuilder.proposedBudget": "Bilancio proposto", "app.components.formBuilder.question": "<PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "Questa domanda non può essere cancellata.", "app.components.formBuilder.questionDescriptionOptional": "Descrizione della domanda (facoltativa)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON>", "app.components.formBuilder.randomize": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.randomizeToolTip": "L'ordine delle risposte sarà randomizzato per ogni utente.", "app.components.formBuilder.range": "Gamma", "app.components.formBuilder.ranking": "Classifica", "app.components.formBuilder.rating": "Valutazione", "app.components.formBuilder.removeAnswer": "Rimuovere la risposta", "app.components.formBuilder.required": "<PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "Rendere obbligatoria la risposta a questa domanda", "app.components.formBuilder.ruleForAnswerLabel": "Se la risposta è:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Se le risposte includono:", "app.components.formBuilder.save": "Risparmiare", "app.components.formBuilder.selectRangeTooltip": "Scegliere il valore massimo per la scala.", "app.components.formBuilder.sentiment": "Scala del sentimento", "app.components.formBuilder.shapefileUpload": "Caricamento di shapefile Esri", "app.components.formBuilder.shortAnswer": "Risposta breve", "app.components.formBuilder.showResponseToUsersToggleLabel": "Mostrare la risposta agli utenti", "app.components.formBuilder.singleChoice": "<PERSON><PERSON><PERSON> singola", "app.components.formBuilder.staleDataErrorMessage2": "Si è verificato un problema. Questo modulo di inserimento è stato salvato più di recente da un'altra parte. <PERSON><PERSON>ò potrebbe essere dovuto al fatto che tu o un altro utente lo avete aperto per la modifica in un'altra finestra del browser. Aggiorna la pagina per ottenere il modulo più recente e poi apporta nuovamente le tue modifiche.", "app.components.formBuilder.stronglyAgree": "Fortemente d'accordo", "app.components.formBuilder.stronglyDisagree": "Fortemente in disaccordo", "app.components.formBuilder.supportArticleLinkText": "questa pagina", "app.components.formBuilder.tags": "Tag", "app.components.formBuilder.title": "<PERSON><PERSON>", "app.components.formBuilder.toLabel": "a", "app.components.formBuilder.unsavedChanges": "Hai delle modifiche non salvate", "app.components.formBuilder.useCustomButton2": "Usa il pulsante della pagina personalizzata", "app.components.formBuilder.veryBad": "Molto male", "app.components.formBuilder.veryGood": "Molto bene", "app.components.ideas.similarIdeas.engageHere": "Impegnati qui", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Non sono stati trovati invii simili.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Abbiamo trovato sottomissioni simili: impegnarsi con loro può aiutare a renderle più forti!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Invii simili sono già stati pubblicati:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Cercate invii simili ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> di un giorno} one {# giorno} other {# giorni}} sinistra", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  settimane rimaste", "app.components.screenReaderCurrency.AED": "<PERSON><PERSON>ham degli Emirati Arabi Uniti", "app.components.screenReaderCurrency.AFN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ALL": "Lek albanese", "app.components.screenReaderCurrency.AMD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ANG": "<PERSON><PERSON>no o<PERSON> delle Antille", "app.components.screenReaderCurrency.AOA": "<PERSON><PERSON><PERSON> angol<PERSON>", "app.components.screenReaderCurrency.ARS": "Peso argentino", "app.components.screenReaderCurrency.AUD": "<PERSON><PERSON> austra<PERSON>", "app.components.screenReaderCurrency.AWG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AZN": "Manat azero", "app.components.screenReaderCurrency.BAM": "Bosnia-Erzegovina Marchio convertibile", "app.components.screenReaderCurrency.BBD": "Dollaro barbadiano", "app.components.screenReaderCurrency.BDT": "Taka del Bangladesh", "app.components.screenReaderCurrency.BGN": "Lev bulgaro", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>hrein", "app.components.screenReaderCurrency.BIF": "<PERSON>", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BND": "Dollaro del Brunei", "app.components.screenReaderCurrency.BOB": "Boliviano <PERSON>no", "app.components.screenReaderCurrency.BOV": "<PERSON>v<PERSON><PERSON> boli<PERSON>o", "app.components.screenReaderCurrency.BRL": "Real brasiliano", "app.components.screenReaderCurrency.BSD": "Dollaro delle Bahamas", "app.components.screenReaderCurrency.BTN": "<PERSON><PERSON><PERSON> b<PERSON>", "app.components.screenReaderCurrency.BWP": "Pula del Botswana", "app.components.screenReaderCurrency.BYR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BZD": "Dollaro del Belize", "app.components.screenReaderCurrency.CAD": "<PERSON><PERSON> can<PERSON>", "app.components.screenReaderCurrency.CDF": "Franco congolese", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "<PERSON> s<PERSON>", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Unità di conto cilena (UF)", "app.components.screenReaderCurrency.CLP": "Peso cileno", "app.components.screenReaderCurrency.CNY": "Yuan cinese", "app.components.screenReaderCurrency.COP": "Peso colombiano", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rica Colón", "app.components.screenReaderCurrency.CRE": "Credito", "app.components.screenReaderCurrency.CUC": "Peso cubano convertibile", "app.components.screenReaderCurrency.CUP": "Peso cubano", "app.components.screenReaderCurrency.CVE": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CZK": "Corona ceca", "app.components.screenReaderCurrency.DJF": "<PERSON>", "app.components.screenReaderCurrency.DKK": "Corona danese", "app.components.screenReaderCurrency.DOP": "Peso dominicano", "app.components.screenReaderCurrency.DZD": "<PERSON>aro <PERSON>", "app.components.screenReaderCurrency.EGP": "Sterlina egiziana", "app.components.screenReaderCurrency.ERN": "Nakfa eritrea", "app.components.screenReaderCurrency.ETB": "Birr etiope", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Dollaro delle Fiji", "app.components.screenReaderCurrency.FKP": "Sterlina delle Isole Falkland", "app.components.screenReaderCurrency.GBP": "Sterlina britannica", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON> geor<PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Sterlina di Gibilterra", "app.components.screenReaderCurrency.GMD": "Dalasi del Gambia", "app.components.screenReaderCurrency.GNF": "Franco della Guinea", "app.components.screenReaderCurrency.GTQ": "Quetzal del Guatemala", "app.components.screenReaderCurrency.GYD": "<PERSON><PERSON> della Guyana", "app.components.screenReaderCurrency.HKD": "Dollaro di Hong Kong", "app.components.screenReaderCurrency.HNL": "Lempira hond<PERSON>gna", "app.components.screenReaderCurrency.HRK": "<PERSON><PERSON> croata", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IDR": "<PERSON><PERSON><PERSON> indonesiana", "app.components.screenReaderCurrency.ILS": "Nuovo siclo is<PERSON>liano", "app.components.screenReaderCurrency.INR": "<PERSON><PERSON><PERSON> indiana", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "Islandese Króna", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "Yen g<PERSON><PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON> keniota", "app.components.screenReaderCurrency.KGS": "Kyrgyzstani Som", "app.components.screenReaderCurrency.KHR": "<PERSON>iel <PERSON>", "app.components.screenReaderCurrency.KMF": "<PERSON> com<PERSON>", "app.components.screenReaderCurrency.KPW": "Won nordcoreano", "app.components.screenReaderCurrency.KRW": "Won sudcoreano", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Dollaro delle Isole Cayman", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON> kazako", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "<PERSON><PERSON><PERSON> libanese", "app.components.screenReaderCurrency.LKR": "Rupia dello Sri Lanka", "app.components.screenReaderCurrency.LRD": "Dollaro liberiano", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Litas lituano", "app.components.screenReaderCurrency.LVL": "Lats lettone", "app.components.screenReaderCurrency.LYD": "<PERSON><PERSON> libico", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "<PERSON><PERSON>o", "app.components.screenReaderCurrency.MGA": "<PERSON><PERSON> ma<PERSON>", "app.components.screenReaderCurrency.MKD": "<PERSON>aro macedone", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mongolo", "app.components.screenReaderCurrency.MOP": "Pataca <PERSON>", "app.components.screenReaderCurrency.MRO": "Ouguiya della Mauritania", "app.components.screenReaderCurrency.MUR": "<PERSON><PERSON><PERSON> mauriziana", "app.components.screenReaderCurrency.MVR": "<PERSON><PERSON><PERSON><PERSON> maldiv<PERSON>", "app.components.screenReaderCurrency.MWK": "Kwacha del Malawi", "app.components.screenReaderCurrency.MXN": "Peso messicano", "app.components.screenReaderCurrency.MXV": "Unidad de Inversion (UDI) messicana", "app.components.screenReaderCurrency.MYR": "<PERSON><PERSON> malese", "app.components.screenReaderCurrency.MZN": "Metical moz<PERSON>", "app.components.screenReaderCurrency.NAD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.NGN": "Nair<PERSON> nigeriana", "app.components.screenReaderCurrency.NIO": "Córdoba nicaraguense", "app.components.screenReaderCurrency.NOK": "Corona norvegese", "app.components.screenReaderCurrency.NPR": "<PERSON><PERSON><PERSON> ne<PERSON>", "app.components.screenReaderCurrency.NZD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.PAB": "Balboa panamense", "app.components.screenReaderCurrency.PEN": "Sol peruviano", "app.components.screenReaderCurrency.PGK": "<PERSON><PERSON> della Papua Nuova Guinea", "app.components.screenReaderCurrency.PHP": "Peso fi<PERSON>o", "app.components.screenReaderCurrency.PKR": "<PERSON><PERSON><PERSON> pakistana", "app.components.screenReaderCurrency.PLN": "Polacco <PERSON>", "app.components.screenReaderCurrency.PYG": "Guaraní <PERSON>", "app.components.screenReaderCurrency.QAR": "Riyal del Qatar", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON> rume<PERSON>", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON> se<PERSON>o", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON>lo russo", "app.components.screenReaderCurrency.RWF": "Franco ruandese", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON><PERSON> sa<PERSON>", "app.components.screenReaderCurrency.SBD": "Dollaro delle Isole Salomone", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SDG": "<PERSON><PERSON><PERSON> su<PERSON>", "app.components.screenReaderCurrency.SEK": "Corona svedese", "app.components.screenReaderCurrency.SGD": "Dollaro di Singapore", "app.components.screenReaderCurrency.SHP": "Sterlina di Sant'Elena", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "Dollaro Surinamese", "app.components.screenReaderCurrency.SSP": "<PERSON><PERSON><PERSON> su<PERSON>", "app.components.screenReaderCurrency.STD": "São Tomé e Príncipe Do<PERSON>", "app.components.screenReaderCurrency.SYP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "<PERSON><PERSON> thailandese", "app.components.screenReaderCurrency.TJS": "<PERSON><PERSON><PERSON> tag<PERSON>", "app.components.screenReaderCurrency.TMT": "Manat turkmeno", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON>", "app.components.screenReaderCurrency.TOK": "Gettone", "app.components.screenReaderCurrency.TOP": "Paʻanga tongano", "app.components.screenReaderCurrency.TRY": "Lira turca", "app.components.screenReaderCurrency.TTD": "Dollaro di Trinidad e Tobago", "app.components.screenReaderCurrency.TWD": "Nuovo dollaro di Taiwan", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Grivna ucraina", "app.components.screenReaderCurrency.UGX": "Scellino ugandese", "app.components.screenReaderCurrency.USD": "Dollaro degli Stati Uniti", "app.components.screenReaderCurrency.USN": "Dollaro USA (giorno successivo)", "app.components.screenReaderCurrency.USS": "Dollaro USA (stesso giorno)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Peso uruguaiano", "app.components.screenReaderCurrency.UZS": "Uzbekistan Som", "app.components.screenReaderCurrency.VEF": "Bolívar venezu<PERSON>", "app.components.screenReaderCurrency.VND": "<PERSON><PERSON>", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "<PERSON><PERSON>", "app.components.screenReaderCurrency.XAF": "Franco CFA dell'Africa centrale", "app.components.screenReaderCurrency.XAG": "Argento (un'oncia troy)", "app.components.screenReaderCurrency.XAU": "Oro (un'oncia troy)", "app.components.screenReaderCurrency.XBA": "Unità composita europea (EURCO)", "app.components.screenReaderCurrency.XBB": "Unità monetaria europea (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Unità di conto europea 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Unità di conto europea 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Dollaro dei Caraibi Orientali", "app.components.screenReaderCurrency.XDR": "Diritti speciali di prelievo", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Franco CFA dell'Africa occidentale", "app.components.screenReaderCurrency.XPD": "Palladio (un'oncia troy)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (un'oncia troy)", "app.components.screenReaderCurrency.XTS": "Codici riservati specificamente ai test", "app.components.screenReaderCurrency.XXX": "Nessuna valuta", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON> yemenita", "app.components.screenReaderCurrency.ZAR": "Rand sudafricano", "app.components.screenReaderCurrency.ZMW": "Kwacha dello Zambia", "app.components.screenReaderCurrency.amount": "Importo", "app.components.screenReaderCurrency.currency": "Valuta", "app.components.trendIndicator.lastQuarter2": "ultimo trimestre", "app.containers.AccessibilityStatement.applicability": "Questa dichiarazione di accessibilità si applica a un {demoPlatformLink} che è rappresentativo di questo sito Web; utilizza lo stesso codice sorgente e ha le stesse funzionalità.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Metodo di valutazione", "app.containers.AccessibilityStatement.assesmentText2022": "L'accessibilità di questo sito è stata valutata da un soggetto esterno non coinvolto nel processo di progettazione e sviluppo. La conformità del suddetto {demoPlatformLink} può essere identificata su questo {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "puoi cambiare le tue preferenze", "app.containers.AccessibilityStatement.changePreferencesText": "In qualsiasi momento, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Eccezioni di conformità", "app.containers.AccessibilityStatement.conformanceStatus": "Stato di conformità", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Ci sforziamo di rendere i nostri contenuti inclusivi per tutti. Tuttavia, in alcuni casi ci possono essere contenuti inaccessibili sulla piattaforma, come indicato di seguito:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "sito Web dimostrativo", "app.containers.AccessibilityStatement.email": "Email:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Strumenti di indagine integrati", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Gli strumenti di indagine integrati disponibili per l'uso su questa piattaforma sono software di terze parti e potrebbero non essere accessibili.", "app.containers.AccessibilityStatement.exception_1": "Le nostre piattaforme di impegno digitale facilitano i contenuti generati dagli utenti e pubblicati da individui e organizzazioni. È possibile che PDF, immagini o altri tipi di file, compresi quelli multimediali, vengano caricati sulla piattaforma come allegati o aggiunti nei campi di testo dagli utenti della piattaforma. Questi documenti potrebbero non essere completamente accessibili.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Accogliamo con favore il tuo feedback sull'accessibilità di questo sito. Contattateci tramite uno dei seguenti metodi:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Processo di feedback", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bruxelles, Belgio", "app.containers.AccessibilityStatement.headTitle": "Dichiarazione di accessibilità | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} si impegna a fornire una piattaforma accessibile a tutti gli utenti, indipendentemente dalla tecnologia o dalle capacità. Gli attuali standard di accessibilità pertinenti vengono rispettati nei nostri continui sforzi per ottimizzare l'accessibilità e l'usabilità delle nostre piattaforme per tutti gli utenti.", "app.containers.AccessibilityStatement.mapping": "Mappatura", "app.containers.AccessibilityStatement.mapping_1": "Le mappe della piattaforma soddisfano parzialmente gli standard di accessibilità. L'estensione della mappa, lo zoom e i widget dell'interfaccia utente possono essere controllati tramite tastiera durante la visualizzazione delle mappe. Gli amministratori possono anche configurare lo stile dei livelli delle mappe nel back office o utilizzando l'integrazione Esri per creare palette di colori e simbologia più accessibili. Anche l'uso di stili diversi di linee o poligoni (ad esempio linee tratteggiate) aiuterà a differenziare i livelli delle mappe laddove possibile; sebbene al momento non sia possibile configurare questo tipo di stile all'interno della nostra piattaforma, è possibile configurarlo se si utilizzano le mappe con l'integrazione Esri.", "app.containers.AccessibilityStatement.mapping_2": "Le mappe della piattaforma non sono completamente accessibili perché non presentano in modo udibile le mappe di base, i livelli delle mappe o le tendenze dei dati agli utenti che utilizzano gli screen reader. Le mappe completamente accessibili dovrebbero presentare in modo udibile gli strati della mappa e descrivere le tendenze rilevanti dei dati. Inoltre, il disegno di mappe a linee e poligoni nei sondaggi non è accessibile perché le forme non possono essere disegnate con la tastiera. Al momento non sono disponibili metodi di input alternativi a causa della complessità tecnica.", "app.containers.AccessibilityStatement.mapping_3": "Per rendere più accessibile il disegno di mappe a linee e poligoni, consigliamo di includere nella domanda del sondaggio o nella descrizione della pagina un'introduzione o una spiegazione di ciò che la mappa mostra e delle tendenze rilevanti. <PERSON><PERSON><PERSON>, si potrebbe prevedere una domanda di testo a risposta breve o lunga in modo che gli intervistati possano descrivere la loro risposta in termini semplici, se necessario (piuttosto che cliccare sulla mappa). Raccomandiamo inoltre di includere le informazioni di contatto del responsabile del progetto, in modo che i rispondenti che non sono in grado di compilare una domanda sulla mappa possano richiedere un metodo alternativo per rispondere alla domanda (ad esempio, un incontro video).", "app.containers.AccessibilityStatement.mapping_4": "Per i progetti e le proposte di Ideazione, c'è un'opzione per visualizzare gli input in una mappa, che non è accessibile. Tuttavia, per questi metodi è disponibile una visualizzazione alternativa a elenco degli input, che è accessibile.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "I nostri workshop online hanno un componente di streaming video live che attualmente non supporta i sottotitoli.", "app.containers.AccessibilityStatement.pageDescription": "Una dichiarazione sull'accessibilità di questo sito web", "app.containers.AccessibilityStatement.postalAddress": "Indirizzo postale:", "app.containers.AccessibilityStatement.publicationDate": "Data di pubblicazione", "app.containers.AccessibilityStatement.publicationDate2024": "Questa dichiarazione di accessibilità è stata pubblicata il 21 agosto 2024.", "app.containers.AccessibilityStatement.responsiveness": "Miriamo a rispondere al feedback entro 1-2 gior<PERSON>.", "app.containers.AccessibilityStatement.statusPageText": "pagina di stato", "app.containers.AccessibilityStatement.technologiesIntro": "L'accessibilità di questo sito si basa sulle seguenti tecnologie per funzionare:", "app.containers.AccessibilityStatement.technologiesTitle": "Tecnologie", "app.containers.AccessibilityStatement.title": "Dichiarazione di accessibilità", "app.containers.AccessibilityStatement.userGeneratedContent": "Contenuto generato dall'utente", "app.containers.AccessibilityStatement.workshops": "Laboratori", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Seleziona il progetto", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Utilizzando il Generatore di contenuti potrai utilizzare opzioni di layout più avanzate. Per le lingue in cui non è disponibile alcun contenuto nel Generatore di contenuti, verrà visualizzato il normale contenuto della descrizione del progetto.", "app.containers.AdminPage.ProjectDescription.linkText": "Modifica la descrizione nel Generatore di contenuti", "app.containers.AdminPage.ProjectDescription.saveError": "Qualcosa è andato storto durante il salvataggio della descrizione del progetto.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Usa il Content Builder per la descrizione", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Utilizzando il Content Builder potrai utilizzare opzioni di layout più avanzate.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "Visualizza il progetto", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Fine del sondaggio", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "<PERSON><PERSON>re un gruppo intelligente", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Gli utenti che corrispondono a tutte le seguenti condizioni saranno aggiunti automaticamente al gruppo:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Fornire almeno una regola", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Alcune condizioni sono incomplete", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Salva gruppo", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "La configurazione dei gruppi intelligenti non fa parte della tua licenza attuale. Contatta il tuo GovSuccess Manager per saperne di più.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Fornire un nome di gruppo", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "La verifica è disabilitata per la tua piattaforma, rimuovi la regola di verifica o contatta il supporto.", "app.containers.App.appMetaDescription": "Benvenuti nella piattaforma di partecipazione online di {orgName}. \nEsplora i progetti locali e partecipa alla discussione!", "app.containers.App.loading": "Caricamento...", "app.containers.App.metaTitle1": "Piattaforma di coinvolgimento dei cittadini | {orgName}", "app.containers.App.skipLinkText": "Vai al contenuto principale", "app.containers.AreaTerms.areaTerm": "area", "app.containers.AreaTerms.areasTerm": "aree", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Un account con questo indirizzo e-mail esiste già. <PERSON><PERSON>i uscire, accedere con questo indirizzo e-mail e verificare il tuo account nella pagina delle impostazioni.", "app.containers.Authentication.steps.AccessDenied.close": "<PERSON><PERSON>", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Non hai i requisiti per partecipare a questo processo.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Torna alla verifica del single sign-on", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Inserire un token", "app.containers.Authentication.steps.Invitation.token": "Gettone", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Hai già un account? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Accedi", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Le e-mail in questa categoria", "app.containers.CampaignsConsentForm.messageError": "C'è stato un errore nel salvare le tue preferenze e-mail.", "app.containers.CampaignsConsentForm.messageSuccess": "Le tue preferenze e-mail sono state salvate.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Che tipo di notifiche e-mail vuoi ricevere? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Notifiche", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "Torna alle impostazioni del profilo", "app.containers.ChangeEmail.confirmationModalTitle": "Confermare l'e-mail", "app.containers.ChangeEmail.emailEmptyError": "Fornire un indirizzo e-mail", "app.containers.ChangeEmail.emailInvalidError": "Fornire un indirizzo e-mail nel formato corretto, <NAME_EMAIL>.", "app.containers.ChangeEmail.emailRequired": "Inserire un indirizzo e-mail.", "app.containers.ChangeEmail.emailTaken": "Questa e-mail è già in uso.", "app.containers.ChangeEmail.emailUpdateCancelled": "L'aggiornamento via e-mail è stato annullato.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Per aggiornare l'e-mail, riavviare il processo.", "app.containers.ChangeEmail.helmetDescription": "Cambiare la pagina dell'e-mail", "app.containers.ChangeEmail.helmetTitle": "Cambiare l'e-mail", "app.containers.ChangeEmail.newEmailLabel": "Nuova e-mail", "app.containers.ChangeEmail.submitButton": "Invia", "app.containers.ChangeEmail.titleAddEmail": "Aggiungi il tuo indirizzo e-mail", "app.containers.ChangeEmail.titleChangeEmail": "Cambiare l'e-mail", "app.containers.ChangeEmail.updateSuccessful": "L'e-mail è stata aggiornata con successo.", "app.containers.ChangePassword.currentPasswordLabel": "Password attuale", "app.containers.ChangePassword.currentPasswordRequired": "Inserire la password attuale", "app.containers.ChangePassword.goHome": "Vai a casa", "app.containers.ChangePassword.helmetDescription": "Pagina di modifica della password", "app.containers.ChangePassword.helmetTitle": "Cam<PERSON>re la <PERSON>", "app.containers.ChangePassword.newPasswordLabel": "Nuova password", "app.containers.ChangePassword.newPasswordRequired": "Inserire la nuova password", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Fornire una password lunga almeno {minimumPasswordLength} caratteri.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "La password è stata aggiornata con successo", "app.containers.ChangePassword.passwordEmptyError": "Inserire la password", "app.containers.ChangePassword.passwordsDontMatch": "Confermare la nuova password", "app.containers.ChangePassword.titleAddPassword": "Aggiungere una password", "app.containers.ChangePassword.titleChangePassword": "Cam<PERSON>re la <PERSON>", "app.containers.Comments.a11y_commentDeleted": "Commento cancellato", "app.containers.Comments.a11y_commentPosted": "Commento inviato", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {nessun like} one {1 like} other {# Mi piace}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON> come", "app.containers.Comments.addCommentError": "Qualcosa è andato storto. Si prega di riprovare più tardi.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "Cancella questo commento", "app.containers.Comments.cancelCommentEdit": "Can<PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Scrivi una risposta...", "app.containers.Comments.commentCancelUpvote": "Undo", "app.containers.Comments.commentDeletedPlaceholder": "Questo commento è stato cancellato.", "app.containers.Comments.commentDeletionCancelButton": "Tieni il mio commento", "app.containers.Comments.commentDeletionConfirmButton": "Cancella il mio commento", "app.containers.Comments.commentLike": "Come", "app.containers.Comments.commentReplyButton": "Rispondi", "app.containers.Comments.commentsSortTitle": "Ordina i commenti per", "app.containers.Comments.completeProfileLinkText": "completa il tuo profilo", "app.containers.Comments.completeProfileToComment": "Per favore {completeRegistrationLink} per commentare.", "app.containers.Comments.confirmCommentDeletion": "Sei sicuro di voler cancellare questo commento? Non si può tornare indietro!", "app.containers.Comments.deleteComment": "Cancellare", "app.containers.Comments.deleteReasonDescriptionError": "Fornire maggiori informazioni sul motivo", "app.containers.Comments.deleteReasonError": "Fornire un motivo", "app.containers.Comments.deleteReason_inappropriate": "È inappropriato o offensivo", "app.containers.Comments.deleteReason_irrelevant": "<PERSON>o non è rilevante", "app.containers.Comments.deleteReason_other": "Altro motivo", "app.containers.Comments.editComment": "Modifica", "app.containers.Comments.guidelinesLinkText": "le linee guida della nostra comunità", "app.containers.Comments.ideaCommentBodyPlaceholder": "Scrivi il tuo commento qui", "app.containers.Comments.internalCommentingNudgeMessage": "La creazione di commenti interni non è inclusa nella tua licenza attuale. Contatta il tuo GovSuccess Manager per saperne di più.", "app.containers.Comments.internalConversation": "Conversazione interna", "app.containers.Comments.loadMoreComments": "Carica altri commenti", "app.containers.Comments.loadingComments": "Caricamento dei commenti...", "app.containers.Comments.loadingMoreComments": "Caricando più commenti...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Questo commento non è visibile agli utenti abituali", "app.containers.Comments.postInternalComment": "Invia un commento interno", "app.containers.Comments.postPublicComment": "Invia un commento pubblico", "app.containers.Comments.profanityError": "Ops! Se<PERSON>ra che il tuo post contenga un linguaggio che non soddisfa {guidelinesLink}. Cerchiamo di mantenere questo spazio sicuro per tutti. Per favore, modifica il tuo messaggio e prova di nuovo.", "app.containers.Comments.publicDiscussion": "Discussione pubblica", "app.containers.Comments.publishComment": "Pubblica il tuo commento", "app.containers.Comments.reportAsSpamModalTitle": "Perché vuoi segnalarlo come spam?", "app.containers.Comments.saveComment": "<PERSON><PERSON>", "app.containers.Comments.signInLinkText": "accedi", "app.containers.Comments.signInToComment": "Per favore {signInLink} per commentare.", "app.containers.Comments.signUpLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.verifyIdentityLinkText": "Verifica la tua identità", "app.containers.Comments.visibleToUsersPlaceholder": "Questo commento è visibile agli utenti abituali", "app.containers.Comments.visibleToUsersWarning": "I commenti pubblicati qui saranno visibili agli utenti abituali.", "app.containers.ContentBuilder.PageTitle": "Descrizione del progetto", "app.containers.CookiePolicy.advertisingContent": "Come {orgName}, non utilizziamo strumenti pubblicitari sulla nostra piattaforma di partecipazione.", "app.containers.CookiePolicy.advertisingTitle": "Pubblicità", "app.containers.CookiePolicy.analyticsContents": "I cookie analitici tracciano il comportamento dei visitatori, come ad esempio quali pagine vengono visitate e per quanto tempo. Possono anche raccogliere alcuni dati tecnici tra cui informazioni sul browser, posizione approssimativa e indirizzi IP. Usiamo questi dati solo internamente per continuare a migliorare l'esperienza complessiva dell'utente e il funzionamento della piattaforma. Tali dati possono anche essere condivisi tra Go Vocal e {orgName} per valutare e migliorare il coinvolgimento con i progetti sulla piattaforma. Si noti che i dati sono anonimi e utilizzati a livello aggregato - non ti identificano personalmente. Tuttavia, è possibile che se questi dati venissero combinati con altre fonti di dati, tale identificazione potrebbe avvenire.", "app.containers.CookiePolicy.analyticsTitle": "Analytics", "app.containers.CookiePolicy.cookiePolicyDescription": "Una spiegazione dettagliata di come utilizziamo i cookie su questa piattaforma", "app.containers.CookiePolicy.cookiePolicyTitle": "Politica dei cookie", "app.containers.CookiePolicy.essentialContent": "Alcuni cookie sono essenziali per garantire il corretto funzionamento di questa piattaforma. Questi cookie essenziali sono utilizzati principalmente per autenticare il tuo account quando visiti la piattaforma e per salvare la tua lingua preferita.", "app.containers.CookiePolicy.essentialTitle": "<PERSON><PERSON> es<PERSON>li", "app.containers.CookiePolicy.externalContent": "Alcune delle nostre pagine possono visualizzare contenuti da fornitori esterni, ad esempio, YouTube o Typeform. Non abbiamo il controllo su questi cookie di terze parti e la visualizzazione del contenuto da questi fornitori esterni può anche comportare l'installazione di cookie sul tuo dispositivo.", "app.containers.CookiePolicy.externalTitle": "<PERSON><PERSON>", "app.containers.CookiePolicy.functionalContents": "I cookie funzionali possono essere abilitati per i visitatori per ricevere notifiche sugli aggiornamenti e per accedere ai canali di supporto direttamente dalla piattaforma.", "app.containers.CookiePolicy.functionalTitle": "Funzionale", "app.containers.CookiePolicy.headCookiePolicyTitle": "Informativa sui cookie | {orgName}", "app.containers.CookiePolicy.intro": "Come la maggior parte dei siti web, utilizziamo i cookie per ottimizzare l'esperienza che tu e gli altri visitatori avete su questa piattaforma. Perché vogliamo essere completamente trasparenti sul perché.", "app.containers.CookiePolicy.manageCookiesDescription": "Puoi attivare o disattivare i cookie analitici, di marketing e funzionali in qualsiasi momento nelle tue preferenze sui cookie. Puoi anche cancellare manualmente o automaticamente i cookie esistenti tramite il tuo browser internet. Tuttavia, i cookie possono essere inseriti di nuovo dopo il tuo consenso in occasione di eventuali visite successive a questa piattaforma. Se non cancelli i cookie, le tue preferenze sui cookie vengono memorizzate per 60 giorni, dopodiché ti verrà chiesto nuovamente il consenso.", "app.containers.CookiePolicy.manageCookiesPreferences": "Vai al tuo {manageCookiesPreferencesButtonText} per vedere una lista completa delle integrazioni di terze parti utilizzate su questa piattaforma e per gestire le tue preferenze.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "impostazioni dei cookie", "app.containers.CookiePolicy.manageCookiesTitle": "Gestione dei tuoi cookie", "app.containers.CookiePolicy.viewPreferencesButtonText": "Impostazioni dei cookie", "app.containers.CookiePolicy.viewPreferencesText": "Le seguenti categorie di cookie potrebbero non essere applicabili a tutti i visitatori o piattaforme; visualizza il tuo {viewPreferencesButton} per un elenco completo delle integrazioni di terze parti applicabili a te.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Per cosa usiamo i cookie?", "app.containers.CustomPageShow.editPage": "Modifica pagina", "app.containers.CustomPageShow.goBack": "Torna indietro", "app.containers.CustomPageShow.notFound": "Pagina non trovata", "app.containers.DisabledAccount.bottomText": "È possibile accedere nuovamente a {date}.", "app.containers.DisabledAccount.termsAndConditions": "Termini e condizioni", "app.containers.DisabledAccount.text2": "Il tuo account sulla piattaforma di partecipazione di {orgName} è stato temporaneamente disabilitato per una violazione delle linee guida della comunità. Per ulteriori informazioni al riguardo, è possibile consultare il sito {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Il suo account è stato temporaneamente disabilitato", "app.containers.EventsShow.addToCalendar": "Aggiungi al calendario", "app.containers.EventsShow.editEvent": "Modifica evento", "app.containers.EventsShow.emailSharingBody2": "Partecipa a questo evento: {eventTitle}. Per saperne di più: {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Data e ora dell'evento", "app.containers.EventsShow.eventFrom2": "<PERSON> \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Torna indietro", "app.containers.EventsShow.goToProject": "Vai al progetto", "app.containers.EventsShow.haveRegistered": "si sono registrati", "app.containers.EventsShow.icsError": "Errore nel download del file ICS", "app.containers.EventsShow.linkToOnlineEvent": "Link all'evento online", "app.containers.EventsShow.locationIconAltText": "Posizione", "app.containers.EventsShow.metaTitle": "Evento: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Riunione online", "app.containers.EventsShow.onlineLinkIconAltText": "Link alla riunione online", "app.containers.EventsShow.registered": "registrato", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registranti} one {1 registrante} other {# registranti}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registranti", "app.containers.EventsShow.registrantsIconAltText": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.socialMediaSharingMessage": "Partecipa a questo evento: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# partecipante} other {# partecipanti}}", "app.containers.EventsViewer.allTime": "Per tutto il tempo", "app.containers.EventsViewer.date": "Data", "app.containers.EventsViewer.thisMonth2": "Il mese prossimo", "app.containers.EventsViewer.thisWeek2": "Settimana prossima", "app.containers.EventsViewer.today": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "Aggiungere un contributo", "app.containers.IdeaButton.addAPetition": "Aggiungi una petizione", "app.containers.IdeaButton.addAProject": "Aggiungere un progetto", "app.containers.IdeaButton.addAProposal": "Aggiungi una proposta", "app.containers.IdeaButton.addAQuestion": "Aggiungi una domanda", "app.containers.IdeaButton.addAnInitiative": "Aggiungi un'iniziativa", "app.containers.IdeaButton.addAnOption": "Aggiungi la tua opzione", "app.containers.IdeaButton.postingDisabled": "Attualmente non si accettano nuove candidature", "app.containers.IdeaButton.postingInNonActivePhases": "I nuovi invii possono essere aggiunti solo nelle fasi attive.", "app.containers.IdeaButton.postingInactive": "Attualmente non si accettano nuove iscrizioni.", "app.containers.IdeaButton.postingLimitedMaxReached": "Avete già completato questo sondaggio. Grazie per la risposta!", "app.containers.IdeaButton.postingNoPermission": "Attualmente non si accettano nuove candidature", "app.containers.IdeaButton.postingNotYetPossible": "Non si accettano ancora nuovi invii.", "app.containers.IdeaButton.signInLinkText": "accedi", "app.containers.IdeaButton.signUpLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.submitAnIssue": "Invia un numero", "app.containers.IdeaButton.submitYourIdea": "Invia la tua idea", "app.containers.IdeaButton.takeTheSurvey": "Prendi il sondaggio", "app.containers.IdeaButton.verificationLinkText": "Verifica la tua identità ora.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON>i tutto", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {nessun commento} one {1 commento} other {# commenti}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {nessun voto} one {1 voto} other {# voti}} fuori da {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Chiudi il pannello dei filtri", "app.containers.IdeaCards.a11y_totalItems": "Messaggi totali: {ideasCount}", "app.containers.IdeaCards.all": "<PERSON><PERSON>", "app.containers.IdeaCards.allStatuses": "Tutti gli stati", "app.containers.IdeaCards.contributions": "Contributi", "app.containers.IdeaCards.ideaTerm": "Idee", "app.containers.IdeaCards.initiatives": "Iniziative", "app.containers.IdeaCards.issueTerm": "<PERSON>i", "app.containers.IdeaCards.list": "Lista", "app.containers.IdeaCards.map": "Mappa", "app.containers.IdeaCards.mostDiscussed": "I più discussi", "app.containers.IdeaCards.newest": "<PERSON><PERSON> recente", "app.containers.IdeaCards.noFilteredResults": "<PERSON>essun risultato trovato. Si prega di provare un filtro o un termine di ricerca diverso.", "app.containers.IdeaCards.numberResults": "Risultati ({postCount})", "app.containers.IdeaCards.oldest": "Il più vecchio", "app.containers.IdeaCards.optionTerm": "Opzioni", "app.containers.IdeaCards.petitions": "Petizioni", "app.containers.IdeaCards.popular": "I più votati", "app.containers.IdeaCards.projectFilterTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.projectTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.proposals": "Proposte", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "Casuale", "app.containers.IdeaCards.resetFilters": "Azzerare i filtri", "app.containers.IdeaCards.showXResults": "Mostra {ideasCount, plural, one {# risultato} other {# risultato}}", "app.containers.IdeaCards.sortTitle": "Ordinamento", "app.containers.IdeaCards.statusTitle": "Stato", "app.containers.IdeaCards.statusesTitle": "Stato", "app.containers.IdeaCards.topics": "Argomenti", "app.containers.IdeaCards.topicsTitle": "Argomenti", "app.containers.IdeaCards.trending": "Trending", "app.containers.IdeaCards.tryDifferentFilters": "<PERSON>essun risultato trovato. Si prega di provare un filtro o un termine di ricerca diverso.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} commenti} one {{ideasCount} commento} other {{ideasCount} commenti}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contributi} one {{ideasCount} contributo} other {{ideasCount} contributi}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} idee} one {{ideasCount} idea} other {{ideasCount} idee}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} iniziative} one {{ideasCount} iniziativa} other {{ideasCount} iniziative}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opzioni} one {{ideasCount} opzione} other {{ideasCount} opzioni}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petizioni} one {{ideasCount} petizioni} other {{ideasCount} petizioni}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} progetti} one {{ideasCount} progetto} other {{ideasCount} progetti}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} proposte} one {{ideasCount} proposta} other {{ideasCount} proposte}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} domande} one {{ideasCount} domanda} other {{ideasCount} domande}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# risultato} other {# risultato}}", "app.containers.IdeasEditPage.contributionFormTitle": "Modifica contributo", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "Il caricamento di uno o più file non è riuscito. Si prega di controllare la dimensione e il formato del file e riprovare.", "app.containers.IdeasEditPage.formTitle": "Modifica idea", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Modifica il tuo post. Aggiungi nuove informazioni e cambia quelle vecchie.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Modifica {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Modifica l'iniziativa", "app.containers.IdeasEditPage.issueFormTitle": "Modifica problema", "app.containers.IdeasEditPage.optionFormTitle": "Modifica opzione", "app.containers.IdeasEditPage.petitionFormTitle": "Modifica petizione", "app.containers.IdeasEditPage.projectFormTitle": "Modifica progetto", "app.containers.IdeasEditPage.proposalFormTitle": "Modifica la proposta", "app.containers.IdeasEditPage.questionFormTitle": "Modifica domanda", "app.containers.IdeasEditPage.save": "<PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "C'è stato un problema nell'invio del modulo. Si prega di controllare eventuali errori e riprovare.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "<PERSON><PERSON> gli <PERSON> pubblicati", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Esplora tutti gli input pubblicati sulla piattaforma di partecipazione di {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Messaggi | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Messaggi", "app.containers.IdeasIndexPage.loadMore": "Carica di più...", "app.containers.IdeasIndexPage.loading": "Caricamento...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Per impostazione predefinita i tuoi invii saranno associati al tuo profilo, a meno che tu non selezioni questa opzione.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Pubblica in forma anonima", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Visibilità del profilo", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Questo sondaggio non è attualmente aperto alle risposte. Ti invitiamo a tornare al progetto per maggiori informazioni.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Questo sondaggio non è attualmente attivo.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Torna al progetto", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Hai già completato questo sondaggio.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Sondaggio inviato", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Grazie per la tua risposta!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "La descrizione del contributo deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Il corpo dell'idea deve contenere più di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Il titolo del contributo deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Il titolo del contributo deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Seleziona almeno un cosponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "La descrizione dell'idea deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "La descrizione dell'idea deve contenere più di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Si prega di fornire una descrizione", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Il titolo dell'idea deve essere inferiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Il titolo dell'idea deve contenere più di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "La descrizione dell'iniziativa deve essere inferiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "La descrizione dell'iniziativa deve essere lunga più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Il titolo dell'iniziativa deve essere inferiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Il titolo dell'iniziativa deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "La descrizione del problema deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "La descrizione del problema deve contenere più di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Il titolo del problema deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Il titolo del numero deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_number_required": "Questo campo è obbligatorio, inserisci un numero valido", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "La descrizione dell'opzione deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "La descrizione dell'opzione deve avere una lunghezza superiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Il titolo dell'opzione deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Il titolo dell'opzione deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Selezionare almeno un tag", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "La descrizione della petizione deve essere inferiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "La descrizione della petizione deve essere lunga più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Il titolo della petizione deve essere inferiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Il titolo della petizione deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "La descrizione del progetto deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "La descrizione del progetto deve contenere più di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Il titolo del progetto deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Il titolo del progetto deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "La descrizione della proposta deve essere inferiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "La descrizione della proposta deve essere lunga più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Il titolo della proposta deve essere inferiore a {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Il titolo della proposta deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Inserire un numero", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Inserire un numero", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "La descrizione della domanda deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "La descrizione del domanda deve contenere più di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Il titolo della domanda deve contenere meno di {limit} caratteri", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Il titolo della domanda deve essere lungo più di {limit} caratteri.", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Si prega di fornire un titolo", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "La descrizione del contributo deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "La descrizione del contributo deve contenere almeno 30 caratteri", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Il titolo del contributo deve essere lungo meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Il titolo del contributo deve essere lungo almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "La descrizione dell'idea deve essere lunga meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "La descrizione dell'idea deve essere lunga almeno 30 caratteri", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Si prega di fornire un titolo", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Il titolo dell'idea deve essere lungo meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "La descrizione dell'idea deve essere lunga almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_includes_banned_words": "<PERSON><PERSON><PERSON> aver usato una o più parole che sono considerate bestemmie da {guidelinesLink}. Per favore modifica il tuo testo per rimuovere qualsiasi bestemmia che potrebbe essere presente.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "La descrizione dell'iniziativa deve essere inferiore a 80 caratteri.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "La descrizione dell'iniziativa deve essere lunga almeno 30 caratteri.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Il titolo dell'iniziativa deve avere una lunghezza inferiore a 80 caratteri.", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Il titolo dell'iniziativa deve essere lungo almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "La descrizione del problema deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "La descrizione del problema deve contenere meno di 30 caratteri", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Il titolo del problema deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Il titolo del problema deve contenere almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "La descrizione dell’opzione deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "La descrizione dell’opzione deve contenere meno di 30 caratteri", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Il titolo dell’opzione deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Il titolo dell’opzione deve contenere almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "La descrizione della petizione deve essere inferiore a 80 caratteri.", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "La descrizione della petizione deve essere lunga almeno 30 caratteri.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Il titolo della petizione deve essere di lunghezza inferiore a 80 caratteri", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Il titolo della petizione deve essere lungo almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "La descrizione del progetto deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "La descrizione del progetto deve contenere meno di 30 caratteri", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Il titolo del progetto deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Il titolo del progetto deve contenere almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "La descrizione della proposta deve essere inferiore a 80 caratteri.", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "La descrizione della proposta deve essere lunga almeno 30 caratteri.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Il titolo della proposta deve essere di lunghezza inferiore a 80 caratteri.", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Il titolo della proposta deve essere lungo almeno 10 caratteri", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Si prega di fornire una descrizione", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "La descrizione della domanda deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "La descrizione della domanda deve contenere meno di 30 caratteri", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Il titolo della domanda deve contenere meno di 80 caratteri", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Il titolo della domanda deve contenere almeno 10 caratteri", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, voglio and<PERSON>", "app.containers.IdeasNewPage.contributionMetaTitle1": "Aggiungi un nuovo contributo al progetto | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Modifica sondaggio", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Pubblica una presentazione e unisciti alla conversazione sulla piattaforma di partecipazione di {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Aggiungi una nuova idea al progetto | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Aggiungi una nuova iniziativa al progetto | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Aggiungi un nuovo problema al progetto | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Sei sicuro di volertene andare?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Le tue bozze di risposta sono state salvate privatamente e potrai tornare a completarle in seguito.", "app.containers.IdeasNewPage.leaveSurvey": "Sondaggio sul congedo", "app.containers.IdeasNewPage.leaveSurveyText": "Le tue risposte non verranno salvate.", "app.containers.IdeasNewPage.optionMetaTitle1": "Aggiungi una nuova opzione al progetto | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Aggiungi una nuova petizione al progetto | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Aggiungi un nuovo progetto al progetto | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Aggiungi una nuova proposta al progetto | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Aggiungi una nuova domanda al progetto | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Accetta l'invito", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Invito alla co-sponsorizzazione", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Co-sponsor", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Sei stato invitato a diventare co-sponsor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invito acc<PERSON>", "app.containers.IdeasShow.Cosponsorship.pending": "in attesa", "app.containers.IdeasShow.MetaInformation.attachments": "Allegati", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} su {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Stato attuale", "app.containers.IdeasShow.MetaInformation.location": "Posizione", "app.containers.IdeasShow.MetaInformation.postedBy": "Inviato da", "app.containers.IdeasShow.MetaInformation.similar": "Ingressi simili", "app.containers.IdeasShow.MetaInformation.topics": "Argomenti", "app.containers.IdeasShow.commentCTA": "Aggiungere un commento", "app.containers.IdeasShow.contributionEmailSharingBody": "Sostenete questo contributo '{postTitle}' a {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Sostieni questo contributo: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Grazie per aver inviato il tuo contributo!", "app.containers.IdeasShow.contributionTwitterMessage": "Sostieni questo contributo: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Sostieni questo contributo: {postTitle}", "app.containers.IdeasShow.currentStatus": "Stato attuale", "app.containers.IdeasShow.deletedUser": "<PERSON>re scon<PERSON>", "app.containers.IdeasShow.ideaEmailSharingBody": "Sostenete la mia idea '{ideaTitle}' a {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Sostieni la mia idea: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Appoggia questa idea: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Appoggia questa idea: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Sostieni questo contributo: {postTitle}", "app.containers.IdeasShow.imported": "<PERSON><PERSON>rta<PERSON>", "app.containers.IdeasShow.importedTooltip": "Questo {inputTerm} è stato raccolto offline e caricato automaticamente sulla piattaforma.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Sostieni questa iniziativa '{ideaTitle}' su {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Sostieni questa iniziativa: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Grazie per aver inviato la tua iniziativa!", "app.containers.IdeasShow.initiativeTwitterMessage": "Sostieni questa iniziativa: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Sostieni questa iniziativa: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Sostenete questo numero '{postTitle}' a {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Supporta questo problema: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Grazie per aver inviato il tuo contributo!", "app.containers.IdeasShow.issueTwitterMessage": "Supporta questo problema: {postTitle}", "app.containers.IdeasShow.metaTitle": "Ingresso: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Supporta questa opzione '{postTitle}' a {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Supporta questa opzione: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "La tua opzione è stata pubblicata con successo!", "app.containers.IdeasShow.optionTwitterMessage": "Supporta questa opzione: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Supporta questa opzione: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Sostieni questa petizione '{ideaTitle}' su {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Sostieni questa petizione: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Grazie per aver inviato la tua petizione!", "app.containers.IdeasShow.petitionTwitterMessage": "Sostieni questa petizione: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Sostieni questa petizione: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Sostenete questo progetto '{postTitle}' a {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Supporta questo progetto: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Grazie per aver presentato il tuo progetto!", "app.containers.IdeasShow.projectTwitterMessage": "Supporta questo progetto: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Supporta questo progetto: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Sostieni questa proposta '{ideaTitle}' su {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Appoggia questa proposta: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Grazie per aver inviato la tua proposta!", "app.containers.IdeasShow.proposalTwitterMessage": "Appoggia questa proposta: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Appoggia questa proposta: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Tempo rimasto per votare:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} su {votingThreshold} voti necessari", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "<PERSON><PERSON><PERSON> il voto", "app.containers.IdeasShow.proposals.VoteControl.days": "<PERSON>ior<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "le nostre linee guida", "app.containers.IdeasShow.proposals.VoteControl.hours": "ore", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Stato e voti", "app.containers.IdeasShow.proposals.VoteControl.minutes": "mins", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Ulteriori informazioni", "app.containers.IdeasShow.proposals.VoteControl.vote": "Vota", "app.containers.IdeasShow.proposals.VoteControl.voted": "Votato", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Riceverai una notifica quando questa iniziativa passerà alla fase successiva. {x, plural, =0 {C'è {xDays} a sinistra.} one {C'è {xDays} a sinistra.} other {Ci sono {xDays} a sinistra.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Il tuo voto è stato inviato!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Purtroppo non puoi votare su questa proposta. Leggi il perché su {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {meno di un giorno} one {un giorno} other {# giorni}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {nessun voto} one {1 voto} other {# voti}}", "app.containers.IdeasShow.questionEmailSharingBody": "Partecipa alla discussione su questa domanda '{postTitle}' a {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Partecipa alla discussione: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "La tua domanda è stata pubblicata con successo!", "app.containers.IdeasShow.questionTwitterMessage": "Partecipa alla discussione: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Partecipa alla discussione: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Perché vuoi segnalarlo come spam?", "app.containers.IdeasShow.share": "Condi<PERSON><PERSON>", "app.containers.IdeasShow.sharingModalSubtitle": "Raggiungi più persone e fai sentire la tua voce.", "app.containers.IdeasShow.sharingModalTitle": "Grazie per aver presentato la tua idea!", "app.containers.Navbar.completeOnboarding": "Completa l'inserimento in azienda", "app.containers.Navbar.completeProfile": "<PERSON>ilo completo", "app.containers.Navbar.confirmEmail2": "Conferma l'e-mail", "app.containers.Navbar.unverified": "Non verificato", "app.containers.Navbar.verified": "Verificato", "app.containers.NewAuthModal.beforeYouFollow": "Prima di seguire", "app.containers.NewAuthModal.beforeYouParticipate": "Prima di partecipare", "app.containers.NewAuthModal.completeYourProfile": "Completate il vostro profilo", "app.containers.NewAuthModal.confirmYourEmail": "Confermare l'e-mail", "app.containers.NewAuthModal.logIn": "Accedi", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Per continuare, leggere i termini sotto riportati.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Completate il vostro profilo.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Tornare alle opzioni di accesso", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Non avete un account? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Il codice deve essere di 4 cifre.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Continua con FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Su questa piattaforma non sono abilitati metodi di autenticazione.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON><PERSON><PERSON><PERSON>, l'utente accetta di ricevere e-mail da questa piattaforma. È possibile selezionare le e-mail che si desidera ricevere nella pagina \"Le mie impostazioni\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Email", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Fornire un indirizzo e-mail nel formato corretto, <NAME_EMAIL>.", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Fornire un indirizzo e-mail", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Immettere l'indirizzo e-mail per continuare.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Hai dimenticato la password?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Accedere al proprio account: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Inserire la password", "app.containers.NewAuthModal.steps.Password.password": "Password", "app.containers.NewAuthModal.steps.Password.rememberMe": "Ricordati di me", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Non selezionare se si utilizza un computer pubblico", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON> fatto", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Ora continuate a partecipare.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "La tua identità è stata verificata. Ora sei un membro a pieno titolo della comunità di questa piattaforma.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Ora sei verificato!", "app.containers.NewAuthModal.steps.close": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.continue": "Continua", "app.containers.NewAuthModal.whatAreYouInterestedIn": "A cosa sei interessato?", "app.containers.NewAuthModal.youCantParticipate": "Non puoi partecipare", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {no unviewed notifications} one {1 unviewed notification} other {# unviewed notifications}}", "app.containers.NotificationMenu.adminRightsReceived": "Ora sei un amministratore della piattaforma", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Il tuo commento su \"{postTitle}\" è stato cancellato da un amministratore perché\n      {reasonCode, select, irrelevant {è irrilevante} inappropriate {il suo contenuto è inappropriato} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} ha accettato l'invito alla co-sponsorizzazione", "app.containers.NotificationMenu.deletedUser": "<PERSON><PERSON>", "app.containers.NotificationMenu.error": "Impossibile caricare le notifiche", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} ha commentato internamente un input che ti è stato assegnato", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} ha commentato internamente un input che ha commentato internamente", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} hai commentato internamente un input in un progetto che gestisci", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} commentare internamente un input non assegnato in un progetto non gestito", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} ha commentato il tuo commento interno", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} ti ha invitato a co-sponsorizzare un contributo", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} ti ha invitato a co-sponsorizzare un'idea", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} ti ha invitato a sponsorizzare una proposta", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} ti ha invitato a co-sponsorizzare una questione", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} ti ha invitato a co-sponsorizzare un'opzione", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} ti ha invitato a co-sponsorizzare una petizione", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} ti ha invitato a co-sponsorizzare un progetto", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} ti ha invitato a co-sponsorizzare una proposta", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} ti ha invitato a sponsorizzare una domanda", "app.containers.NotificationMenu.loadMore": "Carica di più...", "app.containers.NotificationMenu.loading": "Caricamento notifiche...", "app.containers.NotificationMenu.mentionInComment": "{name} ti ha menzionato in un commento", "app.containers.NotificationMenu.mentionInInternalComment": "{name} ti ha citato in un commento interno", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} ti ha menzionato in un aggiornamento ufficiale", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Non hai inviato il sondaggio", "app.containers.NotificationMenu.noNotifications": "Non hai ancora nessuna notifica", "app.containers.NotificationMenu.notificationsLabel": "Notifiche", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} ha dato un aggiornamento ufficiale su un contributo che segui", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} ha dato un aggiornamento ufficiale su un'idea che segui", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} ha fornito un aggiornamento ufficiale su un'iniziativa che segui", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} ha fornito un aggiornamento ufficiale su un argomento che segui", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} ha fornito un aggiornamento ufficiale su un'opzione che segui", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} ha fornito un aggiornamento ufficiale su una petizione che segui", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} ha fornito un aggiornamento ufficiale su un progetto che segui", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} ha fornito un aggiornamento ufficiale su una proposta che segui", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} ha fornito un aggiornamento ufficiale su una domanda che segui", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} ti è stato assegnato", "app.containers.NotificationMenu.projectModerationRightsReceived": "Ora sei un project manager di {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} è entrato in una nuova fase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} entrerà in una nuova fase su {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "È stato pubblicato un nuovo progetto", "app.containers.NotificationMenu.projectReviewRequest": "{name} ha richiesto l'approvazione per la pubblicazione del progetto \"{projectTitle}\".", "app.containers.NotificationMenu.projectReviewStateChange": "{name} approvato \"{projectTitle}\" per la pubblicazione", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} lo stato è cambiato in {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} ha raggiunto la soglia di voto", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} ha accettato il tuo invito", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} ha commentato un contributo che segui", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} ha commentato un'idea che segui", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} ha commentato un'iniziativa che segui", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} ha commentato un argomento che segui", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} ha commentato un'opzione che segui", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} ha commentato una petizione che segui", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} ha commentato un progetto che segui", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} ha commentato una proposta che segui", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} ha commentato una domanda che segui", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} ha segnalato \"{postTitle}\" come spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} ha reagito al tuo commento", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} ha segnalato come spam un commento su \"{postTitle}", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Non hai inviato i tuoi voti", "app.containers.NotificationMenu.votingBasketSubmitted": "Hai votato con successo", "app.containers.NotificationMenu.votingLastChance": "Ultima possibilità di votare per {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} Risultati del voto rivelati", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} assegnato {postTitle} a te", "app.containers.PasswordRecovery.emailError": "Questa non sembra un'email valida", "app.containers.PasswordRecovery.emailLabel": "Email", "app.containers.PasswordRecovery.emailPlaceholder": "Il mio indirizzo e-mail", "app.containers.PasswordRecovery.helmetDescription": "Pagina di reimpostazione della password", "app.containers.PasswordRecovery.helmetTitle": "<PERSON><PERSON><PERSON><PERSON> la tua <PERSON>", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Se questo indirizzo e-mail è registrato sulla piattaforma, è stato inviato un link per la reimpostazione della password.", "app.containers.PasswordRecovery.resetPassword": "Invia un link per la reimpostazione della password", "app.containers.PasswordRecovery.submitError": "Non abbiamo trovato un account collegato a questa email. Puoi provare ad iscriverti invece.", "app.containers.PasswordRecovery.subtitle": "Dove possiamo inviare un link per scegliere una nuova password?", "app.containers.PasswordRecovery.title": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordReset.helmetDescription": "Pagina di reimpostazione della password", "app.containers.PasswordReset.helmetTitle": "<PERSON><PERSON><PERSON><PERSON> la tua <PERSON>", "app.containers.PasswordReset.login": "Accedi", "app.containers.PasswordReset.passwordError": "La password deve essere lunga almeno 8 caratteri", "app.containers.PasswordReset.passwordLabel": "Password", "app.containers.PasswordReset.passwordPlaceholder": "Nuova password", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "La password è stata aggiornata con successo.", "app.containers.PasswordReset.pleaseLogInMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> il login con la nuova password.", "app.containers.PasswordReset.requestNewPasswordReset": "Richiedere una nuova reimpostazione della password", "app.containers.PasswordReset.submitError": "Qualcosa è andato storto. Si prega di riprovare più tardi.", "app.containers.PasswordReset.title": "<PERSON><PERSON><PERSON><PERSON> la tua <PERSON>", "app.containers.PasswordReset.updatePassword": "Conferma la nuova password", "app.containers.ProjectFolderCards.allProjects": "<PERSON><PERSON> i progetti", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} sta attualmente lavorando su", "app.containers.ProjectFolderShowPage.editFolder": "Modifica cartella", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informazioni su questo progetto", "app.containers.ProjectFolderShowPage.metaTitle1": "Cartella: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON><PERSON> al<PERSON>", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON><PERSON> meno", "app.containers.ProjectFolderShowPage.share": "Condi<PERSON><PERSON>", "app.containers.Projects.PollForm.document": "Documento", "app.containers.Projects.PollForm.formCompleted": "Grazie per aver risposto a questo sondaggio!", "app.containers.Projects.PollForm.maxOptions": "massimo. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Hai già fatto questo sondaggio.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "<PERSON>o sondaggio può essere effettuato solo quando questa fase è attiva.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Questo sondaggio non è attualmente abilitato", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Attualmente è impossibile fare questo sondaggio.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Il sondaggio non è più disponibile poiché questo progetto non è più attivo.", "app.containers.Projects.PollForm.sendAnswer": "Invia", "app.containers.Projects.a11y_phase": "Fase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Panoramica delle fasi", "app.containers.Projects.a11y_titleInputs": "<PERSON><PERSON> gli input presentati a questo progetto", "app.containers.Projects.a11y_titleInputsPhase": "<PERSON><PERSON> gli input presentati in questa fase", "app.containers.Projects.accessRights": "<PERSON><PERSON><PERSON> di accesso", "app.containers.Projects.addedToBasket": "Aggiunto al tuo carrello", "app.containers.Projects.allocateBudget": "Allocate il vostro budget", "app.containers.Projects.archived": "Archiviato", "app.containers.Projects.basketSubmitted": "Il tuo cestino è stato inviato!", "app.containers.Projects.contributions": "Contributi", "app.containers.Projects.createANewPhase": "<PERSON>rea una nuova fase", "app.containers.Projects.currentPhase": "Fase attuale", "app.containers.Projects.document": "Documento", "app.containers.Projects.editProject": "Modifica progetto", "app.containers.Projects.emailSharingBody": "Cosa ne pensate di questa iniziativa? Votala e condividi la discussione su {initiativeUrl} per far sentire la tua voce!", "app.containers.Projects.emailSharingSubject": "Sostieni la mia iniziativa: {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON> {date}", "app.containers.Projects.events": "Eventi", "app.containers.Projects.header": "<PERSON><PERSON><PERSON>", "app.containers.Projects.ideas": "Idee", "app.containers.Projects.information": "Informazioni", "app.containers.Projects.initiatives": "Iniziative", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Esamina il documento", "app.containers.Projects.invisibleTitlePhaseAbout": "<PERSON><PERSON><PERSON><PERSON> a questa fase", "app.containers.Projects.invisibleTitlePoll": "Fai il sondaggio", "app.containers.Projects.invisibleTitleSurvey": "Prendi il sondaggio", "app.containers.Projects.issues": "<PERSON>i", "app.containers.Projects.liveDataMessage": "Stai visualizzando i dati in tempo reale. I conteggi dei partecipanti sono continuamente aggiornati per gli amministratori. Gli utenti abituali visualizzano i dati nella cache, il che può comportare leggere differenze nei numeri.", "app.containers.Projects.location": "Posizione:", "app.containers.Projects.manageBasket": "Gestire il carrello", "app.containers.Projects.meetMinBudgetRequirement": "Soddisfa il budget minimo per presentare il tuo carrello.", "app.containers.Projects.meetMinSelectionRequirement": "Soddisfa la selezione richiesta per inviare il tuo carrello.", "app.containers.Projects.metaTitle1": "Progetto: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Budget minimo richiesto", "app.containers.Projects.myBasket": "Basket", "app.containers.Projects.navPoll": "Sondaggio", "app.containers.Projects.navSurvey": "Sondaggio", "app.containers.Projects.newPhase": "Nuova fase", "app.containers.Projects.nextPhase": "Nuova fase", "app.containers.Projects.noEndDate": "Nessuna data di scadenza", "app.containers.Projects.noItems": "Non hai ancora selezionato nessun articolo", "app.containers.Projects.noPastEvents": "Nessun evento passato da visualizzare", "app.containers.Projects.noPhaseSelected": "Nessuna fase selezionata", "app.containers.Projects.noUpcomingOrOngoingEvents": "Al momento non sono programmati eventi imminenti o in corso.", "app.containers.Projects.offlineVotersTooltip": "Questo numero non riflette alcun conteggio di elettori offline.", "app.containers.Projects.options": "Opzioni", "app.containers.Projects.participants": "Partecipanti", "app.containers.Projects.participantsTooltip4": "Questo numero riflette anche i sondaggi anonimi inviati. L'invio di sondaggi anonimi è possibile se i sondaggi sono aperti a tutti (vedi la scheda {accessRightsLink} per questo progetto).", "app.containers.Projects.pastEvents": "Eventi passati", "app.containers.Projects.petitions": "Petizioni", "app.containers.Projects.phases": "Fasi", "app.containers.Projects.previousPhase": "<PERSON>ase precedente", "app.containers.Projects.project": "Progetto", "app.containers.Projects.projectTwitterMessage": "Fai sentire la tua voce! Partecipa a {projectName} | {orgName}", "app.containers.Projects.projects": "<PERSON><PERSON><PERSON>", "app.containers.Projects.proposals": "Proposte", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON><PERSON><PERSON> meno", "app.containers.Projects.readMore": "Leggi di più", "app.containers.Projects.removeItem": "<PERSON><PERSON><PERSON><PERSON> articolo", "app.containers.Projects.requiredSelection": "Selezione richiesta", "app.containers.Projects.reviewDocument": "Esamina il documento", "app.containers.Projects.seeTheContributions": "Vedere i contributi", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON><PERSON> le idee", "app.containers.Projects.seeTheInitiatives": "Guarda le iniziative", "app.containers.Projects.seeTheIssues": "Vedere i problemi", "app.containers.Projects.seeTheOptions": "Vedere le opzioni", "app.containers.Projects.seeThePetitions": "Vedi le petizioni", "app.containers.Projects.seeTheProjects": "<PERSON><PERSON><PERSON> i progetti", "app.containers.Projects.seeTheProposals": "<PERSON>edi le proposte", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON> le do<PERSON>e", "app.containers.Projects.seeUpcomingEvents": "Vedi i prossimi eventi", "app.containers.Projects.share": "Condi<PERSON><PERSON>", "app.containers.Projects.shareThisProject": "Condividi questo progetto", "app.containers.Projects.submitMyBasket": "Submit basket", "app.containers.Projects.survey": "Sondaggio", "app.containers.Projects.takeThePoll": "Fai il sondaggio", "app.containers.Projects.takeTheSurvey": "Prendi il sondaggio", "app.containers.Projects.timeline": "Timeline", "app.containers.Projects.upcomingAndOngoingEvents": "Eventi imminenti e in corso", "app.containers.Projects.upcomingEvents": "Prossimi eventi", "app.containers.Projects.whatsAppMessage": "{projectName} | dalla piattaforma di partecipazione di {orgName}", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "Esplora tutti i progetti in corso di {orgName} per capire come puoi partecipare.\n Vieni a discutere dei progetti locali che ti interessano di più.", "app.containers.ProjectsIndexPage.metaTitle1": "<PERSON><PERSON><PERSON> | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Voglio fare volontariato", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Si prega di {signInLink} o {signUpLink} prima per fare volontariato per questa attività", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "La partecipazione a questa attività non è attualmente aperta.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "accedi", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Ritiro la mia offerta di volontariato", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {nessun volontario} one {# volontario} other {# volontari}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Attenzione: L'indagine incorporata potrebbe presentare problemi di accessibilità per gli utenti di screenreader. In caso di problemi, contatta l'amministratore della piattaforma per ricevere un link all'indagine dalla piattaforma originale. In alternativa, puoi richiedere altri modi per compilare l'indagine.", "app.containers.ProjectsShowPage.process.survey.survey": "Sondaggio", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Per sapere se puoi partecipare a questo sondaggio, ti preghiamo di {logInLink} accedere prima alla piattaforma.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "<PERSON>o sondaggio può essere fatto solo quando questa fase della linea temporale è attiva.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Si prega di {completeRegistrationLink} per partecipare al sondaggio.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "Questo sondaggio non è attualmente abilitato", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "La partecipazione a questo sondaggio richiede la verifica della tua identità. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Il sondaggio non è più disponibile perché il progetto non è più attivo.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "completa la registrazione", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "accedi", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verifica subito il tuo account.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Solo alcuni utenti possono esaminare questo documento. Ti invitiamo a visitare prima {signUpLink} o {logInLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Questo documento può essere rivisto solo quando questa fase è attiva.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Per favore, {completeRegistrationLink} per esaminare il documento.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Purtroppo non hai il diritto di esaminare questo documento.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "L'esame di questo documento richiede la verifica del tuo account. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Il documento non è più disponibile perché il progetto non è più attivo.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 scelta} other {# scelte}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "La percentuale di partecipanti che ha scelto questa opzione.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "La percentuale di voti totali ricevuti da questa opzione.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Costo:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Mostra di più", "app.containers.ReactionControl.a11y_likesDislikes": "Totale Mi piace: {likesCount}, totale dislikes: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Hai cancellato l'antipatia per questo ingresso con successo.", "app.containers.ReactionControl.cancelLikeSuccess": "Hai cancellato il tuo like per questo input con successo.", "app.containers.ReactionControl.dislikeSuccess": "Questo input non ti è piaciuto.", "app.containers.ReactionControl.likeSuccess": "Questo input ti è piaciuto.", "app.containers.ReactionControl.reactionErrorSubTitle": "A causa di un errore non è stato possibile registrare la tua reazione. Riprova tra qualche minuto.", "app.containers.ReactionControl.reactionSuccessTitle": "La tua reazione è stata registrata con successo!", "app.containers.ReactionControl.vote": "Vota", "app.containers.ReactionControl.voted": "Votato", "app.containers.SearchInput.a11y_cancelledPostingComment": "Commento cancellato.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} i commenti sono stati caricati.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# gli eventi sono stati caricati} one {# gli eventi sono stati caricati} other {# gli eventi sono stati caricati}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# i risultati sono stati caricati} one {# i risultati sono stati caricati} other {# i risultati sono stati caricati}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# i risultati della ricerca sono stati caricati} one {# i risultati della ricerca sono stati caricati} other {# i risultati della ricerca sono stati caricati}}.", "app.containers.SearchInput.removeSearchTerm": "Rimuovi il termine di ricerca", "app.containers.SearchInput.searchAriaLabel": "Cerca", "app.containers.SearchInput.searchLabel": "Cerca", "app.containers.SearchInput.searchPlaceholder": "Cerca", "app.containers.SearchInput.searchTerm": "Termine di ricerca: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect è la soluzione proposta dallo Stato francese per assicurare e semplificare l'iscrizione a più di 700 servizi online.", "app.containers.SignIn.or": "Oppure", "app.containers.SignIn.signInError": "Le informazioni fornite non sono corrette. Clicca su \"Hai dimenticato la password?\" per reimpostare la tua password.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Usa FranceConnect per accedere, iscriverti o verificare il tuo account.", "app.containers.SignIn.whatIsFranceConnect": "Cos'è France Connect?", "app.containers.SignUp.adminOptions2": "Per amministratori e project manager", "app.containers.SignUp.backToSignUpOptions": "Torna alle opzioni di accesso", "app.containers.SignUp.continue": "Continua", "app.containers.SignUp.emailConsent": "<PERSON><PERSON><PERSON><PERSON><PERSON>, si accetta di ricevere e-mail da questa piattaforma. Puoi selezionare quali email desideri ricevere nella pagina \"Le mie impostazioni\".", "app.containers.SignUp.emptyFirstNameError": "Inser<PERSON>ci il tuo nome", "app.containers.SignUp.emptyLastNameError": "Inserisci il tuo cognome", "app.containers.SignUp.firstNamesLabel": "Nome", "app.containers.SignUp.goToLogIn": "Hai già un account? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Ho letto e accetto il sito {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Ho letto e accetto il sito {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Accetto che i dati vengano utilizzati su mitgestalten.wien.gv.at. Ulteriori informazioni sono disponibili all’indirizzo {link}.", "app.containers.SignUp.invitationErrorText": "Il tuo invito è scaduto o è già stato utilizzato. Se hai già utilizzato il link di invito per creare un account, prova ad accedere. Altrimenti, registrati per creare un nuovo account.", "app.containers.SignUp.lastNameLabel": "Nome e cognome", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Se<PERSON><PERSON> le tue aree di interesse per essere informato su di esse:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Segui i tuoi argomenti preferiti per essere avvisato:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "<PERSON><PERSON> le preferenze", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Salta per ora", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Accetta la nostra politica sulla privacy per procedere", "app.containers.SignUp.signUp2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.skip": "Salta questo passo", "app.containers.SignUp.tacError": "Accetta i nostri termini e condizioni per procedere", "app.containers.SignUp.thePrivacyPolicy": "l'informativa sulla privacy", "app.containers.SignUp.theTermsAndConditions": "i termini e le condizioni", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {Sembra che tu abbia già provato ad iscriverti senza completare il processo. Clicca invece su Log In, usando le credenziali scelte durante il tentativo precedente.} other {Qualcosa è andato storto. Si prega di riprovare più tardi.}}", "app.containers.SignUp.viennaConsentEmail": "Indirizzo e-mail", "app.containers.SignUp.viennaConsentFirstName": "Nome", "app.containers.SignUp.viennaConsentFooter": "Puoi modificare le informazioni del tuo profilo dopo l'accesso. Se hai già un account con lo stesso indirizzo e-mail su mitgestalten.wien.gv.at, sarà collegato al tuo account corrente.", "app.containers.SignUp.viennaConsentHeader": "Verranno trasmes<PERSON> i seguenti dati:", "app.containers.SignUp.viennaConsentLastName": "Nome e cognome", "app.containers.SignUp.viennaConsentUserName": "Nome utente", "app.containers.SignUp.viennaDataProtection": "informativa sulla privacy di Vienna", "app.containers.SiteMap.contributions": "Contributi", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "<PERSON>i", "app.containers.SiteMap.options": "Opzioni", "app.containers.SiteMap.projects": "<PERSON><PERSON><PERSON>", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Successo", "app.containers.SpamReport.inappropriate": "È inappropriato o offensivo", "app.containers.SpamReport.messageError": "C'è stato un errore nell'invio del modulo, per favore riprova.", "app.containers.SpamReport.messageSuccess": "Il tuo rapporto è stato inviato", "app.containers.SpamReport.other": "Altro motivo", "app.containers.SpamReport.otherReasonPlaceholder": "Descrizione", "app.containers.SpamReport.wrong_content": "<PERSON>o non è rilevante", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Rimuovere l'immagine del profilo", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "I tuoi voti sulle proposte ancora aperte al voto saranno cancellati. I voti sulle proposte per le quali il periodo di votazione è stato chiuso non verranno cancellati.", "app.containers.UsersEditPage.addPassword": "Aggiungi password", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Partecipare a progetti che richiedono una verifica.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verifica la tua identità", "app.containers.UsersEditPage.bio": "Su di te", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Non puoi modificare questo campo perché contiene informazioni verificate.", "app.containers.UsersEditPage.buttonSuccessLabel": "Successo", "app.containers.UsersEditPage.cancel": "Can<PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Modifica dell'e-mail", "app.containers.UsersEditPage.changePassword2": "Modifica della password", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Clicca qui per aggiornare la tua verifica.", "app.containers.UsersEditPage.conditionsLinkText": "le nostre condizioni", "app.containers.UsersEditPage.contactUs": "Un'altra ragione per andarsene? {feedbackLink} e forse possiamo aiutarti.", "app.containers.UsersEditPage.deleteAccountSubtext": "Ci dispiace vederti andare via.", "app.containers.UsersEditPage.deleteMyAccount": "Cancellare il mio account", "app.containers.UsersEditPage.deleteYourAccount": "Elimina il tuo account", "app.containers.UsersEditPage.deletionSection": "Elimina il tuo account", "app.containers.UsersEditPage.deletionSubtitle": "Questa azione non può essere annullata. I contenuti che hai pubblicato sulla piattaforma saranno resi anonimi. Se desideri cancellare tutti i tuoi contenuti, puoi contattarci all'indirizzo <EMAIL>.", "app.containers.UsersEditPage.email": "Email", "app.containers.UsersEditPage.emailEmptyError": "Fornire un indirizzo e-mail", "app.containers.UsersEditPage.emailInvalidError": "Fornire un indirizzo e-mail nel formato corretto, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Fateci sapere", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Nome", "app.containers.UsersEditPage.firstNamesEmptyError": "Fornire un nome", "app.containers.UsersEditPage.h1": "Informazioni sul tuo account", "app.containers.UsersEditPage.h1sub": "Modifica le informazioni del tuo account", "app.containers.UsersEditPage.image": "Immagine dell'avatar", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON>licca per selezionare una foto del profilo (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "<PERSON>tte le impostazioni del tuo profilo", "app.containers.UsersEditPage.language": "<PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "Nome e cognome", "app.containers.UsersEditPage.lastNameEmptyError": "Fornire un cognome", "app.containers.UsersEditPage.loading": "Caricamento...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "È possibile modificare l'e-mail o la password qui.", "app.containers.UsersEditPage.loginCredentialsTitle": "Credenziali di accesso", "app.containers.UsersEditPage.messageError": "Non siamo riusciti a salvare il tuo profilo. Riprova più tardi <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Il tuo profilo è stato salvato.", "app.containers.UsersEditPage.metaDescription": "Questa è la pagina delle impostazioni del profilo di {firstName} {lastName} sulla piattaforma di partecipazione online di {tenantName}. Qui puoi verificare la tua identità, modificare le informazioni del tuo account, cancellare il tuo account e modificare le tue preferenze e-mail.", "app.containers.UsersEditPage.metaTitle1": "Pagina delle impostazioni del profilo di {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Una volta cliccato questo pulsante, non avremo modo di ripristinare il tuo account.", "app.containers.UsersEditPage.noNameWarning2": "Il tuo nome è attualmente visualizzato sulla piattaforma come: \"{displayName}\" perché non hai inserito il tuo nome. Si tratta di un nome autogenerato. Se vuoi cambiarlo, inserisci il tuo nome qui sotto.", "app.containers.UsersEditPage.notificationsSubTitle": "Che tipo di notifiche e-mail vuoi ricevere? ", "app.containers.UsersEditPage.notificationsTitle": "Notifiche via e-mail", "app.containers.UsersEditPage.password": "Scegliere una nuova password", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Fornire una password lunga almeno {minimumPasswordLength} caratteri", "app.containers.UsersEditPage.passwordAddSection": "Aggiungere una password", "app.containers.UsersEditPage.passwordAddSubtitle2": "Impostate una password e accedete facilmente alla piattaforma, senza dover confermare ogni volta la vostra e-mail.", "app.containers.UsersEditPage.passwordChangeSection": "Cam<PERSON>re la <PERSON>", "app.containers.UsersEditPage.passwordChangeSubtitle": "Confermare la password attuale e passare alla nuova.", "app.containers.UsersEditPage.privacyReasons": "Se siete preoccupati per la vostra privacy, potete leggere {conditionsLink}.", "app.containers.UsersEditPage.processing": "Invio...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Il nome è obbligatorio quando si fornisce il cognome", "app.containers.UsersEditPage.reasonsToStayListTitle": "Prima di andare...", "app.containers.UsersEditPage.submit": "<PERSON><PERSON><PERSON> le modifiche", "app.containers.UsersEditPage.tooManyEmails": "<PERSON>vi troppe email? Puoi gestire le tue preferenze di posta elettronica nelle impostazioni del tuo profilo.", "app.containers.UsersEditPage.updateverification": "Le tue informazioni ufficiali sono cambiate? {reverifyButton}", "app.containers.UsersEditPage.user": "Quando vuoi che ti mandiamo un'e-mail di notifica?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Puoi partecipare a progetti che richiedono una verifica.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Sei verificato", "app.containers.UsersEditPage.verifyNow": "Verifica ora", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Scarica le tue risposte (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {nessun like} one {1 like} other {# Mi piace}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Ingresso che questo commento è stato pubblicato in risposta a:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Commenti ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Modifica il mio profilo", "app.containers.UsersShowPage.emptyInfoText": "Non stai seguendo nessuna voce del filtro specificato sopra.", "app.containers.UsersShowPage.eventsWithCount": "Eventi ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Seguendo ({followingCount})", "app.containers.UsersShowPage.inputs": "Ingressi", "app.containers.UsersShowPage.invisibleTitlePostsList": "<PERSON><PERSON> gli <PERSON> presentati da questo partecipante", "app.containers.UsersShowPage.invisibleTitleUserComments": "Tutti i commenti pubblicati da questo partecipante", "app.containers.UsersShowPage.loadMore": "Carica di più", "app.containers.UsersShowPage.loadMoreComments": "Carica altri commenti", "app.containers.UsersShowPage.loadingComments": "Caricamento dei commenti...", "app.containers.UsersShowPage.loadingEvents": "Caricamento eventi...", "app.containers.UsersShowPage.memberSince": "<PERSON><PERSON><PERSON> <PERSON> {date}", "app.containers.UsersShowPage.metaTitle1": "Pagina del profilo di {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Questa persona non ha ancora pubblicato alcun commento.", "app.containers.UsersShowPage.noCommentsForYou": "Non ci sono ancora commenti qui.", "app.containers.UsersShowPage.noEventsForUser": "Non hai ancora partecipato a nessun evento.", "app.containers.UsersShowPage.postsWithCount": "Invii ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Cartelle del progetto", "app.containers.UsersShowPage.projects": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.proposals": "Proposte", "app.containers.UsersShowPage.seePost": "Vedere la presentazione", "app.containers.UsersShowPage.surveyResponses": "Risposte ({responses})", "app.containers.UsersShowPage.topics": "Argomenti", "app.containers.UsersShowPage.tryAgain": "Si è verificato un errore, si prega di riprovare più tardi.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Questa è la pagina del profilo di {firstName} {lastName} sulla piattaforma di partecipazione online di {orgName}. Ecco una panoramica di tutti i loro contributi.", "app.containers.VoteControl.close": "<PERSON><PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "Qualcosa è andato storto", "app.containers.admin.ContentBuilder.default": "predefinito", "app.containers.admin.ContentBuilder.imageTextCards": "Schede immagine e testo", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info e fisarmoniche", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 colonna", "app.containers.admin.ContentBuilder.projectDescription": "Descrizione del progetto", "app.containers.app.navbar.admin": "Gestire la piattaforma", "app.containers.app.navbar.allProjects": "<PERSON><PERSON> i progetti", "app.containers.app.navbar.ariaLabel": "Primario", "app.containers.app.navbar.closeMobileNavMenu": "Chiudere il menu di navigazione mobile", "app.containers.app.navbar.editProfile": "Le mie impostazioni", "app.containers.app.navbar.fullMobileNavigation": "Cellulare completo", "app.containers.app.navbar.logIn": "Accedi", "app.containers.app.navbar.logoImgAltText": "{orgName} Casa", "app.containers.app.navbar.myProfile": "La mia attività", "app.containers.app.navbar.search": "Cerca", "app.containers.app.navbar.showFullMenu": "Mostra il menu completo", "app.containers.app.navbar.signOut": "<PERSON>nne<PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Si è verificato un errore durante il caricamento degli eventi. Per favore, prova a ricaricare la pagina.", "app.containers.eventspage.events": "Eventi", "app.containers.eventspage.eventsPageDescription": "Mostra tutti gli eventi pubblicati sulla piattaforma di partecipazione di {orgName}.", "app.containers.eventspage.eventsPageTitle1": "Eventi | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Progetto", "app.containers.eventspage.noPastEvents": "Nessun evento passato da visualizzare", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Al momento non sono programmati eventi imminenti o in corso.", "app.containers.eventspage.pastEvents": "Eventi passati", "app.containers.eventspage.upcomingAndOngoingEvents": "Eventi imminenti e in corso", "app.containers.footer.accessibility-statement": "Dichiarazione di accessibilità", "app.containers.footer.ariaLabel": "Secondario", "app.containers.footer.cookie-policy": "Politica dei cookie", "app.containers.footer.cookieSettings": "Impostazioni dei cookie", "app.containers.footer.feedbackEmptyError": "Il campo di feedback non può essere vuoto.", "app.containers.footer.poweredBy": "<PERSON><PERSON><PERSON> <PERSON>", "app.containers.footer.privacy-policy": "Politica sulla privacy", "app.containers.footer.siteMap": "Mappa del sito", "app.containers.footer.terms-and-conditions": "Termini e condizioni", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Annullamento", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, voglio and<PERSON>", "app.containers.ideaHeading.editForm": "Modifica modulo", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Sei sicuro di volertene andare?", "app.containers.ideaHeading.leaveIdeaForm": "<PERSON><PERSON><PERSON> per lasciare un'idea", "app.containers.ideaHeading.leaveIdeaText": "Le tue risposte non verranno salvate.", "app.containers.landing.cityProjects": "<PERSON><PERSON><PERSON>", "app.containers.landing.completeProfile": "Completa il tuo profilo", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON>, {firstName}. E' ora di completare il tuo profilo.", "app.containers.landing.createAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.landing.defaultSignedInMessage": "{orgName} ti sta ascoltando. È il tuo turno di far sentire la tua voce!", "app.containers.landing.doItLater": "Lo farò più tardi", "app.containers.landing.new": "nuovo", "app.containers.landing.subtitleCity": "Benvenuti nella piattaforma di partecipazione di {orgName}", "app.containers.landing.titleCity": "Diamo forma al futuro di {orgName} insieme", "app.containers.landing.twitterMessage": "Vota per {ideaTitle} su", "app.containers.landing.upcomingEventsWidgetTitle": "Eventi imminenti e in corso", "app.containers.landing.userDeletedSubtitle": "Puoi creare un nuovo account in qualsiasi momento o {contactLink} per farci sapere cosa possiamo migliorare.", "app.containers.landing.userDeletedSubtitleLinkText": "ci dia una linea", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Il tuo commento è stato cancellato.", "app.containers.landing.userDeletionFailed": "Si è verificato un errore durante la cancellazione del tuo account, siamo stati informati del problema e faremo del nostro meglio per risolverlo. Si prega di riprovare più tardi.", "app.containers.landing.verifyNow": "Verifica ora", "app.containers.landing.verifyYourIdentity": "Verifica la tua identità", "app.containers.landing.viewAllEventsText": "Visualizza tutti gli eventi", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "<PERSON>na alla cartella", "app.errors.after_end_at": "La data di inizio avviene dopo la data di fine", "app.errors.avatar_carrierwave_download_error": "Impossibile scaricare il file dell'avatar.", "app.errors.avatar_carrierwave_integrity_error": "Il file avatar non è di un tipo consentito.", "app.errors.avatar_carrierwave_processing_error": "Impossibile elaborare l'avatar.", "app.errors.avatar_extension_blacklist_error": "L'estensione del file dell'immagine dell'avatar non è consentita. Le estensioni ammesse sono: jpg, jpeg, gif e png.", "app.errors.avatar_extension_whitelist_error": "L'estensione del file dell'immagine dell'avatar non è consentita. Le estensioni ammesse sono: jpg, jpeg, gif e png.", "app.errors.banner_cta_button_multiloc_blank": "Inser<PERSON>ci il testo di un pulsante.", "app.errors.banner_cta_button_url_blank": "Inserisci un collegamento.", "app.errors.banner_cta_button_url_url": "Inserisci un collegamento valido. Assicurati che il link inizi con 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Inser<PERSON>ci il testo di un pulsante.", "app.errors.banner_cta_signed_in_url_blank": "Inserisci un collegamento.", "app.errors.banner_cta_signed_in_url_url": "Inserisci un collegamento valido. Assicurati che il link inizi con 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Inser<PERSON>ci il testo di un pulsante.", "app.errors.banner_cta_signed_out_url_blank": "Inserisci un collegamento.", "app.errors.banner_cta_signed_out_url_url": "Inserisci un collegamento valido. Assicurati che il link inizi con 'https://'.", "app.errors.base_includes_banned_words": "Potresti aver usato una o più parole considerate blasfeme. Ti invitiamo a modificare il tuo testo per rimuovere le eventuali bestemmie presenti.", "app.errors.body_multiloc_includes_banned_words": "La descrizione contiene parole considerate inappropriate.", "app.errors.bulk_import_idea_not_valid": "L'idea risultante non è valida: {value}.", "app.errors.bulk_import_image_url_not_valid": "Non è stato possibile scaricare alcuna immagine da {value}. Assicurati che l'URL sia valido e che termini con un'estensione di file come .png o .jpg. Questo problema si verifica nella riga con ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Posizione ideale con una coordinata mancante in {value}. Questo problema si verifica nella riga con ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Posizione ideale con una coordinata non numerica in {value}. Questo problema si verifica nella riga con ID {row}.", "app.errors.bulk_import_malformed_pdf": "Il file PDF caricato sembra essere malformato. Prova a esportare nuovamente il PDF dalla tua fonte e a caricarlo di nuovo.", "app.errors.bulk_import_maximum_ideas_exceeded": "Il limite massimo di {value} idee è stato superato.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "È stato superato il limite massimo di {value} pagine in un PDF.", "app.errors.bulk_import_not_enough_pdf_pages": "Il PDF caricato non ha abbastanza pagine: dovrebbe avere almeno lo stesso numero di pagine del modello scaricato.", "app.errors.bulk_import_publication_date_invalid_format": "Idea con formato di data di pubblicazione non valido \"{value}\". Utilizza il formato \"GG-MM-AAAA\".", "app.errors.cannot_contain_ideas": "Il metodo di partecipazione che hai selezionato non supporta questo tipo di post. Si prega di modificare la selezione e riprovare.", "app.errors.cant_change_after_first_response": "Non è più possibile cambiare questo, poiché alcuni utenti hanno già risposto", "app.errors.category_name_taken": "Esiste già una categoria con questo nome", "app.errors.confirmation_code_expired": "Codice scaduto. Si prega di richiedere un nuovo codice.", "app.errors.confirmation_code_invalid": "Codice di conferma non valido. Controlla la tua email per il codice corretto o prova 'Invia nuovo codice'", "app.errors.confirmation_code_too_many_resets": "Hai reinviato il codice di conferma troppe volte. Contattateci per ricevere invece un codice d'invito.", "app.errors.confirmation_code_too_many_retries": "Hai provato troppe volte. Richiedi un nuovo codice o prova a cambiare la tua email.", "app.errors.email_already_active": "L'indirizzo email {value} trovato nella riga {row} appartiene già ad un partecipante registrato", "app.errors.email_already_invited": "L'indirizzo email {value} trovato nella riga {row} è già stato invitato", "app.errors.email_blank": "Questo non può essere vuoto", "app.errors.email_domain_blacklisted": "Si prega di utilizzare un dominio e-mail diverso per la registrazione.", "app.errors.email_invalid": "Si prega di utilizzare un indirizzo e-mail valido.", "app.errors.email_taken": "Esiste già un account con questa email. Puoi accedere al suo posto.", "app.errors.email_taken_by_invite": "{value} è già occupato da un invito in sospeso. Controlla la tua cartella spam o contatta {supportEmail} se non riesci a trovarlo.", "app.errors.emails_duplicate": "Uno o più valori duplicati per l'indirizzo e-mail {value} sono stati trovati nelle seguenti righe: {rows}", "app.errors.extension_whitelist_error": "Il formato del file che hai cercato di caricare non è supportato.", "app.errors.file_extension_whitelist_error": "Il formato del file che hai cercato di caricare non è supportato.", "app.errors.first_name_blank": "Questo non può essere vuoto", "app.errors.generics.blank": "Questo non può essere vuoto.", "app.errors.generics.invalid": "Questo non sembra un valore valido", "app.errors.generics.taken": "Questa e-mail esiste già. Un altro account è collegato ad esso.", "app.errors.generics.unsupported_locales": "Questo campo non supporta il locale corrente.", "app.errors.group_ids_unauthorized_choice_moderator": "Come project manager, puoi inviare email solo alle persone che possono accedere al tuo progetto (o ai tuoi progetti)", "app.errors.has_other_overlapping_phases": "I progetti non possono avere fasi sovrapposte.", "app.errors.invalid_email": "L'email {value} trovata nella riga {row} non è un indirizzo email valido", "app.errors.invalid_row": "Si è verificato un errore sconosciuto durante il tentativo di elaborare la riga {row}", "app.errors.is_not_timeline_project": "Il progetto attuale non supporta le fasi.", "app.errors.key_invalid": "La chiave può contenere solo lettere, numeri e underscore (_)", "app.errors.last_name_blank": "Questo non può essere vuoto", "app.errors.locale_blank": "Scegliere una lingua", "app.errors.locale_inclusion": "Per favore scegli una lingua supportata", "app.errors.malformed_admin_value": "Il valore admin {value} trovato nella riga {row} non è valido", "app.errors.malformed_groups_value": "Il gruppo {value} trovato nella riga {row} non è un gruppo valido", "app.errors.max_invites_limit_exceeded1": "Il numero di inviti supera il limite di 1000.", "app.errors.maximum_attendees_greater_than1": "Il numero massimo di iscritti deve essere superiore a 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Il numero massimo di iscritti deve essere maggiore o uguale al numero attuale di iscritti.", "app.errors.no_invites_specified": "Non è stato possibile trovare alcun indirizzo e-mail.", "app.errors.no_recipients": "La campagna non può essere inviata perché non ci sono destinatari. Il gruppo a cui stai inviando la campagna è vuoto oppure nessuno ha acconsentito a ricevere le email.", "app.errors.number_invalid": "Inserisci un numero valido.", "app.errors.password_blank": "Questo non può essere vuoto", "app.errors.password_invalid": "Si prega di ricontrollare la password attuale.", "app.errors.password_too_short": "La password deve essere lunga almeno 8 caratteri", "app.errors.resending_code_failed": "Qualcosa è andato storto durante l'invio del codice di conferma.", "app.errors.slug_taken": "Questo URL di progetto esiste già. Per favore cambia lo slug del progetto con qualcos'altro.", "app.errors.tag_name_taken": "Un tag con questo nome esiste già", "app.errors.title_multiloc_blank": "Il titolo non può essere vuoto.", "app.errors.title_multiloc_includes_banned_words": "Il titolo contiene parole considerate inappropriate.", "app.errors.token_invalid": "I link per la reimpostazione della password possono essere utilizzati solo una volta e sono validi per un'ora dopo l'invio. {passwordResetLink}.", "app.errors.too_common": "Questa password può essere facilmente indovinata. Per favore scegli una password più forte.", "app.errors.too_long": "<PERSON>, scelga una password più corta (massimo 72 caratteri)", "app.errors.too_short": "Scegli una password con almeno 8 caratteri", "app.errors.uncaught_error": "Si è verificato un errore sconosciuto.", "app.errors.unknown_group": "Il gruppo {value} trovato nella riga {row} non è un gruppo conosciuto", "app.errors.unknown_locale": "La lingua {value} trovata nella riga {row} non è una lingua configurata", "app.errors.unparseable_excel": "Il file Excel selezionato non può essere elaborato.", "app.errors.url": "Inserisci un link valido. Assicurati che il link inizi con https://", "app.errors.verification_taken": "La verifica non può essere completata perché un altro account è stato verificato utilizzando gli stessi dati.", "app.errors.view_name_taken": "Una vista con questo nome esiste già", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Il contenuto inappropriato è stato rilevato automaticamente in un post o in un commento", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Accedi con StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Registrati con StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Crea subito un account Stadt Wien e utilizza un unico accesso a molti servizi digitali di Vienna.", "app.modules.id_cow.cancel": "Can<PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "Questo campo non può essere vuoto.", "app.modules.id_cow.helpAltText": "Mostra dove trovare il numero di serie della carta d'identità", "app.modules.id_cow.invalidIdSerialError": "Seriale ID non valido", "app.modules.id_cow.invalidRunError": "Esecuzione non valida", "app.modules.id_cow.noMatchFormError": "Non è stata trovata nessuna corrispondenza.", "app.modules.id_cow.notEntitledFormError": "Non ha diritto.", "app.modules.id_cow.showCOWHelp": "Dove posso trovare il mio numero di serie ID?", "app.modules.id_cow.somethingWentWrongError": "Non possiamo verificarti perché qualcosa è andato storto", "app.modules.id_cow.submit": "Invia", "app.modules.id_cow.takenFormError": "<PERSON><PERSON><PERSON> preso.", "app.modules.id_cow.verifyCow": "Verificare usando COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verifica con FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "Questo campo non può essere vuoto.", "app.modules.id_gent_rrn.gentRrnHelp": "Il suo numero di previdenza sociale è indicato sul retro della sua carta d'identità digitale", "app.modules.id_gent_rrn.invalidRrnError": "Numero di sicurezza sociale non valido", "app.modules.id_gent_rrn.noMatchFormError": "Non siamo riusciti a trovare informazioni sul tuo numero di previdenza sociale", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Non possiamo verificarti perché vivi fuori da Gand", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Non possiamo verificarti perché hai meno di 14 anni", "app.modules.id_gent_rrn.rrnLabel": "Numero di sicurezza sociale", "app.modules.id_gent_rrn.rrnTooltip": "Chiediamo il tuo numero di previdenza sociale per verificare se sei un cittadino di Gand, più grande di 14 anni.", "app.modules.id_gent_rrn.showGentRrnHelp": "Dove posso trovare il mio numero di serie ID?", "app.modules.id_gent_rrn.somethingWentWrongError": "Non possiamo verificarti perché qualcosa è andato storto", "app.modules.id_gent_rrn.submit": "Invia", "app.modules.id_gent_rrn.takenFormError": "Il suo numero di previdenza sociale è già stato utilizzato per verificare un altro conto", "app.modules.id_gent_rrn.verifyGentRrn": "Verificare usando GentRrn", "app.modules.id_id_card_lookup.cancel": "Can<PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "Questo campo non può essere vuoto.", "app.modules.id_id_card_lookup.helpAltText": "Spiegazione della carta d'identità", "app.modules.id_id_card_lookup.invalidCardIdError": "Questo id non è valido.", "app.modules.id_id_card_lookup.noMatchFormError": "Non è stata trovata nessuna corrispondenza.", "app.modules.id_id_card_lookup.showHelp": "Dove posso trovare il mio numero di serie ID?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Non possiamo verificarti perché qualcosa è andato storto", "app.modules.id_id_card_lookup.submit": "Invia", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON><PERSON> preso.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "Questo campo non può essere vuoto.", "app.modules.id_oostende_rrn.invalidRrnError": "Numero di sicurezza sociale non valido", "app.modules.id_oostende_rrn.noMatchFormError": "Non siamo riusciti a trovare informazioni sul tuo numero di previdenza sociale", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Non possiamo verificarti perché vivi fuori Ostenda", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Non possiamo verificarti perché hai meno di 14 anni", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Il suo numero di previdenza sociale è indicato sul retro della sua carta d'identità digitale", "app.modules.id_oostende_rrn.rrnLabel": "Numero di sicurezza sociale", "app.modules.id_oostende_rrn.rrnTooltip": "Chiediamo il tuo numero di previdenza sociale per verificare se sei un cittadino di Ostenda, di età superiore ai 14 anni.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Dove posso trovare il mio numero di previdenza sociale?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Non possiamo verificarti perché qualcosa è andato storto", "app.modules.id_oostende_rrn.submit": "Invia", "app.modules.id_oostende_rrn.takenFormError": "Il suo numero di previdenza sociale è già stato utilizzato per verificare un altro conto", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verificare utilizzando il numero di previdenza sociale", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Hai ricevuto i diritti di amministratore sulla cartella \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Condi<PERSON><PERSON>", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Visualizza i progetti su {folderUrl} per far sentire la tua voce!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | dalla piattaforma di partecipazione di {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | dalla piattaforma di partecipazione di {orgName}", "app.sessionRecording.accept": "Sì, accetto", "app.sessionRecording.modalDescription1": "Per capire meglio i nostri utenti, chiediamo a caso a una piccola percentuale di visitatori di tracciare in dettaglio la loro sessione di navigazione.", "app.sessionRecording.modalDescription2": "L'unico scopo dei dati registrati è quello di migliorare il sito web. Nessuno dei tuoi dati sarà condiviso con terzi. Qualsiasi informazione sensibile inserita verrà filtrata.", "app.sessionRecording.modalDescription3": "Accetti?", "app.sessionRecording.modalDescriptionFaq": "FAQ qui.", "app.sessionRecording.modalTitle": "Aiutaci a migliorare questo sito", "app.sessionRecording.reject": "No, rifiuto", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Condurre un esercizio di allocazione del budget", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Raccogli il feedback su un documento", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "<PERSON><PERSON><PERSON> un sondaggio sulla piattaforma", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON> un sondaggio", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Integra un sondaggio esterno", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Trovare volontari", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "<PERSON><PERSON><PERSON><PERSON> input e feedback", "app.utils.AdminPage.ProjectEdit.shareInformation": "Condividi informazioni", "app.utils.FormattedCurrency.credits": "crediti", "app.utils.FormattedCurrency.tokens": "<PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# crediti} one {# credito} other {# crediti}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "I più discussi", "app.utils.IdeaCards.mostReacted": "La maggior parte delle reazioni", "app.utils.IdeaCards.newest": "<PERSON><PERSON> recente", "app.utils.IdeaCards.oldest": "Il più vecchio", "app.utils.IdeaCards.random": "Casuale", "app.utils.IdeaCards.trending": "Tendenza", "app.utils.IdeasNewPage.contributionFormTitle": "Aggiungi un nuovo contributo", "app.utils.IdeasNewPage.ideaFormTitle": "Aggiungi una nuova idea", "app.utils.IdeasNewPage.initiativeFormTitle": "Aggiungi una nuova iniziativa", "app.utils.IdeasNewPage.issueFormTitle1": "Aggiungi un nuovo commento", "app.utils.IdeasNewPage.optionFormTitle": "Aggiungi una nuova opzione", "app.utils.IdeasNewPage.petitionFormTitle": "Aggiungi una nuova petizione", "app.utils.IdeasNewPage.projectFormTitle": "Aggiungere un nuovo progetto", "app.utils.IdeasNewPage.proposalFormTitle": "Aggiungi una nuova proposta", "app.utils.IdeasNewPage.questionFormTitle": "Aggiungi una nuova domanda", "app.utils.IdeasNewPage.surveyTitle": "Sondaggio", "app.utils.IdeasNewPage.viewYourComment": "Visualizza il tuo commento", "app.utils.IdeasNewPage.viewYourContribution": "Visualizza il tuo contributo", "app.utils.IdeasNewPage.viewYourIdea": "Visualizza la tua idea", "app.utils.IdeasNewPage.viewYourInitiative": "Visualizza la tua iniziativa", "app.utils.IdeasNewPage.viewYourInput": "Visualizza il tuo contributo", "app.utils.IdeasNewPage.viewYourIssue": "Visualizza il tuo problema", "app.utils.IdeasNewPage.viewYourOption": "Visualizza la tua opzione", "app.utils.IdeasNewPage.viewYourPetition": "Visualizza la tua petizione", "app.utils.IdeasNewPage.viewYourProject": "Visualizza il tuo progetto", "app.utils.IdeasNewPage.viewYourProposal": "Visualizza la tua proposta", "app.utils.IdeasNewPage.viewYourQuestion": "Visualizza la tua domanda", "app.utils.Projects.sendSubmission": "Invia l'identificativo dell'invio alla mia e-mail", "app.utils.Projects.sendSurveySubmission": "Invia l'identificativo per l'invio dell'indagine alla mia e-mail", "app.utils.Projects.surveySubmission": "<PERSON><PERSON> del sondaggio", "app.utils.Projects.yourResponseHasTheFollowingId": "La tua risposta ha il seguente identificativo: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Se in seguito decidi di voler rimuovere la tua risposta, contattaci con il seguente identificativo univoco:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Per partecipare a questo evento devi completare il tuo profilo.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Non hai i requisiti per partecipare a questo evento.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Non ti è consentito partecipare a questo evento.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Per partecipare a questo evento devi effettuare il login o registrarti.", "app.utils.actionDescriptors.attendingEventNotVerified": "Devi verificare il tuo account prima di poter partecipare a questo evento.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Per fare il volontario devi completare il tuo profilo.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Non hai i requisiti per fare il volontario.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Non ti è consentito fare volontariato.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Per fare il volontario devi accedere o registrarti.", "app.utils.actionDescriptors.volunteeringNotVerified": "Devi verificare il tuo account prima di poter fare il volontario.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Per favore {completeRegistrationLink} per fare volontariato.", "app.utils.errors.api_error_default.in": "Non è giusto", "app.utils.errors.default.ajv_error_birthyear_required": "Inser<PERSON>ci il tuo anno di nascita", "app.utils.errors.default.ajv_error_date_any": "Si prega di inserire una data valida", "app.utils.errors.default.ajv_error_domicile_required": "Inser<PERSON>ci il tuo luogo di residenza", "app.utils.errors.default.ajv_error_gender_required": "Inser<PERSON><PERSON> il tuo sesso", "app.utils.errors.default.ajv_error_invalid": "Non è valido", "app.utils.errors.default.ajv_error_maxItems": "Non può includere più di {limit, plural, one {# item} other {# elementi}}", "app.utils.errors.default.ajv_error_minItems": "Deve includere almeno {limit, plural, one {# item} other {# elementi}}", "app.utils.errors.default.ajv_error_number_any": "Si prega di inserire un numero valido", "app.utils.errors.default.ajv_error_politician_required": "Si prega di compilare se sei un politico", "app.utils.errors.default.ajv_error_required3": "Il campo è obbligatorio: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Non può essere vuoto", "app.utils.errors.default.api_error_accepted": "Deve essere accettato", "app.utils.errors.default.api_error_blank": "Non può essere vuoto", "app.utils.errors.default.api_error_confirmation": "Non corrisponde", "app.utils.errors.default.api_error_empty": "Non può essere vuoto", "app.utils.errors.default.api_error_equal_to": "Non è giusto", "app.utils.errors.default.api_error_even": "Deve essere pari", "app.utils.errors.default.api_error_exclusion": "È riservato", "app.utils.errors.default.api_error_greater_than": "È troppo piccolo", "app.utils.errors.default.api_error_greater_than_or_equal_to": "È troppo piccolo", "app.utils.errors.default.api_error_inclusion": "Non è incluso nell'elenco", "app.utils.errors.default.api_error_invalid": "Non è valido", "app.utils.errors.default.api_error_less_than": "È troppo grande", "app.utils.errors.default.api_error_less_than_or_equal_to": "È troppo grande", "app.utils.errors.default.api_error_not_a_number": "Non è un numero", "app.utils.errors.default.api_error_not_an_integer": "Deve essere un intero", "app.utils.errors.default.api_error_other_than": "Non è giusto", "app.utils.errors.default.api_error_present": "Deve essere vuoto", "app.utils.errors.default.api_error_too_long": "<PERSON> troppo lungo", "app.utils.errors.default.api_error_too_short": "È troppo corto", "app.utils.errors.default.api_error_wrong_length": "È la lunghezza sbagliata", "app.utils.errors.defaultapi_error_.odd": "Deve essere strano", "app.utils.notInGroup": "Non soddisfi i requisiti per partecipare.", "app.utils.participationMethod.onSurveySubmission": "Grazie. La risposta è stata ricevuta.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Il voto non è più disponibile perché questa fase non è più attiva.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Non hai i requisiti per votare.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Non ti è consentito votare.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Per votare devi effettuare il login o registrarti.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Devi verificare il tuo account prima di poter votare.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>La presentazione dei budget si è chiusa il {endDate}.</b> I partecipanti avevano a disposizione un totale di <b>{maxBudget} ciascuno da distribuire tra {optionCount} opzioni.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "<PERSON>ila<PERSON><PERSON>", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Bilancio presentato 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Non hai i requisiti per assegnare i budget.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Non ti è consentito assegnare budget.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Per assegnare i budget è necessario effettuare il login o registrarsi.", "app.utils.votingMethodUtils.budgetingNotVerified": "Devi verificare il tuo account prima di poter assegnare i budget.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Il tuo budget non sarà conteggiato</b> fino a quando non avrai cliccato su \"Invia\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Il budget minimo richiesto è {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Una volta terminato, clicca su \"Invia\" per inviare il tuo budget.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Seleziona le opzioni che preferisci toccando \"Aggiungi\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Hai a disposizione un totale di <b>{maxBudget} da distribuire tra le opzioni di {optionCount} </b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>Congra<PERSON>la<PERSON>ni, il tuo budget è stato inviato!</b> Puoi controllare le tue opzioni qui sotto in qualsiasi momento o modificarle prima di <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>Congra<PERSON><PERSON><PERSON>ni, il tuo budget è stato inviato!</b> Puoi controllare le tue opzioni qui sotto in qualsiasi momento.", "app.utils.votingMethodUtils.castYourVote": "Esprimi il tuo voto", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Puoi aggiungere un massimo di {maxVotes, plural, one {# crediti} other {# crediti}} per opzione.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Puoi aggiungere un massimo di {maxVotes, plural, one {# punti} other {# punti}} per opzione.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Puoi aggiungere un massimo di {maxVotes, plural, one {# token} other {# token}} per opzione.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Puoi aggiungere un massimo di {maxVotes, plural, one {# voti} other {# voti}} per opzione.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Una volta terminato, clicca su \"Invia\" per esprimere il tuo voto.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Seleziona le opzioni che preferisci toccando \"Seleziona\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Risultati finali", "app.utils.votingMethodUtils.finalTally": "Bilancio finale", "app.utils.votingMethodUtils.howToParticipate": "Come partecipare", "app.utils.votingMethodUtils.howToVote": "Come votare", "app.utils.votingMethodUtils.multipleVotingEnded1": "Le votazioni si sono chiuse su <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 crediti} one {1 credito} other {# crediti}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 punti} one {1 punto} other {# punti}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 gettoni} one {1 gettone} other {# gettoni}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 voti} one {1 voto} other {# voti}}", "app.utils.votingMethodUtils.results": "Risultati", "app.utils.votingMethodUtils.singleVotingEnded": "Le votazioni si sono chiuse su <b>{endDate}.</b> I partecipanti potevano <b>votare per {maxVotes} opzioni.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Seleziona le opzioni che preferisci toccando \"Vota\".", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Hai a disposizione <b>{totalVotes} voti</b> che puoi assegnare alle opzioni.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Una volta terminato, clicca su \"Invia\" per esprimere il tuo voto.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Le votazioni si sono chiuse su <b>{endDate}.</b> I partecipanti potevano <b>votare per 1 opzione.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Seleziona l'opzione che preferisci toccando \"Vota\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Hai <b>1 voto</b> che puoi assegnare a una delle opzioni.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Le votazioni si sono chiuse su <b>{endDate}.</b> I partecipanti potevano <b>votare per tutte le opzioni che desideravano.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Puoi votare tutte le opzioni che desideri.", "app.utils.votingMethodUtils.submitYourBudget": "Invia il tuo budget", "app.utils.votingMethodUtils.submittedBudgetCountText2": "una persona ha presentato il proprio bilancio online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "le persone hanno presentato il loro budget online", "app.utils.votingMethodUtils.submittedVoteCountText2": "una persona ha espresso il proprio voto online", "app.utils.votingMethodUtils.submittedVotesCountText2": "le persone hanno inviato i loro voti online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Voto inviato 🎉", "app.utils.votingMethodUtils.votesCast": "Voti espressi", "app.utils.votingMethodUtils.votingClosed": "Votazione chiusa", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Il tuo voto non sarà conteggiato</b> fino a quando non avrai cliccato su \"Invia\".", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, il tuo voto è stato inviato!</b> Puoi controllare o modificare il tuo voto prima di <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, il tuo voto è stato inviato!</b> Puoi controllare o modificare il tuo voto qui sotto in qualsiasi momento.", "components.UI.IdeaSelect.noIdeaAvailable": "Non ci sono idee disponibili.", "components.UI.IdeaSelect.selectIdea": "Seleziona l'idea", "containers.SiteMap.allProjects": "<PERSON><PERSON> i progetti", "containers.SiteMap.customPageSection": "<PERSON><PERSON>e <PERSON>", "containers.SiteMap.folderInfo": "<PERSON><PERSON> informazioni", "containers.SiteMap.headSiteMapTitle": "Mappa del sito | {orgName}", "containers.SiteMap.homeSection": "Generale", "containers.SiteMap.pageContents": "Contenuto della pagina", "containers.SiteMap.profilePage": "La pagina del tuo profilo", "containers.SiteMap.profileSettings": "Le vostre impostazioni", "containers.SiteMap.projectEvents": "Eventi", "containers.SiteMap.projectIdeas": "Idee", "containers.SiteMap.projectInfo": "Informazioni", "containers.SiteMap.projectPoll": "Sondaggio", "containers.SiteMap.projectSurvey": "Sondaggio", "containers.SiteMap.projectsArchived": "<PERSON><PERSON><PERSON> archiviati", "containers.SiteMap.projectsCurrent": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectsDraft": "Bozze di progetti", "containers.SiteMap.projectsSection": "<PERSON><PERSON><PERSON> {orgName}", "containers.SiteMap.signInPage": "Accedi", "containers.SiteMap.signUpPage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.siteMapDescription": "Da questa pagina, è possibile navigare verso qualsiasi contenuto della piattaforma.", "containers.SiteMap.siteMapTitle": "Mappa del sito della piattaforma di partecipazione di {orgName}", "containers.SiteMap.successStories": "Storie di successo", "containers.SiteMap.timeline": "Fasi del progetto", "containers.SiteMap.userSpaceSection": "Il tuo account", "containers.SubscriptionEndedPage.accessDenied": "Non hai più accesso", "containers.SubscriptionEndedPage.subscriptionEnded": "Questa pagina è accessibile solo per le piattaforme con un abbonamento attivo."}