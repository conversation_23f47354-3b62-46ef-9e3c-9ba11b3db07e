{"EmailSettingsPage.emailSettings": "E-posta ayarları", "EmailSettingsPage.initialUnsubscribeError": "Bu kampanya için aboneliğiniz iptal edilirken bir sorun oluştu. Lütfen yeniden deneyin.", "EmailSettingsPage.initialUnsubscribeLoading": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "EmailSettingsPage.initialUnsubscribeSuccess": "{campaignTitle} adlı kampanya için aboneliğiniz başarıyla iptal edildi.", "UI.FormComponents.optional": "iste<PERSON>e bağlı", "app.closeIconButton.a11y_buttonActionMessage": "Ka<PERSON><PERSON>", "app.components.Areas.areaUpdateError": "Alanınızı kaydederken bir hata oluştu. Lütfen tekrar deneyin.", "app.components.Areas.followedArea": "Takip edilen bölge: {areaTitle}", "app.components.Areas.followedTopic": "Takip edilen konu: {topicTitle}", "app.components.Areas.topicUpdateError": "Konunuzu kaydederken bir hata oluştu. Lütfen tekrar deneyin.", "app.components.Areas.unfollowedArea": "Takip edilmeyen bölge: {areaTitle}", "app.components.Areas.unfollowedTopic": "Takip edilmeyen konu: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Fiyat:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON>", "app.components.AssignBudgetControl.added": "Eklendi", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON> e<PERSON>", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Tüm kredilerinizi dağıttınız.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Bu seçenek için maksimum kredi sayısını dağıttınız.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Tüm puanlarınızı dağıttınız.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Bu seçenek için maksimum puanı dağıttınız.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Tüm jetonlarınızı dağıttınız.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Bu seçenek için maksimum sayıda jeton dağıttınız.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Tüm oylarınızı dağıttınız.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Bu seçenek için maksimum sayıda oy dağıttınız.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(1 çevrimdışı dahil)} other {(# çevrimdışı dahil)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Bu aşama aktif <PERSON>ı için oylama yapılamamaktadır.", "app.components.AssignMultipleVotesControl.removeVote": "Oylamayı kaldır", "app.components.AssignMultipleVotesControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Oyunuzu zaten gönderdiniz. Değiştirmek için \"Gönderinizi değiştirin\" seçeneğine tıklayın.", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Oyunuzu zaten gönderdiniz. Değiştirmek için proje sayfasına geri dönün ve \"Gönderinizi değiştirin\" seçeneğine tıklayın.", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kredi} other {kredi}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {nokta} other {noktalar}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokenler}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {oy} other {oylar}}", "app.components.AssignVoteControl.maxVotesReached1": "Tüm oylarınızı dağıttınız.", "app.components.AssignVoteControl.phaseNotActive": "Bu aşama aktif <PERSON>ı için oylama yapılamamaktadır.", "app.components.AssignVoteControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.selected2": "Seçilmiş", "app.components.AssignVoteControl.voteForAtLeastOne": "En az 1 seçenek için oy verin", "app.components.AssignVoteControl.votesSubmitted1": "Oyunuzu zaten gönderdiniz. Değiştirmek için \"Gönderinizi değiştirin\" seçeneğine tıklayın.", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Oyunuzu zaten gönderdiniz. Değiştirmek için proje sayfasına geri dönün ve \"Gönderinizi değiştirin\" seçeneğine tıklayın.", "app.components.AuthProviders.continue": "<PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "{azureProviderName} ile devam edin", "app.components.AuthProviders.continueWithFacebook": "Facebook ile devam edin", "app.components.AuthProviders.continueWithFakeSSO": "Sahte SSO ile devam edin", "app.components.AuthProviders.continueWithGoogle": "Google ile devam edin", "app.components.AuthProviders.continueWithHoplr": "<PERSON><PERSON>r ile devam edin", "app.components.AuthProviders.continueWithIdAustria": "ID Avusturya ile devam edin", "app.components.AuthProviders.continueWithLoginMechanism": "{loginMechanismName}ile devam edin", "app.components.AuthProviders.continueWithNemlogIn": "MitID ile devam edin", "app.components.AuthProviders.franceConnectMergingFailed": "Bu e-posta adresiyle zaten bir hesap var.{br}{br}Kişisel bilgiler eşleşmediği için FranceConnect'i kullanarak platforma erişemezsiniz. FranceConnect'i kullanarak giriş yapmak için öncelikle bu platformdaki adınızı veya soyadınızı resmi bilgilerinizle eşleşecek şekilde değiştirmeniz gerekecektir.{br}{br}Aşağıda normalde yaptığınız gibi giriş yapabilirsiniz.", "app.components.AuthProviders.goToLogIn": "He<PERSON>b<PERSON><PERSON><PERSON>z var mı? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "He<PERSON>b<PERSON><PERSON><PERSON>z yok mu? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "O<PERSON>um açın", "app.components.AuthProviders.logInWithEmail": "E-posta ile oturum açın", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Doğrulanmak için belirtilen minimum yaşta veya üzerinde olmalısınız.", "app.components.AuthProviders.signUp2": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.signUpButtonAltText": "{loginMechanismName} ile kaydolun", "app.components.AuthProviders.signUpWithEmail": "E-posta ile kaydolun", "app.components.AuthProviders.verificationRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Author.a11yPostedBy": "Tarafından g<PERSON>ildi", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 katılımcı} other {{numberOfParticipants} katılım<PERSON><PERSON><PERSON>}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} k<PERSON><PERSON><PERSON><PERSON><PERSON>lar", "app.components.AvatarBubbles.participant": "KATILIMCI", "app.components.AvatarBubbles.participants1": "Katılımcılar", "app.components.Comments.cancel": "İptal", "app.components.Comments.commentingDisabledInCurrentPhase": "<PERSON><PERSON> anki a<PERSON> yorum yapmak mümkün <PERSON>.", "app.components.Comments.commentingDisabledInactiveProject": "Bu proje şu anda etkin olma<PERSON>ığından yorum yapmak mümkün değil.", "app.components.Comments.commentingDisabledProject": "Bu projede yorum yapma özelliği şu an için devre dışı.", "app.components.Comments.commentingDisabledUnverified": "<PERSON><PERSON> ya<PERSON> i<PERSON> {verifyIdentityLink}.", "app.components.Comments.commentingMaybeNotPermitted": "Hangi işlemlerin yapılabileceğini görmek için lütfen {signInLink}.", "app.components.Comments.inputsAssociatedWithProfile": "Bu seçeneği seçmediğiniz sürece varsayılan olarak gönderimleriniz profilinizle ilişkilendirilecektir.", "app.components.Comments.invisibleTitleComments": "<PERSON><PERSON><PERSON>", "app.components.Comments.leastRecent": "En son", "app.components.Comments.likeComment": "<PERSON><PERSON> yo<PERSON><PERSON>", "app.components.Comments.mostLiked": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.mostRecent": "En son", "app.components.Comments.official": "<PERSON><PERSON><PERSON>", "app.components.Comments.postAnonymously": "İsimsiz o<PERSON>", "app.components.Comments.replyToComment": "<PERSON><PERSON><PERSON> ya<PERSON>", "app.components.Comments.reportAsSpam": "Spam bildir", "app.components.Comments.seeOriginal": "Orijinali göster", "app.components.Comments.seeTranslation": "Çeviriyi <PERSON>", "app.components.Comments.yourComment": "Yo<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.divisiveDescription": "İnsanların eşit derecede hemfikir olduğu ve olmadığı ifadeler:", "app.components.CommonGroundResults.divisiveTitle": "B<PERSON>lücü", "app.components.CommonGroundResults.majorityDescription": "Aşağıdaki konularda %60'tan fazlası şu ya da bu yönde oy kullanmıştır:", "app.components.CommonGroundResults.majorityTitle": "Çoğunluk", "app.components.CommonGroundResults.participantLabel": "KATILIMCI", "app.components.CommonGroundResults.participantsLabel1": "Katılımcılar", "app.components.CommonGroundResults.statementLabel": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.components.CommonGroundResults.statementsLabel1": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabe": "oylama", "app.components.CommonGroundResults.votesLabel1": "oylar", "app.components.CommonGroundStatements.agreeLabel": "Katılıyorum", "app.components.CommonGroundStatements.disagreeLabel": "Katılmıyorum", "app.components.CommonGroundStatements.noMoreStatements": "<PERSON><PERSON> anda yanıt verilecek bir açıklama yok", "app.components.CommonGroundStatements.noResults": "Henüz gösterilecek bir sonuç yok. Lütfen Ortak Zemin aşamasına katıldığınızdan emin olun ve sonrasında burayı tekrar kontrol edin.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Sonuçlar", "app.components.CommonGroundTabs.statementsTabLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommunityMonitorModal.formError": "Bir hata ile karşılaşıldı.", "app.components.CommunityMonitorModal.surveyDescription2": "<PERSON><PERSON> etmekte olan bu anket, y<PERSON><PERSON>işim ve kamu hizmetleri hakkında ne düşündüğünüzü takip etmektedir.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<1 dakika sürer} one {1 dakika sürer} other {# dakika sürer}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "{userEmail} adresine onay kodu bulunan bir e-posta g<PERSON>il<PERSON>.", "app.components.ConfirmationModal.changeYourEmail": "E-posta adresinizi değiştirin.", "app.components.ConfirmationModal.codeInput": "Kod", "app.components.ConfirmationModal.confirmationCodeSent": "<PERSON>ni kod gö<PERSON>il<PERSON>", "app.components.ConfirmationModal.didntGetAnEmail": "E-posta almadınız mı?", "app.components.ConfirmationModal.foundYourCode": "<PERSON><PERSON><PERSON><PERSON> buldunuz mu?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON> gidin.", "app.components.ConfirmationModal.sendEmailWithCode": "Kod E-postasını Gönder", "app.components.ConfirmationModal.sendNewCode": "<PERSON><PERSON>.", "app.components.ConfirmationModal.verifyAndContinue": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>", "app.components.ConfirmationModal.wrongEmail": "E-posta adresi yanlış mı?", "app.components.ConsentManager.Banner.accept": "Kabul et", "app.components.ConsentManager.Banner.ariaButtonClose2": "Politikayı reddet ve başlığı kapat", "app.components.ConsentManager.Banner.close": "Ka<PERSON><PERSON>", "app.components.ConsentManager.Banner.mainText": "Bu platformda {policyLink} ile uyumlu şekilde çerezler kullanılmaktadır.", "app.components.ConsentManager.Banner.manage": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.policyLink": "Çerez politikası", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Bunu web sitemizin reklam kampanyalarını kişiselleştirmek ve bu kampanyaların etki düzeyini ölçmek için kullanırız. Bu platformda reklam göstermeyiz ancak ilgili hizmetler sitemizde ziyaret ettiğiniz sayfaları temel alarak size kişiselleştirilmiş reklamlar sunabilir.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON> ver", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Bu i<PERSON><PERSON> ö<PERSON>ini, platformu nasıl kullandığınızı daha iyi anlayıp gezinme deneyiminizi öğrenmek ve iyileştirmek için kullanırız. Bu bilgiler hiçbir şekilde bireysel kullanıcıları izlemek için kullanılmaz, yalnızca toplu analizlerde kullanılır.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON> gidin", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "İptal", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON> verme", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "İşlevsel", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Bu, web sitesinin temel işlevlerini etkinleştirmek ve izlemek için gereklidir. Burada belirtilen bazı araçlar sizin için geçerli olmayabilir. Daha fazla bilgi için lütfen çerez politikamıza bakın.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Zorun<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Bu platforma kaydolursanız, kullanım kolaylığını artırmak amacıyla, platformu kullanacağınız dili ve bir doğrulama çerezi kaydederiz.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Çerez tercihleriniz", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Araçlar", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "İçerik yükleme feragatnamesi", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "İçerik yükleyerek, bu i<PERSON><PERSON><PERSON>in herhangi bir düzenlemeyi veya fikri mülkiyet hakları, g<PERSON><PERSON><PERSON> hakları, ticari sırlara ilişkin haklar gibi üçüncü tarafların haklarını ihlal etmediğini beyan edersiniz. <PERSON><PERSON><PERSON>, bu i<PERSON><PERSON><PERSON><PERSON> yü<PERSON>erek, yüklenen içerikten kaynaklanan tüm doğrudan ve dolaylı zararlar için tam ve münhasır sorumluluk üstlenmeyi taahhüt edersiniz. Ayrıca, platform sahibini ve Go Vocal'i, yüklediğiniz içerikten doğacak veya sonuçlanacak her türlü üçüncü taraf taleplerine veya üçüncü taraflara karşı yükümlülüklere ve ilgili masraflara karşı tazmin etmeyi taahhüt edersiniz.", "app.components.ContentUploadDisclaimer.onAccept": "Anlıyorum.", "app.components.ContentUploadDisclaimer.onCancel": "İptal", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Bize nedenini s<PERSON>n", "app.components.CustomFieldsForm.addressInputAriaLabel": "<PERSON><PERSON>", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Bir adres girin...", "app.components.CustomFieldsForm.adminFieldTooltip": "Yalnızca yöneticilerin görebileceği alan", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Bu ankete verilen tüm yanıtlar anonimleştirilmiştir.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Bir çokgen için en az üç nokta gereklidir.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Bir çizgi için en az iki nokta gereklidir.", "app.components.CustomFieldsForm.attachmentRequired": "En az bir ek gereklidir", "app.components.CustomFieldsForm.authorFieldLabel": "<PERSON><PERSON>", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Kullanıcı e-postasına veya adına göre arama yapmak için yazmaya başlayın...", "app.components.CustomFieldsForm.back": "<PERSON><PERSON>", "app.components.CustomFieldsForm.budgetFieldLabel": "Bütçe", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Çizmek için haritaya tıklayın. <PERSON>rd<PERSON><PERSON>n, noktaları taşımak için üzerlerine sürükleyin.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Cevabınızı eklemek için haritaya tıklayın veya aşağıya bir adres yazın.", "app.components.CustomFieldsForm.confirm": "<PERSON>aylayın", "app.components.CustomFieldsForm.descriptionMinLength": "Açıklama en az {min} karakter uzunluğunda olmalıdır", "app.components.CustomFieldsForm.descriptionRequired": "Açıklama gereklidir", "app.components.CustomFieldsForm.fieldMaximumItems": "\"{fieldName}\" alan<PERSON> için en fazla {maxSelections, plural, one {# seçenek} other {# seçenek}} seçilebilir", "app.components.CustomFieldsForm.fieldMinimumItems": "\"{fieldName}\" alan<PERSON> için en az {minSelections, plural, one {# seçenek} other {# seçenek}} seçilebilir", "app.components.CustomFieldsForm.fieldRequired": "\"{fieldName}\" alanı gereklidir", "app.components.CustomFieldsForm.fileSizeLimit": "<PERSON><PERSON><PERSON> boyutu sınırı {maxFileSize} MB'dir.", "app.components.CustomFieldsForm.imageRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.minimumCoordinates2": "En az {numPoints} harita noktası gereklidir.", "app.components.CustomFieldsForm.notPublic1": "*Bu cevap sadece proje yöneticileriyle <PERSON>, ka<PERSON><PERSON><PERSON><PERSON>lmayacaktır.", "app.components.CustomFieldsForm.otherArea": "Başka bir yerde", "app.components.CustomFieldsForm.progressBarLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.removeAnswer": "Cevabı kaldır", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*İstediğiniz kadar seçin", "app.components.CustomFieldsForm.selectBetween": "* {minItems} ve {maxItems} se<PERSON><PERSON><PERSON><PERSON> a<PERSON>ından seçim yapın", "app.components.CustomFieldsForm.selectExactly2": "*Tam olarak seçin {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*İstediğiniz kadar seçin", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Çizmek için haritaya dokunun. Ardından, noktaları taşımak için üzerlerine sürükleyin.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Çizmek için haritaya dokunun.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Cevabınızı eklemek için haritaya dokunun.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Cevabınızı eklemek için haritaya dokunun veya aşağıya bir adres yazın.", "app.components.CustomFieldsForm.tapToAddALine": "Satır eklemek için dokunun", "app.components.CustomFieldsForm.tapToAddAPoint": "Nokta eklemek için do<PERSON>nun", "app.components.CustomFieldsForm.tapToAddAnArea": "<PERSON><PERSON> alan e<PERSON> i<PERSON>n", "app.components.CustomFieldsForm.titleMaxLength": "Başlık en fazla {max} karakter uzunluğunda olmalıdır", "app.components.CustomFieldsForm.titleMinLength": "Başlık en az {min} karakter uzunluğunda olmalıdır", "app.components.CustomFieldsForm.titleRequired": "Başlık gereklidir", "app.components.CustomFieldsForm.topicRequired": "En az bir etiket gereklidir", "app.components.CustomFieldsForm.typeYourAnswer": "Cevabınızı yazın", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Cevabınızı yazmanız gerekmektedir", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Bir veya daha fazla shapefile içeren bir zip dosyası yükleyin.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Yazarken seçenekler arasında konum gör<PERSON><PERSON><PERSON><PERSON>miyorsa, kesin bir konum belirtmek için 'enlem, boylam' biçiminde geçerli koordinatlar ekleyebilirsiniz (örneğin: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Bazı alanlar geçersiz. Lütfen hataları düzeltip tekrar deneyin.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Raporunuz gönderilirken bilinmeyen bir hata oluştu. Lütfen tekrar deneyin.", "app.components.ErrorBoundary.errorFormLabelClose": "Ka<PERSON><PERSON>", "app.components.ErrorBoundary.errorFormLabelComments": "Ne oldu?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-posta", "app.components.ErrorBoundary.errorFormLabelName": "Ad", "app.components.ErrorBoundary.errorFormLabelSubmit": "<PERSON><PERSON><PERSON>", "app.components.ErrorBoundary.errorFormSubtitle": "Ekibimiz bilgilendirildi.", "app.components.ErrorBoundary.errorFormSubtitle2": "Bize yardımcı olmak isterseniz aşağıdaki alana ne <PERSON>ğunu yazın.", "app.components.ErrorBoundary.errorFormSuccessMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>il<PERSON>. Teşekkür ederiz.", "app.components.ErrorBoundary.errorFormTitle": "<PERSON><PERSON> sorun var.", "app.components.ErrorBoundary.genericErrorWithForm": "Bir hata oluştu ve bu içeriği görüntüleyemiyoruz. Lütfen tekrar deneyin veya {openForm}", "app.components.ErrorBoundary.openFormText": "so<PERSON>u an<PERSON>amıza yardımcı olun", "app.components.ErrorToast.budgetExceededError": "<PERSON><PERSON><PERSON> bütçeniz yok", "app.components.ErrorToast.votesExceededError": "<PERSON><PERSON>li oyunuz kalmadı.", "app.components.EventAttendanceButton.forwardToFriend": "Bir arkadaşınıza iletin", "app.components.EventAttendanceButton.maxRegistrationsReached": "Etkinlik kayıtları için maksimum sayıya ulaşılmıştır. Hiç yer kalmamıştır.", "app.components.EventAttendanceButton.register": "<PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.registered": "<PERSON><PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.seeYouThere": "Orada görüşürüz!", "app.components.EventAttendanceButton.seeYouThereName": "Orada g<PERSON><PERSON><PERSON><PERSON><PERSON>, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Daha az etkinlik bilgisi görüntüleniyor.", "app.components.EventCard.a11y_moreContentVisible": "Daha fazla etkinlik bilgisi görüntüleniyor.", "app.components.EventCard.a11y_readMore": "\"{eventTitle}\" et<PERSON>liği hakkında daha fazla bilgi edinin.", "app.components.EventCard.endsAt": "Bitiş", "app.components.EventCard.readMore": "Daha fazla bilgi edinin", "app.components.EventCard.showLess": "<PERSON><PERSON> a<PERSON> g<PERSON>", "app.components.EventCard.showMore": "<PERSON><PERSON> faz<PERSON>", "app.components.EventCard.startsAt": "Başlangıç", "app.components.EventPreviews.eventPreviewContinuousTitle2": "<PERSON>u projede yakla<PERSON>an ve devam eden et<PERSON>ler", "app.components.EventPreviews.eventPreviewTimelineTitle3": "<PERSON><PERSON> a<PERSON><PERSON>da yakla<PERSON>an ve devam eden etkin<PERSON>ler", "app.components.FileUploader.a11y_file": "Dosya:", "app.components.FileUploader.a11y_filesToBeUploaded": "Yüklenecek dosyalar: {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON>ç<PERSON> dosya eklenmedi.", "app.components.FileUploader.a11y_removeFile": "Bu dosyayı kaldır", "app.components.FileUploader.fileInputDescription": "Dosya seçmek için tıklayın", "app.components.FileUploader.fileUploadLabel": "Ekler (en fazla 50 MB)", "app.components.FileUploader.file_too_large2": "{maxSizeMb}MB'den büyük dosyalara izin verilmez.", "app.components.FileUploader.incorrect_extension": "{fileName} si<PERSON><PERSON><PERSON><PERSON> ta<PERSON>miyor, <PERSON><PERSON><PERSON>nmeyecek.", "app.components.FilterBoxes.a11y_allFilterSelected": "Seçilen durum filtresi: tümü", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# gönderim} other {# gönderim}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON><PERSON><PERSON> kaldır", "app.components.FilterBoxes.a11y_selectedFilter": "Seçilen durum filtresi: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "{numberOfSelectedTopics, plural, =0 {Sıfır etiket filtresi} one {Bir etiket filtresi} other {# etiket filtresi}} seçildi. {selectedTopicNames}", "app.components.FilterBoxes.all": "Tümü", "app.components.FilterBoxes.areas": "<PERSON><PERSON> g<PERSON> filtrele", "app.components.FilterBoxes.inputs": "girdi<PERSON>", "app.components.FilterBoxes.noValuesFound": "Mevcut değer yok.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON> a<PERSON> g<PERSON>", "app.components.FilterBoxes.showTagsWithNumber": "<PERSON><PERSON><PERSON> gö<PERSON> ({numberTags})", "app.components.FilterBoxes.statusTitle": "Durum", "app.components.FilterBoxes.topicsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FiltersModal.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "Klasör açıklaması:", "app.components.FolderFolderCard.a11y_folderTitle": "Klasör başlığı:", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, one {# proje} other {# proje}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "<PERSON><PERSON><PERSON><PERSON><PERSON> yapıldıktan sonra alan türü <PERSON>ştirilemez.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Tip", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Otomatik <PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Otomatik kaydetme, form düzenleyiciyi açtığınızda varsayılan olarak etkinleştirilir. \"X\" düğmesini kullanarak alan a<PERSON> panelini her kapattığınızda, otomatik olarak bir kaydetme işlemi tetiklenir.", "app.components.GanttChart.timeRange.month": "Ay", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Çok yıllı", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.today": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON> gidin", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "<PERSON><PERSON><PERSON> sayfaya geri dön", "app.components.HookForm.Feedback.errorTitle": "<PERSON><PERSON> sorun var", "app.components.HookForm.Feedback.submissionError": "<PERSON><PERSON><PERSON> den<PERSON>. <PERSON><PERSON> devam ederse bizimle iletişime geçin", "app.components.HookForm.Feedback.submissionErrorTitle": "Bizim ta<PERSON>fımızda bir sorun <PERSON>, üzgünüz", "app.components.HookForm.Feedback.successMessage": "Form başarıyla gönderildi", "app.components.HookForm.PasswordInput.passwordLabel": "Şifre", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON>a kaydır.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON><PERSON><PERSON> ka<PERSON>ı<PERSON>.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} fi<PERSON><PERSON><PERSON>.", "app.components.IdeaCards.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "En çok tartışılanlar", "app.components.IdeaCards.filters.newest": "<PERSON><PERSON>", "app.components.IdeaCards.filters.oldest": "<PERSON><PERSON>", "app.components.IdeaCards.filters.popular": "En çok beğenilenler", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortBy": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sıralama şu şekilde <PERSON>tirildi: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trend", "app.components.IdeaCards.showMore": "<PERSON><PERSON> faz<PERSON>", "app.components.IdeasMap.a11y_hideIdeaCard": "Fikir kartını gizleyin.", "app.components.IdeasMap.a11y_mapTitle": "<PERSON><PERSON> genel g<PERSON>", "app.components.IdeasMap.clickOnMapToAdd": "G<PERSON>di eklemek için haritaya tıklayın", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Bir yönetici olarak, bu aşama aktif olmasa bile haritaya tıklayarak girdilerinizi ekleyebilirsiniz.", "app.components.IdeasMap.filters": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "<PERSON><PERSON> konumda birden fazla giriş", "app.components.IdeasMap.noFilteredResults": "Seçtiğiniz filtrelerle herhangi bir sonuç bulunmadı", "app.components.IdeasMap.noResults": "<PERSON><PERSON><PERSON> bulunmadı", "app.components.IdeasMap.or": "veya", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, beğenmeyen yok.} one {1 dislike.} other {, # dislike.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, beğeni yok.} one {, 1 beğeni.} other {, # beğeni.}}", "app.components.IdeasMap.signInLinkText": "oturum açın", "app.components.IdeasMap.signUpLinkText": "kaydolun", "app.components.IdeasMap.submitIdea2": "<PERSON><PERSON><PERSON>", "app.components.IdeasMap.tapOnMapToAdd": "G<PERSON><PERSON> eklemek için haritaya dokunun", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Bir y<PERSON>netici o<PERSON>ak, bu aşama aktif o<PERSON>, girdilerinizi eklemek için haritaya dokunabilirsiniz.", "app.components.IdeasMap.userInputs2": "Kat<PERSON>lım<PERSON><PERSON>lardan gelen girdiler", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, yorum yok} one {, 1 yorum} other {, # yorum}}", "app.components.IdeasShow.bodyTitle": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.components.IdeasShow.deletePost": "Sil", "app.components.IdeasShow.editPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.goBack": "<PERSON><PERSON> gidin", "app.components.IdeasShow.moreOptions": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.or": "veya", "app.components.IdeasShow.proposedBudgetTitle": "Öngör<PERSON><PERSON> bütçe", "app.components.IdeasShow.reportAsSpam": "Spam bildir", "app.components.IdeasShow.send": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON><PERSON>, daha sonra ya<PERSON>ğı<PERSON>", "app.components.IdeasShowPage.signIn2": "<PERSON><PERSON><PERSON> yap", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu say<PERSON><PERSON> erişemezsiniz. Sayfaya erişmek için oturum açmanız veya kaydolmanız gerekiyor.", "app.components.LocationInput.noOptions": "Seçenek yok", "app.components.Modal.closeWindow": "<PERSON><PERSON><PERSON><PERSON> kapat", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Yeni bir e-posta adresi girin", "app.components.PageNotFound.goBackToHomePage": "<PERSON> say<PERSON>ya geri dön", "app.components.PageNotFound.notFoundTitle": "Sayfa bulunamadı", "app.components.PageNotFound.pageNotFoundDescription": "İstenen sayfa bulunamadı.", "app.components.PagesForm.descriptionMissingOneLanguageError": "En az bir dil için içerik sağlayın", "app.components.PagesForm.editContent": "İçerik", "app.components.PagesForm.fileUploadLabel": "Ekler (en fazla 50 MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Dosyalar 50 MB'tan büyük olamaz. Eklenen dosyalar bu sayfanın altında gösterilir.", "app.components.PagesForm.navbarItemTitle": "<PERSON><PERSON><PERSON>me <PERSON> ad", "app.components.PagesForm.pageTitle": "Başlık", "app.components.PagesForm.savePage": "Sayfayı kaydet", "app.components.PagesForm.saveSuccess": "<PERSON><PERSON> baş<PERSON><PERSON><PERSON> ka<PERSON>il<PERSON>.", "app.components.PagesForm.titleMissingOneLanguageError": "En az bir dil için başlık sağlayın", "app.components.Pagination.back": "<PERSON><PERSON><PERSON> say<PERSON>", "app.components.Pagination.next": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "{votesLimit}limit<PERSON> a<PERSON>an {votesCast}harca<PERSON>ı yaptınız. Lütfen sepetinizden bazı ürünleri kaldırın ve tekrar deneyin.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} sol", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Sepetinizi gönderebilmeniz için en az {votesMinimum} adresinde harcama yapmanız gerekmektedir.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Göndermeden önce en az bir seçenek seçmeniz gerekmektedir.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Göndermeden önce sepetinize bir şey eklemeniz gerekir.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Kred<PERSON> kalmad<PERSON>} other {# bitti {totalNumberOfVotes, plural, one {1 kredi} other {# kredi}} kaldı}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON> kalmadı} other {# dışında {totalNumberOfVotes, plural, one {1 puan} other {# puan}} kaldı}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {<PERSON><PERSON> kalmadı} other {# out of {totalNumberOfVotes, plural, one {1 jeton} other {# jeton}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Oy kalmadı} other {# dışında {totalNumberOfVotes, plural, one {1 oy} other {# oy}} kaldı}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# oylar} one {# oy} other {# oy}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "{votesLimit}sın<PERSON>rın<PERSON> aşan {votesCast} oy kullandınız. Lütfen bazı oyları kaldırın ve tekrar deneyin.", "app.components.ParticipationCTABars.addInput": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.allocateBudget": "Bütçenizi ayırın", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Bütçeniz başarıyla gönderildi.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Katılım için açık", "app.components.ParticipationCTABars.poll": "An<PERSON><PERSON> katılın", "app.components.ParticipationCTABars.reviewDocument": "Belgeyi gözden geçirin", "app.components.ParticipationCTABars.seeContributions": "Katkıları görün", "app.components.ParticipationCTABars.seeEvents3": "Etkinliklere bakın", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeInitiatives": "G<PERSON>ş<PERSON><PERSON><PERSON>ü<PERSON>", "app.components.ParticipationCTABars.seeIssues": "Sorunları görün", "app.components.ParticipationCTABars.seeOptions": "Se<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seePetitions": "Dilekçelere bakınız", "app.components.ParticipationCTABars.seeProjects": "Proje<PERSON>i <PERSON>", "app.components.ParticipationCTABars.seeProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON> bakın", "app.components.ParticipationCTABars.seeQuestions": "Soruları görün", "app.components.ParticipationCTABars.submit": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.takeTheSurvey": "An<PERSON><PERSON> katılın", "app.components.ParticipationCTABars.userHasParticipated": "Bu projeye katıldınız.", "app.components.ParticipationCTABars.viewInputs": "Girişleri gö<PERSON>ü<PERSON>", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "oylama", "app.components.ParticipationCTABars.votesCounter.votes": "oylar", "app.components.PasswordInput.a11y_passwordHidden": "<PERSON><PERSON><PERSON> g<PERSON>", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength1Password": "Parola gücü çok düşük", "app.components.PasswordInput.a11y_strength2Password": "<PERSON><PERSON><PERSON> gü<PERSON> dü<PERSON>", "app.components.PasswordInput.a11y_strength3Password": "<PERSON><PERSON><PERSON>ta d<PERSON>", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.a11y_strength5Password": "Parola gücü çok yüksek", "app.components.PasswordInput.hidePassword": "Parolayı gizle", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Çok kısa (en az {minimumPasswordLength} karakter)", "app.components.PasswordInput.minimumPasswordLengthError": "En az {minimumPasswordLength} karakter uzunluğunda bir parola girin", "app.components.PasswordInput.passwordEmptyError": "Parolanızı girin", "app.components.PasswordInput.passwordStrengthTooltip1": "Parolanızı güçlendirmek için:", "app.components.PasswordInput.passwordStrengthTooltip2": "Ardışık olmayacak şekilde küçük harf, b<PERSON><PERSON><PERSON><PERSON> harf, r<PERSON><PERSON>, özel karakter ve noktalama işaretleri birleşimi kullanın", "app.components.PasswordInput.passwordStrengthTooltip3": "Yaygın olarak kullanılan veya kolay tahmin edilebilecek sözcükler kullanmayın", "app.components.PasswordInput.passwordStrengthTooltip4": "Uzunluğu artırın", "app.components.PasswordInput.showPassword": "Parolayı göster", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "Orta", "app.components.PasswordInput.strength4Password": "Güçlü", "app.components.PasswordInput.strength5Password": "Çok güçlü", "app.components.PostCardsComponents.list": "Liste", "app.components.PostCardsComponents.map": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "<PERSON><PERSON><PERSON> bi<PERSON>gi<PERSON>dir<PERSON> e<PERSON>", "app.components.PostComponents.OfficialFeedback.cancel": "İptal", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Sil", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Bu resmi bilgilendirmeyi silmek istediğinizden emin misiniz?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "En son {date} ta<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastUpdate": "<PERSON> güncelleme: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Kullanıcıların adınızı nasıl göreceğini seçin", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Resmi bilgilendirmeyi yazanın adı", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "<PERSON><PERSON><PERSON> bilgilendirme metin gö<PERSON>i", "app.components.PostComponents.OfficialFeedback.officialUpdates": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.postedOn": "{date} ta<PERSON><PERSON><PERSON>ı", "app.components.PostComponents.OfficialFeedback.publishButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "<PERSON>ha <PERSON> bilgilendirmeleri göster", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Bilgilendirme yapın...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir so<PERSON>", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "<PERSON>jı gü<PERSON>", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Bilgilendirme başarıyla yayımlandı!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "{postUrl} üzerinden '{postTitle}' katkıma destek olun!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Katkıma destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Katkıma destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "{postUrl} üzerinden '{postTitle}' fikrime destek olun!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Fikrime destek olun: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Fikrime destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Bu öneri hakkında ne düşünüyorsunuz? Oylayın ve sesinizi duyurmak için {postUrl} adresindeki tartışmayı paylaşın.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Önerime destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "{postUrl} üzerinde bir '{postTitle}' şeklinde bir yorum yayınladım!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "<PERSON>ni bir yorum yayınladım: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "<PERSON>ni bir yorum yayınladım: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "{postUrl} üzerinden '{postTitle}' seçenek önerime destek olun!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Seçenek önerime destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Seçeneğime destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "{postUrl}adresindeki '{postTitle}' dilek<PERSON><PERSON>i deste<PERSON>!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> deste<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> deste<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "{postUrl} üzerinden '{postTitle}' projeme destek olun!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "<PERSON><PERSON><PERSON> destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "<PERSON><PERSON><PERSON> destek olun: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "{postUrl}adresinden '{postTitle}' tek<PERSON><PERSON><PERSON> des<PERSON>!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> des<PERSON>: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Az önce {orgName}i<PERSON><PERSON> bir teklif gö<PERSON> : {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "{postUrl} üzerinden '{postTitle}' sorusu ile ilgili tartışmaya katılın!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Tartışmaya katılın: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Tartışmaya katılın: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "{postTitle} i<PERSON>in oy verin:", "app.components.PostComponents.linkToHomePage": "<PERSON> <PERSON><PERSON> ba<PERSON>", "app.components.PostComponents.readMore": "Daha fazla bilgi...", "app.components.PostComponents.topics": "<PERSON><PERSON><PERSON>", "app.components.ProjectArchivedIndicator.archivedProject": "Ne yazık ki, arşivlendiği için artık bu projeye katılamazsınız", "app.components.ProjectArchivedIndicator.previewProject": "Taslak proje:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Yalnızca moderatörler ve önizleme bağlantısı olanlar tarafından görülebilir.", "app.components.ProjectCard.a11y_projectDescription": "<PERSON><PERSON>:", "app.components.ProjectCard.a11y_projectTitle": "<PERSON><PERSON> b<PERSON>:", "app.components.ProjectCard.addYourOption": "Seçeneğinizi ekleyin", "app.components.ProjectCard.allocateYourBudget": "Bütçenizi ayırın", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.components.ProjectCard.comment": "<PERSON><PERSON>", "app.components.ProjectCard.contributeYourInput": "Kat<PERSON><PERSON><PERSON> bulunun", "app.components.ProjectCard.finished": "Tamamlandı", "app.components.ProjectCard.joinDiscussion": "Tartışmaya katılın", "app.components.ProjectCard.learnMore": "Daha fazla bilgi edinin", "app.components.ProjectCard.reaction": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.reviewDocument": "Belgeyi gözden geçirin", "app.components.ProjectCard.submitAnIssue": "<PERSON><PERSON>", "app.components.ProjectCard.submitYourIdea": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.submitYourInitiative": "Giriş<PERSON><PERSON><PERSON>", "app.components.ProjectCard.submitYourPetition": "Dilekçenizi gönderin", "app.components.ProjectCard.submitYourProject": "<PERSON>je<PERSON><PERSON>", "app.components.ProjectCard.submitYourProposal": "Teklifinizi gönderin", "app.components.ProjectCard.takeThePoll": "<PERSON><PERSON><PERSON>ya katılın", "app.components.ProjectCard.takeTheSurvey": "An<PERSON><PERSON> katılın", "app.components.ProjectCard.viewTheContributions": "Katkıları görüntüleyin", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheInitiatives": "Giriş<PERSON><PERSON><PERSON> gö<PERSON>", "app.components.ProjectCard.viewTheIssues": "Yorumları görü<PERSON>ü<PERSON>in", "app.components.ProjectCard.viewTheOptions": "Seçenekle<PERSON>", "app.components.ProjectCard.viewThePetitions": "<PERSON>lekç<PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "Proje<PERSON>i <PERSON>", "app.components.ProjectCard.viewTheProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheQuestions": "Soruları görüntüleyin", "app.components.ProjectCard.vote": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# yorum} other {# yorum}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# katkı} other {# katkı}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {hen<PERSON>z fikir yok} one {# fikir} other {# fikir}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# inisiyatifler} one {# inisiyatif} other {# inisiyatifler}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# yorum} other {# yorum}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# seçenek} other {# seçenek}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# dilekçeler} one {# dilekçe} other {# dilekçeler}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# proje} other {# proje}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# teklifler} one {# teklif} other {# teklifler}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# soru} other {# soru}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# Yorumlar} one {# Yorumlar} other {# yorumlar}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# giri<PERSON><PERSON>} one {# giriş} other {# giri<PERSON><PERSON>}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, one {# proje} other {# proje}}", "app.components.ProjectFolderCards.components.Topbar.all": "Tümü", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.components.ProjectFolderCards.components.Topbar.draft": "Taslak", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtre ölçütü", "app.components.ProjectFolderCards.components.Topbar.published2": "Yayınlandı", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Etiket", "app.components.ProjectFolderCards.noProjectYet": "<PERSON><PERSON> anda açık proje yok", "app.components.ProjectFolderCards.noProjectsAvailable": "Kullanılabilir proje yok", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON> faz<PERSON>", "app.components.ProjectFolderCards.stayTuned": "<PERSON>ni katılım fırsatları için tekrar kontrol edin", "app.components.ProjectFolderCards.tryChangingFilters": "Seçilen filtreleri değiş<PERSON>rmeyi den<PERSON>.", "app.components.ProjectTemplatePreview.alsoUsedIn": "<PERSON><PERSON> kentlerde de kullanılır:", "app.components.ProjectTemplatePreview.copied": "Kopyalandı", "app.components.ProjectTemplatePreview.copyLink": "Bağlantıyı kopyala", "app.components.QuillEditor.alignCenter": "<PERSON><PERSON>", "app.components.QuillEditor.alignLeft": "<PERSON>a hizala", "app.components.QuillEditor.alignRight": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.bold": "Kalı<PERSON>", "app.components.QuillEditor.clean": "Biçimlendirmeyi kaldır", "app.components.QuillEditor.customLink": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.customLinkPrompt": "Bağlantıyı girin:", "app.components.QuillEditor.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.image": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.imageAltPlaceholder": "Görselin kısa açıklaması", "app.components.QuillEditor.italic": "İtalik", "app.components.QuillEditor.link": "Bağlantı ekle", "app.components.QuillEditor.linkPrompt": "Bağlantıyı girin:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Sıralı liste", "app.components.QuillEditor.remove": "Kaldır", "app.components.QuillEditor.save": "<PERSON><PERSON>", "app.components.QuillEditor.subtitle": "Alt yazı", "app.components.QuillEditor.title": "Başlık", "app.components.QuillEditor.unorderedList": "Sırasız liste", "app.components.QuillEditor.video": "Video ekle", "app.components.QuillEditor.videoPrompt": "<PERSON><PERSON> girin:", "app.components.QuillEditor.visitPrompt": "Bağlantıyı ziyaret edin:", "app.components.ReactionControl.completeProfileToReact": "Tepki vermek için profilinizi tama<PERSON>layın", "app.components.ReactionControl.dislike": "Be<PERSON>enmeme", "app.components.ReactionControl.dislikingDisabledMaxReached": "{projectName}adres<PERSON>e maksimum beğenmeme sayınıza ulaştınız", "app.components.ReactionControl.like": "Gibi", "app.components.ReactionControl.likingDisabledMaxReached": "{projectName}adres<PERSON>e maksimum beğeni sayınıza ulaştınız.", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Bu aşama başladığında tepki verme etkinleştirilecektir", "app.components.ReactionControl.reactingDisabledPhaseOver": "Bu aşamada tepki vermek artık mümkün değil", "app.components.ReactionControl.reactingDisabledProjectInactive": "Artık {projectName}adresindeki fikirlere tepki veremezsiniz.", "app.components.ReactionControl.reactingNotEnabled": "<PERSON><PERSON><PERSON> verme şu anda bu proje i<PERSON><PERSON> et<PERSON>tir", "app.components.ReactionControl.reactingNotPermitted": "Tepki verme yalnızca belirli gruplar için etkinleştirilir", "app.components.ReactionControl.reactingNotSignedIn": "Tepki vermek için oturum açın.", "app.components.ReactionControl.reactingPossibleLater": "Tepki verme {enabledFromDate}adresinde başlayacaktır.", "app.components.ReactionControl.reactingVerifyToReact": "Tepki verebilmek için kimliğinizi doğrulayın.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Etkinlik tarihi: {startDate} {startTime} adresinden {endDate} {endTime}adresine.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Etkinlik tarihi: {eventDate} ile {startTime} arasında {endTime}.", "app.components.Sharing.linkCopied": "Bağlantı kopyalandı", "app.components.Sharing.or": "veya", "app.components.Sharing.share": "Paylaş", "app.components.Sharing.shareByEmail": "E-posta ile <PERSON>", "app.components.Sharing.shareByLink": "Bağlantıyı kopyala", "app.components.Sharing.shareOnFacebook": "Facebook'ta paylaş", "app.components.Sharing.shareOnTwitter": "<PERSON>'da pay<PERSON>", "app.components.Sharing.shareThisEvent": "<PERSON><PERSON> etkinliği <PERSON>ı<PERSON>", "app.components.Sharing.shareThisFolder": "Paylaş", "app.components.Sharing.shareThisProject": "<PERSON><PERSON> proje<PERSON>", "app.components.Sharing.shareViaMessenger": "<PERSON> il<PERSON>", "app.components.Sharing.shareViaWhatsApp": "WhatsApp ile paylaş", "app.components.SideModal.closeButtonAria": "Ka<PERSON><PERSON>", "app.components.StatusModule.futurePhase": "Henüz başlamamış bir aşamayı görüntülüyorsunuz. Aşama başladığında katılabileceksiniz.", "app.components.StatusModule.modifyYourSubmission1": "G<PERSON>nder<PERSON><PERSON> değ<PERSON>ş<PERSON>rin", "app.components.StatusModule.submittedUntil3": "Oyunuzu şu tarihe kadar verebilirsiniz", "app.components.TopicsPicker.numberOfSelectedTopics": "{numberOfSelectedTopics, plural, =0 {<PERSON>ıfı<PERSON> etiket} one {bir etiket} other {# etiket}} seçildi. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "<PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.moreOptions": "<PERSON><PERSON> faz<PERSON>", "app.components.UI.MoreActionsMenu.showMoreActions": "<PERSON><PERSON> fazla e<PERSON>", "app.components.UI.PhaseFilter.noAppropriatePhases": "Bu proje için uygun bir aşama bulunamamıştır", "app.components.UI.RemoveImageButton.a11y_removeImage": "Kaldır", "app.components.UI.TranslateButton.original": "Orijinal", "app.components.UI.TranslateButton.translate": "<PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "Katılabilmeniz için ek bilgiler gerekmektedir.", "app.components.Unauthorized.completeProfile": "<PERSON> profil", "app.components.Unauthorized.completeProfileTitle": "Katılmak için profilinizi tamamlayın", "app.components.Unauthorized.noPermission": "Bu sayfayı görüntülemek için izniniz yok", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu sayfaya erişim izniniz yok.", "app.components.Upload.errorImageMaxSizeExceeded": "Seçtiğiniz görsel {maxFileSize} MB'tan bü<PERSON>ük", "app.components.Upload.errorImagesMaxSizeExceeded": "Seçtiğiniz bir veya birkaç görsel {maxFileSize} MB'tan büyük", "app.components.Upload.onlyOneImage": "Yalnızca 1 görsel yükleyebilirsiniz", "app.components.Upload.onlyXImages": "Yalnızca {maxItemsCount} görsel yükleyebilirsiniz", "app.components.Upload.remaining": "kalan", "app.components.Upload.uploadImageLabel": "<PERSON>ir görsel seçin (en fazla {maxImageSizeInMb} MB)", "app.components.Upload.uploadMultipleImagesLabel": "Bir veya daha fazla g<PERSON><PERSON>", "app.components.UpsellTooltip.tooltipContent": "Bu özellik mevcut planınıza dahil de<PERSON>dir. Kilidi açmak için Kamu Başarı Yöneticinizle veya yöneticinizle konuşun.", "app.components.UserName.anonymous": "<PERSON><PERSON><PERSON>", "app.components.UserName.anonymousTooltip2": "Bu kullanıcı katkılarını anonimleştirmeye karar verdi", "app.components.UserName.authorWithNoNameTooltip": "Adınızı girmediğiniz için adınız otomatik olarak oluşturuldu. Değiştirmek isterseniz lütfen profilinizi güncelleyin.", "app.components.UserName.deletedUser": "bilin<PERSON>yen yazar", "app.components.UserName.verified": "Doğrulandı", "app.components.VerificationModal.verifyAuth0": "NemID ile doğrulayın", "app.components.VerificationModal.verifyBOSA": "Kimliğinizi itsme veya eID ile doğrulayın", "app.components.VerificationModal.verifyBosaFas": "itsme veya eID ile doğrulayın", "app.components.VerificationModal.verifyClaveUnica": "Kimliğinizi Clave Unica ile doğrulayın", "app.components.VerificationModal.verifyFakeSSO": "Sahte SSO ile Doğrulama", "app.components.VerificationModal.verifyIdAustria": "ID Austria ile doğrulayın", "app.components.VerificationModal.verifyKeycloak": "ID-Porten ile doğrulayın", "app.components.VerificationModal.verifyNemLogIn": "MitID ile doğrulayın", "app.components.VerificationModal.verifyTwoday2": "BankID veya Freja eID+ ile doğrulayın", "app.components.VerificationModal.verifyYourIdentity": "Kimliğinizi doğrulayın", "app.components.VoteControl.budgetingFutureEnabled": "Bütçenizi {enabledFromDate} tarihinden itibaren ayırabilirsiniz.", "app.components.VoteControl.budgetingNotPermitted": "Katılım için bütçe ayırma şu an için etkin değil.", "app.components.VoteControl.budgetingNotPossible": "Bütçenizde değişiklik yapmanız şu an için mümkün değil.", "app.components.VoteControl.budgetingNotVerified": "<PERSON>am etmek i<PERSON> {verifyAccountLink}.", "app.components.VoteInputs._shared.currencyLeft1": "{budgetLeft} / {totalBudget} adresiniz kaldı", "app.components.VoteInputs._shared.numberOfCreditsLeft": "{votesLeft, plural, =0 {krediniz kalmadı} other {# krediniz kalmadı {totalNumberOfVotes, plural, one {1 krediniz var} other {# krediniz var}} krediniz kaldı}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "{votesLeft, plural, =0 {hiç puanınız kalmadı} other {# dışında {totalNumberOfVotes, plural, one {1 puan} other {# puan}} kaldı}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Elinizde {votesLeft, plural, =0 {hiç jeton kalmadı} other {# out of {totalNumberOfVotes, plural, one {1 jeton} other {# jeton}} kaldı}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Elinizde {votesLeft, plural, =0 {oy kalmadı} other {# dışında {totalNumberOfVotes, plural, one {1 oy} other {# oy}} kaldı}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Bütçenizi zaten gönderdiniz. Değiştirmek için \"Gönderiminizi değiştirin\" seçeneğine tıklayın.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Bütçenizi zaten gönderdiniz. Değiştirmek için proje sayfasına geri dönün ve \"Gönderinizi değiştirin\" seçeneğine tıklayın.", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Bu aşama aktif o<PERSON>ı<PERSON>ı için bütçeleme mevcut değildir.", "app.components.VoteInputs.single.youHaveVotedForX2": "<PERSON><PERSON> verdiniz {votes, plural, =0 {# seçenekler} one {# seçenek} other {# seçenek}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, tepki<PERSON> ve oylar gibi bu gir<PERSON><PERSON> il<PERSON> tüm verileri kaybedeceğiniz anlamına gelir. Bu işlem geri alınamaz.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "<PERSON>u girdiyi silmek istediğinizden emin misiniz?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "İptal", "app.components.admin.PostManager.components.PostTable.Row.confirm": "<PERSON>aylayın", "app.components.admin.SlugInput.resultingURL": "Sonuçlanan URL", "app.components.admin.SlugInput.slugTooltip": "Slug, sayfanın web adresinin veya URL'sinin sonundaki benzersiz kelime grubudur.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "URL'yi değiştirirseniz eski URL ile sayfaya yönlendirilen bağlantılar kullanılamaz.", "app.components.admin.SlugInput.urlSlugLabel": "<PERSON><PERSON> Kı<PERSON> Adı", "app.components.admin.UserFilterConditions.addCondition": "Bir koşul ekleyin", "app.components.admin.UserFilterConditions.field_email": "E-posta", "app.components.admin.UserFilterConditions.field_event_attendance": "Etkinlik kayıtları", "app.components.admin.UserFilterConditions.field_follow": "Ta<PERSON><PERSON> et", "app.components.admin.UserFilterConditions.field_lives_in": "Yaşadığ<PERSON> yer", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "<PERSON><PERSON> i<PERSON> an<PERSON>i", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Durum girdisi ile etkile<PERSON> k<PERSON>du", "app.components.admin.UserFilterConditions.field_participated_in_project": "Projeye katkıda bulundu", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Etiket ile bir şey yayınladı", "app.components.admin.UserFilterConditions.field_registration_completed_at": "<PERSON><PERSON><PERSON> tarihi", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Doğrulama", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Düşünce", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "bu et<PERSON><PERSON>lerden herhangi biri için kayıtlı de<PERSON>", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "herhangi bir etkinlik için ka<PERSON>ı<PERSON>ğ<PERSON>", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "bu etkinliklerden biri için ka<PERSON>ı<PERSON>ır", "app.components.admin.UserFilterConditions.predicate_attends_something": "en az bir etkinlik için ka<PERSON>ı<PERSON>ı<PERSON>ır", "app.components.admin.UserFilterConditions.predicate_begins_with": "ile b<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_commented_in": "yorum yapan", "app.components.admin.UserFilterConditions.predicate_contains": "içeren", "app.components.admin.UserFilterConditions.predicate_ends_on": "tari<PERSON>de biten", "app.components.admin.UserFilterConditions.predicate_has_value": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_in": "işlem gerçekleştiren", "app.components.admin.UserFilterConditions.predicate_is": "olan", "app.components.admin.UserFilterConditions.predicate_is_admin": "yönetici", "app.components.admin.UserFilterConditions.predicate_is_after": "sonra", "app.components.admin.UserFilterConditions.predicate_is_before": "önce", "app.components.admin.UserFilterConditions.predicate_is_checked": "kontrol edilen", "app.components.admin.UserFilterConditions.predicate_is_empty": "boş", "app.components.admin.UserFilterConditions.predicate_is_equal": "olan", "app.components.admin.UserFilterConditions.predicate_is_exactly": "tam", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "büyük", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "büyük veya eşit", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "normal kullanıcı", "app.components.admin.UserFilterConditions.predicate_is_not_area": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "klasörü hariç tutar", "app.components.admin.UserFilterConditions.predicate_is_not_input": "g<PERSON><PERSON><PERSON> ha<PERSON> tutar", "app.components.admin.UserFilterConditions.predicate_is_not_project": "proje <PERSON>", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "konu ha<PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of": "<PERSON><PERSON><PERSON><PERSON> biri", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "<PERSON><PERSON><PERSON><PERSON> biri", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "klasörlerden biri", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "girdi<PERSON><PERSON> biri", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "proje<PERSON><PERSON> biri", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "kon<PERSON><PERSON> biri", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "proje <PERSON>", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "küçük", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "küçük veya eşit", "app.components.admin.UserFilterConditions.predicate_is_verified": "doğrulanmış", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "ile b<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "yo<PERSON> ya<PERSON>n", "app.components.admin.UserFilterConditions.predicate_not_contains": "içermeyen", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "ta<PERSON><PERSON><PERSON> bit<PERSON>yen", "app.components.admin.UserFilterConditions.predicate_not_has_value": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_in": "kat<PERSON><PERSON><PERSON> bulu<PERSON>yan", "app.components.admin.UserFilterConditions.predicate_not_is": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "kontrol edilmeyen", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "b<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "normal kullanıcı olmayan", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "proje <PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "doğrulanmamış", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "girdi <PERSON>", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "yoruma tepki vermedi", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "girdiye tepki vermedi", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "bir etkinliğe kayıt yaptırmadı", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "ankete katılmadı", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "oylamaya katılmadı", "app.components.admin.UserFilterConditions.predicate_nothing": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_posted_input": "girdi <PERSON>", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "<PERSON><PERSON>a tepki <PERSON>", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "girdiye tepki verdi", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "bir et<PERSON>liğe kayıtlı", "app.components.admin.UserFilterConditions.predicate_something": "bir <PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_taken_survey": "ankete katıldı", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_voted_in3": "oylamaya katıldı", "app.components.admin.UserFilterConditions.rulesFormLabelField": "<PERSON>z nitelik", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Koşul", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Katkılarınızla ilgili bildirim almayacaksınız", "app.components.anonymousParticipationModal.cancel": "İptal", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON> et", "app.components.anonymousParticipationModal.participateAnonymously": "<PERSON><PERSON><PERSON> o<PERSON> katılın", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "Bu, <b>profi<PERSON><PERSON></b> y<PERSON><PERSON><PERSON><PERSON><PERSON>, proje yöneticilerinden ve diğer sakinlerden bu özel katkı için güvenli bir şekilde <b>g<PERSON><PERSON></b>, b<PERSON><PERSON><PERSON> kimse bu katkıyı sizinle ilişkilendiremez. Anonim katkılar düzenlenemez ve nihai olarak kabul edilir.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Platformumuzu her kullanıcı için güvenli hale getirmek bizim için en önemli önceliktir. Kelimeler önemlidir, bu yüzden lütfen birbirinize karşı nazik olun.", "app.components.avatar.titleForAccessibility": "Profil {fullName}", "app.components.customFields.mapInput.removeAnswer": "Cevabı kaldır", "app.components.customFields.mapInput.undo": "<PERSON><PERSON> al", "app.components.customFields.mapInput.undoLastPoint": "Son noktayı geri al", "app.components.followUnfollow.follow": "Ta<PERSON><PERSON> et", "app.components.followUnfollow.followADiscussion": "Tartışmayı takip edin", "app.components.followUnfollow.followTooltipInputPage2": "Aşağ<PERSON><PERSON><PERSON> durum değişiklikleri, resmi <PERSON> ve yorumlar hakkında e-posta güncellemelerini tetikler. İstediğiniz zaman {unsubscribeLink} adresini ziyaret edebilirsiniz.", "app.components.followUnfollow.followTooltipProjects2": "Aşağıdaki proje değişiklikleri hakkında e-posta güncellemelerini tetikler. İstediğiniz zaman {unsubscribeLink} adresini kullanabilirsiniz.", "app.components.followUnfollow.unFollow": "<PERSON><PERSON><PERSON> bırak", "app.components.followUnfollow.unsubscribe": "ABONELİKTEN ÇIK", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.previous": "<PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.save": "<PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Kullanıcı e-posta adresine veya adına göre aramak için yazmaya başlayın...", "app.components.form.anonymousSurveyMessage2": "Bu ankete verilen tüm yanıtlar anonimleştirilmiştir.", "app.components.form.backToInputManager": "<PERSON><PERSON><PERSON><PERSON> geri dön", "app.components.form.backToProject": "<PERSON><PERSON><PERSON> geri dön", "app.components.form.components.controls.mapInput.removeAnswer": "Cevabı kaldır", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON> al", "app.components.form.components.controls.mapInput.undoLastPoint": "Son noktayı geri al", "app.components.form.controls.addressInputAriaLabel": "<PERSON><PERSON>", "app.components.form.controls.addressInputPlaceholder6": "Bir adres girin...", "app.components.form.controls.adminFieldTooltip": "Alan yalnızca yöneticiler tarafından görülebilir", "app.components.form.controls.allStatementsError": "<PERSON><PERSON><PERSON> ifadeler i<PERSON>in bir cevap seçilmelidir.", "app.components.form.controls.back": "<PERSON><PERSON>", "app.components.form.controls.clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> temizle", "app.components.form.controls.clearAllScreenreader": "Yukarıdaki matris sorusundaki tüm cevapları temizleyin", "app.components.form.controls.clickOnMapMultipleToAdd3": "Çizmek için haritaya tıklayın. <PERSON>rd<PERSON><PERSON>n, noktaları taşımak için üzerlerine sürükleyin.", "app.components.form.controls.clickOnMapToAddOrType": "Cevabınızı eklemek için haritaya tıklayın veya aşağıya bir adres yazın.", "app.components.form.controls.confirm": "<PERSON>aylayın", "app.components.form.controls.cosponsorsPlaceholder": "<PERSON><PERSON><PERSON> i<PERSON> bir isim yazmaya ba<PERSON>n", "app.components.form.controls.currentRank": "<PERSON><PERSON>:", "app.components.form.controls.minimumCoordinates2": "En az {numPoints} harita noktası gereklidir.", "app.components.form.controls.noRankSelected": "<PERSON><PERSON><PERSON><PERSON> seçilmedi", "app.components.form.controls.notPublic1": "*Bu cevap sadece proje yöneticileriyle <PERSON>, ka<PERSON><PERSON><PERSON><PERSON>lmayacaktır.", "app.components.form.controls.optionalParentheses": "(is<PERSON><PERSON><PERSON> bağlı)", "app.components.form.controls.rankingInstructions": "Seçenekleri sıralamak için sürükleyip bırakın.", "app.components.form.controls.selectAsManyAsYouLike": "*İstediğiniz kadar seçin", "app.components.form.controls.selectBetween": "* {minItems} ve {maxItems} se<PERSON><PERSON><PERSON><PERSON> a<PERSON>ından seçim yapın", "app.components.form.controls.selectExactly2": "*Tam olarak seçin {selectExactly, plural, one {# seçenek} other {# options}}", "app.components.form.controls.selectMany": "*İstediğiniz kadar seçin", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Çizmek için haritaya dokunun. Ardından, noktaları taşımak için üzerlerine sürükleyin.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Çizmek için haritaya dokunun.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Cevabınızı eklemek için haritaya dokunun.", "app.components.form.controls.tapOnMapToAddOrType": "Cevabınızı eklemek için haritaya dokunun veya aşağıya bir adres yazın.", "app.components.form.controls.tapToAddALine": "Satır eklemek için dokunun", "app.components.form.controls.tapToAddAPoint": "Nokta eklemek için do<PERSON>nun", "app.components.form.controls.tapToAddAnArea": "<PERSON><PERSON> alan e<PERSON> i<PERSON>n", "app.components.form.controls.uploadShapefileInstructions": "* Bir veya daha fazla shapefile içeren bir zip dosyası yükleyin.", "app.components.form.controls.validCordinatesTooltip2": "Yazarken seçenekler arasında konum gör<PERSON><PERSON><PERSON><PERSON>mezse, kesin bir konum belirtmek için 'enlem, boylam' biçiminde geçerli koordinatlar ekleyebilirsiniz (örneğin: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} {total}dışında", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} {total}dı<PERSON>ında , {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} {total}d<PERSON><PERSON><PERSON><PERSON>, burada {maxValue} {maxLabel}'dir", "app.components.form.error": "<PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "Google Maps tarafından sağlanan konum alanı yüklenemedi.", "app.components.form.progressBarLabel": "<PERSON><PERSON>", "app.components.form.submit": "<PERSON><PERSON><PERSON>", "app.components.form.submitApiError": "Form gönderilirken bir sorun oluştu. Lütfen olası hataları düzeltip tekrar deneyin.", "app.components.form.verifiedBlocked": "Doğrulanmış bilgiler içerdiği için bu alanı düzenleyemezsiniz", "app.components.formBuilder.Page": "Say<PERSON>", "app.components.formBuilder.accessibilityStatement": "eri̇şi̇lebi̇li̇rli̇k beyani", "app.components.formBuilder.addAnswer": "<PERSON><PERSON><PERSON> e<PERSON>", "app.components.formBuilder.addStatement": "Açıklama ekle", "app.components.formBuilder.agree": "Katılıyorum", "app.components.formBuilder.ai1": "YAPAY ZEKA", "app.components.formBuilder.aiUpsellText1": "Yapay zeka paketimize er<PERSON><PERSON><PERSON><PERSON><PERSON> varsa, metin yanıtlarını yapay zeka ile özetleyebilir ve kategorilere ayırabilirsiniz", "app.components.formBuilder.askFollowUpToggleLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLink": "<PERSON><PERSON><PERSON><PERSON> bağlantısı", "app.components.formBuilder.cancelLeaveBuilderButtonText": "İptal", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "Çok sayıda seçin", "app.components.formBuilder.chooseOne": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.close": "Ka<PERSON><PERSON>", "app.components.formBuilder.closed": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.configureMap": "Haritayı yapılandırma", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, gitmek istiyorum.", "app.components.formBuilder.content": "İçerik", "app.components.formBuilder.continuePageLabel": "<PERSON><PERSON> ediyor", "app.components.formBuilder.cosponsors": "Eş sponsorlar", "app.components.formBuilder.default": "Varsayılan", "app.components.formBuilder.defaultContent": "Varsayılan içerik", "app.components.formBuilder.delete": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.deleteButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Bu zaten forma eklenmiştir. Varsayılan içerik yalnızca bir kez kullanılabilir.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Özel içerik eklemek mevcut lisansınızın bir parçası değildir. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.components.formBuilder.disagree": "Katılmıyorum", "app.components.formBuilder.displayAsDropdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> gö<PERSON>", "app.components.formBuilder.displayAsDropdownTooltip": "Seçenekleri bir açılır menüde görüntüleyin. Çok sayıda seçeneğiniz varsa, bu önerilir.", "app.components.formBuilder.done": "Tamamlandı", "app.components.formBuilder.drawArea": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawRoute": "Rota çizin", "app.components.formBuilder.dropPin": "<PERSON><PERSON> pim", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "En az 1 cevap veriniz. Lütfen her cevabın bir başlığı olması gerektiğini unutmayın.", "app.components.formBuilder.emptyOptionError": "En az 1 yanıt verin", "app.components.formBuilder.emptyStatementError": "En az 1 ifade sağlayın", "app.components.formBuilder.emptyTitleError": "Bir soru başlığ<PERSON> girin", "app.components.formBuilder.emptyTitleMessage": "Tüm cevaplar için bir başlık sağlayın", "app.components.formBuilder.emptyTitleStatementMessage": "<PERSON><PERSON><PERSON> ifadeler için bir baş<PERSON><PERSON>k <PERSON>ğlayın", "app.components.formBuilder.enable": "Etkinleştir", "app.components.formBuilder.errorMessage": "Bir sorun var, değişikliklerinizi kaydedebilmek için lütfen sorunu düzel<PERSON>", "app.components.formBuilder.fieldGroup.description": "Açıklama (isteğe bağlı)", "app.components.formBuilder.fieldGroup.title": "Başlık (isteğe bağlı)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "<PERSON><PERSON> and<PERSON>, bu soruların yanıtları yalnızca Input Manager'da<PERSON> dışa aktarılan excel dosyasında mevcuttur ve kullanıcılar tarafından görülemez.", "app.components.formBuilder.fieldLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.fieldLabelStatement": "Açıklamalar", "app.components.formBuilder.fileUpload": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Harita tabanlı sayfa", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Haritayı ba<PERSON><PERSON> olarak yerleştirin veya katılımcılara konuma dayalı sorular sorun.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Optimum kullanıcı deneyimi için harita tabanlı sayfalara nokta, rota veya alan soruları eklemenizi önermiyoruz.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal sayfa", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Anket haritalama özellikleri mevcut lisansınıza da<PERSON>. Daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "<PERSON><PERSON>", "app.components.formBuilder.formEnd": "Form sonu", "app.components.formBuilder.formField.cancelDeleteButtonText": "İptal", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON>, sayfayı sil", "app.components.formBuilder.formField.copyNoun": "Anlaşıldı", "app.components.formBuilder.formField.copyVerb": "Anlaşıldı", "app.components.formBuilder.formField.delete": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, on<PERSON><PERSON> ilişkili mantığı da silecektir. <PERSON><PERSON><PERSON> istediğinizden emin misiniz?", "app.components.formBuilder.formField.deleteResultsInfo": "<PERSON>u geri alınamaz", "app.components.formBuilder.goToPageInputLabel": "<PERSON><PERSON><PERSON> say<PERSON>:", "app.components.formBuilder.good": "İyi", "app.components.formBuilder.helmetTitle": "Form oluşturucu", "app.components.formBuilder.imageFileUpload": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.invalidLogicBadgeMessage": "Geçersiz mantık", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON> (isteğe bağlı)", "app.components.formBuilder.labelsTooltipContent2": "Doğrusal ölçek değerlerinden herhangi biri için isteğe bağlı etiketleri seçin.", "app.components.formBuilder.lastPage": "Bitiş", "app.components.formBuilder.layout": "D<PERSON>zen", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Gitmek istediğine emin misin?", "app.components.formBuilder.leaveBuilderText": "Kaydedilmemiş değişiklikleriniz var. Lütfen ayrılmadan önce kaydedin. Ayrılırsanız, değişikliklerinizi kaybedersiniz.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON>ç<PERSON>k <PERSON>, katılımcıların devam etmek için belirtilen sayıda yanıtı seçmesi gerekir.", "app.components.formBuilder.limitNumberAnswers": "Cevap sayısını sınırlayın", "app.components.formBuilder.linePolygonMapWarning2": "Çizgi ve çokgen çizimi erişilebilirlik standartlarını karşılamayabilir. Daha fazla bilgi {accessibilityStatement}adresinde bulunabilir.", "app.components.formBuilder.linearScale": "Doğrusal ölçek", "app.components.formBuilder.locationDescription": "<PERSON><PERSON>", "app.components.formBuilder.logic": "Mantık", "app.components.formBuilder.logicAnyOtherAnswer": "Başka bir cevap", "app.components.formBuilder.logicConflicts.conflictingLogic": "Çelişkili mantık", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Bu sayfa farklı sayfalara yönlendiren sorular içerir. Katılımcılar birden fazla soruya yanıt verirse, en uzak sayfa gösterilecektir. Bu davranışın amaçladığınız akışla uyumlu olduğundan emin olun.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Bu sayfada uygulanan birden fazla mantık kuralı vardır: çoklu seçim soru mantığı, sayfa düzeyi mantığı ve sorular arası mantık. Bu koşullar çakıştığında, soru mantığı sayfa mantığına göre öncelikli olacak ve en uzak sayfa gösterilecektir. Amaçladığınız akışla uyumlu olduğundan emin olmak için mantığı gözden geçirin.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "<PERSON><PERSON> say<PERSON>, seçeneklerin farklı sayfalara yönlendirdiği çoktan seçmeli bir soru içermektedir. Katılımcılar birden fazla seçenek belirlerse, en uzak sayfa gösterilecektir. Bu davranışın amaçladığınız akışla uyumlu olduğundan emin olun.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "<PERSON>u sayfa, seçeneklerin farklı sayfalara yönlendirdiği çoktan seçmeli bir soru içerir ve diğer sayfalara yönlendiren sorulara sahiptir. Bu koşullar çakışırsa en uzak sayfa gösterilecektir. Bu davranışın amaçladığınız akışla uyumlu olduğundan emin olun.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "<PERSON>u sayfa, seçeneklerin farklı sayfalara yönlendirdiği çoktan seçmeli bir soru içerir ve hem sayfa hem de soru düzeyinde ayarlanmış mantığa sahiptir. Soru mantığı öncelikli olacak ve en uzak sayfa gösterilecektir. Bu davranışın amaçladığınız akışla uyumlu olduğundan emin olun.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Bu sayfada hem sayfa düzeyinde hem de soru düzeyinde ayarlanmış mantık vardır. So<PERSON> mantığ<PERSON>, sayfa düzeyindeki mantığa göre öncelikli olacaktır. Bu davranışın amaçladığınız akışla uyumlu olduğundan emin olun.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Bu sayfa hem sayfa hem de soru düzeyinde mantık ayarına sahiptir ve birden fazla soru farklı sayfalara yönlendirilir. Soru mantığı öncelikli olacak ve en uzak sayfa gösterilecektir. Bu davranışın amaçladığınız akışla uyumlu olduğundan emin olun.", "app.components.formBuilder.logicNoAnswer2": "Cevaplanmadı", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Başka bir cevap varsa", "app.components.formBuilder.logicPanelNoAnswer": "Cevaplanmadıysa", "app.components.formBuilder.logicValidationError": "Mantık önceki sayfalara bağlantı veremez", "app.components.formBuilder.longAnswer": "<PERSON><PERSON><PERSON> cevap", "app.components.formBuilder.mapConfiguration": "<PERSON><PERSON>ırması", "app.components.formBuilder.mapping": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.mappingNotInCurrentLicense": "Anket haritalama özellikleri mevcut lisansınıza da<PERSON>. Daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.components.formBuilder.matrix": "<PERSON><PERSON>", "app.components.formBuilder.matrixSettings.columns": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "Çoktan seçmeli", "app.components.formBuilder.multipleChoiceHelperText": "Birden fazla seçenek farklı sayfalara yönlendiriyorsa ve katılımcılar birden fazlasını seçerse, en uzak sayfa gösterilecektir. Bu davranışın amaçladığınız akışla uyumlu olduğundan emin olun.", "app.components.formBuilder.multipleChoiceImage": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.newField": "<PERSON><PERSON> alan", "app.components.formBuilder.number": "<PERSON><PERSON>", "app.components.formBuilder.ok": "Tamam.", "app.components.formBuilder.open": "Açık", "app.components.formBuilder.optional": "Opsiyonel", "app.components.formBuilder.other": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.otherOption": "\"Diğer\" se<PERSON><PERSON>ği", "app.components.formBuilder.otherOptionTooltip": "Sağlanan yanıtlar tercihleriyle eşleşmiyorsa katılımcıların özel bir yanıt girmelerine izin verin", "app.components.formBuilder.page": "Say<PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "<PERSON>u <PERSON><PERSON> si<PERSON>.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "<PERSON>u sayfa silinemez ve herhangi bir ek alan eklenmesine izin vermez.", "app.components.formBuilder.pageRuleLabel": "<PERSON><PERSON><PERSON> say<PERSON>:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Herhangi bir mantık eklenmezse, form normal akışını izleyecektir. Hem sayfanın hem de soruların mantığı varsa, soru mantığı öncelikli olacaktır. <PERSON>unun amaçladığınız akışla uyumlu olduğundan emin olun Daha fazla bilgi için {supportPageLink}adresini ziyaret edin", "app.components.formBuilder.preview": "Önizleme:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Ortak sponsorlar indirilen PDF'de gösterilmez ve FormSync aracılığıyla içe aktarım için desteklenmez.", "app.components.formBuilder.printSupportTooltip.fileupload": "Dosya yükleme soruları indirilen PDF'de desteklenmiyor olarak gösterilir ve FormSync aracılığıyla içe aktarma için desteklenmez.", "app.components.formBuilder.printSupportTooltip.mapping": "Eşleme soruları indirilen PDF'de gösterilir, ancak katmanlar görünmez. Eşleme soruları FormSync aracılığıyla içe aktarma için desteklenmez.", "app.components.formBuilder.printSupportTooltip.matrix": "Matris soru<PERSON>ı indirilen PDF'de gösterilir ancak şu anda FormSync aracılığıyla içe aktarma için desteklenmemektedir.", "app.components.formBuilder.printSupportTooltip.page": "Sayfa başlıkları ve açıklamaları indirilen PDF'de bölüm başlığı olarak gösterilir.", "app.components.formBuilder.printSupportTooltip.ranking": "Sıralama soruları indirilen PDF'de gösterilir ancak şu anda FormSync aracılığıyla içe aktarma için desteklenmemektedir.", "app.components.formBuilder.printSupportTooltip.topics2": "Etiketler indirilen PDF'de desteklenmiyor olarak gösterilir ve FormSync aracılığıyla içe aktarma için desteklenmez.", "app.components.formBuilder.proposedBudget": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>e", "app.components.formBuilder.question": "<PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON>u soru silinemez.", "app.components.formBuilder.questionDescriptionOptional": "<PERSON><PERSON> (isteğe bağlı)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON> b<PERSON>ş<PERSON>ığı", "app.components.formBuilder.randomize": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.randomizeToolTip": "Cevap<PERSON><PERSON>n sırası her kullanıcı için rastgele belirlenecektir", "app.components.formBuilder.range": "Menzil", "app.components.formBuilder.ranking": "Sıralama", "app.components.formBuilder.rating": "Değerlendirme", "app.components.formBuilder.removeAnswer": "Cevabı kaldır", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "Bu soruyu yanıtlamayı zorunlu hale getirin", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON><PERSON><PERSON> c<PERSON>:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Cevaplar şunları içeriyorsa:", "app.components.formBuilder.save": "<PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "Ölçeğiniz için maksimum değeri seçin.", "app.components.formBuilder.sentiment": "Duyarlılık ölçeği", "app.components.formBuilder.shapefileUpload": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON><PERSON> c<PERSON>p", "app.components.formBuilder.showResponseToUsersToggleLabel": "Kullanıcılara yanıt gö<PERSON>in", "app.components.formBuilder.singleChoice": "Tek seçenek", "app.components.formBuilder.staleDataErrorMessage2": "Bir sorun oluştu. Bu giriş formu yakın zamanda başka bir yere kaydedilmiş. Bunun nedeni sizin veya başka bir kullanıcının formu başka bir tarayıcı penceresinde düzenlemeye açmış olması olabilir. Lütfen en son formu almak için sayfayı yenileyin ve ardından değişikliklerinizi tekrar yapın.", "app.components.formBuilder.stronglyAgree": "Kesinlikle katılıyorum", "app.components.formBuilder.stronglyDisagree": "Kesinlikle katılmıyorum", "app.components.formBuilder.supportArticleLinkText": "bu sayfa", "app.components.formBuilder.tags": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.title": "Başlık", "app.components.formBuilder.toLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.unsavedChanges": "Kaydedilmemiş değişiklikleriniz var", "app.components.formBuilder.useCustomButton2": "Özel sayfa düğ<PERSON><PERSON> k<PERSON>", "app.components.formBuilder.veryBad": "Çok kötü", "app.components.formBuilder.veryGood": "Çok iyi", "app.components.ideas.similarIdeas.engageHere": "<PERSON><PERSON>ya katılın", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Benzer bir başvuru bulunamadı.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Benzer boyun eğmeler bulduk - onlarla etkileşime geçmek onları güçlendirmeye yardımcı olabilir!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Benzer başvurular zaten gönderildi:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Benzer başvurular arıyoruz ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Bir günden az} one {# gün} other {# gün}} sol", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  haftalar kaldı", "app.components.screenReaderCurrency.AED": "Birleşik Arap Emirlikleri Dirhemi", "app.components.screenReaderCurrency.AFN": "Afgan <PERSON>gani", "app.components.screenReaderCurrency.ALL": "Arnavutluk Leki", "app.components.screenReaderCurrency.AMD": "Erm<PERSON>", "app.components.screenReaderCurrency.ANG": "Hollanda Antiller Gulden'i", "app.components.screenReaderCurrency.AOA": "Angola Kwanza", "app.components.screenReaderCurrency.ARS": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.AUD": "Avustralya Doları", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbaycan Manatı", "app.components.screenReaderCurrency.BAM": "Bosna-Hersek Dönüştürülebilir İşaret", "app.components.screenReaderCurrency.BBD": "Barbados Doları", "app.components.screenReaderCurrency.BDT": "Bangladeş Takası", "app.components.screenReaderCurrency.BGN": "Bulgar Lev", "app.components.screenReaderCurrency.BHD": "Bahreyn <PERSON>ı", "app.components.screenReaderCurrency.BIF": "Burundi Frangı", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BND": "Brunei Doları", "app.components.screenReaderCurrency.BOB": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BOV": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.BRL": "Brezilya Reali", "app.components.screenReaderCurrency.BSD": "Bahama <PERSON>", "app.components.screenReaderCurrency.BTN": "Bhutanese Ngultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Belarus Rublesi", "app.components.screenReaderCurrency.BZD": "Belize Doları", "app.components.screenReaderCurrency.CAD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CDF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "İsviçre Frangı", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "<PERSON><PERSON> (UF)", "app.components.screenReaderCurrency.CLP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.CNY": "<PERSON><PERSON>", "app.components.screenReaderCurrency.COP": "Kolombiya Pesosu", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Kosta Rikalı Colón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Küba Dönüştürülebilir Pesosu", "app.components.screenReaderCurrency.CUP": "Küba Pesosu", "app.components.screenReaderCurrency.CVE": "Cape Verdean Escudo", "app.components.screenReaderCurrency.CZK": "Çek Korunası", "app.components.screenReaderCurrency.DJF": "Cibuti Frangı", "app.components.screenReaderCurrency.DKK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.DOP": "<PERSON>inik <PERSON>u", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ERN": "Eritreli Nakfa", "app.components.screenReaderCurrency.ETB": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fiji Doları", "app.components.screenReaderCurrency.FKP": "Falkland Adaları Poundu", "app.components.screenReaderCurrency.GBP": "İngiliz Sterlini", "app.components.screenReaderCurrency.GEL": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Cebelitarık <PERSON>", "app.components.screenReaderCurrency.GMD": "Gambiya Dalası", "app.components.screenReaderCurrency.GNF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GTQ": "Guatemala Quetzal'ı", "app.components.screenReaderCurrency.GYD": "Guyanalı Dolar", "app.components.screenReaderCurrency.HKD": "Hong Kong Doları", "app.components.screenReaderCurrency.HNL": "Honduras Lempira", "app.components.screenReaderCurrency.HRK": "Hırvat Kunası", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IDR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ILS": "İsrail Ye<PERSON>", "app.components.screenReaderCurrency.INR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.IQD": "Irak <PERSON>", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "İzlandaca Króna", "app.components.screenReaderCurrency.JMD": "Jam<PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "Ürdün Dinarı", "app.components.screenReaderCurrency.JPY": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KES": "Kenya Şilini", "app.components.screenReaderCurrency.KGS": "Kırgızistan Som", "app.components.screenReaderCurrency.KHR": "Kamboçya Rieli", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KPW": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KRW": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.KWD": "Kuveyt Dinarı", "app.components.screenReaderCurrency.KYD": "Cayman Adaları Doları", "app.components.screenReaderCurrency.KZT": "Kazakistan Tengesi", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LKR": "Sri Lanka Rupisi", "app.components.screenReaderCurrency.LRD": "<PERSON><PERSON>a <PERSON>", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Litvanya Litası", "app.components.screenReaderCurrency.LVL": "Letonya Latı", "app.components.screenReaderCurrency.LYD": "Libya Dinarı", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "Moldova Leyi", "app.components.screenReaderCurrency.MGA": "Malgaş Ariary", "app.components.screenReaderCurrency.MKD": "Makedon Denarı", "app.components.screenReaderCurrency.MMK": "Myanmar Kyatı", "app.components.screenReaderCurrency.MNT": "Moğol Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Moritanyalı Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritius Rupisi", "app.components.screenReaderCurrency.MVR": "Maldiv Rufiyaa", "app.components.screenReaderCurrency.MWK": "<PERSON>avi <PERSON>", "app.components.screenReaderCurrency.MXN": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MXV": "Meksika Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MZN": "Mozambik Metikali", "app.components.screenReaderCurrency.NAD": "Nam<PERSON><PERSON>", "app.components.screenReaderCurrency.NGN": "Nijerya <PERSON>ı", "app.components.screenReaderCurrency.NIO": "Nikaragua Córdoba", "app.components.screenReaderCurrency.NOK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.NPR": "Nepal Rupisi", "app.components.screenReaderCurrency.NZD": "<PERSON>ni <PERSON>", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.PAB": "Panamalı Balboa", "app.components.screenReaderCurrency.PEN": "Peru Sol", "app.components.screenReaderCurrency.PGK": "Papua Yeni Gine Kina'sı", "app.components.screenReaderCurrency.PHP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.PKR": "Pakistan Rupisi", "app.components.screenReaderCurrency.PLN": "Lehçe Złoty", "app.components.screenReaderCurrency.PYG": "Paraguay Guaraní'si", "app.components.screenReaderCurrency.QAR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RSD": "Sırp Dinarı", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RWF": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SBD": "<PERSON>", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SDG": "Sudan Poundu", "app.components.screenReaderCurrency.SEK": "İsveç Kronu", "app.components.screenReaderCurrency.SGD": "Singapur Doları", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somali Şilini", "app.components.screenReaderCurrency.SRD": "Surinam <PERSON>ı", "app.components.screenReaderCurrency.SSP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.STD": "São Tomé ve Prín<PERSON>", "app.components.screenReaderCurrency.SYP": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Tayland Bahtı", "app.components.screenReaderCurrency.TJS": "Tacikistan Somoni", "app.components.screenReaderCurrency.TMT": "Türkmenistan Manatı", "app.components.screenReaderCurrency.TND": "Tunus <PERSON>", "app.components.screenReaderCurrency.TOK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "Türk Lirası", "app.components.screenReaderCurrency.TTD": "Trinidad ve Tobago Doları", "app.components.screenReaderCurrency.TWD": "<PERSON>ni <PERSON>", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrayna Grivnası", "app.components.screenReaderCurrency.UGX": "Uganda Şilini", "app.components.screenReaderCurrency.USD": "Birleşik Devletler Doları", "app.components.screenReaderCurrency.USN": "Birleşik Devletler Doları (Ertesi gün)", "app.components.screenReaderCurrency.USS": "Birleşik Devletler Doları (Aynı gün)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguay Pesosu", "app.components.screenReaderCurrency.UZS": "Özbekistan Som", "app.components.screenReaderCurrency.VEF": "Venezuela Bolívar'ı", "app.components.screenReaderCurrency.VND": "Vietnamca <PERSON>ồ<PERSON>", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoalı Tala", "app.components.screenReaderCurrency.XAF": "Orta Afrika CFA Frangı", "app.components.screenReaderCurrency.XAG": "Gümüş (bir troy ons)", "app.components.screenReaderCurrency.XAU": "Altın (bir troy ons)", "app.components.screenReaderCurrency.XBA": "Avrupa Kompozit Birimi (EURCO)", "app.components.screenReaderCurrency.XBB": "<PERSON><PERSON><PERSON><PERSON> (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "<PERSON><PERSON><PERSON><PERSON> 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "<PERSON><PERSON><PERSON><PERSON> 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.XDR": "Özel Çekme Hakları", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Batı Afrika CFA Frangı", "app.components.screenReaderCurrency.XPD": "Paladyum (bir troy ons)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "P<PERSON>in (bir troy ons)", "app.components.screenReaderCurrency.XTS": "Test amaçları için özel olarak ayrılmış kodlar", "app.components.screenReaderCurrency.XXX": "Para birimi yok", "app.components.screenReaderCurrency.YER": "Yemen Riyali", "app.components.screenReaderCurrency.ZAR": "Güney Afrika Randı", "app.components.screenReaderCurrency.ZMW": "Zambiya Kwachası", "app.components.screenReaderCurrency.amount": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.currency": "Para Birimi", "app.components.trendIndicator.lastQuarter2": "son <PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.applicability": "Bu erişilebilirlik beyanı, bu web sitesini temsil eden bir {demoPlatformLink} için geçerlidir. Aynı kaynak kodunu kullanır ve aynı şekilde çalışır.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Değerlendirme yöntemi", "app.containers.AccessibilityStatement.assesmentText2022": "Bu sitenin eriş<PERSON>i, tasarım ve geliştirme sürecine dahil olmayan harici bir kuruluş tarafından değerlendirilmiştir. Daha önce bahsedilen {demoPlatformLink} adresinin uygunluğu bu {statusPageLink} üzerinde tanımlanabilir.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "tercihlerinizi değiştirebilirsiniz", "app.containers.AccessibilityStatement.changePreferencesText": "İstedi<PERSON><PERSON><PERSON> {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Uyumluluk istisnaları", "app.containers.AccessibilityStatement.conformanceStatus": "Uyumluluk durumu", "app.containers.AccessibilityStatement.contentConformanceExceptions": "İçeriğimizi herkes için kapsayıcı hale getirmeye çalışıyoruz. <PERSON><PERSON><PERSON>, bazı durumlarda platformda, aşağıda açıklandığı gibi, erişilemeyen içerikler olabilir:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo web sitesi", "app.containers.AccessibilityStatement.email": "E-posta:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Yerleşik anket araçları", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Bu platformda kullanılabilen yerleşik anket araçları üçüncü taraf yazılımlardır ve erişilebilir olmayabilir.", "app.containers.AccessibilityStatement.exception_1": "Dijital katılım platformlarımız, bireyler ve kuruluşlar tarafından kullanıcı içerikleri yayınlanmasını kolaylaştırır. Platform kullanıcılarının dosya eki olarak platforma PDF'ler, görseller veya çoklu ortam dosyaları gibi dosya türleri yüklemeleri veya bunları metin alanlarına girmeleri mümkündür. Bu belgeler için erişilebilirlik özellikleri eksiksiz olmayabilir.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Bu sitenin erişilebilirliği hakkındaki görüşlerinizi bekliyoruz. Lütfen aşağıdaki yöntemlerden birini kullanarak bizimle iletişime geçin:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "<PERSON><PERSON> bi<PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brüksel, Belçika", "app.containers.AccessibilityStatement.headTitle": "Erişilebilirlik Beyanı | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink}, kullandıkları teknoloji veya fiziksel/mental yetenekleri ne olursa olsun, tüm kullanıcılar için erişilebilir bir platform sağlamaya kendini adar. Platformlarımızın tüm kullanıcılar için erişilebilirlik ve kullanılabilirlik özelliklerini en üst düzeye çıkarmak için devam eden çabalarımızda mevcut alakalı erişilebilirlik standartları takip edilir.", "app.containers.AccessibilityStatement.mapping": "<PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.mapping_1": "Platformdaki haritalar erişilebilirlik standartlarını kısmen karşılamaktadır. <PERSON><PERSON>, yakınlaştırma ve kullanıcı arayüzü widget'ları haritalar görüntülenirken klavye kullanılarak kontrol edilebilir. Yöneticiler ayrıca daha erişilebilir renk paletleri ve semboloji oluşturmak için arka ofiste veya Esri entegrasyonunu kullanarak harita katmanlarının stilini yapılandırabilir. Farklı çizgi veya poligon stilleri (örneğin kesikli çizgiler) kullanmak da mümkün olan her yerde harita katmanlarını ayırt etmeye yardımcı olacaktır ve bu stil şu anda platformumuzda yapılandırılamasa da, Esri entegrasyonu ile haritalar kullanılıyorsa yapılandırılabilir.", "app.containers.AccessibilityStatement.mapping_2": "<PERSON><PERSON><PERSON> harita<PERSON>, ekran okuyucu kullanan kullanıcılara temel haritaları, harita katmanlarını veya verilerdeki eğilimleri sesli olarak sunmadıkları için tam olarak erişilebilir değildir. Tam erişilebilir haritaların harita katmanlarını sesli olarak sunması ve verilerdeki ilgili eğilimleri açıklaması gerekir. Ayrıca, anketlerde çizgi ve çokgen harita çizimi, şekiller klavye kullanılarak çizilemediğinden erişilebilir değildir. Teknik karmaşıklık nedeniyle alternatif giriş yöntemleri şu anda mevcut değildir.", "app.containers.AccessibilityStatement.mapping_3": "Çizgi ve poligon harita çizimlerini daha erişilebilir kılmak için anket sorusuna veya sayfa açıklamasına haritanın neyi gösterdiğine ve ilgili eğilimlere dair bir giriş veya açıklama eklenmesini öneriyoruz. Ayrıca, kısa veya uzun cevaplı bir metin sorusu da eklenebilir, bö<PERSON>ce katılımcılar gerektiğinde (haritaya tıklamak yerine) cevaplarını basit terimlerle açıklayabilirler. Ayrıca, bir harita sorusunu dolduramayan katılımcıların soruyu yanıtlamak için alternatif bir yöntem talep edebilmeleri için proje yöneticisinin iletişim bilgilerinin de eklenmesini öneriyoruz (Örn. Video toplantısı).", "app.containers.AccessibilityStatement.mapping_4": "Fikir projeleri ve tekli<PERSON><PERSON>, girdi<PERSON>i bir harita görünümünde görüntüleme seçeneği vardır, ancak buna erişilemez. Ancak, bu yöntemler için girdilerin erişilebilir olan alternatif bir liste görünümü mevcuttur.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Çevrimiçi atölye çalışmalarımız, şu an için alt yazıları desteklemeyen bir canlı video akışı bileşenine sahiptir.", "app.containers.AccessibilityStatement.pageDescription": "Bu web sitesinin erişilebilirlik durumu beyanı", "app.containers.AccessibilityStatement.postalAddress": "Posta adresi:", "app.containers.AccessibilityStatement.publicationDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "app.containers.AccessibilityStatement.publicationDate2024": "Bu erişilebilirlik beyanı 21 Ağustos 2024 tarihinde yayımlanmıştır.", "app.containers.AccessibilityStatement.responsiveness": "Geri bildirimlere 1-2 iş günü içinde yanıt vermeyi hedefliyoruz.", "app.containers.AccessibilityStatement.statusPageText": "durum sayfası", "app.containers.AccessibilityStatement.technologiesIntro": "Bu sitede erişilebilirlik özelliklerinin çalışması için şu teknolojilere ihtiyaç vardır:", "app.containers.AccessibilityStatement.technologiesTitle": "Tek<PERSON>lojiler", "app.containers.AccessibilityStatement.title": "Erişilebilirlik Beyanı", "app.containers.AccessibilityStatement.userGeneratedContent": "Kullanıcılar tarafından oluşturulan içerikler", "app.containers.AccessibilityStatement.workshops": "<PERSON><PERSON><PERSON>eler", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "<PERSON><PERSON>", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "İçerik Oluşturucuyu kullanmak daha gelişmiş düzen seçeneklerini kullanmanızı sağlayacaktır. İçerik oluşturucuda içerik bulunmayan diller için bunun yerine normal proje açıklaması içeriği görüntülenecektir.", "app.containers.AdminPage.ProjectDescription.linkText": "Content Builder'da açıklamayı düzenleme", "app.containers.AdminPage.ProjectDescription.saveError": "Proje açıklamasını kaydederken bir şeyler ters gitti.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Açıklama için Content Builder'ı kullanın", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "İçerik Oluşturucuyu kullanmak daha gelişmiş düzen seçeneklerini kullanmanızı sağlar.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON><PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON><PERSON> sonu", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Akıllı grup oluşturun", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Aşağıdaki koşulların tümünü karşılayan kullanıcılar otomatik olarak gruba eklenir:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "En az bir kural girin", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Bazı koşullar eksik", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Akıllı grupları yapılandırmak mevcut lisansınızın bir parçası değildir. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Grup adı girin", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Platformunuz için doğrulama etkin değil. Doğrulama kuralını kaldırın veya destek ekibiyle iletişime geçin.", "app.containers.App.appMetaDescription": "{orgName} çevrimiçi katılım platformuna hoş geldiniz. \n<PERSON>rel projeleri inceleyin ve tartışmaya katılın.", "app.containers.App.loading": "Yükleniyor...", "app.containers.App.metaTitle1": "Vatandaş katılım platformu | {orgName}", "app.containers.App.skipLinkText": "Ana içeriğe geç", "app.containers.AreaTerms.areaTerm": "alan", "app.containers.AreaTerms.areasTerm": "<PERSON><PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Bu e-posta adresine sahip bir hesap zaten mevcut. <PERSON><PERSON><PERSON><PERSON> kapa<PERSON>, bu e-posta adresiyle giriş yapabilir ve ayarlar sayfasında hesabınızı doğrulayabilirsiniz.", "app.containers.Authentication.steps.AccessDenied.close": "Ka<PERSON><PERSON>", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Bu sürece katılmak için gereken şartları karşılamıyorsunuz.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Tek oturum açma doğrulamasına geri dö<PERSON>ün", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Lütfen bir belirteç girin", "app.containers.Authentication.steps.Invitation.token": "<PERSON><PERSON>", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Zaten bir hesabınız var mı? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "<PERSON><PERSON><PERSON> yap", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Bu kategorideki e-postalar", "app.containers.CampaignsConsentForm.messageError": "E-posta tercihleriniz kaydedilirken bir hata oluş<PERSON>.", "app.containers.CampaignsConsentForm.messageSuccess": "E-posta tercihleriniz ka<PERSON>ildi.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Hangi tür e-posta bildirimleri almak istiyorsunuz? ", "app.containers.CampaignsConsentForm.notificationsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "<PERSON><PERSON>", "app.containers.ChangeEmail.confirmationModalTitle": "E-postanızı onaylayın", "app.containers.ChangeEmail.emailEmptyError": "Bir e-posta adresi <PERSON>ın", "app.containers.ChangeEmail.emailInvalidError": "Doğru formatta bir e-posta adresi girin, <PERSON>rne<PERSON>in <EMAIL>", "app.containers.ChangeEmail.emailRequired": "Lütfen bir e-posta adresi girin.", "app.containers.ChangeEmail.emailTaken": "Bu e-posta zaten kullanılıyor.", "app.containers.ChangeEmail.emailUpdateCancelled": "E-posta güncellemesi iptal edildi.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "E-postanızı güncellemek için lütfen işlemi yeniden başlatın.", "app.containers.ChangeEmail.helmetDescription": "E-posta sayfanızı değiştirin", "app.containers.ChangeEmail.helmetTitle": "E-posta adresinizi değiştirin", "app.containers.ChangeEmail.newEmailLabel": "Yeni e-posta", "app.containers.ChangeEmail.submitButton": "<PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.titleAddEmail": "E-posta adresinizi ekleyin", "app.containers.ChangeEmail.titleChangeEmail": "E-posta adresinizi değiştirin", "app.containers.ChangeEmail.updateSuccessful": "E-postanız başarıyla güncellendi.", "app.containers.ChangePassword.currentPasswordLabel": "Geçerli <PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "Geçerli şifrenizi girin", "app.containers.ChangePassword.goHome": "Eve git", "app.containers.ChangePassword.helmetDescription": "Şifrenizi değiştirin sayfası", "app.containers.ChangePassword.helmetTitle": "Şifrenizi değiştirin", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON>", "app.containers.ChangePassword.newPasswordRequired": "<PERSON><PERSON> girin", "app.containers.ChangePassword.password.minimumPasswordLengthError": "En az {minimumPasswordLength} karakter uzunluğunda bir parola girin", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Parolanız başarıyla güncellendi", "app.containers.ChangePassword.passwordEmptyError": "Şifrenizi girin", "app.containers.ChangePassword.passwordsDontMatch": "<PERSON><PERSON>", "app.containers.ChangePassword.titleAddPassword": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.titleChangePassword": "Şifrenizi değiştirin", "app.containers.Comments.a11y_commentDeleted": "<PERSON><PERSON><PERSON> yorum", "app.containers.Comments.a11y_commentPosted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yorum", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {beğeni yok} one {1 beğeni} other {# beğeni}}", "app.containers.Comments.a11y_undoLike": "Gibi geri al", "app.containers.Comments.addCommentError": "<PERSON>ir sorun olu<PERSON>. Lütfen daha sonra tekrar deneyin.", "app.containers.Comments.adminCommentDeletionCancelButton": "İptal", "app.containers.Comments.adminCommentDeletionConfirmButton": "<PERSON>u yorumu sil", "app.containers.Comments.cancelCommentEdit": "İptal", "app.containers.Comments.childCommentBodyPlaceholder": "Bir yanıt yazın...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON> al", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON>u yorum silindi.", "app.containers.Comments.commentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON> sakla", "app.containers.Comments.commentDeletionConfirmButton": "<PERSON><PERSON><PERSON><PERSON> sil", "app.containers.Comments.commentLike": "Gibi", "app.containers.Comments.commentReplyButton": "Yanıtla", "app.containers.Comments.commentsSortTitle": "Yorumları sıralama ölçütü", "app.containers.Comments.completeProfileLinkText": "profi<PERSON><PERSON> ta<PERSON><PERSON>n", "app.containers.Comments.completeProfileToComment": "<PERSON><PERSON> ya<PERSON> i<PERSON> l<PERSON> {completeRegistrationLink} ad<PERSON>ini ziyaret edin.", "app.containers.Comments.confirmCommentDeletion": "Bu yorumu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "app.containers.Comments.deleteComment": "Sil", "app.containers.Comments.deleteReasonDescriptionError": "Nedeniniz hakkında daha fazla bilgi verin", "app.containers.Comments.deleteReasonError": "<PERSON>ir neden belirtin", "app.containers.Comments.deleteReason_inappropriate": "Uygunsuz veya küçük düşürücü", "app.containers.Comments.deleteReason_irrelevant": "Alakasız", "app.containers.Comments.deleteReason_other": "Başka neden", "app.containers.Comments.editComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "topluluk yönergelerimiz", "app.containers.Comments.ideaCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> buraya yazın", "app.containers.Comments.internalCommentingNudgeMessage": "<PERSON><PERSON>i yo<PERSON>lar yapmak mevcut lisansınıza dahil <PERSON>. Bu konuda daha fazla bilgi edinmek için GovSuccess Yöneticinize ulaşın.", "app.containers.Comments.internalConversation": "<PERSON><PERSON><PERSON>", "app.containers.Comments.loadMoreComments": "<PERSON><PERSON> fazla yorum yükle", "app.containers.Comments.loadingComments": "<PERSON><PERSON><PERSON> yükleniyor...", "app.containers.Comments.loadingMoreComments": "<PERSON>ha fazla yorum yükleniyor...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Bu yorum normal kullanıcılar tarafından görülemez", "app.containers.Comments.postInternalComment": "<PERSON><PERSON><PERSON> yorum g<PERSON>", "app.containers.Comments.postPublicComment": "<PERSON><PERSON>ya açık yorum gönderin", "app.containers.Comments.profanityError": "Gönderinizde {guidelinesLink} ile uyumlu olmayan dil unsurlarına rastladık. Bu platformun herkes için güvenli olması için çaba gösteriyoruz. Lütfen girdiğiniz metni düzenleyip tekrar deneyin.", "app.containers.Comments.publicDiscussion": "Kamuya açık tartışma", "app.containers.Comments.publishComment": "<PERSON><PERSON><PERSON><PERSON><PERSON> yayın<PERSON>ın", "app.containers.Comments.reportAsSpamModalTitle": "<PERSON>unu neden spam olarak bildirmek istiyorsunuz?", "app.containers.Comments.saveComment": "<PERSON><PERSON>", "app.containers.Comments.signInLinkText": "oturum açın", "app.containers.Comments.signInToComment": "<PERSON><PERSON> ya<PERSON> i<PERSON> {signInLink}.", "app.containers.Comments.signUpLinkText": "kaydolun", "app.containers.Comments.verifyIdentityLinkText": "Kimliğinizi doğrulayın", "app.containers.Comments.visibleToUsersPlaceholder": "Bu yorum normal kullanıcılar tarafından görülebilir", "app.containers.Comments.visibleToUsersWarning": "Burada yayınlanan yorumlar normal kullanıcılar tarafından görülebilecektir.", "app.containers.ContentBuilder.PageTitle": "<PERSON>je <PERSON>", "app.containers.CookiePolicy.advertisingContent": "<PERSON><PERSON><PERSON>, bu platformla etkileşimi kişiselleştirmek ve harici pazarlama kampanyalarının etkileşim üzerindeki etkinliğini ölçmek için kullanılabilir. Bu platformda herhangi bir reklam göstermeyiz ancak size, ziyaret ettiğiniz sayfalara göre kişiselleştirilmiş reklamlar sunulabilir.", "app.containers.CookiePolicy.advertisingTitle": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "<PERSON><PERSON><PERSON>, hangi sayfaların ne kadar süreyle ziyaret edildiği gibi ziyaretçi davranışlarının kaydını tutar. Ayrıca tarayıcı bilgileri, yaklaşık konum ve IP adresleri gibi bazı teknik veriler de toplayabilir. Bu verileri yalnızca platformun genel kullanıcı deneyimini ve işleyişini geliştirmeye devam etmek için şirket içinde kullanırız. Bu tür veriler, platformdaki projelere katılımı değerlendirmek ve geliştirmek için Go Vocal ve {orgName} arasında da paylaşılabilir. Veriler anonim olarak ve toplu halde kullanılır, sizi kişisel olarak tanımlamaz. Bununla birlikte, bu verilerin diğer veri kaynaklarıyla birleştirilmesi durumunda, bu tür bir tanımlamanın ortaya çıkması mümkündür.", "app.containers.CookiePolicy.analyticsTitle": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.cookiePolicyDescription": "Bu platformda çerezleri nasıl kullandığımıza dair ayrıntılı bir açıklama", "app.containers.CookiePolicy.cookiePolicyTitle": "Çerez politikası", "app.containers.CookiePolicy.essentialContent": "<PERSON><PERSON><PERSON>erezle<PERSON>, bu platformun düzgün çalışmasını sağlamak için gereklidir. Bu temel çerezler, <PERSON><PERSON><PERSON><PERSON> o<PERSON>, platformu ziyaret ettiğinizde hesabınızın kimliğini doğrulamak ve tercih ettiğiniz dili kaydetmek için kullanılır.", "app.containers.CookiePolicy.essentialTitle": "<PERSON><PERSON>", "app.containers.CookiePolicy.externalContent": "Bazı sayfalarımızda YouTube veya Typeform gibi harici sağlayıcıların içerikleri görüntülenebilir. Bu üçüncü taraf çerezleri üzerinde kontrolümüz yoktur ve bu harici sağlayıcılardan gelen içeriği görüntülemek de cihazınıza çerezler yüklenmesine neden olabilir.", "app.containers.CookiePolicy.externalTitle": "<PERSON><PERSON>", "app.containers.CookiePolicy.functionalContents": "Ziyaretçilerin yenilikler hakkında bildirim alması ve destek kanallarına doğrudan platform içinden erişmesi için işlevsel çerezler kullanılabilir.", "app.containers.CookiePolicy.functionalTitle": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.headCookiePolicyTitle": "Çerez Politikası | {orgName}", "app.containers.CookiePolicy.intro": "<PERSON><PERSON><PERSON><PERSON>, bir web sitesini ziyaret ettiğinizde tarayıcıda veya bilgisayarınızın ya da mobil cihazınızın sabit diskinde depolanan ve daha sonraki ziyaretlerde web sitesi tarafından başvurulabilen metin dosyalarıdır. <PERSON><PERSON><PERSON><PERSON>, ziyaretçilerin bu platformu nasıl kullandığını anlamak, platform tasarımını ve deneyimini geliştirmek, tercihlerinizi hatırlamak (tercih ettiğiniz dil gibi), kayıtlı kullanıcılar ve platform yöneticileri için temel işlevleri desteklemek için kullanırız.", "app.containers.CookiePolicy.manageCookiesDescription": "<PERSON><PERSON><PERSON>, pazar<PERSON><PERSON> ve işlev çerezlerini çerez tercihlerinizden istediğiniz zaman etkinleştirebilir veya devre dışı bırakabilirsiniz. Ayrıca internet tarayıcınız üzerinden mevcut çerezleri manuel veya otomatik olarak silebilirsiniz. An<PERSON>k, bu platforma daha sonra yapacağınız ziyaretlerde izin verirseniz çerezler tekrar yerleştirilebilir. Çerezleri silmezseniz çerez tercihleriniz 60 gün boyunca saklanır, ardından tekrar izniniz istenir.", "app.containers.CookiePolicy.manageCookiesPreferences": "Bu platformda kullanılan üçüncü taraf entegrasyonlarının tam listesini görmek ve tercihlerinizi yönetmek için {manageCookiesPreferencesButtonText} gidin.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "çerez ayarlarınıza", "app.containers.CookiePolicy.manageCookiesTitle": "Çerezlerinizi yönetme", "app.containers.CookiePolicy.viewPreferencesButtonText": "Çerez Ayarları", "app.containers.CookiePolicy.viewPreferencesText": "Aşağıdaki çerez kategorileri tüm ziyaretçiler veya platformlar için geçerli olmayabilir. Sizin için geçerli olan üçüncü taraf entegrasyonlarının tam listesi için: {viewPreferencesButton}", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Çerezleri ne iç<PERSON> k<PERSON>anıyoruz?", "app.containers.CustomPageShow.editPage": "Sayfayı düzenle", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON> gidin", "app.containers.CustomPageShow.notFound": "Sayfa bulunamadı", "app.containers.DisabledAccount.bottomText": "{date}adresinden tekrar oturum açabilirsiniz.", "app.containers.DisabledAccount.termsAndConditions": "şartlar & koşullar", "app.containers.DisabledAccount.text2": "{orgName} katılım platformundaki hesabınız, topluluk kurallarının ihlali nedeniyle geçici olarak devre dışı bırakılmıştır. Bu konuda daha fazla bilgi için {TermsAndConditions}adresine başvurabilirsiniz.", "app.containers.DisabledAccount.title": "Hesabınız geçici olarak devre dışı bırakıldı", "app.containers.EventsShow.addToCalendar": "<PERSON><PERSON><PERSON><PERSON> ekle", "app.containers.EventsShow.editEvent": "Etkinliği düzenle", "app.containers.EventsShow.emailSharingBody2": "<PERSON><PERSON> et<PERSON>liğe katılın: {eventTitle}. Daha fazla bilgi için: {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "<PERSON><PERSON><PERSON><PERSON> tarihi ve saati", "app.containers.EventsShow.eventFrom2": "\"{projectTitle}\" adresinden", "app.containers.EventsShow.goBack": "<PERSON><PERSON>", "app.containers.EventsShow.goToProject": "Projeye git", "app.containers.EventsShow.haveRegistered": "kayıt yaptı<PERSON>", "app.containers.EventsShow.icsError": "ICS dosyası indirilirken hata oluştu", "app.containers.EventsShow.linkToOnlineEvent": "Çevrimiçi etkinlik bağlantısı", "app.containers.EventsShow.locationIconAltText": "<PERSON><PERSON>", "app.containers.EventsShow.metaTitle": "Etkinlik: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Çevrimiçi toplantı", "app.containers.EventsShow.onlineLinkIconAltText": "Çevrimiçi toplantı bağlantısı", "app.containers.EventsShow.registered": "kayıtlı", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 kayıtlı kişi} one {1 kayıtlı kişi} other {# kayıtlı kişi}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} kayıt yapt<PERSON>", "app.containers.EventsShow.registrantsIconAltText": "<PERSON><PERSON><PERSON>", "app.containers.EventsShow.socialMediaSharingMessage": "<PERSON><PERSON> etkinliğe katılın: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# katılımcı} other {# katılımcılar}}", "app.containers.EventsViewer.allTime": "Her zaman", "app.containers.EventsViewer.date": "<PERSON><PERSON><PERSON>", "app.containers.EventsViewer.thisMonth2": "Yaklaşan ay", "app.containers.EventsViewer.thisWeek2": "<PERSON><PERSON><PERSON><PERSON> hafta", "app.containers.EventsViewer.today": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "Bir katkı ekleyin", "app.containers.IdeaButton.addAPetition": "Dilekçe ekleyin", "app.containers.IdeaButton.addAProject": "<PERSON>ir proje ekleyin", "app.containers.IdeaButton.addAProposal": "<PERSON><PERSON> tekli<PERSON> e<PERSON>", "app.containers.IdeaButton.addAQuestion": "<PERSON>ir soru e<PERSON>in", "app.containers.IdeaButton.addAnInitiative": "<PERSON><PERSON> g<PERSON><PERSON>", "app.containers.IdeaButton.addAnOption": "Bir seçenek ekleyin", "app.containers.IdeaButton.postingDisabled": "<PERSON><PERSON> an için yeni gönderim kabul edilmiyor", "app.containers.IdeaButton.postingInNonActivePhases": "<PERSON><PERSON> g<PERSON>ler sadece aktif aşamalarda eklenebilir.", "app.containers.IdeaButton.postingInactive": "<PERSON><PERSON> an için yeni gönderim kabul edilmiyor.", "app.containers.IdeaButton.postingLimitedMaxReached": "Bu anketi zaten tamamladınız. Yanıtınız için teşekkürler!", "app.containers.IdeaButton.postingNoPermission": "<PERSON><PERSON> an için yeni gönderim kabul edilmiyor", "app.containers.IdeaButton.postingNotYetPossible": "Hen<PERSON>z yeni gönderim kabul edilmiyor.", "app.containers.IdeaButton.signInLinkText": "oturum açın", "app.containers.IdeaButton.signUpLinkText": "kaydolun", "app.containers.IdeaButton.submitAnIssue": "<PERSON><PERSON>", "app.containers.IdeaButton.submitYourIdea": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.takeTheSurvey": "An<PERSON><PERSON> katılın", "app.containers.IdeaButton.verificationLinkText": "Kimliğinizi şimdi doğrulayın.", "app.containers.IdeaCard.readMore": "Daha fazla bilgi edinin", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {yorum yok} one {1 yorum} other {# yorum}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {oy yok} one {1 oy} other {# oy}} {votingThreshold}dı<PERSON>ında", "app.containers.IdeaCards.a11y_closeFilterPanel": "Filtreler panelini kapatın", "app.containers.IdeaCards.a11y_totalItems": "Toplam yayın sayısı: {ideasCount}", "app.containers.IdeaCards.all": "Tümü", "app.containers.IdeaCards.allStatuses": "<PERSON><PERSON><PERSON> du<PERSON>", "app.containers.IdeaCards.contributions": "Katkılar", "app.containers.IdeaCards.ideaTerm": "Fikirler", "app.containers.IdeaCards.initiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.list": "Liste", "app.containers.IdeaCards.map": "<PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "En çok tartışılanlar", "app.containers.IdeaCards.newest": "En yeni", "app.containers.IdeaCards.noFilteredResults": "Sonuç bulunamadı. Lütfen farklı bir filtre veya arama terimi deneyin.", "app.containers.IdeaCards.numberResults": "Sonuçlar ({postCount})", "app.containers.IdeaCards.oldest": "En eski", "app.containers.IdeaCards.optionTerm": "Seçenekler", "app.containers.IdeaCards.petitions": "Dilekçeler", "app.containers.IdeaCards.popular": "En çok oylanan", "app.containers.IdeaCards.projectFilterTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.projectTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "Filtreleri sıfırla", "app.containers.IdeaCards.showXResults": "{ideasCount, plural, one {# sonuç} other {# sonuç}} g<PERSON><PERSON>", "app.containers.IdeaCards.sortTitle": "Sıralama", "app.containers.IdeaCards.statusTitle": "Durum", "app.containers.IdeaCards.statusesTitle": "Durum", "app.containers.IdeaCards.topics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.topicsTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.trending": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.tryDifferentFilters": "Sonuç bulunamadı. Lütfen farklı bir filtre veya arama terimi deneyin.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} yorumlar} one {{ideasCount} yorum} other {{ideasCount} yorumlar}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} katkı<PERSON>} one {{ideasCount} katkılar} other {{ideasCount} katkılar}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} fikirler} one {{ideasCount} fikir} other {{ideasCount} fikirler}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} giri<PERSON><PERSON><PERSON>} one {{ideasCount} giri<PERSON><PERSON>} other {{ideasCount} giri<PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} options} one {{ideasCount} option} other {{ideasCount} options}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} dilekçeler} one {{ideasCount} dilekçe} other {{ideasCount} dilekçeler}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projeler} one {{ideasCount} proje} other {{ideasCount} projeler}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} teklifler} one {{ideasCount} teklif} other {{ideasCount} teklifler}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} sorular} one {{ideasCount} soru} other {{ideasCount} sorular}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# sonuç} other {# sonuç}}", "app.containers.IdeasEditPage.contributionFormTitle": "Katkıyı düzenleyin", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "Bir veya daha fazla dosya yüklenemedi. Lütfen dosya boyutunu ve biçimini kontrol edip tekrar deneyin.", "app.containers.IdeasEditPage.formTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Yayınınızı düzenleyin. Yeni bilgiler ekleyin ve eski bilgileri değiştirin.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Düzenleyin: {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.issueFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.optionFormTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.petitionFormTitle": "Dilekçeyi düzenle", "app.containers.IdeasEditPage.projectFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.save": "<PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "Form gönderilirken bir sorun oluştu. Lütfen olası hataları düzeltip tekrar deneyin.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "<PERSON><PERSON><PERSON> gir<PERSON> g<PERSON>", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "{orgName} katılım platformunda yayınlanan tüm girdileri inceleyin.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Mesajlar | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Ya<PERSON><PERSON>nlar", "app.containers.IdeasIndexPage.loadMore": "<PERSON><PERSON> fazla y<PERSON>...", "app.containers.IdeasIndexPage.loading": "Yükleniyor...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Bu seçeneği seçmediğiniz sürece varsayılan olarak gönderimleriniz profilinizle ilişkilendirilecektir.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "İsimsiz o<PERSON>", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "<PERSON>il <PERSON>", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Bu anket şu anda yanıtlara açık değildir. Daha fazla bilgi için lütfen projeye geri dönün.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "<PERSON>u anket şu anda aktif <PERSON>.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "<PERSON><PERSON><PERSON> geri dön", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Bu anketi zaten tamamladınız.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Anket gönderildi", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Yanıtınız için teşekkürler!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Katkı açıklaması {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "<PERSON><PERSON><PERSON> metin g<PERSON> {limit} karakterden uzun olmalıdır", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Katkı ba<PERSON><PERSON><PERSON>ğ<PERSON> {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Katkı ba<PERSON>lığı {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Lütfen en az bir eş sponsor seçin", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Fikir açıklaması {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Fikir açıklaması {limit} karakterden uzun olmalıdır", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Lütfen bir açıklama girin", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "<PERSON><PERSON>r başlığı {limit} karakter uzunluğundan az olmalıdır", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "<PERSON><PERSON>r ba<PERSON>lığı {limit} karakterden uzun olmalıdır", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ı<PERSON>ı {limit} karakter uzunluğundan az olmalıdır", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> {limit} karakter uzunluğundan az olmalıdır", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "<PERSON><PERSON> {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "<PERSON><PERSON> {limit} karakterden uzun olmalıdır", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "<PERSON><PERSON> b<PERSON> {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "<PERSON><PERSON> ba<PERSON>ığı {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON><PERSON> <PERSON><PERSON>, lüt<PERSON> geçerli bir numara girin", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Seçenek açıklaması {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Seçenek açıklaması {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Seçenek başlığı {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Seçenek başlığı {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Lütfen en az bir etiket seçin", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Dilekçe açıklaması {limit} karakter uzunluğundan az olmalıdır", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Dilekçe açıklaması {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Dilekçe başlığı {limit} karakter uzunluğundan az olmalıdır", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Dilekçe başlığı en fazla {limit} karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Proje a<PERSON>ı<PERSON>ı {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Proje a<PERSON>ı<PERSON>ması {limit} karakterden uzun olmalıdır", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "<PERSON><PERSON> b<PERSON> {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "<PERSON><PERSON> b<PERSON> {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> {limit} karakter uzunluğundan az olmalıdır", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON> {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> {limit} karakter uzunluğundan az olmalıdır", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Lütfen bir sayı girin", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Lütfen bir sayı girin", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "<PERSON><PERSON> a<PERSON>ı<PERSON>ması {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "<PERSON><PERSON> a<PERSON>ı {limit} karakterden uzun olmalıdır", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "<PERSON><PERSON> ba<PERSON>ı {limit} karakterden kısa olmalıdır", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "<PERSON><PERSON> b<PERSON>ı {limit} karakter uzunluğundan fazla olmalıdır", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Lütfen bir başlık girin", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Katkı açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Katkı açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Katkı başlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Katkı başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Fikir açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Fikir açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Lütfen bir başlık girin", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Fikir başlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Fikir başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_includes_banned_words": "{guidelinesLink} uyarınca kü<PERSON>ür olarak kabul edilen bir veya daha fazla kelime kullanmış olabilirsiniz. Lütfen metninizde bulunabilecek küfür niteliğindeki kelimeleri kaldırın.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "G<PERSON><PERSON>im açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Girişim açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "<PERSON><PERSON><PERSON><PERSON> başlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "<PERSON><PERSON><PERSON><PERSON> başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "<PERSON><PERSON> açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "<PERSON>run açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "<PERSON><PERSON> b<PERSON>şlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "<PERSON>run başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Seçenek açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Seçenek açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Seçenek başlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Seçenek başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Dilekçe açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Dilekçe açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Dilekçe başlığı en az 80 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Dilekçe başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Proje açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Proje açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "<PERSON>je başlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Proje başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Teklif açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Teklif açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Te<PERSON><PERSON>f başlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Teklif başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Lütfen bir açıklama girin", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Soru açıklaması 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Soru açıklaması en az 30 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Soru başlığı 80 karakterden kısa olmalıdır", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Soru başlığı en az 10 karakter uzunluğunda olmalıdır", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "İptal", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, gitmek istiyorum.", "app.containers.IdeasNewPage.contributionMetaTitle1": "Projeye yeni katkı ekleyin | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Bir gönderi yayınlayın ve {orgName} katılım platformundaki sohbete katılın.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Projeye yeni fi<PERSON>r <PERSON> | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Projeye yeni g<PERSON> | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Projeye yeni konu e<PERSON> | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Gitmek istediğine emin misin?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Taslak cevaplarınız özel olarak kaydedilmiştir ve bunu tamamlamak için daha sonra geri dönebilirsiniz.", "app.containers.IdeasNewPage.leaveSurvey": "Anketten ayrılın", "app.containers.IdeasNewPage.leaveSurveyText": "Cevaplarınız kaydedilmeyecek.", "app.containers.IdeasNewPage.optionMetaTitle1": "Projeye yeni se<PERSON> ekle | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Projeye yeni di<PERSON>kç<PERSON> ekleyin | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Projeye yeni proje ekle | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Projeye yeni tek<PERSON> e<PERSON> | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Projeye yeni soru e<PERSON>in | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "<PERSON><PERSON> et", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Eş sponsorluk daveti", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Eş sponsorlar", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Ortak sponsor olmaya davet edildiniz.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "<PERSON><PERSON> kabul edildi", "app.containers.IdeasShow.Cosponsorship.pending": "be<PERSON><PERSON>e", "app.containers.IdeasShow.MetaInformation.attachments": "Ekler", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} tara<PERSON><PERSON>ndan, {date} tarihinde", "app.containers.IdeasShow.MetaInformation.currentStatus": "<PERSON><PERSON> andaki durum", "app.containers.IdeasShow.MetaInformation.location": "<PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.postedBy": "Yayınlayan", "app.containers.IdeasShow.MetaInformation.similar": "<PERSON><PERSON> gir<PERSON>", "app.containers.IdeasShow.MetaInformation.topics": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.commentCTA": "Bir yorum ekleyin", "app.containers.IdeasShow.contributionEmailSharingBody": "{postUrl} üzerinden '{postTitle}' adlı bu katkıya destek olun!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Bu katkıya destek olun: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Katkınızı gönderdiğiniz için teşekkürler!", "app.containers.IdeasShow.contributionTwitterMessage": "Bu katkıya destek olun: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Bu katkıya destek olun: {postTitle}", "app.containers.IdeasShow.currentStatus": "<PERSON><PERSON> andaki durum", "app.containers.IdeasShow.deletedUser": "bilin<PERSON>yen yazar", "app.containers.IdeasShow.ideaEmailSharingBody": "{ideaUrl} üzerinden '{ideaTitle}' fikrime destek olun!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Fikrime destek olun: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "<PERSON>u fikre destek olun: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "<PERSON>u fikre destek olun: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "<PERSON>u yoruma destek olun: {postTitle}", "app.containers.IdeasShow.imported": "İthal", "app.containers.IdeasShow.importedTooltip": "Bu {inputTerm} çevrimdışı olarak toplanmış ve otomatik olarak platforma yüklenmiştir.", "app.containers.IdeasShow.initiativeEmailSharingBody": "<PERSON>u giri<PERSON><PERSON>i desteklemek için '{ideaTitle}' {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Girişiminizi gönderdiğiniz için teşekkür ederiz!", "app.containers.IdeasShow.initiativeTwitterMessage": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "{postUrl} üzerinden '{postTitle}' adlı bu yoruma destek olun!", "app.containers.IdeasShow.issueEmailSharingSubject": "<PERSON>u yoruma destek olun: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Yorumunuzu gönderdiğiniz için teşekkürler!", "app.containers.IdeasShow.issueTwitterMessage": "<PERSON>u yoruma destek olun: {postTitle}", "app.containers.IdeasShow.metaTitle": "Giriş: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "{postUrl} üzerinden '{postTitle}' adlı bu seçeneğe destek olun!", "app.containers.IdeasShow.optionEmailSharingSubject": "<PERSON><PERSON> seçeneğe destek olun: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Seçeneğiniz başarıyla yayınlandı!", "app.containers.IdeasShow.optionTwitterMessage": "<PERSON><PERSON> seçeneğe destek olun: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "<PERSON><PERSON> seçeneğe destek olun: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "{ideaUrl}adresindeki '{ideaTitle}' imza kampanyasını destekleyin!", "app.containers.IdeasShow.petitionEmailSharingSubject": "<PERSON>u <PERSON>lek<PERSON><PERSON><PERSON> deste<PERSON>in: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Dilekçenizi gönderdiğiniz için teşekkür ederiz!", "app.containers.IdeasShow.petitionTwitterMessage": "<PERSON>u dilek<PERSON><PERSON><PERSON> deste<PERSON>in: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "<PERSON>u dilek<PERSON><PERSON><PERSON> deste<PERSON>in: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "{postUrl} üzerinden '{postTitle}' adlı bu projeye destek olun!", "app.containers.IdeasShow.projectEmailSharingSubject": "Bu projeye destek olun: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Projenizi gönderdiğiniz için teşekkürler!", "app.containers.IdeasShow.projectTwitterMessage": "Bu projeye destek olun: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Bu projeye destek olun: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Bu teklifi '{ideaTitle}' {ideaUrl}adresinden destekleyin!", "app.containers.IdeasShow.proposalEmailSharingSubject": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> deste<PERSON>: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Teklifinizi gönderdiğiniz için teşekkür ederiz!", "app.containers.IdeasShow.proposalTwitterMessage": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> deste<PERSON>in: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> deste<PERSON>in: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "<PERSON>ylama için zaman kaldı:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} {votingThreshold} gere<PERSON><PERSON> dı<PERSON>ı<PERSON>", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Oylamayı iptal et", "app.containers.IdeasShow.proposals.VoteControl.days": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.hours": "saat", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Durum ve oylar", "app.containers.IdeasShow.proposals.VoteControl.minutes": "mins", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "<PERSON><PERSON> fazla bilgi", "app.containers.IdeasShow.proposals.VoteControl.vote": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.voted": "Oylandı", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Bu girişim bir sonraki adıma geçtiğinde bilgilendirileceksiniz. {x, plural, =0 {{xDays} kaldı.} one {{xDays} kaldı.} other {Var {xDays} kaldı.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Oyunuz teslim edildi!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Ne yazık ki bu teklif için oy kullanamazsınız. Nedenini {link}adresinden okuyabilirsiniz.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {bir günden az} one {bir gün} other {# gün}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {oy yok} one {1 oy} other {# oy}}", "app.containers.IdeasShow.questionEmailSharingBody": "{postUrl} üzerinden '{postTitle}' sorusu ile ilgili tartışmaya katılın!", "app.containers.IdeasShow.questionEmailSharingSubject": "Tartışmaya katılın: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Sorunuz başarıyla yayınlandı!", "app.containers.IdeasShow.questionTwitterMessage": "Tartışmaya katılın: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Tartışmaya katılın: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "<PERSON>unu neden spam olarak bildirmek istiyorsunuz?", "app.containers.IdeasShow.share": "Paylaş", "app.containers.IdeasShow.sharingModalSubtitle": "<PERSON>ha fazla insana ulaşın ve sesinizi duyurun.", "app.containers.IdeasShow.sharingModalTitle": "Fikrinizi gönderdiğiniz için teşekkürler!", "app.containers.Navbar.completeOnboarding": "Eksiksiz işe alım", "app.containers.Navbar.completeProfile": "<PERSON> profil", "app.containers.Navbar.confirmEmail2": "E-postayı onayla", "app.containers.Navbar.unverified": "Doğrulanmadı", "app.containers.Navbar.verified": "Doğrulandı", "app.containers.NewAuthModal.beforeYouFollow": "Takip etmeden ö<PERSON>", "app.containers.NewAuthModal.beforeYouParticipate": "Katılmadan önce", "app.containers.NewAuthModal.completeYourProfile": "Profilinizi <PERSON>", "app.containers.NewAuthModal.confirmYourEmail": "E-postanızı onaylayın", "app.containers.NewAuthModal.logIn": "<PERSON><PERSON><PERSON> yap", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Devam etmek için aşağıdaki şartları gözden geçirin.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Lütfen profilinizi tamamlayın.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "<PERSON><PERSON><PERSON> g<PERSON>", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "He<PERSON>b<PERSON><PERSON><PERSON>z yok mu? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Kod 4 haneli olmalıdır.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "FranceConnect ile devam edin", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Bu platformda hiçbir kimlik doğrulama yöntemi etkinleştirilmemiştir.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON><PERSON>, bu platformdan e-posta almayı kabul etmiş olursunuz. Hangi e-postaları almak istediğinizi 'Ayarlarım' sayfasından seçebilirsiniz.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-posta", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Doğru formatta bir e-posta adresi girin, <PERSON>rne<PERSON>in <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Bir e-posta adresi <PERSON>ın", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "<PERSON><PERSON> etmek için e-posta adresinizi girin.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Şifrenizi mi unuttunuz?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Hesabı<PERSON><PERSON>za giriş yapın: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Lütfen şifrenizi girin", "app.containers.NewAuthModal.steps.Password.password": "Şifre", "app.containers.NewAuthModal.steps.Password.rememberMe": "<PERSON><PERSON><PERSON><PERSON> beni", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Herkese açık bir bilgisayar kullanıyorsanız seçmeyin", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON><PERSON> tamam", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Şimdi katılımınıza devam edin.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Kimliğiniz doğrulandı. Artık bu platformdaki topluluğun tam bir üyesisiniz.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Artık doğrulandınız!", "app.containers.NewAuthModal.steps.close": "Ka<PERSON><PERSON>", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON> et", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Ne ile ilgileniyorsunuz?", "app.containers.NewAuthModal.youCantParticipate": "Katılamazsınız", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {gö<PERSON><PERSON><PERSON>ülenmemiş bildirim yok} one {1 görüntülenmemiş bildirim} other {# görüntülenmemiş bildirim}}", "app.containers.NotificationMenu.adminRightsReceived": "Artık platformun bir yöneticisisiniz", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "\"{postTitle}\" hakkındaki yorumunuz bir yönetici tarafından silindi çünkü\n      {reasonCode, select, irrelevant {alakasız} inappropriate {içeriği uygunsuz} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} eş sponsorluk davetinizi kabul etti", "app.containers.NotificationMenu.deletedUser": "Bilinmeyen yazar", "app.containers.NotificationMenu.error": "Bildir<PERSON><PERSON> yü<PERSON>", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} size atanan bir girdi hakkında dahili olarak yorum yaptınız", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} dahili olarak yorumladığınız bir girdi ü<PERSON>inde dahili olarak yorum yaptınız", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} Yönettiğ<PERSON>z bir projedeki bir girdi hakkında dahili olarak yorum yaptınız", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} Yönetilmeyen bir projedeki atanmamış bir girdi hakkında dahili olarak yorum yaptı", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} iç yorumunuz üzerine yorum yaptı", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} sizi bir katkıya ortak sponsor olmaya davet etti", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} sizi bir fikre ortak sponsor o<PERSON>ya davet etti", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} sizi bir teklife sponsor o<PERSON><PERSON> davet etti", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} sizi bir konuya ortak sponsor o<PERSON>ya davet etti", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} sizi bir seçeneğe ortak sponsor olmaya davet etti", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} sizi bir dilekçeye ortak sponsor olmaya davet etti", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} sizi bir projeye ortak sponsor o<PERSON>ya davet etti", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} sizi bir teklife ortak sponsor o<PERSON><PERSON> davet etti", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} sizi bir soruya ortak sponsor o<PERSON>ya davet etti", "app.containers.NotificationMenu.loadMore": "<PERSON><PERSON> fazla y<PERSON>...", "app.containers.NotificationMenu.loading": "Bildirimler yükleniyor...", "app.containers.NotificationMenu.mentionInComment": "{name} bir yo<PERSON><PERSON> sizden bah<PERSON>i", "app.containers.NotificationMenu.mentionInInternalComment": "{name} da<PERSON>i bir yorumda sizden bah<PERSON>i", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} resmi bir bilgilendirmede sizden bah<PERSON>i", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Anketinizi göndermediniz", "app.containers.NotificationMenu.noNotifications": "Henüz bildirim almadınız", "app.containers.NotificationMenu.notificationsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} takip ettiğiniz bir katkı hakkında resmi bir güncelleme yaptı", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} takip ettiğiniz bir fikir hakkında resmi bir güncelleme yaptı", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} takip ettiğiniz bir girişim hakkında resmi bir güncelleme yaptı", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} takip ettiğiniz bir konuda resmi bir güncelleme yaptı", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} takip ettiğiniz bir seçenek hakkında resmi bir güncelleme verdi", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} takip ettiğiniz bir dilekçe hakkında resmi bir güncelleme yaptı", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} takip ettiğiniz bir proje hakkında resmi bir güncelleme verdi", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} takip ettiğiniz bir teklif hakkında resmi bir güncelleme yaptı", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} takip ettiğiniz bir soru hakkında resmi bir güncelleme verdi", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} size atandı", "app.containers.NotificationMenu.projectModerationRightsReceived": "Artık {projectLink} proje yöneticisisiniz", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} yeni bir a<PERSON><PERSON> girdi", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} {phaseStartAt} tarihinde yeni bir a<PERSON><PERSON><PERSON> girecek", "app.containers.NotificationMenu.projectPublished": "Yeni bir proje yayınlandı", "app.containers.NotificationMenu.projectReviewRequest": "{name} \"{projectTitle}\" projesini yayınlamak için onay istedi.", "app.containers.NotificationMenu.projectReviewStateChange": "{name} yayınlanmak üzere onaylandı \"{projectTitle}\"", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} durumu {status}o<PERSON><PERSON>", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} o<PERSON><PERSON>ı", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} da<PERSON><PERSON><PERSON> kabul etti", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} takip ettiğiniz bir katkıya yorum yaptı", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} takip ettiğiniz bir fikir hakkında yorum yaptı", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} takip ettiğiniz bir girişim hakkında yorum yaptı", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} takip ettiğiniz bir konu hakkında yorum yaptı", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} takip ettiğiniz bir seçenek hakkında yorum yaptı", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} takip ettiğiniz bir dilekçeye yorum yaptı", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} takip ettiğiniz bir proje hakkında yorum yaptı", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} takip ettiğiniz bir teklif hakkında yorum yaptı", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} takip ettiğiniz bir soruya yorum yaptı", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} \"{postTitle}\" adresini spam olarak bildirdi", "app.containers.NotificationMenu.userReactedToYourComment": "{name} yo<PERSON><PERSON><PERSON> tepki <PERSON>", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} \"{postTitle}\" hakkındaki bir yorumu spam olarak bildirdi", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Oylarınızı teslim etmediniz", "app.containers.NotificationMenu.votingBasketSubmitted": "Başarılı bir şekilde oy kullandınız", "app.containers.NotificationMenu.votingLastChance": "<PERSON><PERSON> vermek i<PERSON> son <PERSON> {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} o<PERSON><PERSON>ç<PERSON>i açik<PERSON>i", "app.containers.NotificationMenu.xAssignedPostToYou": "{postTitle} {name} tarafından size atandı", "app.containers.PasswordRecovery.emailError": "Bu geçerli bir e-posta olmayabilir", "app.containers.PasswordRecovery.emailLabel": "E-posta", "app.containers.PasswordRecovery.emailPlaceholder": "E-posta adresim", "app.containers.PasswordRecovery.helmetDescription": "Parola sıfırlama sayfası", "app.containers.PasswordRecovery.helmetTitle": "Parolanızı sıfırlayın", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "Bu e-posta adresi platformda kayıtlıysa, bir ş<PERSON>re sıfırlama bağlantısı gönderilmiştir.", "app.containers.PasswordRecovery.resetPassword": "<PERSON><PERSON>a sı<PERSON>ırlama bağlantısı gönder", "app.containers.PasswordRecovery.submitError": "Bu e-posta ile bağlantılı bir hesap bula<PERSON>. Kaydolmayı deneyebilirsiniz.", "app.containers.PasswordRecovery.subtitle": "Yeni parola belirlemek için bağlantıyı nereye gönderebiliriz?", "app.containers.PasswordRecovery.title": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.helmetDescription": "Parola sıfırlama sayfası", "app.containers.PasswordReset.helmetTitle": "Parolanızı sıfırlayın", "app.containers.PasswordReset.login": "<PERSON><PERSON><PERSON> yap", "app.containers.PasswordReset.passwordError": "Parola en az 8 karakter uzunluğunda olmalıdır", "app.containers.PasswordReset.passwordLabel": "Pa<PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON>", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Parolanız başarıyla güncellendi.", "app.containers.PasswordReset.pleaseLogInMessage": "Lütfen yeni şifrenizle giriş yapınız.", "app.containers.PasswordReset.requestNewPasswordReset": "Yeni parola sıfırlama isteği", "app.containers.PasswordReset.submitError": "<PERSON>ir sorun olu<PERSON>. Lütfen daha sonra tekrar deneyin.", "app.containers.PasswordReset.title": "Parolanızı sıfırlayın", "app.containers.PasswordReset.updatePassword": "Yeni parolayı doğrulayın", "app.containers.ProjectFolderCards.allProjects": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} şunlar üzerinde çalışıyor:", "app.containers.ProjectFolderShowPage.editFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Bu proje hakkında bilgi", "app.containers.ProjectFolderShowPage.metaTitle1": "Klasör: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON> fazla bilgi", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON>ha azını göster", "app.containers.ProjectFolderShowPage.share": "Paylaş", "app.containers.Projects.PollForm.document": "Belge", "app.containers.Projects.PollForm.formCompleted": "Teşekkür ederiz! Yanıtınız alınmıştır.", "app.containers.Projects.PollForm.maxOptions": "maks. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Bu ankete zaten katılmıştınız.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Bu anket sadece bu aşama aktifken yapılabilir.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "<PERSON><PERSON> oylama <PERSON>u anda et<PERSON>", "app.containers.Projects.PollForm.pollDisabledNotPossible": "<PERSON><PERSON> anda bu oylamaya katı<PERSON><PERSON>z mümkün <PERSON>.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Bu proje artık etkin olmadığı için oylama artık kullanılamaz.", "app.containers.Projects.PollForm.sendAnswer": "<PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_phase": "Aşama {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "<PERSON><PERSON><PERSON><PERSON><PERSON> genel bakış", "app.containers.Projects.a11y_titleInputs": "Bu projeye gönderilen tüm girdiler", "app.containers.Projects.a11y_titleInputsPhase": "<PERSON><PERSON> aşamada gönder<PERSON>n tüm girdiler", "app.containers.Projects.accessRights": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.addedToBasket": "Sepetinize eklendi", "app.containers.Projects.allocateBudget": "Bütçenizi ayırın", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>miş", "app.containers.Projects.basketSubmitted": "Sepetiniz gönderildi.", "app.containers.Projects.contributions": "Katkılar", "app.containers.Projects.createANewPhase": "<PERSON><PERSON> bir a<PERSON><PERSON> o<PERSON>ş<PERSON>", "app.containers.Projects.currentPhase": "<PERSON><PERSON> <PERSON><PERSON>", "app.containers.Projects.document": "Belge", "app.containers.Projects.editProject": "<PERSON><PERSON><PERSON>", "app.containers.Projects.emailSharingBody": "Bu girişim hakkında ne düşünüyorsunuz? Oylayın ve sesinizi duyurmak için {initiativeUrl} adresindeki tartışmayı paylaşın.", "app.containers.Projects.emailSharingSubject": "G<PERSON><PERSON><PERSON>ime destek olun: {initiativeTitle}.", "app.containers.Projects.endedOn": "{date} tari<PERSON>de sona erdi", "app.containers.Projects.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.header": "<PERSON><PERSON><PERSON>", "app.containers.Projects.ideas": "Fikirler", "app.containers.Projects.information": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.initiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Belgeyi gözden geçirin", "app.containers.Projects.invisibleTitlePhaseAbout": "<PERSON>u aşama hakkında", "app.containers.Projects.invisibleTitlePoll": "<PERSON><PERSON><PERSON>ya katılın", "app.containers.Projects.invisibleTitleSurvey": "An<PERSON><PERSON> katılın", "app.containers.Projects.issues": "<PERSON><PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "Gerçek zamanlı verileri görüntülüyorsunuz. Katılımcı sayıları yöneticiler için sürekli olarak güncellenir. Normal kullanıcıların önbelleğe alınmış verileri gördüğünü ve bu nedenle sayılarda küçük farklılıklar olabileceğini lütfen unutmayın.", "app.containers.Projects.location": "Konum:", "app.containers.Projects.manageBasket": "Sepeti yönetin", "app.containers.Projects.meetMinBudgetRequirement": "Sepetinizi göndermek için minimum bütçeyi karşılayın.", "app.containers.Projects.meetMinSelectionRequirement": "Sepetinizi göndermek için gerekli seçimi karşılayın.", "app.containers.Projects.metaTitle1": "Proje: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "<PERSON><PERSON><PERSON><PERSON> <PERSON> b<PERSON>e", "app.containers.Projects.myBasket": "Sepet", "app.containers.Projects.navPoll": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "Anket", "app.containers.Projects.newPhase": "<PERSON><PERSON>", "app.containers.Projects.nextPhase": "<PERSON><PERSON><PERSON>", "app.containers.Projects.noEndDate": "Bitiş tarihi yok", "app.containers.Projects.noItems": "Henüz hiçbir öğe seçmediniz", "app.containers.Projects.noPastEvents": "Görüntülenecek geçmiş etkinlik yok", "app.containers.Projects.noPhaseSelected": "<PERSON><PERSON><PERSON> seçilmedi", "app.containers.Projects.noUpcomingOrOngoingEvents": "<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> yaklaşan veya devam eden bir et<PERSON>lik yok.", "app.containers.Projects.offlineVotersTooltip": "<PERSON><PERSON> sayı, herhangi bir çevrimdışı seçmen sayımını yansıtmamaktadır.", "app.containers.Projects.options": "Seçenekler", "app.containers.Projects.participants": "Katılımcılar", "app.containers.Projects.participantsTooltip4": "Bu sayı aynı zamanda anonim anket gönderimlerini de yansıtmaktadır. Anketler herkese açıksa anonim anket gönderimleri mümkündür (bu proje için {accessRightsLink} sekmesine bakın).", "app.containers.Projects.pastEvents": "Geçmiş etkinlikler", "app.containers.Projects.petitions": "Dilekçeler", "app.containers.Projects.phases": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.previousPhase": "<PERSON><PERSON><PERSON>", "app.containers.Projects.project": "<PERSON><PERSON>", "app.containers.Projects.projectTwitterMessage": "Sesinizi duyurun! {projectName} | {orgName} adlı projeye katılın", "app.containers.Projects.projects": "<PERSON><PERSON><PERSON>", "app.containers.Projects.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON>ha az bilgi", "app.containers.Projects.readMore": "<PERSON><PERSON> fazla bilgi", "app.containers.Projects.removeItem": "<PERSON><PERSON><PERSON><PERSON> kaldır", "app.containers.Projects.requiredSelection": "Gere<PERSON><PERSON>", "app.containers.Projects.reviewDocument": "Belgeyi gözden geçirin", "app.containers.Projects.seeTheContributions": "Katkıları görüntüleyin", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheInitiatives": "G<PERSON>ş<PERSON><PERSON><PERSON>ü<PERSON>", "app.containers.Projects.seeTheIssues": "Yorumları görü<PERSON>ü<PERSON>in", "app.containers.Projects.seeTheOptions": "Seçenekle<PERSON>", "app.containers.Projects.seeThePetitions": "Dilekç<PERSON><PERSON> bakın", "app.containers.Projects.seeTheProjects": "Proje<PERSON>i <PERSON>", "app.containers.Projects.seeTheProposals": "<PERSON><PERSON><PERSON><PERSON><PERSON> bakın", "app.containers.Projects.seeTheQuestions": "Soruları görüntüleyin", "app.containers.Projects.seeUpcomingEvents": "Yaklaşan etkinlikleri görün", "app.containers.Projects.share": "Paylaş", "app.containers.Projects.shareThisProject": "<PERSON><PERSON> proje<PERSON>", "app.containers.Projects.submitMyBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.survey": "Anket", "app.containers.Projects.takeThePoll": "<PERSON><PERSON><PERSON>ya katılın", "app.containers.Projects.takeTheSurvey": "An<PERSON><PERSON> katılın", "app.containers.Projects.timeline": "Zaman Çizelgesi", "app.containers.Projects.upcomingAndOngoingEvents": "<PERSON><PERSON><PERSON><PERSON> ve devam eden et<PERSON>ler", "app.containers.Projects.upcomingEvents": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.whatsAppMessage": "{projectName} | {orgName} katılım platformundan", "app.containers.Projects.yourBudget": "<PERSON><PERSON> bütçe", "app.containers.ProjectsIndexPage.metaDescription": "Na<PERSON><PERSON>l katılabileceğinizi anlamak için devam eden tüm {orgName} projelerini keşfedin.\n Gelin, sizin için en önemli olan yerel projeleri tartışın.", "app.containers.ProjectsIndexPage.metaTitle1": "<PERSON><PERSON><PERSON> | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "<PERSON><PERSON><PERSON>", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Gönüllü olmak istiyorum", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Bu etkinlikte gönüllü olmak için lütfen önce {signInLink} veya {signUpLink}", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Bu faaliyet için katılım şu anda açık <PERSON>.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "oturum açın", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "kaydolun", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Gönüllülük teklifimi geri alıyorum", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {gö<PERSON><PERSON><PERSON><PERSON> yok} one {# gönüll<PERSON>} other {# gön<PERSON>ll<PERSON>}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Uyarı: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> anket, ekran okuyucu kullanıcıları için erişilebilirlik sorunları içerebilir. Herhangi bir sorunla karşılaşırsanız, orijinal platformdan ankete bir bağlantı almak için lütfen platform yöneticisine ulaşın. Alternatif olarak, anketi doldurmak için başka yollar da talep edebilirsiniz.", "app.containers.ProjectsShowPage.process.survey.survey": "Anket", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Bu ankete katılıp katılamayacağınızı öğrenmek için lütfen önce platformda {logInLink}.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Bu ankete yalnızca bu aşama zaman çizelgesinde aktif olduğunda katılabilirsiniz.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Ankete katılmak için lütfen {completeRegistrationLink} adresini ziyaret ediniz.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON>u anket şu anda et<PERSON>", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Bu ankete katılmak için kimliğinizin doğrulanması gerekiyor. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Bu proje artık aktif olmadığı için anket artık mevcut değildir.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "tam kayıt", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "<PERSON><PERSON><PERSON> yap", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "KAYIT OL", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Hesabınızı şimdi doğrulayın.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Bu belgeyi yalnızca belirli kullanıcılar inceleyebilir. Lütfen önce {signUpLink} veya {logInLink} adresini ziyaret edin.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Bu belge yalnızca bu aşama aktif olduğunda incelenebilir.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Belgeyi incelemek için lü<PERSON>fen {completeRegistrationLink} adresini ziyaret ediniz.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Ne yazık ki, bu belgeyi inceleme hakkına sahip değilsiniz.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Bu belgenin incelenmesi hesabınızın doğrulanmasını gerektirir. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Bu proje artık aktif olmadığı için belge artık mevcut değildir.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(1 çevrimdışı dahil)} other {(# çevrimdışı dahil)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 seçim} other {# seçim}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Bu seçeneği işaretleyen katılımcıların yüzdesi.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "<PERSON><PERSON> se<PERSON><PERSON>ğin aldığı toplam oyların yüzdesi.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Maliyet:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "<PERSON><PERSON> faz<PERSON>", "app.containers.ReactionControl.a11y_likesDislikes": "<PERSON><PERSON> be<PERSON>eni: {likesCount}, total dislikees: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Bu girişten hoşlanmama durumunuzu başarıyla iptal ettiniz.", "app.containers.ReactionControl.cancelLikeSuccess": "<PERSON>u girdi i<PERSON>in be<PERSON> başarıyla iptal ettiniz.", "app.containers.ReactionControl.dislikeSuccess": "<PERSON><PERSON> girdiyi başarılı bir <PERSON><PERSON><PERSON> beğenmediniz.", "app.containers.ReactionControl.likeSuccess": "<PERSON><PERSON> girdiyi başarıyla beğendiniz.", "app.containers.ReactionControl.reactionErrorSubTitle": "Bir hata nedeniyle tepkiniz kaydedilemedi. Lütfen birkaç dakika içinde tekrar deneyin.", "app.containers.ReactionControl.reactionSuccessTitle": "Tepkiniz başarıyla kaydedildi!", "app.containers.ReactionControl.vote": "<PERSON><PERSON><PERSON>", "app.containers.ReactionControl.voted": "Oylandı", "app.containers.SearchInput.a11y_cancelledPostingComment": "<PERSON>rum gönderimi iptal edildi.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} yo<PERSON><PERSON> y<PERSON>.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# etkinlikler yüklendi} one {# etkinlik yüklendi} other {# etkinlikler yüklendi}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# sonuçlar yüklendi} one {# sonuçlar yüklendi} other {# sonuçlar yüklendi}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# arama sonuçları yüklendi} one {# arama sonucu yüklendi} other {# arama sonuçları yüklendi}}.", "app.containers.SearchInput.removeSearchTerm": "<PERSON><PERSON> te<PERSON> kaldır", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "<PERSON><PERSON> terimi: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect, 700'den fazla çevrimiçi hizmete güvenli ve basit bir şekilde kaydolmak için Fransız devleti tarafından önerilen bir çözümdür.", "app.containers.SignIn.or": "Ya da", "app.containers.SignIn.signInError": "Girilen bilgiler doğru değil. Parolanızı sıfırlamak için \"Parolanızı mı unuttunuz?\" seçeneğine tıklayın.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "O<PERSON>um açmak, kaydolmak veya hesabınızı doğrulamak için FranceConnect'i kullanın.", "app.containers.SignIn.whatIsFranceConnect": "FranceConnect nedir?", "app.containers.SignUp.adminOptions2": "Yöneticiler ve proje yöneticileri için", "app.containers.SignUp.backToSignUpOptions": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.continue": "<PERSON><PERSON> et", "app.containers.SignUp.emailConsent": "Kaydolduğunuzda bu platformdan e-postalar almayı kabul etmiş olursunuz. Hangi e-postaları almak istediğinizi 'Ayarlarım' sayfasından belirleyebilirsiniz.", "app.containers.SignUp.emptyFirstNameError": "Adınızı girin", "app.containers.SignUp.emptyLastNameError": "Soyadınızı girin", "app.containers.SignUp.firstNamesLabel": "Ad", "app.containers.SignUp.goToLogIn": "He<PERSON>b<PERSON><PERSON><PERSON>z var mı? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "{link}ad<PERSON>ini okudum ve kabul ediyorum.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "{link}ad<PERSON>ini okudum ve kabul ediyorum.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Verilerin mitgestalten.wien.gv.at adresinde kullanılacağını kabul ediyorum. Daha fazla bilgi {link}adresinde bulunabilir.", "app.containers.SignUp.invitationErrorText": "Davetiyenizin süresi doldu veya zaten kullanıldı. Bir hesap oluşturmak için davet bağlantısını zaten kullandıysanız, oturum açmayı deneyin. <PERSON><PERSON><PERSON>, yeni bir hesap oluşturmak için kaydolun.", "app.containers.SignUp.lastNameLabel": "Soyadı", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Odaklandığın<PERSON>z alanlardan haberdar olmak için bu alanları takip edin:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Favori konularınızdan haberdar olmak için onları takip edin:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Terc<PERSON><PERSON>i ka<PERSON>et", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Şimdilik atla", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Devam etmek için gizlilik politikamızı kabul edin", "app.containers.SignUp.signUp2": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.skip": "Bu adımı atla", "app.containers.SignUp.tacError": "Devam etmek için hüküm ve koşullarımızı kabul edin", "app.containers.SignUp.thePrivacyPolicy": "gi̇zli̇li̇k poli̇ti̇kası", "app.containers.SignUp.theTermsAndConditions": "hük<PERSON><PERSON> ve ko<PERSON>ar", "app.containers.SignUp.unknownError": "{tenantN<PERSON>, select, LiberalDemocrats {Daha önce kaydolmayı denediğinizde işlem tamamlanmamış gibi görünmüş olabilir. Önceki kaydolma girişimi sırasında seçtiğiniz kimlik bilgilerini kullanarak Oturum Aç'a tıklayın.} other {Bir sorun oluştu. Lütfen daha sonra tekrar deneyin.}}", "app.containers.SignUp.viennaConsentEmail": "E-posta adresi", "app.containers.SignUp.viennaConsentFirstName": "Ad", "app.containers.SignUp.viennaConsentFooter": "Oturum açtıktan sonra profil bilgilerinizi değiştirebilirsiniz. Mitgestalten.wien.gv.at adresinde aynı e-posta adresiyle zaten bir hesabınız varsa, bu hesap mevcut hesabınızla ilişkilendirilecektir.", "app.containers.SignUp.viennaConsentHeader": "Aşağıdaki veriler iletilecektir:", "app.containers.SignUp.viennaConsentLastName": "Soyadı", "app.containers.SignUp.viennaConsentUserName": "Kullanıcı adı", "app.containers.SignUp.viennaDataProtection": "vienna gi̇zli̇li̇k poli̇ti̇kasi", "app.containers.SiteMap.contributions": "Katkılar", "app.containers.SiteMap.cookiePolicyLinkTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.issues": "<PERSON><PERSON><PERSON>", "app.containers.SiteMap.options": "Seçenekler", "app.containers.SiteMap.projects": "<PERSON><PERSON><PERSON>", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Başarılı", "app.containers.SpamReport.inappropriate": "Uygunsuz veya küçük düşürücü", "app.containers.SpamReport.messageError": "Form gönderilirken bir hata <PERSON>, lütfen tekrar den<PERSON>in.", "app.containers.SpamReport.messageSuccess": "Raporunuz gönderildi", "app.containers.SpamReport.other": "Başka neden", "app.containers.SpamReport.otherReasonPlaceholder": "<PERSON><PERSON>ı<PERSON><PERSON>", "app.containers.SpamReport.wrong_content": "Alakasız", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "<PERSON>il re<PERSON> kaldı<PERSON>", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Hala oylamaya açık olan tekliflere verdiğiniz oylar silinecektir. Oylama süresi sona eren tekliflere verilen oylar silinmeyecektir.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Doğrulama gerektiren projelere katılmak.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Kimliğinizi doğrulayın", "app.containers.UsersEditPage.bio": "Hakkınızda", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Doğrulanmış bilgiler içerdiği için bu alanı düzenleyemezsiniz.", "app.containers.UsersEditPage.buttonSuccessLabel": "Başarılı", "app.containers.UsersEditPage.cancel": "İptal", "app.containers.UsersEditPage.changeEmail": "E-posta <PERSON>r", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Doğrulamanızı güncellemek için lütfen buraya tıklayın.", "app.containers.UsersEditPage.conditionsLinkText": "koşullarımız", "app.containers.UsersEditPage.contactUs": "Başka bir nedenle mi ayrılmak istiyorsunuz? {feedbackLink}, belki yardımcı olabiliriz.", "app.containers.UsersEditPage.deleteAccountSubtext": "Ayrıldığınızı görmek bizi üzüyor.", "app.containers.UsersEditPage.deleteMyAccount": "Hesabımı sil", "app.containers.UsersEditPage.deleteYourAccount": "Hesabınızı silin", "app.containers.UsersEditPage.deletionSection": "Hesabınızı silin", "app.containers.UsersEditPage.deletionSubtitle": "Bu işlem geri alınamaz. Platformda yayınladığınız içerik anonimleştirilecektir. Tüm içeriklerinizi <NAME_EMAIL> adresinden bizimle iletişime geçebilirsiniz.", "app.containers.UsersEditPage.email": "E-posta", "app.containers.UsersEditPage.emailEmptyError": "Bir e-posta adresi girin", "app.containers.UsersEditPage.emailInvalidError": "<EMAIL> gibi doğru bir biçimle e-posta adresi girin", "app.containers.UsersEditPage.feedbackLinkText": "Bizi bilgilendirin", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Ad", "app.containers.UsersEditPage.firstNamesEmptyError": "Adları girin", "app.containers.UsersEditPage.h1": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.h1sub": "Hesap bil<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "app.containers.UsersEditPage.image": "Avatar resmi", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Bir profil resmi seçmek için tıklayın (maks. 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Profiliniz i<PERSON><PERSON> tü<PERSON>", "app.containers.UsersEditPage.language": "Dil", "app.containers.UsersEditPage.lastName": "Soyadı", "app.containers.UsersEditPage.lastNameEmptyError": "Soyadlarını girin", "app.containers.UsersEditPage.loading": "Yükleniyor...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "E-posta adresinizi veya şifrenizi buradan değiştirebilirsiniz.", "app.containers.UsersEditPage.loginCredentialsTitle": "<PERSON><PERSON>ş kimlik bilgileri", "app.containers.UsersEditPage.messageError": "Profilinizi kaydedemedik. Daha sonra tekrar <NAME_EMAIL> ile iletişime geçin.", "app.containers.UsersEditPage.messageSuccess": "Profili<PERSON>z <PERSON>.", "app.containers.UsersEditPage.metaDescription": "Bu, {firstName} {lastName} adlı kişinin {tenantName} çevrimiçi katılım platformundaki profil ayarları sayfasıdır. Burada kimliğinizi doğrulayabilir, hesap bilgilerinizi düzenleyebilir, hesabınızı silebilir ve e-posta tercihlerinizi düzenleyebilirsiniz.", "app.containers.UsersEditPage.metaTitle1": "{firstName} {lastName} | {orgName}profil a<PERSON><PERSON><PERSON> say<PERSON>ı", "app.containers.UsersEditPage.noGoingBack": "Bu düğmeye tıkladığınızda hesabınızı geri yüklememiz mümkün olmayacaktır.", "app.containers.UsersEditPage.noNameWarning2": "Adı<PERSON><PERSON>z şu anda platformda şu şekilde görüntüleniyor: \"{displayName}\" olarak görüntüleniyor çünkü adınızı girmediniz. Bu otomatik oluşturulan bir isimdir. Değiştirmek isterseniz, lütfen aşağıya adınızı girin.", "app.containers.UsersEditPage.notificationsSubTitle": "Hangi tür e-posta bildirimleri almak istiyorsunuz? ", "app.containers.UsersEditPage.notificationsTitle": "E-posta bi<PERSON>", "app.containers.UsersEditPage.password": "<PERSON><PERSON> bir parola se<PERSON>in", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "En az {minimumPasswordLength} karakter uzunluğunda bir parola girin", "app.containers.UsersEditPage.passwordAddSection": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.passwordAddSubtitle2": "Bir <PERSON><PERSON><PERSON> beli<PERSON>in ve her seferinde e-postanızı onaylamak zorunda kalmadan platforma kolayca giriş yapın.", "app.containers.UsersEditPage.passwordChangeSection": "Şifrenizi değiştirin", "app.containers.UsersEditPage.passwordChangeSubtitle": "Mevcut şifrenizi onaylayın ve yeni şifreyle değiştirin.", "app.containers.UsersEditPage.privacyReasons": "Gizliliğiniz konusunda endişeleriniz varsa {conditionsLink} sayfasını okuyabilirsiniz.", "app.containers.UsersEditPage.processing": "Gönderiliyor...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Soyadı verilirken ad gereklidir", "app.containers.UsersEditPage.reasonsToStayListTitle": "Ayrılmadan önce...", "app.containers.UsersEditPage.submit": "Değişiklikleri kaydet", "app.containers.UsersEditPage.tooManyEmails": "Çok fazla e-posta mı alıyorsunuz? E-posta tercihlerinizi profil ayarlarınızdan yönetebilirsiniz.", "app.containers.UsersEditPage.updateverification": "<PERSON><PERSON>i bilgi<PERSON>z <PERSON> mi? {reverifyButton}", "app.containers.UsersEditPage.user": "Sizi bilgilendirmek için ne zaman e-posta göndermemizi istersiniz?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Doğrulama gerektiren projelere katılabilirsiniz.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Doğrulama işleminiz tamamlandı", "app.containers.UsersEditPage.verifyNow": "Şimdi doğrula", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Yanıtlarınızı indirin (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {beğeni yok} one {1 beğeni} other {# beğeni}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Bu yorumun yanıt olarak yayınlandığı girdi:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "<PERSON><PERSON> ({commentsCount})", "app.containers.UsersShowPage.editProfile": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.emptyInfoText": "Yukarıda belirtilen filtrenin hiçbir öğesini takip etmiyorsunuz.", "app.containers.UsersShowPage.eventsWithCount": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Takip ({followingCount})", "app.containers.UsersShowPage.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.invisibleTitlePostsList": "Bu katılımcı tarafından gönderilen tüm girdiler", "app.containers.UsersShowPage.invisibleTitleUserComments": "Bu katılımcı tarafından yayınlanan tüm yorumlar", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON> faz<PERSON>", "app.containers.UsersShowPage.loadMoreComments": "<PERSON><PERSON> fazla yorum yükle", "app.containers.UsersShowPage.loadingComments": "<PERSON><PERSON><PERSON> yükleniyor...", "app.containers.UsersShowPage.loadingEvents": "<PERSON><PERSON><PERSON> yükleniyor...", "app.containers.UsersShowPage.memberSince": "{date} ta<PERSON><PERSON><PERSON> bu yana <PERSON>ye", "app.containers.UsersShowPage.metaTitle1": "{firstName} {lastName} | {orgName}profil sayfası", "app.containers.UsersShowPage.noCommentsForUser": "<PERSON>u kişi henüz herhangi bir yorum yayınlamadı.", "app.containers.UsersShowPage.noCommentsForYou": "Burada henüz yorum yok.", "app.containers.UsersShowPage.noEventsForUser": "Henüz hiçbir etkinliğe katılmadınız.", "app.containers.UsersShowPage.postsWithCount": "<PERSON><PERSON><PERSON><PERSON> ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "<PERSON><PERSON>", "app.containers.UsersShowPage.projects": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.proposals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.seePost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.surveyResponses": "Yan<PERSON><PERSON>ar ({responses})", "app.containers.UsersShowPage.topics": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.tryAgain": "<PERSON><PERSON> hata <PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar den<PERSON>in.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Bu, {firstName} {lastName} adlı kişinin {orgName} çevrimiçi katılım platformundaki profil say<PERSON>sıdır.", "app.containers.VoteControl.close": "Ka<PERSON><PERSON>", "app.containers.VoteControl.voteErrorTitle": "<PERSON><PERSON> so<PERSON> o<PERSON>", "app.containers.admin.ContentBuilder.default": "varsay<PERSON>lan", "app.containers.admin.ContentBuilder.imageTextCards": "Resim ve metin kartları", "app.containers.admin.ContentBuilder.infoWithAccordions": "B<PERSON><PERSON> ve a<PERSON>onlar", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 sütun", "app.containers.admin.ContentBuilder.projectDescription": "<PERSON>je <PERSON>", "app.containers.app.navbar.admin": "<PERSON>u y<PERSON>in", "app.containers.app.navbar.allProjects": "<PERSON><PERSON><PERSON> proje<PERSON>", "app.containers.app.navbar.ariaLabel": "Birincil", "app.containers.app.navbar.closeMobileNavMenu": "Mobil gezinti menü<PERSON>ü<PERSON>ü kapat", "app.containers.app.navbar.editProfile": "Ayarlarım", "app.containers.app.navbar.fullMobileNavigation": "Tam mobil", "app.containers.app.navbar.logIn": "O<PERSON>um açın", "app.containers.app.navbar.logoImgAltText": "{orgName} <PERSON>", "app.containers.app.navbar.myProfile": "Etkinliklerim", "app.containers.app.navbar.search": "<PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "<PERSON>", "app.containers.app.navbar.signOut": "Çıkış yap", "app.containers.eventspage.errorWhenFetchingEvents": "Etkinlikler yüklenirken bir hata oluştu. Lütfen sayfayı yeniden yüklemeyi deneyin.", "app.containers.eventspage.events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.eventsPageDescription": "{orgName} platformunda yayınlanan tüm etkinlikleri görüntüleyin.", "app.containers.eventspage.eventsPageTitle1": "Etkinlikler | {orgName}", "app.containers.eventspage.filterDropdownTitle": "<PERSON><PERSON><PERSON>", "app.containers.eventspage.noPastEvents": "Görüntülenecek geçmiş etkinlik yok", "app.containers.eventspage.noUpcomingOrOngoingEvents": "<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> yaklaşan veya devam eden bir et<PERSON>lik yok.", "app.containers.eventspage.pastEvents": "Geçmiş etkinlikler", "app.containers.eventspage.upcomingAndOngoingEvents": "<PERSON><PERSON><PERSON><PERSON> ve devam eden et<PERSON>ler", "app.containers.footer.accessibility-statement": "Erişilebilirlik beyanı", "app.containers.footer.ariaLabel": "İkincil", "app.containers.footer.cookie-policy": "Çerez politikası", "app.containers.footer.cookieSettings": "Çerez ayarları", "app.containers.footer.feedbackEmptyError": "<PERSON><PERSON> bi<PERSON><PERSON>m alanı boş o<PERSON>az.", "app.containers.footer.poweredBy": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.footer.privacy-policy": "Gi̇zli̇li̇k poli̇ti̇kası", "app.containers.footer.siteMap": "Site Haritası", "app.containers.footer.terms-and-conditions": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve koş<PERSON>ar", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "İptal", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, gitmek istiyorum.", "app.containers.ideaHeading.editForm": "<PERSON><PERSON>", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Gitmek istediğine emin misin?", "app.containers.ideaHeading.leaveIdeaForm": "<PERSON><PERSON><PERSON> bı<PERSON>n", "app.containers.ideaHeading.leaveIdeaText": "Yanıtlarınız kaydedilmeyecektir.", "app.containers.landing.cityProjects": "<PERSON><PERSON><PERSON>", "app.containers.landing.completeProfile": "Profilinizi <PERSON>", "app.containers.landing.completeYourProfile": "<PERSON>ş geldiniz {firstName}. Profilinizi tamamlama zamanı.", "app.containers.landing.createAccount": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.landing.defaultSignedInMessage": "{orgName} sizi dinliyor. Sesinizi duyurma sırası sizde!", "app.containers.landing.doItLater": "<PERSON><PERSON>", "app.containers.landing.new": "yeni", "app.containers.landing.subtitleCity": "{orgName} katılım platformuna hoş geldiniz", "app.containers.landing.titleCity": "{orgName} i<PERSON><PERSON>i birlikte şekillendirelim", "app.containers.landing.twitterMessage": "{ideaTitle} i<PERSON>in oy verin:", "app.containers.landing.upcomingEventsWidgetTitle": "<PERSON><PERSON><PERSON><PERSON> ve devam eden et<PERSON>ler", "app.containers.landing.userDeletedSubtitle": "İstediğiniz zaman yeni bir hesap oluşturabilir veya {contactLink} neleri geliştirebileceğimizi bildirebilirsiniz.", "app.containers.landing.userDeletedSubtitleLinkText": "bizi<PERSON>le iletişim <PERSON>", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "app.containers.landing.userDeletionFailed": "Hesabınız silinirken bir hata olu<PERSON>, sorun bize bildirildi ve düzeltmek için elimizden geleni yapacağız. Lütfen daha sonra tekrar deneyin.", "app.containers.landing.verifyNow": "Şimdi doğrula", "app.containers.landing.verifyYourIdentity": "Kimliğinizi doğrulayın", "app.containers.landing.viewAllEventsText": "<PERSON><PERSON><PERSON> etkinlikleri görü<PERSON><PERSON><PERSON>", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>ön", "app.errors.after_end_at": "Başlangıç tarihi bitiş tarihinden sonra", "app.errors.avatar_carrierwave_download_error": "Avatar dosyası indirilemedi.", "app.errors.avatar_carrierwave_integrity_error": "Avatar dosyası izin verilen bir türde değil.", "app.errors.avatar_carrierwave_processing_error": "Avatar işlenemedi.", "app.errors.avatar_extension_blacklist_error": "Avatar resminin dosya uzantısına izin verilmez. İzin verilen uzantılar: jpg, jpeg, gif ve png.", "app.errors.avatar_extension_whitelist_error": "Avatar resminin dosya uzantısına izin verilmez. İzin verilen uzantılar: jpg, jpeg, gif ve png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON> düğme metni girin.", "app.errors.banner_cta_button_url_blank": "Bir bağlantı girin.", "app.errors.banner_cta_button_url_url": "Geçerli bir bağlantı girin. Bağlantının 'https://' ile başladığından emin olun.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON> düğme metni girin.", "app.errors.banner_cta_signed_in_url_blank": "Bir bağlantı girin.", "app.errors.banner_cta_signed_in_url_url": "Geçerli bir bağlantı girin. Bağlantının 'https://' ile başladığından emin olun.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON> düğme metni girin.", "app.errors.banner_cta_signed_out_url_blank": "Bir bağlantı girin.", "app.errors.banner_cta_signed_out_url_url": "Geçerli bir bağlantı girin. Bağlantının 'https://' ile başladığından emin olun.", "app.errors.base_includes_banned_words": "Küfür olarak kabul edilen bir veya daha fazla kelime kullanmış olabilirsiniz. Lütfen mevcut olabilecek küfürleri kaldırmak için metninizi değiştirin.", "app.errors.body_multiloc_includes_banned_words": "Açıklama uygunsuz olduğu düşünülen kelimeler içeriyor.", "app.errors.bulk_import_idea_not_valid": "Ortaya çıkan fikir geçerli değildir: {value}.", "app.errors.bulk_import_image_url_not_valid": "{value}adresinden hiçbir görüntü indirilemedi. URL'nin geçerli olduğundan ve .png veya .jpg gibi bir dosya uzantısı ile bittiğinden emin olun. Bu sorun {row}ID'li satırda meydana gelmektedir.", "app.errors.bulk_import_location_point_blank_coordinate": "{value}adresinde eksik koordinatlı fikir konumu. Bu sorun {row}ID'li satırda meydana gelmektedir.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "{value}ad<PERSON><PERSON><PERSON> sayısal olmayan bir koordinata sahip fikir konumu. Bu sorun {row}ID'li satırda meydana gelmektedir.", "app.errors.bulk_import_malformed_pdf": "Yüklenen PDF dosyası hatalı biçimlendirilmiş görünüyor. PDF'yi kaynağınızdan tekrar dışa aktarmayı ve ardından tekrar yüklemeyi deneyin.", "app.errors.bulk_import_maximum_ideas_exceeded": "Maksimum {value} fikir sayısı aşılmıştır.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Bir PDF'deki maksimum {value} sayfa sayısı aşıldı.", "app.errors.bulk_import_not_enough_pdf_pages": "Yüklenen PDF'nin yeterli sayfası yok - en azından indirilen şablonla aynı sayıda sayfası olmalıdır.", "app.errors.bulk_import_publication_date_invalid_format": "Geçersiz yayın tarihi formatına sahip fikir \"{value}\". Lütfen \"DD-MM-YYYY\" formatını kullanın.", "app.errors.cannot_contain_ideas": "Seçtiğiniz katılım yöntemi bu tür yayınları desteklemiyor. Lütfen seçiminizi düzenleyin ve tekrar deneyin.", "app.errors.cant_change_after_first_response": "Bazı kullanıcılar zaten yanıt verdiği için bunu artık değiştiremezsiniz", "app.errors.category_name_taken": "Bu isimde bir kategori zaten var", "app.errors.confirmation_code_expired": "<PERSON>dun süresi doldu. Lütfen yeni bir kod talep edin.", "app.errors.confirmation_code_invalid": "Geçersiz onay kodu. Lütfen doğru kod için e-postanızı kontrol edin veya 'Yeni Kod Gönder'i deneyin", "app.errors.confirmation_code_too_many_resets": "Onay kodunu çok fazla kez yeniden gönderdiniz. Bunun yerine bir davetiye kodu almak için lütfen bizimle iletişime geçin.", "app.errors.confirmation_code_too_many_retries": "Çok fazla kez denediniz. Lütfen yeni bir kod talep edin veya e-postanızı değiştirmeyi deneyin.", "app.errors.email_already_active": "{row} sat<PERSON><PERSON><PERSON><PERSON> bulunan {value} e-posta adresi zaten kayıtlı olan bir katılımcıya ait", "app.errors.email_already_invited": "{row} sat<PERSON><PERSON><PERSON><PERSON> bulunan {value} e-posta adresi zaten davet edilmiş", "app.errors.email_blank": "<PERSON><PERSON> bo<PERSON> o<PERSON>az", "app.errors.email_domain_blacklisted": "Lütfen kayıt olmak için farklı bir e-posta alan adı kullanın.", "app.errors.email_invalid": "Lütfen geçerli bir e-posta adresi kullanın.", "app.errors.email_taken": "Bu e-postaya sahip bir hesap zaten var. Oturum açmayı deneyin.", "app.errors.email_taken_by_invite": "{value} be<PERSON>en bir davet tarafından alınmış. Spam klasörünüzü kontrol edin veya bulamazsanız {supportEmail} ile iletişime geçin.", "app.errors.emails_duplicate": "Aşağıdaki satırlarda {value} e-posta adresi için bir veya daha fazla yinelenen değer bulundu: {rows}", "app.errors.extension_whitelist_error": "Yüklemeye çalıştığınız dosyanın biçimi desteklenmiyor.", "app.errors.file_extension_whitelist_error": "Yüklemeye çalıştığınız dosyanın biçimi desteklenmiyor.", "app.errors.first_name_blank": "<PERSON><PERSON> bo<PERSON> o<PERSON>az", "app.errors.generics.blank": "<PERSON><PERSON> bo<PERSON> o<PERSON>az.", "app.errors.generics.invalid": "Bu geçerli bir değer olmayabilir", "app.errors.generics.taken": "Bu e-posta zaten var. E-postaya başka bir hesap bağlı.", "app.errors.generics.unsupported_locales": "<PERSON>u alan mevcut yerel ayarı desteklemiyor.", "app.errors.group_ids_unauthorized_choice_moderator": "Bir proje yöneticisi o<PERSON>, yalnızca projelerinize erişebilen kişilere e-posta gönderebilirsiniz", "app.errors.has_other_overlapping_phases": "Projelerde çakışan aşamalar olamaz.", "app.errors.invalid_email": "{row} sat<PERSON><PERSON><PERSON><PERSON> bulunan {value} geçerli bir e-posta adresi de<PERSON>", "app.errors.invalid_row": "{row} satırı işlenmeye çalışılırken bilinmeyen bir hata oluştu", "app.errors.is_not_timeline_project": "Geçerli proje, aşamaları desteklemiyor.", "app.errors.key_invalid": "<PERSON><PERSON><PERSON>, rakam ve alt çizgi (_) içerebilir", "app.errors.last_name_blank": "<PERSON><PERSON> bo<PERSON> o<PERSON>az", "app.errors.locale_blank": "Lütfen bir dil seçin", "app.errors.locale_inclusion": "Lütfen desteklenen bir dil seçin", "app.errors.malformed_admin_value": "{row} sat<PERSON><PERSON><PERSON><PERSON> bulunan {value} yönetici değeri geçerli değil", "app.errors.malformed_groups_value": "{row} sat<PERSON><PERSON><PERSON><PERSON> bulunan {value} grubu geçerli bir grup değildir", "app.errors.max_invites_limit_exceeded1": "Davetiye sayısı 1000 sınırını aşıyor.", "app.errors.maximum_attendees_greater_than1": "Maksimum kayıtlı kişi sayısı 0'dan büyük olmalıdır.", "app.errors.maximum_attendees_greater_than_attendees_count1": "<PERSON><PERSON><PERSON> kayıtlı kişi sayısı mevcut kayıtlı kişi sayısına eşit veya daha fazla olmalıdır.", "app.errors.no_invites_specified": "Herhangi bir e-posta adresi bulunamadı.", "app.errors.no_recipients": "Alıcı olmadığı için kampanya gönderilemiyor. Gönderdiğiniz grup ya boştur ya da hiç kimse e-posta almaya onay vermemiştir.", "app.errors.number_invalid": "Lütfen geçerli bir numara girin.", "app.errors.password_blank": "<PERSON><PERSON> bo<PERSON> o<PERSON>az", "app.errors.password_invalid": "Lütfen mevcut şifrenizi tekrar kontrol edin.", "app.errors.password_too_short": "Parola en az 8 karakter uzunluğunda olmalıdır", "app.errors.resending_code_failed": "<PERSON>ay kodu gönderilirken bir şeyler ters gitti.", "app.errors.slug_taken": "Bu proje URL'si zaten var. Lütfen proje adres kısa adını değiştirin.", "app.errors.tag_name_taken": "Bu isimde bir etiket zaten mevcut", "app.errors.title_multiloc_blank": "Başlık boş olamaz.", "app.errors.title_multiloc_includes_banned_words": "Başlık uygunsuz olduğu düşünülen kelimeler içeriyor.", "app.errors.token_invalid": "Parola sıfırlama bağlantıları yalnızca bir kez kullanılabilir ve gönderildikten sonra bir saat süreyle geçerlidir. {passwordResetLink}.", "app.errors.too_common": "Bu parola kolayca tahmin edilebilir. Lütfen daha güçlü bir parola seçin.", "app.errors.too_long": "Lütfen daha kısa bir parola seçin (en fazla 72 karakter)", "app.errors.too_short": "Lütfen en az 8 karakter uzunluğunda bir parola seçin", "app.errors.uncaught_error": "Bilinmeyen bir hata o<PERSON>.", "app.errors.unknown_group": "{row} sat<PERSON><PERSON><PERSON><PERSON> bulunan {value} grubu bilinen bir grup değil", "app.errors.unknown_locale": "{row} sat<PERSON><PERSON><PERSON><PERSON> bulunan {value} dili yapılandırılmış bir dil değil", "app.errors.unparseable_excel": "Seçilen Excel dosyası işlenemedi.", "app.errors.url": "Geçerli bir bağlantı girin. Bağlantının https:// ile başladığından emin olun.", "app.errors.verification_taken": "Aynı bilgiler kullanılarak başka bir hesap doğrulandığı için doğrulama tamamlanamıyor.", "app.errors.view_name_taken": "Bu ada sahip bir gö<PERSON><PERSON><PERSON><PERSON><PERSON> zaten var", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Bir yayın veya yorumda uygunsuz içerik otomatik olarak tespit edildi", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "StandardPortal ile oturum açın", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "StandardPortal ile kaydolun", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Şimdi bir Stadt Wien Hesabı oluşturun ve Viyana'nın birçok dijital hizmeti için tek bir giriş kullanın.", "app.modules.id_cow.cancel": "İptal", "app.modules.id_cow.emptyFieldError": "<PERSON><PERSON> alan boş bırakılamaz.", "app.modules.id_cow.helpAltText": "Kimlik seri numarasının kimliğin neresinde bulunacağını gösterir", "app.modules.id_cow.invalidIdSerialError": "Kimlik seri numarası geçersiz", "app.modules.id_cow.invalidRunError": "Geçersiz RUN", "app.modules.id_cow.noMatchFormError": "Eşleşme bulunamadı.", "app.modules.id_cow.notEntitledFormError": "<PERSON>ki yok.", "app.modules.id_cow.showCOWHelp": "Kimlik Seri Numaramı nerede bulabilirim?", "app.modules.id_cow.somethingWentWrongError": "Bir şeyler ters gittiği için sizi doğrulayamıyoruz", "app.modules.id_cow.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_cow.takenFormError": "Zaten alınmış.", "app.modules.id_cow.verifyCow": "COW kullanarak doğrulayın", "app.modules.id_franceconnect.verificationButtonAltText": "FranceConnect ile doğrulayın", "app.modules.id_gent_rrn.cancel": "İptal", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON><PERSON> alan boş bırakılamaz.", "app.modules.id_gent_rrn.gentRrnHelp": "Sosyal güvenlik numaranız dijital kimlik kartınızın arkasındadır", "app.modules.id_gent_rrn.invalidRrnError": "Sosyal güvenlik numarası geçersiz", "app.modules.id_gent_rrn.noMatchFormError": "Sosyal güvenlik numaranız hakkında bilgi bulamadık", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Gent dışında yaşadığınız için sizi doğrulayamıyoruz", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "14 yaşından küçük olduğunuz için sizi doğrulayamıyoruz", "app.modules.id_gent_rrn.rrnLabel": "Sosyal güvenlik numarası", "app.modules.id_gent_rrn.rrnTooltip": "Gent sakini olup olmadığınızı ve 14 yaşından büyük olup olmadığınızı doğrulamak için sosyal güvenlik numaranızı istiyoruz.", "app.modules.id_gent_rrn.showGentRrnHelp": "Kimlik Seri Numaramı nerede bulabilirim?", "app.modules.id_gent_rrn.somethingWentWrongError": "Bir şeyler ters gittiği için sizi doğrulayamıyoruz", "app.modules.id_gent_rrn.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.takenFormError": "Sosyal güvenlik numaranız başka bir hesabı doğrulamak için kullanılmış", "app.modules.id_gent_rrn.verifyGentRrn": "GentRrn kullanarak doğrulayın", "app.modules.id_id_card_lookup.cancel": "İptal", "app.modules.id_id_card_lookup.emptyFieldError": "<PERSON><PERSON> alan boş bırakılamaz.", "app.modules.id_id_card_lookup.helpAltText": "Kimlik kartı açıklaması", "app.modules.id_id_card_lookup.invalidCardIdError": "Bu kimlik geçerli değil.", "app.modules.id_id_card_lookup.noMatchFormError": "Eşleşme bulunamadı.", "app.modules.id_id_card_lookup.showHelp": "Kimlik Seri Numaramı nerede bulabilirim?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Bir şeyler ters gittiği için sizi doğrulayamıyoruz", "app.modules.id_id_card_lookup.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.takenFormError": "Zaten alınmış.", "app.modules.id_oostende_rrn.cancel": "İptal", "app.modules.id_oostende_rrn.emptyFieldError": "<PERSON><PERSON> alan boş bırakılamaz.", "app.modules.id_oostende_rrn.invalidRrnError": "Sosyal güvenlik numarası geçersiz", "app.modules.id_oostende_rrn.noMatchFormError": "Sosyal güvenlik numaranız hakkında bilgi bulamadık", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Oostende dışında yaşadığınız için sizi doğrulayamıyoruz", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "14 yaşından küçük olduğunuz için sizi doğrulayamıyoruz", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Sosyal güvenlik numaranız dijital kimlik kartınızın arkasındadır", "app.modules.id_oostende_rrn.rrnLabel": "Sosyal güvenlik numarası", "app.modules.id_oostende_rrn.rrnTooltip": "Oostende vatandaşı olup olmadığınızı ve 14 yaşından büyük olup olmadığınızı doğrulamak için sosyal güvenlik numaranızı istiyoruz.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Sosyal güvenlik numaramı nerede bulabilirim?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Bir şeyler ters gittiği için sizi doğrulayamıyoruz", "app.modules.id_oostende_rrn.submit": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.takenFormError": "Sosyal güvenlik numaranız başka bir hesabı doğrulamak için kullanılmış", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Sosyal güvenlik numarasını kullanarak doğrulayın", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "\"{folderName}\" klasörü için yönetici hakları aldınız.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Paylaş", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Sesinizi duyurmak için {folderUrl} adresindeki projeleri inceleyin!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | {orgName} katılım platformundan", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | {orgName} katılım platformundan", "app.sessionRecording.accept": "<PERSON><PERSON>, kabul ediyorum.", "app.sessionRecording.modalDescription1": "Kullanıcılarımızı daha iyi anlamak için, z<PERSON>retçilerin küçük bir yüzdesinden rastgele olarak tarama oturumlarını ayrıntılı olarak izlemelerini istiyoruz.", "app.sessionRecording.modalDescription2": "Kaydedilen verilerin tek amacı web sitesini geliştirmektir. Verilerinizin hiçbiri 3. bir tarafla paylaşılmayacaktır. Girdiğiniz tüm hassas bilgiler filtrelenecektir.", "app.sessionRecording.modalDescription3": "Kabul ediyor musunuz?", "app.sessionRecording.modalDescriptionFaq": "SSS burada.", "app.sessionRecording.modalTitle": "Bu web sitesini geliştirmemize yardımcı olun", "app.sessionRecording.reject": "<PERSON><PERSON><PERSON>, reddediyorum.", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "<PERSON><PERSON> bütçe tahsis denemesi yapın", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Bir belge hakkında geri bildirim top<PERSON>a", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Platform içi bir anket oluşturun", "app.utils.AdminPage.ProjectEdit.createPoll": "Bir oylama o<PERSON>ştur<PERSON>", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Create an external survey", "app.utils.AdminPage.ProjectEdit.findVolunteers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bulun", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Girdi ve geri bildirim top<PERSON>n", "app.utils.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.credits": "kredi", "app.utils.FormattedCurrency.tokens": "jeton", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# kredi} one {# kredi} other {# kredi}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# belirteçler} one {# belirteç} other {# belirteçler}}", "app.utils.IdeaCards.mostDiscussed": "En çok tartışılanlar", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.newest": "<PERSON>", "app.utils.IdeaCards.oldest": "En eski", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Trend", "app.utils.IdeasNewPage.contributionFormTitle": "Yeni katkı ekleyin", "app.utils.IdeasNewPage.ideaFormTitle": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.initiativeFormTitle": "<PERSON><PERSON>", "app.utils.IdeasNewPage.issueFormTitle1": "<PERSON>ni yorum ekle", "app.utils.IdeasNewPage.optionFormTitle": "<PERSON><PERSON> se<PERSON> e<PERSON>", "app.utils.IdeasNewPage.petitionFormTitle": "<PERSON><PERSON>", "app.utils.IdeasNewPage.projectFormTitle": "<PERSON><PERSON> proje e<PERSON>in", "app.utils.IdeasNewPage.proposalFormTitle": "<PERSON><PERSON> te<PERSON>", "app.utils.IdeasNewPage.questionFormTitle": "<PERSON><PERSON> soru e<PERSON>in", "app.utils.IdeasNewPage.surveyTitle": "Anket", "app.utils.IdeasNewPage.viewYourComment": "Yorumunuz<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourContribution": "Katkınızı görüntüleyin", "app.utils.IdeasNewPage.viewYourIdea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourInitiative": "Giriş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourInput": "Girdinizi gö<PERSON>", "app.utils.IdeasNewPage.viewYourIssue": "Sorununuzu gö<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourOption": "Seçeneğinizi gö<PERSON>", "app.utils.IdeasNewPage.viewYourPetition": "Dilekçenizi gör<PERSON><PERSON><PERSON><PERSON>in", "app.utils.IdeasNewPage.viewYourProject": "<PERSON>je<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourProposal": "Teklifinizi görüntüleyin", "app.utils.IdeasNewPage.viewYourQuestion": "Sorunuzu gö<PERSON>", "app.utils.Projects.sendSubmission": "<PERSON><PERSON><PERSON><PERSON>ımlayıcısını e-postama gönderin", "app.utils.Projects.sendSurveySubmission": "Anket gönderme tanımlayıcısını e-postama gönder", "app.utils.Projects.surveySubmission": "<PERSON><PERSON>", "app.utils.Projects.yourResponseHasTheFollowingId": "Yanıtınız aşağıdaki tanımlayıcıya sahiptir: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "Daha sonra yanıtınızın kaldırılmasını istediğinize karar verirseniz, lütfen aşağıdaki benzersiz tanımlayıcı ile bizimle iletişime geçin:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Bu etkinliğe katılmak için profilinizi tamamlamanız gerekmektedir.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Bu etkinliğe katılmak için gereken şartları karşılamıyorsunuz.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Bu etkinliğe katılmanıza izin verilmemektedir.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Bu etkinliğe katılmak için giriş yapmalı veya kayıt olmalısınız.", "app.utils.actionDescriptors.attendingEventNotVerified": "Bu etkinliğe katılabilmeniz için hesabınızı doğrulamanız gerekmektedir.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Gönüllü olmak için profilinizi doldurmalısınız.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Gönüllü olmak için gereken şartları karşılamıyorsunuz.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Gönüllü olmanıza izin verilmiyor.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Gönüllü olmak için giriş yapmalı veya kayıt olmalısınız.", "app.utils.actionDescriptors.volunteeringNotVerified": "Gönüllü olmadan önce hesabınızı doğrulamanız gerekmektedir.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Gönüllü olmak için lütfen {completeRegistrationLink} adresini ziyaret ediniz.", "app.utils.errors.api_error_default.in": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.ajv_error_birthyear_required": "Lütfen doğum yılınızı girin", "app.utils.errors.default.ajv_error_date_any": "Lütfen geçerli bir tarih girin", "app.utils.errors.default.ajv_error_domicile_required": "Lütfen ikamet ettiğiniz yeri girin", "app.utils.errors.default.ajv_error_gender_required": "Lütfen cinsiyetinizi girin", "app.utils.errors.default.ajv_error_invalid": "Geçersiz", "app.utils.errors.default.ajv_error_maxItems": "En çok {limit, plural, one {# öğe} other {# öğe}} bulunabilir", "app.utils.errors.default.ajv_error_minItems": "En az {limit, plural, one {# öğe} other {# öğe}} bulunmalıdır", "app.utils.errors.default.ajv_error_number_any": "Lütfen geçerli bir sayı girin", "app.utils.errors.default.ajv_error_politician_required": "Lütfen politikacı olup olmadığınız bilgisini girin", "app.utils.errors.default.ajv_error_required3": "<PERSON>: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Boş olamaz", "app.utils.errors.default.api_error_accepted": "Kabul edilmeli", "app.utils.errors.default.api_error_blank": "Boş olamaz", "app.utils.errors.default.api_error_confirmation": "Eşleşmiyor", "app.utils.errors.default.api_error_empty": "Boş olamaz", "app.utils.errors.default.api_error_equal_to": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_even": "Çift olmalı", "app.utils.errors.default.api_error_exclusion": "Ayrılmış", "app.utils.errors.default.api_error_greater_than": "Çok küçük", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Çok küçük", "app.utils.errors.default.api_error_inclusion": "Listede yok", "app.utils.errors.default.api_error_invalid": "Geçersiz", "app.utils.errors.default.api_error_less_than": "Çok büyük", "app.utils.errors.default.api_error_less_than_or_equal_to": "Çok büyük", "app.utils.errors.default.api_error_not_a_number": "<PERSON><PERSON> <PERSON><PERSON> değ<PERSON>", "app.utils.errors.default.api_error_not_an_integer": "Bir tamsayı olmalı", "app.utils.errors.default.api_error_other_than": "<PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_present": "Boş olmalı", "app.utils.errors.default.api_error_too_long": "Çok uzun", "app.utils.errors.default.api_error_too_short": "Çok kısa", "app.utils.errors.default.api_error_wrong_length": "Yanlış uzunlukta", "app.utils.errors.defaultapi_error_.odd": "Tek olmalı", "app.utils.notInGroup": "Katılmak için gereken şartları karşılamıyorsunuz.", "app.utils.participationMethod.onSurveySubmission": "Teşekkür ederim. Yanıtınız alınmıştır.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Bu aşama artık aktif olmadığı için oylama yapılamamaktadır.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Oy kullanmak için gerekli şartları karşılamıyorsunuz.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Oy kullanma izniniz yok.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Oy kullanmak için giriş yapmalı veya kayıt olmalısınız.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Oy kullanabilmeniz için hesabınızı doğrulamanız gerekmektedir.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> gönderimi {endDate}tari<PERSON>de sona ermi<PERSON>tir.</b> Kat<PERSON>lımcıların <b> her biri {optionCount} seçenekleri arasında dağıtmak üzere</b> toplam <b>{maxBudget} bütçesine sa<PERSON>ti.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON> sunuldu 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Bütçe atamak için gereken koşulları karşılamıyorsunuz.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Bütçe atamanıza izin verilmez.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Bütçe atamak için oturum açmanız veya kaydolmanız gerekir.", "app.utils.votingMethodUtils.budgetingNotVerified": "Bütçe atayabilmeniz için önce hesabınızı doğrulamanız gerekir.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b><PERSON><PERSON> \"<PERSON><PERSON><PERSON> \"e tıkla<PERSON> kadar bütçeniz</b> say<PERSON><PERSON>yacaktır.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Gerekli minimum bütçe {amount}adresidir.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON><PERSON><PERSON>, bütçenizi göndermek için \"<PERSON><PERSON><PERSON>\" d<PERSON><PERSON><PERSON><PERSON> tıklayın.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "\"Ekle\" üzerine dokunarak tercih ettiğiniz seçenekleri seçin.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "<b> {optionCount} seçenekleri arasında dağıtmak üzere</b> toplam <b>{maxBudget} tutar<PERSON>na sa<PERSON>.</b>", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON>z gönderildi!</b> Aşağıdaki seçeneklerinizi istediğiniz zaman kontrol edebilir veya <b>{endDate}</b>adresinden önce değiştirebilirsiniz.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON>ç<PERSON>z gönderildi!</b> Aşağıdaki seçeneklerinizi istediğiniz zaman kontrol edebilirsiniz.", "app.utils.votingMethodUtils.castYourVote": "Oyun<PERSON>u kullanın", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Seçenek başına en fazla {maxVotes, plural, one {# kredi} other {# kredi}} ekleyebilirsiniz.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Seçenek başına en fazla {maxVotes, plural, one {# puan} other {# puan}} ekleyebilirsiniz.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Seçenek başına en fazla {maxVotes, plural, one {# token} other {# token}} ekleyebilirsiniz.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Seçenek başına en fazla {maxVotes, plural, one {# oy} other {# oy}} ekleyebilirsiniz.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON><PERSON><PERSON><PERSON>, oyunuzu kullanmak i<PERSON> \"<PERSON><PERSON><PERSON> \"e tıklayın.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "\"Seç\" düğmesine dokunarak tercih ettiğiniz seçenekleri belirleyin.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Toplam <b>{totalVotes, plural, one {# krediniz} other {# kredileriniz}} aras<PERSON>nda dağıtmak için {optionCount, plural, one {# seçenek} other {# seçenekler}}. </b>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Toplam <b>{totalVotes, plural, one {# point} other {# points}} a<PERSON><PERSON>nda dağıtmak için {optionCount, plural, one {# option} other {# options}}. </b>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Toplam <b>{totalVotes, plural, one {# token} other {# token}} a<PERSON><PERSON>nda dağıtmak için {optionCount, plural, one {# option} other {# options}}. </b>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Toplam <b>{totalVotes, plural, one {# oy} other {# oy}} a<PERSON><PERSON>nda dağıtmak için {optionCount, plural, one {# seçenek} other {# seçenek}}. </b>", "app.utils.votingMethodUtils.finalResults": "<PERSON><PERSON>", "app.utils.votingMethodUtils.finalTally": "<PERSON>", "app.utils.votingMethodUtils.howToParticipate": "Na<PERSON><PERSON>l katılabilirsiniz", "app.utils.votingMethodUtils.howToVote": "Nasıl oy kullanılır", "app.utils.votingMethodUtils.multipleVotingEnded1": "<PERSON><PERSON><PERSON> <b>{endDate}adresinde sona ermi<PERSON>.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 kredi} one {1 kredi} other {# kredi}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 puan} one {1 puan} other {# puan}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 belirteç} one {1 belirteç} other {# belirteç}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 oy} one {1 oy} other {# oy}}", "app.utils.votingMethodUtils.results": "Sonuçlar", "app.utils.votingMethodUtils.singleVotingEnded": "Oylama <b>{endDate}tari<PERSON><PERSON> kapanmıştır.</b> Katılımcılar <b>adresinden {maxVotes} seçeneklerine oy verebilirler.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "\"Oy Ver\" seçeneğine dokunarak tercih ettiğiniz seçenekleri seçin", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Seçeneklere atayabileceğiniz <b>{totalVotes}</b> oylarınız vardır.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON><PERSON><PERSON><PERSON>, oyunuzu kullanmak i<PERSON> \"<PERSON><PERSON><PERSON> \"e tıklayın.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Oylama <b>{endDate}adresinde kapanmıştır.</b> Katılımcılar <b>adresinden 1 seçenek için oy kullanabilir.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "\"Oy Ver\" seçeneğine dokunarak tercih ettiğiniz seçeneği seçin.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Seçeneklerden birine atayabileceğiniz <b>1 oy</b> hakkınız vardır.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "O<PERSON><PERSON> <b>{endDate}tari<PERSON>de sona ermiştir.</b> Katılımcılar <b>adresinden diledikleri kadar seçeneğe oy verebilirler.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "İstediğiniz kadar seçeneğe oy verebilirsiniz.", "app.utils.votingMethodUtils.submitYourBudget": "Bütçenizi gö<PERSON>in", "app.utils.votingMethodUtils.submittedBudgetCountText2": "k<PERSON><PERSON><PERSON> bütçesini çevrimiçi olarak sundu", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> çevrimiçi olarak sundu", "app.utils.votingMethodUtils.submittedVoteCountText2": "kişi oylarını çevrimiçi olarak sundu", "app.utils.votingMethodUtils.submittedVotesCountText2": "insanlar oylarını çevrimiçi olarak sundu", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "<PERSON><PERSON> g<PERSON>il<PERSON> 🎉", "app.utils.votingMethodUtils.votesCast": "Kullanılan oylar", "app.utils.votingMethodUtils.votingClosed": "<PERSON><PERSON><PERSON> ka<PERSON>", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "\"Gönder\" but<PERSON><PERSON> tıklayana kadar oyunuz <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b>.", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, oyunuz gönderildi!</b> Gönderinizi daha önce kontrol edebilir veya değiştirebilirsiniz <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, oyunuz verilmiştir!</b> Oyunuzu istediğiniz zaman aşağıdan kontrol edebilir veya değiştirebilirsiniz.", "components.UI.IdeaSelect.noIdeaAvailable": "Mevcut bir fikir yok.", "components.UI.IdeaSelect.selectIdea": "<PERSON><PERSON><PERSON>", "containers.SiteMap.allProjects": "<PERSON><PERSON><PERSON> proje<PERSON>", "containers.SiteMap.customPageSection": "<PERSON><PERSON>", "containers.SiteMap.folderInfo": "<PERSON><PERSON> fazla bilgi", "containers.SiteMap.headSiteMapTitle": "Site haritası | {orgName}", "containers.SiteMap.homeSection": "<PERSON><PERSON>", "containers.SiteMap.pageContents": "<PERSON><PERSON> içeriği", "containers.SiteMap.profilePage": "<PERSON><PERSON>", "containers.SiteMap.profileSettings": "Ayarlarınız", "containers.SiteMap.projectEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectIdeas": "Fikirler", "containers.SiteMap.projectInfo": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectPoll": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectSurvey": "Anket", "containers.SiteMap.projectsArchived": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projeler", "containers.SiteMap.projectsCurrent": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.projectsDraft": "Taslak projeler", "containers.SiteMap.projectsSection": "{orgName} projeleri", "containers.SiteMap.signInPage": "O<PERSON>um açın", "containers.SiteMap.signUpPage": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.siteMapDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>i herhangi bir içeriğe gidebilirsiniz.", "containers.SiteMap.siteMapTitle": "{orgName} katılım platformunun site haritası", "containers.SiteMap.successStories": "Başarı hikayeleri", "containers.SiteMap.timeline": "<PERSON><PERSON>", "containers.SiteMap.userSpaceSection": "Hesabınız", "containers.SubscriptionEndedPage.accessDenied": "Artık erişiminiz yok", "containers.SubscriptionEndedPage.subscriptionEnded": "Bu sayfaya yalnızca aktif aboneliği olan platformlar erişebilir."}