{"EmailSettingsPage.emailSettings": "E-Mail-Einstellungen", "EmailSettingsPage.initialUnsubscribeError": "<PERSON><PERSON> von diesem E-Mailverteiler ist ein Fehler aufgetreten.", "EmailSettingsPage.initialUnsubscribeLoading": "Ihre Anfrage wird gerade bearbeitet, bitte haben Sie noch etwas Geduld...", "EmailSettingsPage.initialUnsubscribeSuccess": "Sie haben sich erfolg<PERSON>ich von {campaignTitle} abgemeldet.", "UI.FormComponents.optional": "optional", "app.closeIconButton.a11y_buttonActionMessage": "Schließen", "app.components.Areas.areaUpdateError": "<PERSON><PERSON>rn Ihres Gebiets ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "app.components.Areas.followedArea": "Gefolgter Bereich: {areaTitle}", "app.components.Areas.followedTopic": "Gefolgtes Thema: {topicTitle}", "app.components.Areas.topicUpdateError": "<PERSON><PERSON> Ihres Themas ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "app.components.Areas.unfollowedArea": "Ungefolgter Bereich: {areaTitle}", "app.components.Areas.unfollowedTopic": "Ungefolgtes Thema: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Preis:", "app.components.AssignBudgetControl.add": "Hinzufügen", "app.components.AssignBudgetControl.added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Sie haben alle Ihre Gutschriften verteilt.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Sie haben die maximale Anzahl von Gutschriften für diese Option verteilt.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Sie haben alle Ihre Punk<PERSON> verteilt.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Sie haben die maximale Anzahl von Punkten für diese Option verteilt.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Sie haben alle Ihre Token<PERSON> verteilt.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Sie haben die maximale Anzahl von Token für diese Option verteilt.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Sie haben alle Ihre Stimmen verteilt.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Sie haben die maximale Anzahl von Stimmen für diese Option verteilt.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(davon 1 analog)} other {(davon # analog)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Abstimmungen sind nicht möglich, da diese Phase nicht aktiv ist.", "app.components.AssignMultipleVotesControl.removeVote": "<PERSON><PERSON><PERSON>nen", "app.components.AssignMultipleVotesControl.select": "Auswählen", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Sie haben Ihre Stimme bereits abgegeben. Um sie zu ändern, klicken Sie auf \"Ändern Sie Ihren Beitrag\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Sie haben Ihre Stimme bereits abgegeben. Um sie zu ändern, gehen Sie zurück auf die Projektseite und klicken Sie auf \"Ändern Sie Ihren Beitrag\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON>aben}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON>}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {Token} other {Tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {<PERSON><PERSON><PERSON>} other {Stim<PERSON>}}", "app.components.AssignVoteControl.maxVotesReached1": "Sie haben alle Ihre Stimmen verteilt.", "app.components.AssignVoteControl.phaseNotActive": "Abstimmungen sind nicht möglich, da diese Phase nicht aktiv ist.", "app.components.AssignVoteControl.select": "Auswählen", "app.components.AssignVoteControl.selected2": "Ausgewählt", "app.components.AssignVoteControl.voteForAtLeastOne": "Für mindestens 1 Option stimmen", "app.components.AssignVoteControl.votesSubmitted1": "Sie haben Ihre Stimme bereits abgegeben. Um sie zu ändern, klicken Sie auf \"Ändern Sie Ihren Beitrag\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Sie haben Ihre Stimme bereits abgegeben. Um sie zu ändern, gehen Sie zurück auf die Projektseite und klicken Sie auf \"Ändern Sie Ihren Beitrag\".", "app.components.AuthProviders.continue": "Fortfahren", "app.components.AuthProviders.continueWithAzure": "Weiter mit {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "<PERSON><PERSON> mit Facebook", "app.components.AuthProviders.continueWithFakeSSO": "<PERSON><PERSON> mit Fake SSO", "app.components.AuthProviders.continueWithGoogle": "<PERSON><PERSON> mit Google", "app.components.AuthProviders.continueWithHoplr": "<PERSON><PERSON> mit <PERSON>lr", "app.components.AuthProviders.continueWithIdAustria": "Weiter mit ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "<PERSON><PERSON> mit {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "<PERSON><PERSON> mit MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Es existiert bereits ein Konto mit dieser E-Mail-Adresse.{br}{br}Sie können nicht über FranceConnect auf die Plattform zugreifen, da die persönlichen Daten nicht übereinstimmen. Um sich über FranceConnect anzumelden, müssen Sie zunächst Ihren Vor- oder Nachnamen auf dieser Plattform ändern, damit er mit Ihren offiziellen Angaben übereinstimmt.{br}{br}Sie können sich wie gewohnt unten einloggen.", "app.components.AuthProviders.goToLogIn": "Sie haben bereits ein Konto? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Sie haben kein Konto? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Anmelden", "app.components.AuthProviders.logInWithEmail": "Anmelden mit E-Mail-Adresse und Passwort", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Sie müssen das angegebene Mindestalter erreicht haben, um verifiziert zu werden.", "app.components.AuthProviders.signUp2": "Registrieren", "app.components.AuthProviders.signUpButtonAltText": "Mit {loginMechanismName} einloggen", "app.components.AuthProviders.signUpWithEmail": "Registrierung mit E-Mail und Passwort", "app.components.AuthProviders.verificationRequired": "Verifizierung erforderlich", "app.components.Author.a11yPostedBy": "<PERSON><PERSON><PERSON><PERSON> von", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 teilnehmer} other {{numberOfParticipants} teilnehmende}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} <PERSON><PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.participant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AvatarBubbles.participants1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.cancel": "Abbrechen", "app.components.Comments.commentingDisabledInCurrentPhase": "In der gegenwärtigen Phase können keine Kommentare abgegeben werden.", "app.components.Comments.commentingDisabledInactiveProject": "Kommenta<PERSON> sind nicht möglich, da dieses Projekt derzeit nicht aktiv ist.", "app.components.Comments.commentingDisabledProject": "Die Kommentarfunktion in diesem Projekt ist derzeit deaktiviert.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink}, um kommentieren zu können.", "app.components.Comments.commentingMaybeNotPermitted": "Bitte {signInLink}, um zu sehen, welche Aktionen Sie vornehmen können.", "app.components.Comments.inputsAssociatedWithProfile": "Standardmäßig werden Ihre Beiträge mit Ihrem Profil verknüpft, es sei denn, <PERSON><PERSON> wählen diese Option.", "app.components.Comments.invisibleTitleComments": "Kommentare", "app.components.Comments.leastRecent": "Zuletzt", "app.components.Comments.likeComment": "Kommentar gefällt mir", "app.components.Comments.mostLiked": "Meiste Reaktionen", "app.components.Comments.mostRecent": "Zuletzt", "app.components.Comments.official": "Offiziell", "app.components.Comments.postAnonymously": "Anonym teilnehmen", "app.components.Comments.replyToComment": "<PERSON><PERSON>", "app.components.Comments.reportAsSpam": "Als Spam melden", "app.components.Comments.seeOriginal": "Original anzeigen", "app.components.Comments.seeTranslation": "Siehe Übersetzung", "app.components.Comments.yourComment": "<PERSON>hr Kommentar", "app.components.CommonGroundResults.divisiveDescription": "<PERSON><PERSON><PERSON>, bei denen Menschen gleichermaßen zustimmen und nicht zustimmen:", "app.components.CommonGroundResults.divisiveTitle": "Spaltend", "app.components.CommonGroundResults.majorityDescription": "Mehr als 60% der Befragten stimmten in der einen oder anderen Weise ab:", "app.components.CommonGroundResults.majorityTitle": "Mehrhe<PERSON>", "app.components.CommonGroundResults.participantLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantsLabel1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.statementLabel": "Aussage", "app.components.CommonGroundResults.statementsLabel1": "Aussagen", "app.components.CommonGroundResults.votesLabe": "Stimme", "app.components.CommonGroundResults.votesLabel1": "Stimmen", "app.components.CommonGroundStatements.agreeLabel": "<PERSON><PERSON><PERSON> zu", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON>imme nicht zu", "app.components.CommonGroundStatements.noMoreStatements": "Es liegen derzeit keine Aussagen vor, auf die geantwortet werden kann", "app.components.CommonGroundStatements.noResults": "Es gibt noch keine Ergebnisse zu sehen. <PERSON>te vergewissern <PERSON> sich, dass Sie an der Common Ground-Phase teilgenommen haben und schauen Sie danach wieder hier vorbei.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Ergebnisse", "app.components.CommonGroundTabs.statementsTabLabel": "Aussagen", "app.components.CommunityMonitorModal.formError": "<PERSON>s ist ein Fehler aufgetreten.", "app.components.CommunityMonitorModal.surveyDescription2": "Diese laufende Umfrage erfasst Ihre Meinung zur Verwaltung und öffentlichen Diensten.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Dauer <1 Minute} one {Dauer 1 Minute} other {Dauer # Minuten}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Es wurde eine E-Mail mit einem Bestätigungscode an {userEmail} gesendet.", "app.components.ConfirmationModal.changeYourEmail": "Ändern Sie Ihre E-Mail-Adresse.", "app.components.ConfirmationModal.codeInput": "Code", "app.components.ConfirmationModal.confirmationCodeSent": "Neuer Code gesendet", "app.components.ConfirmationModal.didntGetAnEmail": "Sie haben keine E-Mail erhalten?", "app.components.ConfirmationModal.foundYourCode": "Haben Sie Ihren Code gefunden?", "app.components.ConfirmationModal.goBack": "Zurückgehen.", "app.components.ConfirmationModal.sendEmailWithCode": "E-Mail mit Code senden", "app.components.ConfirmationModal.sendNewCode": "Neuen Code senden.", "app.components.ConfirmationModal.verifyAndContinue": "Überprüfen und fortfahren", "app.components.ConfirmationModal.wrongEmail": "Falsche E-Mail-Adresse?", "app.components.ConsentManager.Banner.accept": "Akzeptieren", "app.components.ConsentManager.Banner.ariaButtonClose2": "Ablehnen und Banner schließen", "app.components.ConsentManager.Banner.close": "Schließen", "app.components.ConsentManager.Banner.mainText": "Diese Webseite verwendet Cookies gemäß unserer {policyLink}.", "app.components.ConsentManager.Banner.manage": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.policyLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Werbung", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Wir verwenden dies, um Werbekampagnen auf unserer Webseite zu personalisieren und ihre Wirksamkeit zu messen. Wir zeigen keine Werbung auf dieser Plattform, aber die folgenden Dienste können Ihnen eine personalisierte Werbung auf der Grundlage der Seiten, die Sie auf unserer Webseite besuchen, anbieten.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analyse", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Analysecookies dienen dem Verständnis, wie die Plattform genutzt wird, um diese zu verbessern. Diese Informationen werden aggregiert für eine Gesamtanalyse und in keiner Weise zur Analyse des Verhaltens einzelner Personen verwendet.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Zurück", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Abbrechen", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON>cht zu<PERSON>en", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funktional", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Dies ist erford<PERSON>lich, um grundlegende Funktionen der Webseite zu aktivieren und zu überwachen. Einige der hier aufgeführten Werkzeuge treffen möglicherweise nicht auf Sie zu. Bitte lesen Sie unsere Cookie-Richtlinie für weitere Informationen.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "Um eine funktionierende Plattform zu haben, speichern wir ein Authentifizierungs-Cookie, wenn Si<PERSON> sich anmelden, und die Sprache, in der Sie diese Plattform nutzen.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Speichern", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON><PERSON><PERSON>-Einstellungen", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Werkzeug(e)", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Haftungsausschluss für das Hochladen von Inhalten", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Indem Sie Inhalte hochladen, er<PERSON><PERSON><PERSON><PERSON>, dass diese Inhalte keine Vorschriften oder Rechte Dritter verletzen, wie z.<PERSON><PERSON> Rechte an geistigem Eigentum, Datenschutzrechte, Rechte an Geschäftsgeheimnissen usw. Indem Sie diese Inhalte hochladen, verpflichten Si<PERSON> sich folglich, die volle und ausschließliche Haftung für alle direkten und indirekten Schäden zu übernehmen, die sich aus den hochgeladenen Inhalten ergeben. Darüber hinaus verpflichten Si<PERSON> sich, den Eigentümer der Plattform und Go Vocal von jeglichen Ansprüchen Dritter oder Verbindlichkeiten gegenüber Dritten und allen damit verbundenen Kosten freizustellen, die aus den von Ihnen hochgeladenen Inhalten entstehen oder resultieren würden.", "app.components.ContentUploadDisclaimer.onAccept": "Einverstanden", "app.components.ContentUploadDisclaimer.onCancel": "Abbrechen", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON><PERSON><PERSON> uns mehr", "app.components.CustomFieldsForm.addressInputAriaLabel": "Adresseingabe", "app.components.CustomFieldsForm.addressInputPlaceholder6": "<PERSON><PERSON><PERSON> e<PERSON>ben...", "app.components.CustomFieldsForm.adminFieldTooltip": "Feld nur für Admins sichtbar", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Alle Antworten auf diese Umfrage sind anonymisiert.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "<PERSON><PERSON><PERSON> ein Polygon sind mindestens drei Punkte erforderlich.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON><PERSON>r eine Linie sind mindestens zwei Punkte erforderlich.", "app.components.CustomFieldsForm.attachmentRequired": "Mindestens ein <PERSON>hang ist erford<PERSON>lich", "app.components.CustomFieldsForm.authorFieldLabel": "Autor*in", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Beginnen Sie mit der Eingabe, um nach einer E-Mail oder einem Namen des Nutzers / der Nutzerin zu suchen...", "app.components.CustomFieldsForm.back": "Zurück", "app.components.CustomFieldsForm.budgetFieldLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "<PERSON>licken Si<PERSON> zum Zeichnen auf die Karte. <PERSON><PERSON><PERSON> dann an den Punkten, um sie zu verschieben.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "<PERSON><PERSON><PERSON> Si<PERSON> auf die Karte oder geben Sie unten eine Adresse ein, um Ihre Antwort hinzuzufügen.", "app.components.CustomFieldsForm.confirm": "Bestätigen", "app.components.CustomFieldsForm.descriptionMinLength": "Die Beschreibung muss mindestens {min} <PERSON><PERSON><PERSON> lang sein.", "app.components.CustomFieldsForm.descriptionRequired": "Die Beschreibung ist erforderlich", "app.components.CustomFieldsForm.fieldMaximumItems": "Höchstens {maxSelections, plural, one {~ Option} other {~ Optionen}} können für das Feld \"{fieldName}\" ausgewählt werden.", "app.components.CustomFieldsForm.fieldMinimumItems": "Mindestens {minSelections, plural, one {# Option} other {# Optionen}} können für das Feld \"{fieldName}\" ausgewählt werden.", "app.components.CustomFieldsForm.fieldRequired": "<PERSON> Feld \"{fieldName}\" ist erforderlich", "app.components.CustomFieldsForm.fileSizeLimit": "Die Dateigröße ist auf {maxFileSize} MB begrenzt.", "app.components.CustomFieldsForm.imageRequired": "Das Bild ist erforderlich", "app.components.CustomFieldsForm.minimumCoordinates2": "Ein Minimum von {numPoints} Kartenpunkten ist erforderlich.", "app.components.CustomFieldsForm.notPublic1": "*Diese Antwort wird nur an die Projektmanager*innen und nicht an die Öffentlichkeit weitergegeben.", "app.components.CustomFieldsForm.otherArea": "<PERSON><PERSON>", "app.components.CustomFieldsForm.progressBarLabel": "Fort<PERSON><PERSON>t", "app.components.CustomFieldsForm.removeAnswer": "Antwort entfernen", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Beliebig viele auswählen", "app.components.CustomFieldsForm.selectBetween": "*Wählen Sie zwischen {minItems} und {maxItems} Optionen", "app.components.CustomFieldsForm.selectExactly2": "*Wählen Sie genau {selectExactly, plural, one {# Option} other {# Optionen}}", "app.components.CustomFieldsForm.selectMany": "*Beliebig viele auswählen", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "<PERSON><PERSON><PERSON> auf die Karte, um zu zeichnen. <PERSON><PERSON><PERSON> dann an den Punkten, um sie zu verschieben.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "<PERSON><PERSON><PERSON> auf die Karte, um zu zeichnen.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "<PERSON><PERSON><PERSON> auf die Karte, um Ihre Antwort hinzuzufügen.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "<PERSON><PERSON><PERSON> auf die Karte oder geben Sie unten eine Adresse ein, um Ihre Antwort hinzuzufügen.", "app.components.CustomFieldsForm.tapToAddALine": "<PERSON><PERSON><PERSON>, um eine Zeile hinzuzufügen", "app.components.CustomFieldsForm.tapToAddAPoint": "<PERSON><PERSON><PERSON>, um einen Punkt hinzuzufügen", "app.components.CustomFieldsForm.tapToAddAnArea": "<PERSON><PERSON><PERSON>, um einen Bereich hinzuzufügen", "app.components.CustomFieldsForm.titleMaxLength": "Der Titel darf höchstens {max} <PERSON><PERSON><PERSON> lang sein.", "app.components.CustomFieldsForm.titleMinLength": "Der Titel muss mindestens {min} <PERSON><PERSON><PERSON> lang sein.", "app.components.CustomFieldsForm.titleRequired": "Der Titel ist erforderlich", "app.components.CustomFieldsForm.topicRequired": "Mindestens ein Tag ist erforderlich", "app.components.CustomFieldsForm.typeYourAnswer": "Antwort eingeben", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Die Eingabe Ihrer Antwort ist erforderlich", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Laden Si<PERSON> eine Zip-Date<PERSON> hoch, die eine oder mehrere Shapefiles enthält.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "Wenn der Ort bei der Eingabe nicht unter den Optionen angezeigt wird, können Sie gültige Koordinaten im Format 'Breitengrad, Längengrad' hinzufügen, um einen genauen Ort anzugeben (z.B.: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Einige Felder waren ungültig. Bitte korrigiere die Fehler und versuche es erneut.", "app.components.ErrorBoundary.errorFormErrorGeneric": "<PERSON><PERSON> des Berichts, ist ein unbekannter Fehler aufgetreten. Bitte versuche es erneut.", "app.components.ErrorBoundary.errorFormLabelClose": "Schließen", "app.components.ErrorBoundary.errorFormLabelComments": "Was ist passiert?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-Mail", "app.components.ErrorBoundary.errorFormLabelName": "Name", "app.components.ErrorBoundary.errorFormLabelSubmit": "Einreichen", "app.components.ErrorBoundary.errorFormSubtitle": "Unser Team wurde benachrichtigt.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON>, dass wir <PERSON>hnen helfen, erz<PERSON><PERSON>en <PERSON> un<PERSON> unten, was passiert ist.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Die Rückmeldung wurde verschickt. Vielen Dank!", "app.components.ErrorBoundary.errorFormTitle": "Anscheinend haben wir Probleme.", "app.components.ErrorBoundary.genericErrorWithForm": "Ein Fehler ist aufgetreten und wir können diesen Inhalt nicht anzeigen. Bitte versuchen Sie es erneut oder {openForm}", "app.components.ErrorBoundary.openFormText": "helfen Si<PERSON> uns herauszufinden was passiert ist", "app.components.ErrorToast.budgetExceededError": "Ihr Budget reicht nicht aus", "app.components.ErrorToast.votesExceededError": "Sie haben nicht mehr genug Stimmen", "app.components.EventAttendanceButton.forwardToFriend": "Weiterleiten an Freund*innen", "app.components.EventAttendanceButton.maxRegistrationsReached": "Die maximale Anzahl von Anmeldungen für die Veranstaltung wurde erreicht. Es sind keine Plätze mehr frei.", "app.components.EventAttendanceButton.register": "Anmelden", "app.components.EventAttendanceButton.registered": "<PERSON><PERSON><PERSON>", "app.components.EventAttendanceButton.seeYouThere": "Wir sehen uns!", "app.components.EventAttendanceButton.seeYouThereName": "Wir sehen uns, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "<PERSON><PERSON> wurden weniger Veranstaltungsinformationen sichtbar.", "app.components.EventCard.a11y_moreContentVisible": "Weitere Veranstaltungsinformationen wurden sichtbar.", "app.components.EventCard.a11y_readMore": "Mehr über die Veranstaltung \"{eventTitle}\" lesen.", "app.components.EventCard.endsAt": "Endet um", "app.components.EventCard.readMore": "<PERSON><PERSON> er<PERSON>", "app.components.EventCard.showLess": "<PERSON><PERSON> anzeigen", "app.components.EventCard.showMore": "<PERSON><PERSON> anzeigen", "app.components.EventCard.startsAt": "Beginnt um", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Anstehende und laufende Veranstaltungen in diesem Projekt", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Anstehende und laufende Veranstaltungen in dieser Phase", "app.components.FileUploader.a11y_file": "Datei:", "app.components.FileUploader.a11y_filesToBeUploaded": "Hochzuladende Dateien: {Dateinamen}", "app.components.FileUploader.a11y_noFiles": "<PERSON><PERSON> hi<PERSON>.", "app.components.FileUploader.a11y_removeFile": "<PERSON><PERSON> en<PERSON>", "app.components.FileUploader.fileInputDescription": "<PERSON><PERSON><PERSON>, um eine Datei auszuwählen", "app.components.FileUploader.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.FileUploader.file_too_large2": "<PERSON><PERSON>, die größer sind als {maxSizeMb}MB, sind nicht erlaubt.", "app.components.FileUploader.incorrect_extension": "{fileName} wird von unserem System nicht unterstützt, es wird nicht hochgeladen.", "app.components.FilterBoxes.a11y_allFilterSelected": "Ausgewählter Statusfilter: alle", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# <PERSON><PERSON><PERSON>} other {# Beiträge}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON>", "app.components.FilterBoxes.a11y_selectedFilter": "Ausgewählter Statusfilter: {Filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Ausgewählt {numberOfSelectedTopics, plural, =0 {Keine Themenfilter} one {Ein Themenfilter} other {# Themenfilter}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Alle", "app.components.FilterBoxes.areas": "<PERSON><PERSON> Geb<PERSON> filtern", "app.components.FilterBoxes.inputs": "Beiträge", "app.components.FilterBoxes.noValuesFound": "<PERSON><PERSON> verfügbar.", "app.components.FilterBoxes.showLess": "<PERSON><PERSON> anzeigen", "app.components.FilterBoxes.showTagsWithNumber": "Alle anzeigen ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Themen-Tags", "app.components.FiltersModal.filters": "Filter", "app.components.FolderFolderCard.a11y_folderDescription": "Beschreibung des Ordners:", "app.components.FolderFolderCard.a11y_folderTitle": "Titel des Ordners:", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# Projekte} one {# Projekt} other {# Projekte}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Der Feldtyp kann nicht geändert werden, sobald es Einreichungen gibt.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "<PERSON><PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automatisch speichern", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Die automatische Speicherung ist standardmäßig aktiviert, wenn Sie den Formulareditor öffnen. <PERSON><PERSON>, wenn Sie das Feldeinstellungsfenster mit der Schaltfläche „X“ schließen, wird automatisch eine Speicherung ausgelöst.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "Quartal", "app.components.GanttChart.timeRange.timeRangeMultiyear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.today": "<PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBack": "Zurück", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Zurück zur vorherigen Seite", "app.components.HookForm.Feedback.errorTitle": "<PERSON><PERSON> gibt ein Problem", "app.components.HookForm.Feedback.submissionError": "Versuchen Sie es erneut. Wenn das Problem weiterhin besteht, kontaktieren Sie uns", "app.components.HookForm.Feedback.submissionErrorTitle": "Es gab ein Problem auf unserer Seite, entschuldigen Sie", "app.components.HookForm.Feedback.successMessage": "Änderungen erfolgreich vorgenommen", "app.components.HookForm.PasswordInput.passwordLabel": "Passwort", "app.components.HorizontalScroll.scrollLeftLabel": "Nach links scrollen.", "app.components.HorizontalScroll.scrollRightLabel": "Nach rechts scrollen.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} Ideen haben geladen.", "app.components.IdeaCards.filters": "Filter", "app.components.IdeaCards.filters.mostDiscussed": "<PERSON><PERSON>", "app.components.IdeaCards.filters.newest": "<PERSON>eu", "app.components.IdeaCards.filters.oldest": "Alt", "app.components.IdeaCards.filters.popular": "Meiste Reaktionen", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortBy": "Sortieren nach", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sortierung geändert zu: {currentSortType}", "app.components.IdeaCards.filters.trending": "Beliebt", "app.components.IdeaCards.showMore": "<PERSON><PERSON> anzeigen", "app.components.IdeasMap.a11y_hideIdeaCard": "Ideen-Karte ausblenden.", "app.components.IdeasMap.a11y_mapTitle": "Kartenübersicht", "app.components.IdeasMap.clickOnMapToAdd": "Auf die Karte klicken um etwas beizutragen", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Als Admin können Sie auf die Karte klicken, um Ihren Beitrag hinzuzufügen, auch wenn diese Phase nicht aktiv ist.", "app.components.IdeasMap.filters": "Filter", "app.components.IdeasMap.multipleInputsAtLocation": "Mehrere Beiträge an diesem Ort", "app.components.IdeasMap.noFilteredResults": "Die ausgewählten Filter ergeben keine Resultate", "app.components.IdeasMap.noResults": "<PERSON>ine Resultate gefunden", "app.components.IdeasMap.or": "oder", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, keine Abneig<PERSON>.} one {1 Abneigung.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, Gefällt mir nicht.} one {, 1 gefällt mir.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "melden Sie sich an", "app.components.IdeasMap.signUpLinkText": "registrieren Si<PERSON> sich", "app.components.IdeasMap.submitIdea2": "<PERSON><PERSON><PERSON>n", "app.components.IdeasMap.tapOnMapToAdd": "Auf die Karte klicken um etwas hinzuzufügen", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Als Admin können Sie auf die Karte tippen, um Ihren Beitrag hinzuzufügen, auch wenn diese Phase nicht aktiv ist.", "app.components.IdeasMap.userInputs2": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, keine <PERSON>} one {, 1 Kommentar} other {, # Kommentare}}", "app.components.IdeasShow.bodyTitle": "Beschreibung", "app.components.IdeasShow.deletePost": "Löschen", "app.components.IdeasShow.editPost": "<PERSON><PERSON><PERSON>", "app.components.IdeasShow.goBack": "Zurück", "app.components.IdeasShow.moreOptions": "Weitere Optionen", "app.components.IdeasShow.or": "oder", "app.components.IdeasShow.proposedBudgetTitle": "Vorgeschlagenes Budget", "app.components.IdeasShow.reportAsSpam": "Als Spam melden", "app.components.IdeasShow.send": "Einreichen", "app.components.IdeasShow.skipSharing": "Vergessen Sie es, ich werde es später tun", "app.components.IdeasShowPage.signIn2": "Anmelden", "app.components.IdeasShowPage.sorryNoAccess": "Um auf diese Seite zuzugreifen, melde Dich bitte an:", "app.components.LocationInput.noOptions": "<PERSON><PERSON>en", "app.components.Modal.closeWindow": "Fenster schließen", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "<PERSON><PERSON><PERSON> Si<PERSON> eine neue E-Mail-Adresse ein", "app.components.PageNotFound.goBackToHomePage": "Zurück zur Startseite", "app.components.PageNotFound.notFoundTitle": "Seite nicht gefunden", "app.components.PageNotFound.pageNotFoundDescription": "Die angeforderte Seite konnte nicht gefunden werden.", "app.components.PagesForm.descriptionMissingOneLanguageError": "<PERSON><PERSON>en Sie Inhalte für mindestens eine Sprache ein", "app.components.PagesForm.editContent": "Inhalt", "app.components.PagesForm.fileUploadLabel": "<PERSON><PERSON><PERSON><PERSON> (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Hinzugefügte Dateien werden am unteren Rand dieser Seite angezeigt.", "app.components.PagesForm.navbarItemTitle": "Name in der Navigationsleiste", "app.components.PagesForm.pageTitle": "Titel", "app.components.PagesForm.savePage": "Seite speichern", "app.components.PagesForm.saveSuccess": "Seite erfolgreich gespeichert.", "app.components.PagesForm.titleMissingOneLanguageError": "<PERSON><PERSON><PERSON> Si<PERSON> einen Titel für mindestens eine Sprache ein", "app.components.Pagination.back": "Vorherige Seite", "app.components.Pagination.next": "Nächste Seite", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "Sie haben {votesCast} verwen<PERSON>, was das Limit von {votesLimit} überschreitet. Bitte entfernen Sie einige Beiträge aus Ihrem Warenkorb und versuchen Sie es erneut.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "noch {budgetLeft} / {totalBudget} übrig", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "<PERSON>e müssen mindestens {votesMinimum} ausgeben, bevor <PERSON> Ihr Budget abschicken können.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "<PERSON>e müssen mindestens eine Option auswählen, bevor <PERSON> a<PERSON>cken können.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "<PERSON><PERSON> müssen etwas zu Ihrem Warenkorb hinzufügen, bevor <PERSON> ihn a<PERSON>chicken können.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON>} other {# out of {totalNumberOfVotes, plural, one {1 Guthaben} other {# G<PERSON>aben}} übrig}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {<PERSON>ine Punk<PERSON> übrig} other {# von {totalNumberOfVotes, plural, one {1 Punkt} other {# <PERSON><PERSON>}} übrig}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Keine Token übrig} other {# out of {totalNumberOfVotes, plural, one {1 Token} other {# Token}} übrig}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {<PERSON><PERSON>hr} other {# von {totalNumberOfVotes, plural, one {1 Stimme} other {# Stimmen}} übrig}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# Stimmen} one {# Stimme} other {# Stimmen}} abgegeben", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Sie haben {votesCast} Stimmen abgegeben, was das Limit von {votesLimit} überschreitet. Bitte entfernen Sie einige Stimmen und versuchen Sie es erneut.", "app.components.ParticipationCTABars.addInput": "<PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.allocateBudget": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Ihr Budget wurde erfolgreich eingereicht.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "<PERSON>zt mitmachen", "app.components.ParticipationCTABars.poll": "An Abstimmung teilnehmen", "app.components.ParticipationCTABars.reviewDocument": "Dokument kommentieren", "app.components.ParticipationCTABars.seeContributions": "Beiträge anzeigen", "app.components.ParticipationCTABars.seeEvents3": "Veranstaltungen anzeigen", "app.components.ParticipationCTABars.seeIdeas": "Ideen anschauen", "app.components.ParticipationCTABars.seeInitiatives": "Initiativen anzeigen", "app.components.ParticipationCTABars.seeIssues": "Probleme anzeigen", "app.components.ParticipationCTABars.seeOptions": "Optionen anzeigen", "app.components.ParticipationCTABars.seePetitions": "Petitionen anzeigen", "app.components.ParticipationCTABars.seeProjects": "Projekte anzeigen", "app.components.ParticipationCTABars.seeProposals": "Vorschläge anzeigen", "app.components.ParticipationCTABars.seeQuestions": "Fragen anzeigen", "app.components.ParticipationCTABars.submit": "Senden", "app.components.ParticipationCTABars.takeTheSurvey": "An Umfrage teilnehmen", "app.components.ParticipationCTABars.userHasParticipated": "Sie haben sich an diesem Projekt beteiligt.", "app.components.ParticipationCTABars.viewInputs": "<PERSON>itr<PERSON><PERSON> an<PERSON>", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "Stimme", "app.components.ParticipationCTABars.votesCounter.votes": "Stimmen", "app.components.PasswordInput.a11y_passwordHidden": "Passwort versteckt", "app.components.PasswordInput.a11y_passwordVisible": "Passwort sichtbar", "app.components.PasswordInput.a11y_strength1Password": "Schlechte Passwortstärke", "app.components.PasswordInput.a11y_strength2Password": "Schwache Passwortstärke", "app.components.PasswordInput.a11y_strength3Password": "Mittlere Passwortstärke", "app.components.PasswordInput.a11y_strength4Password": "Starke Passwortstärke", "app.components.PasswordInput.a11y_strength5Password": "Sehr starke Passwortstärke", "app.components.PasswordInput.hidePassword": "Passwort verstecken", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON> kurz (min. {minimumPasswordLength} <PERSON><PERSON><PERSON>)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON><PERSON><PERSON> Si<PERSON> ein Passwort zur Verfügung, das mindestens {minimumPasswordLength} <PERSON><PERSON><PERSON> lang ist", "app.components.PasswordInput.passwordEmptyError": "Geben Sie Ihr Passwort ein", "app.components.PasswordInput.passwordStrengthTooltip1": "So machen Sie Ihr Passwort stärker:", "app.components.PasswordInput.passwordStrengthTooltip2": "Verwenden Sie eine Kombination aus nicht aufeinanderfolgenden Kleinbuchstaben, Großbuchstaben, Ziffern, Sonderzeichen und Interpunktionszeichen ", "app.components.PasswordInput.passwordStrengthTooltip3": "Vermeiden Sie häufige oder leicht zu erratende Wörter", "app.components.PasswordInput.passwordStrengthTooltip4": "<PERSON><PERSON><PERSON><PERSON><PERSON> Sie die Länge", "app.components.PasswordInput.showPassword": "Passwort anzeigen", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength4Password": "<PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON> <PERSON>", "app.components.PostCardsComponents.list": "Liste", "app.components.PostCardsComponents.map": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Ein offizielles Update hinzufügen", "app.components.PostComponents.OfficialFeedback.cancel": "Abbrechen", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Löschen", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Sind <PERSON> sic<PERSON>, dass Sie dieses offizielle Update löschen möchten?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Letzte Bearbeitung am {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Letztes Update: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Offiziell", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, wie Ihr Name angezeigt werden soll", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Name des Autors/der Autorin des offiziellen Updates", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Textkörper des offiziellen Updates", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Offizielle Updates", "app.components.PostComponents.OfficialFeedback.postedOn": "Veröffentlicht am {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Veröffentlichen", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "<PERSON><PERSON><PERSON> Updates anzeigen", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Ein Update geben...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "Leider hat es ein Problem g<PERSON>ben", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Nachricht aktualisieren", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Das Update wurde erfolgreich veröffentlicht!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Unterstützen Sie meinen Beitrag „{postTitle}“ unter {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Unterstützen Sie meinen Beitrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Unterstützen Sie meinen Beitrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Unterstützen Sie meine Idee '{postTitle}' bei {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "<PERSON><PERSON> Idee unterstützen: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON><PERSON> Idee unterstützen: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Was hälst du von diesem Vorschlag? Stimme ab und teile die Diskussion unter {postUrl}, um deiner Stimme Gehör zu verschaffen!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Meinen Vorschlag unterstützen: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Meinen Vorschlag unterstützen: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Unter {postUrl} habe ich ein Problem „{postTitle}“ gepostet!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Ich habe gerade ein Problem gepostet: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Ich habe gerade ein Problem gepostet: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Unterstützen Sie meine vorgeschlagene Option „{postTitle}“ unter {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Unterstützen Sie meine vorgeschlagene Option: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Unterstützen Sie meine Option: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Unterstütze meine Petition '{postTitle}' auf {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON>tütze meine Petition: '{postTitle}'.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Unterstütze meine Petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Unterstützen Sie mein Projekt „{postTitle}“ unter {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Unterstützen Sie mein Projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Unterstützen Sie mein Projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Unterstütze meinen Vorschlag '{postTitle}' auf {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Unterstütze meinen Vorschlag: '{postTitle}'.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Ich habe gerade einen Vorschlag für {orgName} veröffentlicht: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Beteiligen Sie sich an der Diskussion über die Frage „{postTitle}“ unter {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Nehmen Sie an der Diskussion teil: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Nehmen Sie an der Diskussion teil: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "<PERSON><PERSON><PERSON> {postTitle} auf", "app.components.PostComponents.linkToHomePage": "Link zur Startseite", "app.components.PostComponents.readMore": "Weiterlesen …", "app.components.PostComponents.topics": "Themen", "app.components.ProjectArchivedIndicator.archivedProject": "Leider können Sie in diesem Projekt nicht mehr teilnehmen, weil es bereits archiviert wurde", "app.components.ProjectArchivedIndicator.previewProject": "Projektentwurf:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Sichtbar nur für Projektmanager*innen und diejenigen mit einem Vorschaulink.", "app.components.ProjectCard.a11y_projectDescription": "Projektbeschreibung:", "app.components.ProjectCard.a11y_projectTitle": "Projekttitel:", "app.components.ProjectCard.addYourOption": "Option hinzufügen", "app.components.ProjectCard.allocateYourBudget": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "Kommentar", "app.components.ProjectCard.contributeYourInput": "<PERSON>it<PERSON> e<PERSON>re<PERSON>n", "app.components.ProjectCard.finished": "<PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Fragen stellen", "app.components.ProjectCard.learnMore": "<PERSON><PERSON> er<PERSON>", "app.components.ProjectCard.reaction": "Reaktion", "app.components.ProjectCard.readTheReport": "<PERSON><PERSON><PERSON> lesen", "app.components.ProjectCard.reviewDocument": "Dokument kommentieren", "app.components.ProjectCard.submitAnIssue": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>ichen", "app.components.ProjectCard.submitYourIdea": "I<PERSON><PERSON> e<PERSON><PERSON>ichen", "app.components.ProjectCard.submitYourInitiative": "Initiative einreichen", "app.components.ProjectCard.submitYourPetition": "Petition einreichen", "app.components.ProjectCard.submitYourProject": "Projekt einreichen", "app.components.ProjectCard.submitYourProposal": "Vorschlag einreichen", "app.components.ProjectCard.takeThePoll": "An Umfrage teilnehmen", "app.components.ProjectCard.takeTheSurvey": "An Umfrage teilnehmen", "app.components.ProjectCard.viewTheContributions": "<PERSON>itr<PERSON><PERSON> an<PERSON>", "app.components.ProjectCard.viewTheIdeas": "Ideen anzeigen", "app.components.ProjectCard.viewTheInitiatives": "<PERSON><PERSON>", "app.components.ProjectCard.viewTheIssues": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheOptions": "<PERSON><PERSON> sich die Optionen an", "app.components.ProjectCard.viewThePetitions": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "Proje<PERSON><PERSON> an<PERSON><PERSON>", "app.components.ProjectCard.viewTheProposals": "Vorschläge an<PERSON>hen", "app.components.ProjectCard.viewTheQuestions": "<PERSON><PERSON><PERSON> an<PERSON>", "app.components.ProjectCard.vote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# Vorhaben} other {# Vorhaben}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# <PERSON><PERSON><PERSON>} other {# Beiträge}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {keine Ideen} one {# Idee} other {# Ideen}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# Initiativen} one {# Initiative} other {# Initiativen}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# Vorhaben} other {# Vorhaben}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# Option} other {# Optionen}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {~ Petitionen} one {~ Petition} other {~ Petitionen}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# Projekt} other {# Projekte}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# Vorschläge} one {# Vorschlag} other {# Vorschläge}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# <PERSON><PERSON>} other {# <PERSON><PERSON><PERSON>}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# Kommentare} one {# Kommentar} other {# Kommentare}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# Beiträge} one {# Beitrag} other {# Beiträge}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projekte} one {# projekt} other {# projekte}}", "app.components.ProjectFolderCards.components.Topbar.all": "Alle", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filtern nach", "app.components.ProjectFolderCards.components.Topbar.published2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Themen", "app.components.ProjectFolderCards.noProjectYet": "<PERSON>s gibt noch kein Projekt", "app.components.ProjectFolderCards.noProjectsAvailable": "<PERSON>ine Projekte verfügbar", "app.components.ProjectFolderCards.showMore": "<PERSON><PERSON> anzeigen", "app.components.ProjectFolderCards.stayTuned": "Sc<PERSON><PERSON> Sie bald wieder vorbei für neue Beteiligungsmöglichkeiten", "app.components.ProjectFolderCards.tryChangingFilters": "Versuchen Sie, die ausgewählten Filter zu ändern.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Auch in diesen Städten im Einsatz", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON>", "app.components.QuillEditor.alignCenter": "Text zentrieren", "app.components.QuillEditor.alignLeft": "<PERSON><PERSON> au<PERSON>", "app.components.QuillEditor.alignRight": "Rechts ausrichten", "app.components.QuillEditor.bold": "<PERSON><PERSON>", "app.components.QuillEditor.clean": "Formatierung aufheben", "app.components.QuillEditor.customLink": "<PERSON><PERSON> hi<PERSON>", "app.components.QuillEditor.customLinkPrompt": "<PERSON><PERSON>:", "app.components.QuillEditor.edit": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.image": "Bild hochladen", "app.components.QuillEditor.imageAltPlaceholder": "Kurzbeschreibung des Bildes", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "<PERSON>", "app.components.QuillEditor.linkPrompt": "<PERSON><PERSON>:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Geordnete Liste", "app.components.QuillEditor.remove": "Entfernen", "app.components.QuillEditor.save": "Speichern", "app.components.QuillEditor.subtitle": "Untertitel", "app.components.QuillEditor.title": "Titel", "app.components.QuillEditor.unorderedList": "Ungeordnete Liste", "app.components.QuillEditor.video": "Video hinzufügen", "app.components.QuillEditor.videoPrompt": "<PERSON>üge ein Video hinzu:", "app.components.QuillEditor.visitPrompt": "Besuche den Link:", "app.components.ReactionControl.completeProfileToReact": "Vervollständigen Sie Ihr Profil, um zu reagieren", "app.components.ReactionControl.dislike": "G<PERSON>ä<PERSON><PERSON> mir nicht", "app.components.ReactionControl.dislikingDisabledMaxReached": "Sie haben die maximale An<PERSON>hl von \"<PERSON>efällt mir nicht\" in {projectName} erreicht", "app.components.ReactionControl.like": "Gefällt mir", "app.components.ReactionControl.likingDisabledMaxReached": "Sie haben Ihre maximale <PERSON> von \"Gefällt mir\" in {projectName} erreicht", "app.components.ReactionControl.reactingDisabledFutureEnabled": "<PERSON><PERSON><PERSON> diese Phase beginnt, wird die Beteiligung aktiviert", "app.components.ReactionControl.reactingDisabledPhaseOver": "In dieser Phase ist es nicht mehr möglich sich zu beteiligen", "app.components.ReactionControl.reactingDisabledProjectInactive": "<PERSON>e können nicht mehr auf Ideen in {projectName} reagieren", "app.components.ReactionControl.reactingNotEnabled": "Die Beteiligung ist für dieses Projekt derzeit nicht aktiviert", "app.components.ReactionControl.reactingNotPermitted": "Beteiligung ist nur für bestimmte Gruppen aktiviert", "app.components.ReactionControl.reactingNotSignedIn": "Melden Sie sich an, um sich zu beteiligen.", "app.components.ReactionControl.reactingPossibleLater": "Die Beteiligung beginnt am {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verifizieren Sie Ihre Identität, um sich beteiligen zu können.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Veranstaltungsdatum: {startDate} um {startTime} bis {endDate} um {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Veranstaltungsdatum: {eventDate} von {startTime} bis {endTime}.", "app.components.Sharing.linkCopied": "<PERSON>", "app.components.Sharing.or": "oder", "app.components.Sharing.share": "Teilen", "app.components.Sharing.shareByEmail": "<PERSON> <PERSON><PERSON> teilen", "app.components.Sharing.shareByLink": "<PERSON>", "app.components.Sharing.shareOnFacebook": "Auf Facebook teilen", "app.components.Sharing.shareOnTwitter": "<PERSON>f Twitter teilen", "app.components.Sharing.shareThisEvent": "Veranstaltung teilen", "app.components.Sharing.shareThisFolder": "Freigeben", "app.components.Sharing.shareThisProject": "Dieses Projekt teilen", "app.components.Sharing.shareViaMessenger": "<PERSON><PERSON> <PERSON>", "app.components.Sharing.shareViaWhatsApp": "<PERSON><PERSON> teilen", "app.components.SideModal.closeButtonAria": "Schließen", "app.components.StatusModule.futurePhase": "Sie befinden sich in einer Phase, die noch nicht begonnen hat. Sie werden teilnehmen können, wenn die Phase beginnt.", "app.components.StatusModule.modifyYourSubmission1": "Einreichung ändern", "app.components.StatusModule.submittedUntil3": "Ihre Stimme kann abgegeben werden bis zum", "app.components.TopicsPicker.numberOfSelectedTopics": "Ausgewählt {numberOfSelectedTopics, plural, =0 {keine <PERSON>} one {ein Tag} other {# Tags}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Bild vergrößern", "app.components.UI.MoreActionsMenu.moreOptions": "Weitere Optionen", "app.components.UI.MoreActionsMenu.showMoreActions": "Weitere Aktionen anzeigen", "app.components.UI.PhaseFilter.noAppropriatePhases": "<PERSON>ine geeigneten Phasen für dieses Projekt gefunden", "app.components.UI.RemoveImageButton.a11y_removeImage": "Entfernen", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Übersetzen", "app.components.Unauthorized.additionalInformationRequired": "<PERSON>ür die Teilnahme sind zusätzliche Informationen erforderlich.", "app.components.Unauthorized.completeProfile": "Profil vervollständigen", "app.components.Unauthorized.completeProfileTitle": "Vervollständigen Sie Ihr Profil, um teilzunehmen", "app.components.Unauthorized.noPermission": "<PERSON><PERSON><PERSON><PERSON>, dass Du da bist!", "app.components.Unauthorized.notAuthorized": "<PERSON>e sind leider nicht berechtigt, auf diese Seite zuzugreifen.", "app.components.Upload.errorImageMaxSizeExceeded": "Das gewählte Bild ist größer als {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "Eins oder mehrere der ausgewählten Bilder überschreitet die maximal zulässige Größe von {maxFileSize} Mb pro Bild", "app.components.Upload.onlyOneImage": "Es kann nur 1 Bild hochgeladen werden", "app.components.Upload.onlyXImages": "Es kann nur {maxItemsCount} Bilder hochgel<PERSON><PERSON> werden", "app.components.Upload.remaining": "restliche", "app.components.Upload.uploadImageLabel": "Bild auswählen (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Ein oder mehrere Bilder auswählen", "app.components.UpsellTooltip.tooltipContent": "Diese Funktion ist in Ihrer aktuellen Lizenz nicht enthalten. Sprechen Sie mit Ihrer Kundenbetreuerin oder Ihrem Admin, um sie freizuschalten.", "app.components.UserName.anonymous": "Anonym", "app.components.UserName.anonymousTooltip2": "Diese*r <PERSON><PERSON>erin hat sich entschieden, seinen*ihren Beitrag zu anonymisieren", "app.components.UserName.authorWithNoNameTooltip": "Ihr Name wurde automatisch generiert, da <PERSON><PERSON> Ihren Namen nicht eingegeben haben. Bitte aktualisieren Sie Ihr Profil, wenn Sie ihn ändern möchten.", "app.components.UserName.deletedUser": "unbekannte Person", "app.components.UserName.verified": "Verifiziert", "app.components.VerificationModal.verifyAuth0": "Überprüfen Sie mit NemID", "app.components.VerificationModal.verifyBOSA": "Mit itsme oder eID verifizieren", "app.components.VerificationModal.verifyBosaFas": "Mit itsme oder eID verifizieren", "app.components.VerificationModal.verifyClaveUnica": "Mit Clave Unica verifizieren", "app.components.VerificationModal.verifyFakeSSO": "Überprüfen Sie mit Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Verifizierung mit ID Austria", "app.components.VerificationModal.verifyKeycloak": "Verifizierung mit ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Überprüfen Sie mit MitID", "app.components.VerificationModal.verifyTwoday2": "Überprüfen Sie mit BankID oder Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Werden Sie verifizierte:r <PERSON><PERSON>rger:in", "app.components.VoteControl.budgetingFutureEnabled": "<PERSON>e können Ihr Budget ab {enabledFromDate} zu<PERSON>sen.\n", "app.components.VoteControl.budgetingNotPermitted": "Ein Bürgerhaushalt ist derzeit nicht möglich.", "app.components.VoteControl.budgetingNotPossible": "Derzeit können keine Änderungen an Ihrem Budget vorgenommen werden.\n", "app.components.VoteControl.budgetingNotVerified": "Bitte {verifyAccountLink}, um fortzufahren.\n", "app.components.VoteInputs._shared.currencyLeft1": "Sie haben {budgetLeft} / {totalBudget} übrig", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Sie haben {votesLeft, plural, =0 {keine <PERSON>hr} other {# von {totalNumberOfVotes, plural, one {1 Guthaben} other {# <PERSON><PERSON>aben}} übrig}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Sie haben {votesLeft, plural, =0 {keine <PERSON> mehr} other {# von {totalNumberOfVotes, plural, one {1 <PERSON>t} other {# <PERSON>te}} übrig}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Sie haben {votesLeft, plural, =0 {keine Token übrig} other {# out of {totalNumberOfVotes, plural, one {1 Token} other {# Token}} übrig}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Sie haben {votesLeft, plural, =0 {keine Stimmen übrig} other {# von {totalNumberOfVotes, plural, one {1 Stimme} other {# Stimmen}} übrig}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Sie haben Ihr Budget bereits eingereicht. Um es zu ändern, klicken <PERSON><PERSON> auf \"Ihren Beitrag ändern\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Sie haben Ihr Budget bereits eingereicht. Um es zu ändern, gehen Sie zurück zur Projektseite und klicken Sie auf \"Ihren Beitrag ändern\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgetierung ist nicht möglich, da diese Phase nicht aktiv ist.", "app.components.VoteInputs.single.youHaveVotedForX2": "Sie haben abgestimmt für {votes, plural, =0 {# Optionen} one {# Option} other {# Optionen}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON> bedeutet, dass Sie alle mit diesem Beitrag verbundenen Daten, wie Kommentare, Reaktionen und Abstimmungen, verlieren. Diese Aktion kann nicht rückgängig gemacht werden.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Möchten Sie diesen Beitrag wirklich löschen?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Abbrechen", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Bestätigen", "app.components.admin.SlugInput.resultingURL": "Resultierende URL", "app.components.admin.SlugInput.slugTooltip": "Der Slug ist die eindeutige Wortfolge am Ende der Webadresse oder URL einer Seite.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON>n Sie die URL ändern, werden die Links, die die alte URL nutzen, nicht länger funktionieren.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Eine Bedingung hinzufügen", "app.components.admin.UserFilterConditions.field_email": "E-Mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Veranstaltungsanmeldungen", "app.components.admin.UserFilterConditions.field_follow": "Folgen", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON><PERSON><PERSON> in ", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Gemeinschaftspuls-Umfrage", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Hat mit einem Beitrag mit Status interagiert", "app.components.admin.UserFilterConditions.field_participated_in_project": "Zum Projekt beigetragen", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Zum Thema beigetragen", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Datum der Registrierung", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Verifizierung", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideenfindung", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Vorschläge", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "ist für keine dieser Veranstaltungen angemeldet", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "ist für keine Veranstaltung angemeldet", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "ist für eine dieser Veranstaltungen angemeldet", "app.components.admin.UserFilterConditions.predicate_attends_something": "ist für mindestens eine Veranstaltung angemeldet", "app.components.admin.UserFilterConditions.predicate_begins_with": "beginnt mit", "app.components.admin.UserFilterConditions.predicate_commented_in": "hat kommentiert", "app.components.admin.UserFilterConditions.predicate_contains": "<PERSON>th<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_ends_on": "endet auf", "app.components.admin.UserFilterConditions.predicate_has_value": "hat Wert", "app.components.admin.UserFilterConditions.predicate_in": "hat eine Aktion durchgeführt", "app.components.admin.UserFilterConditions.predicate_is": "ist", "app.components.admin.UserFilterConditions.predicate_is_admin": "ist ein Admin", "app.components.admin.UserFilterConditions.predicate_is_after": "ist nach", "app.components.admin.UserFilterConditions.predicate_is_before": "ist vor", "app.components.admin.UserFilterConditions.predicate_is_checked": "ist geprüft", "app.components.admin.UserFilterConditions.predicate_is_empty": "ist leer", "app.components.admin.UserFilterConditions.predicate_is_equal": "ist", "app.components.admin.UserFilterConditions.predicate_is_exactly": "ist genau", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "ist größer als", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "ist größer als oder gleich wie", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "ist ein*e normale*r <PERSON><PERSON>*in", "app.components.admin.UserFilterConditions.predicate_is_not_area": "schließt Gebiet aus", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "schließt Ordner aus", "app.components.admin.UserFilterConditions.predicate_is_not_input": "schließt Beitrag aus", "app.components.admin.UserFilterConditions.predicate_is_not_project": "schließt Projekt aus", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "schließt Thema aus", "app.components.admin.UserFilterConditions.predicate_is_one_of": "ist eine von", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "eines der Gebiete", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "einen der Ordner", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "einen der Beiträge", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "eines der Projekte", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "eines der Themen", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "ist ein*e Projektmanager*in", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "ist kleiner als", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "ist kleiner als oder gleich wie", "app.components.admin.UserFilterConditions.predicate_is_verified": "ist verifiziert", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "beginnt nicht mit", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "hat nicht kommentiert", "app.components.admin.UserFilterConditions.predicate_not_contains": "enthält nicht", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "endet nicht auf", "app.components.admin.UserFilterConditions.predicate_not_has_value": "hat keinen Wert", "app.components.admin.UserFilterConditions.predicate_not_in": "hat keine Aktion durchgeführt", "app.components.admin.UserFilterConditions.predicate_not_is": "ist nicht", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "ist kein <PERSON>min", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "ist ungeprüft", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "ist nicht leer", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "ist nicht", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "ist kein*e normale*r <PERSON><PERSON>*in", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "ist nicht einer von", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "ist kein*e Projektmanager*in", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "ist nicht verifiziert", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "hat keinen Beitrag verfasst", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "hat nicht auf den Kommentar reagiert", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "hat nicht auf den Beitrag reagiert", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "hat sich nicht zu einer Veranstaltung angemeldet", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "hat nicht an der Umfrage teilgenommen", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "hat sich nicht als Teilnehmende gemeldet", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "hat nicht an der Abstimmung teilgenommen", "app.components.admin.UserFilterConditions.predicate_nothing": "nichts", "app.components.admin.UserFilterConditions.predicate_posted_input": "hat einen Beitrag gepostet", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "hat auf Kommentar reagiert", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "hat auf Beitrag reagiert", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "hat sich für eine Veranstaltung angemeldet", "app.components.admin.UserFilterConditions.predicate_something": "etwas", "app.components.admin.UserFilterConditions.predicate_taken_survey": "hat an der Umfrage teilgenommen", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "hat sich als Teilnehmende gemeldet", "app.components.admin.UserFilterConditions.predicate_voted_in3": "hat an der Abstimmung teilgenommen", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attribut", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Bedingung", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Wert", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Sie erhalten keine Benachrichtigungen zu Ihrem Beitrag", "app.components.anonymousParticipationModal.cancel": "Abbrechen", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "Anonym teilnehmen", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON>it wird <b>Ihr Profil</b> v<PERSON> <PERSON>, Projektmanager*innen und anderen Nutzer*innen für diesen speziellen Beitrag sicher verborgen, sodass niemand diesen Beitrag mit Ihnen in Verbindung bringen kann. Anonyme Beiträge können nicht bearbeitet werden und gelten als endgültig.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Die Sicherheit unserer Plattform für alle Nutzer*innen hat für uns höchste Priorität. Worte sind wichtig, also seien Sie bitte freundlich zu<PERSON>ander.", "app.components.avatar.titleForAccessibility": "<PERSON><PERSON> <PERSON> {fullName}", "app.components.customFields.mapInput.removeAnswer": "Antwort entfernen", "app.components.customFields.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "Letzten Punkt rückgängig machen", "app.components.followUnfollow.follow": "Folgen", "app.components.followUnfollow.followADiscussion": "Diskussion folgen", "app.components.followUnfollow.followTooltipInputPage2": "Sie werden per E-Mail über Statusänderungen, offizielle Updates und Kommentare informiert. Sie können diese E-Mails jederzeit hier {unsubscribeLink}.", "app.components.followUnfollow.followTooltipProjects2": "Nachfolgend erhalten Sie E-Mail-Updates über Projektänderungen. Sie können diese jederzeit hier {unsubscribeLink}.", "app.components.followUnfollow.unFollow": "Nicht mehr folgen", "app.components.followUnfollow.unsubscribe": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.unsubscribeUrl": "/profil/bearbeiten", "app.components.form.ErrorDisplay.guidelinesLinkText": "unsere Richtlinien", "app.components.form.ErrorDisplay.next": "<PERSON><PERSON>", "app.components.form.ErrorDisplay.previous": "Zurück", "app.components.form.ErrorDisplay.save": "Speichern", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Beginnen Sie mit der Eingabe, um nach einer E-Mail oder einem Namen des Nutzers / der Nutzerin zu suchen...", "app.components.form.anonymousSurveyMessage2": "Alle Antworten auf diese Umfrage sind anonymisiert.", "app.components.form.backToInputManager": "Zum Beitragsmanager", "app.components.form.backToProject": "Zurück zum Projekt", "app.components.form.components.controls.mapInput.removeAnswer": "Antwort entfernen", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "Letzten Punkt rückgängig machen", "app.components.form.controls.addressInputAriaLabel": "Adresseingabe", "app.components.form.controls.addressInputPlaceholder6": "<PERSON><PERSON><PERSON> e<PERSON>ben...", "app.components.form.controls.adminFieldTooltip": "Feld nur für Admins sichtbar", "app.components.form.controls.allStatementsError": "<PERSON>ür alle Aussagen muss eine Antwort ausgewählt werden.", "app.components.form.controls.back": "Zurück", "app.components.form.controls.clearAll": "Alle löschen", "app.components.form.controls.clearAllScreenreader": "Löschen Sie alle Antworten aus der obigen Matrixfrage", "app.components.form.controls.clickOnMapMultipleToAdd3": "<PERSON>licken Si<PERSON> zum Zeichnen auf die Karte. <PERSON><PERSON><PERSON> dann an den Punkten, um sie zu verschieben.", "app.components.form.controls.clickOnMapToAddOrType": "<PERSON><PERSON><PERSON> Si<PERSON> auf die Karte oder geben Sie unten eine Adresse ein, um Ihre Antwort hinzuzufügen.", "app.components.form.controls.confirm": "Bestätigen", "app.components.form.controls.cosponsorsPlaceholder": "<PERSON>en Namen e<PERSON>ben, um die Suche zu starten", "app.components.form.controls.currentRank": "Aktuelle Reihenfolge:", "app.components.form.controls.minimumCoordinates2": "Ein Minimum von {numPoints} Kartenpunkten ist erforderlich.", "app.components.form.controls.noRankSelected": "<PERSON><PERSON> Reihenfolge ausgewählt", "app.components.form.controls.notPublic1": "*Diese Antwort wird nur an die Projektmanager*innen und nicht an die Öffentlichkeit weitergegeben.", "app.components.form.controls.optionalParentheses": "(optional)", "app.components.form.controls.rankingInstructions": "Optionen per Drag & Drop priorisieren.", "app.components.form.controls.selectAsManyAsYouLike": "*Beliebig viele auswählen", "app.components.form.controls.selectBetween": "*Wählen Sie zwischen {minItems} und {maxItems} Optionen", "app.components.form.controls.selectExactly2": "*Wählen Sie genau {selectExactly, plural, one {# Option} other {# Optionen}}", "app.components.form.controls.selectMany": "*Beliebig viele auswählen", "app.components.form.controls.tapOnFullscreenMapToAdd4": "<PERSON><PERSON><PERSON> auf die Karte, um zu zeichnen. <PERSON><PERSON><PERSON> dann an den Punkten, um sie zu verschieben.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "<PERSON><PERSON><PERSON> auf die Karte, um zu zeichnen.", "app.components.form.controls.tapOnMapMultipleToAdd3": "<PERSON><PERSON><PERSON> auf die Karte, um Ihre Antwort hinzuzufügen.", "app.components.form.controls.tapOnMapToAddOrType": "<PERSON><PERSON><PERSON> auf die Karte oder geben Sie unten eine Adresse ein, um Ihre Antwort hinzuzufügen.", "app.components.form.controls.tapToAddALine": "<PERSON><PERSON><PERSON>, um eine Zeile hinzuzufügen", "app.components.form.controls.tapToAddAPoint": "<PERSON><PERSON><PERSON>, um einen Punkt hinzuzufügen", "app.components.form.controls.tapToAddAnArea": "<PERSON><PERSON><PERSON>, um einen Bereich hinzuzufügen", "app.components.form.controls.uploadShapefileInstructions": "* Laden Si<PERSON> eine Zip-Date<PERSON> hoch, die eine oder mehrere Shapefiles enthält.", "app.components.form.controls.validCordinatesTooltip2": "Wenn der Ort bei der Eingabe nicht unter den Optionen angezeigt wird, können Sie gültige Koordinaten im Format 'Breitengrad, Längengrad' hinzufügen, um einen genauen Ort anzugeben (z.B.: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} von {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} von {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} von {total}, wobei {maxValue} {maxLabel} ist", "app.components.form.error": "<PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "<PERSON> von Google Maps zur Verfügung gestellte Standort-Feld konnte nicht geladen werden.", "app.components.form.progressBarLabel": "Umfrage-Fortschritt", "app.components.form.submit": "Einreichen", "app.components.form.submitApiError": "Es gab ein Problem beim Absenden des Formulars. Bitte prüfe auf Fehler und versuche es erneut.", "app.components.form.verifiedBlocked": "<PERSON><PERSON> können dieses Feld nicht bearbeiten, da es verifizierte Informationen enthält", "app.components.formBuilder.Page": "Seite", "app.components.formBuilder.accessibilityStatement": "Richtlinie zur Barrierefreiheit", "app.components.formBuilder.addAnswer": "Antwort hinzufügen", "app.components.formBuilder.addStatement": "Aussage hinzufügen", "app.components.formBuilder.agree": "<PERSON><PERSON><PERSON> eher zu", "app.components.formBuilder.ai1": "KI", "app.components.formBuilder.aiUpsellText1": "<PERSON>n Sie Zugang zu unserem KI-<PERSON><PERSON> haben, können Sie Textantworten mit KI zusammenfassen und kategorisieren", "app.components.formBuilder.askFollowUpToggleLabel": "Nachfragen", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Button-Label", "app.components.formBuilder.buttonLink": "Button-Link", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Abbrechen", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> mehrere", "app.components.formBuilder.chooseOne": "Wählen Sie eine Option", "app.components.formBuilder.close": "Schließen", "app.components.formBuilder.closed": "<PERSON><PERSON> g<PERSON>", "app.components.formBuilder.configureMap": "<PERSON><PERSON> konfigurieren", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON><PERSON>, zurück ohne zu speichern", "app.components.formBuilder.content": "Inhalt", "app.components.formBuilder.continuePageLabel": "<PERSON><PERSON> zu", "app.components.formBuilder.cosponsors": "Unterstützer*innen", "app.components.formBuilder.default": "Standard", "app.components.formBuilder.defaultContent": "Standard-Felder", "app.components.formBuilder.delete": "Löschen", "app.components.formBuilder.deleteButtonLabel": "Löschen", "app.components.formBuilder.description": "Beschreibung", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Dieser wurde bereits in das Formular eingefügt. Der Standardinhalt darf nur einmal verwendet werden.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Das Hinzufügen von benutzerdefinierten Inhalten ist nicht Bestandteil Ihrer aktuellen Lizenz. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.components.formBuilder.disagree": "<PERSON>imme eher nicht zu", "app.components.formBuilder.displayAsDropdown": "Als Dropdown anzeigen", "app.components.formBuilder.displayAsDropdownTooltip": "Zeigen Sie die Optionen in einem Dropdown-Menü an. Wenn Sie viele Optionen haben, ist dies empfehlenswert.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "Fläche zeichnen", "app.components.formBuilder.drawRoute": "<PERSON><PERSON>", "app.components.formBuilder.dropPin": "<PERSON><PERSON> setzen", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Geben Sie mindestens 1 Antwort. Bitte beachten Sie, dass jede Antwort einen Titel haben muss.", "app.components.formBuilder.emptyOptionError": "Geben Sie mindestens 1 Antwortmöglichkeit an", "app.components.formBuilder.emptyStatementError": "Mindestens 1 Anweisung angeben", "app.components.formBuilder.emptyTitleError": "Geben Si<PERSON> einen Fragentitel an", "app.components.formBuilder.emptyTitleMessage": "Geben Sie einen Titel für alle Antworten an", "app.components.formBuilder.emptyTitleStatementMessage": "Geben Sie einen Titel für alle Aussagen an", "app.components.formBuilder.enable": "Aktivieren", "app.components.formBuilder.errorMessage": "<PERSON>s gibt ein Problem. Bitte beheben Sie das Problem, damit Sie Ihre Änderungen speichern können", "app.components.formBuilder.fieldGroup.description": "Beschreibung (optional)", "app.components.formBuilder.fieldGroup.title": "Titel (optional)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Derzeit sind die Antworten auf diese Fragen nur in der exportierten Excel-Datei im Beitragsmanager verfügbar und für Nutzer*innen nicht sichtbar.", "app.components.formBuilder.fieldLabel": "Antwortmöglichkeiten", "app.components.formBuilder.fieldLabelStatement": "Aussagen", "app.components.formBuilder.fileUpload": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Kartenbasierte Seite", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Binden Sie die Karte als Kontext ein oder stellen Sie den Teilnehmenden ortsbezogene Fragen.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "Um eine optimale Benutzerfreundlichkeit zu gewährleisten, raten wir davon ab, auf kartenbasierten Seiten Fragen zu Punkten, Routen oder Gebieten zu stellen.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normale Seite", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Die Vermessungsfunktionen sind nicht in Ihrer aktuellen Lizenz enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr zu erfahren.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Seitentyp", "app.components.formBuilder.formEnd": "<PERSON><PERSON> <PERSON>", "app.components.formBuilder.formField.cancelDeleteButtonText": "Abbrechen", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "<PERSON><PERSON>, Seite löschen", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.delete": "Löschen", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Wenn Sie diese Seite löschen, wird auch die damit verbundene Logik gelöscht. Sind <PERSON> sicher, dass Sie sie löschen wollen?", "app.components.formBuilder.formField.deleteResultsInfo": "Dies kann nicht rückgängig gemacht werden", "app.components.formBuilder.goToPageInputLabel": "Die nächste Seite ist dann:", "app.components.formBuilder.good": "Gut", "app.components.formBuilder.helmetTitle": "Eingabeformular erstellen", "app.components.formBuilder.imageFileUpload": "Bild-Upload", "app.components.formBuilder.invalidLogicBadgeMessage": "Ungültige Logik", "app.components.formBuilder.labels2": "Be<PERSON>ichnungen (optional)", "app.components.formBuilder.labelsTooltipContent2": "Wählen Sie optionale Beschriftungen für jeden der linearen Skalenwerte.", "app.components.formBuilder.lastPage": "<PERSON><PERSON>", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Sind <PERSON> sic<PERSON>, dass Sie das Fenster schließen möchten ohne zu speichern?", "app.components.formBuilder.leaveBuilderText": "Sie haben nicht gespeicherte Änderungen. Bitte speichern Sie, bevor <PERSON> gehen. <PERSON>n <PERSON> gehen, gehen Ihre Änderungen verloren.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON><PERSON> diese Funktion aktiviert ist, müssen die Befragten die angegebene Anzahl von Antworten auswählen, um fortzufahren.", "app.components.formBuilder.limitNumberAnswers": "Anzahl der Antworten begrenzen", "app.components.formBuilder.linePolygonMapWarning2": "Das Zeichnen von Linien und Polygonen entspricht möglicherweise nicht den Standards für Barrierefreiheit. Weitere Informationen finden Sie unter {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Lineare Skala", "app.components.formBuilder.locationDescription": "<PERSON><PERSON>", "app.components.formBuilder.logic": "Logik", "app.components.formBuilder.logicAnyOtherAnswer": "<PERSON><PERSON> andere Antwort", "app.components.formBuilder.logicConflicts.conflictingLogic": "Widersprüchliche Logik", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Diese Seite enthält <PERSON>, die zu verschiedenen Seiten führen. Wenn Teilnehmende mehrere Fragen beantworten, wird die am weitesten entfernte Seite angezeigt. Vergewissern Si<PERSON> sich, dass dieses Verhalten mit Ihrem beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Auf diese Seite werden mehrere Logikregeln angewendet: Logik für Mehrfachauswahlfragen, Logik auf Seitenebene und Logik zwischen den Fragen. Wenn sich diese Bedingungen überschneiden, hat die Fragenlogik Vorrang vor der Seitenlogik, und die am weitesten entfernte Seite wird angezeigt. Überprüfen Sie die Logik, um sicherzustellen, dass sie mit Ihrem beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Diese Seite enthält eine Mehrfachauswahlfrage, bei der die Optionen zu verschiedenen Seiten führen. Wenn die Teilnehmenden mehrere Optionen auswählen, wird die am weitesten entfernte Seite angezeigt. Vergewissern Si<PERSON> sich, dass dieses Verhalten mit Ihrem beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Diese Seite enthält eine Mehrfachauswahlfrage, bei der die Optionen zu verschiedenen Seiten führen, und hat Fragen, die zu anderen Seiten führen. Wenn sich diese Bedingungen überschneiden, wird die am weitesten entfernte Seite angezeigt. Vergewissern Si<PERSON> sich, dass dieses Verhalten mit Ihrem beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Diese Seite enthält eine Frage mit Mehrfachauswahl, bei der die Optionen zu verschiedenen Seiten führen, und hat eine Logik, die sowohl auf Seiten- als auch auf Fragenebene festgelegt ist. Die Fragelogik hat Vorrang, und die am weitesten entfernte Seite wird angezeigt. Vergewissern Si<PERSON> sich, dass dieses Verhalten mit Ihrem beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Diese Seite verfügt über eine Logik, die sowohl auf Seiten- als auch auf Fragenebene eingestellt ist. Die Fragenlogik hat Vorrang vor der Logik auf Seitenebene. <PERSON><PERSON><PERSON>, dass dieses Verhalten mit Ihrem beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Auf dieser Seite ist die Logik sowohl auf der Seiten- als auch auf der Fragenebene festgelegt, und mehrere Fragen verweisen auf verschiedene Seiten. Die Fragelogik hat Vorrang, und die am weitesten entfernte Seite wird angezeigt. Vergewissern Si<PERSON> sich, dass dieses Verhalten mit Ihrem beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.logicNoAnswer2": "<PERSON>cht <PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "Wenn eine andere Antwort", "app.components.formBuilder.logicPanelNoAnswer": "Falls nicht beantwortet", "app.components.formBuilder.logicValidationError": "Logik darf nicht auf vorherige Seiten verlinken", "app.components.formBuilder.longAnswer": "Lange Antwort", "app.components.formBuilder.mapConfiguration": "Konfiguration der Karte", "app.components.formBuilder.mapping": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.mappingNotInCurrentLicense": "Die Vermessungsfunktionen sind nicht in Ihrer aktuellen Lizenz enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr zu erfahren.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "Spalten", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoiceHelperText": "Wenn mehrere Optionen zu verschiedenen Seiten führen und die Teilnehmenden mehr als eine auswählen, wird die am weitesten entfernte Seite angezeigt. <PERSON><PERSON><PERSON> sic<PERSON>, dass dieses Verhalten mit dem von Ihnen beabsichtigten Ablauf übereinstimmt.", "app.components.formBuilder.multipleChoiceImage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.maximum": "Maximum", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.number": "<PERSON><PERSON>", "app.components.formBuilder.ok": "Okay", "app.components.formBuilder.open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.optional": "Optional", "app.components.formBuilder.other": "Sonstiges", "app.components.formBuilder.otherOption": "Option \"Sonstiges\"", "app.components.formBuilder.otherOptionTooltip": "Erlauben Sie den Teilnehmenden, eine benutzerdefinierte Antwort einzugeben, wenn die vorgegebenen Antworten nicht ihren Präferenzen entsprechen", "app.components.formBuilder.page": "Seite", "app.components.formBuilder.pageCannotBeDeleted": "Diese Seite kann nicht gelöscht werden.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Diese Seite kann nicht gelö<PERSON>t werden, und es können keine weiteren Felder hinzugefügt werden.", "app.components.formBuilder.pageRuleLabel": "Die nächste Seite ist:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "Wenn keine Logik hinzugefügt wird, folgt das Formular seinem normalen Ablauf. Wenn sowohl die Seite als auch die Fragen eine Logik enthalten, hat die Fragelogik Vorrang. <PERSON><PERSON><PERSON>, dass dies mit Ihrem beabsichtigten Ablauf übereinstimmt. Weitere Informationen finden Sie unter {supportPageLink}", "app.components.formBuilder.preview": "Vorschau:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Mitunterstützer*innen werden in der heruntergeladenen PDF-Datei nicht angezeigt und werden beim Import über Papier-Scans nicht unterstützt.", "app.components.formBuilder.printSupportTooltip.fileupload": "Fragen zum Hochladen von Dateien werden in der heruntergeladenen PDF-Datei als nicht unterstützt angezeigt und werden für den Import über Papier-Scans nicht unterstützt.", "app.components.formBuilder.printSupportTooltip.mapping": "Mapping-Fragen werden in der heruntergeladenen PDF-Datei angezeigt, aber die Ebenen sind nicht sichtbar. Mapping-Fragen werden für den Import über Papier-Scans nicht unterstützt.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrixfragen werden in der heruntergeladenen PDF-Datei angezeigt, werden aber derzeit nicht für den Import über Papier-Scans unterstützt.", "app.components.formBuilder.printSupportTooltip.page": "Seitentitel und Beschreibungen werden in der heruntergeladenen PDF-Datei als Abschnittsüberschrift angezeigt.", "app.components.formBuilder.printSupportTooltip.ranking": "Ranking-Fragen werden in der heruntergeladenen PDF-Datei angezeigt, werden aber derzeit nicht für den Import über Papier-Scans unterstützt.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags werden in der heruntergeladenen PDF-Datei als nicht unterstützt angezeigt und werden für den Import über Papier-Scans nicht unterstützt.", "app.components.formBuilder.proposedBudget": "Vorgeschlagenes Budget", "app.components.formBuilder.question": "Frage", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON>se Frage kann nicht gelöscht werden.", "app.components.formBuilder.questionDescriptionOptional": "Beschreibung der Frage (optional)", "app.components.formBuilder.questionTitle": "Titel der Frage", "app.components.formBuilder.randomize": "Randomisieren", "app.components.formBuilder.randomizeToolTip": "Die Reihenfolge der Antworten wird für jede*n Nutzer*in zufällig festgelegt", "app.components.formBuilder.range": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ranking": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.rating": "Bewertung", "app.components.formBuilder.removeAnswer": "Antwort entfernen", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "Beantwortung dieser Frage erforderlich machen", "app.components.formBuilder.ruleForAnswerLabel": "Wenn die Antwort lautet:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "Wenn die Antworten Folgendes beinhalten:", "app.components.formBuilder.save": "Speichern", "app.components.formBuilder.selectRangeTooltip": "Wählen Sie den maximalen Wert für Ihre Skala.", "app.components.formBuilder.sentiment": "Stimmungsskala", "app.components.formBuilder.shapefileUpload": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.shortAnswer": "Kurze Antwort", "app.components.formBuilder.showResponseToUsersToggleLabel": "Zeige Antwort an Nutzer*innen", "app.components.formBuilder.singleChoice": "Einfa<PERSON>uswahl", "app.components.formBuilder.staleDataErrorMessage2": "Es ist ein Problem aufgetreten. Dieses Eingabeformular wurde vor kurzem an einem anderen Ort gespeichert. Das kann daran liegen, dass Sie oder ein anderer Benutzer es in einem anderen Browserfenster zur Bearbeitung geöffnet haben. Bitte aktualisieren Sie die Seite, um das neueste Formular zu erhalten, und nehmen Sie dann Ihre Änderungen erneut vor.", "app.components.formBuilder.stronglyAgree": "Stimme voll und ganz zu", "app.components.formBuilder.stronglyDisagree": "<PERSON><PERSON><PERSON> überhaupt nicht zu", "app.components.formBuilder.supportArticleLinkText": "dieser Seite", "app.components.formBuilder.tags": "Themen-Tags", "app.components.formBuilder.title": "Titel", "app.components.formBuilder.toLabel": "bis", "app.components.formBuilder.unsavedChanges": "Sie haben ungespeicherte Änderungen", "app.components.formBuilder.useCustomButton2": "Button \"Benutzerdefinierte Seite\" verwenden", "app.components.formBuilder.veryBad": "<PERSON><PERSON> schlecht", "app.components.formBuilder.veryGood": "<PERSON><PERSON> gut", "app.components.ideas.similarIdeas.engageHere": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "app.components.ideas.similarIdeas.noSimilarSubmissions": "<PERSON>ine ähnlichen Beiträge gefunden.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Wir haben ähnliche Beiträge gefunden - mit ihnen zu arbeiten kann helfen, sie stärker zu machen!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Ähnliche Beiträge wurden bereits veröffentlicht:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Ich suche ähnliche Beiträge...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> als e<PERSON> Tag} one {Ein Tag} other {# Tage}} verbleibend", "app.components.phaseTimeLeft.xWeeksLeft": "noch {timeLeft} <PERSON><PERSON>en", "app.components.screenReaderCurrency.AED": "Vereinigte Arabische Emirate Dirham", "app.components.screenReaderCurrency.AFN": "Afghanisch Afghani", "app.components.screenReaderCurrency.ALL": "Albanischer Lek", "app.components.screenReaderCurrency.AMD": "Armenisches Drama", "app.components.screenReaderCurrency.ANG": "Niederländischer Antilleanischer Gulden", "app.components.screenReaderCurrency.AOA": "Angolanischer Kwanza", "app.components.screenReaderCurrency.ARS": "Argentinischer Peso", "app.components.screenReaderCurrency.AUD": "Australischer Dollar", "app.components.screenReaderCurrency.AWG": "Arubanischer Florin", "app.components.screenReaderCurrency.AZN": "Aserbaidschanischer Manat", "app.components.screenReaderCurrency.BAM": "Bosnien-Herzegowina Convertible Mark", "app.components.screenReaderCurrency.BBD": "Barbadischer Dollar", "app.components.screenReaderCurrency.BDT": "Banglades<PERSON><PERSON>", "app.components.screenReaderCurrency.BGN": "Bulgarisch Lev", "app.components.screenReaderCurrency.BHD": "Bahrain<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "Burundischer Franc", "app.components.screenReaderCurrency.BMD": "Be<PERSON><PERSON>scher Dollar", "app.components.screenReaderCurrency.BND": "Brunei-Dollar", "app.components.screenReaderCurrency.BOB": "Bolivianisches Boliviano", "app.components.screenReaderCurrency.BOV": "Bolivianischer Mvdol", "app.components.screenReaderCurrency.BRL": "Brasilianischer Real", "app.components.screenReaderCurrency.BSD": "Bahama-Dollar", "app.components.screenReaderCurrency.BTN": "Bhutanisches Ngultrum", "app.components.screenReaderCurrency.BWP": "Botswanischer Pula", "app.components.screenReaderCurrency.BYR": "Weißrussischer Rubel", "app.components.screenReaderCurrency.BZD": "Belize-Dollar", "app.components.screenReaderCurrency.CAD": "Kanadischer Dollar", "app.components.screenReaderCurrency.CDF": "Kongolesischer Franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Schweizer Franken", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilenische Rechnungseinheit (UF)", "app.components.screenReaderCurrency.CLP": "Chilenischer Peso", "app.components.screenReaderCurrency.CNY": "Chinesis<PERSON>", "app.components.screenReaderCurrency.COP": "Kolumbianischer Peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Ricanischer Colón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Kubanischer konvertierbarer Peso", "app.components.screenReaderCurrency.CUP": "Kubanischer Peso", "app.components.screenReaderCurrency.CVE": "Kapverdischer Escudo", "app.components.screenReaderCurrency.CZK": "Tschechische Krone", "app.components.screenReaderCurrency.DJF": "Dschibutischer Franc", "app.components.screenReaderCurrency.DKK": "Dänische Krone", "app.components.screenReaderCurrency.DOP": "Dominikanischer Peso", "app.components.screenReaderCurrency.DZD": "Algerischer Dinar", "app.components.screenReaderCurrency.EGP": "Ägyptisches Pfund", "app.components.screenReaderCurrency.ERN": "Eritreische Nakfa", "app.components.screenReaderCurrency.ETB": "Äthiopische Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fidschianischer Dollar", "app.components.screenReaderCurrency.FKP": "Falklandinseln Pfund", "app.components.screenReaderCurrency.GBP": "Britisches Pfund", "app.components.screenReaderCurrency.GEL": "Georg<PERSON><PERSON> Lari", "app.components.screenReaderCurrency.GHS": "Ghanaischer Zedi", "app.components.screenReaderCurrency.GIP": "Gibraltar Pfund", "app.components.screenReaderCurrency.GMD": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.GNF": "<PERSON><PERSON>eischer Franc", "app.components.screenReaderCurrency.GTQ": "Guatemaltekischer Quetzal", "app.components.screenReaderCurrency.GYD": "Guyanischer Dollar", "app.components.screenReaderCurrency.HKD": "Hongkong-Dollar", "app.components.screenReaderCurrency.HNL": "Honduranische Lempira", "app.components.screenReaderCurrency.HRK": "Kroatische Kuna", "app.components.screenReaderCurrency.HTG": "Haitianische Kürbisse", "app.components.screenReaderCurrency.HUF": "Ungarischer Forint", "app.components.screenReaderCurrency.IDR": "Indonesische Rupiah", "app.components.screenReaderCurrency.ILS": "Israelischer Neuer Schekel", "app.components.screenReaderCurrency.INR": "Indische Rupie", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.IRR": "Iranischer Rial", "app.components.screenReaderCurrency.ISK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JMD": "Jamaikanischer Dollar", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "Japanischer Yen", "app.components.screenReaderCurrency.KES": "Kenia-Schilling", "app.components.screenReaderCurrency.KGS": "<PERSON><PERSON><PERSON><PERSON> Som", "app.components.screenReaderCurrency.KHR": "Kambodschanischer Riel", "app.components.screenReaderCurrency.KMF": "Komorenfranke", "app.components.screenReaderCurrency.KPW": "Nordkoreanischer Won", "app.components.screenReaderCurrency.KRW": "Südkoreanischer Won", "app.components.screenReaderCurrency.KWD": "Kuwaitische<PERSON> Dinar", "app.components.screenReaderCurrency.KYD": "Kaimaninseln Dollar", "app.components.screenReaderCurrency.KZT": "Ka<PERSON><PERSON>scher Tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libanesisches Pfund", "app.components.screenReaderCurrency.LKR": "Sri Lankische Rupie", "app.components.screenReaderCurrency.LRD": "Liberianischer Dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Litauische Litas", "app.components.screenReaderCurrency.LVL": "Lettischer Lats", "app.components.screenReaderCurrency.LYD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MAD": "Marokkanischer Dirham", "app.components.screenReaderCurrency.MDL": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MGA": "Madagassisches Ariary", "app.components.screenReaderCurrency.MKD": "Mazedonischer Denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolisches Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauretanische Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritische Rupie", "app.components.screenReaderCurrency.MVR": "Maledivische Rufiyaa", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON> Kwacha", "app.components.screenReaderCurrency.MXN": "Mexikanischer Peso", "app.components.screenReaderCurrency.MXV": "Mexikanische Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysischer Ringgit", "app.components.screenReaderCurrency.MZN": "Mosambikanischer Metical", "app.components.screenReaderCurrency.NAD": "Namibia-Dollar", "app.components.screenReaderCurrency.NGN": "Nigerian<PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Nicaragua Córdoba", "app.components.screenReaderCurrency.NOK": "Norwegische Krone", "app.components.screenReaderCurrency.NPR": "Nepalesische Rupie", "app.components.screenReaderCurrency.NZD": "Neuseel<PERSON><PERSON><PERSON> Dollar", "app.components.screenReaderCurrency.OMR": "Omanischer Rial", "app.components.screenReaderCurrency.PAB": "Der panamaische Balboa", "app.components.screenReaderCurrency.PEN": "Peruanischer Sol", "app.components.screenReaderCurrency.PGK": "Papua-Neuguineische Kina", "app.components.screenReaderCurrency.PHP": "Philippinischer Peso", "app.components.screenReaderCurrency.PKR": "<PERSON><PERSON> R<PERSON>", "app.components.screenReaderCurrency.PLN": "Polnisch Złoty", "app.components.screenReaderCurrency.PYG": "Paraguayisches Guaraní", "app.components.screenReaderCurrency.QAR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RON": "Rumänischer Leu", "app.components.screenReaderCurrency.RSD": "Serbischer Dinar", "app.components.screenReaderCurrency.RUB": "Russischer Rubel", "app.components.screenReaderCurrency.RWF": "Ruandischer Franc", "app.components.screenReaderCurrency.SAR": "Saudi Riyal", "app.components.screenReaderCurrency.SBD": "Salomonen Dollar", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SDG": "Sudanesisches Pfund", "app.components.screenReaderCurrency.SEK": "Schwedische Krone", "app.components.screenReaderCurrency.SGD": "Singapur-Dollar", "app.components.screenReaderCurrency.SHP": "St. Helena Pfund", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "Surinamischer Dollar", "app.components.screenReaderCurrency.SSP": "Südsudanesisches Pfund", "app.components.screenReaderCurrency.STD": "São Tomé und Príncipe <PERSON>", "app.components.screenReaderCurrency.SYP": "Syrisches Pfund", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thailändi<PERSON> Baht", "app.components.screenReaderCurrency.TJS": "Tadschikistanische Somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistanischer Manat", "app.components.screenReaderCurrency.TND": "Tunesischer Dinar", "app.components.screenReaderCurrency.TOK": "Einladungscode", "app.components.screenReaderCurrency.TOP": "Tonga Paʻanga", "app.components.screenReaderCurrency.TRY": "Türkische Lira", "app.components.screenReaderCurrency.TTD": "Trinidad und Tobago Dollar", "app.components.screenReaderCurrency.TWD": "Neuer Taiwan-Dollar", "app.components.screenReaderCurrency.TZS": "Tansanischer Schilling", "app.components.screenReaderCurrency.UAH": "Ukrainische Griwna", "app.components.screenReaderCurrency.UGX": "Ugandischer Schilling", "app.components.screenReaderCurrency.USD": "Vereinigte Staaten Dollar", "app.components.screenReaderCurrency.USN": "United States Dollar (Nächster Tag)", "app.components.screenReaderCurrency.USS": "United States Dollar (Gleicher Tag)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayischer Peso", "app.components.screenReaderCurrency.UZS": "Usbekistanischer Som", "app.components.screenReaderCurrency.VEF": "Venezo<PERSON>scher Bolívar", "app.components.screenReaderCurrency.VND": "<PERSON>esis<PERSON>", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoanische Tala", "app.components.screenReaderCurrency.XAF": "Zentralafrikanischer CFA-Franc", "app.components.screenReaderCurrency.XAG": "<PERSON><PERSON><PERSON> (e<PERSON>)", "app.components.screenReaderCurrency.XAU": "Gold (<PERSON><PERSON>)", "app.components.screenReaderCurrency.XBA": "Europäische Kompositeinheit (EURCO)", "app.components.screenReaderCurrency.XBB": "Europäische Währungseinheit (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Europäische Rechnungseinheit 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Europäische Rechnungseinheit 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Ostkaribischer Dollar", "app.components.screenReaderCurrency.XDR": "Sonderziehungsrechte", "app.components.screenReaderCurrency.XFU": "UIC-Franc", "app.components.screenReaderCurrency.XOF": "Westafrikanischer CFA-Franc", "app.components.screenReaderCurrency.XPD": "Palladium (e<PERSON>)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (e<PERSON>)", "app.components.screenReaderCurrency.XTS": "Speziell für Testzwecke reservierte Codes", "app.components.screenReaderCurrency.XXX": "<PERSON><PERSON>", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.ZAR": "Südafrikanischer Rand", "app.components.screenReaderCurrency.ZMW": "Sambische Kwacha", "app.components.screenReaderCurrency.amount": "Betrag", "app.components.screenReaderCurrency.currency": "Währung", "app.components.trendIndicator.lastQuarter2": "letztes Quartal", "app.containers.AccessibilityStatement.applicability": "Diese Richtlinie zur Barrierefreiheit gilt für eine {demoPlatformLink}, die für diese Webseite repräsentativ ist; sie verwendet den gleichen Quellcode und hat die gleichen Funktionalitäten.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Bewertungsmethode", "app.containers.AccessibilityStatement.assesmentText2022": "Die Richtlinie zur Barrierefreiheit dieser Webseite wurde von einer externen Stelle bewertet, die nicht in den Entwurfs- und Entwicklungsprozess eingebunden war. Die Einhaltung der vorgenannten {demoPlatformLink} kann auf dieser {statusPageLink} nachgelesen werden.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "Sie können Ihre Einstellungen", "app.containers.AccessibilityStatement.changePreferencesText": "jederzeit ändern: {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Ausnahmen von der Konformität", "app.containers.AccessibilityStatement.conformanceStatus": "Konformitätsstatus", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Wir bemühen uns unsere Inhalte für alle inklusiv zu gestalten. Allerdings sind bestimmte Inhalte der Plattform unter den folgenden Umständen nicht barrierefrei:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "Demo-Webseite", "app.containers.AccessibilityStatement.email": "E-Mail:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Eingebettete Umfrage-Tools", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Bei den eingebetteten Umfragetools, die auf dieser Plattform zur Verfügung stehen, handelt es sich um Software von Drittanbietern, die eingebettet wird und auf deren Barrierefreiheit wir keinen Einfluss haben.", "app.containers.AccessibilityStatement.exception_1": "Unsere Beteiligungsplattformen fördern die Veröffentlichung von nutzergenerierten Inhalten von Einzelpersonen und Organisationen. Sie ermöglichen den Nutzern unserer Plattformen das Hochladen von PDFs, Bildern, Multimediadateien und anderen Dateitypen in Form von Anhängen oder als Einfügungen in die Textfelder. Möglicherweise sind diese Dokumente nicht vollständig zugänglich.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Wir freuen uns über Rückmeldungen zur Barrierefreiheit dieser Webseite. Bitte kontaktieren Sie uns auf einem der folgenden Wege:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Feedbackprozess", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brüssel, Belgien", "app.containers.AccessibilityStatement.headTitle": "Erklärung zur Barrierefreiheit | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} ist bestrebt, eine Plattform bereitzustellen, die für alle Nutzer zugänglich ist, unabhä<PERSON>ig von Technologie oder Fähigkeiten. Bei unseren ständigen Bemühungen, die Zugänglichkeit und Nutzbarkeit unserer Plattformen für alle Nutzer zu maximieren, halten wir uns an die aktuellen einschlägigen Standards zur Barrierefreiheit.", "app.containers.AccessibilityStatement.mapping": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.mapping_1": "Die Karten auf der Plattform entsprechen teilweise den Standards für Barrierefreiheit. Kartenausschnitt, Zoom und UI-Widgets können bei der Anzeige von Karten über die Tastatur gesteuert werden. Admins können auch den Stil der Kartenebenen im Back Office oder über die Esri-Integration konfigurieren, um zugänglichere Farbpaletten und Symbole zu erstellen. Die Verwendung unterschiedlicher Linien- oder Polygonstile (z.B. gestrichelte Linien) hilft ebenfalls dabei, <PERSON><PERSON><PERSON><PERSON><PERSON> zu unterscheiden, wo immer dies möglich ist. Obwohl ein solches Styling derzeit nicht innerhalb unserer Plattform konfiguriert werden kann, kann es bei der Verwendung von Karten mit der Esri-Integration konfiguriert werden.", "app.containers.AccessibilityStatement.mapping_2": "Die Karten in der Plattform sind nicht vollständig zug<PERSON><PERSON><PERSON>, da <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> oder Trends in den Daten für Nutzer*innen, die Bildschirmlesegeräte verwenden, nicht hörbar darstellen. Vollständig zugängliche Karten müssten die Kartenebenen hörbar darstellen und alle relevanten Trends in den Daten beschreiben. Außerdem ist das Zeichnen von Linien und Polygonen in Umfragen nicht barrierefrei, da die Formen nicht mit der Tastatur gezeichnet werden können. Alternative Eingabemethoden sind zur Zeit aufgrund der technischen Komplexität nicht verfügbar.", "app.containers.AccessibilityStatement.mapping_3": "Um das Zeichnen von Lin<PERSON>- und Polygonkarten zugänglicher zu machen, empf<PERSON><PERSON> wir, in die Umfragefrage oder Seitenbeschreibung eine Einführung oder Erklärung aufzunehmen, was die Karte zeigt und welche Trends relevant sind. Außerdem könnte eine kurze oder lange Textfrage gestellt werden, damit die Befragten ihre Antwort bei Bedarf in einfachen Worten beschreiben können (anstatt auf die Karte zu klicken). Wir empfehlen auch die Angabe von Kontaktinformationen der Projektmanager*innen, damit Befragt<PERSON>, die eine Kartenfrage nicht ausfüllen können, eine alternative Methode zur Beantwortung der Frage anfordern können (z. B. ein Videomeeting).", "app.containers.AccessibilityStatement.mapping_4": "Für Ideenfindungen und Vorschläge gibt es eine Option zur Anzeige der Beiträge in einer Kartenansicht, die nicht zugänglich ist. Für diese Methoden gibt es jedoch eine alternative Listenansicht der Beiträge, die zugänglich ist.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Unsere Online-Workshops haben eine Live-Video-Streaming-Komponente, die derzeit keine Untertitel unterstützt.", "app.containers.AccessibilityStatement.pageDescription": "Eine Erklärung zur Barrierefreiheit dieser Webseite", "app.containers.AccessibilityStatement.postalAddress": "Anschrift:", "app.containers.AccessibilityStatement.publicationDate": "Datum der Veröffentlichung", "app.containers.AccessibilityStatement.publicationDate2024": "Diese Erklärung zur Barrierefreiheit wurde am 21. August 2024 veröffentlicht.", "app.containers.AccessibilityStatement.responsiveness": "Wir bemühen uns innerhalb von 1 bis 2 Tagen auf Ihr Feedback zu reagieren.", "app.containers.AccessibilityStatement.statusPageText": "Statusseite", "app.containers.AccessibilityStatement.technologiesIntro": "<PERSON>ür die Barrierefreiheit dieser Webseite ist die Funktion der folgenden Technologien erforderlich:", "app.containers.AccessibilityStatement.technologiesTitle": "Technologien", "app.containers.AccessibilityStatement.title": "Richtlinie zur Barrierefreiheit", "app.containers.AccessibilityStatement.userGeneratedContent": "Nutzer*innengenerierte Inhalte", "app.containers.AccessibilityStatement.workshops": "Workshops", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Projekt auswählen", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Mit dem Content Builder können Sie erweiterte Layout-Optionen nutzen. <PERSON><PERSON><PERSON>, für die im Content Builder kein Inhalt verfügbar ist, wird stattdessen der reguläre Inhalt der Projektbeschreibung angezeigt.", "app.containers.AdminPage.ProjectDescription.linkText": "Beschreibung im Content Builder bearbeiten", "app.containers.AdminPage.ProjectDescription.saveError": "Beim Speichern der Projektbeschreibung ist ein Fehler aufgetreten.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Content Builder für Beschreibung verwenden", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Mit dem Content Builder können Sie erweiterte Layout-Optionen nutzen.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON><PERSON><PERSON> an<PERSON><PERSON>", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Ende der Umfrage", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "<PERSON>rstellen einer intelligenten Gruppe", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "<PERSON>utz<PERSON>*innen, die alle der folgenden Bedingungen erfüllen, werden automatisch zur Gruppe hinzugefügt:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Mindestens eine Regel eingeben", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Einige Bedingungen sind unvollständig", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Gruppe speichern", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Die Konfiguration von intelligenten Gruppen ist nicht Bestandteil Ihrer aktuellen Lizenz. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Gruppennamen angeben", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Die Verifizierung ist für Ihre Plattform deaktiviert, entfernen Sie die Verifizierungsregel oder kontaktieren Sie den Support.", "app.containers.App.appMetaDescription": "Willkommen auf der Online-Partizipationsplattform der {orgName}. \nErkunden Sie Projekte und beteiligen Sie sich!", "app.containers.App.loading": "Lädt...", "app.containers.App.metaTitle1": "Beteiligungsplattform | {orgName}", "app.containers.App.skipLinkText": "Zum Hauptinhalt wechseln", "app.containers.AreaTerms.areaTerm": "<PERSON><PERSON><PERSON>", "app.containers.AreaTerms.areasTerm": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Es existiert bereits ein Konto mit dieser E-Mail-Adresse. Si<PERSON> können sich abmelden, mit dieser E-Mail-Adresse anmelden und Ihr Konto auf der Einstellungsseite verifizieren.", "app.containers.Authentication.steps.AccessDenied.close": "Schließen", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Sie erfüllen nicht die Voraussetzungen für die Teilnahme an diesem Prozess.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Zurück zur Single Sign-On Verifizierung", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Bitte geben Si<PERSON> einen Einladungscode ein", "app.containers.Authentication.steps.Invitation.token": "Einladungscode", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Sie haben bereits ein Konto? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Anmelden", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-Mails in dieser Kategorie", "app.containers.CampaignsConsentForm.messageError": "Es ist ein Fehler beim Speichern Ihrer E-Mail-Einstellungen aufgetreten.", "app.containers.CampaignsConsentForm.messageSuccess": "Ihre E-Mail-Einstellungen wurden gespeichert.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Welche Arten von E-Mail-Benachrichtigungen möchten Sie erhalten? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Benachrichtigungen", "app.containers.CampaignsConsentForm.submit": "Speichern", "app.containers.ChangeEmail.backToProfile": "<PERSON><PERSON><PERSON> zu den Profileinstellungen", "app.containers.ChangeEmail.confirmationModalTitle": "E-Mail-Adresse bestätigen", "app.containers.ChangeEmail.emailEmptyError": "Geben Sie eine E-Mail-Adresse an", "app.containers.ChangeEmail.emailInvalidError": "Geben Sie eine E-Mail-Adresse im richtigen Format an, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "<PERSON>te geben Si<PERSON> eine E-Mail Adresse ein.", "app.containers.ChangeEmail.emailTaken": "<PERSON>se E-<PERSON>-<PERSON><PERSON>e wird schon verwendet.", "app.containers.ChangeEmail.emailUpdateCancelled": "Das E-Mail-Update wurde gestrichen.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Um Ihre E-Mail-Adresse zu aktualisieren, starten Sie den Vorgang bitte neu.", "app.containers.ChangeEmail.helmetDescription": "Ändern der E-Mail-Seite", "app.containers.ChangeEmail.helmetTitle": "Ändern der E-Mail-Adresse", "app.containers.ChangeEmail.newEmailLabel": "Neue E-Mail-Adresse", "app.containers.ChangeEmail.submitButton": "Senden", "app.containers.ChangeEmail.titleAddEmail": "E-Mail-Ad<PERSON>e hi<PERSON>uf<PERSON>", "app.containers.ChangeEmail.titleChangeEmail": "Ändern der E-Mail-Adresse", "app.containers.ChangeEmail.updateSuccessful": "Ihre E-Mail-Adresse wurde erfolgreich aktualisiert.", "app.containers.ChangePassword.currentPasswordLabel": "Aktuelles Passwort", "app.containers.ChangePassword.currentPasswordRequired": "Geben Sie Ihr aktuelles Passwort ein", "app.containers.ChangePassword.goHome": "Zurück zur Startseite", "app.containers.ChangePassword.helmetDescription": "Seite zum Ändern Ihres Passworts", "app.containers.ChangePassword.helmetTitle": "Ändern Sie Ihr Passwort", "app.containers.ChangePassword.newPasswordLabel": "Neues Passwort", "app.containers.ChangePassword.newPasswordRequired": "<PERSON><PERSON><PERSON> Sie Ihr neues Passwort ein", "app.containers.ChangePassword.password.minimumPasswordLengthError": "<PERSON><PERSON>en Si<PERSON> ein Passwort ein, das mindestens {minimumPasswordLength} <PERSON><PERSON><PERSON> lang ist", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Ihr Passwort wurde erfolgreich aktualisiert", "app.containers.ChangePassword.passwordEmptyError": "Geben Sie Ihr Passwort ein", "app.containers.ChangePassword.passwordsDontMatch": "Bestätigen Sie das neue Passwort", "app.containers.ChangePassword.titleAddPassword": "Ein Passwort hinzufügen", "app.containers.ChangePassword.titleChangePassword": "Ändern Sie Ihr Passwort", "app.containers.Comments.a11y_commentDeleted": "Kommentar <PERSON>", "app.containers.Comments.a11y_commentPosted": "Kommentar ver<PERSON><PERSON><PERSON>licht", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {0 gefällt mir} one {1 gefällt mir} other {# gefällt mir}}", "app.containers.Comments.a11y_undoLike": "Gefällt mir rückgängig machen", "app.containers.Comments.addCommentError": "Etwas ging schief. Bitte versuchen Sie es später nochmal.", "app.containers.Comments.adminCommentDeletionCancelButton": "Abbrechen", "app.containers.Comments.adminCommentDeletionConfirmButton": "Kommentar löschen", "app.containers.Comments.cancelCommentEdit": "Abbrechen", "app.containers.Comments.childCommentBodyPlaceholder": "Re<PERSON>eren...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "Dieses <PERSON> wurde gelöscht.", "app.containers.Comments.commentDeletionCancelButton": "Meinen Kommentar beibehalten", "app.containers.Comments.commentDeletionConfirmButton": "Meinen Kommentar löschen", "app.containers.Comments.commentLike": "Gefällt mir", "app.containers.Comments.commentReplyButton": "Antworten", "app.containers.Comments.commentsSortTitle": "Kommentare sortieren nach ", "app.containers.Comments.completeProfileLinkText": "Vervollständigen Sie Ihr Profil", "app.containers.Comments.completeProfileToComment": "Bitte {completeRegistrationLink} um zu kommentieren.", "app.containers.Comments.confirmCommentDeletion": "Sind <PERSON> sic<PERSON>, dass Sie diesen Kommentar löschen möchten? Es gibt keinen <PERSON>g, um dies rückgängig zu machen!", "app.containers.Comments.deleteComment": "Löschen", "app.containers.Comments.deleteReasonDescriptionError": "G<PERSON>en Sie weitere Informationen zu Ihrem Grund an", "app.containers.Comments.deleteReasonError": "<PERSON><PERSON><PERSON> Si<PERSON> einen Grund an", "app.containers.Comments.deleteReason_inappropriate": "<PERSON>s ist unangemessen oder anstößig", "app.containers.Comments.deleteReason_irrelevant": "Das gehört nicht hierher", "app.containers.Comments.deleteReason_other": "<PERSON><PERSON>", "app.containers.Comments.editComment": "<PERSON><PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "unseren <PERSON>", "app.containers.Comments.ideaCommentBodyPlaceholder": "Schreiben Sie Ihren Kommentar hier", "app.containers.Comments.internalCommentingNudgeMessage": "Die Abgabe interner Kommentare ist nicht in Ihrer aktuellen Lizenz enthalten. Wenden Si<PERSON> sich an Ihre Kundenbetreuerin, um mehr darüber zu erfahren.", "app.containers.Comments.internalConversation": "Interne Diskussion", "app.containers.Comments.loadMoreComments": "<PERSON><PERSON><PERSON> Kommentare laden", "app.containers.Comments.loadingComments": "Kommentare werden geladen...", "app.containers.Comments.loadingMoreComments": "Weitere Kommentare werden geladen ...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Dieser Kommentar ist für normale Nutzer*innen nicht sichtbar", "app.containers.Comments.postInternalComment": "Internen Kommentar abschicken", "app.containers.Comments.postPublicComment": "Öffentlichen Kommentar abschicken", "app.containers.Comments.profanityError": "Ups! Es sieht so aus, als ob dein Beitrag eine Sprache enthält, die nicht {guidelinesLink} entspricht. Wir versuchen, diese Plattform zu einem sicheren Ort für alle zu machen. Bitte editiere deinen Beitrag und versuche es erneut.", "app.containers.Comments.publicDiscussion": "Öffentliche Diskussion", "app.containers.Comments.publishComment": "Veröffentlichen Sie Ihren Kommentar", "app.containers.Comments.reportAsSpamModalTitle": "Warum wollen Sie dies als Spam melden?", "app.containers.Comments.saveComment": "Speichern", "app.containers.Comments.signInLinkText": "melden Sie sich an", "app.containers.Comments.signInToComment": "Bitte {signInLink}, um kommentieren zu können.", "app.containers.Comments.signUpLinkText": "registrieren Si<PERSON> sich", "app.containers.Comments.verifyIdentityLinkText": "Verifizieren Sie Ihre Identität", "app.containers.Comments.visibleToUsersPlaceholder": "Dieser Kommentar ist für normale Nutzer*innen sichtbar", "app.containers.Comments.visibleToUsersWarning": "<PERSON><PERSON><PERSON><PERSON>, die hier gepostet werden, sind für normale Nutzer*innen sichtbar.", "app.containers.ContentBuilder.PageTitle": "Projektbeschreibung", "app.containers.CookiePolicy.advertisingContent": "Werbe-Cookies können zur Personalisierung und zur Messung der Wirksamkeit externer Marketingkampagnen bei der Nutzung dieser Plattform verwendet werden. Wir zeigen keine Werbung auf dieser Plattform, aber Sie können personalisierte Werbung auf der Grundlage der von Ihnen besuchten Seiten erhalten.", "app.containers.CookiePolicy.advertisingTitle": "Werbe-Cookies", "app.containers.CookiePolicy.analyticsContents": "Analyse-Cookies betrachten das Nutzer:innenverhalten, z.B<PERSON>, welche Seiten und für wie lange sie besucht werden. Darüber hinaus erfassen sie technische Daten wie Browser-Informationen, geschätzter Standort und IP-Adressen. Eine Verwendung der Daten erfolgt in rein aggregierter Form ohne Identifikation auf personenbezogener Ebene und nur seitens Plattformanbieters, um die Nutzer*innen-Erfahrung und das Funktionieren der Plattform zu verbessern. Solche Daten können auch zwischen Go Vocal und {orgName} geteilt werden, um die Nutzung der Beteiligungsprojekte optimieren zu können. Beachten Sie, dass es sich hier ausschließlich um bereits anonymisierten und aggregierten Daten handelt.", "app.containers.CookiePolicy.analyticsTitle": "Analyse-Cookies", "app.containers.CookiePolicy.cookiePolicyDescription": "Eine detaillierte Erklärung, wie wir Cookies auf dieser Plattform verwenden", "app.containers.CookiePolicy.cookiePolicyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.essentialContent": "Einige Cookies sind erforderlich, um die ordnungsgemäße Funktion der Plattform sicherzustellen. Diese erforderlichen Cookies werden verwendet, um Ihr Konto zu authentifizieren und Ihre bevorzugte Sprache zu speichern.", "app.containers.CookiePolicy.essentialTitle": "Erforderliche Cookies", "app.containers.CookiePolicy.externalContent": "Beiträge können Inhalte von externen Anbietern, wie z. B. YouTube, anzeigen. Das Ansehen des Inhalts dieser externen Anbieter kann dazu führen, dass Cookies auf Ihrem Gerät installiert werden. Weder Go Vocal noch {orgName} haben Kontrolle über diese Drittpartei-Cookies.", "app.containers.CookiePolicy.externalTitle": "Externe Cookies", "app.containers.CookiePolicy.functionalContents": "Funktionale Cookies ermöglichen es, Benachrichtigungen über Aktualisierungen und direkt von der Plattform Zugriff zu Support-Kanälen zu erhalten.", "app.containers.CookiePolicy.functionalTitle": "Funktionale Cookies", "app.containers.CookiePolicy.headCookiePolicyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> | {orgName}", "app.containers.CookiePolicy.intro": "Cookies sind Textdateien, die im Browser oder auf der Festplatte Ihres Computers oder Ihrer Mobilgeräte gespeichert werden, wenn Sie eine Webseite besuchen und die von der Webseite bei nachfolgenden Besuchen wieder aufgerufen werden können. Cookies werden eingesetzt, um Ihre Präferenzen zu speichern (z.B. Ihre bevorzugte Sprache), um wichtige Funktionen für registrierte Nutzer*innen und Plattform-Admins zu unterstützen und um zu verstehen, wie Nutzer*innen diese Plattform verwenden, um Design und Nutzer*innen-Erfahrung zu verbessern.", "app.containers.CookiePolicy.manageCookiesDescription": "Sie können in Ihren Cookie-Präferenzen jederzeit die erforderlichen Cookies und/oder Analysecookies aktivieren oder deaktivieren. Sie können über Ihren Internet-Browser auch manuell oder automatisch alle vorhanden Cookies löschen, jedoch können die Cookies wieder platziert werden, wenn Sie bei nachfolgenden Besuchen der Plattform erneut Ihre Zustimmung geben Ihre Cookie-Präferenzen werden für 60 Tage gespeichert, nach deren Ablauf werden Sie erneut nach Ihrer Zustimmung gefragt.", "app.containers.CookiePolicy.manageCookiesPreferences": "Gehen Sie zu Ihren {manageCookiesPreferencesButtonText}, um eine vollständige Liste der Drittpartei-Integrationen, die auf dieser Plattform verwendet werden zu sehen und, um Ihre Präferenzen zu bearbeiten.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "Cookie-Einstellungen", "app.containers.CookiePolicy.manageCookiesTitle": "<PERSON><PERSON>e <PERSON> verwalten", "app.containers.CookiePolicy.viewPreferencesButtonText": "Cookie-Einstellungen", "app.containers.CookiePolicy.viewPreferencesText": "Die unten stehenden Cookie-Kategorien müssen nicht für alle Besucher*innen oder Plattformen gelten; sehen <PERSON> sich Ihre {viewPreferencesButton} für eine vollständige Liste, der für Sie geltenden Drittpartei-Integrationen an.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Welche Cookies kommen zum Einsatz?", "app.containers.CustomPageShow.editPage": "Seite bearbeiten", "app.containers.CustomPageShow.goBack": "Zurück", "app.containers.CustomPageShow.notFound": "Seite nicht gefunden", "app.containers.DisabledAccount.bottomText": "<PERSON>e können sich ab {date} erneut anmelden.", "app.containers.DisabledAccount.termsAndConditions": "Geschäftsbedingungen", "app.containers.DisabledAccount.text2": "Ihr Konto auf der Beteiligungsplattform von {orgName} wurde wegen eines Verstoßes gegen die Community-Richtlinien vorübergehend deaktiviert. Weitere Informationen hierzu finden Sie auf der Website {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Ihr Konto wurde vorübergehend deaktiviert", "app.containers.EventsShow.addToCalendar": "Zum Kalender hinzufügen", "app.containers.EventsShow.editEvent": "Veranstaltung bearbeiten", "app.containers.EventsShow.emailSharingBody2": "An Veranstaltung teilnehmen: {eventTitle}. <PERSON><PERSON> unter {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Datum und Uhrzeit der Veranstaltung", "app.containers.EventsShow.eventFrom2": "<PERSON> \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Zurück", "app.containers.EventsShow.goToProject": "Zum Projekt", "app.containers.EventsShow.haveRegistered": "haben sich registriert", "app.containers.EventsShow.icsError": "Fehler beim Herunterladen der ICS-Datei", "app.containers.EventsShow.linkToOnlineEvent": "Link zur Online-Veranstaltung", "app.containers.EventsShow.locationIconAltText": "Ort", "app.containers.EventsShow.metaTitle": "Veranstaltung: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online-Veranstaltung", "app.containers.EventsShow.onlineLinkIconAltText": "Link zum Online-Meeting", "app.containers.EventsShow.registered": "registriert", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 <PERSON><PERSON><PERSON>hmend<PERSON>} one {1 Teilnehmende} other {# Teilnehmende}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} Teilnehmende", "app.containers.EventsShow.registrantsIconAltText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.EventsShow.socialMediaSharingMessage": "An Veranstaltung teilnehmen: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# <PERSON><PERSON><PERSON><PERSON><PERSON>*in} other {# Teilnehm<PERSON>e}}", "app.containers.EventsViewer.allTime": "Alle Zeiträume", "app.containers.EventsViewer.date": "Datum", "app.containers.EventsViewer.thisMonth2": "<PERSON><PERSON><PERSON>", "app.containers.EventsViewer.thisWeek2": "Kommende Woche", "app.containers.EventsViewer.today": "<PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAPetition": "Petition hinzufügen", "app.containers.IdeaButton.addAProject": "Projekt hinzufügen", "app.containers.IdeaButton.addAProposal": "Vorschlag hinzufügen", "app.containers.IdeaButton.addAQuestion": "Eine Frage hinzufügen", "app.containers.IdeaButton.addAnInitiative": "Initiative hinzufügen", "app.containers.IdeaButton.addAnOption": "Eine Option hinzufügen", "app.containers.IdeaButton.postingDisabled": "Derzeit werden keine neuen Beiträge mehr entgegengenommen\n", "app.containers.IdeaButton.postingInNonActivePhases": "Nur in aktiven Phasen können neue Beiträge hinzugefügt werden", "app.containers.IdeaButton.postingInactive": "<PERSON>e können derzeit nicht an diesem Projekt teilnehmen.", "app.containers.IdeaButton.postingLimitedMaxReached": "Sie haben diese Umfrage bereits abgeschlossen. Vielen Dank für Ihre Antwort!", "app.containers.IdeaButton.postingNoPermission": "<PERSON>e können derzeit nicht an diesem Projekt teilnehmen", "app.containers.IdeaButton.postingNotYetPossible": "Hier werden noch keine neuen Beiträge entgegengenommen.\n", "app.containers.IdeaButton.signInLinkText": "melden Sie sich an", "app.containers.IdeaButton.signUpLinkText": "registrieren Si<PERSON> sich", "app.containers.IdeaButton.submitAnIssue": "Kommentieren", "app.containers.IdeaButton.submitYourIdea": "I<PERSON><PERSON> e<PERSON><PERSON>ichen", "app.containers.IdeaButton.takeTheSurvey": "An Umfrage teilnehmen", "app.containers.IdeaButton.verificationLinkText": "Verifiziere jetzt das Konto.", "app.containers.IdeaCard.readMore": "<PERSON><PERSON> er<PERSON>", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {keine <PERSON>} one {1 Kommentar} other {# Kommentare}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {keine <PERSON>im<PERSON>} one {1 Stimme} other {# Stimmen}} von {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Filterfeld schließen", "app.containers.IdeaCards.a11y_totalItems": "Gesamtanzahl der Beiträge: {ideasCount}\n", "app.containers.IdeaCards.all": "Alle", "app.containers.IdeaCards.allStatuses": "Alle Statusmeldungen", "app.containers.IdeaCards.contributions": "Beiträge", "app.containers.IdeaCards.ideaTerm": "Beiträge", "app.containers.IdeaCards.initiatives": "Initiativen", "app.containers.IdeaCards.issueTerm": "Probleme", "app.containers.IdeaCards.list": "Liste", "app.containers.IdeaCards.map": "<PERSON><PERSON>", "app.containers.IdeaCards.mostDiscussed": "<PERSON><PERSON>", "app.containers.IdeaCards.newest": "Neueste", "app.containers.IdeaCards.noFilteredResults": "<PERSON>s wurden keine Ergebnisse gefunden. Bitte versuchen Si<PERSON> es mit einem anderen Filter oder Suchbegriff.\n", "app.containers.IdeaCards.numberResults": "Ergebnisse ({postCount})", "app.containers.IdeaCards.oldest": "Älteste", "app.containers.IdeaCards.optionTerm": "Optionen", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "Hat die meisten Stimmen erhalten", "app.containers.IdeaCards.projectFilterTitle": "{<PERSON><PERSON><PERSON>, select, <PERSON><PERSON><PERSON> {Bezirke} other {Projekte}}", "app.containers.IdeaCards.projectTerm": "Projekte", "app.containers.IdeaCards.proposals": "Vorschläge", "app.containers.IdeaCards.questionTerm": "Fragen", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "<PERSON><PERSON>", "app.containers.IdeaCards.showXResults": "Zeige {ideasCount, plural, one {# <PERSON><PERSON>bnis} other {# Ergebnisse}}", "app.containers.IdeaCards.sortTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Themen", "app.containers.IdeaCards.topicsTitle": "Themen", "app.containers.IdeaCards.trending": "Beliebt", "app.containers.IdeaCards.tryDifferentFilters": "<PERSON>s wurden keine Ergebnisse gefunden. Bitte versuchen Si<PERSON> es mit einem anderen Filter oder Suchbegriff.\n", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} Vorhaben} one {{ideasCount} Vorhaben} other {{ideasCount} Vorhaben}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} Beiträge} one {{ideasCount} Beitrag} other {{ideasCount} Beiträge}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} Ideen} one {{ideasCount} Idee} other {{ideasCount} Ideen}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} Initiativen} one {{ideasCount} Initiative} other {{ideasCount} Initiativen}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} Optionen} one {{ideasCount} Option} other {{ideasCount} Optionen}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} <PERSON>ionen} one {{ideasCount} Petition} other {{ideasCount} <PERSON>ione<PERSON>}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} Projekte} one {{ideasCount} Projekt} other {{ideasCount} Projekte}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} Vorschläge} one {{ideasCount} Vorschlag} other {{ideasCount} Vorschläge}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} Fragen} one {{ideasCount} Frage} other {{ideasCount} Fragen}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# <PERSON><PERSON><PERSON><PERSON>} other {# <PERSON><PERSON><PERSON>nis<PERSON>}}", "app.containers.IdeasEditPage.contributionFormTitle": "<PERSON><PERSON><PERSON>\n", "app.containers.IdeasEditPage.editedPostSave": "Speichern", "app.containers.IdeasEditPage.fileUploadError": "Das Hochladen von einer oder mehreren Dateien ist fehlgeschlagen. Bitte prüfen Sie die Dateigröße und das Format und versuchen Si<PERSON> es erneut.\n", "app.containers.IdeasEditPage.formTitle": "{<PERSON><PERSON><PERSON>, select, <PERSON>ü<PERSON> {Mängelmeldung bearbeiten} other {Idee bearbeiten}}", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Bearbeiten Sie Ihren Beitrag. Fügen Sie neue Informationen hinzu oder ändern Sie al<PERSON>.\n", "app.containers.IdeasEditPage.ideasEditMetaTitle": "{postTitle} | {projectName} bearbeiten\n", "app.containers.IdeasEditPage.initiativeFormTitle": "Initiative bearbeiten", "app.containers.IdeasEditPage.issueFormTitle": "Problem bearbeiten", "app.containers.IdeasEditPage.optionFormTitle": "Option bearbeiten\n", "app.containers.IdeasEditPage.petitionFormTitle": "Petition bearbeiten", "app.containers.IdeasEditPage.projectFormTitle": "Projekt bearbeiten", "app.containers.IdeasEditPage.proposalFormTitle": "Vorschlag bearbeiten", "app.containers.IdeasEditPage.questionFormTitle": "Frage bearbeiten\n", "app.containers.IdeasEditPage.save": "Speichern", "app.containers.IdeasEditPage.submitApiError": "Es gab ein Problem beim Absenden des Formulars. Bitte prüfe auf Fehler und versuche es erneut.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Alle Beiträge veröffentlicht", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Entdecken Sie alle Beiträge, die auf der Beteiligungsplattform {orgName} veröffentlicht wurden.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Beiträge | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Beiträge", "app.containers.IdeasIndexPage.loadMore": "Me<PERSON> laden...", "app.containers.IdeasIndexPage.loading": "Lädt...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Standardmäßig werden Ihre Beiträge mit Ihrem Profil verknüpft, es sei denn, <PERSON><PERSON> wählen diese Option.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Anonym teilnehmen", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Sichtbarkeit des Profils", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Diese Umfrage ist derzeit nicht für Antworten geöffnet. Bitte kehren Si<PERSON> zum Projekt zurück, um weitere Informationen zu erhalten.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Diese Umfrage ist derzeit nicht aktiv.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Zurück zum Projekt", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Sie haben diese Umfrage bereits ausgefüllt.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Umfrage eingereicht", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Vielen Dank für Ihre Antwort!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Die Beitragsbeschreibung muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Der Ideenkörper muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Der Beitragstitel muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Der Titel des Beitrages muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein.", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Mindestens eine*n Unterstützer*in auswählen", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Die Ideenbeschreibung muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Die Ideenbeschreibung muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "<PERSON>te gib eine Beschreibung ein", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Der Titel der Idee muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein.", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Der Titel der Idee muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Die Beschreibung der Initiative muss weniger als {limit} Zei<PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Die Beschreibung der Initiative muss mehr als {limit} Z<PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Der Titel der Initiative muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Der Titel der Initiative muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Die Problembeschreibung muss weniger als {limit} Zei<PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Die Problembeschreibung muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Der Titel der Ausgabe muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Der Titel des Beitrags muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON><PERSON>ld ist erforderlich. Bitte geben Si<PERSON> eine gültige Nummer ein", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Die Beschreibung der Option muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Die Beschreibung der Option muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein.", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Der Titel der Option muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Der Titel der Option muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Bitte wählen Sie mindestens einen Tag aus", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Die Beschreibung der Petition muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Die Beschreibung der Petition muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "Der Titel der Petition muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Der Titel der Petition muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Die Projektbeschreibung muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Die Projektbeschreibung muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Der Projekttitel muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Der Projekttitel muss mehr als {limit} Zeichen umfassen", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Die Beschreibung des Vorschlags muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Die Beschreibung des Vorschlags muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Der Titel des Vorschlags muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Der Titel des Vorschlags muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "<PERSON><PERSON> eine Zahl e<PERSON>ben", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "<PERSON><PERSON> eine Zahl e<PERSON>ben", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Die Fragenbeschreibung muss weniger als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Die Beschreibung muss länger als {limit} <PERSON><PERSON><PERSON> sein", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Der Titel muss kürzer als {limit} <PERSON><PERSON><PERSON> sein", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Der Titel der Frage muss mehr als {limit} <PERSON><PERSON><PERSON> lang sein", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON>te gib eine Überschrift ein", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Die Beitragsbeschreibung muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Die Beitragsbeschreibung muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Der Titel des Beitrags muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Der Titel des Beitrags muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Die Idee-Beschreibung muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Die Ideenbeschreibung muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON>te gib eine Überschrift ein", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Der Titel der Idee darf nur weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Der Ideentitel muss mindestens 10 Zeichen lang sein.", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Sie könnten ein oder mehrere Wörter verwendet haben, die von {guidelinesLink} als Obszönität betrachtet werden. Bitte ändern Sie Ihren Text, um eventuell vorhandene Obszönitäten zu entfernen.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Die Beschreibung der Initiative muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Die Initiativenbeschreibung muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Der Titel der Initiative muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Der Titel der Initiative muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Die Problembeschreibung muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Die Problembeschreibung muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Der Titel der Ausgabe muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Der Titel der Ausgabe muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Die Beschreibung der Option muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Die Beschreibung der Option muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Der Titel der Option muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Der Titel der Option muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Die Beschreibung der Petition muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Die Beschreibung der Petition muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Der Titel der Petition muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Der Titel der Petition muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Die Projektbeschreibung muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Die Projektbeschreibung muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Der Projekttitel muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Der Projekttitel muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Die Beschreibung des Vorschlags muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Die Beschreibung des Vorschlags muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Der Titel des Vorschlags muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Der Titel des Vorschlags muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "<PERSON>te gib eine Beschreibung ein", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Die Beschreibung der Frage muss weniger als 80 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Die Beschreibung der Frage muss mindestens 30 Zeichen lang sein", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Der Titel muss kürzer als 80 Zeichen sein", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Der Titel muss mindestens 10 Zeichen lang sein", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Abbrechen", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON><PERSON>, zurück ohne zu speichern", "app.containers.IdeasNewPage.contributionMetaTitle1": "Neuen Beitrag zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Umfrage bearbeiten", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Posten Sie einen Beitrag und beteiligen Sie sich an der Diskussion auf der Mitwirkungsplattform von {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Neue Idee zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Neue Initiative zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Neues Vorhaben zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Sind <PERSON> sic<PERSON>, dass Sie das Fenster schließen möchten ohne zu speichern?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Ihre Antwortentwürfe wurden privat gespeichert. Sie können später zurückkehren, um sie zu vervollständigen.", "app.containers.IdeasNewPage.leaveSurvey": "Umfrage verlassen", "app.containers.IdeasNewPage.leaveSurveyText": "Ihre Antworten werden nicht gespeichert.", "app.containers.IdeasNewPage.optionMetaTitle1": "Neue Option zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Neue Petition zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Neues Projekt zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Neuen Vorschlag zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Neue Frage zum Projekt hinzufügen | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Einladung annehmen", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Einladung zum Unterstützen", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Unterstützer*innen", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "<PERSON>e wurden als Unterstützer*in eingeladen.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Einladung angenommen", "app.containers.IdeasShow.Cosponsorship.pending": "ausstehend", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} am {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Aktueller Status", "app.containers.IdeasShow.MetaInformation.location": "Ort", "app.containers.IdeasShow.MetaInformation.postedBy": "<PERSON><PERSON><PERSON><PERSON> von", "app.containers.IdeasShow.MetaInformation.similar": "Ähnliche Beiträge", "app.containers.IdeasShow.MetaInformation.topics": "Themen", "app.containers.IdeasShow.commentCTA": "Kommentieren", "app.containers.IdeasShow.contributionEmailSharingBody": "Unterstützen Sie den Beitrag „{postTitle}“ unter {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Unterstützen Sie diesen Beitrag: {postTitle}\n", "app.containers.IdeasShow.contributionSharingModalTitle": "Vielen Dank für Ihren Beitrag!", "app.containers.IdeasShow.contributionTwitterMessage": "Unterstützen Sie diesen Beitrag: {postTitle}\n", "app.containers.IdeasShow.contributionWhatsAppMessage": "Unterstützen Sie diesen Beitrag: {postTitle}\n", "app.containers.IdeasShow.currentStatus": "Derzeitiger Status ", "app.containers.IdeasShow.deletedUser": "unbekannte*r <PERSON><PERSON>*in", "app.containers.IdeasShow.ideaEmailSharingBody": "Unterstützen Sie meine Idee „{ideaTitle}“ unter {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "<PERSON><PERSON> Idee unterstützen: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Unterstützen Sie diese Idee: {postTitle}\n", "app.containers.IdeasShow.ideaWhatsAppMessage": "Unterstützen Sie diese Idee: {postTitle}\n", "app.containers.IdeasShow.ideasWhatsAppMessage": "Unterstützen Sie dieses Thema: {postTitle}\n", "app.containers.IdeasShow.imported": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.importedTooltip": "Diese {inputTerm} wurde auf die Plattform hochgeladen.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Unterstützen Sie diese Initiative '{ideaTitle}' unter {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Diesen Vorschlag unterstützen: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Vielen Dank für die Einreichung!", "app.containers.IdeasShow.initiativeTwitterMessage": "Diese Initiative unterstützen: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Diese Initiative unterstützen: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Unterstützen Sie dieses Anliegen„{postTitle}“ unter {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Unterstützen Sie dieses Anliegen: {postTitle}\n", "app.containers.IdeasShow.issueSharingModalTitle": "Vielen Dank für Ihr Vorhaben!", "app.containers.IdeasShow.issueTwitterMessage": "Unterstützen Sie dieses Anliegen: {postTitle}\n", "app.containers.IdeasShow.metaTitle": "Beitrag: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Unterstützen Sie die Option „{postTitle}“ unter {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Unterstützen Sie diese Option: {postTitle}\n", "app.containers.IdeasShow.optionSharingModalTitle": "Ihre Option wurde erfolgreich gepostet!\n", "app.containers.IdeasShow.optionTwitterMessage": "Unterstützen Sie diese Option: {postTitle}\n", "app.containers.IdeasShow.optionWhatsAppMessage": "Unterstützen Sie diese Option: {postTitle}\n", "app.containers.IdeasShow.petitionEmailSharingBody": "Unterstützen Sie diese Petition '{ideaTitle}' auf {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Unterstütze diese Petition: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Vielen Dank für die Einreichung!", "app.containers.IdeasShow.petitionTwitterMessage": "Unterstütze diese Petition: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Unterstütze diese Petition: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Unterstützen Sie das Projekt „{postTitle}“ unter {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Unterstützen Sie dieses Projekt: {postTitle}\n", "app.containers.IdeasShow.projectSharingModalTitle": "<PERSON><PERSON><PERSON> Dank, dass Sie Ihr Projekt eingereicht haben!\n", "app.containers.IdeasShow.projectTwitterMessage": "Unterstützen Sie dieses Projekt: {postTitle}\n", "app.containers.IdeasShow.projectWhatsAppMessage": "Unterstützen Sie dieses Projekt: {postTitle}\n", "app.containers.IdeasShow.proposalEmailSharingBody": "Unterstütze diesen Vorschlag '{ideaTitle}' auf {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Vorschlag unterstützen: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Vielen Dank für die Einreichung!", "app.containers.IdeasShow.proposalTwitterMessage": "Diesen Vorschlag unterstützen: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Diesen Vorschlag unterstützen: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Verbleibende Zeit zum Abstimmen:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} von {<PERSON><PERSON><PERSON><PERSON><PERSON>} erforderlichen Stimmen", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Abstimmung abbrechen", "app.containers.IdeasShow.proposals.VoteControl.days": "Tage", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "unsere Richtlinien", "app.containers.IdeasShow.proposals.VoteControl.hours": "Stunden", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status und Stimmen", "app.containers.IdeasShow.proposals.VoteControl.minutes": "Minuten", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Mehr Informationen", "app.containers.IdeasShow.proposals.VoteControl.vote": "Auswählen", "app.containers.IdeasShow.proposals.VoteControl.voted": "Ausgewählt", "app.containers.IdeasShow.proposals.VoteControl.votedText": "<PERSON>e werden benachrichtigt, wenn dieser Vorschlag zum nächsten Schritt übergeht. {x, plural, =0 {Es sind noch {xDays} übrig.} one {Es sind noch {xDays} übrig.} other {Es sind noch {xDays} übrig.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Ihre Stimme ist abgegeben worden!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Leider können Sie über diesen Vorschlag nicht abstimmen. Lesen Sie in {link} warum.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {we<PERSON>ger als ein Tag} one {ein Tag} other {# Tage}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {keine <PERSON>} one {1 Stimme} other {# Stimmen}}", "app.containers.IdeasShow.questionEmailSharingBody": "Nehmen Sie an der Diskussion über die Frage „{postTitle}“ unter {postUrl} teil!", "app.containers.IdeasShow.questionEmailSharingSubject": "Beteiligen Sie sich an der Diskussion: {postTitle}\n", "app.containers.IdeasShow.questionSharingModalTitle": "Ihre Frage wurde erfolgreich gepostet!\n", "app.containers.IdeasShow.questionTwitterMessage": "Beteiligen Sie sich an der Diskussion: {postTitle}\n", "app.containers.IdeasShow.questionWhatsAppMessage": "Beteiligen Sie sich an der Diskussion: {postTitle}\n", "app.containers.IdeasShow.reportAsSpamModalTitle": "Warum wollen Sie dies als Spam melden?", "app.containers.IdeasShow.share": "Teilen", "app.containers.IdeasShow.sharingModalSubtitle": "Erreichen Sie mehr Menschen und verschaffen Sie Ihrer Stimme Gehör.", "app.containers.IdeasShow.sharingModalTitle": "Vielen Dank für Ihre Idee!", "app.containers.Navbar.completeOnboarding": "Profil vervollständigen", "app.containers.Navbar.completeProfile": "Profil vervollständigen", "app.containers.Navbar.confirmEmail2": "E-Mail-Adresse bestätigen", "app.containers.Navbar.unverified": "Nicht verifiziert", "app.containers.Navbar.verified": "Verifiziert", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON> folgen", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON><PERSON> te<PERSON>", "app.containers.NewAuthModal.completeYourProfile": "Profil vervollständigen", "app.containers.NewAuthModal.confirmYourEmail": "E-Mail-Adresse bestätigen", "app.containers.NewAuthModal.logIn": "Anmelden", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Um fortzufahren, lesen Si<PERSON> bitte die nachstehenden Bedingungen.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Bitte vervollständigen Sie Ihr Profil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Zurück zu den Anmeldeoptionen", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Sie haben noch kein Konto? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Registrierung", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Der Code muss 4-stellig sein.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "<PERSON>ter mit FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "<PERSON><PERSON> dieser Plattform sind keine Authentifizierungsmethoden aktiviert.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON><PERSON> <PERSON> fort<PERSON>, er<PERSON><PERSON><PERSON><PERSON> sich damit ein<PERSON>, E-Mails von dieser Plattform zu erhalten. Sie können auf der Seite \"Einstellungen\" auswählen, welche E-Mails Sie erhalten möchten.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-Mail-Adresse", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Geben Sie eine E-Mail-Adresse im richtigen Format an, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Geben Sie eine E-Mail-Adresse an", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Geben Sie Ihre E-Mail-Adresse ein, um fortzufahren.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Passwort vergessen?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Melden Si<PERSON> sich mit Ihrem <PERSON> an: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Bitte geben Sie Ihr Passwort ein", "app.containers.NewAuthModal.steps.Password.password": "Passwort", "app.containers.NewAuthModal.steps.Password.rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON>cht ausw<PERSON>hlen, wenn Sie einen öffentlichen Computer benutzen", "app.containers.NewAuthModal.steps.Success.allDone": "<PERSON><PERSON> er<PERSON>t", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Los geht's!", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Ihre Identität wurde überprüft. Sie sind jetzt ein Mitglied der Community auf dieser Plattform.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Sie sind jetzt verifiziert!", "app.containers.NewAuthModal.steps.close": "Schließen", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Woran sind Sie interessiert?", "app.containers.NewAuthModal.youCantParticipate": "<PERSON>e können nicht teilnehmen", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {keine ungeprüften Benachrichtigungen} one {1 ungeprüfte Benachrichtigung} other {# ungeprüfte Benachrichtigungen}}", "app.containers.NotificationMenu.adminRightsReceived": "Sie sind jetzt Admin der Plattform", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "<PERSON><PERSON> Kommentar zu \"{postTitle}\" wur<PERSON> von e<PERSON>m <PERSON>, weil\n      {reasonCode, select, irrelevant {er sachfremd ist} inappropriate {der Inhalt unangemessen ist} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} hat Ihre Unterstützungs-Einladung angenommen", "app.containers.NotificationMenu.deletedUser": "Unbekannte Person", "app.containers.NotificationMenu.error": "Benachrichtigungen konnten nicht geladen werden", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} hat einen Ihnen zugewiesenen Beitrag intern kommentiert", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} hat einen Beitrag intern kommentiert, den Sie intern kommentiert haben", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} hat einen internen Kommentar zu einem Beitrag in einem von Ihnen verwalteten Projekt abgegeben", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} hat einen nicht zugewiesenen Beitrag in einem nicht verwalteten Projekt intern kommentiert", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} hat auf Ihren internen Kommentar geantwortet", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} hat <PERSON><PERSON> e<PERSON>n, einen Beitrag zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} hat <PERSON><PERSON> e<PERSON>, eine Idee zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} hat <PERSON><PERSON> e<PERSON>n, einen Vorschlag zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} hat <PERSON><PERSON> e<PERSON>n, ein <PERSON> zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} hat <PERSON><PERSON> e<PERSON>n, eine <PERSON>tion zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} hat <PERSON><PERSON> e<PERSON>, eine Petition zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} hat <PERSON><PERSON> e<PERSON>n, ein <PERSON>jekt zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} hat <PERSON><PERSON> e<PERSON>n, einen Vorschlag zu unterstützen", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} hat <PERSON><PERSON> e<PERSON>, eine <PERSON> zu unterstützen", "app.containers.NotificationMenu.loadMore": "{org<PERSON><PERSON>, select, <PERSON>ü<PERSON> {Mehr Mängelmeldungen anzeigen} other {Mehr Ideen anzeigen...}}", "app.containers.NotificationMenu.loading": "Benachrichtigungen werden geladen...", "app.containers.NotificationMenu.mentionInComment": "{name} hat Sie in einem Kommentar erwähnt", "app.containers.NotificationMenu.mentionInInternalComment": "{name} hat Sie in einem internen Kommentar erwähnt", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} hat Sie in einem offiziellen Update erwähnt", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Sie haben Ihre Umfrage nicht abgeschickt", "app.containers.NotificationMenu.noNotifications": "Sie haben noch keine Benachrichtigungen", "app.containers.NotificationMenu.notificationsLabel": "Mitteilungen", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} hat ein offizielles Update zu einem Beitrag gegeben, dem <PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} hat ein offizielles Update zu einer Idee gegeben, der Si<PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} hat ein offizielles Update zu einem Vorschlag gegeben, dem <PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} hat ein offizielles Update zu einem Thema gegeben, dem <PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} hat ein offizielles Update zu einer Option gegeben, der Si<PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gab ein offizielles Update zu einer Petition, der Si<PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} hat ein offizielles Update zu einem Projekt gegeben, dem <PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gab ein offizielles Update zu einem Vorschlag, dem <PERSON> folgen", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} hat ein offizielles Update zu einer Frage gegeben, der Si<PERSON> folgen", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} wurde Ihnen zugewiesen", "app.containers.NotificationMenu.projectModerationRightsReceived": "Sie sind jetzt ein*e Projektmanager*in von {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} ist in einer neuen Phase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} wird am  {phaseStartAt} in eine neue Phase eintreten", "app.containers.NotificationMenu.projectPublished": "Ein neues Projekt wurde veröffentlicht", "app.containers.NotificationMenu.projectReviewRequest": "{name} hat die Freigabe zur Veröffentlichung des Projekts \"{projectTitle}\" beantragt", "app.containers.NotificationMenu.projectReviewStateChange": "{name} hat \"{projectTitle}\" zur Veröffentlichung freigegeben", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "Der Status von {ideaTitle} hat sich zu {status} geändert", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} hat die gewünschte Anzahl an Stimmen erreicht", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} hat Ihre Einladung angenommen", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} hat einen Beitrag kommentiert, dem <PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} hat eine Idee kommentiert, der Si<PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} hat einen Vorschlag kommentiert, dem <PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} hat ein Thema kommentiert, dem <PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} hat eine Option kommentiert, der Si<PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} hat eine Petition kommentiert, der Si<PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} hat ein Projekt kommentiert, dem <PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} hat einen Vorschlag kommentiert, dem <PERSON> folgen", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} hat eine Frage kommentiert, der Si<PERSON> folgen", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} hat \"{postTitle}\" als Spam gemeldet", "app.containers.NotificationMenu.userReactedToYourComment": "{name} hat auf Ihren Kommentar reagiert", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} hat einen Kommentar zu '{postTitle}' als Spam gemeldet", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Sie haben Ihre Stimmen nicht abgegeben", "app.containers.NotificationMenu.votingBasketSubmitted": "Sie haben erfolgreich gewählt", "app.containers.NotificationMenu.votingLastChance": "<PERSON><PERSON><PERSON>, <PERSON> {phaseTitle} abzust<PERSON><PERSON>", "app.containers.NotificationMenu.votingResults": "{phaseTitle} Abstimmungsergebnisse veröffentlicht", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} hat Ihnen {postTitle} zu<PERSON><PERSON>t", "app.containers.PasswordRecovery.emailError": "Dies sieht nicht nach einer gültigen E-Mail-Adresse aus", "app.containers.PasswordRecovery.emailLabel": "E-Mail", "app.containers.PasswordRecovery.emailPlaceholder": "Meine E-Mail-Adresse", "app.containers.PasswordRecovery.helmetDescription": "Zurücksetzen des Passworts", "app.containers.PasswordRecovery.helmetTitle": "Setzen Sie ihr Passwort zurück", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "<PERSON>n diese E-Mail-Adresse auf der Plattform registriert ist, wurde ein Link zum Zurücksetzen des Passworts versandt.", "app.containers.PasswordRecovery.resetPassword": "<PERSON> senden", "app.containers.PasswordRecovery.submitError": "Wir konnten kein mit dieser E-Mail verknüpftes Konto finden. Sie können stattdessen versuchen, sich zu registrieren.", "app.containers.PasswordRecovery.subtitle": "Wohin sollen wir den Link senden, um ein neues Passwort zu wählen?", "app.containers.PasswordRecovery.title": "Passwort zurücksetzen", "app.containers.PasswordReset.helmetDescription": "Passwort zurücksetzen", "app.containers.PasswordReset.helmetTitle": "Setzen Sie ihr Passwort zurück", "app.containers.PasswordReset.login": "Anmelden", "app.containers.PasswordReset.passwordError": "Das Passwort muss mindestens 8 Zeichen lang sein", "app.containers.PasswordReset.passwordLabel": "Passwort", "app.containers.PasswordReset.passwordPlaceholder": "Neues Passwort", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Ihr Passwort wurde erfolgreich aktualisiert.", "app.containers.PasswordReset.pleaseLogInMessage": "Bitte melden Si<PERSON> sich mit Ihrem neuen Passwort an.", "app.containers.PasswordReset.requestNewPasswordReset": "Neuen Passwortreset anfordern", "app.containers.PasswordReset.submitError": "Etwas ging schief. Bitte versuchen Sie es später nochmal.", "app.containers.PasswordReset.title": "Setzen Sie ihr Passwort zurück", "app.containers.PasswordReset.updatePassword": "Neues Kennwort bestätigen", "app.containers.ProjectFolderCards.allProjects": "{org<PERSON><PERSON>, select, <PERSON>ü<PERSON> {Alle Bezirke oder Projekte} other {Alle Projekte}}", "app.containers.ProjectFolderCards.currentlyWorkingOn": "Projekte der {orgName}", "app.containers.ProjectFolderShowPage.editFolder": "Ordner bearbeiten", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "<PERSON><PERSON> zu diesem Projekt", "app.containers.ProjectFolderShowPage.metaTitle1": "Ordner: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "<PERSON><PERSON> lesen", "app.containers.ProjectFolderShowPage.seeLess": "<PERSON><PERSON>", "app.containers.ProjectFolderShowPage.share": "Teilen", "app.containers.Projects.PollForm.document": "Dokument", "app.containers.Projects.PollForm.formCompleted": "Danke! Ihr Beitrag ist eingegangen.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Sie haben bereits an dieser Umfrage teilgenommen.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Diese Umfrage kann nur durchgeführt werden, wenn diese Phase aktiv ist.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Diese Abstimmung ist derzeit nicht aktiviert", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Es ist derzeit nicht möglich an dieser Abstimmung teilzunehmen.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Die Abstimmung ist nicht mehr verfügbar, da dieses Projekt nicht mehr aktiv ist.", "app.containers.Projects.PollForm.sendAnswer": "Einreichen", "app.containers.Projects.a11y_phase": "Phase {PhasenNummer}: {PhasenTitel}", "app.containers.Projects.a11y_phasesOverview": "Phasenübersicht", "app.containers.Projects.a11y_titleInputs": "Alle Beiträge zu diesem Projekt", "app.containers.Projects.a11y_titleInputsPhase": "Alle in dieser Phase abgegebenen Beiträge", "app.containers.Projects.accessRights": "Zugriffsrechte", "app.containers.Projects.addedToBasket": "<PERSON><PERSON><PERSON>nz<PERSON>", "app.containers.Projects.allocateBudget": "<PERSON><PERSON> Sie Ihren Haushalt zu", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "<PERSON><PERSON> Warenkorb wurde e<PERSON>eicht!", "app.containers.Projects.contributions": "Beiträge", "app.containers.Projects.createANewPhase": "Neue Phase erstellen", "app.containers.Projects.currentPhase": "Aktuelle Phase", "app.containers.Projects.document": "Dokument", "app.containers.Projects.editProject": "Projekt bearbeiten", "app.containers.Projects.emailSharingBody": "Was halten Sie von dieser Initiative? Stimmen Sie ab und beteiligen Sie sich an der Diskussion unter {initiativeUrl} , um Ihrer Stimme Gehör zu verschaffen!", "app.containers.Projects.emailSharingSubject": "Unterstütze meine Initiative: {initiativeTitle}.", "app.containers.Projects.endedOn": "Endete am {date}", "app.containers.Projects.events": "Veranstaltungen", "app.containers.Projects.header": "Projekte", "app.containers.Projects.ideas": "Ideen", "app.containers.Projects.information": "Informieren", "app.containers.Projects.initiatives": "Initiativen", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Dokument kommentieren", "app.containers.Projects.invisibleTitlePhaseAbout": " ", "app.containers.Projects.invisibleTitlePoll": "Zur Abstimmung", "app.containers.Projects.invisibleTitleSurvey": "An der Umfrage teilnehmen", "app.containers.Projects.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "Sie sehen die Daten in Echtzeit. Die Teilnehmendenzahlen werden für Admins ständig aktualisiert. <PERSON><PERSON> beachten Si<PERSON>, dass normale Nutzende zwischengespeicherte Daten sehen, was zu leichten Abweichungen bei den Zahlen führen kann.", "app.containers.Projects.location": "Adresse oder Standort:", "app.containers.Projects.manageBasket": "Warenkorb bearbeiten", "app.containers.Projects.meetMinBudgetRequirement": "Erfüllen Sie das Mindestbudget, um Ihren Warenkorb einzureichen.", "app.containers.Projects.meetMinSelectionRequirement": "Treffen Sie die erforderliche Auswahl, um Ihren Warenkorb einzureichen.", "app.containers.Projects.metaTitle1": "Projekt: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Ein Mindestbudget ist erforderlich", "app.containers.Projects.myBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navPoll": "Blitz-Umfrage", "app.containers.Projects.navSurvey": "Umfrage", "app.containers.Projects.newPhase": "Neue Phase", "app.containers.Projects.nextPhase": "Nächste Phase", "app.containers.Projects.noEndDate": "<PERSON><PERSON>", "app.containers.Projects.noItems": "Sie haben noch keine Projekte ausgewählt", "app.containers.Projects.noPastEvents": "<PERSON><PERSON> vergangenen Veranstaltungen zum Anzeigen", "app.containers.Projects.noPhaseSelected": "Keine Phase ausgewählt", "app.containers.Projects.noUpcomingOrOngoingEvents": "Zurzeit sind keine Veranstaltungen geplant.", "app.containers.Projects.offlineVotersTooltip": "Diese Z<PERSON> spiegelt keine analogen Wähler*innen wider.", "app.containers.Projects.options": "Optionen", "app.containers.Projects.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.participantsTooltip4": "Diese Z<PERSON> spiegelt auch anonyme Umfragen wider. Anonyme Umfragen sind möglich, wenn die Umfragen für alle zugänglich sind (siehe {accessRightsLink} für dieses Projekt).", "app.containers.Projects.pastEvents": "Vergangene Veranstaltungen", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "Phasen", "app.containers.Projects.previousPhase": "Vorherige Phase", "app.containers.Projects.project": "{<PERSON><PERSON><PERSON>, select, <PERSON><PERSON><PERSON> {Bezirke} other {Projekte}}", "app.containers.Projects.projectTwitterMessage": "Verschaffen Sie sich Gehör! Beteiligen Sie sich an {projectName} | {orgName}\n", "app.containers.Projects.projects": "Projekte", "app.containers.Projects.proposals": "Vorschläge", "app.containers.Projects.questions": "Fragen", "app.containers.Projects.readLess": "<PERSON><PERSON> lesen", "app.containers.Projects.readMore": "<PERSON><PERSON> mehr", "app.containers.Projects.removeItem": "Artikel en<PERSON>fernen", "app.containers.Projects.requiredSelection": "Erforderliche Auswahl", "app.containers.Projects.reviewDocument": "Dokument kommentieren", "app.containers.Projects.seeTheContributions": "Zu den Beiträgen", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON> den Ideen", "app.containers.Projects.seeTheInitiatives": "<PERSON><PERSON>", "app.containers.Projects.seeTheIssues": "<PERSON><PERSON>hab<PERSON>", "app.containers.Projects.seeTheOptions": "Siehe die Optionen\n", "app.containers.Projects.seeThePetitions": "<PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheProjects": "Zu den Projekten", "app.containers.Projects.seeTheProposals": "Vorschläge an<PERSON>hen", "app.containers.Projects.seeTheQuestions": "<PERSON><PERSON><PERSON> an<PERSON>", "app.containers.Projects.seeUpcomingEvents": "Bevorstehende Veranstaltungen", "app.containers.Projects.share": "Teilen", "app.containers.Projects.shareThisProject": "Das Projekt teilen", "app.containers.Projects.submitMyBasket": "Warenko<PERSON> absenden", "app.containers.Projects.survey": "Umfrage", "app.containers.Projects.takeThePoll": "Zur Abstimmung", "app.containers.Projects.takeTheSurvey": "Zur Umfrage", "app.containers.Projects.timeline": "Zeitleiste", "app.containers.Projects.upcomingAndOngoingEvents": "Aktuelle Veranstaltungen", "app.containers.Projects.upcomingEvents": "Aktuelle Veranstaltungen", "app.containers.Projects.whatsAppMessage": "{projectName} | von der Beteiligungsplattform {orgName}", "app.containers.Projects.yourBudget": "Gesamtbudget", "app.containers.ProjectsIndexPage.metaDescription": "Erkunde alle laufende Projekte von {orgName} um zu verstehen, wie du daran teilnehmen kannst. Diskutiere über die lokalen Projekte, die dir am wichtigsten sind.", "app.containers.ProjectsIndexPage.metaTitle1": "Projekte • {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projekte", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Ich nehme teil", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Bitte {signInLink} oder {signUpLink} zu<PERSON><PERSON>, um teilzunehmen.", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "<PERSON><PERSON><PERSON> diese Aktivität ist derzeit keine Teilnahme möglich.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "melden Sie sich an", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "registrieren Si<PERSON> sich", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Ich ziehe meine Teilnahme zurück", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {kein<PERSON>} one {# Teilnehmende:r} other {# Teilnehmende}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Warnung: Die eingebettete Umfrage kann Probleme mit der Zugänglichkeit für Screenreader-Nutzer*innen haben. Wenn Si<PERSON> auf Probleme stoßen, wenden Si<PERSON> sich bitte an den Plattform-Admin, um einen Link zur Umfrage von der ursprünglichen Plattform zu erhalten. Alternativ können Si<PERSON> auch andere Möglichkeiten zum Ausfüllen der Umfrage anfordern.", "app.containers.ProjectsShowPage.process.survey.survey": "Umfrage", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "Um zu erfahren, ob Sie an dieser Umfrage teilnehmen können, besuchen Si<PERSON> bitte zuerst {logInLink} die Plattform.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "An der Umfrage kann nur teilgenommen werden, wenn diese Phase aktiv ist.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Bitte {completeRegistrationLink}, um an der Umfrage teilzunehmen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON>ider verfügst du über keine Rechte, um an dieser Umfrage teilzunehmen.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Die Teilnahme an dieser Umfrage erfordert eine Überprüfung Ihrer Identität. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Diese Umfrage ist nicht länger zugä<PERSON>, da dieses Projekt nicht mehr aktiv ist.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "Registrierung abschließen", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "anmelden", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "registrieren", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Konto verifizieren.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Nur bestimmte Nutzer*innen können dieses Dokument kommentieren. Bitte zuerst {signUpLink} oder {logInLink}.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Dieses Dokument kann nur überprüft werden, wenn diese Phase aktiv ist.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Bitte {completeRegistrationLink} , um das Dokument zu lesen.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "<PERSON><PERSON> sind Si<PERSON> nicht berechtigt, dieses Dokument zu kommentieren.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Für die Durchsicht dieses Dokuments ist eine Verifizierung Ihres Kontos erforderlich. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Das Dokument ist nicht verfügbar, da dieses Projekt nicht aktiv ist.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(davon 1 analog)} other {(davon # analog)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 Option} other {# Optionen}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Der Prozentsatz der Teilnehmenden, die diese Option gewählt haben.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Der Prozentsatz der Gesamtstimmen, die diese Option erhalten hat.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Kosten:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "<PERSON><PERSON> anzeigen", "app.containers.ReactionControl.a11y_likesDislikes": "Gefällt mir insgesamt: {likesCount}, Gefällt mir nicht insgesamt: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Sie haben Ihre Abneigung gegen diese Eingabe erfolgreich aufgehoben.", "app.containers.ReactionControl.cancelLikeSuccess": "Sie haben Ihr Like für diese Eingabe erfolgreich gelöscht.", "app.containers.ReactionControl.dislikeSuccess": "Sie haben diese Eingabe erfolgreich abgelehnt.", "app.containers.ReactionControl.likeSuccess": "Diese Eingabe hat Ihnen gut gefallen.", "app.containers.ReactionControl.reactionErrorSubTitle": "Aufgrund eines Fehlers konnte Ihre Reaktion nicht gespeichert werden. Bitte versuchen Sie es in ein paar <PERSON>uten erneut.", "app.containers.ReactionControl.reactionSuccessTitle": "Ihre Reaktion wurde erfolgreich gespeichert!", "app.containers.ReactionControl.vote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ReactionControl.voted": "Abgesti<PERSON><PERSON>", "app.containers.SearchInput.a11y_cancelledPostingComment": "Posten des Kommentars abgebrochen.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} Komme<PERSON>re wurden geladen.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# Veranstaltungen geladen} one {# Veranstaltung geladen} other {# Veranstaltungen geladen}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# Veranstaltungen geladen} one {# Veranstaltung geladen} other {# Veranstaltungen geladen}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# Suchergebnisse geladen} one {# Suchergebnis geladen} other {# Suchergebnisse geladen}}.", "app.containers.SearchInput.removeSearchTerm": "Suchbegriff entfernen", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "<PERSON><PERSON> Begriff: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect ist die vom französischen Staat vorgeschlagene Lösung, um die Anmeldung bei mehr als 700 Online-Diensten zu sichern und zu vereinfachen.", "app.containers.SignIn.or": "<PERSON><PERSON>", "app.containers.SignIn.signInError": "Die angegebenen Informationen sind nicht korrekt. Klicke bitte auf 'Passwort vergessen', um das Passwort zurückzusetzen.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Verwenden Sie FranceConnect, um sich anzumelden, zu registrieren oder Ihr Konto zu verifizieren.", "app.containers.SignIn.whatIsFranceConnect": "Was ist France Connect?", "app.containers.SignUp.adminOptions2": "<PERSON><PERSON><PERSON> und Projektmanager*innen", "app.containers.SignUp.backToSignUpOptions": "Zur<PERSON> zu den Registrierungsoptionen", "app.containers.SignUp.continue": "Fortfahren", "app.containers.SignUp.emailConsent": "<PERSON><PERSON> der Anmeldung erkl<PERSON>ren Si<PERSON> sich damit ein<PERSON>den, E-Mails von dieser Plattform zu erhalten. Sie können auf der Seite \"Meine Einstellungen\" auswählen, welche E-Mails Sie erhalten möchten.", "app.containers.SignUp.emptyFirstNameError": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Vornamen ein", "app.containers.SignUp.emptyLastNameError": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Familienname ein", "app.containers.SignUp.firstNamesLabel": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.goToLogIn": "Sie haben bereits ein Konto? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Ich habe {link} gelesen.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Ich stimme {link} zu.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "Ich stimme zu, dass diese Daten für meine Anmeldung auf mitgestalten.wien.gv.at übernommen werden. Weitere Informationen finden Sie in {link}.", "app.containers.SignUp.invitationErrorText": "Ihre Einladung ist abgelaufen oder wurde bereits verwendet. Wenn Sie den Einladungslink bereits verwendet haben, um ein Konto zu erstellen, versuchen Si<PERSON> sich anzumelden. Andernfalls registrieren Si<PERSON> sich, um ein neues Konto zu erstellen.", "app.containers.SignUp.lastNameLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Folgen Sie geographischen Gebieten oder Stadtteilen, um über sie informiert zu werden:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Folgen Sie Ihren Lieblingsthemen, um über sie informiert zu werden:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Einstellungen speichern", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Akzeptieren Sie unsere Datenschutzbestimmungen, um fortzufahren", "app.containers.SignUp.signUp2": "Registrieren", "app.containers.SignUp.skip": "<PERSON><PERSON> Schritt überspringen", "app.containers.SignUp.tacError": "Sie müssen unseren Nutzungsbedingungen zustimmen, um fortfahren zu können", "app.containers.SignUp.thePrivacyPolicy": "die Verarbeitung meiner personenbezogenen Daten zu den in den Datenschutzinformationen angeführten Zwecken", "app.containers.SignUp.theTermsAndConditions": "den Nutzungsbedingungen", "app.containers.SignUp.unknownError": "{<PERSON><PERSON><PERSON>, select, LiberalDemocrats {Offen<PERSON> hast du bereits versucht, dich anzumelden, ohne den Vorgang abzuschließen. Klicke stattdessen auf „Anmelden“ und verwende die beim letzten Versuch gewählten Anmeldedaten.} other {Ein Fehler ist aufgetreten. Bitte versuche es später erneut.}}", "app.containers.SignUp.viennaConsentEmail": "E-Mail-Adresse", "app.containers.SignUp.viennaConsentFirstName": "Vorname (sofern vorhanden)", "app.containers.SignUp.viennaConsentFooter": "Sie können Ihre Informationen nachträglich im Profil ändern. Wenn Sie bereits ein Konto mit der selben E-Mail-Adresse auf mitgestalten.wien.gv.at haben, können Sie es jetzt mit Ihrem Stadt Wien Konto verknüpfen.", "app.containers.SignUp.viennaConsentHeader": "Folgende Daten werden bei der Anmeldung einmalig übernommen:", "app.containers.SignUp.viennaConsentLastName": "Familienname (sofern vorhanden)", "app.containers.SignUp.viennaConsentUserName": "Nutzer*innenname", "app.containers.SignUp.viennaDataProtection": "den Datenschutzbedingungen", "app.containers.SiteMap.contributions": "Beiträge", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.options": "Optionen", "app.containers.SiteMap.projects": "Projekte", "app.containers.SiteMap.questions": "Fragen", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Gespeichert!", "app.containers.SpamReport.inappropriate": "Ist unangemessen oder anstößig", "app.containers.SpamReport.messageError": "<PERSON><PERSON> des Formulars trat ein <PERSON> auf, bitte versuche es erneut.", "app.containers.SpamReport.messageSuccess": "<PERSON><PERSON><PERSON> wurde gesendet", "app.containers.SpamReport.other": "<PERSON><PERSON>", "app.containers.SpamReport.otherReasonPlaceholder": "Beschreibung", "app.containers.SpamReport.wrong_content": "Das gehört hier nicht her", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Entferne Profilbild", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "<PERSON>hre Stimmen zu Vorschlägen, über die noch abgestimmt werden kann, werden gelöscht. Stimmen zu Vorschlägen, bei denen die Abstimmungsfrist abgelaufen ist, werden nicht gelöscht.", "app.containers.UsersEditPage.addPassword": "Passwort hinzufügen", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Teilnahme an Projekten für verifizierte Bürger:innen.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Werden Sie verifizierter Bürger", "app.containers.UsersEditPage.bio": "<PERSON>ber dich", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "<PERSON><PERSON> kann nicht bearbeiten, weil es verifizierte Informationen enthält", "app.containers.UsersEditPage.buttonSuccessLabel": "Gespeichert!", "app.containers.UsersEditPage.cancel": "Abbrechen", "app.containers.UsersEditPage.changeEmail": "E-Mail-<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changePassword2": "Passwort ändern", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON>te hier klicken, um die Verifizierung zu aktualisieren.", "app.containers.UsersEditPage.conditionsLinkText": "unsere Nutzungsbedingungen", "app.containers.UsersEditPage.contactUs": "{<PERSON><PERSON><PERSON>, select, wien {Wenn <PERSON>e besser verstehen möchten, wie wir mit Ihren Daten umgehen, finden Sie im Footer Informationen zum Impressum und Datenschutz.} other {Sie gehen aus einem anderen Grund? {feedbackLink} und vielleicht können wir Ihnen helfen.}}.", "app.containers.UsersEditPage.deleteAccountSubtext": "<PERSON><PERSON> bedauern, dass Si<PERSON> gehen.", "app.containers.UsersEditPage.deleteMyAccount": "<PERSON>nen Account löschen", "app.containers.UsersEditPage.deleteYourAccount": "Konto löschen", "app.containers.UsersEditPage.deletionSection": "<PERSON><PERSON> Account lö<PERSON>", "app.containers.UsersEditPage.deletionSubtitle": "Diese Aktion kann nicht rückgängig gemacht werden. Die Inhalte, die Sie auf der Plattform veröffentlicht haben, werden anonymisiert. Wenn Sie alle Ihre Inhalte löschen möchten, können Sie <NAME_EMAIL> kontaktieren.", "app.containers.UsersEditPage.email": "E-Mail", "app.containers.UsersEditPage.emailEmptyError": "Geben Sie eine E-Mail Adresse an", "app.containers.UsersEditPage.emailInvalidError": "Geben Sie eine E-Mail-Adresse im richtigen Format an, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "<PERSON>te hier klicken", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/BGVTCYeO#source={url}", "app.containers.UsersEditPage.firstNames": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON><PERSON> Si<PERSON> einen Vornamen an", "app.containers.UsersEditPage.h1": "<PERSON><PERSON>e Kontoinformationen", "app.containers.UsersEditPage.h1sub": "Bearbeiten Sie Ihre Kontoinformationen", "app.containers.UsersEditPage.image": "Avatar-Bild", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON><PERSON>, um ein Profilbild auszuwählen  (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Alle Einstellungen für Ihr Profil", "app.containers.UsersEditPage.language": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.lastName": "Nachname", "app.containers.UsersEditPage.lastNameEmptyError": "Geben Sie Familiennamen an", "app.containers.UsersEditPage.loading": "Lädt...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Sie können Ihre E-Mail-Adresse oder Ihr Passwort hier ändern.", "app.containers.UsersEditPage.loginCredentialsTitle": "Anmeldedaten", "app.containers.UsersEditPage.messageError": "Wir konnten Ihr Profil nicht speichern. Versuchen Sie es später noch einmal oder <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "<PERSON>hr Profil wurde ges<PERSON>.", "app.containers.UsersEditPage.metaDescription": "Dies ist die Seite mit den Profileinstellungen von {firstName} {lastName} auf der Online-Beteiligungsplattform {tenantName}. Hier können Sie Ihre Identität überprüfen, Ihre Kontoinformationen bearbeiten, Ihr Konto löschen und Ihre E-Mail-Einstellungen ändern.", "app.containers.UsersEditPage.metaTitle1": "Profileinstellungen von {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "<PERSON><PERSON><PERSON> auf den Button unten klicken, haben wir keine Möglichkeit mehr, <PERSON><PERSON> Konto wiederherzustellen.", "app.containers.UsersEditPage.noNameWarning2": "Ihr Name wird derzeit auf der Plattform angezeigt als: \"{displayName}\", da <PERSON>e Ihren Namen nicht eingegeben haben. Dies ist ein automatisch generierter Name. Wenn Sie ihn ändern möchten, geben Sie bitte unten Ihren Namen ein.", "app.containers.UsersEditPage.notificationsSubTitle": "Welche Arten von E-Mail-Benachrichtigungen möchtest du erhalten? ", "app.containers.UsersEditPage.notificationsTitle": "E-Mail-Benachrichtigungen", "app.containers.UsersEditPage.password": "<PERSON><PERSON><PERSON>en Si<PERSON> ein neues Passwort", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON><PERSON><PERSON> Si<PERSON> ein Passwort zur Verfügung, das mindestens {minimumPasswordLength} <PERSON><PERSON><PERSON> lang ist", "app.containers.UsersEditPage.passwordAddSection": "Ein Passwort hinzufügen", "app.containers.UsersEditPage.passwordAddSubtitle2": "Legen Sie ein Passwort fest und melden Sie sich auf der Plattform an, ohne Ihre E-Mail jedes Mal bestätigen zu müssen.", "app.containers.UsersEditPage.passwordChangeSection": "Ändern Sie Ihr Passwort", "app.containers.UsersEditPage.passwordChangeSubtitle": "Bestätigen Sie Ihr aktuelles Passwort und ändern Sie es in ein neues Passwort.", "app.containers.UsersEditPage.privacyReasons": "<PERSON>n <PERSON> sich Sorgen um Ihre Privatsphäre machen, können <PERSON> {conditionsLink} lesen.", "app.containers.UsersEditPage.processing": "Wird gesendet...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Der Vorname ist bei der Angabe des Nachnamens erforderlich", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON><PERSON> gehen...", "app.containers.UsersEditPage.submit": "Änderungen speichern", "app.containers.UsersEditPage.tooManyEmails": "Erhalten Sie zu viele E-Mails? Sie können Ihre E-Mail-Einstellungen in Ihren Profileinstellungen verwalten.", "app.containers.UsersEditPage.updateverification": "Haben sich Ihre offiziellen Informationen geändert? {reverifyButton}", "app.containers.UsersEditPage.user": "Wann sollen wir Ihnen eine E-Mail schicken, um <PERSON> zu benachrichtigen?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "<PERSON>e können an Projekten teilnehmen, die eine Registrierung erfordern.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Sie sind verifiziert", "app.containers.UsersEditPage.verifyNow": "Jetzt verifizieren", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "<PERSON><PERSON><PERSON><PERSON> herunterladen (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {0 gefällt mir} one {1 gefällt mir} other {# gefällt mir}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "<PERSON><PERSON><PERSON>, in dem dieser Kommentar gepostet wurde:\n", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Kommentare ({commentsCount})", "app.containers.UsersShowPage.editProfile": "<PERSON><PERSON> <PERSON>", "app.containers.UsersShowPage.emptyInfoText": "Sie folgen keinem der Elemente des oben angegebenen Filters.", "app.containers.UsersShowPage.eventsWithCount": "Veranstaltungen ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Folge ich ({followingCount})", "app.containers.UsersShowPage.inputs": "Beiträge", "app.containers.UsersShowPage.invisibleTitlePostsList": "Alle von diesem Teilnehmenden eingereichten Beiträge", "app.containers.UsersShowPage.invisibleTitleUserComments": "<PERSON><PERSON> Kommentare, die von diesem Nutzer / dieser Nutzerin gepostet wurden", "app.containers.UsersShowPage.loadMore": "<PERSON><PERSON> <PERSON>", "app.containers.UsersShowPage.loadMoreComments": "<PERSON><PERSON><PERSON> Kommentare laden", "app.containers.UsersShowPage.loadingComments": "Kommentare werden geladen...", "app.containers.UsersShowPage.loadingEvents": "Veranstaltungen werden geladen...", "app.containers.UsersShowPage.memberSince": "Mit<PERSON>ed seit {date}", "app.containers.UsersShowPage.metaTitle1": "<PERSON><PERSON><PERSON><PERSON> von {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Diese Person hat noch keinen Kommentar gepostet.", "app.containers.UsersShowPage.noCommentsForYou": "Hier gibt es noch keine Kommentare.", "app.containers.UsersShowPage.noEventsForUser": "Sie haben noch keine Veranstaltungen besucht.", "app.containers.UsersShowPage.postsWithCount": "Beiträge ({ideasCount})\n", "app.containers.UsersShowPage.projectFolders": "Projekt-Ordner", "app.containers.UsersShowPage.projects": "Projekte", "app.containers.UsersShowPage.proposals": "Vorschläge", "app.containers.UsersShowPage.seePost": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.surveyResponses": "Antworten ({responses})", "app.containers.UsersShowPage.topics": "Themen", "app.containers.UsersShowPage.tryAgain": "Ein Fehler ist aufgetreten, bitte später erneut versuchen.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Dies ist die Profilseite von {firstName} {lastName} auf der Beteiligungsplattform {orgName}. Hier finden Sie eine Übersicht über alle ihre / seine Beiträge.", "app.containers.VoteControl.close": "Schließen", "app.containers.VoteControl.voteErrorTitle": "Etwas ging schief", "app.containers.admin.ContentBuilder.default": "Standard", "app.containers.admin.ContentBuilder.imageTextCards": "Bild & Textkarten", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & Akkordeons", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 Spalte", "app.containers.admin.ContentBuilder.projectDescription": "Projektbeschreibung", "app.containers.app.navbar.admin": "Plattform verwalten", "app.containers.app.navbar.allProjects": "{org<PERSON><PERSON>, select, <PERSON>ü<PERSON> {Alle Bezirke oder Projekte} other {Alle Projekte}}", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Mobiles Navigationsmenü schließen", "app.containers.app.navbar.editProfile": "Einstellungen", "app.containers.app.navbar.fullMobileNavigation": "Vollständig mobil", "app.containers.app.navbar.logIn": "Anmelden", "app.containers.app.navbar.logoImgAltText": "{orgName} Startseite", "app.containers.app.navbar.myProfile": "<PERSON><PERSON>", "app.containers.app.navbar.search": "<PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "Vollständiges Menü anzeigen", "app.containers.app.navbar.signOut": "Abmelden", "app.containers.eventspage.errorWhenFetchingEvents": "<PERSON><PERSON> Veranstaltungen ist ein Fehler aufgetreten. Bit<PERSON> versuchen Sie, die Seite neu zu laden.", "app.containers.eventspage.events": "Veranstaltungen", "app.containers.eventspage.eventsPageDescription": "Entdecken Sie alle Veranstaltungen, die auf der Beteiligungsplattform {orgName} veröffentlicht wurden.", "app.containers.eventspage.eventsPageTitle1": "Veranstaltungen | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projekte", "app.containers.eventspage.noPastEvents": "<PERSON><PERSON> vergangenen Veranstaltungen zum Anzeigen", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Zurzeit sind keine Veranstaltungen geplant.", "app.containers.eventspage.pastEvents": "Vergangene Veranstaltungen", "app.containers.eventspage.upcomingAndOngoingEvents": "Aktuelle Veranstaltungen", "app.containers.footer.accessibility-statement": "Richtlinie zur Barrierefreiheit", "app.containers.footer.ariaLabel": "Sekundäres", "app.containers.footer.cookie-policy": "Cookierichtlinie", "app.containers.footer.cookieSettings": "Cookie-Einstellungen", "app.containers.footer.feedbackEmptyError": "<PERSON>s kann nicht leer sein.", "app.containers.footer.poweredBy": "Ermöglicht durch", "app.containers.footer.privacy-policy": "Impressum & Datenschutz", "app.containers.footer.siteMap": "Sitemap", "app.containers.footer.terms-and-conditions": "Nutzungsbedingungen", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Abbrechen", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON><PERSON>, zurück ohne zu speichern", "app.containers.ideaHeading.editForm": "Formular bear<PERSON>ten", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Sind <PERSON> sic<PERSON>, dass Sie das Fenster schließen möchten ohne zu speichern?", "app.containers.ideaHeading.leaveIdeaForm": "Ideenformular hinterlassen", "app.containers.ideaHeading.leaveIdeaText": "Ihre Antworten werden nicht gespeichert.", "app.containers.landing.cityProjects": "Projekte", "app.containers.landing.completeProfile": "Vervollständigen Sie Ihr Profil", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON>, {firstName}. Es ist an der Zeit, Ihr Profil zu vervollständigen.", "app.containers.landing.createAccount": "Registrieren", "app.containers.landing.defaultSignedInMessage": "{orgName} hört Ihnen zu. Jetzt sind Sie an der Reihe, Ihre Stimme abzugeben!", "app.containers.landing.doItLater": "Ich werde das später machen", "app.containers.landing.new": "neu", "app.containers.landing.subtitleCity": "<PERSON><PERSON><PERSON> wilkommen auf der Beteiligungsplattform {orgName}", "app.containers.landing.titleCity": "<PERSON><PERSON> uns die Zukunft von {orgName} zusammen gestalten", "app.containers.landing.twitterMessage": "<PERSON><PERSON><PERSON> {ideaTitle} auf", "app.containers.landing.upcomingEventsWidgetTitle": "Aktuelle Veranstaltungen", "app.containers.landing.userDeletedSubtitle": "Sie können jederzeit ein neues Konto erstellen oder {contactLink}, um uns mitzuteilen, was wir verbessern können.", "app.containers.landing.userDeletedSubtitleLinkText": "Schrei<PERSON> uns ein paar Zeilen", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Ihr Konto wurde gelöscht.", "app.containers.landing.userDeletionFailed": "Beim Löschen des Accounts ist ein Fehler aufgetreten. Wir wurden über das Problem benachrichtigt und werden unser Bestes geben, um es zu beheben. Bitte versuche es später erneut.", "app.containers.landing.verifyNow": "Jetzt verifizieren", "app.containers.landing.verifyYourIdentity": "Werde ein verifizierter Bürger", "app.containers.landing.viewAllEventsText": "Alle Veranstaltungen anzeigen", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Zurück zum Ordner", "app.errors.after_end_at": "Das Startdatum ist auf die Zeit nach dem Enddatum gesetzt worden", "app.errors.avatar_carrierwave_download_error": "Die Avatardatei konnte nicht heruntergeladen werden.", "app.errors.avatar_carrierwave_integrity_error": "Die Avatardatei hat ein unzulässiges Format.", "app.errors.avatar_carrierwave_processing_error": "Der Avatar konnte nicht verarbeitet werden.", "app.errors.avatar_extension_blacklist_error": "Die Dateiendung des Avatarbildes ist unzulässig. Zulässige Dateiendungen sind jpg, jpeg, gif und png.", "app.errors.avatar_extension_whitelist_error": "Die Dateiendung des Avatarbildes ist unzulässig. Zulässige Dateiendungen sind jpg, jpeg, gif und png.", "app.errors.banner_cta_button_multiloc_blank": "<PERSON><PERSON>en Sie einen Text für den Button ein.", "app.errors.banner_cta_button_url_blank": "<PERSON><PERSON><PERSON> einen Link ein.", "app.errors.banner_cta_button_url_url": "Geben Si<PERSON> einen gültigen Link ein. Achten Si<PERSON> darauf, dass der Link mit \"https://\" beginnt.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "<PERSON><PERSON>en Sie einen Text für den Button ein.", "app.errors.banner_cta_signed_in_url_blank": "<PERSON><PERSON><PERSON> einen Link ein.", "app.errors.banner_cta_signed_in_url_url": "Geben Si<PERSON> einen gültigen Link ein. Achten Si<PERSON> darauf, dass der Link mit \"https://\" beginnt.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "<PERSON><PERSON>en Sie einen Text für den Button ein.", "app.errors.banner_cta_signed_out_url_blank": "<PERSON><PERSON><PERSON> einen Link ein.", "app.errors.banner_cta_signed_out_url_url": "Geben Si<PERSON> einen gültigen Link ein. Achten Si<PERSON> darauf, dass der Link mit \"https://\" beginnt.", "app.errors.base_includes_banned_words": "Möglicherweise haben Si<PERSON> ein oder mehrere Wörter verwendet, die als obszön gelten. Bitte ändern Sie Ihren Text, um etwaige obszöne Ausdrücke zu entfernen.", "app.errors.body_multiloc_includes_banned_words": "Die Beschreibung enthält Wörter, die als unangemessen angesehen werden.", "app.errors.bulk_import_idea_not_valid": "Die daraus resultierende Idee ist nicht gültig: {value}.", "app.errors.bulk_import_image_url_not_valid": "<PERSON>s konnte kein Bild von {value} heruntergeladen werden. <PERSON><PERSON><PERSON>, dass die URL gültig ist und mit einer Dateierweiterung wie .png oder .jpg endet. Dieses Problem tritt in Zeile {row} auf.", "app.errors.bulk_import_location_point_blank_coordinate": "Ideenstandort mit fehlender Koordinate in {value}. <PERSON>ses Problem tritt in Zeile {row} auf.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "I<PERSON>enstandort mit einer nicht-numerischen Koordinate in {value}. Dieses Problem tritt in Zeile {row} auf.", "app.errors.bulk_import_malformed_pdf": "Die hochgeladene PDF-Date<PERSON> scheint fehlerhaft geformt zu sein. Versuchen Sie, die PDF-Datei erneut aus Ihrer Quelle zu exportieren und dann erneut hochzuladen.", "app.errors.bulk_import_maximum_ideas_exceeded": "Die maximale An<PERSON>hl von {value} Ideen wurde überschritten.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Die maximale Anzahl von {value} Seiten in einer PDF-Datei wurde überschritten.", "app.errors.bulk_import_not_enough_pdf_pages": "Die hochgeladene PDF-Datei hat nicht genügend Seiten - sie sollte mindestens so viele Seiten haben wie die heruntergeladene Vorlage.", "app.errors.bulk_import_publication_date_invalid_format": "Idee mit ungültigem Format für das Veröffentlichungsdatum \"{value}\". Bitte verwenden Sie das Format \"TT-MM-JJJJ\".", "app.errors.cannot_contain_ideas": "Diese Phase enthält {ideasCount, plural, one {eine Idee} other {{ideasCount} Ideen}} und die Teilnahme-Methode, in die Sie sie zu ändern versuchen, unterstützt keine Ideen. Bitte entfernen Sie  {ideasCount, plural, one {die Idee} other {die Ideen}} aus der Phase und versuchen Sie es noch einmal.", "app.errors.cant_change_after_first_response": "<PERSON><PERSON> können dies nicht mehr ändern, da einige Nutzer*innen bereits geantwortet haben", "app.errors.category_name_taken": "Eine Kategorie mit diesem Namen existiert bereits", "app.errors.confirmation_code_expired": "Der Code ist abgelaufen. Bitte fordere einen neuen Code an.", "app.errors.confirmation_code_invalid": "Ungültiger Bestätigungscode. Bitte überprüfen Sie Ihre E-Mail auf den richtigen Code oder versuchen Sie \"Neuen Code senden\"", "app.errors.confirmation_code_too_many_resets": "Du hast den Bestätigungscode zu oft erneut gesendet. Bitte kontaktiere uns, um stattdessen einen Einladungscode zu erhalten.", "app.errors.confirmation_code_too_many_retries": "Sie haben es zu oft versucht. Bitte fordern Sie einen neuen Code an oder versuchen Sie, Ihre E-Mail-Adresse zu ändern.", "app.errors.email_already_active": "Die E-Mail-Adresse {value} in der Zeile {row} gehört bereits einem/r registrierten/m <PERSON><PERSON><PERSON>/in.", "app.errors.email_already_invited": "An die E-Mail-Adresse {value} in der Zeile {row} wurde bereits eine Einladung versandt.", "app.errors.email_blank": "Dies kann nicht leer sein", "app.errors.email_domain_blacklisted": "Bitte verwenden Sie zur Anmeldung eine andere E-Mail-Domain.", "app.errors.email_invalid": "Bitte verwenden Sie eine gültige E-Mail-Adresse.", "app.errors.email_taken": "Es existiert bereits ein Konto mit dieser E-Mail-Adresse. Si<PERSON> können sich stattdessen anmelden.", "app.errors.email_taken_by_invite": "{value} wird bereits von einer ausstehenden Einladung in Anspruch genommen.", "app.errors.emails_duplicate": "Eine oder mehrere doppelte Beiträge wurden für die E-Mail-Adresse {value} wurden in folgende/r Zeile/n gefunden: {rows}", "app.errors.extension_whitelist_error": "Das Format der Datei, die Si<PERSON> hochladen möchten, wird nicht unterstützt.", "app.errors.file_extension_whitelist_error": "Das Format des zu hochladenen Datei, wird nicht unterstützt.", "app.errors.first_name_blank": "Dies kann nicht leer sein", "app.errors.generics.blank": "Dies kann nicht leer sein", "app.errors.generics.invalid": "Dies sieht nicht nach einem gültigen Wert aus", "app.errors.generics.taken": "Diese E-Mail-Adresse besteht bereits. Ein anderes Konto ist damit verbunden.", "app.errors.generics.unsupported_locales": "<PERSON><PERSON> Feld unterstützt nicht die derzeitigen regionalen Einstellungen.", "app.errors.group_ids_unauthorized_choice_moderator": "Als Projektmanager*in können Sie E-Mails nur an Personen senden, die Zugriff auf Ihr(e) Projekt(e) haben", "app.errors.has_other_overlapping_phases": "Projekte dürfen keine überlappenden Phasen haben.", "app.errors.invalid_email": "Die E-Mail-Adresse {value} in der Zeile {row} ist keine gültige E-Mail-Adresse.", "app.errors.invalid_row": "Ein unbekannter Fehler trat bei der Verarbeitung der Zeile {row} auf.", "app.errors.is_not_timeline_project": "Das aktuelle Projekt unterstützt keine Phasen.", "app.errors.key_invalid": "Der Schlüssel kann nur Buchstaben, Zahlen und das Unterstrichzeichen enthalten.", "app.errors.last_name_blank": "Dies kann nicht leer sein", "app.errors.locale_blank": "<PERSON>te wähle eine Sprache", "app.errors.locale_inclusion": "Bitte wähle eine der unterstützten Sprachen", "app.errors.malformed_admin_value": "Der in Zeile {row} gefundene Admin-Wert {value} ist nicht gültig", "app.errors.malformed_groups_value": "Die Gruppe {value} in der Zeile {row} ist keine gültige Gruppe.", "app.errors.max_invites_limit_exceeded1": "Die Anzahl der Einladungen übersteigt die Grenze von 1000.", "app.errors.maximum_attendees_greater_than1": "Die maximale Anzahl der angemeldeten Personen muss größer als 0 sein.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Die maximale Anzahl der angemeldeten Personen muss größer oder gleich der aktuellen Anzahl der angemeldeten Personen sein.", "app.errors.no_invites_specified": "Es konnten keine E-Mail-Adressen gefunden werden.", "app.errors.no_recipients": "Die Kampagne kann nicht verschickt werden, weil es keine Empfänger*innen gibt. Die Gruppe, an die Si<PERSON> senden, ist entweder leer, oder niemand hat dem Empfang von E-Mails zugestimmt.", "app.errors.number_invalid": "<PERSON>te geben Si<PERSON> eine gültige Nummer ein.", "app.errors.password_blank": "Dies kann nicht leer sein", "app.errors.password_invalid": "Bitte überprüfen Sie noch einmal Ihr aktuelles Passwort.", "app.errors.password_too_short": "Das Passwort muss mindestens 8 Zeichen lang sein", "app.errors.resending_code_failed": "Beim Versenden des Bestätigungscodes ist ein Fehler aufgetreten.", "app.errors.slug_taken": "Diese Projekt-URL existiert bereits. Bitte ändern Sie den Projekt-Slug in etwas anderes.", "app.errors.tag_name_taken": "Ein Themen-Tag mit diesem Namen existiert bereits", "app.errors.title_multiloc_blank": "Die Überschrift kann nicht leergelassen werden.", "app.errors.title_multiloc_includes_banned_words": "Der Titel enthält Wörter, die als unangemessen gelten.", "app.errors.token_invalid": "Ein Link zum Zurücksetzen des Passworts kann nur einmal verwendet werden und ist eine Stunde nach dem Versand gültig. {passwordResetLink}.", "app.errors.too_common": "Ihr Passwort ist zu leicht zu erraten. Bitte wählen Sie ein stärkeres Passwort.", "app.errors.too_long": "Das Passwort darf höchstens 72 <PERSON>ei<PERSON> lang sein", "app.errors.too_short": "Bitte wählen Sie ein Passwort mit mindestens 8 Zeichen", "app.errors.uncaught_error": "Ein unbekannter Fehler ist aufgetreten.", "app.errors.unknown_group": "Die Gruppe {value} in der Zeile {row} ist ungültig oder unbekannt.", "app.errors.unknown_locale": "Die Sprache {value} in der Zeile {row} ist derzeit nicht konfiguriert.", "app.errors.unparseable_excel": "Die ausgewählte Datei konnte nicht verarbeitet werden.", "app.errors.url": "Geben Si<PERSON> einen gültigen Link ein. Achten Si<PERSON> darauf, dass der Link mit \"https://\" beginnt.", "app.errors.verification_taken": "Die Verifizierung kann nicht abgeschlossen werden, da bereits ein anderes Konto mit denselben Daten verifiziert wurde.", "app.errors.view_name_taken": "Eine Ansicht mit diesem Namen existiert bereits", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Unangemessener Inhalt wurde automatisch in einem Beitrag oder Kommentar erkannt", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Anmelden mit Stadt Wien Konto", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Stadt Wien Konto registrieren", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Jetzt Stadt Wien Konto erstellen und ein Login für viele personalisierte digitale Services der Stadt nutzen.", "app.modules.id_cow.cancel": "Abbrechen", "app.modules.id_cow.emptyFieldError": "Dies kann nicht leer sein", "app.modules.id_cow.helpAltText": "<PERSON><PERSON><PERSON>, wo die Ausweis-Seriennummer auf einem Ausweis zu finden ist", "app.modules.id_cow.invalidIdSerialError": "Ungültige ID seriell", "app.modules.id_cow.invalidRunError": "Ungültiger RUN", "app.modules.id_cow.noMatchFormError": "<PERSON><PERSON>einstimmung gefunden.", "app.modules.id_cow.notEntitledFormError": "<PERSON>cht berechtigt.", "app.modules.id_cow.showCOWHelp": "Wo finde ich meine ID-Seriennummer?", "app.modules.id_cow.somethingWentWrongError": "Wir können Sie nicht verifizieren, weil etwas schief gelaufen ist", "app.modules.id_cow.submit": "Senden", "app.modules.id_cow.takenFormError": "<PERSON><PERSON>.", "app.modules.id_cow.verifyCow": "Verifizierung mit COW", "app.modules.id_franceconnect.verificationButtonAltText": "Mit FranceConnect verifizieren", "app.modules.id_gent_rrn.cancel": "Abbrechen", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON><PERSON>ld darf nicht leer sein.", "app.modules.id_gent_rrn.gentRrnHelp": "Ihre Sozialversicherungsnummer befindet sich auf der Rückseite Ihres digitalen Personalausweises", "app.modules.id_gent_rrn.invalidRrnError": "Ungültige Sozialversicherungsnummer", "app.modules.id_gent_rrn.noMatchFormError": "Wir konnten keine Informationen über Ihre Sozialversicherungsnummer finden", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Wir können Si<PERSON> nicht verifizieren, weil <PERSON><PERSON> auße<PERSON><PERSON><PERSON> von <PERSON> wohnen", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Wir können Sie nicht verifizieren, weil <PERSON><PERSON> jünger als 14 Jahre sind", "app.modules.id_gent_rrn.rrnLabel": "Sozialversicherungsnummer", "app.modules.id_gent_rrn.rrnTooltip": "Wir fragen Sie nach Ihrer Sozialversicherungsnummer, um zu überprüfen, ob Sie Bürger oder Bürgerin von Gent und älter als 14 Jahre sind.", "app.modules.id_gent_rrn.showGentRrnHelp": "Wo kann ich meine ID-Seriennummer finden?", "app.modules.id_gent_rrn.somethingWentWrongError": "Wir können Sie nicht verifizieren, weil etwas schief gelaufen ist", "app.modules.id_gent_rrn.submit": "Senden", "app.modules.id_gent_rrn.takenFormError": "Ihre Sozialversicherungsnummer wurde bereits zur Verifizierung eines anderen Kontos verwendet", "app.modules.id_gent_rrn.verifyGentRrn": "Überprüfen mit GentRrn", "app.modules.id_id_card_lookup.cancel": "Abbrechen", "app.modules.id_id_card_lookup.emptyFieldError": "Dies kann nicht leer sein", "app.modules.id_id_card_lookup.helpAltText": "Ausweis Erklärung", "app.modules.id_id_card_lookup.invalidCardIdError": "Diese ID ist ungültig.", "app.modules.id_id_card_lookup.noMatchFormError": "<PERSON><PERSON>einstimmung gefunden.", "app.modules.id_id_card_lookup.showHelp": "Wo finde ich meine ID-Seriennummer?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Wir können Sie nicht verifizieren, weil etwas schief gelaufen ist", "app.modules.id_id_card_lookup.submit": "Senden", "app.modules.id_id_card_lookup.takenFormError": "<PERSON><PERSON>.", "app.modules.id_oostende_rrn.cancel": "Abbrechen", "app.modules.id_oostende_rrn.emptyFieldError": "Dies kann nicht leer sein", "app.modules.id_oostende_rrn.invalidRrnError": "Ungültige Sozialversicherungsnummer", "app.modules.id_oostende_rrn.noMatchFormError": "Wir konnten keine Informationen über Ihre Sozialversicherungsnummer finden", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Wir können Si<PERSON> nicht verifizieren, da <PERSON><PERSON> auße<PERSON><PERSON><PERSON> von <PERSON> wohnen", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Wir können Sie nicht verifizieren, weil <PERSON><PERSON> jünger als 14 Jahre sind", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Ihre Sozialversicherungsnummer befindet sich auf der Rückseite Ihres digitalen Personalausweises", "app.modules.id_oostende_rrn.rrnLabel": "Sozialversicherungsnummer", "app.modules.id_oostende_rrn.rrnTooltip": "Wir fragen Sie nach Ihrer Sozialversicherungsnummer, um zu überprüfen, ob Sie ein/e <PERSON>ürger/in von Oostende sind, die/der älter als 14 Jahre ist.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Wo kann ich meine Sozialversicherungsnummer finden?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Wir können Sie nicht verifizieren, weil etwas schief gelaufen ist", "app.modules.id_oostende_rrn.submit": "Einreichen", "app.modules.id_oostende_rrn.takenFormError": "Ihre Sozialversicherungsnummer wurde bereits zur Verifizierung eines anderen Kontos verwendet", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Überprüfen anhand der Sozialversicherungsnummer", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Sie haben einen Admin-Zugang für den Ordner \"{folderName}\" erhalten.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Teilen", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "<PERSON><PERSON> sich die Projekte unter {folderUrl} an, um Ihrer Stimme Gehör zu verschaffen!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | von der Beteiligungsplattform {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | von der Beteiligungsplattform {orgName}", "app.sessionRecording.accept": "<PERSON><PERSON>, ich akzeptiere", "app.sessionRecording.modalDescription1": "Um unsere Nutzer*innen besser verstehen zu können, bitten wir einen kleinen Prozentsatz der Besucher*innen, ihre Browsing-Session im Detail zu verfolgen.", "app.sessionRecording.modalDescription2": "Der einzige Zweck der aufgezeichneten Daten ist die Verbesserung der Website. Keine Ihrer Daten werden an Dritte weitergegeben. Alle sensiblen Informationen, die Si<PERSON> eingeben, werden gefiltert.", "app.sessionRecording.modalDescription3": "Sind Si<PERSON> e<PERSON>verstanden?", "app.sessionRecording.modalDescriptionFaq": "FAQ hier.", "app.sessionRecording.modalTitle": "<PERSON><PERSON><PERSON> un<PERSON>, diese Website zu verbessern", "app.sessionRecording.reject": "<PERSON><PERSON>, ich lehne ab", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Budgetzuweisung durchführen", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Feed<PERSON> zu einem Dokument sammeln", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Erweiterte Umfrage erstellen", "app.utils.AdminPage.ProjectEdit.createPoll": "Blitz-Abstimmung erstellen", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Einbinden einer externen Umfrage", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Te<PERSON><PERSON><PERSON><PERSON><PERSON> finden", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Beiträge und Feedback sammeln", "app.utils.AdminPage.ProjectEdit.shareInformation": "<PERSON><PERSON> teilen", "app.utils.FormattedCurrency.credits": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.tokens": "Wertmarken", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# <PERSON><PERSON>aben} one {# Guthaben} other {# Guthaben}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# Token} one {# Token} other {# Token}}", "app.utils.IdeaCards.mostDiscussed": "<PERSON><PERSON>", "app.utils.IdeaCards.mostReacted": "Meiste Reaktionen", "app.utils.IdeaCards.newest": "Neueste", "app.utils.IdeaCards.oldest": "Älteste", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Beliebt", "app.utils.IdeasNewPage.contributionFormTitle": "Neuen Beitrag hinzufügen", "app.utils.IdeasNewPage.ideaFormTitle": "Eine neue Idee hinzufügen\n", "app.utils.IdeasNewPage.initiativeFormTitle": "Neue Initiative hinzufügen", "app.utils.IdeasNewPage.issueFormTitle1": "Ein Vorhaben hinzufügen", "app.utils.IdeasNewPage.optionFormTitle": "Eine neue Option hinzufügen", "app.utils.IdeasNewPage.petitionFormTitle": "Neue Petition hinzufügen", "app.utils.IdeasNewPage.projectFormTitle": "Ein neues Projekt hinzufügen\n", "app.utils.IdeasNewPage.proposalFormTitle": "Neuen Vorschlag hinzufügen", "app.utils.IdeasNewPage.questionFormTitle": "Eine neue Frage hinzufügen ", "app.utils.IdeasNewPage.surveyTitle": "Umfrage", "app.utils.IdeasNewPage.viewYourComment": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourContribution": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourIdea": "<PERSON><PERSON><PERSON> an<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourInitiative": "<PERSON> ansehen", "app.utils.IdeasNewPage.viewYourInput": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourIssue": "<PERSON> ansehen", "app.utils.IdeasNewPage.viewYourOption": "Option an<PERSON>hen", "app.utils.IdeasNewPage.viewYourPetition": "<PERSON><PERSON> an<PERSON>", "app.utils.IdeasNewPage.viewYourProject": "<PERSON><PERSON><PERSON> an<PERSON><PERSON>", "app.utils.IdeasNewPage.viewYourProposal": "Vorschlag ansehen", "app.utils.IdeasNewPage.viewYourQuestion": "<PERSON><PERSON> an<PERSON><PERSON>", "app.utils.Projects.sendSubmission": "Übermittlungskennung an meine E-Mail senden", "app.utils.Projects.sendSurveySubmission": "Kennung für die Übermittlung der Umfrage an meine E-Mail senden", "app.utils.Projects.surveySubmission": "Einreichung der Umfrage", "app.utils.Projects.yourResponseHasTheFollowingId": "Ihre Antwort hat die folgende Kennung: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "<PERSON>n <PERSON>e später entscheiden, dass Ihre Antwort entfernt werden soll, kontaktieren Sie uns bitte unter Angabe der folgenden eindeutigen Kennung:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "<PERSON>e müssen Ihr Profil ausfüllen, um an dieser Veranstaltung teilnehmen zu können.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Sie erfüllen nicht die Voraussetzungen für die Teilnahme an dieser Veranstaltung.", "app.utils.actionDescriptors.attendingEventNotPermitted": "<PERSON>s ist Ihnen nicht gestattet, an dieser Veranstaltung teilzunehmen.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "<PERSON><PERSON> müssen sich anmelden oder registrieren, um an dieser Veranstaltung teilnehmen zu können.", "app.utils.actionDescriptors.attendingEventNotVerified": "<PERSON><PERSON> müssen Ihr Konto verifizieren, bevor <PERSON> an dieser Veranstaltung teilnehmen können.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "<PERSON>e müssen Ihr Profil ausfüllen, um teilzunehmen.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Sie erfüllen nicht die Voraussetzungen zur Teilnahme.", "app.utils.actionDescriptors.volunteeringNotPermitted": "<PERSON>e können leider nicht teilnehmen.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "<PERSON><PERSON> müssen sich anmelden oder registrieren, um teilzunehmen.", "app.utils.actionDescriptors.volunteeringNotVerified": "<PERSON><PERSON> müssen Ihr Konto verifizieren, bevor <PERSON> teilnehmen können.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Bitte {completeRegistrationLink} um teilzunehmen.", "app.utils.errors.api_error_default.in": "<PERSON>t nicht richtig", "app.utils.errors.default.ajv_error_birthyear_required": "<PERSON>te geben Sie Ihr Geburtsjahr ein", "app.utils.errors.default.ajv_error_date_any": "<PERSON>te geben Si<PERSON> ein gültiges Datum ein", "app.utils.errors.default.ajv_error_domicile_required": "<PERSON>te geben Si<PERSON> Ihren Wohnort ein", "app.utils.errors.default.ajv_error_gender_required": "Bitte geben Sie Ihr Geschlecht ein", "app.utils.errors.default.ajv_error_invalid": "Ist ungültig", "app.utils.errors.default.ajv_error_maxItems": "Nicht mehr als {limit, plural, one {# Antwort} other {# Antworten}}", "app.utils.errors.default.ajv_error_minItems": "Mindestens {limit, plural, one {# Antwort} other {# Antworten}} auswählen", "app.utils.errors.default.ajv_error_number_any": "<PERSON>te geben Si<PERSON> eine gültige Zahl ein", "app.utils.errors.default.ajv_error_politician_required": "<PERSON>te geben <PERSON> ein, ob <PERSON>e ein Politiker/in sind", "app.utils.errors.default.ajv_error_required3": "<PERSON>ld ist erforderlich: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Kann nicht leer sein", "app.utils.errors.default.api_error_accepted": "Muss ak<PERSON><PERSON><PERSON>t werden", "app.utils.errors.default.api_error_blank": "Kann nicht leer sein", "app.utils.errors.default.api_error_confirmation": "Stimmt nicht überein", "app.utils.errors.default.api_error_empty": "Kann nicht leer sein", "app.utils.errors.default.api_error_equal_to": "<PERSON>t nicht richtig", "app.utils.errors.default.api_error_even": "Muss gerade sein", "app.utils.errors.default.api_error_exclusion": "Ist reserviert", "app.utils.errors.default.api_error_greater_than": "<PERSON><PERSON> zu klein", "app.utils.errors.default.api_error_greater_than_or_equal_to": "<PERSON><PERSON> zu klein", "app.utils.errors.default.api_error_inclusion": "Ist nicht in der Liste enthalten", "app.utils.errors.default.api_error_invalid": "Ist ungültig", "app.utils.errors.default.api_error_less_than": "<PERSON><PERSON> zu groß", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON><PERSON> zu groß", "app.utils.errors.default.api_error_not_a_number": "<PERSON><PERSON> kein<PERSON>", "app.utils.errors.default.api_error_not_an_integer": "Muss eine integre sein", "app.utils.errors.default.api_error_other_than": "<PERSON>t nicht richtig", "app.utils.errors.default.api_error_present": "Muss leer sein", "app.utils.errors.default.api_error_too_long": "<PERSON><PERSON> zu lang", "app.utils.errors.default.api_error_too_short": "<PERSON><PERSON> zu kurz", "app.utils.errors.default.api_error_wrong_length": "Hat die falsche Länge", "app.utils.errors.defaultapi_error_.odd": "Muss ungerade sein", "app.utils.notInGroup": "Du erfüllst die Teilnahmevoraussetzungen nicht.", "app.utils.participationMethod.onSurveySubmission": "Vielen Dank. Wir haben Ihre Antwort erhalten.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Eine Abstimmung ist nicht mehr möglich, da diese Phase nicht mehr aktiv ist.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Sie erfüllen die Anforderungen zur Abstimmung nicht.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "<PERSON>e sind nicht berechtigt, abzustim<PERSON>.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "<PERSON>e müssen sich anmelden oder sich registrieren um abstimmen zu können.", "app.utils.participationMethodConfig.voting.votingNotVerified": "<PERSON><PERSON> müssen Ihr Konto verifizieren, bevor <PERSON> abstimmen können.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Das Einreichen von Budgets endete am {endDate}.</b> Die Teilnehmenden hatten insgesamt <b>{maxBudget} zur Verfügung, die sie auf {optionCount} aufteilen konnten.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Budget eingereicht", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budget eingereicht 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Sie erfüllen die Anforderungen zur Abstimmung nicht.", "app.utils.votingMethodUtils.budgetingNotPermitted": "<PERSON>e sind nicht berechtigt, abzustim<PERSON>.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "<PERSON>e müssen sich anmelden oder sich registrieren um abstimmen zu können.", "app.utils.votingMethodUtils.budgetingNotVerified": "<PERSON><PERSON> müssen Ihr Konto verifizieren, bevor <PERSON> abstimmen können.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Ihr <PERSON> wird erst gezählt</b> , wenn <PERSON><PERSON> auf \"Senden\" klicken.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Das erforderliche Mindestbudget ist {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON><PERSON> fertig sind, klicken <PERSON><PERSON> auf \"Senden\", um Ihr Budget einzureichen.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Wählen Sie Ihre bevorzugten Optionen, indem Sie auf \"Hinzufügen\" tippen.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Sie haben insgesamt <b>{maxBudget} zur Verfügung, die Sie auf {optionCount} Optionen verteilen können</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON> Glückwunsch, Ihr Budget wurde übermittelt!</b> Sie können Ihre Optionen unten jederzeit überprüfen oder sie vor dem <b>{endDate}</b> ändern.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON> Glückwunsch, Ihr Budget wurde eingereicht!</b> Sie können Ihre Optionen unten jederzeit überprüfen.", "app.utils.votingMethodUtils.castYourVote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Sie können maximal {maxVotes, plural, one {# Guthaben} other {# <PERSON><PERSON>aben}} pro Option hinzufügen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Sie können maximal {maxVotes, plural, one {# <PERSON>t} other {# <PERSON><PERSON>}} pro Option hinzufügen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Sie können maximal {maxVotes, plural, one {# Token} other {# Token}} pro Option hinzufügen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Sie können maximal {maxVotes, plural, one {# Stimme} other {# Stimmen}} pro Option hinzufügen.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON>n <PERSON> fertig sind, klicken <PERSON> auf \"Senden\", um Ihre Stimme abzugeben.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Wählen Sie Ihre bevorzugten Optionen, indem Sie auf \"Auswählen\" klicken.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Sie haben insgesamt <b>{totalVotes, plural, one {# Guthaben} other {# Guthaben}} zum Aufteilen auf {optionCount, plural, one {# Optionen} other {# Optionen}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "Sie haben insgesamt <b>{totalVotes, plural, one {# Punkt} other {# Punkte}} zum Verteilen auf {optionCount, plural, one {# Optionen} other {# Optionen}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Sie haben insgesamt <b>{totalVotes, plural, one {# Token} other {# Token}} zum Verteilen auf {optionCount, plural, one {# Option} other {# Optionen}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Sie haben insgesamt <b>{totalVotes, plural, one {# Stimme} other {# Stimmen}}, die Sie auf {optionCount, plural, one {# Optionen} other {# Optionen}}</b> verteilen könne<PERSON>.", "app.utils.votingMethodUtils.finalResults": "Endgültige Ergebnisse", "app.utils.votingMethodUtils.finalTally": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.howToParticipate": "So nehmen <PERSON>e teil", "app.utils.votingMethodUtils.howToVote": "Wie man abstimmt", "app.utils.votingMethodUtils.multipleVotingEnded1": "Die Abstimmung wurde am <b>{endDate} beendet</b> <b>.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {# <PERSON><PERSON><PERSON><PERSON>} one {# Guthaben} other {# G<PERSON>aben}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 <PERSON><PERSON>} one {1 <PERSON><PERSON>} other {# <PERSON><PERSON>}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {# Token} one {# Token} other {# Token}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 Stimmen} one {1 Stimme} other {# Stimmen}}", "app.utils.votingMethodUtils.results": "Ergebnisse", "app.utils.votingMethodUtils.singleVotingEnded": "Die Abstimmung wurde am <b>{endDate} geschlossen.</b> Die Teilnehmenden konnten <b> für {maxVotes} Optionen abstimmen.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Wählen Sie Ihre bevorzugten Optionen, indem Sie auf \"Auswählen\" klicken", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "<PERSON><PERSON> haben <b>{totalVotes} Stimmen</b>, die Sie den Optionen zuweisen können.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON>n <PERSON> fertig sind, klicken <PERSON> auf \"Senden\", um Ihre Stimme abzugeben.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Die Abstimmung wurde am  <b>{endDate} geschlossen.</b> Die Teilnehmenden konnten <b>für 1 Option abstimmen.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "<PERSON>ählen Sie Ihre bevorzugte Option, indem Sie auf \"Auswählen\" klicken.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "<PERSON>e haben <b>1 <PERSON><PERSON><PERSON>,</b> die Sie einer der Optionen zuordnen können.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Die Abstimmung endete am <b>{endDate}.</b> Die Teilnehmenden konnten <b>für beliebig viele Optionen stimmen.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "<PERSON>e können für so viele Optionen stimmen, wie Sie möchten.", "app.utils.votingMethodUtils.submitYourBudget": "Budget einreichen", "app.utils.votingMethodUtils.submittedBudgetCountText2": "Person hat online abgestimmt.", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "Personen haben online abgestimmt.", "app.utils.votingMethodUtils.submittedVoteCountText2": "Person hat ihre Stimme online abgegeben", "app.utils.votingMethodUtils.submittedVotesCountText2": "Personen haben ihre Stimme online abgegeben", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Stimme eingereicht 🎉", "app.utils.votingMethodUtils.votesCast": "Abgegebene Stimmen", "app.utils.votingMethodUtils.votingClosed": "Abstimmung geschlossen", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b><PERSON><PERSON>e Stimmen werden erst gezählt</b>, wenn <PERSON><PERSON> auf \"<PERSON><PERSON>\" klicken", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON> G<PERSON>wu<PERSON>, <PERSON>hre Stimme wurde abgegeben!</b> <PERSON>e können Ihren Beitrag überprüfen oder ändern, bevor <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON> Glückwunsch, <PERSON>hre Stimme wurde abgegeben!</b> Sie können Ihre Stimmabgabe unten jederzeit überprüfen oder ändern.", "components.UI.IdeaSelect.noIdeaAvailable": "Es sind keine Beiträge vorhanden.", "components.UI.IdeaSelect.selectIdea": "Beitrag auswählen", "containers.SiteMap.allProjects": "{org<PERSON><PERSON>, select, <PERSON>ü<PERSON> {Alle Bezirke oder Projekte} other {Alle Projekte}}", "containers.SiteMap.customPageSection": "Benutzerdefinierte Seiten", "containers.SiteMap.folderInfo": "Mehr Informationen", "containers.SiteMap.headSiteMapTitle": "Website-Karte | {orgName}", "containers.SiteMap.homeSection": "Allgemein", "containers.SiteMap.pageContents": "Seiteninhalt", "containers.SiteMap.profilePage": "<PERSON>hre Profilseite", "containers.SiteMap.profileSettings": "Ihre Einstellungen", "containers.SiteMap.projectEvents": "Veranstaltungen", "containers.SiteMap.projectIdeas": "Ideen", "containers.SiteMap.projectInfo": "Informieren", "containers.SiteMap.projectPoll": "Blitz-Umfrage", "containers.SiteMap.projectSurvey": "Umfrage", "containers.SiteMap.projectsArchived": "Archivierte Projekte", "containers.SiteMap.projectsCurrent": "Laufende Projekte", "containers.SiteMap.projectsDraft": "Projektentwürfe", "containers.SiteMap.projectsSection": "<PERSON><PERSON><PERSON><PERSON> von {orgName}", "containers.SiteMap.signInPage": "Anmelden", "containers.SiteMap.signUpPage": "Registrieren", "containers.SiteMap.siteMapDescription": "<PERSON> dieser Seite aus, kannst du zu allen Inhalten auf der Plattform navigieren.", "containers.SiteMap.siteMapTitle": "Sitemap der Beteiligungsplattform {orgName}", "containers.SiteMap.successStories": "Erfolgsgeschichten", "containers.SiteMap.timeline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON>", "containers.SubscriptionEndedPage.accessDenied": "<PERSON>e haben keinen Zugriff mehr", "containers.SubscriptionEndedPage.subscriptionEnded": "Diese Seite ist nur für Plattformen mit einem aktiven Abonnement zugänglich."}