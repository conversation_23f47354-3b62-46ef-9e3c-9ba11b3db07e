{"EmailSettingsPage.emailSettings": "Innst<PERSON>inger for e-post", "EmailSettingsPage.initialUnsubscribeError": "Det oppstod et problem med å avslutte abonnementet på denne kampanjen, vennligst prøv igjen.", "EmailSettingsPage.initialUnsubscribeLoading": "Forespø<PERSON><PERSON> din er under behandling, vennligst vent...", "EmailSettingsPage.initialUnsubscribeSuccess": "Du har meldt deg av {campaignTitle}.", "UI.FormComponents.optional": "v<PERSON><PERSON><PERSON><PERSON>t", "app.closeIconButton.a11y_buttonActionMessage": "Lukk", "app.components.Areas.areaUpdateError": "Det oppsto en feil under lagring av området ditt. Vennligst prøv igjen.", "app.components.Areas.followedArea": "Fulgt område: {areaTitle}", "app.components.Areas.followedTopic": "Fulgte tema: {topicTitle}", "app.components.Areas.topicUpdateError": "Det oppstod en feil under lagring av emnet ditt. Vennligst prøv igjen.", "app.components.Areas.unfollowedArea": "<PERSON><PERSON><PERSON> fulgt område: {areaTitle}", "app.components.Areas.unfollowedTopic": "<PERSON>kke fulgt emne: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Pris:", "app.components.AssignBudgetControl.add": "Legg til", "app.components.AssignBudgetControl.added": "Lagt til", "app.components.AssignMultipleVotesControl.addVote": "Legg til stemme", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Du har fordelt alle studiepoengene dine.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Du har fordelt det maksimale antallet studiepoeng for dette alternativet.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Du har fordelt alle poengene dine.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Du har delt ut maksimalt antall poeng for dette alternativet.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Du har delt ut alle symbolene dine.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Du har distribuert det maksimale antallet poletter for dette <PERSON>t.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Du har fordelt alle stemmene dine.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Du har delt ut maksimalt antall stemmer for dette alternativet.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(inkl. 1 frakoblet)} other {(inkl. # frakoblet)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Avstemning er ikke tilg<PERSON>ngelig, siden denne fasen ikke er aktiv.", "app.components.AssignMultipleVotesControl.removeVote": "<PERSON><PERSON><PERSON> stemme", "app.components.AssignMultipleVotesControl.select": "Velg", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Du har allerede sendt inn din stemme. For å endre den, klikk på \"Endre din innsending\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Du har allerede sendt inn din stemme. H<PERSON> du vil endre den, går du tilbake til prosjektsiden og klikker på \"Endre din innsending\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credit} other {credits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {point} other {points}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "Du har fordelt alle stemmene dine.", "app.components.AssignVoteControl.phaseNotActive": "Avstemning er ikke tilg<PERSON>ngelig, siden denne fasen ikke er aktiv.", "app.components.AssignVoteControl.select": "Velg", "app.components.AssignVoteControl.selected2": "U<PERSON>val<PERSON><PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "Stem på minst 1 alternativ", "app.components.AssignVoteControl.votesSubmitted1": "Du har allerede sendt inn din stemme. For å endre den, klikk på \"Endre din innsending\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Du har allerede sendt inn din stemme. H<PERSON> du vil endre den, går du tilbake til prosjektsiden og klikker på \"Endre din innsending\".", "app.components.AuthProviders.continue": "Fortsett", "app.components.AuthProviders.continueWithAzure": "Fortsett med {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Fortsett med Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Fortsett med falsk SSO", "app.components.AuthProviders.continueWithGoogle": "Fortsett med Google", "app.components.AuthProviders.continueWithHoplr": "Fortsett med Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Fortsett med ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Fortsett med {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Fortsett med MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Det finnes allerede en konto med denne e-postadressen.{br}{br}Du får ikke tilgang til plattformen ved hjelp av FranceConnect, ettersom personopplysningene ikke stemmer overens. For å logge inn med FranceConnect må du først endre fornavnet eller etternavnet ditt på denne plattformen slik at det stemmer overens med de offisielle opplysningene dine.{br}{br}Du kan logge på som normalt nedenfor.", "app.components.AuthProviders.goToLogIn": "Har du allerede en konto? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Har du ikke en konto? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Logg inn", "app.components.AuthProviders.logInWithEmail": "Logg inn med e-post", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "Du må ha den angitte minimumsalderen eller høyere for å bli verifisert.", "app.components.AuthProviders.signUp2": "Registrer deg", "app.components.AuthProviders.signUpButtonAltText": "Registrer deg på {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Registrer deg med e-post", "app.components.AuthProviders.verificationRequired": "Verifisering kreves", "app.components.Author.a11yPostedBy": "Skrevet av", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 deltaker} other {{numberOfParticipants} deltakere}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} brukere", "app.components.AvatarBubbles.participant": "deltaker", "app.components.AvatarBubbles.participants1": "deltakere", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Det er ikke mulig å kommentere i den nåværende fasen.", "app.components.Comments.commentingDisabledInactiveProject": "Det er ikke mulig å kommentere fordi dette prosjektet ikke er aktivt for øyeblikket.", "app.components.Comments.commentingDisabledProject": "Kommentering i dette prosjektet er for øyeblikket deaktivert.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} til å kommentere.", "app.components.Comments.commentingMaybeNotPermitted": "Vennligst {signInLink} for å se hvilke tiltak som kan iverksettes.", "app.components.Comments.inputsAssociatedWithProfile": "Som standard vil innsendingene dine være knyttet til profilen din, med mindre du velger dette alternativet.", "app.components.Comments.invisibleTitleComments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.leastRecent": "<PERSON>st nylig", "app.components.Comments.likeComment": "<PERSON><PERSON> denne kom<PERSON>n", "app.components.Comments.mostLiked": "De fleste reaksjonene", "app.components.Comments.mostRecent": "De siste", "app.components.Comments.official": "Offisiell", "app.components.Comments.postAnonymously": "Post anonymt", "app.components.Comments.replyToComment": "<PERSON><PERSON> på kommentar", "app.components.Comments.reportAsSpam": "Rapporter som søppelpost", "app.components.Comments.seeOriginal": "Se original", "app.components.Comments.seeTranslation": "<PERSON> <PERSON><PERSON><PERSON>e", "app.components.Comments.yourComment": "<PERSON> kom<PERSON>ar", "app.components.CommonGroundResults.divisiveDescription": "Utsagn der folk er like enige som uenige:", "app.components.CommonGroundResults.divisiveTitle": "Splittende", "app.components.CommonGroundResults.majorityDescription": "<PERSON><PERSON> enn 60 % stemte enten den ene eller den andre veien på følgende spørsmål:", "app.components.CommonGroundResults.majorityTitle": "Flertallet", "app.components.CommonGroundResults.participantLabel": "deltaker", "app.components.CommonGroundResults.participantsLabel1": "deltakere", "app.components.CommonGroundResults.statementLabel": "uttalelse", "app.components.CommonGroundResults.statementsLabel1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.votesLabe": "stemme", "app.components.CommonGroundResults.votesLabel1": "stemmer", "app.components.CommonGroundStatements.agreeLabel": "<PERSON><PERSON>", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundStatements.noMoreStatements": "Det er ingen uttalelser å svare på akkurat nå", "app.components.CommonGroundStatements.noResults": "Det er ingen resultater å vise ennå. <PERSON><PERSON><PERSON> for at du har deltatt i Common Ground-fasen, og kom tilbake hit etterpå.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON><PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Resultater", "app.components.CommonGroundTabs.statementsTabLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommunityMonitorModal.formError": "Det oppstod en feil.", "app.components.CommunityMonitorModal.surveyDescription2": "<PERSON>ne løpende unders<PERSON><PERSON><PERSON> kartlegger hva du mener om styring og offentlige tjenester.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Tar <1 minutt} one {Tar 1 minutt} other {Tar # minutter}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "En e-post med en bekreftelseskode har blitt sendt til {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "<PERSON><PERSON> <PERSON>-<PERSON><PERSON><PERSON><PERSON> din.", "app.components.ConfirmationModal.codeInput": "<PERSON><PERSON>", "app.components.ConfirmationModal.confirmationCodeSent": "Ny kode sendt", "app.components.ConfirmationModal.didntGetAnEmail": "Har du ikke mottatt en e-post?", "app.components.ConfirmationModal.foundYourCode": "Fant du koden din?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON> til<PERSON>.", "app.components.ConfirmationModal.sendEmailWithCode": "Send e-post med kode", "app.components.ConfirmationModal.sendNewCode": "Send ny kode.", "app.components.ConfirmationModal.verifyAndContinue": "Bekreft og fortsett", "app.components.ConfirmationModal.wrongEmail": "Feil e-post?", "app.components.ConsentManager.Banner.accept": "God<PERSON>", "app.components.ConsentManager.Banner.ariaButtonClose2": "Avvis policyen og lukk banneret", "app.components.ConsentManager.Banner.close": "Lukk", "app.components.ConsentManager.Banner.mainText": "Denne plattformen bruker informasjonskapsler i samsvar med vår {policyLink}.", "app.components.ConsentManager.Banner.manage": "Administrer", "app.components.ConsentManager.Banner.policyLink": "Retningslinjer for informasjonskapsler", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Vi bruker dette til å tilpasse og måle effektiviteten av reklamekampanjer på nettstedet vårt. Vi viser ingen reklame på denne plattformen, men følgende tjenester kan tilby deg en personlig tilpasset annonse basert på sidene du besøker på nettstedet vårt.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Tillat", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analyse", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "Vi bruker denne spor<PERSON> for å forstå bedre hvordan du bruker plattformen, slik at vi kan lære og forbedre navigasjonen din. Denne informasjonen brukes kun i masseanalyser, og ikke på noen måte til å spore enkeltpersoner.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON> til<PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON>ke tillat", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Funksjonell", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "<PERSON>te er nødvendig for å aktivere og overvåke grunnleggende funksjoner på nettstedet. Noen av verktøyene som er oppført her, gjelder kansk<PERSON>kke for deg. Les retningslinjene våre for informasjonskapsler for mer informasjon.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "For å ha en funksjonell plattform lagrer vi en informasjonskapsel for autentisering hvis du registrerer deg, og språket du bruker denne plattformen på.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Lagre", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON><PERSON> preferanser for informasjonskapsler", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Verktøy", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "An<PERSON>varsfraskrivelse for opplasting av innhold", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "Ved å laste opp innhold erklærer du at dette innholdet ikke bryter med noen bestemmelser eller tredjeparts rettigheter, for eks<PERSON>pel immaterielle rettigheter, personvernrettigheter, rettigheter til forretningshemmeligheter og så videre.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "<PERSON><PERSON> oss hvorfor", "app.components.CustomFieldsForm.addressInputAriaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Skriv inn en adresse...", "app.components.CustomFieldsForm.adminFieldTooltip": "Feltet er bare synlig for administratorer", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Alle svar på denne under<PERSON><PERSON><PERSON> er anonymisert.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "<PERSON> kreves minst tre punkter for en polygon.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "<PERSON> kreves minst to punkter for en linje.", "app.components.CustomFieldsForm.attachmentRequired": "Minst ett vedlegg er påkrevd", "app.components.CustomFieldsForm.authorFieldLabel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Begynn å skrive for å søke etter brukerens e-postadresse eller navn...", "app.components.CustomFieldsForm.back": "Tilbake", "app.components.CustomFieldsForm.budgetFieldLabel": "Budsjett", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Klikk på kartet for å tegne. Dra deretter på punkter for å flytte dem.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Klikk på kartet eller skriv inn en adresse nedenfor for å legge til svaret ditt.", "app.components.CustomFieldsForm.confirm": "Bekreft", "app.components.CustomFieldsForm.descriptionMinLength": "Beskrivelsen må være på minst {min} tegn", "app.components.CustomFieldsForm.descriptionRequired": "Beskrivelsen er påkrevd", "app.components.CustomFieldsForm.fieldMaximumItems": "Maksimalt {maxSelections, plural, one {# alternativ} other {# alternativer}} kan velges for feltet \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "Minst {minSelections, plural, one {# alternativ} other {# alternativer}} kan velges for feltet \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "Feltet \"{fieldName}\" er obligatorisk", "app.components.CustomFieldsForm.fileSizeLimit": "<PERSON><PERSON><PERSON> for filstørrelse er {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "Bildet er påkrevd", "app.components.CustomFieldsForm.minimumCoordinates2": "<PERSON> kreves minst {numPoints} kartpunkter.", "app.components.CustomFieldsForm.notPublic1": "*<PERSON><PERSON> svaret vil kun bli delt med prosjektledere, og ikke med offentligheten.", "app.components.CustomFieldsForm.otherArea": "Et annet sted", "app.components.CustomFieldsForm.progressBarLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Velg så mange du vil", "app.components.CustomFieldsForm.selectBetween": "*Velg mellom {minItems} og {maxItems}", "app.components.CustomFieldsForm.selectExactly2": "*Velg nøyaktig {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*Velg så mange du vil", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Trykk på kartet for å tegne. Dra deretter på punkter for å flytte dem.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Trykk på kartet for å tegne.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Trykk på kartet for å legge til svaret ditt.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Trykk på kartet eller skriv inn en adresse nedenfor for å legge til svaret ditt.", "app.components.CustomFieldsForm.tapToAddALine": "Trykk for å legge til en linje", "app.components.CustomFieldsForm.tapToAddAPoint": "Trykk for å legge til et punkt", "app.components.CustomFieldsForm.tapToAddAnArea": "Trykk for å legge til et område", "app.components.CustomFieldsForm.titleMaxLength": "<PERSON><PERSON>len må være på maks {max} tegn lang", "app.components.CustomFieldsForm.titleMinLength": "<PERSON><PERSON>len må være på minst {min} tegn", "app.components.CustomFieldsForm.titleRequired": "Tittelen er påkrevd", "app.components.CustomFieldsForm.topicRequired": "Minst én tagg er påkrevd", "app.components.CustomFieldsForm.typeYourAnswer": "Skriv inn svaret ditt", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Det er nødvendig å skrive inn svaret ditt", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Last opp en zip-fil som inneholder en eller flere shapefiler.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "<PERSON><PERSON> plass<PERSON>en ikke vises blant alternativene mens du skriver, kan du legge til gyldige koordinater i formatet \"breddegrad, lengdegrad\" for å angi en nøyaktig plassering (f.eks. -33,019808, -71,495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "<PERSON><PERSON> felter var ugyldige. Vennligst korriger feilene og prøv på nytt.", "app.components.ErrorBoundary.errorFormErrorGeneric": "En ukjent feil oppstod under innsending av rapporten. Vennligst prøv igjen.", "app.components.ErrorBoundary.errorFormLabelClose": "Lukk", "app.components.ErrorBoundary.errorFormLabelComments": "Hva skjedde?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-post", "app.components.ErrorBoundary.errorFormLabelName": "Navn", "app.components.ErrorBoundary.errorFormLabelSubmit": "Send inn", "app.components.ErrorBoundary.errorFormSubtitle": "<PERSON><PERSON> vårt har blitt varslet.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON> du vil at vi skal hjelpe deg, kan du fortelle oss hva som skjedde nedenfor.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Tilbakemeldingen din er sendt. Takk skal du ha!", "app.components.ErrorBoundary.errorFormTitle": "Det ser ut som det er et problem.", "app.components.ErrorBoundary.genericErrorWithForm": "Det oppstod en feil, og vi kan ikke vise dette innholdet. Vennligst prøv igjen, eller {openForm}", "app.components.ErrorBoundary.openFormText": "Hjelp oss å finne ut av det", "app.components.ErrorToast.budgetExceededError": "Du har ikke nok budsjett", "app.components.ErrorToast.votesExceededError": "Du har ikke nok stemmer igjen", "app.components.EventAttendanceButton.forwardToFriend": "<PERSON><PERSON><PERSON>nd til en venn", "app.components.EventAttendanceButton.maxRegistrationsReached": "Det maksimale antallet påmeldinger til arrangementet er nådd. Det er ingen ledige plasser igjen.", "app.components.EventAttendanceButton.register": "Registrer deg", "app.components.EventAttendanceButton.registered": "Registrert", "app.components.EventAttendanceButton.seeYouThere": "Vi ses der!", "app.components.EventAttendanceButton.seeYouThereName": "Vi ses der, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Mindre hendelsesinformasjon ble synlig.", "app.components.EventCard.a11y_moreContentVisible": "Mer informasjon om hendelsen ble synlig.", "app.components.EventCard.a11y_readMore": "Les mer om arrangementet \"{eventTitle}\".", "app.components.EventCard.endsAt": "Slutter på", "app.components.EventCard.readMore": "Les mer om dette", "app.components.EventCard.showLess": "Vis mindre", "app.components.EventCard.showMore": "Vis mer", "app.components.EventCard.startsAt": "Begynner på", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Kommende og pågående arrangementer i dette prosjektet", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Kommende og pågående arrangementer i denne fasen", "app.components.FileUploader.a11y_file": "Fil:", "app.components.FileUploader.a11y_filesToBeUploaded": "Filer som skal lastes opp: {fileNames}", "app.components.FileUploader.a11y_noFiles": "Ingen filer lagt til.", "app.components.FileUploader.a11y_removeFile": "<PERSON><PERSON><PERSON> denne filen", "app.components.FileUploader.fileInputDescription": "Klikk for å velge en fil", "app.components.FileUploader.fileUploadLabel": "Vedlegg (maks. 50 MB)", "app.components.FileUploader.file_too_large2": "Filer som er større enn {maxSizeMb}MB er ikke tillatt.", "app.components.FileUploader.incorrect_extension": "{fileName} ikke støttes av systemet vårt, vil den ikke bli lastet opp.", "app.components.FilterBoxes.a11y_allFilterSelected": "Valgt statusfilter: alle", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {~ # innsending} other {# innsendinger}}", "app.components.FilterBoxes.a11y_removeFilter": "<PERSON><PERSON><PERSON> filter", "app.components.FilterBoxes.a11y_selectedFilter": "Valgt statusfilter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Valgt {numberOfSelectedTopics, plural, =0 {null taggfiltre} one {ett taggfilter} other {# taggfiltre}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Alle", "app.components.FilterBoxes.areas": "<PERSON><PERSON><PERSON> etter o<PERSON>", "app.components.FilterBoxes.inputs": "innganger", "app.components.FilterBoxes.noValuesFound": "Ingen tilgjengelige verdier.", "app.components.FilterBoxes.showLess": "Vis mindre", "app.components.FilterBoxes.showTagsWithNumber": "Vis alle ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Tagger", "app.components.FiltersModal.filters": "Filtre", "app.components.FolderFolderCard.a11y_folderDescription": "Mappebeskrivelse:", "app.components.FolderFolderCard.a11y_folderTitle": "Mappetittel:", "app.components.FolderFolderCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {~ # prosjekter} one {# prosjekt} other {# prosjekter}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Felttypen kan ikke endres når det først er sendt inn.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Automatisk lagring", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automatisk lagring er aktivert som standard når du åpner skjemaredigeringsprogrammet. Hver gang du lukker feltinnstillingspanelet ved hjelp av \"X\"-knap<PERSON>, vil det automatisk utløse en lagring.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "F<PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.year": "<PERSON><PERSON>", "app.components.GanttChart.today": "I dag", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON> til<PERSON>", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Gå tilbake til forrige side", "app.components.HookForm.Feedback.errorTitle": "Det er et problem", "app.components.HookForm.Feedback.submissionError": "Prøv på nytt. Hvis problemet ved<PERSON>, kan du kontakte oss", "app.components.HookForm.Feedback.submissionErrorTitle": "Det oppstod et problem hos oss. Beklager.", "app.components.HookForm.Feedback.successMessage": "Skjemaet er vellykket innsendt", "app.components.HookForm.PasswordInput.passwordLabel": "Passord", "app.components.HorizontalScroll.scrollLeftLabel": "Bla til venstre.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON><PERSON> til hø<PERSON>.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideer har ladet.", "app.components.IdeaCards.filters": "Filtre", "app.components.IdeaCards.filters.mostDiscussed": "Mest diskutert", "app.components.IdeaCards.filters.newest": "Ny", "app.components.IdeaCards.filters.oldest": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.popular": "Mest likt", "app.components.IdeaCards.filters.random": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortBy": "Sorter etter", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sortering endret til: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trender", "app.components.IdeaCards.showMore": "Vis mer", "app.components.IdeasMap.a11y_hideIdeaCard": "Skjul idékortet.", "app.components.IdeasMap.a11y_mapTitle": "Kartoversikt", "app.components.IdeasMap.clickOnMapToAdd": "Klikk på kartet for å legge til dine innspill", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Som administrator kan du klikke på kartet for å legge til innspill, selv om denne fasen ikke er aktiv.", "app.components.IdeasMap.filters": "Filtre", "app.components.IdeasMap.multipleInputsAtLocation": "Flere innganger på dette stedet", "app.components.IdeasMap.noFilteredResults": "Filtrene du valgte ga ingen resultater", "app.components.IdeasMap.noResults": "Ingen resultater funnet", "app.components.IdeasMap.or": "eller", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, ingen misliker.} one {1 dislike.} other {, # misliker.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, ingen likes.} one {, 1 liker.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "logg inn", "app.components.IdeasMap.signUpLinkText": "Registrer deg", "app.components.IdeasMap.submitIdea2": "Send inn innspill", "app.components.IdeasMap.tapOnMapToAdd": "Trykk på kartet for å legge til innspill", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Som administrator kan du trykke på kartet for å legge til innspill, selv om denne fasen ikke er aktiv.", "app.components.IdeasMap.userInputs2": "Innspill fra delta<PERSON>ne", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, ingen kommentarer} one {, 1 kommentar} other {, # kommentarer}}", "app.components.IdeasShow.bodyTitle": "Beskrivelse", "app.components.IdeasShow.deletePost": "<PERSON><PERSON>", "app.components.IdeasShow.editPost": "<PERSON><PERSON>", "app.components.IdeasShow.goBack": "<PERSON><PERSON> til<PERSON>", "app.components.IdeasShow.moreOptions": "Flere alternativer", "app.components.IdeasShow.or": "eller", "app.components.IdeasShow.proposedBudgetTitle": "Foreslått budsjett", "app.components.IdeasShow.reportAsSpam": "Rapporter som søppelpost", "app.components.IdeasShow.send": "Send", "app.components.IdeasShow.skipSharing": "<PERSON><PERSON> over det, jeg gjør det senere", "app.components.IdeasShowPage.signIn2": "Logg inn", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON>, du har ikke tilgang til denne siden. Du må kanskje logge inn eller registrere deg for å få tilgang til den.", "app.components.LocationInput.noOptions": "Ingen alternativer", "app.components.Modal.closeWindow": "Lukk vinduet", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Skriv inn en ny e-postadresse", "app.components.PageNotFound.goBackToHomePage": "Tilbake til hjemmesiden", "app.components.PageNotFound.notFoundTitle": "Siden ble ikke funnet", "app.components.PageNotFound.pageNotFoundDescription": "Den forespurte siden ble ikke funnet.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Tilby innhold på minst ett språk", "app.components.PagesForm.editContent": "Innhold", "app.components.PagesForm.fileUploadLabel": "Vedlegg (maks. 50 MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Filene bør ikke være større enn 50 MB. Filer som legges til vil vises nederst på denne siden.", "app.components.PagesForm.navbarItemTitle": "Navn i navlinjen", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "Lagre side", "app.components.PagesForm.saveSuccess": "Siden er vellykket lagret.", "app.components.PagesForm.titleMissingOneLanguageError": "<PERSON><PERSON><PERSON> tittel for minst ett spr<PERSON>k", "app.components.Pagination.back": "Forrige side", "app.components.Pagination.next": "Neste side", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "<PERSON> har brukt {votesCast}, som overskrider grensen på {votesLimit}. Fjern noen varer fra handlekurven og prøv på nytt.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} venstre", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "Du må bruke minst {votesMinimum} før du kan sende inn handlekurven din.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Du må velge minst ett alternativ før du kan sende inn.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "Du må legge noe i handlekurven før du kan sende det inn.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {Ingen studiepoeng igjen} other {~ # ute av {totalNumberOfVotes, plural, one {1 studiepoeng} other {# studiepoeng}} igjen}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Ingen poeng igjen} other {# ut av {totalNumberOfVotes, plural, one {1 poeng} other {# poeng}} igjen}}~", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Ingen tokens igjen} other {~ # ute av {totalNumberOfVotes, plural, one {1 token} other {# tokens}} igjen}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Ingen stemmer igjen} other {# ut av {totalNumberOfVotes, plural, one {1 stemme} other {# stemmer}} igjen}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# stemmer} one {# stemme} other {# stemmer}} avgitt", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Du har avgitt {votesCast} stemmer, noe som overskrider grensen på {votesLimit}. Vennligst fjern noen stemmer og prøv igjen.", "app.components.ParticipationCTABars.addInput": "Legg til input", "app.components.ParticipationCTABars.allocateBudget": "Fordel budsjettet ditt", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Budsjettet ditt har blitt sendt inn.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Åpent for deltakelse", "app.components.ParticipationCTABars.poll": "Ta avstemningen", "app.components.ParticipationCTABars.reviewDocument": "Gå gjennom dokumentet", "app.components.ParticipationCTABars.seeContributions": "Se bidrag", "app.components.ParticipationCTABars.seeEvents3": "Se arrangementer", "app.components.ParticipationCTABars.seeIdeas": "Se ideer", "app.components.ParticipationCTABars.seeInitiatives": "Se initiativer", "app.components.ParticipationCTABars.seeIssues": "Se utgaver", "app.components.ParticipationCTABars.seeOptions": "Se alternativer", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON>", "app.components.ParticipationCTABars.seeProjects": "Se prosjekter", "app.components.ParticipationCTABars.seeProposals": "Se forslag", "app.components.ParticipationCTABars.seeQuestions": "<PERSON> spørsmål", "app.components.ParticipationCTABars.submit": "Send inn", "app.components.ParticipationCTABars.takeTheSurvey": "Ta del i <PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.userHasParticipated": "Du har deltatt i dette prosjektet.", "app.components.ParticipationCTABars.viewInputs": "<PERSON>is inn<PERSON>ger", "app.components.ParticipationCTABars.volunteer": "Engasjer deg", "app.components.ParticipationCTABars.votesCounter.vote": "stemme", "app.components.ParticipationCTABars.votesCounter.votes": "stemmer", "app.components.PasswordInput.a11y_passwordHidden": "Passord skjult", "app.components.PasswordInput.a11y_passwordVisible": "Passord synlig", "app.components.PasswordInput.a11y_strength1Password": "Dårlig passordstyrke", "app.components.PasswordInput.a11y_strength2Password": "Svak passordstyrke", "app.components.PasswordInput.a11y_strength3Password": "Medium passordstyrke", "app.components.PasswordInput.a11y_strength4Password": "Sterk passordstyrke", "app.components.PasswordInput.a11y_strength5Password": "Svært sterk passordstyrke", "app.components.PasswordInput.hidePassword": "Sk<PERSON><PERSON> passord", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "For kort (min. {minimumPasswordLength} tegn)", "app.components.PasswordInput.minimumPasswordLengthError": "Oppgi et passord som er minst {minimumPasswordLength} tegn langt", "app.components.PasswordInput.passwordEmptyError": "Skriv inn passordet ditt", "app.components.PasswordInput.passwordStrengthTooltip1": "For å gjøre passordet ditt sterkere:", "app.components.PasswordInput.passwordStrengthTooltip2": "Bruk en kombinasjon av små bokstaver, store bokstaver, sifre, spesialtegn og tegnsetting som ikke er fortløpende", "app.components.PasswordInput.passwordStrengthTooltip3": "Unngå vanlige eller lett gjettbare ord", "app.components.PasswordInput.passwordStrengthTooltip4": "<PERSON><PERSON>", "app.components.PasswordInput.showPassword": "<PERSON>is passord", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "Svak", "app.components.PasswordInput.strength3Password": "Medium", "app.components.PasswordInput.strength4Password": "Sterk", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON> sterk", "app.components.PostCardsComponents.list": "Liste", "app.components.PostCardsComponents.map": "Kart", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "<PERSON>gg til en offisiell oppdatering", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Er du sikker på at du vil slette denne offisielle oppdateringen?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Sist redigert {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Siste oppdatering: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Offisiell", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Velg hvordan folk skal se navnet ditt", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Offisielt navn på forfatter av oppdateringen", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Offisiell oppdateringstekst", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Offisielle opp<PERSON><PERSON>er", "app.components.PostComponents.OfficialFeedback.postedOn": "<PERSON><PERSON><PERSON> {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Vis tidligere opp<PERSON><PERSON>er", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Gi en oppdatering...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON>, det oppstod et problem", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Opp<PERSON>r melding", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Oppdateringen din ble publisert!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "<PERSON><PERSON><PERSON> mitt bidrag '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON> mitt bidrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> mitt bidrag: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "<PERSON><PERSON><PERSON> min idé \"{postTitle}\" på {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "<PERSON><PERSON><PERSON> ideen min: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON> ideen min: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Hva synes du om dette forslaget? Stem på det og del diskusjonen på {postUrl} for å gjøre din stemme hørt!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "<PERSON><PERSON><PERSON> forslaget mitt: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON> <PERSON>t mitt: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Jeg la ut en kommentar '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "Jeg la nettopp ut en kommentar: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "Jeg la nettopp ut en kommentar: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "<PERSON><PERSON><PERSON> mitt foreslåtte alternativ \"{postTitle}\" på {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "<PERSON><PERSON><PERSON> mitt foreslåtte alternativ: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON><PERSON> mitt alternativ: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "<PERSON><PERSON><PERSON> underskriftskampanjen min '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON> underskriftskampanjen min: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON> underskriftskampanjen min: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "<PERSON><PERSON><PERSON> prosjektet mitt '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "<PERSON><PERSON><PERSON> prosjektet mitt: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "<PERSON><PERSON><PERSON> prosjektet mitt: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "<PERSON><PERSON><PERSON> forslaget mitt '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON> forslaget mitt: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "Jeg har nettopp lagt ut et forslag til {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Bli med i diskusjonen om dette spørsmålet '{postTitle}' på {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Bli med i diskusjonen: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Bli med i diskusjonen: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "<PERSON><PERSON> på {postTitle} på", "app.components.PostComponents.linkToHomePage": "<PERSON><PERSON> til <PERSON>iden", "app.components.PostComponents.readMore": "Les mer...", "app.components.PostComponents.topics": "<PERSON><PERSON><PERSON>", "app.components.ProjectArchivedIndicator.archivedProject": "Dessverre kan du ikke delta i dette prosjektet lenger, fordi det er arkivert", "app.components.ProjectArchivedIndicator.previewProject": "Utkast til prosjekt:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "<PERSON><PERSON> synlig for moderatorer og de som har en forhåndsvisningskobling.", "app.components.ProjectCard.a11y_projectDescription": "Prosjektbeskrivelse:", "app.components.ProjectCard.a11y_projectTitle": "Prosjekttittel:", "app.components.ProjectCard.addYourOption": "<PERSON>gg til ditt forslag", "app.components.ProjectCard.allocateYourBudget": "Fordel budsjettet ditt", "app.components.ProjectCard.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.comment": "Kommentar", "app.components.ProjectCard.contributeYourInput": "<PERSON><PERSON><PERSON> med dine innspill", "app.components.ProjectCard.finished": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.joinDiscussion": "Bli med i diskusjonen", "app.components.ProjectCard.learnMore": "Les mer", "app.components.ProjectCard.reaction": "Re<PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "Les rapporten", "app.components.ProjectCard.reviewDocument": "Gå gjennom dokumentet", "app.components.ProjectCard.submitAnIssue": "Send inn en kommentar", "app.components.ProjectCard.submitYourIdea": "Send inn ideen din", "app.components.ProjectCard.submitYourInitiative": "Send inn initiativet ditt", "app.components.ProjectCard.submitYourPetition": "Send inn underskriftskampanjen din", "app.components.ProjectCard.submitYourProject": "Send inn prosjektet ditt", "app.components.ProjectCard.submitYourProposal": "Send inn forslaget ditt", "app.components.ProjectCard.takeThePoll": "Ta avstemningen", "app.components.ProjectCard.takeTheSurvey": "Ta del i <PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheContributions": "Se bidragene", "app.components.ProjectCard.viewTheIdeas": "Se ideene", "app.components.ProjectCard.viewTheInitiatives": "Se initiativene", "app.components.ProjectCard.viewTheIssues": "Se kommentarene", "app.components.ProjectCard.viewTheOptions": "Se alternativene", "app.components.ProjectCard.viewThePetitions": "Se underskriftskampanjene", "app.components.ProjectCard.viewTheProjects": "Se prosjektene", "app.components.ProjectCard.viewTheProposals": "Se forslagene", "app.components.ProjectCard.viewTheQuestions": "Se spørsmå<PERSON>", "app.components.ProjectCard.vote": "<PERSON><PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {~ # comments} other {# comments}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {~ # contribution} other {# contributions}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {ingen ideer ennå} one {# idé} other {# ideer}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {~ # initiativer} one {# initiativ} other {# initiativer}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# comment} other {# comments}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# alternativ} other {# alternativer}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {~ # petitions} one {# petition} other {# petitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {~ # prosjekt} other {~ # prosjekter}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {~ # forslag} one {# forslag} other {# forslag}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {~ # spørsm<PERSON>l} other {# spørsmål}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {~ # comments} one {# comments} other {# comments}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {~ # innganger} one {# inngang} other {# innganger}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {~ # prosjekter} one {# prosjekt} other {# prosjekter}}", "app.components.ProjectFolderCards.components.Topbar.all": "Alle", "app.components.ProjectFolderCards.components.Topbar.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.draft": "Utkast", "app.components.ProjectFolderCards.components.Topbar.filterBy": "<PERSON><PERSON><PERSON> etter", "app.components.ProjectFolderCards.components.Topbar.published2": "<PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "Det er for øyeblikket ingen åpne prosjekter", "app.components.ProjectFolderCards.noProjectsAvailable": "Ingen tilgjengelige prosjekter", "app.components.ProjectFolderCards.showMore": "Vis mer", "app.components.ProjectFolderCards.stayTuned": "<PERSON><PERSON> til<PERSON>ke igjen for nye muligheter for engasjement", "app.components.ProjectFolderCards.tryChangingFilters": "<PERSON><PERSON><PERSON><PERSON> <PERSON> endre de valgte filtrene.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Brukes også i disse byene:", "app.components.ProjectTemplatePreview.copied": "<PERSON><PERSON><PERSON>", "app.components.ProjectTemplatePreview.copyLink": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.alignCenter": "Midtstilt tekst", "app.components.QuillEditor.alignLeft": "<PERSON>er til venstre", "app.components.QuillEditor.alignRight": "<PERSON><PERSON> til <PERSON>ø<PERSON>", "app.components.QuillEditor.bold": "Fet", "app.components.QuillEditor.clean": "Fjern formatering", "app.components.QuillEditor.customLink": "<PERSON>gg til knapp", "app.components.QuillEditor.customLinkPrompt": "Skriv inn lenke:", "app.components.QuillEditor.edit": "<PERSON><PERSON>", "app.components.QuillEditor.image": "Last opp bilde", "app.components.QuillEditor.imageAltPlaceholder": "<PERSON>rt beskrivelse av bildet", "app.components.QuillEditor.italic": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.link": "Legg til lenke", "app.components.QuillEditor.linkPrompt": "Skriv inn lenke:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Ordnet liste", "app.components.QuillEditor.remove": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.save": "Lagre", "app.components.QuillEditor.subtitle": "Undertittel", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Uordnet liste", "app.components.QuillEditor.video": "Legg til video", "app.components.QuillEditor.videoPrompt": "Gå inn på video:", "app.components.QuillEditor.visitPrompt": "Besøk link:", "app.components.ReactionControl.completeProfileToReact": "<PERSON><PERSON><PERSON> ut profilen din for å reagere", "app.components.ReactionControl.dislike": "<PERSON><PERSON><PERSON> liker", "app.components.ReactionControl.dislikingDisabledMaxReached": "Du har nådd ditt maksimale antall dislikes på {projectName}", "app.components.ReactionControl.like": "Som", "app.components.ReactionControl.likingDisabledMaxReached": "<PERSON> har nådd ditt maksimale antall likes på {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reacting blir aktivert når denne fasen starter", "app.components.ReactionControl.reactingDisabledPhaseOver": "Det er ikke lenger mulig å reagere i denne fasen", "app.components.ReactionControl.reactingDisabledProjectInactive": "Du kan ikke lenger reagere på ideer på {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Reacting er for øyeblikket ikke aktivert for dette prosjektet", "app.components.ReactionControl.reactingNotPermitted": "Reacting er bare aktivert for visse grupper", "app.components.ReactionControl.reactingNotSignedIn": "Logg inn for å reagere.", "app.components.ReactionControl.reactingPossibleLater": "Reacting vil starte på {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Bekreft identiteten din for å kunne reagere.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Dato for arrangementet: {startDate} på {startTime} til {endDate} på {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Dato for arrangementet: {eventDate} fra {startTime} til {endTime}.", "app.components.Sharing.linkCopied": "<PERSON><PERSON><PERSON> lenke", "app.components.Sharing.or": "eller", "app.components.Sharing.share": "Del", "app.components.Sharing.shareByEmail": "Del via e-post", "app.components.Sharing.shareByLink": "<PERSON><PERSON><PERSON>", "app.components.Sharing.shareOnFacebook": "<PERSON>", "app.components.Sharing.shareOnTwitter": "<PERSON>", "app.components.Sharing.shareThisEvent": "Del dette arrangementet", "app.components.Sharing.shareThisFolder": "Del", "app.components.Sharing.shareThisProject": "Del dette prosjektet", "app.components.Sharing.shareViaMessenger": "<PERSON> via Messenger", "app.components.Sharing.shareViaWhatsApp": "Del via WhatsApp", "app.components.SideModal.closeButtonAria": "Lukk", "app.components.StatusModule.futurePhase": "Du ser på en fase som ikke har startet ennå. Du vil kunne delta når fasen starter.", "app.components.StatusModule.modifyYourSubmission1": "<PERSON><PERSON> innsendingen din", "app.components.StatusModule.submittedUntil3": "Din stemme kan avgis frem til", "app.components.TopicsPicker.numberOfSelectedTopics": "Valgt {numberOfSelectedTopics, plural, =0 {null tagger} one {én tagg} other {# tagger}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "<PERSON><PERSON><PERSON>", "app.components.UI.MoreActionsMenu.moreOptions": "Flere alternativer", "app.components.UI.MoreActionsMenu.showMoreActions": "<PERSON>is flere <PERSON>er", "app.components.UI.PhaseFilter.noAppropriatePhases": "Ingen passende faser funnet for dette prosjektet", "app.components.UI.RemoveImageButton.a11y_removeImage": "<PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Oversett", "app.components.Unauthorized.additionalInformationRequired": "Det kreves ytterligere informasjon for at du skal kunne delta.", "app.components.Unauthorized.completeProfile": "Fullstendig profil", "app.components.Unauthorized.completeProfileTitle": "<PERSON><PERSON><PERSON> ut profilen din for å delta", "app.components.Unauthorized.noPermission": "Du har ikke tillatelse til å se denne siden", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON>, du har ikke tilgang til denne siden.", "app.components.Upload.errorImageMaxSizeExceeded": "<PERSON><PERSON>t du har valgt er større enn {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "<PERSON>tt eller flere av bildene du har valgt, er større enn {maxFileSize}MB", "app.components.Upload.onlyOneImage": "Du kan bare laste opp 1 bilde", "app.components.Upload.onlyXImages": "Du kan bare laste opp bilder fra {maxItemsCount}", "app.components.Upload.remaining": "resterende", "app.components.Upload.uploadImageLabel": "Velg et bilde (maks. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "<PERSON>elg ett eller flere bilder", "app.components.UpsellTooltip.tooltipContent": "Denne funksjonen er ikke inkludert i din nåværende plan. Snakk med din Government Success Manager eller administrator for å låse den opp.", "app.components.UserName.anonymous": "Anonym", "app.components.UserName.anonymousTooltip2": "Denne brukeren har valgt å anonymisere sitt bidrag", "app.components.UserName.authorWithNoNameTooltip": "Navnet ditt har blitt autogenerert fordi du ikke har oppgitt navnet ditt. Oppdater profilen din hvis du ønsker å endre det.", "app.components.UserName.deletedUser": "<PERSON><PERSON><PERSON> for<PERSON>", "app.components.UserName.verified": "Verifisert", "app.components.VerificationModal.verifyAuth0": "Verifiser med NemID", "app.components.VerificationModal.verifyBOSA": "Verifiser med itsme eller eID", "app.components.VerificationModal.verifyBosaFas": "Verifiser med itsme eller eID", "app.components.VerificationModal.verifyClaveUnica": "Verifiser med Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Verifiser med falsk SSO", "app.components.VerificationModal.verifyIdAustria": "Bekreft med ID Austria", "app.components.VerificationModal.verifyKeycloak": "Bekreft med ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verifiser med MitID", "app.components.VerificationModal.verifyTwoday2": "Verifiser med BankID eller Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Bekreft identiteten din", "app.components.VoteControl.budgetingFutureEnabled": "Du kan tildele budsjettet ditt fra {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Deltakende budsjettering er ikke aktivert for øyeblikket.", "app.components.VoteControl.budgetingNotPossible": "Det er ikke mulig å gjøre endringer i budsjettet ditt på nåværende tidspunkt.", "app.components.VoteControl.budgetingNotVerified": "Vennligst {verifyAccountLink} for å fortsette.", "app.components.VoteInputs._shared.currencyLeft1": "Du har {budgetLeft} / {totalBudget} igjen", "app.components.VoteInputs._shared.numberOfCreditsLeft": "Du har {votesLeft, plural, =0 {ingen studiepoeng igjen} other {# ut av {totalNumberOfVotes, plural, one {1 studiepoeng} other {# studiepoeng}} igjen}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "Du har {votesLeft, plural, =0 {ingen poeng igjen} other {# ut av {totalNumberOfVotes, plural, one {1 poeng} other {# poeng}} igjen}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "Du har {votesLeft, plural, =0 {ingen symboler igjen} other {# ut av {totalNumberOfVotes, plural, one {1 symbol} other {# symboler}} igjen}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "Du har {votesLeft, plural, =0 {ingen stemmer igjen} other {# av {totalNumberOfVotes, plural, one {1 stemme} other {# stemmer}} igjen}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Du har allerede sendt inn budsjettet ditt. H<PERSON> du vil endre det, klikker du på \"Endre innsendingen din\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Du har allerede sendt inn budsjettet ditt. H<PERSON> du vil endre det, går du tilbake til prosjektsiden og klikker på \"Endre innsendingen\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budsjettering er ikke til<PERSON>g, siden denne fasen ikke er aktiv.", "app.components.VoteInputs.single.youHaveVotedForX2": "Du har stemt på {votes, plural, =0 {~ # options} one {# option} other {# options}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON><PERSON> betyr at du mister alle data som er knyttet til denne inndataen, for eksempel kommentarer, reaksjoner og stemmer. Denne handlingen kan ikke angres.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Er du sikker på at du vil slette denne inngangen?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Bekreft", "app.components.admin.SlugInput.resultingURL": "Resulterende URL", "app.components.admin.SlugInput.slugTooltip": "Sluggen er det unike settet med ord på slutten av sidens nettadresse, eller URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON><PERSON> du endrer URL-adress<PERSON>, vil lenker til siden som bruker den gamle URL-adressen, ikke lenger fungere.", "app.components.admin.SlugInput.urlSlugLabel": "Snegl", "app.components.admin.UserFilterConditions.addCondition": "Legg til en betingelse", "app.components.admin.UserFilterConditions.field_email": "E-post", "app.components.admin.UserFilterConditions.field_event_attendance": "Registrering av arrangementer", "app.components.admin.UserFilterConditions.field_follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_lives_in": "<PERSON><PERSON> i", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Spørreundersøkelse i lokalsamfunnet", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interagerte med en inngang med status", "app.components.admin.UserFilterConditions.field_participated_in_project": "Bidratt til prosjektet", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Postet noe med taggen", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Registreringsdato", "app.components.admin.UserFilterConditions.field_role": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_verified": "Verifisering", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Forslag", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "ikke er påmeldt til noen av disse arrangementene", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "ikke er påmeldt til noe arrangement", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "er påmeldt til et av disse arrangementene", "app.components.admin.UserFilterConditions.predicate_attends_something": "er påmeldt til minst ett arrangement", "app.components.admin.UserFilterConditions.predicate_begins_with": "begynner med", "app.components.admin.UserFilterConditions.predicate_commented_in": "kommenterte", "app.components.admin.UserFilterConditions.predicate_contains": "inneholder", "app.components.admin.UserFilterConditions.predicate_ends_on": "slutter på", "app.components.admin.UserFilterConditions.predicate_has_value": "har verdi", "app.components.admin.UserFilterConditions.predicate_in": "utført noen handling", "app.components.admin.UserFilterConditions.predicate_is": "er", "app.components.admin.UserFilterConditions.predicate_is_admin": "er en administrator", "app.components.admin.UserFilterConditions.predicate_is_after": "er etter", "app.components.admin.UserFilterConditions.predicate_is_before": "er før", "app.components.admin.UserFilterConditions.predicate_is_checked": "er sjekket", "app.components.admin.UserFilterConditions.predicate_is_empty": "er tom", "app.components.admin.UserFilterConditions.predicate_is_equal": "er", "app.components.admin.UserFilterConditions.predicate_is_exactly": "er nøyaktig", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "er stø<PERSON> enn", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "er større enn eller lik", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "er en vanlig bruker", "app.components.admin.UserFilterConditions.predicate_is_not_area": "ekskluderer område", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "ekskluderer mappe", "app.components.admin.UserFilterConditions.predicate_is_not_input": "ekskluderer input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "ekskluderer prosjekt", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "utelukker emne", "app.components.admin.UserFilterConditions.predicate_is_one_of": "er en av", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "ett av områdene", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "en av mappene", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "en av inngangene", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "ett av prosjektene", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "ett av temaene", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "er prosjektleder", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "er mindre enn", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "er mindre enn eller lik", "app.components.admin.UserFilterConditions.predicate_is_verified": "er verifisert", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "begynner ikke med", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "kommenterte ikke", "app.components.admin.UserFilterConditions.predicate_not_contains": "inneholder ikke", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "slutter ikke på", "app.components.admin.UserFilterConditions.predicate_not_has_value": "har ikke verdi", "app.components.admin.UserFilterConditions.predicate_not_in": "bidro ikke", "app.components.admin.UserFilterConditions.predicate_not_is": "er ikke", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "ikke er administrator", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "er ikke sjekket", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "ikke er tom", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "er ikke", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "ikke er en vanlig bruker", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "er ikke en av", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "ikke er prosjektleder", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "er ikke verifisert", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "postet ikke et innspill", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "reagerte ikke på kommentar", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "reagerte ikke på innspill", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "ikke meldte seg på et arrangement", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "har ikke tatt under<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "meldte seg ikke frivillig", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "deltok ikke i avstemningen", "app.components.admin.UserFilterConditions.predicate_nothing": "ingenting", "app.components.admin.UserFilterConditions.predicate_posted_input": "postet et innspill", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reagerte på kommentar", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reagerte på innspill", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "påmeldt til et arrangement", "app.components.admin.UserFilterConditions.predicate_something": "noe", "app.components.admin.UserFilterConditions.predicate_taken_survey": "har tatt <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "fri<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_voted_in3": "deltok i avstemningen", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attributt", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Tilstand", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Verdi", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Du vil ikke få varsler om bidraget ditt", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "Fortsett", "app.components.anonymousParticipationModal.participateAnonymously": "Delta anonymt", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "<PERSON><PERSON> <b>s<PERSON><PERSON><PERSON> profilen din</b> for <PERSON><PERSON>, prosjektledere og andre beboere for dette spesifikke bidraget, slik at ingen kan knytte dette bidraget til deg. Anonyme bidrag kan ikke redigeres og anses som endelige.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Å gjøre plattformen vår trygg for alle brukere er en topp prioritet for oss. <PERSON>d er viktige, så vær snille mot hverandre.", "app.components.avatar.titleForAccessibility": "Profil av {fullName}", "app.components.customFields.mapInput.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.customFields.mapInput.undo": "<PERSON><PERSON>", "app.components.customFields.mapInput.undoLastPoint": "<PERSON><PERSON> siste <PERSON>t", "app.components.followUnfollow.follow": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followADiscussion": "<PERSON><PERSON><PERSON><PERSON>", "app.components.followUnfollow.followTooltipInputPage2": "Følgende utløser e-postoppdateringer om statusendringer, offisielle oppdateringer og kommentarer. Du kan når som helst gå til {unsubscribeLink} .", "app.components.followUnfollow.followTooltipProjects2": "Følgende utløser e-postoppdateringer om endringer i prosjektet. Du kan når som helst sende {unsubscribeLink} .", "app.components.followUnfollow.unFollow": "<PERSON><PERSON><PERSON> føl<PERSON>", "app.components.followUnfollow.unsubscribe": "meld av", "app.components.followUnfollow.unsubscribeUrl": "/profil/rediger", "app.components.form.ErrorDisplay.guidelinesLinkText": "våre retningslinjer", "app.components.form.ErrorDisplay.next": "Neste", "app.components.form.ErrorDisplay.previous": "Tidligere", "app.components.form.ErrorDisplay.save": "Lagre", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Begynn å skrive for å søke etter brukerens e-postadresse eller navn...", "app.components.form.anonymousSurveyMessage2": "Alle svar på denne under<PERSON><PERSON><PERSON> er anonymisert.", "app.components.form.backToInputManager": "Til<PERSON>ke til input manager", "app.components.form.backToProject": "Tilbake til prosjektet", "app.components.form.components.controls.mapInput.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.form.components.controls.mapInput.undo": "<PERSON><PERSON>", "app.components.form.components.controls.mapInput.undoLastPoint": "<PERSON><PERSON> siste <PERSON>t", "app.components.form.controls.addressInputAriaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.controls.addressInputPlaceholder6": "Skriv inn en adresse...", "app.components.form.controls.adminFieldTooltip": "Feltet er bare synlig for administratorer", "app.components.form.controls.allStatementsError": "Du må velge et svar for alle påstander.", "app.components.form.controls.back": "Tilbake", "app.components.form.controls.clearAll": "<PERSON>ø<PERSON> alle", "app.components.form.controls.clearAllScreenreader": "<PERSON><PERSON>n alle svarene fra matrisespørsmålet ovenfor", "app.components.form.controls.clickOnMapMultipleToAdd3": "Klikk på kartet for å tegne. Dra deretter på punkter for å flytte dem.", "app.components.form.controls.clickOnMapToAddOrType": "Klikk på kartet eller skriv inn en adresse nedenfor for å legge til svaret ditt.", "app.components.form.controls.confirm": "Bekreft", "app.components.form.controls.cosponsorsPlaceholder": "Begynn å skrive inn et navn for å søke", "app.components.form.controls.currentRank": "Nåværende rang:", "app.components.form.controls.minimumCoordinates2": "<PERSON> kreves minst {numPoints} kartpunkter.", "app.components.form.controls.noRankSelected": "Ingen rang valgt", "app.components.form.controls.notPublic1": "*<PERSON><PERSON> svaret vil kun bli delt med prosjektledere, og ikke med offentligheten.", "app.components.form.controls.optionalParentheses": "(valg<PERSON><PERSON>t)", "app.components.form.controls.rankingInstructions": "<PERSON>a og slipp for å rangere alternativer.", "app.components.form.controls.selectAsManyAsYouLike": "*Velg så mange du vil", "app.components.form.controls.selectBetween": "*Velg mellom {minItems} og {maxItems}", "app.components.form.controls.selectExactly2": "*Velg nøyaktig {selectExactly, plural, one {# option} other {# options}}", "app.components.form.controls.selectMany": "*Velg så mange du vil", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Trykk på kartet for å tegne. Dra deretter på punkter for å flytte dem.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Trykk på kartet for å tegne.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Trykk på kartet for å legge til svaret ditt.", "app.components.form.controls.tapOnMapToAddOrType": "Trykk på kartet eller skriv inn en adresse nedenfor for å legge til svaret ditt.", "app.components.form.controls.tapToAddALine": "Trykk for å legge til en linje", "app.components.form.controls.tapToAddAPoint": "Trykk for å legge til et punkt", "app.components.form.controls.tapToAddAnArea": "Trykk for å legge til et område", "app.components.form.controls.uploadShapefileInstructions": "* Last opp en zip-fil som inneholder en eller flere shapefiler.", "app.components.form.controls.validCordinatesTooltip2": "<PERSON><PERSON> plass<PERSON>en ikke vises blant alternativene mens du skriver, kan du legge til gyldige koordinater i formatet \"breddegrad, lengdegrad\" for å angi en nøyaktig plassering (f.eks. -33,019808, -71,495676).", "app.components.form.controls.valueOutOfTotal": "{value} ut av {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} ut av {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} ut av {total}, der {maxValue} er {maxLabel}", "app.components.form.error": "<PERSON><PERSON>", "app.components.form.locationGoogleUnavailable": "Kunne ikke laste inn stedsfeltet fra Google Maps.", "app.components.form.progressBarLabel": "Fremdrift i undersøkelsen", "app.components.form.submit": "Send inn", "app.components.form.submitApiError": "Det oppstod et problem med å sende inn skjemaet. Vennligst se etter eventuelle feil og prøv igjen.", "app.components.form.verifiedBlocked": "Du kan ikke redigere dette feltet fordi det inneholder verifisert informasjon", "app.components.formBuilder.Page": "Side", "app.components.formBuilder.accessibilityStatement": "tilgjengelighetserklæring", "app.components.formBuilder.addAnswer": "<PERSON><PERSON> til svar", "app.components.formBuilder.addStatement": "Legg til uttalelse", "app.components.formBuilder.agree": "<PERSON><PERSON>", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "<PERSON><PERSON> du har tilgang til AI-pakken vår, vil du kunne oppsummere og kategorisere tekstsvar med AI", "app.components.formBuilder.askFollowUpToggleLabel": "<PERSON><PERSON><PERSON><PERSON> om oppfølging", "app.components.formBuilder.bad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.buttonLabel": "Knappemerke", "app.components.formBuilder.buttonLink": "Knappelink", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.chooseMany": "Velg mange", "app.components.formBuilder.chooseOne": "Velg én", "app.components.formBuilder.close": "Lukk", "app.components.formBuilder.closed": "Steng<PERSON>", "app.components.formBuilder.configureMap": "<PERSON>n<PERSON><PERSON><PERSON> kartet", "app.components.formBuilder.confirmLeaveBuilderButtonText": "<PERSON>a, jeg vil dra", "app.components.formBuilder.content": "Innhold", "app.components.formBuilder.continuePageLabel": "Fortsetter å", "app.components.formBuilder.cosponsors": "<PERSON><PERSON><PERSON><PERSON>rer", "app.components.formBuilder.default": "Standard", "app.components.formBuilder.defaultContent": "Standard innhold", "app.components.formBuilder.delete": "<PERSON><PERSON>", "app.components.formBuilder.deleteButtonLabel": "<PERSON><PERSON>", "app.components.formBuilder.description": "Beskrivelse", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Dette er allerede lagt til i skjemaet. Standardinnhold kan bare brukes én gang.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Å legge til tilpasset innhold er ikke en del av din nåværende lisens. Ta kontakt med din GovSuccess Manager for å lære mer om dette.", "app.components.formBuilder.disagree": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.displayAsDropdown": "Vis som nedtrekksmeny", "app.components.formBuilder.displayAsDropdownTooltip": "Vis alternativene i en rullegardinmeny. Dette anbefales hvis du har mange alternativer.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "Tegn område", "app.components.formBuilder.drawRoute": "Tegn rute", "app.components.formBuilder.dropPin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Oppgi minst 1 svar. Vær oppmerksom på at hvert svar må ha en tittel.", "app.components.formBuilder.emptyOptionError": "Oppgi minst 1 svar", "app.components.formBuilder.emptyStatementError": "Gi minst 1 uttalelse", "app.components.formBuilder.emptyTitleError": "Oppgi en tittel på spørsmålet", "app.components.formBuilder.emptyTitleMessage": "<PERSON><PERSON><PERSON> en tittel for alle svarene", "app.components.formBuilder.emptyTitleStatementMessage": "Oppgi en tittel for alle utsagnene", "app.components.formBuilder.enable": "Aktivere", "app.components.formBuilder.errorMessage": "Det er et problem, vennligst fiks problemet for å kunne lagre endringene dine", "app.components.formBuilder.fieldGroup.description": "Beskrivelse (valgfritt)", "app.components.formBuilder.fieldGroup.title": "<PERSON><PERSON><PERSON> (valg<PERSON>ritt)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "For øyeblikket er svarene på disse spørsmålene bare tilgjengelige i den eksporterte Excel-filen i Input Manager, og er ikke synlige for brukerne.", "app.components.formBuilder.fieldLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.fieldLabelStatement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.fileUpload": "Filopplasting", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Kartbasert side", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Legg inn kart som kontekst eller still stedsbaserte spørsmål til deltakerne.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "For å få en optimal brukeropplevelse anbefaler vi ikke å legge til punkt-, rute- eller områdespørsmål på kartbaserte sider.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal side", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Kartleggingsfunksjoner er ikke inkludert i din nåværende lisens. Ta kontakt med din GovSuccess Manager for å finne ut mer.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Sidetype", "app.components.formBuilder.formEnd": "<PERSON><PERSON><PERSON><PERSON> slutt", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "J<PERSON>, slett side", "app.components.formBuilder.formField.copyNoun": "<PERSON><PERSON>", "app.components.formBuilder.formField.copyVerb": "<PERSON><PERSON>", "app.components.formBuilder.formField.delete": "<PERSON><PERSON>", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "<PERSON><PERSON> du sletter denne siden, slettes også logikken som er knyttet til den. Er du sikker på at du vil slette den?", "app.components.formBuilder.formField.deleteResultsInfo": "Dette kan ikke gjø<PERSON> ugjort", "app.components.formBuilder.goToPageInputLabel": "Så er neste side:", "app.components.formBuilder.good": "Bra", "app.components.formBuilder.helmetTitle": "S<PERSON><PERSON><PERSON><PERSON>gger", "app.components.formBuilder.imageFileUpload": "Bildeopplasting", "app.components.formBuilder.invalidLogicBadgeMessage": "Ugyldig logikk", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON> (valgfritt)", "app.components.formBuilder.labelsTooltipContent2": "<PERSON>elg valgf<PERSON> etiketter for alle de lineære skalaverdiene.", "app.components.formBuilder.lastPage": "Slutt", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Er du sikker på at du vil dra?", "app.components.formBuilder.leaveBuilderText": "Du har endringer som ikke er lagret. Vennligst lagre før du går. <PERSON><PERSON> du forlater oss, mister du endringene dine.", "app.components.formBuilder.limitAnswersTooltip": "<PERSON><PERSON><PERSON> den er slått på, må respondentene velge det angitte antallet svar for å fortsette.", "app.components.formBuilder.limitNumberAnswers": "<PERSON><PERSON><PERSON> antall svar", "app.components.formBuilder.linePolygonMapWarning2": "Linje- og polygontegninger oppfyller kanskje ikke tilgjengelighetsstandardene. Du finner mer informasjon på {accessibilityStatement}.", "app.components.formBuilder.linearScale": "<PERSON><PERSON><PERSON> skala", "app.components.formBuilder.locationDescription": "Beliggenhet", "app.components.formBuilder.logic": "Logikk", "app.components.formBuilder.logicAnyOtherAnswer": "Ethvert annet svar", "app.components.formBuilder.logicConflicts.conflictingLogic": "Motstridende logikk", "app.components.formBuilder.logicConflicts.interQuestionConflict": "<PERSON>ne siden inneholder spørsmål som fører til ulike sider. <PERSON><PERSON> deltakerne svarer på flere spørsmål, vises den siden som er lengst unna. <PERSON><PERSON><PERSON> for at dette samsvarer med den tiltenkte flyten.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "<PERSON><PERSON> siden har flere logiske regler: logikk for flervalgsspørsmål, logikk på sidenivå og logikk mellom spørsmål. <PERSON><PERSON>r disse forholdene overlapper h<PERSON>andre, vil spørsmålslogikken ha forrang over sidelogik<PERSON>, og den siden som ligger lengst unna, vil bli vist. Gå gjennom logikken for å sikre at den er i tråd med den tiltenkte flyten.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Denne siden inneholder et flervalgsspørsmål der alternativene fører til forskjellige sider. <PERSON><PERSON> deltakerne velger flere alternativer, vises den siden som ligger lengst unna. <PERSON><PERSON><PERSON> for at dette samsvarer med den tiltenkte flyten.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Denne siden inneholder et flervalgsspørsmål der alternativene fører til forskjellige sider, og har spørsmål som fører til andre sider. Den lengste siden vises hvis disse forholdene overlapper hverandre. <PERSON><PERSON><PERSON> for at dette samsvarer med den tiltenkte flyten.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Denne siden inneholder et flervalgsspørsmål der alternativene fører til forskjellige sider, og har logikk angitt både på side- og spørsmålsnivå. Spørsmålslogikken har forrang, og den siden som ligger lengst unna, vises. <PERSON><PERSON><PERSON> for at denne oppførselen stemmer overens med den tiltenkte flyten.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Denne siden har logikk på både sidenivå og spørsmålsnivå. Spørsmålslogikk vil ha forrang over logikk på sidenivå. <PERSON><PERSON><PERSON> for at denne oppførselen stemmer overens med den tiltenkte flyten.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Denne siden har logikk på både side- og spørsmålsnivå, og flere spørsmål leder til forskjellige sider. Spørsmålslogikken har forrang, og siden som ligger lengst unna, vises. <PERSON><PERSON><PERSON> for at denne oppførselen stemmer overens med den tiltenkte flyten.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.logicPanelAnyOtherAnswer": "<PERSON><PERSON> noe annet svar", "app.components.formBuilder.logicPanelNoAnswer": "<PERSON><PERSON> ikke be<PERSON>t", "app.components.formBuilder.logicValidationError": "Logikk kan ikke lenke til tidligere sider", "app.components.formBuilder.longAnswer": "<PERSON><PERSON> svar", "app.components.formBuilder.mapConfiguration": "Kartkonfigurasjon", "app.components.formBuilder.mapping": "Kartlegging", "app.components.formBuilder.mappingNotInCurrentLicense": "Kartleggingsfunksjoner er ikke inkludert i din nåværende lisens. Ta kontakt med din GovSuccess Manager for å finne ut mer.", "app.components.formBuilder.matrix": "Matrise", "app.components.formBuilder.matrixSettings.columns": "<PERSON>lter", "app.components.formBuilder.matrixSettings.rows": "<PERSON><PERSON>", "app.components.formBuilder.multipleChoice": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multipleChoiceHelperText": "<PERSON><PERSON> flere alternativer fører til forskjellige sider og deltakerne velger mer enn ett, vises den siden som ligger lengst unna. <PERSON><PERSON><PERSON> for at dette samsvarer med den tiltenkte flyten.", "app.components.formBuilder.multipleChoiceImage": "Valg av bilde", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Nøytral", "app.components.formBuilder.newField": "<PERSON><PERSON><PERSON> felt", "app.components.formBuilder.number": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.optional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "Alternativet \"Annet\"", "app.components.formBuilder.otherOptionTooltip": "Tillat deltakerne å legge inn et tilpasset svar hvis de oppgitte svarene ikke stemmer overens med deres preferanser", "app.components.formBuilder.page": "Side", "app.components.formBuilder.pageCannotBeDeleted": "<PERSON>ne siden kan ikke slettes.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "<PERSON>ne siden kan ikke slettes, og det er ikke mulig å legge til flere felt.", "app.components.formBuilder.pageRuleLabel": "Neste side er:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "<PERSON>vis ingen logikk er lagt til, vil skjemaet følge sin normale flyt. <PERSON>vis både siden og spørsmålene har logikk, vil spørsmålslogikken ha forrang. <PERSON><PERSON><PERSON> for at dette stemmer overens med den tiltenkte flyten For mer informasjon, besøk {supportPageLink}", "app.components.formBuilder.preview": "Forhåndsvisning:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Medsponsorer vises ikke på den nedlastede PDF-filen og støttes ikke for import via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Filopplastingsspørsmål vises som ikke-støttet i den nedlastede PDF-filen og støttes ikke for import via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Kartleggingsspørsmål vises på den nedlastede PDF-filen, men lagene vil ikke være synlige. Kartleggingsspørsmål støttes ikke for import via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vises i den nedlastede PDF-filen, men støttes for øyeblikket ikke for import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Sidetitler og -beskrivelser vises som en seksjonsoverskrift i den nedlastede PDF-filen.", "app.components.formBuilder.printSupportTooltip.ranking": "Rangeringsspørsmål vises i den nedlastede PDF-filen, men støttes for øyeblikket ikke for import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tagger vises som ikke-støttet i den nedlastede PDF-filen og støttes ikke for import via FormSync.", "app.components.formBuilder.proposedBudget": "Foreslått budsjett", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "<PERSON><PERSON> s<PERSON>ø<PERSON>let kan ikke slettes.", "app.components.formBuilder.questionDescriptionOptional": "Beskrivelse av spørsmålet (valgfritt)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON> på spørsmålet", "app.components.formBuilder.randomize": "Randomiser", "app.components.formBuilder.randomizeToolTip": "Rekkefølgen på svarene vil bli randomisert per bruker", "app.components.formBuilder.range": "Rekkevidde", "app.components.formBuilder.ranking": "Rangering", "app.components.formBuilder.rating": "Vurdering", "app.components.formBuilder.removeAnswer": "<PERSON><PERSON><PERSON> svar", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "<PERSON><PERSON><PERSON><PERSON> det obligatorisk å svare på dette spørsmålet", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON><PERSON> svaret er..:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "<PERSON><PERSON> s<PERSON> inkluderer:", "app.components.formBuilder.save": "Lagre", "app.components.formBuilder.selectRangeTooltip": "Velg den maksimale verdien for skalaen din.", "app.components.formBuilder.sentiment": "Sentiments<PERSON><PERSON>", "app.components.formBuilder.shapefileUpload": "Esri shapefile-opplasting", "app.components.formBuilder.shortAnswer": "<PERSON><PERSON> svar", "app.components.formBuilder.showResponseToUsersToggleLabel": "Vis respons til brukerne", "app.components.formBuilder.singleChoice": "Enkelt valg", "app.components.formBuilder.staleDataErrorMessage2": "Det har oppst<PERSON><PERSON> et problem. Dette inndataskjemaet har nylig blitt lagret et annet sted. Dette kan skyldes at du eller en annen bruker har det åpent for redigering i et annet nettleservindu. Oppdater siden for å få opp det nyeste skjemaet, og gjør deretter endringene på nytt.", "app.components.formBuilder.stronglyAgree": "<PERSON><PERSON> enig", "app.components.formBuilder.stronglyDisagree": "<PERSON><PERSON> uenig", "app.components.formBuilder.supportArticleLinkText": "denne siden", "app.components.formBuilder.tags": "Tagger", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "til", "app.components.formBuilder.unsavedChanges": "Du har endringer som ikke er lagret", "app.components.formBuilder.useCustomButton2": "Bruk egendefinert sideknapp", "app.components.formBuilder.veryBad": "<PERSON><PERSON><PERSON> d<PERSON>", "app.components.formBuilder.veryGood": "Veldig bra", "app.components.ideas.similarIdeas.engageHere": "<PERSON><PERSON><PERSON> deg her", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Ingen lignende innleveringer funnet.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Vi fant lignende underkastelser - å engasjere seg i dem kan bidra til å gjøre dem sterkere!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Lignende bidrag er allerede lagt ut:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Ser etter lignende bidrag ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON> enn en dag} one {En dag} other {# dager}} igjen", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  uker igjen", "app.components.screenReaderCurrency.AED": "De forente arabiske emiraters dirham", "app.components.screenReaderCurrency.AFN": "afghansk afghansk", "app.components.screenReaderCurrency.ALL": "albansk lek", "app.components.screenReaderCurrency.AMD": "Armensk dramatikk", "app.components.screenReaderCurrency.ANG": "Nederlandsk antillisk gylden", "app.components.screenReaderCurrency.AOA": "Angolansk Kwanza", "app.components.screenReaderCurrency.ARS": "Argentinsk peso", "app.components.screenReaderCurrency.AUD": "Australske dollar", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Aserbajdsjansk manat", "app.components.screenReaderCurrency.BAM": "Bosnia-Hercegovina Convertible Mark", "app.components.screenReaderCurrency.BBD": "Barbadiske dollar", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgarsk Lev", "app.components.screenReaderCurrency.BHD": "Bahrainsk dinar", "app.components.screenReaderCurrency.BIF": "Burundiske franc", "app.components.screenReaderCurrency.BMD": "Bermudianske dollar", "app.components.screenReaderCurrency.BND": "Brunei-dollar", "app.components.screenReaderCurrency.BOB": "Boliviansk Boliviano", "app.components.screenReaderCurrency.BOV": "Boliviansk mvdol", "app.components.screenReaderCurrency.BRL": "Brasiliansk real", "app.components.screenReaderCurrency.BSD": "Bahamas-dollar", "app.components.screenReaderCurrency.BTN": "Bhutanesisk Ngultrum", "app.components.screenReaderCurrency.BWP": "Botswansk pula", "app.components.screenReaderCurrency.BYR": "Hviterussisk rubel", "app.components.screenReaderCurrency.BZD": "Belize-dollar", "app.components.screenReaderCurrency.CAD": "Kanadiske dollar", "app.components.screenReaderCurrency.CDF": "Kongolesisk franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Sveitsiske franc", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilensk regningsenhet (UF)", "app.components.screenReaderCurrency.CLP": "Chilensk peso", "app.components.screenReaderCurrency.CNY": "Kinesiske yuan", "app.components.screenReaderCurrency.COP": "Colombiansk peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Ricas Colón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Cubansk konvertibel peso", "app.components.screenReaderCurrency.CUP": "Cubansk peso", "app.components.screenReaderCurrency.CVE": "Kappverdisk escudo", "app.components.screenReaderCurrency.CZK": "Tsjekkisk koruna", "app.components.screenReaderCurrency.DJF": "Djiboutiske franc", "app.components.screenReaderCurrency.DKK": "Danske kroner", "app.components.screenReaderCurrency.DOP": "Dominikansk peso", "app.components.screenReaderCurrency.DZD": "Algerisk dinar", "app.components.screenReaderCurrency.EGP": "Egyptisk pund", "app.components.screenReaderCurrency.ERN": "Eritreisk Nakfa", "app.components.screenReaderCurrency.ETB": "Etiopiske birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijianske dollar", "app.components.screenReaderCurrency.FKP": "Falklandsøyenes pund", "app.components.screenReaderCurrency.GBP": "Britisk pund", "app.components.screenReaderCurrency.GEL": "georgisk lari", "app.components.screenReaderCurrency.GHS": "Ghanesisk Cedi", "app.components.screenReaderCurrency.GIP": "Gibraltar pund", "app.components.screenReaderCurrency.GMD": "Gambisk dalasi", "app.components.screenReaderCurrency.GNF": "Guineisk franc", "app.components.screenReaderCurrency.GTQ": "Guatemalansk quetzal", "app.components.screenReaderCurrency.GYD": "Guyanske dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong-dollar", "app.components.screenReaderCurrency.HNL": "Honduransk lempira", "app.components.screenReaderCurrency.HRK": "Kroatisk Kuna", "app.components.screenReaderCurrency.HTG": "Haitisk gourde", "app.components.screenReaderCurrency.HUF": "Ungarsk forint", "app.components.screenReaderCurrency.IDR": "Indonesisk rupiah", "app.components.screenReaderCurrency.ILS": "<PERSON>s nye shekel", "app.components.screenReaderCurrency.INR": "Indisk rupi", "app.components.screenReaderCurrency.IQD": "Irakisk dinar", "app.components.screenReaderCurrency.IRR": "Iransk rial", "app.components.screenReaderCurrency.ISK": "Islandsk Króna", "app.components.screenReaderCurrency.JMD": "Jamaicanske dollar", "app.components.screenReaderCurrency.JOD": "Jordansk dinar", "app.components.screenReaderCurrency.JPY": "Japanske yen", "app.components.screenReaderCurrency.KES": "Kenyansk shilling", "app.components.screenReaderCurrency.KGS": "Kirgisisk Som", "app.components.screenReaderCurrency.KHR": "Kambodsjansk riel", "app.components.screenReaderCurrency.KMF": "Komorisk franc", "app.components.screenReaderCurrency.KPW": "Nordkoreanske won", "app.components.screenReaderCurrency.KRW": "Sør-koreansk won", "app.components.screenReaderCurrency.KWD": "Kuwaitisk dinar", "app.components.screenReaderCurrency.KYD": "Cayman Islands dollar", "app.components.screenReaderCurrency.KZT": "Kasakhstansk tenge", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Libanesisk pund", "app.components.screenReaderCurrency.LKR": "Sri Lankas rupi", "app.components.screenReaderCurrency.LRD": "Liberiske dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Litauisk litas", "app.components.screenReaderCurrency.LVL": "Latviske lats", "app.components.screenReaderCurrency.LYD": "Libyske dinarer", "app.components.screenReaderCurrency.MAD": "Marokkansk dirham", "app.components.screenReaderCurrency.MDL": "Moldovisk leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Makedonsk denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolsk Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauritansk ouguiya", "app.components.screenReaderCurrency.MUR": "Mauri<PERSON><PERSON> rupi", "app.components.screenReaderCurrency.MVR": "Maldivisk Rufiyaa", "app.components.screenReaderCurrency.MWK": "Malawiske Kwacha", "app.components.screenReaderCurrency.MXN": "Meksikansk peso", "app.components.screenReaderCurrency.MXV": "Meksikansk Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysiske ringgit", "app.components.screenReaderCurrency.MZN": "Mosambikisk metical", "app.components.screenReaderCurrency.NAD": "Namibiske dollar", "app.components.screenReaderCurrency.NGN": "Nigerianske Naira", "app.components.screenReaderCurrency.NIO": "Nicaraguanske Córdoba", "app.components.screenReaderCurrency.NOK": "Norske kroner", "app.components.screenReaderCurrency.NPR": "Nepalesisk rupi", "app.components.screenReaderCurrency.NZD": "New Zealandske dollar", "app.components.screenReaderCurrency.OMR": "Omansk rial", "app.components.screenReaderCurrency.PAB": "Panamansk Balboa", "app.components.screenReaderCurrency.PEN": "Peruansk sol", "app.components.screenReaderCurrency.PGK": "Papua Ny-Guinea Kina", "app.components.screenReaderCurrency.PHP": "Filippinsk peso", "app.components.screenReaderCurrency.PKR": "Pakistansk rupi", "app.components.screenReaderCurrency.PLN": "Polsk Złoty", "app.components.screenReaderCurrency.PYG": "Guaraní fra Paraguay", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "rumensk leu", "app.components.screenReaderCurrency.RSD": "Serbisk dinar", "app.components.screenReaderCurrency.RUB": "Russisk rubel", "app.components.screenReaderCurrency.RWF": "Rwandiske franc", "app.components.screenReaderCurrency.SAR": "Saudi-riyal", "app.components.screenReaderCurrency.SBD": "Salomonøyenes dollar", "app.components.screenReaderCurrency.SCR": "Seychellisk rupi", "app.components.screenReaderCurrency.SDG": "Sudanske pund", "app.components.screenReaderCurrency.SEK": "Svenske kroner", "app.components.screenReaderCurrency.SGD": "Singapore-dollar", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somalisk shilling", "app.components.screenReaderCurrency.SRD": "Surinamesisk dollar", "app.components.screenReaderCurrency.SSP": "Sør-sudanske pund", "app.components.screenReaderCurrency.STD": "São Tomé og Príncipe Do<PERSON>", "app.components.screenReaderCurrency.SYP": "Syrisk pund", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thailandske baht", "app.components.screenReaderCurrency.TJS": "Tadsjikistansk somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistansk manat", "app.components.screenReaderCurrency.TND": "Tunisisk dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongansk Paʻanga", "app.components.screenReaderCurrency.TRY": "Tyrkisk lira", "app.components.screenReaderCurrency.TTD": "Trinidad og Tobago-dollar", "app.components.screenReaderCurrency.TWD": "Nye Taiwan-dollar", "app.components.screenReaderCurrency.TZS": "Tanzaniansk shilling", "app.components.screenReaderCurrency.UAH": "Ukrainsk hryvnia", "app.components.screenReaderCurrency.UGX": "Ugandisk shilling", "app.components.screenReaderCurrency.USD": "Amerikanske dollar", "app.components.screenReaderCurrency.USN": "Amerikanske dollar (Neste dag)", "app.components.screenReaderCurrency.USS": "Amerikanske dollar (samme dag)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayansk peso", "app.components.screenReaderCurrency.UZS": "Usbekistansk Som", "app.components.screenReaderCurrency.VEF": "Venezuelanske Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamesisk Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoansk Tala", "app.components.screenReaderCurrency.XAF": "Sentralafrikanske CFA-franc", "app.components.screenReaderCurrency.XAG": "<PERSON><PERSON><PERSON><PERSON> (en troy unse)", "app.components.screenReaderCurrency.XAU": "Gull (en troy unse)", "app.components.screenReaderCurrency.XBA": "Europeisk sammensatt enhet (EURCO)", "app.components.screenReaderCurrency.XBB": "Den europeiske monetære enhet (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Europeisk regningsenhet 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Europeisk regningsenhet 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "Øst-Karibiske dollar", "app.components.screenReaderCurrency.XDR": "Spesielle trekkrettigheter", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Vestafrikanske CFA-franc", "app.components.screenReaderCurrency.XPD": "Palladium (én troy unse)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON><PERSON> (én troy unse)", "app.components.screenReaderCurrency.XTS": "<PERSON><PERSON> spesielt reservert for testformål", "app.components.screenReaderCurrency.XXX": "Ingen valuta", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON><PERSON><PERSON> rial", "app.components.screenReaderCurrency.ZAR": "Sørafrikanske rand", "app.components.screenReaderCurrency.ZMW": "Zambisk Kwacha", "app.components.screenReaderCurrency.amount": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.currency": "Valuta", "app.components.trendIndicator.lastQuarter2": "siste k<PERSON>", "app.containers.AccessibilityStatement.applicability": "<PERSON><PERSON> tilgjengelighetserklæringen gjelder for en {demoPlatformLink} som er representativ for dette nettstedet; den bruker samme kildekode og har samme funksjonalitet.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Metode for vurdering", "app.containers.AccessibilityStatement.assesmentText2022": "Tilgjengeligheten til dette nettstedet ble evaluert av en ekstern enhet som ikke var involvert i design- og utviklingsprosessen. Overensstemmelsen med de nevnte {demoPlatformLink} kan identifiseres på denne {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "du kan endre preferansene dine", "app.containers.AccessibilityStatement.changePreferencesText": "<PERSON><PERSON><PERSON> som helst, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Unntak fra samsvar", "app.containers.AccessibilityStatement.conformanceStatus": "Status for samsvar", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Vi bestreber oss på å gjøre innholdet vårt inkluderende for alle. I noen tilfeller kan det imidlertid finnes utilgjengelig innhold på plattformen, som beskrevet nedenfor:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo-nettsted", "app.containers.AccessibilityStatement.email": "E-post:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Innebygde undersøkelsesverktøy", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "De innebygde undersøkelsesverktøyene som er tilgjengelige for bruk på denne plattformen, er tredjeparts programvare og er kanskje ikke tilgjengelige.", "app.containers.AccessibilityStatement.exception_1": "Våre digitale engasjementsplattformer legger til rette for brukergenerert innhold som legges ut av enkeltpersoner og organisasjoner. Det er mulig at PDF-filer, bilder eller andre filtyper, inkludert multimedier, lastes opp til plattformen som vedlegg eller legges inn i tekstfelt av plattformbrukere. Det er ikke sikkert at disse dokumentene er fullt tilgjengelige.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Vi ønsker tilbakemeldinger om tilgjengeligheten på dette nettstedet. Ta kontakt med oss på en av følgende måter:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Tilbakemeldingsprosessen", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brussel, Belgia", "app.containers.AccessibilityStatement.headTitle": "Tilgjengelighetserklæring | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} har forpliktet seg til å tilby en plattform som er tilgjengelig for alle brukere, uavhengig av teknologi eller evner. Vi følger gjeldende standarder for tilgjengelighet i vårt kontinuerlige arbeid med å maksimere tilgjengeligheten og brukervennligheten til plattformene våre for alle brukere.", "app.containers.AccessibilityStatement.mapping": "Kartlegging", "app.containers.AccessibilityStatement.mapping_1": "Kartene på plattformen oppfyller delvis standarder for tilgjengelighet. Kartets utstrekning, zoom og UI-widgets kan styres ved hjelp av tastaturet når du viser kart. Administratorer kan også konfigurere stilen på kartlagene i backoffice, eller ved hjelp av Esri-integrasjonen, for å skape mer tilgjengelige fargepaletter og symbologi. Bruk av ulike linje- eller polygonstiler (f.eks. stiplede linjer) vil også bidra til å skille kartlagene fra hverandre der det er mulig, og selv om slik styling ikke kan konfigureres i plattformen vår på nåværende tidspunkt, kan den konfigureres hvis du bruker kart med Esri-integrasjonen.", "app.containers.AccessibilityStatement.mapping_2": "Kartene i plattformen er ikke fullt tilgjengelige, ettersom de ikke presenterer basiskart, kartlag eller trender i dataene på en hørbar måte for brukere som bruker skjermlesere. Fullt tilgjengelige kart må presentere kartlagene på en hørbar måte og beskrive relevante trender i dataene. I tillegg er det ikke mulig å tegne linjer og polygoner i kart i spørreundersøkelser, ettersom det ikke er mulig å tegne figurer ved hjelp av tastaturet. Alternative inndatametoder er ikke tilgjengelige på nåværende tidspunkt på grunn av teknisk kompleksitet.", "app.containers.AccessibilityStatement.mapping_3": "For å gjøre linje- og polygonkarttegninger mer tilgjengelige anbefaler vi å inkludere en introduksjon eller forklaring i spørsmålet eller sidebeskrivelsen om hva kartet viser og eventuelle relevante trender. I tillegg kan det stilles et kort eller langt tekstspørsmål, slik at respondentene kan beskrive svaret sitt i klartekst hvis det er nødvendig (i stedet for å klikke på kartet). Vi anbefaler også å inkludere kontaktinformasjon til prosjektlederen, slik at respondenter som ikke kan fylle ut et kartspørsm<PERSON>l, kan be om en alternativ metode for å svare på spørsmålet (f.eks. videomøte).", "app.containers.AccessibilityStatement.mapping_4": "For Ideation-prosjekter og -forslag er det mulig å vise inndata i en kartvisning, men denne er ikke tilgjengelig. For disse metodene finnes det imidlertid en alternativ listevisning av inndata, som er tilgjengelig.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Nettverkstedene våre har en direktesendt videostrømmingskomponent, som for øyeblikket ikke støtter undertekster.", "app.containers.AccessibilityStatement.pageDescription": "En erklæring om tilgjengeligheten til dette nettstedet", "app.containers.AccessibilityStatement.postalAddress": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.AccessibilityStatement.publicationDate": "Dato for publisering", "app.containers.AccessibilityStatement.publicationDate2024": "<PERSON><PERSON>gjengelighetserklæringen ble publisert 21. august 2024.", "app.containers.AccessibilityStatement.responsiveness": "Vi tar sikte på å svare på tilbakemeldinger innen 1-2 virkedager.", "app.containers.AccessibilityStatement.statusPageText": "statusside", "app.containers.AccessibilityStatement.technologiesIntro": "Tilgjengeligheten til dette nettstedet er avhengig av følgende teknologier for å fungere:", "app.containers.AccessibilityStatement.technologiesTitle": "Teknologier", "app.containers.AccessibilityStatement.title": "Tilgjengelighetserklæring", "app.containers.AccessibilityStatement.userGeneratedContent": "Brukergenerert innhold", "app.containers.AccessibilityStatement.workshops": "Verksteder", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Velg prosjekt", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "<PERSON><PERSON> du bruker innholdsbyggeren, kan du bruke mer avanserte layoutalternativer. For språk der det ikke finnes noe innhold i innholdsbyggeren, vises det vanlige prosjektbeskrivelsesinnholdet i stedet.", "app.containers.AdminPage.ProjectDescription.linkText": "Rediger beskrivelse i Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "Noe gikk galt da du lagret prosjektbeskrivelsen.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Bruk Content Builder til beskrivelse", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Ved å bruke Content Builder kan du bruke mer avanserte layoutalternativer.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON>is prosjekt", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON>lutt på <PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Opprett en smartgruppe", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Brukere som oppfyller alle de følgende vilkårene, blir automatisk lagt til i gruppen:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Oppgi minst én regel", "app.containers.AdminPage.Users.UsersGroup.rulesError": "<PERSON>en betingelser er ufullstendige", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Lagre gruppe", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Konfigurering av smartgrupper er ikke en del av din nåværende lisens. Ta kontakt med din GovSuccess Manager for å lære mer om dette.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Oppgi et gruppenavn", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verifisering er deaktivert for plattformen din, fjern verifiseringsregelen eller kontakt kundestøtte.", "app.containers.App.appMetaDescription": "Velkommen til den digitale medvirkningsplattformen til {orgName}.\nUtforsk prosjekter og engasjer deg i diskusjonene!", "app.containers.App.loading": "Laster...", "app.containers.App.metaTitle1": "Plattform for innbyggerengasjement | {orgName}", "app.containers.App.skipLinkText": "Gå til hovedinnhold", "app.containers.AreaTerms.areaTerm": "område", "app.containers.AreaTerms.areasTerm": "<PERSON><PERSON>r<PERSON><PERSON>", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Det finnes allerede en konto med denne e-postadressen. Du kan logge av, logge på med denne e-postadressen og bekrefte kontoen din på innstillingssiden.", "app.containers.Authentication.steps.AccessDenied.close": "Lukk", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "Du oppfyller ikke kravene for å delta i denne prosessen.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Gå tilbake til single sign-on-verifisering", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Vennligst skriv inn en token", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Har du allerede en konto? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Logg inn", "app.containers.CampaignsConsentForm.ally_categoryLabel": "E-poster i denne kategorien", "app.containers.CampaignsConsentForm.messageError": "Det oppstod en feil ved lagring av e-postinnstillingene dine.", "app.containers.CampaignsConsentForm.messageSuccess": "E-postprefer<PERSON><PERSON> dine er lagret.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "Hva slags e-postvarsler ønsker du å motta? ", "app.containers.CampaignsConsentForm.notificationsTitle": "<PERSON><PERSON><PERSON>", "app.containers.CampaignsConsentForm.submit": "Lagre", "app.containers.ChangeEmail.backToProfile": "Tilbake til profilinnstillinger", "app.containers.ChangeEmail.confirmationModalTitle": "Bekreft e-postadressen din", "app.containers.ChangeEmail.emailEmptyError": "Oppgi en e-postadresse", "app.containers.ChangeEmail.emailInvalidError": "Oppgi en e-postadresse i riktig format, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "Vennligst skriv inn en e-postadresse.", "app.containers.ChangeEmail.emailTaken": "Denne e-postadressen er allerede i bruk.", "app.containers.ChangeEmail.emailUpdateCancelled": "E-postoppdateringen er avlyst.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "Start prosessen på nytt for å oppdatere e-postadressen din.", "app.containers.ChangeEmail.helmetDescription": "<PERSON><PERSON> e-postsiden din", "app.containers.ChangeEmail.helmetTitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> din", "app.containers.ChangeEmail.newEmailLabel": "Ny e-post", "app.containers.ChangeEmail.submitButton": "Send inn", "app.containers.ChangeEmail.titleAddEmail": "Legg til e-postadressen din", "app.containers.ChangeEmail.titleChangeEmail": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> din", "app.containers.ChangeEmail.updateSuccessful": "E-postadressen din har blitt oppdatert.", "app.containers.ChangePassword.currentPasswordLabel": "Gjeldende passord", "app.containers.ChangePassword.currentPasswordRequired": "Skriv inn ditt nåværende passord", "app.containers.ChangePassword.goHome": "G<PERSON> til hjem", "app.containers.ChangePassword.helmetDescription": "<PERSON><PERSON> passord-siden din", "app.containers.ChangePassword.helmetTitle": "<PERSON><PERSON> passordet ditt", "app.containers.ChangePassword.newPasswordLabel": "<PERSON><PERSON><PERSON> passord", "app.containers.ChangePassword.newPasswordRequired": "Skriv inn det nye passordet ditt", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Oppgi et passord som er minst {minimumPasswordLength} tegn langt", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Passordet ditt har blitt oppdatert", "app.containers.ChangePassword.passwordEmptyError": "Skriv inn passordet ditt", "app.containers.ChangePassword.passwordsDontMatch": "Bekreft nytt passord", "app.containers.ChangePassword.titleAddPassword": "Legg til et passord", "app.containers.ChangePassword.titleChangePassword": "<PERSON><PERSON> passordet ditt", "app.containers.Comments.a11y_commentDeleted": "<PERSON><PERSON><PERSON><PERSON> slettet", "app.containers.Comments.a11y_commentPosted": "Kommentar postet", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {ingen likes} one {1 like} other {# likes}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON> som", "app.containers.Comments.addCommentError": "Noe gikk galt. Vennligst prøv igjen senere.", "app.containers.Comments.adminCommentDeletionCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.adminCommentDeletionConfirmButton": "<PERSON><PERSON> denne kommentaren", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> et svar...", "app.containers.Comments.commentCancelUpvote": "<PERSON><PERSON>", "app.containers.Comments.commentDeletedPlaceholder": "<PERSON>ne kommentaren er slettet.", "app.containers.Comments.commentDeletionCancelButton": "<PERSON><PERSON> kommentaren min", "app.containers.Comments.commentDeletionConfirmButton": "<PERSON><PERSON> min kommentar", "app.containers.Comments.commentLike": "Som", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "Sorter kommentarer etter", "app.containers.Comments.completeProfileLinkText": "<PERSON><PERSON><PERSON><PERSON> profilen din", "app.containers.Comments.completeProfileToComment": "Vennligst {completeRegistrationLink} for å kommentere.", "app.containers.Comments.confirmCommentDeletion": "Er du sikker på at du vil slette denne kommentaren? Det er ingen vei tilbake!", "app.containers.Comments.deleteComment": "<PERSON><PERSON>", "app.containers.Comments.deleteReasonDescriptionError": "Oppgi mer informasjon om årsaken din", "app.containers.Comments.deleteReasonError": "Oppgi en grunn", "app.containers.Comments.deleteReason_inappropriate": "Det er upassende eller støtende", "app.containers.Comments.deleteReason_irrelevant": "<PERSON>te er ikke <PERSON>", "app.containers.Comments.deleteReason_other": "<PERSON><PERSON>", "app.containers.Comments.editComment": "<PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "retningslinjene våre for fellesskapet", "app.containers.Comments.ideaCommentBodyPlaceholder": "<PERSON><PERSON><PERSON><PERSON> din kommentar her", "app.containers.Comments.internalCommentingNudgeMessage": "Å komme med interne kommentarer er ikke inkludert i din nåværende lisens. Ta kontakt med din GovSuccess Manager for å finne ut mer om dette.", "app.containers.Comments.internalConversation": "<PERSON>n sam<PERSON>", "app.containers.Comments.loadMoreComments": "Legg inn flere kommentarer", "app.containers.Comments.loadingComments": "Laster inn kommentarer...", "app.containers.Comments.loadingMoreComments": "Laster inn flere kommentarer...", "app.containers.Comments.notVisibleToUsersPlaceholder": "<PERSON>ne kommentaren er ikke synlig for vanlige brukere", "app.containers.Comments.postInternalComment": "Post intern kommentar", "app.containers.Comments.postPublicComment": "Legg ut offentlig kommentar", "app.containers.Comments.profanityError": "Oops! Det ser ut som om innlegget ditt inneholder noe språk som ikke oppfyller {guidelinesLink}. Vi prøver å gjøre dette til et trygt sted for alle. Vennligst rediger innlegget ditt og prøv igjen.", "app.containers.Comments.publicDiscussion": "<PERSON>entlig <PERSON>", "app.containers.Comments.publishComment": "Legg inn din kommentar", "app.containers.Comments.reportAsSpamModalTitle": "Hvorfor vil du rapportere dette som spam?", "app.containers.Comments.saveComment": "Lagre", "app.containers.Comments.signInLinkText": "logg inn", "app.containers.Comments.signInToComment": "Vennligst {signInLink} for å kommentere.", "app.containers.Comments.signUpLinkText": "Registrer deg", "app.containers.Comments.verifyIdentityLinkText": "Bekreft identiteten din", "app.containers.Comments.visibleToUsersPlaceholder": "<PERSON>ne kommentaren er synlig for vanlige brukere", "app.containers.Comments.visibleToUsersWarning": "<PERSON><PERSON><PERSON><PERSON> som legges ut her, vil være synlige for vanlige brukere.", "app.containers.ContentBuilder.PageTitle": "Prosjektbeskrivelse", "app.containers.CookiePolicy.advertisingContent": "Informasjonskapsler for reklame kan brukes til å tilpasse og måle effektiviteten som eksterne markedsføringskampanjer har på engasjementet med denne plattformen. Vi viser ingen reklame på denne plattformen, men du kan motta personlig tilpassede annonser basert på sidene du besøker.", "app.containers.CookiePolicy.advertisingTitle": "<PERSON><PERSON><PERSON>", "app.containers.CookiePolicy.analyticsContents": "Analyseinformasjonskapsler sporer bes<PERSON><PERSON><PERSON> atferd, for eksempel hvilke sider som besøkes og hvor lenge. De kan også samle inn tekniske data, inkludert nettleserinformasjon, omtrentlig plassering og IP-adresser. Vi bruker kun disse dataene internt for å fortsette å forbedre den generelle brukeropplevelsen og plattformens funksjon. Slike data kan også deles mellom Go Vocal og {orgName} for å vurdere og forbedre engasjementet i prosjekter på plattformen. Merk at dataene er anonyme og brukes på et aggregert nivå - de identifiserer deg ikke personlig. Det er imidlertid mulig at slik identifisering kan forekomme hvis disse dataene kombineres med andre datakilder.", "app.containers.CookiePolicy.analyticsTitle": "Informasjonskapsler for analyse", "app.containers.CookiePolicy.cookiePolicyDescription": "En detaljert forklaring av hvordan vi bruker informasjonskapsler på denne plattformen", "app.containers.CookiePolicy.cookiePolicyTitle": "Retningslinjer for informasjonskapsler", "app.containers.CookiePolicy.essentialContent": "Noen informasjonskapsler er avgjørende for at denne plattformen skal fungere som den skal. Disse viktige informasjonskapslene brukes først og fremst til å autentisere kontoen din når du besøker plattformen, og til å lagre ditt foretrukne språk.", "app.containers.CookiePolicy.essentialTitle": "Viktige informasjonskapsler", "app.containers.CookiePolicy.externalContent": "Noen av sidene våre kan vise innhold fra eksterne leverandører, f.eks. YouTube eller Typeform. Vi har ikke kontroll over disse tredjepartsinformasjonskapslene, og visning av innhold fra disse eksterne leverandørene kan også føre til at informasjonskapsler blir installert på enheten din.", "app.containers.CookiePolicy.externalTitle": "Eksterne informasjonskapsler", "app.containers.CookiePolicy.functionalContents": "Funksjonelle informasjonskapsler kan aktiveres for at besøkende skal kunne motta varsler om oppdateringer og få tilgang til supportkanaler direkte fra plattformen.", "app.containers.CookiePolicy.functionalTitle": "Funksjonelle informasjonskapsler", "app.containers.CookiePolicy.headCookiePolicyTitle": "Retningslinjer for informasjonskapsler | {orgName}", "app.containers.CookiePolicy.intro": "Informasjonskapsler er tekstfiler som lagres i nettleseren eller på harddisken på datamaskinen eller mobilenheten din når du besøker et nettsted, og som nettstedet kan henvise til ved senere besøk. Vi bruker informasjonskapsler for å forstå hvordan besøkende bruker denne plattformen, for å forbedre utformingen og opplevelsen, for å huske preferansene dine (for eksempel foretrukket språk) og for å støtte viktige funksjoner for registrerte brukere og plattformadministratorer.", "app.containers.CookiePolicy.manageCookiesDescription": "Du kan når som helst aktivere eller deaktivere analyse-, marked<PERSON><PERSON><PERSON><PERSON>- og funksjonskapsler i innstillingene dine for informasjonskapsler. Du kan også slette eksisterende informasjonskapsler manuelt eller automatisk via nettleseren din. Informasjonskapslene kan imidlertid plasseres på nytt etter ditt samtykke ved senere besøk på denne plattformen. Hvis du ikke sletter informasjonskapslene, lagres innstillingene dine for informasjonskapsler i 60 dager, og deretter blir du bedt om å gi ditt samtykke på nytt.", "app.containers.CookiePolicy.manageCookiesPreferences": "Gå til {manageCookiesPreferencesButtonText} for å se en fullstendig liste over tredjepartsintegrasjoner som brukes på denne plattformen, og for å administrere preferansene dine.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "innstillinger for informasjonskapsler", "app.containers.CookiePolicy.manageCookiesTitle": "Administrere informasjonskapslene dine", "app.containers.CookiePolicy.viewPreferencesButtonText": "Innstillinger for informasjonskapsler", "app.containers.CookiePolicy.viewPreferencesText": "Kategoriene for informasjonskapsler nedenfor gjelder kanskje ikke for alle besøkende eller plattformer; se {viewPreferencesButton} for en fullstendig liste over tredjepartsintegrasjoner som gjelder for deg.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Hva bruker vi informasjonskapsler til?", "app.containers.CustomPageShow.editPage": "Rediger side", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON> til<PERSON>", "app.containers.CustomPageShow.notFound": "Siden ble ikke funnet", "app.containers.DisabledAccount.bottomText": "Du kan logge på igjen fra {date}.", "app.containers.DisabledAccount.termsAndConditions": "vilkår og betingelser", "app.containers.DisabledAccount.text2": "Kontoen din på medvirkningsplattformen til {orgName} er midlertidig deaktivert på grunn av brudd på retningslinjene for fellesskapet. For mer informasjon om dette kan du gå til {TermsAndConditions}.", "app.containers.DisabledAccount.title": "<PERSON><PERSON><PERSON> din har blitt midler<PERSON>dig deaktivert", "app.containers.EventsShow.addToCalendar": "Legg til i kalenderen", "app.containers.EventsShow.editEvent": "<PERSON><PERSON> hendelse", "app.containers.EventsShow.emailSharingBody2": "Delta på dette arrangementet: {eventTitle}. Les mer på {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Dato og klokkeslett for arrangementet", "app.containers.EventsShow.eventFrom2": "Fra \"{projectTitle}\"", "app.containers.EventsShow.goBack": "<PERSON><PERSON> til<PERSON>", "app.containers.EventsShow.goToProject": "Gå til prosjektet", "app.containers.EventsShow.haveRegistered": "har registrert", "app.containers.EventsShow.icsError": "Feil ved nedlasting av ICS-filen", "app.containers.EventsShow.linkToOnlineEvent": "Lenke til nettbasert arrangement", "app.containers.EventsShow.locationIconAltText": "Beliggenhet", "app.containers.EventsShow.metaTitle": "Arrangement: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Nettmøte", "app.containers.EventsShow.onlineLinkIconAltText": "Lenke til nettmøte", "app.containers.EventsShow.registered": "registrert", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registrerte} one {1 registrert} other {# registrerte}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registranter", "app.containers.EventsShow.registrantsIconAltText": "Registranter", "app.containers.EventsShow.socialMediaSharingMessage": "Delta på dette arrangementet: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {~ # deltaker} other {# deltakere}}", "app.containers.EventsViewer.allTime": "<PERSON><PERSON> tiden", "app.containers.EventsViewer.date": "Da<PERSON>", "app.containers.EventsViewer.thisMonth2": "Kommende måned", "app.containers.EventsViewer.thisWeek2": "Kommende uke", "app.containers.EventsViewer.today": "I dag", "app.containers.IdeaButton.addAContribution": "Legg til et bidrag", "app.containers.IdeaButton.addAPetition": "Legg til en underskriftskampanje", "app.containers.IdeaButton.addAProject": "Legg til et prosjekt", "app.containers.IdeaButton.addAProposal": "Legg til et forslag", "app.containers.IdeaButton.addAQuestion": "Legg til et spørsmål", "app.containers.IdeaButton.addAnInitiative": "Legg til et initiativ", "app.containers.IdeaButton.addAnOption": "Legg til et innspill", "app.containers.IdeaButton.postingDisabled": "Vi tar for øyeblikket ikke imot nye innsendinger", "app.containers.IdeaButton.postingInNonActivePhases": "Nye innleveringer kan bare legges til i aktive faser.", "app.containers.IdeaButton.postingInactive": "Vi tar for øyeblikket ikke imot nye bidrag.", "app.containers.IdeaButton.postingLimitedMaxReached": "Du har allerede fullført denne under<PERSON><PERSON>. Takk for ditt svar!", "app.containers.IdeaButton.postingNoPermission": "Vi tar for øyeblikket ikke imot nye innsendinger", "app.containers.IdeaButton.postingNotYetPossible": "Vi tar ikke imot nye bidrag ennå.", "app.containers.IdeaButton.signInLinkText": "logg inn", "app.containers.IdeaButton.signUpLinkText": "Registrer deg", "app.containers.IdeaButton.submitAnIssue": "Send inn en kommentar", "app.containers.IdeaButton.submitYourIdea": "Send inn ideen din", "app.containers.IdeaButton.takeTheSurvey": "Ta del i <PERSON><PERSON><PERSON>", "app.containers.IdeaButton.verificationLinkText": "Bekreft identiteten din nå.", "app.containers.IdeaCard.readMore": "Les mer om dette", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {ingen kommentarer} one {1 kommentar} other {# kommentarer}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {ingen stemmer} one {1 stemme} other {# stemmer}} ut av {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Lukk filterpanelet", "app.containers.IdeaCards.a11y_totalItems": "Totalt antall stillinger: {ideasCount}", "app.containers.IdeaCards.all": "Alle", "app.containers.IdeaCards.allStatuses": "Alle statuser", "app.containers.IdeaCards.contributions": "Bidrag", "app.containers.IdeaCards.ideaTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.initiatives": "Initiativer", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.list": "Liste", "app.containers.IdeaCards.map": "Kart", "app.containers.IdeaCards.mostDiscussed": "Mest diskutert", "app.containers.IdeaCards.newest": "De siste", "app.containers.IdeaCards.noFilteredResults": "Ingen resultater funnet. Prøv et annet filter eller søkeord.", "app.containers.IdeaCards.numberResults": "Resultater ({postCount})", "app.containers.IdeaCards.oldest": "Eldst", "app.containers.IdeaCards.optionTerm": "Alternativer", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "<PERSON><PERSON>t stemmer", "app.containers.IdeaCards.projectFilterTitle": "Prosjekter", "app.containers.IdeaCards.projectTerm": "Prosjekter", "app.containers.IdeaCards.proposals": "Forslag", "app.containers.IdeaCards.questionTerm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.resetFilters": "Tilbakestill filtre", "app.containers.IdeaCards.showXResults": "Vis {ideasCount, plural, one {# resultat} other {# resultater}}", "app.containers.IdeaCards.sortTitle": "Sortering", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Tagger", "app.containers.IdeaCards.topicsTitle": "Tagger", "app.containers.IdeaCards.trending": "Trender", "app.containers.IdeaCards.tryDifferentFilters": "Ingen resultater funnet. Prøv et annet filter eller søkeord.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} ~ kommentarer} one {{ideasCount} ~ kommentar} other {{ideasCount} kommentarer}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} ~ bidrag} one {{ideasCount} ~ bidrag} other {{ideasCount} bidrag}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ~ ideer} one {{ideasCount} ~ idé} other {{ideasCount} ideer}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} ~ initiativ} one {{ideasCount} ~ initiativ} other {{ideasCount} initiativ}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} alternativer} one {{ideasCount} alternativ} other {{ideasCount} alternativer}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} begjæringer} one {{ideasCount} begjæring} other {{ideasCount} begjæringer}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} ~ prosjekter} one {{ideasCount} ~ prosjekt} other {{ideasCount} prosjekter}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} ~ forslag} one {{ideasCount} ~ forslag} other {{ideasCount} forslag}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} ~ spø<PERSON><PERSON><PERSON><PERSON>} one {{ideasCount} ~ spø<PERSON><PERSON><PERSON><PERSON>} other {{ideasCount} spø<PERSON><PERSON><PERSON><PERSON>}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# result} other {# results}}", "app.containers.IdeasEditPage.contributionFormTitle": "Rediger bidrag", "app.containers.IdeasEditPage.editedPostSave": "Lagre", "app.containers.IdeasEditPage.fileUploadError": "En eller flere filer kunne ikke lastes opp. Kontroller filstørrelsen og formatet, og prøv på nytt.", "app.containers.IdeasEditPage.formTitle": "Rediger idé", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Rediger innlegget ditt. Legg til ny og endre gammel informasjon.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Rediger {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Rediger initiativ", "app.containers.IdeasEditPage.issueFormTitle": "Rediger kommentar", "app.containers.IdeasEditPage.optionFormTitle": "Redigeringsalternativ", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON> begjæ<PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "Rediger prosjekt", "app.containers.IdeasEditPage.proposalFormTitle": "Rediger forslag", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON> s<PERSON><PERSON>", "app.containers.IdeasEditPage.save": "Lagre", "app.containers.IdeasEditPage.submitApiError": "Det oppstod et problem med å sende inn skjemaet. Vennligst se etter eventuelle feil og prøv igjen.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Alle innganger lagt ut", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Utforsk alle innspillene som er lagt ut på medvirkningsplattformen {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Innlegg | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "<PERSON>legg", "app.containers.IdeasIndexPage.loadMore": "Last inn mer...", "app.containers.IdeasIndexPage.loading": "Laster...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Som standard vil innsendingene dine være knyttet til profilen din, med mindre du velger dette alternativet.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Post anonymt", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Synlighet i profilen", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "<PERSON><PERSON> er for øyeblikket ikke åpen for svar. Vennligst gå tilbake til prosjektet for mer informasjon.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "<PERSON><PERSON><PERSON><PERSON> er ikke aktiv for øyeblikket.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Gå tilbake til prosjektet", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Du har allerede fullført denne <PERSON><PERSON><PERSON>.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Innsendt spørreundersøkelse", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Takk for svaret!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Bidragsbeskrivelsen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "Idéteksten må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "<PERSON><PERSON>len på bidraget må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "<PERSON><PERSON>len på bidraget må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Vennligst velg minst én medsponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Idébeskrivelsen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Idébeskrivelsen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Vennligst gi en beskrivelse", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "<PERSON><PERSON><PERSON> på ideen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "<PERSON><PERSON><PERSON> på ideen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Initiativbeskrivelsen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Initiativbeskrivelsen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "<PERSON>ts tittel må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "<PERSON>ts tittel må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Problembeskrivelsen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Problembeskrivelsen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> på utgaven må være mindre enn {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "<PERSON><PERSON><PERSON> på utgaven må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_number_required": "<PERSON>te feltet er obligatorisk, vennligst skriv inn et gyldig nummer", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Alternativbeskrivelsen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Alternativbeskrivelsen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Alternativets tittel må være mindre enn {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Alternativets tittel må inneholde mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Vennligst velg minst én tagg", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Beskrivelsen av begjæringen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Beskrivelsen av begjæringen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> på begjæringen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> på begjæringen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Prosjektbeskrivelsen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Prosjektbeskrivelsen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Prosjekttittelen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Prosjekttittelen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Forslagsbeskrivelsen må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Forslagsbeskrivelsen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Forslagets tittel må være på mindre enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> på forslaget må inneholde mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Vennligst skriv inn et tall", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Vennligst skriv inn et tall", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Spørsmålsbeskrivelsen må være mindre enn {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Spørsmålsbeskrivelsen må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "<PERSON><PERSON><PERSON> på spørsmålet må være mindre enn {limit} tegn lang", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "<PERSON><PERSON><PERSON> på spørsmålet må være på mer enn {limit} tegn", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Vennligst oppgi en tittel", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Bidragsbeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Bidragsbeskrivelsen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "<PERSON><PERSON>len på bidraget må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Tittelen på bidraget må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Idébeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Idébeskrivelsen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Vennligst oppgi en tittel", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "<PERSON><PERSON><PERSON> på ideen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "<PERSON><PERSON>len på ideen må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Du kan ha brukt ett eller flere ord som anses som banning av {guidelinesLink}. Vennligst endre teksten din for å fjerne eventuelle banningord.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Initiativbeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Initiativbeskrivelsen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "<PERSON>ts tittel må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "<PERSON><PERSON><PERSON> på initiativet må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Problembeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Problembeskrivelsen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "<PERSON><PERSON><PERSON> på utgaven må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "T<PERSON>len på utgaven må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Alternativbeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Alternativbeskrivelsen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "<PERSON>ts tittel må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Alternativets tittel må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Beskrivelsen av begjæringen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Beskrivelsen av begjæringen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "<PERSON><PERSON><PERSON> på begjæringen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "T<PERSON>len på begjæringen må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Prosjektbeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Prosjektbeskrivelsen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Prosjekttittelen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Prosjekttittelen må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Forslagsbeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Forslagsbeskrivelsen må være på minst 30 tegn", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "<PERSON><PERSON><PERSON> på forslaget må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "T<PERSON>len på forslaget må være på minst 10 tegn", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Vennligst gi en beskrivelse", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Spørsmålsbeskrivelsen må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Spørsmålsbeskrivelsen må være minst 30 tegn lang", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "<PERSON><PERSON><PERSON> på spørsmålet må være på mindre enn 80 tegn", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "T<PERSON>len på spørsmålet må være på minst 10 tegn", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "<PERSON>a, jeg vil dra", "app.containers.IdeasNewPage.contributionMetaTitle1": "Legg til nytt bidrag til prosjektet | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON>iger under<PERSON><PERSON>", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Legg ut et innlegg og bli med i samtalen på {orgName}.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Legg til ny idé i prosjektet | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Legg til nytt initiativ i prosjektet | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Legg til et nytt problem i prosjektet | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Er du sikker på at du vil dra?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Svarutkastene dine er lagret privat, og du kan gå tilbake for å fullføre dette senere.", "app.containers.IdeasNewPage.leaveSurvey": "<PERSON><PERSON>", "app.containers.IdeasNewPage.leaveSurveyText": "<PERSON><PERSON><PERSON> dine vil ikke bli lagret.", "app.containers.IdeasNewPage.optionMetaTitle1": "Legg til et nytt alternativ i prosjektet | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Legg til ny begjæring i prosjektet | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Legg til nytt prosjekt i prosjekt | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Legg til nytt forslag i prosjektet | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Legg til nytt spørsmål i prosjektet | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Godta invitasjon", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Invitasjon til medsponsorskap", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "<PERSON><PERSON><PERSON><PERSON>rer", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Du er invitert til å bli medsponsor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitasjon akseptert", "app.containers.IdeasShow.Cosponsorship.pending": "i påvente av", "app.containers.IdeasShow.MetaInformation.attachments": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "Av {userName} den {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Nåværende status", "app.containers.IdeasShow.MetaInformation.location": "Beliggenhet", "app.containers.IdeasShow.MetaInformation.postedBy": "Skrevet av", "app.containers.IdeasShow.MetaInformation.similar": "Lignende innganger", "app.containers.IdeasShow.MetaInformation.topics": "Tagger", "app.containers.IdeasShow.commentCTA": "Legg til en kommentar", "app.containers.IdeasShow.contributionEmailSharingBody": "<PERSON><PERSON><PERSON> dette bidraget '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "<PERSON><PERSON><PERSON> bidraget: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Takk for at du sendte inn ditt bidrag!", "app.containers.IdeasShow.contributionTwitterMessage": "<PERSON><PERSON><PERSON> bidraget: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "<PERSON><PERSON><PERSON> bidraget: {postTitle}", "app.containers.IdeasShow.currentStatus": "Nåværende status", "app.containers.IdeasShow.deletedUser": "<PERSON><PERSON><PERSON> for<PERSON>", "app.containers.IdeasShow.ideaEmailSharingBody": "<PERSON><PERSON><PERSON> min idé \"{ideaTitle}\" på {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "<PERSON><PERSON><PERSON> ideen min: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "<PERSON><PERSON><PERSON> denne ideen: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "<PERSON><PERSON><PERSON> denne ideen: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "<PERSON><PERSON><PERSON> denne kommentaren: {postTitle}", "app.containers.IdeasShow.imported": "Importert", "app.containers.IdeasShow.importedTooltip": "<PERSON><PERSON> {inputTerm} ble samlet inn offline og automatisk lastet opp til plattformen.", "app.containers.IdeasShow.initiativeEmailSharingBody": "<PERSON><PERSON><PERSON> dette <PERSON>t '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "<PERSON><PERSON><PERSON>t: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Takk for at du sendte inn initiativet ditt!", "app.containers.IdeasShow.initiativeTwitterMessage": "<PERSON><PERSON><PERSON>t: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON>t: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "<PERSON><PERSON><PERSON> denne kommentaren '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "<PERSON><PERSON><PERSON> denne kommentaren: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Takk for at du sendte inn kommentaren din!", "app.containers.IdeasShow.issueTwitterMessage": "<PERSON><PERSON><PERSON> denne kommentaren: {postTitle}", "app.containers.IdeasShow.metaTitle": "Input: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "<PERSON><PERSON><PERSON> dette alternativet '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "<PERSON><PERSON><PERSON> dette alternativet: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "<PERSON>t ditt har blitt lagt ut!", "app.containers.IdeasShow.optionTwitterMessage": "<PERSON><PERSON><PERSON> dette alternativet: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "<PERSON><PERSON><PERSON> dette alternativet: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "<PERSON><PERSON><PERSON> oppropet{ideaTitle}på {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON> denne underskriftskampanjen: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Takk for at du sendte inn underskriftskampanjen din!", "app.containers.IdeasShow.petitionTwitterMessage": "<PERSON><PERSON><PERSON> denne underskriftskampanjen: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON> denne underskriftskampanjen: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "<PERSON><PERSON><PERSON> dette prosjektet '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "<PERSON><PERSON><PERSON> dette prosjektet: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Takk for at du sendte inn prosjektet ditt!", "app.containers.IdeasShow.projectTwitterMessage": "<PERSON><PERSON><PERSON> dette prosjektet: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "<PERSON><PERSON><PERSON> dette prosjektet: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "<PERSON><PERSON><PERSON> dette forslaget '{ideaTitle}' på {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON>tte forslaget: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Takk for at du sendte inn forslaget ditt!", "app.containers.IdeasShow.proposalTwitterMessage": "<PERSON><PERSON><PERSON>tte forslaget: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "<PERSON><PERSON><PERSON>tte forslaget: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Det er tid igjen til å stemme:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} av {votingThreshold} nødvendige stemmer", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Avbryt avstemning", "app.containers.IdeasShow.proposals.VoteControl.days": "dager", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "våre retningslinjer", "app.containers.IdeasShow.proposals.VoteControl.hours": "timer", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status og avstemninger", "app.containers.IdeasShow.proposals.VoteControl.minutes": "<PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "<PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.vote": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.voted": "Stemte", "app.containers.IdeasShow.proposals.VoteControl.votedText": "Du vil bli informert når forslaget går til neste fase. {x, plural, =0 {Det er {xDays} igjen.} one {Det er {xDays} igjen.} other {Det er {xDays} igjen.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Din stemme har blitt sendt inn!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Dessverre kan du ikke stemme over dette forslaget. Les hvorfor på {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {mindre enn en dag} one {én dag} other {# dager}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {ingen stemmer} one {1 stemme} other {# stemmer}}", "app.containers.IdeasShow.questionEmailSharingBody": "Bli med i diskusjonen om dette spørsmålet '{postTitle}' på {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Bli med i diskusjonen: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ditt har blitt lagt ut!", "app.containers.IdeasShow.questionTwitterMessage": "Bli med i diskusjonen: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Bli med i diskusjonen: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Hvorfor vil du rapportere dette som spam?", "app.containers.IdeasShow.share": "Del", "app.containers.IdeasShow.sharingModalSubtitle": "<PERSON><PERSON> ut til flere og bli hørt.", "app.containers.IdeasShow.sharingModalTitle": "Takk for at du sendte inn ideen din!", "app.containers.Navbar.completeOnboarding": "<PERSON><PERSON><PERSON><PERSON> onboarding", "app.containers.Navbar.completeProfile": "Fullstendig profil", "app.containers.Navbar.confirmEmail2": "Bekreft e-post", "app.containers.Navbar.unverified": "<PERSON>kke verifi<PERSON>t", "app.containers.Navbar.verified": "Verifisert", "app.containers.NewAuthModal.beforeYouFollow": "<PERSON><PERSON><PERSON>ølge<PERSON>", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON> profilen din", "app.containers.NewAuthModal.confirmYourEmail": "Bekreft e-postadressen din", "app.containers.NewAuthModal.logIn": "Logg inn", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Gå gjennom vilkårene nedenfor for å fortsette.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Vennligst fyll ut profilen din.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Gå tilbake til påloggingsalternativene", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Har du ikke en konto? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Registrer deg", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "<PERSON>den må ha 4 siffer.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Fortsett med FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Ingen autentiseringsmetoder er aktivert på denne plattformen.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "Ved å fortsette godtar du å motta e-post fra denne plattformen. Du kan velge hvilke e-poster du ønsker å motta på siden \"Mine innstillinger\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "E-post", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Oppgi en e-postadresse i riktig format, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Oppgi en e-postadresse", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Skriv inn e-postadressen din for å fortsette.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Glemt passord?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Logg inn på kontoen din: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Vennligst skriv inn passordet ditt", "app.containers.NewAuthModal.steps.Password.password": "Passord", "app.containers.NewAuthModal.steps.Password.rememberMe": "Husk meg", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "<PERSON>kke velg hvis du bruker en offentlig datamaskin", "app.containers.NewAuthModal.steps.Success.allDone": "Alt er gjort", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Nå kan du fortsette å delta.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Identiteten din har blitt bekreftet. Du er nå et fullverdig medlem av fellesskapet på denne plattformen.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Du er nå bekreftet!", "app.containers.NewAuthModal.steps.close": "Lukk", "app.containers.NewAuthModal.steps.continue": "Fortsett", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Hva er du interessert i?", "app.containers.NewAuthModal.youCantParticipate": "Du kan ikke delta", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {ingen varsler som ikke er vist} one {1 varsel som ikke er vist} other {# varsler som ikke er vist}}", "app.containers.NotificationMenu.adminRightsReceived": "Du er nå administrator av plattformen", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Kommentaren din på \"{postTitle}\" har blitt slettet av en administrator fordi\n      {reasonCode, select, irrelevant {den er irrelevant} inappropriate {innholdet er upassende} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} aksepterte invitasjonen til medsponsorskap", "app.containers.NotificationMenu.deletedUser": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.error": "Kunne ikke laste inn varsler", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} kommentert internt på en inngang du har fått tildelt", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} kommentert internt på et innspill som du har kommentert internt", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} kommentert internt på et innspill i et prosjekt du administrerer", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} kommenterte internt på en ikke-tildelt inngang i et ikke-administrert prosjekt", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} kommenterte den interne kommentaren din", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} inviterte deg til å være medsponsor for et bidrag", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} inviterte deg til å være medsponsor for en idé", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} inviterte deg til å være medsponsor for et initiativ", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} inviterte deg til å være medsponsor for en sak", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} inviterte deg til å være medsponsor for et alternativ", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} inviterte deg til å være med på en underskriftskampanje", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} inviterte deg til å være medsponsor for et prosjekt", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} inviterte deg til å være medsponsor for et forslag", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} inviterte deg til å være medsponsor for et spørsmål", "app.containers.NotificationMenu.loadMore": "Last inn mer...", "app.containers.NotificationMenu.loading": "Laster inn varsler...", "app.containers.NotificationMenu.mentionInComment": "{name} nevnte deg i en kommentar", "app.containers.NotificationMenu.mentionInInternalComment": "{name} nevnte deg i en intern kommentar", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} nevnt deg i en offisiell oppdatering", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Du har ikke sendt inn undersøkelsen din", "app.containers.NotificationMenu.noNotifications": "Du har ingen varsler ennå", "app.containers.NotificationMenu.notificationsLabel": "<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} ga en offisiell oppdatering om et bidrag du følger", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} ga en offisiell oppdatering på en idé du følger", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} ga en offisiell oppdatering om et initiativ du følger", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} ga en offisiell oppdatering om en sak du følger", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} ga en offisiell oppdatering på et alternativ du følger", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} ga en offisiell oppdatering på en underskriftskampanje du følger", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} ga en offisiell oppdatering om et prosjekt du følger", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} ga en offisiell oppdatering om et forslag du følger", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} ga en offisiell oppdatering på et spørsm<PERSON><PERSON> du følger", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} ble tildelt deg", "app.containers.NotificationMenu.projectModerationRightsReceived": "Du er nå prosjektleder for {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} gikk inn i en ny fase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} vil gå inn i en ny fase på {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Et nytt prosjekt ble publisert", "app.containers.NotificationMenu.projectReviewRequest": "{name} bedt om godkjenning til å publisere prosjektet \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} godkjent \"{projectTitle}\" for publisering", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} Status har endret seg til {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} n<PERSON><PERSON>", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} takket ja til invitasjonen din", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} kommentert et bidrag som du følger", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} kommenterte en idé som du følger", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} kommenterte et initiativ som du følger", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} kommenterte en sak som du følger", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} kommenterte på et alternativ som du følger", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} kommentert en underskriftskampanje som du følger", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} kommentert et prosjekt som du følger", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} kommenterte et forslag som du følger", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} kommenterte et spørsmål som du følger", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} rapporterte \"{postTitle}\" som spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reagerte på din kommentar", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} rapporterte en kommentar til \"{postTitle}\" som spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "Du har ikke sendt inn dine stemmer", "app.containers.NotificationMenu.votingBasketSubmitted": "Du stemte vellykket", "app.containers.NotificationMenu.votingLastChance": "<PERSON>ste sjanse til å stemme på {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} avstemningsresultatene avslørt", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} tildelt {postTitle} til deg", "app.containers.PasswordRecovery.emailError": "Dette ser ikke ut som en gyldig e-post", "app.containers.PasswordRecovery.emailLabel": "E-post", "app.containers.PasswordRecovery.emailPlaceholder": "E-postad<PERSON><PERSON> min", "app.containers.PasswordRecovery.helmetDescription": "Tilbakestill passord-siden din", "app.containers.PasswordRecovery.helmetTitle": "Tilbakestill passordet ditt", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "<PERSON><PERSON> denne e-postadressen er registrert på plattformen, har du fått tilsendt en lenke for tilbakestilling av passord.", "app.containers.PasswordRecovery.resetPassword": "Send en lenke for tilbakestilling av passord", "app.containers.PasswordRecovery.submitError": "Vi fant ingen konto knyttet til denne e-postadressen. Du kan prøve å registrere deg i stedet.", "app.containers.PasswordRecovery.subtitle": "Hvor kan vi sende en lenke for å velge et nytt passord?", "app.containers.PasswordRecovery.title": "Tilbakestilling av passord", "app.containers.PasswordReset.helmetDescription": "Tilbakestill passord-siden din", "app.containers.PasswordReset.helmetTitle": "Tilbakestill passordet ditt", "app.containers.PasswordReset.login": "Logg inn", "app.containers.PasswordReset.passwordError": "Passordet må bestå av minst 8 tegn", "app.containers.PasswordReset.passwordLabel": "Passord", "app.containers.PasswordReset.passwordPlaceholder": "<PERSON><PERSON><PERSON> passord", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Passordet ditt har blitt oppdatert.", "app.containers.PasswordReset.pleaseLogInMessage": "Vennligst logg inn med ditt nye passord.", "app.containers.PasswordReset.requestNewPasswordReset": "Be om tilbakestilling av nytt passord", "app.containers.PasswordReset.submitError": "Noe gikk galt. Vennligst prøv igjen senere.", "app.containers.PasswordReset.title": "Tilbakestill passordet ditt", "app.containers.PasswordReset.updatePassword": "Bekreft nytt passord", "app.containers.ProjectFolderCards.allProjects": "Alle prosjekter", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} jobber for tiden med", "app.containers.ProjectFolderShowPage.editFolder": "Rediger mappe", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informasjon om dette prosjektet", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Les mer om dette", "app.containers.ProjectFolderShowPage.seeLess": "Se mindre", "app.containers.ProjectFolderShowPage.share": "Del", "app.containers.Projects.PollForm.document": "Dokument", "app.containers.Projects.PollForm.formCompleted": "Takk skal du ha! Vi har mottatt svaret ditt.", "app.containers.Projects.PollForm.maxOptions": "maks. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Du har allerede tatt denne avstemningen.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Denne avstemningen kan bare foretas når denne fasen er aktiv.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "Denne avstemningen er ikke aktivert for øyeblikket", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Det er for øyeblikket ikke mulig å ta denne avstemningen.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Avstemningen er ikke lenger tilgjengelig siden dette prosjektet ikke lenger er aktivt.", "app.containers.Projects.PollForm.sendAnswer": "Send", "app.containers.Projects.a11y_phase": "Fase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Oversikt over fasene", "app.containers.Projects.a11y_titleInputs": "Alle innspill som er sendt inn til dette prosjektet", "app.containers.Projects.a11y_titleInputsPhase": "Alle innspill som sendes inn i denne fasen", "app.containers.Projects.accessRights": "Tilgangsrettigheter", "app.containers.Projects.addedToBasket": "Lagt til i handlekurven", "app.containers.Projects.allocateBudget": "Fordel budsjettet ditt", "app.containers.Projects.archived": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.basketSubmitted": "<PERSON>rven er sendt inn!", "app.containers.Projects.contributions": "Bidrag", "app.containers.Projects.createANewPhase": "<PERSON><PERSON><PERSON><PERSON> en ny fase", "app.containers.Projects.currentPhase": "Nåværende fase", "app.containers.Projects.document": "Dokument", "app.containers.Projects.editProject": "Rediger prosjekt", "app.containers.Projects.emailSharingBody": "Hva synes du om dette initiativet? Stem på det og del diskusjonen på {initiativeUrl} for å gjøre din stemme hørt!", "app.containers.Projects.emailSharingSubject": "<PERSON><PERSON><PERSON> initiativet mitt: {initiativeTitle}.", "app.containers.Projects.endedOn": "Avs<PERSON><PERSON><PERSON> på {date}", "app.containers.Projects.events": "Arrangementer", "app.containers.Projects.header": "Prosjekter", "app.containers.Projects.ideas": "<PERSON><PERSON><PERSON>", "app.containers.Projects.information": "Informasjon", "app.containers.Projects.initiatives": "Initiativer", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Gå gjennom dokumentet", "app.containers.Projects.invisibleTitlePhaseAbout": "<PERSON>m denne fasen", "app.containers.Projects.invisibleTitlePoll": "Ta avstemningen", "app.containers.Projects.invisibleTitleSurvey": "Ta del i <PERSON><PERSON><PERSON>", "app.containers.Projects.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.liveDataMessage": "Du ser på data i sanntid. Deltakerantallet oppdateres kontinuerlig for administratorer. Vær oppmerksom på at vanlige brukere ser mellomlagrede data, noe som kan føre til små forskjeller i tallene.", "app.containers.Projects.location": "Beliggenhet:", "app.containers.Projects.manageBasket": "Administrer kurv", "app.containers.Projects.meetMinBudgetRequirement": "Oppnå minimumsbudsjettet for å sende inn kurven.", "app.containers.Projects.meetMinSelectionRequirement": "Oppnå minimumsbudsjettet for å sende inn kurven.", "app.containers.Projects.metaTitle1": "Prosjekt: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Nødvendig minimumsbudsjett", "app.containers.Projects.myBasket": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navPoll": "Meningsmåling", "app.containers.Projects.navSurvey": "Spørreundersøkelse", "app.containers.Projects.newPhase": "Ny fase", "app.containers.Projects.nextPhase": "Neste fase", "app.containers.Projects.noEndDate": "Ingen sluttdato", "app.containers.Projects.noItems": "Du har ikke valgt noen varer ennå", "app.containers.Projects.noPastEvents": "Ingen tidligere arrangementer å vise", "app.containers.Projects.noPhaseSelected": "Ingen fase valgt", "app.containers.Projects.noUpcomingOrOngoingEvents": "Ingen kommende eller pågående arrangementer er for øyeblikket planlagt.", "app.containers.Projects.offlineVotersTooltip": "<PERSON>te tallet gjenspeiler ikke eventuelle offline-velgertellinger.", "app.containers.Projects.options": "Alternativer", "app.containers.Projects.participants": "Deltakere", "app.containers.Projects.participantsTooltip4": "<PERSON>te tallet gjenspeiler også anonyme besvarelser. Anonyme besvarelser er mulig hvis undersøkelsene er åpne for alle (se {accessRightsLink} under fanen for dette prosjektet).", "app.containers.Projects.pastEvents": "Tidligere arrangementer", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "<PERSON><PERSON><PERSON>", "app.containers.Projects.previousPhase": "<PERSON><PERSON><PERSON> fase", "app.containers.Projects.project": "Prosjekt", "app.containers.Projects.projectTwitterMessage": "<PERSON><PERSON><PERSON><PERSON> din stemme hørt! Delta på {projectName} | {orgName}", "app.containers.Projects.projects": "Prosjekter", "app.containers.Projects.proposals": "Forslag", "app.containers.Projects.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.readLess": "<PERSON> mindre", "app.containers.Projects.readMore": "Les mer om dette", "app.containers.Projects.removeItem": "<PERSON><PERSON><PERSON> elementet", "app.containers.Projects.requiredSelection": "Nød<PERSON><PERSON><PERSON> utvalg", "app.containers.Projects.reviewDocument": "Gå gjennom dokumentet", "app.containers.Projects.seeTheContributions": "Se bidragene", "app.containers.Projects.seeTheIdeas": "Se ideene", "app.containers.Projects.seeTheInitiatives": "Se initiativene", "app.containers.Projects.seeTheIssues": "Se kommentarene", "app.containers.Projects.seeTheOptions": "Se alternativene", "app.containers.Projects.seeThePetitions": "<PERSON> <PERSON>j<PERSON>", "app.containers.Projects.seeTheProjects": "Se prosjektene", "app.containers.Projects.seeTheProposals": "Se forslagene", "app.containers.Projects.seeTheQuestions": "Se spørsmå<PERSON>", "app.containers.Projects.seeUpcomingEvents": "Se kommende arrangementer", "app.containers.Projects.share": "Del", "app.containers.Projects.shareThisProject": "Del dette prosjektet", "app.containers.Projects.submitMyBasket": "Send inn kurv", "app.containers.Projects.survey": "Spørreundersøkelse", "app.containers.Projects.takeThePoll": "Ta avstemningen", "app.containers.Projects.takeTheSurvey": "Ta del i <PERSON><PERSON><PERSON>", "app.containers.Projects.timeline": "Tidslinje", "app.containers.Projects.upcomingAndOngoingEvents": "Kommende og pågående arrangementer", "app.containers.Projects.upcomingEvents": "Kommende arrangementer", "app.containers.Projects.whatsAppMessage": "{projectName} | fra medvirkningsplattformen til {orgName}", "app.containers.Projects.yourBudget": "Totalt budsjett", "app.containers.ProjectsIndexPage.metaDescription": "Utforsk alle pågående prosjekter på {orgName} for å forstå hvordan du kan delta.\n Kom og diskuter lokale prosjekter som betyr mest for deg.", "app.containers.ProjectsIndexPage.metaTitle1": "Prosjekter | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Prosjekter", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "<PERSON>g ønsker å delta", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Vennligst {signInLink} eller {signUpLink} først for å melde deg som frivillig til denne aktiviteten", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Det er for øyeblikket ikke mulig å delta på denne aktiviteten.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "logg inn", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "Registrer deg", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Jeg trekker tilbake mitt tilbud om å melde meg frivillig", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {ingen deltakere} one {# deltaker} other {# deltakere}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Advarsel: Den innebygde undersø<PERSON>sen kan ha tilgjengelighetsproblemer for brukere av skjermlesere. Hvis du opplever utfordringer, kan du kontakte plattformadministratoren for å få tilsendt en lenke til undersø<PERSON>sen fra den opprinnelige plattformen. Alternativt kan du be om andre måter å fylle ut unders<PERSON><PERSON>sen på.", "app.containers.ProjectsShowPage.process.survey.survey": "Spørreundersøkelse", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "For å vite om du kan delta i denne under<PERSON><PERSON>, må du først gå inn på plattformen {logInLink} .", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "<PERSON><PERSON><PERSON> kan bare gjennomføres når denne fasen i tidslinjen er aktiv.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Vennligst {completeRegistrationLink} for å svare på under<PERSON><PERSON><PERSON>.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON><PERSON><PERSON><PERSON> er ikke aktivert for øyeblikket", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "For å delta i denne unders<PERSON><PERSON>sen må du bekrefte identiteten din. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "<PERSON><PERSON><PERSON><PERSON><PERSON> er ikke lenger tilgjengelig, siden dette prosjektet ikke lenger er aktivt.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "fullstendig registrering", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "logg inn", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "Registrer deg", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Bekreft kontoen din nå.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Bare visse brukere kan se gjennom dette dokumentet. Vennligst {signUpLink} eller {logInLink} først.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Dette dokumentet kan bare gjennomgås når denne fasen er aktiv.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Vennligst {completeRegistrationLink} for å se gjennom dokumentet.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Du har dessverre ikke rett til å se gjennom dette dokumentet.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Gjennomgang av dette dokumentet krever verifisering av kontoen din. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Dokumentet er ikke lenger tilgjengelig, siden dette prosjektet ikke lenger er aktivt.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(inkl. 1 frakoblet)} other {(inkl. # frakoblet)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 valg} other {# valg}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Prosentandelen av deltakerne som valgte dette alternativet.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Prosentandelen av det totale antallet stemmer dette alternativet fikk.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Kostnad:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Vis mer", "app.containers.ReactionControl.a11y_likesDislikes": "Totalt antall likes: {likesCount}, totalt misliker: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Du har kansellert din motvilje mot denne inngangen.", "app.containers.ReactionControl.cancelLikeSuccess": "Du har kansellert din like for denne inngangen.", "app.containers.ReactionControl.dislikeSuccess": "Du mislikte dette innspillet med suksess.", "app.containers.ReactionControl.likeSuccess": "Du likte dette innspillet godt.", "app.containers.ReactionControl.reactionErrorSubTitle": "På grunn av en feil kunne ikke reaksjonen din registreres. Vennligst prøv igjen om noen minutter.", "app.containers.ReactionControl.reactionSuccessTitle": "Reaksjonen din ble vellykket registrert!", "app.containers.ReactionControl.vote": "<PERSON><PERSON><PERSON>", "app.containers.ReactionControl.voted": "Stemte", "app.containers.SearchInput.a11y_cancelledPostingComment": "Kansellert posting av kommentar.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} kommentarer er lastet inn.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {~ # hendelsene er lastet inn} one {# hendelsen er lastet inn} other {# hendelsene er lastet inn}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# resultatene er lastet inn} one {# resultatet er lastet inn} other {# resultatene er lastet inn}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# søkeresultatene er lastet inn} one {# søkeresultatet er lastet inn} other {# søkeresultatene er lastet inn}}.", "app.containers.SearchInput.removeSearchTerm": "<PERSON><PERSON><PERSON> sø<PERSON>", "app.containers.SearchInput.searchAriaLabel": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchLabel": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchPlaceholder": "<PERSON><PERSON><PERSON>", "app.containers.SearchInput.searchTerm": "Søkeord: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect er den franske statens løsning for å sikre og forenkle påmeldingen til mer enn 700 nettbaserte tjenester.", "app.containers.SignIn.or": "Eller", "app.containers.SignIn.signInError": "Den oppgitte informasjonen er ikke korrekt. Klikk på \"Glemt passord?\" for å tilbakestille passordet ditt.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Bruk FranceConnect til å logge inn, registrere deg eller bekrefte kontoen din.", "app.containers.SignIn.whatIsFranceConnect": "Hva er France Connect?", "app.containers.SignUp.adminOptions2": "For administratorer og prosjektledere", "app.containers.SignUp.backToSignUpOptions": "Gå tilbake til registreringsalternativene", "app.containers.SignUp.continue": "Fortsett", "app.containers.SignUp.emailConsent": "Ved å registrere deg godtar du å motta e-post fra denne plattformen. Du kan velge hvilke e-poster du ønsker å motta på siden \"Mine innstillinger\".", "app.containers.SignUp.emptyFirstNameError": "Skriv inn fornavnet ditt", "app.containers.SignUp.emptyLastNameError": "Skriv inn etternavnet ditt", "app.containers.SignUp.firstNamesLabel": "Fornavn", "app.containers.SignUp.goToLogIn": "Har du allerede en konto? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "<PERSON>g har lest og godtar {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "<PERSON>g har lest og godtar {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON> at opplysningene vil bli brukt på mitgestalten.wien.gv.at. Du finner mer informasjon på {link}.", "app.containers.SignUp.invitationErrorText": "Invitasjonen din har utløpt eller har allerede blitt brukt. Hvis du allerede har brukt invitasjonskoblingen til å opprette en konto, kan du prøve å logge på. <PERSON><PERSON> i<PERSON>, må du registrere deg for å opprette en ny konto.", "app.containers.SignUp.lastNameLabel": "Etternavn", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "<PERSON><PERSON><PERSON><PERSON> fokusområdene dine for å bli varslet om dem:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "<PERSON><PERSON><PERSON><PERSON>e dine for å bli varslet om dem:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "<PERSON>g<PERSON> preferanser", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "<PERSON><PERSON> over inntil videre", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Godta personvernerklæringen vår for å fortsette", "app.containers.SignUp.signUp2": "Registrer deg", "app.containers.SignUp.skip": "<PERSON><PERSON> over dette trinnet", "app.containers.SignUp.tacError": "Godta våre vilkår og betingelser for å fortsette", "app.containers.SignUp.thePrivacyPolicy": "personvernerklæringen", "app.containers.SignUp.theTermsAndConditions": "vilkårene og betingelsene", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {Det ser ut til at du prøvde å registrere deg før uten å fullføre prosessen. Klikk Logg på i stedet ved å bruke legitimasjonen som ble valgt under forrige for<PERSON>ø<PERSON>.} other {Noe gikk galt. Prøv igjen senere.}}", "app.containers.SignUp.viennaConsentEmail": "E-postadresse", "app.containers.SignUp.viennaConsentFirstName": "Fornavn", "app.containers.SignUp.viennaConsentFooter": "Du kan endre profilinformasjonen din etter at du har logget inn. Hvis du allerede har en konto med samme e-postadresse på mitgestalten.wien.gv.at, vil den bli koblet til din nåværende konto.", "app.containers.SignUp.viennaConsentHeader": "Følgende data vil bli overført:", "app.containers.SignUp.viennaConsentLastName": "Etternavn", "app.containers.SignUp.viennaConsentUserName": "Brukernavn", "app.containers.SignUp.viennaDataProtection": "vienna personvernerklæring", "app.containers.SiteMap.contributions": "Bidrag", "app.containers.SiteMap.cookiePolicyLinkTitle": "Informasjonskapsler", "app.containers.SiteMap.issues": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.options": "Alternativer", "app.containers.SiteMap.projects": "Prosjekter", "app.containers.SiteMap.questions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SpamReport.buttonSave": "Rapport", "app.containers.SpamReport.buttonSuccess": "<PERSON><PERSON><PERSON>", "app.containers.SpamReport.inappropriate": "Det er upassende eller støtende", "app.containers.SpamReport.messageError": "Det oppstod en feil ved innsending av skjemaet, vennligst prøv på nytt.", "app.containers.SpamReport.messageSuccess": "<PERSON><PERSON><PERSON> din har blitt sendt", "app.containers.SpamReport.other": "<PERSON><PERSON>", "app.containers.SpamReport.otherReasonPlaceholder": "Beskrivelse", "app.containers.SpamReport.wrong_content": "<PERSON>te er ikke <PERSON>", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "<PERSON><PERSON><PERSON> profi<PERSON>", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Dine stemmer på forslag som fortsatt er åpne for avstemning, vil bli slettet. Stemmer på forslag der avstemningsperioden er avsluttet, vil ikke bli slettet.", "app.containers.UsersEditPage.addPassword": "Legg til passord", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Å delta i prosjekter som krever verifisering.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Bekreft identiteten din", "app.containers.UsersEditPage.bio": "Om deg", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "Du kan ikke redigere dette feltet fordi det inneholder verifisert informasjon.", "app.containers.UsersEditPage.buttonSuccessLabel": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Endre e-post", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON>", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Klikk her for å oppdatere bekreftelsen din.", "app.containers.UsersEditPage.conditionsLinkText": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.contactUs": "Enda en grunn til å dra? {feedbackLink} og kanskje vi kan hjelpe.", "app.containers.UsersEditPage.deleteAccountSubtext": "Vi er lei for at du må gå.", "app.containers.UsersEditPage.deleteMyAccount": "Slett kontoen min", "app.containers.UsersEditPage.deleteYourAccount": "Slett kontoen din", "app.containers.UsersEditPage.deletionSection": "Slett kontoen din", "app.containers.UsersEditPage.deletionSubtitle": "Denne handlingen kan ikke angres. Innholdet du har publisert på plattformen vil bli anonymisert. Hvis du ønsker å slette alt innholdet ditt, kan du kontakte oss på <EMAIL>.", "app.containers.UsersEditPage.email": "E-post", "app.containers.UsersEditPage.emailEmptyError": "Oppgi en e-postadresse", "app.containers.UsersEditPage.emailInvalidError": "Oppgi en e-postadresse i riktig format, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Gi oss beskjed", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "Fornavn", "app.containers.UsersEditPage.firstNamesEmptyError": "Oppgi et fornavn", "app.containers.UsersEditPage.h1": "Kontoinformasjonen din", "app.containers.UsersEditPage.h1sub": "Rediger kontoinformasjonen din", "app.containers.UsersEditPage.image": "Avatarbilde", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Klikk for å velge et profilbilde (maks. 5 MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Alle innstillinger for profilen din", "app.containers.UsersEditPage.language": "Språk", "app.containers.UsersEditPage.lastName": "Etternavn", "app.containers.UsersEditPage.lastNameEmptyError": "Oppgi et etternavn", "app.containers.UsersEditPage.loading": "Laster...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Du kan endre e-postadressen eller passordet ditt her.", "app.containers.UsersEditPage.loginCredentialsTitle": "Påloggingsinformasjon", "app.containers.UsersEditPage.messageError": "Vi klarte ikke å lagre profilen din. Prøv igjen senere <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Profilen din har blitt lagret.", "app.containers.UsersEditPage.metaDescription": "<PERSON>te er siden for profilinnstill<PERSON> på {firstName} {lastName} på den digitale medvirkningsplattformen {tenantName}. Her kan du bekrefte identiteten din, redigere kontoinformasjonen din, slette kontoen din og redigere e-post-preferansene dine.", "app.containers.UsersEditPage.metaTitle1": "Profilinnstillinger på {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "<PERSON><PERSON><PERSON> du klikker på denne knap<PERSON>, har vi ingen mulighet til å gjenopprette kontoen din.", "app.containers.UsersEditPage.noNameWarning2": "Navnet ditt vises for øyeblikket på plattformen som: \"{displayName}\" fordi du ikke har skrevet inn navnet ditt. Dette er et autogenerert navn. Hvis du ønsker å endre det, kan du skrive inn navnet ditt nedenfor.", "app.containers.UsersEditPage.notificationsSubTitle": "Hva slags e-postvarsler ønsker du å motta? ", "app.containers.UsersEditPage.notificationsTitle": "E-postvarsler", "app.containers.UsersEditPage.password": "Velg et nytt passord", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Oppgi et passord som er minst {minimumPasswordLength} tegn langt", "app.containers.UsersEditPage.passwordAddSection": "Legg til et passord", "app.containers.UsersEditPage.passwordAddSubtitle2": "Angi et passord og logg enkelt inn på plattformen, uten å måtte bekrefte e-postadressen din hver gang.", "app.containers.UsersEditPage.passwordChangeSection": "<PERSON><PERSON> passordet ditt", "app.containers.UsersEditPage.passwordChangeSubtitle": "Bekreft ditt nåværende passord og bytt til et nytt passord.", "app.containers.UsersEditPage.privacyReasons": "<PERSON><PERSON> du er bekymret for person<PERSON><PERSON> ditt, kan du lese {conditionsLink}.", "app.containers.UsersEditPage.processing": "Sender...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "Fornavn er påkrevd når du oppgir etternavn", "app.containers.UsersEditPage.reasonsToStayListTitle": "<PERSON><PERSON><PERSON> du <PERSON>å<PERSON>...", "app.containers.UsersEditPage.submit": "<PERSON><PERSON><PERSON> endringer", "app.containers.UsersEditPage.tooManyEmails": "Mottar du for mange e-poster? Du kan administrere e-postpreferansene dine i profilinnstillingene dine.", "app.containers.UsersEditPage.updateverification": "Har den offisielle informasjonen din endret seg? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON><PERSON> vil du at vi skal sende deg en e-post for å varsle deg?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Du kan delta i prosjekter som krever verifisering.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Du er bekreftet", "app.containers.UsersEditPage.verifyNow": "Bekreft nå", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Last ned svarene dine (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {ingen likes} one {1 like} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Innspill som denne kommentaren ble lagt ut som svar på:", "app.containers.UsersShowPage.areas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Kommentarer ({commentsCount})", "app.containers.UsersShowPage.editProfile": "<PERSON><PERSON> profilen min", "app.containers.UsersShowPage.emptyInfoText": "Du følger ikke noen av elementene i det angitte filteret ovenfor.", "app.containers.UsersShowPage.eventsWithCount": "Arrangementer ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Følgende ({followingCount})", "app.containers.UsersShowPage.inputs": "Innganger", "app.containers.UsersShowPage.invisibleTitlePostsList": "Alle innspill sendt inn av denne deltakeren", "app.containers.UsersShowPage.invisibleTitleUserComments": "Alle kommentarer postet av denne deltakeren", "app.containers.UsersShowPage.loadMore": "Last inn mer", "app.containers.UsersShowPage.loadMoreComments": "Legg inn flere kommentarer", "app.containers.UsersShowPage.loadingComments": "Laster inn kommentarer...", "app.containers.UsersShowPage.loadingEvents": "Laster inn arrangementer...", "app.containers.UsersShowPage.memberSince": "Medlem siden {date}", "app.containers.UsersShowPage.metaTitle1": "Profilside for {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Denne personen har ikke lagt inn noen kommentarer ennå.", "app.containers.UsersShowPage.noCommentsForYou": "Det er ingen kommentarer her ennå.", "app.containers.UsersShowPage.noEventsForUser": "Du har ikke deltatt på noen arrangementer ennå.", "app.containers.UsersShowPage.postsWithCount": "Innleveringer ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Prosjektmapper", "app.containers.UsersShowPage.projects": "Prosjekter", "app.containers.UsersShowPage.proposals": "Forslag", "app.containers.UsersShowPage.seePost": "Se innsending", "app.containers.UsersShowPage.surveyResponses": "Svar ({responses})", "app.containers.UsersShowPage.topics": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.tryAgain": "En feil har op<PERSON><PERSON><PERSON>, vennligst prøv igjen senere.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Dette er profilsiden til {firstName} {lastName} på den digitale medvirkningsplattformen {orgName}. Her finner du en oversikt over alle innspillene deres.", "app.containers.VoteControl.close": "Lukk", "app.containers.VoteControl.voteErrorTitle": "<PERSON>e gikk galt", "app.containers.admin.ContentBuilder.default": "standard", "app.containers.admin.ContentBuilder.imageTextCards": "Bilde- og tekstkort", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & trekkspill", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 kolonne", "app.containers.admin.ContentBuilder.projectDescription": "Prosjektbeskrivelse", "app.containers.app.navbar.admin": "Administrer plattform", "app.containers.app.navbar.allProjects": "Alle prosjekter", "app.containers.app.navbar.ariaLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.closeMobileNavMenu": "Lukk navigasjonsmenyen på mobilen", "app.containers.app.navbar.editProfile": "Mine innstillinger", "app.containers.app.navbar.fullMobileNavigation": "Full mobil", "app.containers.app.navbar.logIn": "Logg inn", "app.containers.app.navbar.logoImgAltText": "{orgName} Hjem", "app.containers.app.navbar.myProfile": "Min aktivitet", "app.containers.app.navbar.search": "<PERSON><PERSON><PERSON>", "app.containers.app.navbar.showFullMenu": "Vis hele menyen", "app.containers.app.navbar.signOut": "Logg av", "app.containers.eventspage.errorWhenFetchingEvents": "Det oppstod en feil under innlasting av arrangementer. Prøv å laste inn siden på nytt.", "app.containers.eventspage.events": "Arrangementer", "app.containers.eventspage.eventsPageDescription": "Vis alle arrangementer som er lagt ut på {orgName}sin plattform.", "app.containers.eventspage.eventsPageTitle1": "Arrangementer | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Prosjekter", "app.containers.eventspage.noPastEvents": "Ingen tidligere arrangementer å vise", "app.containers.eventspage.noUpcomingOrOngoingEvents": "Ingen kommende eller pågående arrangementer er for øyeblikket planlagt.", "app.containers.eventspage.pastEvents": "Tidligere arrangementer", "app.containers.eventspage.upcomingAndOngoingEvents": "Kommende og pågående arrangementer", "app.containers.footer.accessibility-statement": "Erklæring om tilgjengelighet", "app.containers.footer.ariaLabel": "Se<PERSON>nd<PERSON><PERSON>", "app.containers.footer.cookie-policy": "Bruk av informasjonskapsler", "app.containers.footer.cookieSettings": "Innstillinger for informasjonskapsler", "app.containers.footer.feedbackEmptyError": "Tilbakemeldingsfeltet kan ikke være tomt.", "app.containers.footer.poweredBy": "Drevet av", "app.containers.footer.privacy-policy": "Personvernerklæring", "app.containers.footer.siteMap": "Kart over nettstedet", "app.containers.footer.terms-and-conditions": "Vilkår og betingelser", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "<PERSON>a, jeg vil dra", "app.containers.ideaHeading.editForm": "<PERSON><PERSON> skjema", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Er du sikker på at du vil dra?", "app.containers.ideaHeading.leaveIdeaForm": "<PERSON><PERSON> igje<PERSON>", "app.containers.ideaHeading.leaveIdeaText": "<PERSON><PERSON><PERSON> dine vil ikke bli lagret.", "app.containers.landing.cityProjects": "Prosjekter", "app.containers.landing.completeProfile": "<PERSON><PERSON><PERSON><PERSON> profilen din", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON>, {firstName}. Det er på tide å fylle ut profilen din.", "app.containers.landing.createAccount": "Registrer deg", "app.containers.landing.defaultSignedInMessage": "{orgName} lytter til deg. Nå er det din tur til å bli hørt!", "app.containers.landing.doItLater": "<PERSON><PERSON> gjør det senere", "app.containers.landing.new": "ny", "app.containers.landing.subtitleCity": "Velkommen til medvirkningsplattformen til {orgName}", "app.containers.landing.titleCity": "La oss forme fremtiden til {orgName} sammen", "app.containers.landing.twitterMessage": "<PERSON><PERSON> på {ideaTitle} på", "app.containers.landing.upcomingEventsWidgetTitle": "Kommende og pågående arrangementer", "app.containers.landing.userDeletedSubtitle": "Du kan når som helst opprette en ny konto eller {contactLink} for å fortelle oss hva vi kan forbedre.", "app.containers.landing.userDeletedSubtitleLinkText": "send oss en melding", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "<PERSON><PERSON><PERSON> din er slettet.", "app.containers.landing.userDeletionFailed": "Det oppstod en feil ved sletting av kontoen din. Vi har blitt varslet om problemet og vil gjøre vårt beste for å løse det. Vennligst prøv igjen senere.", "app.containers.landing.verifyNow": "Bekreft nå", "app.containers.landing.verifyYourIdentity": "Bekreft identiteten din", "app.containers.landing.viewAllEventsText": "Se alle arrangementer", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Tilbake til mappen", "app.errors.after_end_at": "Startdatoen inntreffer etter sluttdatoen", "app.errors.avatar_carrierwave_download_error": "Kunne ikke laste ned avatarfilen.", "app.errors.avatar_carrierwave_integrity_error": "Avatarfilen er ikke av en tillatt type.", "app.errors.avatar_carrierwave_processing_error": "<PERSON><PERSON> ikke behandle avatar.", "app.errors.avatar_extension_blacklist_error": "Filtypen til avatarbildet er ikke tillatt. Tillatte filtyper er: jpg, jpeg, gif og png.", "app.errors.avatar_extension_whitelist_error": "Filtypen til avatarbildet er ikke tillatt. Tillatte filtyper er: jpg, jpeg, gif og png.", "app.errors.banner_cta_button_multiloc_blank": "Skriv inn en knapptekst.", "app.errors.banner_cta_button_url_blank": "Skriv inn en kobling.", "app.errors.banner_cta_button_url_url": "Skriv inn en gyldig lenke. Sørg for at lenken begynner med 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Skriv inn en knapptekst.", "app.errors.banner_cta_signed_in_url_blank": "Skriv inn en kobling.", "app.errors.banner_cta_signed_in_url_url": "Skriv inn en gyldig lenke. Sørg for at lenken begynner med 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Skriv inn en knapptekst.", "app.errors.banner_cta_signed_out_url_blank": "Skriv inn en kobling.", "app.errors.banner_cta_signed_out_url_url": "Skriv inn en gyldig lenke. Sørg for at lenken begynner med 'https://'.", "app.errors.base_includes_banned_words": "Du kan ha brukt ett eller flere ord som regnes som banning. Vennligst endre teksten din for å fjerne eventuelle banningord.", "app.errors.body_multiloc_includes_banned_words": "Beskrivelsen inneholder ord som anses som upassende.", "app.errors.bulk_import_idea_not_valid": "Den resulterende ideen er ikke gyldig: {value}.", "app.errors.bulk_import_image_url_not_valid": "Ingen bilder kunne lastes ned fra {value}. <PERSON><PERSON><PERSON><PERSON> at URL-adressen er gyldig og at den slutter med en filtype som .png eller .jpg. Dette problemet oppstår i raden med ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idea-lokasjon med manglende koordinat i {value}. Dette problemet oppstår i raden med ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idea-posisjon med en ikke-numerisk koordinat i {value}. Dette problemet oppstår i raden med ID {row}.", "app.errors.bulk_import_malformed_pdf": "Den opplastede PDF-filen ser ut til å være feilformet. Prøv å eksportere PDF-filen på nytt fra kilden, og last den deretter opp på nytt.", "app.errors.bulk_import_maximum_ideas_exceeded": "<PERSON><PERSON><PERSON><PERSON> antall ideer på {value} er overskredet.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Maksimalt antall {value} sider i en PDF-fil er overskredet.", "app.errors.bulk_import_not_enough_pdf_pages": "Den opplastede PDF-filen har ikke nok sider - den bør ha minst like mange sider som den nedlastede malen.", "app.errors.bulk_import_publication_date_invalid_format": "Idea med ugyldig publiseringsdatoformat \"{value}\". Vennligst bruk formatet \"DD-MM-YYYYY\".", "app.errors.cannot_contain_ideas": "Deltakelsesmetoden du har valgt, støtter ikke denne typen innlegg. Vennligst rediger valget ditt og prøv igjen.", "app.errors.cant_change_after_first_response": "Du kan ikke lenger endre dette, siden noen brukere allerede har svart", "app.errors.category_name_taken": "En kategori med dette navnet finnes allerede", "app.errors.confirmation_code_expired": "Koden er utløpt. Vennligst be om en ny kode.", "app.errors.confirmation_code_invalid": "Ugyldig bekreftelseskode. Vennligst sjekk e-posten din for riktig kode, eller prøv \"Send ny kode", "app.errors.confirmation_code_too_many_resets": "<PERSON> har sendt bekreftelseskoden for mange ganger. Kontakt oss for å få tilsendt en invitasjonskode i stedet.", "app.errors.confirmation_code_too_many_retries": "Du har prøvd for mange ganger. Be om en ny kode, eller prøv å endre e-postadressen din.", "app.errors.email_already_active": "E-postadressen {value} som finnes i rad {row} tilhører allerede en registrert deltaker", "app.errors.email_already_invited": "E-postadressen {value} som ble funnet i rad {row} var allerede invitert", "app.errors.email_blank": "Denne kan ikke være tom", "app.errors.email_domain_blacklisted": "Bruk et annet e-postdomene for å registrere deg.", "app.errors.email_invalid": "Vennligst bruk en gyldig e-postadresse.", "app.errors.email_taken": "Det finnes allerede en konto med denne e-postadressen. Du kan logge inn i stedet.", "app.errors.email_taken_by_invite": "{value} er allerede opptatt av en ventende invitasjon. Sjekk søppelpostmappen din eller kontakt {supportEmail} hvis du ikke finner den.", "app.errors.emails_duplicate": "En eller flere dupliserte verdier for e-postadressen {value} ble funnet i følgende rad(er): {rows}", "app.errors.extension_whitelist_error": "Formatet på filen du prøvde å laste opp, stø<PERSON> ikke.", "app.errors.file_extension_whitelist_error": "Formatet på filen du prøvde å laste opp, stø<PERSON> ikke.", "app.errors.first_name_blank": "Denne kan ikke være tom", "app.errors.generics.blank": "Denne kan ikke være tom.", "app.errors.generics.invalid": "<PERSON>te ser ikke ut som en gyldig verdi", "app.errors.generics.taken": "Denne e-postadressen finnes allerede. En annen konto er knyttet til den.", "app.errors.generics.unsupported_locales": "<PERSON><PERSON> feltet støtter ikke den gjeldende språkbruken.", "app.errors.group_ids_unauthorized_choice_moderator": "Som prosjektleder kan du bare sende e-post til personer som har tilgang til prosjektet/prosjektene dine", "app.errors.has_other_overlapping_phases": "Prosjekter kan ikke ha overlappende faser.", "app.errors.invalid_email": "E-postadressen {value} i rad {row} er ikke en gyldig e-postadresse", "app.errors.invalid_row": "En ukjent feil oppstod under forsøk på å behandle rad {row}", "app.errors.is_not_timeline_project": "Det nåværende prosjektet støtter ikke faser.", "app.errors.key_invalid": "Nøkkelen kan bare inneholde bokstaver, tall og understrek (_)", "app.errors.last_name_blank": "Denne kan ikke være tom", "app.errors.locale_blank": "Vennligst velg et språk", "app.errors.locale_inclusion": "Vennligst velg et språk som støttes", "app.errors.malformed_admin_value": "Administratorverdien {value} i raden {row} er ikke gyldig", "app.errors.malformed_groups_value": "Gruppen {value} i raden {row} er ikke en gyldig gruppe", "app.errors.max_invites_limit_exceeded1": "<PERSON><PERSON><PERSON> invitasjoner overstiger grensen på 1000.", "app.errors.maximum_attendees_greater_than1": "Maksimalt antall registrerte må være større enn 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Maksimalt antall registrerte må være større enn eller lik det nåværende antallet registrerte.", "app.errors.no_invites_specified": "Fant ingen e-postadresse.", "app.errors.no_recipients": "Kampanjen kan ikke sendes ut fordi det ikke finnes noen mottakere. Gruppen du sender til, er enten tom, eller så har ingen gitt samtykke til å motta e-post.", "app.errors.number_invalid": "Vennligst skriv inn et gyldig nummer.", "app.errors.password_blank": "Denne kan ikke være tom", "app.errors.password_invalid": "Vennligst sjekk ditt nåværende passord igjen.", "app.errors.password_too_short": "Passordet må bestå av minst 8 tegn", "app.errors.resending_code_failed": "Noe gikk galt da bekreftelseskoden ble sendt ut.", "app.errors.slug_taken": "Denne prosjekt-URL-en finnes allerede. Vennligst endre prosjektets slug til noe annet.", "app.errors.tag_name_taken": "En tagg med dette navnet finnes allerede", "app.errors.title_multiloc_blank": "T<PERSON>len kan ikke være tom.", "app.errors.title_multiloc_includes_banned_words": "<PERSON><PERSON><PERSON> inneholder ord som anses som upassende.", "app.errors.token_invalid": "<PERSON><PERSON><PERSON> for tilbakestilling av passord kan bare brukes én gang og er gyldige i én time etter at de er sendt. {passwordResetLink}.", "app.errors.too_common": "Dette passordet kan lett gjettes. Vennligst velg et sterkere passord.", "app.errors.too_long": "Vennligst velg et kortere passord (maks 72 tegn)", "app.errors.too_short": "Vennligst velg et passord med minst 8 tegn", "app.errors.uncaught_error": "En ukjent feil oppstod.", "app.errors.unknown_group": "Gruppen {value} som finnes i rad {row} er ikke en kjent gruppe", "app.errors.unknown_locale": "Språket {value} i rad {row} er ikke et konfigurert språk", "app.errors.unparseable_excel": "Den valgte Excel-filen kunne ikke behandles.", "app.errors.url": "Skriv inn en gyldig lenke. Forsikre deg om at lenken begynner med https://", "app.errors.verification_taken": "Verifiseringen kan ikke fullføres ettersom en annen konto har blitt verifisert med de samme opplysningene.", "app.errors.view_name_taken": "En visning med dette navnet finnes allerede", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Upassende innhold ble automatisk oppdaget i et innlegg eller en kommentar", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Logg inn med StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Registrer deg med StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Opprett en Stadt Wien-konto nå og bruk én innlogging for mange digitale tjenester i Wien.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "<PERSON>te feltet kan ikke være tomt.", "app.modules.id_cow.helpAltText": "Viser hvor du finner ID-serienummeret på et identitetskort", "app.modules.id_cow.invalidIdSerialError": "Ugyldig ID-serie", "app.modules.id_cow.invalidRunError": "Ugyldig RUN", "app.modules.id_cow.noMatchFormError": "Ingen match ble funnet.", "app.modules.id_cow.notEntitledFormError": "<PERSON><PERSON><PERSON> be<PERSON>.", "app.modules.id_cow.showCOWHelp": "<PERSON><PERSON> finner jeg <PERSON>-serien<PERSON><PERSON>t mitt?", "app.modules.id_cow.somethingWentWrongError": "Vi kan ikke verifisere deg fordi noe gikk galt", "app.modules.id_cow.submit": "Send inn", "app.modules.id_cow.takenFormError": "Allerede tatt.", "app.modules.id_cow.verifyCow": "Verifiser ved hjelp av COW", "app.modules.id_franceconnect.verificationButtonAltText": "Bekreft med FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "<PERSON>te feltet kan ikke være tomt.", "app.modules.id_gent_rrn.gentRrnHelp": "Personnummeret ditt står på baksiden av det digitale identitetskortet ditt", "app.modules.id_gent_rrn.invalidRrnError": "Ugyldig personnummer", "app.modules.id_gent_rrn.noMatchFormError": "Vi fant ikke informasjon om personnummeret ditt", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Vi kan ikke bekrefte deg fordi du bor utenfor Gent", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Vi kan ikke verifisere deg fordi du er yngre enn 14 år", "app.modules.id_gent_rrn.rrnLabel": "Personnummer", "app.modules.id_gent_rrn.rrnTooltip": "Vi ber om personnummeret ditt for å bekrefte om du er statsborger i Gent, eldre enn 14 år gammel.", "app.modules.id_gent_rrn.showGentRrnHelp": "<PERSON><PERSON> finner jeg <PERSON>-serien<PERSON><PERSON>t mitt?", "app.modules.id_gent_rrn.somethingWentWrongError": "Vi kan ikke verifisere deg fordi noe gikk galt", "app.modules.id_gent_rrn.submit": "Send inn", "app.modules.id_gent_rrn.takenFormError": "Personnummeret ditt har allerede blitt brukt til å verifisere en annen konto", "app.modules.id_gent_rrn.verifyGentRrn": "Verifiser ved hjelp av GentRrn", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "<PERSON>te feltet kan ikke være tomt.", "app.modules.id_id_card_lookup.helpAltText": "Forklaring av ID-kort", "app.modules.id_id_card_lookup.invalidCardIdError": "Denne ID-en er ikke gyldig.", "app.modules.id_id_card_lookup.noMatchFormError": "Ingen treff ble funnet.", "app.modules.id_id_card_lookup.showHelp": "<PERSON><PERSON> finner jeg <PERSON>-serien<PERSON><PERSON>t mitt?", "app.modules.id_id_card_lookup.somethingWentWrongError": "Vi kan ikke verifisere deg fordi noe gikk galt", "app.modules.id_id_card_lookup.submit": "Send inn", "app.modules.id_id_card_lookup.takenFormError": "Allerede tatt.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "<PERSON>te feltet kan ikke være tomt.", "app.modules.id_oostende_rrn.invalidRrnError": "Ugyldig personnummer", "app.modules.id_oostende_rrn.noMatchFormError": "Vi fant ikke informasjon om personnummeret ditt", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "Vi kan ikke bekrefte deg fordi du bor utenfor Oostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Vi kan ikke verifisere deg fordi du er yngre enn 14 år", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Personnummeret ditt står på baksiden av det digitale identitetskortet ditt", "app.modules.id_oostende_rrn.rrnLabel": "Personnummer", "app.modules.id_oostende_rrn.rrnTooltip": "Vi ber om personnummeret ditt for å bekrefte om du er statsborger i Oostende, eldre enn 14 år gammel.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "<PERSON><PERSON> finner jeg personnummeret mitt?", "app.modules.id_oostende_rrn.somethingWentWrongError": "Vi kan ikke verifisere deg fordi noe gikk galt", "app.modules.id_oostende_rrn.submit": "Send inn", "app.modules.id_oostende_rrn.takenFormError": "Personnummeret ditt har allerede blitt brukt til å verifisere en annen konto", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verifiser ved hjelp av personnummer", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "<PERSON> har fått administratorrettigheter over mappen \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Del", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Se prosjektene på {folderUrl} for å gjøre din stemme hørt!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | på medvirkningsplattformen til {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | på medvirkningsplattformen til {orgName}", "app.sessionRecording.accept": "<PERSON><PERSON>, jeg a<PERSON><PERSON><PERSON>", "app.sessionRecording.modalDescription1": "For å få en bedre forståelse av brukerne våre, ber vi en liten andel av de besøkende om å spore nettlesingsøkten i detalj.", "app.sessionRecording.modalDescription2": "Det eneste formålet med de registrerte dataene er å forbedre nettstedet. Ingen av dataene dine vil bli delt med en tredjepart. All sensitiv informasjon du oppgir, vil bli filtrert.", "app.sessionRecording.modalDescription3": "<PERSON><PERSON> du?", "app.sessionRecording.modalDescriptionFaq": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> her.", "app.sessionRecording.modalTitle": "<PERSON><PERSON>lp oss med å forbedre dette nettstedet", "app.sessionRecording.reject": "<PERSON><PERSON>, jeg avviser", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Gjennomføre en budsjettfordelingsøvelse", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Samle inn tilbakemeldinger på et dokument", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Opprett en undersøkelse på plattformen", "app.utils.AdminPage.ProjectEdit.createPoll": "Opprett en avstemning", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Integrer en ekstern undersøkelse", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Finn frivillige", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Samle inn innspill og tilbakemeldinger", "app.utils.AdminPage.ProjectEdit.shareInformation": "Del informasjon", "app.utils.FormattedCurrency.credits": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.FormattedCurrency.tokens": "symboler", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {~ ~ # credits} one {~ # credit} other {~ # credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {~ # tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Mest diskutert", "app.utils.IdeaCards.mostReacted": "De fleste reaksjonene", "app.utils.IdeaCards.newest": "Nyeste", "app.utils.IdeaCards.oldest": "Eldst", "app.utils.IdeaCards.random": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.trending": "Trender", "app.utils.IdeasNewPage.contributionFormTitle": "Legg til nytt bidrag", "app.utils.IdeasNewPage.ideaFormTitle": "Legg til ny idé", "app.utils.IdeasNewPage.initiativeFormTitle": "Legg til nytt initiativ", "app.utils.IdeasNewPage.issueFormTitle1": "Legg til ny kommentar", "app.utils.IdeasNewPage.optionFormTitle": "Legg til nytt alternativ", "app.utils.IdeasNewPage.petitionFormTitle": "<PERSON><PERSON> til ny begjæring", "app.utils.IdeasNewPage.projectFormTitle": "Legg til nytt prosjekt", "app.utils.IdeasNewPage.proposalFormTitle": "Legg til nytt forslag", "app.utils.IdeasNewPage.questionFormTitle": "Legg til nytt spørsmål", "app.utils.IdeasNewPage.surveyTitle": "Spørreundersøkelse", "app.utils.IdeasNewPage.viewYourComment": "Se din kommentar", "app.utils.IdeasNewPage.viewYourContribution": "Se ditt bidrag", "app.utils.IdeasNewPage.viewYourIdea": "Se ideen din", "app.utils.IdeasNewPage.viewYourInitiative": "<PERSON> <PERSON>t ditt", "app.utils.IdeasNewPage.viewYourInput": "Se innspillene dine", "app.utils.IdeasNewPage.viewYourIssue": "Se utgaven din", "app.utils.IdeasNewPage.viewYourOption": "<PERSON> <PERSON>t ditt", "app.utils.IdeasNewPage.viewYourPetition": "Se underskriftskampanjen din", "app.utils.IdeasNewPage.viewYourProject": "Se prosjektet ditt", "app.utils.IdeasNewPage.viewYourProposal": "Se forslaget ditt", "app.utils.IdeasNewPage.viewYourQuestion": "<PERSON> spørsm<PERSON><PERSON> ditt", "app.utils.Projects.sendSubmission": "Send innsendingsidentifikator til min e-post", "app.utils.Projects.sendSurveySubmission": "Send identifikator for innsending av undersøkelsen til min e-post", "app.utils.Projects.surveySubmission": "Innsending av spørreundersøkelse", "app.utils.Projects.yourResponseHasTheFollowingId": "<PERSON><PERSON><PERSON> ditt har følgende identifikator: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "<PERSON><PERSON> du senere bestemmer deg for at du vil at svaret ditt skal fjernes, ber vi deg kontakte oss med følgende unike identifikator:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "Du må fylle ut profilen din for å delta på dette arrangementet.", "app.utils.actionDescriptors.attendingEventNotInGroup": "Du oppfyller ikke kravene for å delta på dette arrangementet.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Du har ikke lov til å delta på dette arrangementet.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "Du må logge inn eller registrere deg for å delta på dette arrangementet.", "app.utils.actionDescriptors.attendingEventNotVerified": "Du må bekrefte kontoen din før du kan delta på dette arrangementet.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "Du må fylle ut profilen din for å melde deg som frivillig.", "app.utils.actionDescriptors.volunteeringNotInGroup": "Du oppfyller ikke kravene for å melde deg som frivillig.", "app.utils.actionDescriptors.volunteeringNotPermitted": "Du har ikke lov til å melde deg som frivillig.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "Du må logge inn eller registrere deg for å melde deg som frivillig.", "app.utils.actionDescriptors.volunteeringNotVerified": "Du må bekrefte kontoen din før du kan melde deg som frivillig.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Vennligst {completeRegistrationLink} for å melde deg som frivillig.", "app.utils.errors.api_error_default.in": "<PERSON>r ikke riktig", "app.utils.errors.default.ajv_error_birthyear_required": "Vennligst fyll inn fødselsåret ditt", "app.utils.errors.default.ajv_error_date_any": "Vennligst fyll inn en gyldig dato", "app.utils.errors.default.ajv_error_domicile_required": "Vennligst fyll inn ditt bosted", "app.utils.errors.default.ajv_error_gender_required": "Vennligst fyll inn ditt kjønn", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON> <PERSON>", "app.utils.errors.default.ajv_error_maxItems": "Kan ikke inkludere mer enn {limit, plural, one {# element} other {# elementer}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON> inneholde minst {limit, plural, one {# element} other {# elementer}}", "app.utils.errors.default.ajv_error_number_any": "Vennligst fyll inn et gyldig nummer", "app.utils.errors.default.ajv_error_politician_required": "Vennligst fyll inn om du er politiker", "app.utils.errors.default.ajv_error_required3": "Feltet er obligatorisk: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Kan ikke være blank", "app.utils.errors.default.api_error_accepted": "Må aksepteres", "app.utils.errors.default.api_error_blank": "Kan ikke være blank", "app.utils.errors.default.api_error_confirmation": "<PERSON><PERSON><PERSON> ikke overens", "app.utils.errors.default.api_error_empty": "Kan ikke være tom", "app.utils.errors.default.api_error_equal_to": "<PERSON>r ikke riktig", "app.utils.errors.default.api_error_even": "Må være jevn", "app.utils.errors.default.api_error_exclusion": "<PERSON>r <PERSON>rt", "app.utils.errors.default.api_error_greater_than": "Er for liten", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Er for liten", "app.utils.errors.default.api_error_inclusion": "Er ikke inkludert i listen", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON> <PERSON>", "app.utils.errors.default.api_error_less_than": "Er for stor", "app.utils.errors.default.api_error_less_than_or_equal_to": "Er for stor", "app.utils.errors.default.api_error_not_a_number": "Er ikke et tall", "app.utils.errors.default.api_error_not_an_integer": "Må være et heltall", "app.utils.errors.default.api_error_other_than": "<PERSON>r ikke riktig", "app.utils.errors.default.api_error_present": "Må være blank", "app.utils.errors.default.api_error_too_long": "Er for lang", "app.utils.errors.default.api_error_too_short": "Er for kort", "app.utils.errors.default.api_error_wrong_length": "<PERSON>r feil lengde", "app.utils.errors.defaultapi_error_.odd": "Må være merkelig", "app.utils.notInGroup": "Du oppfyller ikke kravene for å delta.", "app.utils.participationMethod.onSurveySubmission": "Takk skal du ha. Vi har mottatt ditt svar.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Det er ikke lenger mulig å stemme, siden denne fasen ikke lenger er aktiv.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Du oppfyller ikke kravene for å stemme.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "Du har ikke lov til å stemme.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "Du må logge inn eller registrere deg for å stemme.", "app.utils.participationMethodConfig.voting.votingNotVerified": "Du må bekrefte kontoen din før du kan stemme.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Innlevering av budsjetter ble avsluttet på {endDate}.</b> <PERSON><PERSON><PERSON> hadde totalt <b>{maxBudget} hver å fordele mellom {optionCount} alternativer.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "<PERSON><PERSON><PERSON>t budsjett", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "<PERSON><PERSON><PERSON>t lagt frem 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Du oppfyller ikke kravene for å tildele budsjetter.", "app.utils.votingMethodUtils.budgetingNotPermitted": "Du har ikke lov til å tildele budsjetter.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "Du må logge inn eller registrere deg for å tildele budsjetter.", "app.utils.votingMethodUtils.budgetingNotVerified": "Du må bekrefte kontoen din før du kan tildele budsjetter.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> ditt blir ikke regnet med</b> fø<PERSON> <PERSON> klikker på \"Send\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Minimumskravet til budsjett er {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "<PERSON><PERSON><PERSON> du er ferdig, k<PERSON><PERSON> du på \"Send\" for å sende inn budsjettet.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "<PERSON><PERSON>g de ønskede alternativene ved å trykke på \"Legg til\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "Du har totalt <b>{maxBudget} å fordele mellom {optionCount} alternativer.</b>", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON>, budsjettet ditt er sendt inn!</b> Du kan når som helst sjekke alternativene nedenfor eller endre dem før <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON>, budsjettet ditt er sendt inn!</b> Du kan når som helst sjekke alternativene nedenfor.", "app.utils.votingMethodUtils.castYourVote": "Avgi din stemme", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "Du kan legge til maksimalt {maxVotes, plural, one {# credit} other {# credits}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "Du kan legge til maksimalt {maxVotes, plural, one {# poeng} other {# poeng}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "Du kan legge til maksimalt {maxVotes, plural, one {# token} other {# tokens}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "Du kan legge til maksimalt {maxVotes, plural, one {# vote} other {# votes}} per alternativ.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "<PERSON><PERSON><PERSON> du er ferdig, k<PERSON><PERSON> du på \"Send\" for å avgi din stemme.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Velg de alternativene du foretrekker ved å trykke på \"Select\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "Du har totalt <b>{totalVotes, plural, one {# credit} other {# credits}} å fordele mellom {optionCount, plural, one {# option} other {# options}}. </b>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "Du har totalt <b>{totalVotes, plural, one {# token} other {# tokens}} å fordele mellom {optionCount, plural, one {# option} other {# options}}. </b>", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "Du har totalt <b>{totalVotes, plural, one {# vote} other {# votes}} å fordele mellom {optionCount, plural, one {# option} other {# options}}. </b>", "app.utils.votingMethodUtils.finalResults": "<PERSON><PERSON><PERSON> resultat", "app.utils.votingMethodUtils.finalTally": "<PERSON><PERSON><PERSON>", "app.utils.votingMethodUtils.howToParticipate": "Hvordan delta", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON><PERSON> stemme", "app.utils.votingMethodUtils.multipleVotingEnded1": "Avstemningen ble avsluttet på <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 studiepoeng} one {1 studiepoeng} other {# studiepoeng}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 poeng} one {1 poeng} other {# poeng}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 stemmer} one {1 stemme} other {# stemmer}}", "app.utils.votingMethodUtils.results": "Resultater", "app.utils.votingMethodUtils.singleVotingEnded": "Avstemningen ble avsluttet på <b>{endDate}.</b> <PERSON><PERSON><PERSON> kunne <b>stemme på {maxVotes} alternativer.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "<PERSON><PERSON>g dine foretrukne alternativer ved å trykke på \"Vote\"", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "Du har <b>{totalVotes} stemmer</b> som du kan tildele alternativene.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "<PERSON><PERSON><PERSON> du er ferdig, k<PERSON><PERSON> du på \"Send\" for å avgi din stemme.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Avstemningen ble avsluttet på <b>{endDate}.</b> <PERSON><PERSON><PERSON> kunne <b>stemme på 1 alternativ.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "<PERSON><PERSON><PERSON> ditt foretrukne alternativ ved å trykke på \"Vote\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "Du har <b>én stemme</b> som du kan tildele til ett av alternativene.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Avstemningen ble avsluttet på <b>{endDate}.</b> <PERSON><PERSON><PERSON> kunne <b>stemme på så mange alternativer de ønsket.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Du kan stemme på så mange alternativer du vil.", "app.utils.votingMethodUtils.submitYourBudget": "Send inn budsjettet ditt", "app.utils.votingMethodUtils.submittedBudgetCountText2": "person sendte inn budsjettet sitt online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "folk sendte inn budsjettene sine på nettet", "app.utils.votingMethodUtils.submittedVoteCountText2": "person sendte inn sin stemme på nettet", "app.utils.votingMethodUtils.submittedVotesCountText2": "folk sendte inn sine stemmer på nettet", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Stemme sendt inn 🎉", "app.utils.votingMethodUtils.votesCast": "Avgitte stemmer", "app.utils.votingMethodUtils.votingClosed": "Avstemningen er avsluttet", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b><PERSON><PERSON><PERSON> din vil ikke bli talt</b> fø<PERSON> du klikker på \"Send\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON>, din stemme er sendt inn!</b> Du kan sjekke eller endre innsendingen din før <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON>, din stemme er sendt inn!</b> Du kan når som helst sjekke eller endre din innsending nedenfor.", "components.UI.IdeaSelect.noIdeaAvailable": "Det er ingen ideer tilgjenge<PERSON>g.", "components.UI.IdeaSelect.selectIdea": "<PERSON><PERSON><PERSON> id<PERSON>", "containers.SiteMap.allProjects": "Alle prosjekter", "containers.SiteMap.customPageSection": "Tilpassede sider", "containers.SiteMap.folderInfo": "<PERSON><PERSON>", "containers.SiteMap.headSiteMapTitle": "Kart over nettstedet | {orgName}", "containers.SiteMap.homeSection": "Generelt", "containers.SiteMap.pageContents": "Innhold på siden", "containers.SiteMap.profilePage": "Profilsiden din", "containers.SiteMap.profileSettings": "<PERSON>e innstillinger", "containers.SiteMap.projectEvents": "Arrangementer", "containers.SiteMap.projectIdeas": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectInfo": "Informasjon", "containers.SiteMap.projectPoll": "Meningsmåling", "containers.SiteMap.projectSurvey": "Spørreundersøkelse", "containers.SiteMap.projectsArchived": "Arkiverte prosjekter", "containers.SiteMap.projectsCurrent": "Pågående prosjekter", "containers.SiteMap.projectsDraft": "Utkast til prosjekter", "containers.SiteMap.projectsSection": "Prosjekter på {orgName}", "containers.SiteMap.signInPage": "Logg inn", "containers.SiteMap.signUpPage": "Registrer deg", "containers.SiteMap.siteMapDescription": "Fra denne siden kan du navigere til hvilket som helst innhold på plattformen.", "containers.SiteMap.siteMapTitle": "Kart over medvirkningsplattformen til {orgName}", "containers.SiteMap.successStories": "Suksesshistorier", "containers.SiteMap.timeline": "Prosjektets faser", "containers.SiteMap.userSpaceSection": "<PERSON> konto", "containers.SubscriptionEndedPage.accessDenied": "Du har ikke lenger tilgang", "containers.SubscriptionEndedPage.subscriptionEnded": "<PERSON>ne siden er kun tilgjengelig for plattformer med et aktivt abonnement."}