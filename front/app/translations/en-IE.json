{"EmailSettingsPage.emailSettings": "Email settings", "EmailSettingsPage.initialUnsubscribeError": "There was an issue unsubscribing from this campaign, please try again.", "EmailSettingsPage.initialUnsubscribeLoading": "Your request is being processed, please wait...", "EmailSettingsPage.initialUnsubscribeSuccess": "You have successfully unsubscribed from {campaignTitle}.", "UI.FormComponents.optional": "optional", "app.closeIconButton.a11y_buttonActionMessage": "Close", "app.components.Areas.areaUpdateError": "An error occurred while saving your area. Please try again.", "app.components.Areas.followedArea": "Followed area: {areaTitle}", "app.components.Areas.followedTopic": "Followed topic: {topicTitle}", "app.components.Areas.topicUpdateError": "An error occurred while saving your topic. Please try again.", "app.components.Areas.unfollowedArea": "Unfollowed area: {areaTitle}", "app.components.Areas.unfollowedTopic": "Unfollowed topic: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Price:", "app.components.AssignBudgetControl.add": "Add", "app.components.AssignBudgetControl.added": "Added", "app.components.AssignMultipleVotesControl.addVote": "Add vote", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "You have distributed all of your credits.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "You have distributed the maximum number of credits for this option.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "You have distributed all of your points.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "You have distributed the maximum number of points for this option.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "You have distributed all of your tokens.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "You have distributed the maximum number of tokens for this option.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "You have distributed all of your votes.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "You have distributed the maximum number of votes for this option.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignMultipleVotesControl.removeVote": "Remove vote", "app.components.AssignMultipleVotesControl.select": "Select", "app.components.AssignMultipleVotesControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {credit} other {credits}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {point} other {points}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokens}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "You have distributed all of your votes.", "app.components.AssignVoteControl.phaseNotActive": "Voting is not available, since this phase is not active.", "app.components.AssignVoteControl.select": "Select", "app.components.AssignVoteControl.selected2": "Selected", "app.components.AssignVoteControl.voteForAtLeastOne": "Vote for at least 1 option", "app.components.AssignVoteControl.votesSubmitted1": "You have already submitted your vote. To modify it, click \"Modify your submission\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "You have already submitted your vote. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.AuthProviders.continue": "Continue", "app.components.AuthProviders.continueWithAzure": "Continue with {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Continue with Facebook", "app.components.AuthProviders.continueWithFakeSSO": "Continue with Fake SSO", "app.components.AuthProviders.continueWithGoogle": "Continue with Google", "app.components.AuthProviders.continueWithHoplr": "Continue with Hoplr", "app.components.AuthProviders.continueWithIdAustria": "Continue with ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Continue with {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Continue with MitID", "app.components.AuthProviders.franceConnectMergingFailed": "An account already exists with this email address.{br}{br}You cannot access the platform using FranceConnect as the personal details do not match. To log in using FranceConnect, you will have to first change your first name or last name on this platform to match your official details.{br}{br}You can log in as you normally do below.", "app.components.AuthProviders.goToLogIn": "Already have an account? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Don't have an account? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "Log in", "app.components.AuthProviders.logInWithEmail": "Log in with <PERSON><PERSON>", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "You must be the specified minimum age or above to be verified.", "app.components.AuthProviders.signUp2": "Sign up", "app.components.AuthProviders.signUpButtonAltText": "Sign up with {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Sign up with <PERSON><PERSON>", "app.components.AuthProviders.verificationRequired": "Verification required", "app.components.Author.a11yPostedBy": "Posted by", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 participant} other {{numberOfParticipants} participants}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} users", "app.components.AvatarBubbles.participant": "participant", "app.components.AvatarBubbles.participants1": "participants", "app.components.Comments.cancel": "Cancel", "app.components.Comments.commentingDisabledInCurrentPhase": "Commenting is not possible in the current phase.", "app.components.Comments.commentingDisabledInactiveProject": "Commenting is not possible because this project is currently not active.", "app.components.Comments.commentingDisabledProject": "Commenting in this project is currently disabled.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} to comment.", "app.components.Comments.commentingMaybeNotPermitted": "Please {signInLink} to see what actions can be taken.", "app.components.Comments.inputsAssociatedWithProfile": "By default your submissions will be associated with your profile, unless you select this option.", "app.components.Comments.invisibleTitleComments": "Comments", "app.components.Comments.leastRecent": "Least recent", "app.components.Comments.likeComment": "Like this comment", "app.components.Comments.mostLiked": "Most reactions", "app.components.Comments.mostRecent": "Most recent", "app.components.Comments.official": "Official", "app.components.Comments.postAnonymously": "Post anonymously", "app.components.Comments.replyToComment": "Reply to comment", "app.components.Comments.reportAsSpam": "Report as spam", "app.components.Comments.seeOriginal": "See original", "app.components.Comments.seeTranslation": "See translation", "app.components.Comments.yourComment": "Your comment", "app.components.CommonGroundResults.divisiveDescription": "Statements where people agree and disagree equally:", "app.components.CommonGroundResults.divisiveTitle": "Divisive", "app.components.CommonGroundResults.majorityDescription": "More than 60% voted one way or the other on the following:", "app.components.CommonGroundResults.majorityTitle": "Majority", "app.components.CommonGroundResults.participantLabel": "participant", "app.components.CommonGroundResults.participantsLabel1": "participants", "app.components.CommonGroundResults.statementLabel": "statement", "app.components.CommonGroundResults.statementsLabel1": "statements", "app.components.CommonGroundResults.votesLabe": "vote", "app.components.CommonGroundResults.votesLabel1": "votes", "app.components.CommonGroundStatements.agreeLabel": "Agree", "app.components.CommonGroundStatements.disagreeLabel": "Disagree", "app.components.CommonGroundStatements.noMoreStatements": "There are no statements to respond to right now", "app.components.CommonGroundStatements.noResults": "There are no results to show yet. Please make sure you have participated in the Common Ground phase and check back here after.", "app.components.CommonGroundStatements.unsureLabel": "Unsure", "app.components.CommonGroundTabs.resultsTabLabel": "Results", "app.components.CommonGroundTabs.statementsTabLabel": "Statements", "app.components.CommunityMonitorModal.formError": "Encountered an error.", "app.components.CommunityMonitorModal.surveyDescription2": "This ongoing survey tracks how you feel about governance and public services.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Takes <1 minute} one {Takes 1 minute} other {Takes # minutes}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "An email with a confirmation code has been sent to {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Change your email.", "app.components.ConfirmationModal.codeInput": "Code", "app.components.ConfirmationModal.confirmationCodeSent": "New code sent", "app.components.ConfirmationModal.didntGetAnEmail": "Didn't receive an email?", "app.components.ConfirmationModal.foundYourCode": "Found your code?", "app.components.ConfirmationModal.goBack": "Go back.", "app.components.ConfirmationModal.sendEmailWithCode": "Send Email with Code", "app.components.ConfirmationModal.sendNewCode": "Send New Code.", "app.components.ConfirmationModal.verifyAndContinue": "Verify and Continue", "app.components.ConfirmationModal.wrongEmail": "Wrong email?", "app.components.ConsentManager.Banner.accept": "Accept", "app.components.ConsentManager.Banner.ariaButtonClose2": "Reject policy and close banner", "app.components.ConsentManager.Banner.close": "Close", "app.components.ConsentManager.Banner.mainText": "This platform uses cookies in accordance with our {policyLink}.", "app.components.ConsentManager.Banner.manage": "Manage", "app.components.ConsentManager.Banner.policyLink": "Cookie policy", "app.components.ConsentManager.Banner.reject": "Reject", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "Advertising", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "We use this to personalize and measure the effectiveness of advertising campaigns of our website. We will not show any advertising on this platform, but the following services might offer you a personalized ad based on the pages you visit on our site.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Allow", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analytics", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "We use this tracking to understand better how you use the platform in order to learn and improve your navigation. This information is only used in mass analytics, and in no way to track individual people.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "Go back", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "Cancel", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "Disallow", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "Functional", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "This is required to enable and monitor basic functionalities of the website. Some tools listed here might not apply to you. Please read our cookie policy for more information.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "Required", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "To have a functional platform, we save an authenticating cookie if you sign up, and the language in which you use this platform.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "Save", "app.components.ConsentManager.Modal.PreferencesDialog.title": "Your cookie preferences", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Tools", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Content upload disclaimer", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "By uploading content, you declare that this content does not violate any regulations or rights of third parties, such as intellectual property rights, privacy rights, rights to trade secrets, and so on. Consequently, by uploading this content, you undertake to bear full and exclusive liability for all direct and indirect damages resulting from the uploaded content. Furthermore, you undertake to indemnify the platform owner and Go Vocal against any third party claims or liabilities against third parties, and any associated costs, that would arise or result from the content you uploaded.", "app.components.ContentUploadDisclaimer.onAccept": "I understand", "app.components.ContentUploadDisclaimer.onCancel": "Cancel", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Tell us why", "app.components.CustomFieldsForm.addressInputAriaLabel": "Address input", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Enter an address...", "app.components.CustomFieldsForm.adminFieldTooltip": "Field only visible to admins", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "At least three points are required for a polygon.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "At least two points are required for a line.", "app.components.CustomFieldsForm.attachmentRequired": "At least one attachment is required", "app.components.CustomFieldsForm.authorFieldLabel": "Author", "app.components.CustomFieldsForm.authorFieldPlaceholder": "Start typing to search by user email or name...", "app.components.CustomFieldsForm.back": "Back", "app.components.CustomFieldsForm.budgetFieldLabel": "Budget", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.confirm": "Confirm", "app.components.CustomFieldsForm.descriptionMinLength": "The description must be at least {min} characters long", "app.components.CustomFieldsForm.descriptionRequired": "The description is required", "app.components.CustomFieldsForm.fieldMaximumItems": "At most {maxSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "At least {minSelections, plural, one {# option} other {# options}} can be selected for the field \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "The field \"{fieldName}\" is required", "app.components.CustomFieldsForm.fileSizeLimit": "The file size limit is {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "The image is required", "app.components.CustomFieldsForm.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.CustomFieldsForm.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.CustomFieldsForm.otherArea": "Somewhere else", "app.components.CustomFieldsForm.progressBarLabel": "Progress", "app.components.CustomFieldsForm.removeAnswer": "Remove answer", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Select as many as you like", "app.components.CustomFieldsForm.selectBetween": "*Select between {minItems} and {maxItems} options", "app.components.CustomFieldsForm.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.CustomFieldsForm.selectMany": "*Choose as many as you like", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.CustomFieldsForm.tapToAddALine": "Tap to add a line", "app.components.CustomFieldsForm.tapToAddAPoint": "Tap to add a point", "app.components.CustomFieldsForm.tapToAddAnArea": "Tap to add an area", "app.components.CustomFieldsForm.titleMaxLength": "The title must be at most {max} characters long", "app.components.CustomFieldsForm.titleMinLength": "The title must be at least {min} characters long", "app.components.CustomFieldsForm.titleRequired": "The title is required", "app.components.CustomFieldsForm.topicRequired": "At least one tag is required", "app.components.CustomFieldsForm.typeYourAnswer": "Type your answer", "app.components.CustomFieldsForm.typeYourAnswerRequired": "It is required to type your answer", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Some fields were invalid. Please correct the errors and try again.", "app.components.ErrorBoundary.errorFormErrorGeneric": "An unknown error occurred while submitting your report. Please try again.", "app.components.ErrorBoundary.errorFormLabelClose": "Close", "app.components.ErrorBoundary.errorFormLabelComments": "What happened?", "app.components.ErrorBoundary.errorFormLabelEmail": "Email", "app.components.ErrorBoundary.errorFormLabelName": "Name", "app.components.ErrorBoundary.errorFormLabelSubmit": "Submit", "app.components.ErrorBoundary.errorFormSubtitle": "Our team has been notified.", "app.components.ErrorBoundary.errorFormSubtitle2": "If you’d like us to help, tell us what happened below.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Your feedback has been sent. Thank you!", "app.components.ErrorBoundary.errorFormTitle": "It looks like there is an issue.", "app.components.ErrorBoundary.genericErrorWithForm": "An error occurred and we cannot display this content. Please try again, or {openForm}", "app.components.ErrorBoundary.openFormText": "help us figure it out", "app.components.ErrorToast.budgetExceededError": "You don't have enough budget", "app.components.ErrorToast.votesExceededError": "You don't have enough votes left", "app.components.EventAttendanceButton.forwardToFriend": "Forward to a friend", "app.components.EventAttendanceButton.maxRegistrationsReached": "The maximum number of event registrations has been reached. There are no spots left.", "app.components.EventAttendanceButton.register": "Register", "app.components.EventAttendanceButton.registered": "Registered", "app.components.EventAttendanceButton.seeYouThere": "See you there!", "app.components.EventAttendanceButton.seeYouThereName": "See you there, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Less event information became visible.", "app.components.EventCard.a11y_moreContentVisible": "More event information became visible.", "app.components.EventCard.a11y_readMore": "Read more about the \"{eventTitle}\" event.", "app.components.EventCard.endsAt": "Ends at", "app.components.EventCard.readMore": "Read more", "app.components.EventCard.showLess": "Show less", "app.components.EventCard.showMore": "Show more", "app.components.EventCard.startsAt": "Starts at", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Upcoming and ongoing events in this project", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Upcoming and ongoing events in this phase", "app.components.FileUploader.a11y_file": "File:", "app.components.FileUploader.a11y_filesToBeUploaded": "Files to be uploaded: {fileNames}", "app.components.FileUploader.a11y_noFiles": "No files added.", "app.components.FileUploader.a11y_removeFile": "Remove this file", "app.components.FileUploader.fileInputDescription": "Click to select a file", "app.components.FileUploader.fileUploadLabel": "Attachments (max. 50MB)", "app.components.FileUploader.file_too_large2": "Files larger than {maxSizeMb}MB are not permitted.", "app.components.FileUploader.incorrect_extension": "{fileName} is not supported by our system, it will not be uploaded.", "app.components.FilterBoxes.a11y_allFilterSelected": "Selected status filter: all", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# submission} other {# submissions}}", "app.components.FilterBoxes.a11y_removeFilter": "Remove filter", "app.components.FilterBoxes.a11y_selectedFilter": "Selected status filter: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Selected {numberOfSelectedTopics, plural, =0 {zero tag filters} one {one tag filter} other {# tag filters}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "All", "app.components.FilterBoxes.areas": "Filter by area", "app.components.FilterBoxes.inputs": "inputs", "app.components.FilterBoxes.noValuesFound": "No values available.", "app.components.FilterBoxes.showLess": "Show less", "app.components.FilterBoxes.showTagsWithNumber": "Show all ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "Tags", "app.components.FiltersModal.filters": "Filters", "app.components.FolderFolderCard.a11y_folderDescription": "Folder description:", "app.components.FolderFolderCard.a11y_folderTitle": "Folder title:", "app.components.FolderFolderCard.archived": "Archived", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# projects} one {# project} other {# projects}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "The field type cannot be changed once there are submissions.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "Type", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autosave", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Auto-saving is enabled by default when you open the form editor. Any time you close the field settings panel using the \"X\" button, it will automatically trigger a save.", "app.components.GanttChart.timeRange.month": "Month", "app.components.GanttChart.timeRange.quarter": "Quarter", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Multi-year", "app.components.GanttChart.timeRange.year": "Year", "app.components.GanttChart.today": "Today", "app.components.GoBackButton.group.edit.goBack": "Go back", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Go back to previous page", "app.components.HookForm.Feedback.errorTitle": "There is a problem", "app.components.HookForm.Feedback.submissionError": "Try again. If the issue persists, contact us", "app.components.HookForm.Feedback.submissionErrorTitle": "There was a problem on our end, sorry", "app.components.HookForm.Feedback.successMessage": "Form successfully submitted", "app.components.HookForm.PasswordInput.passwordLabel": "Password", "app.components.HorizontalScroll.scrollLeftLabel": "<PERSON><PERSON> left.", "app.components.HorizontalScroll.scrollRightLabel": "<PERSON>roll right.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} ideas have loaded.", "app.components.IdeaCards.filters": "Filters", "app.components.IdeaCards.filters.mostDiscussed": "Most discussed", "app.components.IdeaCards.filters.newest": "New", "app.components.IdeaCards.filters.oldest": "Old", "app.components.IdeaCards.filters.popular": "Most liked", "app.components.IdeaCards.filters.random": "Random", "app.components.IdeaCards.filters.sortBy": "Sort by", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sorting changed to: {currentSortType}", "app.components.IdeaCards.filters.trending": "Trending", "app.components.IdeaCards.showMore": "Show more", "app.components.IdeasMap.a11y_hideIdeaCard": "Hide idea card.", "app.components.IdeasMap.a11y_mapTitle": "Map overview", "app.components.IdeasMap.clickOnMapToAdd": "Click on the map to add your input", "app.components.IdeasMap.clickOnMapToAddAdmin2": "As an admin, you can click on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.filters": "Filters", "app.components.IdeasMap.multipleInputsAtLocation": "Multiple inputs at this location", "app.components.IdeasMap.noFilteredResults": "The filters you selected did not return any results", "app.components.IdeasMap.noResults": "No results found", "app.components.IdeasMap.or": "or", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, no dislikes.} one {1 dislike.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, no likes.} one {, 1 like.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "log in", "app.components.IdeasMap.signUpLinkText": "sign up", "app.components.IdeasMap.submitIdea2": "Submit input", "app.components.IdeasMap.tapOnMapToAdd": "Tap on the map to add your input", "app.components.IdeasMap.tapOnMapToAddAdmin2": "As an admin, you can tap on the map to add your input, even if this phase is not active.", "app.components.IdeasMap.userInputs2": "Inputs from participants", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, no comments} one {, 1 comment} other {, # comments}}", "app.components.IdeasShow.bodyTitle": "Description", "app.components.IdeasShow.deletePost": "Delete", "app.components.IdeasShow.editPost": "Edit", "app.components.IdeasShow.goBack": "Go back", "app.components.IdeasShow.moreOptions": "More options", "app.components.IdeasShow.or": "or", "app.components.IdeasShow.proposedBudgetTitle": "Proposed budget", "app.components.IdeasShow.reportAsSpam": "Report as spam", "app.components.IdeasShow.send": "Send", "app.components.IdeasShow.skipSharing": "Skip it, I'll do it later", "app.components.IdeasShowPage.signIn2": "Log in", "app.components.IdeasShowPage.sorryNoAccess": "Sorry, you can't access this page. You may need to log in or sign up to access it.", "app.components.LocationInput.noOptions": "No options", "app.components.Modal.closeWindow": "Close window", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Enter a new email address", "app.components.PageNotFound.goBackToHomePage": "Back to the homepage", "app.components.PageNotFound.notFoundTitle": "Page not found", "app.components.PageNotFound.pageNotFoundDescription": "The requested page could not be found.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Provide content for at least one language", "app.components.PagesForm.editContent": "Content", "app.components.PagesForm.fileUploadLabel": "Attachments (max. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Files should not be larger than 50Mb. Added files will be shown on the bottom of this page.", "app.components.PagesForm.navbarItemTitle": "Name in navbar", "app.components.PagesForm.pageTitle": "Title", "app.components.PagesForm.savePage": "Save page", "app.components.PagesForm.saveSuccess": "<PERSON> successfully saved.", "app.components.PagesForm.titleMissingOneLanguageError": "Provide title for at least one language", "app.components.Pagination.back": "Previous page", "app.components.Pagination.next": "Next page", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "You spent {votesCast}, which exceeds the limit of {votesLimit}. Please remove some items from your basket and try again.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} left", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "You need to spend a minimum of {votesMinimum} before you can submit your basket.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "You need to select at least one option before you can submit.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "You need to add something to your basket before you can submit it.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {No credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {No points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {No tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {No votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# votes} one {# vote} other {# votes}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "You cast {votesCast} votes, which exceeds the limit of {votesLimit}. Please remove some votes and try again.", "app.components.ParticipationCTABars.addInput": "Add input", "app.components.ParticipationCTABars.allocateBudget": "Allocate your budget", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Your budget has been submitted successfully.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Open for participation", "app.components.ParticipationCTABars.poll": "Take the poll", "app.components.ParticipationCTABars.reviewDocument": "Review the document", "app.components.ParticipationCTABars.seeContributions": "See contributions", "app.components.ParticipationCTABars.seeEvents3": "See events", "app.components.ParticipationCTABars.seeIdeas": "See ideas", "app.components.ParticipationCTABars.seeInitiatives": "See initiatives", "app.components.ParticipationCTABars.seeIssues": "See comments", "app.components.ParticipationCTABars.seeOptions": "See options", "app.components.ParticipationCTABars.seePetitions": "See petitions", "app.components.ParticipationCTABars.seeProjects": "See projects", "app.components.ParticipationCTABars.seeProposals": "See proposals", "app.components.ParticipationCTABars.seeQuestions": "See questions", "app.components.ParticipationCTABars.submit": "Submit", "app.components.ParticipationCTABars.takeTheSurvey": "Take the survey", "app.components.ParticipationCTABars.userHasParticipated": "You have participated in this project.", "app.components.ParticipationCTABars.viewInputs": "View inputs", "app.components.ParticipationCTABars.volunteer": "Volunteer", "app.components.ParticipationCTABars.votesCounter.vote": "vote", "app.components.ParticipationCTABars.votesCounter.votes": "votes", "app.components.PasswordInput.a11y_passwordHidden": "Password hidden", "app.components.PasswordInput.a11y_passwordVisible": "Password visible", "app.components.PasswordInput.a11y_strength1Password": "Poor password strength", "app.components.PasswordInput.a11y_strength2Password": "Weak password strength", "app.components.PasswordInput.a11y_strength3Password": "Medium password strength", "app.components.PasswordInput.a11y_strength4Password": "Strong password strength", "app.components.PasswordInput.a11y_strength5Password": "Very strong password strength", "app.components.PasswordInput.hidePassword": "Hide password", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "Too short (min. {minimumPasswordLength} characters)", "app.components.PasswordInput.minimumPasswordLengthError": "Provide a password that is at least {minimumPasswordLength} characters long", "app.components.PasswordInput.passwordEmptyError": "Enter your password", "app.components.PasswordInput.passwordStrengthTooltip1": "To make your password stronger:", "app.components.PasswordInput.passwordStrengthTooltip2": "Use a combination of non-consecutive lowercase letters, uppercase letters, digits, special characters and punctuation", "app.components.PasswordInput.passwordStrengthTooltip3": "Avoid common or easily guessed words", "app.components.PasswordInput.passwordStrengthTooltip4": "Increase the length", "app.components.PasswordInput.showPassword": "Show password", "app.components.PasswordInput.strength1Password": "Poor", "app.components.PasswordInput.strength2Password": "Weak", "app.components.PasswordInput.strength3Password": "Medium", "app.components.PasswordInput.strength4Password": "Strong", "app.components.PasswordInput.strength5Password": "Very strong", "app.components.PostCardsComponents.list": "List", "app.components.PostCardsComponents.map": "Map", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "Add an official update", "app.components.PostComponents.OfficialFeedback.cancel": "Cancel", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Delete", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Are you sure you want to delete this official update?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "Edit", "app.components.PostComponents.OfficialFeedback.lastEdition": "Last edited on {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Last update: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Official", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "Choose how people see your name", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Official update author name", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Official update body text", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Official updates", "app.components.PostComponents.OfficialFeedback.postedOn": "Posted on {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Publish", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Show previous updates", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Give an update...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "Sorry, there was a problem", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Update message", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Your update was published successfully!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "Support my contribution '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "Support my contribution: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "Support my contribution: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Support my idea '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "Support my idea: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "Support my idea: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "What do you think of this proposal? Vote on it and share the discussion at {postUrl} to make your voice heard!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "Support my proposal: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "Support my initiative: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "I posted a comment '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "I just posted a comment: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "I just posted a comment: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "Support my proposed option '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "Support my proposed option: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "Support my option: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "Support my petition '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "Support my petition: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "Support my project '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "Support my project: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "Support my project: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "Support my proposal '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "Support my proposal: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "I just posted a proposal for {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Join the discussion about this question '{postTitle}' at {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "Join the discussion: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "Join the discussion: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "Vote for {postTitle} on", "app.components.PostComponents.linkToHomePage": "Link to home page", "app.components.PostComponents.readMore": "Read more...", "app.components.PostComponents.topics": "Topics", "app.components.ProjectArchivedIndicator.archivedProject": "Unfortunately, you can't participate in this project anymore because it has been archived", "app.components.ProjectArchivedIndicator.previewProject": "Draft project:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Visible only to moderators and those with a preview link.", "app.components.ProjectCard.a11y_projectDescription": "Project description:", "app.components.ProjectCard.a11y_projectTitle": "Project title:", "app.components.ProjectCard.addYourOption": "Add your option", "app.components.ProjectCard.allocateYourBudget": "Allocate your budget", "app.components.ProjectCard.archived": "Archived", "app.components.ProjectCard.comment": "Comment", "app.components.ProjectCard.contributeYourInput": "Contribute your input", "app.components.ProjectCard.finished": "Finished", "app.components.ProjectCard.joinDiscussion": "Join the discussion", "app.components.ProjectCard.learnMore": "Learn more", "app.components.ProjectCard.reaction": "Reaction", "app.components.ProjectCard.readTheReport": "Read the report", "app.components.ProjectCard.reviewDocument": "Review the document", "app.components.ProjectCard.submitAnIssue": "Submit a comment", "app.components.ProjectCard.submitYourIdea": "Submit your idea", "app.components.ProjectCard.submitYourInitiative": "Submit your initiative", "app.components.ProjectCard.submitYourPetition": "Submit your petition", "app.components.ProjectCard.submitYourProject": "Submit your project", "app.components.ProjectCard.submitYourProposal": "Submit your proposal", "app.components.ProjectCard.takeThePoll": "Take the poll", "app.components.ProjectCard.takeTheSurvey": "Take the survey", "app.components.ProjectCard.viewTheContributions": "View the contributions", "app.components.ProjectCard.viewTheIdeas": "View the ideas", "app.components.ProjectCard.viewTheInitiatives": "View the initiatives", "app.components.ProjectCard.viewTheIssues": "View the comments", "app.components.ProjectCard.viewTheOptions": "View the options", "app.components.ProjectCard.viewThePetitions": "View the petitions", "app.components.ProjectCard.viewTheProjects": "View the projects", "app.components.ProjectCard.viewTheProposals": "View the proposals", "app.components.ProjectCard.viewTheQuestions": "View the questions", "app.components.ProjectCard.vote": "Vote", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# comments} other {# comments}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# contribution} other {# contributions}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {no ideas yet} one {# idea} other {# ideas}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# initiatives} one {# initiative} other {# initiatives}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# comment} other {# comments}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# option} other {# options}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petitions} one {# petition} other {# petitions}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# project} other {# projects}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# proposals} one {# proposal} other {# proposals}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# question} other {# questions}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# comments} one {# comments} other {# comments}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# projects} one {# project} other {# projects}}", "app.components.ProjectFolderCards.components.Topbar.all": "All", "app.components.ProjectFolderCards.components.Topbar.archived": "Archived", "app.components.ProjectFolderCards.components.Topbar.draft": "Draft", "app.components.ProjectFolderCards.components.Topbar.filterBy": "Filter by", "app.components.ProjectFolderCards.components.Topbar.published2": "Published", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "There are currently no open projects", "app.components.ProjectFolderCards.noProjectsAvailable": "No projects available", "app.components.ProjectFolderCards.showMore": "Show more", "app.components.ProjectFolderCards.stayTuned": "Check back again for new engagement opportunities", "app.components.ProjectFolderCards.tryChangingFilters": "Try changing the selected filters.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Also used in these cities:", "app.components.ProjectTemplatePreview.copied": "<PERSON>pied", "app.components.ProjectTemplatePreview.copyLink": "Copy link", "app.components.QuillEditor.alignCenter": "Center text", "app.components.QuillEditor.alignLeft": "<PERSON><PERSON> left", "app.components.QuillEditor.alignRight": "Align right", "app.components.QuillEditor.bold": "Bold", "app.components.QuillEditor.clean": "Remove formatting", "app.components.QuillEditor.customLink": "Add button", "app.components.QuillEditor.customLinkPrompt": "Enter link:", "app.components.QuillEditor.edit": "Edit", "app.components.QuillEditor.image": "Upload image", "app.components.QuillEditor.imageAltPlaceholder": "Short description of the image", "app.components.QuillEditor.italic": "Italic", "app.components.QuillEditor.link": "Add link", "app.components.QuillEditor.linkPrompt": "Enter link:", "app.components.QuillEditor.normalText": "Normal", "app.components.QuillEditor.orderedList": "Ordered list", "app.components.QuillEditor.remove": "Remove", "app.components.QuillEditor.save": "Save", "app.components.QuillEditor.subtitle": "Subtitle", "app.components.QuillEditor.title": "Title", "app.components.QuillEditor.unorderedList": "Unordered list", "app.components.QuillEditor.video": "Add video", "app.components.QuillEditor.videoPrompt": "Enter video:", "app.components.QuillEditor.visitPrompt": "Visit link:", "app.components.ReactionControl.completeProfileToReact": "Complete your profile to react", "app.components.ReactionControl.dislike": "Dislike", "app.components.ReactionControl.dislikingDisabledMaxReached": "You've reached your maximum number of dislikes in {projectName}", "app.components.ReactionControl.like": "Like", "app.components.ReactionControl.likingDisabledMaxReached": "You've reached your maximum number of likes in {projectName}", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reacting will be enabled once this phase starts", "app.components.ReactionControl.reactingDisabledPhaseOver": "It's no longer possible to react in this phase", "app.components.ReactionControl.reactingDisabledProjectInactive": "You can no longer react to ideas in {projectName}", "app.components.ReactionControl.reactingNotEnabled": "Reacting is currently not enabled for this project", "app.components.ReactionControl.reactingNotPermitted": "Reacting is only enabled for certain groups", "app.components.ReactionControl.reactingNotSignedIn": "Sign in to react.", "app.components.ReactionControl.reactingPossibleLater": "Reacting will start on {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Verify your identity in order to react.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Event date: {startDate} at {startTime} to {endDate} at {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Event date: {eventDate} from {startTime} to {endTime}.", "app.components.Sharing.linkCopied": "Link copied", "app.components.Sharing.or": "or", "app.components.Sharing.share": "Share", "app.components.Sharing.shareByEmail": "Share by email", "app.components.Sharing.shareByLink": "Copy link", "app.components.Sharing.shareOnFacebook": "Share on Facebook", "app.components.Sharing.shareOnTwitter": "Share on Twitter", "app.components.Sharing.shareThisEvent": "Share this event", "app.components.Sharing.shareThisFolder": "Share", "app.components.Sharing.shareThisProject": "Share this project", "app.components.Sharing.shareViaMessenger": "Share via Messenger", "app.components.Sharing.shareViaWhatsApp": "Share via WhatsApp", "app.components.SideModal.closeButtonAria": "Close", "app.components.StatusModule.futurePhase": "You are viewing a phase that has not started yet. You will be able to participate when the phase starts.", "app.components.StatusModule.modifyYourSubmission1": "Modify your submission", "app.components.StatusModule.submittedUntil3": "Your vote may be submitted until", "app.components.TopicsPicker.numberOfSelectedTopics": "Selected {numberOfSelectedTopics, plural, =0 {zero tags} one {one tag} other {# tags}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Expand image", "app.components.UI.MoreActionsMenu.moreOptions": "More options", "app.components.UI.MoreActionsMenu.showMoreActions": "Show more actions", "app.components.UI.PhaseFilter.noAppropriatePhases": "No appropriate phases found for this project", "app.components.UI.RemoveImageButton.a11y_removeImage": "Remove", "app.components.UI.TranslateButton.original": "Original", "app.components.UI.TranslateButton.translate": "Translate", "app.components.Unauthorized.additionalInformationRequired": "Additional information is required for you to participate.", "app.components.Unauthorized.completeProfile": "Complete profile", "app.components.Unauthorized.completeProfileTitle": "Complete your profile to participate", "app.components.Unauthorized.noPermission": "You don't have permission to view this page", "app.components.Unauthorized.notAuthorized": "Sorry, you're not authorized to access this page.", "app.components.Upload.errorImageMaxSizeExceeded": "The image you selected is larger than {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "One or several images you selected are larger than {maxFileSize}MB", "app.components.Upload.onlyOneImage": "You can only upload 1 image", "app.components.Upload.onlyXImages": "You can only upload {maxItemsCount} images", "app.components.Upload.remaining": "remaining", "app.components.Upload.uploadImageLabel": "Select an image (max. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "Select one or more images", "app.components.UpsellTooltip.tooltipContent": "This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.", "app.components.UserName.anonymous": "Anonymous", "app.components.UserName.anonymousTooltip2": "This user has decided to anonymize their contribution", "app.components.UserName.authorWithNoNameTooltip": "Your name has been autogenerated because you have not entered your name. Please update your profile if you would like to change it.", "app.components.UserName.deletedUser": "unknown author", "app.components.UserName.verified": "Verified", "app.components.VerificationModal.verifyAuth0": "Verify with NemID", "app.components.VerificationModal.verifyBOSA": "Verify with itsme or eID", "app.components.VerificationModal.verifyBosaFas": "Verify with itsme or eID", "app.components.VerificationModal.verifyClaveUnica": "Verify with Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Verify with Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Verify with ID Austria", "app.components.VerificationModal.verifyKeycloak": "Verify with ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Verify with MitID", "app.components.VerificationModal.verifyTwoday2": "Verify with BankID or Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Verify your identity", "app.components.VoteControl.budgetingFutureEnabled": "You can allocate your budget starting from {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Participatory budgeting is not currently enabled.", "app.components.VoteControl.budgetingNotPossible": "Making changes to your budget is not possible at this time.", "app.components.VoteControl.budgetingNotVerified": "Please {verifyAccountLink} to continue.", "app.components.VoteInputs._shared.currencyLeft1": "You have {budgetLeft} / {totalBudget} left", "app.components.VoteInputs._shared.numberOfCreditsLeft": "You have {votesLeft, plural, =0 {no credits left} other {# out of {totalNumberOfVotes, plural, one {1 credit} other {# credits}} left}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "You have {votesLeft, plural, =0 {no points left} other {# out of {totalNumberOfVotes, plural, one {1 point} other {# points}} left}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "You have {votesLeft, plural, =0 {no tokens left} other {# out of {totalNumberOfVotes, plural, one {1 token} other {# tokens}} left}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "You have {votesLeft, plural, =0 {no votes left} other {# out of {totalNumberOfVotes, plural, one {1 vote} other {# votes}} left}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "You have already submitted your budget. To modify it, click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "You have already submitted your budget. To modify it, go back to the project page and click \"Modify your submission\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budgeting is not available, since this phase is not active.", "app.components.VoteInputs.single.youHaveVotedForX2": "You have voted for {votes, plural, =0 {# options} one {# option} other {# options}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "This means you will lose all data associated with this input, like comments, reactions and votes. This action cannot be undone.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "Are you sure you want to delete this input?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "Cancel", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Confirm", "app.components.admin.SlugInput.resultingURL": "Resulting URL", "app.components.admin.SlugInput.slugTooltip": "The slug is the unique set of words at the end of page's web address, or URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "If you change the URL, links to the page using the old URL will no longer work.", "app.components.admin.SlugInput.urlSlugLabel": "Slug", "app.components.admin.UserFilterConditions.addCondition": "Add a condition", "app.components.admin.UserFilterConditions.field_email": "Email", "app.components.admin.UserFilterConditions.field_event_attendance": "Event registrations", "app.components.admin.UserFilterConditions.field_follow": "Follow", "app.components.admin.UserFilterConditions.field_lives_in": "Lives in", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Community monitor survey", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interacted with an input with status", "app.components.admin.UserFilterConditions.field_participated_in_project": "Contributed to project", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Posted something with tag", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Registration date", "app.components.admin.UserFilterConditions.field_role": "Role", "app.components.admin.UserFilterConditions.field_verified": "Verification", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "Ideation", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Proposals", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "is not registered for any of these events", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "is not registered for any event", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "is registered for one of these events", "app.components.admin.UserFilterConditions.predicate_attends_something": "is registered for at least one event", "app.components.admin.UserFilterConditions.predicate_begins_with": "begins with", "app.components.admin.UserFilterConditions.predicate_commented_in": "commented", "app.components.admin.UserFilterConditions.predicate_contains": "contains", "app.components.admin.UserFilterConditions.predicate_ends_on": "ends on", "app.components.admin.UserFilterConditions.predicate_has_value": "has value", "app.components.admin.UserFilterConditions.predicate_in": "performed any action", "app.components.admin.UserFilterConditions.predicate_is": "is", "app.components.admin.UserFilterConditions.predicate_is_admin": "is an admin", "app.components.admin.UserFilterConditions.predicate_is_after": "is after", "app.components.admin.UserFilterConditions.predicate_is_before": "is before", "app.components.admin.UserFilterConditions.predicate_is_checked": "is checked", "app.components.admin.UserFilterConditions.predicate_is_empty": "is empty", "app.components.admin.UserFilterConditions.predicate_is_equal": "is", "app.components.admin.UserFilterConditions.predicate_is_exactly": "is exactly", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "is larger than", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "is larger than or equal to", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "is a normal user", "app.components.admin.UserFilterConditions.predicate_is_not_area": "excludes area", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "excludes folder", "app.components.admin.UserFilterConditions.predicate_is_not_input": "excludes input", "app.components.admin.UserFilterConditions.predicate_is_not_project": "excludes project", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "excludes topic", "app.components.admin.UserFilterConditions.predicate_is_one_of": "is one of", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "one of the areas", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "one of the folders", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "one of the inputs", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "one of the projects", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "one of the topics", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "is a project manager", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "is smaller than", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "is smaller than or equal to", "app.components.admin.UserFilterConditions.predicate_is_verified": "is verified", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "does not begin with", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "didn't comment", "app.components.admin.UserFilterConditions.predicate_not_contains": "does not contain", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "does not end on", "app.components.admin.UserFilterConditions.predicate_not_has_value": "does not have value", "app.components.admin.UserFilterConditions.predicate_not_in": "didn't contribute", "app.components.admin.UserFilterConditions.predicate_not_is": "is not", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "is not an admin", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "is not checked", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "is not empty", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "is not", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "is not a normal user", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "is not one of", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "is not a project manager", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "is not verified", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "didn't post an input", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "didn't react to comment", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "didn't react to input", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "didn't register to an event", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "has not taken survey", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "didn't volunteer", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "didn't participate in voting", "app.components.admin.UserFilterConditions.predicate_nothing": "nothing", "app.components.admin.UserFilterConditions.predicate_posted_input": "posted an input", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "reacted to comment", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "reacted to input", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "registered to an event", "app.components.admin.UserFilterConditions.predicate_something": "something", "app.components.admin.UserFilterConditions.predicate_taken_survey": "has taken survey", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "volunteered", "app.components.admin.UserFilterConditions.predicate_voted_in3": "participated in voting", "app.components.admin.UserFilterConditions.rulesFormLabelField": "Attribute", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "Condition", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "Value", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "You won't get notifications on your contribution", "app.components.anonymousParticipationModal.cancel": "Cancel", "app.components.anonymousParticipationModal.continue": "Continue", "app.components.anonymousParticipationModal.participateAnonymously": "Participate anonymously", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "This will safely <b>hide your profile</b> from admins, project managers and other residents for this specific contribution so that nobody is able to link this contribution to you. Anonymous contributions cannot be edited, and are considered final.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Making our platform safe for every user is a top priority for us. Words matter, so please be kind to each other.", "app.components.avatar.titleForAccessibility": "Profile of {fullName}", "app.components.customFields.mapInput.removeAnswer": "Remove answer", "app.components.customFields.mapInput.undo": "Undo", "app.components.customFields.mapInput.undoLastPoint": "Undo last point", "app.components.followUnfollow.follow": "Follow", "app.components.followUnfollow.followADiscussion": "Follow the discussion", "app.components.followUnfollow.followTooltipInputPage2": "Following triggers email updates about status changes, official updates, and comments. You can {unsubscribeLink} at any time.", "app.components.followUnfollow.followTooltipProjects2": "Following triggers email updates about project changes. You can {unsubscribeLink} at any time.", "app.components.followUnfollow.unFollow": "Unfollow", "app.components.followUnfollow.unsubscribe": "unsubscribe", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "our guidelines", "app.components.form.ErrorDisplay.next": "Next", "app.components.form.ErrorDisplay.previous": "Previous", "app.components.form.ErrorDisplay.save": "Save", "app.components.form.ErrorDisplay.userPickerPlaceholder": "Start typing to search by user email or name...", "app.components.form.anonymousSurveyMessage2": "All responses to this survey are anonymized.", "app.components.form.backToInputManager": "Back to input manager", "app.components.form.backToProject": "Back to project", "app.components.form.components.controls.mapInput.removeAnswer": "Remove answer", "app.components.form.components.controls.mapInput.undo": "Undo", "app.components.form.components.controls.mapInput.undoLastPoint": "Undo last point", "app.components.form.controls.addressInputAriaLabel": "Address input", "app.components.form.controls.addressInputPlaceholder6": "Enter an address...", "app.components.form.controls.adminFieldTooltip": "Field only visible to admins", "app.components.form.controls.allStatementsError": "An answer must be selected for all statements.", "app.components.form.controls.back": "Back", "app.components.form.controls.clearAll": "Clear all", "app.components.form.controls.clearAllScreenreader": "Clear all answers from above matrix question", "app.components.form.controls.clickOnMapMultipleToAdd3": "Click on the map to draw. Then, drag on points to move them.", "app.components.form.controls.clickOnMapToAddOrType": "Click on the map or type an address below to add your answer.", "app.components.form.controls.confirm": "Confirm", "app.components.form.controls.cosponsorsPlaceholder": "Start typing a name to search", "app.components.form.controls.currentRank": "Current rank:", "app.components.form.controls.minimumCoordinates2": "A minimum of {numPoints} map points is required.", "app.components.form.controls.noRankSelected": "No rank selected", "app.components.form.controls.notPublic1": "*This answer will only be shared with project managers, and not to the public.", "app.components.form.controls.optionalParentheses": "(optional)", "app.components.form.controls.rankingInstructions": "Drag and drop to rank options.", "app.components.form.controls.selectAsManyAsYouLike": "*Select as many as you like", "app.components.form.controls.selectBetween": "*Select between {minItems} and {maxItems} options", "app.components.form.controls.selectExactly2": "*Select exactly {selectExactly, plural, one {# option} other {# options}}", "app.components.form.controls.selectMany": "*Choose as many as you like", "app.components.form.controls.tapOnFullscreenMapToAdd4": "Tap on the map to draw. Then, drag on points to move them.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "Tap on the map to draw.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Tap on the map to add your answer.", "app.components.form.controls.tapOnMapToAddOrType": "Tap on the map or type an address below to add your answer.", "app.components.form.controls.tapToAddALine": "Tap to add a line", "app.components.form.controls.tapToAddAPoint": "Tap to add a point", "app.components.form.controls.tapToAddAnArea": "Tap to add an area", "app.components.form.controls.uploadShapefileInstructions": "* Upload a zip file containing one or more shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "If the location is not displayed among the options as you type, you can add valid coordinates in the format 'latitude, longitude' to specify a precise location (eg: -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} out of {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} out of {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} out of {total}, where {maxValue} is {maxLabel}", "app.components.form.error": "Error", "app.components.form.locationGoogleUnavailable": "Couldn't load location field provided by google maps.", "app.components.form.progressBarLabel": "Survey progress", "app.components.form.submit": "Submit", "app.components.form.submitApiError": "There was an issue submitting the form. Please check for any errors and try again.", "app.components.form.verifiedBlocked": "You can't edit this field because it contains verified information", "app.components.formBuilder.Page": "Page", "app.components.formBuilder.accessibilityStatement": "accessibility statement", "app.components.formBuilder.addAnswer": "Add answer", "app.components.formBuilder.addStatement": "Add statement", "app.components.formBuilder.agree": "Agree", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "If you have access to our AI package, you will be able to summarise and categorise text responses with AI", "app.components.formBuilder.askFollowUpToggleLabel": "Ask follow up", "app.components.formBuilder.bad": "Bad", "app.components.formBuilder.buttonLabel": "Button label", "app.components.formBuilder.buttonLink": "Button link", "app.components.formBuilder.cancelLeaveBuilderButtonText": "Cancel", "app.components.formBuilder.category": "Category", "app.components.formBuilder.chooseMany": "Choose many", "app.components.formBuilder.chooseOne": "Choose one", "app.components.formBuilder.close": "Close", "app.components.formBuilder.closed": "Closed", "app.components.formBuilder.configureMap": "Configure map", "app.components.formBuilder.confirmLeaveBuilderButtonText": "Yes, I want to leave", "app.components.formBuilder.content": "Content", "app.components.formBuilder.continuePageLabel": "Continues to", "app.components.formBuilder.cosponsors": "Co-sponsors", "app.components.formBuilder.default": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.defaultContent": "Default content", "app.components.formBuilder.delete": "Delete", "app.components.formBuilder.deleteButtonLabel": "Delete", "app.components.formBuilder.description": "Description", "app.components.formBuilder.disabledBuiltInFieldTooltip": "This has already been added in the form. Default content may only be used once.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Adding custom content is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.components.formBuilder.disagree": "Disagree", "app.components.formBuilder.displayAsDropdown": "Display as dropdown", "app.components.formBuilder.displayAsDropdownTooltip": "Display the options in a dropdown. If you have many options, this is recommended.", "app.components.formBuilder.done": "Done", "app.components.formBuilder.drawArea": "Draw area", "app.components.formBuilder.drawRoute": "Draw route", "app.components.formBuilder.dropPin": "Drop pin", "app.components.formBuilder.editButtonLabel": "Edit", "app.components.formBuilder.emptyImageOptionError": "Provide at least 1 answer. Please note that each answer has to have a title.", "app.components.formBuilder.emptyOptionError": "Provide at least 1 answer", "app.components.formBuilder.emptyStatementError": "Provide at least 1 statement", "app.components.formBuilder.emptyTitleError": "Provide a question title", "app.components.formBuilder.emptyTitleMessage": "Provide a title for all the answers", "app.components.formBuilder.emptyTitleStatementMessage": "Provide a title for all the statements", "app.components.formBuilder.enable": "Enable", "app.components.formBuilder.errorMessage": "There is a problem, please fix the issue to be able to save your changes", "app.components.formBuilder.fieldGroup.description": "Description (optional)", "app.components.formBuilder.fieldGroup.title": "Title (optional)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Currently, answers to these questions are only available in the exported excel file on Input Manager, and not visible to the users.", "app.components.formBuilder.fieldLabel": "Answer choices", "app.components.formBuilder.fieldLabelStatement": "Statements", "app.components.formBuilder.fileUpload": "File upload", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Map-based page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "Embed map as context or ask location based questions to participants.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "For optimal user experience, we do not recommend adding point, route, or area questions to map-based pages.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "Normal page", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "Page type", "app.components.formBuilder.formEnd": "Form end", "app.components.formBuilder.formField.cancelDeleteButtonText": "Cancel", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Yes, delete page", "app.components.formBuilder.formField.copyNoun": "Copy", "app.components.formBuilder.formField.copyVerb": "Copy", "app.components.formBuilder.formField.delete": "Delete", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Deleting this page will also delete the logic associated with it. Are you sure you want to delete it?", "app.components.formBuilder.formField.deleteResultsInfo": "This cannot be undone", "app.components.formBuilder.goToPageInputLabel": "Then next page is:", "app.components.formBuilder.good": "Good", "app.components.formBuilder.helmetTitle": "Form builder", "app.components.formBuilder.imageFileUpload": "Image upload", "app.components.formBuilder.invalidLogicBadgeMessage": "Invalid logic", "app.components.formBuilder.labels2": "Labels (optional)", "app.components.formBuilder.labelsTooltipContent2": "Choose optional labels for any of the linear scale values.", "app.components.formBuilder.lastPage": "Ending", "app.components.formBuilder.layout": "Layout", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "Are you sure you want to leave?", "app.components.formBuilder.leaveBuilderText": "You have unsaved changes. Please save before leaving. If you leave, you'll lose your changes.", "app.components.formBuilder.limitAnswersTooltip": "When turned on, respondents need to select the specified number of answers to proceed.", "app.components.formBuilder.limitNumberAnswers": "Limit number of answers", "app.components.formBuilder.linePolygonMapWarning2": "Line and polygon drawing may not meet accessibility standards. More information can be found in the {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Linear scale", "app.components.formBuilder.locationDescription": "Location", "app.components.formBuilder.logic": "Logic", "app.components.formBuilder.logicAnyOtherAnswer": "Any other answer", "app.components.formBuilder.logicConflicts.conflictingLogic": "Conflicting logic", "app.components.formBuilder.logicConflicts.interQuestionConflict": "This page contains questions that lead to different pages. If participants answer multiple questions, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "This page has multiple logic rules applied: multi-select question logic, page-level logic, and inter-question logic. When these conditions overlap, question logic will take precedence over page logic, and the furthest page will be shown. Review the logic to ensure it aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "This page contains a multi-select question where options lead to different pages. If participants select multiple options, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "This page contains a multi-select question where options lead to different pages and has questions that lead to other pages. The furthest page will be shown if these conditions overlap. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "This page contains a multi-select question where options lead to different pages and has logic set at both the page and question level. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "This page has logic set at both the page level and question level. Question logic will take precedence over page-level logic. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "This page has logic set at both the page and question levels, and multiple questions direct to different pages. Question logic will take precedence, and the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.logicNoAnswer2": "Not answered", "app.components.formBuilder.logicPanelAnyOtherAnswer": "If any other answer", "app.components.formBuilder.logicPanelNoAnswer": "If not answered", "app.components.formBuilder.logicValidationError": "Logic may not link to prior pages", "app.components.formBuilder.longAnswer": "Long answer", "app.components.formBuilder.mapConfiguration": "Map configuration", "app.components.formBuilder.mapping": "Mapping", "app.components.formBuilder.mappingNotInCurrentLicense": "Survey mapping features are not included in your current license. Reach out to your GovSuccess Manager to learn more.", "app.components.formBuilder.matrix": "Matrix", "app.components.formBuilder.matrixSettings.columns": "Columns", "app.components.formBuilder.matrixSettings.rows": "Rows", "app.components.formBuilder.multipleChoice": "Multiple choice", "app.components.formBuilder.multipleChoiceHelperText": "If multiple options lead to different pages and participants select more than one, the furthest page will be shown. Ensure this behavior aligns with your intended flow.", "app.components.formBuilder.multipleChoiceImage": "Image choice", "app.components.formBuilder.multiselect.maximum": "Maximum", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutral", "app.components.formBuilder.newField": "New field", "app.components.formBuilder.number": "Number", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Open", "app.components.formBuilder.optional": "Optional", "app.components.formBuilder.other": "Other", "app.components.formBuilder.otherOption": "\"Other\" option", "app.components.formBuilder.otherOptionTooltip": "Allow participants to enter a custom response if the provided answers do not match their preference", "app.components.formBuilder.page": "Page", "app.components.formBuilder.pageCannotBeDeleted": "This page can't be deleted.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "This page cannot be deleted and does not allow any additional fields to be added.", "app.components.formBuilder.pageRuleLabel": "Next page is:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "If no logic is added, the form will follow its normal flow. If both the page and its questions have logic, the question logic will take precedence. Ensure this aligns with your intended flow For more information, visit {supportPageLink}", "app.components.formBuilder.preview": "Preview:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Co-sponsors are not shown on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "File upload questions are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Mapping questions are shown on the downloaded PDF, but layers will not be visible. Mapping questions are not supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Matrix questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Page titles and descriptions are shown as a section header in the downloaded PDF.", "app.components.formBuilder.printSupportTooltip.ranking": "Ranking questions are shown on the downloaded PDF but are not currently supported for import via FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tags are shown as unsupported on the downloaded PDF and are not supported for import via FormSync.", "app.components.formBuilder.proposedBudget": "Proposed budget", "app.components.formBuilder.question": "Question", "app.components.formBuilder.questionCannotBeDeleted": "This question can't be deleted.", "app.components.formBuilder.questionDescriptionOptional": "Question description (optional)", "app.components.formBuilder.questionTitle": "Question title", "app.components.formBuilder.randomize": "Randomize", "app.components.formBuilder.randomizeToolTip": "The order of the answers will be randomized per user", "app.components.formBuilder.range": "Range", "app.components.formBuilder.ranking": "Ranking", "app.components.formBuilder.rating": "Rating", "app.components.formBuilder.removeAnswer": "Remove answer", "app.components.formBuilder.required": "Required", "app.components.formBuilder.requiredToggleLabel": "Make answering this question required", "app.components.formBuilder.ruleForAnswerLabel": "If answer is:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "If answers include:", "app.components.formBuilder.save": "Save", "app.components.formBuilder.selectRangeTooltip": "Choose the maximum value for your scale.", "app.components.formBuilder.sentiment": "Sentiment scale", "app.components.formBuilder.shapefileUpload": "Esri shapefile upload", "app.components.formBuilder.shortAnswer": "Short answer", "app.components.formBuilder.showResponseToUsersToggleLabel": "Show response to users", "app.components.formBuilder.singleChoice": "Single choice", "app.components.formBuilder.staleDataErrorMessage2": "There has been a problem. This input form has been saved more recently somewhere else. This may be because you or another user has it open for editing in another browser window. Please refresh the page to get the latest form and then make your changes again.", "app.components.formBuilder.stronglyAgree": "Strongly agree", "app.components.formBuilder.stronglyDisagree": "Strongly disagree", "app.components.formBuilder.supportArticleLinkText": "this page", "app.components.formBuilder.tags": "Tags", "app.components.formBuilder.title": "Title", "app.components.formBuilder.toLabel": "to", "app.components.formBuilder.unsavedChanges": "You have unsaved changes", "app.components.formBuilder.useCustomButton2": "Use custom page button", "app.components.formBuilder.veryBad": "Very bad", "app.components.formBuilder.veryGood": "Very good", "app.components.ideas.similarIdeas.engageHere": "Engage here", "app.components.ideas.similarIdeas.noSimilarSubmissions": "No similar submissions found.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "We found similar submisisons - engaging with them can help make them stronger!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Similar submissions already posted:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Looking for similar submissions ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {Less than a day} one {# day} other {# days}} left", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  weeks left", "app.components.screenReaderCurrency.AED": "United Arab Emirates Dirham", "app.components.screenReaderCurrency.AFN": "Afghan Afghani", "app.components.screenReaderCurrency.ALL": "Albanian Lek", "app.components.screenReaderCurrency.AMD": "Armenian Dram", "app.components.screenReaderCurrency.ANG": "Netherlands Antillean Guilder", "app.components.screenReaderCurrency.AOA": "Angolan <PERSON>", "app.components.screenReaderCurrency.ARS": "Argentine Peso", "app.components.screenReaderCurrency.AUD": "Australian Dollar", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Azerbaijani Manat", "app.components.screenReaderCurrency.BAM": "Bosnia-Herzegovina Convertible Mark", "app.components.screenReaderCurrency.BBD": "Barbadian Dollar", "app.components.screenReaderCurrency.BDT": "Bangladeshi Taka", "app.components.screenReaderCurrency.BGN": "Bulgarian Lev", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "Burundian Franc", "app.components.screenReaderCurrency.BMD": "Bermudian Dollar", "app.components.screenReaderCurrency.BND": "Brunei Dollar", "app.components.screenReaderCurrency.BOB": "Bolivian Boliviano", "app.components.screenReaderCurrency.BOV": "Bolivian M<PERSON>", "app.components.screenReaderCurrency.BRL": "Brazilian Real", "app.components.screenReaderCurrency.BSD": "Bahamian Dollar", "app.components.screenReaderCurrency.BTN": "Bhutanese Ngultrum", "app.components.screenReaderCurrency.BWP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BYR": "Belarusian Ruble", "app.components.screenReaderCurrency.BZD": "Belize Dollar", "app.components.screenReaderCurrency.CAD": "Canadian Dollar", "app.components.screenReaderCurrency.CDF": "Congolese Franc", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "Swiss Franc", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilean Unit of Account (UF)", "app.components.screenReaderCurrency.CLP": "Chilean Peso", "app.components.screenReaderCurrency.CNY": "Chinese Yuan", "app.components.screenReaderCurrency.COP": "Colombian Peso", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Costa Rican Colón", "app.components.screenReaderCurrency.CRE": "Credit", "app.components.screenReaderCurrency.CUC": "Cuban Convertible Peso", "app.components.screenReaderCurrency.CUP": "Cuban Peso", "app.components.screenReaderCurrency.CVE": "Cape Verdean Escudo", "app.components.screenReaderCurrency.CZK": "Czech Koruna", "app.components.screenReaderCurrency.DJF": "Djiboutian Franc", "app.components.screenReaderCurrency.DKK": "Danish Krone", "app.components.screenReaderCurrency.DOP": "Dominican Peso", "app.components.screenReaderCurrency.DZD": "Algerian Dinar", "app.components.screenReaderCurrency.EGP": "Egyptian Pound", "app.components.screenReaderCurrency.ERN": "Eritrean Nakfa", "app.components.screenReaderCurrency.ETB": "Ethiopian Birr", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "Fijian Dollar", "app.components.screenReaderCurrency.FKP": "Falkland Islands Pound", "app.components.screenReaderCurrency.GBP": "British Pound", "app.components.screenReaderCurrency.GEL": "Georgian Lari", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "Gibraltar Pound", "app.components.screenReaderCurrency.GMD": "Gambian Dalasi", "app.components.screenReaderCurrency.GNF": "Guinean Franc", "app.components.screenReaderCurrency.GTQ": "Guatemalan <PERSON>", "app.components.screenReaderCurrency.GYD": "Guyanese Dollar", "app.components.screenReaderCurrency.HKD": "Hong Kong Dollar", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HRK": "Croatian Kuna", "app.components.screenReaderCurrency.HTG": "Haitian Gourde", "app.components.screenReaderCurrency.HUF": "Hungarian Forint", "app.components.screenReaderCurrency.IDR": "Indonesian Rupiah", "app.components.screenReaderCurrency.ILS": "Israeli New <PERSON>", "app.components.screenReaderCurrency.INR": "Indian Rupee", "app.components.screenReaderCurrency.IQD": "Iraqi <PERSON>", "app.components.screenReaderCurrency.IRR": "Iranian Rial", "app.components.screenReaderCurrency.ISK": "Icelandic Króna", "app.components.screenReaderCurrency.JMD": "Jamaican Dollar", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "Japanese Yen", "app.components.screenReaderCurrency.KES": "Kenyan Shilling", "app.components.screenReaderCurrency.KGS": "Kyrgyzstani Som", "app.components.screenReaderCurrency.KHR": "Cambodian Riel", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KPW": "North Korean Won", "app.components.screenReaderCurrency.KRW": "South Korean Won", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "Cayman Islands Dollar", "app.components.screenReaderCurrency.KZT": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "Lebanese Pound", "app.components.screenReaderCurrency.LKR": "Sri Lankan Rupee", "app.components.screenReaderCurrency.LRD": "Liberian Dollar", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "Lithuanian Litas", "app.components.screenReaderCurrency.LVL": "Latvian Lats", "app.components.screenReaderCurrency.LYD": "Libyan Dinar", "app.components.screenReaderCurrency.MAD": "Moroccan <PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "Moldovan Leu", "app.components.screenReaderCurrency.MGA": "Malagasy Ariary", "app.components.screenReaderCurrency.MKD": "Macedonian Denar", "app.components.screenReaderCurrency.MMK": "Myanmar Kyat", "app.components.screenReaderCurrency.MNT": "Mongolian Tögrög", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauritanian Ouguiya", "app.components.screenReaderCurrency.MUR": "Mauritian Rupee", "app.components.screenReaderCurrency.MVR": "Maldivian Rufiyaa", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Mexican Peso", "app.components.screenReaderCurrency.MXV": "Mexican Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "Malaysian Ringgit", "app.components.screenReaderCurrency.MZN": "Mozambican Metical", "app.components.screenReaderCurrency.NAD": "Namibian Dollar", "app.components.screenReaderCurrency.NGN": "Nigerian Naira", "app.components.screenReaderCurrency.NIO": "Nicaraguan Córdoba", "app.components.screenReaderCurrency.NOK": "Norwegian Krone", "app.components.screenReaderCurrency.NPR": "Nepalese Rupee", "app.components.screenReaderCurrency.NZD": "New Zealand Dollar", "app.components.screenReaderCurrency.OMR": "Omani R<PERSON>", "app.components.screenReaderCurrency.PAB": "Panamanian Balboa", "app.components.screenReaderCurrency.PEN": "Peruvian Sol", "app.components.screenReaderCurrency.PGK": "Papua New Guinean Kina", "app.components.screenReaderCurrency.PHP": "Philippine Peso", "app.components.screenReaderCurrency.PKR": "Pakistani Rupee", "app.components.screenReaderCurrency.PLN": "Polish Złoty", "app.components.screenReaderCurrency.PYG": "Paraguayan Guaraní", "app.components.screenReaderCurrency.QAR": "Qatari Riyal", "app.components.screenReaderCurrency.RON": "Romanian Leu", "app.components.screenReaderCurrency.RSD": "Serbian Dinar", "app.components.screenReaderCurrency.RUB": "Russian Ruble", "app.components.screenReaderCurrency.RWF": "Rwandan <PERSON>", "app.components.screenReaderCurrency.SAR": "Saudi Riyal", "app.components.screenReaderCurrency.SBD": "Solomon Islands Dollar", "app.components.screenReaderCurrency.SCR": "Seychellois Rupee", "app.components.screenReaderCurrency.SDG": "Sudanese Pound", "app.components.screenReaderCurrency.SEK": "Swedish Krona", "app.components.screenReaderCurrency.SGD": "Singapore Dollar", "app.components.screenReaderCurrency.SHP": "<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leonean Leone", "app.components.screenReaderCurrency.SOS": "Somali Shilling", "app.components.screenReaderCurrency.SRD": "Surinamese Dollar", "app.components.screenReaderCurrency.SSP": "South Sudanese Pound", "app.components.screenReaderCurrency.STD": "São Tomé and Pr<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SYP": "Syrian Pound", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Thai Baht", "app.components.screenReaderCurrency.TJS": "<PERSON>i Somoni", "app.components.screenReaderCurrency.TMT": "Turkmenistani Manat", "app.components.screenReaderCurrency.TND": "Tunisian Dinar", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "Turkish Lira", "app.components.screenReaderCurrency.TTD": "Trinidad and Tobago Dollar", "app.components.screenReaderCurrency.TWD": "New Taiwan Dollar", "app.components.screenReaderCurrency.TZS": "Tanzanian <PERSON>", "app.components.screenReaderCurrency.UAH": "Ukrainian Hryvnia", "app.components.screenReaderCurrency.UGX": "Ugandan <PERSON>", "app.components.screenReaderCurrency.USD": "United States Dollar", "app.components.screenReaderCurrency.USN": "United States Dollar (Next day)", "app.components.screenReaderCurrency.USS": "United States Dollar (Same day)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Uruguayan Peso", "app.components.screenReaderCurrency.UZS": "Uzbekistani Som", "app.components.screenReaderCurrency.VEF": "Venezuelan Bolívar", "app.components.screenReaderCurrency.VND": "Vietnamese Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Central African CFA Franc", "app.components.screenReaderCurrency.XAG": "Silver (one troy ounce)", "app.components.screenReaderCurrency.XAU": "Gold (one troy ounce)", "app.components.screenReaderCurrency.XBA": "European Composite Unit (EURCO)", "app.components.screenReaderCurrency.XBB": "European Monetary Unit (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "European Unit of Account 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "European Unit of Account 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "East Caribbean Dollar", "app.components.screenReaderCurrency.XDR": "Special Drawing Rights", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "West African CFA Franc", "app.components.screenReaderCurrency.XPD": "Palladium (one troy ounce)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "Platinum (one troy ounce)", "app.components.screenReaderCurrency.XTS": "Codes specifically reserved for testing purposes", "app.components.screenReaderCurrency.XXX": "No currency", "app.components.screenReaderCurrency.YER": "Yemeni R<PERSON>", "app.components.screenReaderCurrency.ZAR": "South African Rand", "app.components.screenReaderCurrency.ZMW": "Zambian <PERSON>", "app.components.screenReaderCurrency.amount": "Amount", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON><PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "last quarter", "app.containers.AccessibilityStatement.applicability": "This accessibility statement applies to a {demoPlatformLink} that is representative of this website; it uses the same source code and has the same functionality.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Assessment method", "app.containers.AccessibilityStatement.assesmentText2022": "The accessibility of this site was evaluated by an external entity not involved in the design and development process. The compliance of the forementioned {demoPlatformLink} can be identified on this {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "you can change your preferences", "app.containers.AccessibilityStatement.changePreferencesText": "At any time, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Conformance exceptions", "app.containers.AccessibilityStatement.conformanceStatus": "Conformance status", "app.containers.AccessibilityStatement.contentConformanceExceptions": "We strive to make our content inclusive for all. However, in some instances there may be inaccessible content on the platform as outlined below:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "demo website", "app.containers.AccessibilityStatement.email": "Email:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Embedded survey tools", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "The embedded survey tools that are available for use on this platform are third-party software and may not be accessible.", "app.containers.AccessibilityStatement.exception_1": "Our digital engagement platforms facilitate user-generated content posted by individuals and organizations. It is possible that PDFs, images or other file types including multi-media are uploaded to the platform as attachments or added into text fields by platform users. These documents may not be fully accessible.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "We welcome your feedback on the accessibility of this site. Please contact us via one of the following methods:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Feedback process", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Brussels, Belgium", "app.containers.AccessibilityStatement.headTitle": "Accessibility Statement | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} is committed to providing a platform that is accessible to all users, regardless of technology or ability. Current relevant accessibility standards are adhered to in our on-going efforts to maximise the accessibility and usability of our platforms for all users.", "app.containers.AccessibilityStatement.mapping": "Mapping", "app.containers.AccessibilityStatement.mapping_1": "Maps on the platform partially meet accessibility standards. Map extent, zoom, and UI widgets can be controlled using a keyboard when viewing maps. Admins can also configure the style of map layers in the back office, or using the Esri integration, to create more accessible colour palettes and symbology. Using different line or polygon styles (e.g. dashed lines) will also help differentiate map layers wherever possible, and although such styling cannot be configured within our platform at this time, it can be configured if using maps with the Esri integration.", "app.containers.AccessibilityStatement.mapping_2": "Maps in the platform are not fully accessible as they do not audibly present basemaps, map layers, or trends in the data to users utilizing screen readers. Fully accessible maps would need to audibly present the map layers and describe any relevant trends in the data. Furthermore, line and polygon map drawing in surveys is not accessible as shapes cannot be drawn using a keyboard. Alternative input methods are not available at this time due to technical complexity.", "app.containers.AccessibilityStatement.mapping_3": "To make line and polygon map drawing more accessible, we recommend including an introduction or explanation in the survey question or page description of what the map is showing and any relevant trends. Furthermore, a short or long answer text question could be provided so respondents can describe their answer in plain terms if needed (rather than clicking on the map). We also recommend including contact information for the project manager so respondents who cannot fill in a map question can request an alternative method to answer the question (E.g. Video meeting).", "app.containers.AccessibilityStatement.mapping_4": "For Ideation projects and proposals, there is an option to display inputs in a map view, which is not accessible. However, for these methods there is an alternative list view of inputs available, which is accessible.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Our online workshops have a live video streaming component, which does not currently support subtitles.", "app.containers.AccessibilityStatement.pageDescription": "A statement on the accessibility of this website", "app.containers.AccessibilityStatement.postalAddress": "Postal address:", "app.containers.AccessibilityStatement.publicationDate": "Publication date", "app.containers.AccessibilityStatement.publicationDate2024": "This accessibility statement was published on August 21, 2024.", "app.containers.AccessibilityStatement.responsiveness": "We aim to respond to feedback within 1-2 business days.", "app.containers.AccessibilityStatement.statusPageText": "status page", "app.containers.AccessibilityStatement.technologiesIntro": "The accessibility of this site relies on the following technologies to work:", "app.containers.AccessibilityStatement.technologiesTitle": "Technologies", "app.containers.AccessibilityStatement.title": "Accessibility Statement", "app.containers.AccessibilityStatement.userGeneratedContent": "User-generated content", "app.containers.AccessibilityStatement.workshops": "Workshops", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "Select project", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Using the Content Builder will let you use more advanced layout options. For languages where no content is available in the content builder, the regular project description content will be displayed instead.", "app.containers.AdminPage.ProjectDescription.linkText": "Edit description in Content Builder", "app.containers.AdminPage.ProjectDescription.saveError": "Something went wrong while saving the project description.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Use Content Builder for description", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Using the Content Builder will let you use more advanced layout options.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "View project", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "Survey end", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Create a smart group", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Users matching all of the following conditions will be automatically added to the group:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Provide at least one rule", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Some conditions are incomplete", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Save group", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Configuring smart groups is not part of your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Provide a group name", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Verification is disabled for your platform, remove the verification rule or contact support.", "app.containers.App.appMetaDescription": "Welcome to the online participation platform of {orgName}. \nExplore local projects and engage in the discussion!", "app.containers.App.loading": "Loading...", "app.containers.App.metaTitle1": "Citizen engagement platform | {orgName}", "app.containers.App.skipLinkText": "Skip to main content", "app.containers.AreaTerms.areaTerm": "area", "app.containers.AreaTerms.areasTerm": "areas", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "An account with this email already exists. You can sign out, log in with this email address and verify your account on the settings page.", "app.containers.Authentication.steps.AccessDenied.close": "Close", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "You do not meet the requirements to participate in this process.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Go back to single sign-on verification", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "Please enter a token", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "Already have an account? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "Log in", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Emails in this category", "app.containers.CampaignsConsentForm.messageError": "There was an error saving your email preferences.", "app.containers.CampaignsConsentForm.messageSuccess": "Your email preferences have been saved.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "What kinds of email notifications do you want to receive? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Notifications", "app.containers.CampaignsConsentForm.submit": "Save", "app.containers.ChangeEmail.backToProfile": "Back to profile settings", "app.containers.ChangeEmail.confirmationModalTitle": "Confirm your email", "app.containers.ChangeEmail.emailEmptyError": "Provide an e-mail address", "app.containers.ChangeEmail.emailInvalidError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.ChangeEmail.emailRequired": "Please enter an email address.", "app.containers.ChangeEmail.emailTaken": "This email is already in use.", "app.containers.ChangeEmail.emailUpdateCancelled": "Email update has been cancelled.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "To update your email, please restart the process.", "app.containers.ChangeEmail.helmetDescription": "Change your email page", "app.containers.ChangeEmail.helmetTitle": "Change your email", "app.containers.ChangeEmail.newEmailLabel": "New email", "app.containers.ChangeEmail.submitButton": "Submit", "app.containers.ChangeEmail.titleAddEmail": "Add your email", "app.containers.ChangeEmail.titleChangeEmail": "Change your email", "app.containers.ChangeEmail.updateSuccessful": "Your email has been successfully updated.", "app.containers.ChangePassword.currentPasswordLabel": "Current password", "app.containers.ChangePassword.currentPasswordRequired": "Enter your current password", "app.containers.ChangePassword.goHome": "Go to home", "app.containers.ChangePassword.helmetDescription": "Change your password page", "app.containers.ChangePassword.helmetTitle": "Change your password", "app.containers.ChangePassword.newPasswordLabel": "New password", "app.containers.ChangePassword.newPasswordRequired": "Enter your new password", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Provide a password that is at least {minimumPasswordLength} characters long", "app.containers.ChangePassword.passwordChangeSuccessMessage": "Your password has been successfully updated", "app.containers.ChangePassword.passwordEmptyError": "Enter your password", "app.containers.ChangePassword.passwordsDontMatch": "Confirm new password", "app.containers.ChangePassword.titleAddPassword": "Add a password", "app.containers.ChangePassword.titleChangePassword": "Change your password", "app.containers.Comments.a11y_commentDeleted": "Comment deleted", "app.containers.Comments.a11y_commentPosted": "Comment posted", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {no likes} one {1 like} other {# likes}}", "app.containers.Comments.a11y_undoLike": "Undo like", "app.containers.Comments.addCommentError": "Something went wrong. Please try again later.", "app.containers.Comments.adminCommentDeletionCancelButton": "Cancel", "app.containers.Comments.adminCommentDeletionConfirmButton": "Delete this comment", "app.containers.Comments.cancelCommentEdit": "Cancel", "app.containers.Comments.childCommentBodyPlaceholder": "Write a reply...", "app.containers.Comments.commentCancelUpvote": "Undo", "app.containers.Comments.commentDeletedPlaceholder": "This comment has been deleted.", "app.containers.Comments.commentDeletionCancelButton": "Keep my comment", "app.containers.Comments.commentDeletionConfirmButton": "Delete my comment", "app.containers.Comments.commentLike": "Like", "app.containers.Comments.commentReplyButton": "Reply", "app.containers.Comments.commentsSortTitle": "Sort comments by", "app.containers.Comments.completeProfileLinkText": "complete your profile", "app.containers.Comments.completeProfileToComment": "Please {completeRegistrationLink} to comment.", "app.containers.Comments.confirmCommentDeletion": "Are you sure you want to delete this comment? There's no turning back!", "app.containers.Comments.deleteComment": "Delete", "app.containers.Comments.deleteReasonDescriptionError": "Provide more information on your reason", "app.containers.Comments.deleteReasonError": "Provide a reason", "app.containers.Comments.deleteReason_inappropriate": "It is inappropriate or offensive", "app.containers.Comments.deleteReason_irrelevant": "This is not relevant", "app.containers.Comments.deleteReason_other": "Other reason", "app.containers.Comments.editComment": "Edit", "app.containers.Comments.guidelinesLinkText": "our community guidelines", "app.containers.Comments.ideaCommentBodyPlaceholder": "Write your comment here", "app.containers.Comments.internalCommentingNudgeMessage": "Making internal comments is not included in your current license. Reach out to your GovSuccess Manager to learn more about it.", "app.containers.Comments.internalConversation": "Internal conversation", "app.containers.Comments.loadMoreComments": "Load more comments", "app.containers.Comments.loadingComments": "Loading comments...", "app.containers.Comments.loadingMoreComments": "Loading more comments...", "app.containers.Comments.notVisibleToUsersPlaceholder": "This comment is not visible to regular users", "app.containers.Comments.postInternalComment": "Post internal comment", "app.containers.Comments.postPublicComment": "Post public comment", "app.containers.Comments.profanityError": "Oops! It looks like your post contains some language that doesn’t meet {guidelinesLink}. We try to keep this a safe space for everyone. Please edit your input and try again.", "app.containers.Comments.publicDiscussion": "Public discussion", "app.containers.Comments.publishComment": "Post your comment", "app.containers.Comments.reportAsSpamModalTitle": "Why do you want to report this as spam?", "app.containers.Comments.saveComment": "Save", "app.containers.Comments.signInLinkText": "log in", "app.containers.Comments.signInToComment": "Please {signInLink} to comment.", "app.containers.Comments.signUpLinkText": "sign up", "app.containers.Comments.verifyIdentityLinkText": "Verify your identity", "app.containers.Comments.visibleToUsersPlaceholder": "This comment is visible to regular users", "app.containers.Comments.visibleToUsersWarning": "Comments posted here will be visible to regular users.", "app.containers.ContentBuilder.PageTitle": "Project description", "app.containers.CookiePolicy.advertisingContent": "Advertising cookies may be used to personalize and measure the effectiveness that external marketing campaigns have on engagement with this platform. We will not show any advertising on this platform, but you may receive personalised ads based on the pages you visit.", "app.containers.CookiePolicy.advertisingTitle": "Advertising", "app.containers.CookiePolicy.analyticsContents": "Analytics cookies track visitor behaviour, such as which pages are visited and for how long. They may also collect some technical data including browser information, approximate location and IP addresses. We only use this data internally to continue to improve the overall user experience and functioning of the platform. Such data may also be shared between Go Vocal and {orgName} to assess and improve engagement with projects on the platform. Note that the data is anonymous and used at an aggregated level - it does not identify you personally. However, it is possible that if this data were to be combined with other data sources, such identification could occur.", "app.containers.CookiePolicy.analyticsTitle": "Analytics cookies", "app.containers.CookiePolicy.cookiePolicyDescription": "A detailed explanation of how we use cookies on this platform", "app.containers.CookiePolicy.cookiePolicyTitle": "Cookie policy", "app.containers.CookiePolicy.essentialContent": "Some cookies are essential to ensure the proper functioning of this platform. These essential cookies are primarily used to authenticate your account when you visit the platform and to save your preferred language.", "app.containers.CookiePolicy.essentialTitle": "Essential cookies", "app.containers.CookiePolicy.externalContent": "Some of our pages may display content from external providers, e.g., YouTube or Typeform. We do not have control over these third-party cookies and viewing the content from these external providers may also result in cookies being installed on your device.", "app.containers.CookiePolicy.externalTitle": "External cookies", "app.containers.CookiePolicy.functionalContents": "Functional cookies may be enabled for visitors to receive notifications about updates and to access support channels directly from the platform.", "app.containers.CookiePolicy.functionalTitle": "Functional cookies", "app.containers.CookiePolicy.headCookiePolicyTitle": "Cookie Policy | {orgName}", "app.containers.CookiePolicy.intro": "Cookies are text files stored on the browser or on the hard drive of your computer or mobile device when you visit a website and which may be referenced by the website during subsequent visits. We use cookies to understand how visitors are using this platform to improve its design and experience, to remember your preferences (such as your preferred language) and to support key functions for registered users and platform administrators.", "app.containers.CookiePolicy.manageCookiesDescription": "You can enable or disable analytics, marketing and functional cookies at any time in your cookie preferences. You can also manually or automatically delete any existing cookies via your internet browser. However, the cookies may be placed again after your consent upon any subsequent visits to this platform. If you do not delete the cookies, your cookie preferences are stored for 60 days, after which you will be asked again for your consent.", "app.containers.CookiePolicy.manageCookiesPreferences": "Go to your {manageCookiesPreferencesButtonText} to see a full list of 3rd party integrations used on this platform and to manage your preferences.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "cookie settings", "app.containers.CookiePolicy.manageCookiesTitle": "Managing your cookies", "app.containers.CookiePolicy.viewPreferencesButtonText": "<PERSON><PERSON>", "app.containers.CookiePolicy.viewPreferencesText": "The below cookie categories may not apply to all visitors or platforms; view your {viewPreferencesButton} for a full list of 3rd party integrations applicable to you.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "What do we use cookies for?", "app.containers.CustomPageShow.editPage": "Edit page", "app.containers.CustomPageShow.goBack": "Go back", "app.containers.CustomPageShow.notFound": "Page not found", "app.containers.DisabledAccount.bottomText": "You can sign in again from {date}.", "app.containers.DisabledAccount.termsAndConditions": "terms & conditions", "app.containers.DisabledAccount.text2": "Your account on the participation platform of {orgName} has been temporarily disabled for a violation of community guidelines. For more information on this, you can consult the {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Your account has been temporarily disabled", "app.containers.EventsShow.addToCalendar": "Add to calendar", "app.containers.EventsShow.editEvent": "Edit event", "app.containers.EventsShow.emailSharingBody2": "Attend this event: {eventTitle}. Read more at {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Event date and time", "app.containers.EventsShow.eventFrom2": "From \"{projectTitle}\"", "app.containers.EventsShow.goBack": "Go back", "app.containers.EventsShow.goToProject": "Go to the project", "app.containers.EventsShow.haveRegistered": "have registered", "app.containers.EventsShow.icsError": "Error downloading the ICS file", "app.containers.EventsShow.linkToOnlineEvent": "Link to online event", "app.containers.EventsShow.locationIconAltText": "Location", "app.containers.EventsShow.metaTitle": "Event: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Online meeting", "app.containers.EventsShow.onlineLinkIconAltText": "Online meeting link", "app.containers.EventsShow.registered": "registered", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 registrants} one {1 registrant} other {# registrants}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} registrants", "app.containers.EventsShow.registrantsIconAltText": "Registrants", "app.containers.EventsShow.socialMediaSharingMessage": "Attend this event: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# participant} other {# participants}}", "app.containers.EventsViewer.allTime": "All time", "app.containers.EventsViewer.date": "Date", "app.containers.EventsViewer.thisMonth2": "Upcoming month", "app.containers.EventsViewer.thisWeek2": "Upcoming week", "app.containers.EventsViewer.today": "Today", "app.containers.IdeaButton.addAContribution": "Add a contribution", "app.containers.IdeaButton.addAPetition": "Add a petition", "app.containers.IdeaButton.addAProject": "Add a project", "app.containers.IdeaButton.addAProposal": "Add a proposal", "app.containers.IdeaButton.addAQuestion": "Add a question", "app.containers.IdeaButton.addAnInitiative": "Add an initiative", "app.containers.IdeaButton.addAnOption": "Add an option", "app.containers.IdeaButton.postingDisabled": "New submissions are not currently being accepted", "app.containers.IdeaButton.postingInNonActivePhases": "New submissions can only be added in active phases.", "app.containers.IdeaButton.postingInactive": "New submissions are not currently being accepted.", "app.containers.IdeaButton.postingLimitedMaxReached": "You have already completed this survey. Thanks for your response!", "app.containers.IdeaButton.postingNoPermission": "New submissions are not currently being accepted", "app.containers.IdeaButton.postingNotYetPossible": "New submissions are not yet being accepted.", "app.containers.IdeaButton.signInLinkText": "log in", "app.containers.IdeaButton.signUpLinkText": "sign up", "app.containers.IdeaButton.submitAnIssue": "Submit a comment", "app.containers.IdeaButton.submitYourIdea": "Submit your idea", "app.containers.IdeaButton.takeTheSurvey": "Take the survey", "app.containers.IdeaButton.verificationLinkText": "Verify your identity now.", "app.containers.IdeaCard.readMore": "Read more", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {no comments} one {1 comment} other {# comments}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {no votes} one {1 vote} other {# votes}} out of {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Close filters panel", "app.containers.IdeaCards.a11y_totalItems": "Total posts: {ideasCount}", "app.containers.IdeaCards.all": "All", "app.containers.IdeaCards.allStatuses": "All statuses", "app.containers.IdeaCards.contributions": "Contributions", "app.containers.IdeaCards.ideaTerm": "Ideas", "app.containers.IdeaCards.initiatives": "Initiatives", "app.containers.IdeaCards.issueTerm": "Issues", "app.containers.IdeaCards.list": "List", "app.containers.IdeaCards.map": "Map", "app.containers.IdeaCards.mostDiscussed": "Most discussed", "app.containers.IdeaCards.newest": "Most recent", "app.containers.IdeaCards.noFilteredResults": "No results found. Please try a different filter or search term.", "app.containers.IdeaCards.numberResults": "Results ({postCount})", "app.containers.IdeaCards.oldest": "Oldest", "app.containers.IdeaCards.optionTerm": "Options", "app.containers.IdeaCards.petitions": "Petitions", "app.containers.IdeaCards.popular": "Most voted", "app.containers.IdeaCards.projectFilterTitle": "Projects", "app.containers.IdeaCards.projectTerm": "Projects", "app.containers.IdeaCards.proposals": "Proposals", "app.containers.IdeaCards.questionTerm": "Questions", "app.containers.IdeaCards.random": "Random", "app.containers.IdeaCards.resetFilters": "Reset filters", "app.containers.IdeaCards.showXResults": "Show {ideasCount, plural, one {# result} other {# results}}", "app.containers.IdeaCards.sortTitle": "Sorting", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "Tags", "app.containers.IdeaCards.topicsTitle": "Tags", "app.containers.IdeaCards.trending": "Trending", "app.containers.IdeaCards.tryDifferentFilters": "No results found. Please try a different filter or search term.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} comments} one {{ideasCount} comment} other {{ideasCount} comments}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} contributions} one {{ideasCount} contribution} other {{ideasCount} contributions}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} ideas} one {{ideasCount} idea} other {{ideasCount} ideas}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} initiatives} one {{ideasCount} initiative} other {{ideasCount} initiatives}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} options} one {{ideasCount} option} other {{ideasCount} options}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petitions} one {{ideasCount} petition} other {{ideasCount} petitions}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projects} one {{ideasCount} project} other {{ideasCount} projects}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} proposals} one {{ideasCount} proposal} other {{ideasCount} proposals}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} questions} one {{ideasCount} question} other {{ideasCount} questions}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# result} other {# results}}", "app.containers.IdeasEditPage.contributionFormTitle": "Edit contribution", "app.containers.IdeasEditPage.editedPostSave": "Save", "app.containers.IdeasEditPage.fileUploadError": "One or more files failed to upload. Please check the file size and format and try again.", "app.containers.IdeasEditPage.formTitle": "Edit idea", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Edit your post. Add new and change old information.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Edit {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "Edit initiative", "app.containers.IdeasEditPage.issueFormTitle": "Edit comment", "app.containers.IdeasEditPage.optionFormTitle": "Edit option", "app.containers.IdeasEditPage.petitionFormTitle": "Edit petition", "app.containers.IdeasEditPage.projectFormTitle": "Edit project", "app.containers.IdeasEditPage.proposalFormTitle": "Edit proposal", "app.containers.IdeasEditPage.questionFormTitle": "Edit question", "app.containers.IdeasEditPage.save": "Save", "app.containers.IdeasEditPage.submitApiError": "There was an issue submitting the form. Please check for any errors and try again.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "All inputs posted", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Explore all input posted on the participation platform of {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Posts | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "Posts", "app.containers.IdeasIndexPage.loadMore": "Load more...", "app.containers.IdeasIndexPage.loading": "Loading...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "By default your submissions will be associated with your profile, unless you select this option.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "Post anonymously", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "Profile visibility", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "This survey is not currently open for responses. Please return to the project for more information.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "This survey is not currently active.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Return to project", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "You have already completed this survey.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "Survey submitted", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Thanks for your response!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "The contribution description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "The idea body must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "The contribution title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "The contribution title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Please select at least one cosponsor", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "The idea description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "The idea description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "Please provide a description", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "The idea title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "The idea title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "The initiative description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "The initiative description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "The initiative title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "The initiative title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "The issue description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "The issue description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "The issue title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "The issue title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_number_required": "This field is required, please enter a valid number", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "The option description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "The option description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "The option title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "The option title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Please select at least one tag", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "The petition description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "The petition description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "The petition title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "The petition title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "The project description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "The project description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "The project title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "The project title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "The proposal description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "The proposal description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "The proposal title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "The proposal title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Please enter a number", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Please enter a number", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "The question description must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "The question description must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "The question title must be less than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "The question title must be more than {limit} characters long", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "Please provide a title", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "The contribution description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "The contribution description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "The contribution title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "The contribution title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "The idea description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "The idea description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "Please provide a title", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "The idea title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "The idea title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_includes_banned_words": "You may have used one or more words that are considered profanity by {guidelinesLink}. Please alter your text to remove any profanities that might be present.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "The initiative description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "The initiative description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "The initiative title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "The initiative title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "The issue description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "The issue description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "The issue title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "The issue title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "The option description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "The option description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "The option title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "The option title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "The petition description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "The petition description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "The petition title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "The petition title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "The project description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "The project description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "The project title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "The project title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "The proposal description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "The proposal description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "The proposal title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "The proposal title must be at least 10 characters long", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "Please provide a description", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "The question description must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "The question description must be at least 30 characters long", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "The question title must be less than 80 characters long", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "The question title must be at least 10 characters long", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "Cancel", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.IdeasNewPage.contributionMetaTitle1": "Add new contribution to project | {orgName}", "app.containers.IdeasNewPage.editSurvey": "Edit survey", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Post a submission and join the conversation at {orgName}'s participation platform.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Add new idea to project | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Add new initiative to project | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Add new issue to project | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Your draft answers have been saved privately and you can return to complete this later.", "app.containers.IdeasNewPage.leaveSurvey": "Leave survey", "app.containers.IdeasNewPage.leaveSurveyText": "Your answers won't be saved.", "app.containers.IdeasNewPage.optionMetaTitle1": "Add new option to project | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "Add new petition to project | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Add new project to project | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Add new proposal to project | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Add new question to project | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Accept invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Co-sponsorship invitation", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Co-sponsors", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "You have been invited to become a co-sponsor.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Invitation accepted", "app.containers.IdeasShow.Cosponsorship.pending": "pending", "app.containers.IdeasShow.MetaInformation.attachments": "Attachments", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} on {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Current status", "app.containers.IdeasShow.MetaInformation.location": "Location", "app.containers.IdeasShow.MetaInformation.postedBy": "Posted by", "app.containers.IdeasShow.MetaInformation.similar": "Similar inputs", "app.containers.IdeasShow.MetaInformation.topics": "Tags", "app.containers.IdeasShow.commentCTA": "Add a comment", "app.containers.IdeasShow.contributionEmailSharingBody": "Support this contribution '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "Support this contribution: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Thanks for submitting your contribution!", "app.containers.IdeasShow.contributionTwitterMessage": "Support this contribution: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "Support this contribution: {postTitle}", "app.containers.IdeasShow.currentStatus": "Current status", "app.containers.IdeasShow.deletedUser": "unknown author", "app.containers.IdeasShow.ideaEmailSharingBody": "Support my idea '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "Support my idea: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Support this idea: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Support this idea: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "Support this comment: {postTitle}", "app.containers.IdeasShow.imported": "Imported", "app.containers.IdeasShow.importedTooltip": "This {inputTerm} was collected offline and automatically uploaded to the platform.", "app.containers.IdeasShow.initiativeEmailSharingBody": "Support this initiative '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "Support this initiative: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Thanks for submitting your initiative!", "app.containers.IdeasShow.initiativeTwitterMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "Support this initiative: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "Support this comment '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "Support this comment: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Thanks for submitting your comment!", "app.containers.IdeasShow.issueTwitterMessage": "Support this comment: {postTitle}", "app.containers.IdeasShow.metaTitle": "Input: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "Support this option '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "Support this option: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Your option has been successfully posted!", "app.containers.IdeasShow.optionTwitterMessage": "Support this option: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "Support this option: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "Support this petition '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "Support this petition: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Thanks for submitting your petition!", "app.containers.IdeasShow.petitionTwitterMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "Support this petition: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Support this project '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Support this project: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Thanks for submitting your project!", "app.containers.IdeasShow.projectTwitterMessage": "Support this project: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Support this project: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "Support this proposal '{ideaTitle}' at {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "Support this proposal: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Thanks for submitting your proposal!", "app.containers.IdeasShow.proposalTwitterMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "Support this proposal: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Time left to vote:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} out of {votingThreshold} required votes", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "Cancel vote", "app.containers.IdeasShow.proposals.VoteControl.days": "days", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "our guidelines", "app.containers.IdeasShow.proposals.VoteControl.hours": "hours", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status and votes", "app.containers.IdeasShow.proposals.VoteControl.minutes": "mins", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "More info", "app.containers.IdeasShow.proposals.VoteControl.vote": "Vote", "app.containers.IdeasShow.proposals.VoteControl.voted": "Voted", "app.containers.IdeasShow.proposals.VoteControl.votedText": "You'll get notified when this initiative goes to the next step. {x, plural, =0 {There's {xDays} left.} one {There's {xDays} left.} other {There are {xDays} left.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Your vote has been submitted!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "Unfortunately, you cannot vote on this proposal. Read why in {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {less than a day} one {one day} other {# days}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {no votes} one {1 vote} other {# votes}}", "app.containers.IdeasShow.questionEmailSharingBody": "Join the discussion about this question '{postTitle}' at {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "Join the discussion: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Your question has been successfully posted!", "app.containers.IdeasShow.questionTwitterMessage": "Join the discussion: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "Join the discussion: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Why do you want to report this as spam?", "app.containers.IdeasShow.share": "Share", "app.containers.IdeasShow.sharingModalSubtitle": "Reach more people and make your voice heard.", "app.containers.IdeasShow.sharingModalTitle": "Thanks for submitting your idea!", "app.containers.Navbar.completeOnboarding": "Complete onboarding", "app.containers.Navbar.completeProfile": "Complete profile", "app.containers.Navbar.confirmEmail2": "Confirm email", "app.containers.Navbar.unverified": "Unverified", "app.containers.Navbar.verified": "Verified", "app.containers.NewAuthModal.beforeYouFollow": "Before you follow", "app.containers.NewAuthModal.beforeYouParticipate": "Before you participate", "app.containers.NewAuthModal.completeYourProfile": "Complete your profile", "app.containers.NewAuthModal.confirmYourEmail": "Confirm your email", "app.containers.NewAuthModal.logIn": "Log in", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Review the terms below to continue.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "Please complete your profile.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Go back to login options", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Don't have an account? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Sign up", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Code must have 4 digits.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Continue with FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "No authentication methods are enabled on this platform.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "By continuing, you agree to receive emails from this platform. You can select which emails you wish to receive in the 'My Settings' page.", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Email", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Provide an email address", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Enter your email address to continue.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "Forgot password?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Log in to your account: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "Please enter your password", "app.containers.NewAuthModal.steps.Password.password": "Password", "app.containers.NewAuthModal.steps.Password.rememberMe": "Remember me", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Do not select if using a public computer", "app.containers.NewAuthModal.steps.Success.allDone": "All done", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Now continue your participation.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Your identity has been verified. You're now a full member of the community on this platform.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "You're now verified !", "app.containers.NewAuthModal.steps.close": "Close", "app.containers.NewAuthModal.steps.continue": "Continue", "app.containers.NewAuthModal.whatAreYouInterestedIn": "What are you interested in?", "app.containers.NewAuthModal.youCantParticipate": "You can't participate", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {no unviewed notifications} one {1 unviewed notification} other {# unviewed notifications}}", "app.containers.NotificationMenu.adminRightsReceived": "You're now an administrator of the platform", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Your comment on \"{postTitle}\" has been deleted by an admin because\n      {reasonCode, select, irrelevant {it is irrelevant} inappropriate {its content is inappropriate} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} accepted your co-sponsorship invitation", "app.containers.NotificationMenu.deletedUser": "Unknown author", "app.containers.NotificationMenu.error": "Couldn't load notifications", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} commented internally on an input assigned to you", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} commented internally on an input that you commented on internally", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} commented internally on an input in a project you manage", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} commented internally on an unassigned input in an unmanaged project", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} commented on your internal comment", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} invited you to co-sponsor a contribution", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} invited you to co-sponsor an idea", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} invited you to co-sponsor an initiative", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} invited you to co-sponsor an issue", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} invited you to co-sponsor an option", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} invited you to co-sponsor a petition", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} invited you to co-sponsor a project", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} invited you to co-sponsor a proposal", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} invited you to co-sponsor a question", "app.containers.NotificationMenu.loadMore": "Load more...", "app.containers.NotificationMenu.loading": "Loading notifications...", "app.containers.NotificationMenu.mentionInComment": "{name} mentioned you in a comment", "app.containers.NotificationMenu.mentionInInternalComment": "{name} mentioned you in an internal comment", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} mentioned you in an official update", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "You didn't submit your survey", "app.containers.NotificationMenu.noNotifications": "You don't have any notifications yet", "app.containers.NotificationMenu.notificationsLabel": "Notifications", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} gave an official update on a contribution you follow", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} gave an official update on an idea you follow", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} gave an official update on an initiative you follow", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} gave an official update on an issue you follow", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} gave an official update on an option you follow", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} gave an official update on a petition you follow", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} gave an official update on a project you follow", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} gave an official update on a proposal you follow", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} gave an official update on a question you follow", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} was assigned to you", "app.containers.NotificationMenu.projectModerationRightsReceived": "You're now a project manager of {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} entered a new phase", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} will enter a new phase on {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "A new project was published", "app.containers.NotificationMenu.projectReviewRequest": "{name} requested approval to publish the project \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} approved \"{projectTitle}\" for publication", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} status has changed to {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} reached the voting threshold", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} accepted your invitation", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} commented on a contribution that you follow", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} commented on an idea that you follow", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} commented on an initiative that you follow", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} commented on a issue that you follow", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} commented on an option that you follow", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} commented on a petition that you follow", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} commented on a project that you follow", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} commented on a proposal that you follow", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} commented on a question that you follow", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} reported \"{postTitle}\" as spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} reacted to your comment", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} reported a comment on \"{postTitle}\" as spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "You didn't submit your votes", "app.containers.NotificationMenu.votingBasketSubmitted": "You voted successfully", "app.containers.NotificationMenu.votingLastChance": "Last chance to vote for {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} vote results revealed", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} assigned {postTitle} to you", "app.containers.PasswordRecovery.emailError": "This doesn't look like a valid email", "app.containers.PasswordRecovery.emailLabel": "Email", "app.containers.PasswordRecovery.emailPlaceholder": "My email address", "app.containers.PasswordRecovery.helmetDescription": "Reset your password page", "app.containers.PasswordRecovery.helmetTitle": "Reset your password", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "If this email address is registered on the platform, a password reset link has been sent.", "app.containers.PasswordRecovery.resetPassword": "Send a password reset link", "app.containers.PasswordRecovery.submitError": "We couldn't find an account linked to this email. You can try to sign up instead.", "app.containers.PasswordRecovery.subtitle": "Where can we send a link to choose a new password?", "app.containers.PasswordRecovery.title": "Password reset", "app.containers.PasswordReset.helmetDescription": "Reset your password page", "app.containers.PasswordReset.helmetTitle": "Reset your password", "app.containers.PasswordReset.login": "Log in", "app.containers.PasswordReset.passwordError": "The password must be at least 8 characters long", "app.containers.PasswordReset.passwordLabel": "Password", "app.containers.PasswordReset.passwordPlaceholder": "New password", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "Your password has been successfully updated.", "app.containers.PasswordReset.pleaseLogInMessage": "Please log in with your new password.", "app.containers.PasswordReset.requestNewPasswordReset": "Request a new password reset", "app.containers.PasswordReset.submitError": "Something went wrong. Please try again later.", "app.containers.PasswordReset.title": "Reset your password", "app.containers.PasswordReset.updatePassword": "Confirm new password", "app.containers.ProjectFolderCards.allProjects": "All projects", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} is currently working on", "app.containers.ProjectFolderShowPage.editFolder": "Edit folder", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Information about this project", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Read more", "app.containers.ProjectFolderShowPage.seeLess": "See less", "app.containers.ProjectFolderShowPage.share": "Share", "app.containers.Projects.PollForm.document": "Document", "app.containers.Projects.PollForm.formCompleted": "Thank you! Your response has been received.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "You've already taken this poll.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "This poll can only be taken when this phase is active.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "This poll is not currently enabled", "app.containers.Projects.PollForm.pollDisabledNotPossible": "It is currently impossible to take this poll.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "The poll is no longer available since this project is no longer active.", "app.containers.Projects.PollForm.sendAnswer": "Send", "app.containers.Projects.a11y_phase": "Phase {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Phases overview", "app.containers.Projects.a11y_titleInputs": "All input submitted to this project", "app.containers.Projects.a11y_titleInputsPhase": "All input submitted in this phase", "app.containers.Projects.accessRights": "Access rights", "app.containers.Projects.addedToBasket": "Added to basket", "app.containers.Projects.allocateBudget": "Allocate your budget", "app.containers.Projects.archived": "Archived", "app.containers.Projects.basketSubmitted": "The basket has been submitted!", "app.containers.Projects.contributions": "Contributions", "app.containers.Projects.createANewPhase": "Create a new phase", "app.containers.Projects.currentPhase": "Current phase", "app.containers.Projects.document": "Document", "app.containers.Projects.editProject": "Edit project", "app.containers.Projects.emailSharingBody": "What do you think of this initiative? Vote on it and share the discussion at {initiativeUrl} to make your voice heard!", "app.containers.Projects.emailSharingSubject": "Support my initiative: {initiativeTitle}.", "app.containers.Projects.endedOn": "Ended on {date}", "app.containers.Projects.events": "Events", "app.containers.Projects.header": "Projects", "app.containers.Projects.ideas": "Ideas", "app.containers.Projects.information": "Information", "app.containers.Projects.initiatives": "Initiatives", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Review the document", "app.containers.Projects.invisibleTitlePhaseAbout": "About this phase", "app.containers.Projects.invisibleTitlePoll": "Take the poll", "app.containers.Projects.invisibleTitleSurvey": "Take the survey", "app.containers.Projects.issues": "Comments", "app.containers.Projects.liveDataMessage": "You're viewing real-time data. Participant counts are continuously updated for administrators. Please note that regular users see cached data, which may result in slight differences in the numbers.", "app.containers.Projects.location": "Location:", "app.containers.Projects.manageBasket": "Manage basket", "app.containers.Projects.meetMinBudgetRequirement": "Meet the minimum budget to submit the basket.", "app.containers.Projects.meetMinSelectionRequirement": "Meet the minimum budget to submit the basket.", "app.containers.Projects.metaTitle1": "Project: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "Minimum budget required", "app.containers.Projects.myBasket": "Basket", "app.containers.Projects.navPoll": "Poll", "app.containers.Projects.navSurvey": "Survey", "app.containers.Projects.newPhase": "New phase", "app.containers.Projects.nextPhase": "Next phase", "app.containers.Projects.noEndDate": "No end date", "app.containers.Projects.noItems": "You haven't selected any items yet", "app.containers.Projects.noPastEvents": "No past events to display", "app.containers.Projects.noPhaseSelected": "No phase selected", "app.containers.Projects.noUpcomingOrOngoingEvents": "No upcoming or ongoing events are currently scheduled.", "app.containers.Projects.offlineVotersTooltip": "This number does not reflect any offline voter counts.", "app.containers.Projects.options": "Options", "app.containers.Projects.participants": "Participants", "app.containers.Projects.participantsTooltip4": "This number also reflects anonymous survey submissions. Anonymous survey submissions are possible if surveys are open to everyone (see the {accessRightsLink} tab for this project).", "app.containers.Projects.pastEvents": "Past events", "app.containers.Projects.petitions": "Petitions", "app.containers.Projects.phases": "Phases", "app.containers.Projects.previousPhase": "Previous phase", "app.containers.Projects.project": "Project", "app.containers.Projects.projectTwitterMessage": "Make your voice heard! Participate in {projectName} | {orgName}", "app.containers.Projects.projects": "Projects", "app.containers.Projects.proposals": "Proposals", "app.containers.Projects.questions": "Questions", "app.containers.Projects.readLess": "Read less", "app.containers.Projects.readMore": "Read more", "app.containers.Projects.removeItem": "Remove item", "app.containers.Projects.requiredSelection": "Required selection", "app.containers.Projects.reviewDocument": "Review the document", "app.containers.Projects.seeTheContributions": "See the contributions", "app.containers.Projects.seeTheIdeas": "See the ideas", "app.containers.Projects.seeTheInitiatives": "See the initiatives", "app.containers.Projects.seeTheIssues": "See the comments", "app.containers.Projects.seeTheOptions": "See the options", "app.containers.Projects.seeThePetitions": "See the petitions", "app.containers.Projects.seeTheProjects": "See the projects", "app.containers.Projects.seeTheProposals": "See the proposals", "app.containers.Projects.seeTheQuestions": "See the questions", "app.containers.Projects.seeUpcomingEvents": "See upcoming events", "app.containers.Projects.share": "Share", "app.containers.Projects.shareThisProject": "Share this project", "app.containers.Projects.submitMyBasket": "Submit basket", "app.containers.Projects.survey": "Survey", "app.containers.Projects.takeThePoll": "Take the poll", "app.containers.Projects.takeTheSurvey": "Take the survey", "app.containers.Projects.timeline": "Timeline", "app.containers.Projects.upcomingAndOngoingEvents": "Upcoming and ongoing events", "app.containers.Projects.upcomingEvents": "Upcoming events", "app.containers.Projects.whatsAppMessage": "{projectName} | from the participation platform of {orgName}", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "Explore all ongoing projects of {orgName} to understand how you can participate.\n Come discuss local projects that matter most to you.", "app.containers.ProjectsIndexPage.metaTitle1": "Projects | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projects", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "I want to participate", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "Please {signInLink} or {signUpLink} first in order to volunteer for this activity", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Participation is not currently open for this activity.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "log in", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "sign up", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "I withdraw my offer to volunteer", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {no participants} one {# participant} other {# participants}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Warning: The embedded survey may have accessibility issues for screenreader users. If you experience any challenges, please reach out to the platform admin to receive a link to the survey from the original platform. Alternatively, you can request other ways to fill out the survey.", "app.containers.ProjectsShowPage.process.survey.survey": "Survey", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "To know if you can take part in this survey, please {logInLink} to the platform first.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "This survey can only be taken when this phase in the timeline is active.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Please {completeRegistrationLink} to take the survey.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "This survey is not currently enabled", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Taking this survey requires verification of your identity. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "The survey is no longer available, since this project is no longer active.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "complete registration", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "log in", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "sign up", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Verify your account now.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Only certain users can review this document. Please {signUpLink} or {logInLink} first.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "This document can only be reviewed when this phase is active.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Please {completeRegistrationLink} to review the document.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "Unfortunately, you don't have the rights to review this document.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Reviewing this document requires verification of your account. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "The document is no longer available, since this project is no longer active.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(incl. 1 offline)} other {(incl. # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "The percentage of participants who picked this option.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "The percentage of total votes this option received.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Cost:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Show more", "app.containers.ReactionControl.a11y_likesDislikes": "Total likes: {likesCount}, total dislikes: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "You cancelled your dislike for this input successfully.", "app.containers.ReactionControl.cancelLikeSuccess": "You cancelled your like for this input successfully.", "app.containers.ReactionControl.dislikeSuccess": "You disliked this input successfully.", "app.containers.ReactionControl.likeSuccess": "You liked this input successfully.", "app.containers.ReactionControl.reactionErrorSubTitle": "Due to an error your reaction could not being registered. Please try again in a few minutes.", "app.containers.ReactionControl.reactionSuccessTitle": "Your reaction was successfully registered!", "app.containers.ReactionControl.vote": "Vote", "app.containers.ReactionControl.voted": "Voted", "app.containers.SearchInput.a11y_cancelledPostingComment": "Cancelled posting comment.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} comments have loaded.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# events have loaded} one {# event has loaded} other {# events have loaded}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# results have loaded} one {# result has loaded} other {# results have loaded}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# search results have loaded} one {# search result has loaded} other {# search results have loaded}}.", "app.containers.SearchInput.removeSearchTerm": "Remove search term", "app.containers.SearchInput.searchAriaLabel": "Search", "app.containers.SearchInput.searchLabel": "Search", "app.containers.SearchInput.searchPlaceholder": "Search", "app.containers.SearchInput.searchTerm": "Search term: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect is the solution proposed by the French state to secure and simplify the sign up to more than 700 online services.", "app.containers.SignIn.or": "Or", "app.containers.SignIn.signInError": "The provided information is not correct. Click 'Forgot Password?' to reset your password.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Use FranceConnect to log in, sign up or verify your account.", "app.containers.SignIn.whatIsFranceConnect": "What is France Connect?", "app.containers.SignUp.adminOptions2": "For admins and project managers", "app.containers.SignUp.backToSignUpOptions": "Go back to sign up options", "app.containers.SignUp.continue": "Continue", "app.containers.SignUp.emailConsent": "By signing up, you agree to receive emails from this platform. You can select which emails you wish to receive in the 'My Settings' page.", "app.containers.SignUp.emptyFirstNameError": "Enter your first name", "app.containers.SignUp.emptyLastNameError": "Enter your last name", "app.containers.SignUp.firstNamesLabel": "First name", "app.containers.SignUp.goToLogIn": "Already have an account? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "I have read and agree to {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "I have read and agree to {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "I accept that the data will be used on mitgestalten.wien.gv.at. Further information can befound {link}.", "app.containers.SignUp.invitationErrorText": "Your invitation has expired or has already been used. If you have already used the invitation link to create an account, try signing in. Otherwise, sign up to create a new account.", "app.containers.SignUp.lastNameLabel": "Last name", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Follow your areas of focus to be notified about them:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Follow your favorite topics to be notified about them:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Save preferences", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Skip for now", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Accept our privacy policy to proceed", "app.containers.SignUp.signUp2": "Sign up", "app.containers.SignUp.skip": "Skip this step", "app.containers.SignUp.tacError": "Accept our terms and conditions to proceed", "app.containers.SignUp.thePrivacyPolicy": "the privacy policy", "app.containers.SignUp.theTermsAndConditions": "the terms and conditions", "app.containers.SignUp.unknownError": "{tenant<PERSON><PERSON>, select, LiberalDemocrats {It appears you tried to sign up before without completing the process. Click Log In instead, using the credentials chosen during the previous attempt.} other {Something went wrong. Please try again later.}}", "app.containers.SignUp.viennaConsentEmail": "Email address", "app.containers.SignUp.viennaConsentFirstName": "First name", "app.containers.SignUp.viennaConsentFooter": "You can change your profile information after sign-in. If you already have an account with the same email address on mitgestalten.wien.gv.at, it will be linked with your current account.", "app.containers.SignUp.viennaConsentHeader": "The following data will be transmitted:", "app.containers.SignUp.viennaConsentLastName": "Last name", "app.containers.SignUp.viennaConsentUserName": "User name", "app.containers.SignUp.viennaDataProtection": "the vienna privacy policy", "app.containers.SiteMap.contributions": "Contributions", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Comments", "app.containers.SiteMap.options": "Options", "app.containers.SiteMap.projects": "Projects", "app.containers.SiteMap.questions": "Questions", "app.containers.SpamReport.buttonSave": "Report", "app.containers.SpamReport.buttonSuccess": "Success", "app.containers.SpamReport.inappropriate": "It is inappropriate or offensive", "app.containers.SpamReport.messageError": "There was an error submitting the form, please try again.", "app.containers.SpamReport.messageSuccess": "Your report has been sent", "app.containers.SpamReport.other": "Other reason", "app.containers.SpamReport.otherReasonPlaceholder": "Description", "app.containers.SpamReport.wrong_content": "This is not relevant", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Remove profile picture", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Your votes on proposals that are still open for voting will be deleted. Votes on proposals where the voting period has closed will not be deleted.", "app.containers.UsersEditPage.addPassword": "Add password", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "To participate in projects requiring verification.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Verify your identity", "app.containers.UsersEditPage.bio": "About you", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "You can't edit this field because it contains verified information.", "app.containers.UsersEditPage.buttonSuccessLabel": "Success", "app.containers.UsersEditPage.cancel": "Cancel", "app.containers.UsersEditPage.changeEmail": "Change email", "app.containers.UsersEditPage.changePassword2": "Change password", "app.containers.UsersEditPage.clickHereToUpdateVerification": "Please click here to update your verification.", "app.containers.UsersEditPage.conditionsLinkText": "our conditions", "app.containers.UsersEditPage.contactUs": "Another reason to leave? {feedbackLink} and maybe we can help.", "app.containers.UsersEditPage.deleteAccountSubtext": "We are sorry to see you go.", "app.containers.UsersEditPage.deleteMyAccount": "Delete my account", "app.containers.UsersEditPage.deleteYourAccount": "Delete your account", "app.containers.UsersEditPage.deletionSection": "Delete your account", "app.containers.UsersEditPage.deletionSubtitle": "This action can not be undone. The content you published on the platform will be anonymized. If you wish to delete all your content, you can contact <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "Email", "app.containers.UsersEditPage.emailEmptyError": "Provide an e-mail address", "app.containers.UsersEditPage.emailInvalidError": "Provide an email address in the correct format, <NAME_EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "Let us know", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "First name", "app.containers.UsersEditPage.firstNamesEmptyError": "Provide a first name", "app.containers.UsersEditPage.h1": "Your account information", "app.containers.UsersEditPage.h1sub": "Edit your account information", "app.containers.UsersEditPage.image": "Avatar image", "app.containers.UsersEditPage.imageDropzonePlaceholder": "Click to select a profile picture (max. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "All settings for your profile", "app.containers.UsersEditPage.language": "Language", "app.containers.UsersEditPage.lastName": "Last name", "app.containers.UsersEditPage.lastNameEmptyError": "Provide a last name", "app.containers.UsersEditPage.loading": "Loading...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "You can change your email or password here.", "app.containers.UsersEditPage.loginCredentialsTitle": "Login credentials", "app.containers.UsersEditPage.messageError": "We weren't able to save your profile. Try again later <NAME_EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Your profile has been saved.", "app.containers.UsersEditPage.metaDescription": "This is the profile settings page of {firstName} {lastName} on the online participation platform of {tenantName}. Here you can verify your identity, edit your account information, delete your account and edit your email preferences.", "app.containers.UsersEditPage.metaTitle1": "Profile settings page of {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Once you click this button, we will have no way to restore your account.", "app.containers.UsersEditPage.noNameWarning2": "Your name is currently displayed on the platform as: \"{displayName}\" because you have not entered your name. This is an autogenerated name. If you would like to change it, please enter your name below.", "app.containers.UsersEditPage.notificationsSubTitle": "What kinds of email notifications do you want to receive? ", "app.containers.UsersEditPage.notificationsTitle": "Email notifications", "app.containers.UsersEditPage.password": "Choose a new password", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "Provide a password that is at least {minimumPasswordLength} characters long", "app.containers.UsersEditPage.passwordAddSection": "Add a password", "app.containers.UsersEditPage.passwordAddSubtitle2": "Set a password and easily login to the platform, without having to confirm your email every time.", "app.containers.UsersEditPage.passwordChangeSection": "Change your password", "app.containers.UsersEditPage.passwordChangeSubtitle": "Confirm your current password and change to new password.", "app.containers.UsersEditPage.privacyReasons": "If you are worried about your privacy, you can read {conditionsLink}.", "app.containers.UsersEditPage.processing": "Sending...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "First name is required when providing last name", "app.containers.UsersEditPage.reasonsToStayListTitle": "Before you go...", "app.containers.UsersEditPage.submit": "Save changes", "app.containers.UsersEditPage.tooManyEmails": "Receiving too many emails? You can manage your email preferences in your profile settings.", "app.containers.UsersEditPage.updateverification": "Did your official information change? {reverify<PERSON><PERSON>on}", "app.containers.UsersEditPage.user": "When do you want us to send you an email to notify you?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "You can participate in projects that require verification.", "app.containers.UsersEditPage.verifiedIdentityTitle": "You are verified", "app.containers.UsersEditPage.verifyNow": "Verify now", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Download your responses (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {no likes} one {1 like} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Input that this comment was posted in response to:", "app.containers.UsersShowPage.areas": "Areas", "app.containers.UsersShowPage.commentsWithCount": "Comments ({commentsCount})", "app.containers.UsersShowPage.editProfile": "Edit my profile", "app.containers.UsersShowPage.emptyInfoText": "You are not following any items of the specified filter above.", "app.containers.UsersShowPage.eventsWithCount": "Events ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Following ({followingCount})", "app.containers.UsersShowPage.inputs": "Inputs", "app.containers.UsersShowPage.invisibleTitlePostsList": "All input submitted by this participant", "app.containers.UsersShowPage.invisibleTitleUserComments": "All comments posted by this participant", "app.containers.UsersShowPage.loadMore": "Load more", "app.containers.UsersShowPage.loadMoreComments": "Load more comments", "app.containers.UsersShowPage.loadingComments": "Loading comments...", "app.containers.UsersShowPage.loadingEvents": "Loading events...", "app.containers.UsersShowPage.memberSince": "Member since {date}", "app.containers.UsersShowPage.metaTitle1": "Profile page of {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "This person hasn't posted any comments yet.", "app.containers.UsersShowPage.noCommentsForYou": "There are no comments here yet.", "app.containers.UsersShowPage.noEventsForUser": "You have not attended any events yet.", "app.containers.UsersShowPage.postsWithCount": "Submissions ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Project folders", "app.containers.UsersShowPage.projects": "Projects", "app.containers.UsersShowPage.proposals": "Proposals", "app.containers.UsersShowPage.seePost": "See submission", "app.containers.UsersShowPage.surveyResponses": "Responses ({responses})", "app.containers.UsersShowPage.topics": "Topics", "app.containers.UsersShowPage.tryAgain": "An error has occurred, please try again later.", "app.containers.UsersShowPage.userShowPageMetaDescription": "This is the profile page of {firstName} {lastName} on the online participation platform of {orgName}. Here is an overview of all of their input.", "app.containers.VoteControl.close": "Close", "app.containers.VoteControl.voteErrorTitle": "Something went wrong", "app.containers.admin.ContentBuilder.default": "default", "app.containers.admin.ContentBuilder.imageTextCards": "Image & text cards", "app.containers.admin.ContentBuilder.infoWithAccordions": "Info & accordions", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 column", "app.containers.admin.ContentBuilder.projectDescription": "Project description", "app.containers.app.navbar.admin": "Manage platform", "app.containers.app.navbar.allProjects": "All projects", "app.containers.app.navbar.ariaLabel": "Primary", "app.containers.app.navbar.closeMobileNavMenu": "Close mobile navigation menu", "app.containers.app.navbar.editProfile": "My settings", "app.containers.app.navbar.fullMobileNavigation": "Full mobile", "app.containers.app.navbar.logIn": "Log in", "app.containers.app.navbar.logoImgAltText": "{orgName} Home", "app.containers.app.navbar.myProfile": "My activity", "app.containers.app.navbar.search": "Search", "app.containers.app.navbar.showFullMenu": "Show full menu", "app.containers.app.navbar.signOut": "Sign out", "app.containers.eventspage.errorWhenFetchingEvents": "An error occurred while loading events. Please try reloading the page.", "app.containers.eventspage.events": "Events", "app.containers.eventspage.eventsPageDescription": "Show all events posted on {orgName}'s platform.", "app.containers.eventspage.eventsPageTitle1": "Events | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projects", "app.containers.eventspage.noPastEvents": "No past events to display", "app.containers.eventspage.noUpcomingOrOngoingEvents": "No upcoming or ongoing events are currently scheduled.", "app.containers.eventspage.pastEvents": "Past events", "app.containers.eventspage.upcomingAndOngoingEvents": "Upcoming and ongoing events", "app.containers.footer.accessibility-statement": "Accessibility statement", "app.containers.footer.ariaLabel": "Secondary", "app.containers.footer.cookie-policy": "Cookie policy", "app.containers.footer.cookieSettings": "Cookie settings", "app.containers.footer.feedbackEmptyError": "The feedback field cannot be empty.", "app.containers.footer.poweredBy": "Powered by", "app.containers.footer.privacy-policy": "Privacy policy", "app.containers.footer.siteMap": "Site Map", "app.containers.footer.terms-and-conditions": "Terms and conditions", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "Cancel", "app.containers.ideaHeading.confirmLeaveFormButtonText": "Yes, I want to leave", "app.containers.ideaHeading.editForm": "Edit form", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "Are you sure you want to leave?", "app.containers.ideaHeading.leaveIdeaForm": "Leave idea form", "app.containers.ideaHeading.leaveIdeaText": "Your responses won't be saved.", "app.containers.landing.cityProjects": "Projects", "app.containers.landing.completeProfile": "Complete your profile", "app.containers.landing.completeYourProfile": "Welcome, {firstName}. It's time to complete your profile.", "app.containers.landing.createAccount": "Sign up", "app.containers.landing.defaultSignedInMessage": "{org<PERSON><PERSON>} is listening to you. It’s your turn to make your voice heard!", "app.containers.landing.doItLater": "I'll do it later", "app.containers.landing.new": "new", "app.containers.landing.subtitleCity": "Welcome to the participation platform of {orgName}", "app.containers.landing.titleCity": "Let’s shape the future of {orgName} together", "app.containers.landing.twitterMessage": "Vote for {ideaTitle} on", "app.containers.landing.upcomingEventsWidgetTitle": "Upcoming and ongoing events", "app.containers.landing.userDeletedSubtitle": "You can create a new account at any time or {contactLink} to let us know what we can improve.", "app.containers.landing.userDeletedSubtitleLinkText": "drop us a line", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Your account has been deleted.", "app.containers.landing.userDeletionFailed": "An error occurred deleting your account, we have been notified of the issue and will do our best to fix it. Please try again later.", "app.containers.landing.verifyNow": "Verify now", "app.containers.landing.verifyYourIdentity": "Verify your identity", "app.containers.landing.viewAllEventsText": "View all events", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Back to folder", "app.errors.after_end_at": "The start date occurs after the end date", "app.errors.avatar_carrierwave_download_error": "Could not download avatar file.", "app.errors.avatar_carrierwave_integrity_error": "Avatar file is not of an allowed type.", "app.errors.avatar_carrierwave_processing_error": "Could not process avatar.", "app.errors.avatar_extension_blacklist_error": "The file extension of the avatar image is not allowed. Allowed extensions are: jpg, jpeg, gif and png.", "app.errors.avatar_extension_whitelist_error": "The file extension of the avatar image is not allowed. Allowed extensions are: jpg, jpeg, gif and png.", "app.errors.banner_cta_button_multiloc_blank": "Enter a button text.", "app.errors.banner_cta_button_url_blank": "Enter a link.", "app.errors.banner_cta_button_url_url": "Enter a valid link. Make sure the link starts with 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Enter a button text.", "app.errors.banner_cta_signed_in_url_blank": "Enter a link.", "app.errors.banner_cta_signed_in_url_url": "Enter a valid link. Make sure the link starts with 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Enter a button text.", "app.errors.banner_cta_signed_out_url_blank": "Enter a link.", "app.errors.banner_cta_signed_out_url_url": "Enter a valid link. Make sure the link starts with 'https://'.", "app.errors.base_includes_banned_words": "You may have used one or more words that are considered profanity. Please alter your text to remove any profanities that might be present.", "app.errors.body_multiloc_includes_banned_words": "The description contains words that are considered inappropriate.", "app.errors.bulk_import_idea_not_valid": "The resulting idea is not valid: {value}.", "app.errors.bulk_import_image_url_not_valid": "No image could be downloaded from {value}. Make sure the URL is valid and ends with a file extension such as .png or .jpg. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Idea location with a missing coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Idea location with a non-numeric coordinate in {value}. This issue occurs in the row with ID {row}.", "app.errors.bulk_import_malformed_pdf": "The uploaded PDF file appears to be malformed. Try exporting the PDF again from your source and then upload again.", "app.errors.bulk_import_maximum_ideas_exceeded": "The maximum of {value} ideas has been exceeded.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "The maximum of {value} pages in a PDF has been exceeded.", "app.errors.bulk_import_not_enough_pdf_pages": "The uploaded PDF does not have enough pages - it should have at least the same number of pages as the downloaded template.", "app.errors.bulk_import_publication_date_invalid_format": "Idea with invalid publication date format \"{value}\". Please use the format \"DD-MM-YYYY\".", "app.errors.cannot_contain_ideas": "The participation method you selected does not support this type of post. Please edit your selection and try again.", "app.errors.cant_change_after_first_response": "You can no longer change this, since some users already responded", "app.errors.category_name_taken": "A category with this name already exists", "app.errors.confirmation_code_expired": "Code expired. Please request a new code.", "app.errors.confirmation_code_invalid": "Invalid confirmation code. Please check your email for the correct code or try 'Send New Code'", "app.errors.confirmation_code_too_many_resets": "You've resent the confirmation code too many times. Please contact us to receive an invitation code instead.", "app.errors.confirmation_code_too_many_retries": "You've tried too many times. Please request a new code or try changing your email.", "app.errors.email_already_active": "The email address {value} found in row {row} already belongs to a registered participant", "app.errors.email_already_invited": "The email address {value} found in row {row} was already invited", "app.errors.email_blank": "This cannot be empty", "app.errors.email_domain_blacklisted": "Please use a different email domain to register.", "app.errors.email_invalid": "Please use a valid email address.", "app.errors.email_taken": "An account with this email already exists. You can log in instead.", "app.errors.email_taken_by_invite": "{value} is already taken by a pending invite. Check your spam folder or contact {supportEmail} if you can't find it.", "app.errors.emails_duplicate": "One or more duplicate values for the email address {value} were found in the following row(s): {rows}", "app.errors.extension_whitelist_error": "The format of the file you tried to upload is not supported.", "app.errors.file_extension_whitelist_error": "The format of the file you tried to upload is not supported.", "app.errors.first_name_blank": "This cannot be empty", "app.errors.generics.blank": "This cannot be empty.", "app.errors.generics.invalid": "This doesn't look like a valid value", "app.errors.generics.taken": "This email already exists. Another account is linked to it.", "app.errors.generics.unsupported_locales": "This field does not support the current locale.", "app.errors.group_ids_unauthorized_choice_moderator": "As a project manager, you can only email to people that can access your project(s)", "app.errors.has_other_overlapping_phases": "Projects cannot have overlapping phases.", "app.errors.invalid_email": "The email {value} found in row {row} is not a valid email address", "app.errors.invalid_row": "An unknown error occurred while trying to process row {row}", "app.errors.is_not_timeline_project": "The current project does not support phases.", "app.errors.key_invalid": "The key can only contain letters, numbers and underscores(_)", "app.errors.last_name_blank": "This cannot be empty", "app.errors.locale_blank": "Please choose a language", "app.errors.locale_inclusion": "Please choose a supported language", "app.errors.malformed_admin_value": "The admin value {value} found in row {row} is not valid", "app.errors.malformed_groups_value": "The group {value} found in row {row} is not a valid group", "app.errors.max_invites_limit_exceeded1": "The number of invitations exceeds the limit of 1000.", "app.errors.maximum_attendees_greater_than1": "The maximum number of registrants must be greater than 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "The maximum number of registrants must be greater than or equal to the current number of registrants.", "app.errors.no_invites_specified": "Could not find any email address.", "app.errors.no_recipients": "The campaign can't be sent out because there are no recipients. The group you're sending to is either empty, or nobody has consented to receiving emails.", "app.errors.number_invalid": "Please enter a valid number.", "app.errors.password_blank": "This cannot be empty", "app.errors.password_invalid": "Please check your current password again.", "app.errors.password_too_short": "The password must be at least 8 characters long", "app.errors.resending_code_failed": "Something went wrong while sending out the confirmation code.", "app.errors.slug_taken": "This project URL already exists. Please change the project slug to something else.", "app.errors.tag_name_taken": "A tag with this name already exists", "app.errors.title_multiloc_blank": "The title cannot be empty.", "app.errors.title_multiloc_includes_banned_words": "The title contains words that are considered inappropriate.", "app.errors.token_invalid": "Password reset links can only be used once and are valid for one hour after being sent. {passwordResetLink}.", "app.errors.too_common": "This password can be easily guessed. Please choose a stronger password.", "app.errors.too_long": "Please choose a shorter password (max 72 characters)", "app.errors.too_short": "Please choose a password with at least 8 characters", "app.errors.uncaught_error": "An unknown error occurred.", "app.errors.unknown_group": "The group {value} found in row {row} is not a known group", "app.errors.unknown_locale": "The language {value} found in row {row} is not a configured language", "app.errors.unparseable_excel": "The selected Excel file could not be processed.", "app.errors.url": "Enter a valid link. Make sure the link starts with https://", "app.errors.verification_taken": "Verification cannot be completed as another account has been verified using the same details.", "app.errors.view_name_taken": "A view with this name already exists", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Inappropriate content was auto-detected in a post or comment", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Sign in with StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Sign up with StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Create a Stadt Wien Account now and use one login for many digital services of Vienna.", "app.modules.id_cow.cancel": "Cancel", "app.modules.id_cow.emptyFieldError": "This field cannot be empty.", "app.modules.id_cow.helpAltText": "Shows where to find the ID serial number on an identity card", "app.modules.id_cow.invalidIdSerialError": "Invalid ID serial", "app.modules.id_cow.invalidRunError": "Invalid RUN", "app.modules.id_cow.noMatchFormError": "No match was found.", "app.modules.id_cow.notEntitledFormError": "Not entitled.", "app.modules.id_cow.showCOWHelp": "Where can I find my ID Serial Number ?", "app.modules.id_cow.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_cow.submit": "Submit", "app.modules.id_cow.takenFormError": "Already taken.", "app.modules.id_cow.verifyCow": "Verify using COW", "app.modules.id_franceconnect.verificationButtonAltText": "Verify with FranceConnect", "app.modules.id_gent_rrn.cancel": "Cancel", "app.modules.id_gent_rrn.emptyFieldError": "This field cannot be empty.", "app.modules.id_gent_rrn.gentRrnHelp": "Your social security number is shown on the back of your digital identity card", "app.modules.id_gent_rrn.invalidRrnError": "Invalid social security number", "app.modules.id_gent_rrn.noMatchFormError": "We couldn't find back information on your social security number", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "We can't verify you because you live outside of Ghent", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "We can't verify you because you are younger than 14 years", "app.modules.id_gent_rrn.rrnLabel": "Social security number", "app.modules.id_gent_rrn.rrnTooltip": "We ask your social security number to verify whether you are a citizen of Ghent, older than 14 year old.", "app.modules.id_gent_rrn.showGentRrnHelp": "Where can I find my ID Serial Number ?", "app.modules.id_gent_rrn.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_gent_rrn.submit": "Submit", "app.modules.id_gent_rrn.takenFormError": "Your social security number has already been used to verify another account", "app.modules.id_gent_rrn.verifyGentRrn": "Verify using GentRrn", "app.modules.id_id_card_lookup.cancel": "Cancel", "app.modules.id_id_card_lookup.emptyFieldError": "This field cannot be empty.", "app.modules.id_id_card_lookup.helpAltText": "ID card explanation", "app.modules.id_id_card_lookup.invalidCardIdError": "This id is not valid.", "app.modules.id_id_card_lookup.noMatchFormError": "No match was found.", "app.modules.id_id_card_lookup.showHelp": "Where can I find my ID Serial Number?", "app.modules.id_id_card_lookup.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_id_card_lookup.submit": "Submit", "app.modules.id_id_card_lookup.takenFormError": "Already taken.", "app.modules.id_oostende_rrn.cancel": "Cancel", "app.modules.id_oostende_rrn.emptyFieldError": "This field cannot be empty.", "app.modules.id_oostende_rrn.invalidRrnError": "Invalid social security number", "app.modules.id_oostende_rrn.noMatchFormError": "We couldn't find back information on your social security number", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "We can't verify you because you live outside of Oostende", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "We can't verify you because you are younger than 14 years", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Your social security number is shown on the back of your digital identity card", "app.modules.id_oostende_rrn.rrnLabel": "Social security number", "app.modules.id_oostende_rrn.rrnTooltip": "We ask your social security number to verify whether you are a citizen of Oostende, older than 14 year old.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Where can I find my social security number?", "app.modules.id_oostende_rrn.somethingWentWrongError": "We can't verify you because something went wrong", "app.modules.id_oostende_rrn.submit": "Submit", "app.modules.id_oostende_rrn.takenFormError": "Your social security number has already been used to verify another account", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Verify using social security number", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "You received admin rights over the \"{folderName}\" folder.", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Share", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "View the projects at {folderUrl} to make your voice heard!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | from the participation platform of {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | from the participation platform of {orgName}", "app.sessionRecording.accept": "Yes, I accept", "app.sessionRecording.modalDescription1": "In order to better understand our users, we randomly ask a small percentage of visitors to track their browsing session in detail.", "app.sessionRecording.modalDescription2": "The sole purpose of the recorded data is to improve the website. None of your data will be shared with a 3rd party. Any sensitive information you enter will be filtered.", "app.sessionRecording.modalDescription3": "Do you accept?", "app.sessionRecording.modalDescriptionFaq": "FAQ here.", "app.sessionRecording.modalTitle": "Help us improve this website", "app.sessionRecording.reject": "No, I reject", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Conduct a budget allocation exercise", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Collect feedback on a document", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Create an in-platform survey", "app.utils.AdminPage.ProjectEdit.createPoll": "Create a poll", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Embed an external survey", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Find volunteers", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Collect input and feedback", "app.utils.AdminPage.ProjectEdit.shareInformation": "Share information", "app.utils.FormattedCurrency.credits": "credits", "app.utils.FormattedCurrency.tokens": "tokens", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# credits} one {# credit} other {# credits}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokens} one {# token} other {# tokens}}", "app.utils.IdeaCards.mostDiscussed": "Most discussed", "app.utils.IdeaCards.mostReacted": "Most reactions", "app.utils.IdeaCards.newest": "Newest", "app.utils.IdeaCards.oldest": "Oldest", "app.utils.IdeaCards.random": "Random", "app.utils.IdeaCards.trending": "Trending", "app.utils.IdeasNewPage.contributionFormTitle": "Add new contribution", "app.utils.IdeasNewPage.ideaFormTitle": "Add new idea", "app.utils.IdeasNewPage.initiativeFormTitle": "Add new initiative", "app.utils.IdeasNewPage.issueFormTitle1": "Add new comment", "app.utils.IdeasNewPage.optionFormTitle": "Add new option", "app.utils.IdeasNewPage.petitionFormTitle": "Add new petition", "app.utils.IdeasNewPage.projectFormTitle": "Add new project", "app.utils.IdeasNewPage.proposalFormTitle": "Add new proposal", "app.utils.IdeasNewPage.questionFormTitle": "Add new question", "app.utils.IdeasNewPage.surveyTitle": "Survey", "app.utils.IdeasNewPage.viewYourComment": "View your comment", "app.utils.IdeasNewPage.viewYourContribution": "View your contribution", "app.utils.IdeasNewPage.viewYourIdea": "View your idea", "app.utils.IdeasNewPage.viewYourInitiative": "View your initiative", "app.utils.IdeasNewPage.viewYourInput": "View your input", "app.utils.IdeasNewPage.viewYourIssue": "View your issue", "app.utils.IdeasNewPage.viewYourOption": "View your option", "app.utils.IdeasNewPage.viewYourPetition": "View your petition", "app.utils.IdeasNewPage.viewYourProject": "View your project", "app.utils.IdeasNewPage.viewYourProposal": "View your proposal", "app.utils.IdeasNewPage.viewYourQuestion": "View your question", "app.utils.Projects.sendSubmission": "Send submission identifier to my email", "app.utils.Projects.sendSurveySubmission": "Send survey submission identifier to my email", "app.utils.Projects.surveySubmission": "Survey submission", "app.utils.Projects.yourResponseHasTheFollowingId": "Your response has the following identifier: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "If you later decide that you want your response to be removed, please contact us with the following unique identifier:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "You must complete your profile to attend this event.", "app.utils.actionDescriptors.attendingEventNotInGroup": "You do not meet the requirements to attend this event.", "app.utils.actionDescriptors.attendingEventNotPermitted": "You are not permitted to attend this event.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "You must log in or register to attend this event.", "app.utils.actionDescriptors.attendingEventNotVerified": "You must verify your account before you can attend this event.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "You must complete your profile to volunteer.", "app.utils.actionDescriptors.volunteeringNotInGroup": "You do not meet the requirements to volunteer.", "app.utils.actionDescriptors.volunteeringNotPermitted": "You are not permitted to volunteer.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "You must log in or register to volunteer.", "app.utils.actionDescriptors.volunteeringNotVerified": "You must verify your account before you can volunteer.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Please {completeRegistrationLink} to volunteer.", "app.utils.errors.api_error_default.in": "Is not right", "app.utils.errors.default.ajv_error_birthyear_required": "Please fill in your year of birth", "app.utils.errors.default.ajv_error_date_any": "Please fill in a valid date", "app.utils.errors.default.ajv_error_domicile_required": "Please fill in your place of residence", "app.utils.errors.default.ajv_error_gender_required": "Please fill in your gender", "app.utils.errors.default.ajv_error_invalid": "Is invalid", "app.utils.errors.default.ajv_error_maxItems": "Can't include more than {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_minItems": "Must include at least {limit, plural, one {# item} other {# items}}", "app.utils.errors.default.ajv_error_number_any": "Please fill in a valid number", "app.utils.errors.default.ajv_error_politician_required": "Please fill in whether you are a politician", "app.utils.errors.default.ajv_error_required3": "Field is required: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "Can't be blank", "app.utils.errors.default.api_error_accepted": "Must be accepted", "app.utils.errors.default.api_error_blank": "Can't be blank", "app.utils.errors.default.api_error_confirmation": "Doesn't match", "app.utils.errors.default.api_error_empty": "Can't be empty", "app.utils.errors.default.api_error_equal_to": "Is not right", "app.utils.errors.default.api_error_even": "Must be even", "app.utils.errors.default.api_error_exclusion": "Is reserved", "app.utils.errors.default.api_error_greater_than": "Is too small", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Is too small", "app.utils.errors.default.api_error_inclusion": "Is not included in the list", "app.utils.errors.default.api_error_invalid": "Is invalid", "app.utils.errors.default.api_error_less_than": "Is too big", "app.utils.errors.default.api_error_less_than_or_equal_to": "Is too big", "app.utils.errors.default.api_error_not_a_number": "Is not a number", "app.utils.errors.default.api_error_not_an_integer": "Must be an integer", "app.utils.errors.default.api_error_other_than": "Is not right", "app.utils.errors.default.api_error_present": "Must be blank", "app.utils.errors.default.api_error_too_long": "Is too long", "app.utils.errors.default.api_error_too_short": "Is too short", "app.utils.errors.default.api_error_wrong_length": "Is the wrong length", "app.utils.errors.defaultapi_error_.odd": "Must be odd", "app.utils.notInGroup": "You do not meet the requirements to participate.", "app.utils.participationMethod.onSurveySubmission": "Thank you. Your response has been received.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Voting is no longer available, since this phase is no longer active.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "You do not meet the requirements to vote.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "You are not permitted to vote.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "You must log in or register to vote.", "app.utils.participationMethodConfig.voting.votingNotVerified": "You must verify your account before you can vote.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Submitting budgets closed on {endDate}.</b> Participants had a total of <b>{maxBudget} each to distribute between {optionCount} options.</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Budget submitted", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budget submitted 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "You do not meet the requirements to assign budgets.", "app.utils.votingMethodUtils.budgetingNotPermitted": "You are not permitted to assign budgets.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "You must log in or register to assign budgets.", "app.utils.votingMethodUtils.budgetingNotVerified": "You must verify your account before you can assign budgets.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>Your budget will not be counted</b> until you click \"Submit\"", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "The minimum required budget is {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Once you are done, click \"Submit\" to submit your budget.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "Select your preferred options by tapping on \"Add\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "You have a total of <b>{maxBudget} to distribute between {optionCount} options</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>Congratulations, your budget has been submitted!</b> You can check your options below at any point or modify them before <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>Congratulations, your budget has been submitted!</b> You can check your options below at any point.", "app.utils.votingMethodUtils.castYourVote": "Cast your vote", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# credit} other {# credits}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "You can add a maximum of {maxVotes, plural, one {# point} other {# points}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "You can add a maximum of {maxVotes, plural, one {# token} other {# tokens}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "You can add a maximum of {maxVotes, plural, one {# vote} other {# votes}} per option.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Once you are done, click “Submit” to cast your vote.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "Select your preferred options by tapping on \"Select\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Final results", "app.utils.votingMethodUtils.finalTally": "Final tally", "app.utils.votingMethodUtils.howToParticipate": "How to participate", "app.utils.votingMethodUtils.howToVote": "How to vote", "app.utils.votingMethodUtils.multipleVotingEnded1": "Voting closed on <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 credits} one {1 credit} other {# credits}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 points} one {1 point} other {# points}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokens} one {1 token} other {# tokens}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 votes} one {1 vote} other {# votes}}", "app.utils.votingMethodUtils.results": "Results", "app.utils.votingMethodUtils.singleVotingEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for {maxVotes} options.</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "Select your preferred options by tapping on “Vote”", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "You have <b>{totalVotes} votes</b> that you can assign to the options.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Once you are done, click “Submit” to cast your vote.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for 1 option.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "Select your preferred option by tapping on “Vote”.", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "You have <b>1 vote</b> that you can assign to one of the options.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Voting closed on <b>{endDate}.</b> Participants could <b>vote for as many options as they wished.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "You can vote for as many options as you would like.", "app.utils.votingMethodUtils.submitYourBudget": "Submit your budget", "app.utils.votingMethodUtils.submittedBudgetCountText2": "person submitted their budget online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "people submitted their budgets online", "app.utils.votingMethodUtils.submittedVoteCountText2": "person submitted their vote online", "app.utils.votingMethodUtils.submittedVotesCountText2": "people submitted their votes online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "Vote submitted 🎉", "app.utils.votingMethodUtils.votesCast": "Votes cast", "app.utils.votingMethodUtils.votingClosed": "Voting closed", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Your vote will not be counted</b> until you click \"Submit\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission before <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>Congratulations, your vote has been submitted!</b> You can check or modify your submission below at any point.", "components.UI.IdeaSelect.noIdeaAvailable": "There are no ideas available.", "components.UI.IdeaSelect.selectIdea": "Select idea", "containers.SiteMap.allProjects": "All projects", "containers.SiteMap.customPageSection": "Custom pages", "containers.SiteMap.folderInfo": "More info", "containers.SiteMap.headSiteMapTitle": "Site map | {orgName}", "containers.SiteMap.homeSection": "General", "containers.SiteMap.pageContents": "Page content", "containers.SiteMap.profilePage": "Your profile page", "containers.SiteMap.profileSettings": "Your settings", "containers.SiteMap.projectEvents": "Events", "containers.SiteMap.projectIdeas": "Ideas", "containers.SiteMap.projectInfo": "Information", "containers.SiteMap.projectPoll": "Poll", "containers.SiteMap.projectSurvey": "Survey", "containers.SiteMap.projectsArchived": "Archived projects", "containers.SiteMap.projectsCurrent": "Current projects", "containers.SiteMap.projectsDraft": "Draft projects", "containers.SiteMap.projectsSection": "Projects of {orgName}", "containers.SiteMap.signInPage": "Sign in", "containers.SiteMap.signUpPage": "Sign up", "containers.SiteMap.siteMapDescription": "From this page, you can navigate to any content on the platform.", "containers.SiteMap.siteMapTitle": "Site map of the participation platform of {orgName}", "containers.SiteMap.successStories": "Success stories", "containers.SiteMap.timeline": "Project phases", "containers.SiteMap.userSpaceSection": "Your account", "containers.SubscriptionEndedPage.accessDenied": "You no longer have access", "containers.SubscriptionEndedPage.subscriptionEnded": "This page is only accessible for platforms with an active subscription."}