{"EmailSettingsPage.emailSettings": "Ustawienia poczty e-mail", "EmailSettingsPage.initialUnsubscribeError": "Zaistniał problem z wypisaniem się z tej kampanii, proszę spróbuj ponownie później.", "EmailSettingsPage.initialUnsubscribeLoading": "<PERSON><PERSON> jest roz<PERSON>, pro<PERSON><PERSON> poczekaj...", "EmailSettingsPage.initialUnsubscribeSuccess": "Pomyślnie wypisałeś się z {campaignTitle}.", "UI.FormComponents.optional": "opcjonalnie", "app.closeIconButton.a11y_buttonActionMessage": "Zamknij", "app.components.Areas.areaUpdateError": "Wystąpił błąd podczas zapisywania Twojego obszaru. Spróbuj ponownie.", "app.components.Areas.followedArea": "Obsługiwany obszar: {areaTitle}", "app.components.Areas.followedTopic": "Śledzony temat: {topicTitle}", "app.components.Areas.topicUpdateError": "Wystą<PERSON>ł błąd podczas zapisywania Twojego tematu. Spróbuj ponownie.", "app.components.Areas.unfollowedArea": "Nieobsługiwany obszar: {areaTitle}", "app.components.Areas.unfollowedTopic": "Nieobsługiwany temat: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "Cena:", "app.components.AssignBudgetControl.add": "<PERSON><PERSON><PERSON>", "app.components.AssignBudgetControl.added": "Dodano", "app.components.AssignMultipleVotesControl.addVote": "<PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "Rozdzieliłeś wszystkie swoje kredyty.", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "Rozdzieliłeś maksymalną liczbę punktów dla tej opcji.", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "Rozdzieliłeś wszystkie swoje punkty.", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "Rozdzieliłeś maksymalną liczbę punktów za tę opcję.", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "Rozdałeś wszystkie swoje żetony.", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "Rozdałeś maksymalną liczbę tokenów dla tej opcji.", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "Rozdałeś wszystkie swoje głosy.", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "Rozdałeś maksymalną liczbę głosów dla tej opcji.", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(w tym 1 offline)} other {(w tym # offline)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "Głosowanie nie jest dostę<PERSON>ne, ponieważ ta faza nie jest aktywna.", "app.components.AssignMultipleVotesControl.removeVote": "Us<PERSON>ń głos", "app.components.AssignMultipleVotesControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignMultipleVotesControl.votesSubmitted1": "Oddałeś już swój głos. Aby go zmodyfikować, kliknij \"Modyfikuj swoje zgłoszenie\".", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "Oddałeś już swój głos. Aby go zmodyfikować, wróć na stronę projektu i kliknij \"Modyfikuj swoje zgłoszenie\".", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {kredyty} other {kredyty}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {punkt} other {punkty}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {token} other {tokeny}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {vote} other {votes}}", "app.components.AssignVoteControl.maxVotesReached1": "Rozdałeś wszystkie swoje głosy.", "app.components.AssignVoteControl.phaseNotActive": "Głosowanie nie jest dostę<PERSON>ne, ponieważ ta faza nie jest aktywna.", "app.components.AssignVoteControl.select": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AssignVoteControl.selected2": "Wy<PERSON><PERSON>", "app.components.AssignVoteControl.voteForAtLeastOne": "Zagłosuj na co najmniej 1 opcję", "app.components.AssignVoteControl.votesSubmitted1": "Oddałeś już swój głos. Aby go zmodyfikować, kliknij \"Modyfikuj swoje zgłoszenie\".", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "Oddałeś już swój głos. Aby go zmodyfikować, wróć na stronę projektu i kliknij \"Modyfikuj swoje zgłoszenie\".", "app.components.AuthProviders.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.continueWithAzure": "Kontynu<PERSON>j z {azureProviderName}", "app.components.AuthProviders.continueWithFacebook": "Użyj konta na Facebooku", "app.components.AuthProviders.continueWithFakeSSO": "Kontynuuj z fałszywym SSO", "app.components.AuthProviders.continueWithGoogle": "Użyj konta Google", "app.components.AuthProviders.continueWithHoplr": "Kont<PERSON><PERSON><PERSON><PERSON> z <PERSON>lr", "app.components.AuthProviders.continueWithIdAustria": "Kontynuuj z ID Austria", "app.components.AuthProviders.continueWithLoginMechanism": "Kontynuuj z {loginMechanismName}", "app.components.AuthProviders.continueWithNemlogIn": "Kontynuuj z MitID", "app.components.AuthProviders.franceConnectMergingFailed": "Konto z tym adresem e-mail już istnieje.{br}{br}Nie można uzyskać dostępu do platformy za pomocą FranceConnect, ponieważ dane osobowe nie pasują. Aby zalogować się za pomocą FranceConnect, należy najpierw zmienić imię lub nazwisko na tej platformie, aby dopasować swoje oficjalne dane.{br}{br}Poniżej można się zalogować jak zwykle.", "app.components.AuthProviders.goToLogIn": "<PERSON><PERSON> już konto? {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "Nie masz konta? {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "<PERSON><PERSON><PERSON><PERSON>", "app.components.AuthProviders.logInWithEmail": "Zaloguj się za pomocą e-maila", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "<PERSON><PERSON><PERSON> mieć określony minimalny wiek lub wyższy, aby zost<PERSON> zweryfikowanym.", "app.components.AuthProviders.signUp2": "Zarejestruj się", "app.components.AuthProviders.signUpButtonAltText": "Zarejestruj się przez {loginMechanismName}", "app.components.AuthProviders.signUpWithEmail": "Zarejestruj się za pomocą e-maila", "app.components.AuthProviders.verificationRequired": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Author.a11yPostedBy": "Wysłany przez", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 uczestnik} other {{numberOfParticipants} uczestnicy}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} użytkownicy", "app.components.AvatarBubbles.participant": "uczestnik", "app.components.AvatarBubbles.participants1": "uczestnicy", "app.components.Comments.cancel": "<PERSON><PERSON><PERSON>", "app.components.Comments.commentingDisabledInCurrentPhase": "Komentowanie nie jest możliwe na obecnym etapie.", "app.components.Comments.commentingDisabledInactiveProject": "Komentowanie nie jest możliwe, ponieważ ten projekt nie jest obecnie aktywny.", "app.components.Comments.commentingDisabledProject": "Komentowanie w tym projekcie jest obecnie wyłączone.", "app.components.Comments.commentingDisabledUnverified": "{verifyIdentityLink} aby k<PERSON>.", "app.components.Comments.commentingMaybeNotPermitted": "<PERSON>e każdy ma prawo do komentowania. Proszę {signInLink}, aby s<PERSON>, czy spełniasz wymagania.", "app.components.Comments.inputsAssociatedWithProfile": "Domyślnie Twoje zgłoszenia będą powiązane z Twoim profilem, chyba że wybierzesz tę opcję.", "app.components.Comments.invisibleTitleComments": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.leastRecent": "Najświeższe", "app.components.Comments.likeComment": "Lubię ten komentarz", "app.components.Comments.mostLiked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reakcji", "app.components.Comments.mostRecent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.official": "Oficjalny", "app.components.Comments.postAnonymously": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.replyToComment": "Odpowiedz na komentarz", "app.components.Comments.reportAsSpam": "Zgłoś jako spam", "app.components.Comments.seeOriginal": "<PERSON><PERSON><PERSON><PERSON>", "app.components.Comments.seeTranslation": "Zobacz tłuma<PERSON>", "app.components.Comments.yourComment": "T<PERSON><PERSON>j komentarz", "app.components.CommonGroundResults.divisiveDescription": "Stwierdzenia, w których ludzie zgadzają się i nie zgadzają w równym stopniu:", "app.components.CommonGroundResults.divisiveTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.majorityDescription": "Ponad 60% głosowało w jedną lub drugą stronę w następujących kwestiach:", "app.components.CommonGroundResults.majorityTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CommonGroundResults.participantLabel": "uczestnik", "app.components.CommonGroundResults.participantsLabel1": "uczestnicy", "app.components.CommonGroundResults.statementLabel": "oświadczenie", "app.components.CommonGroundResults.statementsLabel1": "oświadczenia", "app.components.CommonGroundResults.votesLabe": "głosowanie", "app.components.CommonGroundResults.votesLabel1": "głosy", "app.components.CommonGroundStatements.agreeLabel": "Zgad<PERSON> się", "app.components.CommonGroundStatements.disagreeLabel": "<PERSON>e zgadzam <PERSON>ę", "app.components.CommonGroundStatements.noMoreStatements": "W tej chwili nie ma żadnych oświadczeń, na które mógłbyś odpowiedzieć", "app.components.CommonGroundStatements.noResults": "Nie ma jeszcze żadnych wyników. Upewnij się, że wziąłeś udział w fazie Common Ground i sprawdź tutaj po jej zakończeniu.", "app.components.CommonGroundStatements.unsureLabel": "<PERSON><PERSON> mam p<PERSON>", "app.components.CommonGroundTabs.resultsTabLabel": "Wyniki", "app.components.CommonGroundTabs.statementsTabLabel": "Oświadczenia", "app.components.CommunityMonitorModal.formError": "Napotkano błąd.", "app.components.CommunityMonitorModal.surveyDescription2": "Ta bieżąca ankieta śledzi, jak oceniasz zarządzanie i usługi publiczne.", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {Zajmuje <1 minutę} one {Zajmuje 1 minutę} other {Zajmuje # minut}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "Wiadomość z kodem potwierdzającym została wysłana na {userEmail}.", "app.components.ConfirmationModal.changeYourEmail": "Zmień swój adres e-mail.", "app.components.ConfirmationModal.codeInput": "Kod", "app.components.ConfirmationModal.confirmationCodeSent": "Nowy kod wysłany", "app.components.ConfirmationModal.didntGetAnEmail": "Nie otrzymałeś e-maila?", "app.components.ConfirmationModal.foundYourCode": "Znalazłeś swój kod?", "app.components.ConfirmationModal.goBack": "<PERSON><PERSON><PERSON><PERSON>.", "app.components.ConfirmationModal.sendEmailWithCode": "Wyślij e-mail z kodem", "app.components.ConfirmationModal.sendNewCode": "Wyślij nowy kod.", "app.components.ConfirmationModal.verifyAndContinue": "Sprawdź i kontynuuj", "app.components.ConfirmationModal.wrongEmail": "Niewłaściwy e-mail?", "app.components.ConsentManager.Banner.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Banner.ariaButtonClose2": "Odrzucić politykę i zamknąć baner", "app.components.ConsentManager.Banner.close": "Zamknij", "app.components.ConsentManager.Banner.mainText": "Przegl<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ę, zgadzasz się na naszą {policyLink}.", "app.components.ConsentManager.Banner.manage": "Zarządzaj", "app.components.ConsentManager.Banner.policyLink": "Polityka ciasteczek", "app.components.ConsentManager.Banner.reject": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "Wykorzystujemy to do personalizacji i pomiaru skuteczności kampanii reklamowych naszej strony internetowej. Nie pokażemy żadnej reklamy na tej platformie, ale poniższe usługi mogą zaoferować Ci spersonalizowaną reklamę w oparciu o strony, które odwiedzasz na naszej stronie.", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "Zezwól", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "Analityka", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "<PERSON><PERSON><PERSON><PERSON> tego <PERSON>, aby le<PERSON><PERSON>, w jaki spos<PERSON><PERSON> korzystasz z platformy, w celu udogodnienia Twojej nawigacji. Informacje te są wykorzystywane wyłącznie w analizie masowej i w żaden sposób nie służą do śledzenia poszczególnych osób.", "app.components.ConsentManager.Modal.PreferencesDialog.back": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "<PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "<PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "Jest to niezbędne do włączenia i monitorowania podstawowych funkcji strony internetowej. Niektóre z wymienionych tu narzędzi mogą nie mieć zastosowania wobec ciebie. Aby uzyskać więcej informacji prosimy o zapoznanie się z naszą polityką ciasteczek.", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "Google Tag Manager ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "<PERSON><PERSON> z platformy, po zarejestrowaniu się zapisujemy uwierzytelniający plik cookie oraz język, w którym użytkownik korzysta z platformy.", "app.components.ConsentManager.Modal.PreferencesDialog.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ConsentManager.Modal.PreferencesDialog.title": "<PERSON>je preferencje dot<PERSON>cz<PERSON>ce ciasteczek", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "Narzędzia", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "Zastrzeżenie dotyczące przesyłania treści", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tre<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że nie naruszają one żadnych przepisów ani praw osó<PERSON> trzecich, takich jak prawa własności intelektualnej, prawa do prywatności, prawa do tajemnic handlowych itp. W związku z tym, prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te treści, zobowiązujesz się ponieść pełną i wyłączną odpowiedzialność za wszelkie bezpośrednie i pośrednie szkody wynikające z przesłanych treści. Ponadto zobowiązujesz się zwolnić właściciela platformy i Go Vocal z odpowiedzialności za wszelkie roszczenia osób trzecich lub zobowiązania wobec osób trzecich oraz wszelkie związane z tym koszty, które mogą powstać lub wynikać z przesłanych treści.", "app.components.ContentUploadDisclaimer.onAccept": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ContentUploadDisclaimer.onCancel": "<PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "Powiedz nam dlaczego", "app.components.CustomFieldsForm.addressInputAriaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.CustomFieldsForm.addressInputPlaceholder6": "Wprowadź adres...", "app.components.CustomFieldsForm.adminFieldTooltip": "Pole widoczne tylko dla administratorów", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "Wszystkie odpowiedzi udzielone w tej ankiecie są anonimowe.", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "Do utworzenia wielokąta wymagane są co najmniej trzy punkty.", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "Dla linii wymagane są co najmniej dwa punkty.", "app.components.CustomFieldsForm.attachmentRequired": "Wymagany jest co najmniej jeden załącznik", "app.components.CustomFieldsForm.authorFieldLabel": "Autor", "app.components.CustomFieldsForm.authorFieldPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby w<PERSON><PERSON><PERSON> według adresu e-mail lub nazwy użytkownika...", "app.components.CustomFieldsForm.back": "Powró<PERSON>", "app.components.CustomFieldsForm.budgetFieldLabel": "Budżet", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "<PERSON><PERSON><PERSON><PERSON>, aby ją na<PERSON>. Następnie przeciągnij <PERSON>, aby je przes<PERSON>.", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "Kliknij na mapę lub wpisz adres poniżej, aby dodać odpowiedź.", "app.components.CustomFieldsForm.confirm": "Potwierdź", "app.components.CustomFieldsForm.descriptionMinLength": "Opis musi zawierać co najmniej {min} znaków.", "app.components.CustomFieldsForm.descriptionRequired": "Opis jest wymagany", "app.components.CustomFieldsForm.fieldMaximumItems": "Co najwyżej {maxSelections, plural, one {# opcja} other {# opcje}} mogą być wybrane dla pola \"{fieldName}\".", "app.components.CustomFieldsForm.fieldMinimumItems": "Co najmniej {minSelections, plural, one {# opcja} other {# opcje}} mogą być wybrane dla pola \"{fieldName}\".", "app.components.CustomFieldsForm.fieldRequired": "<PERSON> \"{field<PERSON><PERSON>}\" jest wymagane", "app.components.CustomFieldsForm.fileSizeLimit": "Limit rozmiaru pliku wynosi {maxFileSize} MB.", "app.components.CustomFieldsForm.imageRequired": "<PERSON><PERSON><PERSON> jest wymagany", "app.components.CustomFieldsForm.minimumCoordinates2": "Wymagana jest minimalna liczba punktów na mapie {numPoints} .", "app.components.CustomFieldsForm.notPublic1": "*Ta odpowiedź zostanie udostępniona tylko kierownikom projektów, a nie publicznie.", "app.components.CustomFieldsForm.otherArea": "G<PERSON><PERSON>ś indziej", "app.components.CustomFieldsForm.progressBarLabel": "Postęp", "app.components.CustomFieldsForm.removeAnswer": "Usuń odpowiedź", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "*Wybierz dowolną liczbę", "app.components.CustomFieldsForm.selectBetween": "*<PERSON><PERSON><PERSON><PERSON> pomiędzy opcjami {minItems} i {maxItems} .", "app.components.CustomFieldsForm.selectExactly2": "*<PERSON><PERSON><PERSON><PERSON> dokładnie {selectExactly, plural, one {# opcja} other {# opcje}}", "app.components.CustomFieldsForm.selectMany": "*Wybierz dowolną liczbę", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "<PERSON><PERSON><PERSON><PERSON>, aby ją na<PERSON>. Następnie przecią<PERSON>j <PERSON>, aby je przes<PERSON>.", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "<PERSON><PERSON><PERSON><PERSON>, aby r<PERSON>.", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "Dotk<PERSON>j <PERSON>, aby dodać odpowiedź.", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "Dotknij mapy lub wpisz adres poniżej, aby do<PERSON><PERSON> odpowiedź.", "app.components.CustomFieldsForm.tapToAddALine": "<PERSON><PERSON><PERSON><PERSON>, aby dodać linię", "app.components.CustomFieldsForm.tapToAddAPoint": "<PERSON><PERSON><PERSON><PERSON>, aby do<PERSON><PERSON> punkt", "app.components.CustomFieldsForm.tapToAddAnArea": "<PERSON><PERSON><PERSON><PERSON>, aby do<PERSON><PERSON> obszar", "app.components.CustomFieldsForm.titleMaxLength": "Tytuł musi mieć co najwyżej {max} znaków.", "app.components.CustomFieldsForm.titleMinLength": "Tytuł musi zawierać co najmniej {min} znaków.", "app.components.CustomFieldsForm.titleRequired": "<PERSON><PERSON><PERSON> jest wymagany", "app.components.CustomFieldsForm.topicRequired": "Wym<PERSON>y jest co najmniej jeden znacznik", "app.components.CustomFieldsForm.typeYourAnswer": "Wpisz swoją odpowiedź", "app.components.CustomFieldsForm.typeYourAnswerRequired": "Musisz wpisać swoją odpowiedź", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* Prześlij plik zip zawierający jeden lub więcej plików shapefiles.", "app.components.CustomFieldsForm.validCordinatesTooltip2": "<PERSON><PERSON><PERSON> lokalizacja nie jest wy<PERSON><PERSON><PERSON><PERSON> wśr<PERSON>d opcji podczas wpisywania, moż<PERSON><PERSON> dodać prawidłowe współrzędne w formacie \"s<PERSON><PERSON><PERSON><PERSON>, dług<PERSON><PERSON><PERSON> geograficzna\", aby <PERSON><PERSON><PERSON><PERSON><PERSON> dokładną lokalizację (np. -33.019808, -71.495676).", "app.components.ErrorBoundary.errorFormErrorFormEntry": "Niektóre pola były niepoprawne. Proszę popraw błędy i spróbuj ponownie.", "app.components.ErrorBoundary.errorFormErrorGeneric": "Podczas przesyłania raportu wystąpił nieznany błąd. Proszę spróbuj jeszcze raz.", "app.components.ErrorBoundary.errorFormLabelClose": "Zamknij", "app.components.ErrorBoundary.errorFormLabelComments": "Co się stało?", "app.components.ErrorBoundary.errorFormLabelEmail": "E-mail", "app.components.ErrorBoundary.errorFormLabelName": "Nazwa", "app.components.ErrorBoundary.errorFormLabelSubmit": "Prześ<PERSON>j", "app.components.ErrorBoundary.errorFormSubtitle": "Nasz zespół został powiadomiony.", "app.components.ErrorBoundary.errorFormSubtitle2": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> p<PERSON>, pow<PERSON><PERSON> nam, co się stało.", "app.components.ErrorBoundary.errorFormSuccessMessage": "Twoja informacja zwrotna została wysłana. Dziękujemy!", "app.components.ErrorBoundary.errorFormTitle": "Wygląda na to, że jest jakiś problem.", "app.components.ErrorBoundary.genericErrorWithForm": "Wystąpił błąd i nie możemy wyświetlić tej treści. Proszę spróbow<PERSON> ponownie, lub {openForm}", "app.components.ErrorBoundary.openFormText": "pomóż nam to zrozumieć", "app.components.ErrorToast.budgetExceededError": "<PERSON>e masz wystarczającego budżetu", "app.components.ErrorToast.votesExceededError": "<PERSON>e masz wystarczającej liczby głosów", "app.components.EventAttendanceButton.forwardToFriend": "Prześlij dalej do znajomego", "app.components.EventAttendanceButton.maxRegistrationsReached": "Osiągnięto maksymalną liczbę rejestracji na wydarzenie. Nie ma już wolnych miejsc.", "app.components.EventAttendanceButton.register": "Zarejestruj się", "app.components.EventAttendanceButton.registered": "Zarejestrowany", "app.components.EventAttendanceButton.seeYouThere": "Do zobaczenia!", "app.components.EventAttendanceButton.seeYouThereName": "<PERSON> zobaczenia, {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "Mniej informacji o zdarzeniach stało się widocznych.", "app.components.EventCard.a11y_moreContentVisible": "Widocznych stało się więcej informacji o wydarzeniu.", "app.components.EventCard.a11y_readMore": "Przeczytaj więcej o wydarzeniu \"{eventTitle}\".", "app.components.EventCard.endsAt": "Kończy się o", "app.components.EventCard.readMore": "Czytaj więcej", "app.components.EventCard.showLess": "Pokaż mniej", "app.components.EventCard.showMore": "Pokaż więcej", "app.components.EventCard.startsAt": "<PERSON>aczyna się o", "app.components.EventPreviews.eventPreviewContinuousTitle2": "Nadchodzące i trwające wydarzenia w ramach tego projektu", "app.components.EventPreviews.eventPreviewTimelineTitle3": "Nadchodzące i trwające wydarzenia w tej fazie", "app.components.FileUploader.a11y_file": "Plik:", "app.components.FileUploader.a11y_filesToBeUploaded": "Pliki do załadowania: {fileNames}", "app.components.FileUploader.a11y_noFiles": "<PERSON>e dodano żadnych plików.", "app.components.FileUploader.a11y_removeFile": "Usuń ten plik", "app.components.FileUploader.fileInputDescription": "<PERSON><PERSON><PERSON><PERSON>, aby wy<PERSON>ć plik", "app.components.FileUploader.fileUploadLabel": "Załączniki (maks. 50MB)", "app.components.FileUploader.file_too_large2": "Pliki wię<PERSON>ze niż {maxSizeMb}MB nie są dozwolone.", "app.components.FileUploader.incorrect_extension": "{fileName} nie jest obsługiwany przez nasz system, nie zostanie wgrany.", "app.components.FilterBoxes.a11y_allFilterSelected": "Wybrane statusy filtrów: wszystkie", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# inicjatywa} few {# inicjatywy} many {# inicjatyw} other {# inicjatywy}}", "app.components.FilterBoxes.a11y_removeFilter": "Usuń filtr", "app.components.FilterBoxes.a11y_selectedFilter": "Wybrane statusy filtrów: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "Wybrano: {numberOfSelectedTopics, plural, =0 {zero filtrów tematu} one {jeden filtr tematu} few {# filtry tematu} many {# filtrów tematu} other {# filtry tematu}}. {selectedTopicNames}", "app.components.FilterBoxes.all": "Wszystkie", "app.components.FilterBoxes.areas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.FilterBoxes.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.components.FilterBoxes.noValuesFound": "<PERSON><PERSON> dostępny<PERSON> wartości.", "app.components.FilterBoxes.showLess": "Pokaż mniej", "app.components.FilterBoxes.showTagsWithNumber": "Pokaż wszystkie ({numberTags})", "app.components.FilterBoxes.statusTitle": "Status", "app.components.FilterBoxes.topicsTitle": "<PERSON><PERSON><PERSON>", "app.components.FiltersModal.filters": "Filtry", "app.components.FolderFolderCard.a11y_folderDescription": "Opis folderu:", "app.components.FolderFolderCard.a11y_folderTitle": "<PERSON><PERSON><PERSON>:", "app.components.FolderFolderCard.archived": "Zarchiwizowany", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, one {# projekt} few {# projekty} many {# projektów} other {# projekty}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "Typu pola nie można zmienić po dokonaniu zgłoszenia.", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "<PERSON><PERSON>", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "Autozapis", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "Automatyczne zapisywanie jest domyślnie włączone, gdy otwierasz edytor formularzy. Za każdym razem, gdy zamkniesz panel ustawień pola za pomocą przycisku \"X\", automatycznie uruchomi się zapisywanie.", "app.components.GanttChart.timeRange.month": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.quarter": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GanttChart.timeRange.timeRangeMultiyear": "Wieloletni", "app.components.GanttChart.timeRange.year": "Rok", "app.components.GanttChart.today": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBack": "<PERSON><PERSON><PERSON><PERSON>", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "Wróć do poprzedniej strony", "app.components.HookForm.Feedback.errorTitle": "Jest problem", "app.components.HookForm.Feedback.submissionError": "Spróbuj ponownie. Je<PERSON>li problem nadal występuje, skontaktuj się z nami", "app.components.HookForm.Feedback.submissionErrorTitle": "Wystąpił problem z naszej strony, przepraszamy", "app.components.HookForm.Feedback.successMessage": "Formularz został pomyślnie przesłany", "app.components.HookForm.PasswordInput.passwordLabel": "<PERSON><PERSON><PERSON>", "app.components.HorizontalScroll.scrollLeftLabel": "Przewiń w lewo.", "app.components.HorizontalScroll.scrollRightLabel": "Przewiń w prawo.", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} pomysły zostały załadowane.", "app.components.IdeaCards.filters": "Filtry", "app.components.IdeaCards.filters.mostDiscussed": "Najczęściej omawiane", "app.components.IdeaCards.filters.newest": "Nowy", "app.components.IdeaCards.filters.oldest": "Stary", "app.components.IdeaCards.filters.popular": "Najbardziej lubiany", "app.components.IdeaCards.filters.random": "Losowo", "app.components.IdeaCards.filters.sortBy": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "Sortowanie zmieniono na: {currentSortType}", "app.components.IdeaCards.filters.trending": "<PERSON><PERSON><PERSON>", "app.components.IdeaCards.showMore": "Pokaż więcej", "app.components.IdeasMap.a11y_hideIdeaCard": "Ukryj kartkę z pomysłem.", "app.components.IdeasMap.a11y_mapTitle": "Przegląd mapy", "app.components.IdeasMap.clickOnMapToAdd": "Kliknij na mapę, aby dodać swój wkład", "app.components.IdeasMap.clickOnMapToAddAdmin2": "Jako administrator m<PERSON><PERSON><PERSON><PERSON>, aby do<PERSON><PERSON> swój wkład, nawet jeśli ta faza nie jest aktywna.", "app.components.IdeasMap.filters": "Filtry", "app.components.IdeasMap.multipleInputsAtLocation": "Wiele wejść w tej lokalizacji", "app.components.IdeasMap.noFilteredResults": "Wybrane filtry nie zwróciły żadnych wyników", "app.components.IdeasMap.noResults": "Nie znaleziono żadnych wyników", "app.components.IdeasMap.or": "lub", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {, brak polubień.} one {1 dislike.} other {, # dislikes.}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {, brak polubień.} one {, 1 polubienie.} other {, # likes.}}", "app.components.IdeasMap.signInLinkText": "z<PERSON>uj się", "app.components.IdeasMap.signUpLinkText": "zarejestruj się", "app.components.IdeasMap.submitIdea2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane we<PERSON>", "app.components.IdeasMap.tapOnMapToAdd": "Kliknij na mapę, aby dodać swój wkład", "app.components.IdeasMap.tapOnMapToAddAdmin2": "Jako administrator m<PERSON><PERSON><PERSON><PERSON>, aby do<PERSON><PERSON> swój wkład, nawet jeśli ta faza nie jest aktywna.", "app.components.IdeasMap.userInputs2": "Dane wejściowe od uczestników", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, brak komentarzy} one {, 1 komentarz} other {, # komentarzy}}", "app.components.IdeasShow.bodyTitle": "Opis", "app.components.IdeasShow.deletePost": "Usuń", "app.components.IdeasShow.editPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.goBack": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.moreOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>ji", "app.components.IdeasShow.or": "lub", "app.components.IdeasShow.proposedBudgetTitle": "Proponowany budżet", "app.components.IdeasShow.reportAsSpam": "Zgłoś jako spam", "app.components.IdeasShow.send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShow.skipSharing": "Pomijam to, zrobię to później", "app.components.IdeasShowPage.signIn2": "<PERSON><PERSON><PERSON><PERSON>", "app.components.IdeasShowPage.sorryNoAccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nie możesz uzyskać dostępu do tej strony. Aby uzyskać do niej dostęp, musisz się zalogować lub zare<PERSON>.", "app.components.LocationInput.noOptions": "<PERSON>rak opcji", "app.components.Modal.closeWindow": "Zamknij okno", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "Wprowadź nowy adres e-mail", "app.components.PageNotFound.goBackToHomePage": "Powrót do strony głównej", "app.components.PageNotFound.notFoundTitle": "Nie znaleziono strony", "app.components.PageNotFound.pageNotFoundDescription": "Nie udało się znaleźć żądanej strony.", "app.components.PagesForm.descriptionMissingOneLanguageError": "Podaj tre<PERSON>ć w przynajmniej jednym języku", "app.components.PagesForm.editContent": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.fileUploadLabel": "Załączniki (maks. 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "Dodane pliki zostaną pokazane na dole tej strony.", "app.components.PagesForm.navbarItemTitle": "Nazwa w pasku nawigacyjnym", "app.components.PagesForm.pageTitle": "<PERSON><PERSON><PERSON>", "app.components.PagesForm.savePage": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PagesForm.saveSuccess": "Strona została pomyślnie zapisana.", "app.components.PagesForm.titleMissingOneLanguageError": "Podaj tytuł w przynajmniej jednym języku", "app.components.Pagination.back": "Poprzednia strona", "app.components.Pagination.next": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "W<PERSON><PERSON><PERSON><PERSON>ś {votesCast}, co przekracza limit {votesLimit}. Usuń niektóre produkty z koszyka i spróbuj ponownie.", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} left", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "<PERSON><PERSON><PERSON> w<PERSON> co najmniej {votesMinimum} , zani<PERSON> będziesz mógł przesłać swój koszyk.", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "Przed wysłaniem musisz wybrać co najmniej jedną opcję.", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "<PERSON><PERSON><PERSON> dodać coś do koszyka przed jego wysłaniem.", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {<PERSON><PERSON>yt<PERSON>} other {# nie ma {totalNumberOfVotes, plural, one {1 kredyt} other {# kredyty}} pozostało}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {Brak punktów} other {# z {totalNumberOfVotes, plural, one {1 punkt} other {# punktów}} pozostało}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {Brak żetonów} other {# z {totalNumberOfVotes, plural, one {1 żeton} other {# żetonów}} pozostało}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {Brak głosów} other {# z {totalNumberOfVotes, plural, one {1 głos} other {# głosy}} pozostało}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# votes} one {# vote} other {# votes}} cast", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "Oddałeś {votesCast} głosów, co przekracza limit {votesLimit}. Usuń niektóre głosy i spróbuj ponownie.", "app.components.ParticipationCTABars.addInput": "<PERSON><PERSON><PERSON> da<PERSON>", "app.components.ParticipationCTABars.allocateBudget": "Przydziel swój budżet", "app.components.ParticipationCTABars.budgetSubmitSuccess": "Twój budżet został przesłany pomyślnie.", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "Otwarte na uczestnictwo", "app.components.ParticipationCTABars.poll": "<PERSON>ź udział w ankiecie", "app.components.ParticipationCTABars.reviewDocument": "Przejrzyj dokument", "app.components.ParticipationCTABars.seeContributions": "Zobacz wkłady", "app.components.ParticipationCTABars.seeEvents3": "<PERSON><PERSON><PERSON>z w<PERSON>arzenia", "app.components.ParticipationCTABars.seeIdeas": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeInitiatives": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeIssues": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seePetitions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeProjects": "Zobacz proje<PERSON>y", "app.components.ParticipationCTABars.seeProposals": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.seeQuestions": "<PERSON>ob<PERSON>z <PERSON>", "app.components.ParticipationCTABars.submit": "Zgłoś się", "app.components.ParticipationCTABars.takeTheSurvey": "<PERSON>ź udział w ankiecie", "app.components.ParticipationCTABars.userHasParticipated": "W<PERSON><PERSON><PERSON>ś/Wziąłeś udział w tym projekcie.", "app.components.ParticipationCTABars.viewInputs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane we<PERSON>", "app.components.ParticipationCTABars.volunteer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ParticipationCTABars.votesCounter.vote": "głosowanie", "app.components.ParticipationCTABars.votesCounter.votes": "głosy", "app.components.PasswordInput.a11y_passwordHidden": "Hasło ukryte", "app.components.PasswordInput.a11y_passwordVisible": "<PERSON><PERSON><PERSON> w<PERSON>", "app.components.PasswordInput.a11y_strength1Password": "<PERSON><PERSON><PERSON> hasło", "app.components.PasswordInput.a11y_strength2Password": "<PERSON><PERSON><PERSON> has<PERSON>o", "app.components.PasswordInput.a11y_strength3Password": "<PERSON><PERSON><PERSON> has<PERSON>o", "app.components.PasswordInput.a11y_strength4Password": "<PERSON><PERSON><PERSON> hasło", "app.components.PasswordInput.a11y_strength5Password": "<PERSON><PERSON><PERSON> hasło", "app.components.PasswordInput.hidePassword": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "<PERSON><PERSON><PERSON> k<PERSON> (min. {minimumPasswordLength} znaków)", "app.components.PasswordInput.minimumPasswordLengthError": "<PERSON><PERSON><PERSON>, które ma co najmniej {minimumPasswordLength} znaków", "app.components.PasswordInput.passwordEmptyError": "Wprowadź swoje hasło", "app.components.PasswordInput.passwordStrengthTooltip1": "<PERSON><PERSON> wz<PERSON><PERSON><PERSON>ć swoje hasło:", "app.components.PasswordInput.passwordStrengthTooltip2": "Użyj kombinacji następujących po sobie małych liter, wielkich liter, cyfr, znaków specjalnych i interpunkcyjnych", "app.components.PasswordInput.passwordStrengthTooltip3": "Unikaj słów pospolitych lub łatwych do odgadnięcia", "app.components.PasswordInput.passwordStrengthTooltip4": "Zwięks<PERSON> dł<PERSON>", "app.components.PasswordInput.showPassword": "<PERSON><PERSON><PERSON> hasło", "app.components.PasswordInput.strength1Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength2Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength3Password": "Średnie", "app.components.PasswordInput.strength4Password": "<PERSON><PERSON><PERSON>", "app.components.PasswordInput.strength5Password": "<PERSON><PERSON><PERSON>", "app.components.PostCardsComponents.list": "Lista", "app.components.PostCardsComponents.map": "Mapa", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "<PERSON><PERSON><PERSON> aktualizację", "app.components.PostComponents.OfficialFeedback.cancel": "<PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "Usuń", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "Czy na pewno usunąć oficjalną aktualizację?", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "<PERSON><PERSON><PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.lastEdition": "Ostatnio edytowane {date}", "app.components.PostComponents.OfficialFeedback.lastUpdate": "Ostatnio aktualizowane: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "Ofic<PERSON>lne", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, jak ludzie widzą Twoje imię", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "Oficjalna aktualizacja imienia autora", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "Oficjalna autoryzacja tekstu", "app.components.PostComponents.OfficialFeedback.officialUpdates": "Oficjalne aktualizacje", "app.components.PostComponents.OfficialFeedback.postedOn": "Opublikowane {date}", "app.components.PostComponents.OfficialFeedback.publishButtonText": "Opublikuj", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "Pokaż poprzednie aktualizacje", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "Zaktualizuj...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zaistniał pewien problem", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "Zaktuali<PERSON><PERSON>", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "Twoja aktualizacja została pomyślnie opublikowana!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "<PERSON><PERSON><PERSON>j mój wkład '{postTitle}' w {postUrl}!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "<PERSON>rz<PERSON>j mój wkład: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "<PERSON>rz<PERSON>j mój wkład: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "Co sądzisz o tym pomyśle? Zagłosuj na niego i podziel się dyskusją w {postUrl}, aby twój głos był słyszalny!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "<PERSON>rz<PERSON>j mój pomysł: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "<PERSON>rz<PERSON>j mój pomysł: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "Co sądzisz o tym pomyśle? Zagłosuj na niego i podziel się dyskusją w {postUrl}, aby twój głos był słyszalny!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "<PERSON>rz<PERSON>j moją propozycję: {postTitle}.", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> moją inicjatywę: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "Zgłosiłem sprawę '{postTitle}' w {postUrl}!", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "<PERSON>łaśnie zgłosiłem sprawę: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "<PERSON>łaśnie zgłosiłem sprawę: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON> mój wariant '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON> mój wariant: {postTitle}.", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON> mój wariant: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> moją petycję '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> moją petycję: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> moją petycję: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "<PERSON>rz<PERSON>j mój projekt '{postTitle}' w {postUrl}!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "<PERSON>rzyj mój projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "<PERSON>rzyj mój projekt: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> moją propozycję '{postTitle}' na {postUrl}!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> moją propozycję: {postTitle}.", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "<PERSON>łaśnie opublikowałem propozycję dla {orgName}: {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "Dołącz do dyskusji na temat tego pytania '{postTitle}' na stronie {postUrl}!", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "<PERSON><PERSON> udział w dyskusji: {postTitle}.", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "<PERSON><PERSON> udział w dyskusji: {postTitle}.", "app.components.PostComponents.SharingModalContent.twitterMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> na {postTitle} w", "app.components.PostComponents.linkToHomePage": "Link do strony głównej", "app.components.PostComponents.readMore": "Przeczytaj więcej...", "app.components.PostComponents.topics": "<PERSON><PERSON><PERSON>", "app.components.ProjectArchivedIndicator.archivedProject": "<PERSON><PERSON><PERSON>, nie możesz już uczestniczy<PERSON> w tym projekcie, ponieważ został on zarchiwizowany", "app.components.ProjectArchivedIndicator.previewProject": "Projekt wstępny:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "Widoczne tylko dla moderatorów i osób z linkiem podglądu.", "app.components.ProjectCard.a11y_projectDescription": "Opis projektu:", "app.components.ProjectCard.a11y_projectTitle": "Tytuł projektu:", "app.components.ProjectCard.addYourOption": "<PERSON><PERSON><PERSON> swoją opcję", "app.components.ProjectCard.allocateYourBudget": "Przydziel swój budżet", "app.components.ProjectCard.archived": "Zarchiwizowany", "app.components.ProjectCard.comment": "Skomentuj", "app.components.ProjectCard.contributeYourInput": "<PERSON><PERSON><PERSON><PERSON>ć swój wkład", "app.components.ProjectCard.finished": "Zakończone", "app.components.ProjectCard.joinDiscussion": "Przyłącz się do dyskusji", "app.components.ProjectCard.learnMore": "Dowiedz się więcej", "app.components.ProjectCard.reaction": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "<PERSON>rz<PERSON><PERSON><PERSON><PERSON> raport", "app.components.ProjectCard.reviewDocument": "Przejrzyj dokument", "app.components.ProjectCard.submitAnIssue": "Zgłoś sprawę", "app.components.ProjectCard.submitYourIdea": "Zgłoś swój pomysł", "app.components.ProjectCard.submitYourInitiative": "Zgłoś swoją inicjatywę", "app.components.ProjectCard.submitYourPetition": "Prześlij swoją petycję", "app.components.ProjectCard.submitYourProject": "Prześlij swój projekt", "app.components.ProjectCard.submitYourProposal": "Prześlij swoją propozycję", "app.components.ProjectCard.takeThePoll": "<PERSON><PERSON> udział w badaniu", "app.components.ProjectCard.takeTheSurvey": "<PERSON>ź udział w ankiecie", "app.components.ProjectCard.viewTheContributions": "Zobacz wkład", "app.components.ProjectCard.viewTheIdeas": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheInitiatives": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheIssues": "<PERSON><PERSON><PERSON><PERSON> sprawy", "app.components.ProjectCard.viewTheOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewThePetitions": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheProjects": "Zobacz proje<PERSON>y", "app.components.ProjectCard.viewTheProposals": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.viewTheQuestions": "<PERSON>ob<PERSON>z <PERSON>", "app.components.ProjectCard.vote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# komentarz} few {# komentarze} many {# komentarzy} other {# kometarz}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, no {# wkładu} one {# wkład} few {# wkłady} many {# wkładów} other {# wkłady}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {nie ma jeszcze żadnych pomysłów} one {# pomysł} few {# pomysły} many {# pomysłów} other {# pomysły}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# inicjatywy} one {# inicjatywy} other {# inicjatywy}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# sprawa} few {# sprawy} many {# spraw} other {# sprawy}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# opcja} few {# opcje} many {# opcji} other {# opcje}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# petycje} one {# petycje} other {# petycje}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# projekt} few {# projekty} many {# projektów} other {# projekty}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# propozycje} one {# propozycje} other {# propozycje}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# pytanie} few {# pytania} many {# pytań} other {# pytania}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# comments} one {# komentarze} other {# comments}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# inputs} one {# input} other {# inputs}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, one {# projekt} few {# projekty} many {# projektów} other {# projektów}}", "app.components.ProjectFolderCards.components.Topbar.all": "Wszystkie", "app.components.ProjectFolderCards.components.Topbar.archived": "Zarchiwizowany", "app.components.ProjectFolderCards.components.Topbar.draft": "Szkic", "app.components.ProjectFolderCards.components.Topbar.filterBy": "<PERSON><PERSON><PERSON><PERSON>", "app.components.ProjectFolderCards.components.Topbar.published2": "Opublikowano", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "Tag", "app.components.ProjectFolderCards.noProjectYet": "Nie ma jeszcze żadnego projektu", "app.components.ProjectFolderCards.noProjectsAvailable": "Brak dostępnych projektów", "app.components.ProjectFolderCards.showMore": "Pokaż więcej", "app.components.ProjectFolderCards.stayTuned": "Bądź na bieżąco, projekt wkrótce się pojawi.", "app.components.ProjectFolderCards.tryChangingFilters": "Spróbuj zmienić wybrane filtry.", "app.components.ProjectTemplatePreview.alsoUsedIn": "Również używane w tych miastach", "app.components.ProjectTemplatePreview.copied": "Skopiowany", "app.components.ProjectTemplatePreview.copyLink": "Skopiuj link", "app.components.QuillEditor.alignCenter": "Wyśrodkowanie", "app.components.QuillEditor.alignLeft": "Wyrównanie do lewej", "app.components.QuillEditor.alignRight": "Wyrównanie do prawej", "app.components.QuillEditor.bold": "Pogrubienie", "app.components.QuillEditor.clean": "Usuń formatowanie", "app.components.QuillEditor.customLink": "Dodaj przycisk", "app.components.QuillEditor.customLinkPrompt": "Podaj link:", "app.components.QuillEditor.edit": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.image": "Prześ<PERSON>j obrazek", "app.components.QuillEditor.imageAltPlaceholder": "Krótki opis zdjęcia", "app.components.QuillEditor.italic": "Ku<PERSON>ywa", "app.components.QuillEditor.link": "Dodaj link", "app.components.QuillEditor.linkPrompt": "Podaj link:", "app.components.QuillEditor.normalText": "Normalny", "app.components.QuillEditor.orderedList": "Uszeregowana lista", "app.components.QuillEditor.remove": "Usuń", "app.components.QuillEditor.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.QuillEditor.subtitle": "Podtytuł", "app.components.QuillEditor.title": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.unorderedList": "Nieuporządkowana lista", "app.components.QuillEditor.video": "<PERSON><PERSON><PERSON>", "app.components.QuillEditor.videoPrompt": "Wprowadź wideo:", "app.components.QuillEditor.visitPrompt": "Odwiedź link:", "app.components.ReactionControl.completeProfileToReact": "Uzupełnij swój profil, a<PERSON> z<PERSON>ago<PERSON>", "app.components.ReactionControl.dislike": "<PERSON><PERSON> l<PERSON>", "app.components.ReactionControl.dislikingDisabledMaxReached": "Osiągnąłeś maksymalną liczbę polubień na {projectName}.", "app.components.ReactionControl.like": "Na przykład", "app.components.ReactionControl.likingDisabledMaxReached": "Osiągnąłeś maksymalną liczbę polubień na {projectName}.", "app.components.ReactionControl.reactingDisabledFutureEnabled": "Reagowanie zostanie włączone po rozpoczęciu tej fazy", "app.components.ReactionControl.reactingDisabledPhaseOver": "Reakcja w tej fazie nie jest już możliwa", "app.components.ReactionControl.reactingDisabledProjectInactive": "Nie możesz już reagować na pomysły w {projectName}.", "app.components.ReactionControl.reactingNotEnabled": "Reagowanie nie jest obecnie włączone dla tego projektu", "app.components.ReactionControl.reactingNotPermitted": "Reagowanie jest włączone tylko dla niektórych grup", "app.components.ReactionControl.reactingNotSignedIn": "<PERSON><PERSON><PERSON><PERSON>, a<PERSON>.", "app.components.ReactionControl.reactingPossibleLater": "Reagowanie rozpocznie się na stronie {enabledFromDate}", "app.components.ReactionControl.reactingVerifyToReact": "Zweryfikuj swoj<PERSON><PERSON>, aby mó<PERSON>.", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "Data wydarzenia: {startDate} na {startTime} do {endDate} na {endTime}.", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "Data wydarzenia: {eventDate} od {startTime} do {endTime}.", "app.components.Sharing.linkCopied": "<PERSON>", "app.components.Sharing.or": "lub", "app.components.Sharing.share": "Podziel się", "app.components.Sharing.shareByEmail": "Udostępnij za pomocą poczty elektronicznej", "app.components.Sharing.shareByLink": "Skopiuj link", "app.components.Sharing.shareOnFacebook": "Udostępnij na Facebooku", "app.components.Sharing.shareOnTwitter": "Udostępnij na Twitterze", "app.components.Sharing.shareThisEvent": "Udostępnij to wydarzenie", "app.components.Sharing.shareThisFolder": "Podziel się", "app.components.Sharing.shareThisProject": "Podziel się tym projektem", "app.components.Sharing.shareViaMessenger": "Udostępnij za pomocą Messengera", "app.components.Sharing.shareViaWhatsApp": "Udostępniaj przez WhatsApp", "app.components.SideModal.closeButtonAria": "Zamknij", "app.components.StatusModule.futurePhase": "Oglądasz fazę, która jeszcze się nie rozpoczęła. Będziesz mógł wziąć w niej udział po jej rozpo<PERSON>.", "app.components.StatusModule.modifyYourSubmission1": "Zmodyfikuj swoje zgłoszenie", "app.components.StatusModule.submittedUntil3": "Twój głos może zostać oddany do", "app.components.TopicsPicker.numberOfSelectedTopics": "Zaznaczono {numberOfSelectedTopics, plural, =0 {zero tematów} one {jeden temat} few {# tematy} many {# tematów} other {# tematy}}. {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "Rozwiń obraz", "app.components.UI.MoreActionsMenu.moreOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>ji", "app.components.UI.MoreActionsMenu.showMoreActions": "Pokaż więcej działań", "app.components.UI.PhaseFilter.noAppropriatePhases": "Nie znaleziono odpowiednich faz dla tego projektu", "app.components.UI.RemoveImageButton.a11y_removeImage": "Usuń", "app.components.UI.TranslateButton.original": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.UI.TranslateButton.translate": "Prz<PERSON><PERSON><PERSON><PERSON>", "app.components.Unauthorized.additionalInformationRequired": "<PERSON><PERSON> w<PERSON><PERSON>ć udział w konkursie, musisz podać dodatkowe informacje.", "app.components.Unauthorized.completeProfile": "Pełny profil", "app.components.Unauthorized.completeProfileTitle": "Uzupełnij swój profil, aby w<PERSON><PERSON><PERSON> udział", "app.components.Unauthorized.noPermission": "<PERSON>e masz uprawnień do oglądania tej strony", "app.components.Unauthorized.notAuthorized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nie masz uprawnień do dostępu do tej strony.", "app.components.Upload.errorImageMaxSizeExceeded": "Wybrany obraz jest wię<PERSON><PERSON> niż {maxFileSize}MB", "app.components.Upload.errorImagesMaxSizeExceeded": "<PERSON><PERSON> obraz jest wię<PERSON><PERSON> lub kilka wybranych obrazów są większe niż {maxFileSize}MB", "app.components.Upload.onlyOneImage": "Można przesłać tylko 1 zdjęcie", "app.components.Upload.onlyXImages": "<PERSON><PERSON><PERSON><PERSON>rz<PERSON> tylko {maxItemsCount} zd<PERSON><PERSON><PERSON>", "app.components.Upload.remaining": "pozostało", "app.components.Upload.uploadImageLabel": "<PERSON><PERSON><PERSON><PERSON> (maks. {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "<PERSON><PERSON><PERSON><PERSON> jedno lub więcej zdj<PERSON>ć", "app.components.UpsellTooltip.tooltipContent": "Ta funkcja nie jest uwzględniona w Twoim bieżącym planie. Porozmawiaj ze swoim Government Success Managerem lub administratorem, aby ją od<PERSON><PERSON><PERSON>.", "app.components.UserName.anonymous": "<PERSON><PERSON><PERSON><PERSON>", "app.components.UserName.anonymousTooltip2": "Ten użytkownik zdecydował się zanonimizować swój wkład", "app.components.UserName.authorWithNoNameTooltip": "Twoja nazwa została wygenerowana automatycznie, ponieważ nie wprowadziłeś swojego imienia. Je<PERSON>li chcesz je <PERSON>, zaktualizuj swój profil.", "app.components.UserName.deletedUser": "niewiadomy autor", "app.components.UserName.verified": "Zweryfikowany", "app.components.VerificationModal.verifyAuth0": "Zweryfikuj za pomocą NemID", "app.components.VerificationModal.verifyBOSA": "Weryfikacja za pomocą identyfikatora elektronicznego lub eID", "app.components.VerificationModal.verifyBosaFas": "Zweryfikuj za pomocą itsme lub eID", "app.components.VerificationModal.verifyClaveUnica": "Weryfikacja z Clave Unica", "app.components.VerificationModal.verifyFakeSSO": "Zweryfikuj za pomocą Fake SSO", "app.components.VerificationModal.verifyIdAustria": "Zweryfikuj za pomocą ID Austria", "app.components.VerificationModal.verifyKeycloak": "Zweryfikuj za pomocą ID-Porten", "app.components.VerificationModal.verifyNemLogIn": "Zweryfikuj za pomocą MitID", "app.components.VerificationModal.verifyTwoday2": "Zweryfikuj za pomocą BankID lub Freja eID+", "app.components.VerificationModal.verifyYourIdentity": "Zweryfikuj swoją to<PERSON><PERSON>", "app.components.VoteControl.budgetingFutureEnabled": "Możesz przydzielić swój budżet zaczynając od {enabledFromDate}.", "app.components.VoteControl.budgetingNotPermitted": "Budżet obywatelski jest włączone tylko dla niektórych grup.", "app.components.VoteControl.budgetingNotPossible": "Dokonywanie zmian w budżecie nie jest w tej chwili możliwe.", "app.components.VoteControl.budgetingNotVerified": "Proszę {verifyAccountLink} aby k<PERSON>.", "app.components.VoteInputs._shared.currencyLeft1": "<PERSON><PERSON> j<PERSON>e {budgetLeft} / {totalBudget}", "app.components.VoteInputs._shared.numberOfCreditsLeft": "<PERSON><PERSON> {votesLeft, plural, =0 {brak kredytów} other {# z {totalNumberOfVotes, plural, one {1 kredyt} other {# kredytów}} pozostało}}.", "app.components.VoteInputs._shared.numberOfPointsLeft": "<PERSON><PERSON> {votesLeft, plural, =0 {brak punktów} other {# z {totalNumberOfVotes, plural, one {1 punkt} other {# punktów}} pozostało}}.", "app.components.VoteInputs._shared.numberOfTokensLeft": "<PERSON><PERSON> {votesLeft, plural, =0 {brak żetonów} other {# z {totalNumberOfVotes, plural, one {1 żeton} other {# żetonów}} pozostało}}.", "app.components.VoteInputs._shared.numberOfVotesLeft": "<PERSON><PERSON> {votesLeft, plural, =0 {brak głosów} other {# z {totalNumberOfVotes, plural, one {1 głos} other {# głosów}} pozostało}}.", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "Przesłałeś już swój budżet. Aby go zmodyfikować, kliknij \"Modyfikuj swoje zgłoszenie\".", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "Przesłałeś już swój budżet. Aby go zmodyfikować, wróć do strony projektu i kliknij \"Modyfikuj swoje zgłoszenie\".", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "Budżetowanie nie jest dostę<PERSON>, ponieważ ta faza nie jest aktywna.", "app.components.VoteInputs.single.youHaveVotedForX2": "Głosowałeś na {votes, plural, =0 {# opcje} one {# opcje} other {# opcje}}", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "<PERSON><PERSON>za to, że utracisz wszystkie dane powiązane z tymi danymi wejściowymi, takie jak komentarze, reakcje i głosy. Tego działania nie można cofnąć.", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "<PERSON>zy na pewno chcesz usunąć to wejście?", "app.components.admin.PostManager.components.PostTable.Row.cancel": "<PERSON><PERSON><PERSON>", "app.components.admin.PostManager.components.PostTable.Row.confirm": "Potwierdź", "app.components.admin.SlugInput.resultingURL": "Wynikowy adres URL", "app.components.admin.SlugInput.slugTooltip": "Slug to unikalny zestaw słów na końcu adresu internetowego strony, czyli URL.", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "<PERSON><PERSON><PERSON> zmienisz adres URL, linki do strony używające starego adresu URL nie będą już <PERSON>.", "app.components.admin.SlugInput.urlSlugLabel": "Slug (URL)", "app.components.admin.UserFilterConditions.addCondition": "<PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.field_email": "E-mail", "app.components.admin.UserFilterConditions.field_event_attendance": "Rejestracje wydarzeń", "app.components.admin.UserFilterConditions.field_follow": "Śledź", "app.components.admin.UserFilterConditions.field_lives_in": "Mieszka w", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "Ankieta monitora społeczności", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "Interakcja z wkładem o statusie", "app.components.admin.UserFilterConditions.field_participated_in_project": "Przyczynił się do projektu", "app.components.admin.UserFilterConditions.field_participated_in_topic": "Przyczynił się do tematu", "app.components.admin.UserFilterConditions.field_registration_completed_at": "Data rejestracji", "app.components.admin.UserFilterConditions.field_role": "Rola", "app.components.admin.UserFilterConditions.field_verified": "Weryfikacja", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "nie jest zarejestrowany na żadne z tych wydarzeń", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "nie jest zarejestrowany na żadne wydarzenie", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "jest zarejestrowany na jedno z tych wydarzeń", "app.components.admin.UserFilterConditions.predicate_attends_something": "jest zarejestrowany na co najmniej jedno wydarzenie", "app.components.admin.UserFilterConditions.predicate_begins_with": "zaczyna się od", "app.components.admin.UserFilterConditions.predicate_commented_in": "skomentowane", "app.components.admin.UserFilterConditions.predicate_contains": "zawiera", "app.components.admin.UserFilterConditions.predicate_ends_on": "kończy się", "app.components.admin.UserFilterConditions.predicate_has_value": "ma <PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_in": "wykonał jakiekolwiek d<PERSON>łanie", "app.components.admin.UserFilterConditions.predicate_is": "jest", "app.components.admin.UserFilterConditions.predicate_is_admin": "jest administratorem", "app.components.admin.UserFilterConditions.predicate_is_after": "jest po", "app.components.admin.UserFilterConditions.predicate_is_before": "jest przed", "app.components.admin.UserFilterConditions.predicate_is_checked": "jest sprawd<PERSON>y", "app.components.admin.UserFilterConditions.predicate_is_empty": "jest pusty", "app.components.admin.UserFilterConditions.predicate_is_equal": "jest", "app.components.admin.UserFilterConditions.predicate_is_exactly": "jest dokładnie", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "jest wi<PERSON><PERSON><PERSON> niż", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "jest wi<PERSON><PERSON><PERSON> lub równy", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "jest z<PERSON><PERSON><PERSON><PERSON> użytkownikiem", "app.components.admin.UserFilterConditions.predicate_is_not_area": "nie obe<PERSON><PERSON><PERSON> o<PERSON><PERSON>u", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "wyklucza folder", "app.components.admin.UserFilterConditions.predicate_is_not_input": "nie obej<PERSON><PERSON> danych wejściowych", "app.components.admin.UserFilterConditions.predicate_is_not_project": "nie obejmuje projektu", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "wyk<PERSON><PERSON>a temat", "app.components.admin.UserFilterConditions.predicate_is_one_of": "jest jednym z", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "jeden z obszarów", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "jeden z <PERSON>", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "jedno z wejść", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "jeden z projektów", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "jeden z tematów", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "jest menad<PERSON><PERSON>m projektu", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "jest mnie<PERSON><PERSON><PERSON> niż", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "jest mnie<PERSON><PERSON>y lub równy", "app.components.admin.UserFilterConditions.predicate_is_verified": "jest z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "nie zaczyna się od", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "nie sko<PERSON>(a)", "app.components.admin.UserFilterConditions.predicate_not_contains": "nie zaw<PERSON>", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "nie kończy się na", "app.components.admin.UserFilterConditions.predicate_not_has_value": "nie ma warto<PERSON>ci", "app.components.admin.UserFilterConditions.predicate_not_in": "nie przyczynił się", "app.components.admin.UserFilterConditions.predicate_not_is": "nie jest", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "nie jest administratorem", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "nie jest sprawd<PERSON>y", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "nie jest pusty", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "nie jest", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "nie jest zwykłym użytkownikiem", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "nie jest jednym z", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "nie jest menadżerem projektu", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "nie jest we<PERSON><PERSON><PERSON><PERSON>y", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "nie opub<PERSON><PERSON>ł wkładu", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "nie zareagował na komentarz", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "nie reagował na dane wejściowe", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "nie zarejestrowałeś się na wydarzenie", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "nie wziął udziału w ankiecie", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "nie zgłosił się na ochotnika", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "nie brało udziału w głosowaniu", "app.components.admin.UserFilterConditions.predicate_nothing": "nic", "app.components.admin.UserFilterConditions.predicate_posted_input": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł wkład", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "zareagował na komentarz", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "zareagował na dane wejściowe", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "zarejestrowany na wydarzenie", "app.components.admin.UserFilterConditions.predicate_something": "co<PERSON>", "app.components.admin.UserFilterConditions.predicate_taken_survey": "w<PERSON><PERSON>ł udział w ankiecie", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "zgłosił się na ochotnika", "app.components.admin.UserFilterConditions.predicate_voted_in3": "wzięło udział w głosowaniu", "app.components.admin.UserFilterConditions.rulesFormLabelField": "A", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "B", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "C", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "Nie będziesz otrzymywać powiadomień o swoim wkładzie", "app.components.anonymousParticipationModal.cancel": "<PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.anonymousParticipationModal.participateAnonymously": "<PERSON><PERSON> udział <PERSON>", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "S<PERSON>woduje to bezpieczne <b>ukrycie Twojego profilu</b> przed administratorami, kierownikami projektów i innymi rezydentami dla tego konkretnego wkładu, aby nikt nie mógł powiązać tego wkładu z Tobą. Anonimowych wpisów nie można edytować i są one uznawane za ostateczne.", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "Zapewnienie bezpieczeństwa naszej platformy dla każdego użytkownika jest dla nas najwyższym priorytetem. Słowa mają znaczenie, wię<PERSON> bądź dla siebie miły.", "app.components.avatar.titleForAccessibility": "Profil strony {fullName}", "app.components.customFields.mapInput.removeAnswer": "Usuń odpowiedź", "app.components.customFields.mapInput.undo": "Cof<PERSON>j", "app.components.customFields.mapInput.undoLastPoint": "Cofnij ostatni punkt", "app.components.followUnfollow.follow": "Śledź", "app.components.followUnfollow.followADiscussion": "Śledź dyskusję", "app.components.followUnfollow.followTooltipInputPage2": "Śledzenie wyzwala aktualizacje e-mail o zmianach statusu, oficjalnych aktualizacjach i komentarzach. Możesz {unsubscribeLink} w dowolnym momencie.", "app.components.followUnfollow.followTooltipProjects2": "Śledzenie wyzwala aktualizacje e-mail o zmianach w projekcie. Możesz {unsubscribeLink} w dowolnym momencie.", "app.components.followUnfollow.unFollow": "<PERSON><PERSON> o<PERSON>er<PERSON>j", "app.components.followUnfollow.unsubscribe": "zrezygnuj z subskrypcji", "app.components.followUnfollow.unsubscribeUrl": "/profile/edit", "app.components.form.ErrorDisplay.guidelinesLinkText": "nasze wytyczne", "app.components.form.ErrorDisplay.next": "Następny", "app.components.form.ErrorDisplay.previous": "Poprzedni", "app.components.form.ErrorDisplay.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.form.ErrorDisplay.userPickerPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby z<PERSON> e-mail lub użytkownika...", "app.components.form.anonymousSurveyMessage2": "Wszystkie odpowiedzi udzielone w tej ankiecie są anonimowe.", "app.components.form.backToInputManager": "Powrót do menedżera wprowadzania danych", "app.components.form.backToProject": "Powrót do projektu", "app.components.form.components.controls.mapInput.removeAnswer": "Usuń odpowiedź", "app.components.form.components.controls.mapInput.undo": "Cof<PERSON>j", "app.components.form.components.controls.mapInput.undoLastPoint": "Cofnij ostatni punkt", "app.components.form.controls.addressInputAriaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.form.controls.addressInputPlaceholder6": "Wprowadź adres...", "app.components.form.controls.adminFieldTooltip": "Pole widoczne tylko dla administratorów", "app.components.form.controls.allStatementsError": "Odpowiedź musi zostać wybrana dla wszystkich stwierdzeń.", "app.components.form.controls.back": "Powró<PERSON>", "app.components.form.controls.clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko", "app.components.form.controls.clearAllScreenreader": "Usuń wszystkie odpowiedzi z powyższego pytania macierzy", "app.components.form.controls.clickOnMapMultipleToAdd3": "<PERSON><PERSON><PERSON><PERSON>, aby ją na<PERSON>. Następnie przeciągnij <PERSON>, aby je przes<PERSON>.", "app.components.form.controls.clickOnMapToAddOrType": "Kliknij na mapę lub wpisz adres poniżej, aby dodać odpowiedź.", "app.components.form.controls.confirm": "Potwierdź", "app.components.form.controls.cosponsorsPlaceholder": "Zacznij wpisywać nazwę do wyszukania", "app.components.form.controls.currentRank": "Akt<PERSON>na ranga:", "app.components.form.controls.minimumCoordinates2": "Wymagana jest minimalna liczba punktów na mapie {numPoints} .", "app.components.form.controls.noRankSelected": "<PERSON>e wy<PERSON>no rangi", "app.components.form.controls.notPublic1": "*Ta odpowiedź zostanie udostępniona tylko kierownikom projektów, a nie publicznie.", "app.components.form.controls.optionalParentheses": "(opcjonalnie)", "app.components.form.controls.rankingInstructions": "Przeciągnij i upuść, aby uszeregować opcje.", "app.components.form.controls.selectAsManyAsYouLike": "*Wybierz dowolną liczbę", "app.components.form.controls.selectBetween": "*<PERSON><PERSON><PERSON><PERSON> pomiędzy opcjami {minItems} i {maxItems} .", "app.components.form.controls.selectExactly2": "*<PERSON><PERSON><PERSON><PERSON> dokładnie {selectExactly, plural, one {# option} other {# opcje}}", "app.components.form.controls.selectMany": "*Wybierz, ile chcesz", "app.components.form.controls.tapOnFullscreenMapToAdd4": "<PERSON><PERSON><PERSON><PERSON>, aby ją na<PERSON>. Następnie przecią<PERSON>j <PERSON>, aby je przes<PERSON>.", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "<PERSON><PERSON><PERSON><PERSON>, aby r<PERSON>.", "app.components.form.controls.tapOnMapMultipleToAdd3": "Dotk<PERSON>j <PERSON>, aby dodać odpowiedź.", "app.components.form.controls.tapOnMapToAddOrType": "Dotknij mapy lub wpisz adres poniżej, aby do<PERSON><PERSON> odpowiedź.", "app.components.form.controls.tapToAddALine": "<PERSON><PERSON><PERSON><PERSON>, aby dodać linię", "app.components.form.controls.tapToAddAPoint": "<PERSON><PERSON><PERSON><PERSON>, aby do<PERSON><PERSON> punkt", "app.components.form.controls.tapToAddAnArea": "<PERSON><PERSON><PERSON><PERSON>, aby do<PERSON><PERSON> obszar", "app.components.form.controls.uploadShapefileInstructions": "* Prześlij plik zip zawierający jeden lub więcej plików shapefiles.", "app.components.form.controls.validCordinatesTooltip2": "<PERSON><PERSON><PERSON> lokalizacja nie jest wy<PERSON><PERSON><PERSON><PERSON> wśr<PERSON>d opcji podczas wpisywania, moż<PERSON><PERSON> dodać prawidłowe współrzędne w formacie \"s<PERSON><PERSON><PERSON><PERSON>, dług<PERSON><PERSON><PERSON> geograficzna\", aby <PERSON><PERSON><PERSON><PERSON><PERSON> dokładną lokalizację (np. -33.019808, -71.495676).", "app.components.form.controls.valueOutOfTotal": "{value} z {total}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} z {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} z {total}, gdzie {maxValue} to {maxLabel}", "app.components.form.error": "Błąd", "app.components.form.locationGoogleUnavailable": "Nie można załadować pola lokalizacji dostarczonego przez mapy google.", "app.components.form.progressBarLabel": "<PERSON><PERSON><PERSON>", "app.components.form.submit": "Prześ<PERSON>j", "app.components.form.submitApiError": "Wystąpił problem przy przesyłaniu formularza. Sprawdź, czy nie wystąpiły błędy i spróbuj ponownie.", "app.components.form.verifiedBlocked": "<PERSON><PERSON> mo<PERSON><PERSON><PERSON> tego pola, ponieważ zawiera ono zweryfikowane informacje", "app.components.formBuilder.Page": "Strona", "app.components.formBuilder.accessibilityStatement": "oświadczenie o dostępności", "app.components.formBuilder.addAnswer": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.addStatement": "Dodaj oświadczenie", "app.components.formBuilder.agree": "Zgad<PERSON> się", "app.components.formBuilder.ai1": "AI", "app.components.formBuilder.aiUpsellText1": "<PERSON><PERSON><PERSON> masz dostęp do naszego pakietu AI, b<PERSON><PERSON><PERSON><PERSON> mógł podsumowywać i kategoryzować odpowiedzi tekstowe za pomocą AI", "app.components.formBuilder.askFollowUpToggleLabel": "Zapytaj o działania następcze", "app.components.formBuilder.bad": "Zły", "app.components.formBuilder.buttonLabel": "Etykieta przycisku", "app.components.formBuilder.buttonLink": "Przycisk łącza", "app.components.formBuilder.cancelLeaveBuilderButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.category": "Kategoria", "app.components.formBuilder.chooseMany": "Wybierz wiele", "app.components.formBuilder.chooseOne": "<PERSON><PERSON><PERSON><PERSON> jedn<PERSON>", "app.components.formBuilder.close": "Zamknij", "app.components.formBuilder.closed": "Zamknięte", "app.components.formBuilder.configureMap": "Skonfiguruj mapę", "app.components.formBuilder.confirmLeaveBuilderButtonText": "Tak, chcę odejść", "app.components.formBuilder.content": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.continuePageLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.cosponsors": "Współsponsorzy", "app.components.formBuilder.default": "Domyślnie", "app.components.formBuilder.defaultContent": "Z<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.delete": "Usuń", "app.components.formBuilder.deleteButtonLabel": "Usuń", "app.components.formBuilder.description": "Opis", "app.components.formBuilder.disabledBuiltInFieldTooltip": "Zostało to już dodane w formularzu. Treść domyślna może być użyta tylko raz.", "app.components.formBuilder.disabledCustomFieldsTooltip1": "Dodawanie niestandardowej zawartości nie jest częścią Twojej obecnej licencji. Skontaktuj się ze swoim GovSuccess Managerem, aby dowiedzieć się więcej na ten temat.", "app.components.formBuilder.disagree": "<PERSON>e zgadzam <PERSON>ę", "app.components.formBuilder.displayAsDropdown": "Wyświetlaj jako listę rozwijaną", "app.components.formBuilder.displayAsDropdownTooltip": "Wyświetl opcje w formie listy rozwijanej. <PERSON><PERSON><PERSON> masz wiele opcji, jest to zale<PERSON>.", "app.components.formBuilder.done": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawArea": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.drawRoute": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.dropPin": "Zawleczka", "app.components.formBuilder.editButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.emptyImageOptionError": "Podaj co najmniej 1 odpowiedź. Pamiętaj, że każda odpowiedź musi mieć tytuł.", "app.components.formBuilder.emptyOptionError": "Podaj co najmniej 1 odpowiedź", "app.components.formBuilder.emptyStatementError": "Podaj co najmniej 1 stwierdzenie", "app.components.formBuilder.emptyTitleError": "Podaj tytuł p<PERSON>ania", "app.components.formBuilder.emptyTitleMessage": "Podaj tytuł dla wszystkich odpowiedzi", "app.components.formBuilder.emptyTitleStatementMessage": "Podaj tytuł dla wszystkich stwierdzeń", "app.components.formBuilder.enable": "Włącz", "app.components.formBuilder.errorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> problem, pro<PERSON><PERSON> problem, aby mó<PERSON> z<PERSON> z<PERSON>", "app.components.formBuilder.fieldGroup.description": "Opis (opcjonalnie)", "app.components.formBuilder.fieldGroup.title": "Tytuł (opcjonalnie)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "Obecnie odpowiedzi na te pytania są dostępne tylko w wyeksportowanym pliku excel w programie Input Manager, a nie są widoczne dla użytkowników.", "app.components.formBuilder.fieldLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.fieldLabelStatement": "Oświadczenia", "app.components.formBuilder.fileUpload": "Przesyłanie plików", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "Strona oparta na mapie", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "<PERSON><PERSON><PERSON> mapę jako kontekst lub zadawaj uczestnikom pytania oparte na lokalizacji.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ć optymalne wrażenia użytkownika, nie zalecamy dodawania pytań o punkty, trasy lub obszary do stron opartych na mapach.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "<PERSON><PERSON> strona", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "Funkcje mapowania geodezyjnego nie są objęte Twoją obecną licencją. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej.", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "<PERSON><PERSON> strony", "app.components.formBuilder.formEnd": "Koniec formularza", "app.components.formBuilder.formField.cancelDeleteButtonText": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "Tak, usuń stronę", "app.components.formBuilder.formField.copyNoun": "Kopia", "app.components.formBuilder.formField.copyVerb": "Kopia", "app.components.formBuilder.formField.delete": "Usuń", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "Usunięcie tej strony spowoduje również usunięcie powiązanej z nią logiki. <PERSON>zy na pewno chcesz ją usunąć?", "app.components.formBuilder.formField.deleteResultsInfo": "<PERSON>e można tego co<PERSON>", "app.components.formBuilder.goToPageInputLabel": "Potem następna strona to:", "app.components.formBuilder.good": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.helmetTitle": "Konstruktor formularzy", "app.components.formBuilder.imageFileUpload": "Przesyłanie zdjęć", "app.components.formBuilder.invalidLogicBadgeMessage": "Nieprawidłowa logika", "app.components.formBuilder.labels2": "Etykiety (opcjonalnie)", "app.components.formBuilder.labelsTooltipContent2": "Wybierz opcjonalne etykiety dla dowolnej wartości skali liniowej.", "app.components.formBuilder.lastPage": "Zakończenie", "app.components.formBuilder.layout": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "<PERSON><PERSON><PERSON>, że ch<PERSON> ode<PERSON>?", "app.components.formBuilder.leaveBuilderText": "Masz niezapisane zmiany. Zapisz je przed wyjściem. <PERSON><PERSON><PERSON> wyjdziesz, utracisz wprowadzone zmiany.", "app.components.formBuilder.limitAnswersTooltip": "Po włączeniu respondenci muszą wybrać określoną liczbę odpowiedzi, aby k<PERSON><PERSON>.", "app.components.formBuilder.limitNumberAnswers": "Ogranicz liczbę odpowiedzi", "app.components.formBuilder.linePolygonMapWarning2": "Rysowanie linii i wielokątów może nie spełniać standardów dostępności. Więcej informacji można znaleźć na stronie {accessibilityStatement}.", "app.components.formBuilder.linearScale": "Skala liniowa", "app.components.formBuilder.locationDescription": "Lokalizacja", "app.components.formBuilder.logic": "Logika", "app.components.formBuilder.logicAnyOtherAnswer": "Każda inna odpowiedź", "app.components.formBuilder.logicConflicts.conflictingLogic": "Sprzeczna logika", "app.components.formBuilder.logicConflicts.interQuestionConflict": "Ta strona zawiera pytania, które prowadzą do różnych stron. Jeśli uczestnicy odpowiedzą na wiele pytań, wyświetlona zostanie najdalsza strona. Upew<PERSON>j <PERSON>, że to zachowanie jest zgodne z zamierzonym przepływem.", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "Na tej stronie zastosowano wiele reguł logicznych: logikę pytań wielokrotnego wyboru, logikę na poziomie strony i logikę między pytaniami. Gdy te warunki nakładają się na siebie, logika pytania będzie miała pierwszeństwo przed logiką strony i zostanie wyświetlona najdalsza strona. Przejrzyj logikę, aby upewnić się, że jest zgodna z zamierzonym przepływem.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "Ta strona zawiera pytanie wielokrotnego wyboru, w którym opcje prowadzą do różnych stron. Jeśli uczestnicy wybiorą wiele opcji, wyświetlona zostanie najdalsza strona. <PERSON>ew<PERSON><PERSON>, że to zachowanie jest zgodne z zamierzonym przepływem.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "Ta strona zawiera pytanie wielokrotnego wyboru, w którym opcje prowadzą do różnych stron, oraz pytania prowadzące do innych stron. Najdalsza strona zostanie wyświetlona, jeśli te warunki się pokrywają. Upewnij się, że to zachowanie jest zgodne z zamierzonym przepływem.", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "Ta strona zawiera pytanie wielokrotnego wyboru, w którym opcje prowadzą do różnych stron i ma logikę ustawioną zarówno na poziomie strony, jak i pytania. Logika pytania będzie miała pierwszeństwo i zostanie wyświetlona najdalsza strona. Upewnij się, że to zachowanie jest zgodne z zamierzonym przepływem.", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "Ta strona ma logikę ustawioną zarówno na poziomie strony, jak i pytania. Logika pytania będzie miała pierwszeństwo przed logiką na poziomie strony. Upewnij się, że to zachowanie jest zgodne z zamierzonym przepływem.", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "Ta strona ma logikę ustawioną zarówno na poziomie strony, jak i pytania, a wiele pytań kieruje do różnych stron. Logika pytania będzie miała pierwszeństwo i zostanie wyświetlona najdalsza strona. Upewnij się, że to zachowanie jest zgodne z zamierzonym przepływem.", "app.components.formBuilder.logicNoAnswer2": "<PERSON><PERSON> o<PERSON><PERSON>wiedzi", "app.components.formBuilder.logicPanelAnyOtherAnswer": "<PERSON><PERSON><PERSON> inna odpowiedź", "app.components.formBuilder.logicPanelNoAnswer": "<PERSON><PERSON><PERSON> nie udzielono odpowiedzi", "app.components.formBuilder.logicValidationError": "Logika nie może zawierać linków do wcześniejszych stron", "app.components.formBuilder.longAnswer": "Długa odpowiedź", "app.components.formBuilder.mapConfiguration": "Konfiguracja mapy", "app.components.formBuilder.mapping": "Mapowanie", "app.components.formBuilder.mappingNotInCurrentLicense": "Funkcje mapowania geodezyjnego nie są objęte Twoją obecną licencją. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej.", "app.components.formBuilder.matrix": "Mat<PERSON>ca", "app.components.formBuilder.matrixSettings.columns": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.matrixSettings.rows": "Rzędy", "app.components.formBuilder.multipleChoice": "Wybór wielokrotny", "app.components.formBuilder.multipleChoiceHelperText": "Jeśli wiele opcji prowadzi do różnych stron, a uczestnicy wybiorą więcej niż jedną, wyświetlona zostanie najdalsza strona. Upewnij si<PERSON>, że to zachowanie jest zgodne z zamierzonym przepływem.", "app.components.formBuilder.multipleChoiceImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.maximum": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.multiselect.minimum": "Minimum", "app.components.formBuilder.neutral": "Neutralny", "app.components.formBuilder.newField": "<PERSON><PERSON> pole", "app.components.formBuilder.number": "Numer", "app.components.formBuilder.ok": "Ok", "app.components.formBuilder.open": "Otwórz", "app.components.formBuilder.optional": "Opcjonalnie", "app.components.formBuilder.other": "<PERSON><PERSON>", "app.components.formBuilder.otherOption": "<PERSON><PERSON><PERSON> \"Inne\"", "app.components.formBuilder.otherOptionTooltip": "Pozwól uczestnikom wprowadzić niestandardową odpowiedź, jeśli podane odpowiedzi nie odpowiadają ich preferencjom.", "app.components.formBuilder.page": "Strona", "app.components.formBuilder.pageCannotBeDeleted": "<PERSON><PERSON> strony nie można us<PERSON>.", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "Ta strona nie może zostać usunięta i nie pozwala na dodanie żadnych dodatkowych pól.", "app.components.formBuilder.pageRuleLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona to:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "<PERSON><PERSON><PERSON> nie zostanie dodana żadna logika, formularz będzie działał zgodnie z normalnym przepływem. <PERSON><PERSON><PERSON> zarów<PERSON> strona, jak i jej pytania mają logikę, logika pytania będzie miała pierwszeństwo. Upew<PERSON>j się, że jest to zgodne z zamierzonym przepływem Aby uzyskać więcej informacji, odwiedź {supportPageLink}.", "app.components.formBuilder.preview": "Zapowiedź:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "Współsponsorzy nie są wyświetlani w pobranym pliku PDF i nie są obsługiwani w przypadku importu za pośrednictwem FormSync.", "app.components.formBuilder.printSupportTooltip.fileupload": "Pytania dotyczące przesyłania plików są wyświetlane jako nieobsługiwane w pobranym pliku PDF i nie są obsługiwane w przypadku importu za pośrednictwem FormSync.", "app.components.formBuilder.printSupportTooltip.mapping": "Pytania mapujące są wyświetlane w pobranym pliku PDF, ale warstwy nie będą widoczne. Pytania mapujące nie są obsługiwane w przypadku importu przez FormSync.", "app.components.formBuilder.printSupportTooltip.matrix": "Pytania matrycowe są wyświetlane w pobranym pliku PDF, ale obecnie nie są obsługiwane w przypadku importu za pośrednictwem FormSync.", "app.components.formBuilder.printSupportTooltip.page": "Tytuły i opisy stron są wyświetlane jako nagłówki sekcji w pobranym pliku PDF.", "app.components.formBuilder.printSupportTooltip.ranking": "Pytania rankingowe są wyświetlane w pobranym pliku PDF, ale obecnie nie są obsługiwane w przypadku importu za pośrednictwem FormSync.", "app.components.formBuilder.printSupportTooltip.topics2": "Tagi są wyświetlane jako nieobsługiwane w pobranym pliku PDF i nie są obsługiwane w przypadku importu za pośrednictwem FormSync.", "app.components.formBuilder.proposedBudget": "Proponowany budżet", "app.components.formBuilder.question": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.questionCannotBeDeleted": "To pytanie nie może być usunięte.", "app.components.formBuilder.questionDescriptionOptional": "Opis pytania (nieobowiązkowo)", "app.components.formBuilder.questionTitle": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.randomize": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.randomizeToolTip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odpowiedzi będzie losowa dla każdego użytkownika", "app.components.formBuilder.range": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.ranking": "Ranking", "app.components.formBuilder.rating": "Ocena", "app.components.formBuilder.removeAnswer": "<PERSON><PERSON><PERSON><PERSON> odpowiedź", "app.components.formBuilder.required": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.requiredToggleLabel": "Uczyń odpowiedź na to pytanie wymaganą", "app.components.formBuilder.ruleForAnswerLabel": "<PERSON><PERSON><PERSON> odpowiedź brzmi:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "<PERSON><PERSON><PERSON> odpowiedzi obejmują:", "app.components.formBuilder.save": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.selectRangeTooltip": "<PERSON><PERSON><PERSON><PERSON> maks<PERSON> wartość dla swojej skali.", "app.components.formBuilder.sentiment": "Skala nastrojów", "app.components.formBuilder.shapefileUpload": "Przesyłanie plików Esri shapefile", "app.components.formBuilder.shortAnswer": "Krótka odpowiedź", "app.components.formBuilder.showResponseToUsersToggleLabel": "Pokaż odpowiedź dla użytkowników", "app.components.formBuilder.singleChoice": "Pojedynczy wybór", "app.components.formBuilder.staleDataErrorMessage2": "Wystąpił problem. Ten formularz wejściowy został ostatnio zapisany w innym miejscu. <PERSON><PERSON><PERSON> to być spowodowane tym, że Ty lub inny użytkownik ma go otwartego do edycji w innym oknie przeglądarki. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>, aby uzy<PERSON><PERSON> najnowszy formularz, a następnie wprowadź zmiany ponownie.", "app.components.formBuilder.stronglyAgree": "Zdecydowanie się zgadzam", "app.components.formBuilder.stronglyDisagree": "Zdecydowanie się nie zgadzam", "app.components.formBuilder.supportArticleLinkText": "ta strona", "app.components.formBuilder.tags": "Tagi", "app.components.formBuilder.title": "<PERSON><PERSON><PERSON>", "app.components.formBuilder.toLabel": "do", "app.components.formBuilder.unsavedChanges": "<PERSON><PERSON> niezapisane zmiany", "app.components.formBuilder.useCustomButton2": "Użyj niestandardowego przycisku strony", "app.components.formBuilder.veryBad": "<PERSON><PERSON><PERSON><PERSON>", "app.components.formBuilder.veryGood": "<PERSON><PERSON><PERSON>", "app.components.ideas.similarIdeas.engageHere": "Zaangażuj się tutaj", "app.components.ideas.similarIdeas.noSimilarSubmissions": "Nie znaleziono podobnych zgłoszeń.", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "Znaleźliśmy podobne podwładne - angażowanie się w nie może pomóc je wzmoc<PERSON>ć!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "Podobne zgłoszenia zostały już opublikowane:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "Szukasz podobnych zgłoszeń ...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {<PERSON><PERSON><PERSON>} one {# dzień} other {# dni}} lewo", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  tygodni", "app.components.screenReaderCurrency.AED": "Zjednoczone Emiraty Arabskie Dirham", "app.components.screenReaderCurrency.AFN": "Afgań<PERSON><PERSON>", "app.components.screenReaderCurrency.ALL": "Lek <PERSON>ński", "app.components.screenReaderCurrency.AMD": "Armenian Dram", "app.components.screenReaderCurrency.ANG": "Gulden Antyli Holenderskich", "app.components.screenReaderCurrency.AOA": "Angolska Kwanza", "app.components.screenReaderCurrency.ARS": "Peso argentyńskie", "app.components.screenReaderCurrency.AUD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.AWG": "Aruban Florin", "app.components.screenReaderCurrency.AZN": "Manat azerski", "app.components.screenReaderCurrency.BAM": "Bośnia i Hercegowina Zamienny znak towarowy", "app.components.screenReaderCurrency.BBD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BDT": "Taka bangladeska", "app.components.screenReaderCurrency.BGN": "Bułgarski Lew", "app.components.screenReaderCurrency.BHD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BIF": "<PERSON>", "app.components.screenReaderCurrency.BMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BND": "Dolar Brunei", "app.components.screenReaderCurrency.BOB": "Boliviano boliwijskie", "app.components.screenReaderCurrency.BOV": "Boliwijski Mvdol", "app.components.screenReaderCurrency.BRL": "Brazylijski real", "app.components.screenReaderCurrency.BSD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.BTN": "Bhu<PERSON>ński<PERSON> Ngultrum", "app.components.screenReaderCurrency.BWP": "Pula botswańska", "app.components.screenReaderCurrency.BYR": "R<PERSON><PERSON>łoruski", "app.components.screenReaderCurrency.BZD": "Dolar Belize", "app.components.screenReaderCurrency.CAD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.CDF": "<PERSON>", "app.components.screenReaderCurrency.CHE": "WIR Euro", "app.components.screenReaderCurrency.CHF": "<PERSON>", "app.components.screenReaderCurrency.CHW": "WIR Franc", "app.components.screenReaderCurrency.CLF": "Chilijska jednostka rozliczeniowa (UF)", "app.components.screenReaderCurrency.CLP": "Peso chilijskie", "app.components.screenReaderCurrency.CNY": "<PERSON><PERSON> juan", "app.components.screenReaderCurrency.COP": "Peso kolumbijskie", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "Kostarykański Colón", "app.components.screenReaderCurrency.CRE": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.CUC": "Kubańskie peso wymienialne", "app.components.screenReaderCurrency.CUP": "Peso kubańskie", "app.components.screenReaderCurrency.CVE": "Escudo Republiki Zielonego Przylądka", "app.components.screenReaderCurrency.CZK": "Korona czeska", "app.components.screenReaderCurrency.DJF": "Dżibutyjski frank", "app.components.screenReaderCurrency.DKK": "Korona duńska", "app.components.screenReaderCurrency.DOP": "Peso dominikańskie", "app.components.screenReaderCurrency.DZD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.EGP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ERN": "Erytrejska Nakfa", "app.components.screenReaderCurrency.ETB": "Birra etiopska", "app.components.screenReaderCurrency.EUR": "Euro", "app.components.screenReaderCurrency.FJD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.FKP": "Funt falklandzki", "app.components.screenReaderCurrency.GBP": "<PERSON><PERSON> br<PERSON>", "app.components.screenReaderCurrency.GEL": "Gruzińskie Lari", "app.components.screenReaderCurrency.GHS": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GIP": "<PERSON><PERSON> g<PERSON>", "app.components.screenReaderCurrency.GMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.GNF": "<PERSON>", "app.components.screenReaderCurrency.GTQ": "Gwatemalski kwezal", "app.components.screenReaderCurrency.GYD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.HKD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.HNL": "<PERSON><PERSON><PERSON> z <PERSON>u", "app.components.screenReaderCurrency.HRK": "<PERSON><PERSON> chorwa<PERSON>a", "app.components.screenReaderCurrency.HTG": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.HUF": "Forint węgierski", "app.components.screenReaderCurrency.IDR": "<PERSON>up<PERSON>", "app.components.screenReaderCurrency.ILS": "<PERSON><PERSON> szekel izraelski", "app.components.screenReaderCurrency.INR": "Rupia indyjska", "app.components.screenReaderCurrency.IQD": "<PERSON><PERSON> dinar", "app.components.screenReaderCurrency.IRR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ISK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.JMD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JOD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.JPY": "<PERSON>", "app.components.screenReaderCurrency.KES": "<PERSON><PERSON><PERSON> keni<PERSON>", "app.components.screenReaderCurrency.KGS": "Kirgistan Som", "app.components.screenReaderCurrency.KHR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KMF": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KPW": "Won północ<PERSON>kor<PERSON>ński", "app.components.screenReaderCurrency.KRW": "Won połud<PERSON>wokor<PERSON>ński", "app.components.screenReaderCurrency.KWD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KYD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.KZT": "Tenge ka<PERSON>", "app.components.screenReaderCurrency.LAK": "<PERSON>", "app.components.screenReaderCurrency.LBP": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LKR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.LRD": "<PERSON><PERSON> l<PERSON>", "app.components.screenReaderCurrency.LSL": "Lesotho Loti", "app.components.screenReaderCurrency.LTL": "<PERSON><PERSON>ews<PERSON>", "app.components.screenReaderCurrency.LVL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.LYD": "<PERSON><PERSON> l<PERSON>", "app.components.screenReaderCurrency.MAD": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MDL": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MGA": "Malgaski Ariary", "app.components.screenReaderCurrency.MKD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MMK": "Kyat Myanmar", "app.components.screenReaderCurrency.MNT": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MOP": "Macanese <PERSON>", "app.components.screenReaderCurrency.MRO": "Mauretańska Ouguiya", "app.components.screenReaderCurrency.MUR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MVR": "Rufiyaa z Malediwów", "app.components.screenReaderCurrency.MWK": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.MXN": "Peso meksykańskie", "app.components.screenReaderCurrency.MXV": "Meksykański Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.MZN": "Mozambik Metical", "app.components.screenReaderCurrency.NAD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.NGN": "<PERSON><PERSON>", "app.components.screenReaderCurrency.NIO": "Nikaraguańska Kordoba", "app.components.screenReaderCurrency.NOK": "Korona norweska", "app.components.screenReaderCurrency.NPR": "<PERSON><PERSON><PERSON> ne<PERSON>", "app.components.screenReaderCurrency.NZD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.OMR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.PAB": "Panamski Balboa", "app.components.screenReaderCurrency.PEN": "Peruvian Sol", "app.components.screenReaderCurrency.PGK": "<PERSON><PERSON>-<PERSON><PERSON>", "app.components.screenReaderCurrency.PHP": "Peso filipińskie", "app.components.screenReaderCurrency.PKR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.PLN": "Polski złoty", "app.components.screenReaderCurrency.PYG": "Paragwajski Guaraní", "app.components.screenReaderCurrency.QAR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RON": "<PERSON><PERSON><PERSON><PERSON> lej", "app.components.screenReaderCurrency.RSD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.RUB": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.RWF": "<PERSON>", "app.components.screenReaderCurrency.SAR": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SBD": "<PERSON><PERSON> Wysp Sal<PERSON>", "app.components.screenReaderCurrency.SCR": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SDG": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SEK": "Korona szwedzka", "app.components.screenReaderCurrency.SGD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SHP": "<PERSON><PERSON> Święte<PERSON>", "app.components.screenReaderCurrency.SLL": "Sierra Leone", "app.components.screenReaderCurrency.SOS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.SRD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.SSP": "Fun<PERSON> poł<PERSON><PERSON><PERSON><PERSON>dański", "app.components.screenReaderCurrency.STD": "Wyspy Świętego Tomasza i Książęca Dobra", "app.components.screenReaderCurrency.SYP": "Funt syryjski", "app.components.screenReaderCurrency.SZL": "Swazi Lilangeni", "app.components.screenReaderCurrency.THB": "Baht tajski", "app.components.screenReaderCurrency.TJS": "<PERSON><PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.TMT": "Manat turkmeński", "app.components.screenReaderCurrency.TND": "<PERSON><PERSON>", "app.components.screenReaderCurrency.TOK": "Token", "app.components.screenReaderCurrency.TOP": "Tongan Paʻanga", "app.components.screenReaderCurrency.TRY": "<PERSON><PERSON> t<PERSON>a", "app.components.screenReaderCurrency.TTD": "<PERSON><PERSON> i <PERSON>", "app.components.screenReaderCurrency.TWD": "<PERSON><PERSON> dolar tajwa<PERSON>", "app.components.screenReaderCurrency.TZS": "<PERSON><PERSON><PERSON>", "app.components.screenReaderCurrency.UAH": "Hrywna ukraińska", "app.components.screenReaderCurrency.UGX": "<PERSON><PERSON><PERSON>gan<PERSON>", "app.components.screenReaderCurrency.USD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.USN": "<PERSON><PERSON> (następny dzień)", "app.components.screenReaderCurrency.USS": "<PERSON><PERSON> (tego samego dnia)", "app.components.screenReaderCurrency.UYI": "Urugwaj Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "Peso urugwajskie", "app.components.screenReaderCurrency.UZS": "Uzbekistan Som", "app.components.screenReaderCurrency.VEF": "Wenezuelski Bolívar", "app.components.screenReaderCurrency.VND": "Wietnamski Đồng", "app.components.screenReaderCurrency.VUV": "Vanuatu Vatu", "app.components.screenReaderCurrency.WST": "Samoan <PERSON>", "app.components.screenReaderCurrency.XAF": "Środkowoafrykański frank CFA", "app.components.screenReaderCurrency.XAG": "Srebro (jedna uncja trojańska)", "app.components.screenReaderCurrency.XAU": "<PERSON><PERSON><PERSON> (jedna uncja trojańska)", "app.components.screenReaderCurrency.XBA": "Europejska jednostka kompozytowa (EURCO)", "app.components.screenReaderCurrency.XBB": "Europejska jednostka monetarna (E.M.U.-6)", "app.components.screenReaderCurrency.XBC": "Europejska jednostka rozliczeniowa 9 (E.U.A.-9)", "app.components.screenReaderCurrency.XBD": "Europejska jednostka rozliczeniowa 17 (E.U.A.-17)", "app.components.screenReaderCurrency.XCD": "<PERSON><PERSON>", "app.components.screenReaderCurrency.XDR": "Specjalne prawa ciągnienia", "app.components.screenReaderCurrency.XFU": "UIC Franc", "app.components.screenReaderCurrency.XOF": "Zachodnioafry<PERSON>ński frank CFA", "app.components.screenReaderCurrency.XPD": "<PERSON><PERSON><PERSON> (jedna uncja trojańska)", "app.components.screenReaderCurrency.XPF": "CFP Franc", "app.components.screenReaderCurrency.XPT": "<PERSON><PERSON>yna (jedna uncja trojańska)", "app.components.screenReaderCurrency.XTS": "Kody zarezerwowane specjalnie do celów testowych", "app.components.screenReaderCurrency.XXX": "<PERSON><PERSON> waluty", "app.components.screenReaderCurrency.YER": "<PERSON><PERSON>", "app.components.screenReaderCurrency.ZAR": "Rand południowoafrykański", "app.components.screenReaderCurrency.ZMW": "K<PERSON>cha <PERSON>", "app.components.screenReaderCurrency.amount": "K<PERSON><PERSON>", "app.components.screenReaderCurrency.currency": "<PERSON><PERSON><PERSON>", "app.components.trendIndicator.lastQuarter2": "ostatni kwartał", "app.containers.AccessibilityStatement.applicability": "Niniejsze oświadczenie dostępności dotyczy witryny {demoPlatformLink}, kt<PERSON>ra jest reprezentatywna dla tej witryny; wykorzystuje ona ten sam kod źródłowy i ma tę samą funkcjonalność.", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "Metoda oceny", "app.containers.AccessibilityStatement.assesmentText2022": "Dost<PERSON><PERSON><PERSON><PERSON>ć tej strony została oceniona przez podmiot zewnętrzny, kt<PERSON>ry nie był zaangażowany w proces projektowania i rozwoju. Zgodność z wyżej wymienionymi zasadami {demoPlatformLink} można stwierdzić na tej stronie {statusPageLink}.", "app.containers.AccessibilityStatement.changePreferencesButtonText": "możesz zmienić swoje preferencje", "app.containers.AccessibilityStatement.changePreferencesText": "<PERSON> ka<PERSON><PERSON><PERSON> chwili, {changePreferencesButton}.", "app.containers.AccessibilityStatement.conformanceExceptions": "Wyjątki zgodności", "app.containers.AccessibilityStatement.conformanceStatus": "Status zgodności", "app.containers.AccessibilityStatement.contentConformanceExceptions": "Staramy <PERSON>, aby nasze treści były dostępne dla wszystkich. Jednakże w niektórych przypadkach treści na platformie mogą być niedostępne:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "strona demo", "app.containers.AccessibilityStatement.email": "E-mail:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "Wbudowane narzędzia ankietowe", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "Wbudowane narzędzia ankietowe, które są dostępne do użytku na tej platformie, są oprogramowaniem innych firm i mogą być niedostępne.", "app.containers.AccessibilityStatement.exception_1": "Nasze cyfrowe platformy partycypacji ułatwiają użytkownikom umieszczanie treści generowanych przez osoby prywatne i organizacje. Możliwe jest, że pliki PDF, obrazy lub inne typy plików, w tym multimedialne, są umieszczane na platformie jako załączniki lub dodawane do pól tekstowych przez użytkowników platformy. Dokumenty te mogą nie być w pełni dostępne.", "app.containers.AccessibilityStatement.feedbackProcessIntro": "Opinia na temat dostępności strony jest dla nas bardzo ważna. Prosimy o kontakt z nami w jeden z poniższych sposobów:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "Proces przekazywania informacji zwrotnych", "app.containers.AccessibilityStatement.govocalAddress2022": "Boulevard Pachéco 34, 1000 Bruksela, Belgia", "app.containers.AccessibilityStatement.headTitle": "Oświadczenie o dostępności | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} jest zaangażowana w zapewnienie platformy, która jest dostępna dla wszystkich użytkowników, niezależnie od technologii lub umiejętności. Aktualne standardy dostępności są przestrzegane w naszych bieżących pracach, aby zmaksymalizować dostępność i użyteczność naszych platform dla wszystkich użytkowników.", "app.containers.AccessibilityStatement.mapping": "Mapowanie", "app.containers.AccessibilityStatement.mapping_1": "Mapy na platformie częściowo spełniają standardy dostępności. Zakres mapy, powiększenie i widżety interfejsu użytkownika można kontrolować za pomocą klawiatury podczas przeglądania map. <PERSON><PERSON> mogą również skonfigurować styl warstw mapy w zapleczu administracyjnym lub za pomocą integracji Esri, aby stwo<PERSON><PERSON><PERSON> bardziej dostępne palety kolorów i symbolikę. Używanie różnych stylów linii lub wielokątów (np. linii przerywanych) pomoże również rozróżnić warstwy map, jeśli to możliwe, i chociaż takiego stylu nie można obecnie skonfigurować na naszej platformie, można go skonfigurować, jeśli używasz map z integracją Esri.", "app.containers.AccessibilityStatement.mapping_2": "Mapy na platformie nie są w pełni dostępne, ponieważ nie prezentują map bazowych, warstw map ani trendów w danych użytkownikom korzystającym z czytników ekranu. W pełni dostępne mapy musiałyby dźwiękowo prezentować warstwy mapy i opisywać wszelkie istotne trendy w danych. Ponadto rysowanie map liniowych i wielokątów w ankietach nie jest dostępne, ponieważ kształtów nie można rysować za pomocą klawiatury. Alternatywne metody wprowadzania danych nie są obecnie dostępne ze względu na złożoność techniczną.", "app.containers.AccessibilityStatement.mapping_3": "Aby <PERSON><PERSON><PERSON><PERSON> rysowanie map liniowych i wielokątnych bardziej dostępnym, zalecamy włączenie wprowadzenia lub wyjaśnienia w pytaniu ankietowym lub opisie strony, co pokazuje mapa i wszelkie istotne trendy. Ponadto można podać pytanie tekstowe z krótką lub długą odpowiedzią, aby respondenci mogli w razie potrzeby opisać swoją odpowiedź prostymi słowami (zamiast klikać na mapę). Zalecamy również podanie danych kontaktowych kierownika projektu, aby respondenci, którzy nie mogą wypełnić pytania na mapie, mogli poprosić o alternatywną metodę odpowiedzi na pytanie (np. spotkanie wideo).", "app.containers.AccessibilityStatement.mapping_4": "W przypadku projektów i propozycji Ideation istnieje opcja wyświetlania danych wejściowych w widoku mapy, kt<PERSON>ry nie jest dostępny. Jednak dla tych metod dostępny jest alternatywny widok listy danych wejściowych, kt<PERSON>ry jest dostępny.", "app.containers.AccessibilityStatement.onlineWorkshopsException": "Nasze warsztaty online mają komponent streamingu wideo na żywo, który obecnie nie obsługuje napisów.", "app.containers.AccessibilityStatement.pageDescription": "Oświadczenie o dostępności tej strony internetowej", "app.containers.AccessibilityStatement.postalAddress": "<PERSON><PERSON>wy:", "app.containers.AccessibilityStatement.publicationDate": "Data publikacji", "app.containers.AccessibilityStatement.publicationDate2024": "Niniejsze oświadczenie o dostępności zostało opublikowane 21 sierpnia 2024 r.", "app.containers.AccessibilityStatement.responsiveness": "Staramy się udzielać odpowiedzi w ciągu 1-2 dni roboczych.", "app.containers.AccessibilityStatement.statusPageText": "status strony", "app.containers.AccessibilityStatement.technologiesIntro": "Do<PERSON><PERSON><PERSON><PERSON><PERSON>ć tej strony opiera się na następujących technologiach:", "app.containers.AccessibilityStatement.technologiesTitle": "Technologie", "app.containers.AccessibilityStatement.title": "Oświadczenie o dostępności", "app.containers.AccessibilityStatement.userGeneratedContent": "Treści generowane przez użytkowników", "app.containers.AccessibilityStatement.workshops": "Warsztaty", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Korzystanie z Content Builder pozwoli Ci korzystać z bardziej zaawansowanych opcji układu. W przypadku języków, dla któ<PERSON>ch w kreatorze treści nie jest dostępna żadna treść, zamiast niej wyświetlana będzie zwykła treść opisu projektu.", "app.containers.AdminPage.ProjectDescription.linkText": "Edytuj opis w kreatorze treści", "app.containers.AdminPage.ProjectDescription.saveError": "Coś poszło nie tak podczas zapisywania opisu projektu.", "app.containers.AdminPage.ProjectDescription.toggleLabel": "Użyj narzędzia Content Builder do opisu", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Dzięki narzędziu Content Builder możesz korzystać z bardziej zaawansowanych opcji układu.", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "<PERSON>obacz projekt", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "<PERSON>nie<PERSON>", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "Utwórz inteligentną grupę", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "Użytkownicy spełniający wszystkie poniższe warunki zostaną automatycznie dodani do grupy:", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "Podać co najmniej jedną zasadę", "app.containers.AdminPage.Users.UsersGroup.rulesError": "Niektóre warunki są niekompletne", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "Zapisz grupę", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "Konfiguracja inteligentnych grup nie jest częś<PERSON>ą Twojej obecnej licencji. Skontaktuj się ze swoim GovSuccess Managerem, aby dowiedzieć się więcej na ten temat.", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "Podaj nazwę grupy", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "Weryfikacja jest wyłączona dla Twojej platformy, usuń regułę weryfikacji lub skontaktuj się z obsługą klienta.", "app.containers.App.appMetaDescription": "Witamy na internetowej platformie uczestnictwa {orgName}. \nPoznaj lokalne projekty i zaangażuj się w dyskusję!", "app.containers.App.loading": "Ładowanie...", "app.containers.App.metaTitle1": "Platforma zaangażowania obywateli | {orgName}", "app.containers.App.skipLinkText": "Przejdź do głównej treści", "app.containers.AreaTerms.areaTerm": "obszar", "app.containers.AreaTerms.areasTerm": "obszary", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "Konto z tym adresem e-mail już istnieje. <PERSON><PERSON><PERSON><PERSON> w<PERSON>, zalogować przy użyciu tego adresu e-mail i zweryfikować swoje konto na stronie ustawień.", "app.containers.Authentication.steps.AccessDenied.close": "Zamknij", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "<PERSON>e spełniasz wymagań, aby wzi<PERSON><PERSON> udział w tym procesie.", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "Wróć do weryfikacji jednokrotnego logowania", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "<PERSON><PERSON><PERSON> wp<PERSON><PERSON><PERSON> token", "app.containers.Authentication.steps.Invitation.token": "Token", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "<PERSON><PERSON> j<PERSON> konto? {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.CampaignsConsentForm.ally_categoryLabel": "Wiadomości e-mail w tej kategorii", "app.containers.CampaignsConsentForm.messageError": "Wystąpił błąd przy zapisie preferencji dotyczących poczty elektronicznej.", "app.containers.CampaignsConsentForm.messageSuccess": "Twoje preferencje dotyczące poczty elektronicznej zostały zapisane.", "app.containers.CampaignsConsentForm.notificationsSubTitle": "<PERSON><PERSON><PERSON> rodzaje powiadomień e-mail chcesz otrzymywać? ", "app.containers.CampaignsConsentForm.notificationsTitle": "Powiadomienia", "app.containers.CampaignsConsentForm.submit": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangeEmail.backToProfile": "Powrót do ustawień profilu", "app.containers.ChangeEmail.confirmationModalTitle": "Potwierdź swój e-mail", "app.containers.ChangeEmail.emailEmptyError": "Podaj adres e-mail", "app.containers.ChangeEmail.emailInvalidError": "Podaj adres e-mail w prawidłowym formacie, np. <EMAIL>", "app.containers.ChangeEmail.emailRequired": "<PERSON><PERSON><PERSON> podać adres e-mail.", "app.containers.ChangeEmail.emailTaken": "Ten e-mail jest już w użyciu.", "app.containers.ChangeEmail.emailUpdateCancelled": "Aktualizacja mailowa została anulowana.", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "<PERSON><PERSON> swój e-mail, na<PERSON>ży ponownie uru<PERSON> proces.", "app.containers.ChangeEmail.helmetDescription": "Zmień swoją stronę e-mailową", "app.containers.ChangeEmail.helmetTitle": "Zmień swój e-mail", "app.containers.ChangeEmail.newEmailLabel": "Nowa poczta elektroniczna", "app.containers.ChangeEmail.submitButton": "Zgł<PERSON>ś", "app.containers.ChangeEmail.titleAddEmail": "Dodaj swój e-mail", "app.containers.ChangeEmail.titleChangeEmail": "Zmień swój e-mail", "app.containers.ChangeEmail.updateSuccessful": "Twój e-mail został pomyślnie zaktualizowany.", "app.containers.ChangePassword.currentPasswordLabel": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.ChangePassword.currentPasswordRequired": "Wpisz swoje aktualne hasło", "app.containers.ChangePassword.goHome": "Idź do domu", "app.containers.ChangePassword.helmetDescription": "Strona zmiany hasła", "app.containers.ChangePassword.helmetTitle": "Zmień swoje hasło", "app.containers.ChangePassword.newPasswordLabel": "Nowe hasło", "app.containers.ChangePassword.newPasswordRequired": "Wpisz swoje nowe hasło", "app.containers.ChangePassword.password.minimumPasswordLengthError": "Podaj hasło o długości co najmniej {minimumPasswordLength} znaków.", "app.containers.ChangePassword.passwordChangeSuccessMessage": "<PERSON>je hasło zostało pomyślnie zaktualizowane", "app.containers.ChangePassword.passwordEmptyError": "Wpisz swoje hasło", "app.containers.ChangePassword.passwordsDontMatch": "Potwierdź nowe hasło", "app.containers.ChangePassword.titleAddPassword": "<PERSON><PERSON><PERSON>", "app.containers.ChangePassword.titleChangePassword": "Zmień swoje hasło", "app.containers.Comments.a11y_commentDeleted": "Ko<PERSON><PERSON><PERSON>", "app.containers.Comments.a11y_commentPosted": "Komentarz zami<PERSON>zczony", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {brak polubień} one {1 polubienie} other {# likes}}", "app.containers.Comments.a11y_undoLike": "<PERSON><PERSON><PERSON><PERSON> jak", "app.containers.Comments.addCommentError": "Coś poszło nie tak. Proszę spróbuj ponownie później.", "app.containers.Comments.adminCommentDeletionCancelButton": "Skomentuj", "app.containers.Comments.adminCommentDeletionConfirmButton": "Usuń ten komentarz", "app.containers.Comments.cancelCommentEdit": "<PERSON><PERSON><PERSON>", "app.containers.Comments.childCommentBodyPlaceholder": "Napisz odpowiedź...", "app.containers.Comments.commentCancelUpvote": "Cof<PERSON>j", "app.containers.Comments.commentDeletedPlaceholder": "Ten komentarz został usunięty.", "app.containers.Comments.commentDeletionCancelButton": "Zatrzymaj mój komentarz", "app.containers.Comments.commentDeletionConfirmButton": "Usuń mój komentarz", "app.containers.Comments.commentLike": "Na przykład", "app.containers.Comments.commentReplyButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.commentsSortTitle": "Sortuj komentarze według", "app.containers.Comments.completeProfileLinkText": "uzupełnij swój profil", "app.containers.Comments.completeProfileToComment": "Prosimy o {completeRegistrationLink} w celu skomentowania.", "app.containers.Comments.confirmCommentDeletion": "<PERSON>zy na pewno chcesz usunąć ten komentarz? Nie ma odwrotu!", "app.containers.Comments.deleteComment": "Usuń", "app.containers.Comments.deleteReasonDescriptionError": "Podaj więcej informacji na temat przyczyny", "app.containers.Comments.deleteReasonError": "Podaj przyczynę", "app.containers.Comments.deleteReason_inappropriate": "To jest niestosowne lub obraźliwe", "app.containers.Comments.deleteReason_irrelevant": "To jest bez związku", "app.containers.Comments.deleteReason_other": "<PERSON><PERSON> powód", "app.containers.Comments.editComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.guidelinesLinkText": "nasze przewodniki", "app.containers.Comments.ideaCommentBodyPlaceholder": "Napisz swój komentarz tutaj", "app.containers.Comments.internalCommentingNudgeMessage": "Tworzenie wewnętrznych komentarzy nie jest objęte Twoją obecną licencją. Skontaktuj się ze swoim menedżerem GovSuccess, aby dowiedzieć się więcej na ten temat.", "app.containers.Comments.internalConversation": "Rozmowa wewnętrzna", "app.containers.Comments.loadMoreComments": "Załaduj więcej komentarzy", "app.containers.Comments.loadingComments": "Ładowanie komentarzy...", "app.containers.Comments.loadingMoreComments": "Ładowanie większej ilości komentarzy...", "app.containers.Comments.notVisibleToUsersPlaceholder": "Ten komentarz nie jest widoczny dla zwykłych użytkowników", "app.containers.Comments.postInternalComment": "Zamieść wewnętrzny komentarz", "app.containers.Comments.postPublicComment": "Publiczne komentarze", "app.containers.Comments.profanityError": "Mogłeś użyć jednego lub wi<PERSON><PERSON>j słów, kt<PERSON>re są uważane za nieodpowiednie przez {guidelinesLink}. Proszę zmień swój tekst, aby je usunąć.", "app.containers.Comments.publicDiscussion": "Dyskusja publiczna", "app.containers.Comments.publishComment": "<PERSON><PERSON><PERSON>ć swój komentarz", "app.containers.Comments.reportAsSpamModalTitle": "Dlaczego chcesz zgł<PERSON>ić to jako spam?", "app.containers.Comments.saveComment": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Comments.signInLinkText": "z<PERSON>uj się", "app.containers.Comments.signInToComment": "Proszę {signInLink} aby k<PERSON>.", "app.containers.Comments.signUpLinkText": "zarejestruj się", "app.containers.Comments.verifyIdentityLinkText": "Zweryfikuj swoją to<PERSON><PERSON>", "app.containers.Comments.visibleToUsersPlaceholder": "Ten komentarz jest widoczny dla zwykłych użytkowników", "app.containers.Comments.visibleToUsersWarning": "Komentarze zamieszczone tutaj będą widoczne dla stałych użytkowników.", "app.containers.ContentBuilder.PageTitle": "Opis projektu", "app.containers.CookiePolicy.advertisingContent": "Reklamowe pliki cookie mogą być używane do personalizacji i pomiaru skuteczności zewnętrznych kampanii reklamowych oraz jego przełożenia na ruch na platformie. Nie będziemy wyświetlać na platformie żadnych reklam, możesz jednak otrzymywać spersonalizowane reklamy na innych stronach, które odwiedzisz.", "app.containers.CookiePolicy.advertisingTitle": "Reklamowe pliki cookie", "app.containers.CookiePolicy.analyticsContents": "Pliki cookie do analizy śledzą zachowanie odwiedzają<PERSON>ch, np. które strony są odwiedzane i jak długo. Mogą one również gromadzić pewne dane techniczne, w tym informacje o przeglądarce, prz<PERSON><PERSON><PERSON>żoną lokalizację i adresy IP. Dane te wykorzystujemy wyłącznie wewnętrznie w celu dalszego doskonalenia ogólnych doświadczeń użytkowników i funkcjonowania platformy. Takie dane mogą być również udostępniane między Go Vocal i {orgName} w celu oceny i poprawy zaangażowania w projekty na platformie. Na<PERSON>ż<PERSON> pamię<PERSON>ć, że dane te są anonimowe i wykorzystywane na poziomie zagregowanym - nie identyfikują użytkownika osobiście. Możliwe jest jednak, że jeśli dane te zostaną połączone z innymi źródłami danych, może dojść do takiej identyfikacji.", "app.containers.CookiePolicy.analyticsTitle": "Analityczne pliki cookie", "app.containers.CookiePolicy.cookiePolicyDescription": "Szczegółowe wyjaśnienie, w jaki spos<PERSON><PERSON> używamy cookies na tej platformie", "app.containers.CookiePolicy.cookiePolicyTitle": "Polityka ciasteczek", "app.containers.CookiePolicy.essentialContent": "Niektóre pliki cookie są niezbędne do prawidłowego funkcjonowania platformy. Są one używane głównie do uwierzytelnienia twojego konta gdy odwiedzasz platformę oraz do zachowania informacji o preferowanym języku.", "app.containers.CookiePolicy.essentialTitle": "Niezbędne pliki cookie", "app.containers.CookiePolicy.externalContent": "Niektóre z naszych stron mogą wyświ<PERSON><PERSON>ć zawartość od zewnętrznych dostawców, np. YouTube’a lub Typeform. Nie mamy kontroli nad plikami cookie stron trzecich. Oglądanie treści od zewnętrznych dostawców może również skutkować zainstalowaniem cookies na twoim urządzeniu.", "app.containers.CookiePolicy.externalTitle": "Zewnętrzne pliki cookie", "app.containers.CookiePolicy.functionalContents": "Funkcjonalne pliki cookie mogą być włączone dla odwiedzających, aby otr<PERSON><PERSON><PERSON><PERSON> powiadomienia o aktualizacjach i uzyskać dostęp do kanałów wsparcia bezpośrednio z platformy.", "app.containers.CookiePolicy.functionalTitle": "Funkcjonalne pliki cookie", "app.containers.CookiePolicy.headCookiePolicyTitle": "Polityka plików cookie | {orgName}", "app.containers.CookiePolicy.intro": "<PERSON><PERSON> to pliki tekstowe przechowywane w przeglądarce lub na twardym dysku twojego komputera lub urządzenia mobilnego gdy odwiedzasz stronę internetową i z których dana strona może korzystać kolejnym razem gdy ją odwiedzisz. Korzystamy z cookie aby zrozumieć sposób, w jaki użytkownicy korzystają z platformy, celem poprawy jej projektu i zadowolenia z użytkowania, aby zapamiętać twoje preferencje (np. preferowany język) oraz dla wsparcia kluczowych funkcji dla zarejestrowanych użytkowników i administratorów platformy.", "app.containers.CookiePolicy.manageCookiesDescription": "<PERSON><PERSON><PERSON><PERSON> u<PERSON><PERSON><PERSON>ić lub zdezaktywować analityczne, reklamowe oraz funkcjonalne pliki cookie w każdym momencie, w twoich ustawieniach cookie. Moż<PERSON>z również ręcznie lub automatycznie usunąć jakiekolwiek istniejące pliki cookie za pomocą twojej przeglądarki internetowej. Należy jednak pamię<PERSON>ć, że pliki cookie mogą zostać zapisane ponownie, po tym jak udzielisz na to zgodę podczas kolejnej wizyty na platformie. Jeśli nie usuniesz plików cookie, wybrane przez ciebie cookie będą przechowywane przez 60 dni, po których ponownie zostaniesz poproszony o zgodę na ich przechowywanie.", "app.containers.CookiePolicy.manageCookiesPreferences": "Przejdź do {manageCookiesPreferencesButtonText} aby zob<PERSON><PERSON>ć pełną listę integracji ze stronami trzecimi używanych na platformie oraz aby zarządzać swoimi ustawieniami.", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "ustawień cookie", "app.containers.CookiePolicy.manageCookiesTitle": "Zarządzanie plikami cookie", "app.containers.CookiePolicy.viewPreferencesButtonText": "Ustawień cookie", "app.containers.CookiePolicy.viewPreferencesText": "Poniższe kategorie plików cookie mogą nie mieć zastosowania do wszystkich użytkowników lub platform; zapoznaj się z {viewPreferencesButton}, aby uzyskać pełną listę integracji stron trzecich.", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "Do <PERSON><PERSON><PERSON>ż<PERSON> cookie?", "app.containers.CustomPageShow.editPage": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.CustomPageShow.goBack": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.CustomPageShow.notFound": "Nie znaleziono strony", "app.containers.DisabledAccount.bottomText": "Ponownie można się zalogować ze strony {date}.", "app.containers.DisabledAccount.termsAndConditions": "zasady i warunki", "app.containers.DisabledAccount.text2": "Twoje konto na platformie uczestnictwa {orgName} zostało tymczasowo wyłączone z powodu naruszenia wytycznych społeczności. Więcej informacji na ten temat można uzyskać na stronie {TermsAndConditions}.", "app.containers.DisabledAccount.title": "Twoje konto zostało tymczasowo wyłączone", "app.containers.EventsShow.addToCalendar": "Dodaj do kalendarza", "app.containers.EventsShow.editEvent": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.EventsShow.emailSharingBody2": "<PERSON><PERSON> ud<PERSON>ł w tym wydarzeniu: {eventTitle}. Przeczytaj więcej na {eventUrl}", "app.containers.EventsShow.eventDateTimeIcon": "Data i godzina wydarzenia", "app.containers.EventsShow.eventFrom2": "Z \"{projectTitle}\"", "app.containers.EventsShow.goBack": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.EventsShow.goToProject": "Przejdź do projektu", "app.containers.EventsShow.haveRegistered": "zarejestrowali się", "app.containers.EventsShow.icsError": "Błąd pobierania pliku ICS", "app.containers.EventsShow.linkToOnlineEvent": "Link do wydarzenia online", "app.containers.EventsShow.locationIconAltText": "Lokalizacja", "app.containers.EventsShow.metaTitle": "Wydarzenie: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "Spotkanie online", "app.containers.EventsShow.onlineLinkIconAltText": "Link do spotkania online", "app.containers.EventsShow.registered": "zarejestrowany", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 rejestrujących się} one {1 rejestrujący się} other {# rejestrujących się}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} rejest<PERSON><PERSON><PERSON><PERSON>ch", "app.containers.EventsShow.registrantsIconAltText": "Rejestrują<PERSON>", "app.containers.EventsShow.socialMediaSharingMessage": "<PERSON><PERSON> ud<PERSON>ł w tym wydarzeniu: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# uczestnik} other {# uczest<PERSON><PERSON>}}", "app.containers.EventsViewer.allTime": "Cały czas", "app.containers.EventsViewer.date": "Data", "app.containers.EventsViewer.thisMonth2": "Nadchod<PERSON><PERSON><PERSON>", "app.containers.EventsViewer.thisWeek2": "Nadchodzący tydzień", "app.containers.EventsViewer.today": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAContribution": "<PERSON><PERSON><PERSON> w<PERSON>d", "app.containers.IdeaButton.addAPetition": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAProject": "<PERSON><PERSON><PERSON> projekt", "app.containers.IdeaButton.addAProposal": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAQuestion": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnInitiative": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.addAnOption": "<PERSON><PERSON><PERSON>", "app.containers.IdeaButton.postingDisabled": "Nowe zgłoszenia nie są obecnie przyjmowane", "app.containers.IdeaButton.postingInNonActivePhases": "Nowe zgłoszenia mogą być dodawane tylko w aktywnych etapach.", "app.containers.IdeaButton.postingInactive": "Nowe zgłoszenia nie są obecnie przyjmowane.", "app.containers.IdeaButton.postingLimitedMaxReached": "Wypełniłeś już ankietę. Dziękujemy za odpowiedź!", "app.containers.IdeaButton.postingNoPermission": "Nowe zgłoszenia nie są obecnie dostępne", "app.containers.IdeaButton.postingNotYetPossible": "Nowe zgłoszenia nie są jeszcze przyjmowane.", "app.containers.IdeaButton.signInLinkText": "z<PERSON>uj się", "app.containers.IdeaButton.signUpLinkText": "zarejestruj się", "app.containers.IdeaButton.submitAnIssue": "Zgłoś sprawę", "app.containers.IdeaButton.submitYourIdea": "Zgłoś swój pomysł", "app.containers.IdeaButton.takeTheSurvey": "<PERSON>ź udział w ankiecie", "app.containers.IdeaButton.verificationLinkText": "Zweryfikuj swoją to<PERSON><PERSON>.", "app.containers.IdeaCard.readMore": "Czytaj więcej", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {brak komentarzy} one {1 komentarz} few {# komentarze} many {# komentarzy} other {# komentarze}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {brak głosów} one {1 głos} other {# głosów}} poza {votingThreshold}", "app.containers.IdeaCards.a11y_closeFilterPanel": "Zamknij panel filtrów", "app.containers.IdeaCards.a11y_totalItems": "Razem: {ideasCount}", "app.containers.IdeaCards.all": "Wszystkie", "app.containers.IdeaCards.allStatuses": "Wszystkie statusy", "app.containers.IdeaCards.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.ideaTerm": "Pomysły", "app.containers.IdeaCards.initiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.issueTerm": "<PERSON><PERSON>", "app.containers.IdeaCards.list": "Lista", "app.containers.IdeaCards.map": "Mapa", "app.containers.IdeaCards.mostDiscussed": "Najczęściej omawiane", "app.containers.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.noFilteredResults": "Nie znaleziono wyników. Spróbuj innego filtru lub terminu.", "app.containers.IdeaCards.numberResults": "Wyniki ({postCount})", "app.containers.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.optionTerm": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.petitions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.popular": "Najwięcej głosów", "app.containers.IdeaCards.projectFilterTitle": "Projekty", "app.containers.IdeaCards.projectTerm": "Projekty", "app.containers.IdeaCards.proposals": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeaCards.questionTerm": "Pytania", "app.containers.IdeaCards.random": "Losowo", "app.containers.IdeaCards.resetFilters": "Usuń filtry", "app.containers.IdeaCards.showXResults": "<PERSON><PERSON><PERSON> {ideasCount, plural, one {# wynik} few {# wyniki } many {# wyników} other {# wyniki}}", "app.containers.IdeaCards.sortTitle": "Sort<PERSON>nie", "app.containers.IdeaCards.statusTitle": "Status", "app.containers.IdeaCards.statusesTitle": "Status", "app.containers.IdeaCards.topics": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.topicsTitle": "<PERSON><PERSON><PERSON>", "app.containers.IdeaCards.trending": "<PERSON> czasie", "app.containers.IdeaCards.tryDifferentFilters": "Nie znaleziono wyników. Spróbuj innego filtru lub terminu.", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} komentarze} one {{ideasCount} komentarze} other {{ideasCount} komentarze}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} wpłaty} one {{ideasCount} wpłaty} other {{ideasCount} wpłaty}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} pomysły} one {{ideasCount} pomysły} other {{ideasCount} pomysły}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} inicjatywy} one {{ideasCount} inicjatywy} other {{ideasCount} inicjatywy}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} opcje} one {{ideasCount} opcje} other {{ideasCount} opcje}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petycje} one {{ideasCount} petycje} other {{ideasCount} petycje}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} projekty} one {{ideasCount} projekt} other {{ideasCount} projekty}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} propozycje} one {{ideasCount} propozycje} other {{ideasCount} propozycje}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} pytania} one {{ideasCount} pytania} other {{ideasCount} pytania}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# wynik} few {# wyniki} many {# wyników} other {# wyniki}}", "app.containers.IdeasEditPage.contributionFormTitle": "<PERSON><PERSON><PERSON><PERSON> wkład", "app.containers.IdeasEditPage.editedPostSave": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.fileUploadError": "Nie udało się przesłać jednego lub więcej plików. Sprawdź rozmiar i format pliku i spróbuj ponownie.", "app.containers.IdeasEditPage.formTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.ideasEditMetaDescription": "Edytuj swój post. Dodaj nowe i zmień nieaktualne informacje.", "app.containers.IdeasEditPage.ideasEditMetaTitle": "Edytuj {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.issueFormTitle": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "app.containers.IdeasEditPage.optionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.petitionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.projectFormTitle": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.IdeasEditPage.proposalFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.questionFormTitle": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.save": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasEditPage.submitApiError": "Wystąpił problem z przesłaniem formularza. <PERSON><PERSON><PERSON> sprawdź, czy nie wystąpiły błędy i spróbuj ponownie.", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "Wszystkie opublikowane dane wejściowe", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "Poznaj wszystkie inicjatywy zamieszczone na platformie uczestnictwa {orgName}.", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "Posty | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasIndexPage.loadMore": "Załaduj więcej...", "app.containers.IdeasIndexPage.loading": "Ładowanie...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "Domyślnie Twoje zgłoszenia będą powiązane z Twoim profilem, chyba że wybierzesz tę opcję.", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profilu", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "Ta ankieta nie jest obecnie otwarta dla odpowiedzi. Wróć do projektu, aby u<PERSON><PERSON><PERSON> więcej informacji.", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "Ta ankieta nie jest obecnie aktywna.", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "Wróć do projektu", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "Wypełniłeś już tę ankietę.", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "Dziękujemy za twoją odpowiedź!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "Opis wkładu musi mieć długość mniej<PERSON>ą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "<PERSON><PERSON><PERSON><PERSON> pomysłu musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "Tytuł wkładu musi mieć długość mniej<PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "Tytuł pracy musi mieć długość więks<PERSON><PERSON> niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "Wybierz co najmniej jednego współwnioskodawcę", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "Opis pomysłu musi mieć długość mniej<PERSON>ą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "Opis pomysłu musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "<PERSON><PERSON><PERSON> opis", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "Tytuł pomysłu musi mieć długość mniejszą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "Tytuł pomysłu musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "Opis inicjatywy nie może zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "Opis inicjatywy musi zawierać więcej niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "Tytuł inic<PERSON>tywy nie może zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "Tytuł inicjatywy musi zawierać więcej niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "Opis sprawy musi mieć długość mniejszą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "Opis sprawy musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "Tytuł sprawy musi mieć długość mniejszą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "Tytuł wydania musi zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_number_required": "To pole jest w<PERSON><PERSON><PERSON>, w<PERSON><PERSON><PERSON><PERSON> prawidłowy numer", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "Opis opcji musi mieć długość mniejszą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "Opis opcji musi zawierać więcej niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "Tytuł opcji musi mieć długość mniejszą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "Tytuł opcji musi mieć długość więks<PERSON><PERSON> niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "Proszę wybrać co najmniej jeden tag", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "Opis petycji nie może zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "Opis petycji musi zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "<PERSON><PERSON>ł petycji nie może zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "Ty<PERSON>ł petycji musi zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "Opis projektu musi mieć długość mniej<PERSON>ą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "Opis projektu musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "Tytuł projektu musi mieć długość mniej<PERSON>ą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "Tytuł projektu musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "Opis wniosku nie może zawierać więcej niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "Opis wniosku musi zawierać więcej niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "Tytuł wniosku nie może zawierać więcej niż {limit} znaków.", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "Tytuł wniosku musi zawierać więcej niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "Proszę wpisz numer", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "Proszę wpisz numer", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "Opis pytania musi mieć długość mniejszą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "Opis pytania musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "Tytuł pytania musi mieć długość mniejszą niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "Tytuł pytania musi mieć długość wię<PERSON><PERSON><PERSON> niż {limit} znaków", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "<PERSON><PERSON><PERSON> t<PERSON>", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "Opis wkładu musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "Opis wkładu musi mieć co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "Tytuł wkładu musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "Tytuł wkładu musi składać się z co najmniej 10 znaków", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "Opis pomysłu musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "Opis pomysłu musi składać się z co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "<PERSON><PERSON><PERSON> t<PERSON>", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "Tytuł pomysłu musi mieć mniej niż 80 znaków.", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "Tytuł pomysłu musi mieć co najmniej 10 znaków", "app.containers.IdeasNewPage.api_error_includes_banned_words": "Mogłeś użyć jednego lub wi<PERSON><PERSON>j słów, kt<PERSON>re są uważane za nieodpowiednie przez {guidelinesLink}. Proszę zmień swój tekst, aby je usunąć.", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "Opis inicjatywy nie może zawierać więcej niż 80 znaków", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "Opis inicjatywy musi zawierać co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "Tytuł inicjatywy nie może zawierać więcej niż 80 znaków", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "Tytuł inicjatywy musi składać się z co najmniej 10 znaków", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "Opis sprawy musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "Opis sprawy musi mieć co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "Tytuł sprawy musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "Tytuł sprawy musi mieć co najmniej 10 znaków", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "Opis opcji musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "Opis opcji musi mieć długość co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "Dług<PERSON>ść tytułu opcji musi być mniejsza niż 80 znaków", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "Tytuł opcji musi zawierać co najmniej 10 znaków", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "Opis petycji nie może zawierać więcej niż 80 znaków", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "Opis petycji musi zawierać co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "Tytuł petycji nie może zawierać więcej niż 80 znaków.", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "Tytuł petycji musi składać się z co najmniej 10 znaków.", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "Opis projektu musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "Opis projektu musi zawierać co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "Tytuł projektu musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "Tytuł projektu musi składać się z co najmniej 10 znaków", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "Opis propozycji musi zawierać mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "Opis propozycji musi zawierać co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "Tytuł wniosku nie może zawierać więcej niż 80 znaków", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "Tytuł wniosku musi składać się z co najmniej 10 znaków", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "<PERSON><PERSON><PERSON> opis", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "Opis pytania musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "Opis pytania musi mieć co najmniej 30 znaków", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "Tytuł pytania musi mieć mniej niż 80 znaków", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "Tytuł pytania musi mieć co najmniej 10 znaków", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "<PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "Tak, chcę odejść", "app.containers.IdeasNewPage.contributionMetaTitle1": "Dodaj nowy wkład do projektu | {orgName}", "app.containers.IdeasNewPage.editSurvey": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasNewPage.ideaNewMetaDescription": "Wyślij zgłoszenie i dołącz do dyskusji na {orgName} - platformie uczestnictwa.", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "Dodaj nowy pomysł do projektu | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "Dodaj nową inicjatywę do projektu | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "Dodaj nowe wydanie do projektu | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "<PERSON><PERSON><PERSON>, że ch<PERSON> ode<PERSON>?", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "Twoja wersja robocza odpowiedzi została zapisana prywatnie i możesz do niej wrócić później.", "app.containers.IdeasNewPage.leaveSurvey": "Zostaw ankietę", "app.containers.IdeasNewPage.leaveSurveyText": "Twoje odpowiedzi nie zostaną zapisane.", "app.containers.IdeasNewPage.optionMetaTitle1": "<PERSON><PERSON><PERSON> nową opcję do projektu | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "<PERSON><PERSON><PERSON> nową petycję do projektu | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "Dodaj nowy projekt do projektu | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "Dodaj nową propozycję do projektu | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "Dodaj nowe pytanie do projektu | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "Przyjmij zaproszenie", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "Zaproszenie do współsponsorowania", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "Współsponsorzy", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "Zostałeś zaproszony do zostania współsponsorem.", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "Zaproszenie przyjęte", "app.containers.IdeasShow.Cosponsorship.pending": "w toku", "app.containers.IdeasShow.MetaInformation.attachments": "Załączniki", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName}, {date}", "app.containers.IdeasShow.MetaInformation.currentStatus": "Aktualny status", "app.containers.IdeasShow.MetaInformation.location": "Lokalizacja", "app.containers.IdeasShow.MetaInformation.postedBy": "Zgłoszony przez", "app.containers.IdeasShow.MetaInformation.similar": "<PERSON><PERSON><PERSON><PERSON> dane we<PERSON>", "app.containers.IdeasShow.MetaInformation.topics": "<PERSON><PERSON><PERSON>", "app.containers.IdeasShow.commentCTA": "Skomentuj", "app.containers.IdeasShow.contributionEmailSharingBody": "<PERSON><PERSON><PERSON>j ten wkład '{postTitle}' w {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "<PERSON>rz<PERSON>j ten wkład: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "Dziękujemy za przesłanie Twojego wkładu!", "app.containers.IdeasShow.contributionTwitterMessage": "<PERSON>rz<PERSON>j ten wkład: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "<PERSON>rz<PERSON>j ten wkład: {postTitle}", "app.containers.IdeasShow.currentStatus": "Aktualny status", "app.containers.IdeasShow.deletedUser": "niewiadomy autor", "app.containers.IdeasShow.ideaEmailSharingBody": "<PERSON><PERSON><PERSON>j mój pomysł '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.ideaEmailSharingSubject": "<PERSON>rz<PERSON>j mój pomysł: {ideaTitle}.", "app.containers.IdeasShow.ideaTwitterMessage": "Poprzyj ten pomysł: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "Poprzyj ten pomysł: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON> tę sprawę: {postTitle}", "app.containers.IdeasShow.imported": "I<PERSON>rt<PERSON>ne", "app.containers.IdeasShow.importedTooltip": "Ta strona {inputTerm} została zebrana offline i automatycznie przesłana na platformę.", "app.containers.IdeasShow.initiativeEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę inicjatywę '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.initiativeEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę inicjatywę: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "Dziękujemy za zgłoszenie inicjatywy!", "app.containers.IdeasShow.initiativeTwitterMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę inicjatywę: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę inicjatywę: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "<PERSON><PERSON><PERSON>j tę sprawę '{postTitle}' w {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON> tę sprawę: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "Dziękujemy za przesłanie Twojego komentarza!", "app.containers.IdeasShow.issueTwitterMessage": "<PERSON><PERSON><PERSON><PERSON> tę sprawę: {postTitle}", "app.containers.IdeasShow.metaTitle": "Wejście: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "<PERSON><PERSON><PERSON>j tę opcję '{postTitle}' w {postUrl}!", "app.containers.IdeasShow.optionEmailSharingSubject": "<PERSON><PERSON><PERSON>j tę opcję: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "Twoja opcja została pomyślnie przesłana!", "app.containers.IdeasShow.optionTwitterMessage": "<PERSON><PERSON><PERSON>j tę opcję: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "<PERSON><PERSON><PERSON>j tę opcję: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.petitionEmailSharingSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę petycję: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "Dziękujemy za złożenie petycji!", "app.containers.IdeasShow.petitionTwitterMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę petycję: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę petycję: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "Poprzyj ten projekt '{postTitle}' na {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "Poprzyj ten projekt: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "Dziękujemy za zgłoszenie Twojego projektu!", "app.containers.IdeasShow.projectTwitterMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> ten projekt: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "Poprzyj ten projekt: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "<PERSON><PERSON><PERSON><PERSON><PERSON> tę propozycję '{ideaTitle}' na {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "<PERSON><PERSON><PERSON>j tę propozycję: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "Dziękujemy za przesłanie Twojej propozycji!", "app.containers.IdeasShow.proposalTwitterMessage": "<PERSON><PERSON><PERSON>j tę propozycję: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "<PERSON><PERSON><PERSON>j tę propozycję: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "Pozostały czas na głosowanie:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{xVotes} z {votingThreshold} wymaganych głosów", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "<PERSON><PERSON>j głosowanie", "app.containers.IdeasShow.proposals.VoteControl.days": "dni", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "nasze wytyczne", "app.containers.IdeasShow.proposals.VoteControl.hours": "god<PERSON>y", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "Status i głosy", "app.containers.IdeasShow.proposals.VoteControl.minutes": "miny", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "Więcej informacji", "app.containers.IdeasShow.proposals.VoteControl.vote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.IdeasShow.proposals.VoteControl.voted": "Zagłosowano", "app.containers.IdeasShow.proposals.VoteControl.votedText": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy ta inicjatywa przejdzie do następnego etapu. {x, plural, =0 {Zostało {xDays} .} one {Zostało {xDays} .} other {There are {xDays} left.}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "Twój głos został oddany!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "<PERSON><PERSON><PERSON>, nie możesz głosować nad tą propozycją. Przeczytaj dlaczego na {link}.", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {mniej ni<PERSON> jeden dzień} one {jeden dzień} other {# dni}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {brak głosów} one {1 głos} other {# głosów}}", "app.containers.IdeasShow.questionEmailSharingBody": "Dołącz do dyskusji na temat tego pytania '{postTitle}' na stronie {postUrl}!", "app.containers.IdeasShow.questionEmailSharingSubject": "<PERSON><PERSON> udział w dyskusji: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "Twoje pytanie zostało pomyślnie wysłane!", "app.containers.IdeasShow.questionTwitterMessage": "<PERSON><PERSON> udział w dyskusji: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "<PERSON><PERSON> udział w dyskusji: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "Dlaczego chcesz zgł<PERSON>ić to jako spam?", "app.containers.IdeasShow.share": "Podziel się", "app.containers.IdeasShow.sharingModalSubtitle": "Dotrzyj do większej ilości osób i spraw, by twój głos był słyszalny.", "app.containers.IdeasShow.sharingModalTitle": "Dziękujemy za zgłoszenie swojego pomysłu!", "app.containers.Navbar.completeOnboarding": "Pełne wdrożenie", "app.containers.Navbar.completeProfile": "Pełny profil", "app.containers.Navbar.confirmEmail2": "Potwierdź wiadomość e-mail", "app.containers.Navbar.unverified": "Niezweryfikowany", "app.containers.Navbar.verified": "Zweryfikowany", "app.containers.NewAuthModal.beforeYouFollow": "Zanim zaczniesz śledzić", "app.containers.NewAuthModal.beforeYouParticipate": "<PERSON><PERSON><PERSON> weźmiesz ud<PERSON>ł", "app.containers.NewAuthModal.completeYourProfile": "Uzupełnij swój profil", "app.containers.NewAuthModal.confirmYourEmail": "Potwierdź swój e-mail", "app.containers.NewAuthModal.logIn": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "Zapoznaj się z poniższymi warunkami, aby k<PERSON>.", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "<PERSON><PERSON><PERSON> swój profil.", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "Wróć do opcji logowania", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "Nie masz konta? {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "Zarejestruj się", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "Kod musi mieć 4 cyfry.", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "Kontynuuj z FranceConnect", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "Na tej platformie nie są włączone żadne metody uwierzytelniania.", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zgadzasz się na otrzymywanie e-maili z tej platformy. <PERSON><PERSON><PERSON><PERSON>, które e-maile chcesz otrzymywać na stronie \"Moje ustawienia\".", "app.containers.NewAuthModal.steps.EmailSignUp.email": "Email", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "Podaj adres e-mail w prawidłowym formacie, np. <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "Podaj adres e-mail", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "Wpisz swój adres e-mail, aby k<PERSON>.", "app.containers.NewAuthModal.steps.Password.forgotPassword": "<PERSON><PERSON> p<PERSON><PERSON>ła?", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "Zaloguj się na swoje konto: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "<PERSON><PERSON><PERSON> pod<PERSON> hasło", "app.containers.NewAuthModal.steps.Password.password": "<PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.steps.Password.rememberMe": "Pamiętaj o mnie", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "Nie wybieraj w przypadku korzystania z komputera publicznego", "app.containers.NewAuthModal.steps.Success.allDone": "Wszystko zrobione", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "Teraz kontynuuj swój udział.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "Twoja tożsamość została zweryfikowana. Jesteś teraz pełnoprawnym członkiem społeczności na tej platformie.", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "Jesteś teraz zweryfikowany !", "app.containers.NewAuthModal.steps.close": "Zamknij", "app.containers.NewAuthModal.steps.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.NewAuthModal.whatAreYouInterestedIn": "Czym się interesujesz?", "app.containers.NewAuthModal.youCantParticipate": "Nie możesz w tym uczestniczyć", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {brak nieprzejrz<PERSON>ch powiadomień} one {1 nieprzejrzane powiadomienie} few {# nieprzejrzane powiadomienia} many {# nieprzej<PERSON><PERSON>ch powiadomień} other {# nieprzej<PERSON><PERSON>ch  powiadomień}}", "app.containers.NotificationMenu.adminRightsReceived": "<PERSON><PERSON><PERSON> teraz administratorem platformy", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "Twój komentarz na \"{postTitle}\" został usunięty przez administratora, ponieważ\n      {reasonCode, select, irrelevant {jest nieistotny} inappropriate {jego treść jest nieodpowiednia} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} przyjąłeś zaproszenie do współsponsorowania", "app.containers.NotificationMenu.deletedUser": "Niewiadomy autor", "app.containers.NotificationMenu.error": "Nie można załadować powiadomień", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} skomentowałeś wewnętrznie wejście przypisane do ciebie", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} sko<PERSON><PERSON>łeś wewnętrznie dane wejściowe, które skomentowałeś wewnętrznie", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} skomentowałeś wewnętrznie dane wejściowe w projekcie, którym zarządzasz", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} wewnętr<PERSON>nie skomentował nieprzypisane dane wejściowe w niezarząd<PERSON><PERSON> proje<PERSON>cie", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} skomentował twój wewnętrzny komentarz", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} zaprosił Cię do współsponsorowania wkładu", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} zaprosił Cię do współsponsorowania pomysłu", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} zaprosił Cię do objęcia patronatem wniosku", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} zaprosił Cię do współsponsorowania sprawy", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} zaprosił Cię do współsponsorowania opcji", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} zaprosił Cię do współsponsorowania petycji", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} zaprosił Cię do współsponsorowania projektu", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} zaprosił Cię do współsponsorowania wniosku", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} zaprosił Cię do współsponsorowania pytania", "app.containers.NotificationMenu.loadMore": "Załaduj więcej...", "app.containers.NotificationMenu.loading": "Ładowanie powiadomień...", "app.containers.NotificationMenu.mentionInComment": "{name} wspomniał o tobie w komentarzu", "app.containers.NotificationMenu.mentionInInternalComment": "{name} wspomniałem o tobie w wewnętrznym komentarzu", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} wspomniał o tobie w oficjalnej aktualizacji", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "Nie przesłałeś swojej ankiety", "app.containers.NotificationMenu.noNotifications": "<PERSON>e masz jeszcze żadnych powiadomień", "app.containers.NotificationMenu.notificationsLabel": "Powiadomienia", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> ofic<PERSON>lną aktualizację dot<PERSON> wkładu, k<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON>lną aktualizację pomy<PERSON>, k<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> oficjalną aktualizację dotyczą<PERSON>ą inicjatywy, k<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> oficjalną aktualizację w sprawie, k<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> ofic<PERSON>lną aktualizację opc<PERSON>, k<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> ofic<PERSON>lną aktualizację pet<PERSON>, k<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> ofic<PERSON>lną aktualizację dotyczącą projektu, kt<PERSON><PERSON> o<PERSON>er<PERSON>", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> oficjalną aktualizację dotyczącą propozycji, k<PERSON><PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} p<PERSON><PERSON><PERSON><PERSON> oficjalną aktualizację na pytanie, które śledzisz", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} został przypisany do ciebie", "app.containers.NotificationMenu.projectModerationRightsReceived": "Je<PERSON>ś teraz menadżerem projektu {projectLink}", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} wszedł w nową fazę", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} wejdzie w nową fazę od {phaseStartAt}", "app.containers.NotificationMenu.projectPublished": "Nowy projekt został opublikowany", "app.containers.NotificationMenu.projectReviewRequest": "{name} zwrócił się o zgodę na opublikowanie projektu \"{projectTitle}\"", "app.containers.NotificationMenu.projectReviewStateChange": "{name} zatwierdzony \"{projectTitle}\" do publikacji", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} status został zmieniony na {status}", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} osiągnął próg poparcia w głosowaniu", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} p<PERSON><PERSON><PERSON><PERSON><PERSON>je zaproszenie", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wpis, k<PERSON><PERSON><PERSON> obser<PERSON>", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} skomentowałeś pomysł, za którym podążasz", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} skomento<PERSON>ł<PERSON>ś inicjatywę, kt<PERSON><PERSON><PERSON>led<PERSON>", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} skomentowałeś sprawę, kt<PERSON><PERSON><PERSON> śledzisz", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} skomentowałeś opcję, kt<PERSON><PERSON><PERSON> obserwujesz", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} skomento<PERSON>ł<PERSON>ś <PERSON>, kt<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} s<PERSON><PERSON><PERSON><PERSON> projekt, k<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} skomentowałeś propozycję, kt<PERSON><PERSON><PERSON>", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} skomentowałeś pytanie, kt<PERSON>re śledzisz", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} zg<PERSON><PERSON><PERSON><PERSON> \"{postTitle}\" jako spam", "app.containers.NotificationMenu.userReactedToYourComment": "{name} zareagował na twój komentarz", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} zgłosiłeś komentarz do \"{postTitle}\" jako spam", "app.containers.NotificationMenu.votingBasketNotSubmitted": "<PERSON><PERSON> oddałeś swojego głosu", "app.containers.NotificationMenu.votingBasketSubmitted": "Zagłosowałeś pomyślnie", "app.containers.NotificationMenu.votingLastChance": "Ostatnia szansa na oddanie głosu na {phaseTitle}", "app.containers.NotificationMenu.votingResults": "{phaseTitle} wyniki głosowania ujawnione", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} przy<PERSON><PERSON>ł {postTitle} do ciebie", "app.containers.PasswordRecovery.emailError": "To nie wygląda na prawidłowy e-mail", "app.containers.PasswordRecovery.emailLabel": "E-mail", "app.containers.PasswordRecovery.emailPlaceholder": "Mój adres e-mail", "app.containers.PasswordRecovery.helmetDescription": "Zmień hasło do strony", "app.containers.PasswordRecovery.helmetTitle": "Zmień swoje hasło", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "<PERSON><PERSON><PERSON> ten adres e-mail jest zarejestrowany na platformie, został wysłany link do resetowania hasła.", "app.containers.PasswordRecovery.resetPassword": "Wyślij link do zmiany hasła", "app.containers.PasswordRecovery.submitError": "Nie mogliśmy znaleźć konta powiązanego z tym mailem. Zamiast tego spróbuj się zare<PERSON>strować.", "app.containers.PasswordRecovery.subtitle": "Gdzie możemy wysłać link w celu ustawienia nowego hasła?", "app.containers.PasswordRecovery.title": "<PERSON><PERSON><PERSON> hasła", "app.containers.PasswordReset.helmetDescription": "Zmień hasło do strony", "app.containers.PasswordReset.helmetTitle": "Zmień swoje hasło", "app.containers.PasswordReset.login": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordError": "Twoje hasło musi zawierać co najmniej 8 znaków", "app.containers.PasswordReset.passwordLabel": "<PERSON><PERSON><PERSON>", "app.containers.PasswordReset.passwordPlaceholder": "Nowe hasło", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "<PERSON>je hasło zostało pomyślnie zaktualizowane.", "app.containers.PasswordReset.pleaseLogInMessage": "Prosimy o zalogowanie się za pomocą nowego hasła.", "app.containers.PasswordReset.requestNewPasswordReset": "Prośba o zresetowanie nowego hasła", "app.containers.PasswordReset.submitError": "Coś poszło nie tak. Proszę spróbuj ponownie później.", "app.containers.PasswordReset.title": "Zmień swoje hasło", "app.containers.PasswordReset.updatePassword": "Potwierdź nowe hasło", "app.containers.ProjectFolderCards.allProjects": "Wszystkie projekty", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} pracuje obecnie nad", "app.containers.ProjectFolderShowPage.editFolder": "<PERSON><PERSON><PERSON><PERSON> folder", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "Informacje na temat tego projektu", "app.containers.ProjectFolderShowPage.metaTitle1": "Folder: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "Przeczytaj więcej", "app.containers.ProjectFolderShowPage.seeLess": "Zobacz mniej", "app.containers.ProjectFolderShowPage.share": "Podziel się", "app.containers.Projects.PollForm.document": "Dokument", "app.containers.Projects.PollForm.formCompleted": "Dziękujemy! Twoje zgłoszenie zostało odebrane.", "app.containers.Projects.PollForm.maxOptions": "max. {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "Wziąłeś już udział w tej ankiecie.", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "Ta ankieta może zostać przeprowadzona tylko wtedy, gdy ta faza jest aktywna.", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "<PERSON><PERSON><PERSON>, nie masz uprawnień do wzięcia udziału w tej ankiecie.", "app.containers.Projects.PollForm.pollDisabledNotPossible": "Obecnie nie można wziąć udziału w tym badaniu.", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "Badanie nie jest już <PERSON>, ponieważ ten projekt nie jest już aktywny.", "app.containers.Projects.PollForm.sendAnswer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.a11y_phase": "Etap {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "Przegląd etapów", "app.containers.Projects.a11y_titleInputs": "Wszystkie inicjatywy zgłoszone do tego projektu", "app.containers.Projects.a11y_titleInputsPhase": "Wszystkie inicjatywy zgłoszone na tym etapie projektu", "app.containers.Projects.accessRights": "<PERSON><PERSON><PERSON>", "app.containers.Projects.addedToBasket": "Dodano do koszyka", "app.containers.Projects.allocateBudget": "Przydziel swój budżet", "app.containers.Projects.archived": "Zarchiwizowany", "app.containers.Projects.basketSubmitted": "Twój koszyk został wysłany!", "app.containers.Projects.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.createANewPhase": "Utwórz nową fazę", "app.containers.Projects.currentPhase": "Aktualny etap", "app.containers.Projects.document": "Dokument", "app.containers.Projects.editProject": "<PERSON><PERSON><PERSON><PERSON> projekt", "app.containers.Projects.emailSharingBody": "Co sądzisz o tej inicjatywie? Zagłosuj na nią i podziel się dyskusją na stronie {initiativeUrl}, aby Twój głos został usłyszany!", "app.containers.Projects.emailSharingSubject": "<PERSON>rz<PERSON>j moją inicjatywę: {initiativeTitle}.", "app.containers.Projects.endedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {date}", "app.containers.Projects.events": "Wyd<PERSON>zen<PERSON>", "app.containers.Projects.header": "Projekty", "app.containers.Projects.ideas": "Pomysły", "app.containers.Projects.information": "Informacja", "app.containers.Projects.initiatives": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "Przejrzyj dokument", "app.containers.Projects.invisibleTitlePhaseAbout": "O tym etapie", "app.containers.Projects.invisibleTitlePoll": "<PERSON><PERSON> udział w badaniu", "app.containers.Projects.invisibleTitleSurvey": "<PERSON>ź udział w ankiecie", "app.containers.Projects.issues": "Potrzeby", "app.containers.Projects.liveDataMessage": "Przeglądasz dane w czasie rzeczywistym. Liczby uczestników są stale aktualizowane dla administratorów. <PERSON><PERSON><PERSON><PERSON><PERSON>, że zwykli użytkownicy widzą dane z pamięci podręcznej, co może skutkować niewielkimi różnicami w liczbach.", "app.containers.Projects.location": "Lokalizacja:", "app.containers.Projects.manageBasket": "Zarządzaj koszykiem", "app.containers.Projects.meetMinBudgetRequirement": "Spełnij minimalny budżet, aby przes<PERSON>ć swój koszyk.", "app.containers.Projects.meetMinSelectionRequirement": "<PERSON><PERSON><PERSON><PERSON>, aby wys<PERSON><PERSON> swój koszyk.", "app.containers.Projects.metaTitle1": "Projekt: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "<PERSON><PERSON><PERSON><PERSON> jest <PERSON>ny bud<PERSON>", "app.containers.Projects.myBasket": "Basket", "app.containers.Projects.navPoll": "<PERSON><PERSON><PERSON>", "app.containers.Projects.navSurvey": "Ankieta", "app.containers.Projects.newPhase": "Nowa faza", "app.containers.Projects.nextPhase": "Następny etap", "app.containers.Projects.noEndDate": "<PERSON>rak daty końcowej", "app.containers.Projects.noItems": "Nie wybrałeś jeszcze żadnych elementów", "app.containers.Projects.noPastEvents": "Brak minionych wydarzeń do wyświetlenia", "app.containers.Projects.noPhaseSelected": "<PERSON><PERSON> wybrano żadnego etapu", "app.containers.Projects.noUpcomingOrOngoingEvents": "W chwili obecnej nie są planowane żadne nadchodzące lub trwające wydarzenia.", "app.containers.Projects.offlineVotersTooltip": "Liczba ta nie odzwierciedla liczby wyborców offline.", "app.containers.Projects.options": "<PERSON><PERSON><PERSON>", "app.containers.Projects.participants": "Uczestnicy", "app.containers.Projects.participantsTooltip4": "Liczba ta odzwierciedla również anonimowe ankiety. Anonimowe przesyłanie ankiet jest możliwe, jeśli ankiety są otwarte dla wszystkich (zobacz zak<PERSON>d<PERSON> {accessRightsLink} dla tego projektu).", "app.containers.Projects.pastEvents": "Minione wydarzenia", "app.containers.Projects.petitions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.phases": "Etapy", "app.containers.Projects.previousPhase": "Poprzedni etap", "app.containers.Projects.project": "Projekt", "app.containers.Projects.projectTwitterMessage": "Niech twój głos zostanie usłyszany! Weź udział w {projectName} | {orgName}", "app.containers.Projects.projects": "Projekty", "app.containers.Projects.proposals": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.questions": "Pytania", "app.containers.Projects.readLess": "Czytaj mniej", "app.containers.Projects.readMore": "Zobacz więcej", "app.containers.Projects.removeItem": "Usuń element", "app.containers.Projects.requiredSelection": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>bór", "app.containers.Projects.reviewDocument": "Przejrzyj dokument", "app.containers.Projects.seeTheContributions": "Zobacz wkład", "app.containers.Projects.seeTheIdeas": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheInitiatives": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheIssues": "<PERSON><PERSON><PERSON><PERSON> sprawy", "app.containers.Projects.seeTheOptions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeThePetitions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheProjects": "Zobacz proje<PERSON>y", "app.containers.Projects.seeTheProposals": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.Projects.seeTheQuestions": "<PERSON>ob<PERSON>z <PERSON>", "app.containers.Projects.seeUpcomingEvents": "Zobacz nadchodzące wydarzenia", "app.containers.Projects.share": "Podziel się", "app.containers.Projects.shareThisProject": "Podziel się tym projektem", "app.containers.Projects.submitMyBasket": "Submit basket", "app.containers.Projects.survey": "Ankieta", "app.containers.Projects.takeThePoll": "<PERSON>ź udział w sondażu", "app.containers.Projects.takeTheSurvey": "<PERSON>ź udział w ankiecie", "app.containers.Projects.timeline": "<PERSON><PERSON>", "app.containers.Projects.upcomingAndOngoingEvents": "Nadchodzące i trwające wydarzenia", "app.containers.Projects.upcomingEvents": "Nadchodzące wydarzenia", "app.containers.Projects.whatsAppMessage": "{projectName} | z platformy partycypacyjnej {orgName}", "app.containers.Projects.yourBudget": "Total budget", "app.containers.ProjectsIndexPage.metaDescription": "Poznaj wszystkie trwające projekty {orgName}, a<PERSON>, jak moż<PERSON>z w nich uczestniczyć.\n Przyjdź i omów lokalne projekty, które są dla Ciebie najważniejsze.", "app.containers.ProjectsIndexPage.metaTitle1": "Projekty | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "Projekty", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "Chcę się zgłosić na wolontariusza", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "<PERSON><PERSON><PERSON> najpierw {signInLink} lub {signUpLink}, aby zg<PERSON><PERSON><PERSON> się jako wolontariusz do tego działania", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "Udział w tej aktywności nie jest obecnie otwarty.", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "z<PERSON>uj się", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "zarejestruj się", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "Wycofuję moją ofertę na wolontariusza", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {brak wolontariuszy} one {# wolontariusz} few {# wolontariuszy} many {# wolontariuszy} other {# wolontariuszy}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "Ostrzeżenie: Wbudowana ankieta może mieć problemy z dostępnością dla użytkowników czytników ekranu. <PERSON><PERSON><PERSON> napot<PERSON>z jakiekolwi<PERSON> t<PERSON>, skontaktuj się z administratorem platformy, aby o<PERSON><PERSON><PERSON><PERSON> link do ankiety z oryginalnej platformy. Moż<PERSON><PERSON> też pop<PERSON>ić o inne sposoby wypełnienia ankiety.", "app.containers.ProjectsShowPage.process.survey.survey": "Ankieta", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "<PERSON><PERSON> do<PERSON>, czy moż<PERSON><PERSON> wziąć ud<PERSON>ł w tej ankiecie, proszę najpierw {logInLink} do platformy.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "Ta ankieta może być przeprowadzone tylko wtedy, gdy ten etap na osi czasu jest aktywny.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "Prosimy o {completeRegistrationLink} , aby wzi<PERSON><PERSON> udział w ankiecie.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "<PERSON><PERSON><PERSON>, nie masz uprawnień do wzięcia udziału w tej ankiecie.", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "Wzięcie udziału w tej ankiecie wymaga weryfikacji tożsamości. {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "Ankieta nie jest już <PERSON>, ponieważ projekt ten nie jest już aktywny.", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "zakończ rejestrację", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "z<PERSON>uj się", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "zarejestruj się", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "Zweryfikuj swoje konto teraz.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "Tylko niektórzy użytkownicy mogą przeglądać ten dokument. Najpierw odwiedź stronę {signUpLink} lub {logInLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "Ten dokument może by<PERSON> przeglądany tylko wtedy, gdy ta faza jest aktywna.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "Przejrzyj dokument na stronie {completeRegistrationLink} .", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "<PERSON><PERSON><PERSON>, nie masz uprawnień do przeglądania tego dokumentu.", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "Zapoznanie się z tym dokumentem wymaga weryfikacji Twojego konta. {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "Dokument nie jest już <PERSON>, ponieważ projekt ten nie jest już aktywny.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(w tym 1 offline)} other {(w tym # offline)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 pick} other {# picks}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "Odsetek uczestników, którzy wybrali tę opcję.", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "Pro<PERSON><PERSON>witej liczby głosów otrzymanych przez tę opcję.", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "Koszt:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "Pokaż więcej", "app.containers.ReactionControl.a11y_likesDislikes": "Łączna liczba polubień: {likesCount}, łączna liczba nielubianych: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "Pomyślnie anulowałeś niechęć do tego wejścia.", "app.containers.ReactionControl.cancelLikeSuccess": "Pomyślnie anulowałeś polubienie tego wejścia.", "app.containers.ReactionControl.dislikeSuccess": "Nie podobało Ci się to wejście.", "app.containers.ReactionControl.likeSuccess": "Spodobało Ci się to wejście.", "app.containers.ReactionControl.reactionErrorSubTitle": "Z powodu błędu Twoja reakcja nie mogła zostać zarejestrowana. Spróbuj ponownie za kilka minut.", "app.containers.ReactionControl.reactionSuccessTitle": "Twoja reakcja została pomyślnie zarejestrowana!", "app.containers.ReactionControl.vote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.ReactionControl.voted": "Zagłosowano", "app.containers.SearchInput.a11y_cancelledPostingComment": "<PERSON><PERSON><PERSON><PERSON> publikację komentarza.", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} komentarze zostały załadowane.", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# zdarzenia zostały załadowane} one {# zdarzenie zostało załadowane} other {# zdarzenia zostały załadowane}}.", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# wyniki zostały załadowane} one {# wyniki zostały załadowane} other {# wyniki zostały załadowane}}.", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# wyniki wyszukiwania zostały załadowane} one {# wyniki wyszukiwania zostały załadowane} other {# wyniki wyszukiwania zostały załadowane}}.", "app.containers.SearchInput.removeSearchTerm": "<PERSON><PERSON><PERSON> szukany termin", "app.containers.SearchInput.searchAriaLabel": "Szukaj", "app.containers.SearchInput.searchLabel": "Szukaj", "app.containers.SearchInput.searchPlaceholder": "Szukaj", "app.containers.SearchInput.searchTerm": "Znajdź termin: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect to rozwiązanie zaproponowane przez państwo francuskie w celu zabezpieczenia i uproszczenia rejestracji do ponad 700 usług online.", "app.containers.SignIn.or": "Albo", "app.containers.SignIn.signInError": "Podane informacje nie są prawidłowe. Kliknij \"Zapomniałem hasła\", aby zmieni<PERSON> swoje hasło.", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "Użyj FranceConnect, aby <PERSON><PERSON>, z<PERSON><PERSON><PERSON><PERSON><PERSON> lub zweryfikować swoje konto.", "app.containers.SignIn.whatIsFranceConnect": "Co to jest France Connect?", "app.containers.SignUp.adminOptions2": "Dla administratorów i kierowników projektów", "app.containers.SignUp.backToSignUpOptions": "Wróć do opcji rejestracji", "app.containers.SignUp.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.SignUp.emailConsent": "Zaznacz tutaj, a<PERSON>, <PERSON><PERSON> zgadzasz się na otrzymywanie wiadomości e-mail z tej platformy. <PERSON><PERSON><PERSON><PERSON>, które wiadomości e-mail chcesz otrzymywać w ustawieniach użytkownika.", "app.containers.SignUp.emptyFirstNameError": "Wprowadź swoje imię", "app.containers.SignUp.emptyLastNameError": "Wprowadź swoje nazwisko", "app.containers.SignUp.firstNamesLabel": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.goToLogIn": "<PERSON><PERSON> już konto? {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "Przeczytałem i akceptuję {link}.", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "Przeczytałem i akceptuję {link}.", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że dane będą wykorzystywane na stronie mitgestalten.wien.gv.at. Więcej informacji można znaleźć na stronie {link}.", "app.containers.SignUp.invitationErrorText": "Twoje zaproszenie wygasło lub zostało już wykorzystane. Jeśli użyłeś już linku zaproszenia do utworzenia konta, spróbuj się zalogować. W przeciwnym razie zarejestruj się, aby utworzyć nowe konto.", "app.containers.SignUp.lastNameLabel": "Nazwisko", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "Śledź swoje obszary zainteresowania, aby o<PERSON><PERSON><PERSON><PERSON><PERSON> o nich powiadomienia:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "Śledź swoje ulubione tematy, aby <PERSON><PERSON><PERSON><PERSON><PERSON> o nich powiadomienia:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "Zap<PERSON>z <PERSON>", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "Pomiń na razie", "app.containers.SignUp.privacyPolicyNotAcceptedError": "Zaakceptuj naszą politykę p<PERSON>, a<PERSON> k<PERSON>", "app.containers.SignUp.signUp2": "Zarejestruj się", "app.containers.SignUp.skip": "Pomiń ten krok", "app.containers.SignUp.tacError": "Akceptacja naszych warunków jest konieczna, a<PERSON> k<PERSON><PERSON>", "app.containers.SignUp.thePrivacyPolicy": "z Polityką prywatności", "app.containers.SignUp.theTermsAndConditions": "z Regulaminem", "app.containers.SignUp.unknownError": "Coś poszło nie tak. Proszę spróbuj ponownie później.", "app.containers.SignUp.viennaConsentEmail": "Adres e-mail", "app.containers.SignUp.viennaConsentFirstName": "<PERSON><PERSON><PERSON>", "app.containers.SignUp.viennaConsentFooter": "Po zalogowaniu możesz zmienić informacje o swoim profilu. <PERSON><PERSON><PERSON> masz już konto z tym samym adresem e-mail na mitgestalten.wien.gv.at, zostanie ono połączone z Twoim obecnym kontem.", "app.containers.SignUp.viennaConsentHeader": "Następujące dane zostaną przesłane:", "app.containers.SignUp.viennaConsentLastName": "Nazwisko", "app.containers.SignUp.viennaConsentUserName": "Nazwa użytkownika", "app.containers.SignUp.viennaDataProtection": "polityka prywatności vienna", "app.containers.SiteMap.contributions": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.SiteMap.cookiePolicyLinkTitle": "Cookies", "app.containers.SiteMap.issues": "Potrzeby", "app.containers.SiteMap.options": "<PERSON><PERSON><PERSON>", "app.containers.SiteMap.projects": "Projekty", "app.containers.SiteMap.questions": "Pytania", "app.containers.SpamReport.buttonSave": "<PERSON><PERSON>", "app.containers.SpamReport.buttonSuccess": "Sukces", "app.containers.SpamReport.inappropriate": "To jest niestosowne lub obraźliwe", "app.containers.SpamReport.messageError": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd przy przesyłaniu formularza, proszę spróbuj ponownie później.", "app.containers.SpamReport.messageSuccess": "Twój raport został wysłany", "app.containers.SpamReport.other": "<PERSON><PERSON> powód", "app.containers.SpamReport.otherReasonPlaceholder": "Opis", "app.containers.SpamReport.wrong_content": "To jest bez związku", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "Usuń zdjęcie profilowe", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "Twoje głosy oddane na propozycje, na które nadal można głosować, zostaną usunięte. Głosy oddane na propozycje, w przypadku których okres głosowania został zamknięty, nie zostaną usunięte.", "app.containers.UsersEditPage.addPassword": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "Udział w projektach dla zweryfikowanych obywateli.", "app.containers.UsersEditPage.becomeVerifiedTitle": "Zweryfikuj swoją to<PERSON><PERSON>", "app.containers.UsersEditPage.bio": "O tobie", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "<PERSON><PERSON> mo<PERSON><PERSON><PERSON> tego pola, ponieważ zawiera ono zweryfikowane informacje.", "app.containers.UsersEditPage.buttonSuccessLabel": "Sukces", "app.containers.UsersEditPage.cancel": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.changeEmail": "Zmień e-mail", "app.containers.UsersEditPage.changePassword2": "<PERSON><PERSON><PERSON> hasła", "app.containers.UsersEditPage.clickHereToUpdateVerification": "<PERSON><PERSON><PERSON> k<PERSON> tutaj, aby zaktuali<PERSON><PERSON> swoją weryfikację.", "app.containers.UsersEditPage.conditionsLinkText": "nasze warunki", "app.containers.UsersEditPage.contactUs": "Inny powód opuszczenia platformy? {feedbackLink}, może będziemy mogli pomóc.", "app.containers.UsersEditPage.deleteAccountSubtext": "Prz<PERSON>ro nam, że nas opuszczasz.", "app.containers.UsersEditPage.deleteMyAccount": "Us<PERSON>ń moje konto", "app.containers.UsersEditPage.deleteYourAccount": "Usuń swoje konto", "app.containers.UsersEditPage.deletionSection": "Usuń swoje konto", "app.containers.UsersEditPage.deletionSubtitle": "Ta akcja nie może zostać cofnięta. Treści opublikowane na platformie będą anonimizowane. Jeśli chcesz usunąć wszystkie swoje treści, moż<PERSON><PERSON> skontaktować się z nami <NAME_EMAIL>.", "app.containers.UsersEditPage.email": "E-mail", "app.containers.UsersEditPage.emailEmptyError": "Podaj adres e-mail", "app.containers.UsersEditPage.emailInvalidError": "Podaj adres e-mail w prawidłowym formacie, np. <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "<PERSON><PERSON> nam z<PERSON>", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.firstNamesEmptyError": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.h1": "Informacje o Twoim koncie", "app.containers.UsersEditPage.h1sub": "Edytuj informacje o koncie", "app.containers.UsersEditPage.image": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.imageDropzonePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, a<PERSON> w<PERSON><PERSON><PERSON> zd<PERSON><PERSON><PERSON> profilowe (maks. 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "Wszystkie ustawienia dla Twojego profilu", "app.containers.UsersEditPage.language": "Język", "app.containers.UsersEditPage.lastName": "Nazwisko", "app.containers.UsersEditPage.lastNameEmptyError": "Podaj nazwiska", "app.containers.UsersEditPage.loading": "Ładowanie...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "Tutaj możesz zmienić swój e-mail lub hasło.", "app.containers.UsersEditPage.loginCredentialsTitle": "<PERSON>", "app.containers.UsersEditPage.messageError": "<PERSON>e by<PERSON> w stanie zapisać <PERSON>ich zmian. Spróbuj ponownie później lub skontaktuj się z <EMAIL>.", "app.containers.UsersEditPage.messageSuccess": "Twój profil został zapisany.", "app.containers.UsersEditPage.metaDescription": "Jest to strona ustawień profilu {firstName} {lastName} na platformie uczestnictwa online {tenantName}. Tutaj możesz zweryfikować swoją to<PERSON><PERSON>, edytować informacje o swoim koncie, usunąć swoje konto i edytować preferencje dotyczące poczty elektronicznej.", "app.containers.UsersEditPage.metaTitle1": "Strona ustawień profilu {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "Po kliknięciu tego przycisku nie będziemy mieli możliwości przywrócenia Twojego konta.", "app.containers.UsersEditPage.noNameWarning2": "<PERSON>je imię jest obecnie wyświetlane na platformie jako: \"{displayName}\", ponieważ nie wprowadziłeś swojego imienia. Jest to nazwa wygenerowana automatycznie. <PERSON><PERSON><PERSON> ch<PERSON> je <PERSON>, wpisz je poniżej.", "app.containers.UsersEditPage.notificationsSubTitle": "<PERSON><PERSON><PERSON> powiadomienia e-mail ch<PERSON>z otrzymywać? ", "app.containers.UsersEditPage.notificationsTitle": "Powiadomienia pocztą elektroniczną", "app.containers.UsersEditPage.password": "Wybierz nowe hasło", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "<PERSON><PERSON><PERSON>, które ma co najmniej {minimumPasswordLength} znaków", "app.containers.UsersEditPage.passwordAddSection": "<PERSON><PERSON><PERSON>", "app.containers.UsersEditPage.passwordAddSubtitle2": "Ustaw hasło i łatwo zaloguj się do platformy, bez konieczności każdorazowego potwierdzania swojego maila.", "app.containers.UsersEditPage.passwordChangeSection": "Zmień swoje hasło", "app.containers.UsersEditPage.passwordChangeSubtitle": "Potwierdź swoje obecne hasło i zmień je na nowe.", "app.containers.UsersEditPage.privacyReasons": "<PERSON><PERSON><PERSON> martwisz się o swoją p<PERSON>, mo<PERSON><PERSON><PERSON> {conditionsLink}.", "app.containers.UsersEditPage.processing": "Wysyłanie...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "<PERSON><PERSON><PERSON> jest wymagane przy podawaniu nazwiska", "app.containers.UsersEditPage.reasonsToStayListTitle": "Zanim opuścisz...", "app.containers.UsersEditPage.submit": "Zapisz zmiany", "app.containers.UsersEditPage.tooManyEmails": "Otrzymujesz za dużo e-maili? Możesz zarządzać swoimi preferencjami dotyczącymi wiadomości e-mail w ustawieniach swojego profilu.", "app.containers.UsersEditPage.updateverification": "<PERSON><PERSON> zmieniły się Twoje informacje oficjalne? {reverifyButton}", "app.containers.UsersEditPage.user": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> wysłali ci e-maila, <PERSON><PERSON><PERSON> ci<PERSON> pow<PERSON>?", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "Możesz uczestniczyć w projektach, które są dostępne tylko dla zweryfikowanych obywateli.", "app.containers.UsersEditPage.verifiedIdentityTitle": "Konto zweryfikowane", "app.containers.UsersEditPage.verifyNow": "Zweryfikuj teraz", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "Pobierz swoje odpowiedzi (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {brak polubień} one {1 polubienie} other {# likes}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "Post, w którym został zamieszczony ten komentarz:", "app.containers.UsersShowPage.areas": "O<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.commentsWithCount": "Komentarze ({commentsCount})", "app.containers.UsersShowPage.editProfile": "<PERSON><PERSON><PERSON><PERSON> mój profil", "app.containers.UsersShowPage.emptyInfoText": "Nie podążasz za żadnymi elementami określonego powyżej filtra.", "app.containers.UsersShowPage.eventsWithCount": "Wydarzenia ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "Following ({followingCount})", "app.containers.UsersShowPage.inputs": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.invisibleTitlePostsList": "Wszystkie posty zgłoszone przez tego uczestnika", "app.containers.UsersShowPage.invisibleTitleUserComments": "Wszystkie komentarze zamieszczone przez tego użytkownika", "app.containers.UsersShowPage.loadMore": "Załaduj więcej", "app.containers.UsersShowPage.loadMoreComments": "Załaduj więcej komentarzy", "app.containers.UsersShowPage.loadingComments": "Ładowanie komentarzy...", "app.containers.UsersShowPage.loadingEvents": "Ładowanie zdarzeń...", "app.containers.UsersShowPage.memberSince": "Członek od {date}", "app.containers.UsersShowPage.metaTitle1": "Strona profilu {firstName} {lastName} | {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "Ta osoba nie zamieściła jeszcze żadnych komentarzy.", "app.containers.UsersShowPage.noCommentsForYou": "Nie ma tu jeszcze żadnych komentarzy.", "app.containers.UsersShowPage.noEventsForUser": "<PERSON>e uczestniczyłeś jeszcze w żadnych wydarzeniach.", "app.containers.UsersShowPage.postsWithCount": "Posty ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "Foldery projektów", "app.containers.UsersShowPage.projects": "Projekty", "app.containers.UsersShowPage.proposals": "Pro<PERSON><PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.seePost": "Zobacz post", "app.containers.UsersShowPage.surveyResponses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ({responses})", "app.containers.UsersShowPage.topics": "<PERSON><PERSON><PERSON>", "app.containers.UsersShowPage.tryAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sz<PERSON> spróbuj ponownie później.", "app.containers.UsersShowPage.userShowPageMetaDescription": "Jest to strona profi<PERSON> {firstName} {lastName} na platformie uczestnictwa {orgName}. Poniżej znajduje się podgląd jej postów.", "app.containers.VoteControl.close": "Zamknij", "app.containers.VoteControl.voteErrorTitle": "Coś poszło nie tak", "app.containers.admin.ContentBuilder.default": "domyślny", "app.containers.admin.ContentBuilder.imageTextCards": "Karty graficzne i tekstowe", "app.containers.admin.ContentBuilder.infoWithAccordions": "Informacje i akordeony", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 kolumna", "app.containers.admin.ContentBuilder.projectDescription": "Opis projektu", "app.containers.app.navbar.admin": "Admin", "app.containers.app.navbar.allProjects": "Wszystkie projekty", "app.containers.app.navbar.ariaLabel": "Strona główna", "app.containers.app.navbar.closeMobileNavMenu": "Zamknij mobilne menu nawigacyjne", "app.containers.app.navbar.editProfile": "Ustawienia", "app.containers.app.navbar.fullMobileNavigation": "Pełna mobilność", "app.containers.app.navbar.logIn": "<PERSON><PERSON><PERSON><PERSON>", "app.containers.app.navbar.logoImgAltText": "Głowna {orgName}", "app.containers.app.navbar.myProfile": "<PERSON><PERSON><PERSON> profil", "app.containers.app.navbar.search": "Szukaj", "app.containers.app.navbar.showFullMenu": "Pokaż pełne menu", "app.containers.app.navbar.signOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "app.containers.eventspage.errorWhenFetchingEvents": "Wystą<PERSON>ł błąd podczas ładowania wydarzeń. Proszę spróbować ponownie załadować stronę.", "app.containers.eventspage.events": "Wyd<PERSON>zen<PERSON>", "app.containers.eventspage.eventsPageDescription": "Pokaż wszystkie wydarzenia opublikowane na platformie {orgName}'s.", "app.containers.eventspage.eventsPageTitle1": "Wydarzenia | {orgName}", "app.containers.eventspage.filterDropdownTitle": "Projekty", "app.containers.eventspage.noPastEvents": "Brak minionych wydarzeń do wyświetlenia", "app.containers.eventspage.noUpcomingOrOngoingEvents": "W chwili obecnej nie są planowane żadne nadchodzące lub trwające wydarzenia.", "app.containers.eventspage.pastEvents": "Minione wydarzenia", "app.containers.eventspage.upcomingAndOngoingEvents": "Nadchodzące i trwające wydarzenia", "app.containers.footer.accessibility-statement": "Oświadczenie o dostępności", "app.containers.footer.ariaLabel": "<PERSON><PERSON> strona", "app.containers.footer.cookie-policy": "Polityka ciasteczek", "app.containers.footer.cookieSettings": "Ustawienia ciasteczek", "app.containers.footer.feedbackEmptyError": "Pole informacji zwrotnej nie może być puste.", "app.containers.footer.poweredBy": "Wspierani przez", "app.containers.footer.privacy-policy": "Polityka p<PERSON>watności", "app.containers.footer.siteMap": "Mapa strony", "app.containers.footer.terms-and-conditions": "<PERSON><PERSON><PERSON>", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "<PERSON><PERSON><PERSON>", "app.containers.ideaHeading.confirmLeaveFormButtonText": "Tak, chcę odejść", "app.containers.ideaHeading.editForm": "<PERSON><PERSON><PERSON><PERSON>rz", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "<PERSON><PERSON><PERSON>, że ch<PERSON> ode<PERSON>?", "app.containers.ideaHeading.leaveIdeaForm": "Zostaw formularz pomysłu", "app.containers.ideaHeading.leaveIdeaText": "Twoje odpowiedzi nie zostaną zapisane.", "app.containers.landing.cityProjects": "Projekty", "app.containers.landing.completeProfile": "Uzupełnij swój profil", "app.containers.landing.completeYourProfile": "<PERSON><PERSON><PERSON>, {firstName}. <PERSON><PERSON><PERSON>, uzupełnij swój profil.", "app.containers.landing.createAccount": "Zarejestruj się", "app.containers.landing.defaultSignedInMessage": "{orgName} czeka na Twoje pomysły!", "app.containers.landing.doItLater": "Zrobię to później", "app.containers.landing.new": "nowy", "app.containers.landing.subtitleCity": "{orgName} wita na oficjalnej platformie partycypacji", "app.containers.landing.titleCity": "Tworzymy razem {orgName} prz<PERSON><PERSON>ł<PERSON><PERSON><PERSON>", "app.containers.landing.twitterMessage": "Gł<PERSON><PERSON><PERSON> na {ideaTitle} na", "app.containers.landing.upcomingEventsWidgetTitle": "Nadchodzące i trwające wydarzenia", "app.containers.landing.userDeletedSubtitle": "Możesz założyć nowe konto w dowolnym momencie lub {contactLink} aby dać nam znać co moż<PERSON>y poprawić.", "app.containers.landing.userDeletedSubtitleLinkText": "napisz do nas kilka słów", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "Twoje konto zostało usunięte.", "app.containers.landing.userDeletionFailed": "Wystą<PERSON>ł błąd przy usuwaniu Twojego konta, zostaliśmy powiadomieni o problemie i dołożymy wszelkich starań, aby go <PERSON><PERSON><PERSON>. Proszę spróbuj ponownie później.", "app.containers.landing.verifyNow": "Zweryfikuj teraz", "app.containers.landing.verifyYourIdentity": "Zweryfikuj swoją to<PERSON><PERSON>", "app.containers.landing.viewAllEventsText": "Zobacz wszystkie wydarzenia", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "Powrót do folderu", "app.errors.after_end_at": "Data rozpoczęcia po dacie zakończenia", "app.errors.avatar_carrierwave_download_error": "<PERSON>e mogłem pobrać pliku avatara (zdjęcia profilowego).", "app.errors.avatar_carrierwave_integrity_error": "<PERSON><PERSON> a<PERSON> (zdj. profilowego) ma niedozwolony typ.", "app.errors.avatar_carrierwave_processing_error": "Nie można przetworzyć avatara.", "app.errors.avatar_extension_blacklist_error": "Rozszerzenie pliku obrazu awatara nie jest dozwolone. Dozwolone rozszerzenia to: jpg, jpeg, gif i png.", "app.errors.avatar_extension_whitelist_error": "Rozszerzenie pliku obrazu awatara nie jest dozwolone. Dozwolone rozszerzenia to: jpg, jpeg, gif i png.", "app.errors.banner_cta_button_multiloc_blank": "Wprowadź tekst przycisku.", "app.errors.banner_cta_button_url_blank": "Podaj link.", "app.errors.banner_cta_button_url_url": "Wprowadź prawidłowy link. Upewnij się, że link zaczyna się od 'https://'.", "app.errors.banner_cta_signed_in_text_multiloc_blank": "Wprowadź tekst przycisku.", "app.errors.banner_cta_signed_in_url_blank": "Podaj link.", "app.errors.banner_cta_signed_in_url_url": "Wprowadź prawidłowy link. Upewnij się, że link zaczyna się od 'https://'.", "app.errors.banner_cta_signed_out_text_multiloc_blank": "Wprowadź tekst przycisku.", "app.errors.banner_cta_signed_out_url_blank": "Podaj link.", "app.errors.banner_cta_signed_out_url_url": "Wprowadź prawidłowy link. Upewnij się, że link zaczyna się od 'https://'.", "app.errors.base_includes_banned_words": "<PERSON><PERSON> może użyłeś jednego lub więcej słów uznawanych za wulgarne. Zmień swój tekst, aby us<PERSON><PERSON> wszelkie wulgaryzmy, które mogą się w nim znajdować.", "app.errors.body_multiloc_includes_banned_words": "Opis zawiera słowa uznane za nieodpowiednie.", "app.errors.bulk_import_idea_not_valid": "Wynikający z tego pomysł nie jest poprawny: {value}.", "app.errors.bulk_import_image_url_not_valid": "Nie można pobrać żadnego obrazu z {value}. <PERSON><PERSON><PERSON><PERSON>, że adres URL jest prawidłowy i kończy się rozszerzeniem pliku, takim jak .png lub .jpg. Problem ten występuje w wierszu o identyfikatorze {row}.", "app.errors.bulk_import_location_point_blank_coordinate": "Lokalizacja pomysłu z brakującą współrzędną w {value}. Problem ten występuje w wierszu o identyfikatorze {row}.", "app.errors.bulk_import_location_point_non_numeric_coordinate": "Lokalizacja pomysłu z nieliczbową współrzędną w {value}. Problem ten występuje w wierszu o ID {row}.", "app.errors.bulk_import_malformed_pdf": "Przesłany plik PDF wydaje się być zniekształcony. Spróbuj ponownie wyeksportować plik PDF ze źródła, a następnie prześlij go ponownie.", "app.errors.bulk_import_maximum_ideas_exceeded": "Maksymalna liczba pomysłów {value} została przekroczona.", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "Przekroczono maksymalną liczbę stron {value} w pliku PDF.", "app.errors.bulk_import_not_enough_pdf_pages": "Przesłany plik PDF nie ma wystarczającej liczby stron - powinien mieć co najmniej tyle samo stron, co pobrany szablon.", "app.errors.bulk_import_publication_date_invalid_format": "Pomysł z nieprawidłowym formatem daty publikacji \"{value}\". Użyj formatu \"DD-MM-RRRR\".", "app.errors.cannot_contain_ideas": "Ta faza zawiera {ideasCount, plural, one {jeden pomysł} few {{ideasCount} pomysły} many {{ideasCount} pomysłów} other {{ideasCount} pomysły}} a metoda uczestnictwa, którą pró<PERSON><PERSON><PERSON>, nie wspiera pomysłów. <PERSON><PERSON><PERSON> usunąć {ideasCount, plural, one {pomysł} few {pomysły} many {pomysłów} other {pomysły}} z etapu i spróbować ponownie.", "app.errors.cant_change_after_first_response": "Nie możesz już tego z<PERSON>, ponieważ niektórzy użytkownicy na to zareagowali", "app.errors.category_name_taken": "Kategoria o tej nazwie już istnieje", "app.errors.confirmation_code_expired": "Kod wygasł. Prosimy o ponowne przesłanie nowego kodu.", "app.errors.confirmation_code_invalid": "Nieprawidłowy kod potwierdzenia. Proszę sprawdź pocztę w celu uzyskania poprawnego kodu lub spróbuj opcji 'Wyślij nowy kod'", "app.errors.confirmation_code_too_many_resets": "Kod potwierdzający został wysłany zbyt wiele razy. Skontaktuj się z nami, aby otrzymać w zamian kod zaproszenia.", "app.errors.confirmation_code_too_many_retries": "Próbowałeś zbyt wiele razy. Wyślij ponownie kod lub spróbuj zmienić swój adres e-mail.", "app.errors.email_already_active": "Adres e-mail {value} znajdujący się w wierszu {row} należy do zarejestrowanego już użytkownika", "app.errors.email_already_invited": "Wysłano już zaproszenia na adres e-mail {value} znajdujący się w rzędzie {row}", "app.errors.email_blank": "To pole nie może by<PERSON> puste", "app.errors.email_domain_blacklisted": "Proszę użyć innej domeny e-mail do rejestracji.", "app.errors.email_invalid": "Proszę użyć poprawnego adresu e-mail.", "app.errors.email_taken": "Konto z tym emailem już istnieje. Zamiast tego można się zalogować.", "app.errors.email_taken_by_invite": "{value} jest już zajęte przez oczekujące zaproszenie. Sprawdź swój folder spamu lub skontaktuj się z <EMAIL>, jeśli nie możesz go znaleźć.", "app.errors.emails_duplicate": "W następnym wierszu(-ach): {rows} zostały znalezione jedna lub więcej zduplikowanych wartości dla adresu e-mail {value}", "app.errors.extension_whitelist_error": "Format pliku, kt<PERSON>ry pró<PERSON><PERSON>sz <PERSON>, nie jest obsługiwany.", "app.errors.file_extension_whitelist_error": "Format pliku, kt<PERSON><PERSON> próbowałeś prz<PERSON>łać, nie jest obsługiwany.", "app.errors.first_name_blank": "To pole nie może by<PERSON> puste", "app.errors.generics.blank": "To pole nie może by<PERSON> puste.", "app.errors.generics.invalid": "To nie wygląda na prawidłową wartość", "app.errors.generics.taken": "Ten e-mail już istnieje. Inne konto jest z nim powiązane.", "app.errors.generics.unsupported_locales": "To pole nie obsługuje tej lokalizacji.", "app.errors.group_ids_unauthorized_choice_moderator": "Jako kierownik projektu, m<PERSON><PERSON><PERSON><PERSON> w<PERSON>ć e-maile tylko do osób, które mają dostęp do Twojego projektu(ów)", "app.errors.has_other_overlapping_phases": "Projekty nie mogą mieć nakładających się na siebie etapów.", "app.errors.invalid_email": "Email {value} znajdujący się w wierszu {row} nie jest prawidłowym adresem e-mail", "app.errors.invalid_row": "Wystą<PERSON>ł nieznany błąd podczas próby przetworzenia wiersza {row}", "app.errors.is_not_timeline_project": "Obecny projekt nie wspiera etapów.", "app.errors.key_invalid": "Klucz może zawierać tylko litery, cyfry i podkreślenia(_)", "app.errors.last_name_blank": "To pole nie może by<PERSON> puste", "app.errors.locale_blank": "Prosz<PERSON> wybrać język", "app.errors.locale_inclusion": "Proszę wybrać wspierany język", "app.errors.malformed_admin_value": "<PERSON><PERSON>ść admina {value} znajdująca się w wierszu {row} jest niew<PERSON>żna", "app.errors.malformed_groups_value": "Grupa {value} znajdująca się w rzędzie {row} nie jest poprawną grupą", "app.errors.max_invites_limit_exceeded1": "Liczba zaproszeń przekracza limit 1000.", "app.errors.maximum_attendees_greater_than1": "Maksymalna liczba zarejestrowanych musi być większa niż 0.", "app.errors.maximum_attendees_greater_than_attendees_count1": "Maksymalna liczba zarejestrowanych musi być większa lub równa bieżącej liczbie zarejestrowanych.", "app.errors.no_invites_specified": "Nie znaleziono żadnego adresu e-mail.", "app.errors.no_recipients": "Kampania nie może zosta<PERSON> wysłana, poni<PERSON><PERSON>ż nie ma odbiorców. Grupa, do której wysyłasz w<PERSON>, jest albo pusta, albo nikt nie wyraził zgody na otrzymywanie wiadomości e-mail.", "app.errors.number_invalid": "Wprowadź prawidłowy numer.", "app.errors.password_blank": "To pole nie może by<PERSON> puste", "app.errors.password_invalid": "Proszę sprawdzić ponownie swoje aktualne hasło.", "app.errors.password_too_short": "Hasło musi mieć długość co najmniej 8 znaków", "app.errors.resending_code_failed": "Coś poszło nie tak podczas wysyłania kodu potwierdzającego.", "app.errors.slug_taken": "Ten adres URL projektu już istnieje. Proszę zmienić URL.", "app.errors.tag_name_taken": "Tag o tej nazwie już istnieje", "app.errors.title_multiloc_blank": "Tytuł nie może być pusty.", "app.errors.title_multiloc_includes_banned_words": "Tytuł zawiera słowa, które są uważane za nieodpowiednie.", "app.errors.token_invalid": "Linki do resetowania hasła mogą być użyte tylko raz i są ważne przez jedną godzinę po wysłaniu. {passwordResetLink}.", "app.errors.too_common": "Hasło jest zbyt łatwe do odgadnięcia. Wybierz mocniejsze hasło.", "app.errors.too_long": "Hasło musi mieć co najwyżej 72 znaki", "app.errors.too_short": "Hasło musi mieć długość co najmniej 8 znaków", "app.errors.uncaught_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł nieznany błąd.", "app.errors.unknown_group": "Grupa {value} znajdująca się w rzędzie {row} nie jest znaną grupą", "app.errors.unknown_locale": "Ję<PERSON>k {value} znajdujący się w wierszu {row} nie jest skonfigurowany", "app.errors.unparseable_excel": "Wybrany plik Excela nie mógł zostać przetworzony.", "app.errors.url": "Wprowadź prawidłowy link. Upewnij się, że link zaczyna się od https://", "app.errors.verification_taken": "Weryfikacja nie może zostać zakończona, ponieważ inne konto zostało zweryfikowane przy użyciu tych samych danych.", "app.errors.view_name_taken": "Widok o tej nazwie już istnieje", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "Nieodpowiednie treści zostały automatycznie wykryte w poście lub komentarzu", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "Zaloguj się za pomocą StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "Zarejestruj się w StandardPortal", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "Załóż teraz konto Stadt Wien i używaj jednego loginu do wielu usług cyfrowych Wiednia.", "app.modules.id_cow.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_cow.emptyFieldError": "To pole nie może by<PERSON> puste.", "app.modules.id_cow.helpAltText": "<PERSON><PERSON><PERSON><PERSON>, gdzie znaleźć numer seryjny ID na dowodzie osobistym", "app.modules.id_cow.invalidIdSerialError": "Nieprawidłowa seria i numer dowodu osobistego", "app.modules.id_cow.invalidRunError": "Nieprawidłowy RUN", "app.modules.id_cow.noMatchFormError": "Nie znaleziono żadnego dopasowania.", "app.modules.id_cow.notEntitledFormError": "Nieuprawniony.", "app.modules.id_cow.showCOWHelp": "Gdzie mogę znaleźć numer numer PESEL?", "app.modules.id_cow.somethingWentWrongError": "<PERSON>e możemy Cię z<PERSON>, p<PERSON><PERSON><PERSON><PERSON>ś poszło nie tak", "app.modules.id_cow.submit": "Prześ<PERSON>j", "app.modules.id_cow.takenFormError": "Numer wykorzystany.", "app.modules.id_cow.verifyCow": "Weryfikacja przy użyciu COW", "app.modules.id_franceconnect.verificationButtonAltText": "Zweryfikuj za pomocą FranceConnect", "app.modules.id_gent_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_gent_rrn.emptyFieldError": "To pole nie może by<PERSON> puste.", "app.modules.id_gent_rrn.gentRrnHelp": "Twój numer PESEL znajduje się na odwrocie Twojego cyfrowego dowodu tożsamości", "app.modules.id_gent_rrn.invalidRrnError": "Nieprawidłowy numer PESEL", "app.modules.id_gent_rrn.noMatchFormError": "Nie udało nam się znaleźć informacji o twoim numerze PESEL.", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "Nie możemy Cię zwer<PERSON>, ponieważ mieszkasz poza Gandawą", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "Nie możemy Cię zwer<PERSON>, poniew<PERSON>ż masz mniej niż 14 lat.", "app.modules.id_gent_rrn.rrnLabel": "Numer PESEL", "app.modules.id_gent_rrn.rrnTooltip": "Prosimy o podanie numeru Pesel w celu sprawdzenia, czy jesteś obywatelem Gandawy w wieku powyżej 14 lat.", "app.modules.id_gent_rrn.showGentRrnHelp": "Gdzie mogę znaleźć numer PESEL?", "app.modules.id_gent_rrn.somethingWentWrongError": "<PERSON>e możemy Cię z<PERSON>, p<PERSON><PERSON><PERSON><PERSON>ś poszło nie tak", "app.modules.id_gent_rrn.submit": "Prześ<PERSON>j", "app.modules.id_gent_rrn.takenFormError": "Twój numer PESEL został już użyty do weryfikacji innego konta", "app.modules.id_gent_rrn.verifyGentRrn": "Weryfikacja przy użyciu GentRrn", "app.modules.id_id_card_lookup.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_id_card_lookup.emptyFieldError": "To pole nie może by<PERSON> puste.", "app.modules.id_id_card_lookup.helpAltText": "Wyjaśnienie dotyczące dowodu osobistego", "app.modules.id_id_card_lookup.invalidCardIdError": "Ten dowód osobisty jest nieważny.", "app.modules.id_id_card_lookup.noMatchFormError": "Nie znaleziono żadnego dopasowania.", "app.modules.id_id_card_lookup.showHelp": "Gdzie mogę znaleźć numer PESEL?", "app.modules.id_id_card_lookup.somethingWentWrongError": "<PERSON>e możemy Cię z<PERSON>, p<PERSON><PERSON><PERSON><PERSON>ś poszło nie tak", "app.modules.id_id_card_lookup.submit": "Prześ<PERSON>j", "app.modules.id_id_card_lookup.takenFormError": "Numer wykorzystany.", "app.modules.id_oostende_rrn.cancel": "<PERSON><PERSON><PERSON>", "app.modules.id_oostende_rrn.emptyFieldError": "To pole nie może by<PERSON> puste.", "app.modules.id_oostende_rrn.invalidRrnError": "Nieprawidłowy numer PESEL", "app.modules.id_oostende_rrn.noMatchFormError": "Nie udało nam się znaleźć informacji o twoim numerze PESEL.", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "<PERSON><PERSON> możemy Cię s<PERSON>, poniew<PERSON>ż mieszkasz poza Oostende.", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "Nie możemy Cię zwer<PERSON>, poniew<PERSON>ż masz mniej niż 14 lat.", "app.modules.id_oostende_rrn.oostendeRrnHelp": "Twój numer PESEL znajduje się na odwrocie Twojego cyfrowego dowodu tożsamości", "app.modules.id_oostende_rrn.rrnLabel": "Numer PESEL", "app.modules.id_oostende_rrn.rrnTooltip": "Prosimy o podanie numeru Pesel w celu sprawdzenia, czy jesteś obywatelem Gandawy w wieku powyżej 14 lat.", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "Gdzie mogę znaleźć numer PESEL?", "app.modules.id_oostende_rrn.somethingWentWrongError": "<PERSON>e możemy Cię z<PERSON>, p<PERSON><PERSON><PERSON><PERSON>ś poszło nie tak", "app.modules.id_oostende_rrn.submit": "Prześ<PERSON>j", "app.modules.id_oostende_rrn.takenFormError": "Twój numer PESEL został już użyty do weryfikacji innego konta", "app.modules.id_oostende_rrn.verifyOostendeRrn": "Weryfikacja przy użyciu numeru ubezpieczenia społecznego", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "Otr<PERSON>małeś prawa administratora do folderu \"{folderName}\".", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "Podziel się", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "Zapoznaj się z projektami na stronie {folderUrl}, aby Twój głos został usłyszany!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | z platformy partycypacyjnej {orgName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | z platformy partycypacyjnej {orgName}", "app.sessionRecording.accept": "Tak, akceptuję", "app.sessionRecording.modalDescription1": "Aby le<PERSON>j zrozumieć naszych użytkowników, losowo prosimy niewielki odsetek odwiedzających o szczegółowe śledzenie ich sesji przeglądania.", "app.sessionRecording.modalDescription2": "<PERSON><PERSON><PERSON> celem zarejestrowanych danych jest ulepszenie strony internetowej. Żadne z Twoich danych nie będą udostępniane stronom trzecim. Wszelkie wrażliwe informacje, kt<PERSON><PERSON> wp<PERSON><PERSON>sz, b<PERSON><PERSON><PERSON> filtrowane.", "app.sessionRecording.modalDescription3": "Zgadzasz się?", "app.sessionRecording.modalDescriptionFaq": "FAQ tutaj.", "app.sessionRecording.modalTitle": "Pomóż nam ulepszyć tę stronę", "app.sessionRecording.reject": "<PERSON><PERSON>, odrzucam", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "Przydzielanie środków", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "Zbierz opinie na temat dokumentu", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "Tworzenie ankiety Go Vocal", "app.utils.AdminPage.ProjectEdit.createPoll": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.AdminPage.ProjectEdit.createSurveyText": "Osadź ankietę z innych źródeł", "app.utils.AdminPage.ProjectEdit.findVolunteers": "Znajd<PERSON> wolontariuszy", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "Zbieranie inicjatyw i informacji zwrotnych", "app.utils.AdminPage.ProjectEdit.shareInformation": "Podziel się informacjami", "app.utils.FormattedCurrency.credits": "kredyty", "app.utils.FormattedCurrency.tokens": "tokeny", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# kredyty} one {# kredyty} other {# kredyty}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# tokeny} one {# token} other {# tokeny}}", "app.utils.IdeaCards.mostDiscussed": "Najczęściej omawiane", "app.utils.IdeaCards.mostReacted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reakcji", "app.utils.IdeaCards.newest": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.oldest": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.IdeaCards.random": "Przypadkowa", "app.utils.IdeaCards.trending": "<PERSON><PERSON><PERSON>", "app.utils.IdeasNewPage.contributionFormTitle": "Dodaj nowy wkład", "app.utils.IdeasNewPage.ideaFormTitle": "Do<PERSON>j nowy p<PERSON>sł", "app.utils.IdeasNewPage.initiativeFormTitle": "<PERSON><PERSON><PERSON>ą inicjatywę", "app.utils.IdeasNewPage.issueFormTitle1": "Dodaj nowy komentarz", "app.utils.IdeasNewPage.optionFormTitle": "<PERSON><PERSON><PERSON> opcję", "app.utils.IdeasNewPage.petitionFormTitle": "<PERSON><PERSON><PERSON> pet<PERSON>", "app.utils.IdeasNewPage.projectFormTitle": "Do<PERSON>j nowy projekt", "app.utils.IdeasNewPage.proposalFormTitle": "<PERSON><PERSON><PERSON> nową propozycję", "app.utils.IdeasNewPage.questionFormTitle": "<PERSON><PERSON>j nowe pytanie", "app.utils.IdeasNewPage.surveyTitle": "Ankieta", "app.utils.IdeasNewPage.viewYourComment": "Wyświetl swój komentarz", "app.utils.IdeasNewPage.viewYourContribution": "Zobacz swój wkład", "app.utils.IdeasNewPage.viewYourIdea": "<PERSON>obacz swój pomysł", "app.utils.IdeasNewPage.viewYourInitiative": "Wyświetl swoją inicjatywę", "app.utils.IdeasNewPage.viewYourInput": "Wyświetl swój wkład", "app.utils.IdeasNewPage.viewYourIssue": "Wyświetl swój numer", "app.utils.IdeasNewPage.viewYourOption": "Wyświetl swoją opcję", "app.utils.IdeasNewPage.viewYourPetition": "Wyświetl swoją petycję", "app.utils.IdeasNewPage.viewYourProject": "Wyświetl swój projekt", "app.utils.IdeasNewPage.viewYourProposal": "Wyświetl swoją propozycję", "app.utils.IdeasNewPage.viewYourQuestion": "Wyświetl swoje pytanie", "app.utils.Projects.sendSubmission": "Wyślij identyfikator zgłoszenia na mój adres e-mail", "app.utils.Projects.sendSurveySubmission": "Wyślij identyfikator ankiety na mój adres e-mail", "app.utils.Projects.surveySubmission": "Przesyłanie an<PERSON>t", "app.utils.Projects.yourResponseHasTheFollowingId": "Twoja odpowiedź ma następujący identyfikator: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "<PERSON><PERSON><PERSON> później <PERSON>, <PERSON><PERSON>, aby <PERSON><PERSON> odpowiedź została usunięta, skontaktuj się z nami, podając następujący unikalny identyfikator:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "<PERSON><PERSON> w<PERSON><PERSON>ć ud<PERSON>ł w tym wyd<PERSON>, musisz uzupełnić swój profil.", "app.utils.actionDescriptors.attendingEventNotInGroup": "<PERSON>e spełniasz wymagań, aby wzi<PERSON><PERSON> udział w tym wydarzeniu.", "app.utils.actionDescriptors.attendingEventNotPermitted": "Nie możesz uczestniczyć w tym wydarzeniu.", "app.utils.actionDescriptors.attendingEventNotSignedIn": "<PERSON><PERSON> w<PERSON><PERSON> ud<PERSON>ł w tym wyd<PERSON>, musisz się zalogować lub zare<PERSON>wać.", "app.utils.actionDescriptors.attendingEventNotVerified": "<PERSON><PERSON> w<PERSON><PERSON>ć ud<PERSON>ł w tym w<PERSON>, musisz zweryfikować swoje konto.", "app.utils.actionDescriptors.volunteeringMissingRequirements": "<PERSON><PERSON> <PERSON><PERSON><PERSON> w<PERSON>, mus<PERSON>z u<PERSON>ł<PERSON>ć swój profil.", "app.utils.actionDescriptors.volunteeringNotInGroup": "<PERSON><PERSON> spełniasz wymagań, aby zostać wolontariuszem.", "app.utils.actionDescriptors.volunteeringNotPermitted": "<PERSON>e moż<PERSON>z zost<PERSON>ć wolontariuszem.", "app.utils.actionDescriptors.volunteeringNotSignedIn": "<PERSON><PERSON> <PERSON><PERSON><PERSON> w<PERSON>, musisz się zalogować lub zare<PERSON>ć.", "app.utils.actionDescriptors.volunteeringNotVerified": "Mu<PERSON>z zweryfikować swoje konto, zanim będziesz mógł zostać wolontariuszem.", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "Zgłoś się jako wolontariusz na stronie {completeRegistrationLink} .", "app.utils.errors.api_error_default.in": "Nie jest w porządku", "app.utils.errors.default.ajv_error_birthyear_required": "Proszę wpisać swój rok urodzenia", "app.utils.errors.default.ajv_error_date_any": "<PERSON><PERSON><PERSON> wpisać prawdiwłowo datę", "app.utils.errors.default.ajv_error_domicile_required": "Proszę wpisać swoje miejsce zamieszkania", "app.utils.errors.default.ajv_error_gender_required": "<PERSON><PERSON><PERSON> wpisać swoją płeć", "app.utils.errors.default.ajv_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.ajv_error_maxItems": "Nie można dołącz<PERSON> więcej niż {limit, plural, one {# element} few {# elementy} many {# elementów} other {# elementy}}", "app.utils.errors.default.ajv_error_minItems": "<PERSON><PERSON> co najmniej {limit, plural, one {# element} few {# elementy} many {# elementy} other {# elementy}}", "app.utils.errors.default.ajv_error_number_any": "<PERSON><PERSON><PERSON> wpisać prawidłowy numer", "app.utils.errors.default.ajv_error_politician_required": "<PERSON><PERSON><PERSON>, c<PERSON> jest <PERSON>/Pani politykiem", "app.utils.errors.default.ajv_error_required3": "Pole jest wymagane: \"{fieldN<PERSON>}\"", "app.utils.errors.default.ajv_error_type": "<PERSON><PERSON> może by<PERSON> puste", "app.utils.errors.default.api_error_accepted": "<PERSON><PERSON><PERSON><PERSON>", "app.utils.errors.default.api_error_blank": "<PERSON><PERSON> może by<PERSON> puste", "app.utils.errors.default.api_error_confirmation": "<PERSON><PERSON> pas<PERSON>je", "app.utils.errors.default.api_error_empty": "<PERSON><PERSON> może by<PERSON> puste", "app.utils.errors.default.api_error_equal_to": "Nie jest w porządku", "app.utils.errors.default.api_error_even": "<PERSON><PERSON> <PERSON> parzysty", "app.utils.errors.default.api_error_exclusion": "<PERSON><PERSON>", "app.utils.errors.default.api_error_greater_than": "Jest zbyt mały", "app.utils.errors.default.api_error_greater_than_or_equal_to": "Jest zbyt mały", "app.utils.errors.default.api_error_inclusion": "Nie występuje na liście", "app.utils.errors.default.api_error_invalid": "<PERSON><PERSON>", "app.utils.errors.default.api_error_less_than": "<PERSON><PERSON> z<PERSON>t du<PERSON>", "app.utils.errors.default.api_error_less_than_or_equal_to": "<PERSON><PERSON> z<PERSON>t du<PERSON>", "app.utils.errors.default.api_error_not_a_number": "<PERSON>e jest licz<PERSON>ą", "app.utils.errors.default.api_error_not_an_integer": "<PERSON><PERSON> <PERSON><PERSON> licz<PERSON> całkowitą", "app.utils.errors.default.api_error_other_than": "Nie jest w porządku", "app.utils.errors.default.api_error_present": "<PERSON><PERSON> <PERSON> puste", "app.utils.errors.default.api_error_too_long": "<PERSON>st zbyt długi", "app.utils.errors.default.api_error_too_short": "Jest zbyt krótki", "app.utils.errors.default.api_error_wrong_length": "<PERSON>łaściwą długo<PERSON>", "app.utils.errors.defaultapi_error_.odd": "To musi być nieparzyste.", "app.utils.notInGroup": "Nie spełniasz wymogów uczestnictwa.", "app.utils.participationMethod.onSurveySubmission": "Dziękuję. Twoja odpowiedź została przyjęta.", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "Głosowanie nie jest już <PERSON>, ponieważ ta faza nie jest już a<PERSON>ywna.", "app.utils.participationMethodConfig.voting.votingNotInGroup": "Nie spełniasz wymagań uprawniających do głosowania.", "app.utils.participationMethodConfig.voting.votingNotPermitted": "<PERSON><PERSON> m<PERSON>ż<PERSON>z <PERSON>ł<PERSON>.", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "<PERSON><PERSON>, musisz si<PERSON> z<PERSON>ować lub zare<PERSON>.", "app.utils.participationMethodConfig.voting.votingNotVerified": "<PERSON><PERSON><PERSON> oddaniem głosu musisz zweryfikować swoje konto.", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>Składanie budżetów zakończyło się na stronie {endDate}.</b> Uczestnicy mieli do dyspozycji łączną kwotę <b>{maxBudget} do rozdysponowania pomiędzy opcje {optionCount} .</b>", "app.utils.votingMethodUtils.budgetSubmitted": "Przedłoż<PERSON> budżet", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "Budżet przedłożony 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "Nie spełniasz wymagań dotyczących przypisywania budżetów.", "app.utils.votingMethodUtils.budgetingNotPermitted": "<PERSON>e moż<PERSON>z przydzielać budżetów.", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "<PERSON><PERSON><PERSON><PERSON><PERSON> bud<PERSON><PERSON>, musisz się zalogować lub zare<PERSON>.", "app.utils.votingMethodUtils.budgetingNotVerified": "Przed przypisaniem budżetów musisz zweryfikować swoje konto.", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b><PERSON><PERSON><PERSON><PERSON> budżet nie zostanie uwzględniony na stronie</b> , dopóki nie klikniesz przycisku \"Wyślij\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "Minimalny wymagany budżet to {amount}.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "Po zakończeniu kliknij przycisk \"Wyślij\", aby prz<PERSON><PERSON><PERSON> swój budżet.", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "<PERSON><PERSON><PERSON><PERSON>, dotykając przycisku \"Dodaj\".", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "<PERSON><PERSON> <b>{maxBudget} do rozdzielenia między opcje {optionCount} </b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>j budżet został przesłany!</b> <PERSON><PERSON><PERSON><PERSON> sprawdzić swoje opcje poniżej w dowolnym momencie lub zmodyfikować je przed <b>{endDate}</b>.", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> budżet został przesłany!</b> <PERSON><PERSON><PERSON><PERSON> sprawdzić swoje opcje poniżej w dowolnym momencie.", "app.utils.votingMethodUtils.castYourVote": "Oddaj swój głos", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>ć maksymalnie {maxVotes, plural, one {# kredytów} other {# kredytów}} na opcję.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "<PERSON><PERSON><PERSON><PERSON> do<PERSON> maksymalnie {maxVotes, plural, one {# punkt} other {# punkty}} na opcję.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "<PERSON><PERSON><PERSON><PERSON> do<PERSON> maksymalnie {maxVotes, plural, one {# token} other {# tokeny}} na opcję.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "<PERSON><PERSON><PERSON><PERSON> maksymalnie {maxVotes, plural, one {# vote} other {# votes}} na opcję.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "Po zakończeniu kliknij przycisk \"Wyślij\", aby <PERSON> głos.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "<PERSON><PERSON><PERSON><PERSON>ne opcje, dotykając przycisku \"Wybierz\".", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "Wyniki końcowe", "app.utils.votingMethodUtils.finalTally": "Wynik końcowy", "app.utils.votingMethodUtils.howToParticipate": "<PERSON><PERSON> w<PERSON><PERSON> ud<PERSON>ł", "app.utils.votingMethodUtils.howToVote": "<PERSON><PERSON>", "app.utils.votingMethodUtils.multipleVotingEnded1": "Głosowanie zostało zamknięte na stronie <b>{endDate}.</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 kredyt<PERSON>} one {1 kredyt} other {# kredytów}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 punktów} one {1 punkt} other {# punktów}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 tokenów} one {1 token} other {# tokenów}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 głosów} one {1 głos} other {# głosów}}", "app.utils.votingMethodUtils.results": "Wyniki", "app.utils.votingMethodUtils.singleVotingEnded": "Głosowanie zostało zamknięte na stronie <b>{endDate}.</b> Uczestnicy mogli <b>głosować na opcje {maxVotes} .</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "<PERSON><PERSON><PERSON><PERSON> op<PERSON>, dotykając przycisku \"Głosuj\".", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "<PERSON><PERSON> <b>{totalVotes} głosów</b> , które możesz przypisać do opcji.", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "Po zakończeniu kliknij przycisk \"Wyślij\", aby <PERSON> głos.", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "Głosowanie zostało zamknięte na stronie <b>{endDate}.</b> Uczestnicy mogli <b>głosować na 1 opcję.</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "<PERSON><PERSON><PERSON><PERSON> opcję, dotykając przycisku \"Głosuj\".", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "<PERSON><PERSON> <b>1 głos</b> , kt<PERSON><PERSON> możesz przypisać do jednej z opcji.", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "Głosowanie zakończyło się na stronie <b>{endDate}.</b> Uczestnicy mogli <b>głosować na dowolną liczbę opcji.</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "Możesz głosować na dowolną liczbę opcji.", "app.utils.votingMethodUtils.submitYourBudget": "Prześlij swój budżet", "app.utils.votingMethodUtils.submittedBudgetCountText2": "osób przesłało swój budżet online", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "osób przesłało swoje budżety online", "app.utils.votingMethodUtils.submittedVoteCountText2": "osó<PERSON> oddało swój głos online", "app.utils.votingMethodUtils.submittedVotesCountText2": "osób oddało swoje głosy online", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "<PERSON><PERSON><PERSON> 🎉", "app.utils.votingMethodUtils.votesCast": "<PERSON><PERSON>", "app.utils.votingMethodUtils.votingClosed": "Głosowanie zakończone", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>Tw<PERSON><PERSON> głos nie zostanie policzony</b>, dop<PERSON>ki nie klik<PERSON>z \"Wyślij\"", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>j głos został oddany!</b> <PERSON><PERSON><PERSON><PERSON> sprawdzić lub zmodyfikować swoje zgłoszenie przed <b>{endDate}</b>.", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>j głos został oddany!</b> <PERSON><PERSON><PERSON><PERSON> sprawdzić lub zmodyfikować swoje zgłoszenie poniżej w dowolnym momencie.", "components.UI.IdeaSelect.noIdeaAvailable": "<PERSON>e ma dostępnych pomysłów.", "components.UI.IdeaSelect.selectIdea": "<PERSON><PERSON><PERSON><PERSON>", "containers.SiteMap.allProjects": "Wszystkie projekty", "containers.SiteMap.customPageSection": "St<PERSON><PERSON> ni<PERSON>", "containers.SiteMap.folderInfo": "Więcej informacji", "containers.SiteMap.headSiteMapTitle": "Mapa strony | {orgName}", "containers.SiteMap.homeSection": "Ogólne", "containers.SiteMap.pageContents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strony", "containers.SiteMap.profilePage": "<PERSON><PERSON> strona profi<PERSON>a", "containers.SiteMap.profileSettings": "<PERSON><PERSON> us<PERSON>", "containers.SiteMap.projectEvents": "Wyd<PERSON>zen<PERSON>", "containers.SiteMap.projectIdeas": "Pomysły", "containers.SiteMap.projectInfo": "Informacja", "containers.SiteMap.projectPoll": "<PERSON><PERSON><PERSON>", "containers.SiteMap.projectSurvey": "Ankieta", "containers.SiteMap.projectsArchived": "Projekty zarchiwizowane", "containers.SiteMap.projectsCurrent": "Aktualne projekty", "containers.SiteMap.projectsDraft": "Projekty robocze", "containers.SiteMap.projectsSection": "Projekty {orgName}", "containers.SiteMap.signInPage": "Zap<PERSON>z <PERSON>ę", "containers.SiteMap.signUpPage": "Zarejestruj się", "containers.SiteMap.siteMapDescription": "Z tej strony można przejść do dowolnej treści na platformie.", "containers.SiteMap.siteMapTitle": "Mapa strony platformy uczestnictwa {orgName}", "containers.SiteMap.successStories": "Success stories", "containers.SiteMap.timeline": "<PERSON><PERSON><PERSON> projektu", "containers.SiteMap.userSpaceSection": "<PERSON><PERSON> konto", "containers.SubscriptionEndedPage.accessDenied": "<PERSON>e masz już <PERSON>", "containers.SubscriptionEndedPage.subscriptionEnded": "Wygląda na to, że nie masz już dostępu do tej strony, ponieważ Twoja subskrypcja platformy Go Vocal została zakończona."}