{"EmailSettingsPage.emailSettings": "ای میل کی ترتیبات", "EmailSettingsPage.initialUnsubscribeError": "اس مہم سے ان سبسکرائب کرنے میں ایک مسئلہ تھا، براہ کرم دوبارہ کوشش کریں۔", "EmailSettingsPage.initialUnsubscribeLoading": "آپ کی درخواست پر کارروائی ہو رہی ہے، براہ کرم انتظار کریں...", "EmailSettingsPage.initialUnsubscribeSuccess": "آپ نے کامیابی سے {campaignTitle}سے رکنیت ختم کر دی ہے۔", "UI.FormComponents.optional": "اختیاری", "app.closeIconButton.a11y_buttonActionMessage": "بند", "app.components.Areas.areaUpdateError": "آپ کے علاقے کو محفوظ کرتے وقت ایک خرابی پیش آگئی۔ براہ کرم دوبارہ کوشش کریں۔", "app.components.Areas.followedArea": "پیروی کردہ علاقہ: {areaTitle}", "app.components.Areas.followedTopic": "مندرجہ ذیل موضوع: {topicTitle}", "app.components.Areas.topicUpdateError": "آپ کے موضوع کو محفوظ کرتے وقت ایک خرابی پیش آگئی۔ براہ کرم دوبارہ کوشش کریں۔", "app.components.Areas.unfollowedArea": "پیروی نہ کیا گیا علاقہ: {areaTitle}", "app.components.Areas.unfollowedTopic": "پیروی نہیں کیا گیا موضوع: {topicTitle}", "app.components.AssignBudgetControl.a11y_price": "قیمت:", "app.components.AssignBudgetControl.add": "شامل کریں۔", "app.components.AssignBudgetControl.added": "شامل کیا گیا۔", "app.components.AssignMultipleVotesControl.addVote": "ووٹ شامل کریں۔", "app.components.AssignMultipleVotesControl.maxCreditsInTotalReached": "آپ نے اپنے تمام کریڈٹ تقسیم کر دیے ہیں۔", "app.components.AssignMultipleVotesControl.maxCreditsPerInputReached": "آپ نے اس اختیار کے لیے زیادہ سے زیادہ کریڈٹ تقسیم کیے ہیں۔", "app.components.AssignMultipleVotesControl.maxPointsInTotalReached": "آپ نے اپنے تمام پوائنٹس تقسیم کردیئے ہیں۔", "app.components.AssignMultipleVotesControl.maxPointsPerInputReached": "آپ نے اس اختیار کے لیے زیادہ سے زیادہ پوائنٹس تقسیم کیے ہیں۔", "app.components.AssignMultipleVotesControl.maxTokensInTotalReached": "آپ نے اپنے تمام ٹوکن تقسیم کر دیے ہیں۔", "app.components.AssignMultipleVotesControl.maxTokensPerInputReached": "آپ نے اس اختیار کے لیے ٹوکن کی زیادہ سے زیادہ تعداد تقسیم کر دی ہے۔", "app.components.AssignMultipleVotesControl.maxVotesInTotalReached": "آپ نے اپنے تمام ووٹ تقسیم کیے ہیں۔", "app.components.AssignMultipleVotesControl.maxVotesPerInputReached1": "آپ نے اس اختیار کے لیے زیادہ سے زیادہ ووٹ تقسیم کیے ہیں۔", "app.components.AssignMultipleVotesControl.numberManualVotes2": "{manualVotes, plural, one {(بشمول 1 آف لائن)} other {(بشمول # آف لائن)}}", "app.components.AssignMultipleVotesControl.phaseNotActive": "ووٹنگ دستیاب نہیں ہے، کیونکہ یہ مرحلہ فعال نہیں ہے۔", "app.components.AssignMultipleVotesControl.removeVote": "ووٹ ہٹا دیں۔", "app.components.AssignMultipleVotesControl.select": "منتخب کریں۔", "app.components.AssignMultipleVotesControl.votesSubmitted1": "آپ اپنا ووٹ پہلے ہی جمع کر چکے ہیں۔ اس میں ترمیم کرنے کے لیے، \"اپنی جمع کرانے میں ترمیم کریں\" پر کلک کریں۔", "app.components.AssignMultipleVotesControl.votesSubmittedIdeaPage1": "آپ اپنا ووٹ پہلے ہی جمع کر چکے ہیں۔ اس میں ترمیم کرنے کے لیے، پراجیکٹ کے صفحہ پر واپس جائیں اور \"اپنی جمع کرانے میں ترمیم کریں\" پر کلک کریں۔", "app.components.AssignMultipleVotesControl.xCredits1": "{votes, plural, one {کریڈٹ} other {کریڈٹ}}", "app.components.AssignMultipleVotesControl.xPoints1": "{votes, plural, one {پوائنٹ} other {پوائنٹس}}", "app.components.AssignMultipleVotesControl.xTokens1": "{votes, plural, one {ٹوکن} other {ٹوکن}}", "app.components.AssignMultipleVotesControl.xVotes3": "{votes, plural, one {ووٹ} other {ووٹ}}", "app.components.AssignVoteControl.maxVotesReached1": "آپ نے اپنے تمام ووٹ تقسیم کیے ہیں۔", "app.components.AssignVoteControl.phaseNotActive": "ووٹنگ دستیاب نہیں ہے، کیونکہ یہ مرحلہ فعال نہیں ہے۔", "app.components.AssignVoteControl.select": "منتخب کریں۔", "app.components.AssignVoteControl.selected2": "من<PERSON><PERSON>ب", "app.components.AssignVoteControl.voteForAtLeastOne": "کم از کم 1 آپشن کے لیے ووٹ دیں۔", "app.components.AssignVoteControl.votesSubmitted1": "آپ اپنا ووٹ پہلے ہی جمع کر چکے ہیں۔ اس میں ترمیم کرنے کے لیے، \"اپنی جمع کرانے میں ترمیم کریں\" پر کلک کریں۔", "app.components.AssignVoteControl.votesSubmittedIdeaPage1": "آپ اپنا ووٹ پہلے ہی جمع کر چکے ہیں۔ اس میں ترمیم کرنے کے لیے، پراجیکٹ کے صفحہ پر واپس جائیں اور \"اپنی جمع کرانے میں ترمیم کریں\" پر کلک کریں۔", "app.components.AuthProviders.continue": "جاری رکھیں", "app.components.AuthProviders.continueWithAzure": "{azureProviderName}کے ساتھ جاری رکھیں", "app.components.AuthProviders.continueWithFacebook": "فیس بک کے ساتھ جاری رکھیں", "app.components.AuthProviders.continueWithFakeSSO": "جعلی SSO کے ساتھ جاری رکھیں", "app.components.AuthProviders.continueWithGoogle": "گوگل کے ساتھ جاری رکھیں", "app.components.AuthProviders.continueWithHoplr": "Hoplr کے ساتھ جاری رکھیں", "app.components.AuthProviders.continueWithIdAustria": "ID آسٹریا کے ساتھ جاری رکھیں", "app.components.AuthProviders.continueWithLoginMechanism": "{loginMechanismName}کے ساتھ جاری رکھیں", "app.components.AuthProviders.continueWithNemlogIn": "MitID کے ساتھ جاری رکھیں", "app.components.AuthProviders.franceConnectMergingFailed": "اس ای میل ایڈریس کے ساتھ ایک اکاؤنٹ پہلے سے موجود ہے۔{br}{br}آپ FranceConnect کا استعمال کرتے ہوئے پلیٹ فارم تک رسائی حاصل نہیں کر سکتے کیونکہ ذاتی تفصیلات مماثل نہیں ہیں۔ فرانس کنیکٹ کا استعمال کرتے ہوئے لاگ ان کرنے کے لیے، آپ کو پہلے اس پلیٹ فارم پر اپنا پہلا نام یا آخری نام تبدیل کرنا ہوگا تاکہ آپ کی آفیشل تفصیلات سے مماثل ہو۔{br}{br}آپ لاگ ان کر سکتے ہیں جیسا کہ آپ عام طور پر نیچے کرتے ہیں۔", "app.components.AuthProviders.goToLogIn": "پہلے سے ہی اکاؤنٹ ہے؟ {goToOtherFlowLink}", "app.components.AuthProviders.goToSignUp": "اکاؤنٹ نہیں ہے؟ {goToOtherFlowLink}", "app.components.AuthProviders.logIn2": "لاگ ان کریں۔", "app.components.AuthProviders.logInWithEmail": "ای میل کے ساتھ لاگ ان کریں۔", "app.components.AuthProviders.nemlogInUnderMinimumAgeVerificationFailed": "تصدیق کے لیے آپ کی کم از کم عمر یا اس سے زیادہ ہونی چاہیے۔", "app.components.AuthProviders.signUp2": "سائن اپ کریں۔", "app.components.AuthProviders.signUpButtonAltText": "{loginMechanismName}کے ساتھ سائن اپ کریں۔", "app.components.AuthProviders.signUpWithEmail": "ای میل کے ساتھ سائن اپ کریں۔", "app.components.AuthProviders.verificationRequired": "تصدیق درکار ہے۔", "app.components.Author.a11yPostedBy": "کی طرف سے پوسٹ کیا گیا", "app.components.AvatarBubbles.numberOfParticipants1": "{numberOfParticipants, plural, one {1 شریک} other {{numberOfParticipants} شرکاء}}", "app.components.AvatarBubbles.numberOfUsers": "{numberOfUsers} صارفین", "app.components.AvatarBubbles.participant": "شریک", "app.components.AvatarBubbles.participants1": "شرکاء", "app.components.Comments.cancel": "منسوخ کریں۔", "app.components.Comments.commentingDisabledInCurrentPhase": "موجودہ مرحلے میں تبصرہ ممکن نہیں۔", "app.components.Comments.commentingDisabledInactiveProject": "تبصرہ ممکن نہیں ہے کیونکہ یہ پروجیکٹ فی الحال فعال نہیں ہے۔", "app.components.Comments.commentingDisabledProject": "اس پروجیکٹ میں تبصرہ کرنا فی الحال غیر فعال ہے۔", "app.components.Comments.commentingDisabledUnverified": "تبصرہ کرنے کے لیے {verifyIdentityLink} ۔", "app.components.Comments.commentingMaybeNotPermitted": "براہ کرم {signInLink} دیکھیں کہ کیا اقدامات کیے جا سکتے ہیں۔", "app.components.Comments.inputsAssociatedWithProfile": "پہلے سے طے شدہ طور پر آپ کی گذارشات آپ کے پروفائل کے ساتھ منسلک ہوں گی، جب تک کہ آپ اس اختیار کو منتخب نہ کریں۔", "app.components.Comments.invisibleTitleComments": "تبصرے", "app.components.Comments.leastRecent": "سب سے حالیہ", "app.components.Comments.likeComment": "اس تبصرہ کو پسند کریں۔", "app.components.Comments.mostLiked": "زیادہ تر ردعمل", "app.components.Comments.mostRecent": "تازہ ترین", "app.components.Comments.official": "سرکاری", "app.components.Comments.postAnonymously": "گمنام پوسٹ کریں۔", "app.components.Comments.replyToComment": "تبصرہ کا جواب دیں۔", "app.components.Comments.reportAsSpam": "سپیم کے بطور رپورٹ کریں۔", "app.components.Comments.seeOriginal": "اصل دیکھیں", "app.components.Comments.seeTranslation": "ترجمہ دیکھیں", "app.components.Comments.yourComment": "آپ کا تبصرہ", "app.components.CommonGroundResults.divisiveDescription": "ایسے بیانات جہاں لوگ متفق اور متفق نہ ہوں:", "app.components.CommonGroundResults.divisiveTitle": "تفرقہ انگیز", "app.components.CommonGroundResults.majorityDescription": "60% سے زیادہ نے مندرجہ ذیل پر ایک یا دوسرے طریقے سے ووٹ دیا:", "app.components.CommonGroundResults.majorityTitle": "اکثریت", "app.components.CommonGroundResults.participantLabel": "شریک", "app.components.CommonGroundResults.participantsLabel1": "شرکاء", "app.components.CommonGroundResults.statementLabel": "بیان", "app.components.CommonGroundResults.statementsLabel1": "بیانات", "app.components.CommonGroundResults.votesLabe": "ووٹ", "app.components.CommonGroundResults.votesLabel1": "ووٹ", "app.components.CommonGroundStatements.agreeLabel": "متفق", "app.components.CommonGroundStatements.disagreeLabel": "اختلا<PERSON> کرنا", "app.components.CommonGroundStatements.noMoreStatements": "ابھی جواب دینے کے لیے کوئی بیانات نہیں ہیں۔", "app.components.CommonGroundStatements.noResults": "ابھی تک دکھانے کے لیے کوئی نتائج نہیں ہیں۔ براہ کرم یقینی بنائیں کہ آپ نے کامن گراؤنڈ مرحلے میں حصہ لیا ہے اور اس کے بعد دوبارہ یہاں چیک کریں۔", "app.components.CommonGroundStatements.unsureLabel": "بے یقینی", "app.components.CommonGroundTabs.resultsTabLabel": "نتائج", "app.components.CommonGroundTabs.statementsTabLabel": "بیانات", "app.components.CommunityMonitorModal.formError": "ایک خرابی کا سامنا کرنا پڑا۔", "app.components.CommunityMonitorModal.surveyDescription2": "یہ جاری سروے ٹریک کرتا ہے کہ آپ گورننس اور عوامی خدمات کے بارے میں کیسا محسوس کرتے ہیں۔", "app.components.CommunityMonitorModal.xMinutesToComplete": "{minutes, plural, =0 {<1 منٹ لیتا ہے} one {1 منٹ لیتا ہے} other {# منٹ لیتا ہے}}", "app.components.ConfirmationModal.anExampleCodeHasBeenSent": "تصدیقی کوڈ کے ساتھ ایک ای میل {userEmail}پر بھیجا گیا ہے۔", "app.components.ConfirmationModal.changeYourEmail": "اپنا ای میل تبدیل کریں۔", "app.components.ConfirmationModal.codeInput": "کوڈ", "app.components.ConfirmationModal.confirmationCodeSent": "نیا کوڈ بھیجا گیا۔", "app.components.ConfirmationModal.didntGetAnEmail": "ای میل موصول نہیں ہوئی؟", "app.components.ConfirmationModal.foundYourCode": "آپ کا کوڈ ملا؟", "app.components.ConfirmationModal.goBack": "واپس جاؤ۔", "app.components.ConfirmationModal.sendEmailWithCode": "کوڈ کے ساتھ ای میل بھیجیں۔", "app.components.ConfirmationModal.sendNewCode": "نیا کوڈ بھیجیں۔", "app.components.ConfirmationModal.verifyAndContinue": "تصدیق کریں اور جاری رکھیں", "app.components.ConfirmationModal.wrongEmail": "غلط ای میل؟", "app.components.ConsentManager.Banner.accept": "قبول کریں۔", "app.components.ConsentManager.Banner.ariaButtonClose2": "پالیسی کو مسترد کریں اور بینر بند کریں۔", "app.components.ConsentManager.Banner.close": "بند", "app.components.ConsentManager.Banner.mainText": "یہ پلیٹ فارم ہمارے {policyLink}کے مطابق کوکیز کا استعمال کرتا ہے۔", "app.components.ConsentManager.Banner.manage": "انتظام کریں۔", "app.components.ConsentManager.Banner.policyLink": "کوکی پالیسی", "app.components.ConsentManager.Banner.reject": "رد کرنا", "app.components.ConsentManager.Modal.PreferencesDialog.advertising": "ایڈورٹائزنگ", "app.components.ConsentManager.Modal.PreferencesDialog.advertisingPurpose": "ہم اس کا استعمال اپنی ویب سائٹ کی اشتہاری مہموں کی تاثیر کو ذاتی بنانے اور پیمائش کرنے کے لیے کرتے ہیں۔ ہم اس پلیٹ فارم پر کوئی اشتہار نہیں دکھائیں گے، لیکن درج ذیل سروسز آپ کو ہماری سائٹ پر جو صفحات ملاحظہ کرتی ہیں ان کی بنیاد پر آپ کو ذاتی نوعیت کا اشتہار پیش کر سکتی ہیں۔", "app.components.ConsentManager.Modal.PreferencesDialog.allow": "اجازت دیں۔", "app.components.ConsentManager.Modal.PreferencesDialog.analytics": "تجزیات", "app.components.ConsentManager.Modal.PreferencesDialog.analyticsPurpose": "ہم اس ٹریکنگ کو بہتر طور پر سمجھنے کے لیے استعمال کرتے ہیں کہ آپ اپنی نیویگیشن کو سیکھنے اور بہتر بنانے کے لیے پلیٹ فارم کو کس طرح استعمال کرتے ہیں۔ یہ معلومات صرف بڑے پیمانے پر تجزیات میں استعمال ہوتی ہے، اور انفرادی لوگوں کو ٹریک کرنے کے لیے کسی بھی طرح سے نہیں۔", "app.components.ConsentManager.Modal.PreferencesDialog.back": "واپس جاؤ", "app.components.ConsentManager.Modal.PreferencesDialog.cancel": "منسوخ کریں۔", "app.components.ConsentManager.Modal.PreferencesDialog.disallow": "اجازت نہ دیں۔", "app.components.ConsentManager.Modal.PreferencesDialog.functional": "فنکشنل", "app.components.ConsentManager.Modal.PreferencesDialog.functionalPurpose": "ویب سائٹ کے بنیادی افعال کو فعال اور مانیٹر کرنے کے لیے یہ ضروری ہے۔ ہو سکتا ہے یہاں درج کچھ ٹولز آپ پر لاگو نہ ہوں۔ مزید معلومات کے لیے براہ کرم ہماری کوکی پالیسی پڑھیں۔", "app.components.ConsentManager.Modal.PreferencesDialog.google_tag_manager": "گوگل ٹیگ مینیجر ({destinations})", "app.components.ConsentManager.Modal.PreferencesDialog.required": "درکار ہے۔", "app.components.ConsentManager.Modal.PreferencesDialog.requiredPurpose": "ایک فعال پلیٹ فارم حاصل کرنے کے لیے، اگر آپ سائن اپ کرتے ہیں تو ہم ایک تصدیقی کوکی محفوظ کرتے ہیں، اور وہ زبان جس میں آپ یہ پلیٹ فارم استعمال کرتے ہیں۔", "app.components.ConsentManager.Modal.PreferencesDialog.save": "محفوظ کریں۔", "app.components.ConsentManager.Modal.PreferencesDialog.title": "آپ کی کوکی کی ترجیحات", "app.components.ConsentManager.Modal.PreferencesDialog.tools": "اوزار", "app.components.ContentUploadDisclaimer.contentDisclaimerModalHeader": "مواد اپ لوڈ ڈس کلیمر", "app.components.ContentUploadDisclaimer.contentUploadDisclaimerFull2": "مواد اپ لوڈ کرکے، آپ اعلان کرتے ہیں کہ یہ مواد فریق ثالث کے کسی بھی ضابطے یا حقوق کی خلاف ورزی نہیں کرتا، جیسے املاک دانش کے حقوق، رازداری کے حقوق، تجارتی راز کے حقوق وغیرہ۔ اس کے نتیجے میں، اس مواد کو اپ لوڈ کرکے، آپ اپ لوڈ کردہ مواد کے نتیجے میں ہونے والے تمام براہ راست اور بالواسطہ نقصانات کی مکمل اور خصوصی ذمہ داری برداشت کرنے کا عہد کرتے ہیں۔ مزید برآں، آپ پلیٹ فارم کے مالک اور گو ووکل کو تیسرے فریق کے خلاف کسی بھی فریق ثالث کے دعووں یا ذمہ داریوں، اور کسی بھی متعلقہ اخراجات کے خلاف معاوضہ ادا کرنے کا عہد کرتے ہیں، جو آپ کے اپ لوڈ کردہ مواد سے پیدا ہوں گے یا اس کے نتیجے میں ہوں گے۔", "app.components.ContentUploadDisclaimer.onAccept": "میں سمجھتا ہوں۔", "app.components.ContentUploadDisclaimer.onCancel": "منسوخ کریں۔", "app.components.CustomFieldsForm.Fields.SentimentScaleField.tellUsWhy": "ہمیں بتائیں کیوں؟", "app.components.CustomFieldsForm.addressInputAriaLabel": "ایڈریس ان پٹ", "app.components.CustomFieldsForm.addressInputPlaceholder6": "ایک پتہ درج کریں...", "app.components.CustomFieldsForm.adminFieldTooltip": "فیلڈ صرف ایڈمنز کے لیے مرئی ہے۔", "app.components.CustomFieldsForm.anonymousSurveyMessage2": "اس سروے کے تمام جوابات گمنام ہیں۔", "app.components.CustomFieldsForm.atLeastThreePointsRequired": "کثیرالاضلاع کے لیے کم از کم تین پوائنٹس درکار ہیں۔", "app.components.CustomFieldsForm.atLeastTwoPointsRequired": "ایک لائن کے لیے کم از کم دو پوائنٹس درکار ہیں۔", "app.components.CustomFieldsForm.attachmentRequired": "کم از کم ایک منسلکہ درکار ہے۔", "app.components.CustomFieldsForm.authorFieldLabel": "مصنف", "app.components.CustomFieldsForm.authorFieldPlaceholder": "صارف کے ای میل یا نام سے تلاش کرنے کے لیے ٹائپ کرنا شروع کریں...", "app.components.CustomFieldsForm.back": "پیچھے", "app.components.CustomFieldsForm.budgetFieldLabel": "بجٹ", "app.components.CustomFieldsForm.clickOnMapMultipleToAdd3": "ڈرا کرنے کے لیے نقشے پر کلک کریں۔ پھر، ان کو منتقل کرنے کے لیے پوائنٹس پر گھسیٹیں۔", "app.components.CustomFieldsForm.clickOnMapToAddOrType": "اپنا جواب شامل کرنے کے لیے نقشے پر کلک کریں یا نیچے ایک پتہ ٹائپ کریں۔", "app.components.CustomFieldsForm.confirm": "تصدیق کریں۔", "app.components.CustomFieldsForm.descriptionMinLength": "تفصیل کم از کم {min} حروف کی ہونی چاہیے۔", "app.components.CustomFieldsForm.descriptionRequired": "تفصیل درکار ہے۔", "app.components.CustomFieldsForm.fieldMaximumItems": "زیادہ سے زیادہ {maxSelections, plural, one {# آپشن} other {# آپشنز}} فیلڈ کے لیے منتخب کیا جا سکتا ہے \"{fieldName}\"", "app.components.CustomFieldsForm.fieldMinimumItems": "کم از کم {minSelections, plural, one {# آپشن} other {# آپشنز}} فیلڈ کے لیے منتخب کیا جا سکتا ہے \"{fieldName}\"", "app.components.CustomFieldsForm.fieldRequired": "فیلڈ \"{fieldName}\" درکار ہے۔", "app.components.CustomFieldsForm.fileSizeLimit": "فائل کے سائز کی حد {maxFileSize} MB ہے۔", "app.components.CustomFieldsForm.imageRequired": "تصویر درکار ہے۔", "app.components.CustomFieldsForm.minimumCoordinates2": "کم از کم {numPoints} نقشہ پوائنٹس درکار ہیں۔", "app.components.CustomFieldsForm.notPublic1": "*اس جواب کا اشتراک صرف پروجیکٹ مینیجرز کے ساتھ کیا جائے گا، عوام کے ساتھ نہیں۔", "app.components.CustomFieldsForm.otherArea": "کہیں اور", "app.components.CustomFieldsForm.progressBarLabel": "پیش رفت", "app.components.CustomFieldsForm.removeAnswer": "جواب ہٹا دیں۔", "app.components.CustomFieldsForm.selectAsManyAsYouLike": "* جتنے چاہیں منتخب کریں۔", "app.components.CustomFieldsForm.selectBetween": "* {minItems} اور {maxItems} اختیارات کے درمیان منتخب کریں", "app.components.CustomFieldsForm.selectExactly2": "*بالکل منتخب کریں {selectExactly, plural, one {# آپشن} other {# اختیارات}}", "app.components.CustomFieldsForm.selectMany": "* جتنے چاہیں منتخب کریں۔", "app.components.CustomFieldsForm.tapOnFullscreenMapToAdd4": "ڈرا کرنے کے لیے نقشے پر ٹیپ کریں۔ پھر، ان کو منتقل کرنے کے لیے پوائنٹس پر گھسیٹیں۔", "app.components.CustomFieldsForm.tapOnFullscreenMapToAddPoint": "ڈرا کرنے کے لیے نقشے پر ٹیپ کریں۔", "app.components.CustomFieldsForm.tapOnMapMultipleToAdd3": "اپنا جواب شامل کرنے کے لیے نقشے پر تھپتھپائیں۔", "app.components.CustomFieldsForm.tapOnMapToAddOrType": "اپنا جواب شامل کرنے کے لیے نقشے پر ٹیپ کریں یا نیچے ایک پتہ ٹائپ کریں۔", "app.components.CustomFieldsForm.tapToAddALine": "لائن شامل کرنے کے لیے تھپتھپائیں۔", "app.components.CustomFieldsForm.tapToAddAPoint": "پوائنٹ شامل کرنے کے لیے تھپتھپائیں۔", "app.components.CustomFieldsForm.tapToAddAnArea": "ایک علاقہ شامل کرنے کے لیے تھپتھپائیں۔", "app.components.CustomFieldsForm.titleMaxLength": "عنوان زیادہ سے زیادہ {max} حروف کا ہونا چاہیے۔", "app.components.CustomFieldsForm.titleMinLength": "عنوان کم از کم {min} حروف کا ہونا چاہیے۔", "app.components.CustomFieldsForm.titleRequired": "عنوان درکار ہے۔", "app.components.CustomFieldsForm.topicRequired": "کم از کم ایک ٹیگ درکار ہے۔", "app.components.CustomFieldsForm.typeYourAnswer": "اپنا جواب ٹائپ کریں۔", "app.components.CustomFieldsForm.typeYourAnswerRequired": "آپ کا جواب ٹائپ کرنا ضروری ہے۔", "app.components.CustomFieldsForm.uploadShapefileInstructions": "* ایک زپ فائل اپ لوڈ کریں جس میں ایک یا زیادہ شکل فائلیں ہوں۔", "app.components.CustomFieldsForm.validCordinatesTooltip2": "اگر آپ کے ٹائپ کرتے وقت آپشنز میں مقام ظاہر نہیں ہوتا ہے، تو آپ درست مقام کی وضاحت کے لیے 'عرض البلد، طول البلد' میں درست نقاط شامل کر سکتے ہیں (مثال کے طور پر: -33.019808، -71.495676)۔", "app.components.ErrorBoundary.errorFormErrorFormEntry": "کچھ فیلڈز غلط تھیں۔ براہ کرم غلطیاں درست کریں اور دوبارہ کوشش کریں۔", "app.components.ErrorBoundary.errorFormErrorGeneric": "آپ کی رپورٹ جمع کرواتے وقت ایک نامعلوم خرابی پیش آگئی۔ براہ کرم دوبارہ کوشش کریں۔", "app.components.ErrorBoundary.errorFormLabelClose": "بند", "app.components.ErrorBoundary.errorFormLabelComments": "کیا ہوا؟", "app.components.ErrorBoundary.errorFormLabelEmail": "ای میل", "app.components.ErrorBoundary.errorFormLabelName": "نام", "app.components.ErrorBoundary.errorFormLabelSubmit": "جمع کروائیں۔", "app.components.ErrorBoundary.errorFormSubtitle": "ہماری ٹیم کو مطلع کر دیا گیا ہے۔", "app.components.ErrorBoundary.errorFormSubtitle2": "اگر آپ چاہتے ہیں کہ ہم مدد کریں تو نیچے ہمیں بتائیں کہ کیا ہوا ہے۔", "app.components.ErrorBoundary.errorFormSuccessMessage": "آپ کی رائے بھیج دی گئی ہے۔ شکریہ!", "app.components.ErrorBoundary.errorFormTitle": "ایسا لگتا ہے کہ کوئی مسئلہ ہے۔", "app.components.ErrorBoundary.genericErrorWithForm": "ایک خرابی پیش آگئی اور ہم اس مواد کو ظاہر نہیں کر سکتے۔ براہ کرم دوبارہ کوشش کریں، یا {openForm}", "app.components.ErrorBoundary.openFormText": "اس کا پتہ لگانے میں ہماری مدد کریں۔", "app.components.ErrorToast.budgetExceededError": "آپ کے پاس کافی بجٹ نہیں ہے۔", "app.components.ErrorToast.votesExceededError": "آپ کے پاس کافی ووٹ باقی نہیں ہیں۔", "app.components.EventAttendanceButton.forwardToFriend": "کسی دوست کو فارورڈ کریں۔", "app.components.EventAttendanceButton.maxRegistrationsReached": "ایونٹ کی رجسٹریشن کی زیادہ سے زیادہ تعداد تک پہنچ گئی ہے۔ کوئی دھبہ باقی نہیں ہے۔", "app.components.EventAttendanceButton.register": "رجسٹر کریں۔", "app.components.EventAttendanceButton.registered": "رجسٹرڈ", "app.components.EventAttendanceButton.seeYouThere": "وہاں ملتے ہیں!", "app.components.EventAttendanceButton.seeYouThereName": "وہاں ملتے ہیں، {userFirstName}!", "app.components.EventCard.a11y_lessContentVisible": "واقعہ کی کم معلومات نظر آنے لگیں۔", "app.components.EventCard.a11y_moreContentVisible": "مزید واقعہ کی معلومات مرئی ہو گئیں۔", "app.components.EventCard.a11y_readMore": "\"{eventTitle}\" ایونٹ کے بارے میں مزید پڑھیں۔", "app.components.EventCard.endsAt": "پر ختم ہوتا ہے۔", "app.components.EventCard.readMore": "مزید پڑھیں", "app.components.EventCard.showLess": "کم دکھائیں۔", "app.components.EventCard.showMore": "مزید دکھائیں", "app.components.EventCard.startsAt": "پر شروع ہوتا ہے۔", "app.components.EventPreviews.eventPreviewContinuousTitle2": "اس منصوبے میں آنے والے اور جاری واقعات", "app.components.EventPreviews.eventPreviewTimelineTitle3": "اس مرحلے میں آنے والے اور جاری واقعات", "app.components.FileUploader.a11y_file": "فائل:", "app.components.FileUploader.a11y_filesToBeUploaded": "اپ لوڈ کی جانے والی فائلیں: {fileNames}", "app.components.FileUploader.a11y_noFiles": "کوئی فائلیں شامل نہیں کی گئیں۔", "app.components.FileUploader.a11y_removeFile": "اس فائل کو ہٹا دیں۔", "app.components.FileUploader.fileInputDescription": "فائل منتخب کرنے کے لیے کلک کریں۔", "app.components.FileUploader.fileUploadLabel": "منسلکات (زیادہ سے زیادہ 50MB)", "app.components.FileUploader.file_too_large2": "{maxSizeMb}MB سے بڑی فائلوں کی اجازت نہیں ہے۔", "app.components.FileUploader.incorrect_extension": "{fileName} ہمارے سسٹم سے تعاون یافتہ نہیں ہے، اسے اپ لوڈ نہیں کیا جائے گا۔", "app.components.FilterBoxes.a11y_allFilterSelected": "منتخب اسٹیٹس فلٹر: تمام", "app.components.FilterBoxes.a11y_numberOfInputs": "{inputsCount, plural, one {# جمع کرانا} other {# گذارشات}}", "app.components.FilterBoxes.a11y_removeFilter": "فلٹر کو ہٹا دیں۔", "app.components.FilterBoxes.a11y_selectedFilter": "منتخب کردہ اسٹیٹس فلٹر: {filter}", "app.components.FilterBoxes.a11y_selectedTopicFilters": "منتخب کردہ {numberOfSelectedTopics, plural, =0 {صفر ٹیگ فلٹرز} one {ایک ٹیگ فلٹر} other {# ٹیگ فلٹرز}}۔ {selectedTopicNames}", "app.components.FilterBoxes.all": "تمام", "app.components.FilterBoxes.areas": "علاقے کے لحاظ سے فلٹر کریں۔", "app.components.FilterBoxes.inputs": "ان پٹ", "app.components.FilterBoxes.noValuesFound": "کوئی قدر دستیاب نہیں ہے۔", "app.components.FilterBoxes.showLess": "کم دکھائیں۔", "app.components.FilterBoxes.showTagsWithNumber": "تمام دکھائیں ({numberTags})", "app.components.FilterBoxes.statusTitle": "حیثیت", "app.components.FilterBoxes.topicsTitle": "ٹی<PERSON>ز", "app.components.FiltersModal.filters": "فل<PERSON><PERSON><PERSON>", "app.components.FolderFolderCard.a11y_folderDescription": "فولڈر کی تفصیل:", "app.components.FolderFolderCard.a11y_folderTitle": "فولڈر کا عنوان:", "app.components.FolderFolderCard.archived": "محف<PERSON><PERSON> شدہ", "app.components.FolderFolderCard.numberOfProjectsInFolder": "{numberOfProjectsInFolder, plural, no {# پروجیکٹس} one {# پروجیکٹ} other {# پروجیکٹس}}", "app.components.FormBuilder.components.FieldTypeSwitcher.formHasSubmissionsWarning2": "جمع کرانے کے بعد فیلڈ کی قسم کو تبدیل نہیں کیا جا سکتا۔", "app.components.FormBuilder.components.FieldTypeSwitcher.type": "قسم", "app.components.FormBuilder.components.FormBuilderTopBar.autosave": "خودبخود محفوظ کریں۔", "app.components.FormBuilder.components.FormBuilderTopBar.autosaveTooltip4": "جب آپ فارم ایڈیٹر کھولتے ہیں تو خودکار بچت بطور ڈیفالٹ فعال ہوتی ہے۔ جب بھی آپ \"X\" بٹن کا استعمال کرتے ہوئے فیلڈ سیٹنگ پینل کو بند کرتے ہیں، یہ خود بخود ایک بچت کو متحرک کر دے گا۔", "app.components.GanttChart.timeRange.month": "م<PERSON><PERSON><PERSON>ہ", "app.components.GanttChart.timeRange.quarter": "کوارٹر", "app.components.GanttChart.timeRange.timeRangeMultiyear": "کثیر سال", "app.components.GanttChart.timeRange.year": "سال", "app.components.GanttChart.today": "آج", "app.components.GoBackButton.group.edit.goBack": "واپس جاؤ", "app.components.GoBackButton.group.edit.goBackToPreviousPage": "پچھلے صفحے پر واپس جائیں۔", "app.components.HookForm.Feedback.errorTitle": "ایک مسئلہ ہے۔", "app.components.HookForm.Feedback.submissionError": "دوبارہ کوشش کریں۔ اگر مسئلہ برقرار رہتا ہے تو ہم سے رابطہ کریں۔", "app.components.HookForm.Feedback.submissionErrorTitle": "ہماری طرف سے ایک مسئلہ تھا، معذرت", "app.components.HookForm.Feedback.successMessage": "فارم کامیابی کے ساتھ جمع ہو گیا۔", "app.components.HookForm.PasswordInput.passwordLabel": "پاس ورڈ", "app.components.HorizontalScroll.scrollLeftLabel": "بائیں سکرول کریں۔", "app.components.HorizontalScroll.scrollRightLabel": "دائیں سکرول کریں۔", "app.components.IdeaCards.a11y_ideasHaveBeenSorted": "{sortOder} خیالات لوڈ ہو گئے ہیں۔", "app.components.IdeaCards.filters": "فل<PERSON><PERSON><PERSON>", "app.components.IdeaCards.filters.mostDiscussed": "سب سے زیادہ زیر بحث", "app.components.IdeaCards.filters.newest": "نیا", "app.components.IdeaCards.filters.oldest": "پرانا", "app.components.IdeaCards.filters.popular": "سب سے زیادہ پسند کیا۔", "app.components.IdeaCards.filters.random": "بے ترتیب", "app.components.IdeaCards.filters.sortBy": "ترتیب دیں", "app.components.IdeaCards.filters.sortChangedScreenreaderMessage": "ترتیب کو تبدیل کر دیا گیا: {currentSortType}", "app.components.IdeaCards.filters.trending": "ٹرینڈنگ", "app.components.IdeaCards.showMore": "مزید دکھائیں", "app.components.IdeasMap.a11y_hideIdeaCard": "آئیڈیا کارڈ چھپائیں۔", "app.components.IdeasMap.a11y_mapTitle": "نقشہ کا جائزہ", "app.components.IdeasMap.clickOnMapToAdd": "اپنا ان پٹ شامل کرنے کے لیے نقشے پر کلک کریں۔", "app.components.IdeasMap.clickOnMapToAddAdmin2": "بطور منتظم، آپ اپنا ان پٹ شامل کرنے کے لیے نقشے پر کلک کر سکتے ہیں، چاہے یہ مرحلہ فعال نہ ہو۔", "app.components.IdeasMap.filters": "فل<PERSON><PERSON><PERSON>", "app.components.IdeasMap.multipleInputsAtLocation": "اس مقام پر متعدد ان پٹ", "app.components.IdeasMap.noFilteredResults": "آپ کے منتخب کردہ فلٹرز نے کوئی نتیجہ نہیں دیا۔", "app.components.IdeasMap.noResults": "کوئی نتیجہ نہیں ملا", "app.components.IdeasMap.or": "یا", "app.components.IdeasMap.screenReaderDislikesText": "{noOfDislikes, plural, =0 {، کوئی ناپسندیدگی نہیں۔} one {1 ناپسندیدگی۔} other {, # ناپسندیدگی۔}}", "app.components.IdeasMap.screenReaderLikesText": "{noOfLikes, plural, =0 {، کوئی پسند نہیں۔} one {، 1 پسند۔} other {, # پسند۔}}", "app.components.IdeasMap.signInLinkText": "لاگ ان", "app.components.IdeasMap.signUpLinkText": "سائن اپ کریں", "app.components.IdeasMap.submitIdea2": "ان پٹ جمع کروائیں۔", "app.components.IdeasMap.tapOnMapToAdd": "اپنا ان پٹ شامل کرنے کے لیے نقشے پر تھپتھپائیں۔", "app.components.IdeasMap.tapOnMapToAddAdmin2": "بطور منتظم، آپ اپنا ان پٹ شامل کرنے کے لیے نقشے پر ٹیپ کر سکتے ہیں، چاہے یہ مرحلہ فعال نہ ہو۔", "app.components.IdeasMap.userInputs2": "شرکاء کی طرف سے ان پٹ", "app.components.IdeasMap.xComments": "{noOfComments, plural, =0 {, کوئی تبصرہ نہیں} one {, 1 تبصرہ} other {, # تبصرے}}", "app.components.IdeasShow.bodyTitle": "تفصیل", "app.components.IdeasShow.deletePost": "حذف کریں۔", "app.components.IdeasShow.editPost": "ترمیم کریں۔", "app.components.IdeasShow.goBack": "واپس جاؤ", "app.components.IdeasShow.moreOptions": "مزی<PERSON> اختیارات", "app.components.IdeasShow.or": "یا", "app.components.IdeasShow.proposedBudgetTitle": "مجوزہ بجٹ", "app.components.IdeasShow.reportAsSpam": "سپیم کے بطور رپورٹ کریں۔", "app.components.IdeasShow.send": "بھیجیں۔", "app.components.IdeasShow.skipSharing": "اسے چھوڑ دو، میں اسے بعد میں کروں گا۔", "app.components.IdeasShowPage.signIn2": "لاگ ان کریں۔", "app.components.IdeasShowPage.sorryNoAccess": "معذرت، آپ اس صفحہ تک رسائی حاصل نہیں کر سکتے۔ اس تک رسائی کے لیے آپ کو لاگ ان یا سائن اپ کرنے کی ضرورت پڑ سکتی ہے۔", "app.components.LocationInput.noOptions": "کوئی اختیارات نہیں۔", "app.components.Modal.closeWindow": "کھڑکی بند کریں۔", "app.components.NewAuthModal.steps.ChangeEmail.enterNewEmail": "ایک نیا ای میل ایڈریس درج کریں۔", "app.components.PageNotFound.goBackToHomePage": "ہوم پیج پر واپس جائیں۔", "app.components.PageNotFound.notFoundTitle": "صفحہ نہیں ملا", "app.components.PageNotFound.pageNotFoundDescription": "مطلوبہ صفحہ نہیں مل سکا۔", "app.components.PagesForm.descriptionMissingOneLanguageError": "کم از کم ایک زبان کے لیے مواد فراہم کریں۔", "app.components.PagesForm.editContent": "مواد", "app.components.PagesForm.fileUploadLabel": "منسلکات (زیادہ سے زیادہ 50MB)", "app.components.PagesForm.fileUploadLabelTooltip": "فائلیں 50Mb سے بڑی نہیں ہونی چاہئیں۔ شامل کی گئی فائلیں اس صفحہ کے نیچے دکھائی جائیں گی۔", "app.components.PagesForm.navbarItemTitle": "navbar میں نام", "app.components.PagesForm.pageTitle": "عنوان", "app.components.PagesForm.savePage": "صفحہ محفوظ کریں۔", "app.components.PagesForm.saveSuccess": "صفحہ کامیابی کے ساتھ محفوظ ہو گیا۔", "app.components.PagesForm.titleMissingOneLanguageError": "کم از کم ایک زبان کے لیے عنوان فراہم کریں۔", "app.components.Pagination.back": "پچھلا صفحہ", "app.components.Pagination.next": "اگلا صفحہ", "app.components.ParticipationCTABars.VotingCTABar.budgetExceedsLimit": "آپ نے {votesCast}خرچ کیا، جو {votesLimit}کی حد سے زیادہ ہے۔ براہ کرم اپنی ٹوکری سے کچھ آئٹمز ہٹائیں اور دوبارہ کوشش کریں۔", "app.components.ParticipationCTABars.VotingCTABar.currencyLeft1": "{budgetLeft} / {totalBudget} بائیں", "app.components.ParticipationCTABars.VotingCTABar.minBudgetNotReached1": "اپنی ٹوکری جمع کروانے سے پہلے آپ کو کم از کم {votesMinimum} خرچ کرنے کی ضرورت ہے۔", "app.components.ParticipationCTABars.VotingCTABar.noVotesCast4": "جمع کرانے سے پہلے آپ کو کم از کم ایک آپشن منتخب کرنا ہوگا۔", "app.components.ParticipationCTABars.VotingCTABar.nothingInBasket": "آپ کو اپنی ٹوکری میں کچھ شامل کرنے کی ضرورت ہے اس سے پہلے کہ آپ اسے جمع کر سکیں۔", "app.components.ParticipationCTABars.VotingCTABar.numberOfCreditsLeft": "{votesLeft, plural, =0 {کوئی کریڈٹ باقی نہیں} other {# {totalNumberOfVotes, plural, one {1 کریڈٹ میں سے} other {# کریڈٹ}} باقی}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfPointsLeft": "{votesLeft, plural, =0 {کوئی پوائنٹ باقی نہیں} other {# {totalNumberOfVotes, plural, one {1 پوائنٹ میں سے} other {# پوائنٹس}} باقی}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfTokensLeft": "{votesLeft, plural, =0 {کوئی ٹوکن باقی نہیں} other {# {totalNumberOfVotes, plural, one {1 ٹوکن میں سے} other {# ٹوکن}} بائیں}}", "app.components.ParticipationCTABars.VotingCTABar.numberOfVotesLeft": "{votesLeft, plural, =0 {کوئی ووٹ باقی نہیں} other {# {totalNumberOfVotes, plural, one {1 ووٹ میں سے} other {# ووٹ}} باقی}}", "app.components.ParticipationCTABars.VotingCTABar.votesCast": "{votes, plural, =0 {# ووٹ} one {# ووٹ} other {# ووٹ}} کاسٹ", "app.components.ParticipationCTABars.VotingCTABar.votesExceedLimit": "آپ {votesCast} ووٹ ڈالتے ہیں، جو {votesLimit}کی حد سے زیادہ ہے۔ براہ کرم کچھ ووٹ ہٹائیں اور دوبارہ کوشش کریں۔", "app.components.ParticipationCTABars.addInput": "ان پٹ شامل کریں۔", "app.components.ParticipationCTABars.allocateBudget": "اپنا بجٹ مختص کریں۔", "app.components.ParticipationCTABars.budgetSubmitSuccess": "آپ کا بجٹ کامیابی کے ساتھ جمع کر دیا گیا ہے۔", "app.components.ParticipationCTABars.mobileProjectOpenForSubmission": "شرکت کے لیے کھولیں۔", "app.components.ParticipationCTABars.poll": "رائے شماری کریں۔", "app.components.ParticipationCTABars.reviewDocument": "دستاویز کا جائزہ لیں۔", "app.components.ParticipationCTABars.seeContributions": "شراکتیں دیکھیں", "app.components.ParticipationCTABars.seeEvents3": "واقعات دیکھیں", "app.components.ParticipationCTABars.seeIdeas": "آئیڈیاز دیکھیں", "app.components.ParticipationCTABars.seeInitiatives": "اقدامات دیکھیں", "app.components.ParticipationCTABars.seeIssues": "مسائل دیکھیں", "app.components.ParticipationCTABars.seeOptions": "اختیارات دیکھیں", "app.components.ParticipationCTABars.seePetitions": "درخواستیں دیکھیں", "app.components.ParticipationCTABars.seeProjects": "پروجیکٹس دیکھیں", "app.components.ParticipationCTABars.seeProposals": "تجاویز دیکھیں", "app.components.ParticipationCTABars.seeQuestions": "سوالات دیکھیں", "app.components.ParticipationCTABars.submit": "جمع کروائیں۔", "app.components.ParticipationCTABars.takeTheSurvey": "سروے میں حصہ لیں۔", "app.components.ParticipationCTABars.userHasParticipated": "آپ نے اس منصوبے میں حصہ لیا ہے۔", "app.components.ParticipationCTABars.viewInputs": "ان پٹ دیکھیں", "app.components.ParticipationCTABars.volunteer": "رضا<PERSON>ار", "app.components.ParticipationCTABars.votesCounter.vote": "ووٹ", "app.components.ParticipationCTABars.votesCounter.votes": "ووٹ", "app.components.PasswordInput.a11y_passwordHidden": "پاس ورڈ پوشیدہ ہے۔", "app.components.PasswordInput.a11y_passwordVisible": "پاس ورڈ نظر آتا ہے۔", "app.components.PasswordInput.a11y_strength1Password": "پاس ورڈ کی کمزور طاقت", "app.components.PasswordInput.a11y_strength2Password": "کمزور پاس ورڈ کی طاقت", "app.components.PasswordInput.a11y_strength3Password": "درمیانی پاس ورڈ کی طاقت", "app.components.PasswordInput.a11y_strength4Password": "مضبوط پاس ورڈ کی طاقت", "app.components.PasswordInput.a11y_strength5Password": "پاس ورڈ کی بہت مضبوط طاقت", "app.components.PasswordInput.hidePassword": "پاس ورڈ چھپائیں۔", "app.components.PasswordInput.initialPasswordStrengthCheckerMessage": "بہت چھوٹا (کم سے کم {minimumPasswordLength} حروف)", "app.components.PasswordInput.minimumPasswordLengthError": "ایک پاس ورڈ فراہم کریں جو کم از کم {minimumPasswordLength} حروف کا ہو۔", "app.components.PasswordInput.passwordEmptyError": "اپنا پاس ورڈ درج کریں۔", "app.components.PasswordInput.passwordStrengthTooltip1": "اپنے پاس ورڈ کو مضبوط بنانے کے لیے:", "app.components.PasswordInput.passwordStrengthTooltip2": "غیر متواتر چھوٹے حروف، بڑے حروف، ہندسوں، خصوصی حروف اور اوقاف کا مجموعہ استعمال کریں", "app.components.PasswordInput.passwordStrengthTooltip3": "عام یا آسانی سے اندازہ لگانے والے الفاظ سے پرہیز کریں۔", "app.components.PasswordInput.passwordStrengthTooltip4": "لمبائی میں اضافہ کریں۔", "app.components.PasswordInput.showPassword": "پاس ورڈ دکھائیں۔", "app.components.PasswordInput.strength1Password": "<PERSON>ریب", "app.components.PasswordInput.strength2Password": "کمزور", "app.components.PasswordInput.strength3Password": "درمیانہ", "app.components.PasswordInput.strength4Password": "مض<PERSON>وط", "app.components.PasswordInput.strength5Password": "بہت مضبوط", "app.components.PostCardsComponents.list": "فہرست", "app.components.PostCardsComponents.map": "نقشہ", "app.components.PostComponents.OfficialFeedback.addOfficalUpdate": "ایک سرکاری اپ ڈیٹ شامل کریں۔", "app.components.PostComponents.OfficialFeedback.cancel": "منسوخ کریں۔", "app.components.PostComponents.OfficialFeedback.deleteOfficialFeedbackPost": "حذف کریں۔", "app.components.PostComponents.OfficialFeedback.deletionConfirmation": "کیا آپ واقعی اس آفیشل اپ ڈیٹ کو حذف کرنا چاہتے ہیں؟", "app.components.PostComponents.OfficialFeedback.editOfficialFeedbackPost": "ترمیم کریں۔", "app.components.PostComponents.OfficialFeedback.lastEdition": "آخری ترمیم {date}کو ہوئی۔", "app.components.PostComponents.OfficialFeedback.lastUpdate": "آخری اپ ڈیٹ: {lastUpdateDate}", "app.components.PostComponents.OfficialFeedback.official": "سرکاری", "app.components.PostComponents.OfficialFeedback.officialNamePlaceholder": "منتخب کریں کہ لوگ آپ کا نام کیسے دیکھتے ہیں۔", "app.components.PostComponents.OfficialFeedback.officialUpdateAuthor": "باضابطہ اپ ڈیٹ مصنف کا نام", "app.components.PostComponents.OfficialFeedback.officialUpdateBody": "باڈی ٹیکسٹ کو باضابطہ اپ ڈیٹ کریں۔", "app.components.PostComponents.OfficialFeedback.officialUpdates": "سرکاری اپ ڈیٹس", "app.components.PostComponents.OfficialFeedback.postedOn": "{date}پر پوسٹ کیا گیا۔", "app.components.PostComponents.OfficialFeedback.publishButtonText": "شائع کریں۔", "app.components.PostComponents.OfficialFeedback.showPreviousUpdates": "پچھلی اپ ڈیٹس دکھائیں۔", "app.components.PostComponents.OfficialFeedback.textAreaPlaceholder": "اپڈیٹ دیں...", "app.components.PostComponents.OfficialFeedback.updateButtonError": "معذرت، ایک مسئلہ تھا۔", "app.components.PostComponents.OfficialFeedback.updateButtonSaveEditForm": "پیغام کو اپ ڈیٹ کریں۔", "app.components.PostComponents.OfficialFeedback.updateMessageSuccess": "آپ کی تازہ کاری کامیابی کے ساتھ شائع ہوئی!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingBody": "{postUrl}پر میری شراکت '{postTitle}' کی حمایت کریں!", "app.components.PostComponents.SharingModalContent.contributionEmailSharingSubject": "میری شراکت کی حمایت کریں: {postTitle}.", "app.components.PostComponents.SharingModalContent.contributionWhatsAppMessage": "میری شراکت کی حمایت کریں: {postTitle}.", "app.components.PostComponents.SharingModalContent.ideaEmailSharingBody": "{postUrl}پر میرے خیال '{postTitle}' کی حمایت کریں!", "app.components.PostComponents.SharingModalContent.ideaEmailSharingSubjectText": "میرے خیال کی حمایت کریں: {postTitle}", "app.components.PostComponents.SharingModalContent.ideaWhatsAppMessage": "میرے خیال کی حمایت کریں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingBody": "اس تجویز کے بارے میں آپ کا کیا خیال ہے؟ اس پر ووٹ دیں اور اپنی آواز سننے کے لیے {postUrl} پر بحث کا اشتراک کریں!", "app.components.PostComponents.SharingModalContent.initiativeEmailSharingSubject": "میری تجویز کی حمایت کریں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.initiativeWhatsAppMessage": "میرے اقدام کی حمایت کریں: {postTitle}.", "app.components.PostComponents.SharingModalContent.issueEmailSharingBody": "میں نے {postUrl}پر ایک تبصرہ '{postTitle}' پوسٹ کیا !", "app.components.PostComponents.SharingModalContent.issueEmailSharingSubject": "میں نے ابھی ایک تبصرہ پوسٹ کیا ہے: {postTitle}۔", "app.components.PostComponents.SharingModalContent.issueWhatsAppMessage": "میں نے ابھی ایک تبصرہ پوسٹ کیا ہے: {postTitle}۔", "app.components.PostComponents.SharingModalContent.optionEmailSharingBody": "{postUrl}پر میرے مجوزہ آپشن '{postTitle}' کی حمایت کریں!", "app.components.PostComponents.SharingModalContent.optionEmailSharingSubject": "میرے تجویز کردہ آپشن کی حمایت کریں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.optionWhatsAppMessage": "میرے اختیار کی حمایت کریں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.petitionEmailSharingBody": "{postUrl}پر میری پٹیشن '{postTitle}' کی حمایت کریں!", "app.components.PostComponents.SharingModalContent.petitionEmailSharingSubject": "میری درخواست کی حمایت کریں: {postTitle}.", "app.components.PostComponents.SharingModalContent.petitionWhatsAppMessage": "میری درخواست کی حمایت کریں: {postTitle}.", "app.components.PostComponents.SharingModalContent.projectEmailSharingBody": "{postUrl}پر میرے پروجیکٹ '{postTitle}' کی حمایت کریں!", "app.components.PostComponents.SharingModalContent.projectEmailSharingSubject": "میرے پروجیکٹ کی حمایت کریں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.projectWhatsAppMessage": "میرے پروجیکٹ کی حمایت کریں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.proposalEmailSharingBody": "{postUrl}پر میری تجویز '{postTitle}' کی حمایت کریں!", "app.components.PostComponents.SharingModalContent.proposalEmailSharingSubject": "میری تجویز کی حمایت کریں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.proposalWhatsAppMessage": "میں نے ابھی {orgName}کے لیے ایک تجویز پوسٹ کی ہے : {postTitle}", "app.components.PostComponents.SharingModalContent.questionEmailSharingModalContentBody": "اس سوال کے بارے میں بحث میں شامل ہوں '{postTitle}' {postUrl}پر !", "app.components.PostComponents.SharingModalContent.questionEmailSharingSubject": "بحث میں شامل ہوں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.questionWhatsAppMessage": "بحث میں شامل ہوں: {postTitle}۔", "app.components.PostComponents.SharingModalContent.twitterMessage": "{postTitle} کو ووٹ دیں۔", "app.components.PostComponents.linkToHomePage": "ہوم پیج سے لنک کریں۔", "app.components.PostComponents.readMore": "مزید پڑھیں...", "app.components.PostComponents.topics": "موضوعات", "app.components.ProjectArchivedIndicator.archivedProject": "بدقسمتی سے، آپ اس پروجیکٹ میں مزید حصہ نہیں لے سکتے کیونکہ اسے آرکائیو کر دیا گیا ہے۔", "app.components.ProjectArchivedIndicator.previewProject": "ڈرافٹ پروجیکٹ:", "app.components.ProjectArchivedIndicator.previewProjectExplanation": "صرف ماڈریٹرز اور پیش نظارہ لنک والے افراد کے لیے مرئی۔", "app.components.ProjectCard.a11y_projectDescription": "پروجیکٹ کی تفصیل:", "app.components.ProjectCard.a11y_projectTitle": "پروجیکٹ کا عنوان:", "app.components.ProjectCard.addYourOption": "اپنا اختیار شامل کریں۔", "app.components.ProjectCard.allocateYourBudget": "اپنا بجٹ مختص کریں۔", "app.components.ProjectCard.archived": "محف<PERSON><PERSON> شدہ", "app.components.ProjectCard.comment": "تبصرہ", "app.components.ProjectCard.contributeYourInput": "اپنا حصہ ڈالیں۔", "app.components.ProjectCard.finished": "ختم", "app.components.ProjectCard.joinDiscussion": "بحث میں شامل ہوں۔", "app.components.ProjectCard.learnMore": "مزید جانیں", "app.components.ProjectCard.reaction": "<PERSON><PERSON>", "app.components.ProjectCard.readTheReport": "رپورٹ پڑھیں", "app.components.ProjectCard.reviewDocument": "دستاویز کا جائزہ لیں۔", "app.components.ProjectCard.submitAnIssue": "ایک تبصرہ جمع کروائیں", "app.components.ProjectCard.submitYourIdea": "اپنا خیال پیش کریں۔", "app.components.ProjectCard.submitYourInitiative": "اپنی پہل پیش کریں۔", "app.components.ProjectCard.submitYourPetition": "اپنی درخواست جمع کروائیں۔", "app.components.ProjectCard.submitYourProject": "اپنا پروجیکٹ جمع کروائیں۔", "app.components.ProjectCard.submitYourProposal": "اپنی تجویز پیش کریں۔", "app.components.ProjectCard.takeThePoll": "رائے شماری کریں۔", "app.components.ProjectCard.takeTheSurvey": "سروے میں حصہ لیں۔", "app.components.ProjectCard.viewTheContributions": "شراکتیں دیکھیں", "app.components.ProjectCard.viewTheIdeas": "آئیڈیاز دیکھیں", "app.components.ProjectCard.viewTheInitiatives": "اقدامات دیکھیں", "app.components.ProjectCard.viewTheIssues": "تبصرے دیکھیں", "app.components.ProjectCard.viewTheOptions": "اختیارات دیکھیں", "app.components.ProjectCard.viewThePetitions": "درخواستیں دیکھیں", "app.components.ProjectCard.viewTheProjects": "پروجیکٹس دیکھیں", "app.components.ProjectCard.viewTheProposals": "تجاویز دیکھیں", "app.components.ProjectCard.viewTheQuestions": "سوالات دیکھیں", "app.components.ProjectCard.vote": "ووٹ", "app.components.ProjectCard.xComments": "{commentsCount, plural, one {# تبصرے} other {# تبصرے}}", "app.components.ProjectCard.xContributions": "{ideasCount, plural, one {# شراکت} other {# شراکتیں}}", "app.components.ProjectCard.xIdeas": "{ideasCount, plural, =0 {ابھی تک کوئی آئیڈیا نہیں} one {# idea} other {# ideas}}", "app.components.ProjectCard.xInitiatives": "{ideasCount, plural, no {# پہل} one {# پہل} other {# پہل}}", "app.components.ProjectCard.xIssues": "{ideasCount, plural, one {# تبصرہ} other {# تبصرے}}", "app.components.ProjectCard.xOptions": "{ideasCount, plural, one {# آپشن} other {# اختیارات}}", "app.components.ProjectCard.xPetitions": "{ideasCount, plural, no {# درخواستیں} one {# پٹیشن} other {# درخواستیں}}", "app.components.ProjectCard.xProjects": "{ideasCount, plural, one {# پروجیکٹ} other {# پروجیکٹ}}", "app.components.ProjectCard.xProposals": "{ideasCount, plural, no {# تجاویز} one {# تجویز} other {# تجاویز}}", "app.components.ProjectCard.xQuestions": "{ideasCount, plural, one {# سوال} other {# سوالات}}", "app.components.ProjectFolderCard.xComments": "{commentsCount, plural, no {# تبصرے} one {# تبصرے} other {# تبصرے}}", "app.components.ProjectFolderCard.xInputs": "{ideasCount, plural, no {# ان پٹ} one {# ان پٹ} other {# ان پٹ}}", "app.components.ProjectFolderCards.components.Topbar.a11y_projectFilterTabInfo": "{count, plural, no {# پروجیکٹس} one {# پروجیکٹ} other {# پروجیکٹس}}", "app.components.ProjectFolderCards.components.Topbar.all": "تمام", "app.components.ProjectFolderCards.components.Topbar.archived": "محف<PERSON><PERSON> شدہ", "app.components.ProjectFolderCards.components.Topbar.draft": "مسودہ", "app.components.ProjectFolderCards.components.Topbar.filterBy": "کے لحاظ سے فلٹر کریں۔", "app.components.ProjectFolderCards.components.Topbar.published2": "شائع شدہ", "app.components.ProjectFolderCards.components.Topbar.topicTitle": "ٹیگ", "app.components.ProjectFolderCards.noProjectYet": "فی الحال کوئی کھلا پروجیکٹ نہیں ہے۔", "app.components.ProjectFolderCards.noProjectsAvailable": "کوئی پروجیکٹ دستیاب نہیں ہے۔", "app.components.ProjectFolderCards.showMore": "مزید دکھائیں", "app.components.ProjectFolderCards.stayTuned": "مصروفیت کے نئے مواقع کے لیے دوبارہ چیک کریں۔", "app.components.ProjectFolderCards.tryChangingFilters": "منتخب فلٹرز کو تبدیل کرنے کی کوشش کریں۔", "app.components.ProjectTemplatePreview.alsoUsedIn": "ان شہروں میں بھی استعمال کیا جاتا ہے:", "app.components.ProjectTemplatePreview.copied": "کاپی", "app.components.ProjectTemplatePreview.copyLink": "لنک کاپی کریں۔", "app.components.QuillEditor.alignCenter": "مرکزی متن", "app.components.QuillEditor.alignLeft": "بائیں سیدھ کریں۔", "app.components.QuillEditor.alignRight": "دائیں سیدھ کریں۔", "app.components.QuillEditor.bold": "بولڈ", "app.components.QuillEditor.clean": "فارمیٹنگ کو ہٹا دیں۔", "app.components.QuillEditor.customLink": "بٹن شامل کریں۔", "app.components.QuillEditor.customLinkPrompt": "لنک درج کریں:", "app.components.QuillEditor.edit": "ترمیم کریں۔", "app.components.QuillEditor.image": "تصویر اپ لوڈ کریں۔", "app.components.QuillEditor.imageAltPlaceholder": "تصویر کی مختصر تفصیل", "app.components.QuillEditor.italic": "ترچھا", "app.components.QuillEditor.link": "لنک شامل کریں۔", "app.components.QuillEditor.linkPrompt": "لنک درج کریں:", "app.components.QuillEditor.normalText": "نارمل", "app.components.QuillEditor.orderedList": "آرڈر شدہ فہرست", "app.components.QuillEditor.remove": "ہٹا دیں۔", "app.components.QuillEditor.save": "محفوظ کریں۔", "app.components.QuillEditor.subtitle": "ذی<PERSON>ی عنوان", "app.components.QuillEditor.title": "عنوان", "app.components.QuillEditor.unorderedList": "غیر ترتیب شدہ فہرست", "app.components.QuillEditor.video": "ویڈیو شامل کریں۔", "app.components.QuillEditor.videoPrompt": "ویڈیو درج کریں:", "app.components.QuillEditor.visitPrompt": "لنک پر جائیں:", "app.components.ReactionControl.completeProfileToReact": "رد عمل ظاہر کرنے کے لیے اپنا پروفائل مکمل کریں۔", "app.components.ReactionControl.dislike": "ناپسندیدگی", "app.components.ReactionControl.dislikingDisabledMaxReached": "آپ {projectName}میں اپنی ناپسندیدگیوں کی زیادہ سے زیادہ تعداد تک پہنچ گئے ہیں۔", "app.components.ReactionControl.like": "پسند", "app.components.ReactionControl.likingDisabledMaxReached": "آپ {projectName}میں اپنی پسند کی زیادہ سے زیادہ تعداد تک پہنچ گئے ہیں۔", "app.components.ReactionControl.reactingDisabledFutureEnabled": "یہ مرحلہ شروع ہونے کے بعد ردعمل کو فعال کر دیا جائے گا۔", "app.components.ReactionControl.reactingDisabledPhaseOver": "اس مرحلے میں ردعمل ظاہر کرنا اب ممکن نہیں ہے۔", "app.components.ReactionControl.reactingDisabledProjectInactive": "آپ {projectName}میں خیالات پر مزید ردعمل ظاہر نہیں کر سکتے", "app.components.ReactionControl.reactingNotEnabled": "رد عمل فی الحال اس پروجیکٹ کے لیے فعال نہیں ہے۔", "app.components.ReactionControl.reactingNotPermitted": "ردعمل صرف مخصوص گروپوں کے لیے فعال ہے۔", "app.components.ReactionControl.reactingNotSignedIn": "ردعمل ظاہر کرنے کے لیے سائن ان کریں۔", "app.components.ReactionControl.reactingPossibleLater": "رد عمل {enabledFromDate}پر شروع ہوگا۔", "app.components.ReactionControl.reactingVerifyToReact": "رد عمل ظاہر کرنے کے لیے اپنی شناخت کی تصدیق کریں۔", "app.components.ScreenReadableEventDate..multiDayScreenReaderDate": "ایونٹ کی تاریخ: {startDate} at {startTime} to {endDate} at {endTime}۔", "app.components.ScreenReadableEventDate..singleDayScreenReaderDate": "ایونٹ کی تاریخ: {eventDate} {startTime} سے {endTime}تک۔", "app.components.Sharing.linkCopied": "لنک کاپی ہو گیا۔", "app.components.Sharing.or": "یا", "app.components.Sharing.share": "شیئر کریں۔", "app.components.Sharing.shareByEmail": "ای میل کے ذریعے شیئر کریں۔", "app.components.Sharing.shareByLink": "لنک کاپی کریں۔", "app.components.Sharing.shareOnFacebook": "فیس بک پر شیئر کریں۔", "app.components.Sharing.shareOnTwitter": "ٹویٹر پر شیئر کریں۔", "app.components.Sharing.shareThisEvent": "اس واقعہ کو شیئر کریں۔", "app.components.Sharing.shareThisFolder": "شیئر کریں۔", "app.components.Sharing.shareThisProject": "اس پروجیکٹ کو شیئر کریں۔", "app.components.Sharing.shareViaMessenger": "میسنجر کے ذریعے شیئر کریں۔", "app.components.Sharing.shareViaWhatsApp": "واٹس ایپ کے ذریعے شیئر کریں۔", "app.components.SideModal.closeButtonAria": "بند", "app.components.StatusModule.futurePhase": "آپ ایک ایسا مرحلہ دیکھ رہے ہیں جو ابھی شروع نہیں ہوا ہے۔ مرحلہ شروع ہونے پر آپ شرکت کر سکیں گے۔", "app.components.StatusModule.modifyYourSubmission1": "اپنی جمع کرانے میں ترمیم کریں۔", "app.components.StatusModule.submittedUntil3": "آپ کا ووٹ تک جمع کرایا جا سکتا ہے۔", "app.components.TopicsPicker.numberOfSelectedTopics": "منتخب کردہ {numberOfSelectedTopics, plural, =0 {صفر ٹیگز} one {ایک ٹیگ} other {# ٹیگز}}۔ {selectedTopicNames}", "app.components.UI.FullscreenImage.expandImage": "تصویر کو پھیلائیں۔", "app.components.UI.MoreActionsMenu.moreOptions": "مزی<PERSON> اختیارات", "app.components.UI.MoreActionsMenu.showMoreActions": "مزید کارروائیاں دکھائیں۔", "app.components.UI.PhaseFilter.noAppropriatePhases": "اس منصوبے کے لیے کوئی مناسب مراحل نہیں ملے", "app.components.UI.RemoveImageButton.a11y_removeImage": "ہٹا دیں۔", "app.components.UI.TranslateButton.original": "اصل", "app.components.UI.TranslateButton.translate": "ترجمہ کریں۔", "app.components.Unauthorized.additionalInformationRequired": "آپ کو شرکت کرنے کے لیے اضافی معلومات درکار ہیں۔", "app.components.Unauthorized.completeProfile": "مکمل پروفائل", "app.components.Unauthorized.completeProfileTitle": "حصہ لینے کے لیے اپنا پروفائل مکمل کریں۔", "app.components.Unauthorized.noPermission": "آپ کو یہ صفحہ دیکھنے کی اجازت نہیں ہے۔", "app.components.Unauthorized.notAuthorized": "معذرت، آپ اس صفحہ تک رسائی کے مجاز نہیں ہیں۔", "app.components.Upload.errorImageMaxSizeExceeded": "آپ کی منتخب کردہ تصویر {maxFileSize}MB سے بڑی ہے۔", "app.components.Upload.errorImagesMaxSizeExceeded": "آپ کی منتخب کردہ ایک یا متعدد تصاویر {maxFileSize}MB سے بڑی ہیں۔", "app.components.Upload.onlyOneImage": "آپ صرف 1 تصویر اپ لوڈ کر سکتے ہیں۔", "app.components.Upload.onlyXImages": "آپ صرف {maxItemsCount} تصاویر اپ لوڈ کر سکتے ہیں۔", "app.components.Upload.remaining": "باقی", "app.components.Upload.uploadImageLabel": "ایک تصویر منتخب کریں (زیادہ سے زیادہ {maxImageSizeInMb}MB)", "app.components.Upload.uploadMultipleImagesLabel": "ایک یا زیادہ تصاویر منتخب کریں۔", "app.components.UpsellTooltip.tooltipContent": "یہ خصوصیت آپ کے موجودہ پلان میں شامل نہیں ہے۔ اسے غیر مقفل کرنے کے لیے اپنے حکومتی کامیابی کے مینیجر یا منتظم سے بات کریں۔", "app.components.UserName.anonymous": "گمنام", "app.components.UserName.anonymousTooltip2": "اس صارف نے اپنے تعاون کو گمنام کرنے کا فیصلہ کیا ہے۔", "app.components.UserName.authorWithNoNameTooltip": "آپ کا نام خود بخود تیار ہو گیا ہے کیونکہ آپ نے اپنا نام درج نہیں کیا ہے۔ اگر آپ اسے تبدیل کرنا چاہتے ہیں تو براہ کرم اپنا پروفائل اپ ڈیٹ کریں۔", "app.components.UserName.deletedUser": "نامعلوم مصنف", "app.components.UserName.verified": "تصدیق شدہ", "app.components.VerificationModal.verifyAuth0": "NemID کے ساتھ تصدیق کریں۔", "app.components.VerificationModal.verifyBOSA": "اس کی می یا ای آئی ڈی سے تصدیق کریں۔", "app.components.VerificationModal.verifyBosaFas": "اس کی می یا ای آئی ڈی سے تصدیق کریں۔", "app.components.VerificationModal.verifyClaveUnica": "Clave Unica کے ساتھ تصدیق کریں۔", "app.components.VerificationModal.verifyFakeSSO": "جعلی SSO سے تصدیق کریں۔", "app.components.VerificationModal.verifyIdAustria": "ID آسٹریا کے ساتھ تصدیق کریں۔", "app.components.VerificationModal.verifyKeycloak": "ID-Porten کے ساتھ تصدیق کریں۔", "app.components.VerificationModal.verifyNemLogIn": "MitID کے ساتھ تصدیق کریں۔", "app.components.VerificationModal.verifyTwoday2": "BankID یا Freja eID+ کے ساتھ تصدیق کریں۔", "app.components.VerificationModal.verifyYourIdentity": "اپنی شناخت کی تصدیق کریں۔", "app.components.VoteControl.budgetingFutureEnabled": "آپ اپنا بجٹ {enabledFromDate}سے شروع کر کے مختص کر سکتے ہیں۔", "app.components.VoteControl.budgetingNotPermitted": "شراکتی بجٹ فی الحال فعال نہیں ہے۔", "app.components.VoteControl.budgetingNotPossible": "اپنے بجٹ میں تبدیلیاں کرنا اس وقت ممکن نہیں ہے۔", "app.components.VoteControl.budgetingNotVerified": "جاری رکھنے کے لیے براہ کرم {verifyAccountLink} ۔", "app.components.VoteInputs._shared.currencyLeft1": "آپ کے پاس {budgetLeft} / {totalBudget} باقی ہے۔", "app.components.VoteInputs._shared.numberOfCreditsLeft": "آپ کے پاس {votesLeft, plural, =0 {کوئی کریڈٹ باقی نہیں ہے} other {# {totalNumberOfVotes, plural, one {1 کریڈٹ میں سے} other {# کریڈٹ}} باقی}}۔", "app.components.VoteInputs._shared.numberOfPointsLeft": "آپ کے پاس {votesLeft, plural, =0 {کوئی پوائنٹ باقی نہیں} other {# {totalNumberOfVotes, plural, one {1 پوائنٹس} other {# پوائنٹس}} بائیں}}۔", "app.components.VoteInputs._shared.numberOfTokensLeft": "آپ کے پاس {votesLeft, plural, =0 {کوئی ٹوکن نہیں بچا ہے} other {# {totalNumberOfVotes, plural, one {1 ٹوکن میں سے} other {# ٹوکن}} بائیں}}۔", "app.components.VoteInputs._shared.numberOfVotesLeft": "آپ کے پاس {votesLeft, plural, =0 {کوئی ووٹ باقی نہیں} other {# {totalNumberOfVotes, plural, one {1 ووٹوں میں سے} other {# ووٹ}} باقی ہیں}}۔", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmitted1": "آپ اپنا بجٹ پہلے ہی جمع کر چکے ہیں۔ اس میں ترمیم کرنے کے لیے، \"اپنی جمع کرانے میں ترمیم کریں\" پر کلک کریں۔", "app.components.VoteInputs.budgeting.AddToBasketButton.basketAlreadySubmittedIdeaPage1": "آپ اپنا بجٹ پہلے ہی جمع کر چکے ہیں۔ اس میں ترمیم کرنے کے لیے، پراجیکٹ کے صفحہ پر واپس جائیں اور \"اپنی جمع کرانے میں ترمیم کریں\" پر کلک کریں۔", "app.components.VoteInputs.budgeting.AddToBasketButton.phaseNotActive": "بجٹ دستیاب نہیں ہے، کیونکہ یہ مرحلہ فعال نہیں ہے۔", "app.components.VoteInputs.single.youHaveVotedForX2": "آپ نے {votes, plural, =0 {# اختیارات} one {# آپشن} other {# اختیارات}}کو ووٹ دیا ہے۔", "app.components.admin.PostManager.components.ActionBar.deleteInputExplanation": "اس کا مطلب ہے کہ آپ اس ان پٹ سے وابستہ تمام ڈیٹا سے محروم ہو جائیں گے، جیسے تبصرے، ردعمل اور ووٹ۔ اس کارروائی کو کالعدم نہیں کیا جا سکتا۔", "app.components.admin.PostManager.components.ActionBar.deleteInputTitle": "کیا آپ واقعی اس ان پٹ کو حذف کرنا چاہتے ہیں؟", "app.components.admin.PostManager.components.PostTable.Row.cancel": "منسوخ کریں۔", "app.components.admin.PostManager.components.PostTable.Row.confirm": "تصدیق کریں۔", "app.components.admin.SlugInput.resultingURL": "نتی<PERSON><PERSON> خیز URL", "app.components.admin.SlugInput.slugTooltip": "سلگ صفحہ کے ویب ایڈریس، یا URL کے آخر میں الفاظ کا منفرد مجموعہ ہے۔", "app.components.admin.SlugInput.urlSlugBrokenLinkWarning": "اگر آپ URL کو تبدیل کرتے ہیں، تو پرانے URL کو استعمال کرنے والے صفحہ کے لنکس مزید کام نہیں کریں گے۔", "app.components.admin.SlugInput.urlSlugLabel": "سلگ", "app.components.admin.UserFilterConditions.addCondition": "ایک شرط شامل کریں۔", "app.components.admin.UserFilterConditions.field_email": "ای میل", "app.components.admin.UserFilterConditions.field_event_attendance": "ایونٹ کی رجسٹریشنز", "app.components.admin.UserFilterConditions.field_follow": "پیروی کریں۔", "app.components.admin.UserFilterConditions.field_lives_in": "میں رہتا ہے۔", "app.components.admin.UserFilterConditions.field_participated_in_community_monitor": "کمیونٹی مانیٹر سروے", "app.components.admin.UserFilterConditions.field_participated_in_input_status": "حیثیت کے ساتھ ایک ان پٹ کے ساتھ تعامل کیا۔", "app.components.admin.UserFilterConditions.field_participated_in_project": "پروجیکٹ میں تعاون کیا۔", "app.components.admin.UserFilterConditions.field_participated_in_topic": "ٹیگ کے ساتھ کچھ پوسٹ کیا۔", "app.components.admin.UserFilterConditions.field_registration_completed_at": "رجسٹریشن کی تاریخ", "app.components.admin.UserFilterConditions.field_role": "کردار", "app.components.admin.UserFilterConditions.field_verified": "تصدیق", "app.components.admin.UserFilterConditions.ideaStatusMethodIdeation": "<PERSON><PERSON><PERSON><PERSON>", "app.components.admin.UserFilterConditions.ideaStatusMethodProposals": "تجاو<PERSON>ز", "app.components.admin.UserFilterConditions.predicate_attends_none_of": "ان واقعات میں سے کسی کے لیے رجسٹرڈ نہیں ہے۔", "app.components.admin.UserFilterConditions.predicate_attends_nothing": "کسی بھی تقریب کے لیے رجسٹرڈ نہیں ہے۔", "app.components.admin.UserFilterConditions.predicate_attends_some_of": "ان واقعات میں سے ایک کے لیے رجسٹرڈ ہے۔", "app.components.admin.UserFilterConditions.predicate_attends_something": "کم از کم ایک ایونٹ کے لیے رجسٹرڈ ہے۔", "app.components.admin.UserFilterConditions.predicate_begins_with": "کے ساتھ شروع ہوتا ہے", "app.components.admin.UserFilterConditions.predicate_commented_in": "تبصرہ کیا", "app.components.admin.UserFilterConditions.predicate_contains": "پر مشتمل ہے", "app.components.admin.UserFilterConditions.predicate_ends_on": "پر ختم ہوتا ہے", "app.components.admin.UserFilterConditions.predicate_has_value": "قدر ہے", "app.components.admin.UserFilterConditions.predicate_in": "کوئی کارروائی کی", "app.components.admin.UserFilterConditions.predicate_is": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_admin": "ایک ایڈمن ہے", "app.components.admin.UserFilterConditions.predicate_is_after": "کے بعد ہے", "app.components.admin.UserFilterConditions.predicate_is_before": "پہلے ہے", "app.components.admin.UserFilterConditions.predicate_is_checked": "چیک کیا جاتا ہے", "app.components.admin.UserFilterConditions.predicate_is_empty": "خالی ہے", "app.components.admin.UserFilterConditions.predicate_is_equal": "<PERSON><PERSON>", "app.components.admin.UserFilterConditions.predicate_is_exactly": "بالکل ہے", "app.components.admin.UserFilterConditions.predicate_is_larger_than": "سے بڑا ہے", "app.components.admin.UserFilterConditions.predicate_is_larger_than_or_equal": "سے بڑا یا اس کے برابر ہے۔", "app.components.admin.UserFilterConditions.predicate_is_normal_user": "ایک عام صارف ہے۔", "app.components.admin.UserFilterConditions.predicate_is_not_area": "علاقے کو خارج کر دیتا ہے", "app.components.admin.UserFilterConditions.predicate_is_not_folder": "فولڈر کو خارج کرتا ہے۔", "app.components.admin.UserFilterConditions.predicate_is_not_input": "ان پٹ کو خارج کرتا ہے۔", "app.components.admin.UserFilterConditions.predicate_is_not_project": "پروجیکٹ کو خارج کرتا ہے۔", "app.components.admin.UserFilterConditions.predicate_is_not_topic": "موضوع کو خارج کرتا ہے۔", "app.components.admin.UserFilterConditions.predicate_is_one_of": "میں سے ایک ہے", "app.components.admin.UserFilterConditions.predicate_is_one_of_areas": "علاقوں میں سے ایک", "app.components.admin.UserFilterConditions.predicate_is_one_of_folders": "فولڈرز میں سے ایک", "app.components.admin.UserFilterConditions.predicate_is_one_of_inputs": "ان پٹ میں سے ایک", "app.components.admin.UserFilterConditions.predicate_is_one_of_projects": "منصوبوں میں سے ایک", "app.components.admin.UserFilterConditions.predicate_is_one_of_topics": "موضوعات میں سے ایک", "app.components.admin.UserFilterConditions.predicate_is_project_moderator": "پروجیکٹ مینیجر ہے۔", "app.components.admin.UserFilterConditions.predicate_is_smaller_than": "سے چھوٹا ہے", "app.components.admin.UserFilterConditions.predicate_is_smaller_than_or_equal": "سے چھوٹا یا اس کے برابر ہے۔", "app.components.admin.UserFilterConditions.predicate_is_verified": "تصدیق شدہ ہے", "app.components.admin.UserFilterConditions.predicate_not_begins_with": "کے ساتھ شروع نہیں ہوتا", "app.components.admin.UserFilterConditions.predicate_not_commented_in": "تبصرہ نہیں کیا", "app.components.admin.UserFilterConditions.predicate_not_contains": "پر مشتمل نہیں ہے", "app.components.admin.UserFilterConditions.predicate_not_ends_on": "پر ختم نہیں ہوتا", "app.components.admin.UserFilterConditions.predicate_not_has_value": "قدر نہیں ہے", "app.components.admin.UserFilterConditions.predicate_not_in": "تعاون نہیں کیا", "app.components.admin.UserFilterConditions.predicate_not_is": "نہیں ہے", "app.components.admin.UserFilterConditions.predicate_not_is_admin": "ایڈمن نہیں ہے۔", "app.components.admin.UserFilterConditions.predicate_not_is_checked": "چیک نہیں کیا جاتا ہے", "app.components.admin.UserFilterConditions.predicate_not_is_empty": "خالی نہیں ہے", "app.components.admin.UserFilterConditions.predicate_not_is_equal": "نہیں ہے", "app.components.admin.UserFilterConditions.predicate_not_is_normal_user": "عام صارف نہیں ہے۔", "app.components.admin.UserFilterConditions.predicate_not_is_one_of": "میں سے ایک نہیں ہے", "app.components.admin.UserFilterConditions.predicate_not_is_project_moderator": "پروجیکٹ مینیجر نہیں ہے۔", "app.components.admin.UserFilterConditions.predicate_not_is_verified": "تصدیق شدہ نہیں ہے", "app.components.admin.UserFilterConditions.predicate_not_posted_input": "ان پٹ پوسٹ نہیں کیا۔", "app.components.admin.UserFilterConditions.predicate_not_reacted_comment_in": "تبصرہ کرنے پر کوئی ردعمل نہیں دیا", "app.components.admin.UserFilterConditions.predicate_not_reacted_input_in": "ان پٹ پر کوئی رد عمل ظاہر نہیں کیا۔", "app.components.admin.UserFilterConditions.predicate_not_registered_to_an_event": "ایک تقریب میں رجسٹر نہیں کیا", "app.components.admin.UserFilterConditions.predicate_not_taken_survey": "سروے نہیں کیا", "app.components.admin.UserFilterConditions.predicate_not_volunteered_in": "رضاکارانہ طور پر کام نہیں کیا", "app.components.admin.UserFilterConditions.predicate_not_voted_in3": "ووٹنگ میں حصہ نہیں لیا۔", "app.components.admin.UserFilterConditions.predicate_nothing": "کچھ نہیں", "app.components.admin.UserFilterConditions.predicate_posted_input": "ایک ان پٹ پوسٹ کیا", "app.components.admin.UserFilterConditions.predicate_reacted_comment_in": "تبصرہ پر ردعمل ظاہر کیا", "app.components.admin.UserFilterConditions.predicate_reacted_input_in": "ان پٹ پر رد عمل ظاہر کیا۔", "app.components.admin.UserFilterConditions.predicate_registered_to_an_event": "ایک تقریب میں رجسٹرڈ", "app.components.admin.UserFilterConditions.predicate_something": "کچھ", "app.components.admin.UserFilterConditions.predicate_taken_survey": "سروے کیا ہے", "app.components.admin.UserFilterConditions.predicate_volunteered_in": "رضاکارانہ", "app.components.admin.UserFilterConditions.predicate_voted_in3": "ووٹنگ میں حصہ لیا", "app.components.admin.UserFilterConditions.rulesFormLabelField": "وصف", "app.components.admin.UserFilterConditions.rulesFormLabelPredicate": "حالت", "app.components.admin.UserFilterConditions.rulesFormLabelValue": "قدر", "app.components.anonymousParticipationModal.anonymousParticipationWarning": "آپ کو اپنے تعاون پر اطلاعات نہیں ملیں گی۔", "app.components.anonymousParticipationModal.cancel": "منسوخ کریں۔", "app.components.anonymousParticipationModal.continue": "جاری رکھیں", "app.components.anonymousParticipationModal.participateAnonymously": "گمنام طور پر شرکت کریں۔", "app.components.anonymousParticipationModal.participateAnonymouslyModalText": "یہ <b>آپ کی پروفائل</b> کو ایڈمنز، پروجیکٹ مینیجرز اور دیگر رہائشیوں سے اس مخصوص شراکت کے لیے چھپا دے گا تاکہ کوئی بھی اس شراکت کو آپ سے منسلک نہ کر سکے۔ گمنام شراکت میں ترمیم نہیں کی جا سکتی، اور انہیں حتمی سمجھا جاتا ہے۔", "app.components.anonymousParticipationModal.participateAnonymouslyModalTextSection2": "ہمارے پلیٹ فارم کو ہر صارف کے لیے محفوظ بنانا ہماری اولین ترجیح ہے۔ الفاظ اہم ہیں، لہذا براہ کرم ایک دوسرے کے ساتھ نرمی برتیں۔", "app.components.avatar.titleForAccessibility": "{fullName}کا پروفائل", "app.components.customFields.mapInput.removeAnswer": "جواب ہٹا دیں۔", "app.components.customFields.mapInput.undo": "کالعدم", "app.components.customFields.mapInput.undoLastPoint": "آخری پوائنٹ کو کالعدم کریں۔", "app.components.followUnfollow.follow": "پیروی کریں۔", "app.components.followUnfollow.followADiscussion": "بحث پر عمل کریں۔", "app.components.followUnfollow.followTooltipInputPage2": "مندرجہ ذیل اسٹیٹس کی تبدیلیوں، آفیشل اپ ڈیٹس، اور تبصروں کے بارے میں ای میل اپ ڈیٹس کو متحرک کرتا ہے۔ آپ کسی بھی وقت {unsubscribeLink} کر سکتے ہیں۔", "app.components.followUnfollow.followTooltipProjects2": "مندرجہ ذیل منصوبے کی تبدیلیوں کے بارے میں ای میل اپ ڈیٹس کو متحرک کرتا ہے۔ آپ کسی بھی وقت {unsubscribeLink} کر سکتے ہیں۔", "app.components.followUnfollow.unFollow": "ان فالو کریں۔", "app.components.followUnfollow.unsubscribe": "رکنیت ختم کریں", "app.components.followUnfollow.unsubscribeUrl": "/profile/ترمیم کریں۔", "app.components.form.ErrorDisplay.guidelinesLinkText": "ہماری ہدایات", "app.components.form.ErrorDisplay.next": "اگلا", "app.components.form.ErrorDisplay.previous": "پچھلا", "app.components.form.ErrorDisplay.save": "محفوظ کریں۔", "app.components.form.ErrorDisplay.userPickerPlaceholder": "صارف کے ای میل یا نام سے تلاش کرنے کے لیے ٹائپ کرنا شروع کریں...", "app.components.form.anonymousSurveyMessage2": "اس سروے کے تمام جوابات گمنام ہیں۔", "app.components.form.backToInputManager": "ان پٹ مینیجر پر واپس جائیں۔", "app.components.form.backToProject": "پروجیکٹ پر واپس جائیں۔", "app.components.form.components.controls.mapInput.removeAnswer": "جواب ہٹا دیں۔", "app.components.form.components.controls.mapInput.undo": "کالعدم", "app.components.form.components.controls.mapInput.undoLastPoint": "آخری پوائنٹ کو کالعدم کریں۔", "app.components.form.controls.addressInputAriaLabel": "ایڈریس ان پٹ", "app.components.form.controls.addressInputPlaceholder6": "ایک پتہ درج کریں...", "app.components.form.controls.adminFieldTooltip": "فیلڈ صرف ایڈمنز کے لیے مرئی ہے۔", "app.components.form.controls.allStatementsError": "تمام بیانات کے لیے ایک جواب کا انتخاب کرنا ضروری ہے۔", "app.components.form.controls.back": "پیچھے", "app.components.form.controls.clearAll": "سب صاف کریں۔", "app.components.form.controls.clearAllScreenreader": "مندرجہ بالا میٹرکس سوال سے تمام جوابات صاف کریں۔", "app.components.form.controls.clickOnMapMultipleToAdd3": "ڈرا کرنے کے لیے نقشے پر کلک کریں۔ پھر، ان کو منتقل کرنے کے لیے پوائنٹس پر گھسیٹیں۔", "app.components.form.controls.clickOnMapToAddOrType": "اپنا جواب شامل کرنے کے لیے نقشے پر کلک کریں یا نیچے ایک پتہ ٹائپ کریں۔", "app.components.form.controls.confirm": "تصدیق کریں۔", "app.components.form.controls.cosponsorsPlaceholder": "تلاش کرنے کے لیے نام لکھنا شروع کریں۔", "app.components.form.controls.currentRank": "موجودہ درجہ:", "app.components.form.controls.minimumCoordinates2": "کم از کم {numPoints} نقشہ پوائنٹس درکار ہیں۔", "app.components.form.controls.noRankSelected": "کوئی درجہ منتخب نہیں کیا گیا۔", "app.components.form.controls.notPublic1": "*اس جواب کا اشتراک صرف پروجیکٹ مینیجرز کے ساتھ کیا جائے گا، عوام کے ساتھ نہیں۔", "app.components.form.controls.optionalParentheses": "(اختیاری)", "app.components.form.controls.rankingInstructions": "رینک کے اختیارات کے لیے گھسیٹیں اور چھوڑیں۔", "app.components.form.controls.selectAsManyAsYouLike": "* جتنے چاہیں منتخب کریں۔", "app.components.form.controls.selectBetween": "* {minItems} اور {maxItems} اختیارات کے درمیان منتخب کریں", "app.components.form.controls.selectExactly2": "*بالکل منتخب کریں {selectExactly, plural, one {# آپشن} other {# اختیارات}}", "app.components.form.controls.selectMany": "* جتنے چاہیں منتخب کریں۔", "app.components.form.controls.tapOnFullscreenMapToAdd4": "ڈرا کرنے کے لیے نقشے پر ٹیپ کریں۔ پھر، ان کو منتقل کرنے کے لیے پوائنٹس پر گھسیٹیں۔", "app.components.form.controls.tapOnFullscreenMapToAddPoint": "ڈرا کرنے کے لیے نقشے پر ٹیپ کریں۔", "app.components.form.controls.tapOnMapMultipleToAdd3": "اپنا جواب شامل کرنے کے لیے نقشے پر تھپتھپائیں۔", "app.components.form.controls.tapOnMapToAddOrType": "اپنا جواب شامل کرنے کے لیے نقشے پر ٹیپ کریں یا نیچے ایک پتہ ٹائپ کریں۔", "app.components.form.controls.tapToAddALine": "لائن شامل کرنے کے لیے تھپتھپائیں۔", "app.components.form.controls.tapToAddAPoint": "پوائنٹ شامل کرنے کے لیے تھپتھپائیں۔", "app.components.form.controls.tapToAddAnArea": "ایک علاقہ شامل کرنے کے لیے تھپتھپائیں۔", "app.components.form.controls.uploadShapefileInstructions": "* ایک زپ فائل اپ لوڈ کریں جس میں ایک یا زیادہ شکل فائلیں ہوں۔", "app.components.form.controls.validCordinatesTooltip2": "اگر آپ کے ٹائپ کرتے وقت آپشنز میں مقام ظاہر نہیں ہوتا ہے، تو آپ درست مقام کی وضاحت کے لیے 'عرض البلد، طول البلد' میں درست نقاط شامل کر سکتے ہیں (مثلاً: -33.019808، -71.495676)۔", "app.components.form.controls.valueOutOfTotal": "{total}میں سے {value}", "app.components.form.controls.valueOutOfTotalWithLabel": "{value} سے باہر {total}, {label}", "app.components.form.controls.valueOutOfTotalWithMaxExplanation": "{value} {total}میں سے، جہاں {maxValue} ہے {maxLabel}", "app.components.form.error": "خرا<PERSON>ی", "app.components.form.locationGoogleUnavailable": "گوگل میپس کے ذریعہ فراہم کردہ لوکیشن فیلڈ کو لوڈ نہیں کیا جا سکا۔", "app.components.form.progressBarLabel": "سروے کی پیشرفت", "app.components.form.submit": "جمع کروائیں۔", "app.components.form.submitApiError": "فارم جمع کرانے میں ایک مسئلہ تھا۔ براہ کرم کسی خامی کی جانچ کریں اور دوبارہ کوشش کریں۔", "app.components.form.verifiedBlocked": "آپ اس فیلڈ میں ترمیم نہیں کر سکتے کیونکہ یہ تصدیق شدہ معلومات پر مشتمل ہے۔", "app.components.formBuilder.Page": "ص<PERSON><PERSON><PERSON>", "app.components.formBuilder.accessibilityStatement": "رسائی کا بیان", "app.components.formBuilder.addAnswer": "جواب شامل کریں۔", "app.components.formBuilder.addStatement": "بیان شامل کریں۔", "app.components.formBuilder.agree": "متفق", "app.components.formBuilder.ai1": "اے آئی", "app.components.formBuilder.aiUpsellText1": "اگر آپ کو ہمارے AI پیکیج تک رسائی حاصل ہے، تو آپ AI کے ساتھ متنی جوابات کا خلاصہ اور درجہ بندی کر سکیں گے۔", "app.components.formBuilder.askFollowUpToggleLabel": "فالو اپ پوچھیں۔", "app.components.formBuilder.bad": "برا", "app.components.formBuilder.buttonLabel": "بٹن کا لیبل", "app.components.formBuilder.buttonLink": "بٹن لنک", "app.components.formBuilder.cancelLeaveBuilderButtonText": "منسوخ کریں۔", "app.components.formBuilder.category": "زمرہ", "app.components.formBuilder.chooseMany": "بہت سے منتخب کریں۔", "app.components.formBuilder.chooseOne": "ایک کا انتخاب کریں۔", "app.components.formBuilder.close": "بند", "app.components.formBuilder.closed": "بند", "app.components.formBuilder.configureMap": "نقشہ ترتیب دیں۔", "app.components.formBuilder.confirmLeaveBuilderButtonText": "ہاں، میں جانا چاہتا ہوں۔", "app.components.formBuilder.content": "مواد", "app.components.formBuilder.continuePageLabel": "جاری ہے۔", "app.components.formBuilder.cosponsors": "شریک سپانسرز", "app.components.formBuilder.default": "طے شدہ", "app.components.formBuilder.defaultContent": "پہلے سے طے شدہ مواد", "app.components.formBuilder.delete": "حذف کریں۔", "app.components.formBuilder.deleteButtonLabel": "حذف کریں۔", "app.components.formBuilder.description": "تفصیل", "app.components.formBuilder.disabledBuiltInFieldTooltip": "یہ پہلے ہی فارم میں شامل کیا جا چکا ہے۔ پہلے سے طے شدہ مواد صرف ایک بار استعمال کیا جا سکتا ہے۔", "app.components.formBuilder.disabledCustomFieldsTooltip1": "حسب ضرورت مواد شامل کرنا آپ کے موجودہ لائسنس کا حصہ نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.components.formBuilder.disagree": "اختلا<PERSON> کرنا", "app.components.formBuilder.displayAsDropdown": "ڈراپ ڈاؤن کے بطور ڈسپلے کریں۔", "app.components.formBuilder.displayAsDropdownTooltip": "ڈراپ ڈاؤن میں اختیارات ڈسپلے کریں۔ اگر آپ کے پاس بہت سے اختیارات ہیں، تو اس کی سفارش کی جاتی ہے۔", "app.components.formBuilder.done": "ہو گیا", "app.components.formBuilder.drawArea": "علاقہ ڈرا کریں۔", "app.components.formBuilder.drawRoute": "راستہ کھینچنا", "app.components.formBuilder.dropPin": "ڈراپ پن", "app.components.formBuilder.editButtonLabel": "ترمیم کریں۔", "app.components.formBuilder.emptyImageOptionError": "کم از کم 1 جواب فراہم کریں۔ براہ کرم نوٹ کریں کہ ہر جواب کا عنوان ہونا چاہیے۔", "app.components.formBuilder.emptyOptionError": "کم از کم 1 جواب فراہم کریں۔", "app.components.formBuilder.emptyStatementError": "کم از کم 1 بیان فراہم کریں۔", "app.components.formBuilder.emptyTitleError": "سوال کا عنوان دیں۔", "app.components.formBuilder.emptyTitleMessage": "تمام جوابات کے لیے ایک عنوان فراہم کریں۔", "app.components.formBuilder.emptyTitleStatementMessage": "تمام بیانات کے لیے عنوان فراہم کریں۔", "app.components.formBuilder.enable": "فعال کریں۔", "app.components.formBuilder.errorMessage": "ایک مسئلہ ہے، براہ کرم اپنی تبدیلیاں محفوظ کرنے کے لیے مسئلہ کو ٹھیک کریں۔", "app.components.formBuilder.fieldGroup.description": "تفصیل (اختیاری)", "app.components.formBuilder.fieldGroup.title": "عنوان (اختیاری)", "app.components.formBuilder.fieldIsNotVisibleTooltip": "فی الحال، ان سوالوں کے جواب صرف ان پٹ مینیجر پر برآمد شدہ ایکسل فائل میں دستیاب ہیں، اور صارفین کو نظر نہیں آتے۔", "app.components.formBuilder.fieldLabel": "انتخاب کے جوابات دیں۔", "app.components.formBuilder.fieldLabelStatement": "بیانات", "app.components.formBuilder.fileUpload": "فائل اپ لوڈ کریں۔", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapBasedPage": "نقشہ پر مبنی صفحہ", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.mapOptionDescription": "نقشہ کو سیاق و سباق کے طور پر ایمبیڈ کریں یا شرکاء سے مقام پر مبنی سوالات پوچھیں۔", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.noMapInputQuestions": "بہترین صارف کے تجربے کے لیے، ہم نقشہ پر مبنی صفحات میں پوائنٹ، راستے، یا علاقے کے سوالات شامل کرنے کی سفارش نہیں کرتے ہیں۔", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.normalPage": "<PERSON>ا<PERSON> صفحہ", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.notInCurrentLicense": "سروے میپنگ کی خصوصیات آپ کے موجودہ لائسنس میں شامل نہیں ہیں۔ مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.components.formBuilder.formBuilderSettings.pageLayoutSettings.pageType": "صف<PERSON><PERSON> کی قسم", "app.components.formBuilder.formEnd": "فارم کا اختتام", "app.components.formBuilder.formField.cancelDeleteButtonText": "منسوخ کریں۔", "app.components.formBuilder.formField.confirmDeleteFieldWithLogicButtonText": "ہاں، صفحہ حذف کر دیں۔", "app.components.formBuilder.formField.copyNoun": "کاپی", "app.components.formBuilder.formField.copyVerb": "کاپی", "app.components.formBuilder.formField.delete": "حذف کریں۔", "app.components.formBuilder.formField.deleteFieldWithLogicConfirmationQuestion": "اس صفحہ کو حذف کرنے سے اس سے وابستہ منطق بھی حذف ہو جائے گی۔ کیا آپ واقعی اسے حذف کرنا چاہتے ہیں؟", "app.components.formBuilder.formField.deleteResultsInfo": "اسے کالعدم نہیں کیا جا سکتا", "app.components.formBuilder.goToPageInputLabel": "پھر اگلا صفحہ ہے:", "app.components.formBuilder.good": "اچھا", "app.components.formBuilder.helmetTitle": "فارم بنانے والا", "app.components.formBuilder.imageFileUpload": "تصویر اپ لوڈ کریں۔", "app.components.formBuilder.invalidLogicBadgeMessage": "<PERSON>لط منطق", "app.components.formBuilder.labels2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (اختیاری)", "app.components.formBuilder.labelsTooltipContent2": "کسی بھی لکیری پیمانے کی قدروں کے لیے اختیاری لیبل منتخب کریں۔", "app.components.formBuilder.lastPage": "ختم ہونے والا", "app.components.formBuilder.layout": "لے آؤٹ", "app.components.formBuilder.leaveBuilderConfirmationQuestion": "کیا آپ واقعی چھوڑنا چاہتے ہیں؟", "app.components.formBuilder.leaveBuilderText": "آپ کے پاس غیر محفوظ شدہ تبدیلیاں ہیں۔ براہ کرم جانے سے پہلے محفوظ کریں۔ اگر آپ چھوڑ دیتے ہیں تو آپ اپنی تبدیلیاں کھو دیں گے۔", "app.components.formBuilder.limitAnswersTooltip": "آن ہونے پر، جواب دہندگان کو آگے بڑھنے کے لیے جوابات کی مخصوص تعداد کو منتخب کرنا ہوگا۔", "app.components.formBuilder.limitNumberAnswers": "جوابات کی تعداد محدود کریں۔", "app.components.formBuilder.linePolygonMapWarning2": "لائن اور کثیرالاضلاع ڈرائنگ ایکسیسبیلٹی کے معیار پر پورا نہیں اتر سکتی۔ مزید معلومات {accessibilityStatement}میں مل سکتی ہے۔", "app.components.formBuilder.linearScale": "لکیری پیمانہ", "app.components.formBuilder.locationDescription": "مقام", "app.components.formBuilder.logic": "منطق", "app.components.formBuilder.logicAnyOtherAnswer": "کوئی اور جواب", "app.components.formBuilder.logicConflicts.conflictingLogic": "متضاد منطق", "app.components.formBuilder.logicConflicts.interQuestionConflict": "اس صفحہ میں ایسے سوالات ہیں جو مختلف صفحات تک لے جاتے ہیں۔ اگر شرکاء ایک سے زیادہ سوالات کے جواب دیتے ہیں، تو سب سے دور کا صفحہ دکھایا جائے گا۔ یقینی بنائیں کہ یہ رویہ آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.logicConflicts.multipleConflictTypes": "اس صفحہ پر متعدد منطقی اصول لاگو کیے گئے ہیں: کثیر منتخب سوال کی منطق، صفحہ کی سطح کی منطق، اور بین سوالی منطق۔ جب یہ شرائط اوورلیپ ہو جائیں گی، سوال کی منطق کو صفحہ کی منطق پر ترجیح دی جائے گی، اور سب سے دور والا صفحہ دکھایا جائے گا۔ اس بات کو یقینی بنانے کے لیے منطق کا جائزہ لیں کہ یہ آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelect": "اس صفحہ میں ایک کثیر منتخب سوال ہے جہاں اختیارات مختلف صفحات پر لے جاتے ہیں۔ اگر شرکاء متعدد اختیارات کا انتخاب کرتے ہیں، تو سب سے دور کا صفحہ دکھایا جائے گا۔ یقینی بنائیں کہ یہ رویہ آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndInterQuestionConflict": "اس صفحہ میں ایک کثیر انتخابی سوال ہے جہاں اختیارات مختلف صفحات پر لے جاتے ہیں اور ایسے سوالات ہیں جو دوسرے صفحات کی طرف لے جاتے ہیں۔ اگر یہ شرائط اوورلیپ ہوتی ہیں تو سب سے دور کا صفحہ دکھایا جائے گا۔ یقینی بنائیں کہ یہ طرز عمل آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.logicConflicts.multipleGotoInMultiSelectAndQuestionVsPageLogic": "اس صفحہ میں ایک کثیر انتخابی سوال ہے جہاں اختیارات مختلف صفحات پر لے جاتے ہیں اور اس میں صفحہ اور سوال دونوں کی سطح پر منطق ترتیب دی گئی ہے۔ سوال کی منطق کو ترجیح دی جائے گی، اور سب سے دور کا صفحہ دکھایا جائے گا۔ یقینی بنائیں کہ یہ طرز عمل آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.logicConflicts.questionVsPageLogic": "اس صفحہ میں صفحہ کی سطح اور سوال کی سطح دونوں پر منطق ترتیب دی گئی ہے۔ سوال کی منطق کو صفحہ کی سطح کی منطق پر ترجیح دی جائے گی۔ یقینی بنائیں کہ یہ رویہ آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.logicConflicts.questionVsPageLogicAndInterQuestionConflict": "اس صفحہ میں صفحہ اور سوال دونوں سطحوں پر منطق ترتیب دی گئی ہے، اور متعدد سوالات مختلف صفحات پر براہ راست ہیں۔ سوال کی منطق کو ترجیح دی جائے گی، اور سب سے دور کا صفحہ دکھایا جائے گا۔ یقینی بنائیں کہ یہ طرز عمل آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.logicNoAnswer2": "جواب نہیں دیا۔", "app.components.formBuilder.logicPanelAnyOtherAnswer": "کوئی اور جواب ہو تو؟", "app.components.formBuilder.logicPanelNoAnswer": "اگر جواب نہ دیا جائے۔", "app.components.formBuilder.logicValidationError": "منطق پچھلے صفحات سے منسلک نہیں ہوسکتی ہے۔", "app.components.formBuilder.longAnswer": "لمبا جواب", "app.components.formBuilder.mapConfiguration": "نقشہ کی ترتیب", "app.components.formBuilder.mapping": "نقشہ سازی", "app.components.formBuilder.mappingNotInCurrentLicense": "سروے میپنگ کی خصوصیات آپ کے موجودہ لائسنس میں شامل نہیں ہیں۔ مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.components.formBuilder.matrix": "میٹرکس", "app.components.formBuilder.matrixSettings.columns": "کالم", "app.components.formBuilder.matrixSettings.rows": "قطاریں", "app.components.formBuilder.multipleChoice": "متعدد انتخاب", "app.components.formBuilder.multipleChoiceHelperText": "اگر متعدد اختیارات مختلف صفحات پر لے جاتے ہیں اور شرکاء ایک سے زیادہ کو منتخب کرتے ہیں، تو سب سے دور کا صفحہ دکھایا جائے گا۔ یقینی بنائیں کہ یہ رویہ آپ کے مطلوبہ بہاؤ کے مطابق ہے۔", "app.components.formBuilder.multipleChoiceImage": "تصویر کا انتخاب", "app.components.formBuilder.multiselect.maximum": "زیادہ سے زیادہ", "app.components.formBuilder.multiselect.minimum": "کم از کم", "app.components.formBuilder.neutral": "<PERSON><PERSON><PERSON> جانب<PERSON><PERSON><PERSON>", "app.components.formBuilder.newField": "نیا میدان", "app.components.formBuilder.number": "نمبر", "app.components.formBuilder.ok": "ٹھیک ہے", "app.components.formBuilder.open": "کھولیں۔", "app.components.formBuilder.optional": "اختیاری", "app.components.formBuilder.other": "دیگر", "app.components.formBuilder.otherOption": "\"دیگر\" آپشن", "app.components.formBuilder.otherOptionTooltip": "شرکاء کو حسب ضرورت جواب داخل کرنے کی اجازت دیں اگر فراہم کردہ جوابات ان کی ترجیح سے میل نہیں کھاتے ہیں۔", "app.components.formBuilder.page": "ص<PERSON><PERSON><PERSON>", "app.components.formBuilder.pageCannotBeDeleted": "اس صفحہ کو حذف نہیں کیا جا سکتا۔", "app.components.formBuilder.pageCannotBeDeletedNorNewFieldsAdded": "یہ صفحہ حذف نہیں کیا جا سکتا اور کسی اضافی فیلڈ کو شامل کرنے کی اجازت نہیں دیتا۔", "app.components.formBuilder.pageRuleLabel": "اگلا صفحہ ہے:", "app.components.formBuilder.pagesLogicHelperTextDefault1": "اگر کوئی منطق شامل نہیں کی جاتی ہے، تو فارم اپنے معمول کے بہاؤ کی پیروی کرے گا۔ اگر صفحہ اور اس کے سوالات دونوں میں منطق ہے تو سوال کی منطق کو ترجیح دی جائے گی۔ یقینی بنائیں کہ یہ آپ کے مطلوبہ بہاؤ کے مطابق ہے مزید معلومات کے لیے، {supportPageLink}پر جائیں", "app.components.formBuilder.preview": "پیش نظارہ:", "app.components.formBuilder.printSupportTooltip.cosponsor_ids2": "ڈاؤن لوڈ کردہ پی ڈی ایف پر شریک اسپانسرز نہیں دکھائے جاتے ہیں اور فارم سنک کے ذریعے درآمد کے لیے تعاون یافتہ نہیں ہیں۔", "app.components.formBuilder.printSupportTooltip.fileupload": "فائل اپ لوڈ کے سوالات ڈاؤن لوڈ کردہ پی ڈی ایف پر غیر تعاون یافتہ کے طور پر دکھائے گئے ہیں اور FormSync کے ذریعے درآمد کے لیے تعاون یافتہ نہیں ہیں۔", "app.components.formBuilder.printSupportTooltip.mapping": "ڈاؤن لوڈ کردہ پی ڈی ایف پر نقشہ سازی کے سوالات دکھائے گئے ہیں، لیکن پرتیں نظر نہیں آئیں گی۔ نقشہ سازی کے سوالات FormSync کے ذریعے درآمد کرنے کے لیے تعاون یافتہ نہیں ہیں۔", "app.components.formBuilder.printSupportTooltip.matrix": "میٹرکس کے سوالات ڈاؤن لوڈ کردہ پی ڈی ایف پر دکھائے گئے ہیں لیکن فی الحال FormSync کے ذریعے درآمد کے لیے تعاون یافتہ نہیں ہیں۔", "app.components.formBuilder.printSupportTooltip.page": "صفحہ کے عنوانات اور تفصیل کو ڈاؤن لوڈ کردہ پی ڈی ایف میں سیکشن ہیڈر کے طور پر دکھایا گیا ہے۔", "app.components.formBuilder.printSupportTooltip.ranking": "درجہ بندی کے سوالات ڈاؤن لوڈ کردہ پی ڈی ایف پر دکھائے گئے ہیں لیکن فی الحال FormSync کے ذریعے درآمد کے لیے تعاون یافتہ نہیں ہیں۔", "app.components.formBuilder.printSupportTooltip.topics2": "ٹیگز کو ڈاؤن لوڈ کردہ پی ڈی ایف پر غیر تعاون یافتہ کے طور پر دکھایا گیا ہے اور FormSync کے ذریعے درآمد کے لیے تعاون یافتہ نہیں ہیں۔", "app.components.formBuilder.proposedBudget": "مجوزہ بجٹ", "app.components.formBuilder.question": "سوال", "app.components.formBuilder.questionCannotBeDeleted": "اس سوال کو حذف نہیں کیا جا سکتا۔", "app.components.formBuilder.questionDescriptionOptional": "سوال کی تفصیل (اختیاری)", "app.components.formBuilder.questionTitle": "سوال کا عنوان", "app.components.formBuilder.randomize": "بے ترتیب کرنا", "app.components.formBuilder.randomizeToolTip": "جوابات کی ترتیب فی صارف بے ترتیب ہو جائے گی۔", "app.components.formBuilder.range": "رینج", "app.components.formBuilder.ranking": "در<PERSON><PERSON> بندی", "app.components.formBuilder.rating": "در<PERSON><PERSON> بندی", "app.components.formBuilder.removeAnswer": "جواب ہٹا دیں۔", "app.components.formBuilder.required": "درکار ہے۔", "app.components.formBuilder.requiredToggleLabel": "اس سوال کا جواب ضروری بنائیں", "app.components.formBuilder.ruleForAnswerLabel": "اگر جواب ہے:", "app.components.formBuilder.ruleForAnswerLabelMultiselect": "اگر جوابات میں شامل ہیں:", "app.components.formBuilder.save": "محفوظ کریں۔", "app.components.formBuilder.selectRangeTooltip": "اپنے پیمانے کے لیے زیادہ سے زیادہ قیمت کا انتخاب کریں۔", "app.components.formBuilder.sentiment": "جذبات کا پیمانہ", "app.components.formBuilder.shapefileUpload": "ایسری شکل فائل اپ لوڈ کریں۔", "app.components.formBuilder.shortAnswer": "مخت<PERSON>ر جواب", "app.components.formBuilder.showResponseToUsersToggleLabel": "صارفین کو جواب دکھائیں۔", "app.components.formBuilder.singleChoice": "وا<PERSON><PERSON> انتخاب", "app.components.formBuilder.staleDataErrorMessage2": "ایک مسئلہ ہو گیا ہے۔ یہ ان پٹ فارم حال ہی میں کہیں اور محفوظ کیا گیا ہے۔ اس کی وجہ یہ ہو سکتی ہے کہ آپ یا کسی دوسرے صارف نے اسے کسی اور براؤزر ونڈو میں ترمیم کے لیے کھول دیا ہے۔ تازہ ترین فارم حاصل کرنے کے لیے براہ کرم صفحہ کو ریفریش کریں اور پھر اپنی تبدیلیاں دوبارہ کریں۔", "app.components.formBuilder.stronglyAgree": "سختی سے متفق", "app.components.formBuilder.stronglyDisagree": "سختی سے متفق نہیں۔", "app.components.formBuilder.supportArticleLinkText": "یہ صفحہ", "app.components.formBuilder.tags": "ٹی<PERSON>ز", "app.components.formBuilder.title": "عنوان", "app.components.formBuilder.toLabel": "کو", "app.components.formBuilder.unsavedChanges": "آپ کے پاس غیر محفوظ شدہ تبدیلیاں ہیں۔", "app.components.formBuilder.useCustomButton2": "حسب ضرورت صفحہ بٹن استعمال کریں۔", "app.components.formBuilder.veryBad": "بہت برا", "app.components.formBuilder.veryGood": "بہت اچھا", "app.components.ideas.similarIdeas.engageHere": "یہاں مشغول ہوں۔", "app.components.ideas.similarIdeas.noSimilarSubmissions": "کوئی ملتی جلتی گذارشات نہیں ملیں۔", "app.components.ideas.similarIdeas.similarSubmissionsDescription": "ہمیں ملتے جلتے گذارشات ملے - ان کے ساتھ مشغول ہونے سے انہیں مضبوط بنانے میں مدد مل سکتی ہے!", "app.components.ideas.similarIdeas.similarSubmissionsPosted": "اسی طرح کی گذارشات پہلے ہی پوسٹ کی گئی ہیں:", "app.components.ideas.similarIdeas.similarSubmissionsSearch": "اسی طرح کی گذارشات کی تلاش ہے...", "app.components.phaseTimeLeft.xDayLeft": "{timeLeft, plural, =0 {ایک دن سے کم} one {# دن} other {# دن}} باقی", "app.components.phaseTimeLeft.xWeeksLeft": "{timeLeft}  ہفتے باقی ہیں۔", "app.components.screenReaderCurrency.AED": "متحدہ عرب امارات درہم", "app.components.screenReaderCurrency.AFN": "افغانی افغانی۔", "app.components.screenReaderCurrency.ALL": "البانیائی لیک", "app.components.screenReaderCurrency.AMD": "آرمینیائی ڈرام", "app.components.screenReaderCurrency.ANG": "نیدرلینڈ اینٹیلین گلڈر", "app.components.screenReaderCurrency.AOA": "انگولن کوانزا", "app.components.screenReaderCurrency.ARS": "ارجنٹائن پیسو", "app.components.screenReaderCurrency.AUD": "آسٹریلوی ڈالر", "app.components.screenReaderCurrency.AWG": "اروبن فلورن", "app.components.screenReaderCurrency.AZN": "آذربائیجانی منات", "app.components.screenReaderCurrency.BAM": "بوسنیا ہرزیگوینا کنورٹیبل مارک", "app.components.screenReaderCurrency.BBD": "باربیڈین ڈالر", "app.components.screenReaderCurrency.BDT": "بنگلہ دیشی ٹکا", "app.components.screenReaderCurrency.BGN": "بلغاریائی لیو", "app.components.screenReaderCurrency.BHD": "بحرینی دینار", "app.components.screenReaderCurrency.BIF": "برونڈین فرانک", "app.components.screenReaderCurrency.BMD": "برموڈین ڈالر", "app.components.screenReaderCurrency.BND": "برونائی ڈالر", "app.components.screenReaderCurrency.BOB": "بولیوین بولیویانو", "app.components.screenReaderCurrency.BOV": "بو<PERSON><PERSON><PERSON><PERSON>ن <PERSON>", "app.components.screenReaderCurrency.BRL": "برازیلین ریال", "app.components.screenReaderCurrency.BSD": "بہامین ڈالر", "app.components.screenReaderCurrency.BTN": "بھوٹانی نگلٹرم", "app.components.screenReaderCurrency.BWP": "بوٹسوانان پولا", "app.components.screenReaderCurrency.BYR": "بیلاروسی روبل", "app.components.screenReaderCurrency.BZD": "بیلیز ڈالر", "app.components.screenReaderCurrency.CAD": "کینیڈین ڈالر", "app.components.screenReaderCurrency.CDF": "کانگولیس فرانک", "app.components.screenReaderCurrency.CHE": "WIR یورو", "app.components.screenReaderCurrency.CHF": "سوئس فرانک", "app.components.screenReaderCurrency.CHW": "WIR فرانک", "app.components.screenReaderCurrency.CLF": "چلی اکاؤنٹ کی اکائی (UF)", "app.components.screenReaderCurrency.CLP": "چلی پیسو", "app.components.screenReaderCurrency.CNY": "چینی یوآن", "app.components.screenReaderCurrency.COP": "کولمبیا پیسو", "app.components.screenReaderCurrency.COU": "Unidad de Valor Real", "app.components.screenReaderCurrency.CRC": "کوسٹا ریکن کولون", "app.components.screenReaderCurrency.CRE": "کریڈٹ", "app.components.screenReaderCurrency.CUC": "کیوبا کنورٹیبل پیسو", "app.components.screenReaderCurrency.CUP": "کیوبا پیسو", "app.components.screenReaderCurrency.CVE": "کیپ ورڈین اسکوڈو", "app.components.screenReaderCurrency.CZK": "چیک کورونا۔", "app.components.screenReaderCurrency.DJF": "جبوتی فرانک", "app.components.screenReaderCurrency.DKK": "ڈینش کرون", "app.components.screenReaderCurrency.DOP": "ڈومینیکن پیسو", "app.components.screenReaderCurrency.DZD": "الجزائری دینار", "app.components.screenReaderCurrency.EGP": "مصری پاؤنڈ", "app.components.screenReaderCurrency.ERN": "اریٹیرین ناکفا", "app.components.screenReaderCurrency.ETB": "ایتھوپیا کا بیر", "app.components.screenReaderCurrency.EUR": "یورو", "app.components.screenReaderCurrency.FJD": "فجی ڈالر", "app.components.screenReaderCurrency.FKP": "فاک لینڈ جزائر پاؤنڈ", "app.components.screenReaderCurrency.GBP": "برطانوی پاؤنڈ", "app.components.screenReaderCurrency.GEL": "جارجیائی لاری۔", "app.components.screenReaderCurrency.GHS": "گھانا سیڈی", "app.components.screenReaderCurrency.GIP": "جبرالٹر پاؤنڈ", "app.components.screenReaderCurrency.GMD": "گیمبیئن دلاسی", "app.components.screenReaderCurrency.GNF": "گ<PERSON>ان فرانک", "app.components.screenReaderCurrency.GTQ": "گوئٹے مالا کوئٹزل", "app.components.screenReaderCurrency.GYD": "گیانی ڈالر", "app.components.screenReaderCurrency.HKD": "ہانگ کانگ ڈالر", "app.components.screenReaderCurrency.HNL": "ہنڈوران لیمپیرا", "app.components.screenReaderCurrency.HRK": "کروشین کونا", "app.components.screenReaderCurrency.HTG": "ہیتی گورڈے۔", "app.components.screenReaderCurrency.HUF": "ہنگری فورنٹ", "app.components.screenReaderCurrency.IDR": "انڈونیشین روپیہ", "app.components.screenReaderCurrency.ILS": "اسرائیلی نیو شیکل", "app.components.screenReaderCurrency.INR": "ہندوستانی روپیہ", "app.components.screenReaderCurrency.IQD": "عراقی دینار", "app.components.screenReaderCurrency.IRR": "ایرانی ریال", "app.components.screenReaderCurrency.ISK": "آئس لینڈی کرونا", "app.components.screenReaderCurrency.JMD": "جمیکا ڈالر", "app.components.screenReaderCurrency.JOD": "اردنی دینار", "app.components.screenReaderCurrency.JPY": "جا<PERSON>انی ین", "app.components.screenReaderCurrency.KES": "کینیا شلنگ", "app.components.screenReaderCurrency.KGS": "کرغزستانی سوم", "app.components.screenReaderCurrency.KHR": "کمبوڈین ریل", "app.components.screenReaderCurrency.KMF": "کومورین فرانک", "app.components.screenReaderCurrency.KPW": "شمالی کوریائی وون", "app.components.screenReaderCurrency.KRW": "جنوبی کوریائی وون", "app.components.screenReaderCurrency.KWD": "کویتی دینار", "app.components.screenReaderCurrency.KYD": "جزائر کیمین ڈالر", "app.components.screenReaderCurrency.KZT": "قازقستانی ٹینگے۔", "app.components.screenReaderCurrency.LAK": "لاؤ کیپ", "app.components.screenReaderCurrency.LBP": "لبنانی پاؤنڈ", "app.components.screenReaderCurrency.LKR": "سری لنکن روپیہ", "app.components.screenReaderCurrency.LRD": "لائبیرین ڈالر", "app.components.screenReaderCurrency.LSL": "لیسوتھو لوٹی۔", "app.components.screenReaderCurrency.LTL": "لتھوانیائی لیتاس", "app.components.screenReaderCurrency.LVL": "لیٹوین لاٹس", "app.components.screenReaderCurrency.LYD": "لیبیا دینار", "app.components.screenReaderCurrency.MAD": "مراکشی درہم", "app.components.screenReaderCurrency.MDL": "مالڈووان لیو", "app.components.screenReaderCurrency.MGA": "ملاگاسی ایریری", "app.components.screenReaderCurrency.MKD": "مقدونیائی دینار", "app.components.screenReaderCurrency.MMK": "میانمار کیات", "app.components.screenReaderCurrency.MNT": "منگولیا Tögrög", "app.components.screenReaderCurrency.MOP": "میکانی پٹاکا", "app.components.screenReaderCurrency.MRO": "موریطانیہ اوگویا", "app.components.screenReaderCurrency.MUR": "ماریشین روپیہ", "app.components.screenReaderCurrency.MVR": "مالدیپ روفیا۔", "app.components.screenReaderCurrency.MWK": "ملاویان کواچا۔", "app.components.screenReaderCurrency.MXN": "میکسیکن پیسو", "app.components.screenReaderCurrency.MXV": "Mexican Unidad de Inversion (UDI)", "app.components.screenReaderCurrency.MYR": "ملائیشین رنگٹ", "app.components.screenReaderCurrency.MZN": "موزمبیکن میٹیکل", "app.components.screenReaderCurrency.NAD": "نمیبیا ڈالر", "app.components.screenReaderCurrency.NGN": "نائجیرین نائرا", "app.components.screenReaderCurrency.NIO": "نکاراگوان کورڈوبا", "app.components.screenReaderCurrency.NOK": "نارویجن کرون", "app.components.screenReaderCurrency.NPR": "نیپالی روپیہ", "app.components.screenReaderCurrency.NZD": "نیوزی لینڈ ڈالر", "app.components.screenReaderCurrency.OMR": "عمانی ریال", "app.components.screenReaderCurrency.PAB": "پانامہ کا بالبوہ", "app.components.screenReaderCurrency.PEN": "پیرو سول", "app.components.screenReaderCurrency.PGK": "پاپوا نیو گنی کنا", "app.components.screenReaderCurrency.PHP": "فلپائنی پیسو", "app.components.screenReaderCurrency.PKR": "پاکستانی روپیہ", "app.components.screenReaderCurrency.PLN": "پولش Złoty", "app.components.screenReaderCurrency.PYG": "پیراگوئین گارانی", "app.components.screenReaderCurrency.QAR": "قطری ریال", "app.components.screenReaderCurrency.RON": "رومانیہ لیو", "app.components.screenReaderCurrency.RSD": "سربیائی دینار", "app.components.screenReaderCurrency.RUB": "روسی روبل", "app.components.screenReaderCurrency.RWF": "روانڈا فرانک", "app.components.screenReaderCurrency.SAR": "سعودی ریال", "app.components.screenReaderCurrency.SBD": "جزائر سلیمان ڈالر", "app.components.screenReaderCurrency.SCR": "سیشیلوس روپیہ", "app.components.screenReaderCurrency.SDG": "سوڈانی پاؤنڈ", "app.components.screenReaderCurrency.SEK": "سویڈش کرونا", "app.components.screenReaderCurrency.SGD": "سنگاپور ڈالر", "app.components.screenReaderCurrency.SHP": "سینٹ ہیلینا پاؤنڈ", "app.components.screenReaderCurrency.SLL": "سیرا لیونین لیون", "app.components.screenReaderCurrency.SOS": "صومالی شلنگ", "app.components.screenReaderCurrency.SRD": "سورینام ڈالر", "app.components.screenReaderCurrency.SSP": "جنوبی سوڈانی پاؤنڈ", "app.components.screenReaderCurrency.STD": "ساؤ ٹومی اور پرنسپی ڈوبرا", "app.components.screenReaderCurrency.SYP": "شامی پاؤنڈ", "app.components.screenReaderCurrency.SZL": "سوازی لیلانگینی", "app.components.screenReaderCurrency.THB": "تھائی بھات", "app.components.screenReaderCurrency.TJS": "تاجکستانی سومونی۔", "app.components.screenReaderCurrency.TMT": "ترکمانستانی منات", "app.components.screenReaderCurrency.TND": "تیونسی دینار", "app.components.screenReaderCurrency.TOK": "ٹوکن", "app.components.screenReaderCurrency.TOP": "ٹونگن پانگا", "app.components.screenReaderCurrency.TRY": "ترک لیرا", "app.components.screenReaderCurrency.TTD": "ٹرینیڈاڈ اور ٹوباگو ڈالر", "app.components.screenReaderCurrency.TWD": "نیا تائیوان ڈالر", "app.components.screenReaderCurrency.TZS": "تنزانیائی شلنگ", "app.components.screenReaderCurrency.UAH": "یوکرائنی ہریونیا", "app.components.screenReaderCurrency.UGX": "یوگنڈا شلنگ", "app.components.screenReaderCurrency.USD": "امریکی ڈالر", "app.components.screenReaderCurrency.USN": "امریکی ڈالر (اگلے دن)", "app.components.screenReaderCurrency.USS": "امریکی ڈالر (اسی دن)", "app.components.screenReaderCurrency.UYI": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "app.components.screenReaderCurrency.UYU": "یوراگوئین پیسو", "app.components.screenReaderCurrency.UZS": "ازبکستانی سوم", "app.components.screenReaderCurrency.VEF": "وینزویلا بولیوار", "app.components.screenReaderCurrency.VND": "ویتنامی Đồng", "app.components.screenReaderCurrency.VUV": "وانواتو واتو", "app.components.screenReaderCurrency.WST": "سمواں تلہ", "app.components.screenReaderCurrency.XAF": "وسطی افریقی CFA فرانک", "app.components.screenReaderCurrency.XAG": "چاندی (ایک ٹرائے اونس)", "app.components.screenReaderCurrency.XAU": "سونا (ایک ٹرائے اونس)", "app.components.screenReaderCurrency.XBA": "یورپی جامع یونٹ (EURCO)", "app.components.screenReaderCurrency.XBB": "یورپی مانیٹری یونٹ (EMU-6)", "app.components.screenReaderCurrency.XBC": "اکاؤنٹ 9 کی یورپی اکائی (EUA-9)", "app.components.screenReaderCurrency.XBD": "اکاؤنٹ 17 کی یورپی اکائی (EUA-17)", "app.components.screenReaderCurrency.XCD": "مشرقی کیریبین ڈالر", "app.components.screenReaderCurrency.XDR": "خصوصی ڈرائنگ کے حقوق", "app.components.screenReaderCurrency.XFU": "UIC فرانک", "app.components.screenReaderCurrency.XOF": "مغربی افریقی CFA فرانک", "app.components.screenReaderCurrency.XPD": "پیلیڈیم (ایک ٹرائے اونس)", "app.components.screenReaderCurrency.XPF": "CFP فرانک", "app.components.screenReaderCurrency.XPT": "پلاٹینم (ایک ٹرائے اونس)", "app.components.screenReaderCurrency.XTS": "کوڈز خاص طور پر جانچ کے مقاصد کے لیے محفوظ ہیں۔", "app.components.screenReaderCurrency.XXX": "کوئی کرنسی نہیں۔", "app.components.screenReaderCurrency.YER": "یمنی ریال", "app.components.screenReaderCurrency.ZAR": "جنوبی افریقی رینڈ", "app.components.screenReaderCurrency.ZMW": "زیمبیا کواچا۔", "app.components.screenReaderCurrency.amount": "رقم", "app.components.screenReaderCurrency.currency": "کرنسی", "app.components.trendIndicator.lastQuarter2": "آخری سہ ماہی", "app.containers.AccessibilityStatement.applicability": "یہ قابل رسائی بیان {demoPlatformLink} پر لاگو ہوتا ہے جو اس ویب سائٹ کا نمائندہ ہے۔ یہ ایک ہی سورس کوڈ استعمال کرتا ہے اور ایک ہی فعالیت رکھتا ہے۔", "app.containers.AccessibilityStatement.assesmentMethodsTitle": "تشخیص کا طریقہ", "app.containers.AccessibilityStatement.assesmentText2022": "اس سائٹ کی رسائی کا اندازہ کسی بیرونی ادارے کے ذریعے کیا گیا جو ڈیزائن اور ترقی کے عمل میں شامل نہیں ہے۔ مذکورہ بالا {demoPlatformLink} کی تعمیل اس {statusPageLink}پر شناخت کی جا سکتی ہے۔", "app.containers.AccessibilityStatement.changePreferencesButtonText": "آپ اپنی ترجیحات تبدیل کر سکتے ہیں۔", "app.containers.AccessibilityStatement.changePreferencesText": "کسی بھی وقت، {changePreferencesButton}۔", "app.containers.AccessibilityStatement.conformanceExceptions": "مطابقت کی مستثنیات", "app.containers.AccessibilityStatement.conformanceStatus": "مطابقت کی حیثیت", "app.containers.AccessibilityStatement.contentConformanceExceptions": "ہم اپنے مواد کو سب کے لیے شامل کرنے کی کوشش کرتے ہیں۔ تاہم، کچھ صورتوں میں پلیٹ فارم پر ناقابل رسائی مواد ہو سکتا ہے جیسا کہ ذیل میں بیان کیا گیا ہے:", "app.containers.AccessibilityStatement.demoPlatformLinkText": "ڈیمو ویب سائٹ", "app.containers.AccessibilityStatement.email": "ای میل:", "app.containers.AccessibilityStatement.embeddedSurveyTools": "ایمبیڈڈ سروے ٹولز", "app.containers.AccessibilityStatement.embeddedSurveyToolsException": "ایمبیڈڈ سروے ٹولز جو اس پلیٹ فارم پر استعمال کے لیے دستیاب ہیں وہ تھرڈ پارٹی سافٹ ویئر ہیں اور ممکن ہے قابل رسائی نہ ہوں۔", "app.containers.AccessibilityStatement.exception_1": "ہمارے ڈیجیٹل مصروفیت کے پلیٹ فارم افراد اور تنظیموں کے ذریعے پوسٹ کیے گئے صارف کے تیار کردہ مواد کی سہولت فراہم کرتے ہیں۔ یہ ممکن ہے کہ پی ڈی ایف، تصاویر یا دیگر فائل کی اقسام بشمول ملٹی میڈیا پلیٹ فارم پر اٹیچمنٹ کے طور پر اپ لوڈ کیے جائیں یا پلیٹ فارم صارفین کے ذریعے ٹیکسٹ فیلڈز میں شامل کیے جائیں۔ ہو سکتا ہے یہ دستاویزات پوری طرح سے قابل رسائی نہ ہوں۔", "app.containers.AccessibilityStatement.feedbackProcessIntro": "ہم اس سائٹ کی رسائی پر آپ کے تاثرات کا خیرمقدم کرتے ہیں۔ براہ کرم درج ذیل طریقوں میں سے کسی ایک کے ذریعے ہم سے رابطہ کریں:", "app.containers.AccessibilityStatement.feedbackProcessTitle": "تاثرات کا عمل", "app.containers.AccessibilityStatement.govocalAddress2022": "بلیوارڈ پچیکو 34، 1000 برسلز، بیلجیم", "app.containers.AccessibilityStatement.headTitle": "قابل رسائی بیان | {orgName}", "app.containers.AccessibilityStatement.intro2022": "{goVocalLink} ایک ایسا پلیٹ فارم فراہم کرنے کے لیے پرعزم ہے جو ٹیکنالوجی یا قابلیت سے قطع نظر تمام صارفین کے لیے قابل رسائی ہو۔ تمام صارفین کے لیے ہمارے پلیٹ فارمز کی رسائی اور قابل استعمال کو زیادہ سے زیادہ کرنے کی ہماری جاری کوششوں میں موجودہ متعلقہ قابل رسائی معیارات کی پابندی کی جاتی ہے۔", "app.containers.AccessibilityStatement.mapping": "نقشہ سازی", "app.containers.AccessibilityStatement.mapping_1": "پلیٹ فارم پر نقشے جزوی طور پر رسائی کے معیار پر پورا اترتے ہیں۔ نقشے کی حد، زوم، اور UI ویجٹس کو نقشے دیکھتے وقت کی بورڈ کے ذریعے کنٹرول کیا جا سکتا ہے۔ ایڈمنز بیک آفس میں نقشہ کی تہوں کے انداز کو بھی ترتیب دے سکتے ہیں، یا Esri انضمام کا استعمال کرتے ہوئے، مزید قابل رسائی رنگ پیلیٹ اور علامت سازی تخلیق کر سکتے ہیں۔ مختلف لائنوں یا کثیر الاضلاع طرزوں (مثلاً ڈیشڈ لائنز) کا استعمال جہاں بھی ممکن ہو نقشہ کی تہوں کو فرق کرنے میں مدد کرے گا، اور اگرچہ اس وقت اس طرح کے اسٹائل کو ہمارے پلیٹ فارم کے اندر کنفیگر نہیں کیا جا سکتا ہے، لیکن اگر ایسری انضمام کے ساتھ نقشے استعمال کر رہے ہوں تو اسے کنفیگر کیا جا سکتا ہے۔", "app.containers.AccessibilityStatement.mapping_2": "پلیٹ فارم میں نقشے مکمل طور پر قابل رسائی نہیں ہیں کیونکہ وہ اسکرین ریڈرز کا استعمال کرنے والے صارفین کے لیے بنیادی نقشہ جات، نقشہ کی تہوں، یا ڈیٹا میں رجحانات کو آواز کے ساتھ پیش نہیں کرتے ہیں۔ مکمل طور پر قابل رسائی نقشوں کو نقشے کی تہوں کو آواز کے ساتھ پیش کرنے اور ڈیٹا میں کسی بھی متعلقہ رجحانات کو بیان کرنے کی ضرورت ہوگی۔ مزید برآں، سروے میں لائن اور کثیرالاضلاع نقشہ ڈرائنگ قابل رسائی نہیں ہے کیونکہ کی بورڈ کا استعمال کرتے ہوئے شکلیں نہیں بنائی جا سکتیں۔ تکنیکی پیچیدگی کی وجہ سے اس وقت متبادل ان پٹ طریقے دستیاب نہیں ہیں۔", "app.containers.AccessibilityStatement.mapping_3": "لائن اور کثیرالاضلاع نقشے کی ڈرائنگ کو مزید قابل رسائی بنانے کے لیے، ہم تجویز کرتے ہیں کہ سروے کے سوال میں تعارف یا وضاحت شامل کریں یا نقشہ کیا دکھا رہا ہے اس کی صفحہ کی تفصیل اور کوئی متعلقہ رجحانات شامل کریں۔ مزید برآں، ایک مختصر یا لمبا جواب متنی سوال فراہم کیا جا سکتا ہے تاکہ جواب دہندگان ضرورت پڑنے پر اپنے جواب کو سادہ الفاظ میں بیان کر سکیں (نقشے پر کلک کرنے کے بجائے)۔ ہم پروجیکٹ مینیجر کے لیے رابطہ کی معلومات شامل کرنے کی بھی تجویز کرتے ہیں تاکہ جواب دہندگان جو نقشہ کے سوال کو نہیں بھر سکتے وہ سوال کا جواب دینے کے لیے متبادل طریقہ کی درخواست کر سکتے ہیں (مثلاً ویڈیو میٹنگ)۔", "app.containers.AccessibilityStatement.mapping_4": "آئیڈیایشن پروجیکٹس اور پروپوزل کے لیے، نقشے کے منظر میں ان پٹ ڈسپلے کرنے کا آپشن موجود ہے، جو قابل رسائی نہیں ہے۔ تاہم، ان طریقوں کے لیے ان پٹ کا متبادل فہرست منظر دستیاب ہے، جو قابل رسائی ہے۔", "app.containers.AccessibilityStatement.onlineWorkshopsException": "ہماری آن لائن ورکشاپس میں لائیو ویڈیو اسٹریمنگ کا جزو ہے، جو فی الحال سب ٹائٹلز کو سپورٹ نہیں کرتا ہے۔", "app.containers.AccessibilityStatement.pageDescription": "اس ویب سائٹ کی رسائی پر ایک بیان", "app.containers.AccessibilityStatement.postalAddress": "ڈاک کا پتہ:", "app.containers.AccessibilityStatement.publicationDate": "اشاعت کی تاریخ", "app.containers.AccessibilityStatement.publicationDate2024": "رسائی کا یہ بیان 21 اگست 2024 کو شائع ہوا تھا۔", "app.containers.AccessibilityStatement.responsiveness": "ہمارا مقصد 1-2 کاروباری دنوں کے اندر تاثرات کا جواب دینا ہے۔", "app.containers.AccessibilityStatement.statusPageText": "حیثیت کا صفحہ", "app.containers.AccessibilityStatement.technologiesIntro": "اس سائٹ کی رسائی کام کرنے کے لیے درج ذیل ٹیکنالوجیز پر انحصار کرتی ہے:", "app.containers.AccessibilityStatement.technologiesTitle": "ٹیکنالوجیز", "app.containers.AccessibilityStatement.title": "رسائی کا بیان", "app.containers.AccessibilityStatement.userGeneratedContent": "صارف کا تیار کردہ مواد", "app.containers.AccessibilityStatement.workshops": "ورکشاپس", "app.containers.AdminPage.DashboardPage.labelProjectFilter": "پروجیکٹ منتخب کریں۔", "app.containers.AdminPage.ProjectDescription.layoutBuilderWarning": "Content Builder کا استعمال آپ کو لے آؤٹ کے مزید جدید اختیارات استعمال کرنے دے گا۔ ان زبانوں کے لیے جہاں مواد بنانے والے میں کوئی مواد دستیاب نہیں ہے، اس کے بجائے باقاعدہ پروجیکٹ کی تفصیل کا مواد دکھایا جائے گا۔", "app.containers.AdminPage.ProjectDescription.linkText": "Content Builder میں تفصیل میں ترمیم کریں۔", "app.containers.AdminPage.ProjectDescription.saveError": "پروجیکٹ کی تفصیل محفوظ کرتے وقت کچھ غلط ہو گیا۔", "app.containers.AdminPage.ProjectDescription.toggleLabel": "تفصیل کے لیے Content Builder استعمال کریں۔", "app.containers.AdminPage.ProjectDescription.toggleTooltip": "Content Builder کا استعمال آپ کو لے آؤٹ کے مزید جدید اختیارات استعمال کرنے دے گا۔", "app.containers.AdminPage.ProjectDescription.viewPublicProject": "پروجیکٹ دیکھیں", "app.containers.AdminPage.ProjectEdit.formBuilder.surveyEnd": "سروے ختم", "app.containers.AdminPage.Users.GroupCreation.modalHeaderRules": "ایک سمارٹ گروپ بنائیں", "app.containers.AdminPage.Users.GroupCreation.rulesExplanation": "مندرجہ ذیل تمام شرائط سے مماثل صارفین خود بخود گروپ میں شامل ہو جائیں گے۔", "app.containers.AdminPage.Users.UsersGroup.atLeastOneRuleError": "کم از کم ایک اصول فراہم کریں۔", "app.containers.AdminPage.Users.UsersGroup.rulesError": "کچھ شرائط نامکمل ہیں۔", "app.containers.AdminPage.Users.UsersGroup.saveGroup": "گروپ کو محفوظ کریں۔", "app.containers.AdminPage.Users.UsersGroup.smartGroupsAvailability1": "سمارٹ گروپس کو ترتیب دینا آپ کے موجودہ لائسنس کا حصہ نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.AdminPage.Users.UsersGroup.titleFieldEmptyError": "گروپ کا نام بتائیں", "app.containers.AdminPage.Users.UsersGroup.verificationDisabled": "آپ کے پلیٹ فارم کے لیے توثیق کو غیر فعال کر دیا گیا ہے، توثیقی اصول کو ہٹا دیں یا سپورٹ سے رابطہ کریں۔", "app.containers.App.appMetaDescription": "{orgName}کے آن لائن شرکت کے پلیٹ فارم میں خوش آمدید۔ \nمقامی پروجیکٹس کو دریافت کریں اور بحث میں مشغول ہوں!", "app.containers.App.loading": "لوڈ ہو رہا ہے...", "app.containers.App.metaTitle1": "شہری مصروفیت کا پلیٹ فارم | {orgName}", "app.containers.App.skipLinkText": "مرکزی مواد پر جائیں۔", "app.containers.AreaTerms.areaTerm": "علا<PERSON><PERSON>", "app.containers.AreaTerms.areasTerm": "علاقوں", "app.containers.AuthProviders.emailTakenAndUserCanBeVerified": "اس ای میل کے ساتھ ایک اکاؤنٹ پہلے سے موجود ہے۔ آپ سائن آؤٹ کر سکتے ہیں، اس ای میل ایڈریس کے ساتھ لاگ ان کر سکتے ہیں اور ترتیبات کے صفحہ پر اپنے اکاؤنٹ کی تصدیق کر سکتے ہیں۔", "app.containers.Authentication.steps.AccessDenied.close": "بند", "app.containers.Authentication.steps.AccessDenied.youDoNotMeetTheRequirements": "آپ اس عمل میں حصہ لینے کی ضروریات کو پورا نہیں کرتے ہیں۔", "app.containers.Authentication.steps.EmailAndPasswordVerifiedActions.goBackToSSOVerification": "سنگل سائن آن تصدیق پر واپس جائیں۔", "app.containers.Authentication.steps.Invitation.pleaseEnterAToken": "براہ کرم ایک ٹوکن درج کریں۔", "app.containers.Authentication.steps.Invitation.token": "ٹوکن", "app.containers.Authentication.steps.SSOVerification.alreadyHaveAnAccount": "پہلے سے ہی اکاؤنٹ ہے؟ {loginLink}", "app.containers.Authentication.steps.SSOVerification.logIn": "لاگ ان کریں۔", "app.containers.CampaignsConsentForm.ally_categoryLabel": "اس زمرے میں ای میلز", "app.containers.CampaignsConsentForm.messageError": "آپ کی ای میل کی ترجیحات کو محفوظ کرنے میں ایک خرابی تھی۔", "app.containers.CampaignsConsentForm.messageSuccess": "آپ کی ای میل کی ترجیحات محفوظ ہو گئی ہیں۔", "app.containers.CampaignsConsentForm.notificationsSubTitle": "آپ کس قسم کی ای میل اطلاعات موصول کرنا چاہتے ہیں؟ ", "app.containers.CampaignsConsentForm.notificationsTitle": "اطلاعات", "app.containers.CampaignsConsentForm.submit": "محفوظ کریں۔", "app.containers.ChangeEmail.backToProfile": "پروفائل کی ترتیبات پر واپس جائیں۔", "app.containers.ChangeEmail.confirmationModalTitle": "اپنے ای میل کی تصدیق کریں۔", "app.containers.ChangeEmail.emailEmptyError": "ایک ای میل ایڈریس فراہم کریں۔", "app.containers.ChangeEmail.emailInvalidError": "درست فارمیٹ میں ایک ای میل پتہ فراہم کریں، مثال کے طور پر <EMAIL>", "app.containers.ChangeEmail.emailRequired": "براہ کرم ایک ای میل ایڈریس درج کریں۔", "app.containers.ChangeEmail.emailTaken": "یہ ای میل پہلے ہی استعمال میں ہے۔", "app.containers.ChangeEmail.emailUpdateCancelled": "ای میل اپ ڈیٹ منسوخ کر دیا گیا ہے۔", "app.containers.ChangeEmail.emailUpdateCancelledDescription": "اپنی ای میل کو اپ ڈیٹ کرنے کے لیے، براہ کرم عمل کو دوبارہ شروع کریں۔", "app.containers.ChangeEmail.helmetDescription": "اپنا ای میل صفحہ تبدیل کریں۔", "app.containers.ChangeEmail.helmetTitle": "اپنا ای میل تبدیل کریں۔", "app.containers.ChangeEmail.newEmailLabel": "نیا ای میل", "app.containers.ChangeEmail.submitButton": "جمع کروائیں۔", "app.containers.ChangeEmail.titleAddEmail": "اپنا ای میل شامل کریں۔", "app.containers.ChangeEmail.titleChangeEmail": "اپنا ای میل تبدیل کریں۔", "app.containers.ChangeEmail.updateSuccessful": "آپ کا ای میل کامیابی کے ساتھ اپ ڈیٹ ہو گیا ہے۔", "app.containers.ChangePassword.currentPasswordLabel": "موجودہ پاس ورڈ", "app.containers.ChangePassword.currentPasswordRequired": "اپنا موجودہ پاس ورڈ درج کریں۔", "app.containers.ChangePassword.goHome": "گھر جاؤ", "app.containers.ChangePassword.helmetDescription": "اپنا پاس ورڈ صفحہ تبدیل کریں۔", "app.containers.ChangePassword.helmetTitle": "اپنا پاس ورڈ تبدیل کریں۔", "app.containers.ChangePassword.newPasswordLabel": "نیا پاس ورڈ", "app.containers.ChangePassword.newPasswordRequired": "اپنا نیا پاس ورڈ درج کریں۔", "app.containers.ChangePassword.password.minimumPasswordLengthError": "ایک پاس ورڈ فراہم کریں جو کم از کم {minimumPasswordLength} حروف کا ہو۔", "app.containers.ChangePassword.passwordChangeSuccessMessage": "آپ کا پاس ورڈ کامیابی کے ساتھ اپ ڈیٹ ہو گیا ہے۔", "app.containers.ChangePassword.passwordEmptyError": "اپنا پاس ورڈ درج کریں۔", "app.containers.ChangePassword.passwordsDontMatch": "نئے پاس ورڈ کی تصدیق کریں۔", "app.containers.ChangePassword.titleAddPassword": "پاس ورڈ شامل کریں۔", "app.containers.ChangePassword.titleChangePassword": "اپنا پاس ورڈ تبدیل کریں۔", "app.containers.Comments.a11y_commentDeleted": "تبصرہ حذف کر دیا گیا۔", "app.containers.Comments.a11y_commentPosted": "تبصرہ پوسٹ کیا گیا۔", "app.containers.Comments.a11y_likeCount": "{likeCount, plural, =0 {کوئی پسند نہیں} one {1 پسند} other {# پسند}}", "app.containers.Comments.a11y_undoLike": "پسند کو کالعدم کریں۔", "app.containers.Comments.addCommentError": "کچھ غلط ہو گیا۔ براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.Comments.adminCommentDeletionCancelButton": "منسوخ کریں۔", "app.containers.Comments.adminCommentDeletionConfirmButton": "اس تبصرہ کو حذف کریں۔", "app.containers.Comments.cancelCommentEdit": "منسوخ کریں۔", "app.containers.Comments.childCommentBodyPlaceholder": "جواب لکھیں...", "app.containers.Comments.commentCancelUpvote": "کالعدم", "app.containers.Comments.commentDeletedPlaceholder": "یہ تبصرہ حذف کر دیا گیا ہے۔", "app.containers.Comments.commentDeletionCancelButton": "میرا تبصرہ رکھیں", "app.containers.Comments.commentDeletionConfirmButton": "میرا تبصرہ حذف کر دیں۔", "app.containers.Comments.commentLike": "پسند", "app.containers.Comments.commentReplyButton": "جواب دیں۔", "app.containers.Comments.commentsSortTitle": "تبصروں کو ترتیب دیں۔", "app.containers.Comments.completeProfileLinkText": "اپنا پروفائل مکمل کریں۔", "app.containers.Comments.completeProfileToComment": "تبصرہ کرنے کے لیے براہ کرم {completeRegistrationLink} ۔", "app.containers.Comments.confirmCommentDeletion": "کیا آپ واقعی یہ تبصرہ حذف کرنا چاہتے ہیں؟ کوئی واپسی نہیں ہے!", "app.containers.Comments.deleteComment": "حذف کریں۔", "app.containers.Comments.deleteReasonDescriptionError": "اپنی وجہ کے بارے میں مزید معلومات فراہم کریں۔", "app.containers.Comments.deleteReasonError": "کوئی وجہ بتائیں", "app.containers.Comments.deleteReason_inappropriate": "یہ نامناسب یا ناگوار ہے۔", "app.containers.Comments.deleteReason_irrelevant": "یہ متعلقہ نہیں ہے۔", "app.containers.Comments.deleteReason_other": "دوسری وجہ", "app.containers.Comments.editComment": "ترمیم کریں۔", "app.containers.Comments.guidelinesLinkText": "ہماری کمیونٹی گائیڈ لائنز", "app.containers.Comments.ideaCommentBodyPlaceholder": "اپنا تبصرہ یہاں لکھیں۔", "app.containers.Comments.internalCommentingNudgeMessage": "اندرونی تبصرے کرنا آپ کے موجودہ لائسنس میں شامل نہیں ہے۔ اس کے بارے میں مزید جاننے کے لیے اپنے GovSuccess مینیجر سے رابطہ کریں۔", "app.containers.Comments.internalConversation": "اندرونی گفتگو", "app.containers.Comments.loadMoreComments": "مزید تبصرے لوڈ کریں۔", "app.containers.Comments.loadingComments": "تبصرے لوڈ ہو رہے ہیں...", "app.containers.Comments.loadingMoreComments": "مزید تبصرے لوڈ ہو رہے ہیں...", "app.containers.Comments.notVisibleToUsersPlaceholder": "یہ تبصرہ عام صارفین کو نظر نہیں آتا", "app.containers.Comments.postInternalComment": "داخلی تبصرہ پوسٹ کریں۔", "app.containers.Comments.postPublicComment": "عوامی تبصرہ پوسٹ کریں۔", "app.containers.Comments.profanityError": "افوہ! ایسا لگتا ہے کہ آپ کی پوسٹ کچھ ایسی زبان پر مشتمل ہے جو {guidelinesLink}سے نہیں ملتی۔ ہم اسے ہر ایک کے لیے محفوظ جگہ رکھنے کی کوشش کرتے ہیں۔ براہ کرم اپنے ان پٹ میں ترمیم کریں اور دوبارہ کوشش کریں۔", "app.containers.Comments.publicDiscussion": "عوا<PERSON>ی بحث", "app.containers.Comments.publishComment": "اپنا تبصرہ پوسٹ کریں۔", "app.containers.Comments.reportAsSpamModalTitle": "آپ اس کی بطور سپام رپورٹ کیوں کرنا چاہتے ہیں؟", "app.containers.Comments.saveComment": "محفوظ کریں۔", "app.containers.Comments.signInLinkText": "لاگ ان", "app.containers.Comments.signInToComment": "تبصرہ کرنے کے لیے براہ کرم {signInLink} ۔", "app.containers.Comments.signUpLinkText": "سائن اپ کریں", "app.containers.Comments.verifyIdentityLinkText": "اپنی شناخت کی تصدیق کریں۔", "app.containers.Comments.visibleToUsersPlaceholder": "یہ تبصرہ عام صارفین کو نظر آتا ہے۔", "app.containers.Comments.visibleToUsersWarning": "یہاں پوسٹ کیے گئے تبصرے باقاعدہ صارفین کے لیے نظر آئیں گے۔", "app.containers.ContentBuilder.PageTitle": "پروجیکٹ کی تفصیل", "app.containers.CookiePolicy.advertisingContent": "ایڈورٹائزنگ کوکیز کا استعمال اس پلیٹ فارم کے ساتھ منسلک ہونے پر بیرونی مارکیٹنگ مہمات کی تاثیر کو ذاتی بنانے اور پیمائش کرنے کے لیے کیا جا سکتا ہے۔ ہم اس پلیٹ فارم پر کوئی اشتہار نہیں دکھائیں گے، لیکن آپ ان صفحات کی بنیاد پر ذاتی نوعیت کے اشتہارات حاصل کر سکتے ہیں جو آپ دیکھتے ہیں۔", "app.containers.CookiePolicy.advertisingTitle": "ایڈورٹائزنگ", "app.containers.CookiePolicy.analyticsContents": "تجزیاتی کوکیز وزیٹر کے رویے کو ٹریک کرتی ہیں، جیسے کہ کون سے صفحات اور کتنے عرصے تک دیکھے جاتے ہیں۔ وہ کچھ تکنیکی ڈیٹا بھی جمع کر سکتے ہیں جن میں براؤزر کی معلومات، تخمینی مقام اور IP پتے شامل ہیں۔ ہم اس ڈیٹا کو صرف اندرونی طور پر استعمال کرتے ہیں تاکہ صارف کے مجموعی تجربے اور پلیٹ فارم کے کام کاج کو بہتر بنایا جائے۔ اس طرح کے ڈیٹا کو گو ووکل اور {orgName} کے درمیان بھی شیئر کیا جا سکتا ہے تاکہ پلیٹ فارم پر پروجیکٹس کے ساتھ مصروفیت کا اندازہ لگایا جا سکے۔ نوٹ کریں کہ ڈیٹا گمنام ہے اور ایک مجموعی سطح پر استعمال کیا جاتا ہے - یہ آپ کی ذاتی طور پر شناخت نہیں کرتا ہے۔ تاہم، یہ ممکن ہے کہ اگر اس ڈیٹا کو ڈیٹا کے دیگر ذرائع کے ساتھ ملایا جائے تو اس طرح کی شناخت ہو سکتی ہے۔", "app.containers.CookiePolicy.analyticsTitle": "تجزیاتی کوکیز", "app.containers.CookiePolicy.cookiePolicyDescription": "ہم اس پلیٹ فارم پر کوکیز کا استعمال کیسے کرتے ہیں اس کی تفصیلی وضاحت", "app.containers.CookiePolicy.cookiePolicyTitle": "کوکی پالیسی", "app.containers.CookiePolicy.essentialContent": "اس پلیٹ فارم کے مناسب کام کو یقینی بنانے کے لیے کچھ کوکیز ضروری ہیں۔ یہ ضروری کوکیز بنیادی طور پر آپ کے اکاؤنٹ کی تصدیق کے لیے استعمال ہوتی ہیں جب آپ پلیٹ فارم پر جاتے ہیں اور آپ کی پسندیدہ زبان کو محفوظ کرتے ہیں۔", "app.containers.CookiePolicy.essentialTitle": "ضروری کوکیز", "app.containers.CookiePolicy.externalContent": "ہمارے کچھ صفحات بیرونی فراہم کنندگان کا مواد دکھا سکتے ہیں، جیسے، YouTube یا Typeform۔ ہمارا ان تھرڈ پارٹی کوکیز پر کنٹرول نہیں ہے اور ان بیرونی فراہم کنندگان کے مواد کو دیکھنے کے نتیجے میں آپ کے آلے پر کوکیز انسٹال ہو سکتی ہیں۔", "app.containers.CookiePolicy.externalTitle": "بیرونی کوکیز", "app.containers.CookiePolicy.functionalContents": "فنکشنل کوکیز کو زائرین کے لیے اپ ڈیٹس کے بارے میں اطلاعات موصول کرنے اور پلیٹ فارم سے براہ راست سپورٹ چینلز تک رسائی حاصل کرنے کے لیے فعال کیا جا سکتا ہے۔", "app.containers.CookiePolicy.functionalTitle": "فنکشنل کوکیز", "app.containers.CookiePolicy.headCookiePolicyTitle": "کوکی پالیسی | {orgName}", "app.containers.CookiePolicy.intro": "کوکیز وہ ٹیکسٹ فائلیں ہیں جو براؤزر پر یا آپ کے کمپیوٹر یا موبائل ڈیوائس کی ہارڈ ڈرائیو پر محفوظ کی جاتی ہیں جب آپ کسی ویب سائٹ پر جاتے ہیں اور جو بعد میں آنے والے وزٹ کے دوران ویب سائٹ کے ذریعے حوالہ دیا جا سکتا ہے۔ ہم یہ سمجھنے کے لیے کوکیز کا استعمال کرتے ہیں کہ زائرین کس طرح اس پلیٹ فارم کو اس کے ڈیزائن اور تجربے کو بہتر بنانے، آپ کی ترجیحات (جیسے آپ کی ترجیحی زبان) کو یاد رکھنے اور رجسٹرڈ صارفین اور پلیٹ فارم کے منتظمین کے لیے کلیدی کاموں کو سپورٹ کرنے کے لیے استعمال کر رہے ہیں۔", "app.containers.CookiePolicy.manageCookiesDescription": "آپ اپنی کوکی کی ترجیحات میں کسی بھی وقت تجزیات، مارکیٹنگ اور فنکشنل کوکیز کو فعال یا غیر فعال کر سکتے ہیں۔ آپ اپنے انٹرنیٹ براؤزر کے ذریعے کسی بھی موجودہ کوکیز کو دستی طور پر یا خود بخود حذف کر سکتے ہیں۔ تاہم، اس پلیٹ فارم پر آنے والے کسی بھی دورے پر آپ کی رضامندی کے بعد کوکیز کو دوبارہ رکھا جا سکتا ہے۔ اگر آپ کوکیز کو حذف نہیں کرتے ہیں، تو آپ کی کوکیز کی ترجیحات 60 دنوں تک محفوظ رہتی ہیں، جس کے بعد آپ سے دوبارہ رضامندی کے لیے پوچھا جائے گا۔", "app.containers.CookiePolicy.manageCookiesPreferences": "اس پلیٹ فارم پر استعمال ہونے والے فریق ثالث کے انضمام کی مکمل فہرست دیکھنے اور اپنی ترجیحات کا نظم کرنے کے لیے اپنے {manageCookiesPreferencesButtonText} پر جائیں۔", "app.containers.CookiePolicy.manageCookiesPreferencesButtonText": "کوکی کی ترتیبات", "app.containers.CookiePolicy.manageCookiesTitle": "اپنی کوکیز کا انتظام کرنا", "app.containers.CookiePolicy.viewPreferencesButtonText": "کوکی کی ترتیبات", "app.containers.CookiePolicy.viewPreferencesText": "مندرجہ ذیل کوکی زمرے تمام وزٹرز یا پلیٹ فارمز پر لاگو نہیں ہوسکتے ہیں۔ آپ پر لاگو ہونے والے فریق ثالث کے انضمام کی مکمل فہرست کے لیے اپنا {viewPreferencesButton} دیکھیں۔", "app.containers.CookiePolicy.whatDoWeUseCookiesFor": "ہم کوکیز کس کے لیے استعمال کرتے ہیں؟", "app.containers.CustomPageShow.editPage": "صفحہ میں ترمیم کریں۔", "app.containers.CustomPageShow.goBack": "واپس جاؤ", "app.containers.CustomPageShow.notFound": "صفحہ نہیں ملا", "app.containers.DisabledAccount.bottomText": "آپ {date}سے دوبارہ سائن ان کر سکتے ہیں۔", "app.containers.DisabledAccount.termsAndConditions": "شرائط و ضوابط", "app.containers.DisabledAccount.text2": "{orgName} کے شرکت کے پلیٹ فارم پر آپ کا اکاؤنٹ کمیونٹی رہنما خطوط کی خلاف ورزی کی وجہ سے عارضی طور پر غیر فعال کر دیا گیا ہے۔ اس بارے میں مزید معلومات کے لیے، آپ {TermsAndConditions}سے رجوع کر سکتے ہیں۔", "app.containers.DisabledAccount.title": "آپ کا اکاؤنٹ عارضی طور پر غیر فعال کر دیا گیا ہے۔", "app.containers.EventsShow.addToCalendar": "کیلنڈر میں شامل کریں۔", "app.containers.EventsShow.editEvent": "ایونٹ میں ترمیم کریں۔", "app.containers.EventsShow.emailSharingBody2": "اس تقریب میں شرکت کریں: {eventTitle}. {eventUrl}پر مزید پڑھیں", "app.containers.EventsShow.eventDateTimeIcon": "واقعہ کی تاریخ اور وقت", "app.containers.EventsShow.eventFrom2": "\"{projectTitle}\" سے", "app.containers.EventsShow.goBack": "واپس جاؤ", "app.containers.EventsShow.goToProject": "پروجیکٹ پر جائیں۔", "app.containers.EventsShow.haveRegistered": "رجسٹرڈ ہیں", "app.containers.EventsShow.icsError": "ICS فائل ڈاؤن لوڈ کرنے میں خرابی۔", "app.containers.EventsShow.linkToOnlineEvent": "آن لائن ایونٹ کا لنک", "app.containers.EventsShow.locationIconAltText": "مقام", "app.containers.EventsShow.metaTitle": "واقعہ: {eventTitle} | {orgName}", "app.containers.EventsShow.online2": "آن لائن میٹنگ", "app.containers.EventsShow.onlineLinkIconAltText": "آن لائن میٹنگ کا لنک", "app.containers.EventsShow.registered": "رجسٹرڈ", "app.containers.EventsShow.registrantCount": "{attendeesCount, plural, =0 {0 رجسٹر} one {1 رجسٹر} other {# رجسٹر}}", "app.containers.EventsShow.registrantCountWithMaximum": "{attendeesCount} / {maximumNumberOfAttendees} رجسٹر کرنے والے", "app.containers.EventsShow.registrantsIconAltText": "رجسٹر کرنے والے", "app.containers.EventsShow.socialMediaSharingMessage": "اس تقریب میں شرکت کریں: {eventTitle}", "app.containers.EventsShow.xParticipants": "{count, plural, one {# شریک} other {# شرکاء}}", "app.containers.EventsViewer.allTime": "ہر وقت", "app.containers.EventsViewer.date": "تاریخ", "app.containers.EventsViewer.thisMonth2": "آنے والا مہینہ", "app.containers.EventsViewer.thisWeek2": "آنے والا ہفتہ", "app.containers.EventsViewer.today": "آج", "app.containers.IdeaButton.addAContribution": "ایک شراکت شامل کریں۔", "app.containers.IdeaButton.addAPetition": "ایک درخواست شامل کریں۔", "app.containers.IdeaButton.addAProject": "ایک پروجیکٹ شامل کریں۔", "app.containers.IdeaButton.addAProposal": "ایک تجویز شامل کریں۔", "app.containers.IdeaButton.addAQuestion": "ایک سوال شامل کریں۔", "app.containers.IdeaButton.addAnInitiative": "ایک پہل شامل کریں۔", "app.containers.IdeaButton.addAnOption": "ایک آپشن شامل کریں۔", "app.containers.IdeaButton.postingDisabled": "نئی گذارشات فی الحال قبول نہیں کی جا رہی ہیں۔", "app.containers.IdeaButton.postingInNonActivePhases": "نئی گذارشات صرف فعال مراحل میں شامل کی جا سکتی ہیں۔", "app.containers.IdeaButton.postingInactive": "نئی گذارشات فی الحال قبول نہیں کی جا رہی ہیں۔", "app.containers.IdeaButton.postingLimitedMaxReached": "آپ یہ سروے پہلے ہی مکمل کر چکے ہیں۔ آپ کے جواب کے لیے شکریہ!", "app.containers.IdeaButton.postingNoPermission": "نئی گذارشات فی الحال قبول نہیں کی جا رہی ہیں۔", "app.containers.IdeaButton.postingNotYetPossible": "نئی گذارشات ابھی تک قبول نہیں کی جا رہی ہیں۔", "app.containers.IdeaButton.signInLinkText": "لاگ ان", "app.containers.IdeaButton.signUpLinkText": "سائن اپ کریں", "app.containers.IdeaButton.submitAnIssue": "ایک تبصرہ جمع کروائیں", "app.containers.IdeaButton.submitYourIdea": "اپنا خیال پیش کریں۔", "app.containers.IdeaButton.takeTheSurvey": "سروے میں حصہ لیں۔", "app.containers.IdeaButton.verificationLinkText": "ابھی اپنی شناخت کی تصدیق کریں۔", "app.containers.IdeaCard.readMore": "مزید پڑھیں", "app.containers.IdeaCard.xComments": "{commentsCount, plural, =0 {کوئی تبصرہ نہیں} one {1 تبصرہ} other {# تبصرے}}", "app.containers.IdeaCard.xVotesOfY": "{xVotes, plural, =0 {کوئی ووٹ نہیں} one {1 ووٹ} other {# ووٹ}} {votingThreshold}میں سے", "app.containers.IdeaCards.a11y_closeFilterPanel": "فلٹرز پینل بند کریں۔", "app.containers.IdeaCards.a11y_totalItems": "کل پوسٹس: {ideasCount}", "app.containers.IdeaCards.all": "تمام", "app.containers.IdeaCards.allStatuses": "تمام سٹیٹس", "app.containers.IdeaCards.contributions": "شراکتیں", "app.containers.IdeaCards.ideaTerm": "خیالات", "app.containers.IdeaCards.initiatives": "اقدامات", "app.containers.IdeaCards.issueTerm": "مسائل", "app.containers.IdeaCards.list": "فہرست", "app.containers.IdeaCards.map": "نقشہ", "app.containers.IdeaCards.mostDiscussed": "سب سے زیادہ زیر بحث", "app.containers.IdeaCards.newest": "تازہ ترین", "app.containers.IdeaCards.noFilteredResults": "کوئی نتیجہ نہیں ملا۔ براہ کرم کوئی مختلف فلٹر یا تلاش کی اصطلاح آزمائیں۔", "app.containers.IdeaCards.numberResults": "نتائج ({postCount})", "app.containers.IdeaCards.oldest": "سب سے پرانا", "app.containers.IdeaCards.optionTerm": "اختیارات", "app.containers.IdeaCards.petitions": "پٹی<PERSON><PERSON>ز", "app.containers.IdeaCards.popular": "سب سے زیادہ ووٹ دیا۔", "app.containers.IdeaCards.projectFilterTitle": "پروجیکٹس", "app.containers.IdeaCards.projectTerm": "پروجیکٹس", "app.containers.IdeaCards.proposals": "تجاو<PERSON>ز", "app.containers.IdeaCards.questionTerm": "سوالات", "app.containers.IdeaCards.random": "بے ترتیب", "app.containers.IdeaCards.resetFilters": "فلٹرز کو دوبارہ ترتیب دیں۔", "app.containers.IdeaCards.showXResults": "دکھائیں {ideasCount, plural, one {# نتیجہ} other {# نتائج}}", "app.containers.IdeaCards.sortTitle": "چھانٹنا", "app.containers.IdeaCards.statusTitle": "حیثیت", "app.containers.IdeaCards.statusesTitle": "حیثیت", "app.containers.IdeaCards.topics": "ٹی<PERSON>ز", "app.containers.IdeaCards.topicsTitle": "ٹی<PERSON>ز", "app.containers.IdeaCards.trending": "ٹرینڈنگ", "app.containers.IdeaCards.tryDifferentFilters": "کوئی نتیجہ نہیں ملا۔ براہ کرم کوئی مختلف فلٹر یا تلاش کی اصطلاح آزمائیں۔", "app.containers.IdeaCards.xComments3": "{ideasCount, plural, no {{ideasCount} تبصرے} one {{ideasCount} تبصرہ} other {{ideasCount} تبصرے}}", "app.containers.IdeaCards.xContributions2": "{ideasCount, plural, no {{ideasCount} شراکتیں} one {{ideasCount} شراکت} other {{ideasCount} شراکتیں}}", "app.containers.IdeaCards.xIdeas2": "{ideasCount, plural, no {{ideasCount} خیالات} one {{ideasCount} خیال} other {{ideasCount} خیالات}}", "app.containers.IdeaCards.xInitiatives2": "{ideasCount, plural, no {{ideasCount} پہل} one {{ideasCount} پہل} other {{ideasCount} پہل}}", "app.containers.IdeaCards.xOptions2": "{ideasCount, plural, no {{ideasCount} اختیارات} one {{ideasCount} آپشن} other {{ideasCount} اختیارات}}", "app.containers.IdeaCards.xPetitions2": "{ideasCount, plural, no {{ideasCount} petitions} one {{ideasCount} petition} other {{ideasCount} petitions}}", "app.containers.IdeaCards.xProjects3": "{ideasCount, plural, no {{ideasCount} پروجیکٹس} one {{ideasCount} پروجیکٹ} other {{ideasCount} پروجیکٹس}}", "app.containers.IdeaCards.xProposals2": "{ideasCount, plural, no {{ideasCount} تجاویز} one {{ideasCount} تجویز} other {{ideasCount} تجاویز}}", "app.containers.IdeaCards.xQuestion2": "{ideasCount, plural, no {{ideasCount} سوالات} one {{ideasCount} سوال} other {{ideasCount} سوالات}}", "app.containers.IdeaCards.xResults": "{ideasCount, plural, one {# نتیجہ} other {# نتائج}}", "app.containers.IdeasEditPage.contributionFormTitle": "شراکت میں ترمیم کریں۔", "app.containers.IdeasEditPage.editedPostSave": "محفوظ کریں۔", "app.containers.IdeasEditPage.fileUploadError": "ایک یا زیادہ فائلیں اپ لوڈ کرنے میں ناکام ہوگئیں۔ براہ کرم فائل کا سائز اور فارمیٹ چیک کریں اور دوبارہ کوشش کریں۔", "app.containers.IdeasEditPage.formTitle": "آئیڈیا میں ترمیم کریں۔", "app.containers.IdeasEditPage.ideasEditMetaDescription": "اپنی پوسٹ میں ترمیم کریں۔ نئی معلومات شامل کریں اور پرانی معلومات کو تبدیل کریں۔", "app.containers.IdeasEditPage.ideasEditMetaTitle": "ترمیم کریں {postTitle} | {projectName}", "app.containers.IdeasEditPage.initiativeFormTitle": "پہل میں ترمیم کریں۔", "app.containers.IdeasEditPage.issueFormTitle": "تبصرہ میں ترمیم کریں۔", "app.containers.IdeasEditPage.optionFormTitle": "آپشن میں ترمیم کریں۔", "app.containers.IdeasEditPage.petitionFormTitle": "درخواست میں ترمیم کریں۔", "app.containers.IdeasEditPage.projectFormTitle": "پروجیکٹ میں ترمیم کریں۔", "app.containers.IdeasEditPage.proposalFormTitle": "تجویز میں ترمیم کریں۔", "app.containers.IdeasEditPage.questionFormTitle": "سوال میں ترمیم کریں۔", "app.containers.IdeasEditPage.save": "محفوظ کریں۔", "app.containers.IdeasEditPage.submitApiError": "فارم جمع کرانے میں ایک مسئلہ تھا۔ براہ کرم کسی بھی خرابی کی جانچ کریں اور دوبارہ کوشش کریں۔", "app.containers.IdeasIndexPage.a11y_IdeasListTitle1": "تمام ان پٹ پوسٹ کر دیے گئے۔", "app.containers.IdeasIndexPage.inputsIndexMetaDescription": "{orgName}کے شرکت کے پلیٹ فارم پر پوسٹ کیے گئے تمام ان پٹ کو دریافت کریں۔", "app.containers.IdeasIndexPage.inputsIndexMetaTitle1": "پوسٹس | {orgName}", "app.containers.IdeasIndexPage.inputsPageTitle": "پوسٹس", "app.containers.IdeasIndexPage.loadMore": "مزید لوڈ کریں...", "app.containers.IdeasIndexPage.loading": "لوڈ ہو رہا ہے...", "app.containers.IdeasNewPage.IdeasNewForm.inputsAssociatedWithProfile1": "پہلے سے طے شدہ طور پر آپ کی گذارشات آپ کے پروفائل کے ساتھ منسلک ہوں گی، جب تک کہ آپ اس اختیار کو منتخب نہ کریں۔", "app.containers.IdeasNewPage.IdeasNewForm.postAnonymously": "گمنام پوسٹ کریں۔", "app.containers.IdeasNewPage.IdeasNewForm.profileVisibility": "پروفائل کی مرئیت", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveDescription": "یہ سروے فی الحال جوابات کے لیے کھلا نہیں ہے۔ مزید معلومات کے لیے براہ کرم پروجیکٹ پر واپس جائیں۔", "app.containers.IdeasNewPage.SurveyNotActiveNotice.surveyNotActiveTitle": "یہ سروے فی الحال فعال نہیں ہے۔", "app.containers.IdeasNewPage.SurveySubmittedNotice.returnToProject": "پروجیکٹ پر واپس جائیں۔", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedDescription": "آپ یہ سروے پہلے ہی مکمل کر چکے ہیں۔", "app.containers.IdeasNewPage.SurveySubmittedNotice.surveySubmittedTitle": "سروے جمع کرایا", "app.containers.IdeasNewPage.SurveySubmittedNotice.thanksForResponse": "آپ کے جواب کے لیے شکریہ!", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_maxLength": "شراکت کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_contribution_body_multiloc_minLength": "آئیڈیا باڈی {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_maxLength": "تعاون کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_contribution_title_multiloc_minLength1": "تعاون کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_cosponsor_ids_required": "براہ کرم کم از کم ایک معاون منتخب کریں۔", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_maxLength": "خیال کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_minLength": "خیال کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_idea_body_multiloc_required": "براہ کرم تفصیل فراہم کریں۔", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_maxLength1": "خیال کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_idea_title_multiloc_minLength": "خیال کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_maxLength": "پہل کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_initiative_body_multiloc_minLength": "پہل کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_maxLength": "پہل کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_initiative_title_multiloc_minLength1": "پہل کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_maxLength": "مسئلہ کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_issue_body_multiloc_minLength": "مسئلے کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_maxLength": "شمارے کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_issue_title_multiloc_minLength": "شمارے کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_number_required": "یہ فیلڈ درکار ہے، براہ کرم ایک درست نمبر درج کریں۔", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_maxLength": "اختیار کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_option_body_multiloc_minLength1": "اختیار کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_maxLength": "اختیار کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_option_title_multiloc_minLength1": "اختیار کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_option_topic_ids_minItems": "براہ کرم کم از کم ایک ٹیگ منتخب کریں۔", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_maxLength": "درخواست کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_petition_body_multiloc_minLength": "درخواست کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_maxLength": "پٹیشن کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_petition_title_multiloc_minLength1": "پٹیشن کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_maxLength": "پروجیکٹ کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_project_body_multiloc_minLength": "پروجیکٹ کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_maxLength": "پروجیکٹ کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_project_title_multiloc_minLength1": "پروجیکٹ کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_maxLength": "تجویز کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_proposal_body_multiloc_minLength": "تجویز کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_maxLength": "تجویز کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_proposal_title_multiloc_minLength1": "تجویز کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_proposed_budget_required": "براہ کرم ایک نمبر درج کریں۔", "app.containers.IdeasNewPage.ajv_error_proposed_bugdet_type": "براہ کرم ایک نمبر درج کریں۔", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_maxLength": "سوال کی تفصیل {limit} حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_question_body_multiloc_minLength": "سوال کی تفصیل {limit} حروف سے زیادہ لمبی ہونی چاہیے۔", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_maxLength": "سوال کا عنوان {limit} حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_question_title_multiloc_minLength1": "سوال کا عنوان {limit} حروف سے زیادہ لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.ajv_error_title_multiloc_required": "براہ کرم ایک عنوان فراہم کریں۔", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_long": "شراکت کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_contribution_description_multiloc_too_short": "شراکت کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_long": "شراکت کا عنوان 80 حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_contribution_title_multiloc_too_short": "تعاون کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_long": "خیال کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_idea_description_multiloc_too_short": "خیال کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_blank": "براہ کرم ایک عنوان فراہم کریں۔", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_long": "خیال کا عنوان 80 حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_idea_title_multiloc_too_short": "خیال کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_includes_banned_words": "ہو سکتا ہے آپ نے ایک یا زیادہ الفاظ استعمال کیے ہوں جنہیں {guidelinesLink}کے ذریعے بے حرمتی سمجھا جاتا ہے۔ براہِ کرم اپنے متن کو تبدیل کریں تاکہ کوئی بھی گستاخیاں جو موجود ہو اسے ہٹا دیں۔", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_long": "پہل کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_initiative_description_multiloc_too_short": "پہل کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_long": "پہل کا عنوان 80 حروف سے کم لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_initiative_title_multiloc_too_short": "پہل کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_long": "مسئلہ کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_issue_description_multiloc_too_short": "مسئلہ کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_long": "شمارے کا عنوان 80 حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_issue_title_multiloc_too_short": "شمارے کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_long": "اختیار کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_option_description_multiloc_too_short": "اختیار کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_long": "آپشن ٹائٹل 80 حروف سے کم لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_option_title_multiloc_too_short": "اختیار کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_long": "درخواست کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_petition_description_multiloc_too_short": "درخواست کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_long": "پٹیشن کا عنوان 80 حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_petition_title_multiloc_too_short": "پٹیشن کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_long": "پروجیکٹ کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_project_description_multiloc_too_short": "پروجیکٹ کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_long": "پروجیکٹ کا عنوان 80 حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_project_title_multiloc_too_short": "پروجیکٹ کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_long": "تجویز کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_proposal_description_multiloc_too_short": "تجویز کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_long": "تجویز کا عنوان 80 حروف سے کم لمبا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_proposal_title_multiloc_too_short": "تجویز کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_question_description_multiloc_blank": "براہ کرم تفصیل فراہم کریں۔", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_long": "سوال کی تفصیل 80 حروف سے کم ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_question_description_multiloc_too_short": "سوال کی تفصیل کم از کم 30 حروف کی ہونی چاہیے۔", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_long": "سوال کا عنوان 80 حروف سے کم ہونا چاہیے۔", "app.containers.IdeasNewPage.api_error_question_title_multiloc_too_short": "سوال کا عنوان کم از کم 10 حروف کا ہونا چاہیے۔", "app.containers.IdeasNewPage.cancelLeaveSurveyButtonText": "منسوخ کریں۔", "app.containers.IdeasNewPage.confirmLeaveFormButtonText": "ہاں، میں جانا چاہتا ہوں۔", "app.containers.IdeasNewPage.contributionMetaTitle1": "پروجیکٹ میں نیا تعاون شامل کریں | {orgName}", "app.containers.IdeasNewPage.editSurvey": "سروے میں ترمیم کریں۔", "app.containers.IdeasNewPage.ideaNewMetaDescription": "ایک عرضی پوسٹ کریں اور {orgName}کے شرکت کے پلیٹ فارم پر گفتگو میں شامل ہوں۔", "app.containers.IdeasNewPage.ideaNewMetaTitle1": "پروجیکٹ میں نیا آئیڈیا شامل کریں | {orgName}", "app.containers.IdeasNewPage.initiativeMetaTitle1": "پروجیکٹ میں نئی پہل شامل کریں | {orgName}", "app.containers.IdeasNewPage.issueMetaTitle1": "پروجیکٹ میں نیا شمارہ شامل کریں | {orgName}", "app.containers.IdeasNewPage.leaveFormConfirmationQuestion": "کیا آپ واقعی چھوڑنا چاہتے ہیں؟", "app.containers.IdeasNewPage.leaveFormTextLoggedIn": "آپ کے جوابات کے مسودے کو نجی طور پر محفوظ کر لیا گیا ہے اور آپ اسے بعد میں مکمل کرنے کے لیے واپس آ سکتے ہیں۔", "app.containers.IdeasNewPage.leaveSurvey": "سروے چھوڑ دیں۔", "app.containers.IdeasNewPage.leaveSurveyText": "آپ کے جوابات محفوظ نہیں ہوں گے۔", "app.containers.IdeasNewPage.optionMetaTitle1": "پروجیکٹ میں نیا آپشن شامل کریں | {orgName}", "app.containers.IdeasNewPage.petitionMetaTitle1": "پروجیکٹ میں نئی پٹیشن شامل کریں | {orgName}", "app.containers.IdeasNewPage.projectMetaTitle1": "پروجیکٹ میں نیا پروجیکٹ شامل کریں | {orgName}", "app.containers.IdeasNewPage.proposalMetaTitle1": "پروجیکٹ میں نئی تجویز شامل کریں | {orgName}", "app.containers.IdeasNewPage.questionMetaTitle1": "پروجیکٹ میں نیا سوال شامل کریں | {orgName}", "app.containers.IdeasNewPage.surveyNewMetaTitle2": "{surveyTitle} | {orgName}", "app.containers.IdeasShow.Cosponsorship.co-sponsorAcceptInvitation": "دعوت قبول کریں۔", "app.containers.IdeasShow.Cosponsorship.co-sponsorInvitation": "شریک کفالت کی دعوت", "app.containers.IdeasShow.Cosponsorship.co-sponsors": "شریک سپانسرز", "app.containers.IdeasShow.Cosponsorship.cosponsorDescription": "آپ کو شریک کفیل بننے کے لیے مدعو کیا گیا ہے۔", "app.containers.IdeasShow.Cosponsorship.cosponsorInvitationAccepted": "دعوت قبول کر لی", "app.containers.IdeasShow.Cosponsorship.pending": "زیر التواء", "app.containers.IdeasShow.MetaInformation.attachments": "منسلکات", "app.containers.IdeasShow.MetaInformation.byUserOnDate": "{userName} {date}پر", "app.containers.IdeasShow.MetaInformation.currentStatus": "موجودہ حیثیت", "app.containers.IdeasShow.MetaInformation.location": "مقام", "app.containers.IdeasShow.MetaInformation.postedBy": "کی طرف سے پوسٹ کیا گیا", "app.containers.IdeasShow.MetaInformation.similar": "ملتے جلتے ان پٹ", "app.containers.IdeasShow.MetaInformation.topics": "ٹی<PERSON>ز", "app.containers.IdeasShow.commentCTA": "ایک تبصرہ شامل کریں۔", "app.containers.IdeasShow.contributionEmailSharingBody": "اس شراکت کی حمایت کریں '{postTitle}' پر {postUrl}!", "app.containers.IdeasShow.contributionEmailSharingSubject": "اس شراکت کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.contributionSharingModalTitle": "اپنا تعاون جمع کرانے کا شکریہ!", "app.containers.IdeasShow.contributionTwitterMessage": "اس شراکت کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.contributionWhatsAppMessage": "اس شراکت کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.currentStatus": "موجودہ حیثیت", "app.containers.IdeasShow.deletedUser": "نامعلوم مصنف", "app.containers.IdeasShow.ideaEmailSharingBody": "{ideaUrl}پر میرے خیال '{ideaTitle}' کی حمایت کریں!", "app.containers.IdeasShow.ideaEmailSharingSubject": "میرے خیال کی حمایت کریں: {ideaTitle}۔", "app.containers.IdeasShow.ideaTwitterMessage": "اس خیال کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.ideaWhatsAppMessage": "اس خیال کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.ideasWhatsAppMessage": "اس تبصرے کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.imported": "در<PERSON><PERSON><PERSON> شدہ", "app.containers.IdeasShow.importedTooltip": "یہ {inputTerm} آف لائن جمع کیا گیا تھا اور خود بخود پلیٹ فارم پر اپ لوڈ ہو گیا تھا۔", "app.containers.IdeasShow.initiativeEmailSharingBody": "{ideaUrl}پر اس اقدام کی حمایت کریں '{ideaTitle}' !", "app.containers.IdeasShow.initiativeEmailSharingSubject": "اس اقدام کی حمایت کریں: {ideaTitle}", "app.containers.IdeasShow.initiativeSharingModalTitle": "اپنی پہل جمع کرانے کا شکریہ!", "app.containers.IdeasShow.initiativeTwitterMessage": "اس اقدام کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.initiativeWhatsAppMessage": "اس اقدام کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.issueEmailSharingBody": "اس تبصرے کی حمایت کریں '{postTitle}' پر {postUrl}!", "app.containers.IdeasShow.issueEmailSharingSubject": "اس تبصرے کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.issueSharingModalTitle": "اپنا تبصرہ جمع کرانے کا شکریہ!", "app.containers.IdeasShow.issueTwitterMessage": "اس تبصرے کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.metaTitle": "ان پٹ: {inputTitle} | {orgName}", "app.containers.IdeasShow.optionEmailSharingBody": "{postUrl}پر اس آپشن '{postTitle}' کی حمایت کریں!", "app.containers.IdeasShow.optionEmailSharingSubject": "اس اختیار کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.optionSharingModalTitle": "آپ کا اختیار کامیابی کے ساتھ پوسٹ کر دیا گیا ہے!", "app.containers.IdeasShow.optionTwitterMessage": "اس اختیار کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.optionWhatsAppMessage": "اس اختیار کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.petitionEmailSharingBody": "{ideaUrl}پر اس پٹیشن '{ideaTitle}' کی حمایت کریں!", "app.containers.IdeasShow.petitionEmailSharingSubject": "اس پٹیشن کی حمایت کریں: {ideaTitle}", "app.containers.IdeasShow.petitionSharingModalTitle": "اپنی درخواست جمع کروانے کے لیے شکریہ!", "app.containers.IdeasShow.petitionTwitterMessage": "اس پٹیشن کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.petitionWhatsAppMessage": "اس پٹیشن کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.projectEmailSharingBody": "اس پروجیکٹ کی حمایت کریں '{postTitle}' پر {postUrl}!", "app.containers.IdeasShow.projectEmailSharingSubject": "اس پروجیکٹ کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.projectSharingModalTitle": "اپنے پروجیکٹ کو جمع کرانے کا شکریہ!", "app.containers.IdeasShow.projectTwitterMessage": "اس پروجیکٹ کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.projectWhatsAppMessage": "اس پروجیکٹ کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.proposalEmailSharingBody": "اس تجویز کی حمایت کریں '{ideaTitle}' پر {ideaUrl}!", "app.containers.IdeasShow.proposalEmailSharingSubject": "اس تجویز کی حمایت کریں: {ideaTitle}", "app.containers.IdeasShow.proposalSharingModalTitle": "اپنی تجویز پیش کرنے کا شکریہ!", "app.containers.IdeasShow.proposalTwitterMessage": "اس تجویز کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.proposalWhatsAppMessage": "اس تجویز کی حمایت کریں: {postTitle}", "app.containers.IdeasShow.proposals.VoteControl.a11y_timeLeft": "ووٹ ڈالنے کا وقت باقی ہے:", "app.containers.IdeasShow.proposals.VoteControl.a11y_xVotesOfRequiredY": "{votingThreshold} مطلوبہ ووٹوں میں سے {xVotes}", "app.containers.IdeasShow.proposals.VoteControl.cancelVote": "ووٹ منسوخ کریں۔", "app.containers.IdeasShow.proposals.VoteControl.days": "دن", "app.containers.IdeasShow.proposals.VoteControl.guidelinesLinkText": "ہماری ہدایات", "app.containers.IdeasShow.proposals.VoteControl.hours": "گھنٹے", "app.containers.IdeasShow.proposals.VoteControl.invisibleTitle": "حیثیت اور ووٹ", "app.containers.IdeasShow.proposals.VoteControl.minutes": "منٹ", "app.containers.IdeasShow.proposals.VoteControl.moreInfo": "مزید معلومات", "app.containers.IdeasShow.proposals.VoteControl.vote": "ووٹ", "app.containers.IdeasShow.proposals.VoteControl.voted": "ووٹ دیا۔", "app.containers.IdeasShow.proposals.VoteControl.votedText": "جب یہ اقدام اگلے مرحلے پر جائے گا تو آپ کو مطلع کیا جائے گا۔ {x, plural, =0 {{xDays} باقی ہے۔} one {{xDays} باقی ہے۔} other {{xDays} باقی ہیں۔}}", "app.containers.IdeasShow.proposals.VoteControl.votedTitle": "آپ کا ووٹ جمع کر دیا گیا ہے!", "app.containers.IdeasShow.proposals.VoteControl.votingNotPermitted1": "بدقسمتی سے، آپ اس تجویز پر ووٹ نہیں دے سکتے۔ {link}میں کیوں پڑھیں۔", "app.containers.IdeasShow.proposals.VoteControl.xDays": "{x, plural, =0 {ایک دن سے کم} one {ایک دن} other {# دن}}", "app.containers.IdeasShow.proposals.VoteControl.xVotes": "{count, plural, =0 {کوئی ووٹ نہیں} one {1 ووٹ} other {# ووٹ}}", "app.containers.IdeasShow.questionEmailSharingBody": "اس سوال کے بارے میں بحث میں شامل ہوں '{postTitle}' {postUrl}پر !", "app.containers.IdeasShow.questionEmailSharingSubject": "بحث میں شامل ہوں: {postTitle}", "app.containers.IdeasShow.questionSharingModalTitle": "آپ کا سوال کامیابی کے ساتھ پوسٹ کر دیا گیا ہے!", "app.containers.IdeasShow.questionTwitterMessage": "بحث میں شامل ہوں: {postTitle}", "app.containers.IdeasShow.questionWhatsAppMessage": "بحث میں شامل ہوں: {postTitle}", "app.containers.IdeasShow.reportAsSpamModalTitle": "آپ اس کی بطور سپام رپورٹ کیوں کرنا چاہتے ہیں؟", "app.containers.IdeasShow.share": "شیئر کریں۔", "app.containers.IdeasShow.sharingModalSubtitle": "زیادہ سے زیادہ لوگوں تک پہنچیں اور اپنی آواز سنائیں۔", "app.containers.IdeasShow.sharingModalTitle": "اپنا خیال پیش کرنے کا شکریہ!", "app.containers.Navbar.completeOnboarding": "Complete onboarding", "app.containers.Navbar.completeProfile": "مکمل پروفائل", "app.containers.Navbar.confirmEmail2": "ای میل کی تصدیق کریں۔", "app.containers.Navbar.unverified": "<PERSON>یر تصدیق شدہ", "app.containers.Navbar.verified": "تصدیق شدہ", "app.containers.NewAuthModal.beforeYouFollow": "اس سے پہلے کہ آپ پیروی کریں۔", "app.containers.NewAuthModal.beforeYouParticipate": "اس سے پہلے کہ آپ شرکت کریں۔", "app.containers.NewAuthModal.completeYourProfile": "اپنا پروفائل مکمل کریں۔", "app.containers.NewAuthModal.confirmYourEmail": "اپنے ای میل کی تصدیق کریں۔", "app.containers.NewAuthModal.logIn": "لاگ ان کریں۔", "app.containers.NewAuthModal.steps.AcceptPolicies.reviewTheTerms": "جاری رکھنے کے لیے نیچے دی گئی شرائط کا جائزہ لیں۔", "app.containers.NewAuthModal.steps.BuiltInFields.pleaseCompleteYourProfile": "براہ کرم اپنا پروفائل مکمل کریں۔", "app.containers.NewAuthModal.steps.EmailAndPassword.goBackToLoginOptions": "لاگ ان کے اختیارات پر واپس جائیں۔", "app.containers.NewAuthModal.steps.EmailAndPassword.goToSignUp": "اکاؤنٹ نہیں ہے؟ {goToOtherFlowLink}", "app.containers.NewAuthModal.steps.EmailAndPassword.signUp": "سائن اپ کریں۔", "app.containers.NewAuthModal.steps.EmailConfirmation.codeMustHaveFourDigits": "کوڈ میں 4 ہندسوں کا ہونا ضروری ہے۔", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.continueWithFranceConnect": "FranceConnect کے ساتھ جاری رکھیں", "app.containers.NewAuthModal.steps.EmailSignUp.SSOButtons.noAuthenticationMethodsEnabled": "اس پلیٹ فارم پر تصدیق کا کوئی طریقہ فعال نہیں ہے۔", "app.containers.NewAuthModal.steps.EmailSignUp.byContinuing": "جاری رکھ کر، آپ اس پلیٹ فارم سے ای میلز وصول کرنے سے اتفاق کرتے ہیں۔ آپ 'میری ترتیبات' صفحہ میں منتخب کر سکتے ہیں کہ آپ کون سی ای میلز وصول کرنا چاہتے ہیں۔", "app.containers.NewAuthModal.steps.EmailSignUp.email": "ای میل", "app.containers.NewAuthModal.steps.EmailSignUp.emailFormatError": "درست فارمیٹ میں ایک ای میل پتہ فراہم کریں، مثال کے طور پر <EMAIL>", "app.containers.NewAuthModal.steps.EmailSignUp.emailMissingError": "ایک ای میل ایڈریس فراہم کریں۔", "app.containers.NewAuthModal.steps.EmailSignUp.enterYourEmailAddress": "جاری رکھنے کے لیے اپنا ای میل ایڈریس درج کریں۔", "app.containers.NewAuthModal.steps.Password.forgotPassword": "پاس ورڈ بھول گئے؟", "app.containers.NewAuthModal.steps.Password.logInToYourAccount": "اپنے اکاؤنٹ میں لاگ ان کریں: {account}", "app.containers.NewAuthModal.steps.Password.noPasswordError": "براہ کرم اپنا پاس ورڈ درج کریں۔", "app.containers.NewAuthModal.steps.Password.password": "پاس ورڈ", "app.containers.NewAuthModal.steps.Password.rememberMe": "مجھے یاد رکھنا", "app.containers.NewAuthModal.steps.Password.rememberMeTooltip": "عوامی کمپیوٹر استعمال کرنے پر منتخب نہ کریں۔", "app.containers.NewAuthModal.steps.Success.allDone": "سب ہو گیا", "app.containers.NewAuthModal.steps.Success.nowContinueYourParticipation": "اب اپنی شرکت جاری رکھیں۔", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedSubtitle": "آپ کی شناخت کی تصدیق ہو چکی ہے۔ اب آپ اس پلیٹ فارم پر کمیونٹی کے مکمل رکن ہیں۔", "app.containers.NewAuthModal.steps.VerificationSuccess.userVerifiedTitle": "اب آپ تصدیق شدہ ہیں!", "app.containers.NewAuthModal.steps.close": "بند", "app.containers.NewAuthModal.steps.continue": "جاری رکھیں", "app.containers.NewAuthModal.whatAreYouInterestedIn": "آپ کو کس چیز میں دلچسپی ہے؟", "app.containers.NewAuthModal.youCantParticipate": "آپ شرکت نہیں کر سکتے", "app.containers.NotificationMenu.a11y_notificationsLabel": "{count, plural, =0 {کوئی غیر دیکھی ہوئی اطلاعات نہیں} one {1 غیر دیکھی ہوئی اطلاع} other {# غیر دیکھی اطلاعات}}", "app.containers.NotificationMenu.adminRightsReceived": "اب آپ پلیٹ فارم کے منتظم ہیں۔", "app.containers.NotificationMenu.commentDeletedByAdminFor1": "\"{postTitle}\" پر آپ کا تبصرہ ایک منتظم نے حذف کر دیا ہے کیونکہ\n      {reasonCode, select, irrelevant {یہ غیر متعلقہ ہے} inappropriate {اس کا مواد نامناسب ہے} other {{otherReason}}}", "app.containers.NotificationMenu.cosponsorOfYourIdea": "{name} نے آپ کی شریک کفالت کی دعوت قبول کر لی", "app.containers.NotificationMenu.deletedUser": "نامعلوم مصنف", "app.containers.NotificationMenu.error": "اطلاعات کو لوڈ نہیں کیا جا سکا", "app.containers.NotificationMenu.internalCommentOnIdeaAssignedToYou": "{name} نے آپ کو تفویض کردہ ان پٹ پر اندرونی طور پر تبصرہ کیا۔", "app.containers.NotificationMenu.internalCommentOnIdeaYouCommentedInternallyOn": "{name} نے اندرونی طور پر ایک ان پٹ پر تبصرہ کیا جس پر آپ نے اندرونی طور پر تبصرہ کیا۔", "app.containers.NotificationMenu.internalCommentOnIdeaYouModerate": "{name} نے آپ کے زیر انتظام پروجیکٹ میں داخلے پر تبصرہ کیا۔", "app.containers.NotificationMenu.internalCommentOnUnassignedUnmoderatedIdea": "{name} نے ایک غیر منظم پروجیکٹ میں غیر تفویض کردہ ان پٹ پر اندرونی طور پر تبصرہ کیا", "app.containers.NotificationMenu.internalCommentOnYourInternalComment": "{name} نے آپ کے اندرونی تبصرے پر تبصرہ کیا۔", "app.containers.NotificationMenu.invitationToCosponsorContribution": "{name} نے آپ کو تعاون کے لیے مدعو کیا ہے۔", "app.containers.NotificationMenu.invitationToCosponsorIdea": "{name} نے آپ کو ایک آئیڈیا کو شریک سپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.invitationToCosponsorInitiative": "{name} نے آپ کو ایک تجویز کو اسپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.invitationToCosponsorIssue": "{name} نے آپ کو ایک ایشو کو شریک سپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.invitationToCosponsorOption": "{name} نے آپ کو ایک آپشن کو شریک سپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.invitationToCosponsorPetition": "{name} نے آپ کو ایک پٹیشن کو شریک سپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.invitationToCosponsorProject": "{name} نے آپ کو ایک پروجیکٹ کو شریک سپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.invitationToCosponsorProposal": "{name} نے آپ کو ایک تجویز کو شریک سپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.invitationToCosponsorQuestion": "{name} نے آپ کو ایک سوال کو شریک سپانسر کرنے کی دعوت دی۔", "app.containers.NotificationMenu.loadMore": "مزید لوڈ کریں...", "app.containers.NotificationMenu.loading": "اطلاعات لوڈ ہو رہی ہیں...", "app.containers.NotificationMenu.mentionInComment": "{name} نے ایک تبصرہ میں آپ کا ذکر کیا۔", "app.containers.NotificationMenu.mentionInInternalComment": "{name} نے اندرونی تبصرے میں آپ کا تذکرہ کیا۔", "app.containers.NotificationMenu.mentionInOfficialFeedback": "{name} نے ایک آفیشل اپ ڈیٹ میں آپ کا تذکرہ کیا۔", "app.containers.NotificationMenu.nativeSurveyNotSubmitted": "آپ نے اپنا سروے جمع نہیں کرایا", "app.containers.NotificationMenu.noNotifications": "آپ کے پاس ابھی تک کوئی اطلاع نہیں ہے۔", "app.containers.NotificationMenu.notificationsLabel": "اطلاعات", "app.containers.NotificationMenu.officialFeedbackOnContributionYouFollow": "{officialName} نے اس شراکت کے بارے میں ایک آفیشل اپ ڈیٹ دیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.officialFeedbackOnIdeaYouFollow": "{officialName} نے آپ کی پیروی کرنے والے آئیڈیا پر ایک آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.officialFeedbackOnInitiativeYouFollow": "{officialName} نے آپ کی پیروی کرنے والے اقدام پر ایک آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.officialFeedbackOnIssueYouFollow": "{officialName} نے آپ کی پیروی کرنے والے مسئلے پر ایک آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.officialFeedbackOnOptionYouFollow": "{officialName} نے آپ کی پیروی کرنے والے آپشن پر آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.officialFeedbackOnPetitionYouFollow": "{officialName} نے آپ کی پیروی کی درخواست پر ایک آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.officialFeedbackOnProjectYouFollow": "{officialName} نے آپ کی پیروی کرنے والے پروجیکٹ پر آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.officialFeedbackOnProposalYouFollow": "{officialName} نے آپ کی پیروی کی تجویز پر ایک آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.officialFeedbackOnQuestionYouFollow": "{officialName} نے آپ کے پیروی کیے گئے سوال پر ایک آفیشل اپ ڈیٹ دیا۔", "app.containers.NotificationMenu.postAssignedToYou": "{postTitle} آپ کو تفویض کیا گیا تھا۔", "app.containers.NotificationMenu.projectModerationRightsReceived": "اب آپ {projectLink}کے پروجیکٹ مینیجر ہیں۔", "app.containers.NotificationMenu.projectPhaseStarted": "{projectTitle} ایک نئے مرحلے میں داخل ہوا۔", "app.containers.NotificationMenu.projectPhaseUpcoming": "{projectTitle} {phaseStartAt}پر ایک نئے مرحلے میں داخل ہوگا۔", "app.containers.NotificationMenu.projectPublished": "ایک نیا پروجیکٹ شائع ہوا۔", "app.containers.NotificationMenu.projectReviewRequest": "{name} نے پروجیکٹ \"{projectTitle}\" کو شائع کرنے کی منظوری کی درخواست کی", "app.containers.NotificationMenu.projectReviewStateChange": "{name} نے اشاعت کے لیے \"{projectTitle}\" کو منظوری دے دی۔", "app.containers.NotificationMenu.statusChangedOnIdeaYouFollow": "{ideaTitle} اسٹیٹس {status}میں تبدیل ہو گیا ہے۔", "app.containers.NotificationMenu.thresholdReachedForAdmin": "{post} ووٹنگ کی حد تک پہنچ گیا۔", "app.containers.NotificationMenu.userAcceptedYourInvitation": "{name} نے آپ کی دعوت قبول کر لی", "app.containers.NotificationMenu.userCommentedOnContributionYouFollow": "{name} نے اس شراکت پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnIdeaYouFollow": "{name} نے ایک خیال پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnInitiativeYouFollow": "{name} نے ایک ایسے اقدام پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnIssueYouFollow": "{name} نے اس مسئلے پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnOptionYouFollow": "{name} نے ایک آپشن پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnPetitionYouFollow": "{name} نے ایک درخواست پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnProjectYouFollow": "{name} نے ایک ایسے پروجیکٹ پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnProposalYouFollow": "{name} نے اس تجویز پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userCommentedOnQuestionYouFollow": "{name} نے ایک سوال پر تبصرہ کیا جس کی آپ پیروی کرتے ہیں۔", "app.containers.NotificationMenu.userMarkedPostAsSpam1": "{name} نے \"{postTitle}\" کو بطور سپام رپورٹ کیا۔", "app.containers.NotificationMenu.userReactedToYourComment": "{name} نے آپ کے تبصرے پر ردعمل ظاہر کیا۔", "app.containers.NotificationMenu.userReportedCommentAsSpam1": "{name} نے \"{postTitle}\" پر بطور سپام ایک تبصرہ کی اطلاع دی", "app.containers.NotificationMenu.votingBasketNotSubmitted": "آپ نے اپنا ووٹ جمع نہیں کرایا", "app.containers.NotificationMenu.votingBasketSubmitted": "آپ نے کامیابی سے ووٹ دیا۔", "app.containers.NotificationMenu.votingLastChance": "{phaseTitle}کو ووٹ دینے کا آخری موقع", "app.containers.NotificationMenu.votingResults": "{phaseTitle} ووٹ کے نتائج سامنے آ گئے۔", "app.containers.NotificationMenu.xAssignedPostToYou": "{name} تفویض کردہ {postTitle} آپ کو", "app.containers.PasswordRecovery.emailError": "یہ ایک درست ای میل نہیں لگتا", "app.containers.PasswordRecovery.emailLabel": "ای میل", "app.containers.PasswordRecovery.emailPlaceholder": "میرا ای میل پتہ", "app.containers.PasswordRecovery.helmetDescription": "اپنا پاس ورڈ صفحہ دوبارہ ترتیب دیں۔", "app.containers.PasswordRecovery.helmetTitle": "اپنا پاس ورڈ دوبارہ ترتیب دیں۔", "app.containers.PasswordRecovery.passwordResetSuccessMessage": "اگر یہ ای میل پتہ پلیٹ فارم پر رجسٹرڈ ہے تو پاس ورڈ ری سیٹ کرنے کا لنک بھیجا گیا ہے۔", "app.containers.PasswordRecovery.resetPassword": "پاس ورڈ دوبارہ ترتیب دینے کا لنک بھیجیں۔", "app.containers.PasswordRecovery.submitError": "ہمیں اس ای میل سے لنک کردہ اکاؤنٹ نہیں مل سکا۔ آپ اس کے بجائے سائن اپ کرنے کی کوشش کر سکتے ہیں۔", "app.containers.PasswordRecovery.subtitle": "ہم نیا پاس ورڈ منتخب کرنے کے لیے لنک کہاں بھیج سکتے ہیں؟", "app.containers.PasswordRecovery.title": "پاس ورڈ ری سیٹ", "app.containers.PasswordReset.helmetDescription": "اپنا پاس ورڈ صفحہ دوبارہ ترتیب دیں۔", "app.containers.PasswordReset.helmetTitle": "اپنا پاس ورڈ دوبارہ ترتیب دیں۔", "app.containers.PasswordReset.login": "لاگ ان کریں۔", "app.containers.PasswordReset.passwordError": "پاس ورڈ کم از کم 8 حروف کا ہونا چاہیے۔", "app.containers.PasswordReset.passwordLabel": "پاس ورڈ", "app.containers.PasswordReset.passwordPlaceholder": "نیا پاس ورڈ", "app.containers.PasswordReset.passwordUpdatedSuccessMessage": "آپ کا پاس ورڈ کامیابی کے ساتھ اپ ڈیٹ ہو گیا ہے۔", "app.containers.PasswordReset.pleaseLogInMessage": "براہ کرم اپنے نئے پاس ورڈ کے ساتھ لاگ ان کریں۔", "app.containers.PasswordReset.requestNewPasswordReset": "نیا پاس ورڈ دوبارہ ترتیب دینے کی درخواست کریں۔", "app.containers.PasswordReset.submitError": "کچھ غلط ہو گیا۔ براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.PasswordReset.title": "اپنا پاس ورڈ دوبارہ ترتیب دیں۔", "app.containers.PasswordReset.updatePassword": "نئے پاس ورڈ کی تصدیق کریں۔", "app.containers.ProjectFolderCards.allProjects": "تمام منصوبے", "app.containers.ProjectFolderCards.currentlyWorkingOn": "{orgName} فی الحال کام کر رہا ہے۔", "app.containers.ProjectFolderShowPage.editFolder": "فولڈر میں ترمیم کریں۔", "app.containers.ProjectFolderShowPage.invisibleTitleMainContent": "اس منصوبے کے بارے میں معلومات", "app.containers.ProjectFolderShowPage.metaTitle1": "فولڈر: {title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderTwitterMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.projectFolderWhatsAppMessage": "{title} | {orgName}", "app.containers.ProjectFolderShowPage.readMore": "مزید پڑھیں", "app.containers.ProjectFolderShowPage.seeLess": "کم دیکھیں", "app.containers.ProjectFolderShowPage.share": "شیئر کریں۔", "app.containers.Projects.PollForm.document": "دستاویز", "app.containers.Projects.PollForm.formCompleted": "شکریہ! آپ کا جواب موصول ہو گیا ہے۔", "app.containers.Projects.PollForm.maxOptions": "زیادہ سے زیادہ {maxNumber}", "app.containers.Projects.PollForm.pollDisabledAlreadyResponded": "آپ پہلے ہی یہ پول کر چکے ہیں۔", "app.containers.Projects.PollForm.pollDisabledNotActivePhase1": "یہ رائے شماری صرف اس وقت کی جا سکتی ہے جب یہ مرحلہ فعال ہو۔", "app.containers.Projects.PollForm.pollDisabledNotPermitted": "یہ پول فی الحال فعال نہیں ہے۔", "app.containers.Projects.PollForm.pollDisabledNotPossible": "اس رائے شماری کو لینا فی الحال ناممکن ہے۔", "app.containers.Projects.PollForm.pollDisabledProjectInactive": "پول اب دستیاب نہیں ہے کیونکہ یہ پروجیکٹ مزید فعال نہیں ہے۔", "app.containers.Projects.PollForm.sendAnswer": "بھیجیں۔", "app.containers.Projects.a11y_phase": "مرحلہ {phaseNumber}: {phaseTitle}", "app.containers.Projects.a11y_phasesOverview": "مراحل کا جائزہ", "app.containers.Projects.a11y_titleInputs": "تمام ان پٹ اس پروجیکٹ میں جمع کرائے گئے ہیں۔", "app.containers.Projects.a11y_titleInputsPhase": "تمام ان پٹ اس مرحلے میں جمع کرائے گئے ہیں۔", "app.containers.Projects.accessRights": "رسائی کے حقوق", "app.containers.Projects.addedToBasket": "ٹوکری میں شامل کیا گیا۔", "app.containers.Projects.allocateBudget": "اپنا بجٹ مختص کریں۔", "app.containers.Projects.archived": "محف<PERSON><PERSON> شدہ", "app.containers.Projects.basketSubmitted": "ٹوکری جمع کر دی گئی ہے!", "app.containers.Projects.contributions": "شراکتیں", "app.containers.Projects.createANewPhase": "ایک نیا مرحلہ بنائیں", "app.containers.Projects.currentPhase": "موجودہ مرحلہ", "app.containers.Projects.document": "دستاویز", "app.containers.Projects.editProject": "پروجیکٹ میں ترمیم کریں۔", "app.containers.Projects.emailSharingBody": "اس اقدام کے بارے میں آپ کا کیا خیال ہے؟ اس پر ووٹ دیں اور اپنی آواز سننے کے لیے {initiativeUrl} پر بحث کا اشتراک کریں!", "app.containers.Projects.emailSharingSubject": "میرے اقدام کی حمایت کریں: {initiativeTitle}.", "app.containers.Projects.endedOn": "{date}کو ختم ہوا۔", "app.containers.Projects.events": "واقعات", "app.containers.Projects.header": "پروجیکٹس", "app.containers.Projects.ideas": "خیالات", "app.containers.Projects.information": "معلومات", "app.containers.Projects.initiatives": "اقدامات", "app.containers.Projects.invisbleTitleDocumentAnnotation1": "دستاویز کا جائزہ لیں۔", "app.containers.Projects.invisibleTitlePhaseAbout": "اس مرحلے کے بارے میں", "app.containers.Projects.invisibleTitlePoll": "رائے شماری کریں۔", "app.containers.Projects.invisibleTitleSurvey": "سروے میں حصہ لیں۔", "app.containers.Projects.issues": "تبصرے", "app.containers.Projects.liveDataMessage": "آپ ریئل ٹائم ڈیٹا دیکھ رہے ہیں۔ منتظمین کے لیے شرکاء کی تعداد کو مسلسل اپ ڈیٹ کیا جاتا ہے۔ براہ کرم نوٹ کریں کہ عام صارفین کو کیش شدہ ڈیٹا نظر آتا ہے، جس کے نتیجے میں تعداد میں معمولی فرق ہو سکتا ہے۔", "app.containers.Projects.location": "مقام:", "app.containers.Projects.manageBasket": "ٹوکری کا انتظام کریں۔", "app.containers.Projects.meetMinBudgetRequirement": "ٹوکری جمع کرانے کے لیے کم از کم بجٹ کو پورا کریں۔", "app.containers.Projects.meetMinSelectionRequirement": "ٹوکری جمع کرانے کے لیے کم از کم بجٹ کو پورا کریں۔", "app.containers.Projects.metaTitle1": "پروجیکٹ: {projectTitle} | {orgName}", "app.containers.Projects.minBudgetRequired": "کم از کم بجٹ درکار ہے۔", "app.containers.Projects.myBasket": "ٹوکری۔", "app.containers.Projects.navPoll": "رائے شماری", "app.containers.Projects.navSurvey": "سروے", "app.containers.Projects.newPhase": "نیا مرحلہ", "app.containers.Projects.nextPhase": "اگلا مرحلہ", "app.containers.Projects.noEndDate": "کوئی اختتامی تاریخ نہیں۔", "app.containers.Projects.noItems": "آپ نے ابھی تک کوئی آئٹم منتخب نہیں کیا ہے۔", "app.containers.Projects.noPastEvents": "ظاہر کرنے کے لیے کوئی ماضی کے واقعات نہیں ہیں۔", "app.containers.Projects.noPhaseSelected": "کوئی مرحلہ منتخب نہیں کیا گیا۔", "app.containers.Projects.noUpcomingOrOngoingEvents": "کوئی آنے والا یا جاری ایونٹ فی الحال شیڈول نہیں ہے۔", "app.containers.Projects.offlineVotersTooltip": "یہ نمبر کسی بھی آف لائن ووٹر کی گنتی کی عکاسی نہیں کرتا ہے۔", "app.containers.Projects.options": "اختیارات", "app.containers.Projects.participants": "شرکاء", "app.containers.Projects.participantsTooltip4": "یہ نمبر گمنام سروے کی گذارشات کی بھی عکاسی کرتا ہے۔ اگر سروے ہر کسی کے لیے کھلے ہیں تو گمنام سروے کی گذارشات ممکن ہیں (اس پروجیکٹ کے لیے {accessRightsLink} ٹیب دیکھیں)۔", "app.containers.Projects.pastEvents": "ماضی کے واقعات", "app.containers.Projects.petitions": "پٹی<PERSON><PERSON>ز", "app.containers.Projects.phases": "مرا<PERSON>ل", "app.containers.Projects.previousPhase": "پچھلا مرحلہ", "app.containers.Projects.project": "پروجیکٹ", "app.containers.Projects.projectTwitterMessage": "اپنی آواز سنو! {projectName} | میں شرکت کریں۔ {orgName}", "app.containers.Projects.projects": "پروجیکٹس", "app.containers.Projects.proposals": "تجاو<PERSON>ز", "app.containers.Projects.questions": "سوالات", "app.containers.Projects.readLess": "کم پڑھیں", "app.containers.Projects.readMore": "مزید پڑھیں", "app.containers.Projects.removeItem": "آئٹم کو ہٹا دیں۔", "app.containers.Projects.requiredSelection": "مطلوبہ انتخاب", "app.containers.Projects.reviewDocument": "دستاویز کا جائزہ لیں۔", "app.containers.Projects.seeTheContributions": "شراکتیں دیکھیں", "app.containers.Projects.seeTheIdeas": "آئیڈیاز دیکھیں", "app.containers.Projects.seeTheInitiatives": "اقدامات دیکھیں", "app.containers.Projects.seeTheIssues": "تبصرے دیکھیں", "app.containers.Projects.seeTheOptions": "اختیارات دیکھیں", "app.containers.Projects.seeThePetitions": "درخواستیں دیکھیں", "app.containers.Projects.seeTheProjects": "پروجیکٹس دیکھیں", "app.containers.Projects.seeTheProposals": "تجاویز دیکھیں", "app.containers.Projects.seeTheQuestions": "سوالات دیکھیں", "app.containers.Projects.seeUpcomingEvents": "آنے والے واقعات دیکھیں", "app.containers.Projects.share": "شیئر کریں۔", "app.containers.Projects.shareThisProject": "اس پروجیکٹ کو شیئر کریں۔", "app.containers.Projects.submitMyBasket": "ٹوکری جمع کروائیں۔", "app.containers.Projects.survey": "سروے", "app.containers.Projects.takeThePoll": "رائے شماری کریں۔", "app.containers.Projects.takeTheSurvey": "سروے میں حصہ لیں۔", "app.containers.Projects.timeline": "ٹائم لائن", "app.containers.Projects.upcomingAndOngoingEvents": "آنے والے اور جاری واقعات", "app.containers.Projects.upcomingEvents": "آنے والے واقعات", "app.containers.Projects.whatsAppMessage": "{projectName} | {orgName}کے شرکت کے پلیٹ فارم سے", "app.containers.Projects.yourBudget": "کل بجٹ", "app.containers.ProjectsIndexPage.metaDescription": "یہ سمجھنے کے لیے کہ آپ کس طرح حصہ لے سکتے ہیں {orgName} کے تمام جاری پروجیکٹس کو دریافت کریں۔\n آؤ ان مقامی منصوبوں پر بات کریں جو آپ کے لیے سب سے اہم ہیں۔", "app.containers.ProjectsIndexPage.metaTitle1": "پروجیکٹس | {orgName}", "app.containers.ProjectsIndexPage.pageTitle": "پروجیکٹس", "app.containers.ProjectsShowPage.VolunteeringSection.becomeVolunteerButton": "میں شرکت کرنا چاہتا ہوں۔", "app.containers.ProjectsShowPage.VolunteeringSection.notLoggedIn": "اس سرگرمی کے لیے رضاکارانہ طور پر کام کرنے کے لیے براہ کرم پہلے {signInLink} یا {signUpLink}", "app.containers.ProjectsShowPage.VolunteeringSection.notOpenParticipation": "اس سرگرمی کے لیے فی الحال شرکت کھلی نہیں ہے۔", "app.containers.ProjectsShowPage.VolunteeringSection.signInLinkText": "لاگ ان", "app.containers.ProjectsShowPage.VolunteeringSection.signUpLinkText": "سائن اپ کریں", "app.containers.ProjectsShowPage.VolunteeringSection.withdrawVolunteerButton": "میں رضاکارانہ طور پر اپنی پیشکش واپس لیتا ہوں۔", "app.containers.ProjectsShowPage.VolunteeringSection.xVolunteers": "{x, plural, =0 {کوئی شریک نہیں} one {# شریک} other {# شرکاء}}", "app.containers.ProjectsShowPage.process.survey.embeddedSurveyScreenReaderWarning1": "انتباہ: ایمبیڈڈ سروے میں اسکرین ریڈر صارفین کے لیے قابل رسائی مسائل ہو سکتے ہیں۔ اگر آپ کو کوئی چیلنج درپیش ہے، تو براہ کرم اصل پلیٹ فارم سے سروے کا لنک حاصل کرنے کے لیے پلیٹ فارم کے منتظم سے رابطہ کریں۔ متبادل طور پر، آپ سروے کو پُر کرنے کے لیے دوسرے طریقوں سے درخواست کر سکتے ہیں۔", "app.containers.ProjectsShowPage.process.survey.survey": "سروے", "app.containers.ProjectsShowPage.process.survey.surveyDisabledMaybeNotPermitted": "یہ جاننے کے لیے کہ آیا آپ اس سروے میں حصہ لے سکتے ہیں، براہ کرم پہلے پلیٹ فارم پر {logInLink} جائیں۔", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActivePhase": "یہ سروے صرف اس وقت لیا جا سکتا ہے جب ٹائم لائن میں یہ مرحلہ فعال ہو۔", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotActiveUser": "سروے میں حصہ لینے کے لیے براہ کرم {completeRegistrationLink} ۔", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotPermitted": "یہ سروے فی الحال فعال نہیں ہے۔", "app.containers.ProjectsShowPage.process.survey.surveyDisabledNotVerified": "اس سروے میں حصہ لینے کے لیے آپ کی شناخت کی تصدیق ضروری ہے۔ {verificationLink}", "app.containers.ProjectsShowPage.process.survey.surveyDisabledProjectInactive2": "سروے اب دستیاب نہیں ہے، کیونکہ یہ پروجیکٹ اب فعال نہیں ہے۔", "app.containers.ProjectsShowPage.shared.ParticipationPermission.completeRegistrationLinkText": "مکمل رجسٹریشن", "app.containers.ProjectsShowPage.shared.ParticipationPermission.logInLinkText": "لاگ ان", "app.containers.ProjectsShowPage.shared.ParticipationPermission.signUpLinkText": "سائن اپ کریں", "app.containers.ProjectsShowPage.shared.ParticipationPermission.verificationLinkText": "ابھی اپنے اکاؤنٹ کی تصدیق کریں۔", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledMaybeNotPermitted": "صرف مخصوص صارفین ہی اس دستاویز کا جائزہ لے سکتے ہیں۔ براہ کرم پہلے {signUpLink} یا {logInLink} ۔", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActivePhase1": "اس دستاویز کا تب ہی جائزہ لیا جا سکتا ہے جب یہ مرحلہ فعال ہو۔", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotActiveUser": "دستاویز کا جائزہ لینے کے لیے براہ کرم {completeRegistrationLink} ۔", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotPermitted": "بدقسمتی سے، آپ کے پاس اس دستاویز کا جائزہ لینے کے حقوق نہیں ہیں۔", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledNotVerified": "اس دستاویز کا جائزہ لینے کے لیے آپ کے اکاؤنٹ کی تصدیق کی ضرورت ہے۔ {verificationLink}", "app.containers.ProjectsShowPage.shared.document_annotation.documentAnnotationDisabledProjectInactive": "دستاویز اب دستیاب نہیں ہے، کیونکہ یہ پروجیکٹ مزید فعال نہیں ہے۔", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberManualVoters2": "{manualVoters, plural, one {(بشمول 1 آف لائن)} other {(بشمول # آف لائن)}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBar.numberOfPicks": "{baskets, plural, one {1 پک} other {# پک}}", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.budgetingTooltip1": "شرکاء کا فیصد جنہوں نے یہ اختیار اٹھایا۔", "app.containers.ProjectsShowPage.timeline.VotingResults.ProgressBars.votingTooltip": "اس اختیار کو موصول ہونے والے کل ووٹوں کا فیصد۔", "app.containers.ProjectsShowPage.timeline.VotingResults.cost": "لاگت:", "app.containers.ProjectsShowPage.timeline.VotingResults.showMore": "مزید دکھائیں", "app.containers.ReactionControl.a11y_likesDislikes": "کل پسندیدگیاں: {likesCount}، کل ناپسندیدگی: {dislikesCount}", "app.containers.ReactionControl.cancelDislikeSuccess": "آپ نے اس ان پٹ کے لیے اپنی ناپسندیدگی کو کامیابی سے منسوخ کر دیا۔", "app.containers.ReactionControl.cancelLikeSuccess": "آپ نے اس ان پٹ کے لیے اپنی پسند کو کامیابی سے منسوخ کر دیا۔", "app.containers.ReactionControl.dislikeSuccess": "آپ نے اس ان پٹ کو کامیابی سے ناپسند کیا۔", "app.containers.ReactionControl.likeSuccess": "آپ نے یہ ان پٹ کامیابی کے ساتھ پسند کیا۔", "app.containers.ReactionControl.reactionErrorSubTitle": "غلطی کی وجہ سے آپ کا رد عمل درج نہیں ہو سکا۔ براہ کرم چند منٹوں میں دوبارہ کوشش کریں۔", "app.containers.ReactionControl.reactionSuccessTitle": "آپ کا ردعمل کامیابی کے ساتھ رجسٹر ہو گیا!", "app.containers.ReactionControl.vote": "ووٹ", "app.containers.ReactionControl.voted": "ووٹ دیا۔", "app.containers.SearchInput.a11y_cancelledPostingComment": "تبصرہ پوسٹ کرنا منسوخ کر دیا گیا۔", "app.containers.SearchInput.a11y_commentsHaveChanged": "{sortOder} تبصرے لوڈ ہو گئے ہیں۔", "app.containers.SearchInput.a11y_eventsHaveChanged1": "{numberOfEvents, plural, =0 {# ایونٹس لوڈ ہو چکے ہیں} one {# ایونٹ لوڈ ہو گیا ہے} other {# ایونٹس لوڈ ہو گئے ہیں}}۔", "app.containers.SearchInput.a11y_projectsHaveChanged1": "{numberOfFilteredResults, plural, =0 {# نتائج لوڈ ہو گئے ہیں} one {# نتیجہ لوڈ ہو گیا ہے} other {# نتائج لوڈ ہو گئے ہیں}}۔", "app.containers.SearchInput.a11y_searchResultsHaveChanged1": "{numberOfSearchResults, plural, =0 {# تلاش کے نتائج لوڈ ہو چکے ہیں} one {# تلاش کا نتیجہ لوڈ ہو گیا ہے} other {# تلاش کے نتائج لوڈ ہو گئے ہیں}}۔", "app.containers.SearchInput.removeSearchTerm": "تلاش کی اصطلاح کو ہٹا دیں۔", "app.containers.SearchInput.searchAriaLabel": "تلاش کریں۔", "app.containers.SearchInput.searchLabel": "تلاش کریں۔", "app.containers.SearchInput.searchPlaceholder": "تلاش کریں۔", "app.containers.SearchInput.searchTerm": "تلاش کی اصطلاح: {searchTerm}", "app.containers.SignIn.franceConnectIsTheSolutionProposedByTheGovernment": "FranceConnect 700 سے زیادہ آن لائن خدمات کے لیے سائن اپ کو محفوظ اور آسان بنانے کے لیے فرانسیسی ریاست کا تجویز کردہ حل ہے۔", "app.containers.SignIn.or": "یا", "app.containers.SignIn.signInError": "فراہم کردہ معلومات درست نہیں ہیں۔ 'پاس ورڈ بھول گئے؟' اپنا پاس ورڈ دوبارہ ترتیب دینے کے لیے۔", "app.containers.SignIn.useFranceConnectToLoginCreateOrVerifyYourAccount": "لاگ ان، سائن اپ یا اپنے اکاؤنٹ کی تصدیق کے لیے FranceConnect کا استعمال کریں۔", "app.containers.SignIn.whatIsFranceConnect": "فرانس کنیکٹ کیا ہے؟", "app.containers.SignUp.adminOptions2": "منتظمین اور پروجیکٹ مینیجرز کے لیے", "app.containers.SignUp.backToSignUpOptions": "سائن اپ کے اختیارات پر واپس جائیں۔", "app.containers.SignUp.continue": "جاری رکھیں", "app.containers.SignUp.emailConsent": "سائن اپ کرکے، آپ اس پلیٹ فارم سے ای میلز وصول کرنے سے اتفاق کرتے ہیں۔ آپ 'میری ترتیبات' صفحہ میں منتخب کر سکتے ہیں کہ آپ کون سی ای میلز وصول کرنا چاہتے ہیں۔", "app.containers.SignUp.emptyFirstNameError": "اپنا پہلا نام درج کریں۔", "app.containers.SignUp.emptyLastNameError": "اپنا آخری نام درج کریں۔", "app.containers.SignUp.firstNamesLabel": "پہلا نام", "app.containers.SignUp.goToLogIn": "پہلے سے ہی اکاؤنٹ ہے؟ {goToOtherFlowLink}", "app.containers.SignUp.iHaveReadAndAgreeToPrivacy": "میں نے پڑھ لیا ہے اور اس سے اتفاق کرتا ہوں {link}۔", "app.containers.SignUp.iHaveReadAndAgreeToTerms": "میں نے پڑھ لیا ہے اور اس سے اتفاق کرتا ہوں {link}۔", "app.containers.SignUp.iHaveReadAndAgreeToVienna": "میں قبول کرتا ہوں کہ ڈیٹا mitgestalten.wien.gv.at پر استعمال کیا جائے گا۔ مزید معلومات {link}سے مل سکتی ہیں۔", "app.containers.SignUp.invitationErrorText": "آپ کی دعوت کی میعاد ختم ہو چکی ہے یا پہلے ہی استعمال ہو چکی ہے۔ اگر آپ اکاؤنٹ بنانے کے لیے دعوتی لنک پہلے ہی استعمال کر چکے ہیں، تو سائن ان کرنے کی کوشش کریں۔ بصورت دیگر، نیا اکاؤنٹ بنانے کے لیے سائن اپ کریں۔", "app.containers.SignUp.lastNameLabel": "آخری نام", "app.containers.SignUp.onboarding.topicsAndAreas.followAreasOfFocus": "ان کے بارے میں مطلع کرنے کے لیے اپنے فوکس کے شعبوں پر عمل کریں:", "app.containers.SignUp.onboarding.topicsAndAreas.followYourFavoriteTopics": "ان کے بارے میں مطلع کرنے کے لیے اپنے پسندیدہ عنوانات پر عمل کریں:", "app.containers.SignUp.onboarding.topicsAndAreas.savePreferences": "ترجیحات کو محفوظ کریں۔", "app.containers.SignUp.onboarding.topicsAndAreas.skipForNow": "ابھی کے لیے چھوڑ دیں۔", "app.containers.SignUp.privacyPolicyNotAcceptedError": "آگے بڑھنے کے لیے ہماری پرائیویسی پالیسی کو قبول کریں۔", "app.containers.SignUp.signUp2": "سائن اپ کریں۔", "app.containers.SignUp.skip": "اس قدم کو چھوڑ دیں۔", "app.containers.SignUp.tacError": "آگے بڑھنے کے لیے ہماری شرائط و ضوابط کو قبول کریں۔", "app.containers.SignUp.thePrivacyPolicy": "رازداری کی پالیسی", "app.containers.SignUp.theTermsAndConditions": "شرائط و ضوابط", "app.containers.SignUp.unknownError": "{tenantName, select, LiberalDemocrats {ایسا لگتا ہے کہ آپ نے عمل مکمل کیے بغیر پہلے سائن اپ کرنے کی کوشش کی تھی۔ اس کے بجائے لاگ ان پر کلک کریں، پچھلی کوشش کے دوران منتخب کردہ اسناد کا استعمال کرتے ہوئے۔} other {کچھ غلط ہو گیا۔ براہ کرم بعد میں دوبارہ کوشش کریں۔}}", "app.containers.SignUp.viennaConsentEmail": "ای میل ایڈریس", "app.containers.SignUp.viennaConsentFirstName": "پہلا نام", "app.containers.SignUp.viennaConsentFooter": "آپ سائن ان کرنے کے بعد اپنی پروفائل کی معلومات تبدیل کر سکتے ہیں۔ اگر آپ کے پاس پہلے سے ہی mitgestalten.wien.gv.at پر ایک ہی ای میل ایڈریس والا اکاؤنٹ ہے، تو اسے آپ کے موجودہ اکاؤنٹ سے منسلک کر دیا جائے گا۔", "app.containers.SignUp.viennaConsentHeader": "مندرجہ ذیل ڈیٹا منتقل کیا جائے گا:", "app.containers.SignUp.viennaConsentLastName": "آخری نام", "app.containers.SignUp.viennaConsentUserName": "صارف کا نام", "app.containers.SignUp.viennaDataProtection": "ویانا کی رازداری کی پالیسی", "app.containers.SiteMap.contributions": "شراکتیں", "app.containers.SiteMap.cookiePolicyLinkTitle": "کو<PERSON><PERSON>ز", "app.containers.SiteMap.issues": "تبصرے", "app.containers.SiteMap.options": "اختیارات", "app.containers.SiteMap.projects": "پروجیکٹس", "app.containers.SiteMap.questions": "سوالات", "app.containers.SpamReport.buttonSave": "رپورٹ", "app.containers.SpamReport.buttonSuccess": "کامیابی", "app.containers.SpamReport.inappropriate": "یہ نامناسب یا ناگوار ہے۔", "app.containers.SpamReport.messageError": "فارم جمع کرانے میں ایک خامی تھی، براہ کرم دوبارہ کوشش کریں۔", "app.containers.SpamReport.messageSuccess": "آپ کی رپورٹ بھیج دی گئی ہے۔", "app.containers.SpamReport.other": "دوسری وجہ", "app.containers.SpamReport.otherReasonPlaceholder": "تفصیل", "app.containers.SpamReport.wrong_content": "یہ متعلقہ نہیں ہے۔", "app.containers.UsersEditPage.a11y_imageDropzoneRemoveIconAriaTitle": "پروفائل تصویر ہٹا دیں۔", "app.containers.UsersEditPage.activeProposalVotesWillBeDeleted": "ان تجاویز پر آپ کے ووٹ جو ابھی ووٹنگ کے لیے کھلے ہیں حذف کر دیے جائیں گے۔ ان تجاویز پر ووٹ جہاں ووٹنگ کا دورانیہ بند ہو گیا ہے حذف نہیں کیا جائے گا۔", "app.containers.UsersEditPage.addPassword": "پاس ورڈ شامل کریں۔", "app.containers.UsersEditPage.becomeVerifiedSubtitle": "ان منصوبوں میں حصہ لینے کے لیے جن کی تصدیق کی ضرورت ہوتی ہے۔", "app.containers.UsersEditPage.becomeVerifiedTitle": "اپنی شناخت کی تصدیق کریں۔", "app.containers.UsersEditPage.bio": "آپ کے بارے میں", "app.containers.UsersEditPage.bio_placeholder": " ", "app.containers.UsersEditPage.blockedVerified": "آپ اس فیلڈ میں ترمیم نہیں کر سکتے کیونکہ یہ تصدیق شدہ معلومات پر مشتمل ہے۔", "app.containers.UsersEditPage.buttonSuccessLabel": "کامیابی", "app.containers.UsersEditPage.cancel": "منسوخ کریں۔", "app.containers.UsersEditPage.changeEmail": "ای میل تبدیل کریں۔", "app.containers.UsersEditPage.changePassword2": "پاس ورڈ تبدیل کریں۔", "app.containers.UsersEditPage.clickHereToUpdateVerification": "اپنی تصدیق کو اپ ڈیٹ کرنے کے لیے براہ کرم یہاں کلک کریں۔", "app.containers.UsersEditPage.conditionsLinkText": "ہمارے حالات", "app.containers.UsersEditPage.contactUs": "چھوڑنے کی ایک اور وجہ؟ {feedbackLink} اور شاید ہم مدد کر سکتے ہیں۔", "app.containers.UsersEditPage.deleteAccountSubtext": "ہمیں آپ کو جاتے دیکھ کر افسوس ہوا۔", "app.containers.UsersEditPage.deleteMyAccount": "میرا اکاؤنٹ حذف کر دیں۔", "app.containers.UsersEditPage.deleteYourAccount": "اپنا اکاؤنٹ حذف کریں۔", "app.containers.UsersEditPage.deletionSection": "اپنا اکاؤنٹ حذف کریں۔", "app.containers.UsersEditPage.deletionSubtitle": "اس کارروائی کو کالعدم نہیں کیا جا سکتا۔ پلیٹ فارم پر آپ کے شائع کردہ مواد کو گمنام رکھا جائے گا۔ اگر آپ اپنا تمام مواد حذف کرنا چاہتے ہیں تو آپ ہم سے <EMAIL> پر رابطہ کر سکتے ہیں۔", "app.containers.UsersEditPage.email": "ای میل", "app.containers.UsersEditPage.emailEmptyError": "ایک ای میل ایڈریس فراہم کریں۔", "app.containers.UsersEditPage.emailInvalidError": "درست فارمیٹ میں ایک ای میل پتہ فراہم کریں، مثال کے طور پر <EMAIL>", "app.containers.UsersEditPage.feedbackLinkText": "ہمیں بتائیں", "app.containers.UsersEditPage.feedbackLinkUrl": "https://citizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.UsersEditPage.firstNames": "پہلا نام", "app.containers.UsersEditPage.firstNamesEmptyError": "پہلا نام فراہم کریں۔", "app.containers.UsersEditPage.h1": "آپ کے اکاؤنٹ کی معلومات", "app.containers.UsersEditPage.h1sub": "اپنے اکاؤنٹ کی معلومات میں ترمیم کریں۔", "app.containers.UsersEditPage.image": "اوتار کی تصویر", "app.containers.UsersEditPage.imageDropzonePlaceholder": "پروفائل تصویر منتخب کرنے کے لیے کلک کریں (زیادہ سے زیادہ 5MB)", "app.containers.UsersEditPage.invisibleTitleUserSettings": "آپ کے پروفائل کے لیے تمام ترتیبات", "app.containers.UsersEditPage.language": "زبان", "app.containers.UsersEditPage.lastName": "آخری نام", "app.containers.UsersEditPage.lastNameEmptyError": "آخری نام فراہم کریں۔", "app.containers.UsersEditPage.loading": "لوڈ ہو رہا ہے...", "app.containers.UsersEditPage.loginCredentialsSubtitle": "آپ یہاں اپنا ای میل یا پاس ورڈ تبدیل کر سکتے ہیں۔", "app.containers.UsersEditPage.loginCredentialsTitle": "لاگ ان کی اسناد", "app.containers.UsersEditPage.messageError": "ہم آپ کا پروفائل محفوظ نہیں کر سکے۔ بعد میں دوبارہ کوشش کریں یا <EMAIL> سے رابطہ کریں۔", "app.containers.UsersEditPage.messageSuccess": "آپ کا پروفائل محفوظ کر لیا گیا ہے۔", "app.containers.UsersEditPage.metaDescription": "یہ {tenantName}کے آن لائن شرکت کے پلیٹ فارم پر {firstName} {lastName} کا پروفائل سیٹنگ کا صفحہ ہے۔ یہاں آپ اپنی شناخت کی تصدیق کر سکتے ہیں، اپنے اکاؤنٹ کی معلومات میں ترمیم کر سکتے ہیں، اپنا اکاؤنٹ حذف کر سکتے ہیں اور اپنی ای میل کی ترجیحات میں ترمیم کر سکتے ہیں۔", "app.containers.UsersEditPage.metaTitle1": "پروفائل سیٹنگ کا صفحہ {firstName} {lastName} | {orgName}", "app.containers.UsersEditPage.noGoingBack": "اس بٹن پر کلک کرنے کے بعد، ہمارے پاس آپ کا اکاؤنٹ بحال کرنے کا کوئی طریقہ نہیں ہوگا۔", "app.containers.UsersEditPage.noNameWarning2": "آپ کا نام فی الحال پلیٹ فارم پر اس طرح دکھایا گیا ہے: \"{displayName}\" کیونکہ آپ نے اپنا نام درج نہیں کیا ہے۔ یہ ایک خودکار نام ہے۔ اگر آپ اسے تبدیل کرنا چاہتے ہیں، تو براہ کرم نیچے اپنا نام درج کریں۔", "app.containers.UsersEditPage.notificationsSubTitle": "آپ کس قسم کی ای میل اطلاعات موصول کرنا چاہتے ہیں؟ ", "app.containers.UsersEditPage.notificationsTitle": "ای میل اطلاعات", "app.containers.UsersEditPage.password": "ایک نیا پاس ورڈ منتخب کریں۔", "app.containers.UsersEditPage.password.minimumPasswordLengthError": "ایک پاس ورڈ فراہم کریں جو کم از کم {minimumPasswordLength} حروف کا ہو۔", "app.containers.UsersEditPage.passwordAddSection": "پاس ورڈ شامل کریں۔", "app.containers.UsersEditPage.passwordAddSubtitle2": "ہر بار اپنے ای میل کی تصدیق کیے بغیر پاس ورڈ سیٹ کریں اور پلیٹ فارم پر آسانی سے لاگ ان ہوں۔", "app.containers.UsersEditPage.passwordChangeSection": "اپنا پاس ورڈ تبدیل کریں۔", "app.containers.UsersEditPage.passwordChangeSubtitle": "اپنے موجودہ پاس ورڈ کی تصدیق کریں اور نئے پاس ورڈ میں تبدیل کریں۔", "app.containers.UsersEditPage.privacyReasons": "اگر آپ اپنی رازداری کے بارے میں فکر مند ہیں، تو آپ پڑھ سکتے ہیں {conditionsLink}۔", "app.containers.UsersEditPage.processing": "بھیج رہا ہے...", "app.containers.UsersEditPage.provideFirstNameIfLastName": "آخری نام فراہم کرتے وقت پہلا نام درکار ہے۔", "app.containers.UsersEditPage.reasonsToStayListTitle": "جانے سے پہلے...", "app.containers.UsersEditPage.submit": "تبدیلیاں محفوظ کریں۔", "app.containers.UsersEditPage.tooManyEmails": "بہت زیادہ ای میلز موصول ہو رہی ہیں؟ آپ اپنی پروفائل کی ترتیبات میں اپنی ای میل کی ترجیحات کا نظم کر سکتے ہیں۔", "app.containers.UsersEditPage.updateverification": "کیا آپ کی سرکاری معلومات میں تبدیلی آئی؟ {reverifyButton}", "app.containers.UsersEditPage.user": "آپ کب چاہتے ہیں کہ ہم آپ کو مطلع کرنے کے لیے ایک ای میل بھیجیں؟", "app.containers.UsersEditPage.verifiedIdentitySubtitle": "آپ ان منصوبوں میں حصہ لے سکتے ہیں جن کی تصدیق کی ضرورت ہوتی ہے۔", "app.containers.UsersEditPage.verifiedIdentityTitle": "آپ تصدیق شدہ ہیں۔", "app.containers.UsersEditPage.verifyNow": "ابھی تصدیق کریں۔", "app.containers.UsersShowPage.Surveys.SurveySubmissionCard.downloadYourResponses2": "اپنے جوابات ڈاؤن لوڈ کریں (.xlsx)", "app.containers.UsersShowPage.a11y_likesCount": "{likesCount, plural, =0 {کوئی پسند نہیں} one {1 پسند} other {# پسند}}", "app.containers.UsersShowPage.a11y_postCommentPostedIn": "ان پٹ جس کے جواب میں یہ تبصرہ پوسٹ کیا گیا تھا:", "app.containers.UsersShowPage.areas": "علاقے", "app.containers.UsersShowPage.commentsWithCount": "تبصرے ({commentsCount})", "app.containers.UsersShowPage.editProfile": "میرے پروفائل میں ترمیم کریں۔", "app.containers.UsersShowPage.emptyInfoText": "آپ اوپر دیے گئے فلٹر کے کسی آئٹم کی پیروی نہیں کر رہے ہیں۔", "app.containers.UsersShowPage.eventsWithCount": "واقعات ({eventsCount})", "app.containers.UsersShowPage.followingWithCount": "پیروی کر رہا ہے ({followingCount})", "app.containers.UsersShowPage.inputs": "ان پٹ", "app.containers.UsersShowPage.invisibleTitlePostsList": "تمام ان پٹ اس شرکت کنندہ کے ذریعہ جمع کرائے گئے ہیں۔", "app.containers.UsersShowPage.invisibleTitleUserComments": "اس شریک کے ذریعہ پوسٹ کردہ تمام تبصرے۔", "app.containers.UsersShowPage.loadMore": "مزید لوڈ کریں۔", "app.containers.UsersShowPage.loadMoreComments": "مزید تبصرے لوڈ کریں۔", "app.containers.UsersShowPage.loadingComments": "تبصرے لوڈ ہو رہے ہیں...", "app.containers.UsersShowPage.loadingEvents": "ایونٹس لوڈ ہو رہے ہیں...", "app.containers.UsersShowPage.memberSince": "{date}سے ممبر", "app.containers.UsersShowPage.metaTitle1": "{firstName} {lastName} | کا پروفائل صفحہ {orgName}", "app.containers.UsersShowPage.noCommentsForUser": "اس شخص نے ابھی تک کوئی تبصرہ پوسٹ نہیں کیا ہے۔", "app.containers.UsersShowPage.noCommentsForYou": "یہاں ابھی تک کوئی تبصرہ نہیں ہے۔", "app.containers.UsersShowPage.noEventsForUser": "آپ نے ابھی تک کسی تقریب میں شرکت نہیں کی ہے۔", "app.containers.UsersShowPage.postsWithCount": "گذارشات ({ideasCount})", "app.containers.UsersShowPage.projectFolders": "پروجیکٹ فولڈرز", "app.containers.UsersShowPage.projects": "پروجیکٹس", "app.containers.UsersShowPage.proposals": "تجاو<PERSON>ز", "app.containers.UsersShowPage.seePost": "جمع کروانا دیکھیں", "app.containers.UsersShowPage.surveyResponses": "جوابات ({responses})", "app.containers.UsersShowPage.topics": "موضوعات", "app.containers.UsersShowPage.tryAgain": "ایک خرابی پیش آ گئی ہے، براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.UsersShowPage.userShowPageMetaDescription": "یہ {orgName}کے آن لائن شرکت کے پلیٹ فارم پر {firstName} {lastName} کا پروفائل صفحہ ہے۔ یہاں ان کے تمام ان پٹ کا ایک جائزہ ہے۔", "app.containers.VoteControl.close": "بند", "app.containers.VoteControl.voteErrorTitle": "کچھ غلط ہو گیا۔", "app.containers.admin.ContentBuilder.default": "پہلے سے طے شدہ", "app.containers.admin.ContentBuilder.imageTextCards": "تصویر اور ٹیکسٹ کارڈز", "app.containers.admin.ContentBuilder.infoWithAccordions": "معلومات اور موافقت", "app.containers.admin.ContentBuilder.oneColumnLayout": "1 کالم", "app.containers.admin.ContentBuilder.projectDescription": "پروجیکٹ کی تفصیل", "app.containers.app.navbar.admin": "پلیٹ فارم کا نظم کریں۔", "app.containers.app.navbar.allProjects": "تمام منصوبے", "app.containers.app.navbar.ariaLabel": "پرائمری", "app.containers.app.navbar.closeMobileNavMenu": "موبائل نیویگیشن مینو بند کریں۔", "app.containers.app.navbar.editProfile": "میری ترتیبات", "app.containers.app.navbar.fullMobileNavigation": "مکمل موبائل", "app.containers.app.navbar.logIn": "لاگ ان کریں۔", "app.containers.app.navbar.logoImgAltText": "{orgName} گھر", "app.containers.app.navbar.myProfile": "میری سرگرمی", "app.containers.app.navbar.search": "تلاش کریں۔", "app.containers.app.navbar.showFullMenu": "مکمل مینو دکھائیں۔", "app.containers.app.navbar.signOut": "سائن آؤٹ کریں۔", "app.containers.eventspage.errorWhenFetchingEvents": "ایونٹس لوڈ کرتے وقت ایک خرابی پیش آگئی۔ براہ کرم صفحہ کو دوبارہ لوڈ کرنے کی کوشش کریں۔", "app.containers.eventspage.events": "واقعات", "app.containers.eventspage.eventsPageDescription": "{orgName}کے پلیٹ فارم پر پوسٹ کیے گئے تمام واقعات دکھائیں۔", "app.containers.eventspage.eventsPageTitle1": "واقعات | {orgName}", "app.containers.eventspage.filterDropdownTitle": "پروجیکٹس", "app.containers.eventspage.noPastEvents": "ظاہر کرنے کے لیے کوئی ماضی کے واقعات نہیں ہیں۔", "app.containers.eventspage.noUpcomingOrOngoingEvents": "کوئی آنے والا یا جاری ایونٹ فی الحال شیڈول نہیں ہے۔", "app.containers.eventspage.pastEvents": "ماضی کے واقعات", "app.containers.eventspage.upcomingAndOngoingEvents": "آنے والے اور جاری واقعات", "app.containers.footer.accessibility-statement": "قابل رسائی بیان", "app.containers.footer.ariaLabel": "ثانوی", "app.containers.footer.cookie-policy": "کوکی پالیسی", "app.containers.footer.cookieSettings": "کوکی کی ترتیبات", "app.containers.footer.feedbackEmptyError": "فیڈ بیک فیلڈ خالی نہیں ہو سکتی۔", "app.containers.footer.poweredBy": "کی طرف سے طاقت", "app.containers.footer.privacy-policy": "رازداری کی پالیسی", "app.containers.footer.siteMap": "سائٹ کا نقشہ", "app.containers.footer.terms-and-conditions": "شرائط و ضوابط", "app.containers.ideaHeading.cancelLeaveIdeaButtonText": "منسوخ کریں۔", "app.containers.ideaHeading.confirmLeaveFormButtonText": "ہاں، میں جانا چاہتا ہوں۔", "app.containers.ideaHeading.editForm": "فارم میں ترمیم کریں۔", "app.containers.ideaHeading.leaveFormConfirmationQuestion": "کیا آپ واقعی چھوڑنا چاہتے ہیں؟", "app.containers.ideaHeading.leaveIdeaForm": "آئیڈیا فارم چھوڑیں۔", "app.containers.ideaHeading.leaveIdeaText": "آپ کے جوابات محفوظ نہیں ہوں گے۔", "app.containers.landing.cityProjects": "پروجیکٹس", "app.containers.landing.completeProfile": "اپنا پروفائل مکمل کریں۔", "app.containers.landing.completeYourProfile": "خوش آمدید، {firstName}۔ یہ آپ کا پروفائل مکمل کرنے کا وقت ہے۔", "app.containers.landing.createAccount": "سائن اپ کریں۔", "app.containers.landing.defaultSignedInMessage": "{orgName} آپ کو سن رہا ہے۔ اپنی آواز سنانے کی باری ہے!", "app.containers.landing.doItLater": "میں بعد میں کروں گا۔", "app.containers.landing.new": "نیا", "app.containers.landing.subtitleCity": "{orgName}کے شرکت کے پلیٹ فارم میں خوش آمدید", "app.containers.landing.titleCity": "آئیے مل کر {orgName} کے مستقبل کو تشکیل دیں۔", "app.containers.landing.twitterMessage": "{ideaTitle} کو ووٹ دیں۔", "app.containers.landing.upcomingEventsWidgetTitle": "آنے والے اور جاری واقعات", "app.containers.landing.userDeletedSubtitle": "آپ کسی بھی وقت نیا اکاؤنٹ بنا سکتے ہیں یا {contactLink} ہمیں بتا سکتے ہیں کہ ہم کیا بہتر کر سکتے ہیں۔", "app.containers.landing.userDeletedSubtitleLinkText": "ہمیں ایک لائن چھوڑ دو", "app.containers.landing.userDeletedSubtitleLinkUrl": "https://cizenlabco.typeform.com/to/z7baRP?source={url}", "app.containers.landing.userDeletedTitle": "آپ کا اکاؤنٹ حذف کر دیا گیا ہے۔", "app.containers.landing.userDeletionFailed": "آپ کے اکاؤنٹ کو حذف کرنے میں ایک خرابی پیش آگئی، ہمیں اس مسئلے کے بارے میں مطلع کر دیا گیا ہے اور اسے ٹھیک کرنے کی پوری کوشش کریں گے۔ براہ کرم بعد میں دوبارہ کوشش کریں۔", "app.containers.landing.verifyNow": "ابھی تصدیق کریں۔", "app.containers.landing.verifyYourIdentity": "اپنی شناخت کی تصدیق کریں۔", "app.containers.landing.viewAllEventsText": "تمام واقعات دیکھیں", "app.containers.projectsShowPage.folderGoBackButton.backToFolder": "فولڈر پر واپس جائیں۔", "app.errors.after_end_at": "آغاز کی تاریخ اختتامی تاریخ کے بعد ہوتی ہے۔", "app.errors.avatar_carrierwave_download_error": "اوتار فائل ڈاؤن لوڈ نہیں ہو سکی۔", "app.errors.avatar_carrierwave_integrity_error": "اوتار فائل اجازت شدہ قسم کی نہیں ہے۔", "app.errors.avatar_carrierwave_processing_error": "اوتار پر کارروائی نہیں ہو سکی۔", "app.errors.avatar_extension_blacklist_error": "اوتار کی تصویر کی فائل ایکسٹینشن کی اجازت نہیں ہے۔ اجازت شدہ ایکسٹینشنز ہیں: jpg، jpeg، gif اور png۔", "app.errors.avatar_extension_whitelist_error": "اوتار کی تصویر کی فائل ایکسٹینشن کی اجازت نہیں ہے۔ اجازت شدہ ایکسٹینشنز ہیں: jpg، jpeg، gif اور png۔", "app.errors.banner_cta_button_multiloc_blank": "بٹن کا متن درج کریں۔", "app.errors.banner_cta_button_url_blank": "ایک لنک درج کریں۔", "app.errors.banner_cta_button_url_url": "ایک درست لنک درج کریں۔ یقینی بنائیں کہ لنک 'https://' سے شروع ہوتا ہے۔", "app.errors.banner_cta_signed_in_text_multiloc_blank": "بٹن کا متن درج کریں۔", "app.errors.banner_cta_signed_in_url_blank": "ایک لنک درج کریں۔", "app.errors.banner_cta_signed_in_url_url": "ایک درست لنک درج کریں۔ یقینی بنائیں کہ لنک 'https://' سے شروع ہوتا ہے۔", "app.errors.banner_cta_signed_out_text_multiloc_blank": "بٹن کا متن درج کریں۔", "app.errors.banner_cta_signed_out_url_blank": "ایک لنک درج کریں۔", "app.errors.banner_cta_signed_out_url_url": "ایک درست لنک درج کریں۔ یقینی بنائیں کہ لنک 'https://' سے شروع ہوتا ہے۔", "app.errors.base_includes_banned_words": "ہو سکتا ہے آپ نے ایک یا زیادہ الفاظ استعمال کیے ہوں جنہیں بے حرمتی سمجھا جاتا ہے۔ براہِ کرم اپنے متن کو تبدیل کریں تاکہ کوئی بھی گستاخیاں جو موجود ہو اسے ہٹا دیں۔", "app.errors.body_multiloc_includes_banned_words": "تفصیل میں ایسے الفاظ ہیں جو نامناسب سمجھے جاتے ہیں۔", "app.errors.bulk_import_idea_not_valid": "نتیجہ خیز خیال درست نہیں ہے: {value}۔", "app.errors.bulk_import_image_url_not_valid": "{value}سے کوئی تصویر ڈاؤن لوڈ نہیں کی جا سکتی ہے۔ یقینی بنائیں کہ URL درست ہے اور فائل ایکسٹینشن جیسے .png یا .jpg کے ساتھ ختم ہوتا ہے۔ یہ مسئلہ ID {row}کے ساتھ قطار میں ہوتا ہے۔", "app.errors.bulk_import_location_point_blank_coordinate": "{value}میں گمشدہ کوآرڈینیٹ کے ساتھ آئیڈیا لوکیشن۔ یہ مسئلہ ID {row}کے ساتھ قطار میں ہوتا ہے۔", "app.errors.bulk_import_location_point_non_numeric_coordinate": "{value}میں غیر عددی کوآرڈینیٹ کے ساتھ آئیڈیا لوکیشن۔ یہ مسئلہ ID {row}کے ساتھ قطار میں ہوتا ہے۔", "app.errors.bulk_import_malformed_pdf": "اپ لوڈ کردہ پی ڈی ایف فائل خراب معلوم ہوتی ہے۔ اپنے ماخذ سے پی ڈی ایف کو دوبارہ برآمد کرنے کی کوشش کریں اور پھر دوبارہ اپ لوڈ کریں۔", "app.errors.bulk_import_maximum_ideas_exceeded": "{value} خیالات کی زیادہ سے زیادہ حد سے تجاوز کر گیا ہے۔", "app.errors.bulk_import_maximum_pdf_pages_exceeded": "PDF میں {value} صفحات کی زیادہ سے زیادہ حد سے تجاوز کر گیا ہے۔", "app.errors.bulk_import_not_enough_pdf_pages": "اپ لوڈ کردہ پی ڈی ایف میں کافی صفحات نہیں ہیں - اس میں کم از کم اتنے ہی صفحات ہونے چاہئیں جتنے ڈاؤن لوڈ کردہ ٹیمپلیٹ ہیں۔", "app.errors.bulk_import_publication_date_invalid_format": "اشاعت کی تاریخ کی غلط شکل \"{value}\" کے ساتھ آئیڈیا۔ براہ کرم فارمیٹ \"DD-MM-YYYY\" استعمال کریں۔", "app.errors.cannot_contain_ideas": "آپ نے جو شرکت کا طریقہ منتخب کیا ہے وہ اس قسم کی پوسٹ کو سپورٹ نہیں کرتا ہے۔ براہ کرم اپنے انتخاب میں ترمیم کریں اور دوبارہ کوشش کریں۔", "app.errors.cant_change_after_first_response": "آپ اسے مزید تبدیل نہیں کر سکتے، کیونکہ کچھ صارفین پہلے ہی جواب دے چکے ہیں۔", "app.errors.category_name_taken": "اس نام کا ایک زمرہ پہلے سے موجود ہے۔", "app.errors.confirmation_code_expired": "کوڈ کی میعاد ختم ہوگئی۔ براہ کرم ایک نئے کوڈ کی درخواست کریں۔", "app.errors.confirmation_code_invalid": "غلط تصدیقی کوڈ۔ براہ کرم درست کوڈ کے لیے اپنا ای میل چیک کریں یا 'نیا کوڈ بھیجیں' آزمائیں", "app.errors.confirmation_code_too_many_resets": "آپ نے تصدیقی کوڈ کو کئی بار دوبارہ بھیجا ہے۔ اس کی بجائے دعوتی کوڈ وصول کرنے کے لیے براہ کرم ہم سے رابطہ کریں۔", "app.errors.confirmation_code_too_many_retries": "آپ نے بہت بار کوشش کی ہے۔ براہ کرم ایک نئے کوڈ کی درخواست کریں یا اپنا ای میل تبدیل کرنے کی کوشش کریں۔", "app.errors.email_already_active": "ای میل ایڈریس {value} قطار میں پایا گیا {row} پہلے سے ہی ایک رجسٹرڈ شریک کا ہے", "app.errors.email_already_invited": "ای میل ایڈریس {value} قطار میں پایا گیا {row} پہلے ہی مدعو کیا گیا تھا", "app.errors.email_blank": "یہ خالی نہیں ہو سکتا", "app.errors.email_domain_blacklisted": "رجسٹر کرنے کے لیے براہ کرم ایک مختلف ای میل ڈومین استعمال کریں۔", "app.errors.email_invalid": "براہ کرم ایک درست ای میل ایڈریس استعمال کریں۔", "app.errors.email_taken": "اس ای میل کے ساتھ ایک اکاؤنٹ پہلے سے موجود ہے۔ اس کے بجائے آپ لاگ ان کر سکتے ہیں۔", "app.errors.email_taken_by_invite": "{value} پہلے ہی زیر التواء دعوت کے ذریعہ لیا گیا ہے۔ اپنا سپیم فولڈر چیک کریں یا {supportEmail} سے رابطہ کریں اگر آپ اسے نہیں ڈھونڈ سکتے ہیں۔", "app.errors.emails_duplicate": "ای میل ایڈریس {value} کے لیے ایک یا زیادہ ڈپلیکیٹ قدریں درج ذیل قطاروں میں پائی گئیں: {rows}", "app.errors.extension_whitelist_error": "آپ نے جس فائل کو اپ لوڈ کرنے کی کوشش کی ہے اس کا فارمیٹ تعاون یافتہ نہیں ہے۔", "app.errors.file_extension_whitelist_error": "آپ نے جس فائل کو اپ لوڈ کرنے کی کوشش کی ہے اس کا فارمیٹ تعاون یافتہ نہیں ہے۔", "app.errors.first_name_blank": "یہ خالی نہیں ہو سکتا", "app.errors.generics.blank": "یہ خالی نہیں ہو سکتا۔", "app.errors.generics.invalid": "یہ ایک درست قدر کی طرح نہیں لگتا ہے۔", "app.errors.generics.taken": "یہ ای میل پہلے سے موجود ہے۔ ایک اور اکاؤنٹ اس سے منسلک ہے۔", "app.errors.generics.unsupported_locales": "یہ فیلڈ موجودہ لوکل کو سپورٹ نہیں کرتی ہے۔", "app.errors.group_ids_unauthorized_choice_moderator": "پروجیکٹ مینیجر کے طور پر، آپ صرف ان لوگوں کو ای میل کر سکتے ہیں جو آپ کے پروجیکٹ تک رسائی حاصل کر سکتے ہیں", "app.errors.has_other_overlapping_phases": "منصوبوں میں اوورلیپنگ مراحل نہیں ہو سکتے۔", "app.errors.invalid_email": "قطار {row} میں پایا جانے والا ای میل {value} ایک درست ای میل پتہ نہیں ہے", "app.errors.invalid_row": "قطار {row}پر کارروائی کرنے کی کوشش کے دوران ایک نامعلوم خرابی پیش آگئی", "app.errors.is_not_timeline_project": "موجودہ پروجیکٹ مراحل کی حمایت نہیں کرتا ہے۔", "app.errors.key_invalid": "کلید صرف حروف، نمبر اور انڈر سکور پر مشتمل ہو سکتی ہے(_)", "app.errors.last_name_blank": "یہ خالی نہیں ہو سکتا", "app.errors.locale_blank": "براہ کرم ایک زبان کا انتخاب کریں۔", "app.errors.locale_inclusion": "براہ کرم تعاون یافتہ زبان کا انتخاب کریں۔", "app.errors.malformed_admin_value": "قطار {row} میں پائی جانے والی منتظم قدر {value} درست نہیں ہے", "app.errors.malformed_groups_value": "قطار {row} میں پایا جانے والا گروپ {value} ایک درست گروپ نہیں ہے", "app.errors.max_invites_limit_exceeded1": "دعوت ناموں کی تعداد 1000 کی حد سے تجاوز کر گئی ہے۔", "app.errors.maximum_attendees_greater_than1": "رجسٹر کرنے والوں کی زیادہ سے زیادہ تعداد 0 سے زیادہ ہونی چاہیے۔", "app.errors.maximum_attendees_greater_than_attendees_count1": "رجسٹر کرنے والوں کی زیادہ سے زیادہ تعداد رجسٹر کرنے والوں کی موجودہ تعداد سے زیادہ یا اس کے برابر ہونی چاہیے۔", "app.errors.no_invites_specified": "کوئی ای میل پتہ نہیں مل سکا۔", "app.errors.no_recipients": "مہم نہیں بھیجی جا سکتی کیونکہ کوئی وصول کنندہ نہیں ہے۔ آپ جس گروپ کو بھیج رہے ہیں وہ یا تو خالی ہے، یا کسی نے بھی ای میلز وصول کرنے کی رضامندی نہیں دی ہے۔", "app.errors.number_invalid": "براہ کرم ایک درست نمبر درج کریں۔", "app.errors.password_blank": "یہ خالی نہیں ہو سکتا", "app.errors.password_invalid": "براہ کرم اپنا موجودہ پاس ورڈ دوبارہ چیک کریں۔", "app.errors.password_too_short": "پاس ورڈ کم از کم 8 حروف کا ہونا چاہیے۔", "app.errors.resending_code_failed": "تصدیقی کوڈ بھیجتے وقت کچھ غلط ہو گیا۔", "app.errors.slug_taken": "یہ پروجیکٹ URL پہلے سے موجود ہے۔ براہ کرم پروجیکٹ سلگ کو کسی اور چیز میں تبدیل کریں۔", "app.errors.tag_name_taken": "اس نام کا ٹیگ پہلے سے موجود ہے۔", "app.errors.title_multiloc_blank": "عنوان خالی نہیں ہو سکتا۔", "app.errors.title_multiloc_includes_banned_words": "عنوان میں ایسے الفاظ ہیں جو نامناسب سمجھے جاتے ہیں۔", "app.errors.token_invalid": "پاس ورڈ دوبارہ ترتیب دینے والے لنکس صرف ایک بار استعمال کیے جاسکتے ہیں اور بھیجے جانے کے بعد ایک گھنٹے تک درست رہتے ہیں۔ {passwordResetLink}", "app.errors.too_common": "اس پاس ورڈ کا آسانی سے اندازہ لگایا جا سکتا ہے۔ براہ کرم ایک مضبوط پاس ورڈ منتخب کریں۔", "app.errors.too_long": "براہ کرم ایک چھوٹا پاس ورڈ منتخب کریں (زیادہ سے زیادہ 72 حروف)", "app.errors.too_short": "براہ کرم کم از کم 8 حروف والا پاس ورڈ منتخب کریں۔", "app.errors.uncaught_error": "ایک نامعلوم خرابی پیش آگئی۔", "app.errors.unknown_group": "گروپ {value} قطار {row} میں پایا جانے والا گروپ نہیں ہے", "app.errors.unknown_locale": "قطار {row} میں پائی جانے والی زبان {value} کنفیگر شدہ زبان نہیں ہے", "app.errors.unparseable_excel": "منتخب ایکسل فائل پر کارروائی نہیں ہو سکی۔", "app.errors.url": "ایک درست لنک درج کریں۔ یقینی بنائیں کہ لنک https:// سے شروع ہوتا ہے", "app.errors.verification_taken": "تصدیق مکمل نہیں کی جا سکتی کیونکہ اسی تفصیلات کا استعمال کرتے ہوئے دوسرے اکاؤنٹ کی تصدیق کی گئی ہے۔", "app.errors.view_name_taken": "اس نام کے ساتھ ایک منظر پہلے سے موجود ہے۔", "app.modules.commercial.flag_inappropriate_content.citizen.components.inappropriateContentAutoDetected": "پوسٹ یا تبصرے میں نامناسب مواد کا خود بخود پتہ چلا", "app.modules.commercial.id_vienna_saml.components.signInWithStandardPortal": "سٹینڈرڈ پورٹل کے ساتھ سائن ان کریں۔", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortal": "سٹینڈرڈ پورٹل کے ساتھ سائن اپ کریں۔", "app.modules.commercial.id_vienna_saml.components.signUpWithStandardPortalSubHeader": "ابھی ایک Stadt Wien اکاؤنٹ بنائیں اور ویانا کی بہت سی ڈیجیٹل سروسز کے لیے ایک لاگ ان استعمال کریں۔", "app.modules.id_cow.cancel": "منسوخ کریں۔", "app.modules.id_cow.emptyFieldError": "یہ فیلڈ خالی نہیں ہو سکتی۔", "app.modules.id_cow.helpAltText": "یہ دکھاتا ہے کہ شناختی کارڈ پر شناختی سیریل نمبر کہاں تلاش کرنا ہے۔", "app.modules.id_cow.invalidIdSerialError": "غلط ID سیریل", "app.modules.id_cow.invalidRunError": "غلط RUN", "app.modules.id_cow.noMatchFormError": "کوئی مماثلت نہیں ملی۔", "app.modules.id_cow.notEntitledFormError": "حقدار نہیں۔", "app.modules.id_cow.showCOWHelp": "مجھے اپنا آئی ڈی سیریل نمبر کہاں سے مل سکتا ہے؟", "app.modules.id_cow.somethingWentWrongError": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ کچھ غلط ہو گیا۔", "app.modules.id_cow.submit": "جمع کروائیں۔", "app.modules.id_cow.takenFormError": "پہلے ہی لے لیا ہے۔", "app.modules.id_cow.verifyCow": "COW کا استعمال کرتے ہوئے تصدیق کریں۔", "app.modules.id_franceconnect.verificationButtonAltText": "FranceConnect کے ساتھ تصدیق کریں۔", "app.modules.id_gent_rrn.cancel": "منسوخ کریں۔", "app.modules.id_gent_rrn.emptyFieldError": "یہ فیلڈ خالی نہیں ہو سکتی۔", "app.modules.id_gent_rrn.gentRrnHelp": "آپ کا سوشل سیکورٹی نمبر آپ کے ڈیجیٹل شناختی کارڈ کے پیچھے دکھایا گیا ہے۔", "app.modules.id_gent_rrn.invalidRrnError": "غلط سوشل سیکورٹی نمبر", "app.modules.id_gent_rrn.noMatchFormError": "ہمیں آپ کے سوشل سیکیورٹی نمبر پر واپسی کی معلومات نہیں مل سکیں", "app.modules.id_gent_rrn.notEntitledLivesOutsideFormError": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ آپ گینٹ سے باہر رہتے ہیں۔", "app.modules.id_gent_rrn.notEntitledTooYoungFormError": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ آپ کی عمر 14 سال سے کم ہے۔", "app.modules.id_gent_rrn.rrnLabel": "سوشل سیکورٹی نمبر", "app.modules.id_gent_rrn.rrnTooltip": "ہم یہ تصدیق کرنے کے لیے آپ کے سوشل سیکیورٹی نمبر سے پوچھتے ہیں کہ آیا آپ گینٹ کے شہری ہیں، جس کی عمر 14 سال سے زیادہ ہے۔", "app.modules.id_gent_rrn.showGentRrnHelp": "مجھے اپنا آئی ڈی سیریل نمبر کہاں سے مل سکتا ہے؟", "app.modules.id_gent_rrn.somethingWentWrongError": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ کچھ غلط ہو گیا۔", "app.modules.id_gent_rrn.submit": "جمع کروائیں۔", "app.modules.id_gent_rrn.takenFormError": "آپ کا سوشل سیکورٹی نمبر پہلے ہی دوسرے اکاؤنٹ کی تصدیق کے لیے استعمال ہو چکا ہے۔", "app.modules.id_gent_rrn.verifyGentRrn": "GentRrn کا استعمال کرتے ہوئے تصدیق کریں۔", "app.modules.id_id_card_lookup.cancel": "منسوخ کریں۔", "app.modules.id_id_card_lookup.emptyFieldError": "یہ فیلڈ خالی نہیں ہو سکتی۔", "app.modules.id_id_card_lookup.helpAltText": "شناختی کارڈ کی وضاحت", "app.modules.id_id_card_lookup.invalidCardIdError": "یہ آئی ڈی درست نہیں ہے۔", "app.modules.id_id_card_lookup.noMatchFormError": "کوئی مماثلت نہیں ملی۔", "app.modules.id_id_card_lookup.showHelp": "مجھے اپنا آئی ڈی سیریل نمبر کہاں سے مل سکتا ہے؟", "app.modules.id_id_card_lookup.somethingWentWrongError": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ کچھ غلط ہو گیا۔", "app.modules.id_id_card_lookup.submit": "جمع کروائیں۔", "app.modules.id_id_card_lookup.takenFormError": "پہلے ہی لے لیا ہے۔", "app.modules.id_oostende_rrn.cancel": "منسوخ کریں۔", "app.modules.id_oostende_rrn.emptyFieldError": "یہ فیلڈ خالی نہیں ہو سکتی۔", "app.modules.id_oostende_rrn.invalidRrnError": "غلط سوشل سیکورٹی نمبر", "app.modules.id_oostende_rrn.noMatchFormError": "ہمیں آپ کے سوشل سیکیورٹی نمبر پر واپسی کی معلومات نہیں مل سکیں", "app.modules.id_oostende_rrn.notEntitledLivesOutsideFormError1": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ آپ Oostende سے باہر رہتے ہیں۔", "app.modules.id_oostende_rrn.notEntitledTooYoungFormError": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ آپ کی عمر 14 سال سے کم ہے۔", "app.modules.id_oostende_rrn.oostendeRrnHelp": "آپ کا سوشل سیکورٹی نمبر آپ کے ڈیجیٹل شناختی کارڈ کے پیچھے دکھایا گیا ہے۔", "app.modules.id_oostende_rrn.rrnLabel": "سوشل سیکورٹی نمبر", "app.modules.id_oostende_rrn.rrnTooltip": "ہم یہ تصدیق کرنے کے لیے آپ کے سوشل سیکیورٹی نمبر سے پوچھتے ہیں کہ آیا آپ Oostende کے شہری ہیں، جس کی عمر 14 سال سے زیادہ ہے۔", "app.modules.id_oostende_rrn.showOostendeRrnHelp": "مجھے اپنا سوشل سیکورٹی نمبر کہاں سے مل سکتا ہے؟", "app.modules.id_oostende_rrn.somethingWentWrongError": "ہم آپ کی تصدیق نہیں کر سکتے کیونکہ کچھ غلط ہو گیا۔", "app.modules.id_oostende_rrn.submit": "جمع کروائیں۔", "app.modules.id_oostende_rrn.takenFormError": "آپ کا سوشل سیکورٹی نمبر پہلے ہی دوسرے اکاؤنٹ کی تصدیق کے لیے استعمال ہو چکا ہے۔", "app.modules.id_oostende_rrn.verifyOostendeRrn": "سوشل سیکیورٹی نمبر استعمال کرکے تصدیق کریں۔", "app.modules.project_folder.citizen.components.Notification.youveReceivedFolderAdminRights": "آپ کو \"{folderName}\" فولڈر پر منتظم کے حقوق مل گئے ہیں۔", "app.modules.project_folder.citizen.components.ProjectFolderShareButton.share": "شیئر کریں۔", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingBody": "اپنی آواز سننے کے لیے پروجیکٹس کو {folderUrl} پر دیکھیں!", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.emailSharingSubject": "{projectFolderName} | {orgName}کے شرکت کے پلیٹ فارم سے", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.twitterMessage": "{projectFolderName}", "app.modules.project_folder.citizen.components.ProjectFolderSharingModal.whatsAppMessage": "{projectFolderName} | {orgName}کے شرکت کے پلیٹ فارم سے", "app.sessionRecording.accept": "ہاں، میں قبول کرتا ہوں۔", "app.sessionRecording.modalDescription1": "اپنے صارفین کو بہتر طور پر سمجھنے کے لیے، ہم تصادفی طور پر دیکھنے والوں کی ایک چھوٹی فیصد سے ان کے براؤزنگ سیشن کو تفصیل سے ٹریک کرنے کے لیے کہتے ہیں۔", "app.sessionRecording.modalDescription2": "ریکارڈ شدہ ڈیٹا کا واحد مقصد ویب سائٹ کو بہتر بنانا ہے۔ آپ کا کوئی بھی ڈیٹا تیسرے فریق کے ساتھ شیئر نہیں کیا جائے گا۔ آپ جو بھی حساس معلومات داخل کریں گے اسے فلٹر کر دیا جائے گا۔", "app.sessionRecording.modalDescription3": "کیا آپ کو قبول ہے؟", "app.sessionRecording.modalDescriptionFaq": "یہاں اکثر پوچھے گئے سوالات۔", "app.sessionRecording.modalTitle": "اس ویب سائٹ کو بہتر بنانے میں ہماری مدد کریں۔", "app.sessionRecording.reject": "نہیں، میں مسترد کرتا ہوں۔", "app.utils.AdminPage.ProjectEdit.conductParticipatoryBudgetingText": "بجٹ مختص کرنے کی مشق کریں۔", "app.utils.AdminPage.ProjectEdit.createDocumentAnnotation": "کسی دستاویز پر رائے جمع کریں۔", "app.utils.AdminPage.ProjectEdit.createNativeSurvey": "ایک درون پلیٹ فارم سروے بنائیں", "app.utils.AdminPage.ProjectEdit.createPoll": "ایک پول بنائیں", "app.utils.AdminPage.ProjectEdit.createSurveyText": "ایک بیرونی سروے ایمبیڈ کریں۔", "app.utils.AdminPage.ProjectEdit.findVolunteers": "رضاکاروں کو تلاش کریں۔", "app.utils.AdminPage.ProjectEdit.inputAndFeedback": "ان پٹ اور آراء جمع کریں۔", "app.utils.AdminPage.ProjectEdit.shareInformation": "معلومات شیئر کریں۔", "app.utils.FormattedCurrency.credits": "کریڈٹ", "app.utils.FormattedCurrency.tokens": "ٹوکن", "app.utils.FormattedCurrency.xCredits": "{numberOfCredits, plural, =0 {# کریڈٹ} one {# کریڈٹ} other {# کریڈٹ}}", "app.utils.FormattedCurrency.xTokens": "{numberOfTokens, plural, =0 {# ٹوکن} one {# ٹوکن} other {# ٹوکن}}", "app.utils.IdeaCards.mostDiscussed": "سب سے زیادہ زیر بحث", "app.utils.IdeaCards.mostReacted": "زیادہ تر رد عمل", "app.utils.IdeaCards.newest": "تازہ ترین", "app.utils.IdeaCards.oldest": "سب سے پرانا", "app.utils.IdeaCards.random": "بے ترتیب", "app.utils.IdeaCards.trending": "ٹرینڈنگ", "app.utils.IdeasNewPage.contributionFormTitle": "نئی شراکت شامل کریں۔", "app.utils.IdeasNewPage.ideaFormTitle": "نیا خیال شامل کریں۔", "app.utils.IdeasNewPage.initiativeFormTitle": "نئی پہل شامل کریں۔", "app.utils.IdeasNewPage.issueFormTitle1": "نیا تبصرہ شامل کریں۔", "app.utils.IdeasNewPage.optionFormTitle": "نیا آپشن شامل کریں۔", "app.utils.IdeasNewPage.petitionFormTitle": "نئی پٹیشن شامل کریں۔", "app.utils.IdeasNewPage.projectFormTitle": "نیا پروجیکٹ شامل کریں۔", "app.utils.IdeasNewPage.proposalFormTitle": "نئی تجویز شامل کریں۔", "app.utils.IdeasNewPage.questionFormTitle": "نیا سوال شامل کریں۔", "app.utils.IdeasNewPage.surveyTitle": "سروے", "app.utils.IdeasNewPage.viewYourComment": "اپنا تبصرہ دیکھیں", "app.utils.IdeasNewPage.viewYourContribution": "اپنا تعاون دیکھیں", "app.utils.IdeasNewPage.viewYourIdea": "اپنا خیال دیکھیں", "app.utils.IdeasNewPage.viewYourInitiative": "اپنی پہل دیکھیں", "app.utils.IdeasNewPage.viewYourInput": "اپنا ان پٹ دیکھیں", "app.utils.IdeasNewPage.viewYourIssue": "اپنا مسئلہ دیکھیں", "app.utils.IdeasNewPage.viewYourOption": "اپنا آپشن دیکھیں", "app.utils.IdeasNewPage.viewYourPetition": "اپنی پٹیشن دیکھیں", "app.utils.IdeasNewPage.viewYourProject": "اپنا پروجیکٹ دیکھیں", "app.utils.IdeasNewPage.viewYourProposal": "اپنی تجویز دیکھیں", "app.utils.IdeasNewPage.viewYourQuestion": "اپنا سوال دیکھیں", "app.utils.Projects.sendSubmission": "میرے ای میل پر جمع کرانے کا شناخت کنندہ بھیجیں۔", "app.utils.Projects.sendSurveySubmission": "سروے جمع کرانے والے شناخت کنندہ کو میرے ای میل پر بھیجیں۔", "app.utils.Projects.surveySubmission": "سروے جمع کروانا", "app.utils.Projects.yourResponseHasTheFollowingId": "آپ کے جواب میں درج ذیل شناخت کنندہ ہے: {identifier}", "app.utils.Promessagesjects.ifYouLaterDecide": "اگر آپ بعد میں فیصلہ کرتے ہیں کہ آپ اپنے جواب کو ہٹانا چاہتے ہیں، تو براہ کرم درج ذیل منفرد شناخت کنندہ کے ساتھ ہم سے رابطہ کریں:", "app.utils.actionDescriptors.attendingEventMissingRequirements": "اس تقریب میں شرکت کے لیے آپ کو اپنا پروفائل مکمل کرنا ہوگا۔", "app.utils.actionDescriptors.attendingEventNotInGroup": "آپ اس تقریب میں شرکت کی ضروریات کو پورا نہیں کرتے ہیں۔", "app.utils.actionDescriptors.attendingEventNotPermitted": "آپ کو اس تقریب میں شرکت کی اجازت نہیں ہے۔", "app.utils.actionDescriptors.attendingEventNotSignedIn": "اس تقریب میں شرکت کے لیے آپ کو لاگ ان یا رجسٹر ہونا ضروری ہے۔", "app.utils.actionDescriptors.attendingEventNotVerified": "اس ایونٹ میں شرکت کرنے سے پہلے آپ کو اپنے اکاؤنٹ کی تصدیق کرنی ہوگی۔", "app.utils.actionDescriptors.volunteeringMissingRequirements": "آپ کو رضاکارانہ طور پر اپنا پروفائل مکمل کرنا ہوگا۔", "app.utils.actionDescriptors.volunteeringNotInGroup": "آپ رضاکارانہ طور پر کام کرنے کی ضروریات کو پورا نہیں کرتے ہیں۔", "app.utils.actionDescriptors.volunteeringNotPermitted": "آپ کو رضاکارانہ طور پر کام کرنے کی اجازت نہیں ہے۔", "app.utils.actionDescriptors.volunteeringNotSignedIn": "آپ کو رضاکارانہ طور پر لاگ ان یا رجسٹر کرنا ہوگا۔", "app.utils.actionDescriptors.volunteeringNotVerified": "رضاکارانہ طور پر کام کرنے سے پہلے آپ کو اپنے اکاؤنٹ کی تصدیق کرنی ہوگی۔", "app.utils.actionDescriptors.volunteeringdNotActiveUser": "براہ کرم {completeRegistrationLink} رضاکارانہ طور پر کام کریں۔", "app.utils.errors.api_error_default.in": "صحیح نہیں ہے۔", "app.utils.errors.default.ajv_error_birthyear_required": "براہ کرم اپنی پیدائش کا سال بھریں۔", "app.utils.errors.default.ajv_error_date_any": "براہ کرم ایک درست تاریخ بھریں۔", "app.utils.errors.default.ajv_error_domicile_required": "براہ کرم اپنی رہائش کی جگہ پُر کریں۔", "app.utils.errors.default.ajv_error_gender_required": "براہ کرم اپنی جنس بھریں۔", "app.utils.errors.default.ajv_error_invalid": "ناجائز ہے۔", "app.utils.errors.default.ajv_error_maxItems": "{limit, plural, one {# آئٹم} other {# آئٹمز}}سے زیادہ شامل نہیں ہو سکتے", "app.utils.errors.default.ajv_error_minItems": "کم از کم {limit, plural, one {# آئٹم} other {# آئٹمز}}شامل کرنا ضروری ہے", "app.utils.errors.default.ajv_error_number_any": "براہ کرم ایک درست نمبر پُر کریں۔", "app.utils.errors.default.ajv_error_politician_required": "براہ کرم پُر کریں کہ آیا آپ سیاست دان ہیں۔", "app.utils.errors.default.ajv_error_required3": "فیلڈ درکار ہے: \"{fieldName}\"", "app.utils.errors.default.ajv_error_type": "خالی نہیں ہو سکتا", "app.utils.errors.default.api_error_accepted": "ماننا پڑے گا۔", "app.utils.errors.default.api_error_blank": "خالی نہیں ہو سکتا", "app.utils.errors.default.api_error_confirmation": "میل نہیں کھاتا", "app.utils.errors.default.api_error_empty": "خالی نہیں ہو سکتا", "app.utils.errors.default.api_error_equal_to": "صحیح نہیں ہے۔", "app.utils.errors.default.api_error_even": "برابر ہونا چاہیے۔", "app.utils.errors.default.api_error_exclusion": "محفوظ ہے۔", "app.utils.errors.default.api_error_greater_than": "بہت چھوٹا ہے۔", "app.utils.errors.default.api_error_greater_than_or_equal_to": "بہت چھوٹا ہے۔", "app.utils.errors.default.api_error_inclusion": "فہرست میں شامل نہیں ہے۔", "app.utils.errors.default.api_error_invalid": "ناجائز ہے۔", "app.utils.errors.default.api_error_less_than": "بہت بڑا ہے۔", "app.utils.errors.default.api_error_less_than_or_equal_to": "بہت بڑا ہے۔", "app.utils.errors.default.api_error_not_a_number": "نمبر نہیں ہے۔", "app.utils.errors.default.api_error_not_an_integer": "ایک عدد عدد ہونا ضروری ہے۔", "app.utils.errors.default.api_error_other_than": "صحیح نہیں ہے۔", "app.utils.errors.default.api_error_present": "خالی ہونا ضروری ہے۔", "app.utils.errors.default.api_error_too_long": "بہت لمبا ہے۔", "app.utils.errors.default.api_error_too_short": "بہت مختصر ہے۔", "app.utils.errors.default.api_error_wrong_length": "غلط لمبائی ہے۔", "app.utils.errors.defaultapi_error_.odd": "عجیب ہونا چاہیے۔", "app.utils.notInGroup": "آپ حصہ لینے کی ضروریات کو پورا نہیں کرتے ہیں۔", "app.utils.participationMethod.onSurveySubmission": "شکریہ آپ کا جواب موصول ہو گیا ہے۔", "app.utils.participationMethodConfig.voting.votingDisabledProjectInactive": "ووٹنگ مزید دستیاب نہیں ہے، کیونکہ یہ مرحلہ اب فعال نہیں ہے۔", "app.utils.participationMethodConfig.voting.votingNotInGroup": "آپ ووٹ دینے کے تقاضوں کو پورا نہیں کرتے۔", "app.utils.participationMethodConfig.voting.votingNotPermitted": "آپ کو ووٹ ڈالنے کی اجازت نہیں ہے۔", "app.utils.participationMethodConfig.voting.votingNotSignedIn2": "ووٹ دینے کے لیے آپ کو لاگ ان یا رجسٹر ہونا چاہیے۔", "app.utils.participationMethodConfig.voting.votingNotVerified": "ووٹ دینے سے پہلے آپ کو اپنے اکاؤنٹ کی تصدیق کرنی ہوگی۔", "app.utils.votingMethodUtils.budgetParticipationEnded1": "<b>بجٹ جمع کروانا {endDate}کو بند ہو گیا۔</b> شرکاء کے پاس کل <b>{maxBudget} ہر ایک کو {optionCount} اختیارات کے درمیان تقسیم کرنا تھا۔</b>", "app.utils.votingMethodUtils.budgetSubmitted": "بجٹ جمع کرایا", "app.utils.votingMethodUtils.budgetSubmittedWithIcon": "بجٹ پیش کر دیا گیا 🎉", "app.utils.votingMethodUtils.budgetingNotInGroup": "آپ بجٹ تفویض کرنے کی ضروریات کو پورا نہیں کرتے ہیں۔", "app.utils.votingMethodUtils.budgetingNotPermitted": "آپ کو بجٹ تفویض کرنے کی اجازت نہیں ہے۔", "app.utils.votingMethodUtils.budgetingNotSignedIn2": "بجٹ تفویض کرنے کے لیے آپ کو لاگ ان یا رجسٹر کرنا ہوگا۔", "app.utils.votingMethodUtils.budgetingNotVerified": "بجٹ تفویض کرنے سے پہلے آپ کو اپنے اکاؤنٹ کی تصدیق کرنی ہوگی۔", "app.utils.votingMethodUtils.budgetingPreSubmissionWarning": "<b>آپ کا بجٹ</b> شمار نہیں کیا جائے گا جب تک کہ آپ \"جمع کروائیں\" پر کلک نہیں کرتے۔", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsMinBudget1": "کم از کم مطلوبہ بجٹ {amount}ہے۔", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsOnceYouAreDone": "ایک بار جب آپ کام کر لیں، اپنا بجٹ جمع کرانے کے لیے \"جمع کروائیں\" پر کلک کریں۔", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsPreferredOptions": "\"شامل کریں\" پر ٹیپ کرکے اپنے پسندیدہ اختیارات کا انتخاب کریں۔", "app.utils.votingMethodUtils.budgetingSubmissionInstructionsTotalBudget2": "آپ کے پاس {optionCount} اختیارات</b>کے درمیان تقسیم کرنے کے لیے کل <b>{maxBudget} ہیں۔", "app.utils.votingMethodUtils.budgetingSubmittedInstructions2": "<b>مب<PERSON>ر<PERSON> ہو، آپ کا بجٹ جمع کر دیا گیا ہے!</b> آپ کسی بھی وقت نیچے اپنے اختیارات کی جانچ کر سکتے ہیں یا <b>{endDate}</b>سے پہلے ان میں ترمیم کر سکتے ہیں۔", "app.utils.votingMethodUtils.budgetingSubmittedInstructionsNoEndDate": "<b>مبار<PERSON> ہو، آپ کا بجٹ جمع کر دیا گیا ہے!</b> آپ کسی بھی وقت نیچے اپنے اختیارات چیک کر سکتے ہیں۔", "app.utils.votingMethodUtils.castYourVote": "اپنا ووٹ کاسٹ کریں۔", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxCreditsPerIdea1": "آپ فی اختیار زیادہ سے زیادہ {maxVotes, plural, one {# کریڈٹ} other {# کریڈٹ}} شامل کر سکتے ہیں۔", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxPointsPerIdea1": "آپ فی اختیار زیادہ سے زیادہ {maxVotes, plural, one {# پوائنٹ} other {# پوائنٹس}} شامل کر سکتے ہیں۔", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxTokensPerIdea1": "آپ زیادہ سے زیادہ {maxVotes, plural, one {# ٹوکن} other {# ٹوکن}} فی اختیار شامل کر سکتے ہیں۔", "app.utils.votingMethodUtils.cumulativeVotingInstructionsMaxVotesPerIdea4": "آپ فی اختیار زیادہ سے زیادہ {maxVotes, plural, one {# ووٹ} other {# ووٹ}} شامل کر سکتے ہیں۔", "app.utils.votingMethodUtils.cumulativeVotingInstructionsOnceYouAreDone": "ایک بار جب آپ کام کر لیں، اپنا ووٹ ڈالنے کے لیے \"جمع کروائیں\" پر کلک کریں۔", "app.utils.votingMethodUtils.cumulativeVotingInstructionsPreferredOptions2": "\"منتخب کریں\" پر ٹیپ کرکے اپنے پسندیدہ اختیارات منتخب کریں۔", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalCredits2": "You have a total of <b>{totalVotes, plural, one {# credit} other {# credits}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalPoints2": "You have a total of <b>{totalVotes, plural, one {# point} other {# points}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalTokens2": "You have a total of <b>{totalVotes, plural, one {# token} other {# tokens}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.cumulativeVotingInstructionsTotalVotes3": "You have a total of <b>{totalVotes, plural, one {# vote} other {# votes}} to distribute between {optionCount, plural, one {# option} other {# options}}</b>.", "app.utils.votingMethodUtils.finalResults": "حتمی نتائج", "app.utils.votingMethodUtils.finalTally": "حتمی تعداد", "app.utils.votingMethodUtils.howToParticipate": "حصہ لینے کا طریقہ", "app.utils.votingMethodUtils.howToVote": "ووٹ دینے کا طریقہ", "app.utils.votingMethodUtils.multipleVotingEnded1": "<b>{endDate}پر ووٹنگ بند ہو گئی۔</b>", "app.utils.votingMethodUtils.numberOfCredits": "{numberOfVotes, plural, =0 {0 کریڈٹ} one {1 کریڈٹ} other {# کریڈٹ}}", "app.utils.votingMethodUtils.numberOfPoints": "{numberOfVotes, plural, =0 {0 پوائنٹس} one {1 پوائنٹ} other {# پوائنٹس}}", "app.utils.votingMethodUtils.numberOfTokens": "{numberOfVotes, plural, =0 {0 ٹوکن} one {1 ٹوکن} other {# ٹوکن}}", "app.utils.votingMethodUtils.numberOfVotes1": "{numberOfVotes, plural, =0 {0 ووٹ} one {1 ووٹ} other {# ووٹ}}", "app.utils.votingMethodUtils.results": "نتائج", "app.utils.votingMethodUtils.singleVotingEnded": "ووٹنگ <b>{endDate}پر بند ہو گئی۔</b> شرکاء <b> {maxVotes} اختیارات کے لیے ووٹ دے سکتے ہیں۔</b>", "app.utils.votingMethodUtils.singleVotingMultipleVotesPreferredOptions": "\"ووٹ\" پر ٹیپ کرکے اپنے پسندیدہ اختیارات منتخب کریں۔", "app.utils.votingMethodUtils.singleVotingMultipleVotesYouCanVote2": "آپ کے پاس <b>{totalVotes} ووٹ</b> ہیں جنہیں آپ اختیارات کو تفویض کر سکتے ہیں۔", "app.utils.votingMethodUtils.singleVotingOnceYouAreDone": "ایک بار جب آپ کام کر لیں، اپنا ووٹ ڈالنے کے لیے \"جمع کروائیں\" پر کلک کریں۔", "app.utils.votingMethodUtils.singleVotingOneVoteEnded": "ووٹنگ <b>{endDate}پر بند ہو گئی۔</b> شرکاء <b>1 آپشن کو ووٹ دے سکتے ہیں۔</b>", "app.utils.votingMethodUtils.singleVotingOneVotePreferredOption": "\"ووٹ\" پر ٹیپ کرکے اپنا پسندیدہ آپشن منتخب کریں۔", "app.utils.votingMethodUtils.singleVotingOneVoteYouCanVote2": "آپ کے پاس <b>1 ووٹ</b> ہے جسے آپ اختیارات میں سے کسی ایک کو تفویض کر سکتے ہیں۔", "app.utils.votingMethodUtils.singleVotingUnlimitedEnded": "ووٹنگ <b>{endDate}پر بند ہو گئی۔</b> شرکاء <b>جتنے چاہیں آپشنز کو ووٹ دے سکتے ہیں۔</b>", "app.utils.votingMethodUtils.singleVotingUnlimitedVotesYouCanVote": "آپ جتنے چاہیں ووٹ دے سکتے ہیں۔", "app.utils.votingMethodUtils.submitYourBudget": "اپنا بجٹ جمع کروائیں۔", "app.utils.votingMethodUtils.submittedBudgetCountText2": "ایک شخص نے اپنا بجٹ آن لائن جمع کرایا", "app.utils.votingMethodUtils.submittedBudgetsCountText2": "لوگوں نے اپنا بجٹ آن لائن جمع کرایا", "app.utils.votingMethodUtils.submittedVoteCountText2": "ایک شخص نے اپنا ووٹ آن لائن جمع کرایا", "app.utils.votingMethodUtils.submittedVotesCountText2": "لوگوں نے اپنا ووٹ آن لائن جمع کرایا", "app.utils.votingMethodUtils.voteSubmittedWithIcon": "ووٹ جمع کرایا گیا 🎉", "app.utils.votingMethodUtils.votesCast": "ووٹ ڈالے گئے۔", "app.utils.votingMethodUtils.votingClosed": "ووٹنگ بند", "app.utils.votingMethodUtils.votingPreSubmissionWarning1": "<b>آپ کا ووٹ شمار نہیں کیا جائے گا</b> جب تک آپ \"جمع کروائیں\" پر کلک نہیں کرتے", "app.utils.votingMethodUtils.votingSubmittedInstructions1": "<b>مب<PERSON><PERSON><PERSON> ہو، آپ کا ووٹ جمع کر دیا گیا ہے!</b> آپ <b>{endDate}</b>سے پہلے اپنی جمع کرانے کی جانچ یا ترمیم کر سکتے ہیں۔", "app.utils.votingMethodUtils.votingSubmittedInstructionsNoEndDate1": "<b>مب<PERSON><PERSON><PERSON> ہو، آپ کا ووٹ جمع کر دیا گیا ہے!</b> آپ نیچے کسی بھی وقت اپنی جمع آوری کو چیک یا اس میں ترمیم کر سکتے ہیں۔", "components.UI.IdeaSelect.noIdeaAvailable": "کوئی آئیڈیاز دستیاب نہیں ہیں۔", "components.UI.IdeaSelect.selectIdea": "آئیڈیا منتخب کریں۔", "containers.SiteMap.allProjects": "تمام منصوبے", "containers.SiteMap.customPageSection": "حسب ضرورت صفحات", "containers.SiteMap.folderInfo": "مزید معلومات", "containers.SiteMap.headSiteMapTitle": "سائٹ کا نقشہ | {orgName}", "containers.SiteMap.homeSection": "جن<PERSON><PERSON>", "containers.SiteMap.pageContents": "صفحہ کا مواد", "containers.SiteMap.profilePage": "آپ کا پروفائل صفحہ", "containers.SiteMap.profileSettings": "آپ کی ترتیبات", "containers.SiteMap.projectEvents": "واقعات", "containers.SiteMap.projectIdeas": "خیالات", "containers.SiteMap.projectInfo": "معلومات", "containers.SiteMap.projectPoll": "رائے شماری", "containers.SiteMap.projectSurvey": "سروے", "containers.SiteMap.projectsArchived": "محفوظ شدہ پروجیکٹس", "containers.SiteMap.projectsCurrent": "موجودہ پروجیکٹس", "containers.SiteMap.projectsDraft": "ڈرافٹ پروجیکٹس", "containers.SiteMap.projectsSection": "{orgName}کے پروجیکٹس", "containers.SiteMap.signInPage": "سائن ان کریں۔", "containers.SiteMap.signUpPage": "سائن اپ کریں۔", "containers.SiteMap.siteMapDescription": "اس صفحہ سے، آپ پلیٹ فارم پر موجود کسی بھی مواد پر جا سکتے ہیں۔", "containers.SiteMap.siteMapTitle": "{orgName}کے شرکت کے پلیٹ فارم کا سائٹ کا نقشہ", "containers.SiteMap.successStories": "کامیابی کی کہانیاں", "containers.SiteMap.timeline": "پروجیکٹ کے مراحل", "containers.SiteMap.userSpaceSection": "آپ کا اکاؤنٹ", "containers.SubscriptionEndedPage.accessDenied": "آپ کو مزید رسائی حاصل نہیں ہے۔", "containers.SubscriptionEndedPage.subscriptionEnded": "یہ صفحہ صرف فعال سبسکرپشن والے پلیٹ فارمز کے لیے قابل رسائی ہے۔"}