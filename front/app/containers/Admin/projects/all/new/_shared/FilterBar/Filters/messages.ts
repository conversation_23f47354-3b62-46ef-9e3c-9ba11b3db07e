import { defineMessages } from 'react-intl';

export default defineMessages({
  phase_starting_or_ending_soon: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Sort.phase_starting_or_ending_soon',
    defaultMessage: 'Phase starting or ending soon',
  },
  recently_viewed: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_viewed',
    defaultMessage: 'Recently viewed',
  },
  recently_created: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Sort.recently_created',
    defaultMessage: 'Recently created',
  },
  alphabetically_asc: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_asc',
    defaultMessage: 'Alphabetically (a-z)',
  },
  alphabetically_desc: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Sort.alphabetically_desc',
    defaultMessage: 'Alphabetically (z-a)',
  },
  status: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Sort.status',
    defaultMessage: 'Status',
  },
  participationStates: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.participationStates',
    defaultMessage: 'Participation state',
  },
  participationMethodLabel: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.label',
    defaultMessage: 'Participation method',
  },
  participationMethodIdeation: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.ideation',
    defaultMessage: 'Ideation',
  },
  participationMethodVoting: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.voting',
    defaultMessage: 'Voting',
  },
  participationMethodInformation: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.information',
    defaultMessage: 'Information',
  },
  participationMethodProposals: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.proposals',
    defaultMessage: 'Proposals',
  },
  participationMethodSurvey: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodSurvey',
    defaultMessage: 'Survey',
  },
  participationMethodPoll: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.poll',
    defaultMessage: 'Poll',
  },
  participationMethodVolunteering: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.volunteering',
    defaultMessage: 'Volunteering',
  },
  pMDocumentAnnotation: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.pMDocumentAnnotation',
    defaultMessage: 'Document annotation',
  },
  participationMethodDocumentCommonGround: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.participationMethodDocumentCommonGround',
    defaultMessage: 'Common ground',
  },
  filterByCurrentPhaseMethod: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationMethod.filterByCurrentPhaseMethod',
    defaultMessage: 'Filter by the current phase participation method',
  },
  notStarted: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.notStarted',
    defaultMessage: 'Not started',
  },
  collectingData: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.collectingData',
    defaultMessage: 'Collecting data',
  },
  informing: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.informing',
    defaultMessage: 'Informing',
  },
  past: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.ParticipationStates.past',
    defaultMessage: 'Past',
  },
  projectStartDate: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Dates.projectStartDate',
    defaultMessage: 'Project start date',
  },
  folders: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Folders.folders',
    defaultMessage: 'Folders',
  },
  visibilityLabel: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Visibility.label',
    defaultMessage: 'Visibility',
  },
  visibilityPublic: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Visibility.public',
    defaultMessage: 'Public',
  },
  visibilityGroups: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Visibility.groups',
    defaultMessage: 'Groups',
  },
  visibilityAdmins: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Visibility.admins',
    defaultMessage: 'Admins',
  },
  discoverabilityLabel: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.discoverabilityLabel',
    defaultMessage: 'Discoverability',
  },
  discoverabilityPublic: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.public',
    defaultMessage: 'Public',
  },
  discoverabilityHidden: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Discoverability.hidden',
    defaultMessage: 'Hidden',
  },
  addFilter: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.addFilter',
    defaultMessage: 'Add filter',
  },
  noMoreFilters: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.noMoreFilters',
    defaultMessage: 'No more filters to add',
  },
  manager: {
    id: 'app.containers.Admin.projects.all.new.Projects.Filters.Sort.manager',
    defaultMessage: 'Manager',
  },
});
