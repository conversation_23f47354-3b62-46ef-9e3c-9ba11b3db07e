import { defineMessages } from 'react-intl';

export default defineMessages({
  notAssigned: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.notAssigned',
    defaultMessage: 'Not assigned',
  },
  xManagers: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.xManagers',
    defaultMessage: '{numberOfManagers} managers',
  },
  draft: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.draft',
    defaultMessage: 'Draft',
  },
  published: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.published',
    defaultMessage: 'Published',
  },
  archived: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.archived',
    defaultMessage: 'Archived',
  },
  loadingMore: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.loadingMore',
    defaultMessage: 'Loading more…',
  },
  scrollDownToLoadMore: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.scrollDownToLoadMore',
    defaultMessage: 'Scroll down to load more',
  },
  allProjectsHaveLoaded: {
    id: 'app.containers.Admin.projects.all.new.Projects.Table.allProjectsHaveLoaded',
    defaultMessage: 'All projects have been loaded',
  },
});
