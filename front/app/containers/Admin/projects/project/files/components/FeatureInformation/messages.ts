import { defineMessages } from 'react-intl';

export default defineMessages({
  uploadAnyFile: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFile',
    defaultMessage: 'Upload any file',
  },
  uploadAnyFileDescription: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.uploadAnyFileDescription',
    defaultMessage: 'PDF, DOCX, PPTX, CSV, PNG, MP3...',
  },
  addFilesToProject: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProject',
    defaultMessage: 'Add files to your project',
  },
  addFilesToProjectDescription: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToProjectDescription',
    defaultMessage:
      'Attach files from this list to your project, phases, and events.',
  },
  useAIOnFiles: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFiles',
    defaultMessage: 'Use AI to analyze files',
  },
  useAIOnFilesDescription: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.useAIOnFilesDescription',
    defaultMessage: 'Process transcripts, etc.',
  },
  addFilesToSensemaking: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemaking',
    defaultMessage: 'Add files as context to Sensemaking',
  },
  addFilesToSensemakingDescription: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.addFilesToSensemakingDescription',
    defaultMessage:
      'Add files to your Sensemaking project to provide context and insights.',
  },
  comingSoon: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoon',
    defaultMessage: 'Coming soon',
  },
  comingSoonDescription: {
    id: 'app.containers.AdminPage.ProjectFiles.FeatureInformation.comingSoonDescription',
    defaultMessage:
      'Sync surveys, upload interviews, and let AI connect the dots across your data.',
  },
});
