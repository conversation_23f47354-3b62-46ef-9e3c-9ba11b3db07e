import { defineMessages } from 'react-intl';

export default defineMessages({
  viewFile: {
    id: 'app.containers.AdminPage.ProjectFiles.viewFile',
    defaultMessage: 'View file',
  },
  downloadFile: {
    id: 'app.containers.AdminPage.ProjectFiles.downloadFile',
    defaultMessage: 'Download file',
  },
  deleteFile: {
    id: 'app.containers.AdminPage.ProjectFiles.deleteFile2',
    defaultMessage: 'Delete file',
  },
  chooseFiles: {
    id: 'app.containers.AdminPage.ProjectFiles.chooseFiles',
    defaultMessage: 'Choose files',
  },
  editFile: {
    id: 'app.containers.AdminPage.ProjectFiles.editFile',
    defaultMessage: 'Edit file',
  },
  dragAndDropFiles: {
    id: 'app.containers.AdminPage.ProjectFiles.dragAndDropFiles2',
    defaultMessage: 'Drag and drop any files here or',
  },
  allowAiProcessing: {
    id: 'app.containers.AdminPage.ProjectFiles.allowAiProcessing',
    defaultMessage:
      'Allow advanced analytics of these files using AI processing.',
  },
  searchFiles: {
    id: 'app.containers.AdminPage.ProjectFiles.searchFiles',
    defaultMessage: 'Search files',
  },
  filesUploadedSuccessfully: {
    id: 'app.containers.AdminPage.ProjectFiles.filesUploadedSuccessfully',
    defaultMessage: 'All files uploaded successfully',
  },
  unknown: {
    id: 'app.containers.AdminPage.ProjectFiles.unknown',
    defaultMessage: 'Unknown',
  },
  confirmDelete: {
    id: 'app.containers.AdminPage.ProjectFiles.confirmDelete',
    defaultMessage: 'Are you sure you want to delete this file?',
  },
  maxFilesError: {
    id: 'app.containers.AdminPage.ProjectFiles.maxFilesError',
    defaultMessage:
      'You can only upload a maximum of {maxFiles} files at a time.',
  },
  noFilesFound: {
    id: 'app.containers.AdminPage.ProjectFiles.noFilesFound',
    defaultMessage: 'No files found.',
  },
  addFiles: {
    id: 'app.containers.AdminPage.ProjectFiles.addFiles',
    defaultMessage: 'Add files',
  },
  close: {
    id: 'app.containers.AdminPage.ProjectFiles.close',
    defaultMessage: 'Close',
  },
  confirmAndUploadFiles: {
    id: 'app.containers.AdminPage.ProjectFiles.confirmAndUploadFiles',
    defaultMessage: 'Confirm and upload',
  },
  tooManyFiles: {
    id: 'app.containers.AdminPage.ProjectFiles.tooManyFiles',
    defaultMessage:
      'You can only upload a maximum of {maxFiles} files at a time.',
  },
  uploadSummary: {
    id: 'app.containers.AdminPage.ProjectFiles.uploadSummary',
    defaultMessage:
      '{numberOfFiles, plural, one {# file} other {# files}} uploaded successfully, {numberOfErrors, plural, one {# error} other {# errors}}.',
  },
  fileSizeError: {
    id: 'app.containers.AdminPage.ProjectFiles.fileSizeError2',
    defaultMessage:
      'This file will not be uploaded, as it exceeds the maximum limit of 50 MB.',
  },
  description: {
    id: 'app.containers.AdminPage.ProjectFiles.description',
    defaultMessage: 'Description',
  },
  upload: {
    id: 'app.containers.AdminPage.ProjectFiles.upload',
    defaultMessage: 'Upload',
  },
  done: {
    id: 'app.containers.AdminPage.ProjectFiles.done',
    defaultMessage: 'Done',
  },
  selectFileType: {
    id: 'app.containers.AdminPage.ProjectFiles.selectFileType',
    defaultMessage: 'File type',
  },
  informationPoint1Title: {
    id: 'app.containers.AdminPage.ProjectFiles.informationPoint1Title',
    defaultMessage: 'Audio interviews, Town Hall recordings',
  },
  informationPoint1Description: {
    id: 'app.containers.AdminPage.ProjectFiles.informationPoint1Description',
    defaultMessage: 'E.g. WAV, MP3',
  },
  informationPoint2Title: {
    id: 'app.containers.AdminPage.ProjectFiles.informationPoint2Title',
    defaultMessage: 'Reports, informational documents',
  },
  informationPoint2Description: {
    id: 'app.containers.AdminPage.ProjectFiles.informationPoint2Description',
    defaultMessage: 'E.g. PDF, DOCX, PPTX',
  },
  informationPoint3Title: {
    id: 'app.containers.AdminPage.ProjectFiles.informationPoint3Title',
    defaultMessage: 'Images',
  },
  informationPoint3Description: {
    id: 'app.containers.AdminPage.ProjectFiles.informationPoint3Description',
    defaultMessage: 'E.g. PNG, JPG',
  },
  retryUpload: {
    id: 'app.containers.AdminPage.ProjectFiles.retryUpload',
    defaultMessage: 'Retry upload',
  },
  meeting: {
    id: 'app.containers.AdminPage.ProjectFiles.meeting',
    defaultMessage: 'Meeting',
  },
  interview: {
    id: 'app.containers.AdminPage.ProjectFiles.interview',
    defaultMessage: 'Interview',
  },
  strategic_plan: {
    id: 'app.containers.AdminPage.ProjectFiles.strategic_plan',
    defaultMessage: 'Strategic plan',
  },
  info_sheet: {
    id: 'app.containers.AdminPage.ProjectFiles.info_sheet',
    defaultMessage: 'Information',
  },
  policy: {
    id: 'app.containers.AdminPage.ProjectFiles.policy',
    defaultMessage: 'Policy',
  },
  report: {
    id: 'app.containers.AdminPage.ProjectFiles.report',
    defaultMessage: 'Report',
  },
  other: {
    id: 'app.containers.AdminPage.ProjectFiles.other',
    defaultMessage: 'Other',
  },
  save: {
    id: 'app.containers.AdminPage.ProjectFiles.save',
    defaultMessage: 'Save',
  },
  fileNameLabel: {
    id: 'app.containers.AdminPage.ProjectFiles.fileNameLabel',
    defaultMessage: 'File Name',
  },
  saveFileMetadataSuccessMessage: {
    id: 'app.containers.AdminPage.ProjectFiles.saveFileMetadataSuccessMessage',
    defaultMessage: 'File updated successfully.',
  },
  categoryLabel: {
    id: 'app.containers.AdminPage.ProjectFiles.categoryLabel',
    defaultMessage: 'Category',
  },
  fileNameCannotContainDot: {
    id: 'app.containers.AdminPage.ProjectFiles.fileNameCannotContainDot',
    defaultMessage: 'File name may not contain a dot.',
  },
  fileNameRequired: {
    id: 'app.containers.AdminPage.ProjectFiles.fileNameRequired',
    defaultMessage: 'File name is required.',
  },
  fileDescriptionLabel: {
    id: 'app.containers.AdminPage.ProjectFiles.fileDescriptionLabel',
    defaultMessage: 'Description',
  },
  askButton: {
    id: 'app.containers.AdminPage.ProjectFiles.askButton',
    defaultMessage: 'Ask',
  },
});
