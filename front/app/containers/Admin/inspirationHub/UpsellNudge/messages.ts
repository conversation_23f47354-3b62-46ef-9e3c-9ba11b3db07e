import { defineMessages } from 'react-intl';

export default defineMessages({
  upsellTitle: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.upsellTitle',
    defaultMessage: 'Join a network of pioneering democracy practitioners',
  },
  upsellDescription: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.upsellDescriptionV2',
    defaultMessage:
      'The Inspiration Hub connects you to a curated feed of exceptional participation projects on Go Vocal platforms across the world. Learn how other cities run successful projects & talk to other practitioners.',
  },
  featureNotIncluded: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.featureNotIncluded',
    defaultMessage:
      'This feature is not included in your current plan. Talk to your Government Success Manager or admin to unlock it.',
  },
  enableInspirationHub: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.enableInspirationHub',
    defaultMessage: 'Enable Inspiration Hub',
  },
  learnMore: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.learnMore',
    defaultMessage: 'Learn more',
  },
  bulletPoint1: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint1',
    defaultMessage: 'Curated list of the best projects around the globe.',
  },
  bulletPoint2: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint2V2',
    defaultMessage: 'Talk to, and learn from, fellow practitioners.',
  },
  bulletPoint3: {
    id: 'app.containers.Admin.inspirationHub.UpsellNudge.bulletPoint3',
    defaultMessage: 'Filter by method, city size & country.',
  },
});
