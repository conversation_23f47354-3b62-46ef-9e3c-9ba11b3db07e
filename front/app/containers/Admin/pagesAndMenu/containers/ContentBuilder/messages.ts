import { defineMessages } from 'react-intl';

export default defineMessages({
  oneColumn: {
    id: 'app.containers.admin.ContentBuilder.homepage.oneColumnLayout',
    defaultMessage: '1 column',
  },
  imageTextCards: {
    id: 'app.containers.admin.ContentBuilder.homepage.imageTextCards',
    defaultMessage: 'Image & text cards',
  },
  projectsTitle: {
    id: 'app.containers.admin.ContentBuilder.homepage.projectsTitle',
    defaultMessage: 'Projects',
  },
  homepageBannerTitle: {
    id: 'app.containers.admin.ContentBuilder.homepage.homepageBannerTitle',
    defaultMessage: 'Homepage banner',
  },
  proposalsTitle: {
    id: 'app.containers.admin.ContentBuilder.homepage.proposalsTitle',
    defaultMessage: 'Proposals',
  },
  eventsTitle: {
    id: 'app.containers.admin.ContentBuilder.homepage.eventsTitle',
    defaultMessage: 'Events',
  },
  default: {
    id: 'app.containers.admin.ContentBuilder.homepage.default',
    defaultMessage: 'default',
  },
  proposalsDisabledTooltip: {
    id: 'app.containers.admin.ContentBuilder.homepage.proposalsDisabledTooltip',
    defaultMessage:
      'Enable proposals in the “Proposals” section in the admin panel to unlock them in the homepage',
  },
  communityMonitorCtaDefaultTitle: {
    id: 'app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultTitle',
    defaultMessage: 'Help us serve you better',
  },
  communityMonitorCtaDefaultDescription: {
    id: 'app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultDescription',
    defaultMessage:
      'This is a quarterly, ongoing survey that tracks how you feel about governance & public services.',
  },
  communityMonitorCtaDefaultSurveyButtonText: {
    id: 'app.containers.admin.ContentBuilder.homepage.communityMonitorCtaDefaultSurveyButtonText',
    defaultMessage: 'Take the survey',
  },
});
