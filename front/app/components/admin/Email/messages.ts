import { defineMessages } from 'react-intl';

export default defineMessages({
  deliveryStatus_sent: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_sent',
    defaultMessage: 'Sent',
  },
  deliveryStatus_failed: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_failed',
    defaultMessage: 'Failed',
  },
  deliveryStatus_accepted: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_accepted',
    defaultMessage: 'Accepted',
  },
  deliveryStatus_delivered: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_delivered',
    defaultMessage: 'Delivered',
  },
  deliveryStatus_opened: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_opened',
    defaultMessage: 'Opened',
  },
  deliveryStatus_clicked: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_clicked',
    defaultMessage: 'Clicked',
  },
  deliveryStatus_bounced: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_bounced',
    defaultMessage: 'Bounced',
  },
  deliveryStatus_clickedTooltip: {
    id: 'app.components.Admin.Campaigns.deliveryStatus_clickedTooltip',
    defaultMessage:
      'When you added one or more links to your email, the number of users who clicked a link will be shown here.',
  },
  recipientsTitle: {
    id: 'app.components.Admin.Campaigns.recipientsTitle',
    defaultMessage: 'Recipients',
  },
  campaignDeletionConfirmation: {
    id: 'app.components.Admin.Campaigns.campaignDeletionConfirmation',
    defaultMessage: 'Are you sure?',
  },
  deleteCampaignButton: {
    id: 'app.components.Admin.Campaigns.deleteCampaignButton',
    defaultMessage: 'Delete campaign',
  },
  sent: {
    id: 'app.components.Admin.Campaigns.sent',
    defaultMessage: 'Sent',
  },
  statsButton: {
    id: 'app.components.Admin.Campaigns.statsButton',
    defaultMessage: 'Statistics',
  },
  draft: {
    id: 'app.components.Admin.Campaigns.draft',
    defaultMessage: 'Draft',
  },
  manageButtonLabel: {
    id: 'app.components.Admin.Campaigns.manageButtonLabel',
    defaultMessage: 'Manage',
  },
  opened: {
    id: 'app.components.Admin.Campaigns.opened',
    defaultMessage: 'Opened',
  },
  clicked: {
    id: 'app.components.Admin.Campaigns.clicked',
    defaultMessage: 'Clicked',
  },
  project: {
    id: 'app.components.Admin.Campaigns.project',
    defaultMessage: 'Project',
  },
  subject: {
    id: 'app.components.Admin.Campaigns.subject',
    defaultMessage: 'Subject',
  },
});
