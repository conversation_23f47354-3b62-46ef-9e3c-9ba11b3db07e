# frozen_string_literal: true

RubyLLM.configure do |config|
  # config.openai_api_key = ENV.fetch('AZURE_OPENAI_API_KEY')
  # config.openai_api_base = ENV.fetch('AZURE_OPENAI_URI')

  config.bedrock_secret_key = ENV.fetch('AWS_SECRET_ACCESS_KEY', nil)
  config.bedrock_api_key = ENV.fetch('AWS_ACCESS_KEY_ID', nil)
  config.bedrock_region = ENV.fetch('AWS_REGION', 'eu-west-2')
end

module RubyLLM
  module Providers
    class Bedrock
      module Models
        def model_id_with_region(model_id, model_data)
          return model_id unless model_data['inferenceTypesSupported']&.include?('INFERENCE_PROFILE')
          return model_id if model_data['inferenceTypesSupported']&.include?('ON_DEMAND')

          region_prefix = config.bedrock_region.split('-').first
          if region_prefix.in? %w[eu ap us]
            "#{region_prefix}.#{model_id}"
          else
            raise ArgumentError, "Unsupported region prefix: #{region_prefix}. Expected 'eu', 'ap', or 'us'."
          end
        end
      end
    end
  end
end

RubyLLM.models.refresh!
