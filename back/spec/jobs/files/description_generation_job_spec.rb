# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Files::DescriptionGenerationJob do
  # Helper method to create a file with AI processing allowed, but without queuing a
  # description generation job.
  def create_ai_file(...)
    # If the `ai_processing_allowed` attribute is set to true at creation time,
    # the uploader will automatically enqueue a description generation job.
    create(:file, ...).tap do |file|
      file.update!(ai_processing_allowed: true)
    end
  end

  describe '#handle_error' do
    let(:job) { described_class.perform_later(create_ai_file) }

    it 'expires the job for unrecoverable errors' do
      expect(job)
        .to receive(:expire)
        .exactly(described_class::UNRECOVERABLE_ERRORS.size).times

      described_class::UNRECOVERABLE_ERRORS.each do |error_class|
        job.send(:handle_error, error_class.new)
      end
    end

    it 'does not expire the job for recoverable errors' do
      expect(job).not_to receive(:expire)
      job.send(:handle_error, StandardError.new)
    end
  end

  describe '#expire', :active_job_que_adapter do
    let(:job) do
      described_class.with_tracking.perform_later(create_ai_file)
    end

    it 'marks the job as complete' do
      job.send(:expire)
      expect(job.tracker).to be_completed
      expect(job.tracker.error_count).to be_positive
    end
  end

  describe '#job_tracking_context', :active_job_que_adapter do
    it 'sets the file as the job context' do
      file = create_ai_file
      job = described_class.with_tracking.perform_later(file)
      expect(job.tracker.context).to eq(file)
    end

    it 'raises an error if the argument is not a Files::File' do
      job = described_class.perform_later('not a file')
      expect { job.send(:job_tracking_context) }
        .to raise_error('Expected file, got String')
    end
  end

  describe '#perform' do
    let(:generator_service) { instance_double(Files::DescriptionGenerator) }

    before do
      allow(Files::DescriptionGenerator).to receive(:new).and_return(generator_service)
      allow(generator_service).to receive(:generate_descriptions!).and_return(true)
    end

    it 'calls the description generator service' do
      job = described_class.new
      expect(job).to receive(:mark_as_complete!)

      file = create_ai_file
      job.perform(file)

      expect(generator_service).to have_received(:generate_descriptions!).with(file)
    end
  end

  describe '#perform_later' do
    context 'with tracking', :active_job_que_adapter do
      it 'creates a tracker with the correct attributes' do
        file = create_ai_file

        job = nil
        expect { job = described_class.with_tracking.perform_later(file) }
          .to change(QueJob, :count).by(1)
          .and change(Jobs::Tracker, :count).by(1)

        expect(job.tracker).to be_present
        expect(job.tracker.root_job_type).to eq('Files::DescriptionGenerationJob')
        expect(job.tracker.context).to eq(file)
        expect(job.tracker.project_id).to be_nil
      end
    end
  end

  # replace RubyLLM with OpenAI::Client
  it '...' do
    require 'openai'
    OpenAI.configure do |config|
      config.access_token = ENV.fetch('AZURE_OPENAI_API_KEY')
      config.uri_base = ENV.fetch('AZURE_OPENAI_URI') + '/openai/v1'
      config.api_type = :azure
      # config.api_version = 'preview'
    end

    client = OpenAI::Client.new

    locales = AppConfiguration.instance.settings('core', 'locales')
    schema = Files::DescriptionGenerator.new.send(:description_schema, locales)
    file_name = 'image17.png'
    prompt = Files::DescriptionGenerator.new.send(:build_prompt, file_name)

    file = create_ai_file(name: file_name)
    file_data = Base64.strict_encode64(file.content.read)
    file_data = "data:#{file.mime_type};base64,#{file_data}"

    response = client.responses.create(parameters: {
      model: 'gpt-4.1',
      input: [
        { role: 'user', content: [
          { type: 'input_text', text: prompt },
          { type: 'input_file', filename: file_name, file_data: file_data }
        ] }
      ],

      text: {
        format: {
          name: 'multiloc_description',
          type: 'json_schema',
          schema: schema,
          strict: true
        }
      }
    })

    # {"id"=>"resp_68a32748f0f08190bd78a52d637187f701dc44fd8ff59dd0", "object"=>"response", "created_at"=>1755522888, "status"=>"completed", "background"=>false, "content_filters"=>nil, "error"=>nil, "incomplete_details"=>nil, "instructions"=>nil, "max_output_tokens"=>nil, "max_tool_calls"=>nil, "model"=>"gpt-4.1", "output"=>[{"id"=>"msg_68a3274941cc819093491f5b1422ab0a01dc44fd8ff59dd0", "type"=>"message", "status"=>"completed", "content"=>[{"type"=>"output_text", "annotations"=>[], "text"=>"Hello, Szymon! 👋 It's great to meet you. How can I help you today?"}], "role"=>"assistant"}], "parallel_tool_calls"=>true, "previous_response_id"=>nil, "prompt_cache_key"=>nil, "reasoning"=>{"effort"=>nil, "summary"=>nil}, "safety_identifier"=>nil, "service_tier"=>"default", "store"=>true, "temperature"=>1.0, "text"=>{"format"=>{"type"=>"text"}}, "tool_choice"=>"auto", "tools"=>[], "top_p"=>1.0, "truncation"=>"disabled", "usage"=>{"input_tokens"=>14, "input_tokens_details"=>{"cached_tokens"=>0}, "output_tokens"=>22, "output_tokens_details"=>{"reasoning_tokens"=>0}, "total_tokens"=>36}, "user"=>nil, "metadata"=>{}}

    # print output
    puts response['output'].first['content'].first['text']
    require 'pry'
    binding.pry

    # delete response
    client.responses.delete(response_id: response['id'])
  rescue StandardError => e
    require 'pry'
    binding.pry
  end

  it '...' do
    # Using RUbyLLM with bedrock
    require 'pry'
    binding.pry

    RubyLLM.configure do |config|
      config.bedrock_region = ENV.fetch('AWS_REGION')
      config.bedrock_access_key_id = ENV.fetch('AWS_ACCESS_KEY_ID')
    end
  end
end
