# frozen_string_literal: true

FactoryBot.define do
  factory :file_attachment, class: 'Files::FileAttachment' do
    transient do
      project { @overrides[:file]&.projects&.first || @overrides[:attachable]&.try(:project) }
      to { :event } # This must be the name of a factory for an attachable resource.
    end

    attachable do
      to == :project ? 
        (project || association(:project)) : 
        (project ? association(to, project: project) : association(to))
    end

    file do
      project ? association(:file, projects: [project]) : association(:global_file)
    end
  end
end
