pt:
  email_campaigns:
    campaign_type_description:
      "manual": Mensagens oficiais
      "manual_project_participants": Mensagens oficiais aos participantes do projeto
      "admin_rights_received": Direitos de administração recebidos
      "comment_deleted_by_admin": Excluir meu comentario
      "comment_marked_as_spam": Comente o relatório do lixo eletrônico
      "comment_on_your_comment": Uma resposta ao meu comentário
      "comment_on_idea_you_follow": Um comentário sobre uma ideia que segue
      "community_monitor_report": Relatório do monitor comunitário
      "cosponsor_of_your_idea": Um usuário aceita meu convite para co-patrocinar minha proposta
      "event_registration_confirmation": Confirmação da inscrição no evento
      "idea_marked_as_spam": Relatório de ideias spam
      "idea_published": Publicação da minha ideia
      "invitation_to_cosponsor_idea": Convite para co-patrocinar uma proposta
      "invite_received": Convite
      "invite_reminder": <PERSON>mbre<PERSON> do convite
      "internal_comment_on_idea_assigned_to_you": Comentário interno sobre os dados que me foram atribuídos
      "internal_comment_on_idea_you_commented_internally_on": Comentário interno sobre a entrada comentei dentro
      "internal_comment_on_idea_you_moderate": Comentário interno sobre a introdução de dados num projeto ou pasta que eu administro
      "internal_comment_on_unassigned_unmoderated_idea": Comentário interno sobre entrada não atribuída em projeto não gerenciado
      "internal_comment_on_your_internal_comment": Comentário interno sobre o meu comentário interno
      "mention_in_official_feedback": Menção em uma atualização
      "mention_in_internal_comment": Menção num comentário interno
      "new_comment_for_admin": Novo comentário em um projeto que eu sou moderador
      "new_idea_for_admin": Nova idéia em um projeto que eu dirigi
      "official_feedback_on_idea_you_follow": Atualizar uma ideia que você segue
      "password_reset": Redefinir a senha
      "project_moderation_rights_received": Direitos de moderação de projetos recebidos
      "project_folder_moderation_rights_received": Direitos de moderação da pasta de projetos recebidos
      "project_phase_started": Nova fase do projeto
      "project_phase_upcoming": Próxima nova fase do projeto
      "project_published": Projeto publicado
      "project_review_request": Solicitação de revisão do projeto
      "project_review_state_change": Projeto aprovado
      "status_change_on_idea_you_follow": Mudança de status de uma ideia que você segue
      "survey_submitted": Pesquisa enviada
      "threshold_reached_for_admin": Proposta atingiu o limite de votação
      "welcome": Após o cadastro
      "admin_digest": Resumo semanal para gestores
      "moderator_digest": Resumo semanal para gestores de projeto
      "assignee_digest": Resumo semanal das ideas atribuídas
      "user_digest": Resumo semanal
      "voting_basket_submitted": Confirmação da votação
      "native_survey_not_submitted": Pesquisa não enviada
      "voting_basket_not_submitted": Votos não enviados
      "voting_last_chance": Última chance de votar
      "voting_phase_started": Nova fase do projeto com votação
      "voting_results": Resultados da votação
      "your_input_in_screening": Minha contribuição aguarda a triagem
    general:
      by_author: 'por %{authorName}'
      author_wrote: '%{authorName} escreveu:'
      cta_goto_idea: 'Ir para esta ideia'
      cta_goto_input: 'Ir para esta entrada'
      cta_goto:
        idea: 'Ir para esta ideia'
        question: 'Ir para esta pergunta'
        contribution: 'Ir para esta contribuição'
        project: 'Ir para este projeto'
        issue: 'Ir para esta edição'
        option: 'Vá para esta opção'
        proposal: 'Ir para esta proposta'
        petition: 'Vá para esta petição'
      cta_goto_your:
        idea: 'Vá para a sua ideia'
        question: 'Vá para sua pergunta'
        contribution: 'Vá para sua contribuição'
        project: 'Vá para o seu projeto'
        issue: 'Vá para o seu problema'
        option: 'Vá para sua opção'
        proposal: 'Vá para sua proposta'
        petition: 'Vá para sua petição'
      cta_goto_proposal: 'Ir para esta proposta'
      cta_goto_project: 'Ir para este projeto'
    schedules:
      weekly:
        "0": "Semanalmente, aos domingos às %{hourOfDay}"
        "1": "Semanalmente, às segundas-feiras às %{hourOfDay}"
        "2": "Semanalmente, às terças-feiras às %{hourOfDay}"
        "3": "Semanalmente, às quartas-feiras às %{hourOfDay}"
        "4": "Semanalmente, às quintas-feiras às %{hourOfDay}"
        "5": "Semanalmente, às sextas-feiras às %{hourOfDay}"
        "6": "Semanalmente, aos sábados às %{hourOfDay}"
      quarterly: "Trimestralmente, no primeiro dia do trimestre"
    preview_data:
      first_name: 'Jane'
      last_name: 'Doe'
      display_name: 'Jane Doe'
      comment_body: 'Este é um exemplo de comentário usado para visualizar o conteúdo de e-mails. Não se trata de conteúdo real.'
      idea_title: 'Ideia de exemplo'
    footer:
      "link_privacy_policy": "Política de Privacidade"
      "link_terms_conditions": "Termos e condições"
      "link_unsubscribe": "Cancelar a inscrição"
      "powered_by": "Criado por"
      "recipient_statement": "Este e-mail foi enviado a você pela Go Vocal em nome de %{organizationName}, porque você é um usuário registrado de %{organizationLink}."
      "unsubscribe_statement": "Você pode acessar %{unsubscribeLink} se não quiser receber esses e-mails no futuro."
      "unsubscribe_text": "Cancelar a inscrição"
    follow:
      "unfollow_here": "Você recebeu esta notificação por causa de um item que você segue. <a href=\"%{unfollow_url}\">Você pode parar de segui-lo aqui.</a>"
    manual:
      preheader: 'Você tem uma correspondência de %{organizationName}'
    comment_deleted_by_admin:
      reason: 'A razão pela qual o seu comentário foi excluido:'
      cta_view: 'Veja essa idéia'
      event_description: '%{organizationName} apagou o comentário que você escreveu sobre uma idéia.'
      main_header: '%{organizationName} excluiu o seu comentário'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'O seu comentário foi considerado inapropriado.'
      irrelevant_reason: 'O seu comentário foi considerado irrelevante para esse contexto.'
      no_reason: 'Nenhuma razão foi especificada'
      subject: 'O seu comentário foi excluido na plataforma de %{organizationName}'
      preheader: 'O seu comentário foi excluido'
    admin_digest:
      subject: 'Seu relatório semanal de administrador de %{time}'
      preheader: 'Resumo administrativo %{organizationName}'
      title_your_weekly_report: '%{firstName}, o seu relatório semanal'
      text_introduction: 'Nós editamos para você a listagem de idéias que geraram mais atividade na semana passada. Descubra o que está acontecendo na sua plataforma!'
      cta_visit_the_platform: 'Visite a sua plataforma'
      new_users: 'Novos usuários'
      new_inputs: 'Novas entradas'
      new_comments: 'Novos comentários'
      title_activity_past_week: 'Atividade da semana passada'
      title_no_activity_past_week: 'Não houve actividade na semana passada.'
      reached_threshold: 'Atingiu o limite'
      yesterday_by_author: 'Ontem por %{author}'
      today_by_author: 'Hoje por %{author}'
      x_days_ago_by_author: '%{x} días atrás por %{author}'
    admin_rights_received:
      cta_manage_platform: 'Gerencie sua plataforma'
      message_you_became_administrator: 'Foram-lhe concedidos direitos de administrador para a plataforma de participação de %{organizationName}.'
      preheader: 'Foram-lhe concedidos direitos de administrador para a plataforma de participação de %{organizationName}.'
      subject: 'Você se tornou um administrador na plataforma de %{organizationName}'
      text_create_participatory_process: 'Como administrador, você pode criar e configurar novos projetos de participação. Você pode adicionar novas fases usando a linha do tempo. Cada uma destas fases pode ter o seu próprio comportamento no que diz respeito ao lançamento de idéias, comentários e votação.'
      text_moderate_analyse_input: 'Uma vez que os projetos sejam lançados, as primeiras idéias chegarão. Você receberá relatórios semanais com todas as atividades necessárias para que você fique ciente de tudo. A visão geral das idéias o ajudará a moderar a entrada e a colaborar no processamento das mesmas.'
      text_platform_setup: 'Como administrador, você pode configurar sua plataforma de participação. Escolha um logotipo, imagens e cores, escreva uma mensagem pessoal na sua página inicial, envie convites, defina o que quer saber dos seus usuários, ...'
      title_create_participatory_process: 'Desenhar o processo participativo'
      title_moderate_analyse_input: 'Dirigir e analisar os dados introduzidos'
      title_platform_setup: 'Configure a sua plataforma'
      title_what_can_you_do_administrator: 'O que você pode fazer como administrador?'
      title_you_became_administrator: 'Você se tornou um administrador'
    comment_marked_as_spam:
      by_author: 'por %{authorName}'
      commented: '%{authorName} comentado:'
      cta_review_comment: 'Verificar o comentario'
      days_ago: '%{numberOfDays} dias atrás'
      event_description: 'O seguinte comentário postado em <strong>''%{post}''</strong> foi reportado:'
      inappropriate_content: 'Esse comentário é inapropriado ou ofensivo.'
      preheader: 'Ação sobre o comentário reportado como spam'
      reported_this_because: '%{reporterFirstName} denunciou isto porque:'
      subject: '%{organizationName}: %{firstName} %{lastName} denunciou este comentário como spam'
      title_comment_spam_report: '%{firstName} %{lastName} denunciou este comentário como spam'
      today: Hoje
      wrong_content: 'Esse comentário não é relevante.'
      yesterday: Ontem
    community_monitor_report:
      subject: 'Um novo relatório do monitor comunitário está disponível'
      title: 'Um novo relatório do monitor comunitário está disponível'
      text_introduction: 'Você pode acessá-lo clicando no botão abaixo e fazendo login. Você pode acessá-lo clicando no botão abaixo e fazendo login.'
      cta_report_button: 'Ver relatório'
      report_name: 'Relatório do monitor comunitário'
    cosponsor_of_your_idea:
      cta_reply_to: 'Ver sua proposta'
      event_description: 'Parabéns! O site %{cosponsorName} aceitou o seu convite para co-patrocinar a sua proposta.'
      main_header: '%{cosponsorName} aceitou o seu convite para co-patrocinar a sua proposta'
      subject: '%{cosponsorName} aceitou o seu convite para co-patrocinar a sua proposta'
      preheader: '%{cosponsorName} aceitou o seu convite para co-patrocinar a sua proposta'
    assignee_digest:
      subject: 'idéias que requerem a sua opinião: %{numberIdeas}'
      preheader: 'Resumo do proprietario %{organizationName}'
      title_your_weekly_report: '%{firstName}, a entrada dos usuários está à espera da sua opinião'
      cta_manage_your_input: 'Administrar o acesso dos usuários'
      x_inputs_need_your_feedback: 'entradas precisam do seu feedback'
      title_assignment_past_week: 'Últimas ideias atribuídas a você'
      title_no_assignment_past_week: 'Nenhuma nova ideia foi atribuída a você na semana passada'
      yesterday_by_author: 'Ontem por %{author}'
      today_by_author: 'Hoje por %{author}'
      x_days_ago_by_author: '%{x} días atrás por %{author}'
      title_successful_past_week: 'Atribuído a você que atingiu o limite'
    idea_marked_as_spam:
      cta_review: 'Revisão'
      report_inappropriate_offensive_content: 'Conteúdo inapropriado ou ofensivo.'
      report_not_an_idea: 'Este conteúdo não é uma idéia e não pertence a este espaço.'
      subject: 'Você tem um relatório como spam na plataforma de %{organizationName}'
      preheader: 'Ação a ser realizada no relatório de spam'
      reported_this_because: '%{reporterFirstName} denunciou isto porque:'
      title_spam_report: '%{firstName} %{lastName} spam reportado'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Co-patrocinar esta proposta'
      event_description: '%{authorName} criou uma nova proposta e gostaria que você a co-patrocinasse.'
      event_description_cosponsoring: 'Co-patrocinar uma proposta significa que <strong>seu nome será exibido</strong> com os nomes de outros co-patrocinadores da proposta.'
      event_description_before_action: 'Para ver a proposta e aceitar o convite, você deve estar conectado à sua conta.'
      event_description_action: 'Clique abaixo para ler a proposta.'
      main_header: 'Você foi convidado a co-patrocinar uma proposta'
      subject: 'Você foi convidado a co-patrocinar uma proposta'
      preheader: 'Você foi convidado a co-patrocinar a proposta do site %{authorName}'
    invite_reminder:
      cta_accept_invitation: 'Aceite o seu convite'
      invitation_header: 'O seu convite está pendente'
      preheader: '%{organizationName} enviou-lhe um convite para se juntar à sua plataforma de participação há alguns dias.'
      invitation_expiry_message: 'Este convite expira aproximadamente em %{expiryDaysRemaining} dias.'
      subject: 'Você tem pendente um convite para a plataforma de participação de %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} escreveu a seguinte mensagem:'
      cta_accept_invitation: 'Aceite o seu convite'
      invitation_header: 'Você está convidado!'
      invitation_header_message: '%{organizationName} convidou-o para a sua plataforma de participação.'
      invitation_expiry_message: 'Este convite expira em %{expiryDays} dias.'
      preheader: '%{organizationName} Convidou você para se juntar à sua plataforma de participação.'
      subject: 'Está convidado a juntar-se à plataforma de %{organizationName}'
    mention_in_comment:
      cta_reply_to: 'Responder a %{commentAuthor}'
      event_description: '%{commentAuthorFull} mencionou você no seu comentário sobre a idéia ''%{post}''. Clique no link embaixo para entrar na conversa com '' %{commentAuthor}'
      main_header: 'As pessoas estão falando de você'
      subject: 'Alguém mencionou você na plataforma de %{organizationName}'
      preheader: '%{commentAuthor} mencionou você em um comentário'
    mention_in_internal_comment:
      cta_reply_to: 'Ver comentário de %{firstName}'
      event_description: '%{authorNameFull} mencionou você em um comentário interno.'
      subject: '%{firstName} mencionou você em um comentário interno.'
      main_header: '%{firstName} mencionou você em um comentário interno.'
      preheader: '%{authorNameFull} mencionou você em um comentário interno.'
    moderator_digest:
      subject: 'Seu relatório semanal como gerente do projeto de %{project_title}'
      preheader: 'Resumo de gerente do projeto de %{organizationName}'
      title_your_weekly_report: '%{firstName}, o seu relatório semanal'
      text_introduction: 'Nós editamos para você a listagem de idéias que geraram mais atividade na semana passada. Descubra o que está acontecendo com o seu projeto!'
      cta_manage: 'Gerencie seu projeto'
      new_users: 'Novos usuários'
      new_ideas: 'Novas idéias'
      new_comments: 'Novos comentários'
      title_inputs_past_week: 'Novas entradas na última semana'
      title_no_inputs_past_week: 'Nenhuma entrada nova na última semana'
      title_threshold_reached: Limite atingido na última semana
      yesterday_by_author: 'Ontem por %{author}'
      today_by_author: 'Hoje por %{author}'
      x_days_ago_by_author: '%{x} días atrás por %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} comentado:'
      cta_reply_to: 'Checar o comentário de %{commentAuthor}'
      days_ago: '%{numberOfDays} dias atrás'
      event_description: '%{authorName} escreveu um comentário sobre a sua plataforma. Não hesite em juntar-se à discussão e mantenha a conversa ativa!'
      main_header: '%{firstName}, um novo comentário foi postado na sua plataforma'
      subject: 'Há um novo comentário sobre a plataforma em %{organizationName}'
      preheader: '%{authorName} deixou um comentário'
      today: Hoje
      yesterday: Ontem
    comment_on_idea_you_follow:
      cta_reply_to: 'Responder a %{commentAuthor}'
      event_description: '%{authorNameFull} placed a reaction on ''%{inputTitle}''. Click the button below to continue the conversation with %{authorName}.'
      main_header:
        idea: '%{authorName} comentou sobre uma ideia que você segue'
        question: '%{authorName} comentou sobre uma pergunta que você segue'
        contribution: '%{authorName} comentou em uma contribuição que você segue'
        project: '%{authorName} comentou sobre um projeto que você segue'
        issue: '%{authorName} comentou sobre um assunto que você acompanha'
        option: '%{authorName} comentou sobre uma opção que você segue'
        proposal: '%{authorName} comentou sobre uma proposta que você segue'
        petition: '%{authorName} comentou em uma petição que você segue'
      subject: 'There''s a new comment on "%{input_title}"'
      preheader: '%{authorName} deixou um comentário sobre uma ideia para %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Uma nova entrada foi publicada em sua plataforma'
      event_description_publication: '%{authorName} enviou uma nova entrada em sua plataforma. Descubra-a agora, dê um feedback ou altere seu status!'
      cta_publication: 'Dê feedback para %{authorName}'
      main_header_prescreening: 'Uma entrada requer sua revisão'
      event_description_prescreening: '%{authorName} enviou uma nova entrada em sua plataforma.'
      input_not_visible_prescreening: '<b>A entrada não ficará visível</b> até que você modifique seu status.'
      cta_prescreening: 'Revisar a entrada'
      days_ago: '%{numberOfDays} dias atrás'
      preheader: '%{authorName} postou uma nova idéia na sua plataforma'
      today: Hoje
      yesterday: Ontem
    comment_on_your_comment:
      cta_reply_to: 'Responder a %{firstName}'
      event_description: '%{authorNameFull} escreveu uma resposta ao seu comentário sobre ''%{post}'' na plataforma de participação. Clique no botão embaixo para continuar a conversa com '' %{authorName}.'
      subject: 'Você recebeu uma resposta ao seu comentário sobre a plataforma de %{organizationName}'
      main_header: '%{authorName} respondeu ao seu comentário'
      preheader: '%{authorName} respondeu ao seu comentário sobre a plataforma de %{organizationName}'
      replied: '%{authorFirstName} respondeu:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Ver comentário de %{firstName}'
      event_description: '%{authorNameFull} comentou o seu comentário interno.'
      subject: 'Recebeu um comentário sobre o seu comentário interno em ''%{post}'''
      main_header: 'Recebeu um comentário sobre o seu comentário interno em ''%{post}'''
      preheader: '%{authorName} respondeu ao seu comentário interno sobre ''%{post}'''
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Ver comentário de %{firstName}'
      event_description: '%{authorNameFull} comentou dentro de uma entrada que lhe foi atribuída.'
      subject: '''%{post}'' tem um novo comentário interno'
      main_header: '''%{post}'' tem um novo comentário interno'
      preheader: '''%{post}'' tem um novo comentário interno'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Ver comentário de %{firstName}'
      event_description: '%{authorNameFull} comentou dentro de uma entrada que também comentou dentro.'
      subject: '''%{post}'' tem um novo comentário interno'
      main_header: '''%{post}'' tem um novo comentário interno'
      preheader: '''%{post}'' tem um novo comentário interno'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Ver comentário de %{firstName}'
      event_description: '%{authorNameFull} comentou dentro de uma entrada num projeto ou pasta que gerencia.'
      subject: '''%{post}'' tem um novo comentário interno'
      main_header: '''%{post}'' tem um novo comentário interno'
      preheader: '''%{post}'' tem um novo comentário interno'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Ver comentário de %{firstName}'
      event_description: '%{authorNameFull} comentou internamente sobre uma entrada não atribuída num projeto não gerido.'
      subject: '''%{post}'' tem um novo comentário interno'
      main_header: '''%{post}'' tem um novo comentário interno'
      preheader: '''%{post}'' tem um novo comentário interno'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} gave an update on ''%{input_title}''.'
      header_title: 'There''s an update on "%{input_title}"'
      subject: 'Official feedback was posted on "%{input_title}"'
      preheader: 'There''s an update on an input you follow'
    mention_in_official_feedback:
      cta_reply_to: 'Responder a %{organizationName}'
      event_description: '%{organizationName} mencionaram-no na sua opinião sobre a idéia ''%{post}''. Clique no link embaixo para entrar na conversa com '' %{organizationName}'
      main_header: 'Você foi mencionado'
      subject: '%{organizationName} mencionou você na sua opinião'
      preheader: '%{commentAuthor} mencionou você na opinião'
    project_moderation_rights_received:
      cta_manage_project: 'Gerenciar este projeto'
      message_you_became_moderator: 'Um administrador da plataforma de %{organizationName} acabou de fazer você gerente do seguinte projeto:'
      no_ideas: 'Nenhuma idéia até o momento'
      preheader: 'Um administrador da plataforma %{organizationName} acabou de fazer você gestor do seguinte projeto'
      subject: 'Você se tornou o gestor de projeto da plataforma de %{organizationName}'
      text_design_participatory_process: 'Como administrador do projeto, você pode configurar como os usuários interagem com o seu projeto. Você pode adicionar novas fases usando a linha do tempo. Cada uma destas fases pode ter o seu próprio comportamento com relação à publicação de idéias, comentários e votação.'
      text_moderate_analyse_input: 'Uma vez lançado o projeto, as primeiras ideias chegarão. Você receberá relatórios semanais com todas as atividades chave para que você fique a par das coisas. A visão geral das ideias irá ajudá-lo a compreender quais obtiveram mais votos favoráveis e desfavoráveis.'
      text_share_project_information: 'A fim de aumentar a qualidade das idéias que você está recebendo, é fundamental compartilhar informações: adicionar a descrição do projeto, anexar imagens (incluindo esboços e planos), e comunicar todos os eventos que estão acontecendo. Lembre-se: uma boa informação antecede uma boa participação!'
      title_design_participatory_process: 'Desenhar o processo participativo'
      title_moderate_analyse_input: 'Moderar e analisar os dados introduzidos'
      title_share_project_information: 'Fornecer informações sobre o projeto'
      title_what_can_you_do_moderator: 'O que você pode fazer como administrador do projeto?'
      title_you_became_moderator: 'Você se tornou um administrador de projeto'
      x_ideas: '%{numberOfIdeas} ideias'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Visualizar essa pasta'
      message_added_as_folderadmin: 'Você recebeu direitos de administrador na plataforma de participação de %{organizationName} para a seguinte pasta:'
      no_projects: 'Nenhum projeto por enquanto'
      preheader: 'Este é o link que você solicitou para redefinir sua senha'
      subject: '%{organizationName}: Redefina sua senha'
      text_manage_folder: 'Uma pasta é uma maneira de organizar vários projetos de participação em conjunto. Como administrador de pasta, você pode editar a pasta e a descrição da pasta e criar novos projetos (para excluir projetos, entre em contato com o administrador da sua plataforma). Você também terá direitos de gerenciamento de projeto sobre todos os projetos dentro da pasta, o que permite que você edite os projetos, gerencie as entradas e envie e-mails para os participantes.'
      text_moderate_analyse_input: 'Assim que os projetos forem lançados, as primeiras entradas começarão a surgir. Você receberá relatórios semanais com as principais atividades para que possa ficar atualizado sobre o que está acontecendo. O Gerenciador de Posts na sua visualização de administrador ajudará você a ver e gerenciar a entrada, incluindo atribuir status e responder posts e comentários.'
      text_design_participatory_process: 'Você pode gerenciar os projetos de participação dentro da sua pasta — configure o método de participação, adicione uma descrição ao projeto, anexe imagens e comunique eventos relacionados. Você também pode gerenciar como os participantes interagem com os seus projetos, incluindo a definição de direitos de acesso e a configuração de postagens, votações e comentários.'
      title_design_participatory_process: 'Desenhar o processo participativo'
      title_moderate_analyse_input: 'Dirigir e analisar os dados introduzidos'
      title_manage_folder: 'Gerencie as configurações de pasta e crie novos projetos.'
      title_what_can_you_do_folderadmin: 'O que você pode fazer como administrador de pasta?'
      title_added_as_folderadmin: 'Você foi adicionado como administrador de pasta'
      x_projects: '%{numberOfProjects} projetos'
    project_phase_started:
      cta_view_phase: 'Descobrir esta nova fase'
      event_description: 'Este projeto entrou numa nova fase na plataforma de %{organizationName}. Clique no link embaixo para saber mais!'
      main_header: 'Uma nova fase foi iniciada para o projeto ''%{projectName}'''
      subtitle: 'Sobre ''%{projectName}'''
      new_phase: 'Este projeto entrou na fase ''%{phaseTitle}'''
      subject: '%{projectName} entrou numa nova fase'
      preheader: 'Foi iniciada uma nova fase para %{projectName}'
    project_phase_upcoming:
      cta_view_phase: 'Configurar esta nova fase'
      event_description: 'O projeto ''%{projectName}'' estará entrando em uma nova fase em breve. Certifique-se de que todo está preparado para esta fase: Existe uma descrição adequada? As idéias seleccionadas são transferidas para esta fase? Você quer informar para seus usuários sobre as carateristicas desta fase através de uma campanha de e-mail personalizada?'
      main_header: '%{firstName}, um projeto entrará numa nova fase em breve'
      subtitle: 'Sobre ''%{projectName}'''
      new_phase: 'O projeto entrará na fase ''%{phaseTitle}'''
      subject: 'Prepare tudo para a nova fase de %{projectName}'
      preheader: 'Uma nova fase começará em breve para %{projectName}'
    project_published:
      subject: 'Um novo projeto foi publicado na plataforma do %{organizationName}'
      header_title: 'Um novo projeto foi publicado'
      header_message: 'A plataforma de participação %{organizationName} acaba de publicar o seguinte projeto:'
      preheader: 'Um novo projeto foi publicado'
    project_review_request:
      subject: 'Solicitação de revisão: Um projeto está aguardando aprovação.'
      header: '%{requesterName} convidou você para avaliar o projeto "%{projectTitle}"'
      header_message: "No momento, o projeto está em modo de rascunho e não está visível para os usuários. Depois que você o revisar e aprovar, o moderador poderá publicá-lo."
      cta_review_project: "Revisar o projeto"
    project_review_state_change:
      subject: '"%{projectTitle}" " foi aprovado'
      header: '%{reviewerName} aprovou o projeto "%{projectTitle}"'
      header_message: "Agora o projeto está pronto para ser lançado. Você pode publicá-lo quando estiver pronto!"
      cta_go_to_project: "Vá para as configurações do projeto"
    status_change_on_idea_you_follow:
      status_change: 'The new status of this input is ''%{status}'''
      header_message: '%{organizationName} updated the status of the input ''%{input_title}'' on their digital participation platform.'
      header_title: 'An input you follow has a new status'
      subject: 'The status of "%{input_title}" has changed'
      preheader: 'An input you follow has a new status'
    user_digest:
      subject: "Sua atividade na plataforma de participação de %{organizationName}"
      commented: "%{authorFirstName} comentado:"
      preheader: "Visão geral da semana de %{organizationName}"
      title_your_weekly_report: "Descubra o que aconteceu na semana passada"
      intro_text: "Aqui está um resumo do que está acontecendo na plataforma de participação de %{organizationName}."
      cta_go_to_the_platform: "Ir para a plataforma"
      title_no_activity_past_week: "Nenhuma atividade na última semana"
      successful_proposals_title: "Atingiu o limite"
      successful_proposals_text: "Essas propostas receberam apoio suficiente para avançar para a próxima fase! Clique na proposta para saber mais sobre o que acontece a seguir."
      today_by_author: "Hoje por %{author}"
      yesterday_by_author: "Ontem por %{author}"
      x_days_ago_by_author: "%{x} dias atrás por %{author}"
      trending_title: "Tendências"
      trending_text: "Você tem interesse em saber o que está acontecendo na plataforma? Aqui estão as três contribuições mais populares e o que as pessoas estão dizendo sobre elas."
      no_notifications: "Sem notificações"
      one_notification: "1 notificação"
      multiple_notifications: "%{notifCount} notificações"
      no_unread_notifications: "É hora de gerar alguma nova atividade! Visite a plataforma para descobrir o que está acontecendo."
      unread_notifications: "Você tem novas notificações! Visite a plataforma para descobrir o que está acontecendo."
    threshold_reached_for_admin:
      cta_process_initiative: 'Leve esta iniciativa para os próximos passos'
      main_header: 'Uma iniciativa estourou o limite de votação!'
      subject: 'Uma iniciativa estourou o limite de votação na sua plataforma!'
      preheader: 'Garantir de dar próximos passos'
    welcome:
      cta_join_platform: 'Explorar a plataforma'
      subject: 'Bem-vindo à plataforma de %{organizationName}'
      main_header: Bem-vindo!
      message_welcome: 'Parabéns, você se cadastrou com sucesso na plataforma de participação da (e) %{organizationName}. Agora você pode descobrir a plataforma e fazer ouvir a sua voz. Você também pode adicionar uma foto do perfil e uma breve descrição para dizer aos outros quem você é.'
      preheader: 'Aqui esta o que você pode fazer na plataforma de %{organizationName}'
    idea_published:
      subject:
        idea: 'Sua ideia foi publicada'
        question: 'Sua pergunta foi publicada'
        contribution: 'Sua contribuição foi publicada'
        project: 'Seu projeto foi publicado'
        issue: 'Sua edição foi publicada'
        option: 'Sua opção foi publicada'
        proposal: 'Sua proposta foi publicada'
        petition: 'Sua petição foi publicada'
      main_header: 'Você postou uma idéia! Vamos garantir que ela seja lida.'
      header_message: 'Vamos garantir que você o leia.'
      message_get_votes: 'Alcance mais pessoas com a sua idéia:'
      action_published_idea: 'Idea postada'
      action_add_image: '%{addImageLink} aumentar a visibilidade'
      add_image: 'Adicionar uma imagem'
      action_share_fb: 'Aceite seus amigos agora em %{fbLink}'
      action_share_twitter: 'Informe os seus seguidores em %{twitterLink}'
      action_send_email: 'Envie seus contactos para %{sendEmailLink}'
      send_email: e-mail
      action_share_link: 'Compartilhar através de qualquer canal, copiando o %{link}'
      link: Link
      preheader: '%{firstName}, parabéns por postar a sua idéia na plataforma de %{organizationName}. Agora mobilize apoio.'
    your_input_in_screening:
      main_header:
        idea: 'Sua ideia está em "%{prescreening_status_title}"'
        question: 'Sua pergunta está em "%{prescreening_status_title}"'
        contribution: 'Sua contribuição está em "%{prescreening_status_title}"'
        project: 'Seu projeto está em "%{prescreening_status_title}"'
        issue: 'Seu problema está em "%{prescreening_status_title}"'
        option: 'Sua opção está em "%{prescreening_status_title}"'
        proposal: 'Sua proposta está em "%{prescreening_status_title}"'
        petition: 'Sua petição está em "%{prescreening_status_title}"'
      message: '"%{input_title}" se tornará visível para os outros quando for revisado e aprovado.'
      subject: '"%{input_title}" está quase publicado'
      preheader: 'No momento, você está em %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}: Votou com sucesso'
      preheader: 'Você votou com sucesso na plataforma de participação de %{organizationName}'
      title_basket_submitted: 'Você votou com sucesso'
      event_description: 'Obrigado por participar. Os seus votos foram registados. Visite a plataforma de %{organizationName} para ver e gerenciar os seus votos.'
      cta_see_votes_submitted: 'Veja os votos enviados'
      cta_message: 'Clique no botão abaixo para participar'
    native_survey_not_submitted:
      subject: '%{organizationName}: Quase lá! Envie suas respostas'
      preheader: 'Você não concluiu a resposta da pesquisa na plataforma de participação de %{organizationName}'
      title_native_survey_not_submitted: 'Quase lá! Envie suas respostas'
      body_native_survey_not_submitted: 'Você começou a compartilhar suas respostas em %{phaseTitle} , mas não as enviou. Os envios serão encerrados em %{phaseEndDate}. Clique no botão abaixo para continuar de onde você parou.'
      body_native_survey_not_submitted_no_date: 'Você começou a compartilhar suas respostas em %{phaseTitle} , mas não as enviou. Clique no botão abaixo para continuar de onde você parou.'
      cta_complete_your_survey_response: 'Retomar sua resposta à pesquisa'
    voting_basket_not_submitted:
      subject: '%{organizationName}: Você não enviou seus votos'
      preheader: 'Você não enviou os seus votos na plataforma de participação de %{organizationName}'
      title_basket_not_submitted: 'Você não enviou seus votos'
      event_description: 'Você selecionou algumas opções para %{contextTitle}, mas não enviou sua seleção.'
      cta_view_options_and_vote: 'Ver opções e votar'
      cta_message: 'Clique no botão abaixo para enviar as opções selecionadas'
    voting_last_chance:
      subject: '%{organizationName}: Última oportunidade para votar em %{phaseTitle}'
      preheader: 'Última oportunidade para votar em %{phaseTitle} na plataforma de participação de %{organizationName}'
      title_last_chance: 'Última oportunidade para votar em %{phaseTitle}'
      body_1: 'A fase de votação do projeto %{projectTitle} termina amanhã, à meia-noite.'
      body_2: 'O tempo está acabando e notamos que você ainda não votou! Faça isso agora simplesmente clicando no botão abaixo para participar.'
      body_3: 'Ao fazê-lo, terá acesso a uma série de opções e terá a oportunidade de dar a sua contribuição, que é crucial para decidir o futuro deste projeto.'
      cta_vote: 'Voto'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} resultados da votação revelados!'
      preheader: '%{phaseTitle} resultados da votação revelados na plataforma de participação de %{organizationName}'
      title_results: '%{phaseTitle} resultados da votação revelados!'
      body_1: 'Os resultados já chegaram!'
      body_2: 'Os resultados da votação %{phaseTitle} na plataforma %{organizationName} foram publicados!'
      body_3: 'Recomendamos que você analise os resultados e fique atento para mais atualizações sobre as próximas etapas.'
      cta_see_results: 'Veja os resultados na plataforma'
    event_registration_confirmation:
      subject: "Você está dentro! Sua inscrição para \"%{eventTitle}\" está confirmada"
      preheader: "%{firstName}Obrigado por se registrar em %{eventTitle}"
      header_message: "%{firstName}, obrigado por registrar-se para"
      event_details:
        labels:
          date: 'Data'
          location: 'Localização'
          online_link: 'Link on-line'
          description: 'Descrição'
          project: 'Projeto'
      cta_go_to_event: 'Ver o evento'
      cta_add_to_calendar: 'Adicionar ao seu calendário'
    voting_phase_started:
      subject: '%{organizationName}: A fase de votação começou em %{projectName}'
      preheader: 'Começou a fase de votação para %{projectName} na plataforma de participação de %{organizationName}'
      event_description: 'O projeto "%{projectName}" está pedindo para você votar entre %{numIdeas} opções:'
      cta_message: 'Clique no botão abaixo para participar'
      cta_vote: 'Acesse a plataforma para votar'
    survey_submitted:
      subject: '%{organizationName}: Obrigado por sua resposta! 🎉'
      preheader: 'Aqui estão os detalhes do seu envio.'
      main_header: 'Obrigado por compartilhar o que você pensa sobre "%{projectName}"!'
      your_input_submitted: 'Sua entrada para "%{projectName}" foi enviada com sucesso.'
      if_you_would_like_to_review: 'Se quiser revisar seu envio, você pode fazer o download das respostas abaixo.'
      your_submission_has_id: 'Seu envio tem o seguinte identificador exclusivo:'
      you_can_use_this_id: 'Você pode usar esse identificador para entrar em contato com os administradores da plataforma, caso queira que seu envio seja removido.'
      download_responses: 'Baixe suas respostas'
    admin_labels:
      recipient_role:
        admins: 'Aos administradores'
        admins_and_managers: 'Aos administradores e gestores'
        managers: 'Aos gestores'
        project_participants: 'Aos participantes do projeto'
        registered_users: 'Aos usuários registrados'
      recipient_segment:
        admins: 'Administradores'
        admins_and_managers: 'Administradores e gestores'
        admins_and_managers_assigned_to_the_input: 'Administradores e gestores atribuídos à entrada'
        admins_and_managers_managing_the_project: 'Administradores e gestores que gerenciam o projeto'
        admins_assigned_to_a_proposal: 'Administradores atribuídos a uma proposta'
        all_users: 'Todos os usuários'
        all_users_who_uploaded_proposals: 'Todos os usuários que enviaram propostas'
        managers: 'Gestores'
        managers_managing_the_project: 'Gestores que gerem o projeto'
        new_attendee: 'Usuário recém-registrado'
        project_reviewers: 'Revisores de projetos e gerentes de pastas'
        project_review_requester: 'Usuário que solicitou a revisão do projeto'
        user_who_commented: 'Usuário que comentou'
        user_who_is_invited_to_cosponsor_a_proposal: 'Utilizador que é convidado a co-patrocinar uma proposta'
        user_who_is_mentioned: 'Usuário mencionado'
        user_who_is_receiving_admin_rights: 'Usuário que está recebendo direitos de administrador'
        user_who_is_receiving_folder_moderator_rights: 'Usuário que está recebendo direitos de moderador de pasta'
        user_who_is_receiving_project_moderator_rights: 'Usuário que está recebendo direitos de moderador de projeto'
        user_who_published_the_input: 'Usuário que publicou a ideia'
        user_who_published_the_proposal: 'Usuário que publicou a proposta'
        user_who_registers: 'Usuário que se cadastra'
        user_who_submitted_the_input: 'Usuário que enviou a entrada'
        user_who_voted: 'Usuário que votou'
        user_who_was_invited: 'Usuário que foi convidado'
        user_with_unsubmitted_survey: 'Usuário que iniciou, mas não enviou a pesquisa'
        user_with_unsubmitted_votes: 'Usuário que não enviou seus votos'
        users_who_engaged_but_not_voted: 'Usuários que se engajaram com o projeto, mas não votaram'
        users_who_engaged_with_the_project: 'Usuários engajados com o projeto'
        users_who_follow_the_input: 'Usuários que seguem a entrada'
        users_who_follow_the_project: 'Usuários que seguem o projeto'
        users_who_follow_the_proposal: 'Usuários que seguem a proposta'
      content_type:
        comments: 'Comentários'
        content_moderation: 'Moderação de conteúdo'
        events: 'Eventos'
        general: 'Geral'
        inputs: 'Entradas'
        internal_comments: 'Observações internas'
        permissions: 'Permissões'
        projects: 'Projetos'
        proposals: 'Propostas'
        reactions: 'Reações'
        voting: 'Votação'
        surveys: 'Pesquisas'
      trigger:
        7_days_after_invite_is_sent: '7 dias após o envio do convite'
        7_days_before_the_project_changes_phase: '7 dias antes da fase de mudanças do projeto'
        comment_is_deleted: 'Comentário excluído'
        comment_is_flagged_as_spam: 'O comentário foi sinalizado como spam'
        content_gets_flagged_as_innapropiate: 'O conteúdo é sinalizado como impróprio'
        initiative_resubmitted_for_review: 'Proposta reapresentada para revisão'
        input_is_assigned: 'A entrada é atribuída'
        input_is_flagged_as_spam: 'A entrada é sinalizada como spam'
        input_is_published: 'A entrada foi publicada'
        input_is_updated: 'A entrada é atualizada'
        input_status_changes: 'Alterações do status da entrada'
        internal_comment_is_posted_on_idea_assigned_to_user: 'O comentário interno é lançado na entrada atribuída ao utilizador'''
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'O comentário interno é publicado na entrada do projeto ou pasta que o utilizador gere'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'O comentário interno é lançado na entrada que o utilizador comentou internamente'''
        internal_comment_is_posted_on_idea_user_moderates: 'O comentário interno é publicado na entrada que o usuário modera'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Comentário interno é lançado em entrada não atribuída em projeto não gerido'
        project_review_request: 'O moderador solicitou uma revisão do projeto'
        project_review_state_change: 'O revisor aprovou o projeto'
        new_input_awaits_screening: 'Novas informações aguardam a triagem'
        new_input_is_published: 'Nova entrada é publicada'
        new_proposal_is_posted: 'Nova proposta foi postada'
        project_phase_changes: 'Alterações na fase do projeto'
        project_published: 'Projeto publicado'
        proposal_gets_reported_as_spam: 'A proposta foi denunciada como spam'
        proposal_is_assigned_to_admin: 'A proposta está atribuída ao administrador'
        proposal_is_published: 'A proposta foi publicada'
        proposal_is_updated: 'A proposta está atualizada'
        proposal_is_upvoted_above_threshold: 'A proposta foi votada positivamente acima do limiar'
        proposal_status_changes: 'Alterações no status da proposta'
        registration_to_event: 'Inscrição em um evento'
        survey_1_day_after_draft_saved: '1 dia após o usuário ter salvo o questionário pela última vez em rascunho'
        user_accepts_invitation_to_cosponsor_a_proposal: 'O utilizador aceita o convite para co-patrocinar uma proposta'
        user_comments: 'Comentários do usuário'
        user_comments_on_input: 'Comentários dos usuários na entrada'
        user_comments_on_proposal: 'Comentários do usuário sobre a proposta'
        user_is_given_admin_rights: 'Usuário recebendo direitos de administrador'
        user_is_given_folder_moderator_rights: 'Usuário recebendo direitos de moderador de pasta'
        user_is_given_project_moderator_rights: 'Usuário recebendo direitos de moderador de projeto'
        user_is_invited_to_cosponsor_a_proposal: 'O usuário é convidado a co-patrocinar uma proposta'
        user_is_mentioned: 'Usuário mencionado'
        user_is_mentioned_in_internal_comment: 'O usuário é mencionado num comentário interno'
        user_registers_for_the_first_time: 'Usuário se cadastra pela primeira vez'
        user_replies_to_comment: 'Usuário responde ao comentário'
        user_replies_to_internal_comment: 'Respostas do usuário ao comentário interno'
        voting_1_day_after_last_votes: '1 dia após o último voto do usuário'
        voting_2_days_before_phase_closes: '2 dias antes do encerramento da fase de votação'
        voting_basket_submitted: 'Os votos são enviados'
        voting_phase_ended: 'Fim da fase de votação'
