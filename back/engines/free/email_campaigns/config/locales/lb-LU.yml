lb:
  email_campaigns:
    campaign_type_description:
      "manual": Offiziell Norii<PERSON>
      "manual_project_participants": Offiziell Messagen un de Projet Participanten
      "admin_rights_received": <PERSON><PERSON><PERSON>
      "comment_deleted_by_admin": <PERSON><PERSON><PERSON>
      "comment_marked_as_spam": <PERSON>mmentéiert de Bericht vum Spam
      "comment_on_your_comment": Eng Äntwert op mäi Kommentar
      "comment_on_idea_you_follow": <PERSON> zu enger Iddi déi Dir verfollegt
      "community_monitor_report": Communautéit Monitor Rapport
      "cosponsor_of_your_idea": <PERSON> Benotzer akzeptéiert meng Invitatioun fir meng Propositioun ze sponsoren
      "event_registration_confirmation": Event Aschreiwung Bestätegung
      "idea_marked_as_spam": Spambericht iwwer d'Iddien
      "idea_published": Verëffentlechung vu menger Iddi
      "invitation_to_cosponsor_idea": Invitatioun fir eng Propositioun ze sponseren
      "invite_received": Invitatioun
      "invite_reminder": Invitatiounserënnerung
      "internal_comment_on_idea_assigned_to_you": <PERSON>ne Kommentar zum Input, deen mir zougewisen ass
      "internal_comment_on_idea_you_commented_internally_on": Interne Commentaire op Input Ech kommentéiert intern op
      "internal_comment_on_idea_you_moderate": Intern Kommentar op Input am Projet oder Dossier ech verwalten
      "internal_comment_on_unassigned_unmoderated_idea": Intern Kommentar iwwer net zougewisen Input am net verwalteten Projet
      "internal_comment_on_your_internal_comment": Interne Kommentar zu mengem internen Kommentar
      "mention_in_official_feedback": Ernimmung an engem Update
      "mention_in_internal_comment": Ernimmen an engem internen Commentaire
      "new_comment_for_admin": Neie Kommentar an engem Projet, deen ech moderéieren
      "new_idea_for_admin": Nei Iddi an engem Projet, deen ech moderéien
      "official_feedback_on_idea_you_follow": Update op eng Iddi déi Dir verfollegt
      "password_reset": Passwuert zerécksetzen
      "project_moderation_rights_received": Moderatiounsrechter fir Projete kritt
      "project_folder_moderation_rights_received": Administratorrechter fir Dossier'en kritt
      "project_phase_started": Nei Projetsphas
      "project_phase_upcoming": Bevirstoend nei Projetsphas
      "project_published": Projet publizéiert
      "project_review_request": Projet review Ufro
      "project_review_state_change": Projet guttgeheescht
      "status_change_on_idea_you_follow": Status Ännerung vun enger Iddi déi Dir verfollegt
      "survey_submitted": Ëmfro presentéiert
      "threshold_reached_for_admin": Virschlag huet d'Schwell vun de Stëmmen erreecht. 
      "welcome": No der Aschreiwung
      "admin_digest": Wéchentlechen Iwwerbléck fir Verwalteren
      "moderator_digest": Wéchentlech Iwwersiicht fir Projetsmanageren
      "assignee_digest": Wéchentlech Iwwersiicht iwwer zougewisen Iddien
      "user_digest": Wéchentlech Iwwersiicht
      "voting_basket_submitted": Bestätegung vum Vote
      "native_survey_not_submitted": Ëmfro net presentéiert
      "voting_basket_not_submitted": Stëmmen net agereecht
      "voting_last_chance": Déi lescht Chance fir ze wielen
      "voting_phase_started": Nei Projetsphase mat Ofstëmmung
      "voting_results": Wahlresultater
      "your_input_in_screening": Meng Input waart op Screening
    general:
      by_author: 'vum %{authorName}'
      author_wrote: '%{authorName} geschriwwen:'
      cta_goto_idea: 'Gitt op dës Iddi'
      cta_goto_input: 'Gitt op dës Input'
      cta_goto:
        idea: 'Gitt op dës Iddi'
        question: 'Gitt op dës Fro'
        contribution: 'Gitt op dëse Bäitrag'
        project: 'Gitt op dëse Projet'
        issue: 'Gitt op dëst Thema'
        option: 'Gitt op dës Optioun'
        proposal: 'Gitt op dës Propositioun'
        petition: 'Gitt op dës Petitioun'
      cta_goto_your:
        idea: 'Gitt op Är Iddi'
        question: 'Gitt op Är Fro'
        contribution: 'Gitt op Äre Bäitrag'
        project: 'Gitt op Äre Projet'
        issue: 'Gitt op Äert Thema'
        option: 'Gitt op Är Optioun'
        proposal: 'Gitt op Är Propositioun'
        petition: 'Gitt op Är Petitioun'
      cta_goto_proposal: 'Gitt op dës Propositioun'
      cta_goto_project: 'Gitt op dëse Projet'
    schedules:
      weekly:
        "0": "Wochemaart, Sonndes um %{hourOfDay}"
        "1": "Wochemaart, op Méindes %{hourOfDay}"
        "2": "Wochemaart, op Dënschdes %{hourOfDay}"
        "3": "Wochemaart, Mëttwochs %{hourOfDay}"
        "4": "Wochemaart, op Donneschden %{hourOfDay}"
        "5": "Wochemaart, Freideg %{hourOfDay}"
        "6": "Wochemaart, Samschdes %{hourOfDay}"
      quarterly: "Trimester, um éischten Dag vum Véierel"
    preview_data:
      first_name: 'Jane'
      last_name: 'Hinde'
      display_name: 'Jane Doe'
      comment_body: 'Dëst ass e Beispillkommentar, deen benotzt gëtt fir den Inhalt vun E-Maile virzekucken. Dëst ass keen echten Inhalt.'
      idea_title: 'Beispill Iddi'
    footer:
      "link_privacy_policy": "Dateschutzrichtlinn"
      "link_terms_conditions": "Geschäftsbedéngungen"
      "link_unsubscribe": "Ofmellen"
      "powered_by": "Ënnerstëtzt vun"
      "recipient_statement": "Dës E-Mail gouf Iech vum Go Vocal am Numm vun %{organizationName}geschéckt, well Dir e registréierte Benotzer vun %{organizationLink}sidd."
      "unsubscribe_statement": "Dir kënnt %{unsubscribeLink} wann Dir dës E-Mailen an Zukunft net wëllt kréien."
      "unsubscribe_text": "ofmellen"
    follow:
      "unfollow_here": "Dir hutt dës Notifikatioun kritt wéinst engem Artikel deen Dir verfollegt. <a href=\"%{unfollow_url}\">Dir kënnt et hei ofschléissen.</a>"
    manual:
      preheader: 'Dir hutt Mail vun %{organizationName}'
    comment_deleted_by_admin:
      reason: 'De Grond, firwat Äre Kommentar geläscht gouf:'
      cta_view: 'Dës Iddi ukucken'
      event_description: '%{organizationName} huet de Kommentar geläscht, deen Dir zu dëser Iddi geschriwwen hutt.'
      main_header: '%{organizationName} huet Äre Kommentar geläscht'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Äre Kommentar gouf als net ugemiess ageschat'
      irrelevant_reason: 'Äre Kommentar gouf als net relevant fir dëse Kontext betruecht'
      no_reason: 'Et gouf kee Grond uginn'
      subject: 'Äre Kommentar gouf vun der Plattform vun %{organizationName} geläscht'
      preheader: 'Äre Kommentar gouf geläscht'
    admin_digest:
      subject: 'Äre wëchentleche Bericht als Administrateur vun %{time}'
      preheader: 'Bericht vun der Woch vun %{organizationName}'
      title_your_weekly_report: '%{firstName}, Äre wéchentleche Bericht'
      text_introduction: 'Hei eng Auswiel vun de Bäiträg mat der meeschter Aktivitéit aus der leschter Woch. Fannt eraus wat op Ärer Plattform geschitt! !'
      cta_visit_the_platform: 'Besicht Är Plattform'
      new_users: 'Nei Benotzer'
      new_inputs: 'Nei Input'
      new_comments: 'Nei Kommentaren'
      title_activity_past_week: 'Aktivitéit vun der leschter Woch'
      title_no_activity_past_week: 'An der leschter Woch gouf et keng Aktivitéit'
      reached_threshold: 'D''Schwell erreecht'
      yesterday_by_author: 'Gëschter vun %{author}'
      today_by_author: 'Haut vun %{author}'
      x_days_ago_by_author: 'Virun %{x} Deeg vum %{author}'
    admin_rights_received:
      cta_manage_platform: 'Verwalten Är Plattform'
      message_you_became_administrator: 'Dir krut Administratorrechter fir d''participativ Plattform vum %{organizationName}.'
      preheader: 'Dir krut Administratorrechter fir d''participativ Plattform vum %{organizationName}'
      subject: 'Dir sidd en Administrateur vun der Bedeelegungsplattform vun %{organizationName} ginn'
      text_create_participatory_process: 'Als Administrateur kënnt Dir nei participativ Projeten uleeën an upassen. Dir kënnt nei Phasen an der Zäitlinn dobäimaachen. Jiddereng vun dëse Phasen ka bezüglech dem Verëffentleche vun Iddien, Kommentéieren an Ofstëmmen hiert eegent Verhalen hunn.'
      text_moderate_analyse_input: "Esoubal d’Projeten ulafen, kommen déi éischt Iddien eran. Dir kritt wéchentlech Berichter mat den Haaptaktivitéiten, sou datt Dir den Iwwersiicht behaalt, wat sech deet. \nDen Iddien-Manager am Backoffice vun Ärer Plattform erméiglecht et Iech ze gesi wat fir Iddien am meeschte positiv wéi negativ Stëmme kritt huet."
      text_platform_setup: 'Als Administrateur kënnt Dir d''participativ Plattform upassen. Wielt e Logo, Biller, Faarwen, schreift e personaliséierte Message op der Startsäit, verschéckt Invitatiounen a leet fest wat genee dir vun Äre Benotzer wësse wëllt...'
      title_create_participatory_process: 'Entwerft de participative Prozess'
      title_moderate_analyse_input: 'Moderéiert an analyséiert d’Bäiträg'
      title_platform_setup: 'Riicht Är Plattform an'
      title_what_can_you_do_administrator: 'Wat kënnt Dir als Administrateur maachen?'
      title_you_became_administrator: 'Dir sidd Administrateur ginn'
    comment_marked_as_spam:
      by_author: 'vun %{authorName}'
      commented: '%{authorName} huet kommentéiert:'
      cta_review_comment: 'Kommentar iwwerpréiwen'
      days_ago: 'Virun %{numberOfDays} Deeg'
      event_description: 'De folgende Kommentar, deen op <strong>''%{post}''</strong> gepost gouf, gouf gemellt:'
      inappropriate_content: 'De Kommentar ass net ugemiess oder beleedegend.'
      preheader: 'Reagéiert op dëse Kommentar, deen als Spam gemellt gouf'
      reported_this_because: '%{reporterFirstName} huet dëst gemellt, well:'
      subject: '%{organizationName}: %{firstName} %{lastName} huet dëse Kommentar als Spam gemellt'
      title_comment_spam_report: '%{firstName} %{lastName} huet dëse Kommentar als Spam gemellt'
      today: Haut
      wrong_content: 'De Kommentar ass net relevant.'
      yesterday: Gëschter
    community_monitor_report:
      subject: 'En neie Communautéitsmonitor Bericht ass verfügbar'
      title: 'En neie Communautéitsmonitor Bericht ass verfügbar'
      text_introduction: 'E Communautéitsmonitor Bericht gouf fir de leschte Quartal erstallt. Dir kënnt et zougräifen andeems Dir op de Knäppchen hei drënner klickt an aloggen.'
      cta_report_button: 'Kuckt de Bericht'
      report_name: 'Communautéit Monitor Rapport'
    cosponsor_of_your_idea:
      cta_reply_to: 'Kuckt Är Propositioun'
      event_description: 'Gratulatioun! %{cosponsorName} huet Är Invitatioun ugeholl fir Är Propositioun ze co-sponsoren.'
      main_header: '%{cosponsorName} huet Är Invitatioun ugeholl fir Är Propositioun ze co-sponsoren'
      subject: '%{cosponsorName} huet Är Invitatioun ugeholl fir Är Propositioun ze sponsoren'
      preheader: '%{cosponsorName} huet Är Invitatioun ugeholl fir Är Propositioun ze co-sponsoren'
    assignee_digest:
      subject: 'Iddien, déi Äre Feedback erfuerderen: %{numberIdeas}'
      preheader: 'Iwwersiicht vun den Elementer déi Iech zougewise goufen %{organizationName}'
      title_your_weekly_report: '%{firstName}, de Bäitrag vun de Benotzer waart op Äre Feedback'
      cta_manage_your_input: 'Är Bäiträg verwalten'
      x_inputs_need_your_feedback: 'Input brauch Äre Feedback'
      title_assignment_past_week: 'Neist Iddien, déi Iech zougewise goufen'
      title_no_assignment_past_week: 'D''lescht Woch goufen Iech keng nei Iddien zougewisen'
      yesterday_by_author: 'Gëschter vun %{author}'
      today_by_author: 'Haut vun %{author}'
      x_days_ago_by_author: 'Virun %{x} Deeg %{author} vun'
      title_successful_past_week: 'Iech zougewisen, déi d''Schwell erreecht hunn'
    idea_marked_as_spam:
      cta_review: 'Iwwerpréiwung'
      report_inappropriate_offensive_content: 'Ech fannen dësen Inhalt net ugemiess oder beleidegend.'
      report_not_an_idea: 'Dësen Inhalt ass keng Iddi a gehéiert net hei hin.'
      subject: 'Dir hutt e Spambericht op der Plattform vum %{organizationName}'
      preheader: 'Reagéiert op dës Spammeldung'
      reported_this_because: '%{reporterFirstName} huet dëst gemellt, well:'
      title_spam_report: '%{firstName} %{lastName} huet Spam gemellt'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Co-Sponsor dës Propositioun'
      event_description: '%{authorName} huet eng nei Propositioun erstallt a géif gären datt Dir se matsponséiert.'
      event_description_cosponsoring: 'Co-Sponsoréiere vun enger Propositioun bedeit datt <strong>Ären Numm</strong> mat den Nimm vun anere Co-Sponsoren vun der Propositioun ugewise gëtt.'
      event_description_before_action: 'Fir d''Propositioun ze gesinn an d''Invitatioun unzehuelen, musst Dir op Äre Kont ageloggt sinn.'
      event_description_action: 'Klickt hei ënnen fir d''Propositioun ze liesen.'
      main_header: 'Dir sidd invitéiert fir eng Propositioun ze sponseren'
      subject: 'Dir sidd invitéiert fir eng Propositioun ze sponseren'
      preheader: 'Dir sidd invitéiert fir d''Propositioun vum %{authorName}ze sponseren'
    invite_reminder:
      cta_accept_invitation: 'Invitatioun unhuelen'
      invitation_header: 'Är Invitatioun steet aus'
      preheader: '%{organizationName} huet Iech virun e puer Deeg eng Invitatioun geschéckt, fir hirer participativer Plattform bäizetrieden.'
      invitation_expiry_message: 'Dës Invitatioun leeft no ongeféier %{expiryDaysRemaining} Deeg of.'
      subject: 'Ausstoend Invitatioun fir d’participativ Plattform %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} huet déi folgend Noriicht geschriwwen:'
      cta_accept_invitation: 'Huelt Är Invitatioun un'
      invitation_header: 'Dir sidd invitéiert!'
      invitation_header_message: '%{organizationName} huet Iech op hir participativ Plattform invitéiert.'
      invitation_expiry_message: 'Dës Invitatioun leeft an %{expiryDays} Deeg aus.'
      preheader: '%{organizationName} huet Iech eng Invitatioun geschéckt, fir hirer participativer Plattform bäizetrieden.'
      subject: 'Dir sidd invitéiert, fir der Plattform vun %{organizationName} bäizetrieden'
    mention_in_comment:
      cta_reply_to: 'Äntwert %{commentAuthor}'
      event_description: '%{commentAuthorFull} huet Iech a sengem Kommentar zu der Iddi ''%{post}‘ ernimmt. Klickt op de Link hei ënnendrënner, fir mam %{commentAuthor} an d''Gespréich ze kommen'
      main_header: 'Leit schwätzen iwwer Iech'
      subject: 'Et huet een Iech op der Plattform vun %{organizationName} ernimmt'
      preheader: '%{commentAuthor} huet Iech an engem Kommentar ernimmt'
    mention_in_internal_comment:
      cta_reply_to: 'Kuckt de Kommentar vum %{firstName}'
      event_description: '%{authorNameFull} huet Iech an engem internen Kommentar ernimmt.'
      subject: '%{firstName} huet Iech an engem internen Kommentar ernimmt.'
      main_header: '%{firstName} huet Iech an engem internen Kommentar ernimmt.'
      preheader: '%{authorNameFull} huet Iech an engem internen Kommentar ernimmt.'
    moderator_digest:
      subject: 'Wéchentlechen Bericht fir Projetsmanageren vum %{time}'
      preheader: 'Zesummefaassung fir Projetsmanagere vun %{organizationName}'
      title_your_weekly_report: '%{firstName}, Äre wéchentleche Rapport'
      text_introduction: 'Hei eng Auswiel vun de Beiträg mat der meeschter Aktivitéit aus der leschter Woch. Fannt eraus wat op Ärer Plattform geschitt!'
      cta_manage: 'Verwalte Äre Projet'
      new_users: 'Nei Notzeren'
      new_ideas: 'Nei Iddien'
      new_comments: 'Nei Kommentaren'
      title_inputs_past_week: 'Nei Input an der vergaanger Woch'
      title_no_inputs_past_week: 'Keng nei Input an der leschter Woch'
      title_threshold_reached: Schwell erreecht an der leschter Woch
      yesterday_by_author: 'Gëschter vun %{author}'
      today_by_author: 'Haut vun %{author}'
      x_days_ago_by_author: 'Virun %{x} Deeg %{author} vun'
    new_comment_for_admin:
      commented: '%{authorFirstName} huet kommentéiert:'
      cta_reply_to: 'Dem %{commentAuthor} säi Kommentar ukucken'
      days_ago: 'Virun %{numberOfDays} Deeg'
      event_description: '%{authorName} huet en neie Kommentar op Är Plattform hannerlooss.'
      main_header: '%{firstName}, en neie Kommentar gouf op Ärer Plattform gepost'
      subject: 'En neie Kommentar gouf op der %{organizationName} Plattform gepost'
      preheader: '%{authorName} huet e Kommentar hannerlooss'
      today: Haut
      yesterday: Gëschter
    comment_on_idea_you_follow:
      cta_reply_to: 'Äntwert op %{commentAuthor}'
      event_description: '%{authorNameFull} huet eng Reaktioun op ''%{inputTitle}'' gesat. Klickt op de Knäppchen hei ënnen fir d''Gespréich mat %{authorName}weiderzemaachen.'
      main_header:
        idea: '%{authorName} kommentéiert eng Iddi déi Dir verfollegt'
        question: '%{authorName} kommentéiert eng Fro déi Dir verfollegt'
        contribution: '%{authorName} kommentéiert e Bäitrag deen Dir verfollegt'
        project: '%{authorName} kommentéiert e Projet deen Dir verfollegt'
        issue: '%{authorName} kommentéiert en Thema deen Dir verfollegt'
        option: '%{authorName} kommentéiert eng Optioun déi Dir verfollegt'
        proposal: '%{authorName} kommentéiert eng Propositioun déi Dir verfollegt'
        petition: '%{authorName} kommentéiert eng Petitioun déi Dir verfollegt'
      subject: 'Et gëtt en neie Kommentar zu "%{input_title}"'
      preheader: '%{authorName} huet e Kommentar op eng Iddi fir %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'En neien Input gouf op Ärer Plattform publizéiert'
      event_description_publication: '%{authorName} huet en neien Input op Ärer Plattform ofginn. Entdeckt et elo, gitt e Feedback oder ännert säi Status!'
      cta_publication: 'Gitt Feedback un %{authorName}'
      main_header_prescreening: 'En Input erfuerdert Är Iwwerpréiwung'
      event_description_prescreening: '%{authorName} huet en neien Input op Ärer Plattform ofginn.'
      input_not_visible_prescreening: '<b>Den Input wäert net</b> sichtbar sinn bis Dir säi Status ännert.'
      cta_prescreening: 'Iwwerpréift den Input'
      days_ago: 'Virun %{numberOfDays} Deeg'
      preheader: '%{authorName} huet eng nei Iddi op Ärer Plattform gepost'
      today: Haut
      yesterday: Gëschter
    comment_on_your_comment:
      cta_reply_to: 'Äntwert %{firstName}'
      event_description: '%{authorNameFull} huet eng Äntwert zu Ärem Kommentar zu ''%{post}'' op der participativer Plattform geschriwwen. Klickt hei ënnendrënner fir d’Gespréich mam %{authorName} weiderzeféieren.'
      subject: 'Dir hutt eng Äntwert op Äre Kommentar op der Plattform %{organizationName} kritt'
      main_header: '%{authorName} huet op Äre Kommentar geäntwert'
      preheader: '%{authorName} huet op de Kommentar op der Plattform %{organizationName} geäntwert'
      replied: '%{authorFirstName} huet geäntwert:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Kuckt de Kommentar vum %{firstName}'
      event_description: '%{authorNameFull} kommentéiert Ären internen Kommentar.'
      subject: 'Dir hutt e Kommentar zu Ärem internen Kommentar op ''%{post}'' kritt'
      main_header: 'Dir hutt e Kommentar zu Ärem internen Kommentar op ''%{post}'' kritt'
      preheader: '%{authorName} huet op Ären interne Kommentar op ''%{post}'' geäntwert'
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Kuckt de Kommentar vum %{firstName}'
      event_description: '%{authorNameFull} kommentéiert intern op en Input deen Iech zougewisen gouf.'
      subject: '''%{post}'' huet en neien internen Kommentar'
      main_header: '''%{post}'' huet en neien internen Kommentar'
      preheader: '''%{post}'' huet en neien internen Kommentar'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Kuckt de Kommentar vum %{firstName}'
      event_description: '%{authorNameFull} kommentéiert intern op en Input deen Dir intern kommentéiert hutt.'
      subject: '''%{post}'' huet en neien internen Kommentar'
      main_header: '''%{post}'' huet en neien internen Kommentar'
      preheader: '''%{post}'' huet en neien internen Kommentar'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Kuckt de Kommentar vum %{firstName}'
      event_description: '%{authorNameFull} kommentéiert intern op en Input an engem Projet oder Dossier deen Dir geréiert.'
      subject: '''%{post}'' huet en neien internen Kommentar'
      main_header: '''%{post}'' huet en neien internen Kommentar'
      preheader: '''%{post}'' huet en neien internen Kommentar'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Kuckt de Kommentar vum %{firstName}'
      event_description: '%{authorNameFull} kommentéiert intern op en net zougewisenen Input an engem net verwalteten Projet.'
      subject: '''%{post}'' huet en neien internen Kommentar'
      main_header: '''%{post}'' huet en neien internen Kommentar'
      preheader: '''%{post}'' huet en neien internen Kommentar'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} huet en Update iwwer ''%{input_title}'' ginn.'
      header_title: 'Et gëtt en Update iwwer "%{input_title}"'
      subject: 'Offiziell Feedback gouf op "%{input_title}" gepost'
      preheader: 'Et gëtt en Update op engem Input deen Dir verfollegt'
    mention_in_official_feedback:
      cta_reply_to: 'Äntwert %{organizationName}'
      event_description: '%{organizationName} hunn Iech an hirem Feedback zu der Iddi ''%{post}‘ ernimmt. Klickt op de Link hei ënnendrënner, fir Iech mat %{organizationName} z’ënnerhalen'
      main_header: 'Dir sidd ernimmt ginn'
      subject: '%{organizationName} hunn Iech an hirem Feedback ernimmmt'
      preheader: '%{commentAuthor} huet Iech am Feedback ernimmt'
    project_moderation_rights_received:
      cta_manage_project: 'Verwalten dëse Projet'
      message_you_became_moderator: 'En Administrateur vun der participativer Plattform %{organizationName} huet Iech grad zum Projetsmanager vum folgende Projet gemaach:'
      no_ideas: 'Nach keng Iddien'
      preheader: 'En Administrateur vun der participativer Plattform %{organizationName} huet Iech grad zum Projetsmanager vum folgende Projet gemaach'
      subject: 'Dir sidd en Administrateur vun der Bedeelegungsplattform vun %{organizationName} ginn'
      text_design_participatory_process: 'Als Projetsmanager kënnt Dir festleeë wéi d''Participante mat Äre Projeten interagéieren. Dir kënnt mat Hëllef vun der Zäitlinn nei Phase bäifügen. All eenzel Phas kann ënnerschiddlech Astellungunge fir d''Ofstëmmen, d''Kommentéieren an d''Verëffentleche vun Iddien hunn.'
      text_moderate_analyse_input: "Esoubal d’Projeten ulafen, kommen déi éischt Iddien eran. Dir kritt wéchentlech Berichter mat den Haaptaktivitéiten, sou datt Dir den Iwwersiicht behaalt, wat sech deet. \nDen Iddien-Manager am Backoffice vun Ärer Plattform erméiglecht et Iech ze gesi wat fir Iddien am meeschte positiv wéi negativ Stëmme kritt huet."
      text_share_project_information: 'Fir d''Qualitéit vun den Iddien, déi Dir kritt, ze erhéijen ass et wichteg genuch Informatioun ze deelen: Füügt eng Beschreiwung vum Projet dobäi, hänkt Biller un (beispillsweis Skizzen a Pläng) a kommunizéiert iwwer mat dem Projet verbonnen Evenementer. Denkt drun: Gutt Informatiounen sinn d’Viraussetzung fir eng konstruktiv Bedeelegung!'
      title_design_participatory_process: 'Konzipéiert de Bedeelegungsprozess'
      title_moderate_analyse_input: 'Moderéiert an analyséiert d’Bäiträg'
      title_share_project_information: 'Gitt Informatiounen iwwer de Projet'
      title_what_can_you_do_moderator: 'Wat kënnt Dir als Projetsmanager maachen?'
      title_you_became_moderator: 'Dir sidd ab elo Projetsmanager'
      x_ideas: '%{numberOfIdeas} Iddien'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Dësen Dossier ukucken'
      message_added_as_folderadmin: 'Dir hutt Administratorrechter op der participativer Plattform vum %{organizationName} fir folgenden Dossier kritt:'
      no_projects: 'Nach keng Projeten'
      preheader: 'En Administrateur vun der participativer Plattform %{organizationName} huet Iech zum Administrateur vu folgendem Dossier gemaach'
      subject: 'Dir sidd en Administrateur vun engem Dossier op der participativer Plattform %{organizationName} ginn'
      text_manage_folder: 'En Dossier bitt d''Méiglechkeet, e puer Bedeelegungprojeten zesummen z’organiséieren. Als Administrateur vun engem Dossier kënnt Dir d''Beschreiwung vum Dossier änneren an nei Projeten uleeën (fir Projeten ze läschen, kontaktéiert den Administrateur vun Ärer Plattform). Dir kritt och d''Rechter fir all Projete vum Dossier ze verwalten. Dës Rechter erlaben et Iech d''Projeten ze beaarbechten, d’Bäiträg ze verwalten a Participanten eng E-Mail ze schécken.'
      text_moderate_analyse_input: 'Esoubal d’Projeten ulafen, kommen déi éischt Bäiträg eran. Dir kritt wéchentlech Berichter mat den Haaptaktivitéiten, sou datt Dir eng Iwwersiicht behaalt, wat sech deet. De Bäitrag-Manager am Backoffice vun Ärer Plattform erméiglecht et Iech, d’Bäiträg ze gesinn an ze verwalten, inklusiv Statussen zou ze weisen an op Verëffentlechungen a Kommentaren ze äntweren.'
      text_design_participatory_process: 'Dir kënnt déi verschidde Participatiounsprojeten an Ärem Dossier verwalten – d''Method fir d''Participatioun adaptéieren, eng Beschreiwung vum Projet afügen, Biller unhänken, an iwwer Evenementer, déi domat verbonne sinn, informéieren. Dir kënnt och festleeën, wéi d''Participante mat Äre Projeten interagéiere kënnen. Dir kënnt beispillsweis Zougangsrechter festleeën an d''Verëffentlechungs- Ofstëmmungs- a Kommentarastellungen definéieren.'
      title_design_participatory_process: 'Konzipéiert de Bedeelegungsprozess'
      title_moderate_analyse_input: 'Moderéiert an analyséiert d’Bäiträg'
      title_manage_folder: 'Leet d''Astellunge vum Dossier fest an erstellt nei Projeten.'
      title_what_can_you_do_folderadmin: 'Wat kënnt Dir als Administrateur vun Dossiere maachen?'
      title_added_as_folderadmin: 'Dir sidd als Administrateur vun engem Dossier bäigefüügt ginn'
      x_projects: '%{numberOfProjects} Projeten'
    project_phase_started:
      cta_view_phase: 'Entdeckt dës nei Phas'
      event_description: 'Dëse Projet ass op der %{organizationName} Plattform an eng nei Phas gaangen. Klickt op de Link hei ënnen, fir méi gewuer zu ginn!'
      main_header: 'Eng nei Phas ass fir de Projet ''%{projectName}'' ugaangen'
      subtitle: 'Iwwer ''%{projectName}'''
      new_phase: 'Dëse Projet ass an d’Phas ''%{phaseTitle}'' gaangen'
      subject: '%{projectName} ass an eng nei Phas gangen'
      preheader: 'Eng nei Phas gouf fir %{projectName} ugefaangen'
    project_phase_upcoming:
      cta_view_phase: 'Preparéiert dës nei Phas'
      event_description: 'De Projet ''%{projectName}'' geet geschwënn an eng nei Phas. Vergewëssert Iech, datt alles fir dës nei Phas preparéiert ass: Gëtt et eng ugemiess Beschreiwung? Ginn zeréckbehalen Iddien an dës Phas iwwerholl? Wëllt Dir d''Participanten iwwer d’Detailer vun dëser Phas mat enger moossgeschneiderter E-Mail-Campagne informéieren?'
      main_header: '%{firstName}, e Projet geet geschwënn an eng nei Phas'
      subtitle: 'Iwwer ''%{projectName}'''
      new_phase: 'De Projet geet an d’Phas ''%{phaseTitle}'''
      subject: 'Preparéiert alles fir déi nei Phas vu(n) %{projectName}'
      preheader: 'Geschwë fänkt eng nei Phas fir %{projectName} un'
    project_published:
      subject: 'En neie Projet gouf op der Plattform vum %{organizationName}publizéiert'
      header_title: 'En neie Projet gouf publizéiert'
      header_message: 'D''Participatiounsplattform vun %{organizationName} huet just de folgende Projet publizéiert:'
      preheader: 'En neie Projet gouf publizéiert'
    project_review_request:
      subject: 'Iwwerpréiwung Ufro: E Projet waart op Genehmegung.'
      header: '%{requesterName} huet Iech invitéiert de Projet ze iwwerpréiwen "%{projectTitle}"'
      header_message: "De Moment ass de Projet am Entworfmodus an ass net fir Benotzer ze gesinn. Wann Dir et iwwerpréift an guttgeheescht hutt, kann de Moderator et verëffentlechen."
      cta_review_project: "Iwwerpréift de Projet"
    project_review_state_change:
      subject: '"%{projectTitle}" gouf guttgeheescht'
      header: '%{reviewerName} de Projet guttgeheescht "%{projectTitle}"'
      header_message: "De Projet ass elo prett fir live ze goen. Dir kënnt et publizéieren wann Dir prett sidd!"
      cta_go_to_project: "Gitt op de Projet Astellungen"
    status_change_on_idea_you_follow:
      status_change: 'Den neie Status vun dësem Input ass ''%{status}'''
      header_message: '%{organizationName} huet de Status vum Input ''%{input_title}'' op hirer digitaler Participatiounsplattform aktualiséiert.'
      header_title: 'En Input deen Dir verfollegt huet en neie Status'
      subject: 'De Status vun "%{input_title}" ass geännert'
      preheader: 'En Input deen Dir verfollegt huet en neie Status'
    user_digest:
      subject: "Är wéchentlech Aktivitéit op der participativer Plattform %{organizationName}"
      commented: "%{authorFirstName} huet kommentéiert:"
      preheader: "Wéchentlechen Iwwerbléck vun %{organizationName}"
      title_your_weekly_report: "Fannt eraus wat d'lescht Woch geschitt ass"
      intro_text: "Hei ass eng Zesummefaassung vun deem, wat sech op der participativer Plattform %{organizationName} gedoen huet."
      cta_go_to_the_platform: "D'Plattform besichen"
      title_no_activity_past_week: "Keng Aktivitéit an der leschter Woch"
      successful_proposals_title: "D'Schwell erreecht"
      successful_proposals_text: "Dëse Virschlag huet genuch Ënnerstëtzung kritt fir an déi nächst Etapp ze kommen!! Klickt op de Virschlag fir méi gewuer ze ginn, wat als nächst geschitt."
      today_by_author: "Haut vun %{author}"
      yesterday_by_author: "Gëschter vun %{author}"
      x_days_ago_by_author: "Virun %{x} Deeg %{author} vun"
      trending_title: "Trending"
      trending_text: "Interesséiert fir wat op der Plattform geschitt ass? Hei sinn déi dräi meescht trendy Bäiträg a wat d'Leit iwwer si soen."
      no_notifications: "Keng Benoriichtegungen"
      one_notification: "1 Benoriichtegung"
      multiple_notifications: "%{notifCount} Benoriichtegungen"
      no_unread_notifications: "Dir hutt keng ongelies Benoriichtegungen. Besicht d’Plattform fir erauszefannen wat sech deet a fir bäizedroen!"
      unread_notifications: "Dir hutt Benoriichtegungen, déi Dir nach net gelies hutt. Besicht d’Plattform fir erauszefanne wat leeft!"
    threshold_reached_for_admin:
      cta_process_initiative: 'Bréngt dës Initiativ an déi nächst Etapp'
      main_header: 'E Virschlag huet d''Schwell vun den Ofstëmmungen erreecht!'
      subject: 'E Virschlag op Ärer Plattform huet d''Schwell vun den Ofstëmmungen erreecht'
      preheader: 'Stellt sécher, datt Dir déi nächst Schrëtter maacht'
    welcome:
      cta_join_platform: 'Entdeckt d’Plattform'
      subject: 'Wëllkomm op der Plattform vum %{organizationName}'
      main_header: Wëllkomm!
      message_welcome: 'Gléckwonsch, Dir hutt Iech erfollegräich op der participativer Plattform vum %{organizationName} ugemellt. Dir kënnt elo d’Plattform entdecken an Ärer Stëmm Gehéier verschafen. Dir kënnt och e Profilbild  an eng kuerz Beschreiwung afüge fir Iech deenen anere Benotzer virzestellen.'
      preheader: 'Dar hei kënnt Dir op der Plattform vun %{organizationName} maachen'
    idea_published:
      subject:
        idea: 'Är Iddi gouf publizéiert'
        question: 'Är Fro gouf publizéiert'
        contribution: 'Äre Bäitrag gouf publizéiert'
        project: 'Äre Projet gouf publizéiert'
        issue: 'Är Emissioun gouf publizéiert'
        option: 'Är Optioun gouf publizéiert'
        proposal: 'Är Propositioun gouf publizéiert'
        petition: 'Är Petitioun gouf publizéiert'
      main_header: 'Dir hutt eng Iddi gepost! Stellt sécher, datt se gelies gëtt.'
      header_message: 'Loosst eis sécher stellen datt et gelies gëtt.'
      message_get_votes: 'Erreecht méi Leit mat Ärer Iddi:'
      action_published_idea: 'Verëffentlecht Iddi'
      action_add_image: '%{addImageLink} fir d’Siichtbarkeet ze verbesseren'
      add_image: 'A Bild afügen'
      action_share_fb: 'Sot et Äre Frënn op %{fbLink}'
      action_share_twitter: 'Informéiert Är Follower iwwer %{twitterLink}'
      action_send_email: 'Är Kontakter un %{sendEmailLink} schécken'
      send_email: E-Mail
      action_share_link: 'Deelt et iwwer all belibege Kanal, andeem Dir %{link} kopéiert'
      link: Link
      preheader: '%{firstName}, Gléckwonsch fir d’Verëffentlechen vun Ärer Iddi op der %{organizationName} Plattform. Sammelt a kritt elo Ënnerstëtzung.'
    your_input_in_screening:
      main_header:
        idea: 'Är Iddi ass an "%{prescreening_status_title}"'
        question: 'Är Fro ass an "%{prescreening_status_title}"'
        contribution: 'Äre Bäitrag ass an "%{prescreening_status_title}"'
        project: 'Äre Projet ass an "%{prescreening_status_title}"'
        issue: 'Ären Thema ass an "%{prescreening_status_title}"'
        option: 'Är Optioun ass an "%{prescreening_status_title}"'
        proposal: 'Är Propositioun ass an "%{prescreening_status_title}"'
        petition: 'Är Petitioun ass an "%{prescreening_status_title}"'
      message: '"%{input_title}" gëtt fir anerer siichtbar wann et iwwerpréift an guttgeheescht gouf.'
      subject: '"%{input_title}" ass bal publizéiert'
      preheader: 'Et ass de Moment an %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}: Dir hutt erfollegräich gestëmmt'
      preheader: 'Dir hutt erfollegräich op der Participatiounsplattform vun %{organizationName}gestëmmt'
      title_basket_submitted: 'Dir hutt erfollegräich gestëmmt'
      event_description: 'Merci fir d''Matmaachen. Är Stëmmen goufen opgeholl. Besicht d''Plattform vun %{organizationName} fir Är Stëmmen ze gesinn an ze managen.'
      cta_see_votes_submitted: 'Gesinn Stëmmen presentéiert'
      cta_message: 'Klickt op de Knäppchen hei ënnen fir matzemaachen'
    native_survey_not_submitted:
      subject: '%{organizationName}: Bal do! Gitt Är Äntwerten of'
      preheader: 'Dir hutt Är Ëmfro-Äntwert net op der Participatiounsplattform vun %{organizationName}ofgeschloss'
      title_native_survey_not_submitted: 'Bal do! Gitt Är Äntwerten of'
      body_native_survey_not_submitted: 'Dir hutt ugefaang Är Äntwerten op %{phaseTitle} ze deelen awer hutt se net ofginn. Soumissioune ginn op %{phaseEndDate}zou. Klickt op de Knäppchen hei ënnen fir weiderzemaachen wou Dir opgehalen hutt.'
      body_native_survey_not_submitted_no_date: 'Dir hutt ugefaang Är Äntwerten op %{phaseTitle} ze deelen awer hutt se net ofginn. Klickt op de Knäppchen hei ënnen fir weiderzemaachen wou Dir opgehalen hutt.'
      cta_complete_your_survey_response: 'Fuert Är Ëmfro Äntwert op'
    voting_basket_not_submitted:
      subject: '%{organizationName}: Dir hutt Är Stëmmen net ofginn'
      preheader: 'Dir hutt Är Stëmmen net op der Participatiounsplattform vun %{organizationName}ofginn'
      title_basket_not_submitted: 'Dir hutt Är Stëmmen net ofginn'
      event_description: 'Dir hutt e puer Optiounen fir %{contextTitle} ausgewielt, awer Dir hutt Är Auswiel net ofginn.'
      cta_view_options_and_vote: 'View Optiounen a Vote'
      cta_message: 'Klickt op de Knäppchen hei ënnen fir Är gewielte Optiounen ofzeginn'
    voting_last_chance:
      subject: '%{organizationName}: Lescht Chance fir %{phaseTitle}ze wielen'
      preheader: 'Lescht Chance fir %{phaseTitle} op der Participatiounsplattform vun %{organizationName}ze wielen'
      title_last_chance: 'Déi lescht Chance fir %{phaseTitle}ze wielen'
      body_1: 'D''Stëmmphase fir den %{projectTitle} Projet geet muer um Mëtternuecht op en Enn.'
      body_2: 'D''Zäit leeft aus, a mir hu gemierkt datt Dir Är Stëmm nach net ofginn hutt! Akt elo andeems Dir einfach op de Knäppchen hei ënnen klickt fir matzemaachen.'
      body_3: 'Wann Dir dëst maacht, kritt Dir Zougang zu enger Rei vun Optiounen an hutt d''Chance Ären Input ze ginn, wat entscheedend ass fir d''Zukunft vun dësem Projet ze entscheeden.'
      cta_vote: 'Stëmmen'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} Stëmmen Resultater opgedeckt!'
      preheader: '%{phaseTitle} Stëmme Resultater opgedeckt op der Participatioun Plattform vun %{organizationName}'
      title_results: '%{phaseTitle} Stëmmen Resultater opgedeckt!'
      body_1: 'Resultater sinn an!'
      body_2: 'D''Resultater vum %{phaseTitle} Vote an der %{organizationName} Plattform sinn publizéiert ginn!'
      body_3: 'Mir encouragéieren Iech d''Resultater z''iwwerpréiwen a bleiwen ofgeschloss fir weider Updates iwwer déi nächst Schrëtt.'
      cta_see_results: 'Gesinn Resultater an der Plattform'
    event_registration_confirmation:
      subject: "Dir sidd an! Dir sidd ageschriwwen fir \"%{eventTitle}\""
      preheader: "%{firstName}, Merci fir Är Aschreiwung fir %{eventTitle}"
      header_message: "%{firstName}, merci fir Aschreiwung fir"
      event_details:
        labels:
          date: 'Datum'
          location: 'Plaz'
          online_link: 'Online Link'
          description: 'Beschreiwung'
          project: 'Projet'
      cta_go_to_event: 'Kuckt d''Evenement'
      cta_add_to_calendar: 'An Äre Kalenner androen'
    voting_phase_started:
      subject: '%{organizationName}: D''Stëmmphase huet fir den %{projectName}ugefaangen'
      preheader: 'D''Stëmmphase huet fir %{projectName} op der Participatiounsplattform vun %{organizationName}ugefaang'
      event_description: 'De Projet "%{projectName}" freet Iech tëscht %{numIdeas} Optiounen ze stëmmen:'
      cta_message: 'Klickt op de Knäppchen hei ënnen fir matzemaachen'
      cta_vote: 'Gitt op d''Plattform fir ze wielen'
    survey_submitted:
      subject: '%{organizationName}: Merci fir Är Äntwert! 🎉'
      preheader: 'Hei sinn d''Detailer vun Ärer Soumissioun.'
      main_header: 'Merci fir Är Gedanken iwwer "%{projectName}" ze deelen!'
      your_input_submitted: 'Ären Input fir "%{projectName}" gouf erfollegräich ofginn.'
      if_you_would_like_to_review: 'Wann Dir Är Soumissioun wëllt iwwerpréiwen, kënnt Dir Är Äntwerten hei ënnen eroflueden.'
      your_submission_has_id: 'Är Soumissioun huet de folgenden eenzegaartegen Identifizéierer:'
      you_can_use_this_id: 'Dir kënnt dësen Identifizéierer benotze fir d''Plattformadministrateuren ze kontaktéieren am Fall wou Dir wëllt datt Är Soumissioun ewechgeholl gëtt.'
      download_responses: 'Luet Är Äntwerten erof'
    admin_labels:
      recipient_role:
        admins: 'An den Admins'
        admins_and_managers: 'Fir Administrateuren & Manager'
        managers: 'Zu Manager'
        project_participants: 'Zu Projet Participanten'
        registered_users: 'Fir registréiert Benotzer'
      recipient_segment:
        admins: 'Administrateuren'
        admins_and_managers: 'Administrateuren a Manager'
        admins_and_managers_assigned_to_the_input: 'Administrateuren & Manager ginn dem Input zougewisen'
        admins_and_managers_managing_the_project: 'Administrateuren a Manager déi de Projet managen'
        admins_assigned_to_a_proposal: 'Administrateuren zu enger Propositioun zougewisen'
        all_users: 'All Benotzer'
        all_users_who_uploaded_proposals: 'All Benotzer déi Propositioune eropgelueden hunn'
        managers: 'Manager'
        managers_managing_the_project: 'Manager déi de Projet managen'
        new_attendee: 'Nei registréiert Benotzer'
        project_reviewers: 'Project Rezensiounen an Dossier Manager'
        project_review_requester: 'Benotzer deen de Projet review gefrot'
        user_who_commented: 'Benotzer deen kommentéiert'
        user_who_is_invited_to_cosponsor_a_proposal: 'Benotzer deen invitéiert ass eng Propositioun ze co-sponsoren'
        user_who_is_mentioned: 'Benotzer deen ernimmt gëtt'
        user_who_is_receiving_admin_rights: 'Benotzer deen Admin Rechter kritt'
        user_who_is_receiving_folder_moderator_rights: 'Benotzer deen Dossier Moderator Rechter kritt'
        user_who_is_receiving_project_moderator_rights: 'Benotzer deen Projet Moderator Rechter kritt'
        user_who_published_the_input: 'Benotzer deen den Input publizéiert huet'
        user_who_published_the_proposal: 'Benotzer deen d''Propositioun publizéiert huet'
        user_who_registers: 'Benotzer deen registréiert'
        user_who_submitted_the_input: 'Benotzer deen den Input ofginn huet'
        user_who_voted: 'Benotzer deen gestëmmt huet'
        user_who_was_invited: 'Benotzer deen invitéiert gouf'
        user_with_unsubmitted_survey: 'Benotzer deen ugefaang huet awer net hir Ëmfro ofginn huet'
        user_with_unsubmitted_votes: 'Benotzer déi hir Stëmmen net ofginn huet'
        users_who_engaged_but_not_voted: 'Benotzer déi mam Projet engagéiert hunn awer net gestëmmt hunn'
        users_who_engaged_with_the_project: 'Benotzer déi mam Projet engagéiert hunn'
        users_who_follow_the_input: 'Benotzer déi den Input verfollegen'
        users_who_follow_the_project: 'Benotzer déi de Projet verfollegen'
        users_who_follow_the_proposal: 'Benotzer déi d''Propositioun verfollegen'
      content_type:
        comments: 'Kommentaren'
        content_moderation: 'Inhalt Moderatioun'
        events: 'Evenementer'
        general: 'Général'
        inputs: 'Input'
        internal_comments: 'Intern Kommentaren'
        permissions: 'Permissiounen'
        projects: 'Projeten'
        proposals: 'Propositioune'
        reactions: 'Reaktiounen'
        voting: 'Ofstëmmung'
        surveys: 'Ëmfroen'
      trigger:
        7_days_after_invite_is_sent: '7 Deeg no der Invitatioun geschéckt'
        7_days_before_the_project_changes_phase: '7 Deeg virum Projet Ännerungen Phas'
        comment_is_deleted: 'Commentaire geläscht'
        comment_is_flagged_as_spam: 'Kommentar gëtt als Spam markéiert'
        content_gets_flagged_as_innapropiate: 'Inhalt gëtt als innapropiate markéiert'
        initiative_resubmitted_for_review: 'Propositioun nees fir Iwwerpréiwung presentéiert'
        input_is_assigned: 'Input ass zougewisen'
        input_is_flagged_as_spam: 'Input gëtt als Spam markéiert'
        input_is_published: 'Input gëtt publizéiert'
        input_is_updated: 'Input gëtt aktualiséiert'
        input_status_changes: 'Input Status Ännerungen'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Interne Kommentar gëtt op Input gepost dem Benotzer zougewisen'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Interne Kommentar gëtt op Input am Projet oder Dossier gepost'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Interne Kommentar gëtt op den Input gepost Benotzer huet intern kommentéiert'
        internal_comment_is_posted_on_idea_user_moderates: 'Interne Kommentar gëtt op Input Benotzer Moderéiert gepost'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Interne Kommentar gëtt op net zougewisenen Input am net verwalteten Projet gepost'
        project_review_request: 'Moderator gefrot e Projet review'
        project_review_state_change: 'Rezensor huet de Projet guttgeheescht'
        new_input_awaits_screening: 'Neien Input waart op Screening'
        new_input_is_published: 'Neien Input gëtt publizéiert'
        new_proposal_is_posted: 'Nei Propositioun ass gepost'
        project_phase_changes: 'Projet Phase Ännerungen'
        project_published: 'Projet publizéiert'
        proposal_gets_reported_as_spam: 'Propositioun gëtt als Spam gemellt'
        proposal_is_assigned_to_admin: 'Propositioun ass dem Administrateur zougewisen'
        proposal_is_published: 'Propositioun ass publizéiert'
        proposal_is_updated: 'Propositioun gëtt aktualiséiert'
        proposal_is_upvoted_above_threshold: 'D''Propositioun gëtt iwwer d''Schwell gestëmmt'
        proposal_status_changes: 'Propositioun Status Ännerungen'
        registration_to_event: 'Aschreiwung op en Event'
        survey_1_day_after_draft_saved: '1 Dag nodeems de Benotzer fir d''lescht d''Ëmfro am Entworf gespäichert huet'
        user_accepts_invitation_to_cosponsor_a_proposal: 'De Benotzer acceptéiert d''Invitatioun fir eng Propositioun ze sponsoren'
        user_comments: 'Benotzer Kommentaren'
        user_comments_on_input: 'Benotzer Kommentaren op Input'
        user_comments_on_proposal: 'Benotzer Kommentaren op Propositioun'
        user_is_given_admin_rights: 'De Benotzer gëtt Admin Rechter'
        user_is_given_folder_moderator_rights: 'Benotzer gëtt Dossier Moderator Rechter'
        user_is_given_project_moderator_rights: 'Benotzer gëtt Projet Moderator Rechter ginn'
        user_is_invited_to_cosponsor_a_proposal: 'De Benotzer ass invitéiert fir eng Propositioun ze sponseren'
        user_is_mentioned: 'Benotzer gëtt ernimmt'
        user_is_mentioned_in_internal_comment: 'Benotzer gëtt am internen Kommentar ernimmt'
        user_registers_for_the_first_time: 'Benotzer registréiert fir d''éischte Kéier'
        user_replies_to_comment: 'Benotzer Äntwerten op Commentaire'
        user_replies_to_internal_comment: 'Benotzer Äntwerten op intern Kommentar'
        voting_1_day_after_last_votes: '1 Dag nodeems de Benotzer lescht gestëmmt huet'
        voting_2_days_before_phase_closes: '2 Deeg virum Ofstëmmungsphase zou'
        voting_basket_submitted: 'D''Stëmmen ginn agereecht'
        voting_phase_ended: 'D''Wahlphase ass eriwwer'
