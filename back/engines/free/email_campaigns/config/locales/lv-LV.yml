lv:
  email_campaigns:
    campaign_type_description:
      "manual": <PERSON><PERSON><PERSON><PERSON>
      "manual_project_participants": <PERSON><PERSON><PERSON><PERSON>azi<PERSON> projekta dal<PERSON><PERSON><PERSON>
      "admin_rights_received": <PERSON>ņ<PERSON>t<PERSON>s administratora ties<PERSON>
      "comment_deleted_by_admin": <PERSON><PERSON>
      "comment_marked_as_spam": <PERSON><PERSON><PERSON><PERSON> par surogātpastu komentārā
      "comment_on_your_comment": Atbilde uz manu komentāru
      "comment_on_idea_you_follow": <PERSON><PERSON><PERSON><PERSON> par ideju, kurai jūs sekojat
      "community_monitor_report": Kopienas uzraudzības ziņojums
      "cosponsor_of_your_idea": Lietotājs pieņem manu uzaicinājumu atbalstīt manu priekšlikumu.
      "event_registration_confirmation": Pasākuma reģistrācijas apstiprinājums
      "idea_marked_as_spam": Ziņot par ideju kā surogātpastu
      "idea_published": <PERSON><PERSON> id<PERSON>
      "invitation_to_cosponsor_idea": Uzaicinājums līdzfinans<PERSON>t priek<PERSON>likumu
      "invite_received": <PERSON><PERSON><PERSON>āju<PERSON>
      "invite_reminder": Atgādinājums par uzaicinājumu
      "internal_comment_on_idea_assigned_to_you": Iekšējais komentārs par man piešķirto ieguldījumu
      "internal_comment_on_idea_you_commented_internally_on": Iekšējs komentārs par ievadītajiem datiem, ko komentēju iekšēji
      "internal_comment_on_idea_you_moderate": Iekšējs komentārs par ievades datiem projektā vai mapē, ko pārvaldu
      "internal_comment_on_unassigned_unmoderated_idea": Iekšējais komentārs par nepiešķirtu ievades datu nepiešķirtiem ievades datiem neapsaimniekotā projektā
      "internal_comment_on_your_internal_comment": Iekšējais komentārs par manu iekšējo komentāru
      "mention_in_official_feedback": Minēt atjauninājumā
      "mention_in_internal_comment": Pieminēšana iekšējā komentārā
      "new_comment_for_admin": Jauns komentārs projektā, kuru es moderēju
      "new_idea_for_admin": Jauna ideja projektā, kuru es moderēju
      "official_feedback_on_idea_you_follow": Atjaunināt ideju, kurai sekojat
      "password_reset": Paroles atiestatīšana
      "project_moderation_rights_received": Saņemtās projekta moderēšanas tiesības
      "project_folder_moderation_rights_received": Saņemtās mapju pārvaldnieka tiesības
      "project_phase_started": Jauns projekta posms
      "project_phase_upcoming": Gaidāmais jaunais projekta posms
      "project_published": Publicēts projekts
      "project_review_request": Projekta pārskatīšanas pieprasījums
      "project_review_state_change": Projekts apstiprināts
      "status_change_on_idea_you_follow": Idejas statusa maiņa, kurai jūs sekojat
      "survey_submitted": Iesniegtais apsekojums
      "threshold_reached_for_admin": Priekšlikums sasniedzis balsošanas slieksni
      "welcome": Pēc reģistrācijas
      "admin_digest": Nedēļas pārskats administratoriem
      "moderator_digest": Nedēļas pārskats projektu vadītājiem
      "assignee_digest": Nedēļas pārskats par piešķirtajām idejām
      "user_digest": Nedēļas pārskats
      "voting_basket_submitted": Balsošanas apstiprināšana
      "native_survey_not_submitted": Aptauja nav iesniegta
      "voting_basket_not_submitted": Neiesniegtās balsis
      "voting_last_chance": Pēdējā iespēja balsot
      "voting_phase_started": Jauns projekta posms ar balsošanu
      "voting_results": Balsošanas rezultāti
      "your_input_in_screening": Mans ieguldījums gaida skrīningu
    general:
      by_author: 'līdz %{authorName}'
      author_wrote: '%{authorName} rakstīja:'
      cta_goto_idea: 'Pārejiet uz šo ideju'
      cta_goto_input: 'Dodieties uz šo ievades'
      cta_goto:
        idea: 'Pārejiet uz šo ideju'
        question: 'Pārejiet uz šo jautājumu'
        contribution: 'Dodieties uz šo ieguldījumu'
        project: 'Dodieties uz šo projektu'
        issue: 'Pārejiet uz šo jautājumu'
        option: 'Pārejiet uz šo opciju'
        proposal: 'Pārejiet uz šo priekšlikumu'
        petition: 'Dodieties uz šo lūgumrakstu'
      cta_goto_your:
        idea: 'Pārejiet uz savu ideju'
        question: 'Pārejiet uz savu jautājumu'
        contribution: 'Dodieties uz savu ieguldījumu'
        project: 'Dodieties uz savu projektu'
        issue: 'Pārejiet uz savu jautājumu'
        option: 'Dodieties uz savu opciju'
        proposal: 'Pārejiet uz savu priekšlikumu'
        petition: 'Dodieties uz savu lūgumrakstu'
      cta_goto_proposal: 'Pārejiet uz šo priekšlikumu'
      cta_goto_project: 'Dodieties uz šo projektu'
    schedules:
      weekly:
        "0": "Ik nedēļu, svētdienās, %{hourOfDay}"
        "1": "Ik nedēļu, pirmdienās %{hourOfDay}"
        "2": "Ik nedēļu, otrdienās %{hourOfDay}"
        "3": "Ik nedēļu, trešdienās %{hourOfDay}"
        "4": "Ik nedēļu, ceturtdienās %{hourOfDay}"
        "5": "Ik nedēļu, piektdienās %{hourOfDay}"
        "6": "Ik nedēļu sestdienās %{hourOfDay}"
      quarterly: "Ceturkšņa ceturksnis, ceturkšņa pirmajā dienā"
    preview_data:
      first_name: 'Jane'
      last_name: 'Doe'
      display_name: 'Džeina Doe'
      comment_body: 'Šis ir komentāra piemērs, ko izmanto e-pasta vēstuļu satura priekšskatīšanai. Tas nav īsts saturs.'
      idea_title: 'Piemērs Ideja'
    footer:
      "link_privacy_policy": "Privātuma politika"
      "link_terms_conditions": "Noteikumi un nosacījumi"
      "link_unsubscribe": "Atteikties"
      "powered_by": "Darbību nodrošina"
      "recipient_statement": "Šo e-pasta vēstuli jums nosūtīja Go Vocal %{organizationName}vārdā, jo esat reģistrēts %{organizationLink}lietotājs."
      "unsubscribe_statement": "Ja nevēlaties turpmāk saņemt šos e-pasta ziņojumus, varat rakstīt uz %{unsubscribeLink} ."
      "unsubscribe_text": "atteikties"
    follow:
      "unfollow_here": "Jūs esat saņēmis šo paziņojumu, jo jums ir sekots līdzi. <a href=\"%{unfollow_url}\">Jūs varat to atcelt šeit.</a>"
    manual:
      preheader: 'Jums ir pasts no %{organizationName}'
    comment_deleted_by_admin:
      reason: 'Iemesls, kāpēc jūsu komentārs tika dzēsts:'
      cta_view: 'Skatīt šo ideju'
      event_description: '%{organizationName} izdzēsa komentāru, ko jūs rakstījāt par ideju.'
      main_header: '%{organizationName} izdzēsa jūsu komentāru'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Jūsu komentārs tika uzskatīts par nepiemērotu'
      irrelevant_reason: 'Jūsu komentārs tika uzskatīts par nebūtisku šajā kontekstā.'
      no_reason: 'Iemesls nav norādīts'
      subject: 'Jūsu komentārs ir dzēsts no %{organizationName} platformas.'
      preheader: 'Jūsu komentārs ir dzēsts'
    admin_digest:
      subject: 'Jūsu nedēļas administratora ziņojums par %{time}'
      preheader: 'Administratora pārskats par %{organizationName}'
      title_your_weekly_report: '%{firstName}, jūsu nedēļas pārskats'
      text_introduction: 'Mēs esam atlasījuši tos ievadītos datus, kas pagājušajā nedēļā ir izraisījuši vislielāko aktivitāti. Uzziniet, kas notiek jūsu platformā!'
      cta_visit_the_platform: 'Apmeklējiet savu platformu'
      new_users: 'Jauni lietotāji'
      new_inputs: 'Jauni ievades dati'
      new_comments: 'Jauni komentāri'
      title_activity_past_week: 'Pagājušās nedēļas aktivitātes'
      title_no_activity_past_week: 'Pagājušajā nedēļā nebija nekādas darbības'
      reached_threshold: 'Sasniegts slieksnis'
      yesterday_by_author: 'Vakar %{author}'
      today_by_author: 'Šodien %{author}'
      x_days_ago_by_author: 'pirms %{x} dienām %{author}'
    admin_rights_received:
      cta_manage_platform: 'Pārvaldiet savu platformu'
      message_you_became_administrator: 'Jums ir piešķirtas administratora tiesības %{organizationName} līdzdalības platformai.'
      preheader: 'Jums tika piešķirtas administratora tiesības %{organizationName} līdzdalības platformai.'
      subject: 'Jūs kļuvāt par %{organizationName} platformas administratoru.'
      text_create_participatory_process: 'Būdams administrators, varat veidot un konfigurēt jaunus līdzdalības projektus. Jūs varat pievienot jaunus posmus, izmantojot laika grafiku. Katram no šiem posmiem var būt sava uzvedība attiecībā uz ideju publicēšanu, komentēšanu un balsošanu.'
      text_moderate_analyse_input: 'Tiklīdz projekti ir uzsākti, tiks saņemtas pirmās idejas. Jūs saņemsiet nedēļas pārskatus ar visām galvenajām darbībām, lai jūs būtu lietas kursā. Ideju pārskats palīdzēs jums moderēt ievadītos datus un sadarboties to apstrādē.'
      text_platform_setup: 'Būdams administrators, varat izveidot savu līdzdalības platformu. Izvēlieties logotipu, attēlus un krāsas, uzrakstiet personisku vēstījumu savā sākumlapā, izsūtiet uzaicinājumus, definējiet, ko vēlaties zināt par saviem lietotājiem, ...'
      title_create_participatory_process: 'Izstrādāt līdzdalības procesu'
      title_moderate_analyse_input: 'Moderēt un analizēt ievadītos datus'
      title_platform_setup: 'Platformas iestatīšana'
      title_what_can_you_do_administrator: 'Ko jūs varat darīt kā administrators?'
      title_you_became_administrator: 'Jūs kļuvāt par administratoru'
    comment_marked_as_spam:
      by_author: 'autors: %{authorName}'
      commented: '%{authorName} komentēja:'
      cta_review_comment: 'Pārskatīt komentāru'
      days_ago: 'pirms %{numberOfDays} dienām'
      event_description: 'Tika ziņots par šādu <strong>''%{post}''</strong> publicēto komentāru:'
      inappropriate_content: 'Komentārs ir nepiemērots vai aizvainojošs.'
      preheader: 'Rīkojieties sakarā ar šo komentāru, kas tika ziņots kā surogātpasts'
      reported_this_because: '%{reporterFirstName} ziņoja par šo, jo:'
      subject: '%{organizationName}: %{firstName} %{lastName} ziņoja par šo komentāru kā surogātpastu'
      title_comment_spam_report: '%{firstName} %{lastName} ziņoja par šo komentāru kā surogātpastu'
      today: Šodien
      wrong_content: 'Komentārs nav būtisks.'
      yesterday: Vakar
    community_monitor_report:
      subject: 'Ir pieejams jauns Kopienas monitoringa ziņojums'
      title: 'Ir pieejams jauns Kopienas monitoringa ziņojums'
      text_introduction: 'Par iepriekšējo ceturksni ir izveidots kopienas uzraudzības ziņojums. Tam var piekļūt, noklikšķinot uz zemāk redzamās pogas un pieslēdzoties.'
      cta_report_button: 'Apskatīt ziņojumu'
      report_name: 'Kopienas uzraudzības ziņojums'
    cosponsor_of_your_idea:
      cta_reply_to: 'Pārskatīt savu priekšlikumu'
      event_description: 'Apsveicam! %{cosponsorName} ir pieņēmis jūsu uzaicinājumu līdzfinansēt jūsu priekšlikumu.'
      main_header: '%{cosponsorName} ir pieņēmusi jūsu uzaicinājumu līdzfinansēt jūsu priekšlikumu.'
      subject: '%{cosponsorName} ir pieņēmusi jūsu uzaicinājumu līdzfinansēt jūsu priekšlikumu.'
      preheader: '%{cosponsorName} ir pieņēmusi jūsu uzaicinājumu līdzfinansēt jūsu priekšlikumu.'
    assignee_digest:
      subject: 'Idejas, kurām nepieciešama jūsu atsauksme: %{numberIdeas}'
      preheader: 'Izpildītāja pārskats par %{organizationName}'
      title_your_weekly_report: '%{firstName}, pilsoņa ievadītie dati gaida jūsu atsauksmes'
      cta_manage_your_input: 'Pārvaldiet savus ievadītos datus'
      x_inputs_need_your_feedback: 'ievades datiem ir nepieciešama jūsu atgriezeniskā saite'
      title_assignment_past_week: 'Jaunākās jums piešķirtās idejas'
      title_no_assignment_past_week: 'Pagājušajā nedēļā jums netika piešķirtas jaunas idejas'
      yesterday_by_author: 'Vakar %{author}'
      today_by_author: 'Šodien %{author}'
      x_days_ago_by_author: 'pirms %{x} dienām %{author}'
      title_successful_past_week: 'Jums piešķirts, kas sasniedzis slieksni'
    idea_marked_as_spam:
      cta_review: 'Pārskats'
      report_inappropriate_offensive_content: 'Es uzskatu, ka šis saturs ir nepiemērots vai aizskarošs.'
      report_not_an_idea: 'Šis saturs nav ideja, un tas nepieder šeit.'
      subject: 'Jums ir ziņojums par surogātpastu %{organizationName} platformā'
      preheader: 'Rīkojieties sakarā ar šo ziņojumu par surogātpastu'
      reported_this_because: '%{reporterFirstName} ziņoja par šo, jo:'
      title_spam_report: '%{firstName} %{lastName} ziņoja par surogātpastu'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Šā priekšlikuma līdzfinansēšana'
      event_description: '%{authorName} ir izstrādājusi jaunu priekšlikumu un vēlas, lai jūs to atbalstītu.'
      event_description_cosponsoring: 'Priekšlikuma līdzfinansēšana nozīmē, ka <strong>jūsu vārds tiks norādīts</strong> kopā ar citu priekšlikuma līdzfinansētāju vārdiem.'
      event_description_before_action: 'Lai redzētu priekšlikumu un pieņemtu uzaicinājumu, jums ir jābūt pieteicies savā kontā.'
      event_description_action: 'Noklikšķiniet tālāk, lai iepazītos ar priekšlikumu.'
      main_header: 'Jūs esat aicināts kļūt par priekšlikuma līdzfinansētāju.'
      subject: 'Jūs esat aicināts kļūt par priekšlikuma līdzfinansētāju.'
      preheader: 'Jūs esat aicināts līdzfinansēt %{authorName}priekšlikumu.'
    invite_reminder:
      cta_accept_invitation: 'Pieņemt jūsu uzaicinājumu'
      invitation_header: 'Jūsu uzaicinājums vēl nav pieņemts'
      preheader: '%{organizationName} pirms dažām dienām nosūtīja jums uzaicinājumu pievienoties viņu līdzdalības platformai.'
      invitation_expiry_message: 'Šis ielūgums beidzas aptuveni pēc %{expiryDaysRemaining} dienām.'
      subject: 'Gaidāmais uzaicinājums uz %{organizationName} līdzdalības platformu'
    invite_received:
      added_a_message: '%{organizationName} uzrakstīja šādu ziņojumu:'
      cta_accept_invitation: 'Pieņemt jūsu uzaicinājumu'
      invitation_header: 'Jūs esat laipni aicināti!'
      invitation_header_message: '%{organizationName} uzaicināja jūs piedalīties viņu līdzdalības platformā.'
      invitation_expiry_message: 'Šis ielūgums beidzas pēc %{expiryDays} dienām.'
      preheader: '%{organizationName} nosūtīja jums uzaicinājumu pievienoties viņu līdzdalības platformai.'
      subject: 'Jūs esat aicināts pievienoties %{organizationName} platformai'
    mention_in_comment:
      cta_reply_to: 'Atbildēt %{commentAuthor}'
      event_description: '%{commentAuthorFull} ir pieminējis jūs savā komentārā par ideju ''%{post}''. Noklikšķiniet uz zemāk redzamās saites, lai iesaistītos sarunā ar %{commentAuthor}'
      main_header: 'Cilvēki runā par jums'
      subject: 'Kāds pieminēja jūs %{organizationName} platformā.'
      preheader: '%{commentAuthor} pieminēja jūs komentārā'
    mention_in_internal_comment:
      cta_reply_to: 'Skatīt komentāru pēc %{firstName}'
      event_description: '%{authorNameFull} jūs pieminēja iekšējā komentārā.'
      subject: '%{firstName} jūs pieminēja iekšējā komentārā.'
      main_header: '%{firstName} jūs pieminēja iekšējā komentārā.'
      preheader: '%{authorNameFull} jūs pieminēja iekšējā komentārā.'
    moderator_digest:
      subject: 'Jūsu nedēļas projekta vadītāja pārskats par %{project_title}'
      preheader: 'Projekta vadītāja pārskats par %{organizationName}'
      title_your_weekly_report: '%{firstName}, jūsu nedēļas pārskats'
      text_introduction: 'Mēs esam atlasījuši tos ievadītos datus, kas pagājušajā nedēļā ir izraisījuši vislielāko aktivitāti. Uzziniet, kas notiek ar jūsu projektu!'
      cta_manage: 'Projekta pārvaldība'
      new_users: 'Jauni lietotāji'
      new_ideas: 'Jaunas idejas'
      new_comments: 'Jauni komentāri'
      title_inputs_past_week: 'Jauni ievades dati pagājušajā nedēļā'
      title_no_inputs_past_week: 'Pēdējā nedēļā nav saņemti jauni dati'
      title_threshold_reached: Pēdējā nedēļā sasniegtais slieksnis
      yesterday_by_author: 'Vakar %{author}'
      today_by_author: 'Šodien %{author}'
      x_days_ago_by_author: 'pirms %{x} dienām %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} komentēja:'
      cta_reply_to: 'Skatīt %{commentAuthor} komentāru'
      days_ago: 'pirms %{numberOfDays} dienām'
      event_description: '%{authorName} pievienoja jaunu komentāru jūsu platformā.'
      main_header: '%{firstName}, jūsu platformā ir publicēts jauns komentārs.'
      subject: '%{organizationName} platformā ir publicēts jauns komentārs.'
      preheader: '%{authorName} atstāja komentāru'
      today: Šodien
      yesterday: Vakar
    comment_on_idea_you_follow:
      cta_reply_to: 'Atbildēt %{commentAuthor}'
      event_description: '%{authorNameFull} placed a reaction on ''%{inputTitle}''. Click the button below to continue the conversation with %{authorName}.'
      main_header:
        idea: '%{authorName} komentēja ideju, kurai tu seko'
        question: '%{authorName} komentēja jautājumu, kas jums seko'
        contribution: '%{authorName} komentēja ieguldījumu, kuru jūs sekojat'
        project: '%{authorName} komentēja kādu no projektiem, kam tu seko'
        issue: '%{authorName} komentēja jautājumu, kuram jūs sekojat'
        option: '%{authorName} komentēja iespēju, kuru jūs sekojat'
        proposal: '%{authorName} komentēja priekšlikumu, kam tu seko'
        petition: '%{authorName} komentēja lūgumrakstu, kam tu seko'
      subject: 'There''s a new comment on "%{input_title}"'
      preheader: '%{authorName} atstāja komentāru par ideju par %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Jūsu platformā ir publicēts jauns ievade'
      event_description_publication: '%{authorName} ir iesniedzis jaunu ievades ziņojumu par jūsu platformu. Atklājiet to tagad, sniedziet atsauksmes vai mainiet tā statusu!'
      cta_publication: 'Sniedziet atgriezenisko saiti uz %{authorName}'
      main_header_prescreening: 'Ir nepieciešams jūsu pārskats par ievades datiem'
      event_description_prescreening: '%{authorName} ir iesniedzis jaunu ievades ziņojumu par jūsu platformu.'
      input_not_visible_prescreening: '<b>Ievads nebūs redzams,</b> kamēr nemainīsiet tā statusu.'
      cta_prescreening: 'Pārbaudiet ievades datus'
      days_ago: 'pirms %{numberOfDays} dienām'
      preheader: '%{authorName} publicēja jaunu ideju jūsu platformā'
      today: Šodien
      yesterday: Vakar
    comment_on_your_comment:
      cta_reply_to: 'Atbildēt %{firstName}'
      event_description: '%{authorNameFull} uzrakstīja atbildi uz jūsu komentāru par ''%{post}'' līdzdalības platformā. Noklikšķiniet uz zemāk redzamās pogas, lai turpinātu sarunu ar %{authorName}.'
      subject: 'Jūs esat saņēmis atbildi uz savu komentāru %{organizationName} platformā.'
      main_header: '%{authorName} atbildēja uz jūsu komentāru'
      preheader: '%{authorName} atbildēja uz jūsu komentāru %{organizationName} platformā'
      replied: '%{authorFirstName} atbildēja:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Skatīt komentāru pēc %{firstName}'
      event_description: '%{authorNameFull} komentēja jūsu iekšējo komentāru.'
      subject: 'Jūs esat saņēmis komentāru par savu iekšējo komentāru vietnē ''%{post}'''
      main_header: 'Jūs esat saņēmis komentāru par savu iekšējo komentāru vietnē ''%{post}'''
      preheader: '%{authorName} atbildēja uz jūsu iekšējo komentāru par ''%{post}'''
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Skatīt komentāru pēc %{firstName}'
      event_description: '%{authorNameFull} iekšēji komentēja jums piešķirto ievadi.'
      subject: '''%{post}'' ir jauns iekšējais komentārs'
      main_header: '''%{post}'' ir jauns iekšējais komentārs'
      preheader: '''%{post}'' ir jauns iekšējais komentārs'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Skatīt komentāru pēc %{firstName}'
      event_description: '%{authorNameFull} iekšēji komentēja ievades datus, kurus jūs komentējāt iekšēji.'
      subject: '''%{post}'' ir jauns iekšējais komentārs'
      main_header: '''%{post}'' ir jauns iekšējais komentārs'
      preheader: '''%{post}'' ir jauns iekšējais komentārs'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Skatīt komentāru pēc %{firstName}'
      event_description: '%{authorNameFull} iekšēji komentēt ievades ievades failu jūsu pārvaldītajā projektā vai mapē.'
      subject: '''%{post}'' ir jauns iekšējais komentārs'
      main_header: '''%{post}'' ir jauns iekšējais komentārs'
      preheader: '''%{post}'' ir jauns iekšējais komentārs'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Skatīt komentāru pēc %{firstName}'
      event_description: '%{authorNameFull} iekšēji komentēja nepiešķirtu ievadi neapsaimniekotā projektā.'
      subject: '''%{post}'' ir jauns iekšējais komentārs'
      main_header: '''%{post}'' ir jauns iekšējais komentārs'
      preheader: '''%{post}'' ir jauns iekšējais komentārs'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} gave an update on ''%{input_title}''.'
      header_title: 'There''s an update on "%{input_title}"'
      subject: 'Official feedback was posted on "%{input_title}"'
      preheader: 'There''s an update on an input you follow'
    mention_in_official_feedback:
      cta_reply_to: 'Atbildēt %{organizationName}'
      event_description: '%{organizationName} pieminēja jūs atsauksmēs par ideju ''%{post}''. Noklikšķiniet uz zemāk redzamās saites, lai iesaistītos sarunā ar %{organizationName}'
      main_header: 'Jūs esat pieminēts'
      subject: '%{organizationName} pieminēja jūs savā atsauksmē'
      preheader: '%{commentAuthor} pieminēja jūs atsauksmē'
    project_moderation_rights_received:
      cta_manage_project: 'Šī projekta pārvaldība'
      message_you_became_moderator: '%{organizationName} līdzdalības platformas administrators tikko iecēla jūs par šā projekta vadītāju:'
      no_ideas: 'Vēl nav ideju'
      preheader: '%{organizationName} līdzdalības platformas administrators tikko iecēla jūs par šā projekta vadītāju'
      subject: 'Jūs kļuvāt par projektu vadītāju %{organizationName} platformā.'
      text_design_participatory_process: 'Būdams projekta vadītājs, varat konfigurēt lietotāju mijiedarbību projektā. Jūs varat pievienot jaunus posmus, izmantojot laika grafiku. Katram no šiem posmiem var būt sava uzvedība attiecībā uz ideju publicēšanu, komentēšanu un balsošanu.'
      text_moderate_analyse_input: 'Tiklīdz projekts ir uzsākts, tiks saņemtas pirmās idejas. Jūs saņemsiet nedēļas pārskatus ar visām galvenajām darbībām, lai jūs būtu lietas kursā. Ideju pārskats projekta vadītāja skatā palīdzēs jums saprast, kuras idejas ir saņēmušas visvairāk pozitīvo un negatīvo balsu.'
      text_share_project_information: 'Lai uzlabotu iegūto ideju kvalitāti, ir svarīgi dalīties ar pietiekamu informāciju: pievienot projekta aprakstu, pievienot attēlus (tostarp skices un plānus) un informēt par visiem saistītajiem notikumiem. Atcerieties: laba informācija ir priekšnoteikums labai līdzdalībai!'
      title_design_participatory_process: 'Izstrādāt līdzdalības procesu'
      title_moderate_analyse_input: 'Moderēt un analizēt ievadītos datus'
      title_share_project_information: 'Sniegt informāciju par projektu'
      title_what_can_you_do_moderator: 'Ko jūs varat darīt kā projektu vadītājs?'
      title_you_became_moderator: 'Jūs kļuvāt par projektu vadītāju'
      x_ideas: '%{numberOfIdeas} idejas'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Skatīt šo mapi'
      message_added_as_folderadmin: 'Jums ir piešķirtas mapju pārvaldnieka tiesības %{organizationName} līdzdalības platformā attiecībā uz šādu mapi:'
      no_projects: 'Projektu vēl nav'
      preheader: '%{organizationName} līdzdalības platformas administrators tikko padarīja jūs par šādas mapes pārvaldnieku'
      subject: 'Jūs kļuvāt par projekta mapes pārvaldnieku %{organizationName} līdzdalības platformā.'
      text_manage_folder: 'Mape ir veids, kā organizēt vairākus dalības projektus kopā. Būdams mapes pārvaldnieks, varat rediģēt mapi un mapes aprakstu, kā arī veidot jaunus projektus (lai dzēstu projektus, sazinieties ar platformas administratoru). Jums būs arī projektu pārvaldības tiesības attiecībā uz visiem mapē esošajiem projektiem, kas ļaus jums rediģēt projektus, pārvaldīt ievadītos datus un nosūtīt e-pastus dalībniekiem.'
      text_moderate_analyse_input: 'Tiklīdz projekti ir uzsākti, sāks ienākt pirmie ievadītie dati. Jūs saņemsiet nedēļas pārskatus ar galvenajiem pasākumiem, lai jūs varētu sekot līdzi notiekošajam. Ievadīto datu pārvaldnieks panelī "Pārvaldīt platformu" ļauj jums redzēt un pārvaldīt ievadītos datus, tostarp piešķirt statusus un atbildēt uz ziņām un komentāriem.'
      text_design_participatory_process: 'Jūs varat pārvaldīt dažādus līdzdalības projektus mapē - konfigurēt dalības metodi, pievienot projekta aprakstu, attēlus un ziņot par saistītajiem notikumiem. Jūs varat arī pārvaldīt, kā dalībnieki mijiedarbojas ar jūsu projektiem, tostarp iestatīt piekļuves tiesības un konfigurēt publicēšanas, balsošanas un komentēšanas iestatījumus.'
      title_design_participatory_process: 'Izstrādāt līdzdalības procesu'
      title_moderate_analyse_input: 'Moderēt un analizēt ievadītos datus'
      title_manage_folder: 'Pārvaldiet mapes iestatījumus un veidojiet jaunus projektus.'
      title_what_can_you_do_folderadmin: 'Ko jūs varat darīt kā mapju pārvaldnieks?'
      title_added_as_folderadmin: 'Jūs esat pievienots kā mapju pārvaldnieks'
      x_projects: '%{numberOfProjects} projekti'
    project_phase_started:
      cta_view_phase: 'Atklājiet šo jauno posmu'
      event_description: 'Šim projektam ir sācies jauns posms %{organizationName} platformā. Noklikšķiniet uz zemāk redzamās saites, lai uzzinātu vairāk!'
      main_header: 'Projektam ''%{projectName}'' ir sākusies jauns posms.'
      subtitle: 'Par ''%{projectName}'''
      new_phase: 'Šim projektam ir sācies ''%{phaseTitle}'' posms.'
      subject: '%{projectName} ir sācies jauns posms'
      preheader: 'Projektam ''%{projectName}'' ir sācies jauns posms.'
    project_phase_upcoming:
      cta_view_phase: 'Iestatiet šo jauno posmu'
      event_description: 'Projektam ''%{projectName}'' drīzumā sāksies jauns posms. Pārliecinieties, ka viss ir sagatavots šim posmam: Vai ir atbilstošs apraksts? Vai atlasītās idejas ir pārnestas uz šo posmu? Vai vēlaties informēt iedzīvotājus par šī posma specifiku, izmantojot pielāgotu e-pasta kampaņu?'
      main_header: '%{firstName}, projektam drīzumā sāksies jauns posms'
      subtitle: 'Par ''%{projectName}'''
      new_phase: 'Projektam sāksies ''%{phaseTitle}'' posms.'
      subject: 'Sagatavojiet visu %{projectName} jaunajam posmam'
      preheader: 'Drīz sāksies %{projectName} jauns posms'
    project_published:
      subject: 'Platformā %{organizationName}tika publicēts jauns projekts.'
      header_title: 'Publicēts jauns projekts'
      header_message: 'Dalības platformā %{organizationName} tikko publicēts šāds projekts:'
      preheader: 'Publicēts jauns projekts'
    project_review_request:
      subject: 'Pārskata pieprasījums: Projekts gaida apstiprinājumu.'
      header: '%{requesterName} ir aicinājusi jūs pārskatīt projektu "%{projectTitle}"'
      header_message: "Pašlaik projekts ir darba režīmā un nav redzams lietotājiem. Kad to būsiet pārskatījuši un apstiprinājuši, moderators varēs to publicēt."
      cta_review_project: "Projekta pārskatīšana"
    project_review_state_change:
      subject: '"%{projectTitle}" ir apstiprināts'
      header: '%{reviewerName} apstiprināja projektu "%{projectTitle}"'
      header_message: "Tagad projekts ir gatavs palaišanai. Jūs varat to publicēt, kad vien būsiet gatavs!"
      cta_go_to_project: "Dodieties uz projekta iestatījumiem"
    status_change_on_idea_you_follow:
      status_change: 'The new status of this input is ''%{status}'''
      header_message: '%{organizationName} updated the status of the input ''%{input_title}'' on their digital participation platform.'
      header_title: 'An input you follow has a new status'
      subject: 'The status of "%{input_title}" has changed'
      preheader: 'An input you follow has a new status'
    user_digest:
      subject: "Jūsu aktivitāte %{organizationName} līdzdalības platformā"
      commented: "%{authorFirstName} komentēja:"
      preheader: "Nedēļas pārskats par %{organizationName}"
      title_your_weekly_report: "Uzziniet, kas notika pagājušajā nedēļā"
      intro_text: "Šeit ir kopsavilkums par to, kas notika %{organizationName} līdzdalības platformā."
      cta_go_to_the_platform: "Dodieties uz platformu"
      title_no_activity_past_week: "Pagājušajā nedēļā nav veiktas nekādas darbības"
      successful_proposals_title: "Sasniegts slieksnis"
      successful_proposals_text: "Šie priekšlikumi ir guvuši pietiekamu atbalstu, lai pārietu uz nākamo posmu! Noklikšķiniet uz priekšlikuma, lai uzzinātu vairāk par to, kas notiek tālāk."
      today_by_author: "Šodien %{author}"
      yesterday_by_author: "Vakar %{author}"
      x_days_ago_by_author: "pirms %{x} dienām %{author}"
      trending_title: "Tendences"
      trending_text: "Interesē, kas notiek platformā? Lūk, trīs populārākie ieguldījumi un to, ko cilvēki par tiem saka."
      no_notifications: "Nav paziņojumu"
      one_notification: "1 paziņojums"
      multiple_notifications: "%{notifCount} paziņojumi"
      no_unread_notifications: "Jums nav neviena nelasīta paziņojuma. Apmeklējiet platformu, lai ievadītu datus un veidotu jaunus paziņojumus!"
      unread_notifications: "Jums ir nelasīti paziņojumi. Apmeklējiet platformu, lai uzzinātu, kas notiek!"
    threshold_reached_for_admin:
      cta_process_initiative: 'Īstenojiet šo iniciatīvu nākamajos posmos'
      main_header: 'Iniciatīva sasniedza balsošanas slieksni!'
      subject: 'Iniciatīva ir sasniegusi balsošanas slieksni jūsu platformā'
      preheader: 'Pārliecinieties, ka veicat nākamos soļus'
    welcome:
      cta_join_platform: 'Atklājiet platformu'
      subject: 'Laipni lūgti %{organizationName} platformā'
      main_header: Laipni lūdzam!
      message_welcome: 'Apsveicam, jūs veiksmīgi reģistrējāties %{organizationName} līdzdalības platformā. Tagad jūs varat iepazīties ar platformu un paust savu viedokli. Jūs varat arī pievienot profila attēlu un īsu aprakstu, lai citiem pastāstītu par sevi.'
      preheader: 'Lūk, ko jūs varat darīt %{organizationName} platformā.'
    idea_published:
      subject:
        idea: 'Jūsu ideja ir publicēta'
        question: 'Jūsu jautājums ir publicēts'
        contribution: 'Jūsu ieguldījums ir publicēts'
        project: 'Jūsu projekts ir publicēts'
        issue: 'Jūsu izdevums ir publicēts'
        option: 'Jūsu opcija ir publicēta'
        proposal: 'Jūsu priekšlikums ir publicēts'
        petition: 'Jūsu lūgumraksts ir publicēts'
      main_header: 'Jūs publicējāt ideju! Pārliecināsimies, ka tā tiek izlasīta.'
      header_message: 'Pārliecināsimies, ka tas tiek izlasīts.'
      message_get_votes: 'Sasniedziet vairāk cilvēku ar savu ideju:'
      action_published_idea: 'Publicētā ideja'
      action_add_image: '%{addImageLink}, lai palielinātu redzamību'
      add_image: 'Pievienot attēlu'
      action_share_fb: 'Paziņojiet saviem draugiem par %{fbLink}'
      action_share_twitter: 'Informējiet savus sekotājus par %{twitterLink}'
      action_send_email: 'Nosūtiet saviem kontaktiem %{sendEmailLink}'
      send_email: e-pasts
      action_share_link: 'Kopīgojiet to jebkurā kanālā, kopējot %{link}'
      link: saite
      preheader: '%{firstName}, apsveicam ar pirmās idejas publicēsanu %{organizationName} platformā. Tagad savāciet atbalstu savai idejai'
    your_input_in_screening:
      main_header:
        idea: 'Jūsu ideja ir "%{prescreening_status_title}"'
        question: 'Jūsu jautājums ir "%{prescreening_status_title}"'
        contribution: 'Jūsu ieguldījums ir "%{prescreening_status_title}"'
        project: 'Jūsu projekts ir "%{prescreening_status_title}"'
        issue: 'Jūsu jautājums ir sadaļā "%{prescreening_status_title}"'
        option: 'Jūsu iespēja ir "%{prescreening_status_title}"'
        proposal: 'Jūsu priekšlikums ir "%{prescreening_status_title}"'
        petition: 'Jūsu lūgumraksts ir "%{prescreening_status_title}"'
      message: '"%{input_title}" kļūs redzams citiem, kad tas būs pārskatīts un apstiprināts.'
      subject: '"%{input_title}" ir gandrīz publicēts'
      preheader: 'Pašlaik tas ir %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}: Jūs veiksmīgi nobalsojāt'
      preheader: 'Jūs veiksmīgi balsojāt %{organizationName}līdzdalības platformā.'
      title_basket_submitted: 'Jūs veiksmīgi nobalsojāt'
      event_description: 'Paldies par dalību. Jūsu balsis ir reģistrētas. Apmeklējiet %{organizationName} platformu, lai redzētu un pārvaldītu savas balsis.'
      cta_see_votes_submitted: 'Skatīt iesniegtos balsojumus'
      cta_message: 'Noklikšķiniet uz pogas zemāk, lai piedalītos'
    native_survey_not_submitted:
      subject: '%{organizationName}: Gandrīz ir! Nosūtiet savas atbildes'
      preheader: 'Jūs neesat aizpildījis savu aptaujas atbildi %{organizationName}dalības platformā.'
      title_native_survey_not_submitted: 'Gandrīz ir! Nosūtiet savas atbildes'
      body_native_survey_not_submitted: 'Jūs sākāt dalīties ar savām atbildēm vietnē %{phaseTitle} , bet tās neiesūtījāt. Iesniegšana beigsies %{phaseEndDate}. Noklikšķiniet uz zemāk redzamās pogas, lai turpinātu, kur esat pārtraucis.'
      body_native_survey_not_submitted_no_date: 'Jūs sākāt dalīties ar savām atbildēm vietnē %{phaseTitle} , bet tās neiesūtījāt. Noklikšķiniet uz zemāk redzamās pogas, lai turpinātu, kur esat pārtraucis.'
      cta_complete_your_survey_response: 'Turpiniet atbildēt uz aptaujas jautājumiem'
    voting_basket_not_submitted:
      subject: '%{organizationName}: Jūs neiesūtījāt savas balsis'
      preheader: 'Jūs neiesniedzāt savas balsis %{organizationName}dalības platformā.'
      title_basket_not_submitted: 'Jūs neiesniedzāt savas balsis'
      event_description: 'Izvēlējāties vairākas iespējas vietnē %{contextTitle} , bet atlasi neiesūtījāt.'
      cta_view_options_and_vote: 'Skatiet opcijas un balsojiet'
      cta_message: 'Noklikšķiniet uz pogas zemāk, lai iesniegtu atlasītās opcijas'
    voting_last_chance:
      subject: '%{organizationName}: Pēdējā iespēja balsot par %{phaseTitle}'
      preheader: 'Pēdējā iespēja balsot par %{phaseTitle} %{organizationName}dalības platformā'
      title_last_chance: 'Pēdējā iespēja balsot par %{phaseTitle}'
      body_1: 'Rīt pusnaktī noslēgsies balsošana par projektu %{projectTitle} .'
      body_2: 'Laiks iet uz beigām, un mēs pamanījām, ka jūs vēl neesat nobalsojis! Lai piedalītos balsošanā, vienkārši nospiediet zemāk redzamo pogu.'
      body_3: 'Tādējādi jūs iegūsiet piekļuvi dažādām iespējām un iespēju sniegt savu ieguldījumu, kas ir ļoti svarīgs, lai lemtu par šī projekta nākotni.'
      cta_vote: 'Balsojums'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} atklāti balsošanas rezultāti!'
      preheader: '%{phaseTitle} balsošanas rezultāti atklāti %{organizationName}līdzdalības platformā.'
      title_results: '%{phaseTitle} atklāti balsošanas rezultāti!'
      body_1: 'Rezultāti ir zināmi!'
      body_2: 'Ir publicēti %{phaseTitle} platformas %{organizationName} balsojuma rezultāti!'
      body_3: 'Aicinām iepazīties ar rezultātiem un sekot līdzi turpmākai informācijai par nākamajiem soļiem.'
      cta_see_results: 'Skatiet rezultātus platformā'
    event_registration_confirmation:
      subject: "Jūs esat iekļauts! Jūsu reģistrācija \"%{eventTitle}\" ir apstiprināta"
      preheader: "%{firstName}, paldies par reģistrēšanos %{eventTitle}"
      header_message: "%{firstName}, paldies par reģistrēšanos"
      event_details:
        labels:
          date: 'Datums'
          location: 'Atrašanās vieta'
          online_link: 'Tiešsaistes saite'
          description: 'Apraksts'
          project: 'Projekts'
      cta_go_to_event: 'Apskatīt notikumu'
      cta_add_to_calendar: 'Pievienot kalendāram'
    voting_phase_started:
      subject: '%{organizationName}: Sācies %{projectName}balsošanas posms'
      preheader: 'Sākās balsošanas posms par %{projectName} dalības platformā %{organizationName}.'
      event_description: 'Projekts "%{projectName}" aicina jūs balsot starp %{numIdeas} iespējām:'
      cta_message: 'Noklikšķiniet uz pogas zemāk, lai piedalītos'
      cta_vote: 'Dodieties uz platformu, lai balsotu'
    survey_submitted:
      subject: '%{organizationName}: Paldies par atbildi! 🎉'
      preheader: 'Šeit ir sniegta informācija par jūsu iesniegumu.'
      main_header: 'Paldies, ka dalījāties savās pārdomās par "%{projectName}"!'
      your_input_submitted: 'Jūsu dati par "%{projectName}" ir veiksmīgi iesniegti.'
      if_you_would_like_to_review: 'Ja vēlaties pārskatīt savu iesniegumu, atbildes varat lejupielādēt zemāk.'
      your_submission_has_id: 'Jūsu iesniegumam ir šāds unikāls identifikators:'
      you_can_use_this_id: 'Šo identifikatoru varat izmantot, lai sazinātos ar platformas administratoriem, ja vēlaties, lai jūsu iesniegums tiktu dzēsts.'
      download_responses: 'Lejupielādējiet savas atbildes'
    admin_labels:
      recipient_role:
        admins: 'Administratoriem'
        admins_and_managers: 'Administratoriem un vadītājiem'
        managers: 'Vadītājiem'
        project_participants: 'Projekta dalībniekiem'
        registered_users: 'Reģistrētiem lietotājiem'
      recipient_segment:
        admins: 'Administratori'
        admins_and_managers: 'Administratori un vadītāji'
        admins_and_managers_assigned_to_the_input: 'Ievadei piešķirtie administratori un vadītāji'
        admins_and_managers_managing_the_project: 'Admini un vadītāji, kas pārvalda projektu'
        admins_assigned_to_a_proposal: 'Priekšlikumam piešķirtie administratori'
        all_users: 'Visi lietotāji'
        all_users_who_uploaded_proposals: 'Visi lietotāji, kas augšupielādējuši priekšlikumus'
        managers: 'Vadītāji'
        managers_managing_the_project: 'Projekta vadītāji, kas pārvalda projektu'
        new_attendee: 'Jaunreģistrēts lietotājs'
        project_reviewers: 'Projektu recenzenti un mapju pārvaldnieki'
        project_review_requester: 'Lietotājs, kurš pieprasīja projekta pārskatīšanu'
        user_who_commented: 'Lietotājs, kas komentēja'
        user_who_is_invited_to_cosponsor_a_proposal: 'Lietotājs, kurš ir uzaicināts līdzfinansēt priekšlikumu'
        user_who_is_mentioned: 'Lietotājs, kurš ir minēts'
        user_who_is_receiving_admin_rights: 'Lietotājs, kurš saņem administratora tiesības'
        user_who_is_receiving_folder_moderator_rights: 'Lietotājs, kurš saņem mapes moderatora tiesības'
        user_who_is_receiving_project_moderator_rights: 'Lietotājs, kurš saņem projekta moderatora tiesības'
        user_who_published_the_input: 'Lietotājs, kas publicējis ievades datus'
        user_who_published_the_proposal: 'Lietotājs, kurš publicēja priekšlikumu'
        user_who_registers: 'Lietotājs, kas reģistrējas'
        user_who_submitted_the_input: 'Lietotājs, kas ievadījis ievades datus'
        user_who_voted: 'Lietotājs, kas balsoja'
        user_who_was_invited: 'Lietotājs, kurš tika uzaicināts'
        user_with_unsubmitted_survey: 'Lietotājs, kurš ir sācis, bet nav iesniedzis savu aptauju'
        user_with_unsubmitted_votes: 'Lietotājs, kurš nav iesniedzis savu balsojumu'
        users_who_engaged_but_not_voted: 'Lietotāji, kas iesaistījās projektā, bet nav balsojuši'
        users_who_engaged_with_the_project: 'Lietotāji, kas iesaistījās projektā'
        users_who_follow_the_input: 'Lietotāji, kas seko ievades'
        users_who_follow_the_project: 'Lietotāji, kas seko projektam'
        users_who_follow_the_proposal: 'Lietotāji, kas seko priekšlikumam'
      content_type:
        comments: 'Komentāri'
        content_moderation: 'Satura moderēšana'
        events: 'Notikumi'
        general: 'Vispārīgi'
        inputs: 'Ieejas'
        internal_comments: 'Iekšējās piezīmes'
        permissions: 'Atļaujas'
        projects: 'Projekti'
        proposals: 'Priekšlikumi'
        reactions: 'Reakcijas'
        voting: 'Balsošana'
        surveys: 'Aptaujas'
      trigger:
        7_days_after_invite_is_sent: '7 dienas pēc uzaicinājuma nosūtīšanas'
        7_days_before_the_project_changes_phase: '7 dienas pirms projekta izmaiņu fāzes'
        comment_is_deleted: 'Komentārs ir dzēsts'
        comment_is_flagged_as_spam: 'Komentārs ir atzīmēts kā surogātpasts'
        content_gets_flagged_as_innapropiate: 'Saturs tiek atzīmēts kā nepiedienīgs'
        initiative_resubmitted_for_review: 'Priekšlikums atkārtoti iesniegts pārskatīšanai'
        input_is_assigned: 'Ievade ir piešķirta'
        input_is_flagged_as_spam: 'Ievads tiek atzīmēts kā surogātpasts'
        input_is_published: 'Ievade tiek publicēta'
        input_is_updated: 'Ievade tiek atjaunināta'
        input_status_changes: 'Ieejas statusa izmaiņas'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Iekšējais komentārs tiek publicēts par lietotājam piešķirto ievadi'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Iekšējais komentārs tiek publicēts par ievadi projektā vai mapē, ko pārvalda lietotājs'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Iekšējais komentārs tiek publicēts par ievades lietotāja iekšēji komentēto ievadi'
        internal_comment_is_posted_on_idea_user_moderates: 'Iekšējais komentārs tiek publicēts par ieejas lietotāja moderē'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Iekšējais komentārs ir publicēts par nepiešķirtu ievades failu neapsaimniekotā projektā'
        project_review_request: 'Moderators pieprasīja projekta pārskatīšanu'
        project_review_state_change: 'Recenzents apstiprināja projektu'
        new_input_awaits_screening: 'Jauna informācija gaida skrīningu'
        new_input_is_published: 'Tiek publicēti jauni dati'
        new_proposal_is_posted: 'Ir publicēts jauns priekšlikums'
        project_phase_changes: 'Projekta fāzes izmaiņas'
        project_published: 'Publicēts projekts'
        proposal_gets_reported_as_spam: 'Par priekšlikumu tiek ziņots kā par surogātpastu'
        proposal_is_assigned_to_admin: 'Priekšlikums ir piešķirts admin'
        proposal_is_published: 'Priekšlikums ir publicēts'
        proposal_is_updated: 'Priekšlikums ir atjaunināts'
        proposal_is_upvoted_above_threshold: 'Priekšlikums ir atbalstīts virs sliekšņa'
        proposal_status_changes: 'Priekšlikuma statusa izmaiņas'
        registration_to_event: 'Reģistrācija pasākumam'
        survey_1_day_after_draft_saved: '1 diena pēc tam, kad lietotājs pēdējo reizi saglabājis aptaujas melnrakstu.'
        user_accepts_invitation_to_cosponsor_a_proposal: 'Lietotājs piekrīt uzaicinājumam līdzfinansēt priekšlikumu'
        user_comments: 'Lietotāju komentāri'
        user_comments_on_input: 'Lietotāju komentāri par ievades datiem'
        user_comments_on_proposal: 'Lietotāju komentāri par priekšlikumu'
        user_is_given_admin_rights: 'Lietotājam tiek piešķirtas administratora tiesības'
        user_is_given_folder_moderator_rights: 'Lietotājam tiek piešķirtas mapes moderatora tiesības'
        user_is_given_project_moderator_rights: 'Lietotājam tiek piešķirtas projekta moderatora tiesības'
        user_is_invited_to_cosponsor_a_proposal: 'Lietotājs ir aicināts līdzfinansēt priekšlikumu.'
        user_is_mentioned: 'Lietotājs ir minēts'
        user_is_mentioned_in_internal_comment: 'Lietotājs ir minēts iekšējā komentārā'
        user_registers_for_the_first_time: 'Lietotājs reģistrējas pirmo reizi'
        user_replies_to_comment: 'Lietotājs atbild uz komentāru'
        user_replies_to_internal_comment: 'Lietotājs atbild uz iekšēju komentāru'
        voting_1_day_after_last_votes: '1 diena pēc tam, kad lietotājs pēdējo reizi balsojis'
        voting_2_days_before_phase_closes: '2 dienas pirms balsošanas posma beigām'
        voting_basket_submitted: 'Tiek iesniegtas balsis'
        voting_phase_ended: 'Balsošanas posma beigas'
