en-GB:
  email_campaigns:
    campaign_type_description:
      "manual": Official messages
      "manual_project_participants": Official messages to project participants
      "admin_rights_received": Admin rights received
      "comment_deleted_by_admin": Deletion of my comment
      "comment_marked_as_spam": Comment spam report
      "comment_on_your_comment": A reply on my comment
      "comment_on_idea_you_follow": A comment on an idea that you follow
      "community_monitor_report": Community monitor report
      "cosponsor_of_your_idea": A user accepts my invitation to co-sponsor my proposal
      "event_registration_confirmation": Event registration confirmation
      "idea_marked_as_spam": Idea spam report
      "idea_published": Publication of my idea
      "invitation_to_cosponsor_idea": Invitation to co-sponsor a proposal
      "invite_received": Invitation
      "invite_reminder": Invitation reminder
      "internal_comment_on_idea_assigned_to_you": Internal comment on input assigned to me
      "internal_comment_on_idea_you_commented_internally_on": Internal comment on input I commented internally on
      "internal_comment_on_idea_you_moderate": Internal comment on input in project or folder I manage
      "internal_comment_on_unassigned_unmoderated_idea": Internal comment on unassigned input in unmanaged project
      "internal_comment_on_your_internal_comment": Internal comment on my internal comment
      "mention_in_official_feedback": Mention in an update
      "mention_in_internal_comment": Mention in an internal comment
      "new_comment_for_admin": New comment in a project I moderate
      "new_idea_for_admin": New idea in a project I moderate
      "official_feedback_on_idea_you_follow": Update on an input that you follow
      "password_reset": Password reset
      "project_moderation_rights_received": Project moderation rights received
      "project_folder_moderation_rights_received": Folder manager rights received
      "project_phase_started": New project phase
      "project_phase_upcoming": Upcoming new project phase
      "project_published": Project published
      "project_review_request": Project review request
      "project_review_state_change": Project approved
      "status_change_on_idea_you_follow": Status change of an input that you follow
      "survey_submitted": Survey submitted
      "threshold_reached_for_admin": Proposal reached the voting threshold
      "welcome": After registration
      "admin_digest": Weekly overview for admins
      "moderator_digest": Weekly overview for project managers
      "assignee_digest": Weekly overview of assigned ideas
      "user_digest": Weekly overview
      "voting_basket_submitted": Confirmation of voting
      "native_survey_not_submitted": Survey not submitted
      "voting_basket_not_submitted": Votes not submitted
      "voting_last_chance": Last chance to vote
      "voting_phase_started": New project phase with voting
      "voting_results": Voting results
      "your_input_in_screening": My input awaits screening
    general:
      by_author: 'by %{authorName}'
      author_wrote: '%{authorName} wrote:'
      cta_goto_idea: 'Go to this idea'
      cta_goto_input: 'Go to this input'
      cta_goto:
        idea: 'Go to this idea'
        question: 'Go to this question'
        contribution: 'Go to this contribution'
        project: 'Go to this project'
        issue: 'Go to this issue'
        option: 'Go to this option'
        proposal: 'Go to this proposal'
        petition: 'Go to this petition'
      cta_goto_your:
        idea: 'Go to your idea'
        question: 'Go to your question'
        contribution: 'Go to your contribution'
        project: 'Go to your project'
        issue: 'Go to your issue'
        option: 'Go to your option'
        proposal: 'Go to your proposal'
        petition: 'Go to your petition'
      cta_goto_proposal: 'Go to this proposal'
      cta_goto_project: 'Go to this project'
    schedules:
      weekly:
        "0": "Weekly, on Sundays at %{hourOfDay}"
        "1": "Weekly, on Mondays at %{hourOfDay}"
        "2": "Weekly, on Tuesdays at %{hourOfDay}"
        "3": "Weekly, on Wednesdays at %{hourOfDay}"
        "4": "Weekly, on Thursdays at %{hourOfDay}"
        "5": "Weekly, on Fridays at %{hourOfDay}"
        "6": "Weekly, on Saturdays at %{hourOfDay}"
      quarterly: "Quarterly, on the first day of the quarter"
    preview_data:
      first_name: 'Jane'
      last_name: 'Doe'
      display_name: 'Jane Doe'
      comment_body: 'This is an example comment used for previewing the content of emails. This is not real content.'
      idea_title: 'Example Idea'
    footer:
      "link_privacy_policy": "Privacy Policy"
      "link_terms_conditions": "Terms & Conditions"
      "link_unsubscribe": "Unsubscribe"
      "powered_by": "Powered by"
      "recipient_statement": "This e-mail was sent to you by Go Vocal on behalf of %{organizationName}, because you are a registered user of %{organizationLink}."
      "unsubscribe_statement": "You can %{unsubscribeLink} if you do not want to receive these e-mails in the future."
      "unsubscribe_text": "unsubscribe"
    follow:
      "unfollow_here": "You have received this notification because of an item that you follow. <a href=\"%{unfollow_url}\">You can unfollow it here.</a>"
    manual:
      preheader: 'You have mail from %{organizationName}'
    comment_deleted_by_admin:
      reason: 'The reason why your comment was deleted:'
      cta_view: 'View this idea'
      event_description: '%{organizationName} deleted the comment you wrote on an idea.'
      main_header: '%{organizationName} deleted your comment'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Your comment was considered to be inappropriate'
      irrelevant_reason: 'Your comment was considered to be irrelevant for that context'
      no_reason: 'No reason was specified'
      subject: 'Your comment has been deleted from the platform of %{organizationName}'
      preheader: 'Your comment has been deleted'
    admin_digest:
      subject: 'Your weekly admin report of %{time}'
      preheader: 'Admin digest of %{organizationName}'
      title_your_weekly_report: '%{firstName}, your weekly report'
      text_introduction: 'We''ve curated for you the input that has generated the most activity over the past week. Find out what''s happening on your platform!'
      cta_visit_the_platform: 'Visit your platform'
      new_users: 'New users'
      new_inputs: 'New inputs'
      new_comments: 'New comments'
      title_activity_past_week: 'Activity of the past week'
      title_no_activity_past_week: 'There was no activity in the past week'
      reached_threshold: 'Reached the threshold'
      yesterday_by_author: 'Yesterday by %{author}'
      today_by_author: 'Today by %{author}'
      x_days_ago_by_author: '%{x} days ago by %{author}'
    admin_rights_received:
      cta_manage_platform: 'Manage your platform'
      message_you_became_administrator: 'You have been granted administrator rights for the participation platform of %{organizationName}.'
      preheader: 'You were granted administrator rights for the participation platform of %{organizationName}'
      subject: 'You became an administrator on the platform of %{organizationName}'
      text_create_participatory_process: 'As an administrator, you can create and configure new participation projects. You can add new phases using the timeline. Each of these phases can have its own behaviour with regards to idea posting, commenting, and voting.'
      text_moderate_analyse_input: 'Once projects are launched, the first ideas will come in. You will receive weekly reports with all key activities so that you stay on top of things. The ideas overview will help you moderate the input and collaborate on processing it.'
      text_platform_setup: 'As an administrator you can set up your participation platform. Choose a logo, images and colors, write a personal message on your home page, send out invitations, define what you want to know of your users, ...'
      title_create_participatory_process: 'Design the participatory process'
      title_moderate_analyse_input: 'Moderate and analyse the input'
      title_platform_setup: 'Set your platform up'
      title_what_can_you_do_administrator: 'What can you do as an administrator?'
      title_you_became_administrator: 'You became an administrator'
    comment_marked_as_spam:
      by_author: 'by %{authorName}'
      commented: '%{authorName} commented:'
      cta_review_comment: 'Review comment'
      days_ago: '%{numberOfDays} days ago'
      event_description: 'The following comment posted on <strong>''%{post}''</strong> was reported:'
      inappropriate_content: 'The comment is inappropriate or offensive.'
      preheader: 'Act upon this comment reported as spam'
      reported_this_because: '%{reporterFirstName} reported this because:'
      subject: '%{organizationName}: %{firstName} %{lastName} reported this comment as spam'
      title_comment_spam_report: '%{firstName} %{lastName} reported this comment as spam'
      today: Today
      wrong_content: 'The comment is not relevant.'
      yesterday: Yesterday
    community_monitor_report:
      subject: 'A new community monitor report is available'
      title: 'A new community monitor report is available'
      text_introduction: 'A community monitor report has been created for the previous quarter. You can access it by clicking the button below and logging in.'
      cta_report_button: 'View report'
      report_name: 'Community monitor report'
    cosponsor_of_your_idea:
      cta_reply_to: 'View your proposal'
      event_description: 'Congratulations! %{cosponsorName} has accepted your invitation to co-sponsor your proposal.'
      main_header: '%{cosponsorName} has accepted your invitation to co-sponsor your proposal'
      subject: '%{cosponsorName} has accepted your invitation to co-sponsor your proposal'
      preheader: '%{cosponsorName} has accepted your invitation to co-sponsor your proposal'
    assignee_digest:
      subject: 'Ideas requiring your feedback: %{numberIdeas}'
      preheader: 'Assignee digest of %{organizationName}'
      title_your_weekly_report: '%{firstName}, citizen input is awaiting your feedback'
      cta_manage_your_input: 'Manage your input'
      x_inputs_need_your_feedback: 'inputs need your feedback'
      title_assignment_past_week: 'Latest ideas assigned to you'
      title_no_assignment_past_week: 'No new ideas assigned to you last week'
      yesterday_by_author: 'Yesterday by %{author}'
      today_by_author: 'Today by %{author}'
      x_days_ago_by_author: '%{x} days ago by %{author}'
      title_successful_past_week: 'Assigned to you that reached the threshold'
    idea_marked_as_spam:
      cta_review: 'Review'
      report_inappropriate_offensive_content: 'I find this content inappropriate or offensive.'
      report_not_an_idea: 'This content is not an idea and does not belong here.'
      subject: 'You have a spam report on the platform of %{organizationName}'
      preheader: 'Act upon this spam report'
      reported_this_because: '%{reporterFirstName} reported this because:'
      title_spam_report: '%{firstName} %{lastName} reported spam'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Co-sponsor this proposal'
      event_description: '%{authorName} has created a new proposal and would like you to co-sponsor it.'
      event_description_cosponsoring: 'Co-sponsoring a proposal means that <strong>your name will be displayed</strong> with the names of other co-sponsors of the proposal.'
      event_description_before_action: 'To see the proposal and accept the invitation, you must be logged in to your account.'
      event_description_action: 'Click below to read the proposal.'
      main_header: 'You have been invited to co-sponsor a proposal'
      subject: 'You have been invited to co-sponsor a proposal'
      preheader: 'You have been invited to co-sponsor the proposal of %{authorName}'
    invite_reminder:
      cta_accept_invitation: 'Accept your invitation'
      invitation_header: 'Your invitation is pending'
      preheader: '%{organizationName} sent you an invite to join their participation platform some days ago.'
      invitation_expiry_message: 'This invitation expires in approximately %{expiryDaysRemaining} days.'
      subject: 'Pending invitation for the participation platform of %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} wrote the following message:'
      cta_accept_invitation: 'Accept your invitation'
      invitation_header: 'You are invited!'
      invitation_header_message: '%{organizationName} invited you to their participation platform.'
      invitation_expiry_message: 'This invitation expires in %{expiryDays} days.'
      preheader: '%{organizationName} sent you an invite to join their participation platform.'
      subject: 'You are invited to join the platform of %{organizationName}'
    mention_in_comment:
      cta_reply_to: 'Reply to %{commentAuthor}'
      event_description: '%{commentAuthorFull} has mentioned you in his comment on the idea ''%{post}‘. Click the link below to enter the conversation with %{commentAuthor}'
      main_header: 'People are talking about you'
      subject: 'Someone mentioned you on the platform of %{organizationName}'
      preheader: '%{commentAuthor} mentioned you in a comment'
    mention_in_internal_comment:
      cta_reply_to: 'View comment by %{firstName}'
      event_description: '%{authorNameFull} mentioned you in an internal comment.'
      subject: '%{firstName} mentioned you in an internal comment.'
      main_header: '%{firstName} mentioned you in an internal comment.'
      preheader: '%{authorNameFull} mentioned you in an internal comment.'
    moderator_digest:
      subject: 'Weekly manager report of "%{project_title}"'
      preheader: 'Project manager digest of %{organizationName}'
      title_your_weekly_report: '%{firstName}, your weekly report'
      text_introduction: 'We''ve curated for you the input that has generated the most activity over the past week. Find out what''s happening with your project!'
      cta_manage: 'Manage your project'
      new_users: 'New users'
      new_ideas: 'New ideas'
      new_comments: 'New comments'
      title_inputs_past_week: 'New inputs in the past week'
      title_no_inputs_past_week: 'No new inputs in past week'
      title_threshold_reached: Threshold reached in the past week
      yesterday_by_author: 'Yesterday by %{author}'
      today_by_author: 'Today by %{author}'
      x_days_ago_by_author: '%{x} days ago by %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} commented:'
      cta_reply_to: 'View %{commentAuthor}''s comment'
      days_ago: '%{numberOfDays} days ago'
      event_description: '%{authorName} added a new comment on your platform.'
      main_header: '%{firstName}, a new comment has been posted on your platform'
      subject: 'A new comment has been posted on %{organizationName}''s platform'
      preheader: '%{authorName} left a comment'
      today: Today
      yesterday: Yesterday
    comment_on_idea_you_follow:
      cta_reply_to: 'Reply to %{commentAuthor}'
      event_description: '%{authorNameFull} placed a reaction on ''%{inputTitle}''. Click the button below to continue the conversation with %{authorName}.'
      main_header:
        idea: '%{authorName} commented on an idea you follow'
        question: '%{authorName} commented on a question you follow'
        contribution: '%{authorName} commented on a contribution you follow'
        project: '%{authorName} commented on a project you follow'
        issue: '%{authorName} commented on an issue you follow'
        option: '%{authorName} commented on an option you follow'
        proposal: '%{authorName} commented on a proposal you follow'
        petition: '%{authorName} commented on a petition you follow'
      subject: 'There''s a new comment on "%{input_title}"'
      preheader: '%{authorName} left a comment on an idea for %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'A new input has been published on your platform'
      event_description_publication: '%{authorName} has submitted a new input on your platform. Discover it now, give some feedback or change its status!'
      cta_publication: 'Give feedback to %{authorName}'
      main_header_prescreening: 'An input requires your review'
      event_description_prescreening: '%{authorName} has submitted a new input on your platform.'
      input_not_visible_prescreening: '<b>The input won''t be visible</b> until you modify its status.'
      cta_prescreening: 'Review the input'
      days_ago: '%{numberOfDays} days ago'
      preheader: '%{authorName} published a new idea on your platform'
      today: Today
      yesterday: Yesterday
    comment_on_your_comment:
      cta_reply_to: 'Reply to %{firstName}'
      event_description: '%{authorNameFull} wrote a reply to your comment on ''%{post}'' on the participation platform. Click the button below to continue the conversation with %{authorName}.'
      subject: 'You have received a reply on your comment on the platform of %{organizationName}'
      main_header: '%{authorName} replied to your comment'
      preheader: '%{authorName} replied to your comment on %{organizationName}''s platform'
      replied: '%{authorFirstName} replied:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'View comment by %{firstName}'
      event_description: '%{authorNameFull} commented on your internal comment.'
      subject: 'You have received a comment on your internal comment on ''%{post}'''
      main_header: 'You have received a comment on your internal comment on ''%{post}'''
      preheader: '%{authorName} replied to your internal comment on ''%{post}'''
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'View comment by %{firstName}'
      event_description: '%{authorNameFull} commented internally on an input assigned to you.'
      subject: 'New internal comment on ''%{post}'''
      main_header: 'New internal comment on ''%{post}'''
      preheader: 'New internal comment on ''%{post}'''
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'View comment by %{firstName}'
      event_description: '%{authorNameFull} commented internally on an input you commented internally on.'
      subject: 'New internal comment on ''%{post}'''
      main_header: 'New internal comment on ''%{post}'''
      preheader: 'New internal comment on ''%{post}'''
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'View comment by %{firstName}'
      event_description: '%{authorNameFull} commented internally on an input in a project or folder you manage.'
      subject: 'New internal comment on ''%{post}'''
      main_header: 'New internal comment on ''%{post}'''
      preheader: 'New internal comment on ''%{post}'''
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'View comment by %{firstName}'
      event_description: '%{authorNameFull} commented internally on an unassigned input in an unmanaged project.'
      subject: 'New internal comment on ''%{post}'''
      main_header: 'New internal comment on ''%{post}'''
      preheader: 'New internal comment on ''%{post}'''
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} gave an update on ''%{input_title}''.'
      header_title: 'There''s an update on "%{input_title}"'
      subject: 'Official feedback was posted on "%{input_title}"'
      preheader: 'There''s an update on an input you follow'
    mention_in_official_feedback:
      cta_reply_to: 'Reply to %{organizationName}'
      event_description: '%{organizationName} mentioned you in their feedback on the idea ''%{post}‘. Click the link below to enter the conversation with %{organizationName}'
      main_header: 'You have been mentioned'
      subject: '%{organizationName} mentioned you in their feedback'
      preheader: '%{commentAuthor} mentioned you in feedback'
    project_moderation_rights_received:
      cta_manage_project: 'Manage this project'
      message_you_became_moderator: 'An adminstrator of the participation platform of %{organizationName} just made you project manager of the following project:'
      no_ideas: 'No ideas yet'
      preheader: 'An adminstrator of the participation platform of %{organizationName} just made you a project manager of the following project'
      subject: 'You became a project manager on the platform of %{organizationName}'
      text_design_participatory_process: 'As a project manager, you can configure how users interact within your project. You can add new phases using the timeline. Each of these phases can have its own behaviour with regards to idea posting, commenting, and voting.'
      text_moderate_analyse_input: 'Once the project is launched, the first ideas will come in. You will receive weekly reports with all key activities so that you stay on top of things. The ideas overview in your project manager view will help you understand which ideas got the most upvotes and downvotes.'
      text_share_project_information: 'In order to increase the quality of the ideas you''re getting, it is key to share sufficient information: add a project description, attach images (incl. sketches and plans), and communicate all related events going on. Remember: good information precedes good participation!'
      title_design_participatory_process: 'Design the participatory process'
      title_moderate_analyse_input: 'Moderate and analyse the input'
      title_share_project_information: 'Provide project information'
      title_what_can_you_do_moderator: 'What can you do as a project manager?'
      title_you_became_moderator: 'You became a project manager'
      x_ideas: '%{numberOfIdeas} ideas'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'View this folder'
      message_added_as_folderadmin: 'You''ve been given folder manager rights on %{organizationName}''s participation platform for the following folder:'
      no_projects: 'No projects yet'
      preheader: 'An administrator of the participation platform of %{organizationName} just made you a manager of the following folder'
      subject: 'You became a project folder manager on the participation platform of %{organizationName}'
      text_manage_folder: 'A folder is way to organize several participation projects together. As a folder manager, you can edit the folder and folder description and create new projects (to delete projects, contact your platform administrator). You will also have project management rights over all projects within the folder, allowing you to edit the projects, manage the inputs and email participants.'
      text_moderate_analyse_input: 'Once the projects are launched, the first inputs will start coming in. You''ll receive weekly reports with the key activities so that you can stay on top of what’s happening. The Input Manager in your ''Manage Platform'' panel allows you to see and manage the input, including assigning statuses and responding to posts and comments.'
      text_design_participatory_process: 'You can manage the different participation projects within your folder - configure the participation method, add a project description, attach images, and communicate related events. You can also manage how participants interact with your projects, including setting access rights and configuring the posting, voting and commenting settings.'
      title_design_participatory_process: 'Design the participatory process'
      title_moderate_analyse_input: 'Moderate and analyse the input'
      title_manage_folder: 'Manage the folder settings and create new projects.'
      title_what_can_you_do_folderadmin: 'What can you do as a folder manager?'
      title_added_as_folderadmin: 'You''ve been added as a folder manager'
      x_projects: '%{numberOfProjects} projects'
    project_phase_started:
      cta_view_phase: 'Discover this new phase'
      event_description: 'This project entered a new phase on the platform of %{organizationName}. Click on the link below to learn more!'
      main_header: 'A new phase started for project ''%{projectName}'''
      subtitle: 'About ''%{projectName}'''
      new_phase: 'This project entered the phase ''%{phaseTitle}'''
      subject: '%{projectName} entered a new phase'
      preheader: 'A new phase has been started for %{projectName}'
    project_phase_upcoming:
      cta_view_phase: 'Set this new phase up'
      event_description: 'The project ''%{projectName}'' will be entering a new phase soon. Make sure everything is set up for this phase: Is there an adequate description? Are selected ideas transferred to this phase? Do you want to inform your citizens on the specifics of this phase through a custom email campaign?'
      main_header: '%{firstName}, a project will enter a new phase soon'
      subtitle: 'About ''%{projectName}'''
      new_phase: 'The project will enter the phase ''%{phaseTitle}'''
      subject: 'Get everything set up for the new phase of %{projectName}'
      preheader: 'A new phase will start soon for %{projectName}'
    project_published:
      subject: 'A new project was published on the platform of %{organizationName}'
      header_title: 'A new project was published'
      header_message: 'The participation platform of %{organizationName} just published the following project:'
      preheader: 'A new project was published'
    project_review_request:
      subject: 'Review request: A project is waiting for approval.'
      header: '%{requesterName} has invited you to review the project "%{projectTitle}"'
      header_message: "Currently, the project is in draft mode and isn't visible to users. Once you've reviewed and approved it, the moderator will be able to publish it."
      cta_review_project: "Review the project"
    project_review_state_change:
      subject: '"%{projectTitle}" has been approved'
      header: '%{reviewerName} approved the project "%{projectTitle}"'
      header_message: "The project is now ready to go live. You can publish it whenever you're ready!"
      cta_go_to_project: "Go to project settings"
    status_change_on_idea_you_follow:
      status_change: 'The new status of this input is ''%{status}'''
      header_message: '%{organizationName} updated the status of the input ''%{input_title}'' on their digital participation platform.'
      header_title: 'An input you follow has a new status'
      subject: 'The status of "%{input_title}" has changed'
      preheader: 'An input you follow has a new status'
    user_digest:
      subject: "Your activity on the participation platform of %{organizationName}"
      commented: "%{authorFirstName} commented:"
      preheader: "Weekly overview of %{organizationName}"
      title_your_weekly_report: "Discover what happened last week"
      intro_text: "Here's a summary of what happened on the participation platform of %{organizationName}."
      cta_go_to_the_platform: "Go to the platform"
      title_no_activity_past_week: "No activity in the past week"
      successful_proposals_title: "Reached the threshold"
      successful_proposals_text: "These proposals have gotten enough support to move to the next stage! Click on the proposal to learn more about what happens next."
      today_by_author: "Today by %{author}"
      yesterday_by_author: "Yesterday by %{author}"
      x_days_ago_by_author: "%{x} days ago by %{author}"
      trending_title: "Trending"
      trending_text: "Interested in what’s been happening on the platform? Here are the three most trending contributions and what people are saying about them."
      no_notifications: "No notifications"
      one_notification: "1 notification"
      multiple_notifications: "%{notifCount} notifications"
      no_unread_notifications: "You don't have any unread notifications. Visit the platform to contribute your input and generate new notifications!"
      unread_notifications: "You have unread notifications. Visit the platform to discover what's happening!"
    threshold_reached_for_admin:
      cta_process_initiative: 'Take this initiative to the next steps'
      main_header: 'An initiative reached the voting threshold!'
      subject: '"%{input_title}" reached the voting threshold'
      preheader: 'Make sure to take the next steps'
    welcome:
      cta_join_platform: 'Discover the platform'
      subject: 'Welcome to the platform of %{organizationName}'
      main_header: Welcome!
      message_welcome: 'Congratulations, you successfully signed up on the participation platform of %{organizationName}. You can now discover the platform and make your voice heard. You can also add a profile picture and a short description to tell others who you are.'
      preheader: 'Here''s what you can do on the platform of %{organizationName}'
    idea_published:
      subject:
        idea: 'Your idea has been published'
        question: 'Your question has been published'
        contribution: 'Your contribution has been published'
        project: 'Your project has been published'
        issue: 'Your issue has been published'
        option: 'Your option has been published'
        proposal: 'Your proposal has been published'
        petition: 'Your petition has been published'
      main_header: 'You posted "%{input_title}"'
      header_message: 'Let''s make sure it gets read.'
      message_get_votes: 'Reach more people with your idea:'
      action_published_idea: 'Published idea'
      action_add_image: '%{addImageLink} to increase visibility'
      add_image: 'Add an image'
      action_share_fb: 'Let your friends know on %{fbLink}'
      action_share_twitter: 'Inform your followers on %{twitterLink}'
      action_send_email: 'Send your contacts an %{sendEmailLink}'
      send_email: email
      action_share_link: 'Share it via any channel by copying the %{link}'
      link: link
      preheader: '%{firstName}, congratulations on posting your idea on the platform of %{organizationName}. Now gather support.'
    your_input_in_screening:
      main_header:
        idea: 'Your idea is in "%{prescreening_status_title}"'
        question: 'Your question is in "%{prescreening_status_title}"'
        contribution: 'Your contribution is in "%{prescreening_status_title}"'
        project: 'Your project is in "%{prescreening_status_title}"'
        issue: 'Your issue is in "%{prescreening_status_title}"'
        option: 'Your option is in "%{prescreening_status_title}"'
        proposal: 'Your proposal is in "%{prescreening_status_title}"'
        petition: 'Your petition is in "%{prescreening_status_title}"'
      message: '"%{input_title}" will become visible to others once it has been reviewed and approved.'
      subject: '"%{input_title}" is almost published'
      preheader: 'It''s currently in %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}: You voted successfully'
      preheader: 'You voted successfully on the participation platform of %{organizationName}'
      title_basket_submitted: 'You voted successfully'
      event_description: 'Thanks for participating. Your votes have been recorded. Visit the platform of %{organizationName} to see and manage your votes.'
      cta_see_votes_submitted: 'See votes submitted'
      cta_message: 'Click the button below to participate'
    native_survey_not_submitted:
      subject: '%{organizationName}: Almost there! Submit your answers'
      preheader: 'You didn''t complete your survey response on the participation platform of %{organizationName}'
      title_native_survey_not_submitted: 'Almost there! Submit your answers'
      body_native_survey_not_submitted: 'You started sharing your answers on %{phaseTitle} but didn''t submit them. Submissions will close on %{phaseEndDate}. Click on the button below to continue where you left off.'
      body_native_survey_not_submitted_no_date: 'You started sharing your answers on %{phaseTitle} but didn''t submit them. Click on the button below to continue where you left off.'
      cta_complete_your_survey_response: 'Resume your survey response'
    voting_basket_not_submitted:
      subject: '%{organizationName}: You didn''t submit your votes'
      preheader: 'You didn''t submit your votes on the participation platform of %{organizationName}'
      title_basket_not_submitted: 'You didn''t submit your votes'
      event_description: 'You selected a few options for %{contextTitle} but you didn''t submit your selection.'
      cta_view_options_and_vote: 'View options and vote'
      cta_message: 'Click the button below to submit your selected options'
    voting_last_chance:
      subject: '%{organizationName}: Last chance to vote for %{phaseTitle}'
      preheader: 'Last chance to vote for %{phaseTitle} on the participation platform of %{organizationName}'
      title_last_chance: 'Last chance to vote for %{phaseTitle}'
      body_1: 'The voting phase for the %{projectTitle} project is coming to a close tomorrow, at midnight.'
      body_2: 'Time is running out, and we noticed that you haven''t cast your vote yet! Act now by simply clicking the button below to participate.'
      body_3: 'By doing so, you''ll gain access to a range of options and have the chance to provide your input, which is crucial in deciding the future of this project.'
      cta_vote: 'Vote'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} vote results revealed!'
      preheader: '%{phaseTitle} vote results revealed on the participation platform of %{organizationName}'
      title_results: '%{phaseTitle} vote results revealed!'
      body_1: 'Results are in!'
      body_2: 'The results of the %{phaseTitle} vote in the %{organizationName} platform have been published!'
      body_3: 'We encourage you to review the results and stay tuned for further updates on the next steps.'
      cta_see_results: 'See results in the platform'
    event_registration_confirmation:
      subject: "You're in! Your registration for \"%{eventTitle}\" is confirmed"
      preheader: "%{firstName}, thanks for registering for %{eventTitle}"
      header_message: "%{firstName}, thanks for registering for"
      event_details:
        labels:
          date: 'Date'
          location: 'Location'
          online_link: 'Online link'
          description: 'Description'
          project: 'Project'
      cta_go_to_event: 'View the event'
      cta_add_to_calendar: 'Add to your calendar'
    voting_phase_started:
      subject: '%{organizationName}: The voting phase started for %{projectName}'
      preheader: 'The voting phase started for %{projectName} on the participation platform of %{organizationName}'
      event_description: 'The project "%{projectName}" is asking you to vote between %{numIdeas} options:'
      cta_message: 'Click the button below to participate'
      cta_vote: 'Go to the platform to vote'
    survey_submitted:
      subject: '%{organizationName}: Thank you for your response! 🎉'
      preheader: 'Here are the details of your submission.'
      main_header: 'Thank you for sharing your thoughts on "%{projectName}"!'
      your_input_submitted: 'Your input for "%{projectName}" has been submitted successfully.'
      if_you_would_like_to_review: 'If you would like to review your submission, you can download your responses below.'
      your_submission_has_id: 'Your submission has the following unique identifier:'
      you_can_use_this_id: 'You can use this identifier to contact the platform administrators in case you want your submission to be removed.'
      download_responses: 'Download your responses'
    admin_labels:
      recipient_role:
        admins: 'To admins'
        admins_and_managers: 'To admins & managers'
        managers: 'To managers'
        project_participants: 'To project participants'
        registered_users: 'To registered users'
      recipient_segment:
        admins: 'Admins'
        admins_and_managers: 'Admins & managers'
        admins_and_managers_assigned_to_the_input: 'Admins & managers assigned to the input'
        admins_and_managers_managing_the_project: 'Admins & managers managing the project'
        admins_assigned_to_a_proposal: 'Admins assigned to a proposal'
        all_users: 'All users'
        all_users_who_uploaded_proposals: 'All users who uploaded proposals'
        managers: 'Managers'
        managers_managing_the_project: 'Managers managing the project'
        new_attendee: 'Newly registered user'
        project_reviewers: 'Project reviewers and folder managers'
        project_review_requester: 'User who requested the project review'
        user_who_commented: 'User who commented'
        user_who_is_invited_to_cosponsor_a_proposal: 'User who is invited to co-sponsor a proposal'
        user_who_is_mentioned: 'User who is mentioned'
        user_who_is_receiving_admin_rights: 'User who is receiving admin rights'
        user_who_is_receiving_folder_moderator_rights: 'User who is receiving folder moderator rights'
        user_who_is_receiving_project_moderator_rights: 'User who is receiving project moderator rights'
        user_who_published_the_input: 'User who published the input'
        user_who_published_the_proposal: 'User who published the proposal'
        user_who_registers: 'User who registers'
        user_who_submitted_the_input: 'User who submitted the input'
        user_who_voted: 'User who voted'
        user_who_was_invited: 'User who was invited'
        user_with_unsubmitted_survey: 'User who has started but not submitted their survey'
        user_with_unsubmitted_votes: 'User who has not submitted their votes'
        users_who_engaged_but_not_voted: 'Users who engaged with the project but have not voted'
        users_who_engaged_with_the_project: 'Users who engaged with the project'
        users_who_follow_the_input: 'Users who follow the input'
        users_who_follow_the_project: 'Users who follow the project'
        users_who_follow_the_proposal: 'Users who follow the proposal'
      content_type:
        comments: 'Comments'
        content_moderation: 'Content moderation'
        events: 'Events'
        general: 'General'
        inputs: 'Inputs'
        internal_comments: 'Internal comments'
        permissions: 'Permissions'
        projects: 'Projects'
        proposals: 'Proposals'
        reactions: 'Reactions'
        voting: 'Voting'
        surveys: 'Surveys'
      trigger:
        7_days_after_invite_is_sent: '7 days after invite is sent'
        7_days_before_the_project_changes_phase: '7 days before the project changes phase'
        comment_is_deleted: 'Comment is deleted'
        comment_is_flagged_as_spam: 'Comment is flagged as spam'
        content_gets_flagged_as_innapropiate: 'Content gets flagged as innapropiate'
        initiative_resubmitted_for_review: 'Proposal resubmitted for review'
        input_is_assigned: 'Input is assigned'
        input_is_flagged_as_spam: 'Input is flagged as spam'
        input_is_published: 'Input is published'
        input_is_updated: 'Input is updated'
        input_status_changes: 'Input status changes'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Internal comment is posted on input assigned to user'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Internal comment is posted on input in project or folder user manages'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Internal comment is posted on input user commented internally on'
        internal_comment_is_posted_on_idea_user_moderates: 'Internal comment is posted on input user moderates'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Internal comment is posted on unassigned input in unmanaged project'
        project_review_request: 'Moderator requested a project review'
        project_review_state_change: 'Reviewer approved the project'
        new_input_awaits_screening: 'New input awaits screening'
        new_input_is_published: 'New input is published'
        new_proposal_is_posted: 'New proposal is posted'
        project_phase_changes: 'Project phase changes'
        project_published: 'Project published'
        proposal_gets_reported_as_spam: 'Proposal gets reported as spam'
        proposal_is_assigned_to_admin: 'Proposal is assigned to admin'
        proposal_is_published: 'Proposal is published'
        proposal_is_updated: 'Proposal is updated'
        proposal_is_upvoted_above_threshold: 'Proposal is upvoted above threshold'
        proposal_status_changes: 'Proposal status changes'
        registration_to_event: 'Registration to an event'
        survey_1_day_after_draft_saved: '1 day after user last saved the survey in draft'
        user_accepts_invitation_to_cosponsor_a_proposal: 'User accepts invitation to co-sponsor a proposal'
        user_comments: 'User comments'
        user_comments_on_input: 'User comments on input'
        user_comments_on_proposal: 'User comments on proposal'
        user_is_given_admin_rights: 'User is given admin rights'
        user_is_given_folder_moderator_rights: 'User is given folder moderator rights'
        user_is_given_project_moderator_rights: 'User is given project moderator rights'
        user_is_invited_to_cosponsor_a_proposal: 'User is invited to co-sponsor a proposal'
        user_is_mentioned: 'User is mentioned'
        user_is_mentioned_in_internal_comment: 'User is mentioned in internal comment'
        user_registers_for_the_first_time: 'User registers for the first time'
        user_replies_to_comment: 'User replies to comment'
        user_replies_to_internal_comment: 'User replies to internal comment'
        voting_1_day_after_last_votes: '1 day after user last voted'
        voting_2_days_before_phase_closes: '2 days before voting phase closes'
        voting_basket_submitted: 'Votes are submitted'
        voting_phase_ended: 'Voting phase ended'
