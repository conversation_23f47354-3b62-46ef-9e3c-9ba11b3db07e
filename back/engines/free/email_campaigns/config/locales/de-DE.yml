de:
  email_campaigns:
    campaign_type_description:
      "manual": Offizielle Mitteilungen
      "manual_project_participants": E-<PERSON>s an Projektteilnehmende
      "admin_rights_received": Admin-<PERSON><PERSON><PERSON> erhalten
      "comment_deleted_by_admin": Löschung eines Kommentars
      "comment_marked_as_spam": <PERSON><PERSON><PERSON><PERSON> wird als Spam markiert
      "comment_on_your_comment": Antwort auf einen Kommentar
      "comment_on_idea_you_follow": Ein Kommentar zu einer Idee, der Sie folgen
      "community_monitor_report": Gemeinschaftspuls-Bericht
      "cosponsor_of_your_idea": Nutzer*in nimmt meine Einladung an, meinen Vorschlag zu unterstützen
      "event_registration_confirmation": Anmeldebestätigung für die Veranstaltung
      "idea_marked_as_spam": Beitrag wird als Spam gekennzeichnet
      "idea_published": Veröffentlichung des Beitrags
      "invitation_to_cosponsor_idea": Einladung zur Unterstützung eines Vorschlags
      "invite_received": <PERSON><PERSON><PERSON>
      "invite_reminder": Einladungserinnerung
      "internal_comment_on_idea_assigned_to_you": Interner Kommentar zu einem mir zugewiesenen Beitrag
      "internal_comment_on_idea_you_commented_internally_on": Interner Kommentar zu einem Beitrag, den ich intern kommentiert habe
      "internal_comment_on_idea_you_moderate": Interner Kommentar zu einem Beitrag in einem von mir verwalteten Projekt oder Ordner
      "internal_comment_on_unassigned_unmoderated_idea": Interner Kommentar zu nicht zugewiesenem Beitrag in nicht verwaltetem Projekt
      "internal_comment_on_your_internal_comment": Interner Kommentar zu meinem internen Kommentar
      "mention_in_official_feedback": Erwähnung in einem offiziellen Update
      "mention_in_internal_comment": Erwähnung in einem internen Kommentar
      "new_comment_for_admin": Neuer Kommentar zu einem Projekt, das ich moderiere
      "new_idea_for_admin": Neuer Beitrag zu einem Projekt, das moderiert wird
      "official_feedback_on_idea_you_follow": Offizielles Update zu einer Idee, der Sie folgen
      "password_reset": Zurücksetzen des Passworts
      "project_moderation_rights_received": Projektmanagementrechte erhalten
      "project_folder_moderation_rights_received": Ordnermanagementrechte erhalten
      "project_phase_started": Neue Projektphase
      "project_phase_upcoming": Bevorstehende neue Projektphase
      "project_published": Projekt veröffentlicht
      "project_review_request": Antrag auf Projektfreigabe
      "project_review_state_change": Projekt freigegeben
      "status_change_on_idea_you_follow": Statusänderung einer Idee, der Sie folgen
      "survey_submitted": Umfrage eingereicht
      "threshold_reached_for_admin": Vorschlag hat die Abstimmungsschwelle erreicht
      "welcome": Nach der Registrierung
      "admin_digest": Wöchentliche Übersicht für Admins
      "moderator_digest": Wöchentliche Übersicht für Ordner- und Projektmanager*innen
      "assignee_digest": Wöchentliche Übersicht über die zugewiesenen Beiträge
      "user_digest": Wöchentliche Übersicht
      "voting_basket_submitted": Bestätigung der Abstimmung
      "native_survey_not_submitted": Umfrage nicht eingereicht
      "voting_basket_not_submitted": Nicht abgegebene Stimmen
      "voting_last_chance": Letzte Chance zur Abstimmung
      "voting_phase_started": Neue Projektphase mit Abstimmung
      "voting_results": Abstimmungsergebnisse
      "your_input_in_screening": Mein Beitrag wartet auf Überprüfung
    general:
      by_author: 'von %{authorName}'
      author_wrote: '%{authorName} schrieb:'
      cta_goto_idea: 'Zum Beitrag'
      cta_goto_input: 'Zum Beitrag'
      cta_goto:
        idea: 'Zur Idee'
        question: 'Zur Frage'
        contribution: 'Zum Beitrag'
        project: 'Zum Projekt'
        issue: 'Zum Problem'
        option: 'Zur Option'
        proposal: 'Zum Vorschlag'
        petition: 'Zur Petition'
      cta_goto_your:
        idea: 'Zur Idee'
        question: 'Zur Frage'
        contribution: 'Zum Beitrag'
        project: 'Zum Projekt'
        issue: 'Zum Problem'
        option: 'Zur Option'
        proposal: 'Zum Vorschlag'
        petition: 'Zur Petition'
      cta_goto_proposal: 'Zum Vorschlag'
      cta_goto_project: 'Zum Projekt'
    schedules:
      weekly:
        "0": "Wöchentlich, sonntags um %{hourOfDay}"
        "1": "Wöchentlich, montags um %{hourOfDay}"
        "2": "Wöchentlich, dienstags um %{hourOfDay}"
        "3": "Wöchentlich, mittwochs um %{hourOfDay}"
        "4": "Wöchentlich, donnerstags um %{hourOfDay}"
        "5": "Wöchentlich, freitags um %{hourOfDay}"
        "6": "Wöchentlich, samstags um %{hourOfDay}"
      quarterly: "Vierteljährlich, am ersten Tag des Quartals"
    preview_data:
      first_name: 'Erika'
      last_name: 'Mustermann'
      display_name: 'Erika Mustermann'
      comment_body: 'Dies ist ein Beispielkommentar, der für die Vorschau des Inhalts von E-Mails verwendet wird. Dies ist kein echter Inhalt.'
      idea_title: 'Beispiel-Idee'
    footer:
      "link_privacy_policy": "Impressum & Datenschutz"
      "link_terms_conditions": "Nutzungsbedingungen"
      "link_unsubscribe": "Abbestellen"
      "powered_by": "Ermöglicht durch"
      "recipient_statement": "Diese E-Mail wurde Ihnen von Go Vocal im Namen von %{organizationName} zugesandt, da Sie ein*e registrierte*r Nutzer*in von %{organizationLink} sind."
      "unsubscribe_statement": "Sie können %{unsubscribeLink} wählen, wenn Sie diese E-Mails in Zukunft nicht mehr erhalten möchten."
      "unsubscribe_text": "abbestellen"
    follow:
      "unfollow_here": "Sie haben diese Benachrichtigung wegen eines Elementes erhalten, dem Sie folgen. <a href=\"%{unfollow_url}\">Sie können hier aufhören diesem zu folgen.</a>"
    manual:
      preheader: 'Sie haben Post von %{organizationName}'
    comment_deleted_by_admin:
      reason: 'Der Grund, warum Ihr Kommentar gelöscht wurde:'
      cta_view: 'Zur Plattform'
      event_description: '%{organizationName} den Kommentar, den Sie zu einer Idee geschrieben haben, gelöscht.'
      main_header: '%{organizationName} hat Ihren Kommentar gelöscht'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Ihr Kommentar wurde als unangemessen eingeschätzt'
      irrelevant_reason: 'Ihr Kommentar wurde in diesem Zusammenhang als irrelevant betrachtet'
      no_reason: 'Es wurde kein Grund angegeben'
      subject: 'Ihr Kommentar wurde von der Plattform von %{organizationName} gelöscht'
      preheader: 'Ihr Kommentar wurde gelöscht'
    admin_digest:
      subject: 'Ihr wöchentlicher Admin-Bericht vom %{time}'
      preheader: 'Adminzusammenfassung für %{organizationName}'
      title_your_weekly_report: '%{firstName}, Ihr wöchentlicher Bericht'
      text_introduction: 'Wir haben für Sie die Beiträge zusammengestellt, die in der vergangenen Woche die meiste Aktivität hatten. Finden Sie heraus, was auf Ihrer Plattform passiert ist!'
      cta_visit_the_platform: 'Zur Plattform'
      new_users: 'Neue BenutzerInnen'
      new_inputs: 'Neue Beiträge'
      new_comments: 'Neue Kommentare'
      title_activity_past_week: 'Aktivitäten der letzten Woche'
      title_no_activity_past_week: 'In der letzten Woche gab es keine Aktivität'
      reached_threshold: 'Schwelle erreicht'
      yesterday_by_author: 'Gestern von %{author}'
      today_by_author: 'Heute von %{author}'
      x_days_ago_by_author: 'Vor %{x} Tagen von %{author}'
    admin_rights_received:
      cta_manage_platform: 'Verwalten Sie Ihre Plattform'
      message_you_became_administrator: 'Ihnen wurden ein Admin-Zugang für die Beteiligungsplattform %{organizationName} gewährt.'
      preheader: 'Ihnen wurde ein Admin-Zugang für die Beteiligungsplattform %{organizationName} gewährt'
      subject: 'Sie wurden soeben Admin auf der Plattform von %{organizationName}'
      text_create_participatory_process: 'Als Admin können Sie neue Beteiligungsprojekte erstellen und konfigurieren. Über die Zeitleiste können Sie neue Phasen hinzufügen. Jede dieser Phasen kann ihr eigenes Verhalten in Bezug auf das Posten von Ideen, das Kommentieren und das Abstimmen haben.'
      text_moderate_analyse_input: 'Sobald die Projekte angelaufen sind, werden die ersten Ideen eintreffen. Sie erhalten wöchentliche Berichte mit allen wichtigen Aktivitäten, damit Sie den Überblick behalten. Die Ideenübersicht hilft Ihnen, den Input zu moderieren und zu bearbeiten.'
      text_platform_setup: 'Als Admin können Sie Ihre Beteiligungsplattform einrichten. Wählen Sie ein Logo, Bilder und Farben, schreiben Sie eine persönliche Nachricht auf Ihre Homepage, verschicken Sie Einladungen, legen Sie fest, was Sie von Ihren Nutzer:innen wissen wollen, ...'
      title_create_participatory_process: 'Gestalten Sie den partizipativen Prozess'
      title_moderate_analyse_input: 'Moderieren und Analysieren Sie Beiträge'
      title_platform_setup: 'Richten Sie Ihre Plattform ein'
      title_what_can_you_do_administrator: 'Was können Sie als Admin tun?'
      title_you_became_administrator: 'Sie sind nun Admin'
    comment_marked_as_spam:
      by_author: 'von %{authorName}'
      commented: '%{authorName} hat kommentiert:'
      cta_review_comment: 'Kommentar überprüfen'
      days_ago: 'vor %{numberOfDays} Tagen'
      event_description: 'Der folgende Kommentar auf <strong>''%{post}''</strong> wurde gemeldet:'
      inappropriate_content: 'Der Kommentar ist unangemessen oder anstößig.'
      preheader: 'Reagiere auf diesen Kommentar, der als Spam gemeldet wurde'
      reported_this_because: '%{reporterFirstName} hat dies gemeldet, weil:'
      subject: '%{organizationName}: %{firstName} - %{lastName} hat diesen Kommentar als Spam gemeldet'
      title_comment_spam_report: '%{firstName}: %{lastName} - hat diesen Kommentar als Spam gemeldet'
      today: Heute
      wrong_content: 'Der Kommentar ist nicht relevant.'
      yesterday: Gestern
    community_monitor_report:
      subject: 'Ein neuer Gemeinschaftspuls-Bericht ist verfügbar'
      title: 'Ein neuer Gemeinschaftspuls-Bericht ist verfügbar'
      text_introduction: 'Ein Gemeinschaftspuls-Bericht wurde für das letzte Quartal erstellt. Sie können darauf zugreifen, indem Sie auf den Button unten klicken und sich anmelden.'
      cta_report_button: 'Bericht ansehen'
      report_name: 'Gemeinschaftspuls-Bericht'
    cosponsor_of_your_idea:
      cta_reply_to: 'Eigenen Vorschlag ansehen'
      event_description: 'Herzlichen Glückwunsch! %{cosponsorName} hat deine Einladung zum Unterstützen deines Vorschlags angenommen.'
      main_header: '%{cosponsorName} hat die Einladung angenommen, Ihren Vorschlag mit zu unterstützen'
      subject: '%{cosponsorName} hat die Einladung angenommen, Ihren Vorschlag mit zu unterstützen'
      preheader: '%{cosponsorName} hat die Einladung angenommen, Ihren Vorschlag mit zu unterstützen'
    assignee_digest:
      subject: 'Ideen, die Ihr Feedback erfordern: %{numberIdeas}'
      preheader: 'Übersicht der Beauftragten von %{organizationName}'
      title_your_weekly_report: '%{firstName}, Ideen warten auf Ihr Feedback'
      cta_manage_your_input: 'Beiträge verwalten'
      x_inputs_need_your_feedback: 'Beiträge, die Feedback benötigen'
      title_assignment_past_week: 'Letzte Ihnen zugewiesene Ideen'
      title_no_assignment_past_week: 'Ihnen wurden letzte Woche keine neuen Ideen zugewiesen'
      yesterday_by_author: 'Gestern von %{author}'
      today_by_author: 'Heute von %{author}'
      x_days_ago_by_author: 'Vor %{x} Tagen von %{author}'
      title_successful_past_week: 'Ihnen zugewiesen, die den Schwellenwert erreicht haben'
    idea_marked_as_spam:
      cta_review: 'Überprüfung'
      report_inappropriate_offensive_content: 'Ich finde diesen Inhalt unangemessen oder anstößig.'
      report_not_an_idea: 'Dieser Inhalt ist keine Idee und gehört nicht hierhin.'
      subject: 'Sie haben eine Spam-Meldung auf der Plattform von %{organizationName}'
      preheader: 'Handeln Sie auf Basis dieses Spam-Berichts'
      reported_this_because: '%{reporterFirstName} meldete dies, weil:'
      title_spam_report: '%{firstName} %{lastName} hat Spam gemeldet'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Unterstützen Sie diesen Vorschlag'
      event_description: '%{authorName} hat einen neuen Vorschlag erstellt und möchte, dass Sie ihn unterstützen.'
      event_description_cosponsoring: 'Unterstützer*in eines Vorschlags zu sein bedeutet <strong>, dass Ihr Name zusammen mit den Namen der anderen Unterstützer*innen des Vorschlags angezeigt wird</strong>.'
      event_description_before_action: 'Um den Vorschlag zu sehen und die Einladung anzunehmen, müssen Sie in Ihrem Konto eingeloggt sein.'
      event_description_action: 'Klicken Sie auf den Button unten, um den Vorschlag zu lesen.'
      main_header: 'Sie wurden eingeladen, einen Vorschlag zu unterstützen'
      subject: 'Sie wurden eingeladen, einen Vorschlag zu unterstützen'
      preheader: 'Sie wurden eingeladen, den Vorschlag von %{authorName} zu unterstützen'
    invite_reminder:
      cta_accept_invitation: 'Einladung annehmen'
      invitation_header: 'Ihre Einladung steht noch aus'
      preheader: '%{organizationName} hat Ihnen vor einigen Tagen eine Einladung zur Teilnahme an ihrer Beteiligungsplattform geschickt.'
      invitation_expiry_message: 'Diese Einladung läuft in etwa %{expiryDaysRemaining} Tagen ab.'
      subject: 'Ausstehende Einladung für die Beteiligungsplattform %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} hat die folgende Nachricht geschrieben:'
      cta_accept_invitation: 'Einladung annehmen'
      invitation_header: 'Sie sind eingeladen!'
      invitation_header_message: '%{organizationName} hat Sie auf ihre Beteiligungsplattform eingeladen.'
      invitation_expiry_message: 'Diese Einladung läuft in %{expiryDays} Tagen ab.'
      preheader: '%{organizationName} hat Ihnen eine Einladung zur Teilnahme an ihrer Beteiligungsplattform geschickt.'
      subject: 'Sie sind eingeladen worden um an der Plattform von %{organizationName} teilzunehmen'
    mention_in_comment:
      cta_reply_to: 'Antworten Sie %{commentAuthor}'
      event_description: '%{commentAuthorFull} hat dich in seinem Kommentar zur Idee ''%{post}'' erwähnt. Klicke auf den untenstehenden Link, um mit %{commentAuthor} zu interagieren'
      main_header: 'Du wurdest von jemandem erwähnt'
      subject: 'Jemand hat Sie auf der Plattform von %{organizationName} erwähnt'
      preheader: '%{commentAuthor} hat Sie in einem Kommentar erwähnt'
    mention_in_internal_comment:
      cta_reply_to: 'Kommentar anzeigen von %{firstName}'
      event_description: '%{authorNameFull} hat Sie in einem internen Kommentar erwähnt.'
      subject: '%{firstName} hat Sie in einem internen Kommentar erwähnt.'
      main_header: '%{firstName} hat Sie in einem internen Kommentar erwähnt.'
      preheader: '%{authorNameFull} hat Sie in einem internen Kommentar erwähnt.'
    moderator_digest:
      subject: 'Ihr wöchentlicher Projektmanagementbericht von %{project_title}'
      preheader: 'Projektmanagement-Zusammenfassung von %{organizationName}'
      title_your_weekly_report: '%{firstName}, Ihr wöchentlicher Bericht'
      text_introduction: 'Wir haben für Sie die Beiträge zusammengestellt, die in der vergangenen Woche die meiste Aktivität hatten. Finden Sie heraus, was auf Ihrer Plattform passiert ist!'
      cta_manage: 'Verwalten Sie Ihr Projekt'
      new_users: 'Neue Nutzer*innen'
      new_ideas: 'Neue Ideen'
      new_comments: 'Neue Kommentare'
      title_inputs_past_week: 'Neue Beiträge in der letzten Woche'
      title_no_inputs_past_week: 'Keine neuen Beiträge in der letzten Woche'
      title_threshold_reached: Schwellenwert in der letzten Woche erreicht
      yesterday_by_author: 'Gestern von %{author}'
      today_by_author: 'Heute von %{author}'
      x_days_ago_by_author: 'Vor %{x} Tagen von %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} hat kommentiert:'
      cta_reply_to: 'Sehen Sie sich den Kommentar von %{commentAuthor} an'
      days_ago: 'vor %{numberOfDays} Tagen'
      event_description: '%{authorName} hat einen neuen Kommentar zu Ihrer Plattform hinzugefügt.'
      main_header: '%{firstName}, ein neuer Kommentar wurde auf Ihrer Plattform veröffentlicht'
      subject: 'Ein neuer Kommentar wurde auf der Plattform von %{organizationName} veröffentlicht'
      preheader: '%{authorName} hat ein Kommentar hinterlassen'
      today: Heute
      yesterday: Gestern
    comment_on_idea_you_follow:
      cta_reply_to: 'Antworten Sie auf %{commentAuthor}'
      event_description: '%{authorNameFull} hat eine Reaktion auf ''%{inputTitle}'' abgegeben. Klicken Sie auf den Button unten, um das Gespräch mit %{authorName} fortzusetzen.'
      main_header:
        idea: '%{authorName} hat eine Idee kommentiert, der Sie folgen'
        question: '%{authorName} hat eine Frage kommentiert, der Sie folgen'
        contribution: '%{authorName} hat einen Beitrag kommentiert, dem Sie folgen'
        project: '%{authorName} hat ein Projekt kommentiert, dem Sie folgen'
        issue: '%{authorName} hat ein Problem kommentiert, dem Sie folgen'
        option: '%{authorName} hat eine Option kommentiert, der Sie folgen'
        proposal: '%{authorName} hat einen Vorschlag kommentiert, dem Sie folgen'
        petition: '%{authorName} hat eine Petition kommentiert, der Sie folgen'
      subject: 'Es gibt einen neuen Kommentar zu "%{input_title}"'
      preheader: '%{authorName} hinterließ einen Kommentar zu einer Idee für %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Ein neuer Beitrag wurde auf Ihrer Plattform veröffentlicht'
      event_description_publication: '%{authorName} hat einen neuen Beitrag auf deiner Plattform veröffentlicht. Entdecke ihn jetzt, gib Feedback oder ändere seinen Status!'
      cta_publication: '%{authorName} Feedback geben'
      main_header_prescreening: 'Ein Beitrag erfordert Ihre Überprüfung'
      event_description_prescreening: '%{authorName} hat einen neuen Beitrag auf Ihrer Plattform veröffentlicht.'
      input_not_visible_prescreening: '<b>Der Beitrag wird erst sichtbar</b>, wenn Sie den Status ändern.'
      cta_prescreening: 'Beitrag prüfen'
      days_ago: 'vor %{numberOfDays} Tagen'
      preheader: '%{authorName} hat eine neue Idee auf Ihrer Plattform veröffentlicht'
      today: Heute
      yesterday: Gestern
    comment_on_your_comment:
      cta_reply_to: '%{firstName} antworten'
      event_description: '%{authorNameFull} hat eine Antwort auf Ihren Kommentar zu "%{post}" auf der Beteiligungsplattform geschrieben. Klicken Sie auf den Button unten, um auf %{authorName} zu reagieren.'
      subject: 'Sie haben eine Antwort auf Ihren Kommentar auf der Plattform von %{organizationName} erhalten'
      main_header: '%{authorName} hat auf Ihren Kommentar geantwortet'
      preheader: '%{authorName} hat auf Ihren Kommentar auf der Plattform von %{organizationName} geantwortet'
      replied: '%{authorFirstName} hat geantwortet:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Kommentar anzeigen von %{firstName}'
      event_description: '%{authorNameFull} hat auf Ihren internen Kommentar geantwortet.'
      subject: 'Sie haben einen Kommentar zu Ihrem internen Kommentar ''%{post}'' erhalten'
      main_header: 'Sie haben einen Kommentar zu Ihrem internen Kommentar ''%{post}'' erhalten'
      preheader: '%{authorName} hat auf Ihren internen Kommentar %{post} geantwortet'
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Kommentar anzeigen von %{firstName}'
      event_description: '%{authorNameFull} hat einen Ihnen zugewiesenen Beitrag intern kommentiert.'
      subject: '''%{post}'' hat einen neuen internen Kommentar'
      main_header: '''%{post}'' hat einen neuen internen Kommentar'
      preheader: '''%{post}'' hat einen neuen internen Kommentar'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Kommentar anzeigen von %{firstName}'
      event_description: '%{authorNameFull} hat intern einen Beitrag kommentiert, den Sie intern kommentiert haben.'
      subject: '''%{post}'' hat einen neuen internen Kommentar'
      main_header: '''%{post}'' hat einen neuen internen Kommentar'
      preheader: '''%{post}'' hat einen neuen internen Kommentar'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Kommentar anzeigen von %{firstName}'
      event_description: '%{authorNameFull} hat intern einen Beitrag in einem von Ihnen verwalteten Projekt oder Ordner kommentiert.'
      subject: '''%{post}'' hat einen neuen internen Kommentar'
      main_header: '''%{post}'' hat einen neuen internen Kommentar'
      preheader: '''%{post}'' hat einen neuen internen Kommentar'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Kommentar anzeigen von %{firstName}'
      event_description: '%{authorNameFull} hat einen nicht zugewiesenen Beitrag in einem nicht verwalteten Projekt intern kommentiert.'
      subject: '''%{post}'' hat einen neuen internen Kommentar erhalten'
      main_header: '''%{post}'' hat einen neuen internen Kommentar erhalten'
      preheader: '''%{post}'' hat einen neuen internen Kommentar erhalten'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} hat ein offizielles Update zu %{input_title} gegeben''.'
      header_title: 'Es gibt ein Update zu "%{input_title}"'
      subject: 'Ein offizielles Update wurde zu "%{input_title}" veröffentlicht'
      preheader: 'Es gibt ein Update für einen Beitrag, dem Sie folgen'
    mention_in_official_feedback:
      cta_reply_to: '%{organizationName} antworten'
      event_description: '%{organizationName} hat dich in seinem Feedback zu der Idee ''%{post}'' erwähnt. Klicke auf den unten stehenden Link, um mit %{organizationName} zu interagieren'
      main_header: 'Sie wurden erwähnt'
      subject: '%{organizationName} hat Sie in ihrem Feedback erwähnt'
      preheader: '%{commentAuthor} hat Sie in der Rückmeldung erwähnt'
    project_moderation_rights_received:
      cta_manage_project: 'Verwalten Sie dieses Projekt'
      message_you_became_moderator: 'Ein Admin der Beteiligungsplattform %{organizationName} hat Sie gerade zum/zur Projektmanager*in des folgenden Projekts ernannt:'
      no_ideas: 'Noch keine Ideen'
      preheader: 'Ein Admin der Beteiligungsplattform %{organizationName} hat Sie gerade zum/zur Projektmanager*in des folgenden Projekts ernannt'
      subject: 'Sie sind jetzt Projektmanager*in auf der Plattform %{organizationName}'
      text_design_participatory_process: 'Als Projektmanager*in können Sie konfigurieren, wie Nutzer*innen innerhalb Ihres Projekts interagieren. Sie können neue Phasen zur Zeitleiste hinzufügen. Jede dieser Phasen kann unterschiedlich eingestellt werden bezüglich der Ideen-Eingabe, Kommentierung und Abstimmungen.'
      text_moderate_analyse_input: 'Sobald das Projekt angelaufen ist, werden die ersten Ideen eintreffen. Sie erhalten wöchentliche Berichte mit allen wichtigen Aktivitäten, damit Sie den Überblick behalten. Die Ideenübersicht in Ihrer Projektmanagement-Ansicht hilft Ihnen zu verstehen, welche Ideen die meisten Für- und Gegen-Stimmen erhalten haben.'
      text_share_project_information: 'Um die Qualität der Ideen, die Sie erhalten, zu erhöhen, ist es vor allem wichtig, die Bürger:innen genügend zu informieren. Fügen Sie eine Projektbeschreibung hinzu. Fügen Sie Bilder, wie Pläne und Skizzen, hinzu. Kommunizieren Sie alle betreffenden Veranstaltungen und Ereignisse. Denken Sie daran: gut informieren ist das A und O jeder Beteiligung!'
      title_design_participatory_process: 'Gestalten Sie den partizipativen Prozess'
      title_moderate_analyse_input: 'Moderieren und analysieren Sie den Input der Bürger:innen'
      title_share_project_information: 'Stellen Sie Projektinformationen bereit'
      title_what_can_you_do_moderator: 'Was können Sie als Projektmanager*in tun?'
      title_you_became_moderator: 'Sie sind nun Projektmanager*in'
      x_ideas: '%{numberOfIdeas} Ideen'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Diesen Ordner anzeigen'
      message_added_as_folderadmin: 'Sie haben Administrationsrechte auf der Beteiligungsplattform %{organizationName} für den folgenden Ordner erhalten:'
      no_projects: 'Noch keine Projekte erhalten'
      preheader: 'Ein Admin der Beteiligungsplattform %{organizationName} hat Sie soeben zu einer:einem Manager:in des folgenden Projektordners ernannt'
      subject: 'Sie wurden zu einer:einem Ordnermanager:in auf der Plattform von %{organizationName} ernannt'
      text_manage_folder: 'Mit einem Ordner können Sie mehrere Beteiligungsprojekte zusammen organisieren. Als Ordner-Manager*in können Sie den Ordner und die Ordnerbeschreibung bearbeiten und neue Projekte erstellen (um Projekte zu löschen, müssen Sie Ihren Plattform-Admin kontaktieren). Sie werden außerdem Projektmanagementrechte für alle Projekte innerhalb des Ordners erhalten. So können Sie die Projekte bearbeiten, die Beiträge verwalten und E-Mails an Teilnehmende senden.'
      text_moderate_analyse_input: 'Sobald die Projekte gestartet wurden, werden die ersten Beiträge eingehen. Sie werden wöchentliche Berichte mit den wichtigsten Aktivitäten erhalten, damit Sie den Überblick behalten können. Mit dem Beitragsmanager in Ihrer Admin-Ansicht können Sie sich die Beiträge ansehen und sie verwalten (z. B. können Sie einen Status zuweisen und auf Beiträge und Kommentare antworten).'
      text_design_participatory_process: 'Sie können die verschiedenen Beteiligungsprojekte in Ihrem Ordner verwalten – konfigurieren Sie die Teilnahmemethode, fügen Sie eine Projektbeschreibung hinzu, hängen Sie Bilder an und informieren Sie über relevante Ereignisse. Sie können auch festlegen, wie Teilnehmende bei Ihren Projekten mitwirken können. Zum Beispiel können Sie die Zugriffsrechte festlegen und die Beitrags-, Abstimmungs- und Kommentareinstellungen konfigurieren.'
      title_design_participatory_process: 'Gestalten Sie den partizipativen Prozess'
      title_moderate_analyse_input: 'Moderieren und analysieren Sie den Input der Bürger:innen'
      title_manage_folder: 'Verwalten Sie die Ordnereinstellungen und erstellen Sie neue Projekte.'
      title_what_can_you_do_folderadmin: 'Was können Sie als Ordnermanager:in machen?'
      title_added_as_folderadmin: 'Sie wurden als OrdnermanagerIn hinzugefügt'
      x_projects: '%{numberOfProjects} Projekte'
    project_phase_started:
      cta_view_phase: 'Zur neuen Phase'
      event_description: 'Das Projekt auf der Plattform von %{organizationName} befindet sich nun in einer neuen Phase. Klicke auf den Link unten, um mehr zu erfahren!'
      main_header: 'Eine neue Phase für das Projekt ''%{projectName}'' gestartet'
      subtitle: 'Über ''%{projectName}'''
      new_phase: 'Dieses Projekt ist nun in der Phase ''%{phaseTitle}'''
      subject: '%{projectName} ist in einer neuen Phase'
      preheader: 'Für %{projectName} hat eine neue Phase begonnen'
    project_phase_upcoming:
      cta_view_phase: 'Neue Phase einrichten'
      event_description: 'Das Projekt ''%{projectName}'' befindet sich bald in einer neuen Phase. Stellen Sie sicher, dass für diese Phase alles vorbereitet ist: Ist die Beschreibung zutreffend? Werden zurückgehaltene Ideen in diese Phase übernommen? Möchten Sie die Bürger:innen über die Details dieser Phase mit einer gezielten E-Mail-Kampagne informieren?'
      main_header: '%{firstName}, ein Projekt befindet sich bald in einer neuen Phase'
      subtitle: 'Über ''%{projectName}'''
      new_phase: 'Das Projekt geht in die Phase "%{phaseTitle}"'
      subject: 'Richten Sie alles für die neue Phase von %{projectName} ein'
      preheader: 'Bald beginnt eine neue Phase für %{projectName}'
    project_published:
      subject: 'Ein neues Projekt wurde auf der Plattform von %{organizationName} veröffentlicht'
      header_title: 'Ein neues Projekt wurde veröffentlicht'
      header_message: 'Die Beteiligungsplattform von %{organizationName} hat gerade das folgende Projekt veröffentlicht:'
      preheader: 'Ein neues Projekt wurde veröffentlicht'
    project_review_request:
      subject: 'Antrag auf Freigabe: Ein Projekt wartet auf Freigabe.'
      header: '%{requesterName} hat Sie eingeladen, das Projekt "%{projectTitle}" freizugeben'
      header_message: "Derzeit befindet sich das Projekt im Entwurfsmodus und ist für Nutzer*innen nicht sichtbar. Sobald Sie es geprüft und freigegeben haben, kann der/die Projektmanager*in es veröffentlichen."
      cta_review_project: "Projekt überprüfen"
    project_review_state_change:
      subject: '"%{projectTitle}" wurde freigegeben'
      header: '%{reviewerName} hat das Projekt "%{projectTitle}" freigegeben'
      header_message: "Das Projekt ist jetzt bereit, live zu gehen. Sie können es veröffentlichen, wann immer Sie bereit sind!"
      cta_go_to_project: "Zu den Projekteinstellungen"
    status_change_on_idea_you_follow:
      status_change: 'Der neue Status dieses Beitrags ist ''%{status}'''
      header_message: '%{organizationName} hat den Status des Beitrags ''%{input_title}'' auf Ihrer digitalen Beteiligungsplattform aktualisiert.'
      header_title: 'Ein Beitrag, dem Sie folgen, hat einen neuen Status'
      subject: 'Der Status von "%{input_title}" hat sich geändert'
      preheader: 'Ein Beitrag, dem Sie folgen, hat einen neuen Status'
    user_digest:
      subject: "Ihre Aktivität auf der Beteiligungsplattform %{organizationName}"
      commented: "%{authorFirstName} kommentierte:"
      preheader: "Wöchentliche Übersicht über %{organizationName}"
      title_your_weekly_report: "Entdecken Sie, was letzte Woche passiert ist"
      intro_text: "Hier finden Sie eine Zusammenfassung dessen, was auf der Beteiligungsplattform %{organizationName} passiert ist."
      cta_go_to_the_platform: "Zur Plattform"
      title_no_activity_past_week: "Keine Aktivität in der letzten Woche"
      successful_proposals_title: "Schwelle erreicht"
      successful_proposals_text: "Diese Vorschläge haben genug Unterstützung erhalten, um in die nächste Phase zu gehen! Klicke auf den Vorschlag, um mehr darüber zu erfahren, was als nächstes passiert."
      today_by_author: "Heute von %{author}"
      yesterday_by_author: "Gestern von %{author}"
      x_days_ago_by_author: "Vor %{x} Tagen von %{author}"
      trending_title: "Beliebt"
      trending_text: "Möchten Sie wissen, was auf der Plattform passiert ist? Hier sind die drei beliebtesten Beiträge und was die Leute darüber sagen."
      no_notifications: "Keine Benachrichtigungen"
      one_notification: "1 Benachrichtigung"
      multiple_notifications: "%{notifCount} Benachrichtigungen"
      no_unread_notifications: "Sie haben keine ungelesenen Benachrichtigungen. Besuchen Sie die Plattform, um etwas zu posten und neue Benachrichtigungen zu generieren!"
      unread_notifications: "Sie haben ungelesene Benachrichtigungen. Besuchen Sie die Plattform, um zu erfahren, was los ist."
    threshold_reached_for_admin:
      cta_process_initiative: 'Nächste Schritte für diesen Vorschlag'
      main_header: 'Ein Vorschlag hat die Abstimmungsschwelle erreicht!'
      subject: 'Eine Initiative hat die Abstimmungsschwelle auf Ihrer Plattform erreicht'
      preheader: 'Stelle sicher, dass du die nächsten Schritte ausführst'
    welcome:
      cta_join_platform: 'Plattform entdecken'
      subject: 'Ihre Registrierung für die %{organizationName} Plattform'
      main_header: Herzlich Willkommen!
      message_welcome: 'Sie haben sich erfolgreich auf der Beteiligungsplattform %{organizationName} registriert. Hier können Sie im Dialog mit uns zu bestimmten Fragestellungen Ihre Gedanken austauschen, Ideen sammeln und Feedback geben.'
      preheader: 'Das können Sie auf der Plattform von %{organizationName} tun:'
    idea_published:
      subject:
        idea: 'Ihre Idee wurde veröffentlicht'
        question: 'Ihre Frage wurde veröffentlicht'
        contribution: 'Ihr Beitrag wurde veröffentlicht'
        project: 'Ihr Projekt wurde veröffentlicht'
        issue: 'Ihr Problem wurde veröffentlicht'
        option: 'Ihre Option wurde veröffentlicht'
        proposal: 'Dein Vorschlag wurde veröffentlicht'
        petition: 'Ihre Petition wurde veröffentlicht'
      main_header: 'Du hast eine Idee gepostet! Lass uns sicherstellen, dass sie gelesen wird.'
      header_message: 'Sorgen wir dafür, dass sie gelesen wird.'
      message_get_votes: 'Erreiche mit deiner Idee mehr Menschen:'
      action_published_idea: 'Veröffentlichte Idee'
      action_add_image: '%{addImageLink} um die Sichtbarkeit zu erhöhen'
      add_image: 'Füge ein Bild hinzu'
      action_share_fb: 'Informiere deine Freunde auf %{fbLink}'
      action_share_twitter: 'Informiere deine Follower über %{twitterLink}'
      action_send_email: 'Sende deinen Kontakten eine %{sendEmailLink}'
      send_email: e-Mail
      action_share_link: 'Teile es über einen beliebigen Kanal, indem du den %{link} kopierst'
      link: Link
      preheader: '%{firstName}, herzlichen Glückwunsch zum Hinzufügen deiner Idee auf der Plattform von %{organizationName}. Suche dir jetzt Unterstützung für deine Idee.'
    your_input_in_screening:
      main_header:
        idea: 'Ihre Idee ist in "%{prescreening_status_title}"'
        question: 'Ihre Frage ist in "%{prescreening_status_title}"'
        contribution: 'Ihr Beitrag ist in "%{prescreening_status_title}"'
        project: 'Ihr Projekt ist in "%{prescreening_status_title}"'
        issue: 'Ihr Problem ist in "%{prescreening_status_title}"'
        option: 'Ihre Option ist in "%{prescreening_status_title}"'
        proposal: 'Dein Vorschlag ist in "%{prescreening_status_title}"'
        petition: 'Ihre Petition ist in "%{prescreening_status_title}"'
      message: '"%{input_title}" wird für andere sichtbar, sobald der Beitrag geprüft und genehmigt wurde.'
      subject: '"%{input_title}" ist fast veröffentlicht'
      preheader: 'Es befindet sich derzeit in %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}: Sie haben erfolgreich abgestimmt'
      preheader: 'Sie haben erfolgreich auf der Beteiligungsplattform von %{organizationName} abgestimmt'
      title_basket_submitted: 'Sie haben erfolgreich abgestimmt'
      event_description: 'Vielen Dank für Ihre Teilnahme. Ihre Stimmen wurden aufgezeichnet. Besuchen Sie die Plattform von %{organizationName}, um Ihre Stimmen zu sehen und zu verwalten.'
      cta_see_votes_submitted: 'Siehe abgegebene Stimmen'
      cta_message: 'Klicken Sie auf den Button unten, um teilzunehmen'
    native_survey_not_submitted:
      subject: '%{organizationName}: Fast geschafft! Reichen Sie Ihre Antworten ein'
      preheader: 'Sie haben Ihre Umfrageantwort nicht auf der Teilnahmeplattform von %{organizationName}ausgefüllt.'
      title_native_survey_not_submitted: 'Fast geschafft! Reichen Sie Ihre Antworten ein'
      body_native_survey_not_submitted: 'Sie haben begonnen, Ihre Antworten zu %{phaseTitle} zu teilen, haben sie aber nicht eingereicht. Die Einsendungen wird am %{phaseEndDate} geschlossen. Klicken Sie auf den Button unten, um dort fortzufahren, wo Sie aufgehört haben.'
      body_native_survey_not_submitted_no_date: 'Sie haben begonnen, Ihre Antworten zu %{phaseTitle} zu teilen, haben sie aber nicht abgeschickt. Klicken Sie auf den BUtton unten, um dort fortzufahren, wo Sie aufgehört haben.'
      cta_complete_your_survey_response: 'Beantwortung der Umfrage fortsetzen'
    voting_basket_not_submitted:
      subject: '%{organizationName}: Sie haben Ihre Stimmen nicht abgegeben'
      preheader: 'Sie haben Ihre Stimme nicht auf der Beteiligungsplattform von %{organizationName} abgegeben'
      title_basket_not_submitted: 'Sie haben Ihre Stimmen nicht abgegeben'
      event_description: 'Sie haben ein paar Optionen für %{contextTitle} ausgewählt, aber Sie haben Ihre Auswahl nicht abgeschickt.'
      cta_view_options_and_vote: 'Optionen ansehen und abstimmen'
      cta_message: 'Klicken Sie auf den Button unten, um Ihre ausgewählten Optionen abzuschicken'
    voting_last_chance:
      subject: '%{organizationName}: Letzte Chance, für %{phaseTitle} abzustimmen'
      preheader: 'Letzte Chance, für %{phaseTitle} auf der Beteiligungsplattform von %{organizationName} abzustimmen'
      title_last_chance: 'Letzte Chance, für %{phaseTitle} abzustimmen'
      body_1: 'Die Abstimmungsphase für das Projekt %{projectTitle} geht morgen um Mitternacht zu Ende.'
      body_2: 'Die Zeit läuft ab und wir haben festgestellt, dass Sie Ihre Stimme noch nicht abgegeben haben! Klicken Sie auf den Button unten und machen Sie mit.'
      body_3: 'Auf diese Weise erhalten Sie Zugang zu einer Reihe von Optionen und haben die Möglichkeit, Ihre Stimme abzugeben, die für die Entscheidung über die Zukunft dieses Projekts entscheidend ist.'
      cta_vote: 'Abstimmen'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} Abstimmungsergebnisse veröffentlicht!'
      preheader: '%{phaseTitle} Abstimmungsergebnisse auf der Beteiligungsplattform von %{organizationName} veröffentlicht'
      title_results: '%{phaseTitle} Abstimmungsergebnisse veröffentlicht!'
      body_1: 'Die Ergebnisse sind da!'
      body_2: 'Die Ergebnisse der Abstimmung von %{phaseTitle} auf der Plattform %{organizationName} sind veröffentlicht worden!'
      body_3: 'Schauen Sie sich die Ergebnisse an. Wir halten Sie über die nächsten Schritte auf dem Laufenden.'
      cta_see_results: 'Ergebnisse auf der Plattform ansehen'
    event_registration_confirmation:
      subject: "Sie sind dabei! Ihre Anmeldung für \"%{eventTitle}\" ist bestätigt"
      preheader: "%{firstName}, danke für Ihre Anmeldung für %{eventTitle}"
      header_message: "%{firstName}, danke für die Registrierung für"
      event_details:
        labels:
          date: 'Datum'
          location: 'Ort'
          online_link: 'Online-Link'
          description: 'Beschreibung'
          project: 'Projekt'
      cta_go_to_event: 'Veranstaltung ansehen'
      cta_add_to_calendar: 'Zu Ihrem Kalender hinzufügen'
    voting_phase_started:
      subject: '%{organizationName}: Die Abstimmungsphase für %{projectName} hat begonnen'
      preheader: 'Die Abstimmungsphase für %{projectName} hat auf der Teilnahme-Plattform %{organizationName} begonnen'
      event_description: 'Für das Projekt "%{projectName}" können Sie aus %{numIdeas} Optionen wählen:'
      cta_message: 'Auf den Button klicken, um teilzunehmen'
      cta_vote: 'Zur Plattform, um abzustimmen'
    survey_submitted:
      subject: '%{organizationName}: Vielen Dank für Ihre Antwort! 🎉'
      preheader: 'Details zur Einreichung.'
      main_header: 'Danke, dass Sie Ihre Gedanken zu "%{projectName}" mit uns teilen!'
      your_input_submitted: 'Ihr Beitrag für "%{projectName}" wurde erfolgreich versendet.'
      if_you_would_like_to_review: 'Wenn Sie Ihren Beitrag überprüfen möchten, können Sie Ihre Antworten unten herunterladen.'
      your_submission_has_id: 'Ihr Beitrag hat die folgende eindeutige Kennung:'
      you_can_use_this_id: 'Sie können diese Kennung verwenden, um die Admins der Plattform zu kontaktieren, falls Sie möchten, dass Ihr Beitrag entfernt wird.'
      download_responses: 'Antworten herunterladen'
    admin_labels:
      recipient_role:
        admins: 'An Admins'
        admins_and_managers: 'An Admins & Manager*innen'
        managers: 'An Manager*innen'
        project_participants: 'An Projektteilnehmende'
        registered_users: 'An registrierte Nutzer*innen'
      recipient_segment:
        admins: 'Admins'
        admins_and_managers: 'Admins und Manager*innen'
        admins_and_managers_assigned_to_the_input: 'Admins & Manager*innen, die dem Beitrag zugewiesen sind'
        admins_and_managers_managing_the_project: 'Admins & Manager*innen, die das Projekt verwalten'
        admins_assigned_to_a_proposal: 'Einem Vorschlag zugewiesene Admins'
        all_users: 'Alle Nutzer*innen'
        all_users_who_uploaded_proposals: 'Nutzer*in veröffentlicht Vorschläge'
        managers: 'Manager*innen'
        managers_managing_the_project: 'Manager*innen, die das Projekt verwalten'
        new_attendee: 'Neu registrierte*r Benutzer*in'
        project_reviewers: 'Projektüberprüfer*innen und Ordnermanager*innen'
        project_review_requester: 'Projektmanager*in, der oder die die Freigabe beantragt hat'
        user_who_commented: 'Nutzer*in kommentiert'
        user_who_is_invited_to_cosponsor_a_proposal: 'Nutzer*in wird zur Unterstützung eines Vorschlags eingeladen'
        user_who_is_mentioned: 'Erwähnte Admins & Manager*innen'
        user_who_is_receiving_admin_rights: 'Nutzer*in erhält Admin-Rechte'
        user_who_is_receiving_folder_moderator_rights: 'Nutzer*in erhält Ordner-Moderationsrechte'
        user_who_is_receiving_project_moderator_rights: 'Nutzer*in erhält Projektmanagementrechte'
        user_who_published_the_input: 'Nutzer*in veröffentlicht Beitrag'
        user_who_published_the_proposal: 'Nutzer*in veröffentlicht Vorschlag'
        user_who_registers: 'Nutzer*in registriert sich '
        user_who_submitted_the_input: 'Nutzende, die*der den Beitrag veröffentlicht hat'
        user_who_voted: 'Nutzer*innen, die abgestimmt haben'
        user_who_was_invited: 'Eingeladene*r Nutzer*in'
        user_with_unsubmitted_survey: 'Nutzer*innen, die ihre Umfrage begonnen, aber noch nicht abgeschickt haben'
        user_with_unsubmitted_votes: 'Nutzer*innen, die Ihre Stimme(n) nicht abgeschickt haben'
        users_who_engaged_but_not_voted: 'Nutzer*innen, die sich mit dem Projekt beschäftigt, aber nicht abgestimmt haben'
        users_who_engaged_with_the_project: 'Nutzer*in interagiert mit Beitrag'
        users_who_follow_the_input: 'Nutzer*innen, die dem Beitrag folgen'
        users_who_follow_the_project: 'Nutzer*innen, die dem Projekt folgen'
        users_who_follow_the_proposal: 'Nutzer*innen, die dem Vorschlag folgen'
      content_type:
        comments: 'Kommentare'
        content_moderation: 'Inhaltsmoderation'
        events: 'Veranstaltungen'
        general: 'Allgemein'
        inputs: 'Beiträge'
        internal_comments: 'Interne Kommentare'
        permissions: 'Berechtigungen'
        projects: 'Projekte'
        proposals: 'Vorschläge'
        reactions: 'Reaktionen'
        voting: 'Abstimmung'
        surveys: 'Umfragen'
      trigger:
        7_days_after_invite_is_sent: '7 Tage nach Versand der 1. Einladung'
        7_days_before_the_project_changes_phase: '7 Tage bevor das Projekt die Phase wechselt'
        comment_is_deleted: 'Kommentar wird gelöscht'
        comment_is_flagged_as_spam: 'Kommentar wird als Spam markiert'
        content_gets_flagged_as_innapropiate: 'Inhalte werden als unangemessen gekennzeichnet'
        initiative_resubmitted_for_review: 'Vorschlag erneut zur Prüfung eingereicht'
        input_is_assigned: 'Beitrag wird zugewiesen'
        input_is_flagged_as_spam: 'Beitrag wird als Spam gekennzeichnet'
        input_is_published: 'Beitrag wird veröffentlicht'
        input_is_updated: 'Beitrag erhält ein offizielles Update'
        input_status_changes: 'Status eines Beitrags wird geändert'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Interner Kommentar wird zu einem Beitrag veröffentlicht, der Nutzer*in zugewiesen ist'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Interne Kommentare werden zu Beiträgen in von Nutzer*innen verwalteten Projekten oder Ordnern gepostet'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Interner Kommentar wird zu Beitrag gepostet, die Nutzer*in intern kommentiert hat'
        internal_comment_is_posted_on_idea_user_moderates: 'Interner Kommentar wird auf einen Beitrag, der von einem Nutzer / einer Nutzerin moderiert wird, gepostet'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Interner Kommentar wird zu nicht zugewiesenem Beitrag in nicht verwaltetem Projekt veröffentlicht'
        project_review_request: 'Projektmanager*in hat eine Projektfreigabe beantragt'
        project_review_state_change: 'Prüfer*in genehmigt das Projekt'
        new_input_awaits_screening: 'Neuer Beitrag wartet auf Überprüfung'
        new_input_is_published: 'Neuer Beitrag wird veröffentlicht'
        new_proposal_is_posted: 'Neuer Vorschlag wird veröffentlicht'
        project_phase_changes: 'Neue Projektphase beginnt'
        project_published: 'Projekt veröffentlicht'
        proposal_gets_reported_as_spam: 'Vorschlag wird als Spam markiert'
        proposal_is_assigned_to_admin: 'Vorschlag wird Admin zugewiesen'
        proposal_is_published: 'Vorschlag wird veröffentlicht'
        proposal_is_updated: 'Vorschlag wird aktualisiert'
        proposal_is_upvoted_above_threshold: 'Vorschlag erreicht Schwellenwert'
        proposal_status_changes: 'Status des Vorschlags wird geändert'
        registration_to_event: 'Anmeldung zu einer Veranstaltung'
        survey_1_day_after_draft_saved: '1 Tag, nachdem der Nutzer / die Nutzer*in die Umfrage zuletzt als Entwurf gespeichert hat'
        user_accepts_invitation_to_cosponsor_a_proposal: 'Nutzer*in nimmt Einladung zur Unterstützung eines Vorschlags an'
        user_comments: 'Kommentar wird veröffentlicht'
        user_comments_on_input: 'Kommentar wird veröffentlicht'
        user_comments_on_proposal: 'Nutzer*in kommentiert Vorschlag'
        user_is_given_admin_rights: 'Nutzer*in erhält Admin-Rechte'
        user_is_given_folder_moderator_rights: 'Nutzer*in erhält Ordner-Moderationsrechte'
        user_is_given_project_moderator_rights: 'Nutzer*in erhält Projektmanagementrechte'
        user_is_invited_to_cosponsor_a_proposal: 'Nutzer*in ist eingeladen, einen Vorschlag zu unterstützen'
        user_is_mentioned: 'Nutzer*in wird erwähnt'
        user_is_mentioned_in_internal_comment: 'Nutzer*in wird in internem Kommentar erwähnt'
        user_registers_for_the_first_time: 'Direkt nach der 1. Registrierung'
        user_replies_to_comment: 'Antwort wird veröffentlicht'
        user_replies_to_internal_comment: 'Nutzer*in antwortet auf internen Kommentar'
        voting_1_day_after_last_votes: '1 Tag nach der letzten Stimmabgabe der Nutzer*innen'
        voting_2_days_before_phase_closes: '2 Tage vor Ende der Abstimmung'
        voting_basket_submitted: 'Stimmen eingereicht'
        voting_phase_ended: 'Abstimmungsphase beendet'
