hu:
  email_campaigns:
    campaign_type_description:
      "manual": <PERSON><PERSON><PERSON><PERSON>
      "manual_project_participants": <PERSON><PERSON><PERSON><PERSON> a projekt résztvevőinek
      "admin_rights_received": <PERSON>z adminisztrátori jogok meg<PERSON>
      "comment_deleted_by_admin": Hozzászólásom törlése
      "comment_marked_as_spam": Megjegyzés spamjelentés
      "comment_on_your_comment": V<PERSON><PERSON>z a hozzászólásomra
      "comment_on_idea_you_follow": Megjegyzés egy Ön által követett ötlethez
      "community_monitor_report": Közösségi megfigyel<PERSON> jelentés
      "cosponsor_of_your_idea": <PERSON><PERSON> fel<PERSON> elfogadja a meghívásomat, hogy társszponzorálja ajánlatomat
      "event_registration_confirmation": <PERSON><PERSON><PERSON><PERSON> regisztráció megerősítése
      "idea_marked_as_spam": Idea spam jelentés
      "idea_published": Hozz<PERSON>zól<PERSON><PERSON> közzététele
      "invitation_to_cosponsor_idea": <PERSON>l<PERSON>í<PERSON><PERSON> egy javaslat tá<PERSON>szponzorálására
      "invite_received": <PERSON><PERSON><PERSON><PERSON><PERSON>
      "invite_reminder": Megh<PERSON>vó emlékeztető
      "internal_comment_on_idea_assigned_to_you": Belső megjegyzés a hozzám rendelt bevitelhez
      "internal_comment_on_idea_you_commented_internally_on": Belső megjegyzés a bemenethez, amelyhez belső megjegyzést tettem
      "internal_comment_on_idea_you_moderate": Belső megjegyzés az általam kezelt projektben vagy mappában lévő bevitelhez
      "internal_comment_on_unassigned_unmoderated_idea": Belső megjegyzés a nem felügyelt projektben lévő hozzá nem rendelt bemenethez
      "internal_comment_on_your_internal_comment": Belső megjegyzés a belső megjegyzésemhez
      "mention_in_official_feedback": Említse meg a frissítésben
      "mention_in_internal_comment": Említse meg egy belső megjegyzésben
      "new_comment_for_admin": Új megjegyzés egy általam moderált projektben
      "new_idea_for_admin": Új bemenet egy általam moderált projektben
      "official_feedback_on_idea_you_follow": Frissítse a követett bemenetet
      "password_reset": Jelszó visszaállítása
      "project_moderation_rights_received": A projekt moderálási jogai megkapták
      "project_folder_moderation_rights_received": Mappakezelői jogok megérkeztek
      "project_phase_started": Új projekt fázis
      "project_phase_upcoming": Közelgő új projektszakasz
      "project_published": A projekt megjelent
      "project_review_request": Projekt felülvizsgálati kérelem
      "project_review_state_change": Projekt jóváhagyva
      "status_change_on_idea_you_follow": A követett bemenet állapotának változása
      "survey_submitted": Felmérés benyújtva
      "threshold_reached_for_admin": A javaslat elérte a szavazati küszöböt
      "welcome": Regisztráció után
      "admin_digest": Heti áttekintés rendszergazdáknak
      "moderator_digest": Heti áttekintés projektmenedzsereknek
      "assignee_digest": A hozzárendelt ötletek heti áttekintése
      "user_digest": Heti áttekintés
      "voting_basket_submitted": A szavazás megerősítése
      "native_survey_not_submitted": A felmérés nem került benyújtásra
      "voting_basket_not_submitted": A szavazatokat nem nyújtották be
      "voting_last_chance": Utolsó lehetőség szavazni
      "voting_phase_started": Új projektszakasz szavazással
      "voting_results": Szavazási eredmények
      "your_input_in_screening": Hozzászólásom átvilágításra vár
    general:
      by_author: 'készítette: %{authorName}'
      author_wrote: '%{authorName} írta:'
      cta_goto_idea: 'Menj ehhez az ötlethez'
      cta_goto_input: 'Ugrás erre a bemenetre'
      cta_goto:
        idea: 'Menj ehhez az ötlethez'
        question: 'Menj erre a kérdésre'
        contribution: 'Ugrás erre a hozzájárulásra'
        project: 'Ugrás ehhez a projekthez'
        issue: 'Tovább a kérdéshez'
        option: 'Menjen erre a lehetőségre'
        proposal: 'Tovább a javaslathoz'
        petition: 'Tovább a petícióhoz'
      cta_goto_your:
        idea: 'Menj az ötletedhez'
        question: 'Menj a kérdésedre'
        contribution: 'Tovább a hozzájárulásodhoz'
        project: 'Menj a projektedhez'
        issue: 'Menjen a problémájához'
        option: 'Menjen a választott lehetőségre'
        proposal: 'Menjen az ajánlatához'
        petition: 'Menj a petíciódhoz'
      cta_goto_proposal: 'Tovább a javaslathoz'
      cta_goto_project: 'Ugrás ehhez a projekthez'
    schedules:
      weekly:
        "0": "Hetente, vasárnaponként %{hourOfDay}-kor"
        "1": "Hetente, hétfőnként %{hourOfDay}-kor"
        "2": "Hetente, keddenként %{hourOfDay}-kor"
        "3": "Hetente, szerdánként %{hourOfDay}-kor"
        "4": "Hetente, csütörtökönként %{hourOfDay}-kor"
        "5": "Hetente, péntekenként %{hourOfDay}-kor"
        "6": "Hetente, szombatonként %{hourOfDay}-kor"
      quarterly: "Negyedévenként, a negyedév első napján"
    preview_data:
      first_name: 'Jane'
      last_name: 'Dámvadtehén'
      display_name: 'Jane Doe'
      comment_body: 'Ez egy példa a hozzászólásra, amelyet e-mailek tartalmának előnézetére használnak. Ez nem valós tartalom.'
      idea_title: 'Példaötlet'
    footer:
      "link_privacy_policy": "Adatvédelmi szabályzat"
      "link_terms_conditions": "Általános Szerződési Feltételek"
      "link_unsubscribe": "Leiratkozás"
      "powered_by": "Powered by"
      "recipient_statement": "Ezt az e-mailt a Go Vocal küldte neked %{organizationName}nevében, mivel Ön a %{organizationLink}regisztrált felhasználója."
      "unsubscribe_statement": "Ha a jövőben nem szeretné megkapni ezeket az e-maileket, megteheti a %{unsubscribeLink} értékét."
      "unsubscribe_text": "leiratkozás"
    follow:
      "unfollow_here": "Ezt az értesítést egy általad követett elem miatt kaptad. <a href=\"%{unfollow_url}\">Itt leállíthatod a követést.</a>"
    manual:
      preheader: 'Önnek levelei vannak a %{organizationName}feladótól'
    comment_deleted_by_admin:
      reason: 'A hozzászólásod törlésének oka:'
      cta_view: 'Tekintse meg ezt az ötletet'
      event_description: '%{organizationName} törölte egy ötlethez írt megjegyzését.'
      main_header: '%{organizationName} törölte a megjegyzésedet'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Az Ön megjegyzése nem megfelelő'
      irrelevant_reason: 'Az Ön megjegyzése nem volt releváns ebben a kontextusban'
      no_reason: 'Nem adtak meg okot'
      subject: 'Megjegyzését töröltük a %{organizationName}platformjáról'
      preheader: 'A hozzászólásod törölve lett'
    admin_digest:
      subject: 'A %{time}heti rendszergazdai jelentése'
      preheader: 'A %{organizationName}rendszergazdai kivonata'
      title_your_weekly_report: '%{firstName}, a heti jelentés'
      text_introduction: 'Összegyűjtöttük Önnek azt a bemenetet, amely a legtöbb tevékenységet generálta az elmúlt héten. Tudja meg, mi történik a platformján!'
      cta_visit_the_platform: 'Látogassa meg platformját'
      new_users: 'Új felhasználók'
      new_inputs: 'Új bemenetek'
      new_comments: 'Új megjegyzések'
      title_activity_past_week: 'Az elmúlt hét tevékenysége'
      title_no_activity_past_week: 'Az elmúlt héten nem volt tevékenység'
      reached_threshold: 'Elérte a küszöböt'
      yesterday_by_author: 'Tegnap %{author}'
      today_by_author: 'Ma %{author}'
      x_days_ago_by_author: '%{x} napja, %{author}'
    admin_rights_received:
      cta_manage_platform: 'Kezelje platformját'
      message_you_became_administrator: 'Rendszergazdai jogokat kapott a %{organizationName}részvételi platformjához.'
      preheader: 'Rendszergazdai jogokat kapott a %{organizationName}részvételi platformjához'
      subject: 'Ön rendszergazda lett a %{organizationName}platformon'
      text_create_participatory_process: 'Rendszergazdaként új részvételi projekteket hozhat létre és konfigurálhat. Az idővonal segítségével új fázisokat adhat hozzá. Ezen fázisok mindegyikének megvan a maga viselkedése az ötletek közzététele, kommentálása és szavazása tekintetében.'
      text_moderate_analyse_input: 'A projektek elindítása után megérkeznek az első ötletek. Hetente jelentést fog kapni az összes kulcsfontosságú tevékenységről, hogy naprakész legyen a dolgokkal. Az ötletek áttekintése segít moderálni a bemenetet és együttműködni annak feldolgozásában.'
      text_platform_setup: 'Rendszergazdaként beállíthatja részvételi platformját. Válasszon logót, képeket és színeket, írjon személyes üzenetet a kezdőlapjára, küldjön meghívókat, határozza meg, mit szeretne tudni a felhasználókról, ...'
      title_create_participatory_process: 'Tervezze meg a részvételi folyamatot'
      title_moderate_analyse_input: 'Moderálja és elemezze a bemenetet'
      title_platform_setup: 'Állítsa be a platformot'
      title_what_can_you_do_administrator: 'Mit tehet rendszergazdaként?'
      title_you_became_administrator: 'Adminisztrátor lettél'
    comment_marked_as_spam:
      by_author: 'készítette: %{authorName}'
      commented: '%{authorName} hozzászólt:'
      cta_review_comment: 'Tekintse át a megjegyzést'
      days_ago: '%{numberOfDays} napja'
      event_description: 'A <strong>''%{post}''</strong> oldalon közzétett következő megjegyzést jelentették:'
      inappropriate_content: 'A megjegyzés nem helyénvaló vagy sértő.'
      preheader: 'Cselekedjen a spamként bejelentett megjegyzés alapján'
      reported_this_because: '%{reporterFirstName} jelentette ezt, mert:'
      subject: '%{organizationName}: %{firstName} %{lastName} spamként jelentette ezt a megjegyzést'
      title_comment_spam_report: '%{firstName} %{lastName} spamként jelentette ezt a megjegyzést'
      today: Ma
      wrong_content: 'A megjegyzés nem releváns.'
      yesterday: Tegnap
    community_monitor_report:
      subject: 'Elérhető egy új közösségfigyelő jelentés'
      title: 'Elérhető egy új közösségfigyelő jelentés'
      text_introduction: 'Közösségi megfigyelő jelentés készült az előző negyedévről. Az alábbi gombra kattintva és bejelentkezve érheti el.'
      cta_report_button: 'Jelentés megtekintése'
      report_name: 'Közösségi megfigyelő jelentés'
    cosponsor_of_your_idea:
      cta_reply_to: 'Tekintse meg ajánlatát'
      event_description: 'Gratulálok! %{cosponsorName} elfogadta a felkérését, hogy társszponzorálja ajánlatát.'
      main_header: '%{cosponsorName} elfogadta a felkérését, hogy társszponzorálja ajánlatát'
      subject: '%{cosponsorName} elfogadta a felkérését, hogy társszponzorálja ajánlatát'
      preheader: '%{cosponsorName} elfogadta a felkérését, hogy társszponzorálja ajánlatát'
    assignee_digest:
      subject: 'Visszajelzést igénylő bemenetek: %{numberIdeas}'
      preheader: 'A %{organizationName}hozzárendelt kivonata'
      title_your_weekly_report: '%{firstName}, az állampolgárok visszajelzésére várunk'
      cta_manage_your_input: 'Kezelje a bevitelt'
      x_inputs_need_your_feedback: 'a bemenetekhez visszajelzésre van szükség'
      title_assignment_past_week: 'Az Önhöz rendelt legújabb bemenetek'
      title_no_assignment_past_week: 'A múlt héten nem lettek hozzárendelve új bemenetek'
      yesterday_by_author: 'Tegnap %{author}'
      today_by_author: 'Ma %{author}'
      x_days_ago_by_author: '%{x} napja, %{author}'
      title_successful_past_week: 'Önhöz rendelve, amely elérte a küszöböt'
    idea_marked_as_spam:
      cta_review: 'Tekintse át'
      report_inappropriate_offensive_content: 'Ezt a tartalmat nem helyénvalónak vagy sértőnek tartom.'
      report_not_an_idea: 'Ez a tartalom nem releváns és nem tartozik ide.'
      subject: 'Spamjelentése van a %{organizationName}platformon'
      preheader: 'Cselekedjen a spamjelentés alapján'
      reported_this_because: '%{reporterFirstName} jelentette ezt, mert:'
      title_spam_report: '%{firstName} %{lastName} spamet jelentett'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Támogatja ezt a javaslatot'
      event_description: '%{authorName} új javaslatot hozott létre, és szeretné, ha társszponzorálná.'
      event_description_cosponsoring: 'Egy ajánlat társszponzorálása azt jelenti, hogy <strong>az Ön neve</strong> jelenik meg a javaslat többi társszponzorának nevével együtt.'
      event_description_before_action: 'Az ajánlat megtekintéséhez és a meghívás elfogadásához be kell jelentkeznie fiókjába.'
      event_description_action: 'Az ajánlat elolvasásához kattintson az alábbi gombra.'
      main_header: 'Önt felkérték egy javaslat társszponzorálására'
      subject: 'Önt felkérték egy javaslat társszponzorálására'
      preheader: 'Önt meghívták, hogy támogassa a %{authorName}javaslatát'
    invite_reminder:
      cta_accept_invitation: 'Fogadja el a meghívást'
      invitation_header: 'Meghívása függőben van'
      preheader: '%{organizationName} néhány napja meghívót küldött Önnek, hogy csatlakozzon a részvételi platformjukhoz.'
      invitation_expiry_message: 'Ez a meghívó körülbelül %{expiryDaysRemaining} napon belül lejár.'
      subject: 'Függőben lévő meghívó a %{organizationName}részvételi platformjára'
    invite_received:
      added_a_message: '%{organizationName} a következő üzenetet írta:'
      cta_accept_invitation: 'Fogadja el a meghívást'
      invitation_header: 'Meghívták!'
      invitation_header_message: '%{organizationName} meghívott a részvételi platformjukra.'
      invitation_expiry_message: 'Ez a meghívó %{expiryDays} napon belül lejár.'
      preheader: '%{organizationName} meghívót küldött Önnek, hogy csatlakozzon a részvételi platformjukhoz.'
      subject: 'Meghívják, hogy csatlakozzon a %{organizationName}platformjához'
    mention_in_comment:
      cta_reply_to: 'Válasz %{commentAuthor}'
      event_description: '%{commentAuthorFull} megemlített téged a ''%{post}'' ötlethez fűzött megjegyzésében. Kattintson az alábbi linkre, hogy belépjen a beszélgetésbe %{commentAuthor}-vel'
      main_header: 'Az emberek rólad beszélnek'
      subject: 'Valaki megemlítette Önt a %{organizationName}platformján'
      preheader: '%{commentAuthor} megemlített egy megjegyzésben'
    mention_in_internal_comment:
      cta_reply_to: '%{firstName}megjegyzésének megtekintése'
      event_description: '%{authorNameFull} megemlített téged egy belső megjegyzésben.'
      subject: '%{firstName} megemlített téged egy belső megjegyzésben.'
      main_header: '%{firstName} megemlített téged egy belső megjegyzésben.'
      preheader: '%{authorNameFull} megemlített téged egy belső megjegyzésben.'
    moderator_digest:
      subject: '"%{project_title}" heti vezetői jelentése'
      preheader: 'A %{organizationName}projektmenedzseri kivonata'
      title_your_weekly_report: '%{firstName}, a heti jelentés'
      text_introduction: 'Összegyűjtöttük Önnek azt a bemenetet, amely a legtöbb tevékenységet generálta az elmúlt héten. Tudja meg, mi történik a projektjével!'
      cta_manage: 'Kezelje projektjét'
      new_users: 'Új felhasználók'
      new_ideas: 'Új ötletek'
      new_comments: 'Új megjegyzések'
      title_inputs_past_week: 'Új bemenetek az elmúlt héten'
      title_no_inputs_past_week: 'Az elmúlt héten nem érkezett új adat'
      title_threshold_reached: Elérte a küszöböt az elmúlt héten
      yesterday_by_author: 'Tegnap %{author}'
      today_by_author: 'Ma %{author}'
      x_days_ago_by_author: '%{x} napja, %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} hozzászólt:'
      cta_reply_to: '%{commentAuthor}megjegyzésének megtekintése'
      days_ago: '%{numberOfDays} napja'
      event_description: '%{authorName} új megjegyzést fűzött a platformjához.'
      main_header: '%{firstName}, új megjegyzést tettek közzé az Ön platformján'
      subject: 'Új megjegyzést tettek közzé %{organizationName}platformján'
      preheader: '%{authorName} megjegyzést írt'
      today: Ma
      yesterday: Tegnap
    comment_on_idea_you_follow:
      cta_reply_to: 'Válasz %{commentAuthor}'
      event_description: '%{authorNameFull} reakciót adott a ''%{inputTitle}''-re. Kattintson az alábbi gombra a beszélgetés folytatásához %{authorName}.'
      main_header:
        idea: '%{authorName} megjegyzést fűzött egy Ön által követett ötlethez'
        question: '%{authorName} megjegyzést fűzött egy Ön által követett kérdéshez'
        contribution: '%{authorName} megjegyzést fűzött egy általad követett hozzászóláshoz'
        project: '%{authorName} megjegyzést fűzött egy általad követett projekthez'
        issue: '%{authorName} megjegyzést fűzött egy Ön által követett problémához'
        option: '%{authorName} megjegyzést fűzött egy általad követett lehetőséghez'
        proposal: '%{authorName} megjegyzést fűzött egy Ön által követett javaslathoz'
        petition: '%{authorName} megjegyzést fűzött egy Ön által követett petícióhoz'
      subject: 'Új megjegyzés érkezett a következőhöz: "%{input_title}"'
      preheader: '%{authorName} megjegyzést írt egy ötlethez a következőhöz: %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Új bemenetet tettek közzé a platformján'
      event_description_publication: '%{authorName} új bevitelt küldött az Ön platformjára. Fedezze fel most, adjon visszajelzést, vagy módosítsa az állapotát!'
      cta_publication: 'Visszajelzés küldése a %{authorName}címre'
      main_header_prescreening: 'A bemenet ellenőrzését igényli'
      event_description_prescreening: '%{authorName} új bevitelt küldött az Ön platformjára.'
      input_not_visible_prescreening: '<b>A bemenet nem lesz látható</b> , amíg nem módosítja az állapotát.'
      cta_prescreening: 'Tekintse át a bevitelt'
      days_ago: '%{numberOfDays} napja'
      preheader: '%{authorName} új bemenetet tett közzé a platformján'
      today: Ma
      yesterday: Tegnap
    comment_on_your_comment:
      cta_reply_to: 'Válasz %{firstName}'
      event_description: '%{authorNameFull} válaszolt az Ön megjegyzésére a részvételi platformon: ''%{post}''. Kattintson az alábbi gombra a beszélgetés folytatásához %{authorName}.'
      subject: 'Válasz érkezett megjegyzésére a %{organizationName}platformon'
      main_header: '%{authorName} válaszolt a megjegyzésedre'
      preheader: '%{authorName} válaszolt a megjegyzésedre %{organizationName}platformján'
      replied: '%{authorFirstName} válaszolt:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: '%{firstName}megjegyzésének megtekintése'
      event_description: '%{authorNameFull} megjegyzést fűzött a belső megjegyzésedhez.'
      subject: 'Megjegyzés érkezett a(z) „%{post}” című belső megjegyzéséhez'
      main_header: 'Megjegyzés érkezett a(z) „%{post}” című belső megjegyzéséhez'
      preheader: '%{authorName} válaszolt az Ön belső megjegyzésére a következőhöz: "%{post}"'
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: '%{firstName}megjegyzésének megtekintése'
      event_description: '%{authorNameFull} belső megjegyzést fűzött egy Önhöz rendelt bemenethez.'
      subject: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      main_header: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      preheader: 'Új belső megjegyzés a következőhöz: ''%{post}'''
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: '%{firstName}megjegyzésének megtekintése'
      event_description: '%{authorNameFull} belső megjegyzést fűzött egy olyan bemenethez, amelyhez Ön belsőleg megjegyzést fűzött.'
      subject: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      main_header: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      preheader: 'Új belső megjegyzés a következőhöz: ''%{post}'''
    internal_comment_on_idea_you_moderate:
      cta_reply_to: '%{firstName}megjegyzésének megtekintése'
      event_description: '%{authorNameFull} belső megjegyzést fűzött egy bemenethez egy Ön által kezelt projektben vagy mappában.'
      subject: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      main_header: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      preheader: 'Új belső megjegyzés a következőhöz: ''%{post}'''
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: '%{firstName}megjegyzésének megtekintése'
      event_description: '%{authorNameFull} belső megjegyzést fűzött egy nem felügyelt projekt hozzá nem rendelt bemenetéhez.'
      subject: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      main_header: 'Új belső megjegyzés a következőhöz: ''%{post}'''
      preheader: 'Új belső megjegyzés a következőhöz: ''%{post}'''
    official_feedback_on_idea_you_follow:
      header_message: 'A %{feedback_author_name} frissítést adott a következőhöz: ''%{input_title}''.'
      header_title: 'Frissítés érkezett a következőhöz: "%{input_title}"'
      subject: 'A hivatalos visszajelzés a következő helyen jelent meg: "%{input_title}"'
      preheader: 'Frissítés érkezett az Ön által követett bemenethez'
    mention_in_official_feedback:
      cta_reply_to: 'Válasz %{organizationName}'
      event_description: '%{organizationName} megemlített téged a „%{post}” ötlettel kapcsolatos visszajelzésében. Kattintson az alábbi linkre, hogy belépjen a beszélgetésbe %{organizationName}-vel'
      main_header: 'Téged emlegettek'
      subject: '%{organizationName} megemlítette Önt visszajelzésében'
      preheader: '%{commentAuthor} megemlítette Önt visszajelzésében'
    project_moderation_rights_received:
      cta_manage_project: 'A projekt kezelése'
      message_you_became_moderator: 'A %{organizationName} részvételi platformjának adminisztrátora éppen a következő projekt projektvezetőjévé tette:'
      no_ideas: 'Még nincsenek ötletek'
      preheader: 'A %{organizationName} részvételi platformjának adminisztrátora éppen a következő projekt projektvezetőjévé tett téged'
      subject: 'Ön projektmenedzser lett a %{organizationName}platformon'
      text_design_participatory_process: 'Projektmenedzserként beállíthatja, hogy a felhasználók hogyan működjenek együtt a projekten belül. Az idővonal segítségével új fázisokat adhat hozzá. Ezen fázisok mindegyikének megvan a maga viselkedése az ötletek közzététele, kommentálása és szavazása tekintetében.'
      text_moderate_analyse_input: 'A projekt elindítása után megérkeznek az első ötletek. Heti jelentéseket fog kapni az összes kulcsfontosságú tevékenységről, hogy naprakész legyen a dolgokkal. A projektmenedzser nézetben található ötletek áttekintése segít megérteni, hogy mely ötletek kapták a legtöbb tetszésnyilvánítást és nemtetszést.'
      text_share_project_information: 'A kapott ötletek minőségének javítása érdekében kulcsfontosságú, hogy elegendő információt osszon meg: adjon hozzá projektleírást, csatoljon képeket (vázlatokat és terveket is), és kommunikáljon minden kapcsolódó eseményről. Ne feledje: a jó tájékoztatás megelőzi a jó részvételt!'
      title_design_participatory_process: 'Tervezze meg a részvételi folyamatot'
      title_moderate_analyse_input: 'Moderálja és elemezze a bemenetet'
      title_share_project_information: 'Adja meg a projekt adatait'
      title_what_can_you_do_moderator: 'Mit tehet projektmenedzserként?'
      title_you_became_moderator: 'Projektmenedzser lettél'
      x_ideas: '%{numberOfIdeas} ötletek'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Tekintse meg ezt a mappát'
      message_added_as_folderadmin: 'Mappakezelői jogokat kapott a %{organizationName}részvételi platformján a következő mappához:'
      no_projects: 'Még nincsenek projektek'
      preheader: 'A %{organizationName} részvételi platformjának adminisztrátora éppen a következő mappa kezelőjévé tett'
      subject: 'Projektmappakezelő lettél a %{organizationName}részvételi platformján'
      text_manage_folder: 'Egy mappa több részvételi projekt közös szervezésének módja. Mappakezelőként szerkesztheti a mappa- és mappaleírást, és új projekteket hozhat létre (a projektek törléséhez forduljon a platform rendszergazdájához). Ezenkívül projektkezelési jogokkal rendelkezik a mappán belüli összes projektre vonatkozóan, lehetővé téve a projektek szerkesztését, a bemenetek kezelését és a résztvevők e-mailezését.'
      text_moderate_analyse_input: 'A projektek elindítása után megérkeznek az első bemenetek. Hetente kapni fogsz jelentéseket a legfontosabb tevékenységekről, így folyamatosan nyomon követheted, mi történik. A „Platform kezelése” panelen található Bevitelkezelő segítségével megtekintheti és kezelheti a bevitelt, beleértve az állapotok hozzárendelését, valamint a bejegyzésekre és megjegyzésekre adott válaszokat.'
      text_design_participatory_process: 'A mappán belül kezelheti a különböző részvételi projekteket – konfigurálhatja a részvételi módot, hozzáadhat egy projektleírást, csatolhat képeket, és közölheti a kapcsolódó eseményeket. Azt is kezelheti, hogy a résztvevők hogyan lépnek kapcsolatba a projektekkel, beleértve a hozzáférési jogok beállítását, valamint a közzétételi, szavazási és megjegyzési beállítások konfigurálását.'
      title_design_participatory_process: 'Tervezze meg a részvételi folyamatot'
      title_moderate_analyse_input: 'Moderálja és elemezze a bemenetet'
      title_manage_folder: 'Kezelje a mappabeállításokat és hozzon létre új projekteket.'
      title_what_can_you_do_folderadmin: 'Mit tehet mappakezelőként?'
      title_added_as_folderadmin: 'Mappakezelőként adták hozzá'
      x_projects: '%{numberOfProjects} projektek'
    project_phase_started:
      cta_view_phase: 'Fedezze fel ezt az új szakaszt'
      event_description: 'Ez a projekt új szakaszba lépett a %{organizationName}platformon. További információért kattintson az alábbi linkre!'
      main_header: 'Új szakasz kezdődött a ''%{projectName}'' projektben'
      subtitle: 'Körülbelül ''%{projectName}'''
      new_phase: 'Ez a projekt a ''%{phaseTitle}'' fázisba lépett'
      subject: '%{projectName} új szakaszba lépett'
      preheader: 'Új szakasz kezdődött a %{projectName}számára'
    project_phase_upcoming:
      cta_view_phase: 'Állítsa be ezt az új szakaszt'
      event_description: 'A ''%{projectName}'' projekt hamarosan új szakaszba lép. Győződjön meg arról, hogy minden be van állítva ehhez a fázishoz: Van megfelelő leírás? A kiválasztott ötletek átkerülnek ebbe a fázisba? Egyéni e-mail kampányon keresztül szeretné tájékoztatni polgárait ennek a szakasznak a sajátosságairól?'
      main_header: '%{firstName}, egy projekt hamarosan új szakaszba lép'
      subtitle: 'Körülbelül ''%{projectName}'''
      new_phase: 'A projekt a ''%{phaseTitle}'' fázisba lép'
      subject: 'Mindent beállíthat a %{projectName}új szakaszához'
      preheader: 'Hamarosan új szakasz kezdődik a %{projectName}számára'
    project_published:
      subject: 'Egy új projektet tettek közzé a %{organizationName}platformon'
      header_title: 'Egy új projektet tettek közzé'
      header_message: 'A %{organizationName} részvételi platformja nemrég tette közzé a következő projektet:'
      preheader: 'Egy új projektet tettek közzé'
    project_review_request:
      subject: 'Felülvizsgálati kérelem: Egy projekt jóváhagyásra vár.'
      header: '%{requesterName} meghívta Önt a "%{projectTitle}" projekt áttekintésére'
      header_message: "Jelenleg a projekt vázlat módban van, és a felhasználók nem láthatják. Miután áttekintette és jóváhagyta, a moderátor közzéteheti."
      cta_review_project: "Tekintse át a projektet"
    project_review_state_change:
      subject: '"%{projectTitle}" jóváhagyva'
      header: '%{reviewerName} jóváhagyta a "%{projectTitle}" projektet'
      header_message: "A projekt készen áll az éles indításra. Bármikor közzéteheti, amikor készen áll!"
      cta_go_to_project: "Lépjen a projektbeállításokhoz"
    status_change_on_idea_you_follow:
      status_change: 'Ennek a bemenetnek az új állapota ''%{status}'''
      header_message: 'A %{organizationName} frissítette a ''%{input_title}'' bemenet állapotát a digitális részvételi platformjukon.'
      header_title: 'A követett bemenetnek új állapota van'
      subject: 'A "%{input_title}" állapota megváltozott'
      preheader: 'A követett bemenetnek új állapota van'
    user_digest:
      subject: "Ezen a héten a %{organizationName}részvételi platformján"
      commented: "%{authorFirstName} hozzászólt:"
      preheader: "A %{organizationName}heti áttekintése"
      title_your_weekly_report: "Fedezze fel, mi történt a múlt héten"
      intro_text: "Íme egy összefoglaló a %{organizationName}részvételi platformján történtekről."
      cta_go_to_the_platform: "Menj a platformra"
      title_no_activity_past_week: "Nem volt tevékenység az elmúlt héten"
      successful_proposals_title: "Elérte a küszöböt"
      successful_proposals_text: "Az elmúlt héten ezek elegendő támogatást kaptak ahhoz, hogy továbbléphessenek a következő szakaszba."
      today_by_author: "Ma %{author}"
      yesterday_by_author: "Tegnap %{author}"
      x_days_ago_by_author: "%{x} napja, %{author}"
      trending_title: "Felkapott"
      trending_text: "Érdekel, hogy mi történik a platformon? Íme a három legfelkapottabb hozzászólás, és az emberek szavai róluk."
      no_notifications: "Nincsenek értesítések"
      one_notification: "1 értesítés"
      multiple_notifications: "%{notifCount} értesítések"
      no_unread_notifications: "Nincsenek olvasatlan értesítései."
      unread_notifications: "Olvasatlan értesítései vannak. Látogassa meg a platformot, és fedezze fel, mi történik!"
    threshold_reached_for_admin:
      cta_process_initiative: 'Tegye ezt a javaslatot a következő lépésekhez'
      main_header: 'Egy javaslat elérte a szavazati küszöböt!'
      subject: '"%{input_title}" elérte a szavazati küszöböt'
      preheader: 'Mindenképpen tegye meg a következő lépéseket'
    welcome:
      cta_join_platform: 'Fedezze fel a platformot'
      subject: 'Üdvözöljük a %{organizationName}platformján'
      main_header: Üdvözöljük!
      message_welcome: 'Gratulálunk, sikeresen regisztráltál a %{organizationName}részvételi platformjára. Most már felfedezheti a platformot, és hallathatja hangját. Profilképet és rövid leírást is hozzáadhat, hogy elmondhassa másoknak, ki vagy.'
      preheader: 'Íme, mit tehet a %{organizationName}platformon'
    idea_published:
      subject:
        idea: 'Az ötleted megjelent'
        question: 'Kérdését közzétettük'
        contribution: 'Hozzájárulásod megjelent'
        project: 'A projektjét közzétették'
        issue: 'Az Ön problémája megjelent'
        option: 'Az Ön opcióját közzétettük'
        proposal: 'Az Ön javaslata megjelent'
        petition: 'Petícióját közzétették'
      main_header: 'Ön közzétette: "%{input_title}"'
      header_message: 'Gondoskodjunk róla, hogy elolvassák.'
      message_get_votes: 'Érjen el több embert'
      action_published_idea: 'Közzétett'
      action_add_image: '%{addImageLink} a láthatóság növeléséhez'
      add_image: 'Kép hozzáadása'
      action_share_fb: 'Értesítse ismerőseit a %{fbLink}címen'
      action_share_twitter: 'Tájékoztassa követőit a %{twitterLink}címen'
      action_send_email: 'Küldje el névjegyeit egy %{sendEmailLink}'
      send_email: email
      action_share_link: 'Oszd meg bármelyik csatornán a %{link}másolásával'
      link: link
      preheader: '%{firstName}, gratulálunk, hogy hozzászólását közzétette a %{organizationName}platformon. Most gyűjtsön támogatást.'
    your_input_in_screening:
      main_header:
        idea: 'Az Ön ötlete itt található: "%{prescreening_status_title}"'
        question: 'Kérdése a következőben található: "%{prescreening_status_title}"'
        contribution: 'Az Ön hozzájárulása a következőben található: "%{prescreening_status_title}"'
        project: 'Az Ön projektje "%{prescreening_status_title}"'
        issue: 'A probléma itt található: "%{prescreening_status_title}"'
        option: 'Az Ön beállítása a következőben található: "%{prescreening_status_title}"'
        proposal: 'Az Ön ajánlata a következőben található: "%{prescreening_status_title}"'
        petition: 'Az Ön petíciója itt található: "%{prescreening_status_title}"'
      message: 'A(z) "%{input_title}" a felülvizsgálat és jóváhagyás után mások számára látható lesz.'
      subject: '"%{input_title}" majdnem megjelent'
      preheader: 'Jelenleg %{prescreening_status_title}helyen van'
    voting_basket_submitted:
      subject: '%{organizationName}: Sikeresen szavaztál'
      preheader: 'Sikeresen szavaztál a %{organizationName}részvételi platformján'
      title_basket_submitted: 'Sikeresen szavaztál'
      event_description: 'Köszönjük a részvételt. A szavazatait rögzítettük. Látogassa meg a %{organizationName} platformot szavazatainak megtekintéséhez és kezeléséhez.'
      cta_see_votes_submitted: 'Lásd a leadott szavazatokat'
      cta_message: 'A részvételhez kattintson az alábbi gombra'
    native_survey_not_submitted:
      subject: '%{organizationName}: Majdnem kész! Nyújtsa be válaszait'
      preheader: 'Nem töltötte ki a felmérésre adott válaszát a %{organizationName}részvételi platformján'
      title_native_survey_not_submitted: 'Majdnem megvan! Nyújtsa be válaszait'
      body_native_survey_not_submitted: 'Elkezdted megosztani a válaszaidat a %{phaseTitle} oldalon, de nem küldted be őket. A beküldés a következő napon zárul: %{phaseEndDate}. Kattintson az alábbi gombra, hogy ott folytassa, ahol abbahagyta.'
      body_native_survey_not_submitted_no_date: 'Elkezdted megosztani a válaszaidat a %{phaseTitle} oldalon, de nem küldted be őket. Kattintson az alábbi gombra, hogy ott folytassa, ahol abbahagyta.'
      cta_complete_your_survey_response: 'Folytassa a felmérésre adott válaszát'
    voting_basket_not_submitted:
      subject: '%{organizationName}: Ön nem adta le szavazatát'
      preheader: 'Nem adtad le szavazatodat a %{organizationName}részvételi platformján'
      title_basket_not_submitted: 'Nem adtad le a szavazataidat'
      event_description: 'Kiválasztott néhány lehetőséget a %{contextTitle} számára, de nem küldte el a választását.'
      cta_view_options_and_vote: 'Tekintse meg a lehetőségeket és szavazzon'
      cta_message: 'Kattintson az alábbi gombra a kiválasztott opciók elküldéséhez'
    voting_last_chance:
      subject: '%{organizationName}: Utolsó lehetőség, hogy %{phaseTitle}-ra szavazz'
      preheader: 'Utolsó lehetőség szavazni %{phaseTitle} -ra a %{organizationName}részvételi platformján'
      title_last_chance: 'Utolsó lehetőség szavazni %{phaseTitle}-ra'
      body_1: 'A %{projectTitle} projekt szavazási szakasza holnap, éjfélkor véget ér.'
      body_2: 'Fogy az idő, és észrevettük, hogy még nem adtad le a szavazatodat! Cselekedjen most, egyszerűen kattintson az alábbi gombra a részvételhez.'
      body_3: 'Ezáltal számos lehetőséghez juthat hozzá, és megadhatja véleményét, ami kulcsfontosságú a projekt jövőjének eldöntésében.'
      cta_vote: 'Szavazás'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} szavazás eredménye kiderült!'
      preheader: '%{phaseTitle} szavazási eredmények derültek ki a %{organizationName}részvételi platformján'
      title_results: '%{phaseTitle} szavazás eredménye kiderült!'
      body_1: 'Megvannak az eredmények!'
      body_2: 'Megjelent a %{phaseTitle} szavazás eredménye a %{organizationName} platformon!'
      body_3: 'Javasoljuk, hogy tekintse át az eredményeket, és kövesse a következő lépésekkel kapcsolatos további frissítéseket.'
      cta_see_results: 'Lásd az eredményeket a platformon'
    event_registration_confirmation:
      subject: "Benne vagy! Regisztrációját a(z) \"%{eventTitle}\" számára megerősítették"
      preheader: "%{firstName}, köszönjük, hogy regisztráltál a %{eventTitle}számára"
      header_message: "%{firstName}, köszönjük a regisztrációt"
      event_details:
        labels:
          date: 'Dátum'
          location: 'Elhelyezkedés'
          online_link: 'Online link'
          description: 'Leírás'
          project: 'Projekt'
      cta_go_to_event: 'Tekintse meg az eseményt'
      cta_add_to_calendar: 'Add hozzá a naptáradhoz'
    voting_phase_started:
      subject: '%{organizationName}: A szavazási szakasz elkezdődött a %{projectName}számára'
      preheader: 'A %{projectName} szavazási szakasz elkezdődött a %{organizationName}részvételi platformján'
      event_description: 'A "%{projectName}" projekt arra kér, hogy szavazzon a %{numIdeas} opciók között:'
      cta_message: 'A részvételhez kattintson az alábbi gombra'
      cta_vote: 'Menjen a platformra szavazni'
    survey_submitted:
      subject: '%{organizationName}: Köszönöm válaszát! 🎉'
      preheader: 'Íme a beadvány részletei.'
      main_header: 'Köszönjük, hogy megosztotta gondolatait a következővel kapcsolatban: "%{projectName}"!'
      your_input_submitted: 'A "%{projectName}" beírása sikeresen elküldve.'
      if_you_would_like_to_review: 'Ha szeretné felülvizsgálni beadványát, lentebb letöltheti válaszait.'
      your_submission_has_id: 'Beadványának a következő egyedi azonosítója van:'
      you_can_use_this_id: 'Ezzel az azonosítóval kapcsolatba léphet a platform adminisztrátoraival abban az esetben, ha el szeretné távolítani a beküldött anyagot.'
      download_responses: 'Töltse le válaszait'
    admin_labels:
      recipient_role:
        admins: 'Az adminoknak'
        admins_and_managers: 'Adminoknak és menedzsereknek'
        managers: 'A vezetőknek'
        project_participants: 'A projekt résztvevőinek'
        registered_users: 'Regisztrált felhasználóknak'
      recipient_segment:
        admins: 'Adminok'
        admins_and_managers: 'Adminisztrátorok és menedzserek'
        admins_and_managers_assigned_to_the_input: 'A bemenethez rendelt adminisztrátorok és kezelők'
        admins_and_managers_managing_the_project: 'A projektet irányító rendszergazdák és menedzserek'
        admins_assigned_to_a_proposal: 'Az ajánlathoz rendelt adminisztrátorok'
        all_users: 'Minden felhasználó'
        all_users_who_uploaded_proposals: 'Minden felhasználó, aki ajánlatot töltött fel'
        managers: 'Menedzserek'
        managers_managing_the_project: 'A projektet irányító menedzserek'
        new_attendee: 'Újonnan regisztrált felhasználó'
        project_reviewers: 'Projektellenőrök és mappakezelők'
        project_review_requester: 'Felhasználó, aki kérte a projekt felülvizsgálatát'
        user_who_commented: 'Felhasználó, aki hozzászólt'
        user_who_is_invited_to_cosponsor_a_proposal: 'Felhasználó, akit meghívtak egy javaslat társszponzorálására'
        user_who_is_mentioned: 'Az említett felhasználó'
        user_who_is_receiving_admin_rights: 'Felhasználó, aki rendszergazdai jogokat kap'
        user_who_is_receiving_folder_moderator_rights: 'Felhasználó, aki mappamoderátori jogokat kap'
        user_who_is_receiving_project_moderator_rights: 'Felhasználó, aki projektmoderátori jogokat kap'
        user_who_published_the_input: 'Felhasználó, aki közzétette a bemenetet'
        user_who_published_the_proposal: 'Az ajánlatot közzétevő felhasználó'
        user_who_registers: 'Felhasználó, aki regisztrál'
        user_who_submitted_the_input: 'Felhasználó, aki beküldte a bevitelt'
        user_who_voted: 'Felhasználó, aki szavazott'
        user_who_was_invited: 'Meghívott felhasználó'
        user_with_unsubmitted_survey: 'Felhasználó, aki elindította a felmérést, de nem küldte el'
        user_with_unsubmitted_votes: 'Felhasználó, aki nem adta le szavazatát'
        users_who_engaged_but_not_voted: 'Felhasználók, akik részt vettek a projektben, de nem szavaztak'
        users_who_engaged_with_the_project: 'Felhasználók, akik részt vettek a projektben'
        users_who_follow_the_input: 'Felhasználók, akik követik a bevitelt'
        users_who_follow_the_project: 'A projektet követő felhasználók'
        users_who_follow_the_proposal: 'A javaslatot követő felhasználók'
      content_type:
        comments: 'Megjegyzések'
        content_moderation: 'Tartalom moderálása'
        events: 'Események'
        general: 'Általános'
        inputs: 'Bemenetek'
        internal_comments: 'Belső megjegyzések'
        permissions: 'Engedélyek'
        projects: 'Projektek'
        proposals: 'Javaslatok'
        reactions: 'Reakciók'
        voting: 'Szavazás'
        surveys: 'Felmérések'
      trigger:
        7_days_after_invite_is_sent: '7 nappal a meghívó elküldése után'
        7_days_before_the_project_changes_phase: '7 nappal a projektváltási szakasz előtt'
        comment_is_deleted: 'A megjegyzés törölve'
        comment_is_flagged_as_spam: 'A megjegyzés spamként van megjelölve'
        content_gets_flagged_as_innapropiate: 'A tartalom nem megfelelőként lesz megjelölve'
        initiative_resubmitted_for_review: 'A javaslatot újra benyújtották felülvizsgálatra'
        input_is_assigned: 'A bemenet hozzá van rendelve'
        input_is_flagged_as_spam: 'A bevitel spamként van megjelölve'
        input_is_published: 'A bemenetet közzétesszük'
        input_is_updated: 'A bemenet hivatalos visszajelzést kap'
        input_status_changes: 'Bemeneti állapot megváltozik'
        internal_comment_is_posted_on_idea_assigned_to_user: 'A belső megjegyzés a felhasználóhoz rendelt bemenetre kerül közzétételre'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'A belső megjegyzések a projektben vagy a felhasználó által kezelt mappában találhatók'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'A belső megjegyzés a bemenetre kerül közzétételre, amelyhez a felhasználó belső megjegyzést fűzött'
        internal_comment_is_posted_on_idea_user_moderates: 'A belső megjegyzéseket a bemeneti felhasználói moderátorokon teszik közzé'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'A belső megjegyzés a nem felügyelt projekt hozzá nem rendelt bemenetére kerül közzétételre'
        project_review_request: 'A moderátor a projekt felülvizsgálatát kérte'
        project_review_state_change: 'A bíráló jóváhagyta a projektet'
        new_input_awaits_screening: 'Új bemenet vár a vetítésre'
        new_input_is_published: 'Megjelent az új bemenet'
        new_proposal_is_posted: 'Új ajánlatot tettek közzé'
        project_phase_changes: 'Változások a projekt fázisában'
        project_published: 'A projekt megjelent'
        proposal_gets_reported_as_spam: 'Az ajánlatot spamként jelentik be'
        proposal_is_assigned_to_admin: 'Az ajánlat adminisztrátorhoz van rendelve'
        proposal_is_published: 'A javaslat közzétételre került'
        proposal_is_updated: 'A javaslat hivatalos visszajelzést kap'
        proposal_is_upvoted_above_threshold: 'A javaslatot a küszöb felett szavazták meg'
        proposal_status_changes: 'Változások az ajánlat állapotában'
        registration_to_event: 'Regisztráció egy eseményre'
        survey_1_day_after_draft_saved: '1 nappal azután, hogy a felhasználó utoljára elmentette a felmérést vázlatként'
        user_accepts_invitation_to_cosponsor_a_proposal: 'A felhasználó elfogadja a felkérést egy ajánlat társszponzorálására'
        user_comments: 'Felhasználói megjegyzések'
        user_comments_on_input: 'Felhasználói megjegyzések a bevitelhez'
        user_comments_on_proposal: 'Felhasználói megjegyzések az ajánlathoz'
        user_is_given_admin_rights: 'A felhasználó rendszergazdai jogokat kap'
        user_is_given_folder_moderator_rights: 'A felhasználó mappamoderátori jogokat kap'
        user_is_given_project_moderator_rights: 'A felhasználó projektmoderátori jogokat kap'
        user_is_invited_to_cosponsor_a_proposal: 'A felhasználót felkérik, hogy társszponzoráljon egy javaslatot'
        user_is_mentioned: 'A felhasználó meg van említve'
        user_is_mentioned_in_internal_comment: 'A felhasználó meg van említve a belső megjegyzésben'
        user_registers_for_the_first_time: 'A felhasználó először regisztrál'
        user_replies_to_comment: 'Felhasználó válaszol a megjegyzésre'
        user_replies_to_internal_comment: 'Felhasználó válaszol a belső megjegyzésre'
        voting_1_day_after_last_votes: '1 nappal a felhasználó utolsó szavazása után'
        voting_2_days_before_phase_closes: '2 nappal a szavazási szakasz lezárása előtt'
        voting_basket_submitted: 'A szavazatokat leadják'
        voting_phase_ended: 'A szavazási szakasz véget ért'
