tr:
  email_campaigns:
    campaign_type_description:
      "manual": <PERSON><PERSON><PERSON>
      "manual_project_participants": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resmi mesaj<PERSON>
      "admin_rights_received": Yönetici hakları alındı
      "comment_deleted_by_admin": <PERSON><PERSON><PERSON><PERSON>
      "comment_marked_as_spam": <PERSON><PERSON><PERSON> spam olarak bildirilmesi
      "comment_on_your_comment": <PERSON><PERSON><PERSON> bir yan<PERSON>
      "comment_on_idea_you_follow": <PERSON><PERSON><PERSON> et<PERSON> bir fikir hakkında yorum
      "community_monitor_report": <PERSON><PERSON><PERSON> gözlemci raporu
      "cosponsor_of_your_idea": <PERSON><PERSON> kullanıcı teklifime ortak sponsor olma davetimi kabul ediyor
      "event_registration_confirmation": <PERSON><PERSON><PERSON><PERSON> kay<PERSON><PERSON> onayı
      "idea_marked_as_spam": <PERSON><PERSON>rin spam olarak bildirilmesi
      "idea_published": <PERSON><PERSON><PERSON><PERSON> yayım<PERSON>ı
      "invitation_to_cosponsor_idea": <PERSON><PERSON> teklifin ortak sponsorluğuna davet
      "invite_received": <PERSON><PERSON><PERSON>
      "invite_reminder": <PERSON><PERSON><PERSON> an<PERSON>
      "internal_comment_on_idea_assigned_to_you": <PERSON><PERSON> atanan girdiye ilişkin dahili yorum
      "internal_comment_on_idea_you_commented_internally_on": Girdiye ilişkin dahili yorum Dahili olarak yorum yaptım
      "internal_comment_on_idea_you_moderate": Yönettiğim proje veya klasördeki girdiye ilişkin dahili yorum
      "internal_comment_on_unassigned_unmoderated_idea": Yönetilmeyen projede atanmamış girdi hakkında dahili yorum
      "internal_comment_on_your_internal_comment": İç yorumum üzerine iç yorum
      "mention_in_official_feedback": Bir bildirimde bahis
      "mention_in_internal_comment": Dahili bir yorumda bahsedin
      "new_comment_for_admin": Yönettiğim bir projede yeni yorum
      "new_idea_for_admin": Yönettiğim bir projede yeni fikir
      "official_feedback_on_idea_you_follow": Takip ettiğiniz bir fikir hakkında güncelleme
      "password_reset": Parola sıfırla
      "project_moderation_rights_received": Proje moderasyon hakları alındı
      "project_folder_moderation_rights_received": Klasör yöneticisi hakları alındı
      "project_phase_started": Yeni proje aşaması
      "project_phase_upcoming": Yaklaşan yeni proje aşaması
      "project_published": Proje yayınlandı
      "project_review_request": Proje inceleme talebi
      "project_review_state_change": Proje onaylandı
      "status_change_on_idea_you_follow": Takip ettiğiniz bir fikrin durum değişikliği
      "survey_submitted": Anket gönderildi
      "threshold_reached_for_admin": Öneri oylama eşiğine ulaştı
      "welcome": Kayıttan sonra
      "admin_digest": Yöneticiler için haftalık genel bakış
      "moderator_digest": Proje yöneticileri için haftalık genel bakış
      "assignee_digest": Atanmış fikirlere haftalık genel bakış
      "user_digest": Haftalık genel bakış
      "voting_basket_submitted": Oylamanın onaylanması
      "native_survey_not_submitted": Anket gönderilmedi
      "voting_basket_not_submitted": Gönderilmeyen oylar
      "voting_last_chance": Oy vermek için son şans
      "voting_phase_started": Oylama ile yeni proje aşaması
      "voting_results": Oylama sonuçları
      "your_input_in_screening": Girdilerim taranmayı bekliyor
    general:
      by_author: 'tarafından %{authorName}'
      author_wrote: '%{authorName} yazdı:'
      cta_goto_idea: 'Bu fikre git'
      cta_goto_input: 'Bu girişe gidin'
      cta_goto:
        idea: 'Bu fikre git'
        question: 'Bu soruya git'
        contribution: 'Bu katkıya gidin'
        project: 'Bu projeye gidin'
        issue: 'Bu sayıya gidin'
        option: 'Bu seçeneğe gidin'
        proposal: 'Bu teklife gidin'
        petition: 'Bu dilekçeye gidin'
      cta_goto_your:
        idea: 'Fikrinize gidin'
        question: 'Sorunuza gidin'
        contribution: 'Katkı payınıza gidin'
        project: 'Projenize gidin'
        issue: 'Sorununuza gidin'
        option: 'Seçeneğinize gidin'
        proposal: 'Teklifinize gidin'
        petition: 'Dilekçenize gidin'
      cta_goto_proposal: 'Bu teklife gidin'
      cta_goto_project: 'Bu projeye gidin'
    schedules:
      weekly:
        "0": "Haftalık, Pazar günleri %{hourOfDay}adresinde"
        "1": "Haftalık Pazartesi günleri %{hourOfDay}"
        "2": "Haftalık Salı günleri %{hourOfDay}"
        "3": "Haftalık Çarşamba günleri %{hourOfDay}"
        "4": "Haftalık Perşembe günleri %{hourOfDay}"
        "5": "Haftalık Cuma günleri %{hourOfDay}"
        "6": "Haftalık Cumartesi günleri %{hourOfDay}"
      quarterly: "Üç ayda bir, üç aylık dönemin ilk gününde"
    preview_data:
      first_name: 'Jane'
      last_name: 'Doe'
      display_name: 'Jane Doe'
      comment_body: 'Bu, e-postaların içeriğini önizlemek için kullanılan örnek bir yorumdur. Bu gerçek içerik değildir.'
      idea_title: 'Örnek Fikir'
    footer:
      "link_privacy_policy": "Gi̇zli̇li̇k Poli̇ti̇kası"
      "link_terms_conditions": "Hüküm ve Koşullar"
      "link_unsubscribe": "Abonelikten ayrıl"
      "powered_by": "Destekleyen"
      "recipient_statement": "Bu e-posta size Go Vocal tarafından %{organizationName}adına gönderilmiştir, çünkü siz %{organizationLink}sitesinin kayıtlı bir kullanıcısısınız."
      "unsubscribe_statement": "Gelecekte bu e-postaları almak istemiyorsanız %{unsubscribeLink} adresini ziyaret edebilirsiniz."
      "unsubscribe_text": "abonelikten ayrılabilirsiniz"
    follow:
      "unfollow_here": "Takip ettiğiniz bir öğe nedeniyle bu bildirimi aldınız. <a href=\"%{unfollow_url}\">Buradan takibi bırakabilirsiniz.</a>"
    manual:
      preheader: '%{organizationName}adresinden postanız var.'
    comment_deleted_by_admin:
      reason: 'Yorumunuzun silinme nedeni:'
      cta_view: 'Bu fikri görüntüle'
      event_description: '%{organizationName} bir fikir hakkında yazdığınız yorumu sildi.'
      main_header: '%{organizationName} yorumunuzu sildi'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Yorumunuz uygunsuz bulundu'
      irrelevant_reason: 'Yorumunuz konuyla alakasız bulundu'
      no_reason: 'Neden belirtilmedi'
      subject: 'Yorumunuz %{organizationName} platformundan silindi'
      preheader: 'Yorumunuz silindi'
    admin_digest:
      subject: '%{time} için haftalık yönetici raporunuz'
      preheader: '%{organizationName} ile ilgili yönetici özeti'
      title_your_weekly_report: '%{firstName}, haftalık raporunuz'
      text_introduction: 'Geçtiğimiz hafta en fazla etkinlik üreten girdileri derledik. Platformunuzda neler olduğunu öğrenin.'
      cta_visit_the_platform: 'Platformunuzu ziyaret edin'
      new_users: 'Yeni kullanıcılar'
      new_inputs: 'Yeni girişler'
      new_comments: 'Yeni yorumlar'
      title_activity_past_week: 'Geçen haftanın etkinliği'
      title_no_activity_past_week: 'Geçen hafta etkinlik yoktu'
      reached_threshold: 'Eşiğe ulaşıldı'
      yesterday_by_author: '%{author} tarafından dün'
      today_by_author: '%{author} tarafından bugün'
      x_days_ago_by_author: '%{author} tarafından %{x} gün önce'
    admin_rights_received:
      cta_manage_platform: 'Platformunuzu yönetin'
      message_you_became_administrator: '%{organizationName} katılım platformu için yönetici hakları aldınız.'
      preheader: '%{organizationName} katılım platformu için yönetici hakları almıştınız'
      subject: '%{organizationName} platformunda yönetici oldunuz'
      text_create_participatory_process: 'Yönetici olarak yeni katılım projeleri oluşturabilir ve yapılandırabilirsiniz. Zaman çizelgesini kullanarak yeni aşamalar ekleyebilirsiniz. Bu aşamaların her biri fikir yayınlama, yorum yapma ve oylama ile ilgili farklı davranışlar gösterebilir.'
      text_moderate_analyse_input: 'Projeler başlatıldıktan sonra fikirler ulaşmaya başlar. Her şeyden haberdar olmanız için tüm önemli faaliyetleri içeren haftalık raporlar alırsınız. Fikirlere genel bakış, girdileri yönetmenize ve onları işlerken iş birliği yapmanıza yardımcı olacaktır.'
      text_platform_setup: 'Yönetici olarak kendi katılım platformunuzu ayarlayabilirsiniz. Bir logo, görseller ve renkler seçin, ana sayfanıza kişisel bir mesaj yazın, davetiyeler gönderin, kullanıcılarınız hakkında bilmek istediklerinizi tanımlayın vb.'
      title_create_participatory_process: 'Katılımcı süreci tasarlama'
      title_moderate_analyse_input: 'Girdileri yönetme ve analiz etme'
      title_platform_setup: 'Platformu ayarlama'
      title_what_can_you_do_administrator: 'Yönetici olarak neler yapabilirsiniz?'
      title_you_became_administrator: 'Yönetici oldunuz'
    comment_marked_as_spam:
      by_author: '%{authorName} tarafından'
      commented: '%{authorName} yorum yaptı:'
      cta_review_comment: 'Yorumu inceleyin'
      days_ago: '%{numberOfDays} gün önce'
      event_description: '<strong>''%{post}''</strong> hakkında yayınlanan şu yorum bildirildi:'
      inappropriate_content: 'Yorum uygunsuz veya küçük düşürücü.'
      preheader: 'Yorumun spam olarak bildirilmesi ile ilgili işlem'
      reported_this_because: '%{reporterFirstName} bunu bildirdi:'
      subject: '%{organizationName}: %{firstName} %{lastName} bu yorumu spam olarak bildirdi'
      title_comment_spam_report: '%{firstName} %{lastName} bu yorumu spam olarak bildirdi'
      today: Bugün
      wrong_content: 'Bu yorum konuyla alakasız.'
      yesterday: Dün
    community_monitor_report:
      subject: 'Yeni bir toplum izleme raporu hazırlandı'
      title: 'Yeni bir toplum izleme raporu hazırlandı'
      text_introduction: 'Bir önceki çeyrek için bir topluluk izleme raporu oluşturulmuştur. Aşağıdaki butona tıklayıp giriş yaparak rapora erişebilirsiniz.'
      cta_report_button: 'Raporu görüntüle'
      report_name: 'Topluluk gözlemci raporu'
    cosponsor_of_your_idea:
      cta_reply_to: 'Teklifinizi görüntüleyin'
      event_description: 'Tebrikler! %{cosponsorName} teklifinize eş sponsorluk yapma davetinizi kabul etti.'
      main_header: '%{cosponsorName} teklifinize eş sponsorluk yapma davetinizi kabul etti'
      subject: '%{cosponsorName} teklifinize eş sponsorluk yapma davetinizi kabul etti'
      preheader: '%{cosponsorName} teklifinize eş sponsorluk yapma davetinizi kabul etti'
    assignee_digest:
      subject: 'Geri bildiriminizi gerektiren fikir sayısı: %{numberIdeas}'
      preheader: '%{organizationName} için atanan özeti'
      title_your_weekly_report: '%{firstName}, vatandaş girdisi geri bildiriminizi bekliyor'
      cta_manage_your_input: 'Girdilerinizi yönetin'
      x_inputs_need_your_feedback: 'girdilerin geri bildiriminize ihtiyacı var'
      title_assignment_past_week: 'Size atanan en son fikirler'
      title_no_assignment_past_week: 'Geçen hafta size yeni fikir atanmadı'
      yesterday_by_author: '%{author} tarafından dün'
      today_by_author: '%{author} tarafından bugün'
      x_days_ago_by_author: '%{author} tarafından %{x} gün önce'
      title_successful_past_week: 'Eşiğe ulaşan size atananlar'
    idea_marked_as_spam:
      cta_review: 'İnceleme'
      report_inappropriate_offensive_content: 'Bu içeriği uygunsuz veya küçük düşürücü buluyorum.'
      report_not_an_idea: 'Bu içerik bir fikir değil, burada bulunmaması gerekir.'
      subject: '%{organizationName} platformunda bir spam bildiriminiz var'
      preheader: 'Bu spam bildirimi ile ilgili işlem yapın'
      reported_this_because: '%{reporterFirstName} bunu bildirdi:'
      title_spam_report: '%{firstName} %{lastName} spam bildirdi'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Bu teklife eş sponsor olun'
      event_description: '%{authorName} yeni bir teklif hazırladı ve sizin de bu teklife destek vermenizi istiyor.'
      event_description_cosponsoring: 'Bir teklife eş-sponsorluk yapmak, <strong>adınızın</strong> teklifin diğer eş-sponsorlarının <strong>adlarıyla</strong> birlikte <strong>görüntüleneceği</strong> anlamına gelir.'
      event_description_before_action: 'Teklifi görmek ve daveti kabul etmek için hesabınıza giriş yapmış olmanız gerekmektedir.'
      event_description_action: 'Teklifi okumak için aşağıya tıklayın.'
      main_header: 'Bir teklife eş sponsorluk yapmaya davet edildiniz'
      subject: 'Bir teklife eş sponsorluk yapmaya davet edildiniz'
      preheader: '%{authorName}adresindeki teklife eş sponsorluk yapmaya davet edildiniz.'
    invite_reminder:
      cta_accept_invitation: 'Davetiyenizi kabul edin'
      invitation_header: 'Davetiyeniz beklemede'
      preheader: '%{organizationName} birkaç gün önce katılım platformlarına katılmanız için size bir davet gönderdi.'
      invitation_expiry_message: 'Bu davetiyenin süresi yaklaşık %{expiryDaysRemaining} gün içinde dolacaktır.'
      subject: '%{organizationName} katılım platformu için bekleyen davetiye'
    invite_received:
      added_a_message: '%{organizationName} aşağıdaki mesajı yazdı:'
      cta_accept_invitation: 'Davetiyenizi kabul edin'
      invitation_header: 'Davet edildiniz!'
      invitation_header_message: '%{organizationName} sizi katılım platformlarına davet etti.'
      invitation_expiry_message: 'Bu davet %{expiryDays} gün içinde sona erecektir.'
      preheader: '%{organizationName} katılım platformlarına katılmanız için size bir davet gönderdi.'
      subject: '%{organizationName} platformuna katılmaya davet edildiniz'
    mention_in_comment:
      cta_reply_to: '%{commentAuthor} yorumunu yanıtlayın'
      event_description: '%{commentAuthorFull} ''%{post}‘ fikri hakkındaki yorumunda sizden bahsetti. %{commentAuthor} ile sohbete başlamak için aşağıdaki bağlantıya tıklayın'
      main_header: 'İnsanlar sizin hakkınızda konuşuyor'
      subject: 'Birileri %{organizationName} platformunda sizden bahsetti'
      preheader: '%{commentAuthor} bir yorumda sizden bahsetti'
    mention_in_internal_comment:
      cta_reply_to: 'Yorumu görüntüle %{firstName}'
      event_description: '%{authorNameFull} bir iç yorumda sizden bahsetti.'
      subject: '%{firstName} bir iç yorumda sizden bahsetti.'
      main_header: '%{firstName} bir iç yorumda sizden bahsetti.'
      preheader: '%{authorNameFull} bir iç yorumda sizden bahsetti.'
    moderator_digest:
      subject: '%{project_title} haftalık proje yöneticisi raporunuz'
      preheader: '%{organizationName} proje yöneticisi özeti'
      title_your_weekly_report: '%{firstName}, haftalık raporunuz'
      text_introduction: 'Geçtiğimiz hafta en fazla etkinlik üreten girdileri derledik. Projenizde neler olduğunu öğrenin.'
      cta_manage: 'Projenizi yönetin'
      new_users: 'Yeni kullanıcılar'
      new_ideas: 'Yeni fikirler'
      new_comments: 'Yeni yorumlar'
      title_inputs_past_week: 'Geçtiğimiz hafta yeni girdiler'
      title_no_inputs_past_week: 'Geçtiğimiz hafta yeni girdi yok'
      title_threshold_reached: Geçtiğimiz hafta içinde ulaşılan eşik
      yesterday_by_author: '%{author} tarafından dün'
      today_by_author: '%{author} tarafından bugün'
      x_days_ago_by_author: '%{author} tarafından %{x} gün önce'
    new_comment_for_admin:
      commented: '%{authorFirstName} yorum yaptı:'
      cta_reply_to: '%{commentAuthor} adlı kişinin yorumunu görün'
      days_ago: '%{numberOfDays} gün önce'
      event_description: '%{authorName} platformunuza yeni bir yorum ekledi.'
      main_header: '%{firstName}, platformunuzda yeni bir yorum yayınlandı'
      subject: '%{organizationName} platformunda yeni bir yorum yayınlandı'
      preheader: '%{authorName} bir yorum bıraktı'
      today: Bugün
      yesterday: Dün
    comment_on_idea_you_follow:
      cta_reply_to: 'Yanıtla %{commentAuthor}'
      event_description: '%{authorNameFull} placed a reaction on ''%{inputTitle}''. Click the button below to continue the conversation with %{authorName}.'
      main_header:
        idea: '%{authorName} takip ettiğiniz bir fikir hakkında yorum yaptı'
        question: '%{authorName} takip ettiğiniz bir soruya yorum yaptı'
        contribution: '%{authorName} takip ettiğiniz bir katkıya yorum yaptı'
        project: '%{authorName} takip ettiğiniz bir proje hakkında yorum yaptı'
        issue: '%{authorName} takip ettiğiniz bir konu hakkında yorum yaptı'
        option: '%{authorName} takip ettiğiniz bir seçenek hakkında yorum yaptı'
        proposal: '%{authorName} takip ettiğiniz bir teklif hakkında yorum yaptı'
        petition: '%{authorName} takip ettiğiniz bir dilekçeye yorum yaptı'
      subject: 'There''s a new comment on "%{input_title}"'
      preheader: '%{authorName} %{organizationName}için bir fikir üzerine bir yorum bıraktı'
    new_idea_for_admin:
      main_header_publication: 'Platformunuzda yeni bir girdi yayınlandı'
      event_description_publication: '%{authorName} platformunuza yeni bir girdi gönderdi. Şimdi keşfedin, geri bildirimde bulunun veya durumunu değiştirin!'
      cta_publication: '%{authorName}adresine geri bildirimde bulunun'
      main_header_prescreening: 'Bir girdi, gözden geçirmenizi gerektiriyor'
      event_description_prescreening: '%{authorName} platformunuza yeni bir girdi gönderdi.'
      input_not_visible_prescreening: 'Siz durumunu değiştirene kadar <b>girdi görünür olmayacaktır</b>.'
      cta_prescreening: 'Girdiyi gözden geçirin'
      days_ago: '%{numberOfDays} gün önce'
      preheader: '%{authorName} platformunuza yeni bir fikir yayımladı'
      today: Bugün
      yesterday: Dün
    comment_on_your_comment:
      cta_reply_to: '%{firstName} yorumunu yanıtlayın'
      event_description: '%{authorNameFull} katılım platformundaki ''%{post}'' ile ilgili yorumunuza yanıt yazdı. %{authorName} ile sohbete devam etmek için aşağıdaki düğmeye tıklayın.'
      subject: '%{organizationName} platformundaki yorumunuz hakkında bir yanıt aldınız'
      main_header: '%{authorName} yorumunuzu yanıtladı'
      preheader: '%{authorName} %{organizationName} platformundaki yorumunuzu yanıtladı'
      replied: '%{authorFirstName} yanıtı:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Yorumu görüntüle %{firstName}'
      event_description: '%{authorNameFull} iç yorumunuz üzerine yorum yaptı.'
      subject: '''%{post}'' hakkındaki dahili yorumunuz hakkında bir yorum aldınız.'
      main_header: '''%{post}'' hakkındaki dahili yorumunuz hakkında bir yorum aldınız.'
      preheader: '%{authorName} ''%{post}'' hakkındaki dahili yorumunuza yanıt verdi'
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Yorumu görüntüle %{firstName}'
      event_description: '%{authorNameFull} Size atanan bir girdi hakkında dahili olarak yorum yaptı.'
      subject: '''%{post}'' yeni bir dahili yoruma sahiptir'
      main_header: '''%{post}'' yeni bir dahili yoruma sahiptir'
      preheader: '''%{post}'' yeni bir dahili yoruma sahiptir'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Yorumu görüntüle %{firstName}'
      event_description: '%{authorNameFull} dahili olarak yorumladığınız bir girdi üzerinde dahili olarak yorum yaptı.'
      subject: '''%{post}'' yeni bir dahili yoruma sahiptir'
      main_header: '''%{post}'' yeni bir dahili yoruma sahiptir'
      preheader: '''%{post}'' yeni bir dahili yoruma sahiptir'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Yorumu görüntüle %{firstName}'
      event_description: '%{authorNameFull} Yönettiğiniz bir proje veya klasördeki bir girdi hakkında dahili olarak yorum yaptı.'
      subject: '''%{post}'' yeni bir dahili yoruma sahiptir'
      main_header: '''%{post}'' yeni bir dahili yoruma sahiptir'
      preheader: '''%{post}'' yeni bir dahili yoruma sahiptir'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Yorumu görüntüle %{firstName}'
      event_description: '%{authorNameFull} Yönetilmeyen bir projedeki atanmamış bir girdi hakkında dahili olarak yorum yaptı.'
      subject: '''%{post}'' yeni bir dahili yoruma sahiptir'
      main_header: '''%{post}'' yeni bir dahili yoruma sahiptir'
      preheader: '''%{post}'' yeni bir dahili yoruma sahiptir'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} gave an update on ''%{input_title}''.'
      header_title: 'There''s an update on "%{input_title}"'
      subject: 'Official feedback was posted on "%{input_title}"'
      preheader: 'There''s an update on an input you follow'
    mention_in_official_feedback:
      cta_reply_to: '%{organizationName} yorumunu yanıtlayın'
      event_description: '%{organizationName} ''%{post}‘ ile ilgili geri bildiriminde sizden bahsetti. %{organizationName} ile sohbete katılmak için aşağıdaki bağlantıya tıklayın'
      main_header: 'Sizden bahsedildi'
      subject: '%{organizationName} geri bildiriminde sizden bahsetti'
      preheader: '%{commentAuthor} geri bildirimde sizden bahsetti'
    project_moderation_rights_received:
      cta_manage_project: 'Bu projeyi yönetin'
      message_you_became_moderator: '%{organizationName} katılım platformundaki bir yönetici sizi şu projenin proje yöneticisi yaptı:'
      no_ideas: 'Henüz fikir yok'
      preheader: '%{organizationName} katılım platformundaki bir yönetici sizi aşağıdaki projenin proje yöneticisi yaptı'
      subject: '%{organizationName} platformunda proje yöneticisi oldunuz'
      text_design_participatory_process: 'Proje yöneticisi olarak insanların projenizle etkileşim kurma biçimini yapılandırabilirsiniz. Zaman çizelgesini kullanarak yeni aşamalar ekleyebilirsiniz. Bu aşamaların her biri fikir yayınlama, yorum yapma ve oylama ile ilgili farklı davranışlar gösterebilir.'
      text_moderate_analyse_input: 'Proje başlatıldıktan sonra fikirler ulaşmaya başlar. Her şeyden haberdar olmanız için tüm önemli faaliyetleri içeren haftalık raporlar alırsınız. Proje yöneticisi görünümünde bulabileceğiniz fikirlere genel bakış, en çok hangi fikirlerin olumlu veya olumsuz oy aldığını anlamanıza yardımcı olacaktır.'
      text_share_project_information: 'Aldığınız fikirlerin kalitesini yükseltmek için yeterli bilgi paylaşımı çok önemlidir: proje açıklaması ekleme, görseller (çizimler ve planlar dahil) ekleme ve tüm ilgili etkinlikleri bildirme. Unutmayın; iyi bilgilendirme katılımın iyi olmasını sağlar!'
      title_design_participatory_process: 'Katılımcı süreci tasarlama'
      title_moderate_analyse_input: 'Girdileri yönetme ve analiz etme'
      title_share_project_information: 'Proje açıklaması girin'
      title_what_can_you_do_moderator: 'Proje yöneticisi olarak neler yapabilirsiniz?'
      title_you_became_moderator: 'Proje yöneticisi oldunuz'
      x_ideas: '%{numberOfIdeas} fikir'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Bu klasörü görüntüle'
      message_added_as_folderadmin: '%{organizationName} katılım platformunda şu klasör için klasör yöneticisi hakları aldınız:'
      no_projects: 'Henüz proje yok'
      preheader: '%{organizationName} katılım platformundan bir yönetici sizi aşağıdaki klasörün yöneticisi yaptı'
      subject: '%{organizationName} katılım platformunda proje klasörü yöneticisi oldunuz'
      text_manage_folder: 'Klasörler birden fazla katılım projesini organize etmenin bir yöntemidir. Klasör yöneticisi olarak klasörü ve klasör açıklamasını yönetebilir ve yeni projeler oluşturabilirsiniz (projeleri silmek için platformunuzun yöneticisi ile iletişim kurun). Ayrıca, klasörün içindeki tüm projeler için, projeleri düzenlemenize ve girdileri ve e-posta katılımcılarını yönetmenize izin veren proje yönetimi haklarına sahip olursunuz.'
      text_moderate_analyse_input: 'Projeler başlatıldığında girdiler gelmeye başlar. Her şeyden haberdar olmanız için önemli faaliyetleri içeren haftalık raporlar alırsınız. ''Platformu Yönet'' panonuzdaki Girdi Yöneticisi girdileri görmenize, durum atama, yayınları ve yorumları yanıtlama gibi girdi yönetimi işlemlerini gerçekleştirmenize olanak sağlar.'
      text_design_participatory_process: 'Katılım yöntemini yapılandırarak, proje açıklaması ve görseller ekleyerek ve ilgili etkinlikleri bildirerek klasörünüzdeki farklı katılım projelerini yönetebilirsiniz. Ayrıca, erişim hakları atama, yayın, oy ve yorum ayarlarını yapılandırma gibi konular dahil olmak üzere, katılımcıların projelerinizle etkileşim kurma şeklini de yönetebilirsiniz.'
      title_design_participatory_process: 'Katılımcı süreci tasarlama'
      title_moderate_analyse_input: 'Girdileri yönetme ve analiz etme'
      title_manage_folder: 'Klasör ayarlarını yönetin ve yeni projeler oluşturun.'
      title_what_can_you_do_folderadmin: 'Klasör yöneticisi olarak neler yapabilirsiniz?'
      title_added_as_folderadmin: 'Klasör yöneticisi olarak eklendiniz'
      x_projects: '%{numberOfProjects} proje'
    project_phase_started:
      cta_view_phase: 'Bu yeni aşamayı inceleyin'
      event_description: 'Bu proje, %{organizationName} platformunda yeni bir aşamaya geçti. Daha fazla bilgi için aşağıdaki bağlantıyı tıklayın.'
      main_header: '''%{projectName}'' adlı proje için yeni bir aşama başladı'
      subtitle: 'Hakkında ''%{projectName}'''
      new_phase: 'Bu proje ''%{phaseTitle}'' aşamasına geçti'
      subject: '%{projectName} yeni bir aşamaya girdi'
      preheader: '%{projectName} adlı proje için yeni bir aşama başladı'
    project_phase_upcoming:
      cta_view_phase: 'Bu yeni aşamayı ayarlayın'
      event_description: '''%{projectName}'' adlı proje yakında yeni bir aşamaya geçiyor. Bu aşama için her şeyin hazır olduğundan emin olun: Uygun bir açıklama var mı? Seçilen fikirler bu aşamaya aktarıldı mı? Özel bir e-posta kampanyası ile vatandaşlarınızı bu aşamanın özellikleri hakkında bilgilendirmek istiyor musunuz?'
      main_header: '%{firstName}, bir proje yakında yeni bir aşamaya geçiyor'
      subtitle: 'Hakkında ''%{projectName}'''
      new_phase: 'Proje ''%{phaseTitle}'' aşamasına geçecek'
      subject: '%{projectName} için yeni aşama ile ilgili her şeyi ayarlayın'
      preheader: '%{projectName} için yakında yeni bir aşama başlıyor'
    project_published:
      subject: '%{organizationName}platformunda yeni bir proje yayınlandı'
      header_title: 'Yeni bir proje yayınlandı'
      header_message: '%{organizationName} katılım platformu aşağıdaki projeyi yayınladı:'
      preheader: 'Yeni bir proje yayınlandı'
    project_review_request:
      subject: 'İnceleme talebi: Bir proje onay için bekliyor.'
      header: '%{requesterName} sizi "%{projectTitle}" projesini incelemeye davet etti.'
      header_message: "Şu anda proje taslak modundadır ve kullanıcılar tarafından görülemez. Siz inceleyip onayladıktan sonra moderatör projeyi yayınlayabilecektir."
      cta_review_project: "Projeyi gözden geçirin"
    project_review_state_change:
      subject: '"%{projectTitle}" onaylanmıştır'
      header: '%{reviewerName} "%{projectTitle}" projesini onayladı.'
      header_message: "Proje artık yayına girmeye hazır. Hazır olduğunuzda yayınlayabilirsiniz!"
      cta_go_to_project: "Proje ayarlarına gidin"
    status_change_on_idea_you_follow:
      status_change: 'The new status of this input is ''%{status}'''
      header_message: '%{organizationName} updated the status of the input ''%{input_title}'' on their digital participation platform.'
      header_title: 'An input you follow has a new status'
      subject: 'The status of "%{input_title}" has changed'
      preheader: 'An input you follow has a new status'
    user_digest:
      subject: "%{organizationName} katılım platformundaki etkinlik durumunuz"
      commented: "%{authorFirstName} yorum yaptı:"
      preheader: "%{organizationName} haftalık genel bakışı"
      title_your_weekly_report: "Geçen hafta neler olduğunu öğrenin"
      intro_text: "%{organizationName} katılım platformunda geçen hafta neler olduğunun bir özeti."
      cta_go_to_the_platform: "Platforma gidin"
      title_no_activity_past_week: "Geçen hafta etkinlik yoktu"
      successful_proposals_title: "Eşiğe ulaşıldı"
      successful_proposals_text: "Bu öneriler, bir sonraki aşamaya geçmek için yeterli desteği aldı! Bundan sonra ne olacağı ile ilgili daha fazla bilgi için öneriye tıklayın."
      today_by_author: "%{author} tarafından bugün"
      yesterday_by_author: "%{author} tarafından dün"
      x_days_ago_by_author: "%{author} tarafından %{x} gün önce"
      trending_title: "Trend"
      trending_text: "Platformda neler olup bittiğini merak ediyor musunuz? İşte en trend üç katkı ve insanların onlar hakkında söyledikleri."
      no_notifications: "Bildirim yok"
      one_notification: "1 bildirim"
      multiple_notifications: "%{notifCount} bildirim"
      no_unread_notifications: "Okunmamış bildiriminiz yok. Girdilerinizle katkı yapmak ve yeni bildirimler oluşturmak için platformu ziyaret edin."
      unread_notifications: "Okunmamış bildirileriniz var. Neler olup bittiğini öğrenmek için platformu ziyaret edin."
    threshold_reached_for_admin:
      cta_process_initiative: 'Bu girişimi sonraki adımlara taşıyın'
      main_header: 'Bir girişim oylama eşiğine ulaştı.'
      subject: 'Bir girişim, platformunuzdaki oylama eşiğine ulaştı'
      preheader: 'Sonraki adımları mutlaka gerçekleştirin'
    welcome:
      cta_join_platform: 'Platformu keşfedin'
      subject: '%{organizationName} platformuna hoş geldiniz'
      main_header: Hoş geldiniz!
      message_welcome: 'Tebrikler! %{organizationName} katılım platformunda başarıyla kaydoldunuz. Şimdi platformu keşfedebilir ve sesinizi duyurabilirsiniz. Ayrıca, kendinizi başkalarına tanıtmak için bir profil resmi ve kısa açıklama ekleyebilirsiniz.'
      preheader: '%{organizationName} platformunda şunları yapabilirsiniz'
    idea_published:
      subject:
        idea: 'Fikriniz yayınlandı'
        question: 'Sorunuz yayınlandı'
        contribution: 'Katkınız yayınlandı'
        project: 'Projeniz yayınlandı'
        issue: 'Sayınız yayınlandı'
        option: 'Seçeneğiniz yayınlandı'
        proposal: 'Teklifiniz yayınlandı'
        petition: 'Dilekçeniz yayınlandı'
      main_header: 'Bir fikir yayınladınız! Şimdi okunmasını sağlayalım.'
      header_message: 'Okunduğundan emin olalım.'
      message_get_votes: 'Fikrinizi daha fazla kişiye ulaştırın:'
      action_published_idea: 'Yayımlanan fikir'
      action_add_image: 'Görünürlüğünüzü artırmak için %{addImageLink}'
      add_image: 'Görsel ekleyin'
      action_share_fb: '%{fbLink} arkadaşlarınıza haber verin'
      action_share_twitter: '%{twitterLink} takipçilerinize haber verin'
      action_send_email: 'İlgili kişilerinize bir %{sendEmailLink} gönderin'
      send_email: e-posta
      action_share_link: '%{link} kopyalayarak her tür kanaldan paylaşın'
      link: Bağlantı
      preheader: '%{firstName}, %{organizationName} platformunda fikrinizi yayınladığınız için tebrik ederiz. Şimdi destek toplayın.'
    your_input_in_screening:
      main_header:
        idea: 'Fikriniz "%{prescreening_status_title}" adresinde yer almaktadır.'
        question: 'Sorunuz "%{prescreening_status_title}" adresinde yer almaktadır.'
        contribution: 'Katkınız "%{prescreening_status_title}" adresinde yer almaktadır.'
        project: 'Projeniz "%{prescreening_status_title}" adresinde'
        issue: 'Sorununuz "%{prescreening_status_title}" adresinde'
        option: 'Seçeneğiniz "%{prescreening_status_title}" adresinde.'
        proposal: 'Teklifiniz "%{prescreening_status_title}" adresinde yer almaktadır.'
        petition: 'Dilekçeniz "%{prescreening_status_title}" adresinde yer almaktadır.'
      message: '"%{input_title}" ifadesi incelendikten ve onaylandıktan sonra başkaları tarafından görülebilir hale gelecektir.'
      subject: '"%{input_title}" neredeyse yayınlandı'
      preheader: 'Şu anda %{prescreening_status_title}adresinde'
    voting_basket_submitted:
      subject: '%{organizationName}: Başarılı bir şekilde oy kullandınız'
      preheader: '%{organizationName}katılım platformunda başarıyla oy kullandınız'
      title_basket_submitted: 'Başarılı bir şekilde oy kullandınız'
      event_description: 'Katıldığınız için teşekkürler. Oylarınız kaydedilmiştir. Oylarınızı görmek ve yönetmek için %{organizationName} platformunu ziyaret edin.'
      cta_see_votes_submitted: 'Verilen oyları görün'
      cta_message: 'Katılmak için aşağıdaki butona tıklayın'
    native_survey_not_submitted:
      subject: '%{organizationName}: Neredeyse bitti! Cevaplarınızı gönderin'
      preheader: 'Anket yanıtınızı %{organizationName}katılım platformunda tamamlamadınız'
      title_native_survey_not_submitted: 'Neredeyse bitti! Cevaplarınızı gönderin'
      body_native_survey_not_submitted: 'Cevaplarınızı %{phaseTitle} adresinde paylaşmaya başladınız ancak göndermediniz. Gönderimler %{phaseEndDate}adresinde sona erecektir. Kaldığınız yerden devam etmek için aşağıdaki düğmeye tıklayın.'
      body_native_survey_not_submitted_no_date: 'Cevaplarınızı %{phaseTitle} adresinde paylaşmaya başladınız ancak göndermediniz. Kaldığınız yerden devam etmek için aşağıdaki butona tıklayın.'
      cta_complete_your_survey_response: 'Anket yanıtınıza devam edin'
    voting_basket_not_submitted:
      subject: '%{organizationName}: Oylarınızı teslim etmediniz'
      preheader: 'Oylarınızı %{organizationName}katılım platformuna göndermediniz'
      title_basket_not_submitted: 'Oylarınızı teslim etmediniz'
      event_description: '%{contextTitle} için birkaç seçenek belirlediniz ancak seçiminizi göndermediniz.'
      cta_view_options_and_vote: 'Seçenekleri görüntüleyin ve oy verin'
      cta_message: 'Seçtiğiniz seçenekleri göndermek için aşağıdaki düğmeyi tıklayın'
    voting_last_chance:
      subject: '%{organizationName}: %{phaseTitle}adresine oy vermek için son şans'
      preheader: '%{organizationName}katılım platformunda %{phaseTitle} için oy kullanmak için son şans'
      title_last_chance: 'Oy vermek için son şans %{phaseTitle}'
      body_1: '%{projectTitle} projesi için oylama aşaması yarın gece yarısı sona eriyor.'
      body_2: 'Zaman daralıyor ve henüz oyunuzu kullanmadığınızı fark ettik! Katılmak için aşağıdaki butona tıklayarak hemen harekete geçin.'
      body_3: 'Bunu yaparak, bir dizi seçeneğe erişim kazanacak ve bu projenin geleceğine karar vermede çok önemli olan katkılarınızı sunma şansına sahip olacaksınız.'
      cta_vote: 'Oylama'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} oylama sonuçları açıklandı!'
      preheader: '%{phaseTitle} oy sonuçlari %{organizationName}katilim platformu üzeri̇nden açiklandi'
      title_results: '%{phaseTitle} oylama sonuçlari açiklandi!'
      body_1: 'Sonuçlar geldi!'
      body_2: '%{organizationName} platformundaki %{phaseTitle} oylamasının sonuçları yayınlandı!'
      body_3: 'Sonuçları incelemenizi ve sonraki adımlarla ilgili güncellemeler için bizi takip etmenizi öneririz.'
      cta_see_results: 'Platformdaki sonuçları görün'
    event_registration_confirmation:
      subject: "Girdiniz! \"%{eventTitle}\" için kaydınız onaylandı"
      preheader: "%{firstName}, %{eventTitle}adresine kaydolduğunuz için teşekkürler"
      header_message: "%{firstName}'a kaydolduğunuz için teşekkür ederiz."
      event_details:
        labels:
          date: 'Tarih'
          location: 'Konum'
          online_link: 'Çevrimiçi bağlantı'
          description: 'Açıklama'
          project: 'Proje'
      cta_go_to_event: 'Etkinliği görüntüleyin'
      cta_add_to_calendar: 'Takviminize ekleyin'
    voting_phase_started:
      subject: '%{organizationName}: %{projectName}için oylama aşaması başladı'
      preheader: '%{projectName} için %{organizationName}katılım platformunda oylama süreci başladı.'
      event_description: '"%{projectName}" projesi sizden %{numIdeas} seçenekleri arasında oylama yapmanızı istiyor:'
      cta_message: 'Katılmak için aşağıdaki butona tıklayın'
      cta_vote: 'Oy vermek için platforma gidin'
    survey_submitted:
      subject: '%{organizationName}: Cevabınız için teşekkür ederim! 🎉'
      preheader: 'İşte başvurunuzun ayrıntıları.'
      main_header: '"%{projectName}" hakkındaki düşüncelerinizi paylaştığınız için teşekkür ederiz!'
      your_input_submitted: '"%{projectName}" için girdiniz başarıyla gönderildi.'
      if_you_would_like_to_review: 'Gönderinizi gözden geçirmek isterseniz, yanıtlarınızı aşağıdan indirebilirsiniz.'
      your_submission_has_id: 'Gönderiniz aşağıdaki benzersiz tanımlayıcıya sahiptir:'
      you_can_use_this_id: 'Gönderinizin kaldırılmasını istemeniz durumunda platform yöneticileriyle iletişime geçmek için bu tanımlayıcıyı kullanabilirsiniz.'
      download_responses: 'Yanıtlarınızı indirin'
    admin_labels:
      recipient_role:
        admins: 'Yöneticilere'
        admins_and_managers: 'Yöneticilere ve müdürlere'
        managers: 'Yöneticilere'
        project_participants: 'Proje katılımcılarına'
        registered_users: 'Kayıtlı kullanıcılara'
      recipient_segment:
        admins: 'Yöneticiler'
        admins_and_managers: 'Yöneticiler ve müdürler'
        admins_and_managers_assigned_to_the_input: 'Girişe atanan yöneticiler ve müdürler'
        admins_and_managers_managing_the_project: 'Projeyi yöneten yöneticiler ve müdürler'
        admins_assigned_to_a_proposal: 'Bir teklife atanan yöneticiler'
        all_users: 'Tüm kullanıcılar'
        all_users_who_uploaded_proposals: 'Teklif yükleyen tüm kullanıcılar'
        managers: 'Yöneticiler'
        managers_managing_the_project: 'Projeyi yöneten yöneticiler'
        new_attendee: 'Yeni kayıtlı kullanıcı'
        project_reviewers: 'Proje gözden geçiricileri ve klasör yöneticileri'
        project_review_requester: 'Proje incelemesini talep eden kullanıcı'
        user_who_commented: 'Yorum yapan kullanıcı'
        user_who_is_invited_to_cosponsor_a_proposal: 'Bir teklife eş sponsorluk yapmaya davet edilen kullanıcı'
        user_who_is_mentioned: 'Bahsi geçen kullanıcı'
        user_who_is_receiving_admin_rights: 'Yönetici haklarını alan kullanıcı'
        user_who_is_receiving_folder_moderator_rights: 'Klasör moderatörü haklarını alan kullanıcı'
        user_who_is_receiving_project_moderator_rights: 'Proje moderatörü haklarını alan kullanıcı'
        user_who_published_the_input: 'Girdiyi yayınlayan kullanıcı'
        user_who_published_the_proposal: 'Teklifi yayınlayan kullanıcı'
        user_who_registers: 'Kayıt olan kullanıcı'
        user_who_submitted_the_input: 'Girdiyi gönderen kullanıcı'
        user_who_voted: 'Oy veren kullanıcı'
        user_who_was_invited: 'Davet edilen kullanıcı'
        user_with_unsubmitted_survey: 'Anketini başlatmış ancak göndermemiş kullanıcı'
        user_with_unsubmitted_votes: 'Oylarını göndermemiş olan kullanıcı'
        users_who_engaged_but_not_voted: 'Proje ile ilgilenen ancak oy kullanmayan kullanıcılar'
        users_who_engaged_with_the_project: 'Proje ile etkileşime geçen kullanıcılar'
        users_who_follow_the_input: 'Girişi takip eden kullanıcılar'
        users_who_follow_the_project: 'Projeyi takip eden kullanıcılar'
        users_who_follow_the_proposal: 'Teklifi takip eden kullanıcılar'
      content_type:
        comments: 'Yorumlar'
        content_moderation: 'İçerik moderasyonu'
        events: 'Etkinlikler'
        general: 'Genel'
        inputs: 'Girişler'
        internal_comments: 'Dahili yorumlar'
        permissions: 'İzinler'
        projects: 'Projeler'
        proposals: 'Teklifler'
        reactions: 'Tepkiler'
        voting: 'Oylama'
        surveys: 'Anketler'
      trigger:
        7_days_after_invite_is_sent: 'Davetiye gönderildikten 7 gün sonra'
        7_days_before_the_project_changes_phase: 'Proje değişiklik aşamasından 7 gün önce'
        comment_is_deleted: 'Yorum silindi'
        comment_is_flagged_as_spam: 'Yorum spam olarak işaretlendi'
        content_gets_flagged_as_innapropiate: 'İçerik uygunsuz olarak işaretlenir'
        initiative_resubmitted_for_review: 'Teklif incelenmek üzere yeniden sunuldu'
        input_is_assigned: 'Giriş atanmıştır'
        input_is_flagged_as_spam: 'Girdi spam olarak işaretlendi'
        input_is_published: 'Girdi yayınlandı'
        input_is_updated: 'Girdi güncellenir'
        input_status_changes: 'Giriş durumu değişiklikleri'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Kullanıcıya atanan girdiye dahili yorum gönderilir'''
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Dahili yorum, kullanıcının yönettiği proje veya klasördeki girdiye gönderilir'''
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Dahili yorum, kullanıcının dahili olarak yorum yaptığı girdiye gönderilir'''
        internal_comment_is_posted_on_idea_user_moderates: 'Giriş kullanıcı moderatörlerine dahili yorum gönderilir'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Yönetilmeyen projedeki atanmamış girdiye dahili yorum gönderildi'''
        project_review_request: 'Moderatör proje incelemesi talep etti'
        project_review_state_change: 'Gözden geçiren projeyi onayladı'
        new_input_awaits_screening: 'Yeni girdiler taranmayı bekliyor'
        new_input_is_published: 'Yeni girdi yayınlandı'
        new_proposal_is_posted: 'Yeni teklif yayınlandı'
        project_phase_changes: 'Proje aşaması değişiklikleri'
        project_published: 'Proje yayınlandı'
        proposal_gets_reported_as_spam: 'Teklif spam olarak bildiriliyor'
        proposal_is_assigned_to_admin: 'Teklif yöneticiye atanır'
        proposal_is_published: 'Teklif yayınlandı'
        proposal_is_updated: 'Teklif güncellendi'
        proposal_is_upvoted_above_threshold: 'Teklif eşik değerin üzerinde oylandı'
        proposal_status_changes: 'Teklif durumu değişiklikleri'
        registration_to_event: 'Bir etkinliğe kayıt'
        survey_1_day_after_draft_saved: 'Kullanıcı anketi en son taslak olarak kaydettikten 1 gün sonra'
        user_accepts_invitation_to_cosponsor_a_proposal: 'Kullanıcı bir teklife eş sponsorluk davetini kabul eder'
        user_comments: 'Kullanıcı yorumları'
        user_comments_on_input: 'Giriş hakkında kullanıcı yorumları'
        user_comments_on_proposal: 'Teklifle ilgili kullanıcı yorumları'
        user_is_given_admin_rights: 'Kullanıcıya yönetici hakları verilir'
        user_is_given_folder_moderator_rights: 'Kullanıcıya klasör moderatörü hakları verilir'
        user_is_given_project_moderator_rights: 'Kullanıcıya proje moderatörü hakları verilir'
        user_is_invited_to_cosponsor_a_proposal: 'Kullanıcı bir teklife eş sponsorluk yapmaya davet edilir'
        user_is_mentioned: 'Kullanıcıdan bahsedilir'
        user_is_mentioned_in_internal_comment: 'Dahili yorumda kullanıcıdan bahsediliyor'
        user_registers_for_the_first_time: 'Kullanıcı ilk kez kaydolur'
        user_replies_to_comment: 'Kullanıcı yoruma yanıt veriyor'
        user_replies_to_internal_comment: 'Kullanıcı dahili yoruma yanıt veriyor'
        voting_1_day_after_last_votes: 'Kullanıcı son oylamadan 1 gün sonra'
        voting_2_days_before_phase_closes: 'Oylama aşamasının kapanmasına 2 gün kala'
        voting_basket_submitted: 'Oylar teslim edildi'
        voting_phase_ended: 'Oylama aşaması sona erdi'
