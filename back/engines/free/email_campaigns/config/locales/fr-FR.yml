fr:
  email_campaigns:
    campaign_type_description:
      "manual": Messages officiels
      "manual_project_participants": Messages officiels aux participants d'un projet
      "admin_rights_received": Droits d'administrateur reçus
      "comment_deleted_by_admin": Suppression de mon commentaire
      "comment_marked_as_spam": Commenter le rapport de spam
      "comment_on_your_comment": Une réponse à mon commentaire
      "comment_on_idea_you_follow": Un commentaire sur une idée que vous suivez
      "community_monitor_report": Rapport de l'Observatoire de communauté
      "cosponsor_of_your_idea": Un utilisateur accepte de soutenir ma proposition
      "event_registration_confirmation": Confirmation de l'inscription à l'événement
      "idea_marked_as_spam": Rapport sur le spam d'idées
      "idea_published": Publication de mon idée
      "invitation_to_cosponsor_idea": Invitation à soutenir une proposition
      "invite_received": Invitation
      "invite_reminder": Rappel d'invitation
      "internal_comment_on_idea_assigned_to_you": Commentaire interne sur une contribution qui m'est attribuée
      "internal_comment_on_idea_you_commented_internally_on": Commentaire interne sur une contribution que j'ai commentée en interne
      "internal_comment_on_idea_you_moderate": Commentaire interne sur les contributions dans le projet ou le dossier que je gère
      "internal_comment_on_unassigned_unmoderated_idea": Commentaire interne sur une contribution non attribuée dans un projet non géré
      "internal_comment_on_your_internal_comment": Commentaire interne sur mon commentaire interne
      "mention_in_official_feedback": Mention dans une mise à jour
      "mention_in_internal_comment": Mention dans un commentaire interne
      "new_comment_for_admin": Nouveau commentaire dans un projet que je modère
      "new_idea_for_admin": Nouvelle idée dans un projet que je modère
      "official_feedback_on_idea_you_follow": Mise à jour d'une idée que vous suivez
      "password_reset": Réinitialisation du mot de passe
      "project_moderation_rights_received": Droits de modération de projet reçus
      "project_folder_moderation_rights_received": Droits d'administrateur de dossiers reçus
      "project_phase_started": Nouvelle phase de projet
      "project_phase_upcoming": Nouvelle phase de projet à venir
      "project_published": Projet publié
      "project_review_request": Demande de révision d'un projet
      "project_review_state_change": Projet approuvé
      "status_change_on_idea_you_follow": Changement de statut d'une idée que vous suivez
      "survey_submitted": Enquête soumise
      "threshold_reached_for_admin": La proposition a atteint le seuil de vote
      "welcome": Après inscription
      "admin_digest": Aperçu hebdomadaire pour les administrateurs
      "moderator_digest": Aperçu hebdomadaire pour les administrateurs projet
      "assignee_digest": Aperçu hebdomadaire des idées attribuées
      "user_digest": Aperçu hebdomadaire
      "voting_basket_submitted": Confirmation de vote
      "native_survey_not_submitted": Réponse à l'enquête non soumise
      "voting_basket_not_submitted": Votes non soumis
      "voting_last_chance": Dernière chance pour voter
      "voting_phase_started": Nouvelle phase de projet avec vote
      "voting_results": Résultats du vote
      "your_input_in_screening": Ma contribution est en attente de validation
    general:
      by_author: 'par %{authorName}'
      author_wrote: '%{authorName} a écrit :'
      cta_goto_idea: 'Voir l''idée'
      cta_goto_input: 'Voir la contribution'
      cta_goto:
        idea: 'Voir l''idée'
        question: 'Voir la question'
        contribution: 'Voir la contribution'
        project: 'Voir le projet'
        issue: 'Voir le problème'
        option: 'Voir l''option'
        proposal: 'Voir la proposition'
        petition: 'Voir la pétition'
      cta_goto_your:
        idea: 'Accédez à votre idée'
        question: 'Accédez à votre question'
        contribution: 'Accédez à votre contribution'
        project: 'Accédez à votre projet'
        issue: 'Accédez à votre problème'
        option: 'Accédez à votre option'
        proposal: 'Accédez à votre proposition'
        petition: 'Accédez à votre pétition'
      cta_goto_proposal: 'Voir la proposition'
      cta_goto_project: 'Voir le projet'
    schedules:
      weekly:
        "0": "Chaque semaine, le dimanche à %{hourOfDay}"
        "1": "Chaque semaine, le lundi à %{hourOfDay}"
        "2": "Chaque semaine, le mardi à %{hourOfDay}"
        "3": "Chaque semaine, le mercredi à %{hourOfDay}"
        "4": "Chaque semaine, le jeudi à %{hourOfDay}"
        "5": "Chaque semaine, le vendredi à %{hourOfDay}"
        "6": "Chaque semaine, le samedi à %{hourOfDay}"
      quarterly: "Le premier jour de chaque trimestre"
    preview_data:
      first_name: 'Jeanne'
      last_name: 'Dupont'
      display_name: 'Jeanne Dupont'
      comment_body: 'Il s''agit d''un exemple de commentaire utilisé pour prévisualiser le contenu des courriels. Il ne s''agit pas d''un contenu réel.'
      idea_title: 'Exemple d''idée'
    footer:
      "link_privacy_policy": "Politique de confidentialité"
      "link_terms_conditions": "Conditions Générales d'Utilisation"
      "link_unsubscribe": "Se désabonner"
      "powered_by": "Généré par"
      "recipient_statement": "Cet e-mail vous a été envoyé par %{organizationName} via Go Vocal, car vous êtes un utilisateur de la plateforme %{organizationLink}."
      "unsubscribe_statement": "Vous pouvez %{unsubscribeLink} si vous ne voulez plus recevoir ces e-mails à l'avenir."
      "unsubscribe_text": "vous désabonner"
    follow:
      "unfollow_here": "Vous avez reçu cette notification parce que vous suivez ce sujet. <a href=\"%{unfollow_url}\">Vous pouvez vous désabonner ici.</a>"
    manual:
      preheader: 'Vous avez reçu un e-mail de la part de %{organizationName}'
    comment_deleted_by_admin:
      reason: 'La raison pour laquelle votre commentaire a été supprimé :'
      cta_view: 'Voir cette idée'
      event_description: '%{organizationName} a supprimé le commentaire que vous avez écrit sur une idée.'
      main_header: '%{organizationName} a supprimé votre commentaire'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Votre commentaire a été considéré comme inapproprié'
      irrelevant_reason: 'Votre commentaire a été considéré comme non pertinent dans ce contexte'
      no_reason: 'Aucune raison n’a été spécifié'
      subject: 'Votre commentaire a été supprimé de la plateforme de %{organizationName}'
      preheader: 'Votre commentaire a été supprimé'
    admin_digest:
      subject: 'Votre rapport hebdomadaire du %{time}'
      preheader: 'Rapport hebdomadaire de la plateforme de %{organizationName}'
      title_your_weekly_report: '%{firstName}, voici votre rapport hebdomadaire !'
      text_introduction: 'Voici une sélection des idées les plus actives la semaine dernière. Découvrez ce qui se passe sur votre plateforme !'
      cta_visit_the_platform: 'Visitez votre plateforme'
      new_users: 'Nouveaux utilisateurs'
      new_inputs: 'Nouvelles contributions'
      new_comments: 'Nouveaux commentaires'
      title_activity_past_week: 'Activité de la semaine passée'
      title_no_activity_past_week: 'Il n''y a eu aucune activité la semaine passée'
      reached_threshold: 'Propositions ayant atteint le nombre de votes requis'
      yesterday_by_author: 'Hier par %{author}'
      today_by_author: 'Aujourd''hui par %{author}'
      x_days_ago_by_author: 'Il y a %{x} jours par %{author}'
    admin_rights_received:
      cta_manage_platform: 'Gérez votre plateforme'
      message_you_became_administrator: "Vous avez reçu les droits d'administration pour la plateforme de participation de %{organizationName}.\n\n"
      preheader: 'Vous avez reçu les droits d''administration pour la plateforme de participation de %{organizationName}.'
      subject: "Vous êtes désormais administrateur·rice de la plateforme de %{organizationName} !\n"
      text_create_participatory_process: 'En tant qu''administrateur, vous pouvez créer et configurer des nouveaux projets participatifs. Vous pouvez ajouter des phases en utilisant la frise chronologique. Chacune des phases peut avoir son propre fonctionnement pour la publication d''idées, les commentaires et les votes.'
      text_moderate_analyse_input: 'Une fois que les projets sont lancés, les premières idées vont commencer à arriver. Vous recevrez des rapports hebdomadaires contenant les activités principales afin que vous puissiez avoir une vue d''ensemble de ce qui se passe sur la plateforme. La vue d''ensemble des idées vous aidera à modérer et gérer les contributions.'
      text_platform_setup: 'En tant qu''administrateur, vous pouvez configurer la plateforme participative. Choisissez un logo, des images, des couleurs, écrivez un message personnalisé sur la page d''accueil, invitez de nouvelles personnes à rejoindre la plateforme, choisissez les questions à poser à vos utilisateurs... '
      title_create_participatory_process: 'Concevoir le processus de participation'
      title_moderate_analyse_input: 'Modérer et analyser les contributions'
      title_platform_setup: 'Configurer votre plateforme'
      title_what_can_you_do_administrator: 'Que pouvez-vous faire en tant qu’administrateur ?'
      title_you_became_administrator: 'Vous êtes devenu administrateur'
    comment_marked_as_spam:
      by_author: 'par %{authorName}'
      commented: '%{authorName} a commenté :'
      cta_review_comment: 'Voir ce commentaire'
      days_ago: 'Il y a %{numberOfDays} jours'
      event_description: 'Le commentaire suivant posté sur <strong>''%{post}''</strong> a été signalé :'
      inappropriate_content: 'Je trouve ce contenu inapproprié ou offensant.'
      preheader: 'Révisez le commentaire et prennez action'
      reported_this_because: '%{reporterFirstName} a signalé ceci parce que :'
      subject: '%{organizationName}: %{firstName} %{lastName} a signalé un commentaire comme spam'
      title_comment_spam_report: '%{firstName} %{lastName} a signalé ce commentaire comme spam'
      today: Aujourd'hui
      wrong_content: 'Le commentaire n’est pas pertinent.'
      yesterday: Hier
    community_monitor_report:
      subject: 'Un nouveau rapport de l''Observatoire de communauté est disponible'
      title: 'Un nouveau rapport de l''Observatoire de communauté est disponible'
      text_introduction: 'Le rapport de suivi communautaire du trimestre précédent est disponible. Vous pouvez y accéder en cliquant sur le bouton ci-dessous et en vous connectant.'
      cta_report_button: 'Voir le rapport'
      report_name: 'Rapport de l''Observatoire de communauté'
    cosponsor_of_your_idea:
      cta_reply_to: 'Voir votre proposition'
      event_description: 'Félicitations ! %{cosponsorName} a accepté de soutenir votre proposition.'
      main_header: '%{cosponsorName} a accepté de soutenir votre proposition'
      subject: '%{cosponsorName} a accepté de soutenir votre proposition'
      preheader: '%{cosponsorName} a accepté de soutenir votre proposition'
    assignee_digest:
      subject: 'Idées en attente de mise à jour: %{numberIdeas}'
      preheader: 'Rapport de gestion de %{organizationName}'
      title_your_weekly_report: '%{firstName}, la contribution des citoyens est en attente de votre mise à jour'
      cta_manage_your_input: 'Gérer vos contributions'
      x_inputs_need_your_feedback: 'contributions attendent votre retour'
      title_assignment_past_week: 'Dernières idées qui vous ont été attribuées'
      title_no_assignment_past_week: 'Aucune nouvelles idées vous ont été attribuées la semaine dernière'
      yesterday_by_author: 'Hier par %{author}'
      today_by_author: 'Aujourd''hui par %{author}'
      x_days_ago_by_author: 'Il y a %{x} jours par %{author}'
      title_successful_past_week: 'Contributions ayant atteint le nombre de votes requis qui vous sont assignées'
    idea_marked_as_spam:
      cta_review: 'Vérifier'
      report_inappropriate_offensive_content: 'Je trouve ce contenu inapproprié ou offensant.'
      report_not_an_idea: 'Ce contenu n''est pas une idée et n''a pas sa place ici.'
      subject: 'Un spam a été signalé sur la plateforme de %{organizationName}'
      preheader: 'Agir sur ce rapport de spam'
      reported_this_because: '%{reporterFirstName} a signalé ceci parce que :'
      title_spam_report: '%{firstName} %{lastName} a signalé un spam :'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Soutenir cette proposition'
      event_description: '%{authorName} a créé une nouvelle proposition et aimerait que vous la souteniez.'
      event_description_cosponsoring: 'Soutenir une proposition signifie que <strong>votre nom sera affiché</strong> avec les noms des autres soutiens de la proposition.'
      event_description_before_action: 'Pour voir la proposition et accepter l''invitation, vous devez être connecté à votre compte.'
      event_description_action: 'Cliquez ci-dessous pour lire la proposition.'
      main_header: 'Vous avez été invité à soutenir une proposition'
      subject: 'Vous avez été invité à soutenir une proposition'
      preheader: 'Vous avez été invité à soutenir la proposition de %{authorName}'
    invite_reminder:
      cta_accept_invitation: 'Accepter votre invitation'
      invitation_header: 'Votre invitation est toujours en attente !'
      preheader: 'Il y a quelques jours, %{organizationName} vous a invité.e à rejoindre sa plateforme de participation.'
      invitation_expiry_message: 'Cette invitation expire dans environ %{expiryDaysRemaining} jours.'
      subject: 'Invitation en attente pour rejoindre la plateforme de participation de %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} a écrit le message suivant :'
      cta_accept_invitation: 'Accepter votre invitation'
      invitation_header: 'Vous venez de recevoir une invitation !'
      invitation_header_message: '%{organizationName} vous a invité.e à rejoindre sa plateforme de participation.'
      invitation_expiry_message: 'Cette invitation expire dans %{expiryDays} jours.'
      preheader: '%{organizationName} vous a envoyé une invitation à rejoindre leur plateforme de participation.'
      subject: 'Vous avez reçu une invitation pour rejoindre la plateforme de %{organizationName}'
    mention_in_comment:
      cta_reply_to: 'Répondre à %{commentAuthor}'
      event_description: '%{commentAuthorFull} vous a mentionné dans son commentaire sur l''idée ''%{post}''. Cliquez sur le lien ci-dessous pour entrer dans la conversation avec %{commentAuthor}'
      main_header: 'Les gens parlent de vous'
      subject: 'Quelqu''un vous a mentionné sur la plateforme de %{organizationName}'
      preheader: '%{commentAuthor} vous a mentionné dans un commentaire'
    mention_in_internal_comment:
      cta_reply_to: 'Voir le commentaire de %{firstName}'
      event_description: '%{authorNameFull} vous a mentionné dans un commentaire interne.'
      subject: '%{firstName} vous a mentionné dans un commentaire interne.'
      main_header: '%{firstName} vous a mentionné dans un commentaire interne.'
      preheader: '%{authorNameFull} vous a mentionné dans un commentaire interne.'
    moderator_digest:
      subject: 'Votre rapport hebdomadaire de gestionnaire de projet pour "%{project_title}"'
      preheader: 'Résumé du rapport administrateur projet de %{organizationName}'
      title_your_weekly_report: '%{firstName}, voici votre rapport hebdomadaire !'
      text_introduction: 'Voici une sélection des idées qui ont généré le plus d''activité la semaine dernière. Découvrez ce qui se passe sur votre plateforme !'
      cta_manage: 'Gérez votre projet'
      new_users: 'Nouveaux utilisateurs'
      new_ideas: 'Nouvelles idées'
      new_comments: 'Nouveaux commentaires'
      title_inputs_past_week: 'Nouvelles contributions de cette semaine'
      title_no_inputs_past_week: 'Pas de nouvelles contributions cette semaine'
      title_threshold_reached: Contributions ayant atteint le nombre de votes requis la semaine dernière
      yesterday_by_author: 'Hier par %{author}'
      today_by_author: 'Aujourd''hui par %{author}'
      x_days_ago_by_author: 'Il y a %{x} jours par %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} a commenté :'
      cta_reply_to: 'Vérifier le commentaire %{commentAuthor}'
      days_ago: 'Il y a %{numberOfDays} jours'
      event_description: '%{authorName} a écrit un commentaire sur votre plateforme. N''hésitez pas à joindre à la discussion et à poursuivre la conversation !'
      main_header: '%{firstName}, un nouveau commentaire a été posté sur votre plate-forme'
      subject: 'Il y a un nouveau commentaire sur la plateforme de %{organizationName}'
      preheader: '%{authorName} a laissé un commentaire'
      today: Aujourd'hui
      yesterday: Hier
    comment_on_idea_you_follow:
      cta_reply_to: 'Répondre à %{commentAuthor}'
      event_description: '%{authorNameFull} a réagi à « %{inputTitle} ». Cliquez sur le bouton ci-dessous pour continuer la conversation avec %{authorName}.'
      main_header:
        idea: '%{authorName} a commenté une idée que vous suivez'
        question: '%{authorName} a commenté une question que vous suivez'
        contribution: '%{authorName} a commenté une contribution que vous suivez'
        project: '%{authorName} a commenté un projet que vous suivez'
        issue: '%{authorName} a commenté un problème que vous suivez'
        option: '%{authorName} a commenté une option que vous suivez'
        proposal: '%{authorName} a commenté une proposition que vous suivez'
        petition: '%{authorName} a commenté une pétition que vous suivez'
      subject: 'Il y a un nouveau commentaire sur « %{input_title} »'
      preheader: '%{authorName} a publié un commentaire sur une idée pour %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Une nouvelle contribution a été publiée sur votre plateforme'
      event_description_publication: '%{authorName} a soumis une nouvelle contribution sur votre plateforme. Découvrez-la maintenant, donnez votre avis ou changez son statut !'
      cta_publication: 'Faites votre retour à %{authorName}'
      main_header_prescreening: 'Une contribution requiert votre approbation'
      event_description_prescreening: '%{authorName} a soumis une nouvelle contribution sur votre plateforme.'
      input_not_visible_prescreening: '<b>La contribution ne sera pas visible</b> tant que vous n''aurez pas modifié son statut.'
      cta_prescreening: 'Examiner la contribution'
      days_ago: 'Il y a %{numberOfDays} jours'
      preheader: '%{authorName} a publié une nouvelle idée sur votre plateforme'
      today: Aujourd'hui
      yesterday: Hier
    comment_on_your_comment:
      cta_reply_to: 'Répondre à %{firstName}'
      event_description: '%{authorNameFull} a écrit une réponse à votre commentaire sur ''%{post}'' sur la plateforme de participation. Cliquez sur le bouton ci-dessous pour continuer la conversation avec %{authorName}.'
      subject: 'Vous avez reçu un commentaire sur la plateforme de %{organizationName}'
      main_header: '%{authorName} a répondu à votre commentaire'
      preheader: '%{authorName} a répondu à votre commentaire sur la plateforme de %{organizationName}'
      replied: '%{authorFirstName} a répondu :'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Voir le commentaire de %{firstName}'
      event_description: '%{authorNameFull} a commenté votre commentaire interne.'
      subject: 'Vous avez reçu un commentaire sur votre commentaire interne sur ''%{post}'''
      main_header: 'Vous avez reçu un commentaire sur votre commentaire interne sur ''%{post}'''
      preheader: '%{authorName} a répondu à votre commentaire interne sur "%{post}".'
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Voir le commentaire de %{firstName}'
      event_description: '%{authorNameFull} a commenté en interne une entrée qui vous a été attribuée.'
      subject: '''%{post}'' a un nouveau commentaire interne'
      main_header: '''%{post}'' a un nouveau commentaire interne'
      preheader: '''%{post}'' a un nouveau commentaire interne'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Voir le commentaire de %{firstName}'
      event_description: '%{authorNameFull} a commenté en interne une entrée que vous avez commentée en interne.'
      subject: '''%{post}'' a un nouveau commentaire interne'
      main_header: '''%{post}'' a un nouveau commentaire interne'
      preheader: '''%{post}'' a un nouveau commentaire interne'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Voir le commentaire de %{firstName}'
      event_description: '%{authorNameFull} a commenté en interne une entrée dans un projet ou un dossier que vous gérez.'
      subject: '''%{post}'' a un nouveau commentaire interne'
      main_header: '''%{post}'' a un nouveau commentaire interne'
      preheader: '''%{post}'' a un nouveau commentaire interne'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Voir le commentaire de %{firstName}'
      event_description: '%{authorNameFull} a commenté en interne une entrée non attribuée dans un projet non géré.'
      subject: '%{post} a un nouveau commentaire interne'
      main_header: '%{post}" a un nouveau commentaire interne'
      preheader: '%{post}" a un nouveau commentaire interne'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} a publié une mise à jour sur « %{input_title} ».'
      header_title: 'Il y a une mise à jour sur « %{input_title} »'
      subject: 'Un retour officiel a été publié sur « %{input_title} »'
      preheader: 'Il y a une mise à jour sur une contribution que vous suivez'
    mention_in_official_feedback:
      cta_reply_to: 'Répondre à %{organizationName}'
      event_description: '%{organizationName} vous ont mentionné dans leurs commentaires sur l''idée ''%{post}''. Cliquez sur le lien ci-dessous pour entrer dans la conversation avec %{organizationName}'
      main_header: 'Vous avez été mentionné'
      subject: '%{organizationName} vous ont mentionné dans leurs commentaires'
      preheader: '%{commentAuthor} vous a mentionné dans un commentaire'
    project_moderation_rights_received:
      cta_manage_project: 'Gérez ce projet'
      message_you_became_moderator: 'Un administrateur de la plateforme de %{organizationName} vous a nommé administrateur projet du projet suivant :'
      no_ideas: 'Il n''y a pas encore d''idées'
      preheader: 'Un administrateur de la plateforme de %{organizationName} vous a nommé administrateur projet du projet suivant'
      subject: 'Vous êtes à présent un administrateur projet sur la plateforme de %{organizationName}'
      text_design_participatory_process: 'En tant que modérateur de projet, vous pouvez configurer la façon dont les utilisateurs interagissent avec votre projet. Vous pouvez ajouter de nouvelles phases à la ligne du temps et les personnaliser selon votre projet (publication d''idées, commentaires, votes).'
      text_moderate_analyse_input: 'Une fois le projet lancé, les premières idées vont arriver. Vous recevrez des rapports hebdomadaires avec un résumé des activités importantes, afin de ne rien manquer. Dans votre panneau d''administrateur projet, découvrez quelles idées ont obtenu le plus de votes positifs ou négatifs.'
      text_share_project_information: 'Afin d''obtenir des contributions de meilleure qualité, il est important de partager assez d''informations : ajouter une description du projet, y joindre des images (par exemple des schemas ou des plans), et communiquer tous les événements en lien avec le projet. Souvenez-vous : c''est la bonne information qui provoque la bonne participation !'
      title_design_participatory_process: 'Concevoir le processus de participation'
      title_moderate_analyse_input: 'Modérer et analyser les contributions'
      title_share_project_information: 'Fournir des informations au sujet du projet'
      title_what_can_you_do_moderator: 'Que pouvez-vous faire en tant qu''administrateur projet ?'
      title_you_became_moderator: 'Vous êtes à présent administrateur projet'
      x_ideas: '%{numberOfIdeas} idées'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Voir ce dossier'
      message_added_as_folderadmin: 'Vous avez reçu des droits d''administrateur sur la plateforme de participation de %{organizationName} pour le dossier suivant :'
      no_projects: 'Pas encore de projets'
      preheader: 'Un administrateur de la plateforme de %{organizationName} vous a nommé administrateur du dossier suivant'
      subject: 'Vous êtes à présent un administrateur de dossier sur la plateforme de %{organizationName}'
      text_manage_folder: 'Un dossier est un moyen de grouper plusieurs projets de participation. En tant que gestionnaire de dossier, vous pouvez modifier le dossier, sa description et créer de nouveaux projets (pour supprimer des projets, veuillez contacter l''administrateur de votre plateforme). Vous aurez également les droits de gestion sur tous les projets du dossier, ce qui vous permettra de les modifier, de gérer les contributions et d''envoyer des e-mails aux participants.'
      text_moderate_analyse_input: 'Une fois les projets lancés, les contributions ne tarderont pas à arriver. Vous recevrez donc des rapports hebdomadaires pour vous maintenir informé·e de l''avancement de vos projets. De plus, la "vue d''ensemble" du tableau de bord vous permettra de suivre et de gérer les contributions (attribution de statuts, mises à jour officielles, modération...).'
      text_design_participatory_process: 'Vous pouvez gérer les différents projets de participation dans votre dossier, c''est-à-dire configurer la méthode de participation, ajouter une description de projet et des images, ou enore promouvoir les événements associés. Vous pouvez également gérer comment les participants interagissent avec vos projets, notamment en définissant les droits d''accès et en configurant les paramètres de publication, de vote et de commentaire.'
      title_design_participatory_process: 'Concevoir le processus de participation'
      title_moderate_analyse_input: 'Modérer et analyser les contributions'
      title_manage_folder: 'Gérez les paramètres du dossier et créez de nouveaux projets.'
      title_what_can_you_do_folderadmin: 'Que pouvez-vous faire en tant qu''administrateur de dossiers ?'
      title_added_as_folderadmin: 'Vous êtes à présent gestionnaire de dossiers'
      x_projects: '%{numberOfProjects} projets'
    project_phase_started:
      cta_view_phase: 'Découvrez cette nouvelle phase'
      event_description: 'Ce projet est entré dans une nouvelle phase sur la plate-forme de %{organizationName}. Cliquez sur le lien ci-dessous pour en savoir plus !'
      main_header: 'Une nouvelle phase a commencé pour le projet ''%{projectName}'''
      subtitle: 'À propos de ''%{projectName}'''
      new_phase: 'Ce projet est entré dans la phase ''%{phaseTitle}'''
      subject: '%{projectName} est entré.e dans une nouvelle phase'
      preheader: 'Une nouvelle phase a commencé pour %{projectName}'
    project_phase_upcoming:
      cta_view_phase: 'Configurer cette nouvelle phase'
      event_description: 'Le projet ''%{projectName}'' va bientôt entrer dans une nouvelle phase. Assurez-vous que tout est en place pour cette phase: Y a-til une description adéquate? Les idées retenues sont-elles transférées à cette phase? Souhaitez-vous informer vos citoyens des particularités de cette nouvelle phase avec une campagne d''email adaptée?'
      main_header: '%{firstName}, un projet va bientôt entrer dans une nouvelle phase'
      subtitle: 'À propos de ''%{projectName}'''
      new_phase: 'Le projet va rentrer dans la phase ''%{phaseTitle}'''
      subject: 'Configurez les détails nécessaires à la nouvelle phase de %{projectName}'
      preheader: 'Une nouvelle phase va bientôt commencer pour %{projectName}'
    project_published:
      subject: 'Un nouveau projet a été publié sur la plateforme de %{organizationName}'
      header_title: 'Un nouveau projet a été publié'
      header_message: 'La plateforme de participation de %{organizationName} vient de publier le projet suivant :'
      preheader: 'Un nouveau projet a été publié'
    project_review_request:
      subject: 'Demande de révision : Un projet est en attente d''approbation.'
      header: '%{requesterName} vous a invité à vérifier le projet "%{projectTitle}"'
      header_message: "Actuellement, le projet est en mode brouillon et n'est pas visible par les utilisateurs. Une fois que vous l'aurez vérifié et approuvé, le modérateur pourra le publier."
      cta_review_project: "Vérifier le projet"
    project_review_state_change:
      subject: '"%{projectTitle}" a été approuvé'
      header: '%{reviewerName} a approuvé le projet "%{projectTitle}"'
      header_message: "Le projet est maintenant prêt à être mis en ligne. Vous pouvez le publier dès que vous êtes prêt !"
      cta_go_to_project: "Accéder au projet"
    status_change_on_idea_you_follow:
      status_change: 'Le nouveau statut de cette contribution est « %{status} »'
      header_message: '%{organizationName} a mis à jour le statut de la contribution « %{input_title} » sur leur plateforme de participation.'
      header_title: 'Une contribution que vous suivez a un nouveau statut'
      subject: 'Le statut de « %{input_title} » a changé'
      preheader: 'Une contribution que vous suivez a un nouveau statut'
    user_digest:
      subject: "Mise à jour hebdomadaire de la plate-forme de participation de %{organizationName}"
      commented: "%{authorFirstName} a commenté :"
      preheader: "Aperçu hebdomadaire de %{organizationName}"
      title_your_weekly_report: "Découvrez ce qui s'est passé la semaine dernière"
      intro_text: "Voici un résumé de ce qu'il s'est passé sur la plateforme de participation de %{organizationName}."
      cta_go_to_the_platform: "Voir la plateforme"
      title_no_activity_past_week: "Pas d'activité la semaine passée"
      successful_proposals_title: "Propositions ayant atteint le nombre de votes requis"
      successful_proposals_text: "Ces propositions ont reçu un soutien suffisant pour passer à l'étape suivante ! Cliquez sur la proposition pour en savoir plus sur les prochaines étapes."
      today_by_author: "Aujourd'hui par %{author}"
      yesterday_by_author: "Hier par %{author}"
      x_days_ago_by_author: "Il y a %{x} jours par %{author}"
      trending_title: "Populaires"
      trending_text: "Intéressé par ce qui se passe sur la plateforme ? Voici les trois contributions les plus populaires et ce que les gens en disent."
      no_notifications: "Aucune notification"
      one_notification: "1 notification"
      multiple_notifications: "%{notifCount} notifications"
      no_unread_notifications: "Il est temps de recréer de l'activité ! Visitez la plateforme pour découvrir ce qu'il s'y passe."
      unread_notifications: "Vous avez de nouvelles notifications ! Visitez la plate-forme pour découvrir ce qu'il s'y passe."
    threshold_reached_for_admin:
      cta_process_initiative: 'Passez cette proposition à l''étape suivante'
      main_header: 'Une proposition a atteint le nombre de voix requis!'
      subject: '"%{input_title}" a atteint le nombre de voix requis sur votre plateforme'
      preheader: 'Assurez-vous de passer aux étapes suivantes'
    welcome:
      cta_join_platform: 'Découvrir la plateforme'
      subject: 'Bienvenue sur la plateforme de %{organizationName}'
      main_header: Bienvenue !
      message_welcome: 'Félicitations, vous vous êtes inscrit.e avec succès sur la plateforme de participation de %{organizationName}. Vous pouvez maintenant découvrir la plateforme et faire entendre votre voix. Vous pouvez également ajouter une photo de profil et une courte description pour dire aux autres qui vous êtes.'
      preheader: 'Voici ce que vous pouvez faire sur la plateforme de %{organizationName}'
    idea_published:
      subject:
        idea: 'Votre idée a été publiée'
        question: 'Votre question a été publiée'
        contribution: 'Votre contribution a été publiée'
        project: 'Votre projet a été publié'
        issue: 'Votre problème a été publié'
        option: 'Votre option a été publiée'
        proposal: 'Votre proposition a été publiée'
        petition: 'Votre pétition a été publiée'
      main_header: 'Vous avez posté « %{input_title} »'
      header_message: 'Assurons-nous qu''il soit lu.'
      message_get_votes: 'Partagez-la autour de vous :'
      action_published_idea: 'Publiez votre idée'
      action_add_image: '%{addImageLink} pour donner plus de visibilité à votre idée'
      add_image: 'Ajoutez une image'
      action_share_fb: 'Informez vos amis sur %{fbLink}'
      action_share_twitter: 'Informez vos followers sur %{twitterLink}'
      action_send_email: 'Envoyez un %{sendEmailLink} à vos contacts'
      send_email: courriel
      action_share_link: 'Partagez-la via n''importe quel canal en copiant le %{link}'
      link: lien
      preheader: '%{firstName}, félicitations pour avoir posté votre idée sur la plateforme de %{organizationName}. A présent, obtenez du soutien pour votre idée.'
    your_input_in_screening:
      main_header:
        idea: 'Le statut de votre idée est « %{prescreening_status_title} »'
        question: 'Le statut de votre question est « %{prescreening_status_title} »'
        contribution: 'Le statut de votre contribution est « %{prescreening_status_title} »'
        project: 'Le statut de votre projet est « %{prescreening_status_title} »'
        issue: 'Le statut de votre problème est « %{prescreening_status_title} »'
        option: 'Le statut de votre option est « %{prescreening_status_title} »'
        proposal: 'Le statut de votre proposition est « %{prescreening_status_title} »'
        petition: 'Le statut de votre pétition est « %{prescreening_status_title} »'
      message: 'Votre contribution « %{input_title} » deviendra visible pour les autres une fois qu''elle aura été relue et approuvée.'
      subject: '« %{input_title} » est presque publié'
      preheader: 'Le statut de votre contribution est actuellement « %{prescreening_status_title} »'
    voting_basket_submitted:
      subject: '%{organizationName}: Vous avez voté avec succès'
      preheader: 'Votre vote a bien été enregistré sur la plateforme de participation de %{organizationName}'
      title_basket_submitted: 'Vous avez voté avec succès'
      event_description: 'Merci de votre participation. Vos votes ont bien été enregistrés. Vous pouvez consulter et gérer vos votes sur la plateforme %{organizationName}.'
      cta_see_votes_submitted: 'Voir les votes soumis'
      cta_message: 'Cliquez sur le bouton ci-dessous pour participer'
    native_survey_not_submitted:
      subject: '%{organizationName}: Nous y sommes presque ! Soumettez vos réponses'
      preheader: 'Vous n''avez pas terminé votre réponse à l''enquête sur la plateforme de participation de %{organizationName}'
      title_native_survey_not_submitted: 'Vous y êtes presque ! Envoyez vos réponses'
      body_native_survey_not_submitted: 'Vous avez commencé à répondre à l''enquête sur %{phaseTitle}, mais vous n''avez pas soumis vos réponses. Les soumissions seront clôturées le %{phaseEndDate}. Cliquez sur le bouton ci-dessous pour reprendre là où vous vous êtes arrêté.'
      body_native_survey_not_submitted_no_date: 'Vous avez commencé à répondre à l''enquête sur %{phaseTitle}, mais vous n''avez pas soumis vos réponses. Cliquez sur le bouton ci-dessous pour reprendre là où vous vous êtes arrêté.'
      cta_complete_your_survey_response: 'Continuer l''enquête'
    voting_basket_not_submitted:
      subject: '%{organizationName}Vous n''avez pas soumis vos votes'
      preheader: 'Vous n''avez pas soumis vos votes sur la plateforme de participation de %{organizationName}'
      title_basket_not_submitted: 'Vous n''avez pas soumis vos votes'
      event_description: 'Vous avez choisi quelques options pour %{contextTitle}, mais vous n''avez pas soumis votre sélection.'
      cta_view_options_and_vote: 'Voir les options et voter'
      cta_message: 'Cliquez sur le bouton ci-dessous pour soumettre les options sélectionnées'
    voting_last_chance:
      subject: '%{organizationName}: Dernière chance de participer au vote pour %{phaseTitle}'
      preheader: 'Dernière chance prendre part au vote pour %{phaseTitle} sur la plateforme de participation de %{organizationName}'
      title_last_chance: 'Dernière chance de voter pour %{phaseTitle}'
      body_1: 'La phase de vote pour le projet %{projectTitle} prendra fin demain, à minuit.'
      body_2: 'Le temps file, et nous avons remarqué que vous n''avez pas encore voté ! Agissez dès maintenant en cliquant sur le bouton ci-dessous pour participer.'
      body_3: 'Saisissez cette opportunité pour faire entendre votre voix et contribuer activement à façonner l''avenir du projet.'
      cta_vote: 'Voter'
    voting_results:
      subject: '%{organizationName}: Les résultats du vote pour %{phaseTitle} sont disponibles !'
      preheader: 'Les résultats des votes pour %{phaseTitle} sur la plateforme de participation de %{organizationName} ont été publiés'
      title_results: 'Les résultats du vote pour %{phaseTitle} sont disponibles !'
      body_1: 'Les résultats sont arrivés !'
      body_2: 'Les résultats du vote pour %{phaseTitle} sur la plateforme %{organizationName} ont été publiés !'
      body_3: 'Nous vous invitons à prendre connaissance des résultats et à rester attentifs pour de futures mises à jour concernant les prochaines étapes.'
      cta_see_results: 'Voir les résultats sur la plateforme'
    event_registration_confirmation:
      subject: "Votre inscription à \"%{eventTitle}\" est confirmée !"
      preheader: "%{firstName}, merci de vous être inscrit à l'événement « %{eventTitle} »"
      header_message: "%{firstName}, merci pour votre inscription à"
      event_details:
        labels:
          date: 'Date'
          location: 'Lieu'
          online_link: 'Lien en ligne'
          description: 'Description'
          project: 'Projet'
      cta_go_to_event: 'Voir l''événement'
      cta_add_to_calendar: 'Ajouter à votre calendrier'
    voting_phase_started:
      subject: '%{organizationName}: La phase de vote a commencé pour %{projectName}'
      preheader: 'La phase de vote a commencé pour %{projectName} sur la plateforme de participation de %{organizationName}'
      event_description: 'Le projet "%{projectName}" vous invite à exprimer votre préférence parmi %{numIdeas} options :'
      cta_message: 'Cliquez sur le bouton ci-dessous pour participer'
      cta_vote: 'Se rendre sur la plateforme pour voter'
    survey_submitted:
      subject: '%{organizationName}: Merci pour votre réponse ! 🎉'
      preheader: 'Voici les détails de votre soumission.'
      main_header: 'Merci d''avoir partagé votre avis sur « %{projectName} » !'
      your_input_submitted: 'Votre contribution pour « %{projectName} » a été soumise avec succès.'
      if_you_would_like_to_review: 'Si vous souhaitez revoir votre soumission, vous pouvez télécharger vos réponses ci-dessous.'
      your_submission_has_id: 'Votre soumission a l''identifiant unique suivant :'
      you_can_use_this_id: 'Vous pouvez utiliser cet identifiant pour contacter les administrateurs de la plateforme si vous souhaitez que votre soumission soit supprimée.'
      download_responses: 'Téléchargez vos réponses'
    admin_labels:
      recipient_role:
        admins: 'Aux administrateurs'
        admins_and_managers: 'Aux administrateurs et aux gestionnaires'
        managers: 'Aux gestionnaires'
        project_participants: 'Aux participants du projet'
        registered_users: 'Aux utilisateurs enregistrés'
      recipient_segment:
        admins: 'Administrateurs'
        admins_and_managers: 'Administrateurs et gestionnaires'
        admins_and_managers_assigned_to_the_input: 'Les administrateurs et les gestionnaires assignés à la contribution'
        admins_and_managers_managing_the_project: 'Administrateurs et gestionnaires du projet'
        admins_assigned_to_a_proposal: 'Administrateurs assignés à une proposition'
        all_users: 'Tous les utilisateurs'
        all_users_who_uploaded_proposals: 'Tous les utilisateurs qui ont soumis des propositions'
        managers: 'Gestionnaires'
        managers_managing_the_project: 'Responsables de la gestion du projet'
        new_attendee: 'L''utilisateur qui s''est inscrit'
        project_reviewers: 'Les réviseurs de projet et les gestionnaires de dossiers'
        project_review_requester: 'L''utilisateur qui a demandé l''approbation pour publier le projet'
        user_who_commented: 'Utilisateur qui a commenté'
        user_who_is_invited_to_cosponsor_a_proposal: 'L''utilisateur invité à soutenir la proposition'
        user_who_is_mentioned: 'Utilisateur mentionné'
        user_who_is_receiving_admin_rights: 'L''utilisateur qui reçoit les droits d''administrateur'
        user_who_is_receiving_folder_moderator_rights: 'L''utilisateur qui reçoit les droits de gestionnaire de dossier'
        user_who_is_receiving_project_moderator_rights: 'L''utilisateur qui reçoit les droits de gestionnaire de projet'
        user_who_published_the_input: 'L''utilisateur qui a posté la contribution'
        user_who_published_the_proposal: 'L''utilisateur qui a posté la proposition'
        user_who_registers: 'Utilisateur qui s''inscrit'
        user_who_submitted_the_input: 'Utilisateur qui a soumis la contribution'
        user_who_voted: 'Utilisateur ayant voté'
        user_who_was_invited: 'Utilisateur invité'
        user_with_unsubmitted_survey: 'Utilisateur qui a commencé mais n''a pas soumis sa réponse'
        user_with_unsubmitted_votes: 'Utilisateur qui n''a pas soumis ses votes'
        users_who_engaged_but_not_voted: 'Utilisateurs ayant participé au projet, mais n''ayant pas voté'
        users_who_engaged_with_the_project: 'Les utilisateurs qui ont participé au projet'
        users_who_follow_the_input: 'Les utilisateurs qui suivent la contribution'
        users_who_follow_the_project: 'Les utilisateurs qui suivent le projet'
        users_who_follow_the_proposal: 'Les utilisateurs qui suivent la proposition'
      content_type:
        comments: 'Commentaires'
        content_moderation: 'Modération du contenu'
        events: 'Événements'
        general: 'Général'
        inputs: 'Contributions'
        internal_comments: 'Commentaires internes'
        permissions: 'Permissions'
        projects: 'Projets'
        proposals: 'Propositions'
        reactions: 'Réactions'
        voting: 'Vote'
        surveys: 'Enquêtes'
      trigger:
        7_days_after_invite_is_sent: '7 jours après l''envoi de l''invitation'
        7_days_before_the_project_changes_phase: '7 jours avant le changement de phase du projet'
        comment_is_deleted: 'Suppression d''un commentaire'
        comment_is_flagged_as_spam: 'Commentaire marqué comme spam'
        content_gets_flagged_as_innapropiate: 'Contenu signalé comme inapproprié'
        initiative_resubmitted_for_review: 'Proposition soumise à nouveau pour examen'
        input_is_assigned: 'Une contribution est assignée'
        input_is_flagged_as_spam: 'Une contribution est signalée comme spam'
        input_is_published: 'Une contribution est publiée'
        input_is_updated: 'Une contribution est mise à jour'
        input_status_changes: 'Changements du statut d''une contribution'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Un commentaire interne est publié sur la contribution attribuée à l''utilisateur'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Un commentaire interne est publié sur les données saisies dans le projet ou le dossier géré par l''utilisateur'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Un commentaire interne est publié sur l''entrée "l''utilisateur a commenté en interne'
        internal_comment_is_posted_on_idea_user_moderates: 'Un commentaire interne est posté sur l''entrée modérée par l''utilisateur'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Un commentaire interne est publié sur une entrée non attribuée dans un projet non géré'
        project_review_request: 'Demande d''approbation pour publier un projet'
        project_review_state_change: 'Le projet a été approuvé'
        new_input_awaits_screening: 'Nouvelle contribution en attente de validation'
        new_input_is_published: 'Une nouvelle contribution a été postée'
        new_proposal_is_posted: 'Publication d''une nouvelle proposition'
        project_phase_changes: 'Changements de phase du projet'
        project_published: 'Publication d''un projet'
        proposal_gets_reported_as_spam: 'Proposition marquée comme spam'
        proposal_is_assigned_to_admin: 'Une proposition est assignée à un administrateur'
        proposal_is_published: 'Une proposition est publiée'
        proposal_is_updated: 'Mise à jour d''une proposition'
        proposal_is_upvoted_above_threshold: 'Une proposition dépasse le seuil de votes requis '
        proposal_status_changes: 'Changement du statut d''une proposition'
        registration_to_event: 'Inscription à un événement'
        survey_1_day_after_draft_saved: '1 jour après la dernière sauvegarde du brouillon'
        user_accepts_invitation_to_cosponsor_a_proposal: 'Un utilisateur accepte de soutenir une proposition'
        user_comments: 'Nouveaux commentaires d''utilisateurs'
        user_comments_on_input: 'L''utilisateur commente une contribution'
        user_comments_on_proposal: 'Commentaires sur une proposition'
        user_is_given_admin_rights: 'L''utilisateur reçoit des droits d''administrateur'
        user_is_given_folder_moderator_rights: 'L''utilisateur se voit attribuer des droits de gestionnaire de dossier'
        user_is_given_project_moderator_rights: 'L''utilisateur se voit attribuer des droits de gestionnaire de projet'
        user_is_invited_to_cosponsor_a_proposal: 'Un utilisateur est invité à soutenir une proposition'
        user_is_mentioned: 'L''utilisateur est mentionné'
        user_is_mentioned_in_internal_comment: 'L''utilisateur est mentionné dans un commentaire interne'
        user_registers_for_the_first_time: 'L''utilisateur s''inscrit pour la première fois'
        user_replies_to_comment: 'Réponse à un commentaire'
        user_replies_to_internal_comment: 'L''utilisateur répond à un commentaire interne'
        voting_1_day_after_last_votes: '1 jour après le dernier vote de l''utilisateur'
        voting_2_days_before_phase_closes: '2 jours avant la clôture de la phase de vote'
        voting_basket_submitted: 'Les votes sont soumis'
        voting_phase_ended: 'La phase de vote est terminée'
