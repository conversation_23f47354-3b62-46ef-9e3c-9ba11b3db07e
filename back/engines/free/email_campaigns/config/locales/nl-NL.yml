nl:
  email_campaigns:
    campaign_type_description:
      "manual": <PERSON><PERSON><PERSON><PERSON> be<PERSON>
      "manual_project_participants": Officiële berichten aan projectdeelnemers
      "admin_rights_received": Platformbeheerdersrechten ontvangen
      "comment_deleted_by_admin": Verwijder<PERSON> van mijn reactie
      "comment_marked_as_spam": <PERSON><PERSON>ie spammelding
      "comment_on_your_comment": Een antwoord op mijn reactie
      "comment_on_idea_you_follow": Een reactie op een idee dat je volgt
      "community_monitor_report": <PERSON><PERSON><PERSON> van de tevredenheidsmonitor
      "cosponsor_of_your_idea": <PERSON><PERSON> gebruiker accepteert mijn uitnodiging om mijn voorstel te steunen
      "event_registration_confirmation": Bevestiging registratie activiteit
      "idea_marked_as_spam": Idee spammelding
      "idea_published": <PERSON><PERSON> van mijn idee
      "invitation_to_cosponsor_idea": Uitnodiging om een voorstel te steunen
      "invite_received": Uitnodiging
      "invite_reminder": Herinnering uitnodiging
      "internal_comment_on_idea_assigned_to_you": Interne reactie op aan mij toegewezen bijdrage
      "internal_comment_on_idea_you_commented_internally_on": Interne reactie op een bijdrage waarop ik intern heb gereageerd
      "internal_comment_on_idea_you_moderate": Interne reactie op een bijdrage in een project of map die ik beheer
      "internal_comment_on_unassigned_unmoderated_idea": Interne reactie op een niet-toegewezen bijdrage in een onbeheerd project
      "internal_comment_on_your_internal_comment": Interne reactie op mijn interne reactie
      "mention_in_official_feedback": Vermelding in een update
      "mention_in_internal_comment": Vermelding in een interne reactie
      "new_comment_for_admin": Nieuwe reactie in een project dat ik modereer
      "new_idea_for_admin": Nieuw idee in een project dat ik modereer
      "official_feedback_on_idea_you_follow": Update over een idee dat je volgt
      "password_reset": Herstel wachtwoord
      "project_moderation_rights_received": Projectbeheerdersrechten ontvangen
      "project_folder_moderation_rights_received": Mapbeheerdersrechten ontvangen
      "project_phase_started": Nieuwe projectfase
      "project_phase_upcoming": Opkomende nieuwe projectfase
      "project_published": Project gepubliceerd
      "project_review_request": Projectbeoordelingsverzoek
      "project_review_state_change": Project goedgekeurd
      "status_change_on_idea_you_follow": Statuswijziging van een idee dat je volgt
      "survey_submitted": Vragenlijst ingediend
      "threshold_reached_for_admin": Een voorstel bereikte het benodigd aantal stemmen
      "welcome": Na registratie
      "admin_digest": Weekoverzicht voor platformbeheerders
      "moderator_digest": Weekoverzicht voor projectbeheerders
      "assignee_digest": Weekoverzicht van toegewezen ideeën
      "user_digest": Weekoverzicht
      "voting_basket_submitted": Bevestiging van stemming
      "native_survey_not_submitted": Vragenlijst niet ingediend
      "voting_basket_not_submitted": Niet-ingediende stemmen
      "voting_last_chance": Laatste kans om te stemmen
      "voting_phase_started": Nieuwe projectfase met stemmen
      "voting_results": Stemresultaten
      "your_input_in_screening": Mijn input wacht op screening
    general:
      by_author: 'door %{authorName}'
      author_wrote: '%{authorName} schreef:'
      cta_goto_idea: 'Ga naar dit idee'
      cta_goto_input: 'Ga naar deze bijdrage'
      cta_goto:
        idea: 'Ga naar dit idee'
        question: 'Ga naar deze vraag'
        contribution: 'Ga naar deze bijdrage'
        project: 'Ga naar dit project'
        issue: 'Ga naar deze reactie'
        option: 'Ga naar deze optie'
        proposal: 'Ga naar dit voorstel'
        petition: 'Ga naar deze petitie'
      cta_goto_your:
        idea: 'Ga naar je idee'
        question: 'Ga naar je vraag'
        contribution: 'Ga naar je bijdrage'
        project: 'Ga naar je project'
        issue: 'Ga naar je reactie'
        option: 'Ga naar je optie'
        proposal: 'Ga naar je voorstel'
        petition: 'Ga naar je petitie'
      cta_goto_proposal: 'Ga naar dit voorstel'
      cta_goto_project: 'Ga naar dit project'
    schedules:
      weekly:
        "0": "Wekelijks, op zondag om %{hourOfDay}"
        "1": "Wekelijks, op maandag om %{hourOfDay}"
        "2": "Wekelijks, op dinsdag om %{hourOfDay}"
        "3": "Wekelijks, op woensdag om %{hourOfDay}"
        "4": "Wekelijks, op donderdag om %{hourOfDay}"
        "5": "Wekelijks, op vrijdag om %{hourOfDay}"
        "6": "Wekelijks, op zaterdag om %{hourOfDay}"
      quarterly: "Per kwartaal, op de eerste dag van het kwartaal"
    preview_data:
      first_name: 'Emma'
      last_name: 'de Vries'
      display_name: 'Emma de Vries'
      comment_body: 'Dit is een voorbeeldreactie die wordt gebruikt om een voorvertoning van inhoud van e-mails te laten zien. Dit is niet de echte inhoud.'
      idea_title: 'Voorbeeldidee'
    footer:
      "link_privacy_policy": "Privacybeleid"
      "link_terms_conditions": "Gebruiksvoorwaarden"
      "link_unsubscribe": "Uitschrijven"
      "powered_by": "Mogelijk gemaakt door"
      "recipient_statement": "Deze e-mail is door Go Vocal naar je verzonden namens %{organizationName}, omdat je een geregistreerde gebruiker bent van %{organizationLink}."
      "unsubscribe_statement": "Je kunt je %{unsubscribeLink} als je deze e-mails in de toekomst niet meer wilt ontvangen."
      "unsubscribe_text": "uitschrijven"
    follow:
      "unfollow_here": "Je hebt deze melding ontvangen vanwege een item dat je volgt. <a href=\"%{unfollow_url}\">Je kunt het hier ontvolgen.</a>"
    manual:
      preheader: 'Je hebt mail van %{organizationName}'
    comment_deleted_by_admin:
      reason: 'De reden waarom je reactie is verwijderd:'
      cta_view: 'Bekijk dit idee'
      event_description: '%{organizationName} verwijderde de reactie dat je bij een idee schreef.'
      main_header: '%{organizationName} verwijderde je reactie'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Je reactie werd als ongepast beschouwd'
      irrelevant_reason: 'Je reactie werd als niet relevant beschouwd'
      no_reason: 'Er werd geen reden opgegeven'
      subject: 'Je reactie werd verwijderd op het platform van %{organizationName}'
      preheader: 'Je reactie werd verwijderd'
    admin_digest:
      subject: 'Je wekelijks platformbeheerdersrapport van %{time}'
      preheader: 'Weekoverzicht van %{organizationName}'
      title_your_weekly_report: '%{firstName}, je wekelijks overzicht'
      text_introduction: 'Ontdek rond welke ideeën er het meest werd gediscussieerd de afgelopen week. Hieronder vind je het overzicht!'
      cta_visit_the_platform: 'Ga naar het platform'
      new_users: 'Nieuwe gebruikers'
      new_inputs: 'Nieuwe bijdrage'
      new_comments: 'Nieuwe reacties'
      title_activity_past_week: 'Activiteit van de afgelopen week'
      title_no_activity_past_week: 'Er was geen activiteit in de afgelopen week'
      reached_threshold: 'Drempel bereikt'
      yesterday_by_author: 'Gisteren door %{author}'
      today_by_author: 'Vandaag door %{author}'
      x_days_ago_by_author: '%{x} dagen geleden door %{author}'
    admin_rights_received:
      cta_manage_platform: 'Beheer je platform'
      message_you_became_administrator: 'Je bent platformbeheerder geworden van het participatieplatform van %{organizationName}.'
      preheader: 'Je bent platformbeheerder geworden van het participatieplatform van %{organizationName}'
      subject: 'Je bent platformbeheerder geworden van het participatieplatform van %{organizationName}'
      text_create_participatory_process: 'Als platformbeheerder kun je nieuwe participatieprojecten aanmaken en vormgeven. Je kunt nieuwe projectfases toevoegen met behulp van een tijdslijn. Elke projectfase heeft een participatiemethode, bijvoorbeeld een poll, ideeëngeneratie, een enquete of een stemronde.'
      text_moderate_analyse_input: 'Zodra een project gelanceerd is, zullen de eerste bijdrages binnenlopen. Om goed op de hoogte te blijven, krijg je een wekelijks overzicht van alle belangrijke activiteiten op het platform. In de post-sectie van het dashboard kun je alle input beheren, erover terugkoppelen naar je inwoners en analyseren.'
      text_platform_setup: 'Als platformbeheerder kun je het participatieplatform vormgeven. Kies een logo, voeg afbeeldingen en kleuren toe, schrijf een persoonlijk bericht op de homepagina, verstuur uitnodigingen, bepaal wat je wilt weten van de mensen op het platform ...'
      title_create_participatory_process: 'Geef het participatieproces vorm'
      title_moderate_analyse_input: 'Beheer en analyseer de input'
      title_platform_setup: 'Zet je platform klaar'
      title_what_can_you_do_administrator: 'Platformbeheerder zijn, wat houdt het in?'
      title_you_became_administrator: 'Je bent een platformbeheerder geworden'
    comment_marked_as_spam:
      by_author: 'door %{authorName}'
      commented: '%{authorName} schreef:'
      cta_review_comment: 'Beoordeel reactie'
      days_ago: '%{numberOfDays} dagen geleden'
      event_description: 'De volgende reactie gepost bij <strong>''%{post}''</strong> werd gerapporteerd:'
      inappropriate_content: 'De reactie is ongepast of aanstootgevend.'
      preheader: 'Onderneem actie tegen dit idee dat als spam gerapporteerd werd'
      reported_this_because: '%{reporterFirstName} rapporteerde dit omdat:'
      subject: '%{organizationName}: %{firstName} %{lastName} rapporteerde deze reactie als spam'
      title_comment_spam_report: '%{firstName} %{lastName} rapporteerde deze reactie als spam'
      today: Vandaag
      wrong_content: 'De reactie is niet relevant.'
      yesterday: Gisteren
    community_monitor_report:
      subject: 'Er is een nieuw rapport van de tevredenheidsmonitor beschikbaar'
      title: 'Er is een nieuw rapport van de tevredenheidsmonitor beschikbaar'
      text_introduction: 'Er is een rapport van de tevredenheidsmonitor gemaakt voor het vorige kwartaal. Je kunt het bekijken door op de knop hieronder te klikken en in te loggen.'
      cta_report_button: 'Bekijk rapport'
      report_name: 'Rapport van de tevredenheidsmonitor'
    cosponsor_of_your_idea:
      cta_reply_to: 'Bekijk je voorstel'
      event_description: 'Gefeliciteerd! %{cosponsorName} heeft je uitnodiging om je voorstel te steunen geaccepteerd.'
      main_header: '%{cosponsorName} heeft je uitnodiging om je voorstel te steunen geaccepteerd'
      subject: '%{cosponsorName} heeft je uitnodiging om je voorstel te steunen geaccepteerd'
      preheader: '%{cosponsorName} heeft je uitnodiging om je voorstel te steunen geaccepteerd'
    assignee_digest:
      subject: 'Aantal bijdrages die je feedback nodig hebben: %{numberIdeas}'
      preheader: 'Overzicht van %{organizationName}'
      title_your_weekly_report: '%{firstName}, de input van de burgers wacht op je feedback'
      cta_manage_your_input: 'Beheer je input'
      x_inputs_need_your_feedback: 'bijdragen hebben je feedback nodig'
      title_assignment_past_week: 'Bijdrages die recent aan jou werden toegewezen'
      title_no_assignment_past_week: 'De afgelopen week werden geen nieuwe bijdrages aan jou toegewezen'
      yesterday_by_author: 'Gisteren door %{author}'
      today_by_author: 'Vandaag door %{author}'
      x_days_ago_by_author: '%{x} dagen geleden door %{author}'
      title_successful_past_week: 'Aan jou toegewezen, en hebben de drempel bereikt'
    idea_marked_as_spam:
      cta_review: 'Beoordeling'
      report_inappropriate_offensive_content: 'Ik vind deze inhoud ongepast of aanstootgevend.'
      report_not_an_idea: 'Dit is geen idee en hoort hier niet thuis.'
      subject: 'Je hebtt een spamrapport op het platform van %{organizationName}'
      preheader: 'Neem actie'
      reported_this_because: '%{reporterFirstName} rapporteerde dit omdat:'
      title_spam_report: '%{firstName} %{lastName} heeft spam gemeld'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Steun dit voorstel'
      event_description: '%{authorName} heeft een nieuw voorstel gemaakt en wil graag dat jij het steunt.'
      event_description_cosponsoring: 'Ondersteuner zijn van een voorstel betekent dat <strong>jouw naam wordt weergegeven</strong> met de namen van andere ondersteuners van het voorstel.'
      event_description_before_action: 'Om het voorstel te zien en de uitnodiging te accepteren, moet je ingelogd zijn met je account.'
      event_description_action: 'Klik hieronder om het voorstel te lezen.'
      main_header: 'Je bent uitgenodigd om een voorstel te steunen'
      subject: 'Je bent uitgenodigd om een voorstel te steunen'
      preheader: 'Je bent uitgenodigd om het voorstel van %{authorName} te steunen'
    invite_reminder:
      cta_accept_invitation: 'Accepteer je uitnodiging'
      invitation_header: 'Je hebt je uitnodiging nog niet geaccepteerd'
      preheader: 'Werk mee aan de toekomst van %{organizationName}.'
      invitation_expiry_message: 'Deze uitnodiging verloopt over ongeveer %{expiryDaysRemaining} dagen.'
      subject: 'Reminder: registreer je op het participatieplatform van %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} schreef het volgende bericht:'
      cta_accept_invitation: 'Accepteer je uitnodiging'
      invitation_header: 'Je bent uitgenodigd!'
      invitation_header_message: '%{organizationName} nodigde je uit op hun participatieplatform.'
      invitation_expiry_message: 'Deze uitnodiging verloopt over %{expiryDays} dagen.'
      preheader: '%{organizationName} stuurde je een uitnodiging om je te registreren op hun participatieplatform.'
      subject: 'Je bent uitgenodigd om je te registeren op het participatieplatform van %{organizationName}'
    mention_in_comment:
      cta_reply_to: 'Antwoord op %{commentAuthor}'
      event_description: '%{commentAuthorFull} heeft je genoemd in zijn reactie op het idee ''%{post}''. Klik op onderstaande link om in gesprek te gaan met %{commentAuthor}'
      main_header: 'Je werd vermeld in een comment'
      subject: 'Iemand heeft je vernoemd op het platform van %{organizationName}'
      preheader: '%{commentAuthor} heeft je vermeld in een reactie'
    mention_in_internal_comment:
      cta_reply_to: 'Bekijk reactie van %{firstName}'
      event_description: '%{authorNameFull} noemde je in een interne reactie.'
      subject: '%{firstName} noemde je in een interne reactie.'
      main_header: '%{firstName} noemde je in een interne reactie.'
      preheader: '%{authorNameFull} noemde je in een interne reactie.'
    moderator_digest:
      subject: 'Je weekoverzicht als projectbeheerder van "%{project_title}"'
      preheader: 'Het weekoverzicht voor projectbeheerders van %{organizationName}'
      title_your_weekly_report: '%{firstName}, je wekelijks overzicht'
      text_introduction: 'Ontdek rond welke bijdrages er het meest werd gediscussieerd de afgelopen week. Hieronder kom je te weten wat er leeft in je project!'
      cta_manage: 'Beheer je project'
      new_users: 'Nieuwe gebruikers'
      new_ideas: 'Nieuwe bijdrages'
      new_comments: 'Nieuwe reacties'
      title_inputs_past_week: 'Nieuwe inputs in de afgelopen week'
      title_no_inputs_past_week: 'Geen nieuwe inputs in de afgelopen week'
      title_threshold_reached: Drempel bereikt in de afgelopen week
      yesterday_by_author: 'Gisteren door %{author}'
      today_by_author: 'Vandaag door %{author}'
      x_days_ago_by_author: '%{x} dagen geleden door %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} schreef:'
      cta_reply_to: 'Bekijk de reactie van %{commentAuthor}'
      days_ago: '%{numberOfDays} dagen geleden'
      event_description: '%{authorName} schreef een reactie op je platform. Aarzel niet om aan de discussie deel te nemen en het gesprek gaande te houden!'
      main_header: '%{firstName}, er werd een nieuwe reactie geplaatst op je platform'
      subject: 'Er is een nieuwe reactie op het platform van %{organizationName}'
      preheader: '%{authorName} schreef een reactie'
      today: Vandaag
      yesterday: Gisteren
    comment_on_idea_you_follow:
      cta_reply_to: 'Reageer op %{commentAuthor}'
      event_description: '%{authorNameFull} heeft een reactie geplaatst op ''%{inputTitle}''. Klik op de onderstaande knop om het gesprek voort te zetten met %{authorName}.'
      main_header:
        idea: '%{authorName} heeft gereageerd op een idee dat je volgt'
        question: '%{authorName} heeft gereageerd op een vraag die je volgt'
        contribution: '%{authorName} heeft gereageerd op een bijdrage die je volgt'
        project: '%{authorName} heeft gereageerd op een project dat je volgt'
        issue: '%{authorName} heeft gereageerd op een reactie die je volgt'
        option: '%{authorName} heeft gereageerd op een optie die je volgt'
        proposal: '%{authorName} heeft gereageerd op een voorstel dat je volgt'
        petition: '%{authorName} heeft gereageerd op een petitie die je volgt'
      subject: 'Er is een nieuwe reactie op "%{input_title}"'
      preheader: '%{authorName} heeft een reactie achtergelaten op een idee voor %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Er is een nieuwe bijdrage gepubliceerd op je platform'
      event_description_publication: '%{authorName} heeft een nieuwe bijdrage ingediend op jouw platform. Ontdek het nu, geef feedback of verander de status!'
      cta_publication: 'Geef %{authorName} feedback'
      main_header_prescreening: 'Een bijdrage vereist je beoordeling'
      event_description_prescreening: '%{authorName} heeft een nieuwe bijdrage op je platform ingediend.'
      input_not_visible_prescreening: '<b>De bijdrage is pas zichtbaar</b> als je de status aanpast.'
      cta_prescreening: 'Bekijk de bijdrage'
      days_ago: '%{numberOfDays} dagen geleden'
      preheader: '%{authorName} heeft een nieuw idee geplaatst op jouw platform'
      today: Vandaag
      yesterday: Gisteren
    comment_on_your_comment:
      cta_reply_to: 'Antwoord op %{firstName}'
      event_description: '%{authorNameFull} schreef een antwoord op je reactie op ''%{post}'' op het participatieplatform. Klik op de onderstaande knop om het gesprek voort te zetten met %{authorName}.'
      subject: 'Je hebt een reactie gekregen in een gesprek op het platform van %{organizationName}'
      main_header: '%{authorName} antwoordde op je reactie'
      preheader: '%{authorName} antwoordde op je reactie op het platform van %{organizationName}'
      replied: '%{authorFirstName} reageerde:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Bekijk reactie van %{firstName}'
      event_description: '%{authorNameFull} heeft gereageerd op je interne reactie.'
      subject: 'Je hebt een reactie ontvangen op je interne reactie op ''%{post}'''
      main_header: 'Je hebt een reactie ontvangen op je interne reactie op ''%{post}'''
      preheader: '%{authorName} heeft gereageerd op je interne reactie op ''%{post}'''
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Bekijk de reactie van %{firstName}'
      event_description: '%{authorNameFull} heeft een interne reactie gegeven op een aan jou toegewezen bijdrage.'
      subject: '''%{post}'' heeft een nieuwe interne reactie'
      main_header: '''%{post}'' heeft een nieuwe interne reactie'
      preheader: '''%{post}'' heeft een nieuwe interne reactie'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Bekijk de reactie van %{firstName}'
      event_description: '%{authorNameFull} gaf een interne reactie op een bijdrage waar je een interne reactie op gaf.'
      subject: '''%{post}'' heeft een nieuwe interne reactie'
      main_header: '''%{post}'' heeft een nieuwe interne reactie'
      preheader: '''%{post}'' heeft een nieuwe interne reactie'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Bekijk de reactie van %{firstName}'
      event_description: '%{authorNameFull} gaf een interne reactie op een bijdrage in een project of map die je beheert.'
      subject: '''%{post}'' heeft een nieuwe interne reactie'
      main_header: '''%{post}'' heeft een nieuwe interne reactie'
      preheader: '''%{post}'' heeft een nieuwe interne reactie'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Bekijk de reactie van %{firstName}'
      event_description: '%{authorNameFull} heeft een interne reactie gegeven op een niet-toegewezen bijdrage in een onbeheerd project.'
      subject: '''%{post}'' heeft een nieuwe interne reactie'
      main_header: '''%{post}'' heeft een nieuwe interne reactie'
      preheader: '''%{post}'' heeft een nieuwe interne reactie'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} Gaf een update bij %{input_title}''.'
      header_title: 'Er is een update op "%{input_title}"'
      subject: 'Er is officiële feedback geplaatst op "%{input_title}"'
      preheader: 'Er is een update over een idee dat je volgt'
    mention_in_official_feedback:
      cta_reply_to: 'Antwoord op %{organizationName}'
      event_description: '%{organizationName} heeft je genoemd in hun feedback op het idee ''%{post}''. Klik op onderstaande link om in gesprek te gaan met %{organizationName}'
      main_header: 'Je bent genoemd'
      subject: '%{organizationName} noemde je in hun feedback'
      preheader: '%{commentAuthor} heeft je genoemd in feedback'
    project_moderation_rights_received:
      cta_manage_project: 'Beheer dit project'
      message_you_became_moderator: 'Een platformbeheerder van het platform van %{organizationName} heeft jou zojuist projectbeheerder gemaakt van het volgende project:'
      no_ideas: 'Nog geen bijdrages'
      preheader: 'Een platformbeheerder van het platform van %{organizationName} heeft jou zojuist projectbeheerder gemaakt van het volgende project'
      subject: 'Je bent nu projectbeheerder op het platform van %{organizationName}'
      text_design_participatory_process: 'Als projectbeheerder kan je mee het project instellen en vormgeven en bepalen hoe andere gebruikers binnen je project werken. Je kan de tijdslijn aanpassen en de verschillende fases instellen. Kies bij elke fase de ''inspraakmethode'': bijdrages posten, reacties verzamelen en/of stemmen of gebruik een fase om enkel informatie te geven.'
      text_moderate_analyse_input: 'Wanneer het project eenmaal is gelanceerd zullen de eerste bijdrages binnenkomen. Je ontvangt wekelijks een rapport met alle kernactiviteiten (nieuwe ideeën, stemmen, reacties) zodat je altijd op de hoogte blijft. In het ideeën-overzicht binnen je project kan je zien welke ideeën de meeste voor- en tegenstemmen kregen, en je kan de projectstatus aanpassen of thema''s toevoegen.'
      text_share_project_information: 'Om de kwaliteit van de bijdrages die binnenkomen te verhogen is het belangrijk om voldoende informatie aan je doelpubliek te geven: vul de beschrijving van het project in, voeg afbeeldingen toe (bv. ook schetsen en plannen) en communiceer ook over verwante activiteiten. Onthoud: kwaliteitsvolle informatie gaat vooraf aan goede participatie!'
      title_design_participatory_process: 'Geef vorm aan het participatieproces'
      title_moderate_analyse_input: 'Beheer en analyseer de input'
      title_share_project_information: 'Geef informatie bij het project.'
      title_what_can_you_do_moderator: 'Wat kan je doen als projectbeheerder?'
      title_you_became_moderator: 'Je werd net projectbeheerder'
      x_ideas: '%{numberOfIdeas} bijdrages'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Bekijk deze map'
      message_added_as_folderadmin: 'Je hebt beheerdersrechten gekregen op %{organizationName}''s participatieplatform voor de volgende map:'
      no_projects: 'Nog geen projecten'
      preheader: 'Een platformbeheerder van het platform van %{organizationName} heeft jou zojuist mapbeheerder gemaakt van de volgende map'
      subject: 'Je bent nu mapbeheerder op het participatieplatform van %{organizationName}'
      text_manage_folder: 'Een map is een manier om verschillende participatieprojecten samen te organiseren. Als mapbeheerder kan je de map en de mapbeschrijving bewerken en nieuwe projecten aanmaken (neem contact op met een platformbeheerder om projecten te verwijderen). Je hebt ook projectbeheerderrechten over alle projecten binnen de map, waardoor je de projecten kan bewerken, de inputs kan beheren en deelnemers kan e-mailen.'
      text_moderate_analyse_input: "Zodra de projecten zijn gelanceerd beginnen de eerste inputs binnen te komen. U ontvangt wekelijkse rapporten met de belangrijkste activiteiten, zodat u op de hoogte kunt blijven van wat er gebeurt. De Post Manager in uw adminweergave zal u helpen de input te zien en beheren, inclusief het toekennen van statussen en het reageren op berichten en commentaren.\n"
      text_design_participatory_process: "U kunt de verschillende participatieprojecten in uw map beheren - configureer de participatiemethode, voeg een projectbeschrijving toe, afbeeldingen bijvoegen, en gerelateerde evenementen communiceren. U kunt ook instellen hoe deelnemers met uw projecten omgaan, waaronder het instellen van toegangsrechten en het configureren van de instellingen voor posten, stemmen en commentaar geven.\n"
      title_design_participatory_process: 'Geef het participatieproces vorm'
      title_moderate_analyse_input: 'Beheer en analyseer de input'
      title_manage_folder: 'Beheer de mapinstellingen en maak nieuwe projecten.'
      title_what_can_you_do_folderadmin: 'Wat kan je doen als mapbeheerder?'
      title_added_as_folderadmin: 'Je werd net mapbeheerder'
      x_projects: '%{numberOfProjects} projecten'
    project_phase_started:
      cta_view_phase: 'Ontdek deze nieuwe fase'
      event_description: 'Dit project is een nieuwe fase ingegaan op het platform van %{organizationName}. Klik op de link hieronder om meer te weten te komen!'
      main_header: 'Een nieuwe fase is begonnen voor het project ''%{projectName}'''
      subtitle: 'Over ''%{projectName}'''
      new_phase: 'Het project is de fase ''%{phaseTitle}'' ingegaan'
      subject: '%{projectName} ging een nieuwe fase in'
      preheader: 'Een nieuwe fase is gestart voor %{projectName}'
    project_phase_upcoming:
      cta_view_phase: 'Zet deze fase klaar'
      event_description: 'Het project ''%{projectName}'' gaat binnenkort een nieuwe fase in. Zorg ervoor dat alles klaar staat: Is er een goede beschrijving? Werden geselecteerde bijdrages al naar deze fase overgeplaatst? Wil je je burgers nog informeren over bepaalde zaken via een e-mailcampagne?'
      main_header: '%{firstName}, een project gaat binnenkort een nieuwe fase in'
      subtitle: 'Over ''%{projectName}'''
      new_phase: 'Het project gaat de fase ''%{phaseTitle}'' in'
      subject: 'Zet alles klaar voor de nieuwe fase van %{projectName}'
      preheader: 'Een nieuwe fase gaat binnenkort van start voor %{projectName}'
    project_published:
      subject: 'Er is een nieuw project gepubliceerd op het platform van %{organizationName}'
      header_title: 'Er is een nieuw project gepubliceerd'
      header_message: 'Het participatieplatform van %{organizationName} heeft zojuist het volgende project gepubliceerd:'
      preheader: 'Er is een nieuw project gepubliceerd'
    project_review_request:
      subject: 'Beoordelingsverzoek: Een project wacht op goedkeuring.'
      header: '%{requesterName} heeft je uitgenodigd om het project "%{projectTitle}" te beoordelen'
      header_message: "Momenteel staat het project in conceptmodus en is het niet zichtbaar voor gebruikers. Zodra je het hebt goedgekeurd, kan de moderator het publiceren."
      cta_review_project: "Het project beoordelen"
    project_review_state_change:
      subject: '"%{projectTitle}" is goedgekeurd'
      header: '%{reviewerName} heeft het project "%{projectTitle}" goedgekeurd'
      header_message: "Het project is nu klaar om live te gaan. Je kunt het publiceren wanneer je er klaar voor bent!"
      cta_go_to_project: "Ga naar projectinstellingen"
    status_change_on_idea_you_follow:
      status_change: 'De nieuwe status van dit idee is ''%{status}'''
      header_message: '%{organizationName} heeft de status van de input ''%{input_title}'' op hun digitale participatieplatform bijgewerkt.'
      header_title: 'Een bijdrage die je volgt, heeft een nieuwe status'
      subject: 'De status van "%{input_title}" is veranderd'
      preheader: 'Een bijdrage die je volgt heeft een nieuwe status'
    user_digest:
      subject: "Wekelijkse update van het participatieplatform van %{organizationName}"
      commented: "%{authorFirstName} schreef:"
      preheader: "Dit gebeurde er vorige week op het platform"
      title_your_weekly_report: "Ontdek wat er vorige week is gebeurd"
      intro_text: "Hier is een overzicht van wat er is gebeurd op het participatieplatform van %{organizationName}."
      cta_go_to_the_platform: "Ga naar het platform"
      title_no_activity_past_week: "Geen activiteit de afgelopen week"
      successful_proposals_title: "Bereikte het benodigd aantal stemmen"
      successful_proposals_text: "Deze voorstellen hebben genoeg steun gevonden om naar de volgende stap te gaan! Klik op het voorstel om meer te weten te komen over wat er hierna gebeurt."
      today_by_author: "Vandaag door %{author}"
      yesterday_by_author: "Gisteren door %{author}"
      x_days_ago_by_author: "%{x} dagen geleden door %{author}"
      trending_title: "Populair"
      trending_text: "Ben je geïnteresseerd in wat er gebeurt op het platform? Hier zijn de drie meest trending bijdragen en wat mensen erover zeggen."
      no_notifications: "Geen meldingen"
      one_notification: "1 melding"
      multiple_notifications: "%{notifCount} meldingen"
      no_unread_notifications: "Tijd om deel te nemen!  Bezoek het platform om te ontdekken wat er aan het gebeuren is."
      unread_notifications: "Je hebt nieuwe meldingen! Bezoek het platform om te ontdekken wat er aan het gebeuren is."
    threshold_reached_for_admin:
      cta_process_initiative: 'Zet de volgende stappen met dit voorstel'
      main_header: 'Een voorstel heeft het benodigd aantal stemmen bereikt!'
      subject: '"%{input_title}" heeft het benodigd aantal stemmen op je platform bereikt'
      preheader: 'Tijd om de volgende stappen te nemen'
    welcome:
      cta_join_platform: 'Ontdek het platform'
      subject: 'Welkom op het platform van %{organizationName}'
      main_header: Welkom!
      message_welcome: 'Leuk dat je je hebt geregistreerd op het participatieplatform van %{organizationName}. Je kan het platform nu ontdekken en je stem laten horen. Je kan ook een profielfoto en een korte beschrijving toevoegen om anderen te vertellen wie je bent.'
      preheader: 'Hier is wat je kan doen op het platform van %{organizationName}'
    idea_published:
      subject:
        idea: 'Je idee is gepubliceerd'
        question: 'Je vraag is gepubliceerd'
        contribution: 'Je bijdrage is gepubliceerd'
        project: 'Je project is gepubliceerd'
        issue: 'Je reactie is gepubliceerd'
        option: 'Je optie is gepubliceerd'
        proposal: 'Je voorstel is gepubliceerd'
        petition: 'Je petitie is gepubliceerd'
      main_header: 'Je hebt "%{input_title}" geplaatst'
      header_message: 'Laten we ervoor zorgen dat het gelezen wordt.'
      message_get_votes: 'Bereik meer mensen met je idee:'
      action_published_idea: 'Idee gepubliceerd'
      action_add_image: '%{addImageLink} om de visibiliteit van je idee te vergroten'
      add_image: 'Voeg een afbeelding toe'
      action_share_fb: 'Breng je vrienden op de hoogte op %{fbLink}'
      action_share_twitter: 'Informeer je volgers op %{twitterLink}'
      action_send_email: 'Stuur je contactpersonen een %{sendEmailLink}'
      send_email: e-mail
      action_share_link: 'Deel het op een kanaal naar keuze met deze %{link}'
      link: link
      preheader: '%{firstName}, fijn dat je een idee hebt gedeeld op het platform van %{organizationName}. Zoek er nu steun voor.'
    your_input_in_screening:
      main_header:
        idea: 'Je idee staat in "%{prescreening_status_title}"'
        question: 'Je vraag staat in "%{prescreening_status_title}"'
        contribution: 'Je bijdrage staat in "%{prescreening_status_title}"'
        project: 'Je project staat in "%{prescreening_status_title}"'
        issue: 'Je reactie staat in "%{prescreening_status_title}"'
        option: 'Je optie staat op "%{prescreening_status_title}"'
        proposal: 'Je voorstel staat in "%{prescreening_status_title}"'
        petition: 'Je petitie staat in "%{prescreening_status_title}"'
      message: '"%{input_title}" wordt zichtbaar voor anderen zodra het is beoordeeld en goedgekeurd.'
      subject: '"%{input_title}" is bijna gepubliceerd'
      preheader: 'Het staat momenteel in %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}: Je hebt succesvol gestemd'
      preheader: 'Je hebt succesvol gestemd op het participatieplatform van %{organizationName}'
      title_basket_submitted: 'Je hebt succesvol gestemd'
      event_description: 'Bedankt voor je deelname. Je stemmen zijn geregistreerd. Ga naar het platform van %{organizationName} om je stemmen te bekijken en te beheren.'
      cta_see_votes_submitted: 'Bekijk ingediende stemmen'
      cta_message: 'Klik op de knop hieronder om deel te nemen'
    native_survey_not_submitted:
      subject: '%{organizationName}: Bijna klaar! Stuur je antwoorden in'
      preheader: 'Je hebt de vragenlijst op het participatieplatform van %{organizationName} niet afgerond'
      title_native_survey_not_submitted: 'Bijna klaar! Stuur je antwoorden in'
      body_native_survey_not_submitted: 'Je bent begonnen met het delen van je antwoorden op %{phaseTitle} maar hebt ze niet ingestuurd. Inzendingen sluiten op %{phaseEndDate}. Klik op de knop hieronder om verder te gaan waar je gebleven was.'
      body_native_survey_not_submitted_no_date: 'Je bent begonnen met het delen van je antwoorden op %{phaseTitle} , maar hebt ze niet ingestuurd. Klik op de knop hieronder om verder te gaan waar je gebleven was.'
      cta_complete_your_survey_response: 'Ga verder met het beantwoorden van de vragenlijst'
    voting_basket_not_submitted:
      subject: '%{organizationName}: Je hebt je stemmen niet ingediend'
      preheader: 'Je hebt je stem niet uitgebracht op het participatieplatform van %{organizationName}'
      title_basket_not_submitted: 'Je hebt je stemmen niet ingediend'
      event_description: 'Je hebt een paar opties geselecteerd voor %{contextTitle}, maar je hebt je selectie niet ingediend.'
      cta_view_options_and_vote: 'Bekijk opties en stem'
      cta_message: 'Klik op de knop hieronder om je geselecteerde opties in te dienen'
    voting_last_chance:
      subject: '%{organizationName}: Laatste kans om te stemmen voor %{phaseTitle}'
      preheader: 'Laatste kans om te stemmen voor %{phaseTitle} op het participatieplatform van %{organizationName}'
      title_last_chance: 'Laatste kans om te stemmen voor %{phaseTitle}'
      body_1: 'De stemfase voor het %{projectTitle} project loopt morgen om middernacht af.'
      body_2: 'De tijd dringt en we hebben gemerkt dat je nog geen stem hebt uitgebracht! Doe nu mee door op de knop hieronder te klikken.'
      body_3: 'Door dit te doen krijg je toegang tot een reeks opties en krijg je de kans om je inbreng te geven, wat cruciaal is bij het beslissen over de toekomst van dit project.'
      cta_vote: 'Stem'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} stemresultaten bekendgemaakt!'
      preheader: '%{phaseTitle} stemresultaten bekendgemaakt op het participatieplatform van %{organizationName}'
      title_results: '%{phaseTitle} stemresultaten bekendgemaakt!'
      body_1: 'De resultaten zijn binnen!'
      body_2: 'De resultaten van de %{phaseTitle} stemming op het %{organizationName} platform zijn gepubliceerd!'
      body_3: 'We raden je aan om de resultaten te bekijken en op de hoogte te blijven van de volgende stappen.'
      cta_see_results: 'Bekijk resultaten op het platform'
    event_registration_confirmation:
      subject: "Je bent erbij! Je inschrijving voor \"%{eventTitle}\" is bevestigd"
      preheader: "%{firstName}, bedankt voor het registreren voor %{eventTitle}"
      header_message: "%{firstName}, bedankt voor het registreren voor"
      event_details:
        labels:
          date: 'Datum'
          location: 'Locatie'
          online_link: 'Online link'
          description: 'Beschrijving'
          project: 'Projecten'
      cta_go_to_event: 'Bekijk het evenement'
      cta_add_to_calendar: 'Toevoegen aan je kalender'
    voting_phase_started:
      subject: '%{organizationName}: De stemmingsfase is begonnen voor %{projectName}'
      preheader: 'De stemmingsfase is begonnen voor %{projectName} op het participatieplatform van %{organizationName}'
      event_description: 'Het project "%{projectName}" vraagt je om te stemmen tussen %{numIdeas} opties:'
      cta_message: 'Klik op de knop hieronder om deel te nemen'
      cta_vote: 'Ga naar het platform om te stemmen'
    survey_submitted:
      subject: '%{organizationName}: Bedankt voor je reactie! 🎉'
      preheader: 'Hier zijn de details van je inzending.'
      main_header: 'Bedankt voor het delen van je mening over "%{projectName}"!'
      your_input_submitted: 'Je bijdrage voor "%{projectName}" is succesvol ingediend.'
      if_you_would_like_to_review: 'Als je je inzending nog eens wilt bekijken, kun je je reacties hieronder downloaden.'
      your_submission_has_id: 'Je inzending heeft de volgende unieke identificatiecode:'
      you_can_use_this_id: 'Je kunt deze identificatiecode gebruiken om contact op te nemen met de beheerders van het platform als je wilt dat je inzending wordt verwijderd.'
      download_responses: 'Download je reacties'
    admin_labels:
      recipient_role:
        admins: 'Aan platformbeheerders'
        admins_and_managers: 'Aan beheerders'
        managers: 'Aan beheerders'
        project_participants: 'Aan projectdeelnemers'
        registered_users: 'Aan geregistreerde gebruikers'
      recipient_segment:
        admins: 'Platformbeheerders'
        admins_and_managers: 'Beheerders'
        admins_and_managers_assigned_to_the_input: 'Aan de bijdrage toegewezen beheerders'
        admins_and_managers_managing_the_project: 'Platformbeheerders & beheerders die het project beheren'
        admins_assigned_to_a_proposal: 'Aan een voorstel toegewezen platformbeheerders'
        all_users: 'Alle gebruikers'
        all_users_who_uploaded_proposals: 'Alle gebruikers die voorstellen hebben geüpload'
        managers: 'Beheerders'
        managers_managing_the_project: 'Beheerders die het project beheren'
        new_attendee: 'Nieuw geregistreerde gebruiker'
        project_reviewers: 'Projectbeoordelaars en mapmanagers'
        project_review_requester: 'Gebruiker die de projectbeoordeling heeft aangevraagd'
        user_who_commented: 'Gebruiker die een reactie gaf'
        user_who_is_invited_to_cosponsor_a_proposal: 'Gebruiker die is uitgenodigd om een voorstel te steunen'
        user_who_is_mentioned: 'Gebruiker die genoemd wordt'
        user_who_is_receiving_admin_rights: 'Gebruiker die platformbeheerdersrechten krijgt'
        user_who_is_receiving_folder_moderator_rights: 'Gebruiker die mapbeheerdersrechten krijgt'
        user_who_is_receiving_project_moderator_rights: 'Gebruiker die projectbeheerdersrechten krijgt'
        user_who_published_the_input: 'Gebruiker die de bijdrage heeft gepubliceerd'
        user_who_published_the_proposal: 'Gebruiker die het voorstel heeft gepubliceerd'
        user_who_registers: 'Gebruiker die zich registreert'
        user_who_submitted_the_input: 'Gebruiker die de bijdrage heeft ingestuurd'
        user_who_voted: 'Gebruiker die heeft gestemd'
        user_who_was_invited: 'Gebruiker die werd uitgenodigd'
        user_with_unsubmitted_survey: 'Gebruiker die zijn vragenlijst is begonnen, maar nog niet heeft ingediend'
        user_with_unsubmitted_votes: 'Gebruiker die nog geen stem heeft uitgebracht'
        users_who_engaged_but_not_voted: 'Gebruikers die betrokken waren bij het project maar niet hebben gestemd'
        users_who_engaged_with_the_project: 'Gebruikers die zich met het project bezighielden'
        users_who_follow_the_input: 'Gebruikers die de bijdrage volgen'
        users_who_follow_the_project: 'Gebruikers die het project volgen'
        users_who_follow_the_proposal: 'Gebruikers die het voorstel volgen'
      content_type:
        comments: 'Reacties'
        content_moderation: 'Inhoudelijke moderatie'
        events: 'Activiteiten'
        general: 'Algemeen'
        inputs: 'Bijdragen'
        internal_comments: 'Interne reacties'
        permissions: 'Machtigingen'
        projects: 'Projecten'
        proposals: 'Voorstellen'
        reactions: 'Likes/dislikes'
        voting: 'Stemming'
        surveys: 'Vragenlijsten'
      trigger:
        7_days_after_invite_is_sent: '7 dagen na verzending van de uitnodiging'
        7_days_before_the_project_changes_phase: '7 dagen voor het project van fase verandert'
        comment_is_deleted: 'Reactie is verwijderd'
        comment_is_flagged_as_spam: 'Reactie is gemarkeerd als spam'
        content_gets_flagged_as_innapropiate: 'Inhoud wordt gemarkeerd als ontoelaatbaar'
        initiative_resubmitted_for_review: 'Voorstel opnieuw ingediend voor beoordeling'
        input_is_assigned: 'De bijdrage is toegewezen'
        input_is_flagged_as_spam: 'Bijdrage is gemarkeerd als spam'
        input_is_published: 'Input is gepubliceerd'
        input_is_updated: 'De bijdrage is bijgewerkt'
        input_status_changes: 'Wijzigingen in de status van de input'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Interne reactie is geplaatst op aan gebruiker toegewezen bijdrage'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Interne reactie is geplaatst op bijdrage in project of map die gebruiker beheert'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Interne reactie is geplaatst op bijdrage waar gebruiker interne reactie op heeft gegeven'
        internal_comment_is_posted_on_idea_user_moderates: 'Interne reactie is geplaatst op bijdrage die gebruiker modereert'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Interne reactie is geplaatst op niet-toegewezen bijdrage in onbeheerd project'
        project_review_request: 'Moderator heeft een projectbeoordeling aangevraagd'
        project_review_state_change: 'Reviewer heeft het project goedgekeurd'
        new_input_awaits_screening: 'Nieuwe bijdrage wacht op screening'
        new_input_is_published: 'Nieuwe bijdrage is gepubliceerd'
        new_proposal_is_posted: 'Nieuw voorstel is geplaatst'
        project_phase_changes: 'Wijzigingen in projectfase'
        project_published: 'Project gepubliceerd'
        proposal_gets_reported_as_spam: 'Voorstel wordt gerapporteerd als spam'
        proposal_is_assigned_to_admin: 'Voorstel is toegewezen aan platformbeheerder'
        proposal_is_published: 'Voorstel is gepubliceerd'
        proposal_is_updated: 'Het voorstel wordt bijgewerkt'
        proposal_is_upvoted_above_threshold: 'Voorstel wordt boven de drempel gestemd'
        proposal_status_changes: 'Wijzigingen in de status van het voorstel'
        registration_to_event: 'Registratie voor een activiteit'
        survey_1_day_after_draft_saved: '1 dag nadat de gebruiker de vragenlijst voor het laatst in concept heeft opgeslagen'
        user_accepts_invitation_to_cosponsor_a_proposal: 'Gebruiker accepteert uitnodiging om een voorstel te steunen'
        user_comments: 'Reacties van de gebruiker'
        user_comments_on_input: 'Gebruiker reageert op bijdrage'
        user_comments_on_proposal: 'Gebruiker reageert op het voorstel'
        user_is_given_admin_rights: 'Gebruiker krijgt platformbeheerdersrechten'
        user_is_given_folder_moderator_rights: 'Gebruiker krijgt mapbeheerdersrechten'
        user_is_given_project_moderator_rights: 'Gebruiker krijgt projectbeheerdersrechten'
        user_is_invited_to_cosponsor_a_proposal: 'Gebruiker is uitgenodigd om een voorstel te steunen'
        user_is_mentioned: 'Gebruiker wordt genoemd'
        user_is_mentioned_in_internal_comment: 'Gebruiker wordt genoemd in interne reactie'
        user_registers_for_the_first_time: 'Gebruiker registreert zich voor het eerst'
        user_replies_to_comment: 'Gebruiker reageert op reactie'
        user_replies_to_internal_comment: 'Gebruiker reageert op interne reactie'
        voting_1_day_after_last_votes: '1 dag nadat gebruiker voor het laatst heeft gestemd'
        voting_2_days_before_phase_closes: '2 dagen voordat de stemmingsfase sluit'
        voting_basket_submitted: 'Stemmen zijn ingediend'
        voting_phase_ended: 'Stemmingsfase beëindigd'
