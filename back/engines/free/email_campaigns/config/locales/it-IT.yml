it:
  email_campaigns:
    campaign_type_description:
      "manual": <PERSON><PERSON><PERSON><PERSON> ufficiali
      "manual_project_participants": <PERSON><PERSON><PERSON>i ufficiali ai partecipanti al progetto
      "admin_rights_received": <PERSON>ritti di amministratore ricevuti
      "comment_deleted_by_admin": Cancellazione del mio commento
      "comment_marked_as_spam": Segnalazione di spam di commenti
      "comment_on_your_comment": Una risposta al mio commento
      "comment_on_idea_you_follow": Un commento su un'idea che segui
      "community_monitor_report": Rapporto di monitoraggio della comunità
      "cosponsor_of_your_idea": Un utente accetta il mio invito a co-sponsorizzare la mia proposta
      "event_registration_confirmation": Conferma della registrazione all'evento
      "idea_marked_as_spam": Segnalazione di spam di idee
      "idea_published": Pubblicazione della mia idea
      "invitation_to_cosponsor_idea": Invito a co-sponsorizzare una proposta
      "invite_received": Invito
      "invite_reminder": Promemoria dell'invito
      "internal_comment_on_idea_assigned_to_you": Commento interno sull'input che mi è stato assegnato
      "internal_comment_on_idea_you_commented_internally_on": Commento interno su input Ho commentato internamente su
      "internal_comment_on_idea_you_moderate": Commento interno su un input nel progetto o nella cartella che gestisco
      "internal_comment_on_unassigned_unmoderated_idea": Commento interno su un input non assegnato in un progetto non gestito
      "internal_comment_on_your_internal_comment": Commento interno sul mio commento interno
      "mention_in_official_feedback": Menzione in un aggiornamento
      "mention_in_internal_comment": Menzione in un commento interno
      "new_comment_for_admin": Nuovo commento in un progetto che modero
      "new_idea_for_admin": Nuova idea in un progetto che modero
      "official_feedback_on_idea_you_follow": Aggiornamenti su un'idea che segui
      "password_reset": Ripristino della password
      "project_moderation_rights_received": Diritti di moderazione del progetto ricevuti
      "project_folder_moderation_rights_received": Diritti di gestione delle cartelle ricevuti
      "project_phase_started": Nuova fase del progetto
      "project_phase_upcoming": Prossima nuova fase del progetto
      "project_published": Progetto pubblicato
      "project_review_request": Richiesta di revisione del progetto
      "project_review_state_change": Progetto approvato
      "status_change_on_idea_you_follow": Cambiamento di stato di un'idea che segui
      "survey_submitted": Sondaggio inviato
      "threshold_reached_for_admin": La proposta ha raggiunto la soglia di voto
      "welcome": Dopo la registrazione
      "admin_digest": Panoramica settimanale per gli amministratori
      "moderator_digest": Panoramica settimanale per i project manager
      "assignee_digest": Panoramica settimanale delle idee assegnate
      "user_digest": Panoramica settimanale
      "voting_basket_submitted": Conferma del voto
      "native_survey_not_submitted": Sondaggio non inviato
      "voting_basket_not_submitted": Voti non inviati
      "voting_last_chance": Ultima possibilità di voto
      "voting_phase_started": Nuova fase del progetto con votazione
      "voting_results": Risultati delle votazioni
      "your_input_in_screening": Il mio contributo è in attesa di essere vagliato
    general:
      by_author: 'da %{authorName}'
      author_wrote: '%{authorName} ha scritto:'
      cta_goto_idea: 'Vai a questa idea'
      cta_goto_input: 'Vai a questo ingresso'
      cta_goto:
        idea: 'Vai a questa idea'
        question: 'Vai a questa domanda'
        contribution: 'Vai a questo contributo'
        project: 'Vai a questo progetto'
        issue: 'Vai a questo numero'
        option: 'Vai a questa opzione'
        proposal: 'Vai a questa proposta'
        petition: 'Vai a questa petizione'
      cta_goto_your:
        idea: 'Vai alla tua idea'
        question: 'Vai alla tua domanda'
        contribution: 'Vai al tuo contributo'
        project: 'Vai al tuo progetto'
        issue: 'Vai al tuo problema'
        option: 'Vai alla tua opzione'
        proposal: 'Vai alla tua proposta'
        petition: 'Vai alla tua petizione'
      cta_goto_proposal: 'Vai a questa proposta'
      cta_goto_project: 'Vai a questo progetto'
    schedules:
      weekly:
        "0": "Settimanalmente, la domenica su %{hourOfDay}"
        "1": "Settimanalmente, il lunedì %{hourOfDay}"
        "2": "Settimanalmente, il martedì %{hourOfDay}"
        "3": "Settimanalmente, il mercoledì %{hourOfDay}"
        "4": "Settimanalmente, il giovedì %{hourOfDay}"
        "5": "Settimanalmente, il venerdì %{hourOfDay}"
        "6": "Settimanalmente, il sabato %{hourOfDay}"
      quarterly: "Trimestralmente, il primo giorno del trimestre"
    preview_data:
      first_name: 'Jane'
      last_name: 'Sconosciuto'
      display_name: 'Sconosciuta'
      comment_body: 'Questo è un commento di esempio utilizzato per visualizzare l''anteprima del contenuto delle e-mail. Non si tratta di un contenuto reale.'
      idea_title: 'Esempio di idea'
    footer:
      "link_privacy_policy": "Politica sulla privacy"
      "link_terms_conditions": "Termini e condizioni"
      "link_unsubscribe": "Cancella l'iscrizione"
      "powered_by": "Alimentato da"
      "recipient_statement": "Questa e-mail ti è stata inviata da Go Vocal per conto di %{organizationName}, perché sei un utente registrato di %{organizationLink}."
      "unsubscribe_statement": "Se non vuoi più ricevere queste e-mail in futuro, puoi scrivere a %{unsubscribeLink} ."
      "unsubscribe_text": "cancella l'iscrizione"
    follow:
      "unfollow_here": "Hai ricevuto questa notifica a causa di un articolo che segui. <a href=\"%{unfollow_url}\">Puoi disiscriverlo qui.</a>"
    manual:
      preheader: 'Hai ricevuto una mail da %{organizationName}'
    comment_deleted_by_admin:
      reason: 'Il motivo per cui il tuo commento è stato cancellato:'
      cta_view: 'Visualizza questa idea'
      event_description: '%{organizationName} ha cancellato il commento che hai scritto su un''idea.'
      main_header: '%{organizationName} ha cancellato il tuo commento'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Il tuo commento è stato considerato inappropriato'
      irrelevant_reason: 'Il tuo commento è stato considerato irrilevante per quel contesto'
      no_reason: 'Nessun motivo è stato specificato'
      subject: 'Il tuo commento è stato cancellato dalla piattaforma di %{organizationName}'
      preheader: 'Il tuo commento è stato cancellato'
    admin_digest:
      subject: 'Il tuo rapporto amministrativo settimanale di %{time}'
      preheader: 'Messaggi Amministrazione di %{organizationName}'
      title_your_weekly_report: '%{firstName}, il tuo rapporto settimanale'
      text_introduction: 'Abbiamo curato per te gli input che hanno generato più attività nell''ultima settimana. Scopri cosa sta succedendo sulla tua piattaforma!'
      cta_visit_the_platform: 'Visita la tua piattaforma'
      new_users: 'Nuovi utenti'
      new_inputs: 'Nuovi ingressi'
      new_comments: 'Nuovi commenti'
      title_activity_past_week: 'Attività della settimana passata'
      title_no_activity_past_week: 'Non c''è stata attività nell''ultima settimana'
      reached_threshold: 'Raggiunta la soglia'
      yesterday_by_author: 'Ieri da %{author}'
      today_by_author: 'Oggi da %{author}'
      x_days_ago_by_author: '%{x} giorni fa da %{author}'
    admin_rights_received:
      cta_manage_platform: 'Gestire la piattaforma'
      message_you_became_administrator: 'Ti sono stati concessi i diritti di amministratore per la piattaforma di partecipazione di %{organizationName}.'
      preheader: 'Ti sono garantiti i diritti di amministratore per la piattaforma di partecipazione di %{organizationName}'
      subject: 'Sei diventato un amministratore sulla piattaforma di %{organizationName}'
      text_create_participatory_process: 'Come amministratore, puoi creare e configurare nuovi progetti di partecipazione. Puoi aggiungere nuove fasi usando la timeline. Ciascuna di queste fasi può avere il proprio comportamento per quanto riguarda la pubblicazione di idee, i commenti e il voto.'
      text_moderate_analyse_input: 'Una volta lanciati i progetti, arriveranno le prime idee. Riceverai rapporti settimanali con tutte le attività chiave in modo da rimanere in cima alle cose. La panoramica delle idee ti aiuterà a moderare l''input e a collaborare alla sua elaborazione.'
      text_platform_setup: 'Come amministratore puoi impostare la tua piattaforma di partecipazione. Scegli un logo, immagini e colori, scrivi un messaggio personale sulla tua home page, invia inviti, definisci cosa vuoi sapere dei tuoi utenti, ...'
      title_create_participatory_process: 'Progettare il processo partecipativo'
      title_moderate_analyse_input: 'Moderare e analizzare l''input'
      title_platform_setup: 'Imposta la tua piattaforma'
      title_what_can_you_do_administrator: 'Cosa puoi fare come amministratore?'
      title_you_became_administrator: 'Sei diventato un amministratore'
    comment_marked_as_spam:
      by_author: 'da %{authorName}'
      commented: '%{authorName} ha commentato:'
      cta_review_comment: 'Rivedi il commento'
      days_ago: '%{numberOfDays} giorni fa da'
      event_description: 'Il seguente commento pubblicato su <strong>''%{post}''</strong> è stato riportato:'
      inappropriate_content: 'Il commento è inappropriato o offensivo.'
      preheader: 'Agisci su questo commento segnalato come spam'
      reported_this_because: '%{reporterFirstName} ha riportato questo perché:'
      subject: '%{organizationName}: %{firstName} %{lastName} ha segnalato questo commento come spam'
      title_comment_spam_report: '%{firstName} %{lastName} ha segnalato questo commento come spam'
      today: Oggi
      wrong_content: 'Il commento non è pertinente.'
      yesterday: Ieri
    community_monitor_report:
      subject: 'È disponibile un nuovo rapporto di monitoraggio della comunità'
      title: 'È disponibile un nuovo rapporto di monitoraggio della comunità'
      text_introduction: 'È stato creato un rapporto di monitoraggio della comunità per il trimestre precedente. Puoi accedervi cliccando sul pulsante qui sotto ed effettuando il login.'
      cta_report_button: 'Visualizza il rapporto'
      report_name: 'Rapporto di monitoraggio della comunità'
    cosponsor_of_your_idea:
      cta_reply_to: 'Visualizza la tua proposta'
      event_description: 'Congratulazioni! %{cosponsorName} ha accettato l''invito a co-sponsorizzare la tua proposta.'
      main_header: '%{cosponsorName} ha accettato il tuo invito a co-sponsorizzare la tua proposta'
      subject: '%{cosponsorName} ha accettato il tuo invito a co-sponsorizzare la tua proposta'
      preheader: '%{cosponsorName} ha accettato il tuo invito a co-sponsorizzare la tua proposta'
    assignee_digest:
      subject: 'Idee che richiedono il vostro feedback: %{numberIdeas}'
      preheader: 'Messaggi Amministrazione di %{organizationName}'
      title_your_weekly_report: '%{firstName}, l''input del cittadino è in attesa del tuo feedback'
      cta_manage_your_input: 'Gestisci il tuo input'
      x_inputs_need_your_feedback: 'Gli input hanno bisogno del tuo feedback'
      title_assignment_past_week: 'Le ultime idee che ti sono state assegnate'
      title_no_assignment_past_week: 'Nessuna nuova idea assegnata la settimana scorsa'
      yesterday_by_author: 'Ieri da %{author}'
      today_by_author: 'Oggi da %{author}'
      x_days_ago_by_author: '%{x} giorni fa da %{author}'
      title_successful_past_week: 'Assegnati a te che hanno raggiunto la soglia'
    idea_marked_as_spam:
      cta_review: 'Recensione'
      report_inappropriate_offensive_content: 'Trovo questo contenuto inappropriato o offensivo.'
      report_not_an_idea: 'Questo contenuto non è un''idea e non appartiene a questo posto.'
      subject: 'Hai un rapporto di spam sulla piattaforma di %{organizationName}'
      preheader: 'Agire su questo rapporto di spam'
      reported_this_because: '%{reporterFirstName} ha riportato questo perché:'
      title_spam_report: '%{firstName} %{lastName} segnalato spam'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Co-sponsorizza questa proposta'
      event_description: '%{authorName} ha creato una nuova proposta e vorrebbe che tu la co-sponsorizzassi.'
      event_description_cosponsoring: 'Co-sponsorizzare una proposta significa che <strong>il tuo nome verrà visualizzato</strong> insieme a quello degli altri co-sponsor della proposta.'
      event_description_before_action: 'Per vedere la proposta e accettare l''invito, devi aver effettuato l''accesso al tuo account.'
      event_description_action: 'Clicca qui sotto per leggere la proposta.'
      main_header: 'Sei stato invitato a co-sponsorizzare una proposta'
      subject: 'Sei stato invitato a co-sponsorizzare una proposta'
      preheader: 'Sei stato invitato a sponsorizzare la proposta di %{authorName}.'
    invite_reminder:
      cta_accept_invitation: 'Accetta il tuo invito'
      invitation_header: 'Il tuo invito è in attesa'
      preheader: '%{organizationName} ti ha inviato un invito a partecipare alla loro piattaforma di partecipazione alcuni giorni fa.'
      invitation_expiry_message: 'Questo invito scade tra circa %{expiryDaysRemaining} giorni.'
      subject: 'In attesa dell''invito per la piattaforma di partecipazione di %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} ha scritto il seguente messaggio:'
      cta_accept_invitation: 'Accetta il tuo invito'
      invitation_header: 'Sei invitato!'
      invitation_header_message: '%{organizationName} ti ha invitato alla loro piattaforma di partecipazione.'
      invitation_expiry_message: 'Questo invito scade tra %{expiryDays} giorni.'
      preheader: '%{organizationName} ti ha invitato alla loro piattaforma di partecipazione.'
      subject: 'Siete invitati a partecipare alla piattaforma di %{organizationName}'
    mention_in_comment:
      cta_reply_to: 'Rispondi a %{commentAuthor}'
      event_description: '%{commentAuthorFull} ti ha menzionato nel suo commento sull''idea ''%{post}''. Clicca il link qui sotto per entrare nella conversazione con %{commentAuthor}'
      main_header: 'La gente parla di te'
      subject: 'Qualcuno ti ha menzionato sulla piattaforma di %{organizationName}'
      preheader: '%{commentAuthor} ti ha menzionato in un commento'
    mention_in_internal_comment:
      cta_reply_to: 'Visualizza il commento di %{firstName}'
      event_description: '%{authorNameFull} Ti ho citato in un commento interno.'
      subject: '%{firstName} Ti ho citato in un commento interno.'
      main_header: '%{firstName} Ti ho citato in un commento interno.'
      preheader: '%{authorNameFull} Ti ho citato in un commento interno.'
    moderator_digest:
      subject: 'Il tuo rapporto settimanale del project manager di %{project_title}'
      preheader: 'Messaggi del project manager di %{organizationName}'
      title_your_weekly_report: '%{firstName}, il tuo rapporto settimanale'
      text_introduction: 'Abbiamo curato per te gli input che hanno generato più attività nell''ultima settimana. Scopri cosa sta succedendo con il tuo progetto!'
      cta_manage: 'Gestire il progetto'
      new_users: 'Nuovi utenti'
      new_ideas: 'Nuove idee'
      new_comments: 'Nuovi commenti'
      title_inputs_past_week: 'Nuovi ingressi nell''ultima settimana'
      title_no_inputs_past_week: 'Nessun nuovo ingresso nell''ultima settimana'
      title_threshold_reached: Soglia raggiunta nell'ultima settimana
      yesterday_by_author: 'Ieri da %{author}'
      today_by_author: 'Oggi da %{author}'
      x_days_ago_by_author: '%{x} giorni fa da %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} ha commentato:'
      cta_reply_to: 'Visualizza il commento di %{commentAuthor}''s'
      days_ago: '%{numberOfDays} giorni fa'
      event_description: '%{authorName} ha aggiunto un nuovo commento sulla tua piattaforma.'
      main_header: '%{firstName}, un nuovo commento è stato inserito sulla tua piattaforma'
      subject: 'Un nuovo commento è stato inserito sulla piattaforma di %{organizationName}'
      preheader: '%{authorName} ha lasciato un commento'
      today: Oggi
      yesterday: Ieri
    comment_on_idea_you_follow:
      cta_reply_to: 'Rispondi a %{commentAuthor}'
      event_description: '%{authorNameFull} placed a reaction on ''%{inputTitle}''. Click the button below to continue the conversation with %{authorName}.'
      main_header:
        idea: '%{authorName} ha commentato un''idea che segui'
        question: '%{authorName} ha commentato una domanda che segui'
        contribution: '%{authorName} ha commentato un contributo che segui'
        project: '%{authorName} ha commentato un progetto che segui'
        issue: '%{authorName} ha commentato un argomento che segue'
        option: '%{authorName} ha commentato un''opzione che segui'
        proposal: '%{authorName} ha commentato una proposta che segui'
        petition: '%{authorName} ha commentato una petizione che segui'
      subject: 'There''s a new comment on "%{input_title}"'
      preheader: '%{authorName} ha lasciato un commento su un''idea per %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Un nuovo input è stato pubblicato sulla tua piattaforma'
      event_description_publication: '%{authorName} ha inviato un nuovo contributo sulla tua piattaforma. Scoprilo subito, dai un feedback o cambia il suo stato!'
      cta_publication: 'Fornisci un feedback a %{authorName}'
      main_header_prescreening: 'Un input richiede la tua revisione'
      event_description_prescreening: '%{authorName} ha inviato un nuovo input sulla tua piattaforma.'
      input_not_visible_prescreening: '<b>L''input non sarà visibile</b> finché non modificherai il suo stato.'
      cta_prescreening: 'Esamina l''input'
      days_ago: '%{numberOfDays} giorni fa'
      preheader: '%{authorName} ha pubblicato una nuova idea sulla tua piattaforma'
      today: Oggi
      yesterday: Ieri
    comment_on_your_comment:
      cta_reply_to: 'Rispondi a %{firstName}'
      event_description: '%{authorNameFull} ha scritto una risposta al tuo commento su ''%{post}'' sulla piattaforma di partecipazione. Clicca il pulsante qui sotto per continuare la conversazione con %{authorName}.'
      subject: 'Hai ricevuto una risposta al tuo commento sulla piattaforma di %{organizationName}'
      main_header: '%{authorName} ha risposto al tuo commento'
      preheader: '%{authorName} ha risposto al tuo commento sulla piattaforma di %{organizationName}'
      replied: '%{authorFirstName} ha commentato:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Visualizza il commento di %{firstName}'
      event_description: '%{authorNameFull} ha commentato il tuo commento interno.'
      subject: 'Hai ricevuto un commento sul tuo commento interno su ''%{post}''.'
      main_header: 'Hai ricevuto un commento sul tuo commento interno su ''%{post}''.'
      preheader: '%{authorName} ho risposto al tuo commento interno su ''%{post}''.'
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Visualizza il commento di %{firstName}'
      event_description: '%{authorNameFull} ha commentato internamente un input che ti è stato assegnato.'
      subject: '''%{post}'' ha un nuovo commento interno'
      main_header: '''%{post}'' ha un nuovo commento interno'
      preheader: '''%{post}'' ha un nuovo commento interno'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Visualizza il commento di %{firstName}'
      event_description: '%{authorNameFull} ha commentato internamente un input che hai commentato internamente.'
      subject: '''%{post}'' ha un nuovo commento interno'
      main_header: '''%{post}'' ha un nuovo commento interno'
      preheader: '''%{post}'' ha un nuovo commento interno'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Visualizza il commento di %{firstName}'
      event_description: '%{authorNameFull} ha commentato internamente un input in un progetto o in una cartella che gestisci.'
      subject: '''%{post}'' ha un nuovo commento interno'
      main_header: '''%{post}'' ha un nuovo commento interno'
      preheader: '''%{post}'' ha un nuovo commento interno'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Visualizza il commento di %{firstName}'
      event_description: '%{authorNameFull} ha commentato internamente un input non assegnato in un progetto non gestito.'
      subject: '''%{post}'' ha un nuovo commento interno'
      main_header: '''%{post}'' ha un nuovo commento interno'
      preheader: '''%{post}'' ha un nuovo commento interno'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} gave an update on ''%{input_title}''.'
      header_title: 'There''s an update on "%{input_title}"'
      subject: 'Official feedback was posted on "%{input_title}"'
      preheader: 'There''s an update on an input you follow'
    mention_in_official_feedback:
      cta_reply_to: 'Rispondi a %{organizationName}'
      event_description: '%{organizationName} ti hanno menzionato nel loro feedback sull''idea ''%{post}''. Clicca il link qui sotto per entrare nella conversazione con %{organizationName}'
      main_header: 'Sei stato menzionato'
      subject: '%{organizationName} ti hanno menzionato nel loro feedback'
      preheader: '%{commentAuthor} ti ha menzionato nel feedback'
    project_moderation_rights_received:
      cta_manage_project: 'Gestire questo progetto'
      message_you_became_moderator: 'Un amministratore della piattaforma di partecipazione di %{organizationName} ti ha appena nominato project manager del seguente progetto:'
      no_ideas: 'Nessuna idea ancora'
      preheader: 'Un amministratore della piattaforma di partecipazione di %{organizationName} ti ha appena nominato project manager del seguente progetto'
      subject: 'Sei diventato un project manager sulla piattaforma di %{organizationName}'
      text_design_participatory_process: 'Come project manager, puoi configurare come gli utenti interagiscono all''interno del tuo progetto. Puoi aggiungere nuove fasi usando la timeline. Ognuna di queste fasi può avere il proprio comportamento per quanto riguarda la pubblicazione di idee, i commenti e il voto.'
      text_moderate_analyse_input: 'Una volta lanciato il progetto, arriveranno le prime idee. Riceverai rapporti settimanali con tutte le attività chiave in modo da rimanere in cima alle cose. La panoramica delle idee nella tua vista di project manager ti aiuterà a capire quali idee hanno ottenuto il maggior numero di upvotes e downvotes.'
      text_share_project_information: 'Per aumentare la qualità delle idee che stai ricevendo, è fondamentale condividere informazioni sufficienti: aggiungi una descrizione del progetto, allega immagini (inclusi schizzi e piani), e comunica tutti gli eventi correlati in corso. Ricorda: una buona informazione precede una buona partecipazione!'
      title_design_participatory_process: 'Progettare il processo partecipativo'
      title_moderate_analyse_input: 'Moderare e analizzare l''input'
      title_share_project_information: 'Fornire informazioni sul progetto'
      title_what_can_you_do_moderator: 'Cosa puoi fare come project manager?'
      title_you_became_moderator: 'Sei diventato un project manager'
      x_ideas: '%{numberOfIdeas} idee'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Visualizza questa cartella'
      message_added_as_folderadmin: 'Ti sono stati dati i diritti di gestione delle cartelle sulla piattaforma di partecipazione di %{organizationName} per la seguente cartella:'
      no_projects: 'Nessun progetto ancora'
      preheader: 'Un amministratore della piattaforma di partecipazione di %{organizationName} ti ha appena nominato manager della seguente cartella'
      subject: 'Sei diventato un gestore di cartelle di progetto sulla piattaforma di partecipazione di %{organizationName}'
      text_manage_folder: 'Una cartella è un modo per organizzare diversi progetti di partecipazione insieme. Come manager di una cartella, puoi modificare la cartella e la descrizione della cartella e creare nuovi progetti (per eliminare i progetti, contatta l''amministratore della piattaforma). Avrai anche i diritti di gestione del progetto su tutti i progetti all''interno della cartella, permettendoti di modificare i progetti, gestire gli input e inviare e-mail ai partecipanti.'
      text_moderate_analyse_input: 'Una volta che i progetti sono lanciati, i primi input inizieranno ad arrivare. Riceverai rapporti settimanali con le attività principali in modo da poter rimanere in cima a ciò che sta accadendo. L''Input Manager nel tuo pannello "Manage Platform" ti permette di vedere e gestire gli input, inclusa l''assegnazione di status e la risposta a post e commenti.'
      text_design_participatory_process: 'Puoi gestire i diversi progetti di partecipazione all''interno della tua cartella - configurare il metodo di partecipazione, aggiungere una descrizione del progetto, allegare immagini e comunicare gli eventi correlati. Puoi anche gestire il modo in cui i partecipanti interagiscono con i tuoi progetti, compresa l''impostazione dei diritti di accesso e la configurazione delle impostazioni di pubblicazione, votazione e commento.'
      title_design_participatory_process: 'Progettare il processo partecipativo'
      title_moderate_analyse_input: 'Moderare e analizzare l''input'
      title_manage_folder: 'Gestisci le impostazioni della cartella e crea nuovi progetti.'
      title_what_can_you_do_folderadmin: 'Cosa puoi fare come gestore di cartelle?'
      title_added_as_folderadmin: 'Sei stato aggiunto come gestore di cartelle'
      x_projects: '%{numberOfProjects} progetti'
    project_phase_started:
      cta_view_phase: 'Scopri questa nuova fase'
      event_description: 'Questo progetto è entrato in una nuova fase sulla piattaforma di %{organizationName}. Clicca sul link qui sotto per saperne di più!'
      main_header: 'Una nuova fase è iniziata per il progetto ''%{projectName}'''
      subtitle: 'Informazioni su ''%{projectName}'''
      new_phase: 'Questo progetto è entrato nella fase ''%{phaseTitle}'''
      subject: '%{projectName} è entrato in una nuova fase'
      preheader: 'Una nuova fase è stata avviata per %{projectName}'
    project_phase_upcoming:
      cta_view_phase: 'Impostare questa nuova fase'
      event_description: 'Il progetto ''%{projectName}'' entrerà presto in una nuova fase. Assicurati che tutto sia impostato per questa fase: C''è una descrizione adeguata? Le idee selezionate sono trasferite a questa fase? Vuoi informare i tuoi cittadini sulle specifiche di questa fase attraverso una campagna email personalizzata?'
      main_header: '%{firstName}, un progetto entrerà presto in una nuova fase'
      subtitle: 'Informazioni su ''%{projectName}'''
      new_phase: 'Il progetto entrerà nella fase ''%{phaseTitle}'''
      subject: 'Prepara tutto per la nuova fase di %{projectName}'
      preheader: 'Una nuova fase inizierà presto per %{projectName}'
    project_published:
      subject: 'Un nuovo progetto è stato pubblicato sulla piattaforma di %{organizationName}.'
      header_title: 'È stato pubblicato un nuovo progetto'
      header_message: 'La piattaforma di partecipazione di %{organizationName} ha appena pubblicato il seguente progetto:'
      preheader: 'È stato pubblicato un nuovo progetto'
    project_review_request:
      subject: 'Richiesta di revisione: Un progetto è in attesa di approvazione.'
      header: '%{requesterName} ti ha invitato a visionare il progetto "%{projectTitle}".'
      header_message: "Attualmente il progetto è in modalità bozza e non è visibile agli utenti. Una volta che l'avrai rivisto e approvato, il moderatore potrà pubblicarlo."
      cta_review_project: "Rivedere il progetto"
    project_review_state_change:
      subject: '"%{projectTitle}" è stato approvato'
      header: '%{reviewerName} ha approvato il progetto "%{projectTitle}".'
      header_message: "Il progetto è ora pronto per essere pubblicato. Puoi pubblicarlo quando sei pronto!"
      cta_go_to_project: "Vai alle impostazioni del progetto"
    status_change_on_idea_you_follow:
      status_change: 'The new status of this input is ''%{status}'''
      header_message: '%{organizationName} updated the status of the input ''%{input_title}'' on their digital participation platform.'
      header_title: 'An input you follow has a new status'
      subject: 'The status of "%{input_title}" has changed'
      preheader: 'An input you follow has a new status'
    user_digest:
      subject: "La tua attività sulla piattaforma di partecipazione di %{organizationName}"
      commented: "%{authorFirstName} ha commentato:"
      preheader: "Panoramica settimanale di %{organizationName}"
      title_your_weekly_report: "Scopri cosa è successo la settimana scorsa"
      intro_text: "Ecco un riassunto di quello che è successo sulla piattaforma di partecipazione di %{organizationName}."
      cta_go_to_the_platform: "Vai alla piattaforma"
      title_no_activity_past_week: "Nessuna attività nell'ultima settimana"
      successful_proposals_title: "Raggiunta la soglia"
      successful_proposals_text: "Queste proposte hanno ottenuto abbastanza sostegno per passare alla fase successiva! Clicca sulla proposta per saperne di più su cosa succede dopo."
      today_by_author: "Oggi da %{author}"
      yesterday_by_author: "Ieri da %{author}"
      x_days_ago_by_author: "%{x} giorni fa da %{author}"
      trending_title: "Tendenza"
      trending_text: "Ti interessa sapere cosa sta succedendo sulla piattaforma? Ecco i tre contributi più in voga e cosa dicono gli utenti."
      no_notifications: "Nessuna notifica"
      one_notification: "1 notifica"
      multiple_notifications: "%{notifCount} notifiche"
      no_unread_notifications: "Non hai notifiche non lette. Visita la piattaforma per contribuire con il tuo contributo e generare nuove notifiche!"
      unread_notifications: "Hai delle notifiche non lette. Visita la piattaforma per scoprire cosa sta succedendo!"
    threshold_reached_for_admin:
      cta_process_initiative: 'Portare questa iniziativa alle fasi successive'
      main_header: 'Un''iniziativa ha raggiunto la soglia di voto!'
      subject: 'Un''iniziativa ha raggiunto la soglia di voto sulla tua piattaforma'
      preheader: 'Assicurati di fare i prossimi passi'
    welcome:
      cta_join_platform: 'Scoprire la piattaforma'
      subject: 'Benvenuti sulla piattaforma di %{organizationName}'
      main_header: Benvenuti!
      message_welcome: 'Congratulazioni, ti sei iscritto con successo alla piattaforma di partecipazione di %{organizationName}. Ora puoi scoprire la piattaforma e far sentire la tua voce. Puoi anche aggiungere una foto del profilo e una breve descrizione per dire agli altri chi sei.'
      preheader: 'Ecco cosa potete fare sulla piattaforma di %{organizationName}'
    idea_published:
      subject:
        idea: 'La tua idea è stata pubblicata'
        question: 'La tua domanda è stata pubblicata'
        contribution: 'Il tuo contributo è stato pubblicato'
        project: 'Il tuo progetto è stato pubblicato'
        issue: 'Il tuo numero è stato pubblicato'
        option: 'La tua opzione è stata pubblicata'
        proposal: 'La tua proposta è stata pubblicata'
        petition: 'La tua petizione è stata pubblicata'
      main_header: 'Hai pubblicato un''idea! Assicuriamoci che venga letta.'
      header_message: 'Assicuriamoci che venga letto.'
      message_get_votes: 'Raggiungi più persone con la tua idea:'
      action_published_idea: 'Idea pubblicata'
      action_add_image: '%{addImageLink} per aumentare la visibilità'
      add_image: 'Aggiungere un''immagine'
      action_share_fb: 'Fallo sapere ai tuoi amici su %{fbLink}'
      action_share_twitter: 'Informa i tuoi seguaci su %{twitterLink}'
      action_send_email: 'Invia ai tuoi contatti un %{sendEmailLink}'
      send_email: e-mail
      action_share_link: 'Condividilo tramite qualsiasi canale copiando il %{link}'
      link: link
      preheader: '%{firstName}, complimenti per aver pubblicato la tua idea sulla piattaforma di %{organizationName}. Ora raccogliete il sostegno.'
    your_input_in_screening:
      main_header:
        idea: 'La tua idea è in "%{prescreening_status_title}".'
        question: 'La tua domanda è in "%{prescreening_status_title}".'
        contribution: 'Il tuo contributo è su "%{prescreening_status_title}".'
        project: 'Il tuo progetto è in "%{prescreening_status_title}".'
        issue: 'Il tuo problema è in "%{prescreening_status_title}".'
        option: 'La tua opzione è in "%{prescreening_status_title}".'
        proposal: 'La tua proposta è su "%{prescreening_status_title}".'
        petition: 'La tua petizione è su "%{prescreening_status_title}".'
      message: '"%{input_title}" diventerà visibile agli altri una volta che sarà stata rivista e approvata.'
      subject: '"%{input_title}" è quasi pubblicato'
      preheader: 'Attualmente si trova su %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}Hai votato con successo'
      preheader: 'Hai votato con successo sulla piattaforma di partecipazione di %{organizationName}'
      title_basket_submitted: 'Hai votato con successo'
      event_description: 'Grazie per aver partecipato. I tuoi voti sono stati registrati. Visita la piattaforma di %{organizationName} per vedere e gestire i tuoi voti.'
      cta_see_votes_submitted: 'Vedi i voti inviati'
      cta_message: 'Clicca sul pulsante qui sotto per partecipare'
    native_survey_not_submitted:
      subject: '%{organizationName}Ci siamo quasi! Invia le tue risposte'
      preheader: 'Non hai completato la risposta al sondaggio sulla piattaforma di partecipazione di %{organizationName}.'
      title_native_survey_not_submitted: 'Ci siamo quasi! Invia le tue risposte'
      body_native_survey_not_submitted: 'Hai iniziato a condividere le tue risposte su %{phaseTitle} ma non le hai inviate. L''invio delle risposte si chiuderà il giorno %{phaseEndDate}. Clicca sul pulsante qui sotto per continuare da dove hai lasciato.'
      body_native_survey_not_submitted_no_date: 'Hai iniziato a condividere le tue risposte su %{phaseTitle} ma non le hai inviate. Clicca sul pulsante qui sotto per continuare da dove avevi interrotto.'
      cta_complete_your_survey_response: 'Riprendi la risposta al sondaggio'
    voting_basket_not_submitted:
      subject: '%{organizationName}Non hai inviato i tuoi voti'
      preheader: 'Non hai inviato i tuoi voti sulla piattaforma di partecipazione di %{organizationName}.'
      title_basket_not_submitted: 'Non hai inviato i tuoi voti'
      event_description: 'Hai selezionato alcune opzioni per %{contextTitle} ma non hai inviato la tua selezione.'
      cta_view_options_and_vote: 'Visualizza le opzioni e vota'
      cta_message: 'Clicca sul pulsante sottostante per inviare le opzioni selezionate'
    voting_last_chance:
      subject: '%{organizationName}Ultima possibilità di votare per %{phaseTitle}'
      preheader: 'Ultima possibilità di votare per %{phaseTitle} sulla piattaforma di partecipazione di %{organizationName}'
      title_last_chance: 'Ultima possibilità di votare per %{phaseTitle}'
      body_1: 'La fase di votazione del progetto %{projectTitle} si concluderà domani a mezzanotte.'
      body_2: 'Il tempo sta per scadere e abbiamo notato che non hai ancora espresso il tuo voto! Agisci subito cliccando sul pulsante qui sotto per partecipare.'
      body_3: 'In questo modo avrai accesso a una serie di opzioni e avrai la possibilità di dare il tuo contributo, che è fondamentale per decidere il futuro di questo progetto.'
      cta_vote: 'Vota'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} : rivelati i risultati delle votazioni!'
      preheader: '%{phaseTitle} risultati del voto rivelati sulla piattaforma di partecipazione di %{organizationName}'
      title_results: '%{phaseTitle} I risultati delle votazioni sono stati rivelati!'
      body_1: 'I risultati sono arrivati!'
      body_2: 'Sono stati pubblicati i risultati della votazione di %{phaseTitle} nella piattaforma %{organizationName} !'
      body_3: 'Ti invitiamo a esaminare i risultati e a rimanere sintonizzato per ulteriori aggiornamenti sui prossimi passi.'
      cta_see_results: 'Vedi i risultati nella piattaforma'
    event_registration_confirmation:
      subject: "Sei dentro! La tua iscrizione a \"%{eventTitle}\" è confermata"
      preheader: "%{firstName}, grazie per esserti registrato su %{eventTitle}"
      header_message: "%{firstName}, grazie per esserti registrato a"
      event_details:
        labels:
          date: 'Data'
          location: 'Posizione'
          online_link: 'Link online'
          description: 'Descrizione'
          project: 'Progetto'
      cta_go_to_event: 'Guarda l''evento'
      cta_add_to_calendar: 'Aggiungi al tuo calendario'
    voting_phase_started:
      subject: '%{organizationName}: La fase di voto è iniziata per %{projectName}'
      preheader: 'La fase di votazione è iniziata per %{projectName} sulla piattaforma di partecipazione di %{organizationName}.'
      event_description: 'Il progetto "%{projectName}" ti chiede di votare tra le opzioni di %{numIdeas} :'
      cta_message: 'Clicca sul pulsante qui sotto per partecipare'
      cta_vote: 'Vai alla piattaforma per votare'
    survey_submitted:
      subject: '%{organizationName}Grazie per la tua risposta! 🎉'
      preheader: 'Ecco i dettagli del tuo invio.'
      main_header: 'Grazie per aver condiviso i tuoi pensieri su "%{projectName}"!'
      your_input_submitted: 'Il tuo contributo per "%{projectName}" è stato inviato con successo.'
      if_you_would_like_to_review: 'Se vuoi rivedere la tua candidatura, puoi scaricare le tue risposte qui sotto.'
      your_submission_has_id: 'Il tuo invio ha il seguente identificativo unico:'
      you_can_use_this_id: 'Puoi utilizzare questo identificativo per contattare gli amministratori della piattaforma nel caso in cui desideri che il tuo invio venga rimosso.'
      download_responses: 'Scarica le tue risposte'
    admin_labels:
      recipient_role:
        admins: 'Agli amministratori'
        admins_and_managers: 'Agli amministratori e ai manager'
        managers: 'Ai manager'
        project_participants: 'Ai partecipanti al progetto'
        registered_users: 'Agli utenti registrati'
      recipient_segment:
        admins: 'Amministratori'
        admins_and_managers: 'Amministratori e manager'
        admins_and_managers_assigned_to_the_input: 'Amministratori e manager assegnati all''ingresso'
        admins_and_managers_managing_the_project: 'Amministratori e manager che gestiscono il progetto'
        admins_assigned_to_a_proposal: 'Amministratori assegnati a una proposta'
        all_users: 'Tutti gli utenti'
        all_users_who_uploaded_proposals: 'Tutti gli utenti che hanno caricato proposte'
        managers: 'Dirigenti'
        managers_managing_the_project: 'Manager che gestiscono il progetto'
        new_attendee: 'Utente appena registrato'
        project_reviewers: 'Revisori di progetti e gestori di cartelle'
        project_review_requester: 'Utente che ha richiesto la revisione del progetto'
        user_who_commented: 'Utente che ha commentato'
        user_who_is_invited_to_cosponsor_a_proposal: 'Utente che viene invitato a co-sponsorizzare una proposta'
        user_who_is_mentioned: 'Utente che viene menzionato'
        user_who_is_receiving_admin_rights: 'Utente che riceve i diritti di amministratore'
        user_who_is_receiving_folder_moderator_rights: 'Utente che riceve i diritti di moderatore della cartella'
        user_who_is_receiving_project_moderator_rights: 'Utente che riceve i diritti di moderatore del progetto'
        user_who_published_the_input: 'Utente che ha pubblicato l''input'
        user_who_published_the_proposal: 'Utente che ha pubblicato la proposta'
        user_who_registers: 'Utente che si registra'
        user_who_submitted_the_input: 'Utente che ha inviato l''input'
        user_who_voted: 'Utente che ha votato'
        user_who_was_invited: 'Utente invitato'
        user_with_unsubmitted_survey: 'Utente che ha iniziato ma non ha inviato l''indagine'
        user_with_unsubmitted_votes: 'Utente che non ha inviato i propri voti'
        users_who_engaged_but_not_voted: 'Utenti che si sono impegnati nel progetto ma non hanno votato'
        users_who_engaged_with_the_project: 'Utenti che hanno partecipato al progetto'
        users_who_follow_the_input: 'Gli utenti che seguono l''input'
        users_who_follow_the_project: 'Utenti che seguono il progetto'
        users_who_follow_the_proposal: 'Gli utenti che seguono la proposta'
      content_type:
        comments: 'Commenti'
        content_moderation: 'Moderazione dei contenuti'
        events: 'Eventi'
        general: 'Generale'
        inputs: 'Ingressi'
        internal_comments: 'Commenti interni'
        permissions: 'Permessi'
        projects: 'Progetti'
        proposals: 'Proposte'
        reactions: 'Reazioni'
        voting: 'Votazione'
        surveys: 'Sondaggi'
      trigger:
        7_days_after_invite_is_sent: '7 giorni dopo l''invio dell''invito'
        7_days_before_the_project_changes_phase: '7 giorni prima della fase di modifica del progetto'
        comment_is_deleted: 'Il commento viene cancellato'
        comment_is_flagged_as_spam: 'Il commento viene segnalato come spam'
        content_gets_flagged_as_innapropiate: 'Il contenuto viene segnalato come inappropiato'
        initiative_resubmitted_for_review: 'Proposta ripresentata per la revisione'
        input_is_assigned: 'L''ingresso è assegnato'
        input_is_flagged_as_spam: 'L''input viene contrassegnato come spam'
        input_is_published: 'L''input è pubblicato'
        input_is_updated: 'L''input viene aggiornato'
        input_status_changes: 'Cambiamento dello stato dell''ingresso'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Il commento interno è pubblicato sull''input assegnato all''utente'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Il commento interno viene pubblicato sull''input del progetto o della cartella che l''utente gestisce'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Il commento interno viene pubblicato sull''input che l''utente ha commentato internamente'
        internal_comment_is_posted_on_idea_user_moderates: 'Il commento interno è pubblicato su input che l''utente modera'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Viene pubblicato un commento interno su un input non assegnato in un progetto non gestito'
        project_review_request: 'Il moderatore ha richiesto una revisione del progetto'
        project_review_state_change: 'Il revisore ha approvato il progetto'
        new_input_awaits_screening: 'Un nuovo input in attesa di essere vagliato'
        new_input_is_published: 'Viene pubblicato un nuovo input'
        new_proposal_is_posted: 'La nuova proposta è pubblicata'
        project_phase_changes: 'Cambiamenti di fase del progetto'
        project_published: 'Progetto pubblicato'
        proposal_gets_reported_as_spam: 'La proposta viene segnalata come spam'
        proposal_is_assigned_to_admin: 'La proposta è assegnata all''amministratore'
        proposal_is_published: 'La proposta viene pubblicata'
        proposal_is_updated: 'La proposta è aggiornata'
        proposal_is_upvoted_above_threshold: 'La proposta è votata oltre la soglia'
        proposal_status_changes: 'Modifiche allo stato della proposta'
        registration_to_event: 'Registrazione ad un evento'
        survey_1_day_after_draft_saved: '1 giorno dopo l''ultima volta che l''utente ha salvato l''indagine in bozza'
        user_accepts_invitation_to_cosponsor_a_proposal: 'L''utente accetta l''invito a co-sponsorizzare una proposta'
        user_comments: 'Commenti degli utenti'
        user_comments_on_input: 'Commenti degli utenti sull''input'
        user_comments_on_proposal: 'Commenti degli utenti sulla proposta'
        user_is_given_admin_rights: 'All''utente vengono concessi i diritti di amministratore'
        user_is_given_folder_moderator_rights: 'All''utente vengono concessi i diritti di moderatore della cartella'
        user_is_given_project_moderator_rights: 'All''utente vengono concessi i diritti di moderatore del progetto'
        user_is_invited_to_cosponsor_a_proposal: 'L''utente è invitato a co-sponsorizzare una proposta'
        user_is_mentioned: 'L''utente è menzionato'
        user_is_mentioned_in_internal_comment: 'L''utente è menzionato in un commento interno'
        user_registers_for_the_first_time: 'L''utente si registra per la prima volta'
        user_replies_to_comment: 'L''utente risponde al commento'
        user_replies_to_internal_comment: 'L''utente risponde al commento interno'
        voting_1_day_after_last_votes: '1 giorno dopo l''ultimo voto dell''utente'
        voting_2_days_before_phase_closes: '2 giorni prima della chiusura della fase di voto'
        voting_basket_submitted: 'I voti vengono inviati'
        voting_phase_ended: 'La fase di votazione si è conclusa'
