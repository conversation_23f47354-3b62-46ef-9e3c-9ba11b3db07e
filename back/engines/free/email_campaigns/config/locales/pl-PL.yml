pl:
  email_campaigns:
    campaign_type_description:
      "manual": Of<PERSON><PERSON>ln<PERSON> wiadomości
      "manual_project_participants": Oficjalne wiadomości dla uczestników projektu
      "admin_rights_received": <PERSON>rzyznanie uprawnień administratora
      "comment_deleted_by_admin": <PERSON><PERSON><PERSON><PERSON> mojego komentarza
      "comment_marked_as_spam": Raport dot. komentarzy oznaczonych jako spam
      "comment_on_your_comment": Odpowiedź na mój komentarz
      "comment_on_idea_you_follow": Komentarz do pomysłu, za którym podążasz
      "community_monitor_report": Sprawozdanie monitora wspólnotowego
      "cosponsor_of_your_idea": Użytkownik akceptuje moje zaproszenie do współsponsorowania mojej propozycji.
      "event_registration_confirmation": Potwierdzenie rejestracji na wydarzenie
      "idea_marked_as_spam": Raport dot. pomysłów oznaczonych jako spam
      "idea_published": Publikac<PERSON> mojego pomysłu
      "invitation_to_cosponsor_idea": Zaproszenie do współsponsorowania wniosku
      "invite_received": Zaproszen<PERSON>
      "invite_reminder": Przypomnienie o zaproszeniu
      "internal_comment_on_idea_assigned_to_you": Wewnętrzny komentarz dotyczący przydzielonych mi danych wejściowych
      "internal_comment_on_idea_you_commented_internally_on": Wewnętrzny komentarz do danych wejściowych, które skomentowałem wewnętrznie
      "internal_comment_on_idea_you_moderate": Wewnętrzny komentarz do danych wejściowych w zarządzanym przeze mnie projekcie lub folderze
      "internal_comment_on_unassigned_unmoderated_idea": Wewnętrzny komentarz do nieprzypisanych danych wejściowych w niezarządzanym projekcie
      "internal_comment_on_your_internal_comment": Wewnętrzny komentarz do mojego wewnętrznego komentarza
      "mention_in_official_feedback": Wzmianka w aktualizacji
      "mention_in_internal_comment": Wzmianka w komentarzu wewnętrznym
      "new_comment_for_admin": Nowy komentarz w projekcie, który moderuję
      "new_idea_for_admin": Nowy pomysł w projekcie, który moderuję
      "official_feedback_on_idea_you_follow": Zaktualizuj pomysł, który śledzisz
      "password_reset": Zmiana hasła
      "project_moderation_rights_received": Przyznanie prawa do moderacji projektu
      "project_folder_moderation_rights_received": Przydzielono uprawnienia menedżera folderów
      "project_phase_started": Nowy etap projektu
      "project_phase_upcoming": Nadchodzący nowy etap projektu
      "project_published": Projekt opublikowany
      "project_review_request": Żądanie przeglądu projektu
      "project_review_state_change": Projekt zatwierdzony
      "status_change_on_idea_you_follow": Zmiana statusu pomysłu, za którym podążasz
      "survey_submitted": Przesłana ankieta
      "threshold_reached_for_admin": Propozycja osiągnęła próg poparcia w głosowaniu
      "welcome": Po rejestracji
      "admin_digest": Tygodniowy przegląd dla administratorów
      "moderator_digest": Tygodniowy przegląd dla menadżerów projektu
      "assignee_digest": Tygodniowy przegląd przydzielonych pomysłów
      "user_digest": Tygodniowy przegląd
      "voting_basket_submitted": Potwierdzenie głosowania
      "native_survey_not_submitted": Ankieta nie została przesłana
      "voting_basket_not_submitted": Nieoddane głosy
      "voting_last_chance": Ostatnia szansa na oddanie głosu
      "voting_phase_started": Nowa faza projektu z głosowaniem
      "voting_results": Wyniki głosowania
      "your_input_in_screening": Mój wkład czeka na sprawdzenie
    general:
      by_author: 'przez %{authorName}'
      author_wrote: '%{authorName} napisał:'
      cta_goto_idea: 'Przejdź do tego pomysłu'
      cta_goto_input: 'Przejdź do tego wejścia'
      cta_goto:
        idea: 'Przejdź do tego pomysłu'
        question: 'Przejdź do tego pytania'
        contribution: 'Przejdź do tego wkładu'
        project: 'Przejdź do tego projektu'
        issue: 'Przejdź do tego wydania'
        option: 'Przejdź do tej opcji'
        proposal: 'Przejdź do tej propozycji'
        petition: 'Przejdź do tej petycji'
      cta_goto_your:
        idea: 'Przejdź do swojego pomysłu'
        question: 'Przejdź do pytania'
        contribution: 'Przejdź do swojego wkładu'
        project: 'Przejdź do swojego projektu'
        issue: 'Przejdź do swojej sprawy'
        option: 'Przejdź do swojej opcji'
        proposal: 'Przejdź do swojej propozycji'
        petition: 'Przejdź do swojej petycji'
      cta_goto_proposal: 'Przejdź do tej propozycji'
      cta_goto_project: 'Przejdź do tego projektu'
    schedules:
      weekly:
        "0": "Co tydzień, w niedziele, na stronie %{hourOfDay}."
        "1": "Co tydzień, w poniedziałki %{hourOfDay}"
        "2": "Co tydzień, we wtorki %{hourOfDay}"
        "3": "Co tydzień, w środy %{hourOfDay}"
        "4": "Co tydzień, w czwartki %{hourOfDay}"
        "5": "Co tydzień, w piątki %{hourOfDay}"
        "6": "Co tydzień, w soboty %{hourOfDay}"
      quarterly: "Kwartalnie, pierwszego dnia kwartału"
    preview_data:
      first_name: 'Jane'
      last_name: 'Doe'
      display_name: 'Jane Doe'
      comment_body: 'Jest to przykładowy komentarz używany do podglądu treści wiadomości e-mail. To nie jest prawdziwa treść.'
      idea_title: 'Przykładowy pomysł'
    footer:
      "link_privacy_policy": "Polityka prywatności"
      "link_terms_conditions": "Regulamin"
      "link_unsubscribe": "Wypisz mnie z subskrypcji"
      "powered_by": "Wspierane przez"
      "recipient_statement": "Ten e-mail został wysłany do Ciebie przez Go Vocal w imieniu %{organizationName}, ponieważ jesteś zarejestrowanym użytkownikiem %{organizationLink}."
      "unsubscribe_statement": "Możesz odwiedzić stronę %{unsubscribeLink} , jeśli nie chcesz otrzymywać tych wiadomości e-mail w przyszłości."
      "unsubscribe_text": "zrezygnować z subskrybcji"
    follow:
      "unfollow_here": "Otrzymałeś to powiadomienie z powodu elementu, który obserwujesz. <a href=\"%{unfollow_url}\">Możesz przestać go obserwować tutaj.</a>"
    manual:
      preheader: 'Masz pocztę od %{organizationName}'
    comment_deleted_by_admin:
      reason: 'Powód, dla którego twój komentarz został usunięty:'
      cta_view: 'Zobacz ten pomysł'
      event_description: '%{organizationName} usunął Twój komentarz do pomysłu.'
      main_header: '%{organizationName} usunął Twój komentarz'
      other_reason: '%{otherReason}'
      inappropriate_reason: 'Twój komentarz został uznany za niewłaściwy'
      irrelevant_reason: 'Twój komentarz został uznany za nieistotny w tym kontekście'
      no_reason: 'Nie podano żadnego powodu'
      subject: 'Twój komentarz na platformie %{organizationName} został usunięty'
      preheader: 'Twój komentarz został usunięty'
    admin_digest:
      subject: 'Twój tygodniowy raport administratora z %{time}'
      preheader: 'Skrót dla administratora %{organizationName}'
      title_your_weekly_report: '%{firstName}, twój tygodniowy raport'
      text_introduction: 'Stworzyliśmy dla Ciebie listę pomysłów, które wygenerowały największą aktywności w ciągu ostatniego tygodnia. Dowiedz się, co dzieje się na Twojej platformie!'
      cta_visit_the_platform: 'Odwiedź swoją platformę'
      new_users: 'Nowi użytkownicy'
      new_inputs: 'Nowe wejścia'
      new_comments: 'Nowe komentarze'
      title_activity_past_week: 'Aktywność w ciągu ostatniego tygodnia'
      title_no_activity_past_week: 'Nie było żadnej aktywności w ostatnim tygodniu'
      reached_threshold: 'Osiągnąłeś próg'
      yesterday_by_author: 'Wczoraj wg %{author}'
      today_by_author: 'Dzisiaj wg %{author}'
      x_days_ago_by_author: '%{x} dni temu wg %{author}'
    admin_rights_received:
      cta_manage_platform: 'Zarządzaj swoją platformą'
      message_you_became_administrator: 'Uzyskałeś uprawnienia administratora do platformy uczestnictwa %{organizationName}.'
      preheader: 'Uzyskałeś uprawnienia administratora do platformy uczestnictwa %{organizationName}'
      subject: 'Zostałeś administratorem na platformie %{organizationName}'
      text_create_participatory_process: 'Jako administrator możesz tworzyć i konfigurować nowe projekty uczestnictwa. Możesz dodawać nowe etapy za pomocą osi czasu. Każdy z tych etapów może mieć swoje własne zachowanie w odniesieniu do zamieszczania pomysłów, komentowania i głosowania.'
      text_moderate_analyse_input: 'Po uruchomieniu projektów, pojawią się pierwsze pomysły. Będziesz otrzymywał cotygodniowe raporty nt. wszystkich kluczowych aktywności, abyś był na bieżąco. Przegląd pomysłów pomoże Ci moderować dane wejściowe i współpracować przy ich przetwarzaniu.'
      text_platform_setup: 'Jako administrator możesz skonfigurować swoją platformę uczestnictwa. Wybierz logo, zdjęcia i kolory, napisz osobistą wiadomość na swojej stronie głównej, wyślij zaproszenia, określ, co chcesz wiedzieć o swoich użytkownikach, ...'
      title_create_participatory_process: 'Zaprojektuj proces partycypacyjny'
      title_moderate_analyse_input: 'Moderuj i analizuj dane'
      title_platform_setup: 'Ustaw swoją platformę'
      title_what_can_you_do_administrator: 'Co możesz zrobić jako administrator?'
      title_you_became_administrator: 'Zostałeś administratorem'
    comment_marked_as_spam:
      by_author: 'wg %{authorName}'
      commented: '%{authorName} skomentował:'
      cta_review_comment: 'Przejrzyj komentarz'
      days_ago: '%{numberOfDays} dni temu'
      event_description: 'W <strong>"%{post}"</strong> został zamieszczony następujący komentarz:'
      inappropriate_content: 'Ten komentarz jest niestosowny lub obraźliwy.'
      preheader: 'Podejmij działanie w związku ze zgłoszeniem komentarza, jako spam'
      reported_this_because: '%{reporterFirstName} zgłosił to, ponieważ:'
      subject: '%{organizationName}: %{firstName} %{lastName} zgłosił ten komentarz jako spam'
      title_comment_spam_report: '%{firstName} %{lastName} zgłosił ten komentarz jako spam'
      today: Dzisiaj
      wrong_content: 'Ten komentarz jest bez związku.'
      yesterday: Wczoraj
    community_monitor_report:
      subject: 'Dostępny jest nowy raport z monitoringu społeczności'
      title: 'Dostępny jest nowy raport z monitoringu społeczności'
      text_introduction: 'Utworzono raport monitora społeczności za poprzedni kwartał. Możesz uzyskać do niego dostęp, klikając poniższy przycisk i logując się.'
      cta_report_button: 'Zobacz raport'
      report_name: 'Sprawozdanie monitora wspólnotowego'
    cosponsor_of_your_idea:
      cta_reply_to: 'Wyświetl swoją propozycję'
      event_description: 'Gratulacje! %{cosponsorName} przyjął Twoje zaproszenie do współsponsorowania Twojego wniosku.'
      main_header: '%{cosponsorName} przyjęła Twoje zaproszenie do współsponsorowania Twojego wniosku'
      subject: '%{cosponsorName} przyjęła Twoje zaproszenie do współsponsorowania Twojego wniosku'
      preheader: '%{cosponsorName} przyjęła Twoje zaproszenie do współsponsorowania Twojego wniosku'
    assignee_digest:
      subject: 'Pomysły wymagające Twojej opinii: %{numberIdeas}'
      preheader: 'Skrót dla pełnomocnika %{organizationName}'
      title_your_weekly_report: '%{firstName}, głos mieszkańca czeka na Twoją opinię'
      cta_manage_your_input: 'Zarządzaj swoimi treściami'
      x_inputs_need_your_feedback: 'dane wejściowe potrzebują Twojej opinii'
      title_assignment_past_week: 'Najnowsze pomysły przydzielone do Ciebie'
      title_no_assignment_past_week: 'Brak nowych pomysłów przypisanych do Ciebie w zeszłym tygodniu'
      yesterday_by_author: 'Wczoraj przez %{author}'
      today_by_author: 'Dzisiaj wg %{author}'
      x_days_ago_by_author: '%{x} dni temu wg %{author}'
      title_successful_past_week: 'Przypisany do Ciebie, który osiągnął próg'
    idea_marked_as_spam:
      cta_review: 'Recenzja'
      report_inappropriate_offensive_content: 'Uważam tę treść za nieodpowiednią lub obraźliwą.'
      report_not_an_idea: 'Ta treść nie jest pomysłem i to nie jest jej miejsce.'
      subject: 'Masz raport dot. spamu na platformie %{organizationName}'
      preheader: 'Podejmij działanie w związku ze zgłoszeniem spamu'
      reported_this_because: '%{reporterFirstName} zgłosił to, ponieważ:'
      title_spam_report: '%{firstName} %{lastName} zgłosił spam'
    invitation_to_cosponsor_idea:
      cta_reply_to: 'Współsponsoruj tę propozycję'
      event_description: '%{authorName} stworzył nową propozycję i chciałby, abyś ją współsponsorował.'
      event_description_cosponsoring: 'Współsponsorowanie wniosku oznacza, że <strong>Twoje imię i nazwisko będzie wyświetlane</strong> wraz z imionami i nazwiskami innych współsponsorów wniosku.'
      event_description_before_action: 'Aby zobaczyć propozycję i zaakceptować zaproszenie, musisz być zalogowany na swoje konto.'
      event_description_action: 'Kliknij poniżej, aby przeczytać propozycję.'
      main_header: 'Zostałeś zaproszony do współsponsorowania wniosku'
      subject: 'Zostałeś zaproszony do współsponsorowania wniosku'
      preheader: 'Zostałeś zaproszony do współsponsorowania propozycji %{authorName}'
    invite_reminder:
      cta_accept_invitation: 'Przyjmij zaproszenie'
      invitation_header: 'Twoje zaproszenie jest w toku'
      preheader: '%{organizationName} wysłał do Ciebie zaproszenie do udziału w platformie kilka dni temu.'
      invitation_expiry_message: 'Zaproszenie wygasa za około %{expiryDaysRemaining} dni.'
      subject: 'W oczekiwaniu na zaproszenie na platformę uczestnictwa %{organizationName}'
    invite_received:
      added_a_message: '%{organizationName} napisał następującą wiadomość:'
      cta_accept_invitation: 'Przyjmij zaproszenie'
      invitation_header: 'Jesteś zaproszony!'
      invitation_header_message: '%{organizationName} zaprosił cię na ich platformę uczestnictwa.'
      invitation_expiry_message: 'Zaproszenie wygasa za %{expiryDays} dni.'
      preheader: '%{organizationName} wysłał do Ciebie zaproszenie do przyłączenia się do jego platformy uczestnictwa.'
      subject: 'Jesteś zaproszony do platformy %{organizationName}'
    mention_in_comment:
      cta_reply_to: 'Odpowiedz %{commentAuthor}'
      event_description: '%{commentAuthorFull} wspomniał o tobie w swoim komentarzu do pomysłu "%{post}". Kliknij na poniższy link, aby przejść do rozmowy z %{commentAuthor}'
      main_header: 'Ludzie mówią o Tobie'
      subject: 'Ktoś wspomniał o Tobie na platformie %{organizationName}'
      preheader: '%{commentAuthor} wspomniał o Tobie w komentarzu'
    mention_in_internal_comment:
      cta_reply_to: 'Wyświetl komentarz przez %{firstName}'
      event_description: '%{authorNameFull} wspomniałem o tobie w wewnętrznym komentarzu.'
      subject: '%{firstName} wspomniałem o tobie w wewnętrznym komentarzu.'
      main_header: '%{firstName} wspomniałem o tobie w wewnętrznym komentarzu.'
      preheader: '%{authorNameFull} wspomniałem o tobie w wewnętrznym komentarzu.'
    moderator_digest:
      subject: 'Twój tygodniowy raport moderatora projektu z %{project_title}'
      preheader: 'Skrót dla moderatora projektu %{organizationName}'
      title_your_weekly_report: '%{firstName}, twój tygodniowy raport'
      text_introduction: 'Stworzyliśmy dla Ciebie listę pomysłów, które wygenerowały największą aktywność w ciągu ostatniego tygodnia. Dowiedz się, co dzieje się w ramach Twojego projektu!'
      cta_manage: 'Zarządzaj swoim projektem'
      new_users: 'Nowi użytkownicy'
      new_ideas: 'Nowe pomysły'
      new_comments: 'Nowe komentarze'
      title_inputs_past_week: 'Nowe wejścia w ciągu ostatniego tygodnia'
      title_no_inputs_past_week: 'Brak nowych wejść w ciągu ostatniego tygodnia'
      title_threshold_reached: Próg osiągnięty w ciągu ostatniego tygodnia
      yesterday_by_author: 'Wczoraj przez %{author}'
      today_by_author: 'Dzisiaj wg %{author}'
      x_days_ago_by_author: '%{x} dni temu wg %{author}'
    new_comment_for_admin:
      commented: '%{authorFirstName} skomentował:'
      cta_reply_to: 'Sprawdź komentarz %{commentAuthor}'
      days_ago: '%{numberOfDays} dni temu'
      event_description: '%{authorName} napisał komentarz na Twojej platformie. Dołącz śmiało do dyskusji by kontynuować rozmowę!'
      main_header: '%{firstName}, zamieszczono nowy komentarz na Twojej platformie'
      subject: 'Pojawił się nowy komentarz na platformie %{organizationName}'
      preheader: '%{authorName} skomentował'
      today: Dzisiaj
      yesterday: Wczoraj
    comment_on_idea_you_follow:
      cta_reply_to: 'Odpowiedz na %{commentAuthor}'
      event_description: '%{authorNameFull} placed a reaction on ''%{inputTitle}''. Click the button below to continue the conversation with %{authorName}.'
      main_header:
        idea: '%{authorName} skomentowałeś pomysł, który śledzisz'
        question: '%{authorName} skomentowałeś pytanie, które śledzisz'
        contribution: '%{authorName} skomentował wpis, który obserwujesz'
        project: '%{authorName} skomentował śledzony przez Ciebie projekt'
        issue: '%{authorName} skomentowałeś sprawę, którą śledzisz'
        option: '%{authorName} skomentowałeś opcję, którą obserwujesz'
        proposal: '%{authorName} skomentowałeś propozycję, którą obserwujesz'
        petition: '%{authorName} skomentowałeś petycję, którą śledzisz'
      subject: 'There''s a new comment on "%{input_title}"'
      preheader: '%{authorName} zostawił komentarz do pomysłu na %{organizationName}'
    new_idea_for_admin:
      main_header_publication: 'Nowe dane wejściowe zostały opublikowane na Twojej platformie'
      event_description_publication: '%{authorName} przesłał nowe dane wejściowe na Twoją platformę. Odkryj go teraz, przekaż opinię lub zmień jego status!'
      cta_publication: 'Prześlij opinię na adres %{authorName}'
      main_header_prescreening: 'Dane wejściowe wymagają Twojej weryfikacji'
      event_description_prescreening: '%{authorName} przesłał nowe dane wejściowe na Twoją platformę.'
      input_not_visible_prescreening: '<b>Wejście nie będzie widoczne</b>, dopóki nie zmienisz jego statusu.'
      cta_prescreening: 'Przejrzyj dane wejściowe'
      days_ago: '%{numberOfDays} dni temu'
      preheader: '%{authorName} opublikował nowy pomysł na Twojej platformie'
      today: Dzisiaj
      yesterday: Wczoraj
    comment_on_your_comment:
      cta_reply_to: 'Odpowiedz %{firstName}'
      event_description: '%{authorNameFull} napisał odpowiedź na Twój komentarz na temat "%{post}" na platformie uczestnictwa. Kliknij przycisk poniżej, aby kontynuować rozmowę z %{authorName}.'
      subject: 'Otrzymałeś odpowiedź na swój komentarz na platformie %{organizationName}'
      main_header: '%{authorName} odpowiedział na Twój komentarz'
      preheader: '%{authorName} odpowiedział na Twój komentarz na platformie %{organizationName}'
      replied: '%{authorFirstName} odpowiedział:'
    internal_comment_on_your_internal_comment:
      cta_reply_to: 'Wyświetl komentarz przez %{firstName}'
      event_description: '%{authorNameFull} skomentował twój wewnętrzny komentarz.'
      subject: 'Otrzymałeś komentarz do swojego wewnętrznego komentarza na stronie ''%{post}'''
      main_header: 'Otrzymałeś komentarz do swojego wewnętrznego komentarza na stronie ''%{post}'''
      preheader: '%{authorName} odpowiedział na twój wewnętrzny komentarz na stronie ''%{post}'''
    internal_comment_on_idea_assigned_to_you:
      cta_reply_to: 'Wyświetl komentarz przez %{firstName}'
      event_description: '%{authorNameFull} skomentowałeś wewnętrznie wejście przypisane do Ciebie.'
      subject: '''%{post}'' ma nowy komentarz wewnętrzny'
      main_header: '''%{post}'' ma nowy komentarz wewnętrzny'
      preheader: '''%{post}'' ma nowy komentarz wewnętrzny'
    internal_comment_on_idea_you_commented_internally_on:
      cta_reply_to: 'Wyświetl komentarz przez %{firstName}'
      event_description: '%{authorNameFull} skomentowałeś wewnętrznie dane wejściowe, które skomentowałeś wewnętrznie.'
      subject: '''%{post}'' ma nowy komentarz wewnętrzny'
      main_header: '''%{post}'' ma nowy komentarz wewnętrzny'
      preheader: '''%{post}'' ma nowy komentarz wewnętrzny'
    internal_comment_on_idea_you_moderate:
      cta_reply_to: 'Wyświetl komentarz przez %{firstName}'
      event_description: '%{authorNameFull} komentować wewnętrznie dane wejściowe w projekcie lub folderze, którym zarządzasz.'
      subject: '''%{post}'' ma nowy komentarz wewnętrzny'
      main_header: '''%{post}'' ma nowy komentarz wewnętrzny'
      preheader: '''%{post}'' ma nowy komentarz wewnętrzny'
    internal_comment_on_unassigned_unmoderated_idea:
      cta_reply_to: 'Wyświetl komentarz przez %{firstName}'
      event_description: '%{authorNameFull} skomentował wewnętrznie nieprzypisane dane wejściowe w niezarządzanym projekcie.'
      subject: '''%{post}'' ma nowy komentarz wewnętrzny'
      main_header: '''%{post}'' ma nowy komentarz wewnętrzny'
      preheader: '''%{post}'' ma nowy komentarz wewnętrzny'
    official_feedback_on_idea_you_follow:
      header_message: '%{feedback_author_name} gave an update on ''%{input_title}''.'
      header_title: 'There''s an update on "%{input_title}"'
      subject: 'Official feedback was posted on "%{input_title}"'
      preheader: 'There''s an update on an input you follow'
    mention_in_official_feedback:
      cta_reply_to: 'Odpowiedz %{organizationName}'
      event_description: '%{organizationName} wspomniał o Tobie w swojej informacji zwrotnej na temat pomysłu "%{post}". Kliknij na poniższy link, aby przejść do rozmowy z %{organizationName}'
      main_header: 'Wspomniano o Tobie'
      subject: '%{organizationName} wspomniał o Tobie w swojej informacji zwrotnej'
      preheader: '%{commentAuthor} wspomniał o Tobie w informacji zwrotnej'
    project_moderation_rights_received:
      cta_manage_project: 'Zarządzaj tym projektem'
      message_you_became_moderator: 'Administrator platformy partycypacyjnej %{organizationName} właśnie ustanowił Cię moderatorem poniższego projektu:'
      no_ideas: 'Nie ma jeszcze pomysłów'
      preheader: 'Administrator platformy partycypacyjnej %{organizationName} właśnie ustanowił Cię moderatorem poniższego projektu'
      subject: 'Zostałeś moderatorem na platformie %{organizationName}'
      text_design_participatory_process: 'Jako moderator projektu, możesz skonfigurować sposób interakcji użytkowników w ramach Twojego projektu. Możesz dodawać nowe etapy za pomocą osi czasu. Każdy z tych etapów może mieć swoje własne zachowanie w odniesieniu do zamieszczania pomysłów, komentowania i głosowania.'
      text_moderate_analyse_input: 'Gdy projekt zostanie uruchomiony, pojawią się pierwsze pomysły. Będziesz otrzymywał cotygodniowe raporty nt. wszystkich kluczowych działań, abyś był na bieżąco. Przegląd pomysłów w widoku moderatora projektu pomoże Ci zrozumieć, które z nich otrzymały najwięcej głosów pro i kontra.'
      text_share_project_information: 'Aby podnieść jakość otrzymywanych pomysłów, kluczowe jest dzielenie się odpowiednimi informacjami: dodanie opisu projektu, dołączenie zdjęć (w tym szkiców i planów) oraz informowanie o wszystkich wydarzeniach z tym związanych. Pamiętaj: im lepsza informacja tym większe uczestnictwo obywateli!'
      title_design_participatory_process: 'Projektowanie procesu partycypacyjnego'
      title_moderate_analyse_input: 'Moderuj i analizuj dane'
      title_share_project_information: 'Udziel informacji o projekcie'
      title_what_can_you_do_moderator: 'Co możesz zrobić jako moderator projektu?'
      title_you_became_moderator: 'Zostałeś moderatorem'
      x_ideas: 'ilość pomysłów: %{numberOfIdeas}'
    project_folder_moderation_rights_received:
      cta_view_folder_button: 'Zobacz ten pomysł'
      message_added_as_folderadmin: 'Otrzymałeś uprawnienia menedżera folderów na platformie partycypacyjnej %{organizationName}dla następującego folderu:'
      no_projects: 'Nie ma jeszcze projektów'
      preheader: 'Administrator platformy partycypacyjnej %{organizationName} właśnie ustanowił Cię moderatorem poniższego folderu'
      subject: 'Zostałeś managerem folderu projektu na platformie uczestnictwa %{organizationName}'
      text_manage_folder: 'Folder jest sposobem na zorganizowanie kilku projektów razem. Jako administrator folderu, możesz edytować folder i jego opis oraz tworzyć nowe projekty (aby usunąć projekty, skontaktuj się z administratorem platformy). Będziesz miał również prawa do zarządzania wszystkimi projektami w ramach folderu, co pozwoli Ci na edycję projektów, zarządzanie wkładem i wysyłanie maili do uczestników.'
      text_moderate_analyse_input: 'Gdy projekty zostaną uruchomione, zaczną napływać pierwsze dane wejściowe. Będziesz otrzymywać cotygodniowe raporty z kluczowymi działaniami, abyś mógł być na bieżąco z tym, co się dzieje. Pomysły w panelu administracyjnym pozwalają na przeglądanie i zarządzanie wkładem, w tym nadawanie statusów i odpowiadanie na posty i komentarze.'
      text_design_participatory_process: 'Możesz zarządzać różnymi projektami uczestnictwa w ramach swojego folderu - konfigurować metody uczestnictwa, dodawać opis projektu, załączać zdjęcia i informować o powiązanych wydarzeniach. Możesz również zarządzać sposobem, w jaki uczestnicy wchodzą w interakcję z Twoimi projektami, w tym ustawiać prawa dostępu i konfigurować ustawienia wysyłania postów, głosowania i komentowania.'
      title_design_participatory_process: 'Zaprojektuj proces partycypacyjny'
      title_moderate_analyse_input: 'Moderuj i analizuj dane'
      title_manage_folder: 'Zarządzaj ustawieniami folderów i twórz nowe projekty.'
      title_what_can_you_do_folderadmin: 'Co możesz zrobić jako menedżer folderów?'
      title_added_as_folderadmin: 'Zostałeś dodany jako menedżer folderów'
      x_projects: '%{numberOfProjects} projekty'
    project_phase_started:
      cta_view_phase: 'Odkryj nowy etap'
      event_description: 'Ten projekt wszedł w nowy etap na platformie %{organizationName}. Kliknij na poniższy link, aby dowiedzieć się więcej!'
      main_header: 'Rozpoczął się nowy etap projektu "%{projectName}"'
      subtitle: 'O ''%{projectName}'''
      new_phase: 'Projekt ten wszedł w etap "%{phaseTitle}"'
      subject: '%{projectName} wszedł w nowy etap'
      preheader: 'Rozpoczął się nowy etap projektu %{projectName}'
    project_phase_upcoming:
      cta_view_phase: 'Sprawdź następny etap'
      event_description: 'Projekt "%{projectName}" wkrótce wejdzie w nowy etap. Upewnij się, że wszystko jest przygotowane: Czy opis jest odpowiedni? Czy wybrane pomysły zostały przeniesione do nowego etapu? Czy chcesz poinformować swoich obywateli o specyfice tego etapu poprzez specjalną kampanię e-mailową?'
      main_header: '%{firstName}, projekt wkrótce przejdzie do nowego etapu'
      subtitle: 'O ''%{projectName}'''
      new_phase: 'Projekt wejdzie w etap "%{phaseTitle}"'
      subject: 'Skonfiguruj nowy etap %{projectName}'
      preheader: 'Wkrótce rozpocznie się nowy etap projektu dla %{projectName}'
    project_published:
      subject: 'Nowy projekt został opublikowany na platformie %{organizationName}'
      header_title: 'Nowy projekt został opublikowany'
      header_message: 'Platforma uczestnictwa %{organizationName} właśnie opublikowała następujący projekt:'
      preheader: 'Nowy projekt został opublikowany'
    project_review_request:
      subject: 'Wniosek o weryfikację: Projekt oczekuje na zatwierdzenie.'
      header: '%{requesterName} zaprosił Cię do zapoznania się z projektem "%{projectTitle}"'
      header_message: "Obecnie projekt znajduje się w trybie wersji roboczej i nie jest widoczny dla użytkowników. Gdy go sprawdzisz i zatwierdzisz, moderator będzie mógł go opublikować."
      cta_review_project: "Przejrzyj projekt"
    project_review_state_change:
      subject: '"%{projectTitle}" został zatwierdzony'
      header: '%{reviewerName} zatwierdził projekt "%{projectTitle}"'
      header_message: "Projekt jest już gotowy do uruchomienia. Możesz go opublikować, kiedy tylko będziesz gotowy!"
      cta_go_to_project: "Przejdź do ustawień projektu"
    status_change_on_idea_you_follow:
      status_change: 'The new status of this input is ''%{status}'''
      header_message: '%{organizationName} updated the status of the input ''%{input_title}'' on their digital participation platform.'
      header_title: 'An input you follow has a new status'
      subject: 'The status of "%{input_title}" has changed'
      preheader: 'An input you follow has a new status'
    user_digest:
      subject: "Tygodniowa aktualizacja platformy uczestnictwa %{organizationName}"
      commented: "%{authorFirstName} skomentował:"
      preheader: "Tygodniowy przegląd %{organizationName}"
      title_your_weekly_report: "Dowiedz się, co wydarzyło się w zeszłym tygodniu"
      intro_text: "Oto podsumowanie tego, co wydarzyło się na platformie uczestnictwa %{organizationName}."
      cta_go_to_the_platform: "Przejdź na platformę"
      title_no_activity_past_week: "Brak aktywności w ciągu ostatniego tygodnia"
      successful_proposals_title: "Osiągnąłeś próg"
      successful_proposals_text: "These proposals have gotten enough support to move to the next stage! Click on the proposal to learn more about what happens next."
      today_by_author: "Dzisiaj wg %{author}"
      yesterday_by_author: "Wczoraj przez %{author}"
      x_days_ago_by_author: "%{x} dni temu wg %{author}"
      trending_title: "Trendy"
      trending_text: "Interesuje Cię, co dzieje się na platformie? Oto trzy najpopularniejsze wpisy i to, co ludzie o nich mówią."
      no_notifications: "Brak powiadomień"
      one_notification: "1 powiadomienie"
      multiple_notifications: "Powiadomienia: %{notifCount}"
      no_unread_notifications: "Nie masz żadnych nieprzeczytanych powiadomień. Odwiedź platformę, aby wnieść swój wkład i wygenerować nowe powiadomienia!"
      unread_notifications: "Masz nieprzeczytane powiadomienia. Odwiedź platformę, aby dowiedzieć się, co się dzieje!"
    threshold_reached_for_admin:
      cta_process_initiative: 'Skieruj tę inicjatywę do dalszych kroków'
      main_header: 'Inicjatywa osiągnęła próg poparcia!'
      subject: 'Inicjatywa na Twojej platformie osiągnęła wymagany próg poparcia'
      preheader: 'Upewnij się, że wykonasz następne kroki'
    welcome:
      cta_join_platform: 'Odkryj platformę'
      subject: 'Witamy na platformie %{organizationName}'
      main_header: Witaj!
      message_welcome: 'Gratulacje, zapisałeś się na platformę uczestnictwa %{organizationName}. Teraz możesz odkryć platformę i sprawić, że Twój głos będzie słyszalny. Możesz również dodać zdjęcie profilowe i krótki opis, aby się przedstawić.'
      preheader: 'Oto co możesz zrobić na platformie %{organizationName}'
    idea_published:
      subject:
        idea: 'Twój pomysł został opublikowany'
        question: 'Twoje pytanie zostało opublikowane'
        contribution: 'Twój wkład został opublikowany'
        project: 'Twój projekt został opublikowany'
        issue: 'Twój numer został opublikowany'
        option: 'Twoja opcja została opublikowana'
        proposal: 'Twoja propozycja została opublikowana'
        petition: 'Twoja petycja została opublikowana'
      main_header: 'Zamieściłeś pomysł! Upewnijmy się, że na pewno zostanie zauważony.'
      header_message: 'Upewnijmy się, że zostanie przeczytany.'
      message_get_votes: 'Zapoznaj więcej ludzi ze swoim pomysłem:'
      action_published_idea: 'Opublikowany pomysł'
      action_add_image: '%{addImageLink} aby zwiększyć widoczność'
      add_image: 'Dodaj obrazek'
      action_share_fb: 'Daj znać znajomym na %{fbLink}'
      action_share_twitter: 'Poinformuj swoich zwolenników na %{twitterLink}'
      action_send_email: 'Wyślij do swoich kontaktów %{sendEmailLink}'
      send_email: e-mail
      action_share_link: 'Udostępnij w dowolny sposób, kopiując %{link}'
      link: link
      preheader: '%{firstName}, gratuluję umieszczenia pomysłu na platformie %{organizationName}. Teraz zbierz poparcie.'
    your_input_in_screening:
      main_header:
        idea: 'Twój pomysł jest w "%{prescreening_status_title}".'
        question: 'Twoje pytanie jest w "%{prescreening_status_title}".'
        contribution: 'Twój wkład jest w "%{prescreening_status_title}"'
        project: 'Twój projekt znajduje się na stronie "%{prescreening_status_title}".'
        issue: 'Twój problem jest w "%{prescreening_status_title}".'
        option: 'Twoja opcja znajduje się w "%{prescreening_status_title}".'
        proposal: 'Twoja propozycja znajduje się na stronie "%{prescreening_status_title}".'
        petition: 'Twoja petycja znajduje się na stronie "%{prescreening_status_title}".'
      message: '"%{input_title}" stanie się widoczny dla innych po jego sprawdzeniu i zatwierdzeniu.'
      subject: '"%{input_title}" jest prawie opublikowany'
      preheader: 'Obecnie znajduje się na stronie %{prescreening_status_title}'
    voting_basket_submitted:
      subject: '%{organizationName}Pomyślnie zagłosowałeś'
      preheader: 'Pomyślnie zagłosowałeś na platformie uczestnictwa %{organizationName}'
      title_basket_submitted: 'Zagłosowałeś pomyślnie'
      event_description: 'Dziękujemy za udział. Twoje głosy zostały zarejestrowane. Odwiedź platformę %{organizationName} , aby zobaczyć i zarządzać swoimi głosami.'
      cta_see_votes_submitted: 'Zobacz oddane głosy'
      cta_message: 'Kliknij poniższy przycisk, aby wziąć udział'
    native_survey_not_submitted:
      subject: '%{organizationName}Już prawie! Prześlij swoje odpowiedzi'
      preheader: 'Nie wypełniłeś swojej ankiety na platformie uczestnictwa %{organizationName}.'
      title_native_survey_not_submitted: 'Już prawie! Prześlij swoje odpowiedzi'
      body_native_survey_not_submitted: 'Zacząłeś udostępniać swoje odpowiedzi na stronie %{phaseTitle} , ale ich nie przesłałeś. Zgłoszenia zostaną zamknięte na stronie %{phaseEndDate}. Kliknij poniższy przycisk, aby kontynuować od miejsca, w którym skończyłeś.'
      body_native_survey_not_submitted_no_date: 'Zacząłeś udostępniać swoje odpowiedzi na stronie %{phaseTitle} , ale ich nie przesłałeś. Kliknij poniższy przycisk, aby kontynuować od miejsca, w którym przerwałeś.'
      cta_complete_your_survey_response: 'Wznów swoją odpowiedź na ankietę'
    voting_basket_not_submitted:
      subject: '%{organizationName}Nie oddałeś swojego głosu'
      preheader: 'Nie oddałeś głosu na platformie uczestnictwa %{organizationName}.'
      title_basket_not_submitted: 'Nie oddałeś swojego głosu'
      event_description: 'Wybrałeś kilka opcji dla %{contextTitle} , ale nie przesłałeś swojego wyboru.'
      cta_view_options_and_vote: 'Zobacz opcje i zagłosuj'
      cta_message: 'Kliknij poniższy przycisk, aby przesłać wybrane opcje'
    voting_last_chance:
      subject: '%{organizationName}Ostatnia szansa na oddanie głosu na %{phaseTitle}'
      preheader: 'Ostatnia szansa, aby zagłosować na %{phaseTitle} na platformie uczestnictwa %{organizationName}'
      title_last_chance: 'Ostatnia szansa na oddanie głosu na %{phaseTitle}'
      body_1: 'Faza głosowania na projekt %{projectTitle} dobiegnie końca jutro o północy.'
      body_2: 'Czas ucieka, a my zauważyliśmy, że jeszcze nie oddałeś swojego głosu! Działaj teraz, klikając poniższy przycisk, aby wziąć udział.'
      body_3: 'W ten sposób uzyskasz dostęp do szeregu opcji i będziesz mieć szansę na wniesienie swojego wkładu, który ma kluczowe znaczenie dla podjęcia decyzji o przyszłości tego projektu.'
      cta_vote: 'Głosuj'
    voting_results:
      subject: '%{organizationName}: %{phaseTitle} wyniki głosowania ujawnione!'
      preheader: '%{phaseTitle} wyniki głosowania ujawnione na platformie uczestnictwa %{organizationName}'
      title_results: '%{phaseTitle} wyniki głosowania ujawnione!'
      body_1: 'Wyniki już są!'
      body_2: '%{organizationName} Wyniki głosowania na platformie %{phaseTitle} zostały opublikowane!'
      body_3: 'Zachęcamy do zapoznania się z wynikami i czekania na dalsze aktualizacje dotyczące kolejnych kroków.'
      cta_see_results: 'Zobacz wyniki na platformie'
    event_registration_confirmation:
      subject: "Jesteś na miejscu! Twoja rejestracja na \"%{eventTitle}\" została potwierdzona"
      preheader: "%{firstName}dzięki za rejestrację na %{eventTitle}"
      header_message: "%{firstName}dzięki za rejestrację na"
      event_details:
        labels:
          date: 'Data'
          location: 'Lokalizacja'
          online_link: 'Link online'
          description: 'Opis'
          project: 'Projekt'
      cta_go_to_event: 'Zobacz wydarzenie'
      cta_add_to_calendar: 'Dodaj do swojego kalendarza'
    voting_phase_started:
      subject: '%{organizationName}Rozpoczęła się faza głosowania na %{projectName}'
      preheader: 'Rozpoczęła się faza głosowania na stronie %{projectName} na platformie uczestnictwa %{organizationName}.'
      event_description: 'Projekt "%{projectName}" prosi Cię o głosowanie pomiędzy opcjami %{numIdeas} :'
      cta_message: 'Kliknij poniższy przycisk, aby wziąć udział'
      cta_vote: 'Przejdź do platformy, aby zagłosować'
    survey_submitted:
      subject: '%{organizationName}Dziękuję za twoją odpowiedź! 🎉'
      preheader: 'Oto szczegóły Twojego zgłoszenia.'
      main_header: 'Dziękujemy za podzielenie się Twoimi przemyśleniami na temat "%{projectName}"!'
      your_input_submitted: 'Twoje dane wejściowe dla "%{projectName}" zostały przesłane pomyślnie.'
      if_you_would_like_to_review: 'Jeśli chcesz przejrzeć swoje zgłoszenie, możesz pobrać swoje odpowiedzi poniżej.'
      your_submission_has_id: 'Twoje zgłoszenie ma następujący unikalny identyfikator:'
      you_can_use_this_id: 'Możesz użyć tego identyfikatora, aby skontaktować się z administratorami platformy, jeśli chcesz, aby Twoje zgłoszenie zostało usunięte.'
      download_responses: 'Pobierz swoje odpowiedzi'
    admin_labels:
      recipient_role:
        admins: 'Do administratorów'
        admins_and_managers: 'Do administratorów i menedżerów'
        managers: 'Do kierowników'
        project_participants: 'Do uczestników projektu'
        registered_users: 'Dla zarejestrowanych użytkowników'
      recipient_segment:
        admins: 'Administratorzy'
        admins_and_managers: 'Administratorzy i menedżerowie'
        admins_and_managers_assigned_to_the_input: 'Administratorzy i menedżerowie przypisani do wejścia'
        admins_and_managers_managing_the_project: 'Administratorzy i menedżerowie zarządzający projektem'
        admins_assigned_to_a_proposal: 'Administratorzy przypisani do propozycji'
        all_users: 'Wszyscy użytkownicy'
        all_users_who_uploaded_proposals: 'Wszyscy użytkownicy, którzy wysłali propozycje'
        managers: 'Menedżerowie'
        managers_managing_the_project: 'Menedżerowie zarządzający projektem'
        new_attendee: 'Nowo zarejestrowany użytkownik'
        project_reviewers: 'Recenzenci projektów i menedżerowie folderów'
        project_review_requester: 'Użytkownik, który zażądał przeglądu projektu'
        user_who_commented: 'Użytkownik, który skomentował'
        user_who_is_invited_to_cosponsor_a_proposal: 'Użytkownik zaproszony do współsponsorowania propozycji'
        user_who_is_mentioned: 'Użytkownik, który został wymieniony'
        user_who_is_receiving_admin_rights: 'Użytkownik, który otrzymuje uprawnienia administratora'
        user_who_is_receiving_folder_moderator_rights: 'Użytkownik, który otrzymuje uprawnienia moderatora folderu'
        user_who_is_receiving_project_moderator_rights: 'Użytkownik, który otrzymuje uprawnienia moderatora projektu'
        user_who_published_the_input: 'Użytkownik, który opublikował dane wejściowe'
        user_who_published_the_proposal: 'Użytkownik, który opublikował propozycję'
        user_who_registers: 'Użytkownik, który się rejestruje'
        user_who_submitted_the_input: 'Użytkownik, który przesłał dane wejściowe'
        user_who_voted: 'Użytkownik, który głosował'
        user_who_was_invited: 'Użytkownik, który został zaproszony'
        user_with_unsubmitted_survey: 'Użytkownik, który rozpoczął ankietę, ale jej nie przesłał'
        user_with_unsubmitted_votes: 'Użytkownik, który nie oddał głosu'
        users_who_engaged_but_not_voted: 'Użytkownicy, którzy zaangażowali się w projekt, ale nie głosowali'
        users_who_engaged_with_the_project: 'Użytkownicy, którzy zaangażowali się w projekt'
        users_who_follow_the_input: 'Użytkownicy, którzy śledzą dane wejściowe'
        users_who_follow_the_project: 'Użytkownicy, którzy śledzą projekt'
        users_who_follow_the_proposal: 'Użytkownicy, którzy śledzą propozycję'
      content_type:
        comments: 'Komentarze'
        content_moderation: 'Moderacja treści'
        events: 'Wydarzenia'
        general: 'Ogólne'
        inputs: 'Wejścia'
        internal_comments: 'Uwagi wewnętrzne'
        permissions: 'Uprawnienia'
        projects: 'Projekty'
        proposals: 'Propozycje'
        reactions: 'Reakcje'
        voting: 'Głosowanie'
        surveys: 'Ankiety'
      trigger:
        7_days_after_invite_is_sent: '7 dni po wysłaniu zaproszenia'
        7_days_before_the_project_changes_phase: '7 dni przed zmianą fazy projektu'
        comment_is_deleted: 'Komentarz został usunięty.'
        comment_is_flagged_as_spam: 'Komentarz został oznaczony jako spam'
        content_gets_flagged_as_innapropiate: 'Zawartość zostanie oznaczona jako niestosowna'
        initiative_resubmitted_for_review: 'Wniosek ponownie przesłany do przeglądu'
        input_is_assigned: 'Wejście jest przypisane'
        input_is_flagged_as_spam: 'Wejście jest oznaczone jako spam'
        input_is_published: 'Dane wejściowe są publikowane'
        input_is_updated: 'Wejście jest aktualizowane'
        input_status_changes: 'Zmiany stanu wejścia'
        internal_comment_is_posted_on_idea_assigned_to_user: 'Komentarz wewnętrzny jest publikowany na wejściu przypisanym do użytkownika'
        internal_comment_is_posted_on_idea_in_project_or_folder_user_manages: 'Komentarz wewnętrzny jest publikowany na wejściu w projekcie lub folderze zarządzanym przez użytkownika'
        internal_comment_is_posted_on_idea_user_has_commented_internally_on: 'Komentarz wewnętrzny jest publikowany na wejściu, które użytkownik skomentował wewnętrznie'
        internal_comment_is_posted_on_idea_user_moderates: 'Komentarz wewnętrzny jest publikowany na wejściu moderowanym przez użytkownika'
        internal_comment_is_posted_on_unassigned_unmoderated_idea: 'Komentarz wewnętrzny jest publikowany na nieprzypisanych danych wejściowych w niezarządzanym projekcie'
        project_review_request: 'Moderator zażądał przeglądu projektu'
        project_review_state_change: 'Recenzent zatwierdził projekt'
        new_input_awaits_screening: 'Nowy wkład czeka na ekranizację'
        new_input_is_published: 'Nowy wkład jest publikowany'
        new_proposal_is_posted: 'Nowa propozycja została opublikowana'
        project_phase_changes: 'Zmiany fazy projektu'
        project_published: 'Projekt opublikowany'
        proposal_gets_reported_as_spam: 'Propozycja zostaje zgłoszona jako spam'
        proposal_is_assigned_to_admin: 'Propozycja jest przypisana do administratora'
        proposal_is_published: 'Wniosek został opublikowany'
        proposal_is_updated: 'Propozycja jest aktualizowana'
        proposal_is_upvoted_above_threshold: 'Propozycja została przegłosowana powyżej progu'
        proposal_status_changes: 'Zmiany statusu propozycji'
        registration_to_event: 'Rejestracja na wydarzenie'
        survey_1_day_after_draft_saved: '1 dzień po ostatnim zapisaniu ankiety w wersji roboczej przez użytkownika'
        user_accepts_invitation_to_cosponsor_a_proposal: 'Użytkownik akceptuje zaproszenie do współsponsorowania propozycji'
        user_comments: 'Komentarze użytkowników'
        user_comments_on_input: 'Komentarze użytkowników na temat wprowadzania danych'
        user_comments_on_proposal: 'Komentarze użytkowników na temat propozycji'
        user_is_given_admin_rights: 'Użytkownik otrzymuje prawa administratora'
        user_is_given_folder_moderator_rights: 'Użytkownik otrzymuje prawa moderatora folderu'
        user_is_given_project_moderator_rights: 'Użytkownik otrzymuje uprawnienia moderatora projektu'
        user_is_invited_to_cosponsor_a_proposal: 'Użytkownik jest zaproszony do współsponsorowania propozycji'
        user_is_mentioned: 'Użytkownik jest wymieniony'
        user_is_mentioned_in_internal_comment: 'Użytkownik jest wymieniony w komentarzu wewnętrznym'
        user_registers_for_the_first_time: 'Użytkownik rejestruje się po raz pierwszy'
        user_replies_to_comment: 'Użytkownik odpowiada na komentarz'
        user_replies_to_internal_comment: 'Użytkownik odpowiada na wewnętrzny komentarz'
        voting_1_day_after_last_votes: '1 dzień po ostatnim głosowaniu użytkownika'
        voting_2_days_before_phase_closes: '2 dni przed zakończeniem fazy głosowania'
        voting_basket_submitted: 'Głosy są oddawane'
        voting_phase_ended: 'Faza głosowania zakończona'
